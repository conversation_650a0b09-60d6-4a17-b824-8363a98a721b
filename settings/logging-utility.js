// Global Logging Utility for SEO Time Machines
// Controls ALL console logging based on debug mode setting
// 
// This utility provides centralized control over all debugging output:
// - When debug mode is OFF: All console methods become no-op functions (completely silent)
// - When debug mode is ON: All console methods work normally
//
// Individual components (QuickActionsDOMRestore, DOMSnapshotUtility, etc.) 
// now use this global system instead of their own debug mode properties.
// Simply use console.log(), console.error(), etc. anywhere in the codebase.

(function() {
    'use strict';
    
    // Store original console methods
    const originalConsole = {
        log: console.log,
        error: console.error,
        warn: console.warn,
        info: console.info,
        debug: console.debug,
        trace: console.trace,
        table: console.table,
        group: console.group,
        groupEnd: console.groupEnd,
        groupCollapsed: console.groupCollapsed,
        time: console.time,
        timeEnd: console.timeEnd,
        clear: console.clear,
        count: console.count,
        assert: console.assert
    };
    
    // Silent functions (no-op)
    const silentConsole = {
        log: () => {},
        error: () => {},
        warn: () => {},
        info: () => {},
        debug: () => {},
        trace: () => {},
        table: () => {},
        group: () => {},
        groupEnd: () => {},
        groupCollapsed: () => {},
        time: () => {},
        timeEnd: () => {},
        clear: () => {},
        count: () => {},
        assert: () => {}
    };
    
    let debugModeEnabled = false;
    
    // Function to enable/disable logging
    function setLoggingEnabled(enabled) {
        debugModeEnabled = enabled;
        
        if (enabled) {
            // Restore original console methods
            Object.keys(originalConsole).forEach(method => {
                console[method] = originalConsole[method];
            });
        } else {
            // Replace with silent methods
            Object.keys(silentConsole).forEach(method => {
                console[method] = silentConsole[method];
            });
        }
    }
    
    // IMMEDIATELY disable logging by default (before any async operations)
    setLoggingEnabled(false);
    
    // Minimal Reader Settings Bridge - Sync settings to localStorage for MAIN world scripts
    function syncMinimalReaderSettings() {
        if (typeof chrome !== 'undefined' && chrome.storage) {
            chrome.storage.sync.get(['gmbExtractorSettings'], async function(result) {
                const settings = result.gmbExtractorSettings || {};
                
                // Sync all minimal reader settings to chrome.storage.local for service worker compatibility
                try {
                    await chrome.storage.local.set({
                        minimalReaderEnabled: settings.minimalReaderEnabled !== false ? 'true' : 'false',
                        minimalReaderTheme: settings.minimalReaderTheme || 'white',
                        minimalReaderFontSize: (settings.minimalReaderFontSize || 18).toString(),
                        minimalReaderLineHeight: (settings.minimalReaderLineHeight || 1.6).toString(),
                        minimalReaderFontWeight: settings.minimalReaderFontWeight || 'normal',
                        minimalReaderButtonPosition: settings.minimalReaderButtonPosition || 'top-left',
                        minimalReaderSpeedReadingEnabled: settings.minimalReaderSpeedReadingEnabled === true ? 'true' : 'false',
                        minimalReaderStopWordFading: settings.minimalReaderStopWordFading !== false ? 'true' : 'false',
                        debugMode: settings.debugMode === true ? 'true' : 'false'
                    });
                } catch (error) {
                    console.error('LoggingUtility: Error syncing settings to chrome.storage.local:', error);
                }
                
                // Dispatch custom event to notify MAIN world scripts
                if (typeof window !== 'undefined') {
                    window.dispatchEvent(new CustomEvent('minimalReaderSettingsUpdate'));
                }
            });
        }
    }
    
    // Load debug mode setting and apply it
    function loadDebugModeSetting() {
        if (typeof chrome !== 'undefined' && chrome.storage) {
            chrome.storage.local.get(['gmbExtractorSettings'], function(result) {
                const settings = result.gmbExtractorSettings || {};
                // Enable debug mode when the user toggles it ON (no secret mode required)
                const debugMode = settings.debugMode === true;
                setLoggingEnabled(debugMode);
                
                // Show this message if debug mode is enabled
                if (debugMode) {
                    originalConsole.log('GMB Logging Utility: Debug mode enabled - logging restored');
                }
                
                // Also sync minimal reader settings
                syncMinimalReaderSettings();
            });
        }
        // If chrome.storage not available, logging stays disabled (already set above)
    }
    
    // Listen for settings updates (simplified - no real-time needed)
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'updateSettings') {
                // Enable debug mode when the user toggles it ON (no secret mode required)
                const debugMode = message.settings.debugMode === true;
                setLoggingEnabled(debugMode);
                
                // Also sync minimal reader settings to localStorage
                const settings = message.settings;
                localStorage.setItem('minimalReaderEnabled', settings.minimalReaderEnabled !== false ? 'true' : 'false');
                localStorage.setItem('minimalReaderTheme', settings.minimalReaderTheme || 'white');
                localStorage.setItem('minimalReaderFontSize', (settings.minimalReaderFontSize || 18).toString());
                localStorage.setItem('minimalReaderLineHeight', (settings.minimalReaderLineHeight || 1.6).toString());
                localStorage.setItem('minimalReaderFontWeight', settings.minimalReaderFontWeight || 'normal');
                localStorage.setItem('minimalReaderButtonPosition', settings.minimalReaderButtonPosition || 'top-left');
                localStorage.setItem('minimalReaderSpeedReadingEnabled', settings.minimalReaderSpeedReadingEnabled === true ? 'true' : 'false');
                localStorage.setItem('minimalReaderStopWordFading', settings.minimalReaderStopWordFading !== false ? 'true' : 'false');
                localStorage.setItem('debugMode', settings.debugMode === true ? 'true' : 'false');
                
                // Dispatch custom event to notify MAIN world scripts
                if (typeof window !== 'undefined') {
                    window.dispatchEvent(new CustomEvent('minimalReaderSettingsUpdate'));
                }
                
                sendResponse({received: true});
            }
        });
    }
    
    // Load settings after initial disable
    loadDebugModeSetting();
    
    // Make logging control available globally for manual testing
    // Check if we're in window context (popup/content) or service worker context
    if (typeof window !== 'undefined') {
        window.GMBLogging = {
            enable: () => setLoggingEnabled(true),
            disable: () => setLoggingEnabled(false),
            isEnabled: () => debugModeEnabled,
            reload: loadDebugModeSetting
        };
    } else if (typeof self !== 'undefined') {
        // Service worker context
        self.GMBLogging = {
            enable: () => setLoggingEnabled(true),
            disable: () => setLoggingEnabled(false),
            isEnabled: () => debugModeEnabled,
            reload: loadDebugModeSetting
        };
    }
    
})(); 