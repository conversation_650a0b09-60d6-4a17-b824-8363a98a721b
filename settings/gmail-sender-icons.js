(async () => {
  // Prevent multiple executions
  if (window.GMBGmailSenderIconsLoaded) {
    return;
  }
  window.GMBGmailSenderIconsLoaded = true;

  let isEnabled = true;
  const iconCache = new Map();
  let observer = null;

  // --- Core Functions ---

  function enableStyling() {
    document.body.classList.add('gmb-sender-icons-enabled');
  }

  function disableStyling() {
    document.body.classList.remove('gmb-sender-icons-enabled');
  }

  function getDomain(email) {
    if (!email) return null;
    const m = email.match(/@([\w.-]+)/);
    return m ? m[1] : null;
  }

  function createIcon(email) {
    const domain = getDomain(email);
    if (!domain) return null;

    if (iconCache.has(domain)) {
      return iconCache.get(domain).cloneNode(true);
    }

    // Create unified container with background
    const container = document.createElement('span');
    container.className = 'gmb-sender-icon-container';
    container.style.cssText = 'display: inline-flex; align-items: center; background-color: #e1e3e1; border-radius: 4px; padding: 0px 6px!important; height: 18px!important; margin-right: 8px; vertical-align: middle;';

    // Create favicon image
    const img = document.createElement('img');
    img.className = 'gmb-sender-icon';
    img.src = `https://www.google.com/s2/favicons?domain=${domain}&sz=16`;
    img.style.cssText = 'width: 16px; height: 16px; border-radius: 2px; margin-right: 6px; vertical-align: middle; flex-shrink: 0;';
    img.alt = domain;
    img.title = `From ${domain}`;
    
    // Create domain text
    const domainText = document.createElement('span');
    domainText.className = 'gmb-sender-domain';
    domainText.textContent = domain;
    domainText.style.cssText = 'font-size: 12px; color: #5f6368; font-weight: 400; white-space: nowrap;';
    
    let fallbackCreated = false;
    img.onerror = () => {
      if (fallbackCreated) {
        // If favicon fails, create a colored letter bubble
        const letterBubble = document.createElement('span');
        letterBubble.style.cssText = 'display: inline-flex; align-items: center; justify-content: center; width: 16px; height: 16px; border-radius: 50%; background: #1a73e8; color: white; font-size: 10px; font-weight: bold; margin-right: 6px; vertical-align: middle;';
        letterBubble.textContent = domain.charAt(0).toUpperCase();
        letterBubble.title = `From ${domain}`;
        container.replaceChild(letterBubble, img);
        return;
      }
      fallbackCreated = true;
      img.src = `https://ui-avatars.com/api/?name=${domain.charAt(0)}&size=16&background=random&color=fff&format=png&rounded=true`;
    };
    
    container.appendChild(img);
    container.appendChild(domainText);
    iconCache.set(domain, container);
    return container.cloneNode(true);
  }

  function addIconToElement(emailEl) {
    if (!emailEl) return;
    
    const email = emailEl.getAttribute('email');
    if (!email) return;

    // Find the target cell using the exact selector .zA > .a4W
    const parentRow = emailEl.closest('tr.zA');
    if (!parentRow) return;
    
    const targetCell = parentRow.querySelector('.a4W');
    if (!targetCell) return;
    
    // Prevent duplication: check if an icon already exists in this target cell
    if (targetCell.querySelector('.gmb-sender-icon-container')) {
        return;
    }

    const icon = createIcon(email);
    if (!icon) return;
    
    // Look for the .y6 element (following Gmail Sender Icons pattern)
    const y6Element = targetCell.querySelector('.y6');
    
    if (y6Element) {
        // Insert the icon before the .y6 element (like Gmail Sender Icons does)
        y6Element.insertAdjacentElement('beforebegin', icon);
    } else {
        // Fallback: look for .a4X and insert at beginning, or insert at cell beginning
        const a4XElement = targetCell.querySelector('.a4X');
        if (a4XElement) {
            a4XElement.insertBefore(icon, a4XElement.firstChild);
        } else {
            const firstChild = targetCell.firstChild;
            if (firstChild) {
                targetCell.insertBefore(icon, firstChild);
            } else {
                targetCell.appendChild(icon);
            }
        }
    }
    
    // Mark as processed
    parentRow.classList.add('gmb-icon-processed');
  }

  function scanForEmails() {
    if (!isEnabled) return;
    // Look for email elements in unprocessed rows
    const senderElements = document.querySelectorAll('tr.zA:not(.gmb-icon-processed) .yW span[email]');
    senderElements.forEach(addIconToElement);
  }

  function removeAllIcons() {
    document.querySelectorAll('.gmb-sender-icon-container').forEach(el => el.remove());
    document.querySelectorAll('.gmb-icon-processed').forEach(el => el.classList.remove('gmb-icon-processed'));
    // Stop the observer
    if (observer) {
      observer.disconnect();
      observer = null;
    }
    // Remove the enabling class
    disableStyling();
  }
  
  // --- Initialization and Settings ---

  async function loadSettingsAndInit() {
    try {
      const data = await chrome.storage.local.get('gmbExtractorSettings');
      const settings = data.gmbExtractorSettings || {};
      isEnabled = settings.showGmailIconsEnabled !== false;
    } catch (e) {
      isEnabled = true; // Default to on if settings fail
    }
    
    if(isEnabled) {
      run();
    }
  }

  function run() {
    // Enable the CSS styling
    enableStyling();
    
    scanForEmails();
    
    observer = new MutationObserver(() => {
        requestAnimationFrame(scanForEmails);
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  chrome.runtime.onMessage.addListener((request) => {
    if (request.action === 'updateSettings' && request.settings) {
      const newSetting = request.settings.showGmailIconsEnabled !== false;
      if (newSetting !== isEnabled) {
        isEnabled = newSetting;
        if (isEnabled) {
          run();
        } else {
          removeAllIcons();
        }
      }
    }
  });

  if (window.location.hostname === 'mail.google.com') {
      loadSettingsAndInit();
  }

})(); 