// SERP Numbering Content Script
// Adds numbered indicators to Google Search Engine Results Page headings

(function() {
    'use strict';
    
    // Configuration
    const TARGET_SELECTOR = 'h3.LC20lb.MBeuO.DKV0Md';
    let numberedElements = new Set();
    let isEnabled = true; // Default to enabled
    
    // Function to create and style the number indicator
    function createNumberIndicator(number) {
        const indicator = document.createElement('span');
        indicator.className = 'serp-number-indicator';
        indicator.textContent = number + '.';
        
        // Styling for plain number without background
        indicator.style.cssText = `
            display: inline-block;
            color: #6B7280;
            font-size: 18px;
            font-weight: 600;
            margin-left: -12px;
            position: absolute;
            left: -12px;
            bottom: 0px;
            font-family: arial, sans-serif;
            line-height: 1.3;
            z-index: 100;
        `;
        
        return indicator;
    }
    
    // Function to add numbers to SERP headings
    function addSerpNumbers() {
        if (!isEnabled) return;
        
        const headings = document.querySelectorAll(TARGET_SELECTOR);
        let counter = 1;
        
        headings.forEach((heading) => {
            // Skip if already numbered
            if (numberedElements.has(heading)) {
                return;
            }
            
            // Skip if this heading is not visible or is part of ads
            const rect = heading.getBoundingClientRect();
            if (rect.width === 0 || rect.height === 0) {
                return;
            }
            
            // Check if this is not an ad (ads typically have different parent structures)
            const searchResult = heading.closest('[data-ved]');
            if (!searchResult) {
                return;
            }
            
            // Create and prepend the number indicator
            const numberIndicator = createNumberIndicator(counter);
            
            // Make the heading container relative for absolute positioning
            heading.style.cssText = `
                position: relative !important;
            `;
            
            // Insert the number indicator at the beginning
            heading.insertBefore(numberIndicator, heading.firstChild);
            
            // Mark as numbered
            numberedElements.add(heading);
            counter++;
        });
    }
    
    // Function to remove existing numbers (for cleanup)
    function removeSerpNumbers() {
        const indicators = document.querySelectorAll('.serp-number-indicator');
        indicators.forEach(indicator => indicator.remove());
        
        // Reset heading styles
        const headings = document.querySelectorAll(TARGET_SELECTOR);
        headings.forEach(heading => {
            heading.style.position = '';
        });
        
        numberedElements.clear();
    }
    
    // Function to check if we're on a Google search results page
    function isGoogleSearchResultsPage() {
        return window.location.hostname.includes('google.') && 
               window.location.pathname === '/search' &&
               document.querySelector('#search');
    }
    
    // Initialize the numbering system
    function initializeSerpNumbering() {
        if (!isGoogleSearchResultsPage()) {
            return;
        }
        
        console.log('SERP Numbering: Initializing on Google search results page');
        
        // Load settings
        if (typeof chrome !== 'undefined' && chrome.storage) {
            chrome.storage.local.get(['gmbExtractorSettings'], function(result) {
                const settings = result.gmbExtractorSettings || {};
                isEnabled = settings.serpNumbering !== false; // Default to true
                console.log('SERP Numbering: Settings loaded, enabled:', isEnabled);
                
                if (isEnabled) {
                    // Initial numbering
                    setTimeout(() => {
                        addSerpNumbers();
                    }, 1000);
                }
            });
        } else {
            // Fallback - just run
            setTimeout(() => {
                addSerpNumbers();
            }, 1000);
        }
        
        // Watch for new content (infinite scroll, dynamic loading)
        const observer = new MutationObserver((mutations) => {
            let shouldUpdate = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if new search results were added
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.querySelector && node.querySelector(TARGET_SELECTOR)) {
                                shouldUpdate = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldUpdate) {
                // Debounce updates
                clearTimeout(window.serpNumberingTimeout);
                window.serpNumberingTimeout = setTimeout(() => {
                    addSerpNumbers();
                }, 500);
            }
        });
        
        // Observe the search results container
        const searchContainer = document.querySelector('#search') || document.querySelector('#rso');
        if (searchContainer) {
            observer.observe(searchContainer, {
                childList: true,
                subtree: true
            });
        }
        
        // Handle page navigation (back/forward)
        window.addEventListener('popstate', () => {
            setTimeout(() => {
                removeSerpNumbers();
                if (isGoogleSearchResultsPage()) {
                    addSerpNumbers();
                }
            }, 1000);
        });
        
        // Handle search form submissions
        const searchForm = document.querySelector('form[role="search"]');
        if (searchForm) {
            searchForm.addEventListener('submit', () => {
                setTimeout(() => {
                    removeSerpNumbers();
                    addSerpNumbers();
                }, 2000);
            });
        }
        
        // Start when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    addSerpNumbers();
                }, 1000);
            });
        } else {
            setTimeout(() => {
                addSerpNumbers();
            }, 1000);
        }
        
        // Also listen for URL changes (single page app navigation)
        let currentUrl = window.location.href;
        new MutationObserver(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                setTimeout(() => {
                    removeSerpNumbers();
                    if (isGoogleSearchResultsPage()) {
                        addSerpNumbers();
                    }
                }, 1000);
            }
        }).observe(document, { subtree: true, childList: true });
        
        console.log('SERP Numbering: Content script initialized');
    }
    
    // Listen for settings updates from the settings page
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'updateSettings') {
                isEnabled = message.settings.serpNumbering !== false;
                
                if (!isEnabled) {
                    // Remove existing numbers if disabled
                    removeSerpNumbers();
                    console.log('SERP Numbering: Disabled via settings update');
                } else {
                    // Re-enable and add numbers if enabled
                    console.log('SERP Numbering: Enabled via settings update');
                    if (isGoogleSearchResultsPage()) {
                        setTimeout(() => {
                            addSerpNumbers();
                        }, 500);
                    }
                }
                sendResponse({received: true});
            }
        });
    }
    
    // Initialize immediately
    initializeSerpNumbering();
    
    console.log('SERP Numbering: Content script loaded');
})(); 