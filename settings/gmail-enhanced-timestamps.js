// Gmail Enhanced Timestamps - Enhanced timestamp visibility with purple border styling
(function() {
    'use strict';
    
    // Prevent multiple loads
    if (window.GMBGmailEnhancedTimestampsLoaded) return;
    window.GMBGmailEnhancedTimestampsLoaded = true;
    
    let isEnabled = true; // Default to ON
    let debugMode = false;
    let stylesInjected = false;
    
    // Check if we're on Gmail
    function isGmailPage() {
        return window.location.hostname === 'mail.google.com';
    }
    
    // Load settings on initialization
    async function loadSettings() {
        try {
            const result = await chrome.storage.local.get('gmbExtractorSettings');
            const settings = result.gmbExtractorSettings || {};
            isEnabled = settings.gmailEnhancedTimestampsEnabled !== false; // Default to true if not set
            debugMode = settings.debugMode || false;
            
            if (debugMode) {
                console.log('Gmail Enhanced Timestamps: Settings loaded, enabled:', isEnabled);
            }
            
            if (isEnabled) {
                initializeFeature();
            } else {
                disableFeature();
            }
        } catch (error) {
            console.error('Gmail Enhanced Timestamps: Error loading settings:', error);
            // Default to enabled if error loading settings
            isEnabled = true;
            initializeFeature();
        }
    }
    
    // Initialize the feature
    function initializeFeature() {
        if (!isGmailPage() || !isEnabled) {
            return;
        }
        
        if (debugMode) {
            console.log('Gmail Enhanced Timestamps: Initializing feature');
        }
        
        // Inject enhanced timestamp styles
        injectEnhancedTimestampStyles();
        
        // Start monitoring for timestamps
        startTimestampMonitoring();
        
        // Apply enhanced styling to existing elements
        setTimeout(() => {
            applyEnhancedTimestamps();
        }, 1000);
    }
    
    // Disable the feature
    function disableFeature() {
        if (debugMode) {
            console.log('Gmail Enhanced Timestamps: Feature disabled');
        }
        
        // Remove enhanced timestamp styles
        removeEnhancedTimestampStyles();
        
        // Remove any mutation observers if they exist
        if (window.gmailEnhancedTimestampsObserver) {
            window.gmailEnhancedTimestampsObserver.disconnect();
            window.gmailEnhancedTimestampsObserver = null;
        }
    }
    
    // Inject CSS styles for enhanced timestamps
    function injectEnhancedTimestampStyles() {
        if (stylesInjected) return;
        
        const style = document.createElement('style');
        style.id = 'gmail-enhanced-timestamps-css';
        style.textContent = `
            /* Gmail Enhanced Timestamps - Subtle Purple Border */
            .ig .g3, .hI .g3, .iv .g3 {
                background: rgba(255, 255, 255, 0.95) !important;
                border: 1px solid #f0f0f0 !important;
                border-radius: 4px !important;
                padding: 4px 8px !important;
                font-size: 12px !important;
                font-weight: 500 !important;
                margin: 3px 0 !important;
                display: inline-block !important;
                max-width: fit-content !important;
                line-height:15px!important;
                white-space: nowrap !important;
                transition: all 0.2s ease !important;
                backdrop-filter: blur(5px) !important;
            }
            
            /* Subtle hover effect for enhanced timestamps */
            .ig .g3:hover, .hI .g3:hover, .iv .g3:hover {
                border-color: #8b5cf6 !important;
                background: rgba(124, 58, 237, 0.05) !important;
            }
            
            /* Dark theme compatibility */
            [data-darktheme="true"] .ig .g3, 
            [data-darktheme="true"] .hI .g3, 
            [data-darktheme="true"] .iv .g3 {
                background: rgba(40, 40, 40, 0.95) !important;
                border-color: #8b5cf6 !important;
                color: #e5e5e5 !important;
            }
            
            [data-darktheme="true"] .ig .g3:hover, 
            [data-darktheme="true"] .hI .g3:hover, 
            [data-darktheme="true"] .iv .g3:hover {
                background: rgba(124, 58, 237, 0.1) !important;
                border-color: #a78bfa !important;
            }
            
            /* Print view compatibility */
            @media print {
                .ig .g3, .hI .g3, .iv .g3 {
                    background: white !important;
                    border: 1px solid #7c3aed !important;
                    color: inherit !important;
                }
            }
        `;
        
        (document.head || document.documentElement).appendChild(style);
        stylesInjected = true;
        
        if (debugMode) {
            console.log('Gmail Enhanced Timestamps: Enhanced timestamp styles injected');
        }
    }
    
    // Remove enhanced timestamp styles
    function removeEnhancedTimestampStyles() {
        const existingStyle = document.getElementById('gmail-enhanced-timestamps-css');
        if (existingStyle) {
            existingStyle.remove();
            stylesInjected = false;
            
            if (debugMode) {
                console.log('Gmail Enhanced Timestamps: Enhanced timestamp styles removed');
            }
        }
    }
    
    // Apply enhanced styling to existing timestamp elements
    function applyEnhancedTimestamps() {
        if (!isEnabled) return;
        
        const timestampElements = document.querySelectorAll('.ig .g3, .hI .g3, .iv .g3');
        
        if (debugMode && timestampElements.length > 0) {
            console.log(`Gmail Enhanced Timestamps: Found ${timestampElements.length} timestamp elements to enhance`);
        }
        
        timestampElements.forEach((element) => {
            // Add a class to mark as processed
            if (!element.classList.contains('stm-enhanced-timestamp')) {
                element.classList.add('stm-enhanced-timestamp');
                
                if (debugMode) {
                    console.log('Gmail Enhanced Timestamps: Enhanced timestamp element:', element);
                }
            }
        });
    }
    
    // Start monitoring for new timestamps
    function startTimestampMonitoring() {
        // Use mutation observer to detect dynamically loaded content
        const observer = new MutationObserver((mutations) => {
            if (!isEnabled) return;
            
            let shouldCheckTimestamps = false;
            
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if this node or its children contain timestamp elements
                            if (node.querySelector && node.querySelector('.g3')) {
                                shouldCheckTimestamps = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldCheckTimestamps) {
                // Apply enhanced styling to new elements
                setTimeout(() => {
                    applyEnhancedTimestamps();
                }, 300);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Store observer globally for cleanup
        window.gmailEnhancedTimestampsObserver = observer;
    }
    
    // Listen for settings changes
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'updateSettings' && request.settings) {
                const newEnabled = request.settings.gmailEnhancedTimestampsEnabled !== false;
                const newDebugMode = request.settings.debugMode || false;
                
                if (newEnabled !== isEnabled) {
                    isEnabled = newEnabled;
                    debugMode = newDebugMode;
                    
                    if (isEnabled) {
                        initializeFeature();
                    } else {
                        disableFeature();
                    }
                } else if (newDebugMode !== debugMode) {
                    debugMode = newDebugMode;
                }
                
                sendResponse({ success: true });
            }
        });
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadSettings);
    } else {
        loadSettings();
    }
})(); 