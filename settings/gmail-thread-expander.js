// Gmail Thread Expander - Automatically expand conversation threads
(function() {
    'use strict';
    
    // Prevent multiple loads
    if (window.GMBGmailThreadExpanderLoaded) return;
    window.GMBGmailThreadExpanderLoaded = true;
    
    let isEnabled = true; // Default to ON
    let debugMode = false;
    let processedThreads = new Set(); // Track already processed threads
    
    // Check if we're on Gmail
    function isGmailPage() {
        return window.location.hostname === 'mail.google.com';
    }
    
    // Context validation using centralized manager
    function isExtensionContextValid() {
        return window.GMBContextManager ? window.GMBContextManager.isContextValid() : false;
    }
    
    // Safe Chrome storage wrapper using centralized context manager
    async function safeChromeStorageGet(keys) {
        if (window.GMBContextManager) {
            return await window.GMBContextManager.safeStorageGet(keys);
        }
        
        // Fallback for cases where context manager isn't loaded yet
        try {
            if (!chrome.storage || !chrome.storage.local) {
                return {};
            }
            return await chrome.storage.local.get(keys);
        } catch (error) {
            // Silent fallback - no console errors for context invalidation
            return {};
        }
    }
    
    // Load settings on initialization
    async function loadSettings() {
        try {
            const result = await safeChromeStorageGet('gmbExtractorSettings');
            const settings = result.gmbExtractorSettings || {};
            isEnabled = settings.gmailThreadExpanderEnabled !== false; // Default to true if not set
            debugMode = settings.debugMode || false;
            
            if (debugMode) {
                console.log('Gmail Thread Expander: Settings loaded, enabled:', isEnabled);
            }
            
            if (isEnabled) {
                initializeFeature();
            } else {
                disableFeature();
            }
        } catch (error) {
            // Handle context invalidation errors silently
            if (error.message && error.message.includes('Extension context invalidated')) {
                if (debugMode) {
                    console.log('Gmail Thread Expander: Context invalidated during settings load, using defaults');
                }
                return;
            }
            
            // Log other errors only in debug mode
            if (debugMode) {
                console.error('Gmail Thread Expander: Error loading settings:', error);
            }
            
            // Default to enabled if error loading settings
            isEnabled = true;
            initializeFeature();
        }
    }
    
    // Initialize the feature
    function initializeFeature() {
        if (!isGmailPage() || !isEnabled) {
            return;
        }
        
        if (debugMode) {
            console.log('Gmail Thread Expander: Initializing feature');
        }
        
        // Start monitoring for thread count buttons
        startThreadMonitoring();
        
        // Also check for existing threads on page load
        setTimeout(() => {
            expandExistingThreads();
        }, 1000);
    }
    
    // Disable the feature
    function disableFeature() {
        if (debugMode) {
            console.log('Gmail Thread Expander: Feature disabled');
        }
        
        // Clear processed threads set
        processedThreads.clear();
        
        // Remove any mutation observers if they exist
        if (window.gmailThreadExpanderObserver) {
            window.gmailThreadExpanderObserver.disconnect();
            window.gmailThreadExpanderObserver = null;
        }
    }
    


    
    // Start monitoring for new thread count buttons
    function startThreadMonitoring() {
        // Use mutation observer to detect dynamically loaded content
        const observer = new MutationObserver((mutations) => {
            if (!isEnabled) return;
            
            let shouldCheckThreads = false;
            
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if this node or its children contain thread count buttons
                            if (node.querySelector && (node.querySelector('.adx') || node.classList.contains('adx'))) {
                                shouldCheckThreads = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldCheckThreads) {
                // Debounce to avoid excessive calls
                setTimeout(() => {
                    expandExistingThreads();
                }, 500);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Store observer globally for cleanup
        window.gmailThreadExpanderObserver = observer;
    }
    
    // Find and expand existing threads
    function expandExistingThreads() {
        if (!isEnabled) return;
        
        const threadCountButtons = document.querySelectorAll('.adx');
        
        if (debugMode && threadCountButtons.length > 0) {
            console.log(`Gmail Thread Expander: Found ${threadCountButtons.length} thread count buttons`);
        }
        
        threadCountButtons.forEach((button, index) => {
            // Create unique identifier for this thread
            const threadId = getThreadIdentifier(button);
            
            // Skip if already processed
            if (processedThreads.has(threadId)) {
                return;
            }
            
            // Mark as processed
            processedThreads.add(threadId);
            
            // Delay each thread expansion to avoid overwhelming Gmail
            setTimeout(() => {
                expandThread(button);
            }, index * 200); // 200ms delay between each thread
        });
    }
    
    // Create unique identifier for a thread
    function getThreadIdentifier(button) {
        // Use multiple attributes to create a unique identifier
        const parentElement = button.closest('[data-thread-id]') || button.closest('.zA');
        if (parentElement) {
            const threadId = parentElement.getAttribute('data-thread-id');
            if (threadId) return threadId;
        }
        
        // Fallback: use position and text content
        const rect = button.getBoundingClientRect();
        const text = button.textContent.trim();
        return `${rect.top}-${rect.left}-${text}`;
    }
    
    // Expand a single thread
    function expandThread(threadCountButton) {
        if (!isEnabled || !threadCountButton) return;
        
        try {
            if (debugMode) {
                console.log('Gmail Thread Expander: Clicking thread count button:', threadCountButton);
            }
            
            // Click the thread count button to expand the thread
            threadCountButton.click();
            
            // Expand individual messages after a short delay
            setTimeout(() => {
                expandIndividualMessages();
            }, 150);
            
        } catch (error) {
            console.error('Gmail Thread Expander: Error expanding thread:', error);
        }
    }
    
    // Expand individual messages within the thread
    function expandIndividualMessages() {
        if (!isEnabled) return;
        
        // Find individual message elements (.hI and .ig classes)
        const messageElements = document.querySelectorAll('.hI, .ig');
        
        if (debugMode && messageElements.length > 0) {
            console.log(`Gmail Thread Expander: Found ${messageElements.length} individual messages to expand`);
        }
        
        if (messageElements.length === 0) {
            // No messages to expand, trigger scroll immediately
            if (debugMode) {
                console.log('Gmail Thread Expander: No messages to expand, triggering scroll');
            }
            setTimeout(() => {
                scrollToFirstMessage();
            }, 200);
            return;
        }
        
        // Remember the first message element to scroll back to it later
        const firstMessage = messageElements[0];
        if (debugMode) {
            console.log('Gmail Thread Expander: Remembering first message for scroll target:', firstMessage);
        }
        
        let expandedCount = 0;
        const totalMessages = messageElements.length;
        
        messageElements.forEach((messageElement, index) => {
            // Delay each message expansion to avoid overwhelming Gmail
            setTimeout(() => {
                try {
                    if (debugMode) {
                        console.log(`Gmail Thread Expander: Expanding message ${index + 1}/${totalMessages}:`, messageElement);
                    }
                    
                    messageElement.click();
                    expandedCount++;
                    
                    // Check if this was the last message
                    if (expandedCount === totalMessages) {
                        if (debugMode) {
                            console.log('Gmail Thread Expander: All expansions complete, scrolling to first message');
                        }
                        // Wait a short delay for Gmail to process the last click, then scroll to first message
                        setTimeout(() => {
                            scrollToFirstMessage(firstMessage);
                        }, 300); // Short delay after last message expansion
                    }
                    
                } catch (error) {
                    console.error('Gmail Thread Expander: Error expanding message:', error);
                    // Still count as expanded to prevent hanging
                    expandedCount++;
                    if (expandedCount === totalMessages) {
                        setTimeout(() => {
                            scrollToFirstMessage(firstMessage);
                        }, 300);
                    }
                }
            }, index * 100); // 100ms delay between each message
        });
    }
    
    // Scroll to the first message (but only when reverse order is NOT enabled)
    async function scrollToFirstMessage(firstMessageElement = null) {
        if (!isEnabled) return;
        
        try {
            // Check if extension context is valid before storage access
            if (!isExtensionContextValid()) {
                if (debugMode) {
                    console.log('Gmail Thread Expander: Context invalidated, skipping scroll');
                }
                return;
            }
            
            // Check if reverse order is also enabled using safe storage wrapper
            const result = await safeChromeStorageGet('gmbExtractorSettings');
            const settings = result.gmbExtractorSettings || {};
            const reverseOrderEnabled = settings.reverseGmailOrderEnabled !== false;
            
            // Skip scrolling entirely if both features are enabled
            if (reverseOrderEnabled) {
                if (debugMode) {
                    console.log('Gmail Thread Expander: Both features enabled, skipping scroll (letting reverse order handle default behavior)');
                }
                return;
            }
            
            // Only scroll if thread expander is the only feature enabled
            if (firstMessageElement && firstMessageElement.parentNode) {
                firstMessageElement.scrollIntoView({ behavior: 'instant', block: 'start' });
                
                if (debugMode) {
                    console.log('Gmail Thread Expander: Scrolled to first message element:', firstMessageElement);
                }
                return;
            }
            
            // Fallback: Find the first message if not provided
            const firstMessage = document.querySelector('.hI, .ig');
            if (firstMessage) {
                firstMessage.scrollIntoView({ behavior: 'instant', block: 'start' });
                
                if (debugMode) {
                    console.log('Gmail Thread Expander: Scrolled to first message (fallback):', firstMessage);
                }
                return;
            }
            
            // Final fallback: Look for Gmail 3-dot menu (always at top)
            const topElement = document.querySelector('.asa');
            if (topElement) {
                topElement.scrollIntoView({ behavior: 'instant', block: 'start' });
                
                if (debugMode) {
                    console.log('Gmail Thread Expander: Scrolled to top using .asa element');
                }
                return;
            }
            
            if (debugMode) {
                console.log('Gmail Thread Expander: No elements found to scroll to');
            }
        } catch (error) {
            // Handle context invalidation errors silently
            if (error.message && error.message.includes('Extension context invalidated')) {
                if (debugMode) {
                    console.log('Gmail Thread Expander: Context invalidated during scroll, ignoring');
                }
                return;
            }
            
            // Log other errors only in debug mode
            if (debugMode) {
                console.error('Gmail Thread Expander: Error during scroll to first message:', error);
            }
        }
    }
    
    // Listen for settings changes with context validation
    if (isExtensionContextValid() && typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            try {
                // Validate context on each message
                if (!isExtensionContextValid()) {
                    sendResponse({ success: false, error: 'Extension context invalidated' });
                    return;
                }
                
                if (request.action === 'updateSettings' && request.settings) {
                    const newEnabled = request.settings.gmailThreadExpanderEnabled !== false;
                    const newDebugMode = request.settings.debugMode || false;
                    
                    if (newEnabled !== isEnabled) {
                        isEnabled = newEnabled;
                        debugMode = newDebugMode;
                        
                        if (isEnabled) {
                            initializeFeature();
                        } else {
                            disableFeature();
                        }
                    } else if (newDebugMode !== debugMode) {
                        debugMode = newDebugMode;
                    }
                    
                    sendResponse({ success: true });
                }
            } catch (error) {
                // Silent error handling for context invalidation
                sendResponse({ success: false, error: error.message });
            }
        });
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadSettings);
    } else {
        loadSettings();
    }
})(); 