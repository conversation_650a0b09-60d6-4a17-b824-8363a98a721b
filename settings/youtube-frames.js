// YouTube Frames - Capture framed screenshots from YouTube videos
(function() {
    'use strict';
    
    // Embedded YouTube frame SVG data
    const YOUTUBE_FRAME_SVG = `<svg width="1200" height="787" viewBox="0 0 900 590.25" xmlns="http://www.w3.org/2000/svg"><g fill="#fcfcfc"><path d="M0 0h900v5.527a125 125 0 0 1-.69-4.988C657.037.555 414.772.54 172.5.547 114.998.6 57.495.435 0 .63zm424.643 256.118a33818 33818 0 0 0 67.305 38.858c-22.388 13.035-44.88 25.898-67.29 38.902-.022-25.92.015-51.84-.015-77.76m-355.861 249.3c247.08-.015 494.16 0 741.24-.007 25.523.098 51.052-.195 76.575.15q-.011 3.87 0 7.74c-272.61.038-545.22-.007-817.822.022 0-2.64 0-5.272.007-7.905m619.725 28.74c1.2.015 2.415.015 3.63.135.555 1.47.345 3.188 1.035 4.598 1.545.758 3.165 1.357 4.732 2.1 1.35-.93 2.7-1.86 4.05-2.805.847.922 1.71 1.845 2.573 2.768-.953 1.222-1.912 2.445-2.857 3.667a61 61 0 0 1 2.145 5.115c1.567.262 3.12.503 4.71.758q-.047 1.911-.083 3.855c-1.56.18-3.098.367-4.643.563a51 51 0 0 1-2.085 4.988c.9 1.35 1.823 2.692 2.752 4.035-.892.855-1.778 1.718-2.647 2.595a262 262 0 0 0-3.795-2.828c-1.612.735-3.277 1.35-4.867 2.107-.593 1.5-.72 3.135-1.02 4.718-1.26-.03-2.52-.045-3.765-.06-.217-1.575-.435-3.143-.652-4.695a52 52 0 0 1-5.048-2.07c-1.313.93-2.61 1.868-3.915 2.805-.87-.922-1.748-1.838-2.61-2.745a228 228 0 0 1 2.843-3.683 59 59 0 0 1-2.078-5.077c-1.597-.285-3.202-.563-4.792-.862.022-1.222.03-2.445.173-3.652 1.492-.383 3.045-.503 4.583-.72a61 61 0 0 1 2.093-5.07q-1.395-1.971-2.79-3.93c.907-.877 1.823-1.755 2.737-2.625 1.237.96 2.482 1.92 3.735 2.888a43 43 0 0 1 5.002-2.07c.292-1.605.578-3.202.855-4.8m.315 11.512c-4.688.833-6.945 7.027-3.998 10.732 2.828 4.275 10.17 3.405 11.857-1.455 2.25-4.867-2.715-10.68-7.86-9.277m-81.93-10.68c1.643-.525 3.405-.315 5.107-.36 8.79.105 17.587-.112 26.378.105 1.627-.075 2.677 1.47 2.445 3.007.015 9.75.03 19.508 0 29.258.21 1.5-.862 3.113-2.498 2.88-10.035.06-20.078.045-30.112.007-1.35.188-2.685-.825-2.588-2.228-.135-9.712 0-19.433-.068-29.152.022-1.245-.128-3.007 1.335-3.518m5.205 12.607c-1.432 2.558-1.163 5.843-.458 8.587 1.755 4.147 7.598 3.893 10.545 1.193a234 234 0 0 0-1.905-1.995c-1.515.81-3.375 1.935-5.04.81-1.912-1.522-1.373-4.478-.975-6.57 1.14-2.4 4.192-1.545 5.978-.405.645-.615 1.297-1.222 1.95-1.83-2.715-2.535-7.748-3.037-10.095.21m12.832.75c-.788 2.647-.885 5.715.18 8.303 2.07 3.713 7.425 3.277 10.283.742-.458-.495-1.38-1.485-1.838-1.98-1.657.825-3.78 2.01-5.43.517-1.455-1.748-1.163-4.463-.532-6.48 1.245-2.243 4.14-1.305 5.895-.262q.956-.898 1.935-1.785c-2.85-2.79-8.595-3.037-10.492.945m114.9-13.508c9.63-.36 19.275-.075 28.912-.173 8.933.09 17.872-.18 26.797.15v35.063c-18.57 0-37.14.007-55.703 0 0-11.685.007-23.362-.007-35.04m3.113 3.022c0 9.668-.038 19.328.015 28.995 16.5-.068 33.008-.03 49.508-.022 0-9.652-.007-19.305.007-28.965-16.515.007-33.023.03-49.53-.007m83.88-3.03c4.313-.007 8.633-.022 12.953 0a204 204 0 0 0-.105 3.083c-3.24-.015-6.48 0-9.705-.03-.015 3.277-.007 6.555-.03 9.832-.772 0-2.325-.007-3.098-.007-.022-4.298-.007-8.587-.015-12.878m22.357-.105c4.268 0 8.543.135 12.817-.06.03 4.35.007 8.7.007 13.05q-1.561-.022-3.09-.022c-.007-3.262-.007-6.533-.007-9.795q-4.894.009-9.765.007c0-1.065.007-2.123.038-3.18m-298.515 8.58c2.19-1.703 4.47-3.48 7.26-4.02 5.58-1.388 11.813 1.403 14.587 6.413 2.752 4.657 2.228 10.973-1.26 15.105-3.24 4.05-8.955 5.94-13.95 4.447-2.58-.563-4.492-2.49-6.728-3.743-4.522-.255-9.053.038-13.575-.098-1.748.083-3.248-.922-4.792-1.56-1.125-1.32-2.482-2.573-3.022-4.245-.323-2.317-.128-4.665-.112-6.982l.907.038c-.06-.367-.188-1.095-.247-1.462l.555-.15c1.5-2.183 4.027-3.75 6.712-3.735 4.553-.06 9.105.007 13.665-.007m6.877 3.397c-.06 3.795-.068 7.59 0 11.385 3.292-1.838 6.548-3.735 9.81-5.633-3.18-2.07-6.51-3.893-9.81-5.752m-526.715-4.659c1.388-.007 2.775-.022 4.162-.015.038 6.735.007 13.47.022 20.212-1.395 0-2.782-.007-4.178.007-.007-6.735.007-13.47-.007-20.205m57.082-.007q2.093-.011 4.185 0c-.007 6.735 0 13.47 0 20.205q-2.093-.011-4.185 0 .011-10.103 0-20.205m9.457 0q2.115-.011 4.23 0c0 6.735-.007 13.47 0 20.205-1.41-.007-2.828-.015-4.238-.015.007-6.728 0-13.462.007-20.19m37.965.112a4078 4078 0 0 1 17.303 9.982c-5.715 3.45-11.565 6.675-17.31 10.08q-.024-10.037.007-20.063m19.095-.098c1.41-.015 2.828-.022 4.245-.022.015 6.735 0 13.477.007 20.212-1.41-.007-2.828-.007-4.238-.007-.038-6.728-.007-13.455-.015-20.183m51.135.3c1.328-.443 2.205.915 2.018 2.115-.038 5.258-.165 10.53-.323 15.788.247 1.343-1.057 2.782-2.385 2.032-3-1.545-5.49-3.907-8.467-5.49-1.838-.622-3.877-.083-5.7-.712-1.065-1.035-.622-2.723-.772-4.042.18-1.59-.45-3.667.975-4.815 2.145-.427 4.35-.068 6.51-.307 2.888-1.208 5.228-3.45 8.145-4.567m10.965-.78c5.902 4.748 5.73 14.535.608 19.83-.63.96-1.808.99-2.835 1.028 2.91-3.555 5.865-7.762 4.935-12.638-.015-3.053-3.007-5.197-2.708-8.22m91.035 2.243c.383-1.275 1.282-2.07 2.7-2.01-2.647 6.428-5.595 12.727-8.332 19.11-.367 1.23-1.365 1.89-2.647 2.04 2.513-6.48 5.572-12.735 8.28-19.14m-270.771 8.317c5.82-3.292 11.572-6.69 17.385-9.99.022 6.697.007 13.388.007 20.085-5.768-3.412-11.625-6.683-17.393-10.095m218.572-7.83c1.845-.765 4.327-.682 5.73.907 1.522 1.605 1.755 3.945 1.882 6.06.03 2.505 0 5.205-1.29 7.44-1.643 2.888-6.473 2.895-8.115 0-1.403-2.468-1.357-5.445-1.215-8.19.158-2.325.78-5.032 3.007-6.217m1.297 1.808c-2.025 1.808-1.417 4.95-1.545 7.395.06 1.98.165 5.317 2.843 5.393 2.377-.998 2.063-4.05 2.213-6.158-.158-2.34.398-5.662-2.093-6.945a96 96 0 0 1-1.417.315m17.438-1.905c2.175-.833 5.145-.3 6.217 1.987 1.56 3.188 1.373 6.975.72 10.387-.413 2.37-2.475 4.522-5.018 4.268-3.48.18-5.19-3.645-5.145-6.607-.112-3.458-.39-8.19 3.225-10.035m-.495 6.33c-.045 3.022-.517 6.87 2.407 8.783 3.21-1.695 2.655-5.745 2.632-8.79-.12-1.838-.36-4.62-2.723-4.815-2.025.622-2.167 3.067-2.317 4.822m10.297-3.015c.608-4.875 9.225-5.025 9.78-.06.427 1.815-1.057 3.083-2.183 4.23 1.44 1.23 3.135 2.745 2.708 4.875-.503 5.37-9.803 5.955-10.883.682-.547-2.167 1.042-3.9 2.655-5.048-1.208-1.268-2.58-2.775-2.078-4.68m2.632.015c-.405 1.688 1.343 2.558 2.333 3.555 1.087-.998 2.888-1.875 2.468-3.652-.532-2.348-4.56-2.377-4.8.098m-.75 8.978c.053 3.232 5.723 3.232 5.813.022.075-2.04-2.205-2.67-3.585-3.563-.93 1.02-2.55 1.89-2.228 3.54m30.922-8.813c1.005-1.583 2.093-3.6 4.2-3.757 2.453-.578 5.58.712 5.88 3.458.503 1.845-.765 3.322-1.958 4.537 2.498 1.305 3.322 4.673 1.59 6.945-2.73 3.51-9.428 2.228-9.99-2.46.54-.105 1.62-.315 2.152-.42.833 1.11 1.478 2.91 3.202 2.662 2.28.255 3.322-2.662 2.31-4.387-.818-1.425-2.67-1.073-4.02-1.403 0-.42 0-1.268-.007-1.688 1.275-.338 3.285.128 3.803-1.485 1.073-1.875-.6-3.518-2.16-4.268a16 16 0 0 0-2.76 2.768c-.75-.173-1.5-.338-2.243-.503m34.657-3.48c2.175-.847 5.123-.338 6.232 1.92 1.575 3.195 1.395 7.012.742 10.433-.413 2.4-2.513 4.575-5.085 4.275-3.435.135-5.093-3.637-5.085-6.563-.128-3.465-.375-8.168 3.195-10.065m-.45 6.255c-.09 3.045-.465 6.788 2.317 8.842 3.368-1.627 2.723-5.827 2.708-8.91-.098-1.838-.435-4.537-2.768-4.71-1.965.705-2.093 3.022-2.257 4.777m-14.235-6.337q4.318-.031 8.647-.007l-.36 2.37q-3.096.002-6.18-.007a471 471 0 0 1-.315 4.35c1.275-.772 2.692-1.763 4.26-1.185 5.265 1.567 4.192 11.137-1.478 11.16-2.737.48-4.853-1.56-5.947-3.825.585-.247 1.748-.75 2.333-.998.78.983 1.297 2.662 2.843 2.595 3.697.488 4.095-6.038.855-6.855-1.41-.255-2.34.9-3.308 1.68-.458-.06-1.38-.188-1.838-.247.188-3.015.352-6.022.488-9.03"/><path d="M212.798 545.153c1.335-1.515 1.897.81 2.573 1.56 1.868 3.345 1.763 7.755-.435 10.935-.758 1.013-1.575 2.355-3.015 2.34-.352-2.025 2.093-3.173 2.558-5.04 1.522-3.353-.135-6.817-1.68-9.795m52.725 1.125c.915 0 1.83 0 2.752.015-.015 1.005-.015 2.025-.015 3.045q-1.328.032-2.632.098c-.045-1.065-.075-2.115-.105-3.157m64.672.015q1.361-.022 2.745-.022c-.022 1.042-.053 2.093-.075 3.15q-1.328-.056-2.655-.068c-.007-1.028-.015-2.047-.015-3.06"/><path d="M211.178 552.675c.488-1.725-1.987-3.705.075-4.973 2.28 2.865 2.265 7.732-1.492 9.353-.09-1.62.93-2.925 1.417-4.38m54.36 2.655c.907 0 1.823.007 2.737.03-.015 1.005-.015 2.018-.015 3.037-.885.022-1.77.045-2.647.083-.038-1.065-.06-2.115-.075-3.15m64.657.015q1.361-.011 2.745 0a436 436 0 0 0-.053 3.105c-.892-.022-1.785-.03-2.677-.038-.007-1.028-.015-2.055-.015-3.067m496.627 14.535c0-4.29 0-8.572.007-12.862h3.12c0 3.27 0 6.533.007 9.803 3.248-.03 6.488-.015 9.742-.03.007 1.02.022 2.047.053 3.083-4.313.015-8.625.007-12.93.007m32.078-12.848c1.028-.015 2.063-.015 3.113-.015-.007 4.29 0 8.58 0 12.87-4.298-.007-8.587 0-12.878-.007q-.011-1.552 0-3.083c3.262.007 6.525.007 9.788-.03-.022-3.248-.015-6.495-.022-9.735M0 588.795c3.99.345 7.995.277 11.992.27 296.002-.007 592.005.007 888.007-.007v1.192H0z"/></g><g fill="#292d2f"><path d="M0 .63C57.495.435 114.998.6 172.5.547 414.772.54 657.037.555 899.31.54q.294 2.5.69 4.988v583.53c-296.002.015-592.005 0-888.007.007-3.998.007-8.002.075-11.992-.27zm9.06 9.345c-.12 96.42.068 192.862-.083 289.29.315 11.752-.292 23.505.075 35.258-.053 14.482.022 28.965-.007 43.447-.345 14.265.225 28.53-.053 42.795.075 25.027-.09 50.055.112 75.082 293.377-.03 586.748.045 880.125-.038.03-161.947-.007-323.888.015-485.835zm3.42 495.435c-.022 2.64-.022 5.28-.015 7.928 18.765-.007 37.537.022 56.31-.015 272.603-.03 545.213.015 817.822-.022q-.011-3.87 0-7.74c-25.523-.345-51.052-.053-76.575-.15-247.08.007-494.16-.007-741.24.007-18.765-.022-37.53 0-56.302-.007m676.028 28.747a593 593 0 0 1-.855 4.8 43 43 0 0 0-5.002 2.07c-1.252-.968-2.498-1.927-3.735-2.888-.915.87-1.83 1.748-2.737 2.625.93 1.305 1.86 2.618 2.79 3.93a61 61 0 0 0-2.093 5.07c-1.537.217-3.09.338-4.583.72-.143 1.208-.15 2.43-.173 3.652 1.59.3 3.195.578 4.792.862a58 58 0 0 0 2.078 5.077 229 229 0 0 0-2.843 3.683c.862.907 1.74 1.823 2.61 2.745 1.305-.938 2.603-1.875 3.915-2.805a52 52 0 0 0 5.048 2.07c.217 1.552.435 3.12.652 4.695 1.245.015 2.505.03 3.765.06.3-1.583.427-3.218 1.02-4.718 1.59-.758 3.255-1.373 4.867-2.107 1.268.922 2.535 1.875 3.795 2.828q1.307-1.314 2.647-2.595a275 275 0 0 1-2.752-4.035 50 50 0 0 0 2.085-4.988c1.545-.195 3.083-.383 4.643-.563.022-1.297.053-2.58.083-3.855-1.59-.255-3.143-.495-4.71-.758a61 61 0 0 0-2.145-5.115c.945-1.222 1.905-2.445 2.857-3.667-.862-.922-1.725-1.845-2.573-2.768-1.35.945-2.7 1.875-4.05 2.805-1.567-.742-3.188-1.343-4.732-2.1-.69-1.41-.48-3.127-1.035-4.598-1.215-.12-2.43-.12-3.63-.135m-81.615.833c-1.462.51-1.313 2.272-1.335 3.518.068 9.72-.068 19.44.068 29.152-.098 1.403 1.237 2.415 2.588 2.228 10.035.038 20.078.053 30.112-.007 1.635.232 2.708-1.38 2.498-2.88.03-9.75.015-19.508 0-29.258.232-1.537-.818-3.083-2.445-3.007-8.79-.217-17.587 0-26.378-.105-1.703.045-3.465-.165-5.107.36m132.938-.15c.015 11.678.007 23.355.007 35.04 18.563.007 37.133 0 55.703 0v-35.063c-8.925-.33-17.865-.06-26.797-.15-9.637.098-19.282-.188-28.912.173m86.992-.007c.007 4.29-.007 8.58.015 12.878.772 0 2.325.007 3.098.007.022-3.277.015-6.555.03-9.832 3.225.03 6.465.015 9.705.03.03-1.035.06-2.063.105-3.083-4.32-.022-8.64-.007-12.953 0m22.357-.105c-.03 1.057-.038 2.115-.038 3.18q4.871.002 9.765-.007c0 3.262 0 6.533.007 9.795 1.02 0 2.047.007 3.09.022 0-4.35.022-8.7-.007-13.05-4.275.195-8.55.06-12.817.06m-298.515 8.58c-4.56.015-9.113-.053-13.665.007-2.685-.015-5.213 1.552-6.712 3.735l-.555.15c.06.367.188 1.095.247 1.462l-.907-.038c-.015 2.317-.21 4.665.112 6.982.54 1.672 1.897 2.925 3.022 4.245 1.545.637 3.045 1.643 4.792 1.56 4.522.135 9.053-.158 13.575.098 2.235 1.252 4.147 3.18 6.728 3.743 4.995 1.492 10.71-.398 13.95-4.447 3.488-4.133 4.012-10.447 1.26-15.105-2.775-5.01-9.008-7.8-14.587-6.413-2.79.54-5.07 2.317-7.26 4.02m-519.841-1.258c.015 6.735 0 13.47.007 20.205 1.395-.015 2.782-.007 4.178-.007-.015-6.742.015-13.477-.022-20.212-1.388-.007-2.775.007-4.162.015m57.082-.007q.011 10.103 0 20.205 2.093-.011 4.185 0c0-6.735-.007-13.47 0-20.205q-2.093-.011-4.185 0m9.457 0c-.007 6.728 0 13.462-.007 20.19 1.41 0 2.828.007 4.238.015-.007-6.735 0-13.47 0-20.205q-2.115-.011-4.23 0m37.965.112q-.031 10.026-.007 20.063c5.745-3.405 11.595-6.63 17.31-10.08a3817 3817 0 0 0-17.303-9.982m19.095-.098c.007 6.728-.022 13.455.015 20.183 1.41 0 2.828 0 4.238.007-.007-6.735.007-13.477-.007-20.212-1.417 0-2.835.007-4.245.022m51.135.3c-2.917 1.117-5.258 3.36-8.145 4.567-2.16.24-4.365-.12-6.51.307-1.425 1.147-.795 3.225-.975 4.815.15 1.32-.292 3.007.772 4.042 1.823.63 3.863.09 5.7.712 2.978 1.583 5.468 3.945 8.467 5.49 1.328.75 2.632-.69 2.385-2.032.158-5.258.285-10.53.323-15.788.188-1.2-.69-2.558-2.018-2.115m10.965-.78c-.3 3.022 2.692 5.167 2.708 8.22.93 4.875-2.025 9.082-4.935 12.638 1.028-.038 2.205-.068 2.835-1.028 5.123-5.295 5.295-15.082-.608-19.83m91.035 2.243c-2.708 6.405-5.768 12.66-8.28 19.14 1.282-.15 2.28-.81 2.647-2.04 2.737-6.383 5.685-12.683 8.332-19.11-1.417-.06-2.317.735-2.7 2.01m-270.773 8.317c5.768 3.412 11.625 6.683 17.393 10.095 0-6.697.015-13.388-.007-20.085-5.813 3.3-11.565 6.697-17.385 9.99m218.572-7.83c-2.228 1.185-2.85 3.893-3.007 6.217-.143 2.745-.188 5.723 1.215 8.19 1.643 2.895 6.473 2.888 8.115 0 1.29-2.235 1.32-4.935 1.29-7.44-.128-2.115-.36-4.455-1.882-6.06-1.403-1.59-3.885-1.672-5.73-.907m18.735-.098c-3.615 1.845-3.338 6.577-3.225 10.035-.045 2.963 1.665 6.788 5.145 6.607 2.542.255 4.605-1.897 5.018-4.268.652-3.412.84-7.2-.72-10.387-1.073-2.287-4.042-2.82-6.217-1.987m9.803 3.315c-.503 1.905.87 3.412 2.078 4.68-1.612 1.147-3.202 2.88-2.655 5.048 1.08 5.272 10.38 4.688 10.883-.682.427-2.13-1.268-3.645-2.708-4.875 1.125-1.147 2.61-2.415 2.183-4.23-.555-4.965-9.172-4.815-9.78.06m32.805.18q1.115.245 2.243.503a16 16 0 0 1 2.76-2.768c1.56.75 3.232 2.393 2.16 4.268-.517 1.612-2.527 1.147-3.803 1.485.007.42.007 1.268.007 1.688 1.35.33 3.202-.022 4.02 1.403 1.013 1.725-.03 4.643-2.31 4.387-1.725.247-2.37-1.552-3.202-2.662-.532.105-1.612.315-2.152.42.563 4.688 7.26 5.97 9.99 2.46 1.732-2.272.907-5.64-1.59-6.945 1.193-1.215 2.46-2.692 1.958-4.537-.3-2.745-3.428-4.035-5.88-3.458-2.107.158-3.195 2.175-4.2 3.757m34.657-3.48c-3.57 1.897-3.322 6.6-3.195 10.065-.007 2.925 1.65 6.697 5.085 6.563 2.573.3 4.673-1.875 5.085-4.275.652-3.42.833-7.238-.742-10.433-1.11-2.257-4.058-2.768-6.232-1.92m-14.685-.083a519 519 0 0 1-.488 9.03c.458.06 1.38.188 1.838.247.968-.78 1.897-1.935 3.308-1.68 3.24.818 2.843 7.342-.855 6.855-1.545.068-2.063-1.612-2.843-2.595-.585.247-1.748.75-2.333.998 1.095 2.265 3.21 4.305 5.947 3.825 5.67-.022 6.742-9.592 1.478-11.16-1.567-.578-2.985.413-4.26 1.185.112-1.455.217-2.902.315-4.35q3.084.009 6.18.007l.36-2.37a674 674 0 0 0-8.647.007m-123.877 1.013c1.545 2.978 3.202 6.442 1.68 9.795-.465 1.868-2.91 3.015-2.558 5.04 1.44.015 2.257-1.328 3.015-2.34 2.198-3.18 2.302-7.59.435-10.935-.675-.75-1.237-3.075-2.573-1.56m52.725 1.125c.03 1.042.06 2.093.105 3.157q1.305-.066 2.632-.098c0-1.02 0-2.04.015-3.045-.922-.015-1.838-.015-2.752-.015m64.672.015q.001 1.519.015 3.06 1.328.011 2.655.068c.022-1.057.053-2.107.075-3.15q-1.384 0-2.745.022m-119.017 6.383c-.488 1.455-1.507 2.76-1.417 4.38 3.757-1.62 3.772-6.488 1.492-9.353-2.063 1.268.413 3.248-.075 4.973m54.36 2.655q.021 1.553.075 3.15c.877-.038 1.763-.06 2.647-.083 0-1.02 0-2.032.015-3.037q-1.375-.032-2.737-.03m64.657.015c0 1.013.007 2.04.015 3.067.892.007 1.785.015 2.677.038.015-1.05.03-2.078.053-3.105q-1.384-.011-2.745 0m496.627 14.535c4.305 0 8.617.007 12.93-.007-.03-1.035-.045-2.063-.053-3.083-3.255.015-6.495 0-9.742.03-.007-3.27-.007-6.533-.007-9.803h-3.12c-.007 4.29-.007 8.572-.007 12.862m32.078-12.848c.007 3.24 0 6.488.022 9.735a694 694 0 0 1-9.788.03q-.011 1.53 0 3.083c4.29.007 8.58 0 12.878.007 0-4.29-.007-8.58 0-12.87-1.05 0-2.085 0-3.113.015"/><path d="M443.183 216.45c23.7-2.243 48.21 6.982 64.425 24.457 10.658 11.295 17.865 25.815 20.303 41.16 2.85 17.317-.315 35.557-8.902 50.865-10.965 19.83-30.885 34.41-53.13 38.752-7.433 1.537-15.075 1.763-22.635 1.335 15.825.998 31.95-2.782 45.555-10.965 20.692-12.203 35.145-34.373 37.718-58.282 2.963-23.828-5.925-48.713-23.25-65.325-11.587-11.295-26.753-18.87-42.765-21.285-5.715-1.252-11.543-.443-17.317-.712m299.76 321.413c16.508.038 33.015.015 49.53.007-.015 9.66-.007 19.313-.007 28.965-16.5-.007-33.008-.045-49.508.022-.053-9.668-.015-19.328-.015-28.995M612.1 547.597c2.348-3.248 7.38-2.745 10.095-.21-.652.608-1.305 1.215-1.95 1.83-1.785-1.14-4.838-1.995-5.978.405-.398 2.093-.938 5.048.975 6.57 1.665 1.125 3.525 0 5.04-.81.63.652 1.268 1.32 1.905 1.995-2.948 2.7-8.79 2.955-10.545-1.193-.705-2.745-.975-6.03.458-8.587m12.832.75c1.897-3.982 7.643-3.735 10.492-.945-.652.593-1.297 1.185-1.935 1.785-1.755-1.042-4.65-1.98-5.895.262-.63 2.018-.922 4.732.532 6.48 1.65 1.492 3.772.307 5.43-.517.458.495 1.38 1.485 1.838 1.98-2.857 2.535-8.212 2.97-10.283-.742-1.065-2.588-.968-5.655-.18-8.303m63.892-2.677c5.145-1.403 10.11 4.41 7.86 9.277-1.688 4.86-9.03 5.73-11.857 1.455-2.948-3.705-.69-9.9 3.998-10.732m-432.165.443a96 96 0 0 0 1.417-.315c2.49 1.282 1.935 4.605 2.093 6.945-.15 2.107.165 5.16-2.213 6.158-2.677-.075-2.782-3.412-2.843-5.393.128-2.445-.48-5.588 1.545-7.395m16.942 4.425c.15-1.755.292-4.2 2.317-4.822 2.362.195 2.603 2.978 2.723 4.815.022 3.045.578 7.095-2.632 8.79-2.925-1.912-2.453-5.76-2.407-8.783m12.93-3c.24-2.475 4.268-2.445 4.8-.098.42 1.778-1.38 2.655-2.468 3.652-.99-.998-2.737-1.868-2.333-3.555m64.38 2.94c.165-1.755.292-4.072 2.257-4.777 2.333.173 2.67 2.873 2.768 4.71.015 3.083.66 7.283-2.708 8.91-2.782-2.055-2.407-5.798-2.317-8.842m206.632-3.772c3.3 1.86 6.63 3.683 9.81 5.752-3.262 1.897-6.518 3.795-9.81 5.633-.068-3.795-.06-7.59 0-11.385m-271.763 9.81c-.323-1.65 1.297-2.52 2.228-3.54 1.38.892 3.66 1.522 3.585 3.563-.09 3.21-5.76 3.21-5.813-.022"/></g><path d="M387.66 245.22c13.462-16.703 34.065-27.57 55.523-28.77 5.775.27 11.603-.54 17.317.712 16.013 2.415 31.178 9.99 42.765 21.285 17.325 16.612 26.213 41.498 23.25 65.325-2.573 23.91-17.025 46.08-37.718 58.282-13.605 8.183-29.73 11.962-45.555 10.965-24.15-1.462-47.123-14.962-60.27-35.25-8.512-12.885-13.035-28.343-12.848-43.777.075-17.625 6.375-35.13 17.535-48.773m36.983 10.897c.03 25.92-.007 51.84.015 77.76 22.41-13.005 44.902-25.867 67.29-38.902a33060 33060 0 0 1-67.305-38.858M12.48 505.41c18.773.007 37.537-.015 56.302.007q-.009 3.947-.007 7.905c-18.773.038-37.545.007-56.31.015-.007-2.647-.007-5.287.015-7.928" fill="#ee302d"/></svg>`;
    
    let isEnabled = true; // Default to true
    let debugMode = false;
    
    // YouTube-specific elements
    let f2cButton = null;
    let f2dButton = null;
    let areButtonsAppended = false;
    let framesObserver = null;
    
    // Check if we're on a YouTube watch page
    function isYouTubePage() {
        return window.location.hostname.includes('youtube.com') && 
               window.location.pathname.includes('/watch');
    }
    
    // Load settings from localStorage (MAIN world doesn't have Chrome API access)
    function loadSettings() {
        try {
            const storedEnabled = localStorage.getItem('youtubeFramesEnabled');
            const storedDebug = localStorage.getItem('debugMode');
            
            // Explicit boolean checks - only default to true if setting doesn't exist
            isEnabled = storedEnabled === null ? true : storedEnabled === 'true';
            debugMode = storedDebug === 'true';
            
            if (debugMode) {
                console.log('[YouTube Frames] Settings loaded:', {
                    enabled: isEnabled
                });
                console.log('[YouTube Frames] Settings in localStorage:', {
                    enabled: localStorage.getItem('youtubeFramesEnabled'),
                    debug: localStorage.getItem('debugMode')
                });
            }
            
            return isEnabled;
        } catch (error) {
            console.error('[YouTube Frames] Error loading settings:', error);
            return true; // Default to enabled on error
        }
    }
    
    // === BUTTON CREATION & MANAGEMENT ===
    
    function createFrameButtons() {
        if (f2cButton && f2dButton) return; // Already created
        
        // Create F2C (Frame to Copy) button
        f2cButton = document.createElement("button");
        f2cButton.className = "frameButton f2c-button ytp-button";
        f2cButton.style.cssText = `
            width: auto;
            float: left;
            margin-right: 4px;
            border: 1px solid #374151;
            border-radius: 3px;
            color: #d1d5db;
            padding: 0px 6px;
            font-size: 11px;
            font-weight: 400;
            transition: all 0.2s ease;
        `;
        f2cButton.textContent = "F2C";
        f2cButton.title = "Frame to Copy - Screenshot with YouTube frame to clipboard";
        f2cButton.onclick = () => captureFramedScreenshot('copy');
        
        // Add CSS keyframe animation for pulse effect
        if (!document.getElementById('f2c-pulse-animation')) {
            const style = document.createElement('style');
            style.id = 'f2c-pulse-animation';
            style.textContent = `
                @keyframes f2c-pulse {
                    0% { transform: scale(1); opacity: 1; }
                    50% { transform: scale(1.1); opacity: 0.8; }
                    100% { transform: scale(1); opacity: 1; }
                }
                .f2c-pulsing {
                    animation: f2c-pulse 0.6s ease-out;
                }
            `;
            document.head.appendChild(style);
        }
        
        // Add hover animation
        f2cButton.addEventListener('mouseenter', () => {
            f2cButton.style.backgroundColor = '#4b5563';
        });
        f2cButton.addEventListener('mouseleave', () => {
            f2cButton.style.backgroundColor = '';
        });
        
        // Add pulse animation on button press
        f2cButton.addEventListener('mousedown', () => {
            f2cButton.classList.add('f2c-pulsing');
        });
        
        // Remove animation class when animation completes
        f2cButton.addEventListener('animationend', () => {
            f2cButton.classList.remove('f2c-pulsing');
        });
        
        // Create F2D (Frame to Download) button
        f2dButton = document.createElement("button");
        f2dButton.className = "frameButton f2d-button ytp-button";
        f2dButton.style.cssText = `
            width: auto;
            float: left;
            margin-right: 6px;
            border: 1px solid #374151;
            border-radius: 3px;
            color: #d1d5db;
            padding: 0px 6px;
            font-size: 11px;
            font-weight: 400;
            transition: all 0.2s ease;
        `;
        f2dButton.textContent = "F2D";
        f2dButton.title = "Frame to Download - Screenshot with YouTube frame to file";
        f2dButton.onclick = () => captureFramedScreenshot('download');
        
        // Add CSS keyframe animation for pulse effect (reuse existing F2C animation)
        if (!document.getElementById('f2d-pulse-animation')) {
            const style = document.createElement('style');
            style.id = 'f2d-pulse-animation';
            style.textContent = `
                @keyframes f2d-pulse {
                    0% { transform: scale(1); opacity: 1; }
                    50% { transform: scale(1.1); opacity: 0.8; }
                    100% { transform: scale(1); opacity: 1; }
                }
                .f2d-pulsing {
                    animation: f2d-pulse 0.6s ease-out;
                }
            `;
            document.head.appendChild(style);
        }
        
        // Add hover animation
        f2dButton.addEventListener('mouseenter', () => {
            f2dButton.style.backgroundColor = '#4b5563';
        });
        f2dButton.addEventListener('mouseleave', () => {
            f2dButton.style.backgroundColor = '';
        });
        
        // Add pulse animation on button press
        f2dButton.addEventListener('mousedown', () => {
            f2dButton.classList.add('f2d-pulsing');
        });
        
        // Remove animation class when animation completes
        f2dButton.addEventListener('animationend', () => {
            f2dButton.classList.remove('f2d-pulsing');
        });
    }
    
    function addFrameButtons() {
        if ((!f2cButton || !f2dButton) || areButtonsAppended) return;
        
        const ytpRightControls = document.getElementsByClassName("ytp-right-controls")[0];
        if (!ytpRightControls) {
            areButtonsAppended = false;
            return;
        }
        
        // Add both buttons to YouTube controls (F2D first, then F2C)
        ytpRightControls.prepend(f2dButton);
        ytpRightControls.prepend(f2cButton);
        areButtonsAppended = true;
        
        if (debugMode) {
            console.log('[YouTube Frames] F2C and F2D buttons added to YouTube controls');
            console.log('[YouTube Frames] Debug info:', {
                enabled: isEnabled,
                pageType: window.location.pathname,
                buttonsCreated: !!(f2cButton && f2dButton),
                buttonsAppended: areButtonsAppended,
                controlsFound: !!document.getElementsByClassName("ytp-right-controls")[0]
            });
        }
    }
    
    function removeFrameButtons() {
        if (f2cButton && f2cButton.parentNode) {
            f2cButton.parentNode.removeChild(f2cButton);
        }
        if (f2dButton && f2dButton.parentNode) {
            f2dButton.parentNode.removeChild(f2dButton);
        }
        areButtonsAppended = false;
    }
    
    // === FRAMED SCREENSHOT CAPTURE FUNCTIONALITY ===
    
    async function captureFramedScreenshot(action) {
        try {
            const video = document.getElementsByClassName("video-stream")[0];
            if (!video) {
                console.error('[YouTube Frames] No video element found');
                return;
            }
            
            // Get video title for filename
            let title = '';
            const headerEls = document.querySelectorAll("h1.title.ytd-video-primary-info-renderer, h1.watch-title-container");
            if (headerEls.length > 0) {
                title = headerEls[0].innerText.trim();
            }
            
            // Get current time for filename
            const time = video.currentTime;
            let minutes = Math.floor(time / 60);
            let seconds = Math.floor(time - (minutes * 60));
            
            if (minutes >= 60) {
                const hours = Math.floor(minutes / 60);
                minutes = minutes - (hours * 60);
                title += ` ${hours}-${minutes}-${seconds}`;
            } else {
                title += ` ${minutes}-${seconds}`;
            }
            
            const filename = `${title} framed.png`;
            
            // Create video screenshot canvas
            const videoCanvas = document.createElement("canvas");
            videoCanvas.width = video.videoWidth;
            videoCanvas.height = video.videoHeight;
            const videoCtx = videoCanvas.getContext('2d');
            videoCtx.drawImage(video, 0, 0, videoCanvas.width, videoCanvas.height);
            
            // Convert embedded SVG to canvas-compatible image
            const svgData = 'data:image/svg+xml;base64,' + btoa(YOUTUBE_FRAME_SVG);
            const frameImg = new Image();
            
            frameImg.onload = function() {
                // Create final composite canvas
                const finalCanvas = document.createElement("canvas");
                finalCanvas.width = frameImg.width;
                finalCanvas.height = frameImg.height;
                const finalCtx = finalCanvas.getContext('2d');
                
                // Calculate video placement within frame
                // Based on the actual YouTube frame SVG dimensions (900x590.25)
                const frameWidth = frameImg.width;
                const frameHeight = frameImg.height;
                
                // Calculate video area within frame based on the actual frame image
                // The video area is the light gray area above the dark controls bar
                const videoAreaX = Math.floor(frameWidth * 0.011); // ~1.1% margin from left (black border)
                const videoAreaY = Math.floor(frameWidth * 0.011); // ~1.1% margin from top (black border)
                const videoAreaWidth = Math.floor(frameWidth * 0.978); // ~97.8% of frame width (accounting for borders)
                const videoAreaHeight = Math.floor(frameHeight * 0.82); // ~82% of frame height (leaving space for controls bar)
                
                // Calculate video scaling to COVER the frame area while maintaining aspect ratio
                const videoAspectRatio = videoCanvas.width / videoCanvas.height;
                const areaAspectRatio = videoAreaWidth / videoAreaHeight;
                
                let scaledVideoWidth, scaledVideoHeight, videoX, videoY;
                
                if (videoAspectRatio > areaAspectRatio) {
                    // Video is wider than area - scale to fill height, crop width
                    scaledVideoHeight = videoAreaHeight;
                    scaledVideoWidth = videoAreaHeight * videoAspectRatio;
                } else {
                    // Video is taller than area - scale to fill width, crop height
                    scaledVideoWidth = videoAreaWidth;
                    scaledVideoHeight = videoAreaWidth / videoAspectRatio;
                }
                
                // Center the scaled video within the frame area (may extend beyond for cropping)
                videoX = videoAreaX + (videoAreaWidth - scaledVideoWidth) / 2;
                videoY = videoAreaY + (videoAreaHeight - scaledVideoHeight) / 2;
                
                // Save canvas state and set up clipping region to prevent video overflow
                finalCtx.save();
                finalCtx.rect(videoAreaX, videoAreaY, videoAreaWidth, videoAreaHeight);
                finalCtx.clip();
                
                // Draw the video screenshot as background (may extend beyond clip area, will be cropped)
                finalCtx.drawImage(videoCanvas, videoX, videoY, scaledVideoWidth, scaledVideoHeight);
                
                // Restore canvas state (removes clipping)
                finalCtx.restore();
                
                // Draw the YouTube frame overlay on top (unclipped, includes play button and controls)
                finalCtx.drawImage(frameImg, 0, 0);
                
                // Handle action
                if (action === 'copy') {
                    // Copy to clipboard with linked framed image using context7 approach
                    finalCanvas.toBlob(async function(blob) {
                        try {
                            // Get current YouTube URL for linking
                            const youtubeUrl = window.location.href;
                            
                            // Create the complete framed image as a linked element in DOM
                            const linkElement = document.createElement('a');
                            linkElement.href = youtubeUrl;
                            linkElement.target = '_blank';
                            
                            const imgElement = document.createElement('img');
                            imgElement.src = finalCanvas.toDataURL('image/png');
                            imgElement.style.display = 'block';
                            imgElement.style.maxWidth = 'none';
                            imgElement.style.border = 'none';
                            imgElement.alt = 'YouTube Frame Screenshot';
                            
                            // Append the complete framed image to the link
                            linkElement.appendChild(imgElement);
                            
                            // Position element off-screen but make it visible to browser
                            linkElement.style.position = 'absolute';
                            linkElement.style.left = '-9999px';
                            linkElement.style.top = '-9999px';
                            linkElement.style.zIndex = '-1';
                            document.body.appendChild(linkElement);
                            
                            // Use modern Clipboard API with multiple formats including the linked HTML
                            const reader = new FileReader();
                            reader.onload = async function() {
                                const base64DataUrl = reader.result;
                                
                                // Create HTML representation of the linked framed image with URL hyperlink below (compact, no extra spacing)
                                const htmlContent = `<a href="${youtubeUrl}" target="_blank" style="display: inline-block; margin: 0; padding: 0; line-height: 0;"><img src="${base64DataUrl}" alt="YouTube Frame Screenshot" style="display: block; margin: 0; padding: 0; border: none; vertical-align: top;" /></a><br><a href="${youtubeUrl}" target="_blank">${youtubeUrl}</a>`;
                                const htmlBlob = new Blob([htmlContent], { type: 'text/html' });
                                
                                // Create clipboard item with both HTML and image formats
                                const clipboardItemInput = new ClipboardItem({
                                    "text/html": htmlBlob,
                                    "image/png": blob
                                });
                                
                                await navigator.clipboard.write([clipboardItemInput]);
                                
                                // Clean up DOM element
                                document.body.removeChild(linkElement);
                                
                                if (debugMode) {
                                    console.log('[YouTube Frames] F2C: Linked framed image copied to clipboard with URL:', youtubeUrl);
                                }
                            };
                            
                            reader.readAsDataURL(blob);
                            
                        } catch (error) {
                            console.error('[YouTube Frames] F2C: Failed to copy to clipboard:', error);
                            // Clean up if there was an error
                            const tempElement = document.querySelector('a[href*="youtube.com"]');
                            if (tempElement && tempElement.style.position === 'absolute') {
                                document.body.removeChild(tempElement);
                            }
                        }
                    }, 'image/png');
                } else if (action === 'download') {
                    // Download file  
                    finalCanvas.toBlob(function(blob) {
                        const downloadLink = document.createElement("a");
                        downloadLink.download = filename;
                        downloadLink.href = URL.createObjectURL(blob);
                        downloadLink.click();
                        
                        if (debugMode) {
                            console.log('[YouTube Frames] F2D: Framed image downloaded as', filename);
                        }
                    }, 'image/png');
                }
            };
            
            frameImg.onerror = function() {
                console.error('[YouTube Frames] Failed to load embedded SVG frame');
            };
            
            // Load the embedded SVG as image
            frameImg.src = svgData;
            
        } catch (error) {
            console.error('[YouTube Frames] Error capturing framed screenshot:', error);
        }
    }
    
    // === MUTATION OBSERVER MANAGEMENT ===
    
    function setupFramesObserver() {
        if (framesObserver) return;
        
        framesObserver = new MutationObserver((mutations) => {
            let shouldCheck = false;
            for (let mutation of mutations) {
                if (mutation.type === 'childList') {
                    shouldCheck = true;
                    break;
                }
            }
            
            if (shouldCheck && isEnabled && isYouTubePage()) {
                const ytpRightControls = document.getElementsByClassName("ytp-right-controls")[0];
                
                if (ytpRightControls && !areButtonsAppended && f2cButton && f2dButton) {
                    // Verify no existing frame buttons before adding
                    const existingButtons = document.querySelectorAll('.frameButton');
                    if (existingButtons.length === 0) {
                        addFrameButtons();
                    } else {
                        // Buttons exist but state is inconsistent - fix state
                        areButtonsAppended = true;
                    }
                }
            } else if (shouldCheck && !isEnabled) {
                // Feature is disabled - remove any existing buttons
                const existingButtons = document.querySelectorAll('.frameButton');
                existingButtons.forEach(btn => {
                    if (btn.parentNode) {
                        btn.parentNode.removeChild(btn);
                    }
                });
                areButtonsAppended = false;
            }
        });
        
        framesObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    function removeFramesObserver() {
        if (framesObserver) {
            framesObserver.disconnect();
            framesObserver = null;
        }
    }
    
    // === INITIALIZATION & CLEANUP ===
    
    function initializeYouTubeFrames() {
        if (!isYouTubePage()) return;
        
        // Force cleanup of any existing state before initializing
        if (f2cButton || f2dButton || areButtonsAppended) {
            if (debugMode) {
                console.log('[YouTube Frames] Cleaning up existing state before initialization');
            }
            disableYouTubeFrames();
        }
        
        createFrameButtons();
        addFrameButtons();
        setupFramesObserver();
        
        if (debugMode) {
            console.log('[YouTube Frames] Functionality initialized');
        }
    }
    
    function disableYouTubeFrames() {
        // Force state reset
        f2cButton = null;
        f2dButton = null;
        areButtonsAppended = false;
        
        // Remove buttons
        removeFrameButtons();
        
        // Force remove any remaining frame buttons
        document.querySelectorAll('.frameButton').forEach(btn => {
            if (btn.parentNode) {
                btn.parentNode.removeChild(btn);
            }
        });
        
        // Clean up observer
        removeFramesObserver();
        
        if (debugMode) {
            console.log('[YouTube Frames] Functionality disabled and cleaned up');
        }
    }
    
    // === SETTINGS CHANGE LISTENER ===
    
    // Settings change listener via localStorage events
    window.addEventListener('storage', (e) => {
        if (e.key === 'youtubeFramesEnabled' || e.key === 'debugMode') {
            const wasEnabled = isEnabled;
            loadSettings();
            
            if (debugMode) {
                console.log('[YouTube Frames] Settings updated:', { enabled: isEnabled });
            }
            
            // Handle enable/disable changes
            if (wasEnabled !== isEnabled) {
                if (isEnabled) {
                    initializeYouTubeFrames();
                } else {
                    disableYouTubeFrames();
                }
            }
        }
    });
    
    // === MAIN INITIALIZATION ===
    
    function initialize() {
        if (debugMode) {
            console.log('[YouTube Frames] Initialize called:', {
                isYouTubePage: isYouTubePage(),
                pageUrl: window.location.href,
                readyState: document.readyState
            });
        }
        
        if (!isYouTubePage()) {
            if (debugMode) {
                console.log('[YouTube Frames] Not a YouTube watch page, exiting');
            }
            return;
        }
        
        const shouldRun = loadSettings();
        
        if (shouldRun) {
            if (debugMode) {
                console.log('[YouTube Frames] Starting feature');
            }
            initializeYouTubeFrames();
        } else {
            if (debugMode) {
                console.log('[YouTube Frames] Feature disabled in settings');
            }
        }
    }
    
    // Start when DOM is ready with a small delay to ensure settings are loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(initialize, 100); // Small delay to ensure settings bridge runs first
        });
    } else {
        setTimeout(initialize, 100); // Small delay to ensure settings bridge runs first
    }
    
    // Handle page navigation on YouTube (SPA)
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            if (debugMode) {
                console.log('[YouTube Frames] Page navigation detected');
            }
            
            // Re-initialize on navigation
            if (isEnabled) {
                setTimeout(() => {
                    if (isYouTubePage()) {
                        initializeYouTubeFrames();
                    } else {
                        disableYouTubeFrames();
                    }
                }, 500);
            }
        }
    }).observe(document, { subtree: true, childList: true });
})();