// Minimal Reader Settings Bridge - Syncs Chrome storage settings to localStorage for MAIN world scripts
(function() {
    'use strict';
    
    // This script runs in ISOLATED world and has access to Chrome APIs
    // It syncs settings to localStorage for Minimal Reader scripts that run in MAIN world
    
    // Function to sync settings to localStorage
    function syncSettingsToLocalStorage() {
        chrome.storage.sync.get(['gmbExtractorSettings'], (result) => {
            const settings = result.gmbExtractorSettings || {};
            
            // Minimal Reader main toggle
            localStorage.setItem('minimalReaderEnabled', settings.minimalReaderEnabled !== false ? 'true' : 'false');
            
            // Minimal Reader appearance settings
            localStorage.setItem('minimalReaderTheme', settings.minimalReaderTheme || 'white');
            localStorage.setItem('minimalReaderFontSize', settings.minimalReaderFontSize || '18');
            localStorage.setItem('minimalReaderLineHeight', settings.minimalReaderLineHeight || '1.6');
            localStorage.setItem('minimalReaderFontWeight', settings.minimalReaderFontWeight || 'normal');
            localStorage.setItem('minimalReaderButtonPosition', settings.minimalReaderButtonPosition || 'top-left');
            
            // Minimal Reader functionality settings
            localStorage.setItem('minimalReaderSpeedReadingEnabled', settings.minimalReaderSpeedReadingEnabled === true ? 'true' : 'false');
            localStorage.setItem('minimalReaderStopWordFading', settings.minimalReaderStopWordFading !== false ? 'true' : 'false');
            
            // Debug mode
            localStorage.setItem('debugMode', settings.debugMode === true ? 'true' : 'false');
            
            console.log('[Minimal Reader Settings Bridge] Settings synced to localStorage:', {
                enabled: localStorage.getItem('minimalReaderEnabled'),
                theme: localStorage.getItem('minimalReaderTheme'),
                position: localStorage.getItem('minimalReaderButtonPosition'),
                fontSize: localStorage.getItem('minimalReaderFontSize'),
                speedReading: localStorage.getItem('minimalReaderSpeedReadingEnabled')
            });
            
            // Dispatch custom event to notify MAIN world scripts
            window.dispatchEvent(new CustomEvent('minimalReaderSettingsUpdate'));
        });
    }
    
    // Sync on page load
    syncSettingsToLocalStorage();
    
    // Listen for settings updates
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'updateSettings' && message.settings) {
            // Sync new settings to localStorage
            const settings = message.settings;
            
            // Minimal Reader main toggle
            localStorage.setItem('minimalReaderEnabled', settings.minimalReaderEnabled !== false ? 'true' : 'false');
            
            // Minimal Reader appearance settings
            localStorage.setItem('minimalReaderTheme', settings.minimalReaderTheme || 'white');
            localStorage.setItem('minimalReaderFontSize', settings.minimalReaderFontSize || '18');
            localStorage.setItem('minimalReaderLineHeight', settings.minimalReaderLineHeight || '1.6');
            localStorage.setItem('minimalReaderFontWeight', settings.minimalReaderFontWeight || 'normal');
            localStorage.setItem('minimalReaderButtonPosition', settings.minimalReaderButtonPosition || 'top-left');
            
            // Minimal Reader functionality settings
            localStorage.setItem('minimalReaderSpeedReadingEnabled', settings.minimalReaderSpeedReadingEnabled === true ? 'true' : 'false');
            localStorage.setItem('minimalReaderStopWordFading', settings.minimalReaderStopWordFading !== false ? 'true' : 'false');
            
            // Debug mode
            localStorage.setItem('debugMode', settings.debugMode === true ? 'true' : 'false');
            
            console.log('[Minimal Reader Settings Bridge] Settings updated in localStorage:', {
                enabled: localStorage.getItem('minimalReaderEnabled'),
                theme: localStorage.getItem('minimalReaderTheme'),
                position: localStorage.getItem('minimalReaderButtonPosition'),
                fontSize: localStorage.getItem('minimalReaderFontSize'),
                speedReading: localStorage.getItem('minimalReaderSpeedReadingEnabled')
            });
            
            // Dispatch custom event to notify MAIN world scripts
            window.dispatchEvent(new CustomEvent('minimalReaderSettingsUpdate'));
        }
    });
})();