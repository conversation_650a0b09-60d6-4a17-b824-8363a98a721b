// YouTube Screenshot - Capture screenshots from YouTube videos
(function() {
    'use strict';
    
    
    let isEnabled = true; // Default to true
    let screenshotKeyEnabled = true;
    let screenshotFunctionality = 2; // 0: save, 1: copy, 2: both
    let debugMode = false;
    
    // YouTube-specific elements
    let s2cButton = null;
    let s2dButton = null;
    let areButtonsAppended = false;
    let screenshotObserver = null;
    
    // Check if we're on a YouTube watch page
    function isYouTubePage() {
        return window.location.hostname.includes('youtube.com') && 
               window.location.pathname.includes('/watch');
    }
    
    // Load settings from localStorage (MAIN world doesn't have Chrome API access)
    function loadSettings() {
        try {
            const storedEnabled = localStorage.getItem('screenshotYouTubeEnabled');
            const storedKeyEnabled = localStorage.getItem('screenshotKeyEnabled');
            const storedFunctionality = localStorage.getItem('screenshotFunctionality');
            const storedDebug = localStorage.getItem('debugMode');
            
            // Explicit boolean checks - only default to true if setting doesn't exist
            isEnabled = storedEnabled === null ? true : storedEnabled === 'true';
            screenshotKeyEnabled = storedKeyEnabled === null ? true : storedKeyEnabled === 'true';
            screenshotFunctionality = storedFunctionality !== null ? parseInt(storedFunctionality) : 2;
            debugMode = storedDebug === 'true';
            
            if (debugMode) {
                console.log('[YouTube Screenshot] Settings loaded:', {
                    enabled: isEnabled,
                    keyEnabled: screenshotKeyEnabled,
                    functionality: screenshotFunctionality
                });
            }
            
            return isEnabled;
        } catch (error) {
            console.error('[YouTube Screenshot] Error loading settings:', error);
            return true; // Default to enabled on error
        }
    }
    
    // === SCREENSHOT BUTTON CREATION & MANAGEMENT ===
    
    function createScreenshotButtons() {
        if (s2cButton && s2dButton) return; // Already created
        
        // Create S2C (Screenshot to Clipboard) button
        s2cButton = document.createElement("button");
        s2cButton.className = "screenshotButton s2c-button ytp-button";
        s2cButton.style.cssText = `
            width: auto;
            float: left;
            margin-right: 4px;
            border: 1px solid #374151;
            border-radius: 3px;
            color: #d1d5db;
            padding: 0px 6px;
            font-size: 11px;
            font-weight: 400;
            transition: all 0.2s ease;
        `;
        s2cButton.textContent = "S2C";
        s2cButton.onclick = captureToClipboard;
        
        // Add CSS keyframe animation for pulse effect
        if (!document.getElementById('s2c-pulse-animation')) {
            const style = document.createElement('style');
            style.id = 's2c-pulse-animation';
            style.textContent = `
                @keyframes s2c-pulse {
                    0% { transform: scale(1); opacity: 1; }
                    50% { transform: scale(1.1); opacity: 0.8; }
                    100% { transform: scale(1); opacity: 1; }
                }
                .s2c-pulsing {
                    animation: s2c-pulse 0.6s ease-out;
                }
            `;
            document.head.appendChild(style);
        }
        
        // Add hover animation
        s2cButton.addEventListener('mouseenter', () => {
            s2cButton.style.backgroundColor = '#4b5563';
        });
        s2cButton.addEventListener('mouseleave', () => {
            s2cButton.style.backgroundColor = '';
        });
        
        // Add pulse animation on button press
        s2cButton.addEventListener('mousedown', () => {
            s2cButton.classList.add('s2c-pulsing');
        });
        
        // Remove animation class when animation completes
        s2cButton.addEventListener('animationend', () => {
            s2cButton.classList.remove('s2c-pulsing');
        });
        
        // Create S2D (Screenshot to Download) button
        s2dButton = document.createElement("button");
        s2dButton.className = "screenshotButton s2d-button ytp-button";
        s2dButton.style.cssText = `
            width: auto;
            float: left;
            margin-right: 6px;
            border: 1px solid #374151;
            border-radius: 3px;
            color: #d1d5db;
            padding: 0px 6px;
            font-size: 11px;
            font-weight: 400;
            transition: all 0.2s ease;
        `;
        s2dButton.textContent = "S2D";
        s2dButton.onclick = captureToDownload;
        
        // Add CSS keyframe animation for pulse effect
        if (!document.getElementById('s2d-pulse-animation')) {
            const style = document.createElement('style');
            style.id = 's2d-pulse-animation';
            style.textContent = `
                @keyframes s2d-pulse {
                    0% { transform: scale(1); opacity: 1; }
                    50% { transform: scale(1.1); opacity: 0.8; }
                    100% { transform: scale(1); opacity: 1; }
                }
                .s2d-pulsing {
                    animation: s2d-pulse 0.6s ease-out;
                }
            `;
            document.head.appendChild(style);
        }
        
        // Add hover animation
        s2dButton.addEventListener('mouseenter', () => {
            s2dButton.style.backgroundColor = '#4b5563';
        });
        s2dButton.addEventListener('mouseleave', () => {
            s2dButton.style.backgroundColor = '';
        });
        
        // Add pulse animation on button press
        s2dButton.addEventListener('mousedown', () => {
            s2dButton.classList.add('s2d-pulsing');
        });
        
        // Remove animation class when animation completes
        s2dButton.addEventListener('animationend', () => {
            s2dButton.classList.remove('s2d-pulsing');
        });
    }
    
    function addScreenshotButtons() {
        if ((!s2cButton || !s2dButton) || areButtonsAppended) return;
        
        const ytpRightControls = document.getElementsByClassName("ytp-right-controls")[0];
        if (!ytpRightControls) {
            areButtonsAppended = false;
            return;
        }
        
        // Add both buttons to YouTube controls
        ytpRightControls.prepend(s2dButton);
        ytpRightControls.prepend(s2cButton);
        areButtonsAppended = true;
        
        if (debugMode) {
            console.log('[YouTube Screenshot] S2C and S2D buttons added to YouTube controls');
        }
    }
    
    function removeScreenshotButtons() {
        if (s2cButton && s2cButton.parentNode) {
            s2cButton.parentNode.removeChild(s2cButton);
        }
        if (s2dButton && s2dButton.parentNode) {
            s2dButton.parentNode.removeChild(s2dButton);
        }
        areButtonsAppended = false;
    }
    
    // === SCREENSHOT CAPTURE FUNCTIONALITY ===
    
    function captureToClipboard() {
        const video = document.getElementsByClassName("video-stream")[0];
        if (!video) {
            console.error('[YouTube Screenshot] No video element found');
            return;
        }
        
        // Get video title and timestamp
        const { title, canvas } = getVideoData(video);
        
        // Always copy to clipboard (force mode 1)
        canvas.toBlob(async function(blob) {
            try {
                const clipboardItemInput = new ClipboardItem({ "image/png": blob });
                await navigator.clipboard.write([clipboardItemInput]);
                
                if (debugMode) {
                    console.log('[YouTube Screenshot] S2C: Image copied to clipboard');
                }
            } catch (error) {
                console.error('[YouTube Screenshot] S2C: Failed to copy to clipboard:', error);
            }
        }, 'image/png');
    }
    
    function captureToDownload() {
        const video = document.getElementsByClassName("video-stream")[0];
        if (!video) {
            console.error('[YouTube Screenshot] No video element found');
            return;
        }
        
        // Get video title and timestamp
        const { title, canvas } = getVideoData(video);
        const filename = `${title}.png`;
        
        // Always download to file (force mode 0)
        canvas.toBlob(function(blob) {
            downloadBlob(blob, filename);
            
            if (debugMode) {
                console.log('[YouTube Screenshot] S2D: Image downloaded as', filename);
            }
        }, 'image/png');
    }
    
    function getVideoData(video) {
        // Get video title
        let title = '';
        const headerEls = document.querySelectorAll("h1.title.ytd-video-primary-info-renderer, h1.watch-title-container");
        if (headerEls.length > 0) {
            title = headerEls[0].innerText.trim();
        }
        
        // Get current time
        const time = video.currentTime;
        let minutes = Math.floor(time / 60);
        let seconds = Math.floor(time - (minutes * 60));
        
        if (minutes >= 60) {
            const hours = Math.floor(minutes / 60);
            minutes = minutes - (hours * 60);
            title += ` ${hours}-${minutes}-${seconds}`;
        } else {
            title += ` ${minutes}-${seconds}`;
        }
        
        // Create canvas and capture
        const canvas = document.createElement("canvas");
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);
        
        return { title, canvas };
    }
    
    function captureYouTubeScreenshot() {
        const video = document.getElementsByClassName("video-stream")[0];
        if (!video) {
            console.error('[YouTube Screenshot] No video element found');
            return;
        }
        
        // Get video title and timestamp
        const { title, canvas } = getVideoData(video);
        const filename = `${title}.png`;
        
        // Handle different functionality modes
        if (screenshotFunctionality === 1 || screenshotFunctionality === 2) {
            // Copy to clipboard (always PNG for clipboard)
            canvas.toBlob(async function(blob) {
                try {
                    const clipboardItemInput = new ClipboardItem({ "image/png": blob });
                    await navigator.clipboard.write([clipboardItemInput]);
                    
                    if (debugMode) {
                        console.log('[YouTube Screenshot] Image copied to clipboard');
                    }
                } catch (error) {
                    console.error('[YouTube Screenshot] Failed to copy to clipboard:', error);
                }
            }, 'image/png');
        }
        
        // Save to file
        if (screenshotFunctionality === 0 || screenshotFunctionality === 2) {
            canvas.toBlob(function(blob) {
                downloadBlob(blob, filename);
            }, 'image/png');
        }
    }
    
    function downloadBlob(blob, filename) {
        const downloadLink = document.createElement("a");
        downloadLink.download = filename;
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.click();
        
        if (debugMode) {
            console.log('[YouTube Screenshot] Image downloaded as', filename);
        }
    }
    
    // === KEYBOARD LISTENER MANAGEMENT ===
    
    function handleScreenshotKeydown(e) {
        // Skip if user is typing
        if (document.activeElement.contentEditable === 'true' || 
            document.activeElement.tagName === 'INPUT' || 
            document.activeElement.tagName === 'TEXTAREA') {
            return;
        }
        
        // Screenshot key (P)
        if (screenshotKeyEnabled && e.key === 'p') {
            captureYouTubeScreenshot();
            e.preventDefault();
            return false;
        }
    }
    
    function setupScreenshotKeyboardListener() {
        document.addEventListener('keydown', handleScreenshotKeydown);
    }
    
    function removeScreenshotKeyboardListener() {
        document.removeEventListener('keydown', handleScreenshotKeydown);
    }
    
    // === MUTATION OBSERVER MANAGEMENT ===
    
    function setupScreenshotObserver() {
        if (screenshotObserver) return;
        
        screenshotObserver = new MutationObserver((mutations) => {
            let shouldCheck = false;
            for (let mutation of mutations) {
                if (mutation.type === 'childList') {
                    shouldCheck = true;
                    break;
                }
            }
            
            if (shouldCheck && isEnabled && isYouTubePage()) {
                const ytpRightControls = document.getElementsByClassName("ytp-right-controls")[0];
                
                if (ytpRightControls && !areButtonsAppended && s2cButton && s2dButton) {
                    // Verify no existing screenshot buttons before adding
                    const existingButtons = document.querySelectorAll('.screenshotButton');
                    if (existingButtons.length === 0) {
                        addScreenshotButtons();
                    } else {
                        // Buttons exist but state is inconsistent - fix state
                        areButtonsAppended = true;
                    }
                }
            } else if (shouldCheck && !isEnabled) {
                // Feature is disabled - remove any existing buttons
                const existingButtons = document.querySelectorAll('.screenshotButton');
                existingButtons.forEach(btn => {
                    if (btn.parentNode) {
                        btn.parentNode.removeChild(btn);
                    }
                });
                areButtonsAppended = false;
            }
        });
        
        screenshotObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    function removeScreenshotObserver() {
        if (screenshotObserver) {
            screenshotObserver.disconnect();
            screenshotObserver = null;
        }
    }
    
    // === INITIALIZATION & CLEANUP ===
    
    function initializeYouTubeScreenshot() {
        if (!isYouTubePage()) return;
        
        // Force cleanup of any existing state before initializing
        if (s2cButton || s2dButton || areButtonsAppended) {
            if (debugMode) {
                console.log('[YouTube Screenshot] Cleaning up existing state before initialization');
            }
            disableYouTubeScreenshot();
        }
        
        createScreenshotButtons();
        addScreenshotButtons();
        setupScreenshotKeyboardListener();
        setupScreenshotObserver();
        
        if (debugMode) {
            console.log('[YouTube Screenshot] Functionality initialized');
        }
    }
    
    function disableYouTubeScreenshot() {
        // Force state reset
        s2cButton = null;
        s2dButton = null;
        areButtonsAppended = false;
        
        // Remove buttons
        removeScreenshotButtons();
        
        // Force remove any remaining screenshot buttons
        document.querySelectorAll('.screenshotButton').forEach(btn => {
            if (btn.parentNode) {
                btn.parentNode.removeChild(btn);
            }
        });
        
        // Clean up listeners and observer
        removeScreenshotKeyboardListener();
        removeScreenshotObserver();
        
        if (debugMode) {
            console.log('[YouTube Screenshot] Functionality disabled and cleaned up');
        }
    }
    
    // === SETTINGS CHANGE LISTENER ===
    
    // Settings change listener via localStorage events
    window.addEventListener('storage', (e) => {
        if (e.key === 'screenshotYouTubeEnabled' || e.key === 'screenshotKeyEnabled' || 
            e.key === 'screenshotFunctionality' || e.key === 'debugMode') {
            const wasEnabled = isEnabled;
            loadSettings();
            
            if (debugMode) {
                console.log('[YouTube Screenshot] Settings updated:', { enabled: isEnabled });
            }
            
            // Handle enable/disable changes
            if (wasEnabled !== isEnabled) {
                if (isEnabled) {
                    initializeYouTubeScreenshot();
                } else {
                    disableYouTubeScreenshot();
                }
            }
        }
    });
    
    // === MAIN INITIALIZATION ===
    
    function initialize() {
        if (!isYouTubePage()) {
            console.log('[YouTube Screenshot] Not a YouTube watch page, exiting');
            return;
        }
        
        const shouldRun = loadSettings();
        
        if (shouldRun) {
            console.log('[YouTube Screenshot] Starting feature');
            initializeYouTubeScreenshot();
        } else {
            console.log('[YouTube Screenshot] Feature disabled in settings');
        }
    }
    
    // Start when DOM is ready with a small delay to ensure settings are loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(initialize, 100); // Small delay to ensure settings bridge runs first
        });
    } else {
        setTimeout(initialize, 100); // Small delay to ensure settings bridge runs first
    }
    
    // Handle page navigation on YouTube (SPA)
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            if (debugMode) {
                console.log('[YouTube Screenshot] Page navigation detected');
            }
            
            // Re-initialize on navigation
            if (isEnabled) {
                setTimeout(() => {
                    if (isYouTubePage()) {
                        initializeYouTubeScreenshot();
                    } else {
                        disableYouTubeScreenshot();
                    }
                }, 500);
            }
        }
    }).observe(document, { subtree: true, childList: true });
})();