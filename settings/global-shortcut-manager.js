// Global Keyboard Shortcut Manager
// Based on proven STM extension architecture for robust cross-platform support

console.log('SEO Time Machines: Global shortcut manager loading...');

// Context validation for global shortcut manager
function isExtensionContextValid() {
    try {
        return !!(chrome && chrome.storage && chrome.storage.local && chrome.runtime && chrome.runtime.id);
    } catch (error) {
        return false;
    }
}

// Safe Chrome storage wrapper for global shortcut manager
async function safeChromeStorageGet(keys) {
    try {
        if (!isExtensionContextValid()) {
            return {};
        }
        return await chrome.storage.local.get(keys);
    } catch (error) {
        if (error.message && error.message.includes('Extension context invalidated')) {
            return {};
        }
        throw error;
    }
}

class GlobalShortcutManager {
    constructor() {
        console.log('🔵 GlobalShortcuts: Constructor called on:', window.location.href);
        this.shortcuts = {};
        this.isInitialized = false;
        this.isMac = navigator.platform.toLowerCase().includes('mac');
        this.isGooglePage = this.checkGooglePage();
        
        console.log('🔵 GlobalShortcuts: Initial setup:', {
            platform: navigator.platform,
            isMac: this.isMac,
            isGooglePage: this.isGooglePage,
            url: window.location.href
        });
        
        this.init();
    }

    // Safe Chrome sync storage wrapper
    async safeChromeStorageSyncGet(keys) {
        try {
            if (!isExtensionContextValid()) {
                return {};
            }
            return await chrome.storage.sync.get(keys);
        } catch (error) {
            if (error.message && error.message.includes('Extension context invalidated')) {
                return {};
            }
            return {}; // Fallback for sync storage issues
        }
    }

    checkGooglePage() {
        return window.location.hostname.includes('google.') ||
               window.location.hostname.includes('maps.google');
    }

    async init() {
        try {
            console.log('🔵 GlobalShortcuts: Starting initialization...');
            
            // Load shortcuts from storage - check Copy Element, Colorpicker, Screenshot, Copy Replace, and Text Transformers
            const result = await chrome.storage.sync.get([
                'copyElementShortcut', 'copyElementEnabled',
                'colorpickerShortcut', 'colorPickerEnabled',
                'screenshotShortcut', 'screenshotToolEnabled',
                'copyReplaceShortcut',
                'textTransformersCapitalCaseShortcut',
                'textTransformersLowerCaseShortcut',
                'textTransformersUpperCaseShortcut',
                'textTransformersSentenceCaseShortcut',
                'textTransformersSlugifyShortcut',
                'textTransformersTrimToPageShortcut',
                'textTransformersSortAlphabeticallyShortcut',
                'textTransformersRemoveEmptyLinesShortcut',
                'textTransformersRemoveDuplicateLinesShortcut',
                'textTimeMachineLauncherShortcut',
                'newTabRedirectShortcut'
            ]);
            
            // Load Copy Replace settings from main settings object (stored in local storage)
            const copyReplaceResult = await safeChromeStorageGet(['gmbExtractorSettings']);
            const mainSettings = copyReplaceResult.gmbExtractorSettings || {};
            
            console.log('🔵 GlobalShortcuts: Sync storage result:', result);
            console.log('🔵 GlobalShortcuts: Local storage (Copy Replace) settings:', {
                enabled: mainSettings.copyReplaceEnabled,
                sourceDomain: mainSettings.copyReplaceSourceDomain,
                targetDomain: mainSettings.copyReplaceTargetDomain,
                textTransformersEnabled: mainSettings.textTransformersEnabled
            });
            
            // Check if Copy Element is enabled (defaults to true if not set)
            const copyElementEnabled = result.copyElementEnabled !== false; // Default to enabled
            // Check if Colorpicker is enabled (defaults to true if not set)
            const colorpickerEnabled = result.colorPickerEnabled !== false; // Default to enabled
            // Check if Screenshot is enabled (defaults to true if not set)
            const screenshotEnabled = result.screenshotToolEnabled !== false; // Default to enabled
            // Check if Copy Replace is enabled (defaults to true if not set)
            const copyReplaceEnabled = mainSettings.copyReplaceEnabled !== false; // Default to enabled
            // Check if Text Transformers is enabled (defaults to false if not set)
            const textTransformersEnabled = mainSettings.textTransformersEnabled === true;
            
            console.log('🔵 GlobalShortcuts: Feature enablement check:', {
                copyElementEnabled: copyElementEnabled,
                colorpickerEnabled: colorpickerEnabled,
                screenshotEnabled: screenshotEnabled,
                copyReplaceEnabled: copyReplaceEnabled,
                textTransformersEnabled: textTransformersEnabled
            });
            
            let hasActiveShortcuts = false;
            
            if (copyElementEnabled) {
                this.shortcuts['copyElement'] = result.copyElementShortcut || this.getDefaultShortcut();
                hasActiveShortcuts = true;
            }
            
            if (colorpickerEnabled) {
                this.shortcuts['colorpicker'] = result.colorpickerShortcut || this.getDefaultColorpickerShortcut();
                hasActiveShortcuts = true;
            }
            
            if (screenshotEnabled) {
                const screenshotShortcut = result.screenshotShortcut || this.getDefaultScreenshotShortcut();
                this.shortcuts['screenshot'] = screenshotShortcut;
                hasActiveShortcuts = true;
                console.log('🔵 GlobalShortcuts: Screenshot shortcut configured:', {
                    enabled: screenshotEnabled,
                    shortcut: screenshotShortcut,
                    fromStorage: !!result.screenshotShortcut,
                    defaultUsed: !result.screenshotShortcut
                });
            } else {
                console.log('🔵 GlobalShortcuts: Screenshot shortcut DISABLED');
            }
            
            if (copyReplaceEnabled) {
                this.shortcuts['copyReplace'] = result.copyReplaceShortcut || this.getDefaultCopyReplaceShortcut();
                hasActiveShortcuts = true;
            }
            
            // Text Transformers shortcuts (only if enabled and shortcuts are configured)
            if (textTransformersEnabled) {
                if (result.textTransformersCapitalCaseShortcut) {
                    this.shortcuts['textTransformersCapitalCase'] = result.textTransformersCapitalCaseShortcut;
                    hasActiveShortcuts = true;
                }
                if (result.textTransformersLowerCaseShortcut) {
                    this.shortcuts['textTransformersLowerCase'] = result.textTransformersLowerCaseShortcut;
                    hasActiveShortcuts = true;
                }
                if (result.textTransformersUpperCaseShortcut) {
                    this.shortcuts['textTransformersUpperCase'] = result.textTransformersUpperCaseShortcut;
                    hasActiveShortcuts = true;
                }
                if (result.textTransformersSentenceCaseShortcut) {
                    this.shortcuts['textTransformersSentenceCase'] = result.textTransformersSentenceCaseShortcut;
                    hasActiveShortcuts = true;
                }
                if (result.textTransformersSlugifyShortcut) {
                    this.shortcuts['textTransformersSlugify'] = result.textTransformersSlugifyShortcut;
                    hasActiveShortcuts = true;
                }
                if (result.textTransformersTrimToPageShortcut) {
                    this.shortcuts['textTransformersTrimToPage'] = result.textTransformersTrimToPageShortcut;
                    hasActiveShortcuts = true;
                }
                if (result.textTransformersSortAlphabeticallyShortcut) {
                    this.shortcuts['textTransformersSortAlphabetically'] = result.textTransformersSortAlphabeticallyShortcut;
                    hasActiveShortcuts = true;
                }
                if (result.textTransformersRemoveEmptyLinesShortcut) {
                    this.shortcuts['textTransformersRemoveEmptyLines'] = result.textTransformersRemoveEmptyLinesShortcut;
                    hasActiveShortcuts = true;
                }
                if (result.textTransformersRemoveDuplicateLinesShortcut) {
                    this.shortcuts['textTransformersRemoveDuplicateLines'] = result.textTransformersRemoveDuplicateLinesShortcut;
                    hasActiveShortcuts = true;
                }
                if (result.textTimeMachineLauncherShortcut) {
                    this.shortcuts['textTimeMachineLauncher'] = result.textTimeMachineLauncherShortcut;
                    hasActiveShortcuts = true;
                }
                if (result.newTabRedirectShortcut) {
                    this.shortcuts['newTabRedirect'] = result.newTabRedirectShortcut;
                    hasActiveShortcuts = true;
                }
            }
            
            if (hasActiveShortcuts) {
                this.setupEventListeners();
                this.isInitialized = true;
                
                console.log('SEO Time Machines: Global shortcuts initialized', {
                    shortcuts: this.shortcuts,
                    platform: this.isMac ? 'Mac' : 'Windows/Linux',
                    isGooglePage: this.isGooglePage,
                    isInitialized: this.isInitialized,
                    copyElementEnabled: copyElementEnabled,
                    colorpickerEnabled: colorpickerEnabled,
                    screenshotEnabled: screenshotEnabled,
                    copyReplaceEnabled: copyReplaceEnabled,
                    textTransformersEnabled: textTransformersEnabled
                });
            } else {
                console.log('SEO Time Machines: Global shortcuts disabled - no enabled actions', {
                    copyElementEnabled: copyElementEnabled,
                    colorpickerEnabled: colorpickerEnabled,
                    screenshotEnabled: screenshotEnabled,
                    copyReplaceEnabled: copyReplaceEnabled,
                    textTransformersEnabled: textTransformersEnabled
                });
            }
        } catch (error) {
            console.error('SEO Time Machines: Error initializing shortcuts:', error);
        }
    }

    getDefaultShortcut() {
        // Platform-specific defaults for Copy Element
        return this.isMac ? 'Cmd+Shift+C' : 'Ctrl+Shift+C';
    }

    getDefaultColorpickerShortcut() {
        // Platform-specific defaults for Colorpicker
        return this.isMac ? 'Cmd+Shift+P' : 'Ctrl+Shift+P';
    }

    getDefaultScreenshotShortcut() {
        // Platform-specific defaults for Screenshot - avoid system conflicts
        return this.isMac ? 'Cmd+Shift+T' : 'Ctrl+Shift+T';
    }

    getDefaultCopyReplaceShortcut() {
        // Platform-specific defaults for Copy Replace
        return this.isMac ? 'Cmd+Shift+V' : 'Ctrl+Shift+V';
    }

    setupEventListeners() {
        // Listen for keyboard events with capture=true for higher priority
        document.addEventListener('keydown', (e) => this.handleKeydown(e), { capture: true, passive: false });
        
        // Listen for storage changes (both sync and local)
        chrome.storage.onChanged.addListener((changes, area) => {
            if (changes.copyElementShortcut) {
                this.shortcuts['copyElement'] = changes.copyElementShortcut.newValue;
            }
            if (changes.colorpickerShortcut) {
                this.shortcuts['colorpicker'] = changes.colorpickerShortcut.newValue;
            }
            if (changes.screenshotShortcut) {
                this.shortcuts['screenshot'] = changes.screenshotShortcut.newValue;
            }
            if (changes.copyReplaceShortcut) {
                this.shortcuts['copyReplace'] = changes.copyReplaceShortcut.newValue;
            }
            // Text Transformers shortcuts
            if (changes.textTransformersCapitalCaseShortcut) {
                this.shortcuts['textTransformersCapitalCase'] = changes.textTransformersCapitalCaseShortcut.newValue;
            }
            if (changes.textTransformersLowerCaseShortcut) {
                this.shortcuts['textTransformersLowerCase'] = changes.textTransformersLowerCaseShortcut.newValue;
            }
            if (changes.textTransformersUpperCaseShortcut) {
                this.shortcuts['textTransformersUpperCase'] = changes.textTransformersUpperCaseShortcut.newValue;
            }
            if (changes.textTransformersSentenceCaseShortcut) {
                this.shortcuts['textTransformersSentenceCase'] = changes.textTransformersSentenceCaseShortcut.newValue;
            }
            if (changes.textTransformersSlugifyShortcut) {
                this.shortcuts['textTransformersSlugify'] = changes.textTransformersSlugifyShortcut.newValue;
            }
            if (changes.textTransformersTrimToPageShortcut) {
                this.shortcuts['textTransformersTrimToPage'] = changes.textTransformersTrimToPageShortcut.newValue;
            }
            if (changes.textTransformersSortAlphabeticallyShortcut) {
                this.shortcuts['textTransformersSortAlphabetically'] = changes.textTransformersSortAlphabeticallyShortcut.newValue;
            }
            if (changes.textTransformersRemoveEmptyLinesShortcut) {
                this.shortcuts['textTransformersRemoveEmptyLines'] = changes.textTransformersRemoveEmptyLinesShortcut.newValue;
            }
            if (changes.textTransformersRemoveDuplicateLinesShortcut) {
                this.shortcuts['textTransformersRemoveDuplicateLines'] = changes.textTransformersRemoveDuplicateLinesShortcut.newValue;
            }
            if (changes.textTimeMachineLauncherShortcut) {
                this.shortcuts['textTimeMachineLauncher'] = changes.textTimeMachineLauncherShortcut.newValue;
            }
            if (changes.newTabRedirectShortcut) {
                this.shortcuts['newTabRedirect'] = changes.newTabRedirectShortcut.newValue;
            }
            if (changes.copyElementEnabled || changes.colorPickerEnabled || changes.screenshotToolEnabled || changes.gmbExtractorSettings) {
                // Re-check enabled settings when they change
                try {
                    Promise.all([
                        this.safeChromeStorageSyncGet(['copyElementEnabled', 'colorPickerEnabled', 'screenshotToolEnabled']),
                        safeChromeStorageGet(['gmbExtractorSettings'])
                    ]).then(([syncResult, localResult]) => {
                        const copyElementEnabled = syncResult.copyElementEnabled !== false;
                        const colorpickerEnabled = syncResult.colorPickerEnabled !== false;
                        const screenshotEnabled = syncResult.screenshotToolEnabled !== false;
                        const mainSettings = localResult.gmbExtractorSettings || {};
                        const copyReplaceEnabled = mainSettings.copyReplaceEnabled !== false;
                        const textTransformersEnabled = mainSettings.textTransformersEnabled === true;
                        const hasActiveShortcuts = copyElementEnabled || colorpickerEnabled || screenshotEnabled || copyReplaceEnabled || textTransformersEnabled;
                        
                        if (!hasActiveShortcuts) {
                            console.log('SEO Time Machines: Disabling shortcuts - no enabled actions', {
                                copyElementEnabled: copyElementEnabled,
                                colorpickerEnabled: colorpickerEnabled,
                                screenshotEnabled: screenshotEnabled,
                                copyReplaceEnabled: copyReplaceEnabled,
                                textTransformersEnabled: textTransformersEnabled
                            });
                            this.isInitialized = false;
                        } else if (!this.isInitialized) {
                            console.log('SEO Time Machines: Re-enabling shortcuts - actions enabled');
                            this.init(); // Re-initialize if enabled
                        }
                    }).catch(error => {
                        console.log('SEO Time Machines: Could not refresh shortcuts after settings change:', error);
                    });
                } catch (error) {
                    console.log('SEO Time Machines: Extension context invalidated during settings change');
                }
            }
        });
    }

    handleKeydown(e) {
        if (!this.isInitialized) return;

        // Don't interfere with extension popups or chrome pages
        if (window.location.protocol === 'chrome-extension:' || 
            window.location.protocol === 'chrome:' ||
            e.target.closest('.stm-screenshot-overlay, .stm-screenshot-toolbar')) {
            return;
        }

        // Ignore standalone modifier key presses
        if (['Control', 'Shift', 'Alt', 'Meta'].includes(e.key)) {
            return;
        }

        const pressedShortcut = this.formatShortcut(e);
        if (!pressedShortcut) return;

        // Check if this is Copy Replace shortcut (allow in input fields)
        const copyReplaceShortcut = this.shortcuts['copyReplace'];
        const isCopyReplaceShortcut = copyReplaceShortcut && this.matchesShortcut(pressedShortcut, copyReplaceShortcut);
        
        // Check if this is a Text Transformers shortcut (allow in input fields)
        const isTextTransformersShortcut = (
            (this.shortcuts['textTransformersCapitalCase'] && this.matchesShortcut(pressedShortcut, this.shortcuts['textTransformersCapitalCase'])) ||
            (this.shortcuts['textTransformersLowerCase'] && this.matchesShortcut(pressedShortcut, this.shortcuts['textTransformersLowerCase'])) ||
            (this.shortcuts['textTransformersUpperCase'] && this.matchesShortcut(pressedShortcut, this.shortcuts['textTransformersUpperCase'])) ||
            (this.shortcuts['textTransformersSentenceCase'] && this.matchesShortcut(pressedShortcut, this.shortcuts['textTransformersSentenceCase'])) ||
            (this.shortcuts['textTransformersSlugify'] && this.matchesShortcut(pressedShortcut, this.shortcuts['textTransformersSlugify'])) ||
            (this.shortcuts['textTransformersTrimToPage'] && this.matchesShortcut(pressedShortcut, this.shortcuts['textTransformersTrimToPage'])) ||
            (this.shortcuts['textTransformersSortAlphabetically'] && this.matchesShortcut(pressedShortcut, this.shortcuts['textTransformersSortAlphabetically'])) ||
            (this.shortcuts['textTransformersRemoveEmptyLines'] && this.matchesShortcut(pressedShortcut, this.shortcuts['textTransformersRemoveEmptyLines'])) ||
            (this.shortcuts['textTransformersRemoveDuplicateLines'] && this.matchesShortcut(pressedShortcut, this.shortcuts['textTransformersRemoveDuplicateLines'])) ||
            (this.shortcuts['textTimeMachineLauncher'] && this.matchesShortcut(pressedShortcut, this.shortcuts['textTimeMachineLauncher'])) ||
            (this.shortcuts['newTabRedirect'] && this.matchesShortcut(pressedShortcut, this.shortcuts['newTabRedirect']))
        );

        // For shortcuts other than Copy Replace and Text Transformers, don't handle input if we're in an input field or editable content
        if (!isCopyReplaceShortcut && !isTextTransformersShortcut && (e.target.tagName === 'INPUT' || 
            e.target.tagName === 'TEXTAREA' || 
            e.target.isContentEditable ||
            e.target.closest('[contenteditable="true"]'))) {
            return;
        }

        let shortcutMatched = false;

        // Check if this matches our copy element shortcut
        const copyShortcut = this.shortcuts['copyElement'];
        if (copyShortcut && this.matchesShortcut(pressedShortcut, copyShortcut)) {
            // Additional check to prevent conflicts with other copy actions
            if (!e.target.closest('.copy-element-active, .screenshot-active')) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeCopyElement();
                shortcutMatched = true;
            }
        }

        // Check if this matches our colorpicker shortcut
        const colorpickerShortcut = this.shortcuts['colorpicker'];
        if (!shortcutMatched && colorpickerShortcut && this.matchesShortcut(pressedShortcut, colorpickerShortcut)) {
            e.preventDefault();
            e.stopImmediatePropagation();
            this.executeColorpicker();
            shortcutMatched = true;
        }

        // Check if this matches our screenshot shortcut
        const screenshotShortcut = this.shortcuts['screenshot'];
        if (!shortcutMatched && screenshotShortcut && this.matchesShortcut(pressedShortcut, screenshotShortcut)) {
            console.log('🔵 GlobalShortcuts: Screenshot shortcut MATCHED!', {
                pressedShortcut: pressedShortcut,
                expectedShortcut: screenshotShortcut,
                target: e.target.tagName
            });
            e.preventDefault();
            e.stopImmediatePropagation();
            this.executeScreenshot();
            shortcutMatched = true;
        }

        // Check Text Transformers shortcuts (not allowed in input fields)
        if (!shortcutMatched) {
            const textTransformersCapitalCaseShortcut = this.shortcuts['textTransformersCapitalCase'];
            if (textTransformersCapitalCaseShortcut && this.matchesShortcut(pressedShortcut, textTransformersCapitalCaseShortcut)) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeTextTransformersCapitalCase();
                shortcutMatched = true;
            }
        }

        if (!shortcutMatched) {
            const textTransformersLowerCaseShortcut = this.shortcuts['textTransformersLowerCase'];
            if (textTransformersLowerCaseShortcut && this.matchesShortcut(pressedShortcut, textTransformersLowerCaseShortcut)) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeTextTransformersLowerCase();
                shortcutMatched = true;
            }
        }

        if (!shortcutMatched) {
            const textTransformersUpperCaseShortcut = this.shortcuts['textTransformersUpperCase'];
            if (textTransformersUpperCaseShortcut && this.matchesShortcut(pressedShortcut, textTransformersUpperCaseShortcut)) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeTextTransformersUpperCase();
                shortcutMatched = true;
            }
        }

        if (!shortcutMatched) {
            const textTransformersSentenceCaseShortcut = this.shortcuts['textTransformersSentenceCase'];
            if (textTransformersSentenceCaseShortcut && this.matchesShortcut(pressedShortcut, textTransformersSentenceCaseShortcut)) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeTextTransformersSentenceCase();
                shortcutMatched = true;
            }
        }

        if (!shortcutMatched) {
            const textTransformersSlugifyShortcut = this.shortcuts['textTransformersSlugify'];
            if (textTransformersSlugifyShortcut && this.matchesShortcut(pressedShortcut, textTransformersSlugifyShortcut)) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeTextTransformersSlugify();
                shortcutMatched = true;
            }
        }
        if (!shortcutMatched) {
            const textTransformersTrimToPageShortcut = this.shortcuts['textTransformersTrimToPage'];
            if (textTransformersTrimToPageShortcut && this.matchesShortcut(pressedShortcut, textTransformersTrimToPageShortcut)) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeTextTransformersTrimToPage();
                shortcutMatched = true;
            }
        }
        if (!shortcutMatched) {
            const textTransformersSortAlphabeticallyShortcut = this.shortcuts['textTransformersSortAlphabetically'];
            if (textTransformersSortAlphabeticallyShortcut && this.matchesShortcut(pressedShortcut, textTransformersSortAlphabeticallyShortcut)) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeTextTransformersSortAlphabetically();
                shortcutMatched = true;
            }
        }
        if (!shortcutMatched) {
            const textTransformersRemoveEmptyLinesShortcut = this.shortcuts['textTransformersRemoveEmptyLines'];
            if (textTransformersRemoveEmptyLinesShortcut && this.matchesShortcut(pressedShortcut, textTransformersRemoveEmptyLinesShortcut)) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeTextTransformersRemoveEmptyLines();
                shortcutMatched = true;
            }
        }
        if (!shortcutMatched) {
            const textTransformersRemoveDuplicateLinesShortcut = this.shortcuts['textTransformersRemoveDuplicateLines'];
            if (textTransformersRemoveDuplicateLinesShortcut && this.matchesShortcut(pressedShortcut, textTransformersRemoveDuplicateLinesShortcut)) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeTextTransformersRemoveDuplicateLines();
                shortcutMatched = true;
            }
        }
        if (!shortcutMatched) {
            const textTimeMachineLauncherShortcut = this.shortcuts['textTimeMachineLauncher'];
            if (textTimeMachineLauncherShortcut && this.matchesShortcut(pressedShortcut, textTimeMachineLauncherShortcut)) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeTextTimeMachineLauncher();
                shortcutMatched = true;
            }
        }
        if (!shortcutMatched) {
            const newTabRedirectShortcut = this.shortcuts['newTabRedirect'];
            if (newTabRedirectShortcut && this.matchesShortcut(pressedShortcut, newTabRedirectShortcut)) {
                e.preventDefault();
                e.stopImmediatePropagation();
                this.executeNewTabRedirect();
                shortcutMatched = true;
            }
        }

        // Check if this matches our copy replace shortcut (already checked above for input field logic)
        if (!shortcutMatched && isCopyReplaceShortcut) {
            e.preventDefault();
            e.stopImmediatePropagation();
            this.executeCopyReplace(e.target);
            shortcutMatched = true;
        }
    }

    formatShortcut(e) {
        const parts = [];
        
        // Add modifiers in consistent order
        if (e.ctrlKey) parts.push('Ctrl');
        if (e.altKey) parts.push('Alt');
        if (e.metaKey) parts.push('Cmd');
        if (e.shiftKey) parts.push('Shift');
        
        // Must have at least one modifier
        if (parts.length === 0) return null;

        // Special keys mapping
        const specialKeys = {
            'arrowup': '↑',
            'arrowdown': '↓',
            'arrowleft': '←',
            'arrowright': '→',
            'enter': '↵',
            'tab': '⇥',
            'escape': 'Esc',
            'backspace': '⌫',
            'delete': '⌦',
            'space': ' '
        };

        const key = e.key;
        const keyLower = key.toLowerCase();

        // Handle different types of keys
        if (specialKeys[keyLower]) {
            parts.push(specialKeys[keyLower]);
        } else if (/^f(1[0-2]|[1-9])$/i.test(keyLower)) {
            // Function keys
            parts.push(keyLower.toUpperCase());
        } else if (key.length === 1) {
            // Single characters - preserve exact character
            parts.push(key);
        } else {
            return null;
        }
        
        return parts.join('+');
    }

    matchesShortcut(pressed, saved) {
        if (!pressed || !saved) return false;
        
        // Normalize both shortcuts for comparison
        return this.normalizeShortcut(pressed) === this.normalizeShortcut(saved);
    }

    normalizeShortcut(shortcut) {
        if (!shortcut) return '';
        
        // Convert display shortcuts to standard format
        return shortcut
            .replace(/⌃/g, 'Ctrl')
            .replace(/⌥/g, 'Alt')
            .replace(/⌘/g, 'Cmd')
            .replace(/⇧/g, 'Shift')
            .replace(/Command/g, 'Cmd')
            .replace(/Control/g, 'Ctrl')
            .replace(/Option/g, 'Alt');
    }

    // Duplicate Shortcut Detection System
    checkShortcutConflict(newShortcut, excludeKey = null) {
        if (!newShortcut || !this.shortcuts) {
            return { hasConflict: false };
        }

        const normalizedNew = this.normalizeShortcut(newShortcut);
        
        // Tool name mappings for user-friendly display
        const toolNames = {
            'copyElement': 'Copy Element',
            'colorpicker': 'Color Picker', 
            'screenshot': 'Screenshot Tool',
            'copyReplace': 'Copy Replace',
            'textTransformersCapitalCase': 'Text Transformers (Capital Case)',
            'textTransformersLowerCase': 'Text Transformers (Lower Case)',
            'textTransformersUpperCase': 'Text Transformers (Upper Case)',
            'textTransformersSentenceCase': 'Text Transformers (Sentence Case)',
            'textTransformersSlugify': 'Text Transformers (Slugify)',
            'textTransformersTrimToPage': 'Text Transformers (Trim to Page)',
            'textTransformersSortAlphabetically': 'Text Transformers (Sort Alphabetically)',
            'textTransformersRemoveEmptyLines': 'Text Transformers (Remove Empty Lines)',
            'textTransformersRemoveDuplicateLines': 'Text Transformers (Remove Duplicate Lines)',
            'textTimeMachineLauncher': 'Text Time Machine Launcher',
            'newTabRedirect': 'New Tab Redirect'
        };

        // Check each existing shortcut for conflicts
        for (const [key, shortcut] of Object.entries(this.shortcuts)) {
            // Skip the key we're updating (allows changing same shortcut)
            if (excludeKey && key === excludeKey) {
                continue;
            }

            if (this.matchesShortcut(normalizedNew, shortcut)) {
                return {
                    hasConflict: true,
                    conflictingTool: toolNames[key] || key,
                    conflictingKey: key,
                    conflictingShortcut: shortcut
                };
            }
        }

        return { hasConflict: false };
    }

    // Visual feedback system for shortcut input errors
    showShortcutError(inputElement, message, type = 'error') {
        if (!inputElement) return;

        // Remove any existing error displays for this input
        const existingError = inputElement.parentNode.querySelector('.shortcut-error-message');
        if (existingError) {
            existingError.remove();
        }

        // Create error message element
        const errorElement = document.createElement('div');
        errorElement.className = 'shortcut-error-message';
        errorElement.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #dc2626;
            color: #ffffff;
            padding: 4px 8px;
            font-size: 11px;
            font-weight: 500;
            border-radius: 0 0 4px 4px;
            border: 1px solid #b91c1c;
            border-top: none;
            z-index: 1000;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
            animation: shortcutErrorSlideIn 0.2s ease-out;
        `;

        // Add CSS animation if not already present
        if (!document.getElementById('shortcut-error-styles')) {
            const styles = document.createElement('style');
            styles.id = 'shortcut-error-styles';
            styles.textContent = `
                @keyframes shortcutErrorSlideIn {
                    from { transform: translateY(-100%); opacity: 0; }
                    to { transform: translateY(0); opacity: 1; }
                }
                @keyframes shortcutErrorSlideOut {
                    from { transform: translateY(0); opacity: 1; }
                    to { transform: translateY(-100%); opacity: 0; }
                }
                .shortcut-input-error {
                    border-color: #dc2626 !important;
                    box-shadow: 0 0 0 1px #dc2626 !important;
                }
            `;
            document.head.appendChild(styles);
        }

        errorElement.textContent = message;

        // Ensure parent has relative positioning
        const parent = inputElement.parentNode;
        const originalPosition = parent.style.position;
        if (!originalPosition || originalPosition === 'static') {
            parent.style.position = 'relative';
        }

        // Add error styling to input
        inputElement.classList.add('shortcut-input-error');

        // Insert error element after input
        parent.appendChild(errorElement);

        // Auto-remove after 5 seconds or when input changes
        const removeError = () => {
            if (errorElement && errorElement.parentNode) {
                errorElement.style.animation = 'shortcutErrorSlideOut 0.2s ease-in';
                setTimeout(() => {
                    if (errorElement.parentNode) {
                        errorElement.remove();
                    }
                }, 200);
            }
            inputElement.classList.remove('shortcut-input-error');
            inputElement.removeEventListener('input', removeError);
            inputElement.removeEventListener('focus', removeError);
        };

        // Auto-remove timeout
        setTimeout(removeError, 5000);

        // Remove on input change or focus
        inputElement.addEventListener('input', removeError, { once: true });
        inputElement.addEventListener('focus', removeError, { once: true });

        return errorElement;
    }

    // Create overwrite confirmation dialog
    showOverwriteConfirmation(conflictInfo, newShortcut) {
        return new Promise((resolve) => {
            // Remove any existing confirmation dialogs
            const existing = document.querySelector('.shortcut-overwrite-dialog');
            if (existing) {
                existing.remove();
            }

            // Create overlay
            const overlay = document.createElement('div');
            overlay.className = 'shortcut-overwrite-dialog';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.7);
                z-index: 10000000;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
            `;

            // Create dialog
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                background: #0a0a0a;
                border: 2px solid #7C3AED;
                border-radius: 12px;
                padding: 24px;
                max-width: 400px;
                width: 90%;
                color: #d1d5db;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            `;

            dialog.innerHTML = `
                <div style="margin-bottom: 16px;">
                    <h3 style="margin: 0 0 8px 0; color: #f59e0b; font-size: 18px; font-weight: 600;">
                        ⚠️ Shortcut Already Used
                    </h3>
                    <p style="margin: 0; font-size: 14px; line-height: 1.5; color: #9ca3af;">
                        The shortcut <strong style="color: #7C3AED;">${newShortcut}</strong> is already assigned to:
                    </p>
                    <p style="margin: 8px 0 0 0; font-size: 16px; font-weight: 600; color: #d1d5db;">
                        ${conflictInfo.conflictingTool}
                    </p>
                </div>
                <div style="margin-bottom: 20px; padding: 12px; background: #1a1a1a; border-radius: 6px; border-left: 3px solid #f59e0b;">
                    <p style="margin: 0; font-size: 13px; color: #9ca3af;">
                        Do you want to remove it from <strong>${conflictInfo.conflictingTool}</strong> and assign it to the new function?
                    </p>
                </div>
                <div style="display: flex; gap: 12px; justify-content: flex-end;">
                    <button id="shortcut-cancel-btn" style="
                        padding: 10px 20px;
                        background: #374151;
                        color: #d1d5db;
                        border: 1px solid #4b5563;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        transition: all 0.2s;
                    ">Cancel</button>
                    <button id="shortcut-overwrite-btn" style="
                        padding: 10px 20px;
                        background: #dc2626;
                        color: #ffffff;
                        border: 1px solid #b91c1c;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        transition: all 0.2s;
                    ">Overwrite</button>
                </div>
            `;

            overlay.appendChild(dialog);
            document.body.appendChild(overlay);

            // Add event listeners
            const cancelBtn = dialog.querySelector('#shortcut-cancel-btn');
            const overwriteBtn = dialog.querySelector('#shortcut-overwrite-btn');

            const cleanup = () => {
                overlay.remove();
            };

            cancelBtn.addEventListener('click', () => {
                cleanup();
                resolve(false);
            });

            overwriteBtn.addEventListener('click', () => {
                cleanup();
                resolve(true);
            });

            // ESC key to cancel
            const escHandler = (e) => {
                if (e.key === 'Escape') {
                    cleanup();
                    document.removeEventListener('keydown', escHandler);
                    resolve(false);
                }
            };
            document.addEventListener('keydown', escHandler);

            // Click outside to cancel
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    cleanup();
                    resolve(false);
                }
            });
        });
    }

    // Get all active shortcuts for debugging/display
    getAllActiveShortcuts() {
        const toolNames = {
            'copyElement': 'Copy Element',
            'colorpicker': 'Color Picker', 
            'screenshot': 'Screenshot Tool',
            'copyReplace': 'Copy Replace',
            'textTransformersCapitalCase': 'Text Transformers (Capital Case)',
            'textTransformersLowerCase': 'Text Transformers (Lower Case)',
            'textTransformersUpperCase': 'Text Transformers (Upper Case)',
            'textTransformersSentenceCase': 'Text Transformers (Sentence Case)',
            'textTransformersSlugify': 'Text Transformers (Slugify)',
            'textTransformersTrimToPage': 'Text Transformers (Trim to Page)',
            'textTransformersSortAlphabetically': 'Text Transformers (Sort Alphabetically)',
            'textTransformersRemoveEmptyLines': 'Text Transformers (Remove Empty Lines)',
            'textTransformersRemoveDuplicateLines': 'Text Transformers (Remove Duplicate Lines)',
            'textTimeMachineLauncher': 'Text Time Machine Launcher',
            'newTabRedirect': 'New Tab Redirect'
        };

        return Object.entries(this.shortcuts).map(([key, shortcut]) => ({
            key,
            shortcut,
            toolName: toolNames[key] || key,
            normalized: this.normalizeShortcut(shortcut)
        }));
    }


    async executeCopyElement() {
        console.log('SEO Time Machines: Copy Element shortcut triggered on:', window.location.hostname);
        
        // Check if we're on a restricted page
        const isRestrictedPage = window.location.protocol === 'chrome:' ||
                               window.location.protocol === 'chrome-extension:' ||
                               window.location.protocol === 'moz-extension:' ||
                               window.location.protocol === 'edge:' ||
                               window.location.protocol === 'opera:' ||
                               window.location.href.includes('about:');
        
        if (isRestrictedPage) {
            console.warn('SEO Time Machines: Cannot use Copy Element on restricted page');
            this.showNotification('⚠️ Copy Element cannot be used on browser internal pages');
            return;
        }
        
        try {
            // Double-check settings before execution as safety measure
            const result = await chrome.storage.sync.get(['copyElementEnabled']);
            const copyElementEnabled = result.copyElementEnabled !== false;
            
            if (!copyElementEnabled) {
                console.log('SEO Time Machines: Copy Element execution blocked - Copy Element is disabled', {
                    copyElementEnabled: copyElementEnabled
                });
                return;
            }
            
            // Copy Element should now be directly available since it's loaded with content scripts
            if (typeof window.CopyElementAction !== 'undefined') {
                console.log('SEO Time Machines: Copy Element available, executing directly');
                window.CopyElementAction.execute();
            } else {
                console.error('SEO Time Machines: Copy Element not available - this should not happen');
                this.showNotification('⚠️ Copy Element not available');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Copy Element:', error);
            this.showNotification('Error executing Copy Element');
        }
    }

    async executeColorpicker() {
        try {
            // Double-check settings before execution as safety measure
            const result = await chrome.storage.sync.get(['colorPickerEnabled']);
            const colorpickerEnabled = result.colorPickerEnabled !== false;
            
            if (!colorpickerEnabled) {
                console.log('SEO Time Machines: Colorpicker execution blocked - Colorpicker is disabled', {
                    colorpickerEnabled: colorpickerEnabled
                });
                return;
            }
            
            // Load the Colorpicker script and execute it
            await this.loadAndExecuteColorpicker();
        } catch (error) {
            console.error('SEO Time Machines: Error executing Colorpicker:', error);
            this.showNotification('Error executing Color Picker');
        }
    }

    executeScreenshot() {
        console.log('🔵 GlobalShortcuts: Screenshot shortcut TRIGGERED on:', window.location.hostname);
        
        try {
            // Log available screenshot functions
            console.log('🔵 GlobalShortcuts: Available screenshot functions:', {
                startScreenshotSelection: typeof window.startScreenshotSelection,
                startAreaSelection: typeof window.startAreaSelection,
                STMScreenshotSelectorActive: !!window.STMScreenshotSelectorActive
            });
            
            // Check if new screenshot selector function is available (Handy Screenshot approach)
            if (typeof window.startScreenshotSelection === 'function') {
                console.log('🔵 GlobalShortcuts: Starting screenshot selection (NEW method)...');
                window.startScreenshotSelection();
            } else if (typeof window.startAreaSelection === 'function') {
                // Fallback to old function for backward compatibility
                console.log('🔵 GlobalShortcuts: Using fallback area selection (OLD method)...');
                window.startAreaSelection();
            } else {
                console.error('🔴 GlobalShortcuts: Screenshot selector NOT LOADED - no functions available');
                this.showNotification('Screenshot tool not ready - please reload the page');
            }
            
        } catch (error) {
            console.error('SEO Time Machines: Error executing Screenshot:', error);
            this.showNotification('Screenshot error: ' + error.message);
        }
    }

    async executeCopyReplace(targetElement) {
        console.log('SEO Time Machines: Copy Replace shortcut triggered on:', window.location.hostname);
        
        try {
            // Double-check settings before execution as safety measure
            const result = await safeChromeStorageGet(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            const copyReplaceEnabled = settings.copyReplaceEnabled !== false;
            
            if (!copyReplaceEnabled) {
                console.log('SEO Time Machines: Copy Replace execution blocked - Copy Replace is disabled', {
                    copyReplaceEnabled: copyReplaceEnabled
                });
                return;
            }
            
            const sourceDomain = settings.copyReplaceSourceDomain || '';
            const targetDomain = settings.copyReplaceTargetDomain || '';
            const debugMode = settings.debugMode || false;
            
            console.log('SEO Time Machines: Copy Replace settings loaded:', {
                sourceDomain: sourceDomain,
                targetDomain: targetDomain,
                targetElement: targetElement?.tagName
            });
            
            // Check if domains are configured
            if (!sourceDomain || !targetDomain) {
                this.showNotification('⚠️ Copy Replace: Please configure source and target domains in settings');
                return;
            }
            
            // Check clipboard permissions
            if (!navigator.clipboard) {
                this.showNotification('❌ Copy Replace: Clipboard API not available');
                return;
            }
            
            // Read clipboard content
            const clipboardText = await navigator.clipboard.readText();
            
            if (!clipboardText) {
                this.showNotification('⚠️ Copy Replace: Clipboard is empty');
                return;
            }
            
            console.log('SEO Time Machines: Copy Replace clipboard content:', {
                length: clipboardText.length,
                preview: clipboardText.substring(0, 100),
                containsSourceDomain: clipboardText.includes(sourceDomain)
            });
            
            // Check if clipboard contains a URL with the source domain
            if (!clipboardText.includes(sourceDomain)) {
                this.showNotification(`⚠️ Copy Replace: Clipboard doesn't contain "${sourceDomain}"`);
                return;
            }
            
            // Perform domain replacement
            const replacedText = this.replaceUrlDomain(clipboardText, sourceDomain, targetDomain);
            
            if (replacedText === clipboardText) {
                this.showNotification('⚠️ Copy Replace: No changes made to URL');
                return;
            }
            
            // Write back to clipboard
            await navigator.clipboard.writeText(replacedText);
            
            // Simulate paste operation
            await this.simulatePaste(replacedText, targetElement);
            
            console.log('SEO Time Machines: Copy Replace completed:', {
                originalLength: clipboardText.length,
                replacedLength: replacedText.length,
                changed: replacedText !== clipboardText
            });
            
            // Success - no notification needed, operation completed silently
            
        } catch (error) {
            console.error('SEO Time Machines: Error executing Copy Replace:', error);
            
            if (error.name === 'NotAllowedError') {
                this.showNotification('❌ Copy Replace: Clipboard access denied');
            } else {
                this.showNotification('❌ Copy Replace: Operation failed');
            }
        }
    }

    replaceUrlDomain(text, sourceDomain, targetDomain) {
        try {
            // Clean up domains for comparison
            const cleanSourceDomain = sourceDomain.replace(/^https?:\/\//, '').replace(/\/$/, '');
            let cleanTargetDomain = targetDomain.replace(/^https?:\/\//, '').replace(/\/$/, '');
            
            // Pattern to match URLs with the source domain
            const urlPattern = new RegExp(`(https?://)?(www\\.)?${this.escapeRegExp(cleanSourceDomain)}(/.*)?(\\?.*?)?(#.*)?`, 'gi');
            
            const replacedText = text.replace(urlPattern, (match, protocol, www, path, query, hash) => {
                // Construct the replacement URL - ALWAYS start with HTTPS
                let replacement = 'https://' + cleanTargetDomain;
                
                // Add path, query, and hash if they exist
                if (path) replacement += path;
                if (query) replacement += query;
                if (hash) replacement += hash;
                
                return replacement;
            });
            
            return replacedText;
            
        } catch (error) {
            console.error('SEO Time Machines: Error during domain replacement:', error);
            return text; // Return original text if replacement fails
        }
    }

    escapeRegExp(string) {
        // Escape special regex characters
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    async simulatePaste(text, targetElement) {
        try {
            // Use the provided target element or fall back to the currently focused element
            const activeElement = targetElement || document.activeElement;
            
            console.log('SEO Time Machines: Copy Replace paste simulation:', {
                text: text.substring(0, 50) + '...',
                targetProvided: !!targetElement,
                activeElementTag: activeElement?.tagName,
                activeElementType: activeElement?.type
            });
            
            if (activeElement && (activeElement.tagName === 'INPUT' || 
                                 activeElement.tagName === 'TEXTAREA' || 
                                 activeElement.isContentEditable)) {
                
                if (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA') {
                    // Handle input/textarea
                    const start = activeElement.selectionStart;
                    const end = activeElement.selectionEnd;
                    const currentValue = activeElement.value;
                    
                    activeElement.value = currentValue.substring(0, start) + text + currentValue.substring(end);
                    activeElement.selectionStart = activeElement.selectionEnd = start + text.length;
                    
                    // Trigger input event
                    activeElement.dispatchEvent(new Event('input', { bubbles: true }));
                    console.log('SEO Time Machines: Copy Replace pasted into INPUT/TEXTAREA');
                    
                } else if (activeElement.isContentEditable) {
                    // Handle contenteditable
                    const selection = window.getSelection();
                    if (selection.rangeCount > 0) {
                        const range = selection.getRangeAt(0);
                        range.deleteContents();
                        range.insertNode(document.createTextNode(text));
                        range.collapse(false);
                        selection.removeAllRanges();
                        selection.addRange(range);
                        console.log('SEO Time Machines: Copy Replace pasted into contenteditable');
                    }
                }
                
            } else {
                console.log('SEO Time Machines: Copy Replace - no suitable paste target, text in clipboard');
            }
            
        } catch (error) {
            console.error('SEO Time Machines: Error during Copy Replace paste simulation:', error);
        }
    }


    async loadAndExecuteColorpicker() {
        try {
            // ColorPickerAction should always be available now as it's loaded as a content script
            if (typeof window.ColorPickerAction !== 'undefined') {
                console.log('SEO Time Machines: Executing ColorPickerAction');
                window.ColorPickerAction.execute();
                this.showNotification('Color Picker activated - click to pick colors');
            } else {
                console.error('SEO Time Machines: ColorPickerAction not available');
                this.showNotification('Color Picker could not be loaded');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Color Picker:', error);
            this.showNotification('Error executing Color Picker');
        }
    }


    cleanupCopyElement() {
        // Use proper Copy Element reset if available
        if (typeof window.CopyElementAction !== 'undefined' && window.CopyElementAction.reset) {
            window.CopyElementAction.reset();
        } else if (window.copyElementCleanup) {
            window.copyElementCleanup();
        }
    }

    showInlineNotification(msg, type = 'success') {
        const existing = document.querySelector('.copy-element-notification');
        if (existing) {
            existing.remove();
        }

        const notif = document.createElement('div');
        notif.className = 'copy-element-notification';
        
        if (type === 'error') {
            notif.style.color = '#ef4444';
            notif.style.borderColor = '#ef4444';
        }
        
        notif.textContent = msg;
        document.body.appendChild(notif);

        setTimeout(() => notif.classList.add('show'), 10);
        setTimeout(() => {
            notif.classList.remove('show');
            setTimeout(() => {
                if (notif.parentNode) {
                    notif.remove();
                }
            }, 400);
        }, 1500);
    }

    fallbackCopy(text, message) {
        try {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            const successful = document.execCommand('copy');
            textArea.remove();
            
            if (successful) {
                this.showInlineNotification(message);
            } else {
                this.showInlineNotification('❌ Copy failed', 'error');
            }
            this.cleanupCopyElement();
        } catch (error) {
            console.error('Fallback copy failed:', error);
            this.showInlineNotification('❌ Copy failed', 'error');
            this.cleanupCopyElement();
        }
    }

    refreshShortcuts() {
        console.log('SEO Time Machines: Refreshing shortcuts...');
        this.init();
    }

    showNotification(message) {
        // Simple notification system
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('SEO Time Machines', { body: message });
        } else {
            console.log('SEO Time Machines:', message);
        }
    }

    // Text Transformers execution methods
    async executeTextTransformersCapitalCase() {
        console.log('SEO Time Machines: Text Transformers Capital Case shortcut triggered');
        
        try {
            if (typeof window.TextTransformers !== 'undefined') {
                const result = await window.TextTransformers.executeCapitalCase();
                if (result && result.error) {
                    console.error('SEO Time Machines: Text Transformers error:', result.error);
                } else {
                    console.log('SEO Time Machines: Text Transformers Capital Case completed:', result);
                }
            } else {
                console.error('SEO Time Machines: TextTransformers not available');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Text Transformers Capital Case:', error);
        }
    }

    async executeTextTransformersLowerCase() {
        console.log('SEO Time Machines: Text Transformers Lower Case shortcut triggered');
        
        try {
            if (typeof window.TextTransformers !== 'undefined') {
                const result = await window.TextTransformers.executeLowerCase();
                if (result && result.error) {
                    console.error('SEO Time Machines: Text Transformers error:', result.error);
                } else {
                    console.log('SEO Time Machines: Text Transformers Lower Case completed:', result);
                }
            } else {
                console.error('SEO Time Machines: TextTransformers not available');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Text Transformers Lower Case:', error);
        }
    }

    async executeTextTransformersUpperCase() {
        console.log('SEO Time Machines: Text Transformers Upper Case shortcut triggered');
        
        try {
            if (typeof window.TextTransformers !== 'undefined') {
                const result = await window.TextTransformers.executeUpperCase();
                if (result && result.error) {
                    console.error('SEO Time Machines: Text Transformers error:', result.error);
                } else {
                    console.log('SEO Time Machines: Text Transformers Upper Case completed:', result);
                }
            } else {
                console.error('SEO Time Machines: TextTransformers not available');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Text Transformers Upper Case:', error);
        }
    }

    async executeTextTransformersSentenceCase() {
        console.log('SEO Time Machines: Text Transformers Sentence Case shortcut triggered');
        
        try {
            if (typeof window.TextTransformers !== 'undefined') {
                const result = await window.TextTransformers.executeSentenceCase();
                if (result && result.error) {
                    console.error('SEO Time Machines: Text Transformers error:', result.error);
                } else {
                    console.log('SEO Time Machines: Text Transformers Sentence Case completed:', result);
                }
            } else {
                console.error('SEO Time Machines: TextTransformers not available');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Text Transformers Sentence Case:', error);
        }
    }

    async executeTextTransformersSlugify() {
        console.log('SEO Time Machines: Text Transformers Slugify shortcut triggered');
        
        try {
            if (typeof window.TextTransformers !== 'undefined') {
                const result = await window.TextTransformers.executeSlugify();
                if (result && result.error) {
                    console.error('SEO Time Machines: Text Transformers error:', result.error);
                } else {
                    console.log('SEO Time Machines: Text Transformers Slugify completed:', result);
                }
            } else {
                console.error('SEO Time Machines: TextTransformers not available');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Text Transformers Slugify:', error);
        }
    }
    async executeTextTransformersTrimToPage() {
        console.log('SEO Time Machines: Text Transformers Trim to Page shortcut triggered');
        
        try {
            if (typeof window.TextTransformers !== 'undefined') {
                const result = await window.TextTransformers.executeTrimToPage();
                if (result && result.error) {
                    console.error('SEO Time Machines: Text Transformers error:', result.error);
                } else {
                    console.log('SEO Time Machines: Text Transformers Trim to Page completed:', result);
                }
            } else {
                console.error('SEO Time Machines: TextTransformers not available');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Text Transformers Trim to Page:', error);
        }
    }
    
    async executeTextTransformersSortAlphabetically() {
        console.log('SEO Time Machines: Text Transformers Sort Alphabetically shortcut triggered');
        
        try {
            if (typeof window.TextTransformers !== 'undefined') {
                const result = await window.TextTransformers.executeSortAlphabetically();
                if (result && result.error) {
                    console.error('SEO Time Machines: Text Transformers error:', result.error);
                } else {
                    console.log('SEO Time Machines: Text Transformers Sort Alphabetically completed:', result);
                }
            } else {
                console.error('SEO Time Machines: TextTransformers not available');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Text Transformers Sort Alphabetically:', error);
        }
    }
    
    async executeTextTransformersRemoveEmptyLines() {
        console.log('SEO Time Machines: Text Transformers Remove Empty Lines shortcut triggered');
        
        try {
            if (typeof window.TextTransformers !== 'undefined') {
                const result = await window.TextTransformers.executeRemoveEmptyLines();
                if (result && result.error) {
                    console.error('SEO Time Machines: Text Transformers error:', result.error);
                } else {
                    console.log('SEO Time Machines: Text Transformers Remove Empty Lines completed:', result);
                }
            } else {
                console.error('SEO Time Machines: TextTransformers not available');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Text Transformers Remove Empty Lines:', error);
        }
    }
    
    async executeTextTransformersRemoveDuplicateLines() {
        console.log('SEO Time Machines: Text Transformers Remove Duplicate Lines shortcut triggered');
        
        try {
            if (typeof window.TextTransformers !== 'undefined') {
                const result = await window.TextTransformers.executeRemoveDuplicateLines();
                if (result && result.error) {
                    console.error('SEO Time Machines: Text Transformers error:', result.error);
                } else {
                    console.log('SEO Time Machines: Text Transformers Remove Duplicate Lines completed:', result);
                }
            } else {
                console.error('SEO Time Machines: TextTransformers not available');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Text Transformers Remove Duplicate Lines:', error);
        }
    }
    
    async executeTextTimeMachineLauncher() {
        console.log('SEO Time Machines: Text Time Machine Launcher shortcut triggered');
        
        try {
            if (typeof window.TextTransformers !== 'undefined') {
                const result = await window.TextTransformers.executeTextTimeMachineLauncher();
                if (result && result.error) {
                    console.error('SEO Time Machines: Text Time Machine Launcher error:', result.error);
                } else {
                    console.log('SEO Time Machines: Text Time Machine Launcher completed:', result);
                }
            } else {
                console.error('SEO Time Machines: TextTransformers not available');
            }
        } catch (error) {
            console.error('SEO Time Machines: Error executing Text Time Machine Launcher:', error);
        }
    }

    async executeNewTabRedirect() {
        console.log('🔴 DEBUG: New Tab Redirect shortcut triggered - Starting execution trace');
        
        // Show user notification for debugging
        this.showNotification('🔴 New Tab Redirect shortcut triggered - Check console for debug info');
        
        console.log('🔴 DEBUG: Current context:', {
            location: window.location.href,
            hasWindow: typeof window !== 'undefined',
            hasChrome: typeof chrome !== 'undefined',
            hasTextTransformers: typeof window.TextTransformers !== 'undefined'
        });
        
        try {
            // Step 1: Check TextTransformers availability
            if (typeof window.TextTransformers !== 'undefined') {
                console.log('🔴 DEBUG: TextTransformers is available, calling executeNewTabRedirect()');
                console.log('🔴 DEBUG: TextTransformers object:', window.TextTransformers);
                
                // Step 2: Call the method and trace result
                const result = await window.TextTransformers.executeNewTabRedirect();
                console.log('🔴 DEBUG: TextTransformers.executeNewTabRedirect() returned:', result);
                
                // Step 3: Check result
                if (result && result.error) {
                    console.error('🔴 DEBUG: New Tab Redirect error detected:', result.error);
                    console.error('SEO Time Machines: New Tab Redirect error:', result.error);
                } else {
                    console.log('🔴 DEBUG: New Tab Redirect completed successfully:', result);
                    console.log('SEO Time Machines: New Tab Redirect completed:', result);
                }
            } else {
                console.error('🔴 DEBUG: TextTransformers not available!');
                console.error('🔴 DEBUG: Available window properties:', Object.keys(window).filter(key => key.includes('Text')));
                console.error('SEO Time Machines: TextTransformers not available');
                
                // Fallback attempt: Try to load TextTransformers
                console.log('🔴 DEBUG: Attempting to access TextTransformers directly...');
                if (window.TextTransformers) {
                    console.log('🔴 DEBUG: Found TextTransformers via direct access!');
                } else {
                    console.error('🔴 DEBUG: TextTransformers completely unavailable');
                }
            }
        } catch (error) {
            console.error('🔴 DEBUG: Exception caught in executeNewTabRedirect:', error);
            console.error('🔴 DEBUG: Error stack:', error.stack);
            console.error('SEO Time Machines: Error executing New Tab Redirect:', error);
        }
        
        console.log('🔴 DEBUG: New Tab Redirect execution trace completed');
    }

}

// Global Shortcut Validation Function
// This function can be called from any shortcut input field to validate and handle duplicates
window.validateShortcut = async function(inputElement, newShortcut, currentTool, currentKey, options = {}) {
    return new Promise(async (resolve) => {
        try {
            // Default options
            const opts = {
                allowEmpty: false,
                showVisualFeedback: true,
                autoSave: false,
                storageKey: null,
                ...options
            };

            // Clear any existing errors first
            if (opts.showVisualFeedback && inputElement) {
                const existingError = inputElement.parentNode.querySelector('.shortcut-error-message');
                if (existingError) {
                    existingError.remove();
                }
                inputElement.classList.remove('shortcut-input-error');
            }

            // Check if shortcut is empty
            if (!newShortcut || newShortcut.trim() === '') {
                if (!opts.allowEmpty && opts.showVisualFeedback && inputElement) {
                    window.globalShortcutManager?.showShortcutError(
                        inputElement, 
                        'Shortcut cannot be empty'
                    );
                    resolve(false);
                    return;
                }
                resolve(true);
                return;
            }

            // Validate shortcut format (must have at least one modifier)
            const hasModifier = newShortcut.includes('Ctrl') || 
                               newShortcut.includes('Cmd') || 
                               newShortcut.includes('Alt') || 
                               newShortcut.includes('Shift') ||
                               newShortcut.includes('⌃') || 
                               newShortcut.includes('⌘') || 
                               newShortcut.includes('⌥') || 
                               newShortcut.includes('⇧');

            if (!hasModifier) {
                if (opts.showVisualFeedback && inputElement) {
                    window.globalShortcutManager?.showShortcutError(
                        inputElement, 
                        'Shortcut must include a modifier key (Ctrl, Alt, Cmd, or Shift)'
                    );
                }
                resolve(false);
                return;
            }

            // Check for conflicts using the global shortcut manager
            if (!window.globalShortcutManager) {
                console.warn('Global shortcut manager not available for validation');
                resolve(true);
                return;
            }

            const conflictResult = window.globalShortcutManager.checkShortcutConflict(newShortcut, currentKey);
            
            if (!conflictResult.hasConflict) {
                // No conflict - shortcut is valid
                console.log('SEO Time Machines: Shortcut validation passed:', newShortcut);
                resolve(true);
                return;
            }

            // Conflict detected
            console.log('SEO Time Machines: Shortcut conflict detected:', {
                newShortcut,
                currentTool,
                conflictResult
            });

            if (opts.showVisualFeedback && inputElement) {
                window.globalShortcutManager.showShortcutError(
                    inputElement, 
                    `Already used by ${conflictResult.conflictingTool}`
                );
            }

            // Ask user if they want to overwrite
            const shouldOverwrite = await window.globalShortcutManager.showOverwriteConfirmation(
                conflictResult, 
                newShortcut
            );

            if (shouldOverwrite) {
                console.log('SEO Time Machines: User chose to overwrite shortcut');
                
                // Clear the conflicting shortcut from storage
                try {
                    // Determine storage type and key for the conflicting shortcut
                    const conflictingKey = conflictResult.conflictingKey;
                    
                    // Map shortcut keys to their storage keys
                    const storageKeyMap = {
                        'copyElement': 'copyElementShortcut',
                        'colorpicker': 'colorpickerShortcut',
                        'screenshot': 'screenshotShortcut',
                        'copyReplace': 'copyReplaceShortcut',
                        'textTransformersCapitalCase': 'textTransformersCapitalCaseShortcut',
                        'textTransformersLowerCase': 'textTransformersLowerCaseShortcut',
                        'textTransformersUpperCase': 'textTransformersUpperCaseShortcut',
                        'textTransformersSentenceCase': 'textTransformersSentenceCaseShortcut',
                        'textTransformersSlugify': 'textTransformersSlugifyShortcut',
                        'textTransformersTrimToPage': 'textTransformersTrimToPageShortcut',
                        'textTransformersSortAlphabetically': 'textTransformersSortAlphabeticallyShortcut',
                        'textTransformersRemoveEmptyLines': 'textTransformersRemoveEmptyLinesShortcut',
                        'textTransformersRemoveDuplicateLines': 'textTransformersRemoveDuplicateLinesShortcut',
                        'textTimeMachineLauncher': 'textTimeMachineLauncherShortcut',
                        'newTabRedirect': 'newTabRedirectShortcut'
                    };

                    const storageKey = storageKeyMap[conflictingKey];
                    
                    if (storageKey) {
                        // Clear the conflicting shortcut
                        if (conflictingKey === 'copyReplace') {
                            // Copy Replace uses local storage
                            const settings = await safeChromeStorageGet(['gmbExtractorSettings']);
                            const gmbSettings = settings.gmbExtractorSettings || {};
                            delete gmbSettings.copyReplaceShortcut;
                            chrome.storage.local.set({ gmbExtractorSettings: gmbSettings });
                        } else {
                            // Other shortcuts use sync storage
                            const clearData = {};
                            clearData[storageKey] = '';
                            chrome.storage.sync.set(clearData);
                        }
                        
                        // Update the global shortcut manager's shortcuts object
                        delete window.globalShortcutManager.shortcuts[conflictingKey];
                        
                        // Update the visual input field for the conflicting shortcut
                        const inputSelectors = {
                            'copyElement': '#copyElementCustomShortcut',
                            'colorpicker': '#colorpickerCustomShortcut',
                            'screenshot': '#screenshotCustomShortcut',
                            'textTransformersCapitalCase': '#textTransformersCapitalCaseShortcut',
                            'textTransformersLowerCase': '#textTransformersLowerCaseShortcut',
                            'textTransformersUpperCase': '#textTransformersUpperCaseShortcut',
                            'textTransformersSentenceCase': '#textTransformersSentenceCaseShortcut',
                            'textTransformersSlugify': '#textTransformersSlugifyShortcut',
                            'textTransformersTrimToPage': '#textTransformersTrimToPageShortcut',
                            'textTransformersSortAlphabetically': '#textTransformersSortAlphabeticallyShortcut',
                            'textTransformersRemoveEmptyLines': '#textTransformersRemoveEmptyLinesShortcut',
                            'textTransformersRemoveDuplicateLines': '#textTransformersRemoveDuplicateLinesShortcut',
                            'textTimeMachineLauncher': '#textTimeMachineLauncherShortcut',
                            'newTabRedirect': '#newTabRedirectShortcut'
                        };
                        
                        const conflictingInputSelector = inputSelectors[conflictingKey];
                        if (conflictingInputSelector) {
                            const conflictingInputElement = document.querySelector(conflictingInputSelector);
                            if (conflictingInputElement) {
                                conflictingInputElement.value = '';
                                
                                // Clear any existing status messages for the conflicting input
                                const statusSelectors = {
                                    'copyElement': '#shortcutStatus',
                                    'colorpicker': '#colorpickerShortcutStatus',
                                    'screenshot': '#screenshotShortcutStatus'
                                };
                                
                                const statusSelector = statusSelectors[conflictingKey];
                                if (statusSelector) {
                                    const statusElement = document.querySelector(statusSelector);
                                    if (statusElement) {
                                        statusElement.textContent = 'Shortcut cleared (overwritten)';
                                        statusElement.className = 'shortcut-status info';
                                        
                                        // Clear status message after 3 seconds
                                        setTimeout(() => {
                                            statusElement.textContent = '';
                                            statusElement.className = 'shortcut-status';
                                        }, 3000);
                                    }
                                }
                                
                                console.log('SEO Time Machines: Cleared visual input for:', conflictingKey);
                            }
                        }
                        
                        console.log('SEO Time Machines: Cleared conflicting shortcut:', conflictingKey);
                    }
                } catch (error) {
                    console.error('SEO Time Machines: Error clearing conflicting shortcut:', error);
                }

                // Clear any error messages
                if (opts.showVisualFeedback && inputElement) {
                    const existingError = inputElement.parentNode.querySelector('.shortcut-error-message');
                    if (existingError) {
                        existingError.remove();
                    }
                    inputElement.classList.remove('shortcut-input-error');
                }

                resolve(true);
            } else {
                console.log('SEO Time Machines: User cancelled shortcut overwrite');
                resolve(false);
            }

        } catch (error) {
            console.error('SEO Time Machines: Error in shortcut validation:', error);
            if (opts.showVisualFeedback && inputElement) {
                window.globalShortcutManager?.showShortcutError(
                    inputElement, 
                    'Validation error occurred'
                );
            }
            resolve(false);
        }
    });
};

// Helper function to get current shortcuts for debugging
window.getCurrentShortcuts = function() {
    if (!window.globalShortcutManager) {
        console.warn('Global shortcut manager not available');
        return [];
    }
    return window.globalShortcutManager.getAllActiveShortcuts();
};

// Helper function to check a specific shortcut without UI
window.checkShortcutConflict = function(shortcut, excludeKey = null) {
    if (!window.globalShortcutManager) {
        console.warn('Global shortcut manager not available');
        return { hasConflict: false };
    }
    return window.globalShortcutManager.checkShortcutConflict(shortcut, excludeKey);
};

// Initialize the global shortcut manager on ALL pages
console.log('SEO Time Machines: Loading global shortcut manager on:', window.location.hostname);

// Initialize immediately
const globalShortcutManager = new GlobalShortcutManager();

// Export to window for access if needed
window.globalShortcutManager = globalShortcutManager; 