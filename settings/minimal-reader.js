// Minimal Reader Feature - Transform any webpage into a clean reading experience
(async () => {
  // Prevent multiple executions
  if (window.MinimalReaderLoaded) {
    return;
  }
  window.MinimalReaderLoaded = true;

  let isEnabled = true;
  let isActive = false;
  let settings = {
    theme: 'white',
    fontSize: 18,
    lineHeight: 1.6,
    fontWeight: 'normal',
    buttonPosition: 'top-left',
    speedReadingEnabled: false,
    stopWordFading: true
  };
  let blacklistDomains = []; // Dynamic blacklist loaded from storage

  // DOM elements
  let floatingButton = null;

  // --- Settings Management ---

  async function loadSettings() {
    try {
      console.log('Minimal Reader: Loading settings from storage');
      const data = await chrome.storage.local.get('gmbExtractorSettings');
      const storageSettings = data.gmbExtractorSettings || {};
      
      console.log('Minimal Reader: Raw storage data:', data);
      console.log('Minimal Reader: Extracted settings:', storageSettings);
      
      // Explicit boolean checks - CRITICAL (following golden rules like mass-unsubscribe.js)
      isEnabled = storageSettings.minimalReaderEnabled !== false; // Default to true
      
      // Load actual settings from chrome.storage
      settings.theme = storageSettings.minimalReaderTheme || 'white';
      settings.fontSize = storageSettings.minimalReaderFontSize || 18;
      settings.lineHeight = storageSettings.minimalReaderLineHeight || 1.6;
      settings.fontWeight = storageSettings.minimalReaderFontWeight || 'normal';
      settings.buttonPosition = storageSettings.minimalReaderButtonPosition || 'top-left';
      settings.speedReadingEnabled = storageSettings.minimalReaderSpeedReadingEnabled === true;
      settings.stopWordFading = storageSettings.minimalReaderStopWordFading !== false;
      
      // Load blacklist domains with validation
      const storedBlacklist = storageSettings.minimalReaderBlacklistDomains;
      if (Array.isArray(storedBlacklist)) {
        blacklistDomains = storedBlacklist;
        console.log('Minimal Reader: Loaded blacklist from storage:', blacklistDomains);
      } else {
        blacklistDomains = [];
        console.log('Minimal Reader: No valid blacklist found in storage, initialized empty array');
      }
      
      console.log('Minimal Reader: Settings loaded', { isEnabled, settings, blacklistDomains });
    } catch (e) {
      isEnabled = true; // Default to enabled if settings fail
      blacklistDomains = []; // Initialize empty array on error
      console.error('Error loading Minimal Reader settings:', e);
    }
  }

  // Listen for settings updates (following mass-unsubscribe.js pattern exactly)
  chrome.runtime.onMessage.addListener((request) => {
    if (request.action === 'updateSettings' && request.settings) {
      console.log('Minimal Reader: Received settings update:', request.settings);
      
      const newSetting = request.settings.minimalReaderEnabled !== false;
      const oldButtonPosition = settings.buttonPosition;
      
      // Update all settings
      isEnabled = newSetting;
      settings.theme = request.settings.minimalReaderTheme || 'white';
      settings.fontSize = request.settings.minimalReaderFontSize || 18;
      settings.lineHeight = request.settings.minimalReaderLineHeight || 1.6;
      settings.fontWeight = request.settings.minimalReaderFontWeight || 'normal';
      settings.buttonPosition = request.settings.minimalReaderButtonPosition || 'top-left';
      settings.speedReadingEnabled = request.settings.minimalReaderSpeedReadingEnabled === true;
      settings.stopWordFading = request.settings.minimalReaderStopWordFading !== false;
      
      // Update blacklist domains with validation
      const updatedBlacklist = request.settings.minimalReaderBlacklistDomains;
      if (Array.isArray(updatedBlacklist)) {
        blacklistDomains = updatedBlacklist;
        console.log('Minimal Reader: Updated blacklist from message:', blacklistDomains);
      } else {
        console.warn('Minimal Reader: Invalid blacklist received in settings update, keeping current:', blacklistDomains);
      }
      
      console.log('Minimal Reader: Settings updated', { isEnabled, settings, blacklistDomains });
      
      if (isEnabled && isTargetPage()) {
        // If button position changed, recreate button
        if (oldButtonPosition !== settings.buttonPosition) {
          removeButton();
          addMinimalReaderButton();
        } else if (!document.getElementById('minimal-reader-btn')) {
          addMinimalReaderButton();
        }
        
        // Update interface settings if reader is open
        if (isActive) {
          window.postMessage({
            type: 'MINIMAL_READER_UPDATE_SETTINGS',
            settings: settings
          }, '*');
        }
      } else {
        removeButton();
      }
    }
  });

  // --- Button Management (following mass-unsubscribe.js pattern) ---

  function addMinimalReaderButton() {
    // Check if we're on a valid page
    if (!isTargetPage()) {
      return;
    }

    // Remove existing button if any
    const existingButton = document.getElementById('minimal-reader-btn');
    if (existingButton) {
      existingButton.remove();
    }

    // Only add button if enabled
    if (!isEnabled) {
      return;
    }

    // Create floating button
    const button = document.createElement('button');
    button.id = 'minimal-reader-btn';
    button.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z" fill="currentColor"/>
      </svg>
    `;
    
    // Apply positioning based on settings
    const position = getButtonPosition();
    
    button.style.cssText = `
      position: fixed !important;
      ${position.top ? `top: ${position.top} !important;` : ''}
      ${position.bottom ? `bottom: ${position.bottom} !important;` : ''}
      ${position.left ? `left: ${position.left} !important;` : ''}
      ${position.right ? `right: ${position.right} !important;` : ''}
      width: 48px !important;
      height: 48px !important;
      background: #7C3AED !important;
      border: 2px solid #7C3AED !important;
      border-radius: 50% !important;
      cursor: pointer !important;
      z-index: 2147483647 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      color: white !important;
      box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3) !important;
      transition: all 0.2s ease !important;
      user-select: none !important;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif !important;
      pointer-events: all !important;
      visibility: visible !important;
      opacity: 1 !important;
      transform: scale(1) !important;
    `;

    // Hover effects
    button.addEventListener('mouseenter', () => {
      button.style.transform = 'scale(1.1) !important';
      button.style.boxShadow = '0 6px 16px rgba(124, 58, 237, 0.4) !important';
    });

    button.addEventListener('mouseleave', () => {
      button.style.transform = 'scale(1) !important';
      button.style.boxShadow = '0 4px 12px rgba(124, 58, 237, 0.3) !important';
    });

    // Alternative event handler to capture mousedown events (in case click doesn't work)
    button.addEventListener('mousedown', (event) => {
      console.log('Minimal Reader: Button mousedown event:', {
        type: event.type,
        button: event.button,
        buttons: event.buttons,
        shiftKey: event.shiftKey,
        ctrlKey: event.ctrlKey,
        altKey: event.altKey,
        metaKey: event.metaKey
      });
    });

    // Click handler with shift+click support for blacklisting
    button.addEventListener('click', async (event) => {
      try {
        console.log('Minimal Reader: Button clicked, shift key pressed:', event.shiftKey);
        console.log('Minimal Reader: Event details:', {
          type: event.type,
          button: event.button,
          buttons: event.buttons,
          shiftKey: event.shiftKey,
          ctrlKey: event.ctrlKey,
          altKey: event.altKey,
          metaKey: event.metaKey,
          target: event.target,
          currentTarget: event.currentTarget
        });
        
        // Check if shift key is pressed
        if (event.shiftKey) {
          console.log('Minimal Reader: Processing shift+click for blacklist');
          event.preventDefault();
          event.stopPropagation();
          
          // Add current domain to blacklist
          console.log('Minimal Reader: Calling addCurrentDomainToBlacklist()');
          const success = await addCurrentDomainToBlacklist();
          console.log('Minimal Reader: addCurrentDomainToBlacklist() returned:', success);
          
          if (success) {
            // Show notification
            const currentDomain = normalizeDomain(window.location.hostname);
            console.log(`Minimal Reader: "${currentDomain}" added to blacklist. Button will no longer appear on this domain.`);
            
            // Create a simple notification
            const notification = document.createElement('div');
            notification.style.cssText = `
              position: fixed;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              background: #0a0a0a;
              border: 2px solid #7C3AED;
              border-radius: 8px;
              padding: 16px 20px;
              z-index: 2147483647;
              color: #d1d5db;
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
              font-size: 14px;
              box-shadow: 0 8px 32px rgba(0,0,0,0.6);
              pointer-events: none;
            `;
            notification.textContent = `"${currentDomain}" added to blacklist`;
            document.body.appendChild(notification);
            
            // Remove notification after 2 seconds
            setTimeout(() => {
              if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
              }
            }, 2000);
          } else {
            console.error('Minimal Reader: Failed to add domain to blacklist');
            // Show error notification
            const notification = document.createElement('div');
            notification.style.cssText = `
              position: fixed;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              background: #dc2626;
              border: 2px solid #dc2626;
              border-radius: 8px;
              padding: 16px 20px;
              z-index: 2147483647;
              color: white;
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
              font-size: 14px;
              box-shadow: 0 8px 32px rgba(0,0,0,0.6);
              pointer-events: none;
            `;
            notification.textContent = 'Failed to add domain to blacklist';
            document.body.appendChild(notification);
            
            // Remove notification after 3 seconds
            setTimeout(() => {
              if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
              }
            }, 3000);
          }
          
          return;
        }
        
        // Normal click - toggle reader
        console.log('Minimal Reader: Normal click, toggling reader');
        toggleReader();
        
      } catch (error) {
        console.error('Minimal Reader: Error in click handler:', error);
        // Show error notification
        const notification = document.createElement('div');
        notification.style.cssText = `
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: #dc2626;
          border: 2px solid #dc2626;
          border-radius: 8px;
          padding: 16px 20px;
          z-index: 2147483647;
          color: white;
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
          font-size: 14px;
          box-shadow: 0 8px 32px rgba(0,0,0,0.6);
          pointer-events: none;
        `;
        notification.textContent = 'Error processing click';
        document.body.appendChild(notification);
        
        // Remove notification after 3 seconds
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 3000);
      }
    });

    // Add to page
    document.body.appendChild(button);
    floatingButton = button;

    // Floating button created (reduced logging for less spam)
  }

  function removeButton() {
    const existingButton = document.getElementById('minimal-reader-btn');
    if (existingButton) {
      existingButton.remove();
    }
    floatingButton = null;
    console.log('Minimal Reader: Button removed');
  }

  // Get button position CSS based on settings
  function getButtonPosition() {
    const offset = '20px';
    
    switch (settings.buttonPosition) {
      case 'top-left':
        return { top: offset, left: offset };
      case 'top-right':
        return { top: offset, right: offset };
      case 'bottom-left':
        return { bottom: offset, left: offset };
      case 'bottom-right':
        return { bottom: offset, right: offset };
      default:
        console.error('Minimal Reader: Unknown button position:', settings.buttonPosition);
        return { top: offset, left: offset };
    }
  }

  // --- Reader Management ---

  function toggleReader() {
    if (isActive) {
      closeReader();
    } else {
      openReader();
    }
  }

  function openReader() {
    if (isActive) return;
    
    isActive = true;
    console.log('Minimal Reader: Opening reader interface');
    
    // Communicate with MAIN world interface via postMessage
    window.postMessage({
      type: 'MINIMAL_READER_OPEN',
      settings: settings,
      extensionId: chrome.runtime.id
    }, '*');
    
    // Update button state
    if (floatingButton) {
      floatingButton.style.background = '#059669'; // Green when active
      floatingButton.style.borderColor = '#059669';
    }
  }

  function closeReader() {
    if (!isActive) return;
    
    isActive = false;
    console.log('Minimal Reader: Closing reader interface');
    
    // Communicate with MAIN world interface via postMessage
    window.postMessage({
      type: 'MINIMAL_READER_CLOSE'
    }, '*');
    
    // Reset button state
    if (floatingButton) {
      floatingButton.style.background = '#7C3AED'; // Purple when inactive
      floatingButton.style.borderColor = '#7C3AED';
    }
  }

  // Listen for messages from MAIN world interface
  window.addEventListener('message', (event) => {
    if (event.source !== window) return;
    
    if (event.data.type === 'MINIMAL_READER_INTERFACE_CLOSED') {
      isActive = false;
      // Reset button state
      if (floatingButton) {
        floatingButton.style.background = '#7C3AED';
        floatingButton.style.borderColor = '#7C3AED';
      }
    }
  });

  // --- Page Detection ---

  // Default blacklist for minimal reader - sites where we don't want the reader button to appear
  const DEFAULT_MINIMAL_READER_BLACKLIST = [
    'youtube.com',
    'www.youtube.com',
    'vimeo.com',
    'www.vimeo.com',
    'google.com',
    'maps.google.com'
  ];

  // --- Blacklist Management Functions ---

  // Function to normalize domain for comparison
  function normalizeDomain(domain) {
    return domain.toLowerCase().replace(/^www\./, '');
  }

  // Function to extract domain from URL
  function extractDomainFromUrl(url) {
    try {
      const urlObj = new URL(url);
      return normalizeDomain(urlObj.hostname);
    } catch (error) {
      // If URL parsing fails, try to extract domain from href text
      const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^\/\s]+)/);
      return match ? normalizeDomain(match[1]) : '';
    }
  }

  // Function to check if a domain is blacklisted
  function isDomainBlacklisted(url) {
    if (!url) return false;
    
    const domain = extractDomainFromUrl(url);
    if (!domain) return false;
    
    // Check both default blacklist and user blacklist
    const allBlacklistedDomains = [...DEFAULT_MINIMAL_READER_BLACKLIST, ...blacklistDomains];
    
    const isBlacklisted = allBlacklistedDomains.some(blacklistDomain => {
      const normalizedBlacklist = normalizeDomain(blacklistDomain);
      // Exact match: example.com matches example.com
      // Subdomain match: example.com matches www.example.com, app.example.com, etc.
      return domain === normalizedBlacklist || domain.endsWith('.' + normalizedBlacklist);
    });
    
    if (isBlacklisted) {
      console.log(`Minimal Reader: Domain "${domain}" is blacklisted`);
    }
    
    return isBlacklisted;
  }

  // Function to add current domain to blacklist
  async function addCurrentDomainToBlacklist() {
    try {
      console.log('Minimal Reader: Starting addCurrentDomainToBlacklist()');
      const currentDomain = window.location.hostname;
      const normalizedDomain = normalizeDomain(currentDomain);
      
      console.log('Minimal Reader: Current domain:', currentDomain, 'Normalized:', normalizedDomain);
      console.log('Minimal Reader: Current blacklistDomains array:', blacklistDomains);
      
      // Ensure blacklistDomains is initialized
      if (!Array.isArray(blacklistDomains)) {
        console.warn('Minimal Reader: blacklistDomains is not an array, initializing as empty array');
        blacklistDomains = [];
      }
      
      // Check if domain is already blacklisted
      if (blacklistDomains.includes(normalizedDomain)) {
        console.log(`Minimal Reader: Domain "${normalizedDomain}" is already blacklisted`);
        return false;
      }
      
      // Add to blacklist
      blacklistDomains.push(normalizedDomain);
      console.log('Minimal Reader: Added domain to local array:', blacklistDomains);
      
      // Save to storage with timeout protection
      const storagePromise = new Promise(async (resolve, reject) => {
        try {
          console.log('Minimal Reader: Getting current settings from storage');
          const result = await chrome.storage.local.get(['gmbExtractorSettings']);
          console.log('Minimal Reader: Current storage result:', result);
          
          const settings = result.gmbExtractorSettings || {};
          settings.minimalReaderBlacklistDomains = blacklistDomains;
          
          console.log('Minimal Reader: Saving updated settings:', settings);
          await chrome.storage.local.set({ gmbExtractorSettings: settings });
          console.log('Minimal Reader: Successfully saved to storage');
          
          resolve(true);
        } catch (error) {
          console.error('Minimal Reader: Storage operation failed:', error);
          reject(error);
        }
      });
      
      // Add timeout to storage operation
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('Storage operation timed out after 5 seconds'));
        }, 5000);
      });
      
      await Promise.race([storagePromise, timeoutPromise]);
      
      console.log(`Minimal Reader: Successfully added "${normalizedDomain}" to blacklist`);
      
      // Remove button from current page
      removeButton();
      
      return true;
      
    } catch (error) {
      console.error('Minimal Reader: Error adding domain to blacklist:', error);
      // Rollback: remove from local array if storage failed
      if (Array.isArray(blacklistDomains)) {
        const currentDomain = window.location.hostname;
        const normalizedDomain = normalizeDomain(currentDomain);
        const index = blacklistDomains.indexOf(normalizedDomain);
        if (index > -1) {
          blacklistDomains.splice(index, 1);
          console.log('Minimal Reader: Rolled back local array change due to storage error');
        }
      }
      return false;
    }
  }

  // CMS backend exclusion patterns
  const CMS_BACKEND_PATTERNS = [
    '/wp-admin/',
    '/wp-login.php',
    '/wp-content/',
    '/administrator/',
    '/admin/',
    '/backend/',
    '/panel/',
    '/dashboard/',
    '/cms/',
    '/manager/',
    '/control/',
    '/cp/',
    '/system/',
    '/typo3/',
    '/craft/',
    '/statamic/',
    '/kirby/',
    '/grav/',
    '/october/',
    '/drupal/',
    '/joomla/',
    '/magento/',
    '/shopify/',
    '/woocommerce/',
    '/prestashop/',
    '/opencart/'
  ];

  // Backend/Editor URL parameter blacklist - only parameters that indicate admin/editor interfaces
  const BACKEND_EDITOR_PARAMETERS = [
    // WordPress Page Builders
    'bricks=run',
    'bricks_preview=true',
    'bricks_iframe=true',
    'elementor-preview=',
    'elementor_library=',
    'elementor_version=',
    'fl_builder',
    'fl_builder_draft',
    'fl_builder_ui=',
    'et_fb=1',
    'et_pb_preview=true',
    'vc_editable=true',
    'vc_action=',
    'vc_post_id=',
    'oxygen_iframe=true',
    'oxygen_editing=true',
    'ct_builder=true',
    'brizy-edit',
    'brizy-edit-iframe',
    'brizy-edit-post=',
    'tve=true',
    'tar_post_id=',
    'gutenberg-editor=true',
    'block-editor=1',
    'siteorigin_panels_live_editor',
    'so_live_editor=1',
    'cs-preview=true',
    'cornerstone_preview=',
    'fusion_load_nonce=',
    'fb-edit=1',
    
    // WordPress Core Admin
    'preview=true',
    'preview_id=',
    'preview_nonce=',
    'customized=',
    'customize_changeset_uuid=',
    
    // Other CMS Admin/Editor Interfaces
    'preview_theme_id=',  // Shopify
    'pb=0',              // Shopify page builder
    'ss-live-preview',   // Squarespace
    'nochrome=true',     // Squarespace
    'wix-draft-mode',    // Wix
    'draft=true',        // Wix
    'wix_preview=',      // Wix
    'edit-mode=true',    // Webflow
    'webflow_editor=',   // Webflow
    
    // Generic Backend/Admin Patterns
    'admin_preview=true',
    'mode=edit',
    'editor=true',
    'edit=true',
    'live_preview=',
    'builder_mode='
  ];

  function isBlacklisted() {
    return isDomainBlacklisted(window.location.href);
  }

  function isCMSBackend() {
    const currentPath = window.location.pathname.toLowerCase();
    return CMS_BACKEND_PATTERNS.some(pattern => 
      currentPath.includes(pattern.toLowerCase())
    );
  }

  function hasPortNumber() {
    // Check if URL contains a port number (e.g., site.com:8080, localhost:3000)
    const url = window.location.href;
    const portPattern = /:\d+\//;
    return portPattern.test(url) || (url.includes(':') && /:\d+$/.test(url));
  }

  function hasBackendEditorParameters() {
    // Check if URL contains any backend/editor parameters that indicate admin interface
    const currentSearch = window.location.search;
    const currentHash = window.location.hash;
    const fullQuery = currentSearch + currentHash; // Check both search params and hash params
    
    const foundParam = BACKEND_EDITOR_PARAMETERS.find(param => {
      // Handle different parameter formats
      if (param.includes('=')) {
        // Exact parameter match (e.g., 'bricks=run', 'et_fb=1')
        return fullQuery.includes('?' + param) || 
               fullQuery.includes('&' + param) || 
               fullQuery.includes('#' + param);
      } else {
        // Parameter key only (e.g., 'fl_builder', 'brizy-edit')
        return fullQuery.includes('?' + param + '=') || 
               fullQuery.includes('&' + param + '=') || 
               fullQuery.includes('#' + param + '=') ||
               fullQuery.includes('?' + param + '&') ||
               fullQuery.includes('&' + param + '&') ||
               fullQuery.includes('#' + param + '&') ||
               fullQuery.includes('?' + param) ||
               fullQuery.includes('&' + param) ||
               fullQuery.includes('#' + param);
      }
    });
    
    if (foundParam) {
      console.log('Minimal Reader: Backend/editor parameter detected:', foundParam, 'in URL:', window.location.href);
    }
    
    return !!foundParam;
  }

  function isTargetPage() {
    // Allow on all pages except extension pages AND only in top-level window (not iframes) AND not blacklisted AND not CMS backend AND not sites with port numbers AND not backend/editor interfaces
    return !window.location.href.startsWith('chrome-extension://') && 
           window === window.top && 
           !isBlacklisted() &&
           !isCMSBackend() &&
           !hasPortNumber() &&
           !hasBackendEditorParameters();
  }

  // --- Initialize (following mass-unsubscribe.js pattern exactly) ---

  // Load settings first
  await loadSettings();
  
  // Add button when page loads (only if enabled)
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addMinimalReaderButton);
  } else {
    addMinimalReaderButton();
  }

  // Handle page navigation changes (like mass-unsubscribe.js does for Gmail)
  let lastUrl = window.location.href;
  
  const observer = new MutationObserver(() => {
    const currentUrl = window.location.href;
    
    // Check if URL changed
    if (currentUrl !== lastUrl) {
      lastUrl = currentUrl;
      
      // Remove button when navigating to extension pages
      if (!isTargetPage()) {
        removeButton();
        return;
      }
    }
    
    // Add button if we're on a valid page and button doesn't exist
    if (isEnabled && isTargetPage() && !document.getElementById('minimal-reader-btn')) {
      setTimeout(addMinimalReaderButton, 1000);
    }
  });
  
  // Safe observer initialization function
  function initializeMutationObserver() {
    try {
      if (document.body) {
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
        console.log('Minimal Reader: MutationObserver initialized successfully');
      } else {
        // Retry after a short delay if document.body is not ready
        setTimeout(initializeMutationObserver, 100);
      }
    } catch (error) {
      console.error('Minimal Reader: Failed to initialize MutationObserver:', error);
      // Retry once more after a longer delay
      setTimeout(() => {
        try {
          if (document.body) {
            observer.observe(document.body, {
              childList: true,
              subtree: true
            });
            console.log('Minimal Reader: MutationObserver initialized on retry');
          }
        } catch (retryError) {
          console.error('Minimal Reader: MutationObserver retry failed:', retryError);
        }
      }, 1000);
    }
  }
  
  // Initialize observer safely
  initializeMutationObserver();

  // Store observer reference globally for cleanup
  window.minimalReaderObserver = observer;

  // --- Cleanup Function (CRITICAL for context menu reliability) ---
  
  window.minimalReaderCleanup = function() {
    console.log('Minimal Reader: Performing selective cleanup for context menu reliability');
    
    try {
      // 1. Temporarily disconnect MutationObserver (CRITICAL - prevents DOM watching interference)
      if (window.minimalReaderObserver) {
        window.minimalReaderObserver.disconnect();
        window.minimalReaderObserver = null;
        console.log('Minimal Reader: MutationObserver temporarily disconnected');
        
        // Restore observer after a short delay
        setTimeout(() => {
          if (isEnabled && isTargetPage()) {
            initializeMutationObserver();
            console.log('Minimal Reader: MutationObserver restored after context menu operation');
          }
        }, 1000);
      }
      
      // 2. Temporarily hide button during context menu operation (don't permanently remove)
      const button = document.getElementById('minimal-reader-btn');
      if (button) {
        button.style.display = 'none';
        console.log('Minimal Reader: Button temporarily hidden for context menu');
        
        // Restore button after a short delay
        setTimeout(() => {
          if (button && isEnabled && isTargetPage()) {
            button.style.display = 'flex';
            console.log('Minimal Reader: Button restored after context menu operation');
          }
        }, 500);
      }
      
      // 3. Close any active reader interface (but don't disable the feature)
      if (isActive) {
        closeReader();
      }
      
      // 4. Remove any minimal reader interface elements
      const interfaces = document.querySelectorAll('#minimal-reader-interface, .minimal-reader-overlay, .minimal-reader-panel, [data-minimal-reader]');
      interfaces.forEach(el => el.remove());
      
      console.log('Minimal Reader: Selective cleanup performed (feature remains enabled)');
      return true;
      
    } catch (error) {
      console.error('Minimal Reader: Cleanup error:', error);
      return false;
    }
  };

  console.log('Minimal Reader: Initialized following mass-unsubscribe pattern');
})();