// Quick Actions Reordering Functionality
// Handles reordering of quick actions with up/down arrow buttons

class QuickActionsReorder {
    constructor() {
        this.defaultOrder = [
            { id: 'htagsBtn', name: 'H<PERSON>s Highlighter', setting: 'htagsEnabled' },
            { id: 'headingStructureBtn', name: 'Heading Structure', setting: 'headingStructureEnabled' },
            { id: 'showLinksBtn', name: 'Show Links', setting: 'showLinksEnabled' },
            { id: 'showHiddenBtn', name: 'Show Hidden', setting: 'showHiddenEnabled' },
            { id: 'boldFromSerpBtn', name: 'Bold From SERP', setting: 'boldFromSerpEnabled' },
            { id: 'schemaBtn', name: 'Schema Extractor', setting: 'schemaEnabled' },
            { id: 'imagesBtn', name: 'Images SEO Audit', setting: 'imagesEnabled' },
            { id: 'metadataBtn', name: '<PERSON><PERSON><PERSON>ly<PERSON>', setting: 'metadataEnabled' },
            { id: 'utmBuilderBtn', name: 'UTM Builder', setting: 'utmBuilderEnabled' },
            { id: 'pageStructureBtn', name: 'Page Structure', setting: 'pageStructureEnabled' },
            { id: 'linksExtractorBtn', name: 'Links Extractor', setting: 'linksExtractorEnabled' },
            { id: 'bulkLinkOpenBtn', name: 'Bulk Link Open', setting: 'bulkLinkOpenEnabled' },
            { id: 'responsiveBtn', name: 'Responsive Device Simulator', setting: 'responsiveEnabled' },
            { id: 'seoTestsBtn', name: 'SEO Tests', setting: 'seoTestsEnabled' },
            { id: 'trackerDetectionBtn', name: 'Tracker Detection', setting: 'trackerDetectionEnabled' },
            { id: 'copyElementBtn', name: 'Copy Element', setting: 'copyElementEnabled' },
            { id: 'colorPickerBtn', name: 'Color Picker', setting: 'colorPickerEnabled' },
            { id: 'youtubeEmbedScraperBtn', name: 'YouTube Embed Scraper', setting: 'youtubeEmbedScraperEnabled' }
        ];
        
        // BULLETPROOF-FIX: Validate and enforce YouTube Embed Scraper is always last
        this.enforceYouTubeLastInArray(this.defaultOrder);
        this.currentOrder = [...this.defaultOrder];
        this.init();
    }

    // BULLETPROOF-FIX: Array validator to ensure YouTube Embed Scraper is always last
    enforceYouTubeLastInArray(array) {
        const youtubeIndex = array.findIndex(item => item.id === 'youtubeEmbedScraperBtn');
        if (youtubeIndex !== -1 && youtubeIndex !== array.length - 1) {
            console.warn('🚨 CRITICAL: YouTube Embed Scraper was not last in array. Force-fixing...');
            const youtubeItem = array.splice(youtubeIndex, 1)[0];
            array.push(youtubeItem);
            console.log('✅ YouTube Embed Scraper moved to last position in array');
        }
    }

    async init() {
        await this.loadOrder();
        
        // Wait for the settings content to be loaded
        this.waitForSettingsContent().then(() => {
            this.addReorderControls();
            this.reorderSettingsItems();
        });
    }

    waitForSettingsContent() {
        return new Promise((resolve) => {
            const checkContent = () => {
                const quickActionsContent = document.getElementById('quick-actions-settings-content');
                if (quickActionsContent && quickActionsContent.children.length > 0) {
                    resolve();
                } else {
                    setTimeout(checkContent, 100);
                }
            };
            checkContent();
        });
    }

    async loadOrder() {
        try {
            const result = await chrome.storage.local.get('quickActionsOrder');
            if (result.quickActionsOrder && Array.isArray(result.quickActionsOrder)) {
                // Merge saved order with default items, ensuring all default items are included
                const savedOrder = result.quickActionsOrder;
                this.currentOrder = [];
                
                // First, add items in saved order
                savedOrder.forEach(savedItem => {
                    const defaultItem = this.defaultOrder.find(item => item.id === savedItem.id);
                    if (defaultItem) {
                        this.currentOrder.push(defaultItem);
                    }
                });
                
                // Then add any missing default items at the end (but before YouTube)
                this.defaultOrder.forEach(defaultItem => {
                    if (!this.currentOrder.find(item => item.id === defaultItem.id)) {
                        // BULLETPROOF-FIX: Never add items after YouTube, always insert before it
                        if (defaultItem.id === 'youtubeEmbedScraperBtn') {
                            this.currentOrder.push(defaultItem);
                        } else {
                            // Find YouTube's position and insert before it
                            const youtubeIndex = this.currentOrder.findIndex(item => item.id === 'youtubeEmbedScraperBtn');
                            if (youtubeIndex !== -1) {
                                this.currentOrder.splice(youtubeIndex, 0, defaultItem);
                            } else {
                                this.currentOrder.push(defaultItem);
                            }
                        }
                    }
                });
                
                // BULLETPROOF-FIX: YouTube Embed Scraper must ALWAYS be last (strengthened)
                this.enforceYouTubeLastInArray(this.currentOrder);
            }
        } catch (error) {
            console.error('Error loading quick actions order:', error);
            this.currentOrder = [...this.defaultOrder];
        }
    }

    async saveOrder() {
        try {
            // BULLETPROOF-FIX: YouTube Embed Scraper must ALWAYS be last before saving (strengthened)
            this.enforceYouTubeLastInArray(this.currentOrder);
            
            await chrome.storage.local.set({ 
                quickActionsOrder: this.currentOrder.map(item => ({ id: item.id, name: item.name }))
            });
            console.log('Quick actions order saved:', this.currentOrder);
        } catch (error) {
            console.error('Error saving quick actions order:', error);
        }
    }

    addReorderControls() {
        // Find all settings items in the quick actions section
        const settingsItems = document.querySelectorAll('#quick-actions-settings-content .settings-item');
        
        settingsItems.forEach((item, index) => {
            // Skip the keyboard shortcuts item (first item)
            if (index === 0) return;
            
            const titleElement = item.querySelector('.settings-item__title');
            if (!titleElement) return;

            const titleText = titleElement.textContent.trim();
            
            // Skip items that are not reorderable (like Keyword Density which is right-click only)
            if (titleText === 'Keyword Density (Right-Click Menu)') return;
            
            const quickActionItem = this.currentOrder.find(action => action.name === titleText);
            if (!quickActionItem) return;

            // Create reorder controls container
            const reorderControls = document.createElement('div');
            reorderControls.className = 'reorder-controls';
            reorderControls.innerHTML = `
                <button class="reorder-btn reorder-btn--up" type="button" title="Move up">
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
                    </svg>
                </button>
                <button class="reorder-btn reorder-btn--down" type="button" title="Move down">
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6z"/>
                    </svg>
                </button>
            `;

            // Add CSS for reorder buttons
            this.addReorderStyles();

            // Insert reorder controls before the toggle switch
            const controlsContainer = item.querySelector('.settings-item__control');
            if (controlsContainer) {
                controlsContainer.parentNode.insertBefore(reorderControls, controlsContainer);
            }

            // Add event listeners
            const upBtn = reorderControls.querySelector('.reorder-btn--up');
            const downBtn = reorderControls.querySelector('.reorder-btn--down');

            upBtn.addEventListener('click', () => this.moveUp(quickActionItem.id));
            downBtn.addEventListener('click', () => this.moveDown(quickActionItem.id));
        });
    }

    addReorderStyles() {
        // Check if styles already exist
        if (document.getElementById('reorder-styles')) return;

        const style = document.createElement('style');
        style.id = 'reorder-styles';
        style.textContent = `
            .settings-item__content {
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .settings-item__info {
                flex: 1;
            }

            .reorder-controls {
                display: flex;
                flex-direction: column;
                gap: 2px;
                margin-right: 8px;
            }

            .reorder-btn {
                background: #374151;
                border: 1px solid #4b5563;
                border-radius: 4px;
                color: #9ca3af;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 2px;
                transition: all 0.2s ease;
                width: 20px;
                height: 20px;
            }

            .reorder-btn:hover {
                background: #4b5563;
                border-color: #6b7280;
                color: #d1d5db;
            }

            .reorder-btn:active {
                background: #374151;
                transform: scale(0.95);
            }

            .reorder-btn svg {
                width: 12px;
                height: 12px;
            }

            .reorder-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .reorder-btn:disabled:hover {
                background: #374151;
                border-color: #4b5563;
                color: #9ca3af;
                transform: none;
            }
        `;
        document.head.appendChild(style);
    }

    moveUp(itemId) {
        const currentIndex = this.currentOrder.findIndex(item => item.id === itemId);
        if (currentIndex > 0) {
            // Store scroll position before reordering
            const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            
            // Swap with previous item
            [this.currentOrder[currentIndex], this.currentOrder[currentIndex - 1]] = 
            [this.currentOrder[currentIndex - 1], this.currentOrder[currentIndex]];
            
            this.saveOrder();
            this.reorderSettingsItems();
            this.updateReorderControls();
            this.notifyShortcutsUpdate();
            
            // Restore scroll position after reordering
            requestAnimationFrame(() => {
                document.documentElement.scrollTop = scrollTop;
                document.body.scrollTop = scrollTop;
            });
        }
    }

    moveDown(itemId) {
        const currentIndex = this.currentOrder.findIndex(item => item.id === itemId);
        
        // BULLETPROOF-FIX: Prevent any item from moving below YouTube Embed Scraper (strengthened)
        const youtubeIndex = this.currentOrder.findIndex(item => item.id === 'youtubeEmbedScraperBtn');
        const nextIndex = currentIndex + 1;
        
        // If trying to move an item into YouTube's position or below it, prevent the move
        if (nextIndex >= youtubeIndex && youtubeIndex !== -1) {
            console.warn('🚨 BLOCKED: Cannot move item below YouTube Embed Scraper - it must always be last');
            return;
        }
        
        // Also prevent moving YouTube itself (double protection)
        if (itemId === 'youtubeEmbedScraperBtn') {
            console.warn('🚨 BLOCKED: YouTube Embed Scraper cannot be moved - it must always be last');
            return;
        }
        
        if (currentIndex < this.currentOrder.length - 1) {
            // Store scroll position before reordering
            const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            
            // Swap with next item
            [this.currentOrder[currentIndex], this.currentOrder[currentIndex + 1]] = 
            [this.currentOrder[currentIndex + 1], this.currentOrder[currentIndex]];
            
            // BULLETPROOF-FIX: Enforce YouTube last after any move operation
            this.enforceYouTubeLastInArray(this.currentOrder);
            
            this.saveOrder();
            this.reorderSettingsItems();
            this.updateReorderControls();
            this.notifyShortcutsUpdate();
            
            // Restore scroll position after reordering
            requestAnimationFrame(() => {
                document.documentElement.scrollTop = scrollTop;
                document.body.scrollTop = scrollTop;
            });
        }
    }

    reorderSettingsItems() {
        const container = document.querySelector('#quick-actions-settings-content');
        if (!container) return;

        const settingsItems = container.querySelectorAll('.settings-item');
        const keyboardShortcutsItem = settingsItems[0]; // First item is always keyboard shortcuts

        // Create a map of current items by title
        const itemsMap = new Map();
        settingsItems.forEach((item, index) => {
            if (index === 0) return; // Skip keyboard shortcuts
            
            const titleElement = item.querySelector('.settings-item__title');
            if (titleElement) {
                const title = titleElement.textContent.trim();
                itemsMap.set(title, item);
            }
        });

        // Remove all items except keyboard shortcuts
        settingsItems.forEach((item, index) => {
            if (index > 0) item.remove();
        });

        // Re-add items in the correct order
        this.currentOrder.forEach(action => {
            const item = itemsMap.get(action.name);
            if (item) {
                container.appendChild(item);
            }
        });

        // Re-add reorder controls after reordering
        setTimeout(() => {
            this.removeExistingReorderControls();
            this.addReorderControls();
        }, 10);
    }

    removeExistingReorderControls() {
        const existingControls = document.querySelectorAll('.reorder-controls');
        existingControls.forEach(control => control.remove());
    }

    updateReorderControls() {
        const reorderControls = document.querySelectorAll('.reorder-controls');
        const youtubeIndex = this.currentOrder.findIndex(item => item.id === 'youtubeEmbedScraperBtn');
        
        reorderControls.forEach((controls, index) => {
            const upBtn = controls.querySelector('.reorder-btn--up');
            const downBtn = controls.querySelector('.reorder-btn--down');
            
            // Disable up button for first item
            upBtn.disabled = index === 0;
            
            // FORCE-FIX: Disable down button for last item AND for item before YouTube Embed Scraper
            const isLastItem = index === reorderControls.length - 1;
            const isBeforeYoutube = youtubeIndex !== -1 && index === youtubeIndex - 1;
            
            downBtn.disabled = isLastItem || isBeforeYoutube;
        });
    }

    notifyShortcutsUpdate() {
        console.log('Notifying shortcuts system of order change...');
        console.log('Current order:', this.currentOrder.map(item => `${item.name} (${item.id})`));
        
        // Notify the shortcuts system about the order change
        if (window.quickActionsShortcuts) {
            const orderedActions = this.getOrderedActions();
            console.log('Sending ordered actions to shortcuts:', orderedActions.map(a => `${a.name} (${a.id})`));
            window.quickActionsShortcuts.updateOrder(orderedActions);
        } else {
            console.log('Shortcuts system not available, will be picked up from storage on next load');
        }
        
        // Also notify any content scripts about the order change
        try {
            chrome.tabs.query({}, (tabs) => {
                tabs.forEach(tab => {
                    chrome.tabs.sendMessage(tab.id, {
                        type: 'QUICK_ACTIONS_ORDER_CHANGED',
                        order: this.getOrderedActions()
                    }).catch(() => {
                        // Ignore errors for tabs that don't have content scripts
                    });
                });
            });
        } catch (error) {
            console.log('Could not notify content scripts about order change:', error);
        }
    }

    getOrderedActions() {
        // Return actions in the current order for use by shortcuts system
        return this.currentOrder.map(item => ({
            id: item.id,
            name: item.name.replace(' Highlighter', '').replace(' Extractor', '').replace(' SEO Audit', '').replace(' Analyzer', ''),
            action: this.getActionFunction(item.id)
        }));
    }

    getActionFunction(buttonId) {
        // Map button IDs to their corresponding action functions
        const actionMap = {
            'htagsBtn': () => window.quickActionsShortcuts?.executeHtagsScript(),
            'headingStructureBtn': () => window.quickActionsShortcuts?.executeHeadingStructureScript(),
            'showLinksBtn': () => window.quickActionsShortcuts?.executeShowLinksScript(),
            'showHiddenBtn': () => window.quickActionsShortcuts?.executeShowHiddenScript(),
            'boldFromSerpBtn': () => window.quickActionsShortcuts?.executeBoldFromSerpScript(),
            'schemaBtn': () => window.quickActionsShortcuts?.executeSchemaScript(),
            'imagesBtn': () => window.quickActionsShortcuts?.executeImagesScript(),
            'metadataBtn': () => window.quickActionsShortcuts?.executeMetadataScript(),
            'pageStructureBtn': () => window.quickActionsShortcuts?.executePageStructureScript(),
            'youtubeEmbedScraperBtn': () => window.YouTubeEmbedScraperAction?.execute(),
            'copyElementBtn': () => window.quickActionsShortcuts?.executeCopyElementScript(),
            'colorPickerBtn': () => window.ColorPickerAction?.execute()
        };
        return actionMap[buttonId] || (() => {});
    }

    resetToDefault() {
        this.currentOrder = [...this.defaultOrder];
        this.saveOrder();
        this.reorderSettingsItems();
        this.updateReorderControls();
        this.notifyShortcutsUpdate();
    }

    getCurrentOrder() {
        return [...this.currentOrder];
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        if (!window.quickActionsReorder) {
            window.quickActionsReorder = new QuickActionsReorder();
        }
    });
} else {
    if (!window.quickActionsReorder) {
        window.quickActionsReorder = new QuickActionsReorder();
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QuickActionsReorder;
}

window.QuickActionsReorder = QuickActionsReorder; 