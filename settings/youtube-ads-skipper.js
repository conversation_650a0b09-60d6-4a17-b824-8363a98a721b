// YouTube Ads Skipper - Optimized for speed
(function() {
    'use strict';
    
    
    let isEnabled = true;
    let observer = null;
    let isProcessing = false;
    let debugMode = false;
    
    // Load settings from localStorage (MAIN world doesn't have Chrome API access)
    function loadSettings() {
        try {
            const storedEnabled = localStorage.getItem('youtubeAdsSkipperEnabled');
            const storedDebug = localStorage.getItem('debugMode');
            
            isEnabled = storedEnabled !== 'false'; // Default to true
            debugMode = storedDebug === 'true';
            
            return isEnabled;
        } catch (error) {
            console.error('[STM-ads-skipper] Error loading settings:', error);
            isEnabled = true; // Fail safe to default state
            return true;
        }
    }
    
    // Initialize
    function initialize() {
        const shouldRun = loadSettings();
        
        if (!shouldRun) {
            if (debugMode) {
                console.log('[STM-ads-skipper] Feature disabled in settings');
            }
            return false;
        }
        return true;
    }
    
    // Find parent ytd-rich-item-renderer
    function findParentRenderer(element) {
        if (!element) return null;
        if (element.tagName === 'YTD-RICH-ITEM-RENDERER') return element;
        return findParentRenderer(element.parentElement);
    }
    
    // Get YouTube player
    function getPlayer() {
        try {
            const ytdPlayer = document.getElementById('ytd-player');
            if (ytdPlayer && ytdPlayer.getPlayer) {
                return ytdPlayer.getPlayer();
            }
        } catch (e) {
            // Ignore errors
        }
        return null;
    }
    
    // Video ad skipping - old method first, new methods as fallbacks
    function skipVideoAd() {
        if (isProcessing) return false;
        isProcessing = true;
        
        try {
            // Strategy 1 (PRIMARY): Original working method - cancelPlayback/playVideo
            const player = getPlayer();
            if (player) {
                try {
                    player.cancelPlayback();
                    setTimeout(function() {
                        player.playVideo();
                    }, 300); // Reduced delay from 1000ms to 300ms for faster response
                    console.log('[STM-ads-skipper] Ad skipped using METHOD 1: cancelPlayback/playVideo');
                    return true;
                } catch (e) {
                    // Silent error handling
                }
            }
            
            // Strategy 2 (FALLBACK): Click skip button if available
            const skipButton = document.querySelector('.ytp-skip-ad-button, .ytp-ad-skip-button, .ytp-ad-skip-button-modern');
            if (skipButton && skipButton.offsetParent !== null) {
                skipButton.click();
                console.log('[STM-ads-skipper] Ad skipped using METHOD 2: Skip button click');
                return true;
            }
            
            // Strategy 3 (FALLBACK): Fast forward to end of ad
            const video = document.querySelector('video');
            if (video && video.duration && video.currentTime < video.duration) {
                video.currentTime = video.duration;
                console.log('[STM-ads-skipper] Ad skipped using METHOD 3: Fast forward to end');
                return true;
            }
            
            // Strategy 4 (FALLBACK): Try YouTube player skipAd method
            try {
                if (player && typeof player.skipAd === 'function') {
                    player.skipAd();
                    console.log('[STM-ads-skipper] Ad skipped using METHOD 4: Player skipAd() API');
                    return true;
                }
            } catch (e) {
                // Ignore player errors
            }
            
            return false;
        } catch (e) {
            return false;
        } finally {
            // Reset processing flag after a short delay
            setTimeout(() => {
                isProcessing = false;
            }, 800); // Increased to 800ms to account for the 300ms delay in primary method
        }
    }
    
    // Check for ads and skip them
    function checkAndSkipAds() {
        // Remove ad slot renderers (sidebar/feed ads)
        const adSlots = document.getElementsByTagName('ytd-ad-slot-renderer');
        if (adSlots.length > 0) {
            Array.from(adSlots).forEach(function(adSlot) {
                const parent = findParentRenderer(adSlot);
                if (parent) {
                    parent.remove();
                }
                try {
                    adSlot.remove();
                    console.log('[STM-ads-skipper] Ad skipped using METHOD 5: Ad slot removal');
                } catch (e) {
                    // Ignore errors
                }
            });
        }
        
        // Check for video ads
        const moviePlayer = document.getElementById('movie_player');
        if (moviePlayer) {
            // Multiple detection methods for ad state
            const isAdShowing = moviePlayer.classList.contains('ad-showing') ||
                              moviePlayer.classList.contains('ad-interrupting') ||
                              document.querySelector('.ytp-ad-text') ||
                              document.querySelector('.ytp-skip-ad-button') ||
                              document.querySelector('.ytp-ad-skip-button');
            
            if (isAdShowing) {
                skipVideoAd();
            }
        }
    }
    
    // Optimized mutation observer with throttling
    let lastCheck = 0;
    const THROTTLE_MS = 100; // Check at most every 100ms
    
    function throttledCheck() {
        const now = Date.now();
        if (now - lastCheck > THROTTLE_MS) {
            lastCheck = now;
            checkAndSkipAds();
        }
    }
    
    // Setup observer with better performance
    function setupObserver() {
        const moviePlayer = document.getElementById('movie_player');
        if (!moviePlayer) return false;
        
        observer = new MutationObserver(throttledCheck);
        
        observer.observe(moviePlayer, {
            attributes: true,
            attributeFilter: ['class'], // Only watch class changes
            childList: true,
            subtree: false // Reduce scope for better performance
        });
        
        // Also observe document for ad slot changes
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        return true;
    }
    
    // Settings change listener via localStorage events
    window.addEventListener('storage', (e) => {
        if (e.key === 'youtubeAdsSkipperEnabled' || e.key === 'debugMode') {
            const wasEnabled = isEnabled;
            loadSettings();
            
            if (debugMode) {
                console.log('[STM-ads-skipper] Settings updated:', { enabled: isEnabled });
            }
            
            // Handle enable/disable changes
            if (wasEnabled !== isEnabled) {
                if (!isEnabled && observer) {
                    // Disable: cleanup observer
                    observer.disconnect();
                    observer = null;
                    if (debugMode) {
                        console.log('[STM-ads-skipper] Feature disabled, observer cleaned up');
                    }
                } else if (isEnabled && !observer) {
                    // Enable: setup observer
                    setupObserver();
                    if (debugMode) {
                        console.log('[STM-ads-skipper] Feature enabled, observer setup');
                    }
                }
            }
        }
    });
    
    // Initialize and start the system
    console.log('[STM-ads-skipper] Script loaded and initializing...');
    
    // Signal that ads skipper is starting
    window.stmAdsSkipperActive = true;
    
    const shouldRun = initialize();
    if (shouldRun) {
        if (!shouldRun) {
            console.log('[STM-ads-skipper] Extension is disabled, not starting');
            // Signal completion even when disabled
            window.stmAdsSkipperActive = false;
            return; // Extension is disabled
        }
        
        console.log('[STM-ads-skipper] Extension enabled, setting up observer...');
        
        // Initial setup with faster retry
        const setupInterval = setInterval(function() {
            if (setupObserver()) {
                console.log('[STM-ads-skipper] Observer setup successful, monitoring for ads');
                clearInterval(setupInterval);
                // Run initial check
                checkAndSkipAds();
                // Signal completion after setup
                window.stmAdsSkipperActive = false;
            }
        }, 100); // Faster setup polling
        
        // Cleanup after 10 seconds if setup fails
        setTimeout(() => {
            clearInterval(setupInterval);
            console.log('[STM-ads-skipper] Setup timeout reached');
            // Signal completion on timeout
            window.stmAdsSkipperActive = false;
        }, 10000);
        
        // Initialization complete
    } else {
        console.log('[STM-ads-skipper] Feature disabled, not starting');
        // Signal completion when disabled
        window.stmAdsSkipperActive = false;
    }
})(); 