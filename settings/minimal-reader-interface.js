// Minimal Reader Interface - Full reading overlay with exact Reeader functionality
(function() {
    'use strict';
    
    // Interface state
    let isOpen = false;
    let readerContainer = null;
    let originalBodyStyle = '';
    let settings = {};
    let extractedContent = null;
    let currentFontSize = 18;
    let isFocusMode = false;
    let isStopWordFading = false;
    let isShowSiteElements = false;
    let popupOpen = false;
    
    // Focus Ruler state
    let isFocusRulerEnabled = false;
    let rulerElement = null;
    let rulerSettings = {
        show: true,
        color: '#7C3AED',
        height: 40,
        position: 30,
        opacity: 70
    };
    
    // Focus Mode state
    let focusModeSettings = {
        gutterWidth: 50,     // Range: 0-200px
        textOpacity: 60      // Range: 10-100% (inverted logic: lower = more transparent borders)
    };
    
    // Stop Words state
    let stopWordsSettings = {
        opacity: 40          // Range: 10-100%, default 40%
    };
    
    // Show Site Elements state
    let showSiteElementsSettings = {
        enabled: false       // Default: false to hide site elements by default
    };
    
    // Outline state
    let isOutlineEnabled = false;
    let outlinePanel = null;
    
    // Outline font scaling state
    let outlineFontScale = {
        scale: 100           // Range: 50-200%, default 100%
    };
    
    // Settings persistence
    function saveSettings() {
        try {
            const settingsToSave = {
                fontSize: currentFontSize,
                theme: settings.theme || 'white',
                fontWeight: settings.fontWeight || 'normal',
                focusMode: isFocusMode,
                stopWordFading: isStopWordFading,
                showSiteElements: isShowSiteElements,
                showImages: settings.showImages !== false,
                showMeta: settings.showMeta !== false,
                focusRuler: isFocusRulerEnabled,
                rulerSettings: rulerSettings,
                focusModeSettings: focusModeSettings,
                stopWordsSettings: stopWordsSettings,
                showSiteElementsSettings: showSiteElementsSettings,
                outline: isOutlineEnabled,
                outlineFontScale: outlineFontScale
            };
            localStorage.setItem('gmb-reader-settings', JSON.stringify(settingsToSave));
        } catch (e) {
            console.log('GMB Reader: Could not save settings to localStorage');
        }
    }
    
    function loadSettings() {
        try {
            const saved = localStorage.getItem('gmb-reader-settings');
            if (saved) {
                return JSON.parse(saved);
            }
        } catch (e) {
            console.log('GMB Reader: Could not load settings from localStorage');
        }
        
        // Return default settings
        return {
            fontSize: 18,
            theme: 'white',
            fontWeight: 'normal',
            focusMode: false,
            stopWordFading: false,
            showSiteElements: false,
            showImages: true,
            showMeta: true,
            focusRuler: false,
            rulerSettings: {
                show: true,
                color: '#7C3AED',
                height: 40,
                position: 30,
                opacity: 70
            },
            focusModeSettings: {
                gutterWidth: 50,
                textOpacity: 60
            },
            stopWordsSettings: {
                opacity: 40
            },
            showSiteElementsSettings: {
                enabled: false
            },
            outline: false,
            outlineFontScale: {
                scale: 100
            }
        };
    }
    
    // Load CSS (MAIN world compatible - no chrome.runtime access)
    function loadCSS(providedExtensionId) {
        if (document.getElementById('gmb-minimal-reader-css')) return;
        
        let extensionId = providedExtensionId;
        
        // Fallback: Get extension ID from current script URL (since we're injected by the extension)
        if (!extensionId) {
            const scriptElements = document.querySelectorAll('script');
            for (const script of scriptElements) {
                const src = script.src || '';
                const match = src.match(/chrome-extension:\/\/([a-z]{32})\//);
                if (match) {
                    extensionId = match[1];
                    break;
                }
            }
        }
        
        // Last resort: try to get extension ID from any chrome-extension:// resource in the page
        if (!extensionId) {
            const allElements = document.querySelectorAll('*');
            for (const element of allElements) {
                const src = String(element.src || element.href || '');
                const match = src.match(/chrome-extension:\/\/([a-z]{32})\//);
                if (match) {
                    extensionId = match[1];
                    break;
                }
            }
        }
        
        const link = document.createElement('link');
        link.id = 'gmb-minimal-reader-css';
        link.rel = 'stylesheet';
        link.type = 'text/css';
        
        if (extensionId) {
            link.href = `chrome-extension://${extensionId}/css/minimal-reader.css`;
        } else {
            // Last resort fallback - this shouldn't happen but just in case
            console.error('Minimal Reader: Could not determine extension ID for CSS loading');
            return;
        }
        
        document.head.appendChild(link);
    }
    
    // Check readability extractor availability (all scripts preloaded in MAIN world)
    function loadReadabilityExtractor() {
        return new Promise((resolve) => {
            if (window.GmbReadabilityExtractor) {
                resolve();
                return;
            }
            
            // Scripts are preloaded together in MAIN world, so if extractor isn't available,
            // wait a bit for all scripts to initialize
            let attempts = 0;
            const maxAttempts = 50; // 5 seconds max
            const checkInterval = setInterval(() => {
                attempts++;
                if (window.GmbReadabilityExtractor) {
                    clearInterval(checkInterval);
                    resolve();
                } else if (attempts >= maxAttempts) {
                    clearInterval(checkInterval);
                    console.error('Minimal Reader: Readability extractor not available after waiting');
                    resolve(); // Resolve anyway to prevent blocking
                }
            }, 100);
        });
    }
    
    function open(userSettings, extensionId) {
        if (isOpen) return;
        
        // Load saved settings from localStorage
        const savedSettings = loadSettings();
        
        // Merge user settings with saved settings (user settings take priority)
        settings = { ...savedSettings, ...userSettings };
        currentFontSize = settings.fontSize || 18;
        isFocusMode = settings.focusMode === true;
        isStopWordFading = settings.stopWordFading === true;
        isShowSiteElements = settings.showSiteElements === true;
        isFocusRulerEnabled = settings.focusRuler === true;
        rulerSettings = { ...rulerSettings, ...settings.rulerSettings };
        focusModeSettings = { ...focusModeSettings, ...settings.focusModeSettings };
        stopWordsSettings = { ...stopWordsSettings, ...settings.stopWordsSettings };
        showSiteElementsSettings = { ...showSiteElementsSettings, ...settings.showSiteElementsSettings };
        isOutlineEnabled = settings.outline === true;
        outlineFontScale = { ...outlineFontScale, ...settings.outlineFontScale };
        
        loadCSS(extensionId);
        loadReadabilityExtractor().then(() => {
            createReaderInterface();
        });
    }
    
    function close() {
        if (!isOpen) return;
        
        isOpen = false;
        
        // Restore original body styles
        if (originalBodyStyle !== null) {
            document.body.style.cssText = originalBodyStyle;
        }
        
        // Remove reader container
        if (readerContainer) {
            readerContainer.remove();
            readerContainer = null;
        }
        
        // Remove ruler element
        removeRulerElement();
        
        // Hide outline panel
        if (isOutlineEnabled) {
            hideOutlinePanel();
        }
        
        // Notify main controller
        if (window.GmbMinimalReaderClose) {
            window.GmbMinimalReaderClose();
        }
    }
    
    function createReaderInterface() {
        if (isOpen) return;
        
        isOpen = true;
        
        // Store original body styles
        originalBodyStyle = document.body.style.cssText;
        
        // Extract content
        extractedContent = window.GmbReadabilityExtractor.extract();
        
        // Create container
        readerContainer = document.createElement('div');
        readerContainer.id = 'gmb-reader-root';
        
        // Set container HTML
        readerContainer.innerHTML = createReaderHTML();
        
        // Modify body for overlay
        document.body.style.height = '100vh';
        document.body.style.overflow = 'hidden';
        
        // Add to page
        document.body.appendChild(readerContainer);
        
        // Setup event listeners
        setupEventListeners();
        
        // Apply initial settings
        applyTheme();
        updateFontSize();
        applyFontWeight();
        
        if (isFocusMode) {
            enableFocusMode();
        }
        
        if (isStopWordFading) {
            enableStopWordFading();
        }
        
        // Update reading time display
        updateReadingTime();
        
        // Initialize image toggle state (default: show images)
        const imagesButton = document.getElementById('gmb-reader-images-toggle');
        if (imagesButton) {
            if (settings.showImages === false) {
                imagesButton.textContent = 'Show Images';
                imagesButton.classList.add('gmb-reader-active');
                readerContainer.querySelector('.gmb-reader-app').classList.add('gmb-reader-hide-images');
            } else {
                imagesButton.textContent = 'Hide Images';
                imagesButton.classList.remove('gmb-reader-active');
                readerContainer.querySelector('.gmb-reader-app').classList.remove('gmb-reader-hide-images');
            }
        }
        
        // Initialize meta toggle state (default: show meta)
        const metaButton = document.getElementById('gmb-reader-meta-toggle');
        const metaElement = document.querySelector('.gmb-reader-meta');
        if (metaButton && metaElement) {
            if (settings.showMeta === false) {
                metaButton.textContent = 'Show Meta';
                metaButton.classList.add('gmb-reader-active');
                metaElement.style.display = 'none';
            } else {
                metaButton.textContent = 'Hide Meta';
                metaButton.classList.remove('gmb-reader-active');
                metaElement.style.display = 'block';
            }
        }
        
        // Initialize focus mode and stop word toggle states
        const focusButton = document.getElementById('gmb-reader-focus-toggle');
        if (focusButton) {
            if (isFocusMode) {
                focusButton.textContent = 'Focus Mode';
                focusButton.classList.add('gmb-reader-active');
            } else {
                focusButton.textContent = 'Focus Mode';
                focusButton.classList.remove('gmb-reader-active');
            }
        }
        
        const stopWordsButton = document.getElementById('gmb-reader-stopwords-toggle');
        if (stopWordsButton) {
            if (isStopWordFading) {
                stopWordsButton.textContent = 'Stop Words';
                stopWordsButton.classList.add('gmb-reader-active');
            } else {
                stopWordsButton.textContent = 'Stop Words';
                stopWordsButton.classList.remove('gmb-reader-active');
            }
        }
        
        const showSiteElementsButton = document.getElementById('gmb-reader-show-site-elements-toggle');
        if (showSiteElementsButton) {
            if (isShowSiteElements) {
                showSiteElementsButton.textContent = 'Show Site Elements';
                showSiteElementsButton.classList.add('gmb-reader-active');
            } else {
                showSiteElementsButton.textContent = 'Show Site Elements';
                showSiteElementsButton.classList.remove('gmb-reader-active');
            }
        }
        
        // Initialize ruler controls
        initializeRulerControls();
        
        // Initialize focus mode controls
        initializeFocusModeControls();
        
        // Initialize stop words controls
        initializeStopWordsControls();
        
        // Initialize outline
        initializeOutline();
    }
    
    function createReaderHTML() {
        return `
            <div class="gmb-reader-app gmb-reader-theme-${settings.theme}">
                <div class="gmb-reader-app-header">
                    <div class="gmb-reader-app-header__content">
                        <div class="gmb-reader-header-left">
                            <div class="gmb-reader-button--close" id="gmb-reader-close">✕</div>
                        </div>
                        
                        <div class="gmb-reader-header-center">
                            <div class="gmb-reader-theme--toggle">
                                <div class="gmb-reader-font--update gmb-reader-dec" id="gmb-reader-font-dec">A-</div>
                                <div class="gmb-reader-font--update gmb-reader-inc" id="gmb-reader-font-inc">A+</div>
                                
                                <div class="gmb-reader-theme--change gmb-reader-square" style="background: white;" id="gmb-reader-theme-white" title="White Theme"></div>
                                <div class="gmb-reader-theme--change gmb-reader-square" style="background: #f7f0e4;" id="gmb-reader-theme-yellow" title="Yellow Theme"></div>
                                <div class="gmb-reader-theme--change gmb-reader-square" style="background: #1c1f21;" id="gmb-reader-theme-dark" title="Dark Theme"></div>
                            </div>
                        </div>
                        
                        <div class="gmb-reader-header-right">
                            <div class="gmb-reader-popup-toggle__wrapper">
                                <div class="gmb-reader-popup-toggle--button" id="gmb-reader-popup-toggle">
                                    <figure></figure>
                                    <figure></figure>
                                    <figure></figure>
                                </div>
                                
                                <div class="gmb-reader-popup__wrapper gmb-reader-popup-theme-${settings.theme}" id="gmb-reader-popup" style="display: none;">
                                    <!-- Row 1: Bold and Outline -->
                                    <div class="gmb-reader-popup-buttons-row">
                                        <button id="gmb-reader-font-weight" class="gmb-reader-popup-btn">Bold</button>
                                        <button id="gmb-reader-outline-toggle" class="gmb-reader-popup-btn">Outline</button>
                                    </div>
                                    
                                    <!-- Outline Font Scale Settings Accordion -->
                                    <div class="gmb-reader-ruler-accordion" id="gmb-reader-outline-accordion" style="display: none;">
                                        <div class="gmb-reader-ruler-settings">
                                            <!-- Outline Font Scale -->
                                            <div class="gmb-reader-ruler-control">
                                                <label class="gmb-reader-ruler-label">Outline Font Scale</label>
                                                <div class="gmb-reader-ruler-slider-container">
                                                    <input type="range" id="gmb-reader-outline-font-scale" min="50" max="200" value="100" class="gmb-reader-ruler-range">
                                                    <span class="gmb-reader-ruler-value" id="gmb-reader-outline-font-scale-value">100%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Row 2: Focus Mode and Stop Words -->
                                    <div class="gmb-reader-popup-buttons-row">
                                        <button id="gmb-reader-focus-toggle" class="gmb-reader-popup-btn">Focus Mode</button>
                                        <button id="gmb-reader-stopwords-toggle" class="gmb-reader-popup-btn">Stop Words</button>
                                    </div>
                                    
                                    <!-- Focus Mode Settings Accordion -->
                                    <div class="gmb-reader-ruler-accordion" id="gmb-reader-focus-mode-accordion" style="display: none;">
                                        <div class="gmb-reader-ruler-settings">
                                            <!-- Gutter Width -->
                                            <div class="gmb-reader-ruler-control">
                                                <label class="gmb-reader-ruler-label">Gutter Width</label>
                                                <div class="gmb-reader-ruler-slider-container">
                                                    <input type="range" id="gmb-reader-focus-gutter-width" min="0" max="200" value="50" class="gmb-reader-ruler-range">
                                                    <span class="gmb-reader-ruler-value" id="gmb-reader-focus-gutter-width-value">50px</span>
                                                </div>
                                            </div>
                                            
                                            <!-- Text Opacity -->
                                            <div class="gmb-reader-ruler-control">
                                                <label class="gmb-reader-ruler-label">Text Opacity</label>
                                                <div class="gmb-reader-ruler-slider-container">
                                                    <input type="range" id="gmb-reader-focus-text-opacity" min="10" max="100" value="100" class="gmb-reader-ruler-range">
                                                    <span class="gmb-reader-ruler-value" id="gmb-reader-focus-text-opacity-value">100%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Stop Words Settings Accordion -->
                                    <div class="gmb-reader-ruler-accordion gmb-reader-accordion-separator" id="gmb-reader-stopwords-accordion" style="display: none;">
                                        <div class="gmb-reader-ruler-settings">
                                            <!-- Stop Words Opacity -->
                                            <div class="gmb-reader-ruler-control">
                                                <label class="gmb-reader-ruler-label">Stop Words Opacity</label>
                                                <div class="gmb-reader-ruler-slider-container">
                                                    <input type="range" id="gmb-reader-stopwords-opacity" min="10" max="100" value="40" class="gmb-reader-ruler-range">
                                                    <span class="gmb-reader-ruler-value" id="gmb-reader-stopwords-opacity-value">40%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Row 3: Images and Meta -->
                                    <div class="gmb-reader-popup-buttons-row">
                                        <button id="gmb-reader-images-toggle" class="gmb-reader-popup-btn">Hide Images</button>
                                        <button id="gmb-reader-meta-toggle" class="gmb-reader-popup-btn">Hide Meta</button>
                                    </div>
                                    
                                    <!-- Row 4: Focus Ruler -->
                                    <div class="gmb-reader-popup-buttons-row">
                                        <button id="gmb-reader-focus-ruler-toggle" class="gmb-reader-popup-btn">Focus Ruler</button>
                                    </div>
                                    
                                    <!-- Row 5: Show Site Elements -->
                                    <div class="gmb-reader-popup-buttons-row">
                                        <button id="gmb-reader-show-site-elements-toggle" class="gmb-reader-popup-btn gmb-reader-popup-btn-last">Show Site Elements</button>
                                    </div>
                                    
                                    <!-- Focus Ruler Settings Accordion -->
                                    <div class="gmb-reader-ruler-accordion" id="gmb-reader-ruler-accordion" style="display: none;">
                                        <div class="gmb-reader-ruler-settings">
                                            <!-- Ruler Show Toggle -->
                                            <div class="gmb-reader-ruler-control">
                                                <label class="gmb-reader-ruler-label">Ruler Show</label>
                                                <label class="gmb-reader-ruler-switch">
                                                    <input type="checkbox" id="gmb-reader-ruler-show" checked>
                                                    <span class="gmb-reader-ruler-slider"></span>
                                                </label>
                                            </div>
                                            
                                            <!-- Ruler Color -->
                                            <div class="gmb-reader-ruler-control">
                                                <label class="gmb-reader-ruler-label">Ruler Color</label>
                                                <div class="gmb-reader-ruler-colors">
                                                    <div class="gmb-reader-ruler-color" data-color="#000000" style="background-color: #000000;" title="Black"></div>
                                                    <div class="gmb-reader-ruler-color gmb-reader-ruler-color-active" data-color="#7C3AED" style="background-color: #7C3AED;" title="Purple"></div>
                                                    <div class="gmb-reader-ruler-color" data-color="#3B82F6" style="background-color: #3B82F6;" title="Blue"></div>
                                                    <div class="gmb-reader-ruler-color" data-color="#EF4444" style="background-color: #EF4444;" title="Red"></div>
                                                    <div class="gmb-reader-ruler-color" data-color="#10B981" style="background-color: #10B981;" title="Green"></div>
                                                    <div class="gmb-reader-ruler-color" data-color="#F59E0B" style="background-color: #F59E0B;" title="Orange"></div>
                                                    <div class="gmb-reader-ruler-hex-container">
                                                        <input type="text" id="gmb-reader-ruler-hex" class="gmb-reader-ruler-hex-input" placeholder="#7C3AED" maxlength="7">
                                                        <button id="gmb-reader-ruler-hex-apply" class="gmb-reader-ruler-hex-apply">Apply</button>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Ruler Height -->
                                            <div class="gmb-reader-ruler-control">
                                                <label class="gmb-reader-ruler-label">Ruler Height</label>
                                                <div class="gmb-reader-ruler-slider-container">
                                                    <input type="range" id="gmb-reader-ruler-height" min="1" max="100" value="40" class="gmb-reader-ruler-range">
                                                    <span class="gmb-reader-ruler-value" id="gmb-reader-ruler-height-value">40px</span>
                                                </div>
                                            </div>
                                            
                                            <!-- Ruler Position -->
                                            <div class="gmb-reader-ruler-control">
                                                <label class="gmb-reader-ruler-label">Ruler Position</label>
                                                <div class="gmb-reader-ruler-slider-container">
                                                    <input type="range" id="gmb-reader-ruler-position" min="0" max="100" value="30" class="gmb-reader-ruler-range">
                                                    <span class="gmb-reader-ruler-value" id="gmb-reader-ruler-position-value">30%</span>
                                                </div>
                                            </div>
                                            
                                            <!-- Ruler Opacity -->
                                            <div class="gmb-reader-ruler-control">
                                                <label class="gmb-reader-ruler-label">Ruler Opacity</label>
                                                <div class="gmb-reader-ruler-slider-container">
                                                    <input type="range" id="gmb-reader-ruler-opacity" min="10" max="100" value="70" class="gmb-reader-ruler-range">
                                                    <span class="gmb-reader-ruler-value" id="gmb-reader-ruler-opacity-value">70%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Simple Text Outline Overlay -->
                <div class="gmb-reader-outline-panel gmb-reader-outline-theme-${settings.theme}" id="gmb-reader-outline-panel" style="display: none;">
                    <div class="gmb-reader-outline-content" id="gmb-reader-outline-content">
                        <div class="gmb-reader-outline-empty">No headings found</div>
                    </div>
                </div>
                
                <div class="gmb-reader-app-wrapper" id="gmb-reader-content-wrapper">
                    <div class="gmb-reader-content__wrapper">
                        <div class="gmb-reader-meta">
                            <div class="gmb-reader-url" id="gmb-reader-url">${window.location.href}</div>
                            <div class="gmb-reader-reading-time" id="gmb-reader-reading-time"></div>
                        </div>
                        <h1>${extractedContent.title}</h1>
                        ${extractedContent.author ? `<p><em>By ${extractedContent.author}</em></p>` : ''}
                        ${extractedContent.publishedDate ? `<p><small>${new Date(extractedContent.publishedDate).toLocaleDateString()}</small></p>` : ''}
                        <div id="gmb-reader-article-content">${extractedContent.content}</div>
                    </div>
                </div>
            </div>
        `;
    }
    
    function setupEventListeners() {
        // Close button
        document.getElementById('gmb-reader-close').addEventListener('click', close);
        
        // Font size controls
        document.getElementById('gmb-reader-font-dec').addEventListener('click', decreaseFontSize);
        document.getElementById('gmb-reader-font-inc').addEventListener('click', increaseFontSize);
        
        // Theme controls
        document.getElementById('gmb-reader-theme-white').addEventListener('click', () => changeTheme('white'));
        document.getElementById('gmb-reader-theme-yellow').addEventListener('click', () => changeTheme('yellow'));
        document.getElementById('gmb-reader-theme-dark').addEventListener('click', () => changeTheme('dark'));
        
        // Popup toggle
        document.getElementById('gmb-reader-popup-toggle').addEventListener('click', togglePopup);
        
        
        // Font weight toggle
        document.getElementById('gmb-reader-font-weight').addEventListener('click', toggleFontWeight);
        
        // Outline toggle
        document.getElementById('gmb-reader-outline-toggle').addEventListener('click', toggleOutline);
        
        // Focus mode toggle
        document.getElementById('gmb-reader-focus-toggle').addEventListener('click', toggleFocusMode);
        
        // Stop words toggle
        document.getElementById('gmb-reader-stopwords-toggle').addEventListener('click', toggleStopWords);
        
        // Images toggle
        document.getElementById('gmb-reader-images-toggle').addEventListener('click', toggleImages);
        
        // Meta toggle
        document.getElementById('gmb-reader-meta-toggle').addEventListener('click', toggleMeta);
        
        // Focus Ruler controls
        document.getElementById('gmb-reader-focus-ruler-toggle').addEventListener('click', toggleFocusRuler);
        
        // Show Site Elements toggle
        document.getElementById('gmb-reader-show-site-elements-toggle').addEventListener('click', toggleShowSiteElements);
        document.getElementById('gmb-reader-ruler-show').addEventListener('change', toggleRulerVisibility);
        document.getElementById('gmb-reader-ruler-height').addEventListener('input', updateRulerHeight);
        document.getElementById('gmb-reader-ruler-position').addEventListener('input', updateRulerPosition);
        document.getElementById('gmb-reader-ruler-opacity').addEventListener('input', updateRulerOpacity);
        
        // Focus Mode controls
        document.getElementById('gmb-reader-focus-gutter-width').addEventListener('input', updateFocusModeGutterWidth);
        document.getElementById('gmb-reader-focus-text-opacity').addEventListener('input', updateFocusModeTextOpacity);
        
        // Stop Words controls
        document.getElementById('gmb-reader-stopwords-opacity').addEventListener('input', updateStopWordsOpacity);
        
        // Outline controls
        document.getElementById('gmb-reader-outline-font-scale').addEventListener('input', updateOutlineFontScale);
        
        // Ruler color selection
        const colorElements = document.querySelectorAll('.gmb-reader-ruler-color');
        colorElements.forEach(colorEl => {
            colorEl.addEventListener('click', () => updateRulerColor(colorEl.dataset.color));
        });
        
        // Hex color input
        document.getElementById('gmb-reader-ruler-hex-apply').addEventListener('click', applyHexColor);
        
        
        // Close popup when clicking outside
        document.addEventListener('click', (e) => {
            const popup = document.getElementById('gmb-reader-popup');
            const toggle = document.getElementById('gmb-reader-popup-toggle');
            
            if (popup && !popup.contains(e.target) && !toggle.contains(e.target)) {
                closePopup();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', handleKeyboardShortcuts);
    }
    
    function handleKeyboardShortcuts(e) {
        if (!isOpen) return;
        
        // Escape to close
        if (e.key === 'Escape') {
            close();
            return;
        }
        
        // Prevent default for our shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case '=':
                case '+':
                    e.preventDefault();
                    increaseFontSize();
                    break;
                case '-':
                    e.preventDefault();
                    decreaseFontSize();
                    break;
            }
        }
    }
    
    function increaseFontSize() {
        currentFontSize = Math.min(currentFontSize + 1, 32);
        updateFontSize();
        saveSettings();
    }
    
    function decreaseFontSize() {
        currentFontSize = Math.max(currentFontSize - 1, 12);
        updateFontSize();
        saveSettings();
    }
    
    function updateFontSize() {
        const wrapper = document.getElementById('gmb-reader-content-wrapper');
        if (wrapper) {
            wrapper.style.setProperty('font-size', currentFontSize + 'px', 'important');
        }
    }
    
    
    function changeTheme(theme) {
        settings.theme = theme;
        applyTheme();
        saveSettings();
    }
    
    function applyTheme() {
        const app = readerContainer.querySelector('.gmb-reader-app');
        if (app) {
            // Remove existing theme classes
            app.classList.remove('gmb-reader-theme-white', 'gmb-reader-theme-yellow', 'gmb-reader-theme-dark');
            
            // Add new theme class
            app.classList.add(`gmb-reader-theme-${settings.theme}`);
        }
        
        // Update popup theme
        const popup = document.getElementById('gmb-reader-popup');
        if (popup) {
            // Remove existing popup theme classes
            popup.classList.remove('gmb-reader-popup-theme-white', 'gmb-reader-popup-theme-yellow', 'gmb-reader-popup-theme-dark');
            
            // Add new popup theme class
            popup.classList.add(`gmb-reader-popup-theme-${settings.theme}`);
        }
        
        // Update outline theme
        updateOutlineTheme();
    }
    
    function applyFontWeight() {
        const button = document.getElementById('gmb-reader-font-weight');
        const wrapper = document.getElementById('gmb-reader-content-wrapper');
        
        if (button && wrapper && settings.fontWeight) {
            if (settings.fontWeight === 'bold') {
                button.textContent = 'Normal';
                button.classList.add('gmb-reader-active');
                wrapper.style.fontWeight = 'bold';
            } else {
                button.textContent = 'Bold';
                button.classList.remove('gmb-reader-active');
                wrapper.style.fontWeight = 'normal';
            }
        }
    }
    
    function togglePopup() {
        const popup = document.getElementById('gmb-reader-popup');
        if (popup) {
            if (popupOpen) {
                closePopup();
            } else {
                openPopup();
            }
        }
    }
    
    function openPopup() {
        const popup = document.getElementById('gmb-reader-popup');
        if (popup) {
            popup.style.display = 'block';
            popupOpen = true;
        }
    }
    
    function closePopup() {
        const popup = document.getElementById('gmb-reader-popup');
        if (popup) {
            popup.style.display = 'none';
            popupOpen = false;
        }
    }
    
    function toggleFontWeight() {
        const button = document.getElementById('gmb-reader-font-weight');
        const wrapper = document.getElementById('gmb-reader-content-wrapper');
        
        if (button && wrapper) {
            const isBold = button.textContent === 'Bold';
            
            if (isBold) {
                button.textContent = 'Normal';
                button.classList.add('gmb-reader-active');
                wrapper.style.fontWeight = 'bold';
                settings.fontWeight = 'bold';
            } else {
                button.textContent = 'Bold';
                button.classList.remove('gmb-reader-active');
                wrapper.style.fontWeight = 'normal';
                settings.fontWeight = 'normal';
            }
            saveSettings();
        }
    }
    
    function toggleFocusMode() {
        if (isFocusMode) {
            disableFocusMode();
        } else {
            enableFocusMode();
        }
    }
    
    function enableFocusMode() {
        isFocusMode = true;
        settings.focusMode = true;
        const app = readerContainer.querySelector('.gmb-reader-app');
        const button = document.getElementById('gmb-reader-focus-toggle');
        const accordion = document.getElementById('gmb-reader-focus-mode-accordion');
        
        if (app) {
            app.classList.add('gmb-reader-focus');
        }
        
        if (button) {
            button.classList.add('gmb-reader-active');
        }
        
        if (accordion) {
            accordion.style.display = 'block';
        }
        
        applyFocusModeStyles();
        saveSettings();
    }
    
    function disableFocusMode() {
        isFocusMode = false;
        settings.focusMode = false;
        const app = readerContainer.querySelector('.gmb-reader-app');
        const button = document.getElementById('gmb-reader-focus-toggle');
        const accordion = document.getElementById('gmb-reader-focus-mode-accordion');
        
        if (app) {
            app.classList.remove('gmb-reader-focus');
        }
        
        if (button) {
            button.classList.remove('gmb-reader-active');
        }
        
        if (accordion) {
            accordion.style.display = 'none';
        }
        
        saveSettings();
    }
    
    function toggleStopWords() {
        if (isStopWordFading) {
            disableStopWordFading();
        } else {
            enableStopWordFading();
        }
    }
    
    function enableStopWordFading() {
        isStopWordFading = true;
        settings.stopWordFading = true;
        const app = readerContainer.querySelector('.gmb-reader-app');
        const button = document.getElementById('gmb-reader-stopwords-toggle');
        const accordion = document.getElementById('gmb-reader-stopwords-accordion');
        
        if (app) {
            app.classList.add('gmb-reader-stopwords');
        }
        
        if (button) {
            button.classList.add('gmb-reader-active');
        }
        
        if (accordion) {
            accordion.style.display = 'block';
        }
        
        // Process stop words in content
        processStopWords();
        
        // Apply stop words styles
        applyStopWordsStyles();
        
        saveSettings();
    }
    
    function disableStopWordFading() {
        isStopWordFading = false;
        settings.stopWordFading = false;
        const app = readerContainer.querySelector('.gmb-reader-app');
        const button = document.getElementById('gmb-reader-stopwords-toggle');
        const accordion = document.getElementById('gmb-reader-stopwords-accordion');
        
        if (app) {
            app.classList.remove('gmb-reader-stopwords');
        }
        
        if (button) {
            button.classList.remove('gmb-reader-active');
        }
        
        if (accordion) {
            accordion.style.display = 'none';
        }
        
        // Remove stop word fading safely
        removeStopWordSpans();
        
        saveSettings();
    }
    
    // Helper function to safely remove stop word spans
    function removeStopWordSpans() {
        const content = document.getElementById('gmb-reader-article-content');
        if (content) {
            const fadeSpans = content.querySelectorAll('.gmb-reader-fade');
            console.log('Removing', fadeSpans.length, 'stop word spans');
            fadeSpans.forEach(span => {
                // Replace span with its text content to avoid HTML corruption
                const textNode = document.createTextNode(span.textContent);
                span.parentNode.replaceChild(textNode, span);
            });
        }
    }
    
    function toggleImages() {
        const button = document.getElementById('gmb-reader-images-toggle');
        const app = readerContainer.querySelector('.gmb-reader-app');
        
        if (button && app) {
            const isShowingImages = button.textContent === 'Hide Images';
            
            if (isShowingImages) {
                // Hide images
                button.textContent = 'Show Images';
                button.classList.add('gmb-reader-active');
                app.classList.add('gmb-reader-hide-images');
                settings.showImages = false;
            } else {
                // Show images
                button.textContent = 'Hide Images';
                button.classList.remove('gmb-reader-active');
                app.classList.remove('gmb-reader-hide-images');
                settings.showImages = true;
            }
            saveSettings();
        }
    }
    
    function toggleMeta() {
        const button = document.getElementById('gmb-reader-meta-toggle');
        const metaElement = document.querySelector('.gmb-reader-meta');
        
        if (button && metaElement) {
            const isShowingMeta = button.textContent === 'Hide Meta';
            
            if (isShowingMeta) {
                // Hide meta
                button.textContent = 'Show Meta';
                button.classList.add('gmb-reader-active');
                metaElement.style.display = 'none';
                settings.showMeta = false;
            } else {
                // Show meta
                button.textContent = 'Hide Meta';
                button.classList.remove('gmb-reader-active');
                metaElement.style.display = 'block';
                settings.showMeta = true;
            }
            saveSettings();
        }
    }
    
    // Show Site Elements Functions
    function toggleShowSiteElements() {
        if (isShowSiteElements) {
            disableShowSiteElements();
        } else {
            enableShowSiteElements();
        }
    }
    
    function enableShowSiteElements() {
        isShowSiteElements = true;
        showSiteElementsSettings.enabled = true;
        settings.showSiteElements = true;
        
        const button = document.getElementById('gmb-reader-show-site-elements-toggle');
        if (button) {
            button.classList.add('gmb-reader-active');
        }
        
        // Re-extract content with new settings
        if (window.GmbReadabilityExtractor) {
            const currentConfig = window.GmbReadabilityExtractor.getConfig();
            const newConfig = {
                cleaningOptions: {
                    ...currentConfig.cleaningOptions,
                    showSiteElements: true
                }
            };
            
            // Re-extract and update content
            extractedContent = window.GmbReadabilityExtractor.extract(newConfig);
            const contentElement = document.getElementById('gmb-reader-article-content');
            if (contentElement && extractedContent.content) {
                contentElement.innerHTML = extractedContent.content;
                
                // Reapply stop words if enabled
                if (isStopWordFading) {
                    processStopWords();
                }
            }
        }
        
        saveSettings();
    }
    
    function disableShowSiteElements() {
        isShowSiteElements = false;
        showSiteElementsSettings.enabled = false;
        settings.showSiteElements = false;
        
        const button = document.getElementById('gmb-reader-show-site-elements-toggle');
        if (button) {
            button.classList.remove('gmb-reader-active');
        }
        
        // Re-extract content with new settings
        if (window.GmbReadabilityExtractor) {
            const newConfig = {
                cleaningOptions: {
                    ...window.GmbReadabilityExtractor.getConfig().cleaningOptions,
                    showSiteElements: false
                }
            };
            
            // Re-extract and update content
            extractedContent = window.GmbReadabilityExtractor.extract(newConfig);
            const contentElement = document.getElementById('gmb-reader-article-content');
            if (contentElement && extractedContent.content) {
                contentElement.innerHTML = extractedContent.content;
                
                // Reapply stop words if enabled
                if (isStopWordFading) {
                    processStopWords();
                }
            }
        }
        
        saveSettings();
    }
    
    // Focus Ruler Functions
    function toggleFocusRuler() {
        const button = document.getElementById('gmb-reader-focus-ruler-toggle');
        const accordion = document.getElementById('gmb-reader-ruler-accordion');
        
        if (isFocusRulerEnabled) {
            disableFocusRuler();
        } else {
            enableFocusRuler();
        }
    }
    
    function enableFocusRuler() {
        isFocusRulerEnabled = true;
        const button = document.getElementById('gmb-reader-focus-ruler-toggle');
        const accordion = document.getElementById('gmb-reader-ruler-accordion');
        
        if (button) {
            button.classList.add('gmb-reader-active');
        }
        
        if (accordion) {
            accordion.style.display = 'block';
        }
        
        createRulerElement();
        updateRulerDisplay();
        saveSettings();
    }
    
    function disableFocusRuler() {
        isFocusRulerEnabled = false;
        const button = document.getElementById('gmb-reader-focus-ruler-toggle');
        const accordion = document.getElementById('gmb-reader-ruler-accordion');
        
        if (button) {
            button.classList.remove('gmb-reader-active');
        }
        
        if (accordion) {
            accordion.style.display = 'none';
        }
        
        removeRulerElement();
        saveSettings();
    }
    
    function createRulerElement() {
        if (rulerElement) return;
        
        rulerElement = document.createElement('div');
        rulerElement.className = 'gmb-reader-focus-ruler';
        rulerElement.id = 'gmb-reader-focus-ruler';
        
        document.body.appendChild(rulerElement);
    }
    
    function removeRulerElement() {
        if (rulerElement) {
            rulerElement.remove();
            rulerElement = null;
        }
    }
    
    function updateRulerDisplay() {
        if (!rulerElement) return;
        
        // Convert hex to rgba with custom alpha from opacity setting
        const hexToRgba = (hex, alpha) => {
            const r = parseInt(hex.slice(1, 3), 16);
            const g = parseInt(hex.slice(3, 5), 16);
            const b = parseInt(hex.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        };
        
        const alpha = rulerSettings.opacity / 100; // Convert percentage to decimal
        rulerElement.style.backgroundColor = hexToRgba(rulerSettings.color, alpha);
        rulerElement.style.height = rulerSettings.height + 'px';
        rulerElement.style.display = rulerSettings.show ? 'block' : 'none';
        rulerElement.style.top = rulerSettings.position + '%';
    }
    
    function toggleRulerVisibility() {
        const checkbox = document.getElementById('gmb-reader-ruler-show');
        rulerSettings.show = checkbox.checked;
        updateRulerDisplay();
        saveSettings();
    }
    
    function updateRulerColor(color) {
        rulerSettings.color = color;
        
        // Update active color selection
        document.querySelectorAll('.gmb-reader-ruler-color').forEach(el => {
            el.classList.remove('gmb-reader-ruler-color-active');
        });
        const colorElement = document.querySelector(`[data-color="${color}"]`);
        if (colorElement) {
            colorElement.classList.add('gmb-reader-ruler-color-active');
        }
        
        updateRulerDisplay();
        saveSettings();
    }
    
    function applyHexColor() {
        const hexInput = document.getElementById('gmb-reader-ruler-hex');
        let hexValue = hexInput.value.trim();
        
        // Add # if not present
        if (hexValue && !hexValue.startsWith('#')) {
            hexValue = '#' + hexValue;
        }
        
        // Validate hex color (basic validation)
        const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
        if (hexRegex.test(hexValue)) {
            updateRulerColor(hexValue);
            hexInput.value = hexValue; // Update input with proper format
        } else {
            // Invalid hex, flash the input border red
            hexInput.style.borderColor = '#EF4444';
            setTimeout(() => {
                hexInput.style.borderColor = '';
            }, 1000);
        }
    }
    
    function updateRulerHeight() {
        const slider = document.getElementById('gmb-reader-ruler-height');
        const valueDisplay = document.getElementById('gmb-reader-ruler-height-value');
        
        rulerSettings.height = parseInt(slider.value);
        valueDisplay.textContent = rulerSettings.height + 'px';
        
        updateRulerDisplay();
        saveSettings();
    }
    
    function updateRulerPosition() {
        const slider = document.getElementById('gmb-reader-ruler-position');
        const valueDisplay = document.getElementById('gmb-reader-ruler-position-value');
        
        rulerSettings.position = parseInt(slider.value);
        valueDisplay.textContent = rulerSettings.position + '%';
        
        updateRulerDisplay();
        saveSettings();
    }
    
    function updateRulerOpacity() {
        const slider = document.getElementById('gmb-reader-ruler-opacity');
        const valueDisplay = document.getElementById('gmb-reader-ruler-opacity-value');
        
        rulerSettings.opacity = parseInt(slider.value);
        valueDisplay.textContent = rulerSettings.opacity + '%';
        
        updateRulerDisplay();
        saveSettings();
    }
    
    // Focus Mode Control Functions
    function updateFocusModeGutterWidth() {
        const slider = document.getElementById('gmb-reader-focus-gutter-width');
        const valueDisplay = document.getElementById('gmb-reader-focus-gutter-width-value');
        
        focusModeSettings.gutterWidth = parseInt(slider.value);
        valueDisplay.textContent = focusModeSettings.gutterWidth + 'px';
        
        applyFocusModeStyles();
        saveSettings();
    }
    
    function updateFocusModeTextOpacity() {
        const slider = document.getElementById('gmb-reader-focus-text-opacity');
        const valueDisplay = document.getElementById('gmb-reader-focus-text-opacity-value');
        
        focusModeSettings.textOpacity = parseInt(slider.value);
        valueDisplay.textContent = focusModeSettings.textOpacity + '%';
        
        applyFocusModeStyles();
        saveSettings();
    }
    
    function applyFocusModeStyles() {
        if (!readerContainer) return;
        
        // Apply CSS custom properties to the reader container
        const app = readerContainer.querySelector('.gmb-reader-app');
        if (app) {
            app.style.setProperty('--focus-gutter-width', focusModeSettings.gutterWidth + 'px');
            // Invert opacity logic to match Stop Words behavior: lower values = more transparent borders
            app.style.setProperty('--focus-text-opacity', (100 - focusModeSettings.textOpacity) / 100);
        }
        
        // Update stop words opacity to prevent compounding when both features are active
        if (isStopWordFading) {
            applyStopWordsStyles();
        }
    }
    
    // Stop Words Control Functions
    function updateStopWordsOpacity() {
        const slider = document.getElementById('gmb-reader-stopwords-opacity');
        const valueDisplay = document.getElementById('gmb-reader-stopwords-opacity-value');
        
        stopWordsSettings.opacity = parseInt(slider.value);
        valueDisplay.textContent = stopWordsSettings.opacity + '%';
        
        applyStopWordsStyles();
        saveSettings();
    }
    
    function applyStopWordsStyles() {
        if (!readerContainer) return;
        
        // Apply CSS custom properties to the reader container
        const app = readerContainer.querySelector('.gmb-reader-app');
        if (app) {
            let finalOpacity = stopWordsSettings.opacity / 100;
            
            // Prevent compounding when both Focus Mode and Stop Words are active
            if (isFocusMode && isStopWordFading) {
                // Get focus border opacity (remember it's inverted: lower setting = more opacity)
                const focusBorderOpacity = (100 - focusModeSettings.textOpacity) / 100;
                
                // Compensate: desired_opacity = compensated_opacity × border_opacity
                // So: compensated_opacity = desired_opacity ÷ border_opacity
                if (focusBorderOpacity > 0) {
                    finalOpacity = Math.min(1.0, finalOpacity / focusBorderOpacity);
                }
            }
            
            app.style.setProperty('--stopwords-opacity', finalOpacity);
        }
    }
    
    function initializeRulerControls() {
        // Set initial values from settings
        const showCheckbox = document.getElementById('gmb-reader-ruler-show');
        const heightSlider = document.getElementById('gmb-reader-ruler-height');
        const positionSlider = document.getElementById('gmb-reader-ruler-position');
        const opacitySlider = document.getElementById('gmb-reader-ruler-opacity');
        const heightValue = document.getElementById('gmb-reader-ruler-height-value');
        const positionValue = document.getElementById('gmb-reader-ruler-position-value');
        const opacityValue = document.getElementById('gmb-reader-ruler-opacity-value');
        const hexInput = document.getElementById('gmb-reader-ruler-hex');
        
        if (showCheckbox) showCheckbox.checked = rulerSettings.show;
        if (heightSlider) heightSlider.value = rulerSettings.height;
        if (positionSlider) positionSlider.value = rulerSettings.position;
        if (opacitySlider) opacitySlider.value = rulerSettings.opacity;
        if (heightValue) heightValue.textContent = rulerSettings.height + 'px';
        if (positionValue) positionValue.textContent = rulerSettings.position + '%';
        if (opacityValue) opacityValue.textContent = rulerSettings.opacity + '%';
        if (hexInput) hexInput.value = rulerSettings.color;
        
        // Set active color
        document.querySelectorAll('.gmb-reader-ruler-color').forEach(el => {
            el.classList.remove('gmb-reader-ruler-color-active');
            if (el.dataset.color === rulerSettings.color) {
                el.classList.add('gmb-reader-ruler-color-active');
            }
        });
        
        // Initialize button state
        const button = document.getElementById('gmb-reader-focus-ruler-toggle');
        const accordion = document.getElementById('gmb-reader-ruler-accordion');
        
        if (isFocusRulerEnabled) {
            if (button) button.classList.add('gmb-reader-active');
            if (accordion) accordion.style.display = 'block';
            createRulerElement();
            updateRulerDisplay();
        } else {
            if (button) button.classList.remove('gmb-reader-active');
            if (accordion) accordion.style.display = 'none';
        }
    }
    
    function initializeFocusModeControls() {
        // Set initial values from settings
        const gutterWidthSlider = document.getElementById('gmb-reader-focus-gutter-width');
        const textOpacitySlider = document.getElementById('gmb-reader-focus-text-opacity');
        const gutterWidthValue = document.getElementById('gmb-reader-focus-gutter-width-value');
        const textOpacityValue = document.getElementById('gmb-reader-focus-text-opacity-value');
        
        if (gutterWidthSlider) gutterWidthSlider.value = focusModeSettings.gutterWidth;
        if (textOpacitySlider) textOpacitySlider.value = focusModeSettings.textOpacity;
        if (gutterWidthValue) gutterWidthValue.textContent = focusModeSettings.gutterWidth + 'px';
        if (textOpacityValue) textOpacityValue.textContent = focusModeSettings.textOpacity + '%';
        
        // Initialize accordion state
        const accordion = document.getElementById('gmb-reader-focus-mode-accordion');
        
        if (isFocusMode) {
            if (accordion) accordion.style.display = 'block';
            applyFocusModeStyles();
        } else {
            if (accordion) accordion.style.display = 'none';
        }
    }
    
    function initializeStopWordsControls() {
        // Set initial values from settings
        const opacitySlider = document.getElementById('gmb-reader-stopwords-opacity');
        const opacityValue = document.getElementById('gmb-reader-stopwords-opacity-value');
        
        if (opacitySlider) opacitySlider.value = stopWordsSettings.opacity;
        if (opacityValue) opacityValue.textContent = stopWordsSettings.opacity + '%';
        
        // Initialize accordion state
        const accordion = document.getElementById('gmb-reader-stopwords-accordion');
        
        if (isStopWordFading) {
            if (accordion) accordion.style.display = 'block';
            applyStopWordsStyles();
        } else {
            if (accordion) accordion.style.display = 'none';
        }
    }
    
    // Outline Functions
    function toggleOutline() {
        if (isOutlineEnabled) {
            disableOutline();
        } else {
            enableOutline();
        }
    }
    
    function updateOutlineFontScale() {
        const slider = document.getElementById('gmb-reader-outline-font-scale');
        const valueDisplay = document.getElementById('gmb-reader-outline-font-scale-value');
        
        outlineFontScale.scale = parseInt(slider.value);
        valueDisplay.textContent = outlineFontScale.scale + '%';
        
        // Regenerate outline with new font scaling
        if (isOutlineEnabled) {
            generateOutlineLinks();
        }
        
        saveSettings();
    }
    
    function enableOutline() {
        isOutlineEnabled = true;
        const button = document.getElementById('gmb-reader-outline-toggle');
        const accordion = document.getElementById('gmb-reader-outline-accordion');
        
        if (button) {
            button.classList.add('gmb-reader-active');
        }
        
        if (accordion) {
            accordion.style.display = 'block';
        }
        
        showOutlinePanel();
        generateOutlineLinks();
        saveSettings();
    }
    
    function disableOutline() {
        isOutlineEnabled = false;
        const button = document.getElementById('gmb-reader-outline-toggle');
        const accordion = document.getElementById('gmb-reader-outline-accordion');
        
        if (button) {
            button.classList.remove('gmb-reader-active');
        }
        
        if (accordion) {
            accordion.style.display = 'none';
        }
        
        hideOutlinePanel();
        saveSettings();
    }
    
    function showOutlinePanel() {
        outlinePanel = document.getElementById('gmb-reader-outline-panel');
        if (outlinePanel) {
            outlinePanel.style.display = 'block';
        }
    }
    
    function hideOutlinePanel() {
        if (outlinePanel) {
            outlinePanel.style.display = 'none';
        }
    }
    
    function generateOutlineLinks() {
        const contentWrapper = document.getElementById('gmb-reader-article-content');
        const outlineContent = document.getElementById('gmb-reader-outline-content');
        
        if (!contentWrapper || !outlineContent) return;
        
        // Find all headings h1-h6
        const headings = contentWrapper.querySelectorAll('h1, h2, h3, h4, h5, h6');
        
        if (headings.length === 0) {
            outlineContent.innerHTML = '<div class="gmb-reader-outline-empty">No headings found</div>';
            return;
        }
        
        // Generate outline links as plain text divs
        let outlineHTML = '';
        headings.forEach((heading, index) => {
            const level = heading.tagName.toLowerCase();
            const text = heading.textContent.trim();
            const id = heading.id || `gmb-reader-heading-${index}`;
            
            // Add ID to heading if it doesn't have one
            if (!heading.id) {
                heading.id = id;
            }
            
            // Determine indent and base font size based on heading level
            let indent = 0;
            let baseFontSize = 16; // Base size in pixels
            let fontWeight = '400';
            
            switch(level) {
                case 'h1':
                    indent = 0;
                    baseFontSize = 24;
                    fontWeight = '700';
                    break;
                case 'h2':
                    indent = 0;
                    baseFontSize = 22;
                    fontWeight = '600';
                    break;
                case 'h3':
                    indent = 16;
                    baseFontSize = 20;
                    fontWeight = '500';
                    break;
                case 'h4':
                    indent = 32;
                    baseFontSize = 18;
                    fontWeight = '500';
                    break;
                case 'h5':
                    indent = 48;
                    baseFontSize = 17;
                    fontWeight = '400';
                    break;
                case 'h6':
                    indent = 64;
                    baseFontSize = 16;
                    fontWeight = '400';
                    break;
            }
            
            // Apply font scaling
            const scaledFontSize = Math.round(baseFontSize * (outlineFontScale.scale / 100));
            const fontSize = scaledFontSize + 'px';
            
            outlineHTML += `
                <div class="gmb-reader-outline-text-link" 
                     onclick="scrollToHeading('${id}')" 
                     title="${text}"
                     style="
                         padding-left: ${indent}px !important;
                         font-size: ${fontSize} !important;
                         font-weight: ${fontWeight} !important;
                         margin: 0 !important;
                         padding-top: 2px !important;
                         padding-bottom: 2px !important;
                         cursor: pointer !important;
                         color: inherit !important;
                         line-height: 1.3 !important;
                         font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
                     ">
                    ${text}
                </div>
            `;
        });
        
        outlineContent.innerHTML = outlineHTML;
    }
    
    function scrollToHeading(headingId) {
        const heading = document.getElementById(headingId);
        if (heading) {
            heading.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
            
            // Flash the heading briefly to show where we scrolled to
            const originalStyle = heading.style.cssText;
            heading.style.background = 'rgba(124, 58, 237, 0.2)';
            heading.style.transition = 'background 0.3s ease';
            
            setTimeout(() => {
                heading.style.background = '';
                setTimeout(() => {
                    heading.style.cssText = originalStyle;
                }, 300);
            }, 800);
        }
    }
    
    // Make scrollToHeading globally accessible
    window.scrollToHeading = scrollToHeading;
    
    function updateOutlineTheme() {
        if (!outlinePanel) return;
        
        // Remove existing theme classes
        outlinePanel.classList.remove('gmb-reader-outline-theme-white', 'gmb-reader-outline-theme-yellow', 'gmb-reader-outline-theme-dark');
        
        // Add current theme class
        outlinePanel.classList.add(`gmb-reader-outline-theme-${settings.theme}`);
    }
    
    function initializeOutlineControls() {
        // Set initial values from settings
        const fontScaleSlider = document.getElementById('gmb-reader-outline-font-scale');
        const fontScaleValue = document.getElementById('gmb-reader-outline-font-scale-value');
        
        if (fontScaleSlider) fontScaleSlider.value = outlineFontScale.scale;
        if (fontScaleValue) fontScaleValue.textContent = outlineFontScale.scale + '%';
        
        // Initialize accordion state
        const accordion = document.getElementById('gmb-reader-outline-accordion');
        
        if (isOutlineEnabled) {
            if (accordion) accordion.style.display = 'block';
        } else {
            if (accordion) accordion.style.display = 'none';
        }
    }
    
    function initializeOutline() {
        const button = document.getElementById('gmb-reader-outline-toggle');
        
        if (isOutlineEnabled) {
            if (button) button.classList.add('gmb-reader-active');
            showOutlinePanel();
            generateOutlineLinks();
        } else {
            if (button) button.classList.remove('gmb-reader-active');
            hideOutlinePanel();
        }
        
        // Initialize outline controls
        initializeOutlineControls();
    }
    
    function calculateReadingTime() {
        if (!extractedContent || !extractedContent.content) {
            return 0;
        }
        
        // Create a temporary div to extract text content without HTML tags
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = extractedContent.content;
        const textContent = tempDiv.textContent || tempDiv.innerText || '';
        
        // Count words (split by whitespace and filter out empty strings)
        const words = textContent.trim().split(/\s+/).filter(word => word.length > 0);
        const wordCount = words.length;
        
        // Calculate reading time (200 words per minute is standard)
        const readingTimeMinutes = Math.ceil(wordCount / 200);
        
        return readingTimeMinutes;
    }
    
    function updateReadingTime() {
        const readingTimeElement = document.getElementById('gmb-reader-reading-time');
        if (readingTimeElement) {
            const minutes = calculateReadingTime();
            const timeText = minutes === 1 ? '1 min read' : `${minutes} min read`;
            readingTimeElement.textContent = timeText;
        }
    }
    
    function processStopWords() {
        // Only process if stop words are actually enabled
        if (!isStopWordFading) {
            console.log('Stop words processing skipped - feature is disabled');
            return;
        }
        
        const content = document.getElementById('gmb-reader-article-content');
        if (content && window.GmbReadabilityExtractor) {
            // Clear any existing stop word spans first to avoid double-processing
            if (content.querySelector('.gmb-reader-fade')) {
                console.log('Clearing existing stop word spans before reprocessing');
                removeStopWordSpans();
            }
            
            // Use the new safe DOM-aware processing
            if (window.GmbReadabilityExtractor.processStopWordsInDOM) {
                console.log('Processing stop words safely (DOM-aware, text nodes only)');
                window.GmbReadabilityExtractor.processStopWordsInDOM(content);
            } else {
                console.warn('New safe stop words processor not available, skipping to prevent HTML corruption');
            }
        }
    }
    
    function updateSettings(newSettings) {
        settings = { ...settings, ...newSettings };
        
        // Update font size if changed
        if (newSettings.fontSize && newSettings.fontSize !== currentFontSize) {
            currentFontSize = newSettings.fontSize;
            updateFontSize();
        }
        
        
        // Update theme if changed
        if (newSettings.theme && newSettings.theme !== settings.theme) {
            applyTheme();
        }
        
        // Update font weight if changed
        if (newSettings.fontWeight && newSettings.fontWeight !== settings.fontWeight) {
            applyFontWeight();
        }
        
        // Update focus mode if changed
        if (newSettings.focusMode !== undefined && newSettings.focusMode !== isFocusMode) {
            if (newSettings.focusMode) {
                enableFocusMode();
            } else {
                disableFocusMode();
            }
        }
        
        // Update stop word fading if changed
        if (newSettings.stopWordFading !== undefined && newSettings.stopWordFading !== isStopWordFading) {
            if (newSettings.stopWordFading) {
                enableStopWordFading();
            } else {
                disableStopWordFading();
            }
        }
    }
    
    // Listen for messages from ISOLATED world (button script)
    window.addEventListener('message', (event) => {
        if (event.source !== window) return;
        
        switch (event.data.type) {
            case 'MINIMAL_READER_OPEN':
                if (event.data.settings) {
                    open(event.data.settings, event.data.extensionId);
                }
                break;
                
            case 'MINIMAL_READER_CLOSE':
                close();
                break;
                
            case 'MINIMAL_READER_UPDATE_SETTINGS':
                if (event.data.settings) {
                    updateSettings(event.data.settings);
                }
                break;
        }
    });
    
    // Global function to close reader (for interface close button)
    window.GmbMinimalReaderClose = () => {
        close();
        // Notify ISOLATED world that interface was closed
        window.postMessage({
            type: 'MINIMAL_READER_INTERFACE_CLOSED'
        }, '*');
    };
    
    console.log('Minimal Reader Interface: Initialized with postMessage communication');
})();