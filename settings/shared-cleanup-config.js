// Shared Cleanup Configuration
// Central source of truth for all DOM cleanup operations
// Used by both background.js context menu cleanup and popup Quick Actions DOM restore

class SharedCleanupConfig {
    static getGlobalListeners() {
        return [
            // Quick Action escape listeners
            'quickEditEscapeListener',
            'quickEditListeners',
            'colorPaletteEscapeListener', 
            'colorPickerEscapeListener',
            'fontInspectorEscapeListener',
            'fontStylesEscapeListener',
            'cssClassInspectorEscapeListener',
            'pageStructureEscapeListener',
            'robotsTxtEscapeListener',
            'mozdaCheckerEscapeListener',
            'keywordDensityEscapeListener',
            'xmlSitemapEscapeListener',
            'screenReaderEscapeListener',
            'waybackMachineEscapeListener',
            'linkCheckerEscapeListener',
            'dragSelectLinksEscapeListener',
            'linksExtractorEscapeListener',
            'bulkLinkOpenEscapeListener',
            'imagesActionEscapeListener',
            'responsiveEscapeListener',
            'copyElementEscapeListener',
            'cleanSelectedContentEscapeListener',
            'cleanAndTitleEscapeListener',
            'htmlCleanerEscapeListener',
            'semanticElementsEscapeListener',
            'utmBuilderEscapeListener',
            'emailGeneratorEscapeListener'
        ];
    }

    static getGlobalProperties() {
        return [
            // Quick Action class instances
            'HtagsAction', 'ShowLinksAction', 'ShowHiddenAction', 'KeywordAction',
            'BoldFromSerpAction', 'SchemaAction', 'ImagesAction', 'MetadataAction', 'UTMBuilderAction',
            'PageStructureAction', 'YouTubeEmbedScraperAction', 'QuickEditAction',
            'ColorPaletteExtractorAction', 'ColorPickerAction', 'FontInspectorAction', 'FontStylesAction', 'CSSClassInspectorAction',
            'RobotsTxtAction', 'MOZDACheckerAction', 'XMLSitemapCheckerAction',
            'ScreenReaderSimulationAction', 'WaybackMachineAction', 'PageKeywordDensityAction',
            'LinkCheckerAction', 'CopyElementAction', 'LinksExtractorAction', 'BulkLinkOpenAction', 'CleanSelectedContentAction', 'CleanAndTitleAction', 'HTMLCleanerAction',
            'ResponsiveAction', 'SemanticElementsAction', 'EmailGeneratorAction',
            
            // Email Generator properties
            'emailGeneratorOriginalEmail',
            
            // Drag Select Links properties
            'DragSelectLinksActive', 'dragSelectLinksCleanup', 'dragSelectLinksEscapeListener',
            
            // Links Extractor properties
            'linksExtractorActive', 'linksExtractorCleanup',
            
            // Screenshot Selector properties
            'STMScreenshotSelectorActive',
            
            // Alert popup properties
            'alertPopupHandler', 'alertPopupInstance',
            
            // Tooltip System properties
            'UniversalTooltipSystem', 'InfoIconInitializer',
            
            // Enhanced Sound Preview System properties
            'soundPreviewSystem', 'currentPreviewState', 'updatePreviewButtonUI', 'playEnhancedPreview', 'playFullAudioSound',
            'stopCurrentPreview', 'handlePreviewButtonClick', 'tickingPreviewInterval',
            
            // Minimal Reader properties
            'MinimalReaderLoaded', 'minimalReaderObserver', 'minimalReaderCleanup',
            
            // Global state variables
            'isKeyPressed', 'isDragging', 'imagesActionActive', 'linksExtractorActive'
        ];
    }

    static getPanelSelectors() {
        return [
            // Quick Action panels and overlays
            '#quick-edit-notice', '.quick-edit-selection-box', '#quick-edit-animation-style',
            '.color-palette-extractor-panel', '.color-picker-panel', '.color-picker-tooltip',
            '.font-inspector-panel', '.font-styles-panel', '.css-class-inspector-panel',
            '.page-structure-panel', '.robots-txt-panel', '.mozda-checker-panel',
            '.keyword-density-panel', '.xml-sitemap-panel', '.screen-reader-panel',
            '.wayback-machine-panel', '.email-generator-popup', '.word-counter-panel', '.word-counter-quick-tooltip',
            '.linkchecker-notification', '.clean-content-notification',
            
            // Content analysis panels
            '.heading-structure-panel', '.heading-structure-overlay',
            '.htags-highlight', '.htags-panel', '.htags-overlay',
            '.schema-panel', '.schema-overlay',
            '.images-panel', '.images-overlay', '.images-audit-panel',
            '.metadata-panel', '.metadata-overlay',
            '.utm-builder-panel',
            '.youtube-embed-scraper-panel', '.youtube-embed-scraper-overlay',
            
            // Link and content tools
            '.show-links-highlight', '.show-links-panel',
            '.show-hidden-reveal', '.show-hidden-panel',
            '.bold-from-serp-highlight',
            '.page-structure-overlay', '.page-structure-hover-outline',
            
            // Links Extractor elements
            '.links-extractor-panel', '.links-extractor-notification', '.links-extractor-styles',
            '.links-extractor-instructions', '[data-links-extractor-style]',
            
            // Bulk Link Open elements
            '.bulk-link-open-popup', '[data-bulk-link-open]',
            
            // Drag Select Links elements
            '#drag-select-links-popup', '.drag-select-links-panel', '[data-drag-select-box]',
            '.drag-select-highlight', '.drag-select-counter',
            
            // Copy Element elements
            '.copy-element-notification', '.copy-element-styles', '.copy-element-instructions',
            '.copy-element-hover', '[data-copy-element-style]', '[data-copy-element-instructions]', '[data-copy-element-notification]',
            
            // Clean content elements
            '.clean-and-title-notification', '[data-clean-and-title-notification]', '[data-clean-and-title]',
            '.clean-content-notification', '[data-clean-content-notification]',
            '.html-cleaner-settings-panel',
            
            // Responsive simulator
            '.responsive-simulator-panel', '#responsive-simulator-panel',
            
            // Generic selectors
            '[data-quick-action]', '[data-context-action]',
            '.quick-action-overlay', '.context-action-overlay',
            '.extension-panel', '.extension-overlay',
            
            // Tooltip System elements
            '.universal-tooltip', '.info-icon-container', '.has-tooltip',
            '#universal-tooltip-styles',
            
            // Alert popup elements
            '#gmb-alert-popup',
            
            // YouTube Thumbnail Viewer elements
            '.youtube-thumbnail-viewer-popup',
            
            // Screenshot Selector elements (prevents purple overlay persistence)
            '[style*="z-index: 999999"]', // Screenshot overlay
            '[style*="z-index: 1000000"]', // Screenshot instruction banner
            
            // Enhanced Sound Preview System elements
            '#pomodoroWorkSoundPreview', '#pomodoroBreakSoundPreview', '#pomodoroTickingSoundPreview',
            '.pomodoro-preview-btn.playing' // Preview buttons in playing state
            
            // NOTE: Minimal Reader elements are NOT in general panel cleanup to prevent button disappearing
            // They are only cleaned up during context menu interference scenarios in background.js
        ];
    }

    static getContextMenuInterferenceCleanup() {
        // Specific cleanup pattern for context menu interference
        return {
            dragSelectLinks: [
                'dragSelectLinksCleanup',
                'DragSelectLinksActive',
                'dragSelectLinksEscapeListener'
            ],
            imagesAction: [
                'imagesActionEscapeListener',
                '.images-audit-panel'
            ],
            minimalReader: [
                'MinimalReaderLoaded',
                'minimalReaderObserver',
                'minimalReaderCleanup',
                '#minimal-reader-btn'
            ],
            globalState: [
                'isKeyPressed',
                'isDragging'
            ],
            contextMenuPrevention: [
                'preventContextMenu' // Both local and window versions
            ]
        };
    }

    static getSpecialCleanupHandlers() {
        return [
            // Functions that require special cleanup handling
            {
                name: 'quickEditListeners',
                type: 'object',
                events: ['keydown', 'keyup', 'mousedown', 'mousemove', 'mouseup']
            },
            {
                name: 'dragSelectLinksCleanup',
                type: 'function',
                action: 'execute'
            },
            {
                name: 'UniversalTooltipSystem',
                type: 'object',
                method: 'cleanup'
            },
            {
                name: 'InfoIconInitializer',
                type: 'object',
                method: 'cleanup'
            }
        ];
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SharedCleanupConfig;
} else if (typeof window !== 'undefined') {
    window.SharedCleanupConfig = SharedCleanupConfig;
}