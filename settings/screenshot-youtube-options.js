(function() {
    'use strict';
    
    // Default settings
    const defaultSettings = {
        screenshotKeyEnabled: true,
        screenshotFileFormat: 'png',
        screenshotFunctionality: 2 // Both
    };
    
    let currentSettings = {};
    
    // Initialize settings when page loads
    document.addEventListener('DOMContentLoaded', function() {
        loadSettings();
        setupEventListeners();
    });
    
    function loadSettings() {
        chrome.storage.sync.get(['gmbExtractorSettings'], function(result) {
            const settings = result.gmbExtractorSettings || {};
            
            // Load Screenshot YouTube specific settings
            currentSettings = {
                screenshotKeyEnabled: settings.screenshotKeyEnabled !== false,
                screenshotFileFormat: settings.screenshotFileFormat || 'png',
                screenshotFunctionality: settings.screenshotFunctionality !== undefined ? settings.screenshotFunctionality : 2
            };
            
            updateUI();
        });
    }
    
    function updateUI() {
        // Update checkbox states
        document.getElementById('screenshotKeyEnabled').checked = currentSettings.screenshotKeyEnabled;
        
        // Update radio buttons
        const functionalityRadios = document.querySelectorAll('input[name="screenshotFunctionality"]');
        functionalityRadios.forEach(radio => {
            radio.checked = parseInt(radio.value) === currentSettings.screenshotFunctionality;
        });
    }
    
    function setupEventListeners() {
        // Checkbox listeners
        document.getElementById('screenshotKeyEnabled').addEventListener('change', function() {
            currentSettings.screenshotKeyEnabled = this.checked;
        });
        
        // Radio button listeners
        const functionalityRadios = document.querySelectorAll('input[name="screenshotFunctionality"]');
        functionalityRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    currentSettings.screenshotFunctionality = parseInt(this.value);
                }
            });
        });
        
        // Button listeners
        document.getElementById('saveBtn').addEventListener('click', saveSettings);
        document.getElementById('cancelBtn').addEventListener('click', closeWindow);
        
        // ESC key listener
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeWindow();
            }
        });
    }
    
    function saveSettings() {
        chrome.storage.sync.get(['gmbExtractorSettings'], function(result) {
            const settings = result.gmbExtractorSettings || {};
            
            // Update Screenshot YouTube settings
            settings.screenshotKeyEnabled = currentSettings.screenshotKeyEnabled;
            settings.screenshotFileFormat = currentSettings.screenshotFileFormat;
            settings.screenshotFunctionality = currentSettings.screenshotFunctionality;
            
            chrome.storage.sync.set({ gmbExtractorSettings: settings }, function() {
                // Send update message to content scripts
                chrome.tabs.query({}, function(tabs) {
                    tabs.forEach(tab => {
                        chrome.tabs.sendMessage(tab.id, {
                            action: 'updateSettings',
                            settings: settings
                        }).catch(() => {
                            // Ignore errors for tabs that don't have content scripts
                        });
                    });
                });
                
                // Show success message briefly
                const saveBtn = document.getElementById('saveBtn');
                const originalText = saveBtn.textContent;
                saveBtn.textContent = 'Saved!';
                saveBtn.style.background = '#10B981';
                
                setTimeout(() => {
                    saveBtn.textContent = originalText;
                    saveBtn.style.background = '#7C3AED';
                    closeWindow();
                }, 1000);
            });
        });
    }
    
    function closeWindow() {
        window.close();
    }
})();