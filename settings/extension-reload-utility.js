// Extension Reload Utility for SEO Time Machines
// Provides safe extension reload functionality

(function() {
    'use strict';
    
    // Extension reload function
    function reloadExtension() {
        try {
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.reload) {
                console.log('GMB Extension Reload: Reloading extension...');
                chrome.runtime.reload();
            } else {
                console.error('GMB Extension Reload: chrome.runtime.reload not available');
                // Fallback: Show notification to user
                if (window.GMBNotifications && window.GMBNotifications.showError) {
                    window.GMBNotifications.showError(
                        'Reload Failed', 
                        'Extension reload not available. Please reload manually from extensions page.'
                    );
                }
            }
        } catch (error) {
            console.error('GMB Extension Reload: Error during reload:', error);
            if (window.GMBNotifications && window.GMBNotifications.showError) {
                window.GMBNotifications.showError(
                    'Reload Error', 
                    'Failed to reload extension: ' + error.message
                );
            }
        }
    }
    
    // Make reload function available globally
    window.GMBExtensionReload = {
        reload: reloadExtension,
        
        // Helper function for confirmation dialog
        reloadWithConfirmation: function() {
            if (confirm('Reload the extension? This will close any open popups and refresh all content scripts.')) {
                reloadExtension();
            }
        }
    };
    
    console.log('GMB Extension Reload Utility: Loaded');
})(); 