// Notification Utility for SEO Time Machines
// Controls all browser notifications based on user settings

(function() {
    'use strict';
    
    let notificationsEnabled = true; // Default to enabled
    let settingsLoaded = false;
    
    // Load notification setting from storage
    function loadNotificationSetting() {
        if (typeof chrome !== 'undefined' && chrome.storage) {
            chrome.storage.local.get(['gmbExtractorSettings'], function(result) {
                if (chrome.runtime.lastError) {
                    console.warn('GMBNotifications: Error loading settings:', chrome.runtime.lastError);
                    settingsLoaded = true;
                    return;
                }
                
                const settings = result.gmbExtractorSettings || {};
                notificationsEnabled = settings.notificationUtilityEnabled !== false; // Default to true if undefined
                settingsLoaded = true;
            });
        } else {
            settingsLoaded = true;
        }
    }
    
    // Function to show notification via background script (respects user setting)
    function showNotification(title, options = {}) {
        // If settings not loaded yet, wait and retry
        if (!settingsLoaded) {
            setTimeout(() => showNotification(title, options), 100);
            return;
        }
        
        // Only show if notifications are enabled
        if (notificationsEnabled) {
            // Try background script first (has full access to Chrome APIs)
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                chrome.runtime.sendMessage({
                    action: 'showNotification',
                    title: title,
                    message: options.body || options.message || '',
                    priority: options.priority || 1
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.warn('GMBNotifications: Background message failed:', chrome.runtime.lastError);
                        // Fallback to direct API or web notifications
                        fallbackToDirectAPI(title, options);
                    } else if (response && response.success) {
                        console.log('GMBNotifications: Background notification created:', response.notificationId);
                    } else {
                        console.warn('GMBNotifications: Background notification failed:', response?.error);
                        // Fallback to direct API or web notifications
                        fallbackToDirectAPI(title, options);
                    }
                });
                return 'background-request';
            } else {
                // Fallback to direct API or web notifications
                return fallbackToDirectAPI(title, options);
            }
        }
        
        // Return null if notifications disabled
        return null;
    }
    
    // Fallback to direct Chrome API or web notifications
    function fallbackToDirectAPI(title, options = {}) {
        // Try direct Chrome notifications API
        if (typeof chrome !== 'undefined' && chrome.notifications) {
            const notificationOptions = {
                type: 'basic',
                iconUrl: chrome.runtime?.getURL ? chrome.runtime.getURL('images/icon48.png') : 'images/icon48.png',
                title: title,
                message: options.body || options.message || '',
                ...options
            };
            
            const notificationId = 'gmb-direct-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            
            chrome.notifications.create(notificationId, notificationOptions, (createdId) => {
                if (chrome.runtime.lastError) {
                    console.warn('GMBNotifications: Direct API failed:', chrome.runtime.lastError);
                    // Final fallback to web notifications
                    fallbackToWebNotification(title, options);
                } else {
                    console.log('GMBNotifications: Direct notification created:', createdId);
                }
            });
            
            return notificationId;
        } else {
            // Final fallback to web notifications
            return fallbackToWebNotification(title, options);
        }
    }
    
    // Fallback to web notifications for contexts where Chrome API isn't available
    function fallbackToWebNotification(title, options = {}) {
        if (typeof Notification !== 'undefined' && Notification.permission === 'granted') {
            return new Notification(title, options);
        }
        return null;
    }
    
    // Function to request notification permission (Chrome extension global permission)
    function requestNotificationPermission() {
        if (!settingsLoaded) {
            setTimeout(requestNotificationPermission, 100);
            return Promise.resolve('default');
        }
        
        // Chrome extension notifications don't need explicit permission request
        // Permission is granted when the extension is installed (via manifest)
        if (typeof chrome !== 'undefined' && chrome.notifications) {
            return Promise.resolve('granted');
        }
        
        // Fallback to web notification permission for non-extension contexts
        if (notificationsEnabled && typeof Notification !== 'undefined' && Notification.permission === 'default') {
            return Notification.requestPermission();
        }
        
        return Promise.resolve(typeof Notification !== 'undefined' ? Notification.permission : 'denied');
    }
    
    // Listen for settings updates
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'updateSettings') {
                notificationsEnabled = message.settings.notificationUtilityEnabled !== false;
                sendResponse({received: true});
            }
        });
    }
    
    // Load initial settings
    loadNotificationSetting();
    
    // Make notification control available globally
    window.GMBNotifications = {
        show: showNotification,
        requestPermission: requestNotificationPermission,
        isEnabled: () => notificationsEnabled,
        reload: loadNotificationSetting,
        
        // Helper functions for common notification types
        showSuccess: (title, message) => showNotification(title, {
            message: message,
            type: 'basic',
            iconUrl: chrome.runtime?.getURL ? chrome.runtime.getURL('images/icon48.png') : 'images/icon48.png'
        }),
        
        showError: (title, message) => showNotification(title, {
            message: message,
            type: 'basic',
            iconUrl: chrome.runtime?.getURL ? chrome.runtime.getURL('images/icon48.png') : 'images/icon48.png'
        }),
        
        showProgress: (title, message) => showNotification(title, {
            message: message,
            type: 'progress',
            progress: 0,
            iconUrl: chrome.runtime?.getURL ? chrome.runtime.getURL('images/icon48.png') : 'images/icon48.png'
        }),
        
        // Test function for debugging
        test: () => {
            console.log('🔔 Testing GMB Notifications...');
            console.log('- Notifications enabled:', notificationsEnabled);
            console.log('- Chrome API available:', typeof chrome !== 'undefined' && !!chrome.notifications);
            console.log('- Web API available:', typeof Notification !== 'undefined');
            
            if (notificationsEnabled) {
                showNotification('SEO Time Machines Test', {
                    message: 'Global extension notifications are working!',
                    type: 'basic'
                });
            } else {
                console.log('Notifications are disabled in extension settings');
            }
        }
    };
    
})(); 