// Gmail Email Pinner - Pin important emails for quick access
(function() {
    'use strict';
    
    // Prevent multiple loads
    if (window.GMBGmailEmailPinnerLoaded) return;
    window.GMBGmailEmailPinnerLoaded = true;
    
    let isEnabled = true; // Default to ON
    let debugMode = false;
    let pinnedEmails = [];
    let activeOperations = new Set(); // Track active async operations
    
    // Enhanced global promise rejection handler for context invalidation
    window.addEventListener('unhandledrejection', function(event) {
        const reason = event.reason;
        const isContextError = reason && reason.message && (
            reason.message.includes('Extension context invalidated') ||
            reason.message.includes('Extension has been reloaded') ||
            reason.message.includes('This extension has been invalidated') ||
            reason.message.includes('The extension which generated this script is no longer available') ||
            reason.message.includes('chrome-extension://') ||
            (reason.stack && reason.stack.includes('Extension context invalidated'))
        );
        
        if (isContextError) {
            // Silently prevent unhandled context invalidation errors
            event.preventDefault();
            
            // Mark context as invalidated if not already done
            if (!contextInvalidated) {
                markContextInvalidated('Unhandled promise rejection detected: ' + reason.message);
            }
            
            if (debugMode) {
                console.warn('Gmail Email Pinner: Suppressed unhandled context invalidation promise rejection:', reason.message);
            }
            
            // Additional cleanup for persistent errors
            try {
                // Cancel all active operations to prevent further errors
                if (typeof activeOperations !== 'undefined' && activeOperations.size > 0) {
                    activeOperations.forEach(op => {
                        try {
                            op.cancel();
                        } catch (cancelError) {
                            // Ignore cleanup errors
                        }
                    });
                }
            } catch (cleanupError) {
                // Ignore cleanup errors
            }
        }
    });
    
    // Check if we're on Gmail
    function isGmailPage() {
        return window.location.hostname === 'mail.google.com';
    }
    
    // Check if we're in an individual email (from any Gmail context)
    function isIndividualEmail() {
        const url = window.location.href;
        
        // Must be Gmail
        if (!url.includes('mail.google.com/mail/u/')) {
            return false;
        }
        
        // Extract the fragment (part after #)
        const hashPart = url.split('#')[1];
        if (!hashPart) {
            return false;
        }
        
        // List of multi-email list views to exclude
        const listViews = [
            'inbox',       // inbox list
            'starred',     // starred emails list
            'snoozed',     // snoozed emails list
            'drafts',      // drafts list
            'sent',        // sent emails list
            'spam',        // spam folder
            'trash',       // trash folder
            'all',         // all mail (when showing list)
            'important',   // important emails list
            'chats'        // chat conversations list
        ];
        
        // Check if it's a plain list view (exclude these)
        if (listViews.includes(hashPart)) {
            return false;
        }
        
        // Check if it starts with a list view but has no email ID (still a list)
        for (const listView of listViews) {
            if (hashPart.startsWith(listView + '/') || hashPart.startsWith(listView + '?')) {
                // Extract what comes after the list view
                const afterListView = hashPart.split(listView + '/')[1] || hashPart.split(listView + '?')[1];
                
                // If there's something after the slash and it's long enough to be an email ID
                if (afterListView && afterListView.length > 10) {
                    return true; // This is an individual email
                }
                return false; // This is still a list view
            }
        }
        
        // For search URLs like #search/query/emailId
        if (hashPart.startsWith('search/')) {
            const searchParts = hashPart.split('/');
            // search/query/emailId - should have at least 3 parts
            if (searchParts.length >= 3) {
                const emailId = searchParts[searchParts.length - 1]; // Last part should be email ID
                return emailId.length > 10; // Email IDs are long
            }
            return false;
        }
        
        // For label URLs like #label/labelname/emailId
        if (hashPart.startsWith('label/')) {
            const labelParts = hashPart.split('/');
            // label/labelname/emailId - should have at least 3 parts
            if (labelParts.length >= 3) {
                const emailId = labelParts[labelParts.length - 1]; // Last part should be email ID
                return emailId.length > 10; // Email IDs are long
            }
            return false;
        }
        
        // For any other pattern, if it has a long string at the end, it's likely an individual email
        const lastPart = hashPart.split('/').pop();
        return lastPart && lastPart.length > 10 && !listViews.includes(lastPart);
    }
    
    // Enhanced global context monitoring system
    // Context validation now handled by centralized GMBContextManager
    function isExtensionContextValid(forceCheck = false) {
        return window.GMBContextManager ? window.GMBContextManager.isContextValid(forceCheck) : false;
    }
    
    // Simplified context invalidation handling
    function markContextInvalidated(reason) {
        if (debugMode) {
            console.log('Gmail Email Pinner: Context invalidated -', reason);
        }
        
        // IMMEDIATE operation cancellation - stop everything
        try {
            // Cancel all active async operations
            for (const operation of activeOperations) {
                try {
                    if (operation && typeof operation.cancel === 'function') {
                        operation.cancel();
                    }
                } catch (cancelError) {
                    // Ignore cancel errors
                }
            }
            activeOperations.clear();
            
            // Clear all intervals and timeouts
            if (typeof urlCheckInterval !== 'undefined') {
                clearInterval(urlCheckInterval);
            }
            
            // Disconnect observers
            if (window.gmailEmailPinnerObserver) {
                window.gmailEmailPinnerObserver.disconnect();
            }
        } catch (cleanupError) {
            // Ignore cleanup errors in invalidated context
        }
    }
    
    // Simplified tracked operation using centralized context manager
    function createTrackedOperation(name, asyncFunction, timeoutMs = 10000) {
        const operationId = `${name}_${Date.now()}_${Math.random()}`;
        let cancelled = false;
        
        const operation = {
            id: operationId,
            name: name,
            cancel: () => { cancelled = true; },
            isCancelled: () => cancelled
        };
        
        // Add to active operations
        activeOperations.add(operation);
        
        const wrappedFunction = async (...args) => {
            try {
                if (!isExtensionContextValid() || cancelled) {
                    throw new Error('Extension context invalidated or operation cancelled');
                }
                
                const result = await asyncFunction(...args);
                
                if (cancelled) {
                    throw new Error('Operation cancelled');
                }
                
                return result;
            } catch (error) {
                if (error.message.includes('Extension context invalidated')) {
                    markContextInvalidated(`Operation ${name} failed due to context invalidation`);
                }
                throw error;
            } finally {
                activeOperations.delete(operation);
            }
        };
        
        return wrappedFunction;
    }
    
    // Use centralized safe storage wrappers
    async function safeChromeStorageGet(keys) {
        return window.GMBContextManager ? await window.GMBContextManager.safeStorageGet(keys) : {};
    }
    
    async function safeChromeStorageSet(data) {
        return window.GMBContextManager ? await window.GMBContextManager.safeStorageSet(data) : false;
    }
    
    function safeChromeRuntimeSendMessage(message) {
        if (!isExtensionContextValid()) {
            if (debugMode) {
                console.log('Gmail Email Pinner: Cannot send message - context invalidated');
            }
            return Promise.resolve();
        }
        
        try {
            return chrome.runtime.sendMessage(message).catch((error) => {
                if (error.message && error.message.includes('Extension context invalidated')) {
                    markContextInvalidated('Runtime sendMessage failed');
                }
                return Promise.resolve();
            });
        } catch (error) {
            if (error.message && error.message.includes('Extension context invalidated')) {
                markContextInvalidated('Runtime sendMessage setup failed');
            }
            return Promise.resolve();
        }
    }
    
    // Load settings and pinned emails
    async function loadSettings() {
        if (!isExtensionContextValid()) {
            return; // Silent return when context invalidated
        }
        
        try {
            const result = await safeChromeStorageGet(['gmbExtractorSettings', 'gmailPinnedEmails']);
            const settings = result.gmbExtractorSettings || {};
            isEnabled = settings.emailPinnerEnabled !== false; // Default to true if not set
            debugMode = settings.debugMode || false;
            pinnedEmails = result.gmailPinnedEmails || [];
            
            if (debugMode) {
                console.log('Gmail Email Pinner: Settings loaded, enabled:', isEnabled);
                console.log('Gmail Email Pinner: Pinned emails loaded:', pinnedEmails.length);
            }
            
            if (isEnabled && isIndividualEmail()) {
                initializeFeature();
            } else {
                disableFeature();
            }
        } catch (error) {
            console.error('Gmail Email Pinner: Error loading settings:', error);
            // Default to enabled if error loading settings
            isEnabled = true;
            if (isIndividualEmail()) {
                initializeFeature();
            }
        }
    }
    
    // Initialize the feature
    function initializeFeature() {
        if (!isExtensionContextValid()) {
            return; // Silent return when context invalidated
        }
        
        if (!isGmailPage() || !isEnabled || !isIndividualEmail()) {
            return;
        }
        
        if (debugMode) {
            console.log('Gmail Email Pinner: Initializing feature for individual email');
        }
        
        // Check for jump requests in URL
        checkAndHandleJumpRequest();
        
        // Start monitoring for timestamp containers to add pin icons
        startPinIconMonitoring();
        
        // Apply pin icons to existing elements
        setTimeout(() => {
            addPinIcons();
        }, 1000);
    }

    
    // Check for jump request in URL and handle it
    function checkAndHandleJumpRequest() {
        try {
            const url = window.location.href;
            const urlParams = extractUrlParameters(url);
            
            // Handle jump to specific message
            if (urlParams.jumpToMessage) {
                if (debugMode) {
                    console.log('Gmail Email Pinner: Found jump request in URL:', {
                        timestampHash: urlParams.jumpToMessage,
                        url: url
                    });
                }
                
                // Delay jump to allow page elements to load
                setTimeout(() => {
                    const success = jumpToSpecificMessage(urlParams.jumpToMessage);
                    if (success) {
                        // Clean up URL after successful jump
                        cleanupJumpParameter();
                    }
                }, 1500);
            }
            
            // Handle scroll to jump links container (for "Open Email" actions)
            if (urlParams.scrollToJumpLinks === 'true') {
                if (debugMode) {
                    console.log('Gmail Email Pinner: Found scroll to jump links request in URL:', {
                        url: url
                    });
                }
                
                // Delay scroll to allow page elements and jump links container to load
                setTimeout(() => {
                    scrollToJumpLinksContainer();
                }, 2500); // Longer delay to wait for jump links container to be created
            }
        } catch (error) {
            if (debugMode) {
                console.error('Gmail Email Pinner: Error checking jump request:', error);
            }
        }
    }
    
    // Extract URL parameters from Gmail hash-based URLs
    function extractUrlParameters(url) {
        const parameters = {};
        
        try {
            // Gmail uses hash-based navigation, so check after the #
            const hashPart = url.split('#')[1];
            if (!hashPart) return parameters;
            
            // Look for query parameters in the hash part
            const queryIndex = hashPart.indexOf('?');
            if (queryIndex === -1) return parameters;
            
            const queryString = hashPart.substring(queryIndex + 1);
            const pairs = queryString.split('&');
            
            for (const pair of pairs) {
                const [key, value] = pair.split('=');
                if (key && value) {
                    parameters[decodeURIComponent(key)] = decodeURIComponent(value);
                }
            }
        } catch (error) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: Error parsing URL parameters:', error);
            }
        }
        
        return parameters;
    }
    
    // Remove jump parameter from URL after successful navigation
    function cleanupJumpParameter() {
        try {
            const url = window.location.href;
            const urlParts = url.split('#');
            const baseUrl = urlParts[0];
            let hashPart = urlParts[1] || '';
            
            // Remove jumpToMessage parameter
            if (hashPart.includes('jumpToMessage=')) {
                // Remove the parameter and any trailing &
                hashPart = hashPart.replace(/[?&]jumpToMessage=[^&]*&?/g, '');
                // Clean up trailing ? or &
                hashPart = hashPart.replace(/[?&]$/, '');
                
                // Update URL without causing page reload
                const cleanUrl = `${baseUrl}#${hashPart}`;
                if (history.replaceState) {
                    history.replaceState(null, null, cleanUrl);
                }
                
                if (debugMode) {
                    console.log('Gmail Email Pinner: Cleaned up jump parameter from URL');
                }
            }
        } catch (error) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: Error cleaning jump parameter:', error);
            }
        }
    }
    
    // Remove scroll parameter from URL after successful navigation
    function cleanupScrollParameter() {
        try {
            const url = window.location.href;
            const urlParts = url.split('#');
            const baseUrl = urlParts[0];
            let hashPart = urlParts[1] || '';
            
            // Remove scrollToJumpLinks parameter
            if (hashPart.includes('scrollToJumpLinks=')) {
                // Remove the parameter and any trailing &
                hashPart = hashPart.replace(/[?&]scrollToJumpLinks=[^&]*&?/g, '');
                // Clean up trailing ? or &
                hashPart = hashPart.replace(/[?&]$/, '');
                
                // Update URL without causing page reload
                const cleanUrl = `${baseUrl}#${hashPart}`;
                if (history.replaceState) {
                    history.replaceState(null, null, cleanUrl);
                }
                
                if (debugMode) {
                    console.log('Gmail Email Pinner: Cleaned up scroll parameter from URL');
                }
            }
        } catch (error) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: Error cleaning scroll parameter:', error);
            }
        }
    }
    
    // Disable the feature
    function disableFeature() {
        if (debugMode) {
            console.log('Gmail Email Pinner: Feature disabled');
        }
        
        // Remove existing pin icons
        removePinIcons();
        
        // Remove any mutation observers if they exist
        if (window.gmailEmailPinnerObserver) {
            window.gmailEmailPinnerObserver.disconnect();
            window.gmailEmailPinnerObserver = null;
        }
    }
    
    // Extract email ID from any Gmail URL pattern
    function extractEmailId(url) {
        const hashPart = url.split('#')[1];
        if (!hashPart) return null;
        
        // Get the last segment after any forward slash
        const lastSegment = hashPart.split('/').pop();
        
        // Email IDs are long alphanumeric strings
        return lastSegment && lastSegment.length > 10 ? lastSegment : null;
    }

    // Individual email container detection system for thread-aware pinning
    
    // Find all email containers within a Gmail thread
    function findEmailContainers() {
        // Gmail uses different container classes for different email states
        return document.querySelectorAll([
            '.ii.gt', // Standard email container
            '.ii.gt.m144e', // Expanded email container  
            '.adn.ads', // Collapsed email container
            '.kv', // Another email container type
        ].join(', '));
    }
    
    // Find the email container that contains a specific timestamp element
    function findEmailContainerByTimestamp(timestampElement) {
        if (!timestampElement) return null;
        
        // Traverse up the DOM to find the email container
        let current = timestampElement;
        while (current && current !== document.body) {
            // Check if current element is an email container
            if (current.matches && current.matches('.ii.gt, .ii.gt.m144e, .adn.ads, .kv')) {
                return current;
            }
            current = current.parentElement;
        }
        return null;
    }
    
    // Find the currently active/visible email container (most recently interacted with)
    function findCurrentEmailContainer() {
        const containers = findEmailContainers();
        
        // Try to find the expanded/active container first
        for (const container of containers) {
            // Check if container is expanded (has certain classes or attributes)
            if (container.classList.contains('m144e') || 
                container.querySelector('.g3') || // Has timestamp visible
                container.querySelector('.hP')) { // Has subject visible
                return container;
            }
        }
        
        // If no expanded container, return the last visible one
        return containers[containers.length - 1] || null;
    }
    
    // Extract individual email timestamp from a specific container
    function extractIndividualEmailTimestamp(emailContainer = null) {
        const container = emailContainer || findCurrentEmailContainer();
        if (!container) return null;
        
        // Look for timestamp element within this specific container
        const timestampElement = container.querySelector('.ig .g3[title], .hI .g3[title], .iv .g3[title]');
        return timestampElement ? timestampElement.getAttribute('title') : null;
    }
    
    // Extract individual email title from a specific container  
    function extractIndividualEmailTitle(emailContainer = null) {
        const container = emailContainer || findCurrentEmailContainer();
        
        // Try H2 within container first (scoped search)
        if (container) {
            const containerH2 = container.querySelector('h2');
            if (containerH2 && containerH2.textContent && containerH2.textContent.trim()) {
                const title = containerH2.textContent.trim();
                
                if (debugMode) {
                    console.log('Gmail Email Pinner: Found title in container H2:', {
                        title: title,
                        containerClass: container.className,
                        h2Element: containerH2
                    });
                }
                
                return title;
            }
        }
        
        // Fallback: Try page-level H2 (Gmail subject is sometimes outside container)
        const pageH2 = document.querySelector('h2[jsname="r4nke"]');
        if (pageH2 && pageH2.textContent && pageH2.textContent.trim()) {
            const title = pageH2.textContent.trim();
            
            if (debugMode) {
                console.log('Gmail Email Pinner: Found title in page-level H2:', {
                    title: title,
                    h2Element: pageH2
                });
            }
            
            return title;
        }
        
        // Last resort: Any H2 on the page
        const anyH2 = document.querySelector('h2');
        if (anyH2 && anyH2.textContent && anyH2.textContent.trim()) {
            const title = anyH2.textContent.trim();
            
            if (debugMode) {
                console.log('Gmail Email Pinner: Found title in any H2:', {
                    title: title,
                    h2Element: anyH2
                });
            }
            
            return title;
        }
        
        // Debug: Log failure details
        if (debugMode) {
            console.warn('Gmail Email Pinner: No H2 title found anywhere:', {
                hasContainer: !!container,
                containerH2Count: container ? container.querySelectorAll('h2').length : 0,
                pageH2Count: document.querySelectorAll('h2').length,
                pageH2WithJsname: !!document.querySelector('h2[jsname="r4nke"]')
            });
        }
        
        return 'Unknown Subject';
    }
    
    // Extract individual email sender from a specific container
    function extractIndividualEmailSender(emailContainer = null) {
        const container = emailContainer || findCurrentEmailContainer();
        if (!container) return 'Unknown Sender';
        
        // Look for sender element within this specific container
        let senderElement = container.querySelector('.gD[email]');
        if (senderElement && senderElement.getAttribute('email')) {
            return senderElement.getAttribute('email');
        }
        
        // Alternative selector for sender within container
        senderElement = container.querySelector('.go .g2, .yX .yW, .qu .yW');
        if (senderElement) {
            return senderElement.textContent.trim();
        }
        
        return 'Unknown Sender';
    }
    
    // Create a hash from timestamp for unique identification
    function createTimestampHash(timestamp) {
        if (!timestamp) return 'no_timestamp';
        
        // Create a simple hash from the timestamp string
        let hash = 0;
        for (let i = 0; i < timestamp.length; i++) {
            const char = timestamp.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16);
    }
    
    // Create composite unique ID for individual email within thread
    function createIndividualEmailId(emailId, nativeTimestamp) {
        if (!emailId || !nativeTimestamp) return emailId || 'unknown_email';
        
        const timestampHash = createTimestampHash(nativeTimestamp);
        return `${emailId}_${timestampHash}`;
    }
    
    // Parse Gmail URL to extract thread and message information
    function parseGmailUrl(url) {
        const hashPart = url.split('#')[1];
        if (!hashPart) return null;
        
        const segments = hashPart.split('/');
        return {
            fullHash: hashPart,
            segments: segments,
            lastSegment: segments[segments.length - 1],
            threadInfo: segments.join('_') // Use full path as thread identifier
        };
    }
    
    // Extract email data from current page
    function extractEmailData() {
        const url = window.location.href;
        const urlInfo = parseGmailUrl(url);
        const emailId = extractEmailId(url);
        
        // ONLY use individual email container detection - no fallbacks
        const currentContainer = findCurrentEmailContainer();
        if (!currentContainer) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: Individual email container not found - cannot pin');
            }
            return null; // Fail cleanly - no fallbacks
        }
        
        // Extract individual email data - all fields required
        const individualTitle = extractIndividualEmailTitle(currentContainer);
        const individualSender = extractIndividualEmailSender(currentContainer);
        const nativeTimestamp = extractIndividualEmailTimestamp(currentContainer);
        
        // Validate all required data exists
        if (!emailId || !individualTitle || !individualSender || !nativeTimestamp) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: Missing required individual email data:', {
                    emailId: !!emailId,
                    title: !!individualTitle, 
                    sender: !!individualSender,
                    nativeTimestamp: !!nativeTimestamp
                });
            }
            return null; // Fail cleanly - no fallbacks
        }
        
        // Create unique composite ID - REQUIRED
        const uniqueCompositeId = createIndividualEmailId(emailId, nativeTimestamp);
        if (!uniqueCompositeId) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: Failed to create composite ID');
            }
            return null; // Fail cleanly
        }
        
        // Create timestamp hash for jump link functionality
        const timestampHash = createTimestampHash(nativeTimestamp);
        
        // Store clean URL for "Open Email" functionality (no jump parameters)
        // This ensures "Open Email" navigates to thread top where jump links are located
        const cleanUrl = url;
        
        // Return ONLY new system data structure
        const emailData = {
            uniqueCompositeId: uniqueCompositeId,     // PRIMARY identifier
            nativeTimestamp: nativeTimestamp,         // Individual email timestamp
            title: individualTitle,                   // Individual email title
            sender: individualSender,                 // Individual email sender
            url: cleanUrl,                           // Clean URL (no jump parameters)
            threadInfo: urlInfo ? urlInfo.threadInfo : emailId, // Thread context
            dateAdded: Date.now(),                   // Pin creation time
            timestampHash: timestampHash             // Hash for jump link identification
        };
        
        if (debugMode) {
            console.log('Gmail Email Pinner: Successfully extracted individual email data:', {
                uniqueId: uniqueCompositeId,
                title: individualTitle,
                sender: individualSender,
                nativeTimestamp: nativeTimestamp,
                timestampHash: timestampHash,
                cleanUrl: cleanUrl
            });
        }
        
        return emailData;
    }

    // Create enhanced URL with jump parameter for message navigation
    function createJumpLinkUrl(originalUrl, timestampHash) {
        if (!originalUrl || !timestampHash) {
            return originalUrl;
        }
        
        try {
            // Parse the URL to add jump parameter
            const urlParts = originalUrl.split('#');
            const baseUrl = urlParts[0];
            const hashPart = urlParts[1] || '';
            
            // Check if hash part already has query parameters
            const hasQuery = hashPart.includes('?');
            const separator = hasQuery ? '&' : '?';
            
            // Add jump parameter to the hash part
            const enhancedHash = `${hashPart}${separator}jumpToMessage=${timestampHash}`;
            const enhancedUrl = `${baseUrl}#${enhancedHash}`;
            
            if (debugMode) {
                console.log('Gmail Email Pinner: Created jump link URL:', {
                    original: originalUrl,
                    enhanced: enhancedUrl,
                    timestampHash: timestampHash
                });
            }
            
            return enhancedUrl;
        } catch (error) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: Error creating jump link URL:', error);
            }
            return originalUrl; // Fallback to original URL
        }
    }

    
    // Jump to specific message within a thread based on timestamp hash
    function jumpToSpecificMessage(timestampHash) {
        if (!timestampHash) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: No timestamp hash provided for jump');
            }
            return false;
        }
        
        if (debugMode) {
            console.log('Gmail Email Pinner: Attempting to jump to message with hash:', timestampHash);
        }
        
        // Find all email containers in the thread
        const emailContainers = findEmailContainers();
        
        if (emailContainers.length === 0) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: No email containers found for jump');
            }
            return false;
        }
        
        // Find the container with matching timestamp hash
        let targetContainer = null;
        
        for (const container of emailContainers) {
            // Find timestamp element within this container
            const timestampElement = container.querySelector('.ig .g3[title], .hI .g3[title], .iv .g3[title]');
            if (timestampElement) {
                const nativeTimestamp = timestampElement.getAttribute('title');
                if (nativeTimestamp) {
                    const containerTimestampHash = createTimestampHash(nativeTimestamp);
                    
                    if (debugMode) {
                        console.log('Gmail Email Pinner: Checking container timestamp hash:', {
                            nativeTimestamp: nativeTimestamp,
                            hash: containerTimestampHash,
                            target: timestampHash
                        });
                    }
                    
                    if (containerTimestampHash === timestampHash) {
                        targetContainer = container;
                        break;
                    }
                }
            }
        }
        
        if (!targetContainer) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: Target message not found for timestamp hash:', timestampHash);
            }
            return false;
        }
        
        // Scroll to the target container with smooth animation
        targetContainer.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center',
            inline: 'nearest'
        });
        
        // Add visual highlight effect
        highlightMessage(targetContainer);
        
        if (debugMode) {
            console.log('Gmail Email Pinner: Successfully jumped to message with hash:', timestampHash);
        }
        
        return true;
    }
    
    // Add visual highlight to a message container
    function highlightMessage(container) {
        if (!container) return;
        
        // Add highlight class with transition
        const originalBackground = container.style.backgroundColor;
        const originalTransition = container.style.transition;
        
        // Set highlight style
        container.style.transition = 'background-color 0.3s ease';
        container.style.backgroundColor = 'rgba(124, 58, 237, 0.1)';
        
        // Remove highlight after delay
        setTimeout(() => {
            container.style.backgroundColor = originalBackground;
            
            // Remove transition after animation
            setTimeout(() => {
                container.style.transition = originalTransition;
            }, 300);
        }, 2000);
    }
    
    // Scroll to jump links container for "Open Email" navigation
    function scrollToJumpLinksContainer() {
        if (debugMode) {
            console.log('Gmail Email Pinner: Attempting to scroll to jump links container');
        }
        
        // Try to find the jump links container with retry logic
        const maxRetries = 10;
        const retryDelay = 500;
        let attempts = 0;
        
        const tryScroll = () => {
            const container = document.querySelector('.gmail-jump-links-container');
            
            if (container) {
                // Container found, scroll to it
                container.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'start',
                    inline: 'nearest'
                });
                
                // Add purple highlight effect to make it obvious
                highlightJumpLinksContainer(container);
                
                if (debugMode) {
                    console.log('Gmail Email Pinner: Successfully scrolled to jump links container');
                }
                
                // Clean up URL parameter after successful scroll
                cleanupScrollParameter();
                
                return true;
            } else {
                attempts++;
                if (attempts < maxRetries) {
                    if (debugMode) {
                        console.log(`Gmail Email Pinner: Jump links container not found, retry ${attempts}/${maxRetries}`);
                    }
                    setTimeout(tryScroll, retryDelay);
                } else {
                    if (debugMode) {
                        console.warn('Gmail Email Pinner: Jump links container not found after maximum retries');
                    }
                    // Clean up URL parameter even if scroll failed
                    cleanupScrollParameter();
                }
                return false;
            }
        };
        
        // Start the scroll attempt
        tryScroll();
    }
    
    // Add visual highlight to jump links container
    function highlightJumpLinksContainer(container) {
        if (!container) return;
        
        const originalBackground = container.style.backgroundColor;
        const originalTransition = container.style.transition;
        const originalBorder = container.style.border;
        
        // Set highlight style with purple theme
        container.style.transition = 'all 0.3s ease';
        container.style.backgroundColor = 'rgba(124, 58, 237, 0.15)';
        container.style.border = '2px solid rgba(124, 58, 237, 0.4)';
        
        // Remove highlight after delay
        setTimeout(() => {
            container.style.backgroundColor = originalBackground;
            container.style.border = originalBorder;
            
            // Remove transition after animation
            setTimeout(() => {
                container.style.transition = originalTransition;
            }, 300);
        }, 3000);
    }
    
    // Check if current email is already pinned
    function isCurrentEmailPinned() {
        // Extract individual email data
        const emailData = extractEmailData();
        
        // If individual email detection failed, treat as not pinned
        if (!emailData || !emailData.uniqueCompositeId) {
            if (debugMode) {
                console.log('Gmail Email Pinner: Cannot determine pin status - individual detection failed');
            }
            return false;
        }
        
        // ONLY check composite ID - no fallbacks
        const isPinned = pinnedEmails.some(email => 
            email.uniqueCompositeId === emailData.uniqueCompositeId
        );
        
        if (debugMode && isPinned) {
            console.log('Gmail Email Pinner: Individual email is pinned:', {
                uniqueId: emailData.uniqueCompositeId,
                title: emailData.title
            });
        }
        
        return isPinned;
    }
    
    // Internal implementation of toggleEmailPin
    async function _toggleEmailPinImpl() {
        const emailData = extractEmailData();
        
        // If individual email extraction failed, abort operation
        if (!emailData || !emailData.uniqueCompositeId) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: Cannot pin/unpin - individual email detection failed');
            }
            // Could show user notification here about detection failure
            return;
        }
        
        const uniqueCompositeId = emailData.uniqueCompositeId;
        
        if (isCurrentEmailPinned()) {
            // Unpin email - ONLY use composite ID
            pinnedEmails = pinnedEmails.filter(email => 
                email.uniqueCompositeId !== uniqueCompositeId
            );
            
            if (debugMode) {
                console.log('Gmail Email Pinner: Unpinned individual email:', {
                    title: emailData.title,
                    uniqueId: uniqueCompositeId,
                    nativeTimestamp: emailData.nativeTimestamp
                });
            }
        } else {
            // Pin email - store complete individual email data
            pinnedEmails.push(emailData);
            
            if (debugMode) {
                console.log('Gmail Email Pinner: Pinned individual email:', {
                    title: emailData.title,
                    uniqueId: uniqueCompositeId,
                    nativeTimestamp: emailData.nativeTimestamp,
                    sender: emailData.sender
                });
            }
        }
        
        // Save to storage (using safe wrapper)
        await safeChromeStorageSet({ gmailPinnedEmails: pinnedEmails });
        
        // Update pin icons
        updatePinIcons();
        
        // Update popup counter
        updatePopupCounter();
    }
    
    // Tracked version that prevents race conditions
    const toggleEmailPin = createTrackedOperation('toggleEmailPin', _toggleEmailPinImpl);
    
    // Create pin icon element
    function createPinIcon(timestampElement) {
        const pinIcon = document.createElement('div');
        pinIcon.className = 'gmail-pin-icon';
        pinIcon.title = 'Pin this email for quick access';
        pinIcon.style.cssText = `
            display: inline-block;
            margin-right: 8px;
            margin-top: 2px;
            cursor: pointer;
            padding: 3px;
            border-radius: 4px;
            transition: all 0.2s ease;
            vertical-align: middle;
            border: 1px solid transparent;
        `;
        
        // Add hover effect styles for unpinned (almost invisible) pins
        pinIcon.addEventListener('mouseenter', function() {
            const svg = this.querySelector('svg path');
            if (svg && svg.getAttribute('fill') === '#e0e0e0') {
                // Only apply hover effect to unpinned (almost invisible) pins
                svg.setAttribute('fill', '#7C3AED');
                this.style.border = '1px solid #7C3AED';
                this.style.background = 'rgba(124, 58, 237, 0.1)';
                this.style.opacity = '1';
            }
        });
        
        pinIcon.addEventListener('mouseleave', function() {
            const svg = this.querySelector('svg path');
            if (svg && svg.getAttribute('fill') === '#7C3AED') {
                // Get the associated timestamp to check if this is actually pinned
                const timestampElement = this.associatedTimestampElement;
                if (timestampElement && !isSpecificTimestampPinned(timestampElement)) {
                    // This is an unpinned pin, revert to almost invisible
                    svg.setAttribute('fill', '#e0e0e0');
                    this.style.border = '1px solid #f0f0f0';
                    this.style.background = 'transparent';
                    this.style.opacity = '0.7';
                }
            }
        });
        
        // CRITICAL: Store reference to the specific timestamp element
        pinIcon.associatedTimestampElement = timestampElement;
        
        // Get the unique timestamp for THIS email for debugging
        if (timestampElement && debugMode) {
            const nativeTimestamp = timestampElement.getAttribute('title');
            const emailId = extractEmailId(window.location.href);
            const uniqueId = createIndividualEmailId(emailId, nativeTimestamp);
            pinIcon.dataset.uniqueId = uniqueId;
            
            if (debugMode) {
                console.log('Gmail Email Pinner: Created pin icon for timestamp:', {
                    nativeTimestamp: nativeTimestamp,
                    uniqueId: uniqueId
                });
            }
        }
        
        // Create SVG pin icon with almost invisible appearance for unpinned state
        pinIcon.innerHTML = `
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 12V4H17V2H7V4H8V12L6 14V16H11V22H13V16H18V14L16 12Z" fill="#e0e0e0"/>
            </svg>
        `;
        
        // Set initial styling for unpinned state (almost invisible with very faint grey outline)
        pinIcon.style.border = '1px solid #f0f0f0';
        pinIcon.style.background = 'transparent';
        
        // Click handler for THIS specific timestamp
        pinIcon.addEventListener('click', (e) => {
            e.stopPropagation();
            toggleSpecificEmailPin(timestampElement);
        });
        
        return pinIcon;
    }

    // Toggle pin for specific email based on timestamp element
    async function toggleSpecificEmailPin(timestampElement) {
        if (!timestampElement) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: No timestamp element provided for toggle');
            }
            return;
        }
        
        // Extract data for this specific timestamp element
        const emailData = extractDataFromTimestamp(timestampElement);
        if (!emailData || !emailData.uniqueCompositeId) {
            if (debugMode) {
                console.warn('Gmail Email Pinner: Failed to extract data from timestamp element');
            }
            return;
        }
        
        // Check if THIS specific email is pinned
        const isPinned = pinnedEmails.some(email => 
            email.uniqueCompositeId === emailData.uniqueCompositeId
        );
        
        if (isPinned) {
            // Unpin this specific email
            pinnedEmails = pinnedEmails.filter(email => 
                email.uniqueCompositeId !== emailData.uniqueCompositeId
            );
            
            if (debugMode) {
                console.log('Gmail Email Pinner: Unpinned specific email:', {
                    title: emailData.title,
                    uniqueId: emailData.uniqueCompositeId,
                    nativeTimestamp: emailData.nativeTimestamp
                });
            }
        } else {
            // Pin this specific email
            pinnedEmails.push(emailData);
            
            if (debugMode) {
                console.log('Gmail Email Pinner: Pinned specific email:', {
                    title: emailData.title,
                    uniqueId: emailData.uniqueCompositeId,
                    nativeTimestamp: emailData.nativeTimestamp,
                    sender: emailData.sender
                });
            }
        }
        
        // Save and update
        await safeChromeStorageSet({ gmailPinnedEmails: pinnedEmails });
        updatePinIcons(); // Will now show individual status per pin
        updatePopupCounter();
    }
    
    // Extract email data from specific timestamp element
    function extractDataFromTimestamp(timestampElement) {
        if (!timestampElement) return null;
        
        const nativeTimestamp = timestampElement.getAttribute('title');
        if (!nativeTimestamp) return null;
        
        const emailId = extractEmailId(window.location.href);
        if (!emailId) return null;
        
        // Find the email container that contains this timestamp
        let container = timestampElement;
        while (container && !container.matches('.ii.gt, .ii.gt.m144e, .adn.ads, .kv')) {
            container = container.parentElement;
        }
        
        // Extract data from this specific container
        const title = container ? extractIndividualEmailTitle(container) : 'Unknown Subject';
        const sender = container ? extractIndividualEmailSender(container) : 'Unknown Sender';
        const uniqueCompositeId = createIndividualEmailId(emailId, nativeTimestamp);
        
        return {
            uniqueCompositeId: uniqueCompositeId,
            nativeTimestamp: nativeTimestamp,
            title: title,
            sender: sender,
            url: window.location.href,
            threadInfo: emailId,
            dateAdded: Date.now()
        };
    }
    
    // Check if specific timestamp is pinned
    function isSpecificTimestampPinned(timestampElement) {
        if (!timestampElement) return false;
        
        const nativeTimestamp = timestampElement.getAttribute('title');
        if (!nativeTimestamp) return false;
        
        const emailId = extractEmailId(window.location.href);
        const uniqueCompositeId = createIndividualEmailId(emailId, nativeTimestamp);
        
        return pinnedEmails.some(email => 
            email.uniqueCompositeId === uniqueCompositeId
        );
    }
    
    // Add pin icons to timestamp containers
    function addPinIcons() {
        if (!isExtensionContextValid()) {
            return; // Silent return when context invalidated
        }
        
        if (!isEnabled || !isIndividualEmail()) return;
        
        // Target timestamp containers (same as time formatter mechanism)
        const timestampContainers = document.querySelectorAll('.ig, .hI, .iv');
        
        timestampContainers.forEach((container) => {
            // Check if pin icon already exists in this container
            if (container.querySelector('.gmail-pin-icon')) {
                return;
            }
            
            // Find the specific .g3[title] element (same as time formatter mechanism)
            const timestampElement = container.querySelector('.g3[title]');
            if (timestampElement) {
                // Create pin icon associated with THIS specific timestamp
                const pinIcon = createPinIcon(timestampElement);
                
                // Insert pin icon before the timestamp element
                timestampElement.parentNode.insertBefore(pinIcon, timestampElement);
                
                if (debugMode) {
                    const timestamp = timestampElement.getAttribute('title');
                    console.log('Gmail Email Pinner: Added pin icon for timestamp:', timestamp);
                }
            }
        });
        
        // Update pin icon states individually (each checks its own timestamp)
        updatePinIcons();
    }
    
    // Update pin icon appearance based on pin status
    function updatePinIcons() {
        // Quick context check before DOM manipulation
        if (!isExtensionContextValid()) {
            return; // Silent return when context invalidated
        }
        
        const pinIcons = document.querySelectorAll('.gmail-pin-icon');
        
        pinIcons.forEach(icon => {
            // Get the associated timestamp element for THIS specific pin icon
            const timestampElement = icon.associatedTimestampElement;
            if (!timestampElement) {
                if (debugMode) {
                    console.warn('Gmail Email Pinner: Pin icon missing associated timestamp element');
                }
                return;
            }
            
            // Check if THIS specific email (by timestamp) is pinned
            const isPinned = isSpecificTimestampPinned(timestampElement);
            const svg = icon.querySelector('svg path');
            
            if (svg) {
                if (isPinned) {
                    svg.setAttribute('fill', '#7C3AED'); // Purple for THIS pinned email
                    icon.title = 'Unpin this email';
                    icon.style.background = 'rgba(124, 58, 237, 0.1)';
                    icon.style.border = '1px solid #7C3AED';
                } else {
                    svg.setAttribute('fill', '#e0e0e0'); // Almost invisible grey for THIS unpinned email
                    icon.title = 'Pin this email for quick access';
                    icon.style.background = 'transparent';
                    icon.style.border = '1px solid #f0f0f0';
                    icon.style.opacity = '0.7';
                }
                
                if (debugMode) {
                    const nativeTimestamp = timestampElement.getAttribute('title');
                    console.log('Gmail Email Pinner: Updated pin icon status:', {
                        nativeTimestamp: nativeTimestamp,
                        isPinned: isPinned,
                        color: isPinned ? 'purple' : 'almost invisible'
                    });
                }
            }
        });
    }
    
    // Internal implementation of checkRedPinSync (now checks grey pin sync)
    async function _checkRedPinSyncImpl() {
        if (!isEnabled || !isIndividualEmail()) return;
        
        const pinIcons = document.querySelectorAll('.gmail-pin-icon svg path');
        
        // Only check if we have purple pins (now indicating pinned emails)
        const hasPurplePins = Array.from(pinIcons).some(svg => svg.getAttribute('fill') === '#7C3AED');
        if (!hasPurplePins) return;
        
        // Extract current individual email data
        const currentEmailData = extractEmailData();
        
        // If individual detection failed, treat as unpinned (switch to subtle grey)
        if (!currentEmailData || !currentEmailData.uniqueCompositeId) {
            if (debugMode) {
                console.log('Gmail Email Pinner: Individual detection failed, switching pin to subtle grey');
            }
            updatePinIcons();
            return;
        }
        
        // Check if this specific individual email exists in storage
        const result = await safeChromeStorageGet('gmailPinnedEmails');
        const storedPinnedEmails = result.gmailPinnedEmails || [];
        
        // ONLY check composite ID - no fallbacks
        const emailExists = storedPinnedEmails.some(email => 
            email.uniqueCompositeId === currentEmailData.uniqueCompositeId
        );
        
        // If pin is purple but this specific email not in storage, switch to subtle grey
        if (!emailExists) {
            if (debugMode) {
                console.log('Gmail Email Pinner: Individual email was unpinned from popup, switching to subtle grey:', {
                    uniqueId: currentEmailData.uniqueCompositeId,
                    title: currentEmailData.title,
                    nativeTimestamp: currentEmailData.nativeTimestamp
                });
            }
            
            // Update local pinned emails array to match storage
            pinnedEmails = storedPinnedEmails;
            
            // Update pin icons to subtle grey (unpinned)
            updatePinIcons();
            
            // Update popup counter
            updatePopupCounter();
        }
    }
    
    // Tracked version that prevents race conditions
    const checkRedPinSync = createTrackedOperation('checkRedPinSync', _checkRedPinSyncImpl);
    
    // Remove all pin icons
    function removePinIcons() {
        const pinIcons = document.querySelectorAll('.gmail-pin-icon');
        pinIcons.forEach(icon => icon.remove());
    }
    
    // Update popup counter (if popup is open)
    function updatePopupCounter() {
        // Send message to popup to update counter
        if (!isExtensionContextValid()) {
            return; // Silent return when context invalidated
        }
        
        safeChromeRuntimeSendMessage({
            action: 'updateEmailPinnerCounter',
            count: pinnedEmails.length
        });
    }
    
    // Start monitoring for new timestamp containers
    function startPinIconMonitoring() {
        const observer = new MutationObserver((mutations) => {
            if (!isEnabled || !isIndividualEmail()) return;
            
            let shouldAddPinIcons = false;
            
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if this node or its children contain timestamp elements
                            if (node.querySelector && node.querySelector('.g3')) {
                                shouldAddPinIcons = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldAddPinIcons) {
                setTimeout(() => {
                    addPinIcons();
                }, 300);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Store observer globally for cleanup
        window.gmailEmailPinnerObserver = observer;
    }
    
    // Listen for URL changes (Gmail SPA navigation) and check grey pin sync
    let currentUrl = window.location.href;
    let urlCheckInterval = setInterval(() => {
        try {
            // Check if extension context is still valid
            if (!isExtensionContextValid()) {
                clearInterval(urlCheckInterval);
                return;
            }
            
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                
                if (debugMode) {
                    console.log('Gmail Email Pinner: URL changed, checking if individual email');
                }
                
                // Remove existing pin icons
                removePinIcons();
                
                // Reinitialize if we're now in an individual email
                if (isEnabled && isIndividualEmail()) {
                    setTimeout(() => {
                        try {
                            initializeFeature();
                        } catch (initError) {
                            if (debugMode) {
                                console.error('Gmail Email Pinner: Error in initializeFeature:', initError);
                            }
                        }
                    }, 500);
                }
            } else if (isEnabled && isIndividualEmail()) {
                // Check if grey pins should be switched to purple (unpinned from popup)
                // Double-check context before calling tracked operation
                if (isExtensionContextValid(true, true)) {
                    // Using tracked operation that automatically handles context invalidation
                    // TEMPORARILY DISABLED to stop console spam - will implement timestamp-specific checking
                    // checkRedPinSync().catch((error) => { ... });
                }
            }
        } catch (intervalError) {
            if (debugMode) {
                console.error('Gmail Email Pinner: Error in URL check interval:', intervalError);
            }
            // If there's a critical error, clear the interval to prevent spam
            if (intervalError.message && intervalError.message.includes('Extension context invalidated')) {
                markContextInvalidated('Interval error');
                clearInterval(urlCheckInterval);
            }
        }
    }, 1000);
    
    // Listen for settings changes
    if (isExtensionContextValid()) {
        chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
            try {
                // Check context validity for each message
                if (!isExtensionContextValid()) {
                    sendResponse({ success: false, error: 'Extension context invalidated' });
                    return;
                }
                
                if (request.action === 'updateSettings' && request.settings) {
                    try {
                        const newEnabled = request.settings.emailPinnerEnabled !== false;
                        const newDebugMode = request.settings.debugMode || false;
                        
                        if (newEnabled !== isEnabled) {
                            isEnabled = newEnabled;
                            debugMode = newDebugMode;
                            
                            if (isEnabled && isIndividualEmail()) {
                                initializeFeature();
                            } else {
                                disableFeature();
                            }
                        } else if (newDebugMode !== debugMode) {
                            debugMode = newDebugMode;
                        }
                        
                        sendResponse({ success: true });
                    } catch (settingsError) {
                        if (debugMode) {
                            console.error('Gmail Email Pinner: Error updating settings:', settingsError);
                        }
                        sendResponse({ success: false, error: settingsError.message });
                    }
                }
                
                // Handle requests for pinned emails from popup
                if (request.action === 'getPinnedEmails') {
                    sendResponse({ pinnedEmails: pinnedEmails });
                }
                
                // Handle unpin request from popup
                if (request.action === 'unpinEmail' && request.emailId) {
                    try {
                        // ONLY support composite ID system - no fallbacks
                        pinnedEmails = pinnedEmails.filter(email => 
                            email.uniqueCompositeId !== request.emailId
                        );
                        await safeChromeStorageSet({ gmailPinnedEmails: pinnedEmails });
                        updatePinIcons(); // Update current page if it's the unpinned email
                        sendResponse({ success: true });
                    } catch (error) {
                        if (error.message && error.message.includes('Extension context invalidated')) {
                            markContextInvalidated('Unpin request storage failed');
                        }
                        if (debugMode) {
                            console.error('Gmail Email Pinner: Error updating storage in unpin request:', error);
                        }
                        sendResponse({ success: false, error: error.message });
                    }
                }
            } catch (listenerError) {
                if (listenerError.message && listenerError.message.includes('Extension context invalidated')) {
                    markContextInvalidated('Message listener error');
                }
                if (debugMode) {
                    console.error('Gmail Email Pinner: Error in message listener:', listenerError);
                }
                sendResponse({ success: false, error: listenerError.message });
            }
        });
    }
    
    // Initialize when DOM is ready with error handling
    try {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                try {
                    loadSettings();
                } catch (initError) {
                    console.error('Gmail Email Pinner: Error during DOM loaded initialization:', initError);
                }
            });
        } else {
            loadSettings().catch((error) => {
                console.error('Gmail Email Pinner: Error during immediate initialization:', error);
            });
        }
    } catch (setupError) {
        console.error('Gmail Email Pinner: Error setting up initialization:', setupError);
    }
})();