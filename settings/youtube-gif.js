// YouTube WebP Viewer - Simple WebP viewing on hover (like thumbnail viewer)
(function() {
    'use strict';
    
    let isEnabled = true; // Default to true
    let debugMode = false;
    
    // YouTube-specific elements
    let webpButton = null;
    let isWebpButtonAppended = false;
    let webpObserver = null;
    let currentWebpPopup = null;
    let hoverTimeout = null;
    
    // Check if we're on a YouTube page
    function isYouTubePage() {
        return window.location.hostname.includes('youtube.com');
    }
    
    // Load settings from localStorage (MAIN world doesn't have Chrome API access)
    function loadSettings() {
        try {
            const storedEnabled = localStorage.getItem('youtubeGifEnabled');
            const storedDebug = localStorage.getItem('debugMode');
            
            // Explicit boolean checks - only default to true if setting doesn't exist
            isEnabled = storedEnabled === null ? true : storedEnabled === 'true';
            debugMode = storedDebug === 'true';
            
            if (debugMode) {
                console.log('[YouTube WebP Viewer] Settings loaded:', {
                    enabled: isEnabled
                });
            }
            
            return isEnabled;
        } catch (error) {
            console.error('[YouTube WebP Viewer] Error loading settings:', error);
            return true; // Default to enabled on error
        }
    }
    
    // === HOVER-BASED WebP DETECTION ===
    
    function setupHoverDetection() {
        if (!isEnabled) return;
        
        // Add hover listeners to video thumbnails (like thumbnail viewer approach)
        const videoThumbnails = document.querySelectorAll('ytd-rich-item-renderer, ytd-grid-video-renderer, ytd-video-renderer');
        
        videoThumbnails.forEach(thumbnail => {
            // Skip if already has listener
            if (thumbnail.hasWebpListener) return;
            
            thumbnail.addEventListener('mouseenter', () => {
                if (hoverTimeout) clearTimeout(hoverTimeout);
                
                hoverTimeout = setTimeout(() => {
                    // Only process WebP detection if hover sustained
                    detectWebPOnHover(thumbnail);
                }, 300); // Small delay to avoid spam
            });
            
            thumbnail.addEventListener('mouseleave', () => {
                if (hoverTimeout) {
                    clearTimeout(hoverTimeout);
                    hoverTimeout = null;
                }
            });
            
            thumbnail.hasWebpListener = true;
        });
    }
    
    function detectWebPOnHover(thumbnailElement) {
        if (!isEnabled) return;
        
        try {
            // Look for WebP URL in hover overlay (dynamically generated)
            const overlay = thumbnailElement.querySelector('#mouseover-overlay');
            if (overlay) {
                const webpImg = overlay.querySelector('img.style-scope.ytd-moving-thumbnail-renderer, img#thumbnail');
                if (webpImg && webpImg.src && webpImg.src.includes('i.ytimg.com/an_webp/')) {
                    // Store WebP URL for this thumbnail
                    thumbnailElement.setAttribute('data-webp-url', webpImg.src);
                    
                    // Add WebP badge if not already present
                    addWebpBadge(thumbnailElement);
                    
                    if (debugMode) {
                        console.log('[YouTube WebP Viewer] WebP detected:', webpImg.src);
                    }
                }
            }
        } catch (error) {
            if (debugMode) {
                console.error('[YouTube WebP Viewer] Error detecting WebP:', error);
            }
        }
    }
    
    function addWebpBadge(thumbnailElement) {
        // Check if badge already exists
        if (thumbnailElement.querySelector('.youtube-webp-badge')) return;
        
        // Find the thumbnail container
        const thumbnail = thumbnailElement.querySelector('#thumbnail');
        if (!thumbnail) return;
        
        // Create GIF badge
        const badge = document.createElement('div');
        badge.className = 'youtube-webp-badge';
        badge.textContent = 'GIF';
        badge.style.cssText = `
            position: absolute;
            bottom: 5px;
            left: 5px;
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            color: #ffffff;
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12px;
            z-index: 999;
            border: 1px solid #8b5cf6;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
            cursor: pointer;
        `;
        badge.title = 'View animated GIF';
        
        // Make sure thumbnail has position relative
        if (window.getComputedStyle(thumbnail).position !== 'relative') {
            thumbnail.style.position = 'relative';
        }
        
        // Add click handler
        badge.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            showWebpViewer(thumbnailElement);
        });
        
        thumbnail.appendChild(badge);
    }
    
    // === WebP VIEWER POPUP (EXACTLY LIKE THUMBNAIL VIEWER) ===
    
    function showWebpViewer(thumbnailElement) {
        // Close existing popup if any
        closeWebpPopup();
        
        // Get WebP URL and video title
        const webpUrl = thumbnailElement.getAttribute('data-webp-url');
        if (!webpUrl) {
            showWebpError('No WebP URL found. Please hover over the thumbnail to detect it first.');
            return;
        }
        
        let title = 'YouTube Video';
        const titleElement = thumbnailElement.querySelector('#video-title, .ytd-video-meta-block [aria-label]');
        if (titleElement) {
            title = titleElement.textContent.trim();
        }
        
        // Create and show popup (exact pattern from thumbnail viewer)
        createWebpPopup({ webpUrl, title });
    }
    
    function createWebpPopup(webpData) {
        // Create popup container with universal design standards (exactly like thumbnail viewer)
        const popup = document.createElement('div');
        popup.className = 'youtube-webp-viewer-popup';
        popup.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #0a0a0a;
            border: 2px solid #7C3AED;
            border-radius: 12px;
            padding: 0;
            width: 700px;
            max-height: 90vh;
            z-index: 9999999;
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            color: #d1d5db;
            resize: both;
            overflow: hidden;
            min-width: 500px;
            min-height: 400px;
        `;
        
        // Create header with dark theme (exactly like thumbnail viewer)
        const header = document.createElement('div');
        header.style.cssText = `
            background: #1a1a1a;
            color: white;
            padding: 12px 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
            user-select: none;
            font-size: 16px;
            font-weight: 700;
        `;
        
        const titleText = document.createElement('span');
        titleText.textContent = 'YouTube WebP Viewer'; // NO EMOJIS
        
        // Close button
        const closeButton = document.createElement('button');
        closeButton.textContent = '✕';
        closeButton.style.cssText = `
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        `;
        
        closeButton.onclick = closeWebpPopup;
        
        header.appendChild(titleText);
        header.appendChild(closeButton);
        
        // Create content area (exactly like thumbnail viewer)
        const content = document.createElement('div');
        content.style.cssText = `
            padding: 20px 20px 30px 20px;
            max-height: calc(90vh - 60px);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        `;
        
        // Video title
        const videoTitle = document.createElement('div');
        videoTitle.style.cssText = `
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
            word-wrap: break-word;
            text-align: center;
        `;
        videoTitle.textContent = webpData.title;
        
        // Primary WebP display (exactly like thumbnail viewer)
        const webpContainer = document.createElement('div');
        webpContainer.style.cssText = `
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 15px;
        `;
        
        const webpImg = document.createElement('img');
        webpImg.src = webpData.webpUrl;
        webpImg.style.cssText = `
            max-width: 100%;
            max-height: 300px;
            border: 1px solid #374151;
            border-radius: 8px;
            cursor: pointer;
            transition: opacity 0.2s ease;
        `;
        
        webpImg.onclick = () => window.open(webpData.webpUrl, '_blank');
        
        // Instructional text for saving
        const instructionText = document.createElement('div');
        instructionText.textContent = 'Right click this image to save or simply drag it to your desktop...';
        instructionText.style.cssText = `
            font-size: 14px;
            color: #9ca3af;
            text-align: center;
            font-style: italic;
            margin-top: 10px;
        `;
        
        // Action buttons (exactly like thumbnail viewer)
        const actionButtons = document.createElement('div');
        actionButtons.style.cssText = `
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 15px;
        `;
        
        // Copy URL button
        const copyButton = document.createElement('button');
        copyButton.textContent = 'Copy URL';
        copyButton.style.cssText = `
            background: #059669;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: background-color 0.2s ease;
        `;
        
        copyButton.onclick = () => {
            navigator.clipboard.writeText(webpData.webpUrl).then(() => {
                const originalText = copyButton.textContent;
                copyButton.textContent = 'Copied!';
                setTimeout(() => {
                    copyButton.textContent = originalText;
                }, 1500);
            });
        };
        
        
        actionButtons.appendChild(copyButton);
        
        // Assemble popup (exactly like thumbnail viewer)
        webpContainer.appendChild(webpImg);
        webpContainer.appendChild(instructionText);
        webpContainer.appendChild(actionButtons);
        
        content.appendChild(videoTitle);
        content.appendChild(webpContainer);
        
        popup.appendChild(header);
        popup.appendChild(content);
        
        // Make draggable (exactly like thumbnail viewer)
        makeWebpPopupDraggable(popup, header);
        
        // ESC key handler (exactly like thumbnail viewer)
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                closeWebpPopup();
            }
        };
        document.addEventListener('keydown', handleEscape);
        popup._escapeHandler = handleEscape;
        
        // Add to page
        document.body.appendChild(popup);
        currentWebpPopup = popup;
        
        // Handle image load error
        webpImg.onerror = () => {
            webpImg.style.display = 'none';
            const errorMsg = document.createElement('div');
            errorMsg.textContent = 'Unable to load WebP image';
            errorMsg.style.cssText = `
                color: #ef4444;
                text-align: center;
                padding: 20px;
                font-style: italic;
            `;
            webpContainer.insertBefore(errorMsg, webpImg);
        };
    }
    
    function makeWebpPopupDraggable(element, handle) {
        let isDragging = false;
        let dragStartX = 0;
        let dragStartY = 0;
        let initialTransform = element.style.transform;
        const DRAG_THRESHOLD = 5; // pixels
        
        handle.onmousedown = dragMouseDown;
        
        function dragMouseDown(e) {
            e = e || window.event;
            
            // Store initial mouse position
            dragStartX = e.clientX;
            dragStartY = e.clientY;
            isDragging = false;
            
            // Store initial transform for restoration
            initialTransform = element.style.transform;
            
            // Set up event listeners
            document.onmouseup = closeDragElement;
            document.onmousemove = checkDragThreshold;
        }
        
        function checkDragThreshold(e) {
            e = e || window.event;
            
            // Calculate distance moved
            const deltaX = Math.abs(e.clientX - dragStartX);
            const deltaY = Math.abs(e.clientY - dragStartY);
            
            // Only start dragging if moved beyond threshold
            if (!isDragging && (deltaX > DRAG_THRESHOLD || deltaY > DRAG_THRESHOLD)) {
                isDragging = true;
                
                // Convert to absolute positioning for dragging
                const rect = element.getBoundingClientRect();
                element.style.top = rect.top + 'px';
                element.style.left = rect.left + 'px';
                element.style.transform = 'none';
                
                // Switch to actual drag handler
                document.onmousemove = elementDrag;
                
                // Prevent text selection during drag
                document.body.style.userSelect = 'none';
            }
        }
        
        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            
            if (!isDragging) return;
            
            // Calculate new position
            const deltaX = e.clientX - dragStartX;
            const deltaY = e.clientY - dragStartY;
            
            const rect = element.getBoundingClientRect();
            const newTop = rect.top + deltaY;
            const newLeft = rect.left + deltaX;
            
            // Keep within viewport bounds
            const maxTop = window.innerHeight - element.offsetHeight;
            const maxLeft = window.innerWidth - element.offsetWidth;
            
            element.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
            element.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
            
            // Update drag start position for next move
            dragStartX = e.clientX;
            dragStartY = e.clientY;
        }
        
        function closeDragElement() {
            // Clean up event listeners
            document.onmouseup = null;
            document.onmousemove = null;
            
            // Restore text selection
            document.body.style.userSelect = '';
            
            // Reset drag state
            isDragging = false;
        }
    }
    
    function closeWebpPopup() {
        if (currentWebpPopup) {
            // Cleanup event listener
            if (currentWebpPopup._escapeHandler) {
                document.removeEventListener('keydown', currentWebpPopup._escapeHandler);
            }
            
            currentWebpPopup.remove();
            currentWebpPopup = null;
        }
    }
    
    function showWebpError(message) {
        // Simple error notification (like thumbnail viewer)
        const errorDiv = document.createElement('div');
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ef4444;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10000000;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            errorDiv.remove();
        }, 3000);
    }
    
    // === MUTATION OBSERVER MANAGEMENT ===
    
    function setupWebpObserver() {
        if (webpObserver) return;
        
        webpObserver = new MutationObserver((mutations) => {
            let shouldCheck = false;
            for (let mutation of mutations) {
                if (mutation.type === 'childList') {
                    shouldCheck = true;
                    break;
                }
            }
            
            if (shouldCheck && isEnabled && isYouTubePage()) {
                // Setup hover detection for new videos (throttled)
                setTimeout(() => {
                    setupHoverDetection();
                }, 100);
            }
        });
        
        webpObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    function removeWebpObserver() {
        if (webpObserver) {
            webpObserver.disconnect();
            webpObserver = null;
        }
    }
    
    // === CLEANUP ===
    
    function removeWebpBadges() {
        try {
            // Remove all WebP badges
            const existingBadges = document.querySelectorAll('.youtube-webp-badge');
            existingBadges.forEach(badge => badge.remove());
            
            // Clear hover listeners
            const thumbnails = document.querySelectorAll('[hasWebpListener]');
            thumbnails.forEach(thumbnail => {
                thumbnail.hasWebpListener = false;
            });
            
            if (debugMode) {
                console.log('[YouTube WebP Viewer] All WebP badges removed');
            }
        } catch (error) {
            console.error('[YouTube WebP Viewer] Error removing WebP badges:', error);
        }
    }
    
    // === INITIALIZATION & CLEANUP ===
    
    function initializeYouTubeWebpViewer() {
        if (!isYouTubePage()) return;
        
        // Setup hover detection immediately
        setupHoverDetection();
        setupWebpObserver();
        
        if (debugMode) {
            console.log('[YouTube WebP Viewer] Functionality initialized');
        }
    }
    
    function disableYouTubeWebpViewer() {
        // Remove all badges and observers
        removeWebpBadges();
        removeWebpObserver();
        closeWebpPopup();
        
        if (debugMode) {
            console.log('[YouTube WebP Viewer] Functionality disabled and cleaned up');
        }
    }
    
    // === SETTINGS CHANGE LISTENER ===
    
    // Settings change listener via localStorage events
    window.addEventListener('storage', (e) => {
        if (e.key === 'youtubeGifEnabled' || e.key === 'debugMode') {
            const wasEnabled = isEnabled;
            loadSettings();
            
            if (debugMode) {
                console.log('[YouTube WebP Viewer] Settings updated:', { enabled: isEnabled });
            }
            
            // Handle enable/disable changes
            if (wasEnabled !== isEnabled) {
                if (isEnabled) {
                    initializeYouTubeWebpViewer();
                } else {
                    disableYouTubeWebpViewer();
                }
            }
        }
    });
    
    // === MAIN INITIALIZATION ===
    
    function initialize() {
        if (!isYouTubePage()) {
            console.log('[YouTube WebP Viewer] Not a YouTube page, exiting');
            return;
        }
        
        const shouldRun = loadSettings();
        
        if (shouldRun) {
            console.log('[YouTube WebP Viewer] Starting feature');
            initializeYouTubeWebpViewer();
        } else {
            console.log('[YouTube WebP Viewer] Feature disabled in settings');
        }
    }
    
    // Start when DOM is ready with a small delay to ensure settings are loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(initialize, 100); // Small delay to ensure settings bridge runs first
        });
    } else {
        setTimeout(initialize, 100); // Small delay to ensure settings bridge runs first
    }
    
    // Handle page navigation on YouTube (SPA)
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            if (debugMode) {
                console.log('[YouTube WebP Viewer] Page navigation detected');
            }
            
            // Re-initialize on navigation
            if (isEnabled) {
                setTimeout(() => {
                    if (isYouTubePage()) {
                        initializeYouTubeWebpViewer();
                    } else {
                        disableYouTubeWebpViewer();
                    }
                }, 500);
            }
        }
    }).observe(document, { subtree: true, childList: true });
})();