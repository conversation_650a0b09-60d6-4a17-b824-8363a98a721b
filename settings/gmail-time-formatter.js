// Gmail Time Formatter - Enhanced time display for Gmail
(function() {
    'use strict';
    
    // Prevent multiple loads
    if (window.STMGmailTimeFormatterLoaded) return;
    window.STMGmailTimeFormatterLoaded = true;
    
    let isEnabled = true; // Default to ON
    let debugMode = false;
    let customFormat = '%m-%d %H:%M'; // Default format: MM-DD HH:MM
    let cache = {};
    let stopCallingMe = false;
    
    // Date helpers
    Date.prototype.getWeek = function() {
        const date = new Date(this.getTime());
        date.setHours(0, 0, 0, 0);
        date.setDate(date.getDate() + 3 - (date.getDay() + 6) % 7);
        const week1 = new Date(date.getFullYear(), 0, 4);
        return 1 + Math.round(((date.getTime() - week1.getTime()) / 864e5 - 3 + (week1.getDay() + 6) % 7) / 7);
    };
    
    function getMonthIndexForString(str) {
        const date = new Date();
        for (let i = 0; i < 12; i++) {
            date.setMonth(i);
            if (str.indexOf(date.toLocaleString(document.documentElement.lang, { month: "long" })) !== -1) {
                return i;
            }
        }
        for (let i = 0; i < 12; i++) {
            date.setMonth(i);
            if (str.indexOf(date.toLocaleString(document.documentElement.lang, { month: "short" })) !== -1) {
                return i;
            }
        }
        return null;
    }
    
    function pad(str) {
        str = str + "";
        return str.length === 1 ? "0" + str : str;
    }
    
    // Check if we're on Gmail
    function isGmailPage() {
        return window.location.hostname === 'mail.google.com';
    }
    
    // Load settings
    async function loadSettings() {
        if (!isGmailPage()) return;
        
        try {
            const result = await chrome.storage.local.get('gmbExtractorSettings');
            const settings = result.gmbExtractorSettings || {};
            
            isEnabled = settings.showGmailTimeEnabled !== false; // Default to true
            debugMode = settings.debugMode || false;
            customFormat = settings.gmailTimeFormat || '%a - %d %b, %H:%M';
            
            if (debugMode) {
                console.log('STM Gmail Time Formatter: Settings loaded', {
                    enabled: isEnabled,
                    format: customFormat,
                    debugMode: debugMode
                });
            }
            
            if (isEnabled) {
                initializeMutationObservers();
            }
        } catch (error) {
            console.error('STM Gmail Time Formatter: Error loading settings:', error);
            // Use defaults if error
            initializeMutationObservers();
        }
    }
    
    function getDateObjectForString(str) {
        if (stopCallingMe || !customFormat || !str) return str;
        
        if (!cache.hasOwnProperty(str)) {
            let y = null, mo = null, d = null, h = null, nd = null;
            
            // Extract year
            const yearMatch = /\d{4}/.exec(str);
            y = yearMatch ? parseInt(yearMatch[0], 10) : null;
            
            // Extract month
            mo = getMonthIndexForString(str);
            
            // Extract day
            const dayMatch = /(\d{1,2})\.?,? /.exec(str);
            d = dayMatch ? parseInt(dayMatch[1], 10) : null;
            
            // Extract time
            const timeMatch = / (\d{1,2})(\.|:)(\d{1,2})/.exec(str);
            h = timeMatch ? parseInt(timeMatch[1], 10) : null;
            nd = timeMatch ? parseInt(timeMatch[3], 10) : null;
            
            // Handle AM/PM
            if (/PM/.exec(str) && h < 12) {
                h += 12;
            }
            if (/AM/.exec(str) && h === 12) {
                h = 0;
            }
            
            // Handle Japanese format
            if (document.documentElement.lang === "ja") {
                const jaMonthMatch = str.match(/(\d{1,2})月/);
                const jaDayMatch = str.match(/(\d{1,2})日/);
                if (jaMonthMatch) mo = jaMonthMatch[1] - 1;
                if (jaDayMatch) d = jaDayMatch[1];
            }
            
            if (y === null || mo === null || d === null || h === null || nd === null) {
                stopCallingMe = true;
                return str;
            }
            
            const dateObj = new Date(y, mo, d, h, nd);
            
            // Format the date using custom format
            const formatted = customFormat
                .replace(/%Y/g, dateObj.getFullYear())
                .replace(/%y/g, dateObj.getFullYear().toString().substr(2, 2))
                .replace(/%m/g, pad(dateObj.getMonth() + 1))
                .replace(/%-m/g, dateObj.getMonth() + 1)
                .replace(/%b/g, dateObj.toLocaleString(document.documentElement.lang, { month: "short" }))
                .replace(/%B/g, dateObj.toLocaleString(document.documentElement.lang, { month: "long" }))
                .replace(/%U/g, dateObj.getWeek())
                .replace(/%d/g, pad(dateObj.getDate()))
                .replace(/%-d/g, dateObj.getDate())
                .replace(/%a/g, dateObj.toLocaleString(document.documentElement.lang, { weekday: "short" }))
                .replace(/%A/g, dateObj.toLocaleString(document.documentElement.lang, { weekday: "long" }))
                .replace(/%H/g, pad(dateObj.getHours()))
                .replace(/%-H/g, dateObj.getHours())
                .replace(/%I/g, pad((dateObj.getHours() + 11) % 12 + 1))
                .replace(/%-I/g, (dateObj.getHours() + 11) % 12 + 1)
                .replace(/%M/g, pad(dateObj.getMinutes()))
                .replace(/%p/g, dateObj.getHours() > 11 ? "PM" : "AM");
            
            cache[str] = formatted;
        }
        
        return cache[str];
    }
    
    function getDateString(str) {
        const newVal = getDateObjectForString(str);
        return newVal !== null ? newVal : str;
    }
    
    function parentsFirstWidth(element) {
        if (element.nodeName === "BODY") return null;
        const w = parseInt(element.style.width, 10);
        return isNaN(w) ? parentsFirstWidth(element.parentNode) : w;
    }
    
    function renderListItem(summaries) {
        if (!isEnabled) return;
        
        let stringWidth = 0;
        const collection = document.getElementsByClassName("xW");
        
        for (let a = 0; a < collection.length; a++) {
            if (parentsFirstWidth(collection[a].parentNode) && parentsFirstWidth(collection[a].parentNode) < 500) {
                // Skip narrow containers
            } else {
                collection[a].style.maxWidth = "none";
            }
            
            const spans = collection[a].getElementsByTagName("span");
            for (let b = 0; b < spans.length; b++) {
                const title = spans[b].getAttribute("title");
                if (title) {
                    const datestring = getDateString(title);
                    spans[b].textContent = datestring;
                    stringWidth = Math.max(stringWidth, spans[b].offsetWidth);
                }
            }
        }
        
        if (stringWidth !== 0) {
            const xXCollection = document.getElementsByClassName("xX");
            for (let a = 0; a < xXCollection.length; a++) {
                xXCollection[a].style.width = stringWidth + 28 + "px";
            }
        }
    }
    
    function renderListItemCol1(summaries) {
        if (!isEnabled) return;
        
        let stringWidth = 0;
        const collection = document.getElementsByClassName("yf");
        
        for (let a = 0; a < collection.length; a++) {
            const spans = collection[a].getElementsByTagName("span");
            for (let b = 0; b < spans.length; b++) {
                const title = spans[b].getAttribute("title");
                if (title) {
                    const datestring = getDateString(title);
                    spans[b].textContent = datestring;
                    stringWidth = Math.max(stringWidth, spans[b].offsetWidth);
                }
            }
        }
        
        if (stringWidth !== 0) {
            const xXCollection = document.getElementsByClassName("xX");
            for (let a = 0; a < xXCollection.length; a++) {
                xXCollection[a].style.width = stringWidth + 28 + "px";
            }
        }
    }
    
    function renderDetailItem(summaries) {
        if (!isEnabled) return;
        
        const spans = document.getElementsByClassName("g3");
        for (let b = 0; b < spans.length; b++) {
            const title = spans[b].getAttribute("title");
            if (title) {
                const datestring = getDateString(title);
                spans[b].textContent = datestring;
            }
        }
    }
    
    // Lightweight mutation observer for Gmail
    function initializeMutationObservers() {
        if (!isGmailPage() || !isEnabled) return;
        
        // Use our own simple mutation observer approach since we already have MutationSummary elsewhere
        const observer = new MutationObserver((mutations) => {
            let shouldProcess = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if any added nodes contain time elements
                    for (let node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.classList && (node.classList.contains('xW') || node.classList.contains('yf') || node.classList.contains('g3'))) {
                                shouldProcess = true;
                                break;
                            }
                            // Also check descendants
                            if (node.querySelector && (node.querySelector('.xW') || node.querySelector('.yf') || node.querySelector('.g3'))) {
                                shouldProcess = true;
                                break;
                            }
                        }
                    }
                }
            });
            
            if (shouldProcess) {
                // Debounce the processing
                clearTimeout(window.stmGmailTimeDebounce);
                window.stmGmailTimeDebounce = setTimeout(() => {
                    renderListItem();
                    renderListItemCol1();
                    renderDetailItem();
                }, 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Initial processing
        setTimeout(() => {
            renderListItem();
            renderListItemCol1();
            renderDetailItem();
        }, 1000);
        
        if (debugMode) {
            console.log('STM Gmail Time Formatter: Mutation observers initialized');
        }
    }
    
    // Create settings panel with formatting help
    function createSettingsPanel() {
        return new Promise((resolve) => {
            // Check if panel already exists
            const existingPanel = document.querySelector('.stm-gmail-time-settings-panel');
            if (existingPanel) {
                existingPanel.remove();
            }
            
            // Create panel HTML
            const panel = document.createElement('div');
            panel.className = 'stm-gmail-time-settings-panel';
            panel.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #0a0a0a;
                border: 2px solid #7C3AED;
                border-radius: 12px;
                padding: 24px;
                z-index: 9999999;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                box-shadow: 0 8px 32px rgba(0,0,0,0.6);
                max-width: 520px;
                width: 90%;
                color: #d1d5db;
                max-height: 80vh;
                overflow-y: auto;
            `;
            
            panel.innerHTML = `
                <div style="color: #7C3AED; font-size: 18px; font-weight: 700; margin-bottom: 20px; text-align: center;">
                    📅 Gmail Time Format Settings
                </div>
                
                <!-- Current Format Display -->
                <div style="background: #111111; border-radius: 8px; padding: 16px; margin-bottom: 20px; border: 1px solid #2a2a2a;">
                    <div style="font-size: 14px; line-height: 1.5; color: #9ca3af; margin-bottom: 8px;">
                        Current format: <span style="color: #7C3AED; font-family: monospace; font-weight: 600;">${customFormat}</span>
                    </div>
                    <div style="font-size: 12px; color: #6b7280;">
                        Preview: <span id="formatPreview" style="color: #d1d5db; font-weight: 500;"></span>
                    </div>
                </div>
                
                <!-- Format Input -->
                <div style="margin-bottom: 20px;">
                    <label style="display: block; font-weight: 600; margin-bottom: 8px; color: #d1d5db;">
                        Custom Format:
                    </label>
                    <input type="text" id="gmailTimeFormatInput" value="${customFormat}" style="
                        width: 100%;
                        padding: 10px 12px;
                        background: #1a1a1a;
                        border: 1px solid #374151;
                        border-radius: 6px;
                        color: #d1d5db;
                        font-size: 14px;
                        font-family: monospace;
                        box-sizing: border-box;
                    ">
                </div>
                
                <!-- Quick Presets -->
                <div style="margin-bottom: 20px;">
                    <div style="font-weight: 600; margin-bottom: 12px; color: #d1d5db;">
                        Quick Presets:
                    </div>
                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                        <button class="preset-btn" data-format="%a - %d %b, %H:%M" style="
                            background: #7C3AED; border: 1px solid #7C3AED; color: white; padding: 6px 12px; 
                            border-radius: 4px; font-size: 12px; cursor: pointer; font-family: monospace; font-weight: 600;
                        ">Mon - 31 Dec, 23:59</button>
                        <button class="preset-btn" data-format="%m-%d %H:%M" style="
                            background: #374151; border: 1px solid #4b5563; color: #d1d5db; padding: 6px 12px; 
                            border-radius: 4px; font-size: 12px; cursor: pointer; font-family: monospace;
                        ">12-31 23:59</button>
                        <button class="preset-btn" data-format="%Y-%m-%d %H:%M" style="
                            background: #374151; border: 1px solid #4b5563; color: #d1d5db; padding: 6px 12px; 
                            border-radius: 4px; font-size: 12px; cursor: pointer; font-family: monospace;
                        ">2024-12-31 23:59</button>
                        <button class="preset-btn" data-format="%m/%d/%y %I:%M %p" style="
                            background: #374151; border: 1px solid #4b5563; color: #d1d5db; padding: 6px 12px; 
                            border-radius: 4px; font-size: 12px; cursor: pointer; font-family: monospace;
                        ">12/31/24 11:59 PM</button>
                        <button class="preset-btn" data-format="%b %d, %H:%M" style="
                            background: #374151; border: 1px solid #4b5563; color: #d1d5db; padding: 6px 12px; 
                            border-radius: 4px; font-size: 12px; cursor: pointer; font-family: monospace;
                        ">Dec 31, 23:59</button>
                    </div>
                </div>
                
                <!-- Format Help -->
                <div style="background: #111111; border-radius: 8px; padding: 16px; margin-bottom: 20px; border: 1px solid #2a2a2a;">
                    <div style="font-weight: 600; margin-bottom: 12px; color: #d1d5db; font-size: 14px;">
                        Format Specifiers:
                    </div>
                    <div style="font-size: 12px; line-height: 1.6; color: #9ca3af; font-family: monospace;">
                        <div style="margin-bottom: 4px;"><span style="color: #7C3AED;">%Y</span> - Year (2024) <span style="margin-left: 20px; color: #7C3AED;">%y</span> - Year (24)</div>
                        <div style="margin-bottom: 4px;"><span style="color: #7C3AED;">%m</span> - Month (01-12) <span style="margin-left: 20px; color: #7C3AED;">%-m</span> - Month (1-12)</div>
                        <div style="margin-bottom: 4px;"><span style="color: #7C3AED;">%b</span> - Month short (Dec) <span style="margin-left: 20px; color: #7C3AED;">%B</span> - Month full (December)</div>
                        <div style="margin-bottom: 4px;"><span style="color: #7C3AED;">%d</span> - Day (01-31) <span style="margin-left: 20px; color: #7C3AED;">%-d</span> - Day (1-31)</div>
                        <div style="margin-bottom: 4px;"><span style="color: #7C3AED;">%H</span> - Hour 24h (00-23) <span style="margin-left: 20px; color: #7C3AED;">%I</span> - Hour 12h (01-12)</div>
                        <div style="margin-bottom: 4px;"><span style="color: #7C3AED;">%M</span> - Minutes (00-59) <span style="margin-left: 20px; color: #7C3AED;">%p</span> - AM/PM</div>
                        <div style="margin-bottom: 4px;"><span style="color: #7C3AED;">%a</span> - Weekday short (Wed) <span style="margin-left: 20px; color: #7C3AED;">%A</span> - Weekday full (Wednesday)</div>
                    </div>
                </div>
                
                <div style="display: flex; gap: 12px; justify-content: flex-end;">
                    <button id="gmail-time-cancel" style="
                        background: transparent;
                        border: 1px solid #6b7280;
                        color: #d1d5db;
                        padding: 8px 16px;
                        border-radius: 6px;
                        font-size: 14px;
                        cursor: pointer;
                    ">Cancel</button>
                    <button id="gmail-time-save" style="
                        background: #7C3AED;
                        border: 1px solid #7C3AED;
                        color: white;
                        padding: 8px 16px;
                        border-radius: 6px;
                        font-size: 14px;
                        cursor: pointer;
                        font-weight: 600;
                    ">Save Format</button>
                </div>
            `;
            
            document.body.appendChild(panel);
            
            // Update preview in real-time
            const input = document.getElementById('gmailTimeFormatInput');
            const preview = document.getElementById('formatPreview');
            
            function updatePreview() {
                const testDate = new Date();
                const format = input.value;
                
                const formatted = format
                    .replace(/%Y/g, testDate.getFullYear())
                    .replace(/%y/g, testDate.getFullYear().toString().substr(2, 2))
                    .replace(/%m/g, pad(testDate.getMonth() + 1))
                    .replace(/%-m/g, testDate.getMonth() + 1)
                    .replace(/%b/g, testDate.toLocaleString(document.documentElement.lang, { month: "short" }))
                    .replace(/%B/g, testDate.toLocaleString(document.documentElement.lang, { month: "long" }))
                    .replace(/%d/g, pad(testDate.getDate()))
                    .replace(/%-d/g, testDate.getDate())
                    .replace(/%a/g, testDate.toLocaleString(document.documentElement.lang, { weekday: "short" }))
                    .replace(/%A/g, testDate.toLocaleString(document.documentElement.lang, { weekday: "long" }))
                    .replace(/%H/g, pad(testDate.getHours()))
                    .replace(/%-H/g, testDate.getHours())
                    .replace(/%I/g, pad((testDate.getHours() + 11) % 12 + 1))
                    .replace(/%-I/g, (testDate.getHours() + 11) % 12 + 1)
                    .replace(/%M/g, pad(testDate.getMinutes()))
                    .replace(/%p/g, testDate.getHours() > 11 ? "PM" : "AM");
                
                preview.textContent = formatted;
            }
            
            input.addEventListener('input', updatePreview);
            updatePreview();
            
            // Preset buttons
            document.querySelectorAll('.preset-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    input.value = btn.dataset.format;
                    updatePreview();
                });
            });
            
            // Save button
            document.getElementById('gmail-time-save').addEventListener('click', () => {
                const newFormat = input.value.trim();
                if (!newFormat) {
                    alert('Please enter a valid format');
                    return;
                }
                
                // Save to storage
                chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
                    const settings = result.gmbExtractorSettings || {};
                    settings.gmailTimeFormat = newFormat;
                    
                    chrome.storage.local.set({ gmbExtractorSettings: settings }, () => {
                        customFormat = newFormat;
                        cache = {}; // Clear cache
                        stopCallingMe = false;
                        panel.remove();
                        showNotification('Gmail time format saved!', 'success');
                        
                        // Re-process current page
                        setTimeout(() => {
                            renderListItem();
                            renderListItemCol1();
                            renderDetailItem();
                        }, 100);
                        
                        resolve(newFormat);
                    });
                });
            });
            
            // Cancel button
            document.getElementById('gmail-time-cancel').addEventListener('click', () => {
                panel.remove();
                resolve(null);
            });
            
            // ESC key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    panel.remove();
                    document.removeEventListener('keydown', handleEscape);
                    resolve(null);
                }
            };
            document.addEventListener('keydown', handleEscape);
        });
    }
    
    // Show notification
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(10, 10, 10, 0.95);
            border: 1px solid #7C3AED;
            color: #d1d5db;
            padding: 10px 16px;
            border-radius: 8px;
            z-index: 10000000;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 4px 20px rgba(0,0,0,0.4);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateX(20px);
            transition: all 0.3s ease-out;
            max-width: 300px;
            pointer-events: none;
        `;
        
        const icon = type === 'success' ? '📅' : type === 'error' ? '⚠️' : 'ℹ️';
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 14px;">${icon}</span>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        requestAnimationFrame(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        });
        
        // Animate out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(20px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2500);
    }
    
    // Listen for settings changes
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'updateSettings' && request.settings) {
                const newEnabled = request.settings.showGmailTimeEnabled !== false;
                const newFormat = request.settings.gmailTimeFormat || '%m-%d %H:%M';
                
                if (newEnabled !== isEnabled || newFormat !== customFormat) {
                    isEnabled = newEnabled;
                    customFormat = newFormat;
                    cache = {}; // Clear cache
                    stopCallingMe = false;
                    
                    if (isEnabled) {
                        loadSettings();
                    }
                }
                
                sendResponse({ success: true });
            }
        });
        
        // Listen for messages from content script to check if active
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (!request || (typeof request === 'object' && Object.keys(request).length === 0) || (request && !request.action)) {
                // Response for tab activation check
                if (isEnabled) {
                    sendResponse({ message: "colorize" });
                }
            }
        });
    }
    
    // Make functions available globally for settings integration
    window.stmGmailTimeFormatter = {
        createSettingsPanel,
        isEnabled: () => isEnabled,
        getFormat: () => customFormat
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadSettings);
    } else {
        loadSettings();
    }
    
})(); 