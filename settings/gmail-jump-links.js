// Gmail Jump Links - Navigate quickly between PINNED emails in threads
(function() {
    'use strict';
    
    // Prevent multiple loads
    if (window.GMBGmailJumpLinksLoaded) return;
    window.GMBGmailJumpLinksLoaded = true;
    
    let isEnabled = true; // Default to ON
    let debugMode = false;
    let reverseOrderEnabled = false; // Track reverse Gmail order status
    let processedThreads = new Set(); // Track already processed threads
    let jumpLinksContainer = null;
    let currentThreadId = null;
    let pinnedEmails = []; // Cache of pinned emails from storage
    
    // Check if we're on Gmail
    function isGmailPage() {
        return window.location.hostname === 'mail.google.com';
    }
    
    // Context validation using centralized manager
    function isExtensionContextValid() {
        return window.GMBContextManager ? window.GMBContextManager.isContextValid() : false;
    }
    
    // Check if we're in an individual email thread (not a list view)
    function isIndividualThread() {
        const url = window.location.href;
        
        // Must be Gmail
        if (!url.includes('mail.google.com/mail/u/')) {
            return false;
        }
        
        // Extract the fragment (part after #)
        const hashPart = url.split('#')[1];
        if (!hashPart) {
            return false;
        }
        
        // List of multi-email list views to exclude
        const listViews = [
            'inbox',       // inbox list
            'starred',     // starred emails list
            'snoozed',     // snoozed emails list
            'drafts',      // drafts list
            'sent',        // sent emails list
            'spam',        // spam folder
            'trash',       // trash folder
            'all',         // all mail (when showing list)
            'important',   // important emails list
            'chats'        // chat conversations list
        ];
        
        // Check if it's a plain list view (exclude these)
        if (listViews.includes(hashPart)) {
            return false;
        }
        
        // Check if it starts with a list view but has email ID (individual thread)
        for (const listView of listViews) {
            if (hashPart.startsWith(listView + '/') || hashPart.startsWith(listView + '?')) {
                const afterListView = hashPart.split(listView + '/')[1] || hashPart.split(listView + '?')[1];
                if (afterListView && afterListView.length > 10) {
                    return true; // This is an individual thread
                }
                return false; // This is still a list view
            }
        }
        
        // For search URLs like #search/query/emailId
        if (hashPart.startsWith('search/')) {
            const searchParts = hashPart.split('/');
            if (searchParts.length >= 3) {
                const emailId = searchParts[searchParts.length - 1];
                return emailId.length > 10;
            }
            return false;
        }
        
        // For label URLs like #label/labelname/emailId
        if (hashPart.startsWith('label/')) {
            const labelParts = hashPart.split('/');
            if (labelParts.length >= 3) {
                const emailId = labelParts[labelParts.length - 1];
                return emailId.length > 10;
            }
            return false;
        }
        
        // For any other pattern, if it has a long string at the end, it's likely an individual thread
        const lastPart = hashPart.split('/').pop();
        return lastPart && lastPart.length > 10 && !listViews.includes(lastPart);
    }
    
    // Use centralized context manager for safe storage access
    async function safeChromeStorageGet(keys) {
        // Use centralized context manager if available, otherwise fallback to silent handling
        if (window.GMBContextManager) {
            return await window.GMBContextManager.safeStorageGet(keys);
        }
        
        // Fallback for cases where context manager isn't loaded yet
        try {
            if (!chrome.storage || !chrome.storage.local) {
                return {};
            }
            return await chrome.storage.local.get(keys);
        } catch (error) {
            // Silent fallback - no console errors for context invalidation
            return {};
        }
    }
    
    // Detect if reverse Gmail order CSS is currently active
    function detectReverseGmailOrder() {
        try {
            // Check if any div[role=list] has flex-direction: column-reverse
            const listElements = document.querySelectorAll('div[role=list]:not(.brd)');
            for (const element of listElements) {
                const computedStyle = window.getComputedStyle(element);
                if (computedStyle.flexDirection === 'column-reverse') {
                    return true;
                }
            }
            return false;
        } catch (error) {
            if (debugMode) {
                console.error('Gmail Jump Links: Error detecting reverse order:', error);
            }
            return false;
        }
    }
    
    // Load settings and pinned emails on initialization
    async function loadSettings() {
        try {
            const result = await safeChromeStorageGet(['gmbExtractorSettings', 'gmailPinnedEmails']);
            const settings = result.gmbExtractorSettings || {};
            isEnabled = settings.gmailJumpLinksEnabled !== false; // Default to true if not set
            debugMode = settings.debugMode || false;
            pinnedEmails = result.gmailPinnedEmails || [];
            
            // Detect reverse order from actual DOM/CSS state
            reverseOrderEnabled = detectReverseGmailOrder();
            
            if (debugMode) {
                console.log('Gmail Jump Links: Settings loaded, enabled:', isEnabled);
                console.log('Gmail Jump Links: Reverse order detected:', reverseOrderEnabled);
                console.log('Gmail Jump Links: Pinned emails loaded:', pinnedEmails.length);
            }
            
            if (isEnabled) {
                initializeFeature();
            } else {
                disableFeature();
            }
        } catch (error) {
            console.error('Gmail Jump Links: Error loading settings:', error);
            // Default to enabled if error loading settings
            isEnabled = true;
            initializeFeature();
        }
    }
    
    // Initialize the feature with proper timing coordination
    function initializeFeature() {
        if (!isGmailPage() || !isEnabled) {
            return;
        }
        
        if (debugMode) {
            console.log('Gmail Jump Links: Initializing feature');
        }
        
        // Start monitoring for thread changes
        startThreadMonitoring();
        
        // Wait longer for initial load - coordinate with Thread Expander
        setTimeout(() => {
            if (debugMode) {
                console.log('Gmail Jump Links: Initial check after page load');
            }
            checkAndInjectJumpLinksForPinnedEmails();
        }, 3000); // Increased delay to wait for Thread Expander
    }
    
    // Disable the feature
    function disableFeature() {
        if (debugMode) {
            console.log('Gmail Jump Links: Feature disabled');
        }
        
        // Remove existing jump links
        removeJumpLinks();
        
        // Clear processed threads set
        processedThreads.clear();
        
        // Remove any mutation observers if they exist
        if (window.gmailJumpLinksObserver) {
            window.gmailJumpLinksObserver.disconnect();
            window.gmailJumpLinksObserver = null;
        }
    }
    
    // Start monitoring for thread changes with better coordination
    function startThreadMonitoring() {
        const observer = new MutationObserver((mutations) => {
            if (!isEnabled) return;
            
            let shouldCheckThreads = false;
            
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if this node contains thread content
                            if (node.querySelector && (
                                node.querySelector('.ii.gt') || // Email containers
                                node.querySelector('h2[jsname="r4nke"]') || // Thread title
                                node.classList.contains('ii') ||
                                node.classList.contains('gt')
                            )) {
                                shouldCheckThreads = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldCheckThreads) {
                // Longer debounce to wait for Thread Expander to complete
                setTimeout(() => {
                    if (debugMode) {
                        console.log('Gmail Jump Links: Thread content detected, checking after delay');
                    }
                    checkAndInjectJumpLinksForPinnedEmails();
                }, 2000); // Increased delay for Thread Expander coordination
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Store observer globally for cleanup
        window.gmailJumpLinksObserver = observer;
        
        // Also monitor URL changes for Gmail SPA navigation
        let currentUrl = window.location.href;
        setInterval(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                if (debugMode) {
                    console.log('Gmail Jump Links: URL changed');
                }
                // Remove existing jump links and reset
                removeJumpLinks();
                processedThreads.clear();
                currentThreadId = null;
                
                // Check new page with proper delay
                setTimeout(() => {
                    if (debugMode) {
                        console.log('Gmail Jump Links: Checking new URL after navigation');
                    }
                    checkAndInjectJumpLinksForPinnedEmails();
                }, 3000); // Longer delay for navigation
            }
        }, 1000);
    }
    
    // NEW: Main function to check and inject jump links ONLY for pinned emails
    async function checkAndInjectJumpLinksForPinnedEmails() {
        if (!isEnabled || !isIndividualThread()) {
            return;
        }
        
        const threadId = extractThreadId();
        if (!threadId) {
            return;
        }
        
        // Skip if we've already processed this thread
        if (currentThreadId === threadId && jumpLinksContainer && document.contains(jumpLinksContainer)) {
            return;
        }
        
        if (debugMode) {
            console.log('Gmail Jump Links: Processing thread for pinned emails:', threadId);
        }
        
        // Refresh reverse order detection (it might change dynamically)
        reverseOrderEnabled = detectReverseGmailOrder();
        
        if (debugMode) {
            console.log('Gmail Jump Links: Reverse order currently:', reverseOrderEnabled);
        }
        
        // Wait for Thread Expander to complete if it's enabled
        await waitForThreadExpansion();
        
        // Refresh pinned emails from storage
        await refreshPinnedEmails();
        
        if (pinnedEmails.length === 0) {
            if (debugMode) {
                console.log('Gmail Jump Links: No pinned emails found in storage');
            }
            return;
        }
        
        // Find the thread title (H2) 
        const threadTitle = findThreadTitle();
        if (!threadTitle) {
            if (debugMode) {
                console.log('Gmail Jump Links: Thread title not found');
            }
            return;
        }
        
        // Find the main email container for jump links insertion
        let mainContainer = findMainEmailContainer();
        
        // Enhanced error handling with retry mechanism
        if (!mainContainer) {
            if (debugMode) {
                console.log('Gmail Jump Links: Initial container detection failed, attempting retry...');
            }
            
            // Wait a bit for page to fully load and try once more
            await new Promise(resolve => setTimeout(resolve, 1000));
            mainContainer = findMainEmailContainer();
            
            if (!mainContainer) {
                if (debugMode) {
                    console.log('Gmail Jump Links: Container detection failed after retry - Gmail layout may have changed');
                    console.log('Gmail Jump Links: Please check if Gmail has updated its DOM structure');
                }
                
                // Silent failure - feature gracefully disabled for this thread
                return;
            } else if (debugMode) {
                console.log('Gmail Jump Links: Container found after retry - continuing normally');
            }
        }
        
        // Find pinned emails in current thread
        const pinnedEmailsInThread = await findPinnedEmailsInCurrentThread();
        
        if (pinnedEmailsInThread.length === 0) {
            if (debugMode) {
                console.log('Gmail Jump Links: No pinned emails found in current thread');
            }
            // Remove existing jump links if any
            removeJumpLinks();
            
            
            // Track this thread
            currentThreadId = threadId;
            processedThreads.add(threadId);
            return;
        }
        
        if (debugMode) {
            console.log('Gmail Jump Links: Found', pinnedEmailsInThread.length, 'pinned emails in thread');
        }
        
        // Remove existing jump links
        removeJumpLinks();
        
        // Create jump links ONLY for pinned emails
        createJumpLinksForPinnedEmails(threadTitle, pinnedEmailsInThread, mainContainer);
        
        // Track this thread
        currentThreadId = threadId;
        processedThreads.add(threadId);
    }
    
    // NEW: Wait for Thread Expander to complete expansion
    async function waitForThreadExpansion() {
        return new Promise((resolve) => {
            // Check if Thread Expander is enabled
            const threadExpanderEnabled = typeof window.GMBGmailThreadExpanderLoaded !== 'undefined';
            
            if (threadExpanderEnabled) {
                if (debugMode) {
                    console.log('Gmail Jump Links: Thread Expander detected, waiting for completion');
                }
                // Wait longer for Thread Expander to complete
                setTimeout(resolve, 4000);
            } else {
                if (debugMode) {
                    console.log('Gmail Jump Links: Thread Expander not detected, shorter wait');
                }
                // Shorter wait if Thread Expander is not enabled
                setTimeout(resolve, 1500);
            }
        });
    }
    
    // NEW: Refresh pinned emails from storage
    async function refreshPinnedEmails() {
        try {
            const result = await safeChromeStorageGet('gmailPinnedEmails');
            pinnedEmails = result.gmailPinnedEmails || [];
            if (debugMode) {
                console.log('Gmail Jump Links: Refreshed pinned emails count:', pinnedEmails.length);
            }
        } catch (error) {
            if (debugMode) {
                console.error('Gmail Jump Links: Error refreshing pinned emails:', error);
            }
            pinnedEmails = [];
        }
    }
    
    // NEW: Find pinned emails that exist in the current thread
    async function findPinnedEmailsInCurrentThread() {
        const currentEmailId = extractEmailId(window.location.href);
        if (!currentEmailId) {
            if (debugMode) {
                console.log('Gmail Jump Links: Could not extract email ID from URL');
            }
            return [];
        }
        
        // Find all email containers in current thread
        const emailContainers = document.querySelectorAll([
            '.ii.gt', // Standard email container
            '.ii.gt.m144e', // Expanded email container  
            '.adn.ads', // Collapsed email container
            '.kv', // Another email container type
        ].join(', '));
        
        if (!emailContainers.length) {
            if (debugMode) {
                console.log('Gmail Jump Links: No email containers found in thread');
            }
            return [];
        }
        
        const pinnedEmailsInThread = [];
        
        // Check each email container to see if it matches a pinned email
        emailContainers.forEach((container) => {
            const timestampElement = container.querySelector('.ig .g3[title], .hI .g3[title], .iv .g3[title]');
            if (timestampElement) {
                const nativeTimestamp = timestampElement.getAttribute('title');
                if (nativeTimestamp) {
                    // Create unique composite ID using same logic as Email Pinner
                    const uniqueCompositeId = createIndividualEmailId(currentEmailId, nativeTimestamp);
                    
                    // Check if this email is in our pinned emails list
                    const pinnedEmail = pinnedEmails.find(email => 
                        email.uniqueCompositeId === uniqueCompositeId
                    );
                    
                    if (pinnedEmail) {
                        // This email is pinned! Add to our list
                        const time = formatTimestamp(nativeTimestamp);
                        pinnedEmailsInThread.push({
                            container: container,
                            nativeTimestamp: nativeTimestamp,
                            displayTime: time.time,
                            displayDate: time.date,
                            sender: extractSenderFromContainer(container),
                            uniqueCompositeId: uniqueCompositeId,
                            pinnedEmailData: pinnedEmail
                        });
                    }
                }
            }
        });
        
        // Sort by timestamp (earliest to latest)
        pinnedEmailsInThread.sort((a, b) => {
            const dateA = new Date(a.nativeTimestamp);
            const dateB = new Date(b.nativeTimestamp);
            return dateA - dateB;
        });
        
        return pinnedEmailsInThread;
    }
    
    // Extract email ID from Gmail URL
    function extractEmailId(url) {
        const hashPart = url.split('#')[1];
        if (!hashPart) return null;
        
        // Get the last segment after any forward slash
        const lastSegment = hashPart.split('/').pop();
        
        // Email IDs are long alphanumeric strings
        return lastSegment && lastSegment.length > 10 ? lastSegment : null;
    }
    
    // Create unique composite ID (same logic as Email Pinner)
    function createIndividualEmailId(emailId, nativeTimestamp) {
        if (!emailId || !nativeTimestamp) return emailId || 'unknown_email';
        
        const timestampHash = createTimestampHash(nativeTimestamp);
        return `${emailId}_${timestampHash}`;
    }
    
    // Create timestamp hash (same logic as Email Pinner)
    function createTimestampHash(timestamp) {
        if (!timestamp) return 'no_timestamp';
        
        let hash = 0;
        for (let i = 0; i < timestamp.length; i++) {
            const char = timestamp.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16);
    }
    
    // Extract thread ID from current URL
    function extractThreadId() {
        const url = window.location.href;
        const hashPart = url.split('#')[1];
        if (!hashPart) return null;
        
        // Use the hash part as thread ID
        return hashPart.split('?')[0]; // Remove any query parameters
    }
    
    // Find the Gmail thread title (H2)
    function findThreadTitle() {
        // Look for Gmail thread title - try multiple selectors
        let threadTitle = document.querySelector('h2[jsname="r4nke"]'); // Main Gmail thread title
        
        if (!threadTitle) {
            // Fallback selectors
            threadTitle = document.querySelector('h2[data-thread-perm-id]') ||
                         document.querySelector('h2.hP') ||
                         document.querySelector('h2'); // Last resort
        }
        
        return threadTitle;
    }
    
    // Find the main Gmail email container where jump links should be inserted
    function findMainEmailContainer() {
        // Enhanced container detection with multiple strategies
        let container = null;
        let detectionMethod = '';

        // Strategy 1: Try original selectors (legacy compatibility)
        const legacySelectors = [
            '.nH.a98.iY',
            'div.nH.a98.iY',
            '[class*="nH"][class*="a98"][class*="iY"]',
            '.nH.iY',
            '.a98.iY'
        ];

        for (const selector of legacySelectors) {
            container = document.querySelector(selector);
            if (container && isValidEmailContainer(container)) {
                detectionMethod = 'legacy';
                if (debugMode) {
                    console.log('Gmail Jump Links: Found container via legacy selector:', selector);
                }
                break;
            }
        }

        // Strategy 2: Modern Gmail selectors (2024+ layouts)
        if (!container) {
            const modernSelectors = [
                '[role="main"] .nH',
                '.nH[data-thread-id]',
                '.nH.if',
                '.nH.aBn',
                '.nH.a98',
                '.nH:has(.ii.gt)',
                'div[data-thread-perm-id] .nH',
                '[jsmodel] .nH'
            ];

            for (const selector of modernSelectors) {
                try {
                    container = document.querySelector(selector);
                    if (container && isValidEmailContainer(container)) {
                        detectionMethod = 'modern';
                        if (debugMode) {
                            console.log('Gmail Jump Links: Found container via modern selector:', selector);
                        }
                        break;
                    }
                } catch (e) {
                    // Some selectors might not be supported in all browsers
                    continue;
                }
            }
        }

        // Strategy 3: Heuristic detection - find parent of email containers
        if (!container) {
            const emailContainers = document.querySelectorAll([
                '.ii.gt',
                '.ii.gt.m144e',
                '.adn.ads',
                '.kv'
            ].join(', '));

            if (emailContainers.length > 0) {
                // Find common parent container that could serve as insertion point
                let commonParent = emailContainers[0].parentElement;
                
                // Walk up the DOM to find a suitable container
                while (commonParent && commonParent !== document.body) {
                    // Check if this parent contains multiple email elements
                    const childEmails = commonParent.querySelectorAll('.ii.gt, .adn.ads, .kv');
                    
                    if (childEmails.length >= emailContainers.length && 
                        isValidEmailContainer(commonParent)) {
                        container = commonParent;
                        detectionMethod = 'heuristic';
                        if (debugMode) {
                            console.log('Gmail Jump Links: Found container via heuristic detection');
                        }
                        break;
                    }
                    
                    commonParent = commonParent.parentElement;
                }
            }
        }

        // Strategy 4: Thread-based detection using thread title proximity
        if (!container) {
            const threadTitle = findThreadTitle();
            if (threadTitle) {
                // Look for containers near the thread title
                const titleContainer = threadTitle.closest('[role="main"], .nH, [data-thread-id]');
                if (titleContainer && isValidEmailContainer(titleContainer)) {
                    container = titleContainer;
                    detectionMethod = 'thread-proximity';
                    if (debugMode) {
                        console.log('Gmail Jump Links: Found container via thread title proximity');
                    }
                }
            }
        }

        // Strategy 5: Fallback to any reasonable container
        if (!container) {
            const fallbackSelectors = [
                '[role="main"]',
                '.Bu.y3',  // Gmail main content area
                '.AO',      // Gmail content wrapper
                'div[data-thread-perm-id]',
                '.nH'       // Any nH element as last resort
            ];

            for (const selector of fallbackSelectors) {
                container = document.querySelector(selector);
                if (container) {
                    // Extra validation for fallback containers
                    const hasEmailContent = container.querySelector('.ii.gt, .adn.ads, h2[jsname="r4nke"]');
                    if (hasEmailContent) {
                        detectionMethod = 'fallback';
                        if (debugMode) {
                            console.log('Gmail Jump Links: Found container via fallback selector:', selector);
                        }
                        break;
                    }
                }
            }
        }

        // Enhanced logging and validation
        if (debugMode) {
            if (container) {
                console.log('Gmail Jump Links: Successfully found main email container:', {
                    method: detectionMethod,
                    tagName: container.tagName,
                    className: container.className,
                    hasEmailChildren: container.querySelectorAll('.ii.gt, .adn.ads, .kv').length,
                    isVisible: isElementVisible(container)
                });
            } else {
                // Only warn in debug mode - silent failure otherwise
                console.log('Gmail Jump Links: No suitable main email container found');
                
                // Diagnostic information for debugging
                console.log('Gmail Jump Links: Available elements for debugging:', {
                    emailContainers: document.querySelectorAll('.ii.gt, .adn.ads, .kv').length,
                    nHElements: document.querySelectorAll('.nH').length,
                    mainRoleElements: document.querySelectorAll('[role="main"]').length,
                    threadTitle: !!findThreadTitle()
                });
            }
        }

        return container;
    }

    // Validate that a container is suitable for inserting jump links
    function isValidEmailContainer(container) {
        if (!container) return false;
        
        try {
            // Must be a DOM element
            if (!container.nodeType || container.nodeType !== Node.ELEMENT_NODE) {
                return false;
            }
            
            // Must be visible and have dimensions
            if (!isElementVisible(container)) {
                return false;
            }
            
            // Should be able to insert content (not input/select/etc)
            const tagName = container.tagName.toLowerCase();
            const invalidTags = ['input', 'select', 'textarea', 'button', 'img', 'video', 'audio'];
            if (invalidTags.includes(tagName)) {
                return false;
            }
            
            // Should have a reasonable position in the document
            const rect = container.getBoundingClientRect();
            if (rect.width < 100 || rect.height < 50) {
                return false;
            }
            
            // Prefer containers that have email-related content
            const hasEmailContent = container.querySelector('.ii.gt, .adn.ads, .kv, h2[jsname="r4nke"]');
            const hasGmailClasses = /nH|gmail|email|thread/i.test(container.className);
            
            return hasEmailContent || hasGmailClasses;
            
        } catch (error) {
            if (debugMode) {
                console.log('Gmail Jump Links: Error validating container:', error);
            }
            return false;
        }
    }
    
    // Check if an element is visible
    function isElementVisible(element) {
        if (!element) return false;
        
        try {
            // Check computed style
            const style = window.getComputedStyle(element);
            if (style.display === 'none' || 
                style.visibility === 'hidden' || 
                style.opacity === '0') {
                return false;
            }
            
            // Check if element has dimensions
            const rect = element.getBoundingClientRect();
            return rect.width > 0 && rect.height > 0;
            
        } catch (error) {
            return true; // Default to visible if we can't check
        }
    }
    
    // Extract sender from email container
    function extractSenderFromContainer(container) {
        // Look for sender element within this specific container
        let senderElement = container.querySelector('.gD[email]');
        if (senderElement && senderElement.getAttribute('email')) {
            return senderElement.getAttribute('email').split('@')[0]; // Get name part only
        }
        
        // Alternative selector for sender within container
        senderElement = container.querySelector('.go .g2, .yX .yW, .qu .yW');
        if (senderElement) {
            return senderElement.textContent.trim().split(' ')[0]; // First name only
        }
        
        return 'Email';
    }
    
    // Format timestamp for display
    function formatTimestamp(nativeTimestamp) {
        try {
            const date = new Date(nativeTimestamp);
            
            // Format time (e.g., "10:30 AM")
            const time = date.toLocaleString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
            
            // Format date (e.g., "Jan 15")
            const dateStr = date.toLocaleString('en-US', {
                month: 'short',
                day: 'numeric'
            });
            
            return { time, date: dateStr };
        } catch (error) {
            if (debugMode) {
                console.error('Gmail Jump Links: Error formatting timestamp:', error);
            }
            return { time: 'Time', date: 'Date' };
        }
    }
    
    // NEW: Create jump links ONLY for pinned emails
    function createJumpLinksForPinnedEmails(threadTitle, pinnedEmailsInThread, mainContainer) {
        try {
            // Create the jump links container
            const jumpContainer = document.createElement('div');
            jumpContainer.className = 'gmail-jump-links-container';
            jumpContainer.style.cssText = `
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                margin: 12px 0;
                padding: 12px;
                background: rgba(124, 58, 237, 0.05);
                border: 1px solid rgba(124, 58, 237, 0.2);
                border-radius: 6px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto";
            `;
            
            // Add a label indicating these are pinned emails
            const label = document.createElement('span');
            label.textContent = `Jump to pinned (${pinnedEmailsInThread.length}):`;
            label.style.cssText = `
                font-size: 12px;
                color: #6b7280;
                font-weight: 500;
                align-self: center;
                margin-right: 4px;
            `;
            jumpContainer.appendChild(label);
            
            // Create jump links ONLY for pinned emails
            pinnedEmailsInThread.forEach((emailData) => {
                const jumpLink = document.createElement('button');
                jumpLink.className = 'gmail-jump-link';
                jumpLink.textContent = `${emailData.displayTime} - ${emailData.displayDate}`;
                jumpLink.style.cssText = `
                    background: #7C3AED;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 11px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    box-shadow: 0 1px 2px rgba(0,0,0,0.15);
                `;
                
                // Add hover effects with purple theme for pinned emails
                jumpLink.addEventListener('mouseenter', () => {
                    jumpLink.style.background = '#6d28d9';
                    jumpLink.style.transform = 'translateY(-1px)';
                    jumpLink.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
                });
                
                jumpLink.addEventListener('mouseleave', () => {
                    jumpLink.style.background = '#7C3AED';
                    jumpLink.style.transform = 'translateY(0)';
                    jumpLink.style.boxShadow = '0 1px 2px rgba(0,0,0,0.15)';
                });
                
                // Add click handler for pinned email navigation
                jumpLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    jumpToPinnedEmail(emailData);
                });
                
                jumpContainer.appendChild(jumpLink);
            });
            
            // Insert the jump links container at the beginning of the main email container
            mainContainer.insertBefore(jumpContainer, mainContainer.firstChild);
            
            // Store reference for cleanup
            jumpLinksContainer = jumpContainer;
            
            if (debugMode) {
                console.log('Gmail Jump Links: Jump links created for', pinnedEmailsInThread.length, 'pinned emails');
            }
            
        } catch (error) {
            console.error('Gmail Jump Links: Error creating jump links for pinned emails:', error);
        }
    }
    
    // Jump to specific pinned email
    function jumpToPinnedEmail(emailData) {
        try {
            const container = emailData.container;
            if (!container || !document.contains(container)) {
                if (debugMode) {
                    console.warn('Gmail Jump Links: Pinned email container not found');
                }
                return;
            }
            
            // Smart scrolling based on reverse order status
            const scrollBlock = reverseOrderEnabled ? 'end' : 'start';
            container.scrollIntoView({ 
                behavior: 'smooth', 
                block: scrollBlock,
                inline: 'nearest'
            });
            
            // Add visual highlight effect for pinned email
            highlightPinnedEmail(container);
            
            if (debugMode) {
                console.log('Gmail Jump Links: Jumped to pinned email:', emailData.displayTime);
            }
            
        } catch (error) {
            console.error('Gmail Jump Links: Error jumping to pinned email:', error);
        }
    }
    
    // Add visual highlight to a pinned email container (red theme)
    function highlightPinnedEmail(container) {
        if (!container) return;
        
        const originalBackground = container.style.backgroundColor;
        const originalTransition = container.style.transition;
        
        // Set highlight style with purple theme for pinned emails
        container.style.transition = 'background-color 0.3s ease';
        container.style.backgroundColor = 'rgba(124, 58, 237, 0.15)'; // Purple highlight for pinned
        
        // Remove highlight after delay
        setTimeout(() => {
            container.style.backgroundColor = originalBackground;
            
            // Remove transition after animation
            setTimeout(() => {
                container.style.transition = originalTransition;
            }, 300);
        }, 2000);
    }
    
    // Remove existing jump links
    function removeJumpLinks() {
        if (jumpLinksContainer && document.contains(jumpLinksContainer)) {
            jumpLinksContainer.remove();
            jumpLinksContainer = null;
        }
        
        // Also remove any orphaned jump links containers
        const existingContainers = document.querySelectorAll('.gmail-jump-links-container');
        existingContainers.forEach(container => container.remove());
    }
    
    
    
    
    // Listen for settings changes with context validation
    if (isExtensionContextValid() && typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            try {
                // Validate context on each message
                if (!isExtensionContextValid()) {
                    sendResponse({ success: false, error: 'Extension context invalidated' });
                    return;
                }
                
                if (request.action === 'updateSettings' && request.settings) {
                    const newEnabled = request.settings.gmailJumpLinksEnabled !== false;
                    const newDebugMode = request.settings.debugMode || false;
                    
                    if (newEnabled !== isEnabled) {
                        isEnabled = newEnabled;
                        debugMode = newDebugMode;
                        
                        if (isEnabled) {
                            initializeFeature();
                        } else {
                            disableFeature();
                        }
                    } else if (newDebugMode !== debugMode) {
                        debugMode = newDebugMode;
                    }
                    
                    sendResponse({ success: true });
                }
            } catch (error) {
                // Silent error handling for context invalidation
                sendResponse({ success: false, error: error.message });
            }
        });
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadSettings);
    } else {
        loadSettings();
    }
})();