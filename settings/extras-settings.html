<div class="settings-section">
    <div class="settings-section__title">Advanced Features</div>
    
    <!-- Search Bar for Advanced Features -->
    <div class="advanced-features-search-container">
        <input type="text" id="advancedFeaturesSearch" class="advanced-features-search" placeholder="Search settings... (3+ letters, press space to expand results)" autocomplete="off">
        <button type="button" id="clearAdvancedFeaturesSearch" class="advanced-features-search-clear" title="Clear search">×</button>
    </div>
    
    <!-- Google & Search Tools Accordion -->
    <div class="settings-accordion">
        <div class="settings-accordion-header" id="googleToolsHeader">
            <div class="settings-accordion-title">
                Google & Search Tools
            </div>
            <span class="settings-accordion-icon" id="googleToolsIcon">▼</span>
        </div>
        <div class="settings-accordion-content" id="googleToolsContent">
            <div class="settings-accordion-body">
                <!-- Select All/Deselect All Toggle -->
                <div class="settings-item settings-item--select-all">
                    <div class="settings-item__content">
                        <div class="settings-item__info">
                            <div class="settings-item__label">Select All / Deselect All</div>
                            <div class="settings-item__description">Toggle all options in this section on or off</div>
                        </div>
                        <div class="settings-item__control">
                            <div class="toggle-switch toggle-switch--active" id="googleToolsSelectAll" data-accordion="googleTools"></div>
                        </div>
                    </div>
                </div>
                <div class="settings-group">
                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Location Changer</div>
                                <div class="settings-item__description">Enable location spoofing functionality in the main popup. Keyboard shortcut: L key to toggle accordion open/close.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="locationChangerToggle" data-setting="locationChangerEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">SERP Numbering</div>
                                <div class="settings-item__description">Add numbered indicators to Google search results (1, 2, 3...)</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="serpNumberingToggle" data-setting="serpNumbering"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">
                                    Sponsored Highlighter
                                    <div class="settings-item__inline-controls">
                                        <input type="color" id="sponsoredHighlighterColorPicker" class="inline-color-swatch" value="#ff0000" title="Choose highlight color">
                                        <input type="text" id="sponsoredHighlighterColor" class="inline-color-hex" data-setting="sponsoredHighlighterColor" value="#ff0000" placeholder="#ff0000" maxlength="7" title="Enter hex color code">
                                    </div>
                                </div>
                                <div class="settings-item__description">Highlight "Sponsored" text with customizable borders on Google pages</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="sponsoredHighlighterToggle" data-setting="sponsoredHighlighter"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Search Result Stats</div>
                                <div class="settings-item__description">Display Google search result statistics (e.g., "About 1,000 results") at the top of search pages</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="searchResultStatsToggle" data-setting="searchResultStats"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Top URLs Copier</div>
                                <div class="settings-item__description">Add a button to Google search pages that copies the top 5 search result URLs to clipboard</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="topUrlsCopierToggle" data-setting="topUrlsCopierEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Current Location Display</div>
                                <div class="settings-item__description">Display the current location from Location Changer on Google search pages (above tracked domains jump navigation)</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="currentLocationDisplayToggle" data-setting="currentLocationDisplayEnabled"></div>
                            </div>
                        </div>
                    </div>


                    <div class="settings-item settings-item--expanded">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Tracked Domains</div>
                                <div class="settings-item__description">Highlight your tracked domains in Google search results with purple dashed underlines</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="trackedDomainsToggle" data-setting="trackedDomainsEnabled"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tracked-domains-accordion">
                        <div class="tracked-domains-header" id="trackedDomainsHeader">
                            <div class="tracked-domains-title">
                                <span style="color: #7C3AED; font-size: 16px;">●</span> Domain Configuration
                            </div>
                            <span class="tracked-domains-icon" id="trackedDomainsIcon">▼</span>
                        </div>
                        <div class="tracked-domains-content" id="trackedDomainsContent">
                            <div class="tracked-domains-body">
                                <div class="tracked-domains-input-group">
                                    <label class="tracked-domains-input-label">Tracked Domains (one per line)</label>
                                    <div class="tracked-domains-textarea-wrapper">
                                        <textarea id="trackedDomainsList" class="tracked-domains-textarea" data-setting="trackedDomainsList" placeholder="babyboofashion.com&#10;beginningboutique.com.au&#10;example.com" rows="6"></textarea>
                                        <div class="tracked-domains-color-picker">
                                            <input type="color" id="trackedDomainsColorPicker" class="tracked-domains-color-swatch" value="#7c3aed" title="Choose highlight color">
                                            <input type="text" id="trackedDomainsColor" class="tracked-domains-color-hex" data-setting="trackedDomainsColor" value="#7c3aed" placeholder="#7c3aed" maxlength="7" title="Enter hex color code">
                                        </div>
                                    </div>
                                    <div class="tracked-domains-help">
                                        Enter domain names one per line. Subdomains and paths will be automatically matched.
                                        <br>Example: "example.com" will match "www.example.com/page" and "shop.example.com"
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Business & GMB Tools Accordion -->
    <div class="settings-accordion">
        <div class="settings-accordion-header" id="businessToolsHeader">
            <div class="settings-accordion-title">
                Business & GMB Tools
            </div>
            <span class="settings-accordion-icon" id="businessToolsIcon">▼</span>
        </div>
        <div class="settings-accordion-content" id="businessToolsContent">
            <div class="settings-accordion-body">
                <!-- Select All/Deselect All Toggle -->
                <div class="settings-item settings-item--select-all">
                    <div class="settings-item__content">
                        <div class="settings-item__info">
                            <div class="settings-item__label">Select All / Deselect All</div>
                            <div class="settings-item__description">Toggle all options in this section on or off</div>
                        </div>
                        <div class="settings-item__control">
                            <div class="toggle-switch toggle-switch--active" id="businessToolsSelectAll" data-accordion="businessTools"></div>
                        </div>
                    </div>
                </div>
                <div class="settings-group">
                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Citation Hunter</div>
                                <div class="settings-item__description">Enable "Find Citations" button to search for business mentions across the web</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="citationHunterToggle" data-setting="citationHunter"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Open Single Listing</div>
                                <div class="settings-item__description">Enable "Open Single Listing" button for quick access to clean business listing URLs in multiple listings mode</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="openSingleListingToggle" data-setting="openSingleListing"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Search NAP Injector</div>
                                <div class="settings-item__description">Enable "Copy NAP" button injection in Google Search results and business popups for quick NAP data extraction</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="searchNAPInjectorToggle" data-setting="searchNAPInjector"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Developer & Content Tools Accordion -->
    <div class="settings-accordion">
        <div class="settings-accordion-header" id="developerToolsHeader">
            <div class="settings-accordion-title">
                Developer & Content Tools
            </div>
            <span class="settings-accordion-icon" id="developerToolsIcon">▼</span>
        </div>
        <div class="settings-accordion-content" id="developerToolsContent">
            <div class="settings-accordion-body">
                <!-- Select All/Deselect All Toggle -->
                <div class="settings-item settings-item--select-all">
                    <div class="settings-item__content">
                        <div class="settings-item__info">
                            <div class="settings-item__label">Select All / Deselect All</div>
                            <div class="settings-item__description">Toggle all options in this section on or off</div>
                        </div>
                        <div class="settings-item__control">
                            <div class="toggle-switch toggle-switch--active" id="developerToolsSelectAll" data-accordion="developerTools"></div>
                        </div>
                    </div>
                </div>
                <div class="settings-group">
                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Auto Clean & Title on Copy Element</div>
                                <div class="settings-item__description">Automatically clean HTML and add title attributes when using Shift+Click with Copy Element. This removes styles, classes, IDs, and adds title attributes to heading tags.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch" id="autoCleanAndTitleToggle" data-setting="autoCleanAndTitleEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">HTML Cleaning Options</div>
                                <div class="settings-item__description">Configure which HTML cleaning operations to apply when "Auto Clean & Title" is enabled. Choose which elements to remove: styles, classes, IDs, comments, empty tags, and extra spaces.</div>
                            </div>
                            <div class="settings-item__control">
                                <button class="settings-button" id="htmlCleanerOptionsBtn">⚙️ Configure Options</button>
                            </div>
                        </div>   
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Click to Copy Links</div>
                                <div class="settings-item__description">Hover over any link and press Ctrl+C (Cmd+C on Mac) to instantly copy the link URL to clipboard</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="clickToCopyToggle" data-setting="clickToCopyEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Copy Replace</div>
                                <div class="settings-item__description">Replace domain in clipboard URLs before pasting. Configure source and target domains, then press Ctrl+Shift+V (Cmd+Shift+V on Mac) to automatically replace and paste URLs.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="copyReplaceToggle" data-setting="copyReplaceEnabled"></div>
                            </div>
                        </div>
                    </div>


                    <div class="copy-replace-accordion">
                        <div class="copy-replace-header" id="copyReplaceHeader">
                            <div class="copy-replace-title">
                                <span style="color: #7C3AED; font-size: 16px;">●</span> Copy Replace Domain Configuration
                            </div>
                            <span class="copy-replace-icon" id="copyReplaceIcon">▼</span>
                        </div>
                        <div class="copy-replace-content" id="copyReplaceContent">
                            <div class="copy-replace-body">
                                <div class="copy-replace-input-group">
                                    <label class="copy-replace-input-label">Source Domain (to replace):</label>
                                    <input type="text" id="copyReplaceSourceDomain" class="copy-replace-input" data-setting="copyReplaceSourceDomain" placeholder="domain-to-be-replaced.com" title="Enter the domain to be replaced (without protocol)">
                                    <div class="copy-replace-help">
                                        Enter the domain you want to replace. Example: domain-to-be-replaced.com
                                    </div>
                                </div>
                                
                                <div class="copy-replace-input-group">
                                    <label class="copy-replace-input-label">Target Domain (replacement):</label>
                                    <input type="text" id="copyReplaceTargetDomain" class="copy-replace-input" data-setting="copyReplaceTargetDomain" placeholder="target-domain.com" title="Enter the target domain (with or without https://)">
                                    <div class="copy-replace-help">
                                        Enter the replacement domain. HTTPS will be added automatically. Example: target-domain.com
                                    </div>
                                </div>
                                
                                <div class="copy-replace-info">
                                    <div class="copy-replace-example">
                                        <strong>How it works:</strong><br>
                                        Copy: <code>https://domain-to-be-replaced.com/page/</code><br>
                                        Press <strong>Ctrl+Shift+V</strong> → Pastes: <code>https://target-domain.com/page/</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="settings-item settings-item--expanded">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Drag Select Links</div>
                                <div class="settings-item__description">Hold hotkey and drag to select an area on any webpage, then extract and manage all links within the selected region in a popup interface</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="dragSelectLinksToggle" data-setting="dragSelectLinksEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="drag-select-accordion">
                        <div class="drag-select-header" id="dragSelectLinksHeader">
                            <div class="drag-select-title">
                                <span style="color: #7C3AED; font-size: 16px;">●</span> Drag Select Configuration
                            </div>
                            <span class="drag-select-icon" id="dragSelectLinksIcon">▼</span>
                        </div>
                        <div class="drag-select-content" id="dragSelectLinksContent">
                            <div class="drag-select-body">
                                <div class="drag-select-input-group">
                                    <label class="drag-select-input-label">Hotkey:</label>
                                    <select id="dragSelectLinksHotkey" class="drag-select-select" data-setting="dragSelectLinksHotkey">
                                        <option value="90">Z Key</option>
                                        <option value="88">X Key</option>
                                        <option value="67">C Key</option>
                                        <option value="86">V Key</option>
                                        <option value="66">B Key</option>
                                    </select>
                                </div>
                                

                                
                                <div class="drag-select-input-group">
                                    <label class="drag-select-input-label">Link Filtering:</label>
                                    <select id="dragSelectLinksFilterMode" class="drag-select-select" data-setting="dragSelectLinksFilterMode">
                                        <option value="exclude">Exclude links containing specified text</option>
                                        <option value="include">Include only links containing specified text</option>
                                    </select>
                                </div>
                                
                                <div class="drag-select-input-group">
                                    <label class="drag-select-input-label">Filter Text (comma-separated):</label>
                                    <input type="text" id="dragSelectLinksFilterText" class="drag-select-input" data-setting="dragSelectLinksFilterText" placeholder="javascript,#,mailto" title="Enter text to filter by (comma-separated)">
                                    <div class="drag-select-help">
                                        Enter keywords to filter links by (comma-separated). Use exclude mode to remove unwanted links or include mode to only show specific links.
                                    </div>
                                </div>
                                
                                <div class="drag-select-input-group">
                                    <div class="drag-select-checkbox-group">
                                        <label class="drag-select-checkbox-label">
                                            <input type="checkbox" id="dragSelectLinksRemoveDuplicates" data-setting="dragSelectLinksRemoveDuplicates" checked>
                                            <span class="drag-select-checkbox-text">Remove duplicate URLs</span>
                                        </label>
                                        
                                        <label class="drag-select-checkbox-label">
                                            <input type="checkbox" id="dragSelectLinksIncludeText" data-setting="dragSelectLinksIncludeText" checked>
                                            <span class="drag-select-checkbox-text">Include link text in results</span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="drag-select-input-group">
                                    <label class="drag-select-input-label">Format Options:</label>
                                    
                                    <div class="drag-select-format-section">
                                                                     <div class="drag-select-format-group">
                                             <div class="drag-select-format-title">TEXT</div>
                                             <div class="drag-select-radio-group">
                                                 <label class="drag-select-radio-label">
                                                     <input type="radio" name="textFormat" value="text-url" data-setting="dragSelectTextFormat">
                                                     <div>
                                                         <span class="drag-select-radio-text">URL Only</span>
                                                         <span class="drag-select-example">Example: https://www.google.com</span>
                                                     </div>
                                                 </label>
                                                 <label class="drag-select-radio-label">
                                                     <input type="radio" name="textFormat" value="text-title" data-setting="dragSelectTextFormat">
                                                     <div>
                                                         <span class="drag-select-radio-text">Title Only</span>
                                                         <span class="drag-select-example">Example: google</span>
                                                     </div>
                                                 </label>
                                                 <label class="drag-select-radio-label">
                                                     <input type="radio" name="textFormat" value="text-combined" data-setting="dragSelectTextFormat" checked>
                                                     <div>
                                                         <span class="drag-select-radio-text">Title and URL</span>
                                                         <span class="drag-select-example">Example: google    https://www.google.com</span>
                                                     </div>
                                                 </label>
                                             </div>
                                         </div>
                                        
                                                                     <div class="drag-select-format-group">
                                             <div class="drag-select-format-title">HTML</div>
                                             <div class="drag-select-radio-group">
                                                 <label class="drag-select-radio-label">
                                                     <input type="radio" name="htmlFormat" value="html-url" data-setting="dragSelectHtmlFormat" checked>
                                                     <div>
                                                         <span class="drag-select-radio-text">Web page URL</span>
                                                         <span class="drag-select-example">Example: &lt;a href="https://www.google.com"&gt;https://www.google.com&lt;/a&gt;</span>
                                                     </div>
                                                 </label>
                                                 <label class="drag-select-radio-label">
                                                     <input type="radio" name="htmlFormat" value="html-title" data-setting="dragSelectHtmlFormat">
                                                     <div>
                                                         <span class="drag-select-radio-text">Web page Title</span>
                                                         <span class="drag-select-example">Example: &lt;a href="https://www.google.com"&gt;google&lt;/a&gt;</span>
                                                     </div>
                                                 </label>
                                             </div>
                                         </div>
                                        
                                                                     <div class="drag-select-format-group">
                                             <div class="drag-select-format-title">JSON</div>
                                             <div class="drag-select-radio-group">
                                                 <label class="drag-select-radio-label">
                                                     <input type="radio" name="jsonFormat" value="json-url" data-setting="dragSelectJsonFormat" checked>
                                                     <div>
                                                         <span class="drag-select-radio-text">URL Only</span>
                                                         <span class="drag-select-example">Example: [{"url":"https://www.google.com"}]</span>
                                                     </div>
                                                 </label>
                                                 <label class="drag-select-radio-label">
                                                     <input type="radio" name="jsonFormat" value="json-title" data-setting="dragSelectJsonFormat">
                                                     <div>
                                                         <span class="drag-select-radio-text">Title Only</span>
                                                         <span class="drag-select-example">Example: [{"title":"google"}]</span>
                                                     </div>
                                                 </label>
                                                 <label class="drag-select-radio-label">
                                                     <input type="radio" name="jsonFormat" value="json-both" data-setting="dragSelectJsonFormat">
                                                     <div>
                                                         <span class="drag-select-radio-text">Title and URL</span>
                                                         <span class="drag-select-example">Example: [{"url":"https://www.google.com","title":"google"}]</span>
                                                     </div>
                                                 </label>
                                             </div>
                                         </div>
                                        
                                        <div class="drag-select-format-group">
                                            <div class="drag-select-format-title">Custom</div>
                                            <div class="drag-select-custom-group">
                                                <div class="drag-select-help">
                                                    Use {title} and {url} variables, don't forget add {br} at the end, if not, all the data will be merged into one line!
                                                </div>
                                                <textarea id="dragSelectCustomFormat" class="drag-select-custom-input" data-setting="dragSelectCustomFormat" placeholder="<a href='{url}'>{title}</a>{br}" rows="3"></textarea>
                                                <div class="drag-select-example">Example: &lt;a href="{url}"&gt;{title}&lt;/a&gt;{title}{br}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                

                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Show Hidden Auto-Detection</div>
                                <div class="settings-item__description">Automatically detect and notify about hidden content on page load. Works independently from the Show Hidden Quick Action button.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="showhiddenAutoDetectionToggle" data-setting="showhiddenAutoDetectionEnabled"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Text Tools Accordion -->
    <div class="settings-accordion">
        <div class="settings-accordion-header" id="textToolsHeader">
            <div class="settings-accordion-title">
                Text Tools
            </div>
            <span class="settings-accordion-icon" id="textToolsIcon">▼</span>
        </div>
        <div class="settings-accordion-content" id="textToolsContent">
            <div class="settings-accordion-body">
                <!-- Select All/Deselect All Toggle -->
                <div class="settings-item settings-item--select-all">
                    <div class="settings-item__content">
                        <div class="settings-item__info">
                            <div class="settings-item__label">Select All / Deselect All</div>
                            <div class="settings-item__description">Toggle all options in this section on or off</div>
                        </div>
                        <div class="settings-item__control">
                            <div class="toggle-switch toggle-switch--active" id="textToolsSelectAll" data-accordion="textTools"></div>
                        </div>
                    </div>
                </div>
                <div class="settings-group">
                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Text Transformers</div>
                                <div class="settings-item__description">Transform selected text with custom keyboard shortcuts. Supports Capital Case, lower case, UPPER CASE, Sentence case, slugify transformations, and Remove Empty Lines.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch" id="textTransformersToggle" data-setting="textTransformersEnabled"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Text Transformers Configuration Accordion -->
                    <div class="copy-replace-accordion">
                        <div class="copy-replace-header" id="textTransformersHeader">
                            <div class="copy-replace-title">
                                <span style="color: #7C3AED; font-size: 16px;">●</span> Text Transformers Configuration
                            </div>
                            <span class="copy-replace-icon" id="textTransformersIcon">▼</span>
                        </div>
                        <div class="copy-replace-content" id="textTransformersContent">
                            <div class="copy-replace-body">
                                <!-- Auto-Paste Toggle -->
                                <div class="settings-item" style="margin-bottom: 20px; border-bottom: 1px solid #374151; padding-bottom: 15px;">
                                    <div class="settings-item__content">
                                        <div class="settings-item__info">
                                            <div class="settings-item__label" style="color: #7C3AED; font-weight: 600;">
                                                <span style="color: #7C3AED; font-size: 18px;">●</span>
                                                Auto-Paste Transformed Text
                                            </div>
                                            <div class="settings-item__description">
                                                When enabled, transformed text replaces selected text automatically. When disabled, transformed text is only copied to clipboard for manual pasting.
                                            </div>
                                        </div>
                                        <div class="settings-item__control">
                                            <div class="toggle-switch toggle-switch--active" id="textTransformersAutoPasteToggle" data-setting="textTransformersAutoPaste"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="custom-shortcut-container">
                                    <div class="custom-shortcut-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        Capital Case Transformation Shortcut
                                    </div>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input type="text" id="textTransformersCapitalCaseShortcut" class="custom-shortcut-input" data-setting="textTransformersCapitalCaseShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for Capital Case transformation">
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>Capital Case Transformation</strong><br>
                                                Transforms text to Capital Case (Title Case).<br>
                                                Example: "hello world" → "Hello World"
                                            </div>
                                        </div>
                                    </div>
                                    <div class="shortcut-status"></div>
                                </div>
                                
                                <div class="custom-shortcut-container">
                                    <div class="custom-shortcut-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        lower case Transformation Shortcut
                                    </div>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input type="text" id="textTransformersLowerCaseShortcut" class="custom-shortcut-input" data-setting="textTransformersLowerCaseShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for lower case transformation">
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>lower case Transformation</strong><br>
                                                Transforms text to lower case.<br>
                                                Example: "Hello World" → "hello world"
                                            </div>
                                        </div>
                                    </div>
                                    <div class="shortcut-status"></div>
                                </div>
                                
                                <div class="custom-shortcut-container">
                                    <div class="custom-shortcut-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        UPPER CASE Transformation Shortcut
                                    </div>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input type="text" id="textTransformersUpperCaseShortcut" class="custom-shortcut-input" data-setting="textTransformersUpperCaseShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for UPPER CASE transformation">
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>UPPER CASE Transformation</strong><br>
                                                Transforms text to UPPER CASE.<br>
                                                Example: "hello world" → "HELLO WORLD"
                                            </div>
                                        </div>
                                    </div>
                                    <div class="shortcut-status"></div>
                                </div>
                                
                                <div class="custom-shortcut-container">
                                    <div class="custom-shortcut-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        Sentence case Transformation Shortcut
                                    </div>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input type="text" id="textTransformersSentenceCaseShortcut" class="custom-shortcut-input" data-setting="textTransformersSentenceCaseShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for Sentence case transformation">
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>Sentence case Transformation</strong><br>
                                                Transforms text to Sentence case.<br>
                                                Example: "hello world. new sentence" → "Hello world. New sentence"
                                            </div>
                                        </div>
                                    </div>
                                    <div class="shortcut-status"></div>
                                </div>
                                
                                <div class="custom-shortcut-container">
                                    <div class="custom-shortcut-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        slugify Transformation Shortcut
                                    </div>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input type="text" id="textTransformersSlugifyShortcut" class="custom-shortcut-input" data-setting="textTransformersSlugifyShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for slugify transformation">
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>slugify Transformation</strong><br>
                                                Transforms text to URL-friendly slug.<br>
                                                Example: "Hello World!" → "hello-world"
                                            </div>
                                        </div>
                                    </div>
                                    <div class="shortcut-status"></div>
                                </div>
                                
                                <div class="custom-shortcut-container">
                                    <div class="custom-shortcut-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        Trim to Page Transformation Shortcut
                                    </div>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input type="text" id="textTransformersTrimToPageShortcut" class="custom-shortcut-input" data-setting="textTransformersTrimToPageShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for trim to page transformation">
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>Trim to Page Transformation</strong><br>
                                                Extracts the last segment after the final slash.<br>
                                                Example: "diamond/alternatives/moissanite" → "moissanite"
                                            </div>
                                        </div>
                                    </div>
                                    <div class="shortcut-status"></div>
                                </div>
                                
                                <div class="custom-shortcut-container">
                                    <div class="custom-shortcut-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        Sort Alphabetically Transformation Shortcut
                                    </div>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input type="text" id="textTransformersSortAlphabeticallyShortcut" class="custom-shortcut-input" data-setting="textTransformersSortAlphabeticallyShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for sort alphabetically transformation">
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>Sort Alphabetically Transformation</strong><br>
                                                Sorts text lines in alphabetical order.<br>
                                                Example: "Charlie<br>Alpha<br>Beta" → "Alpha<br>Beta<br>Charlie"
                                            </div>
                                        </div>
                                    </div>
                                    <div class="shortcut-status"></div>
                                </div>
                                
                                <div class="custom-shortcut-container">
                                    <div class="custom-shortcut-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        Remove Empty Lines Transformation Shortcut
                                    </div>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input type="text" id="textTransformersRemoveEmptyLinesShortcut" class="custom-shortcut-input" data-setting="textTransformersRemoveEmptyLinesShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for remove empty lines transformation">
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>Remove Empty Lines Transformation</strong><br>
                                                Removes all empty lines from text while preserving content and order.<br>
                                                Example: "Line 1<br><br>Line 2<br><br>Line 3" → "Line 1<br>Line 2<br>Line 3"
                                            </div>
                                        </div>
                                    </div>
                                    <div class="shortcut-status"></div>
                                </div>
                                
                                <div class="custom-shortcut-container">
                                    <div class="custom-shortcut-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        Remove Duplicate Lines Transformation Shortcut
                                    </div>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input type="text" id="textTransformersRemoveDuplicateLinesShortcut" class="custom-shortcut-input" data-setting="textTransformersRemoveDuplicateLinesShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for remove duplicate lines transformation">
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>Remove Duplicate Lines Transformation</strong><br>
                                                Removes duplicate lines while preserving the first occurrence.<br>
                                                Example: "Line A<br>Line B<br>Line A" → "Line A<br>Line B"
                                            </div>
                                        </div>
                                    </div>
                                    <div class="shortcut-status"></div>
                                </div>
                                
                                <!-- Text Time Machine Launcher Section -->
                                <div class="copy-replace-info" style="margin-top: 30px; border-top: 2px solid #7C3AED; padding-top: 20px;">
                                    <div class="copy-replace-example">
                                        <strong>Text Time Machine Launcher:</strong><br>
                                        Configure a URL and keyboard shortcut to quickly launch your local text editor or any file/application.
                                    </div>
                                </div>
                                
                                <div class="copy-replace-input-group" style="margin-top: 15px;">
                                    <label class="copy-replace-input-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        Text Time Machine URL:
                                    </label>
                                    <input type="text" id="textTimeMachineUrl" class="copy-replace-input" data-setting="textTimeMachineUrl" placeholder="file:///Users/<USER>/Documents/UNCLUTTER%20NEW/CLAUDE%20DEV/GMB%20Extractor/Local%20Text%20Editor/text-time-machine.html" title="Enter the full URL or file path to launch">
                                    <div class="copy-replace-help">
                                        Enter the full URL or file:// path to launch when the shortcut is pressed. 
                                        <br>Example: file:///Users/<USER>/path/to/file.html or https://example.com
                                    </div>
                                </div>
                                
                                <div class="custom-shortcut-container" style="margin-top: 20px;">
                                    <div class="custom-shortcut-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        Text Time Machine Launcher Shortcut
                                    </div>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input type="text" id="textTimeMachineLauncherShortcut" class="custom-shortcut-input" data-setting="textTimeMachineLauncherShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for Text Time Machine launcher">
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>Text Time Machine Launcher</strong><br>
                                                Opens the specified URL in a new tab when the shortcut is pressed.<br>
                                                Works globally across all websites and applications.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="shortcut-status"></div>
                                </div>
                                
                                <div class="copy-replace-info">
                                    <div class="copy-replace-example">
                                        <strong>How to use:</strong><br>
                                        1. Enable Text Transformers toggle above<br>
                                        2. Set keyboard shortcuts for desired transformations<br>
                                        3. Select any text on a webpage<br>
                                        4. Press your configured shortcut to transform and replace the text
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Productivity Tools -->
    <div class="settings-accordion">
        <div class="settings-accordion-header" id="productivityHeader">
            <div class="settings-accordion-title">
                Productivity
            </div>
            <span class="settings-accordion-icon" id="productivityIcon">▼</span>
        </div>
        <div class="settings-accordion-content" id="productivityContent">
            <div class="settings-accordion-body">
                <!-- Select All/Deselect All Toggle -->
                <div class="settings-item settings-item--select-all">
                    <div class="settings-item__content">
                        <div class="settings-item__info">
                            <div class="settings-item__label">Select All / Deselect All</div>
                            <div class="settings-item__description">Toggle all options in this section on or off</div>
                        </div>
                        <div class="settings-item__control">
                            <div class="toggle-switch toggle-switch--active" id="productivitySelectAll" data-accordion="productivity"></div>
                        </div>
                    </div>
                </div>
                <div class="settings-group">
                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Pomodoro Timer</div>
                                <div class="settings-item__description">Enable/disable Pomodoro timer functionality in the main popup. Configure timer settings in General Settings. Keyboard shortcut: T key to start/stop timer.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="pomodoroToggle" data-setting="pomodoroEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Todo List</div>
                                <div class="settings-item__description">Enable/disable Tasks/Todo List functionality in the main popup for task management. Click the 3-dot grip area (⋯) next to any task to select it, then use <strong>⌘ + ↑/↓ arrows</strong> (Mac) or <strong>Ctrl + ↑/↓ arrows</strong> (Windows) to reorder tasks. Keyboard shortcut: D key to toggle accordion open/close.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="tasksToggle" data-setting="tasksEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Alerts & Reminders</div>
                                <div class="settings-item__description">Enable/disable Alerts & Reminders functionality for scheduled notifications.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="alertsToggle" data-setting="alertsEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Quick Timer</div>
                                <div class="settings-item__description">Enable/disable Quick Timer functionality for countdown timers with badges and sound notifications. Keyboard shortcut: Q key to toggle accordion open/close.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="quickTimerToggle" data-setting="quickTimerEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Quick Timer Browser Notifications</div>
                                <div class="settings-item__description">Show browser notifications when Quick Timer countdown completes (in addition to popup alert).</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="quickTimerNotificationsToggle" data-setting="quickTimerNotifications"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Notification Utility</div>
                                <div class="settings-item__description">Enable/disable browser notification system for extension alerts and status updates.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="notificationUtilityToggle" data-setting="notificationUtilityEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Popup Keyboard Shortcuts</div>
                                <div class="settings-item__description">Use keyboard shortcuts in the popup to quickly access functions: <strong>R</strong> (Reload), <strong>S</strong> (Settings), <strong>T</strong> (Pomodoro Start/Stop), <strong>A</strong> (Alerts), <strong>E</strong> (Emails), <strong>Q</strong> (Quick Timer Toggle), <strong>D</strong> (Todo Toggle), <strong>L</strong> (Location Changer Toggle). Shortcuts work when popup is focused and no input fields are active.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="popupShortcutsToggle" data-setting="popupShortcutsEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">
                                    TODO Display Count
                                    <div class="settings-item__inline-controls">
                                        <select id="pomodoroTodoDisplayCount" class="inline-select" data-setting="pomodoroTodoDisplayCount">
                                            <option value="1">Show 1 task</option>
                                            <option value="2">Show 2 tasks</option>
                                            <option value="3" selected>Show 3 tasks</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="settings-item__description">Choose how many active TODO tasks to display in the popup (up to 3 lines)</div>
                            </div>
                            <div class="settings-item__control">
                                <!-- No toggle needed, just the dropdown -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- General & Browser Tools Accordion -->
    <div class="settings-accordion">
        <div class="settings-accordion-header" id="generalToolsHeader">
            <div class="settings-accordion-title">
                General & Browser Tools
            </div>
            <span class="settings-accordion-icon" id="generalToolsIcon">▼</span>
        </div>
        <div class="settings-accordion-content" id="generalToolsContent">
            <div class="settings-accordion-body">
                <!-- Select All/Deselect All Toggle -->
                <div class="settings-item settings-item--select-all">
                    <div class="settings-item__content">
                        <div class="settings-item__info">
                            <div class="settings-item__label">Select All / Deselect All</div>
                            <div class="settings-item__description">Toggle all options in this section on or off</div>
                        </div>
                        <div class="settings-item__control">
                            <div class="toggle-switch toggle-switch--active" id="generalToolsSelectAll" data-accordion="generalTools"></div>
                        </div>
                    </div>
                </div>
                <div class="settings-group">
                    <div class="settings-item settings-item--expanded">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">UTM Tracking Cleaner</div>
                                <div class="settings-item__description">Remove UTM tracking parameters and clean redirect links for improved privacy. Runs automatically in the background when enabled.
                                    <br><br><strong>⚡ Quick Disable:</strong> In the extension popup, hold <kbd>Shift</kbd> and click the UTM whitelist button to temporarily disable UTM cleaning for quick troubleshooting.</div>
                            </div>
                            <div class="settings-item__control">
                                <button class="settings-button" id="utmTrackingCleanerConfigBtn" style="margin-right: 30px;">⚙️ Configure Options</button>
                                <div class="toggle-switch toggle-switch--active" id="utmTrackingCleanerToggle" data-setting="utmTrackingCleanerEnabled"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="utm-cleaner-accordion">
                        <div class="utm-cleaner-header" id="utmCleanerHeader">
                            <div class="utm-cleaner-title">
                                <span style="color: #7C3AED; font-size: 16px;">●</span> Whitelist Configuration
                            </div>
                            <span class="utm-cleaner-icon" id="utmCleanerIcon">▼</span>
                        </div>
                        <div class="utm-cleaner-content" id="utmCleanerContent">
                            <div class="utm-cleaner-body">
                                <div class="utm-cleaner-input-group">
                                    <label class="utm-cleaner-input-label">Whitelisted Domains (one per line)</label>
                                    <div class="utm-cleaner-textarea-wrapper">
                                        <textarea id="utmCleanerWhitelistDomains" class="utm-cleaner-textarea" data-setting="utmCleanerWhitelistDomains" placeholder="example.com&#10;important-site.org&#10;trusted-domain.net" rows="6"></textarea>
                                    </div>
                                    <div class="utm-cleaner-help">
                                        Enter domain names one per line. These domains will be excluded from UTM cleaning to preserve functionality.
                                        <br>Example: "affiliate-site.com" will preserve tracking parameters on that domain while cleaning others.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item settings-item--expanded">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">
                                    Minimal Reader
                                    <div class="settings-item__inline-controls">
                                        <select id="minimalReaderButtonPosition" class="inline-select" data-setting="minimalReaderButtonPosition">
                                            <option value="top-left">Top Left</option>
                                            <option value="top-right">Top Right</option>
                                            <option value="bottom-left">Bottom Left</option>
                                            <option value="bottom-right">Bottom Right</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="settings-item__description">Transform any webpage into a clean, distraction-free reading experience with speed reading features and customizable themes
                                    <br><br><strong>💡 Quick Tip:</strong> Hold <kbd>Shift</kbd> and click the reader button to instantly blacklist the current domain. The button will disappear and won't show on that domain until you remove it from the blacklist below.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="minimalReaderToggle" data-setting="minimalReaderEnabled"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="minimal-reader-accordion">
                        <div class="minimal-reader-header" id="minimalReaderBlacklistHeader">
                            <div class="minimal-reader-title">
                                <span style="color: #7C3AED; font-size: 16px;">●</span> Blacklist Configuration
                            </div>
                            <span class="minimal-reader-icon" id="minimalReaderBlacklistIcon">▼</span>
                        </div>
                        <div class="minimal-reader-content" id="minimalReaderBlacklistContent">
                            <div class="minimal-reader-body">
                                <div class="minimal-reader-input-group">
                                    <label class="minimal-reader-input-label">Blacklisted Domains (one per line)</label>
                                    <div class="minimal-reader-textarea-wrapper">
                                        <textarea id="minimalReaderBlacklistDomains" class="minimal-reader-textarea" data-setting="minimalReaderBlacklistDomains" placeholder="example.com&#10;social-media-site.com&#10;news-site.org" rows="6"></textarea>
                                    </div>
                                    <div class="minimal-reader-help">
                                        Enter domain names one per line. These domains will be excluded from showing the minimal reader button.
                                        <br>Example: "facebook.com" will prevent the reader button from appearing on Facebook.
                                        <br><strong>Tip:</strong> Hold Shift and click the reader button to quickly add the current domain to this blacklist.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Screenshot Tool - Add inside generalToolsContent settings-group -->
                    <div class="settings-item settings-item--expanded">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Screenshot Tool</div>
                                <div class="settings-item__description">Capture, annotate, and manage screenshots with professional drawing tools (arrow, line, text, box). Uses local storage for optimal performance.</div>
                            </div>
                            <div class="settings-item__control">
                                <button class="settings-button" id="screenshotToolConfigBtn" style="margin-right: 30px;">⚙️ Configure</button>
                                <div class="toggle-switch toggle-switch--active" id="screenshotToolToggle" data-setting="screenshotToolEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Screenshot Configuration Accordion -->
                    <div class="screenshot-tool-accordion">
                        <div class="screenshot-tool-header" id="screenshotToolHeader">
                            <div class="screenshot-tool-title">
                                <span style="color: #7C3AED; font-size: 16px;">●</span> Screenshot Configuration
                            </div>
                            <span class="screenshot-tool-icon" id="screenshotToolIcon">▼</span>
                        </div>
                        <div class="screenshot-tool-content" id="screenshotToolContent">
                            <div class="screenshot-tool-body">
                                <div class="screenshot-tool-input-group">
                                    <label class="screenshot-tool-input-label">Custom Global Shortcut:</label>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input 
                                            type="text" 
                                            id="screenshotCustomShortcut" 
                                            class="custom-shortcut-input screenshot-tool-select" 
                                            data-setting="screenshotShortcut"
                                            placeholder="Click and press keys to set shortcut..."
                                            title="Press the keys you want to use as a shortcut. Examples: Ctrl+Shift+S, Alt+S, Ctrl+Cmd+X"
                                        />
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>Supported formats:</strong><br>
                                                • Ctrl+Shift+S<br>
                                                • Alt+S<br>
                                                • Cmd+Shift+S (Mac)<br>
                                                • F1-F12 with modifiers<br>
                                                <br>
                                                <strong>Note:</strong> Avoid browser shortcuts
                                            </div>
                                        </div>
                                    </div>
                                    <div id="screenshotShortcutStatus" class="shortcut-status"></div>
                                </div>
                                
                                <div class="screenshot-tool-input-group">
                                    <label class="screenshot-tool-input-label">Default Drawing Color:</label>
                                    <div class="screenshot-tool-color-picker">
                                        <input type="color" id="screenshotToolColorPicker" class="screenshot-tool-color-swatch" value="#7c3aed" title="Choose drawing color">
                                        <input type="text" id="screenshotToolDrawingColor" class="screenshot-tool-color-hex" data-setting="screenshotToolDrawingColor" value="#7c3aed" placeholder="#7c3aed" maxlength="7" title="Enter hex color code">
                                    </div>
                                </div>
                                
                                
                                <div class="screenshot-tool-input-group">
                                    <div class="screenshot-tool-checkbox-group">
                                        <label class="screenshot-tool-checkbox-label">
                                            <input type="checkbox" id="screenshotToolShowUI" data-setting="screenshotToolShowUI" checked>
                                            <span class="screenshot-tool-checkbox-text">Show drawing toolbar</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- New Tab Redirect -->
                    <div class="settings-item settings-item--expanded">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">New Tab Redirect</div>
                                <div class="settings-item__description">Automatically redirect new tabs to a custom URL of your choice. When enabled, all new tabs will navigate to your specified URL instead of showing the default new tab page.<br><br>
                                NOTE: Command+T will NOT work, as this is the default Chrome New Tab Shortcut.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="newTabRedirectToggle" data-setting="newTabRedirectEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <!-- New Tab Redirect Configuration Accordion -->
                    <div class="copy-replace-accordion">
                        <div class="copy-replace-header" id="newTabRedirectHeader">
                            <div class="copy-replace-title">
                                <span style="color: #7C3AED; font-size: 16px;">●</span> Redirect Configuration
                            </div>
                            <span class="copy-replace-icon" id="newTabRedirectIcon">▼</span>
                        </div>
                        <div class="copy-replace-content" id="newTabRedirectContent">
                            <div class="copy-replace-body">
                                <div class="copy-replace-info">
                                    <div class="copy-replace-example">
                                        <strong>New Tab Redirect:</strong><br>
                                        Configure a URL and keyboard shortcut to quickly launch your desired webpage in a new tab.
                                    </div>
                                </div>
                                
                                <div class="copy-replace-input-group">
                                    <label class="copy-replace-input-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        REDIRECT URL:
                                    </label>
                                    <input type="text" id="newTabRedirectUrl" class="copy-replace-input" data-setting="newTabRedirectUrl" placeholder="https://www.google.com/webhp?num=100" title="Enter the complete URL where new tabs should redirect. Must include https:// protocol.">
                                    <div class="copy-replace-help">
                                        Enter the complete URL where new tabs should redirect. Must include https:// protocol.
                                        <br>Example: https://www.google.com/webhp?num=100
                                    </div>
                                </div>
                                
                                <div class="custom-shortcut-container">
                                    <div class="custom-shortcut-label">
                                        <span style="color: #7C3AED; font-size: 18px;">●</span>
                                        New Tab Redirect Shortcut
                                    </div>
                                    <div class="custom-shortcut-input-wrapper">
                                        <input type="text" id="newTabRedirectShortcut" class="custom-shortcut-input" data-setting="newTabRedirectShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for New Tab Redirect">
                                        <div class="shortcut-help">
                                            <span class="help-icon">ℹ️</span>
                                            <div class="help-tooltip">
                                                <strong>New Tab Redirect</strong><br>
                                                Opens the specified URL in a new tab when the shortcut is pressed.<br>
                                                Works globally across all websites and applications.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="shortcut-status"></div>
                                </div>
                                
                                <div class="copy-replace-info" style="margin-top: 30px; border-top: 2px solid #7C3AED; padding-top: 20px;">
                                    <div class="copy-replace-example">
                                        <strong>How it works:</strong><br>
                                        When you open a new tab (Ctrl+T), instead of showing Chrome's default new tab page, you'll be
                                        automatically redirected to your specified URL.
                                    </div>
                                </div>
                                
                                <div class="copy-replace-info">
                                    <div class="copy-replace-example">
                                        <strong>Note:</strong> This feature requires the extension to override Chrome's new tab page. Disable this feature to
                                        restore the default new tab behavior.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Gmail Tools Accordion -->
    <div class="settings-accordion">
        <div class="settings-accordion-header" id="gmailToolsHeader">
            <div class="settings-accordion-title">
                Gmail Tools
            </div>
            <span class="settings-accordion-icon" id="gmailToolsIcon">▼</span>
        </div>
        <div class="settings-accordion-content" id="gmailToolsContent">
            <div class="settings-accordion-body">
                <!-- Select All/Deselect All Toggle -->
                <div class="settings-item settings-item--select-all">
                    <div class="settings-item__content">
                        <div class="settings-item__info">
                            <div class="settings-item__label">Select All / Deselect All</div>
                            <div class="settings-item__description">Toggle all options in this section on or off</div>
                        </div>
                        <div class="settings-item__control">
                            <div class="toggle-switch toggle-switch--active" id="gmailToolsSelectAll" data-accordion="gmailTools"></div>
                        </div>
                    </div>
                </div>
                <div class="settings-group">
                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Reverse Gmail Order</div>
                                <div class="settings-item__description">Reverse email conversation threads to show newest messages at the top for better Gmail workflow</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="reverseGmailOrderToggle" data-setting="reverseGmailOrderEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item settings-item--expanded">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Show Gmail Time</div>
                                <div class="settings-item__description">Format Gmail timestamps with custom date/time format. Shows full date and time instead of Gmail's relative timestamps.</div>
                            </div>
                            <div class="settings-item__control">
                                <button class="settings-button" id="gmailTimeConfigBtn" style="margin-right: 30px;">⚙️ Configure Format</button>
                                <div class="toggle-switch toggle-switch--active" id="showGmailTimeToggle" data-setting="showGmailTimeEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Show Gmail Icons</div>
                                <div class="settings-item__description">Display sender profile icons next to email addresses in Gmail inbox and conversation views for better visual identification</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="showGmailIconsToggle" data-setting="showGmailIconsEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Gmail Thread Expander</div>
                                <div class="settings-item__description">Automatically expand Gmail conversation threads by clicking thread count buttons and expanding individual messages. Provides complete email visibility with smart timing and auto-scroll functionality.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="gmailThreadExpanderToggle" data-setting="gmailThreadExpanderEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Gmail Thread Jump Links</div>
                                <div class="settings-item__description">Display jump links at the top of Gmail threads to quickly navigate between emails. Shows chronological links with time and date for easy navigation within long conversations.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="gmailJumpLinksToggle" data-setting="gmailJumpLinksEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Gmail Enhanced Timestamps</div>
                                <div class="settings-item__description">Enhance Gmail email timestamps with subtle purple borders for better visibility and readability. Improves the visual prominence of timestamps in email conversations.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="gmailEnhancedTimestampsToggle" data-setting="gmailEnhancedTimestampsEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Mass Unsubscribe</div>
                                <div class="settings-item__description">Scan Gmail pages for unsubscribe opportunities and process them in batch. Shows a floating button to review and unsubscribe from multiple mailing lists at once.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="massUnsubscribeToggle" data-setting="massUnsubscribeEnabled"></div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Email Pinner</div>
                                <div class="settings-item__description">Pin important emails for quick access. Adds a pin icon to individual Gmail emails and displays pinned emails in the popup with direct reply links.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="emailPinnerToggle" data-setting="emailPinnerEnabled"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- YouTube Tools Accordion -->
    <div class="settings-accordion">
        <div class="settings-accordion-header" id="youtubeToolsHeader">
            <div class="settings-accordion-title">
                YouTube Tools
            </div>
            <span class="settings-accordion-icon" id="youtubeToolsIcon">▼</span>
        </div>
        <div class="settings-accordion-content" id="youtubeToolsContent">
            <div class="settings-accordion-body">
                <!-- Select All/Deselect All Toggle -->
                <div class="settings-item settings-item--select-all">
                    <div class="settings-item__content">
                        <div class="settings-item__info">
                            <div class="settings-item__label">Select All / Deselect All</div>
                            <div class="settings-item__description">Toggle all options in this section on or off</div>
                        </div>
                        <div class="settings-item__control">
                            <div class="toggle-switch toggle-switch--active" id="youtubeToolsSelectAll" data-accordion="youtubeTools"></div>
                        </div>
                    </div>
                </div>
                <div class="settings-group">
                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">YouTube Ads Skipper</div>
                                <div class="settings-item__description">Automatically skip YouTube ads and remove ad elements from the page for uninterrupted viewing experience</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="youtubeAdsSkipperToggle" data-setting="youtubeAdsSkipperEnabled"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-item settings-item--expanded">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">Video Speed Controller</div>
                                <div class="settings-item__description">Control video playback speed with keyboard shortcuts on YouTube and all video websites. Hover over any video to see speed controls.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="videoSpeedControllerToggle" data-setting="videoSpeedControllerEnabled"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="video-speed-accordion">
                        <div class="video-speed-header" id="videoSpeedHeader">
                            <div class="video-speed-title">
                                <span style="color: #7C3AED; font-size: 16px;">●</span> Speed Controller Configuration
                            </div>
                            <span class="video-speed-icon" id="videoSpeedIcon">▼</span>
                        </div>
                        <div class="video-speed-content" id="videoSpeedContent">
                            <div class="video-speed-body">
                                <div class="video-speed-input-group">
                                    <label class="video-speed-checkbox-label">
                                        <input type="checkbox" id="videoSpeedControllerStartHidden" data-setting="videoSpeedControllerStartHidden">
                                        <span class="video-speed-checkbox-text">Start with overlay hidden</span>
                                    </label>
                                    
                                    <label class="video-speed-checkbox-label">
                                        <input type="checkbox" id="videoSpeedControllerRememberSpeed" data-setting="videoSpeedControllerRememberSpeed">
                                        <span class="video-speed-checkbox-text">Remember speed across videos</span>
                                    </label>
                                    
                                    <label class="video-speed-checkbox-label">
                                        <input type="checkbox" id="videoSpeedControllerAudioSupport" data-setting="videoSpeedControllerAudioSupport">
                                        <span class="video-speed-checkbox-text">Enable for audio elements</span>
                                    </label>
                                </div>
                                
                                <div class="video-speed-input-group">
                                    <label class="video-speed-input-label">Overlay Opacity:</label>
                                    <input type="range" id="videoSpeedControllerOpacity" class="video-speed-range" data-setting="videoSpeedControllerOpacity" min="0.1" max="1" step="0.1" value="0.3">
                                    <span id="videoSpeedOpacityValue" class="video-speed-value">0.3</span>
                                </div>
                                
                                <div class="video-speed-input-group">
                                    <button class="settings-button" id="videoSpeedShortcutsBtn">⚙️ Customize Shortcuts</button>
                                </div>
                                
                                <div class="video-speed-input-group">
                                    <label class="video-speed-input-label">Blacklisted Sites (one per line):</label>
                                    <textarea id="videoSpeedControllerBlacklist" class="video-speed-textarea" data-setting="videoSpeedControllerBlacklist" placeholder="example.com&#10;streaming-site.com&#10;/regex-pattern/" rows="4"></textarea>
                                    <div class="video-speed-help">
                                        Enter domain names or regex patterns (enclosed in /) to exclude sites from video speed control.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">YouTube Screenshot Tool</div>
                                <div class="settings-item__description">Capture screenshots from YouTube videos using the P key or toolbar button. Save to disk and/or copy to clipboard.</div>
                            </div>
                            <div class="settings-item__control">
                                <button class="settings-button" id="screenshotYouTubeConfigBtn" style="margin-right: 30px;">Configure Options</button>
                                <div class="toggle-switch toggle-switch--active" id="screenshotYouTubeToggle" data-setting="screenshotYouTubeEnabled"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">YouTube Thumbnail Viewer</div>
                                <div class="settings-item__description">View all available thumbnail resolutions for any YouTube video. Access via button next to video title.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="youtubeThumbnailViewerToggle" data-setting="youtubeThumbnailViewerEnabled"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">YouTube Frames</div>
                                <div class="settings-item__description">Capture video screenshots with YouTube player frame overlay. F2C button copies to clipboard, F2D button downloads the framed image.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="youtubeFramesToggle" data-setting="youtubeFramesEnabled"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="settings-item__content">
                            <div class="settings-item__info">
                                <div class="settings-item__label">YouTube GIF</div>
                                <div class="settings-item__description">Add GIF download buttons to YouTube video thumbnails. Hover over video preview to generate GIF, then click the GIF icon to download with frame overlay.</div>
                            </div>
                            <div class="settings-item__control">
                                <div class="toggle-switch toggle-switch--active" id="youtubeGifToggle" data-setting="youtubeGifEnabled"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="settings-section">
    <div class="settings-section__title">Import/Export</div>
    
    <div class="settings-group">
        <div class="settings-item">
            <div class="settings-item__content">
                <div class="settings-item__info">
                    <div class="settings-item__label">Export settings</div>
                    <div class="settings-item__description">Export current settings configuration to a file</div>
                </div>
                <div class="settings-item__control">
                    <button class="settings-button" id="exportSettingsBtn">Export Settings</button>
                </div>
            </div>
        </div>

        <div class="settings-item">
            <div class="settings-item__content">
                <div class="settings-item__info">
                    <div class="settings-item__label">Import settings</div>
                    <div class="settings-item__description">Import settings configuration from a file</div>
                </div>
                <div class="settings-item__control">
                    <input type="file" id="importSettingsFile" accept=".json" style="display: none;">
                    <button class="settings-button" id="importSettingsBtn">Import Settings</button>
                </div>
            </div>
        </div>

        <div class="settings-item">
            <div class="settings-item__content">
                <div class="settings-item__info">
                    <div class="settings-item__label">Reset to defaults</div>
                    <div class="settings-item__description">Restore all settings to their original default values</div>
                </div>
                <div class="settings-item__control">
                    <button class="settings-button settings-button--warning" id="resetSettingsBtn">Reset to Defaults</button>
                </div>
            </div>
        </div>

        <div class="settings-item">
            <div class="settings-item__content">
                <div class="settings-item__info">
                    <div class="settings-item__label">Clear all data</div>
                    <div class="settings-item__description">Remove all stored extraction data and cache</div>
                </div>
                <div class="settings-item__control">
                    <button class="settings-button settings-button--danger" id="clearDataBtn">Clear All Data</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Audio manager now loaded in background script for global availability -->
<!-- Include sound preview script for settings screen functionality -->
<script src="../js/pomodoro/sound-preview.js"></script> 