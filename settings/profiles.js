function initializeProfiles() {
    const profilesRepeater = document.getElementById('profiles-repeater');
    const addProfileBtn = document.getElementById('add-profile-btn');
    const rerunOnboardingBtn = document.getElementById('rerun-onboarding-btn');

    const settingsManager = {
        async get(key) {
            return new Promise((resolve) => {
                chrome.storage.local.get(key, (result) => {
                    resolve(result[key]);
                });
            });
        },
        async set(key, value) {
            return new Promise((resolve) => {
                chrome.storage.local.set({ [key]: value }, () => {
                    resolve();
                });
            });
        },
        async getAll() {
            return new Promise((resolve) => {
                chrome.storage.local.get(null, (items) => {
                    resolve(items);
                });
            });
        },
        async setAll(settings) {
            return new Promise((resolve) => {
                chrome.storage.local.set(settings, () => {
                    resolve();
                });
            });
        },
        
        // PROFILE-SAFE METHODS - These methods protect profiles and system data
        async getSettingsOnly() {
            return new Promise((resolve) => {
                chrome.storage.local.get(null, (items) => {
                    // Create a copy excluding profile-related and system keys
                    const settingsOnly = { ...items };
                    
                    // Remove profile-related data to prevent recursive saving
                    delete settingsOnly.profiles;
                    
                    // Remove system flags that shouldn't be included in profiles
                    delete settingsOnly.onboardingCompleted;
                    delete settingsOnly.showOnboardingAfterReload;
                    
                    // Remove temporary/session data
                    delete settingsOnly.extensionVersion;
                    delete settingsOnly.lastUpdateCheck;
                    
                    console.log('🛡️ Profile Protection: Excluded profiles and system flags from settings');
                    resolve(settingsOnly);
                });
            });
        },
        
        async setSettingsOnly(settings) {
            return new Promise((resolve) => {
                // First get current profiles and system data to preserve them
                chrome.storage.local.get(['profiles', 'onboardingCompleted', 'showOnboardingAfterReload'], (systemData) => {
                    
                    // Create safe settings object (exclude any profile/system keys from input)
                    const safeSettings = { ...settings };
                    delete safeSettings.profiles;
                    delete safeSettings.onboardingCompleted;
                    delete safeSettings.showOnboardingAfterReload;
                    
                    // Merge with preserved system data
                    const finalSettings = { ...safeSettings, ...systemData };
                    
                    console.log('🛡️ Profile Protection: Preserving profiles and system flags during settings update');
                    
                    chrome.storage.local.set(finalSettings, () => {
                        resolve();
                    });
                });
            });
        }
    };

    const profilesManager = {
        async getProfiles() {
            return await settingsManager.get('profiles') || [];
        },
        async saveProfiles(profiles) {
            await settingsManager.set('profiles', profiles);
        },
        async addProfile(name) {
            const profiles = await this.getProfiles();
            
            // 🛡️ PROFILE PROTECTION: Use getSettingsOnly() to exclude profiles from saved data
            // This prevents recursive saving where profiles get included in profile data
            const settingsOnly = await settingsManager.getSettingsOnly();
            
            console.log(`🛡️ Profile Protection: Adding profile '${name}' with settings-only data (profiles excluded)`);
            console.log(`📊 Profile Data: Saving ${Object.keys(settingsOnly).length} settings (profiles and system flags excluded)`);
            
            profiles.push({ name, settings: settingsOnly, enabled: true });
            await this.saveProfiles(profiles);
            ui.renderProfiles();
            ui.showProfileNotification(`Profile '${name}' saved successfully.`, 'success');
        },
        async loadProfile(name) {
            const profiles = await this.getProfiles();
            const profile = profiles.find(p => p.name === name);
            if (profile) {
                // 🛡️ PROFILE PROTECTION: Use setSettingsOnly() to preserve current profiles
                // This prevents overwriting profiles when loading a profile
                await settingsManager.setSettingsOnly(profile.settings);
                
                console.log(`🛡️ Profile Protection: Loaded profile '${name}' while preserving current profiles`);
                
                // Instead of reloading, update the UI and show a notification
                if (window.settingsManager) {
                    window.settingsManager.updateUI();
                    ui.showProfileNotification(`Profile '${name}' loaded successfully.`, 'success');
                } else {
                    console.warn('SettingsManager not available to update UI after profile load.');
                    window.location.reload(); // Fallback if SettingsManager is not available
                }
            } else {
                ui.showProfileNotification(`Profile '${name}' not found.`, 'error');
            }
        },
        async deleteProfile(name) {
            let profiles = await this.getProfiles();
            const initialLength = profiles.length;
            profiles = profiles.filter(p => p.name !== name);
            await this.saveProfiles(profiles);
            ui.renderProfiles();
            if (profiles.length < initialLength) {
                ui.showProfileNotification(`Profile '${name}' deleted successfully.`, 'success');
            } else {
                ui.showProfileNotification(`Profile '${name}' not found.`, 'error');
            }
        },
        async toggleProfile(name, enabled) {
            let profiles = await this.getProfiles();
            const profile = profiles.find(p => p.name === name);
            if (profile) {
                profile.enabled = enabled;
                await this.saveProfiles(profiles);
            }
        },
        
        // 🛡️ PROFILE PROTECTION LAYER
        // This is the ONLY method that should ever delete profiles
        async clearAllData() {
            // Show strong confirmation dialog
            const confirmed = confirm(
                '⚠️ DANGER: This will delete ALL extension data including:\n\n' +
                '• All saved profiles\n' +
                '• All settings\n' +
                '• All user data\n\n' +
                'This action cannot be undone!\n\n' +
                'Are you absolutely sure you want to continue?'
            );
            
            if (!confirmed) {
                console.log('🛡️ Profile Protection: Clear all data cancelled by user');
                return false;
            }
            
            // Second confirmation for profiles specifically
            const profileConfirmed = confirm(
                '🛡️ FINAL WARNING: This will permanently delete all saved profiles!\n\n' +
                'This is the ONLY way profiles can be deleted.\n\n' +
                'Click OK to permanently delete everything, or Cancel to keep your data.'
            );
            
            if (!profileConfirmed) {
                console.log('🛡️ Profile Protection: Profile deletion cancelled by user');
                return false;
            }
            
            try {
                console.log('🛡️ Profile Protection: User confirmed deletion of ALL data including profiles');
                
                // Clear ALL storage data
                await chrome.storage.local.clear();
                await chrome.storage.sync.clear();
                
                console.log('🛡️ Profile Protection: ALL data cleared including profiles');
                ui.showProfileNotification('All data including profiles cleared successfully.', 'success');
                
                // Reload UI to reflect changes
                ui.renderProfiles();
                if (window.settingsManager) {
                    window.settingsManager.updateUI();
                }
                
                return true;
            } catch (error) {
                console.error('❌ Error clearing all data:', error);
                ui.showProfileNotification('Error clearing data: ' + error.message, 'error');
                return false;
            }
        },
        
        // 🛡️ PROFILE PROTECTION: Verification method to check if profiles are safe
        async verifyProfileProtection() {
            const profiles = await this.getProfiles();
            const profileCount = profiles.length;
            
            console.log(`🛡️ Profile Protection: Verified ${profileCount} profiles are safe`);
            return profileCount;
        }
    };

    const ui = {
        init() {
            this.renderProfiles();
            addProfileBtn.addEventListener('click', () => this.addProfile());
            rerunOnboardingBtn.addEventListener('click', () => this.rerunOnboarding());
            document.getElementById('profilesHeader').addEventListener('click', this.toggleAccordion);
            
            // 🛡️ PROFILE PROTECTION: Connect existing "Clear All Data" button to protected clearAllData function
            const clearAllDataBtn = document.getElementById('clear-all-data-btn');
            if (clearAllDataBtn) {
                clearAllDataBtn.addEventListener('click', () => profilesManager.clearAllData());
                console.log('🛡️ Profile Protection: Connected existing Clear All Data button to protected function');
            }
        },
        toggleAccordion() {
            const content = document.getElementById('profilesContent');
            const icon = document.getElementById('profilesIcon');
            content.classList.toggle('expanded');
            icon.classList.toggle('expanded');
        },
        async renderProfiles() {
            const profiles = await profilesManager.getProfiles();
            profilesRepeater.innerHTML = '';
            profiles.forEach(profile => {
                const profileItem = this.createProfileItem(profile.name, profile.enabled);
                profilesRepeater.appendChild(profileItem);
            });
        },
        createProfileItem(name = '', enabled = true) {
            const profileItem = document.createElement('div');
            profileItem.className = 'profile-item';

            const nameInput = document.createElement('input');
            nameInput.type = 'text';
            nameInput.className = 'profile-name-input';
            nameInput.value = name;
            nameInput.placeholder = 'Profile Name';

            const saveBtn = document.createElement('button');
            saveBtn.textContent = 'Save';
            saveBtn.className = 'profile-save-btn';
            saveBtn.addEventListener('click', () => {
                const newName = nameInput.value.trim();
                if (newName) {
                    profilesManager.addProfile(newName);
                }
            });

            const loadBtn = document.createElement('button');
            loadBtn.textContent = 'Load';
            loadBtn.className = 'profile-load-btn';
            loadBtn.addEventListener('click', () => {
                const profileName = nameInput.value.trim();
                if (profileName) {
                    profilesManager.loadProfile(profileName);
                }
            });

            const deleteBtn = document.createElement('button');
            deleteBtn.textContent = 'Delete';
            deleteBtn.className = 'profile-delete-btn';
            deleteBtn.addEventListener('click', () => {
                const profileName = nameInput.value.trim();
                if (profileName && confirm(`Are you sure you want to delete the '${profileName}' profile?`)) {
                    profilesManager.deleteProfile(profileName);
                }
            });

            const toggle = this.createToggle(enabled);
            toggle.addEventListener('click', () => {
                const profileName = nameInput.value.trim();
                if(profileName) {
                    profilesManager.toggleProfile(profileName, toggle.classList.contains('toggle-switch--active'));
                }
            });

            profileItem.appendChild(nameInput);
            profileItem.appendChild(saveBtn);
            profileItem.appendChild(loadBtn);
            profileItem.appendChild(deleteBtn);
            profileItem.appendChild(toggle);

            return profileItem;
        },
        createToggle(initialState) {
            const toggle = document.createElement('div');
            toggle.className = 'toggle-switch';
            if (initialState) {
                toggle.classList.add('toggle-switch--active');
            }
            toggle.addEventListener('click', () => {
                toggle.classList.toggle('toggle-switch--active');
            });
            return toggle;
        },
        addProfile() {
            const profileItem = this.createProfileItem();
            profilesRepeater.appendChild(profileItem);
        },
        showProfileNotification(message, type) {
            // Remove any existing notifications
            document.querySelectorAll('.profile-notification').forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.textContent = message;
            notification.className = 'profile-notification';

            let backgroundColor = 'rgba(0, 0, 0, 0.9)';
            let borderColor = 'rgba(124, 58, 237, 0.3)';
            let textColor = '#fff';

            if (type === 'success') {
                backgroundColor = 'linear-gradient(135deg, #10b981, #34d399)';
                borderColor = '#059669';
            } else if (type === 'error') {
                backgroundColor = 'linear-gradient(135deg, #ef4444, #f87171)';
                borderColor = '#dc2626';
            }

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${backgroundColor};
                color: ${textColor};
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 13px;
                font-weight: 600;
                z-index: 10000;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
                border: 1px solid ${borderColor};
                animation: profileNotificationFadeIn 0.2s ease-out forwards;
                white-space: nowrap;
            `;

            // Add animation keyframes if not already added
            if (!document.getElementById('profile-notification-styles')) {
                const style = document.createElement('style');
                style.id = 'profile-notification-styles';
                style.textContent = `
                    @keyframes profileNotificationFadeIn {
                        from { opacity: 0; transform: translateX(100px) scale(0.8); }
                        to { opacity: 1; transform: translateX(0) scale(1); }
                    }
                    @keyframes profileNotificationFadeOut {
                        from { opacity: 1; transform: translateX(0) scale(1); }
                        to { opacity: 0; transform: translateX(100px) scale(0.8); }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'profileNotificationFadeOut 0.2s ease-in forwards';
                    setTimeout(() => {
                        notification.remove();
                    }, 200);
                }
            }, 3000);
        },
        async rerunOnboarding() {
            try {
                // 🛡️ PROFILE PROTECTION: Verify profiles before reset
                const profileCount = await profilesManager.verifyProfileProtection();
                
                // Show confirmation before resetting onboarding
                if (!confirm(
                    `🛡️ PROFILE PROTECTION: Your ${profileCount} saved profiles will be preserved.\n\n` +
                    'This will reset ALL settings and restart the setup wizard.\n' +
                    'This will reload the extension.\n\n' +
                    'Your profiles are SAFE and will not be deleted.\n\n' +
                    'Continue?'
                )) {
                    return;
                }
                
                console.log('🔄 Starting comprehensive onboarding reset and reload...');
                console.log(`🛡️ Profile Protection: ${profileCount} profiles will be preserved during reset`);
                
                // Step 1: Reset ALL toggle settings using comprehensive list (same as seo-ninja mode)
                console.log('📊 Resetting all toggle settings...');
                const allSettings = await this.getAllAvailableSettings();
                const resetSettings = {};
                
                // Set all toggle settings to false
                Object.keys(allSettings).forEach(key => {
                    resetSettings[key] = false;
                });
                
                console.log(`🔧 Resetting ${Object.keys(resetSettings).length} toggle settings to false`);
                
                // Get current settings to preserve non-toggle data
                const currentSettingsResult = await chrome.storage.local.get('gmbExtractorSettings');
                const existingSettings = currentSettingsResult.gmbExtractorSettings || {};
                
                // Merge - this will turn off all toggles but keep other user data
                const updatedSettings = { ...existingSettings, ...resetSettings };
                
                // Save reset settings
                await chrome.storage.local.set({ 
                    gmbExtractorSettings: updatedSettings
                });
                
                // Step 2: Remove onboarding completion flag
                await chrome.storage.local.remove(['onboardingCompleted']);
                
                // Step 3: Set flag to show onboarding after reload
                await chrome.storage.local.set({ 
                    showOnboardingAfterReload: true 
                });
                
                // 🛡️ PROFILE PROTECTION: Verify profiles survived the reset
                const profileCountAfter = await profilesManager.verifyProfileProtection();
                if (profileCountAfter === profileCount) {
                    console.log(`✅ Profile Protection: Confirmed ${profileCountAfter} profiles preserved during reset`);
                } else {
                    console.error(`❌ Profile Protection: Profile count mismatch! Before: ${profileCount}, After: ${profileCountAfter}`);
                }
                
                console.log('✅ Settings reset complete, triggering extension reload...');
                
                // Step 4: Reload extension (this will close settings and reload all content scripts)
                if (window.GMBExtensionReload && window.GMBExtensionReload.reload) {
                    window.GMBExtensionReload.reload();
                } else {
                    // Fallback: show message to user
                    this.showProfileNotification('Settings reset! Please reload the extension manually from chrome://extensions/', 'success');
                    console.error('Extension reload utility not available');
                }
                
            } catch (error) {
                console.error('❌ Error during onboarding reset:', error);
                this.showProfileNotification('Error resetting onboarding: ' + error.message, 'error');
            }
        },
        
        // Helper function to get all available settings (same comprehensive list used by seo-ninja mode)
        async getAllAvailableSettings() {
            return {
                // Quick Actions
                htagsEnabled: true,
                headingStructureEnabled: true,
                showLinksEnabled: true,
                showHiddenEnabled: true,
                schemaEnabled: true,
                imagesEnabled: true,
                metadataEnabled: true,
                utmBuilderEnabled: true,
                pageStructureEnabled: true,
                copyElementEnabled: true,
                linksExtractorEnabled: true,
                responsiveEnabled: true,
                seoTestsEnabled: true,
                trackerDetectionEnabled: true,
                keywordEnabled: true,
                boldFromSerpEnabled: true,
                youtubeEmbedScraperEnabled: true,
                keyboardShortcutsEnabled: true,
                
                // Extras Tools
                locationChangerEnabled: true,
                serpNumbering: true,
                sponsoredHighlighter: true,
                searchResultStats: true,
                citationHunter: true,
                searchNAPInjector: true,
                currentLocationDisplayEnabled: true,
                trackedDomainsEnabled: true,
                openSingleListing: true,
                textTransformersEnabled: true,
                
                // Gmail Tools
                reverseGmailOrderEnabled: true,
                showGmailTimeEnabled: true,
                showGmailIconsEnabled: true,
                massUnsubscribeEnabled: true,
                gmailEnhancedTimestampsEnabled: true,
                emailPinnerEnabled: true,
                gmailThreadExpanderEnabled: true,
                
                // YouTube Tools
                screenshotYouTubeEnabled: true,
                youtubeGifEnabled: true,
                youtubeFramesEnabled: true,
                youtubeThumbnailViewerEnabled: true,
                youtubeAdsSkipperEnabled: true,
                screenshotKeyEnabled: true,
                
                // Productivity Tools
                pomodoroEnabled: true,
                quickTimerEnabled: true,
                tasksEnabled: true,
                alertsEnabled: true,
                videoSpeedControllerEnabled: true,
                notificationUtilityEnabled: true,
                
                // UTM & URL Tools
                utmTrackingCleanerEnabled: true,
                utmCopyCleanEnabled: true,
                utmCleanAndGoEnabled: true,
                
                // Browser Tools
                minimalReaderEnabled: true,
                dragSelectLinksEnabled: true,
                screenshotToolEnabled: true,
                
                // Additional Tools
                topUrlsCopierEnabled: true,
                colorPickerEnabled: true,
                
                // Global Settings
                globalShortcutsEnabled: true,
                clickToCopyEnabled: true,
                copyReplaceEnabled: true,
                autoCleanAndTitleEnabled: true,
                showhiddenAutoDetectionEnabled: true
            };
        }
    };

    ui.init();
}
