// Gmail Reverse Order - Simple CSS injection approach
(function() {
  'use strict';
  
  // Prevent multiple loads
  if (window.GMBReverseGmailOrderLoaded) return;
  window.GMBReverseGmailOrderLoaded = true;
  
  let isEnabled = true; // Default to ON
  let cssLink = null;
  
  // Check if we're on Gmail
  function isGmailPage() {
    return window.location.hostname === 'mail.google.com';
  }
  
  // Load settings on initialization
  async function loadSettings() {
    try {
      const result = await chrome.storage.local.get('gmbExtractorSettings');
      const settings = result.gmbExtractorSettings || {};
      isEnabled = settings.reverseGmailOrderEnabled !== false; // Default to true if not set
      
      if (isEnabled) {
        injectCSS();
        addReplyButtonListeners();
      } else {
        removeCSS();
      }
    } catch (error) {
      // Default to enabled if error loading settings
      injectCSS();
      addReplyButtonListeners();
    }
  }
  
  // Inject the CSS to reverse Gmail order
  function injectCSS() {
    if (!isGmailPage()) {
      return;
    }
    
    // Remove existing CSS if any
    removeCSS();
    
    // Create and inject CSS link
    cssLink = document.createElement('link');
    cssLink.rel = 'stylesheet';
    cssLink.type = 'text/css';
    cssLink.href = chrome.runtime.getURL('css/reverse-gmail-order.css');
    cssLink.id = 'gmb-reverse-gmail-order-css';
    
    (document.head || document.documentElement).appendChild(cssLink);
  }
  
  // Remove the CSS
  function removeCSS() {
    if (cssLink && cssLink.parentNode) {
      cssLink.parentNode.removeChild(cssLink);
      cssLink = null;
    }
    
    // Also remove by ID in case of multiple instances
    const existingLink = document.getElementById('gmb-reverse-gmail-order-css');
    if (existingLink && existingLink.parentNode) {
      existingLink.parentNode.removeChild(existingLink);
    }
  }
  
  // Add click listener for Reply, Reply All, and Forward buttons
  function addReplyButtonListeners() {
    if (!isGmailPage() || !isEnabled) return;
    
    // Listen for clicks on the entire document
    document.addEventListener('click', function(event) {
      const target = event.target;
      
      // Check if clicked element is a reply button (Reply, Reply All, or Forward)
      if (target && target.classList && target.classList.contains('ams')) {
                 // Check for specific classes: bkI (Reply All), bkH (Reply), bkG (Forward)
         if (target.classList.contains('bkI') || target.classList.contains('bkH') || target.classList.contains('bkG')) {
           // Delay to allow Gmail to process the click first
           setTimeout(() => {
             // Try to find and focus the compose/reply textarea
             const composeElements = [
               '[role="textbox"]',           // Modern Gmail compose area
               'div[contenteditable="true"]', // Compose textarea
               '.Am.Al.editable',            // Reply field
               '.editable',                  // Generic editable field
               'textarea[name="body"]',      // Fallback textarea
               '.gmail_quote'                // Area near quote (to get close)
             ];
             
             let focused = false;
             composeElements.forEach((selector) => {
               if (!focused) {
                 const element = document.querySelector(selector);
                 if (element) {
                   element.focus();
                   element.scrollIntoView({ behavior: 'instant', block: 'start' });
                   focused = true;
                 }
               }
             });
             
             // Fallback: Simulate Gmail keyboard shortcuts
             if (!focused) {
               // Simulate 'c' key press (Gmail shortcut for compose)
               const cEvent = new KeyboardEvent('keydown', {
                 key: 'c',
                 code: 'KeyC',
                 bubbles: true,
                 cancelable: true
               });
               document.dispatchEvent(cEvent);
               
               // Also try Tab key to focus on compose area
               setTimeout(() => {
                 const tabEvent = new KeyboardEvent('keydown', {
                   key: 'Tab',
                   code: 'Tab',
                   bubbles: true,
                   cancelable: true
                 });
                 document.dispatchEvent(tabEvent);
               }, 50);
             }
           }, 150);
        }
      }
    }, true); // Use capture phase to catch the event early
  }

  // Listen for settings changes
  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'updateSettings' && request.settings) {
        const newEnabled = request.settings.reverseGmailOrderEnabled !== false;
        
        if (newEnabled !== isEnabled) {
          isEnabled = newEnabled;
          
          if (isEnabled) {
            injectCSS();
            addReplyButtonListeners();
          } else {
            removeCSS();
          }
        }
        
        sendResponse({ success: true });
      }
    });
  }
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadSettings);
  } else {
    loadSettings();
  }
})(); 