// Video Speed Controller - Control video playback speed on any website
(function() {
    'use strict';
    
    let isEnabled = false; // Default to false, will be loaded from settings
    let showOverlay = true; // Always show overlay when enabled
    let controllerOpacity = 0.3;
    let startHidden = false;
    let rememberSpeed = false;
    let audioSupport = false;
    let lastSpeed = 1.0;
    let keyBindings = [];
    let blacklist = '';
    let debugMode = false;
    
    
    // Video controller instances
    const controllers = new Map();
    
    // Initialize settings
    async function loadSettings() {
        try {
            // Video speed controller settings are ALL in local storage now
            const localResult = await chrome.storage.local.get('gmbExtractorLargeSettings');
            const settings = localResult.gmbExtractorLargeSettings || {};
            
            // Screenshot YouTube settings are in sync storage
            const syncResult = await chrome.storage.sync.get(['gmbExtractorSettings']);
            const syncSettings = syncResult.gmbExtractorSettings || {};
            
            
            // Explicit boolean checks following extension pattern
            // These default to true in settings.js for new users
            isEnabled = settings.videoSpeedControllerEnabled !== false; // Default true from settings.js
            showOverlay = true; // Always show overlay when enabled
            debugMode = settings.debugMode === true;
            
            // Load other settings with proper defaults
            controllerOpacity = settings.videoSpeedControllerOpacity || 0.3;
            startHidden = settings.videoSpeedControllerStartHidden === true;
            rememberSpeed = settings.videoSpeedControllerRememberSpeed === true;
            audioSupport = settings.videoSpeedControllerAudioSupport === true;
            lastSpeed = parseFloat(settings.videoSpeedControllerLastSpeed) || 1.0;
            keyBindings = settings.videoSpeedControllerKeyBindings || getDefaultKeyBindings();
            blacklist = settings.videoSpeedControllerBlacklist || '';
            
            
            if (debugMode) {
                console.log('Video Speed Controller: Settings loaded', {
                    enabled: isEnabled,
                    debugMode: debugMode,
                    opacity: controllerOpacity,
                    lastSpeed: lastSpeed,
                    rememberSpeed: rememberSpeed,
                    keyBindings: keyBindings,
                    blacklisted: isBlacklisted()
                });
            }
            
            if (isEnabled && !isBlacklisted()) {
                if (debugMode) {
                    console.log('Video Speed Controller: Conditions met, initializing...');
                }
                initializeVideoSpeedController();
            } else {
                if (debugMode) {
                    console.log('Video Speed Controller: Not initializing', {
                        enabled: isEnabled,
                        blacklisted: isBlacklisted()
                    });
                }
                disableVideoSpeedController();
            }
            
        } catch (error) {
            console.error('Video Speed Controller: Error loading settings:', error);
            // Fail safe to disabled per golden rules
            isEnabled = false;
            keyBindings = getDefaultKeyBindings();
        }
    }
    
    function getDefaultKeyBindings() {
        return [
            { action: 'display', key: 86, value: 0, force: false }, // V
            { action: 'slower', key: 83, value: 0.1, force: false }, // S
            { action: 'faster', key: 68, value: 0.1, force: false }, // D
            { action: 'rewind', key: 90, value: 10, force: false }, // Z
            { action: 'advance', key: 88, value: 10, force: false }, // X
            { action: 'preferred', key: 71, value: 1.8, force: true } // G
        ];
    }
    
    function isBlacklisted() {
        if (!blacklist) return false;
        
        const lines = blacklist.split('\n').map(line => line.trim()).filter(line => line);
        const currentUrl = window.location.href;
        
        return lines.some(pattern => {
            if (!pattern) return false;
            try {
                if (pattern.startsWith('/') && pattern.endsWith('/')) {
                    // Regex pattern
                    const regex = new RegExp(pattern.slice(1, -1));
                    return regex.test(currentUrl);
                } else {
                    // Simple string match
                    return currentUrl.includes(pattern);
                }
            } catch (e) {
                return false;
            }
        });
    }
    
    // Enhanced settings persistence with proper storage destinations
    async function saveSpeedToStorage(speed, retries = 3) {
        if (typeof chrome === 'undefined' || !chrome.storage || !chrome.runtime?.id) {
            if (debugMode) {
                console.warn('Video Speed Controller: Chrome storage not available or extension context invalidated');
            }
            return;
        }
        
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                // ALL video speed settings are in local storage now
                const localResult = await chrome.storage.local.get('gmbExtractorLargeSettings');
                const localSettings = localResult.gmbExtractorLargeSettings || {};
                
                // Update the speed in local settings
                localSettings.videoSpeedControllerLastSpeed = speed;
                
                // Save to local storage only
                await chrome.storage.local.set({ gmbExtractorLargeSettings: localSettings });
                
                if (debugMode) {
                    console.log('Video Speed Controller: Speed saved successfully:', speed);
                }
                return; // Success
                
            } catch (error) {
                console.error(`Video Speed Controller: Save attempt ${attempt} failed:`, error);
                if (attempt === retries) {
                    if (debugMode) {
                        console.error('Video Speed Controller: Failed to save after retries:', error);
                    }
                    return;
                }
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 500 * attempt));
            }
        }
    }
    
    function initializeVideoSpeedController() {
        if (!isEnabled || isBlacklisted()) {
            if (debugMode) {
                console.log('Video Speed Controller: Initialization blocked', {
                    enabled: isEnabled,
                    blacklisted: isBlacklisted()
                });
            }
            return;
        }
        
        // Initialize styles
        injectStyles();
        
        // Initialize keyboard listeners
        initializeKeyboardListeners();
        
        // Find and process existing videos
        processExistingVideos();
        
        // Set up mutation observer for new videos
        setupMutationObserver();
        
        if (debugMode) {
            console.log('Video Speed Controller: Initialization complete!');
        }
    }
    
    function disableVideoSpeedController() {
        // Remove all controllers
        controllers.forEach((controller, video) => {
            removeController(video);
        });
        controllers.clear();
        
        // Remove styles
        const styleElement = document.getElementById('vsc-styles');
        if (styleElement) {
            styleElement.remove();
        }
        
        // Remove keyboard listeners from all targets
        document.removeEventListener('keydown', handleKeydown, { capture: true });
        window.removeEventListener('keydown', handleKeydown, { capture: true });
        if (document.documentElement) {
            document.documentElement.removeEventListener('keydown', handleKeydown, { capture: true });
        }
        
        if (debugMode) {
            console.log('Video Speed Controller: Disabled');
        }
    }
    
    function injectStyles() {
        // Remove existing styles
        const existingStyle = document.getElementById('vsc-styles');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        // Inject new styles
        const style = document.createElement('style');
        style.id = 'vsc-styles';
        style.textContent = `
            .vsc-controller {
                position: absolute;
                z-index: 9999999;
            }
            .vsc-nosource { display: none !important; }
            .vsc-hidden { display: none !important; }
            .vsc-manual { visibility: visible !important; opacity: 1 !important; }
            video.vcs-dragging { border: 2px dashed #7C3AED !important; }
        `;
        document.head.appendChild(style);
    }
    
    function processExistingVideos() {
        const selector = audioSupport ? 'video, audio' : 'video';
        const mediaElements = document.querySelectorAll(selector);
        
        mediaElements.forEach(media => {
            if (!controllers.has(media)) {
                createController(media);
            }
        });
    }
    
    function setupMutationObserver() {
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                // Check added nodes
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { // Element node
                        checkForMedia(node, true);
                    }
                });
                
                // Check removed nodes
                mutation.removedNodes.forEach(node => {
                    if (node.nodeType === 1) {
                        checkForMedia(node, false);
                    }
                });
            });
        });
        
        // Start observing the entire document body
        if (document.body) {
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        } else {
            // Wait for body to be available
            const bodyObserver = new MutationObserver(() => {
                if (document.body) {
                    bodyObserver.disconnect();
                    observer.observe(document.body, {
                        childList: true,
                        subtree: true
                    });
                }
            });
            bodyObserver.observe(document.documentElement, {
                childList: true,
                subtree: true
            });
        }
    }
    
    function checkForMedia(element, added) {
        if (!element || !element.querySelectorAll) return;
        
        const tagName = element.tagName;
        if (tagName === 'VIDEO' || (tagName === 'AUDIO' && audioSupport)) {
            if (added && !controllers.has(element)) {
                createController(element);
            } else if (!added && controllers.has(element)) {
                removeController(element);
            }
        }
        
        // Check children
        const selector = audioSupport ? 'video, audio' : 'video';
        const mediaElements = element.querySelectorAll(selector);
        mediaElements.forEach(media => {
            if (added && !controllers.has(media)) {
                createController(media);
            } else if (!added && controllers.has(media)) {
                removeController(media);
            }
        });
    }
    
    function createController(media) {
        if (controllers.has(media)) return;
        
        const controller = new VideoController(media);
        controllers.set(media, controller);
        
        if (debugMode) {
            console.log('Video Speed Controller: Created controller for video', {
                src: media.currentSrc,
                controllersCount: controllers.size
            });
        }
    }
    
    function removeController(media) {
        const controller = controllers.get(media);
        if (controller) {
            controller.remove();
            controllers.delete(media);
        }
    }
    
    // Video Controller Class
    function VideoController(media) {
        this.media = media;
        this.id = Math.random().toString(36).substr(2, 9);
        
        // Speed Guardian System
        this.userInitiatedChange = false;
        this.desiredSpeed = 1.0;
        this.speedGuardianInterval = null;
        this.lastKnownSpeed = 1.0;
        this.speedRestoreAttempts = 0;
        this.maxRestoreAttempts = 5;
        this.wasAdPlaying = false;
        this.srcChangeInterval = null;
        
        // Initialize speed based on settings
        if (rememberSpeed) {
            // Use last global speed when remember speed is enabled
            this.desiredSpeed = lastSpeed;
        } else {
            // Don't remember speed - use preferred speed as default
            const preferredBinding = keyBindings.find(kb => kb.action === 'preferred');
            this.desiredSpeed = preferredBinding ? preferredBinding.value : 1.0;
        }
        
        // Store the initial desired speed
        this.speed = this.desiredSpeed;
        
        // Apply the speed to the video with enhanced timing
        this.applySpeedWithRetry();
        
        this.initializeController();
        this.attachEventListeners();
        this.startSpeedGuardian();
    }
    
    VideoController.prototype.initializeController = function() {
        // Always create visual overlay when controller is created
        const wrapper = document.createElement('div');
        wrapper.className = 'vsc-controller';
        wrapper.dataset.vscid = this.id;
        
        if (!this.media.currentSrc) {
            wrapper.classList.add('vsc-nosource');
        }
        
        if (startHidden) {
            wrapper.classList.add('vsc-hidden');
        }
        
        // Create shadow root for style isolation
        const shadow = wrapper.attachShadow({ mode: 'open' });
        
        // Create controller HTML
        const speed = parseFloat(this.media.playbackRate).toFixed(2);
        shadow.innerHTML = `
            <style>
                :host {
                    all: initial;
                    position: absolute;
                    top: 10px;
                    left: 10px;
                    z-index: 9999999;
                }
                #controller {
                    background: #0a0a0a;
                    color: #d1d5db;
                    border: 2px solid #7C3AED;
                    border-radius: 8px;
                    padding: 8px;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                    font-size: 14px;
                    cursor: move;
                    user-select: none;
                    opacity: ${controllerOpacity};
                    transition: opacity 0.2s;
                }
                #controller:hover {
                    opacity: 0.9;
                }
                #controller.dragging {
                    cursor: grabbing;
                    opacity: 0.7;
                }
                .speed-display {
                    font-weight: bold;
                    font-size: 16px;
                    color: #ffffff;
                    margin-right: 8px;
                }
                #controls {
                    display: none;
                    margin-left: 8px;
                }
                #controller:hover #controls {
                    display: inline-block;
                }
                button {
                    background: #1a1a1a;
                    color: #d1d5db;
                    border: 1px solid #7C3AED;
                    border-radius: 4px;
                    padding: 4px 8px;
                    margin: 0 2px;
                    cursor: pointer;
                    font-size: 12px;
                    transition: all 0.2s;
                }
                button:hover {
                    background: #7C3AED;
                    color: #fff;
                }
                button:active {
                    transform: scale(0.95);
                }
                .hide-button {
                    opacity: 0.6;
                }
            </style>
            <div id="controller">
                <span class="speed-display" data-action="drag">${speed}x</span>
                <span id="controls">
                    <button data-action="rewind" title="Rewind">⏪</button>
                    <button data-action="slower" title="Slower">−</button>
                    <button data-action="faster" title="Faster">+</button>
                    <button data-action="advance" title="Advance">⏩</button>
                    <button data-action="display" class="hide-button" title="Hide">✕</button>
                </span>
            </div>
        `;
        
        // Store references
        this.wrapper = wrapper;
        this.shadow = shadow;
        this.controller = shadow.querySelector('#controller');
        this.speedDisplay = shadow.querySelector('.speed-display');
        
        // Add event listeners to shadow DOM elements
        const buttons = shadow.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = e.target.dataset.action;
                this.handleAction(action);
            });
        });
        
        // Add drag functionality
        this.speedDisplay.addEventListener('mousedown', (e) => {
            this.handleDrag(e);
        });
        
        // Position the controller
        this.positionController();
        
        // Add to DOM
        if (this.media.parentElement) {
            this.media.parentElement.appendChild(wrapper);
        }
    };
    
    VideoController.prototype.positionController = function() {
        if (!this.wrapper) return;
        
        // Find the best parent element for positioning
        let parentElement = this.media.parentElement;
        
        // For YouTube and other video players that use wrapper divs
        while (parentElement && parentElement !== document.body) {
            const position = window.getComputedStyle(parentElement).position;
            if (position === 'relative' || position === 'absolute' || position === 'fixed') {
                break;
            }
            parentElement = parentElement.parentElement;
        }
        
        // If no positioned parent found, use the video's parent
        if (!parentElement || parentElement === document.body) {
            parentElement = this.media.parentElement;
        }
        
        // Ensure parent is positioned
        if (parentElement && window.getComputedStyle(parentElement).position === 'static') {
            parentElement.style.position = 'relative';
        }
        
        // Position the wrapper absolutely within the parent
        this.wrapper.style.cssText = `
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 2147483647;
        `;
        
        // Move wrapper to the correct parent if needed
        if (this.wrapper.parentElement !== parentElement) {
            parentElement.appendChild(this.wrapper);
        }
    };
    
    // Speed Guardian System Methods
    VideoController.prototype.applySpeedWithRetry = function() {
        const applySpeed = () => {
            if (this.media.readyState >= 1) { // HAVE_METADATA
                this.userInitiatedChange = true;
                this.media.playbackRate = this.desiredSpeed;
                this.lastKnownSpeed = this.desiredSpeed;
                this.userInitiatedChange = false;
                
                if (debugMode) {
                    console.log('Video Speed Controller: Speed applied successfully', this.desiredSpeed);
                }
            } else {
                // Retry after a short delay
                setTimeout(() => {
                    if (this.speedRestoreAttempts < this.maxRestoreAttempts) {
                        this.speedRestoreAttempts++;
                        this.applySpeedWithRetry();
                    }
                }, 100);
            }
        };
        
        // Apply immediately if possible, otherwise wait for metadata
        if (this.media.readyState >= 1) {
            applySpeed();
        } else {
            const setInitialSpeed = () => {
                applySpeed();
                this.media.removeEventListener('loadedmetadata', setInitialSpeed);
            };
            this.media.addEventListener('loadedmetadata', setInitialSpeed);
        }
    };
    
    VideoController.prototype.startSpeedGuardian = function() {
        // Enhanced speed guardian with ad detection
        this.speedGuardianInterval = setInterval(() => {
            const currentSpeed = this.media.playbackRate;
            const speedDifference = Math.abs(currentSpeed - this.desiredSpeed);
            
            // If the speed was changed externally (not by user), restore it
            if (speedDifference > 0.01 && !this.userInitiatedChange) {
                if (debugMode) {
                    console.log('Video Speed Controller: External speed change detected, restoring', {
                        current: currentSpeed,
                        desired: this.desiredSpeed
                    });
                }
                this.restoreDesiredSpeed();
            }
            
            // Enhanced ad detection for YouTube and other platforms
            this.detectAndHandleAds();
        }, 500); // Check every 500ms
        
        // Listen for video state changes that might reset speed
        this.media.addEventListener('loadstart', () => {
            setTimeout(() => this.restoreDesiredSpeed(), 100);
        });
        
        this.media.addEventListener('playing', () => {
            setTimeout(() => this.restoreDesiredSpeed(), 100);
        });
        
        this.media.addEventListener('seeked', () => {
            setTimeout(() => this.restoreDesiredSpeed(), 50);
        });
        
        // Enhanced ad-specific event listeners
        this.media.addEventListener('ended', () => {
            // Video ended - might be ad ending, restore speed after delay
            setTimeout(() => this.restoreDesiredSpeed(), 200);
        });
        
        this.media.addEventListener('loadeddata', () => {
            // New video data loaded - might be after ad, restore speed
            setTimeout(() => this.restoreDesiredSpeed(), 100);
        });
        
        this.media.addEventListener('durationchange', () => {
            // Duration changed - might be ad to content transition
            setTimeout(() => this.restoreDesiredSpeed(), 100);
        });
        
        // Monitor for sudden src changes (common in ad transitions)
        this.lastSrc = this.media.currentSrc;
        this.srcChangeInterval = setInterval(() => {
            if (this.media.currentSrc !== this.lastSrc) {
                this.lastSrc = this.media.currentSrc;
                if (debugMode) {
                    console.log('Video Speed Controller: Video source changed, restoring speed');
                }
                setTimeout(() => this.restoreDesiredSpeed(), 150);
            }
        }, 250);
    };
    
    VideoController.prototype.stopSpeedGuardian = function() {
        if (this.speedGuardianInterval) {
            clearInterval(this.speedGuardianInterval);
            this.speedGuardianInterval = null;
        }
        if (this.srcChangeInterval) {
            clearInterval(this.srcChangeInterval);
            this.srcChangeInterval = null;
        }
    };
    
    VideoController.prototype.detectAndHandleAds = function() {
        // Platform-specific ad detection logic
        const hostname = window.location.hostname;
        
        if (hostname.includes('youtube.com')) {
            this.handleYouTubeAds();
        } else if (hostname.includes('vimeo.com')) {
            this.handleVimeoAds();
        } else {
            this.handleGenericAds();
        }
    };
    
    VideoController.prototype.handleYouTubeAds = function() {
        // YouTube-specific ad detection
        const adOverlay = document.querySelector('.ytp-ad-overlay-container');
        const adPlayer = document.querySelector('.ytp-ad-player-overlay');
        const skipButton = document.querySelector('.ytp-ad-skip-button');
        const adText = document.querySelector('.ytp-ad-text');
        
        // Check if ad is currently playing
        const isAdPlaying = adOverlay || adPlayer || skipButton || adText;
        
        if (isAdPlaying && !this.wasAdPlaying) {
            // Ad just started
            this.wasAdPlaying = true;
            if (debugMode) {
                console.log('Video Speed Controller: YouTube ad detected, monitoring for completion');
            }
        } else if (!isAdPlaying && this.wasAdPlaying) {
            // Ad just ended
            this.wasAdPlaying = false;
            if (debugMode) {
                console.log('Video Speed Controller: YouTube ad ended, restoring speed');
            }
            // Restore speed after ad ends
            setTimeout(() => this.restoreDesiredSpeed(), 300);
        }
    };
    
    VideoController.prototype.handleVimeoAds = function() {
        // Vimeo-specific ad detection
        const adContainer = document.querySelector('.vp-ads');
        const adOverlay = document.querySelector('.vp-ad-overlay');
        
        const isAdPlaying = adContainer || adOverlay;
        
        if (isAdPlaying && !this.wasAdPlaying) {
            this.wasAdPlaying = true;
            if (debugMode) {
                console.log('Video Speed Controller: Vimeo ad detected');
            }
        } else if (!isAdPlaying && this.wasAdPlaying) {
            this.wasAdPlaying = false;
            if (debugMode) {
                console.log('Video Speed Controller: Vimeo ad ended, restoring speed');
            }
            setTimeout(() => this.restoreDesiredSpeed(), 300);
        }
    };
    
    VideoController.prototype.handleGenericAds = function() {
        // Generic ad detection based on common patterns
        const adSelectors = [
            '[class*="ad-"]',
            '[class*="advertisement"]',
            '[class*="preroll"]',
            '[class*="midroll"]',
            '[id*="ad-"]',
            '[id*="advertisement"]'
        ];
        
        let isAdPlaying = false;
        for (const selector of adSelectors) {
            if (document.querySelector(selector)) {
                isAdPlaying = true;
                break;
            }
        }
        
        if (isAdPlaying && !this.wasAdPlaying) {
            this.wasAdPlaying = true;
            if (debugMode) {
                console.log('Video Speed Controller: Generic ad detected');
            }
        } else if (!isAdPlaying && this.wasAdPlaying) {
            this.wasAdPlaying = false;
            if (debugMode) {
                console.log('Video Speed Controller: Generic ad ended, restoring speed');
            }
            setTimeout(() => this.restoreDesiredSpeed(), 300);
        }
    };
    
    VideoController.prototype.restoreDesiredSpeed = function() {
        if (this.media.playbackRate !== this.desiredSpeed) {
            this.userInitiatedChange = true;
            this.media.playbackRate = this.desiredSpeed;
            this.lastKnownSpeed = this.desiredSpeed;
            this.userInitiatedChange = false;
            
            if (debugMode) {
                console.log('Video Speed Controller: Speed restored to', this.desiredSpeed);
            }
        }
    };
    
    VideoController.prototype.attachEventListeners = function() {
        // Listen for playback rate changes
        this.handleRateChange = (e) => {
            const speed = parseFloat(this.media.playbackRate).toFixed(2);
            if (this.speedDisplay) {
                this.speedDisplay.textContent = speed + 'x';
            }
            
            // Only update desired speed if this was a user-initiated change
            if (this.userInitiatedChange) {
                this.desiredSpeed = parseFloat(speed);
                this.lastKnownSpeed = this.desiredSpeed;
                
                // Update global last speed
                lastSpeed = parseFloat(speed);
                
                // Save speed to storage with retry mechanism
                saveSpeedToStorage(lastSpeed);
                
                if (debugMode) {
                    console.log('Video Speed Controller: User changed speed to', speed);
                }
            } else {
                // This was an external change - check if we need to restore
                const speedDifference = Math.abs(parseFloat(speed) - this.desiredSpeed);
                if (speedDifference > 0.01) {
                    if (debugMode) {
                        console.log('Video Speed Controller: External speed change detected in handleRateChange', {
                            current: speed,
                            desired: this.desiredSpeed
                        });
                    }
                    // Restore desired speed after a short delay
                    setTimeout(() => this.restoreDesiredSpeed(), 100);
                }
            }
        };
        
        this.media.addEventListener('ratechange', this.handleRateChange);
        
        // Listen for source changes
        this.handleSourceChange = () => {
            if (!this.media.currentSrc && this.wrapper) {
                this.wrapper.classList.add('vsc-nosource');
            } else if (this.wrapper) {
                this.wrapper.classList.remove('vsc-nosource');
            }
        };
        
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.attributeName === 'src' || mutation.attributeName === 'currentSrc') {
                    this.handleSourceChange();
                }
            });
        });
        
        observer.observe(this.media, {
            attributes: true,
            attributeFilter: ['src', 'currentSrc']
        });
        
        this.observer = observer;
    };
    
    VideoController.prototype.handleAction = function(action) {
        const binding = keyBindings.find(kb => kb.action === action);
        if (!binding) return;
        
        switch (action) {
            case 'slower':
                this.adjustSpeed(-binding.value);
                this.blinkController();
                break;
            case 'faster':
                this.adjustSpeed(binding.value);
                this.blinkController();
                break;
            case 'rewind':
                this.media.currentTime -= binding.value;
                break;
            case 'advance':
                this.media.currentTime += binding.value;
                break;
            case 'preferred':
                this.togglePreferredSpeed(binding.value);
                this.blinkController();
                break;
            case 'display':
                this.toggleDisplay();
                break;
        }
    };
    
    VideoController.prototype.adjustSpeed = function(delta) {
        const currentSpeed = this.media.playbackRate;
        const newSpeed = Math.max(0.07, Math.min(16, currentSpeed + delta));
        
        // Mark as user-initiated change
        this.userInitiatedChange = true;
        this.media.playbackRate = parseFloat(newSpeed.toFixed(2));
        this.desiredSpeed = parseFloat(newSpeed.toFixed(2));
        this.userInitiatedChange = false;
    };
    
    VideoController.prototype.togglePreferredSpeed = function(preferredSpeed) {
        // Simple toggle: if at preferred speed -> go to 1x, otherwise -> go to preferred speed
        this.userInitiatedChange = true;
        
        if (Math.abs(this.media.playbackRate - preferredSpeed) < 0.01) {
            this.media.playbackRate = 1.0;
            this.desiredSpeed = 1.0;
        } else {
            this.media.playbackRate = preferredSpeed;
            this.desiredSpeed = preferredSpeed;
        }
        
        this.userInitiatedChange = false;
    };
    
    
    VideoController.prototype.toggleDisplay = function() {
        if (this.wrapper) {
            this.wrapper.classList.toggle('vsc-hidden');
            this.wrapper.classList.add('vsc-manual');
        }
    };
    
    VideoController.prototype.blinkController = function() {
        if (!this.wrapper) return;
        
        // Show controller temporarily if hidden
        if (this.wrapper.classList.contains('vsc-hidden')) {
            this.wrapper.classList.remove('vsc-hidden');
            
            // Hide it again after 1 second
            clearTimeout(this.blinkTimeout);
            this.blinkTimeout = setTimeout(() => {
                if (!this.wrapper.classList.contains('vsc-manual')) {
                    this.wrapper.classList.add('vsc-hidden');
                }
            }, 1000);
        }
    };
    
    VideoController.prototype.handleDrag = function(e) {
        e.preventDefault();
        
        const controller = this.controller;
        controller.classList.add('dragging');
        
        const startX = e.clientX;
        const startY = e.clientY;
        const startLeft = parseInt(controller.style.left);
        const startTop = parseInt(controller.style.top);
        
        const handleMouseMove = (e) => {
            const dx = e.clientX - startX;
            const dy = e.clientY - startY;
            controller.style.left = (startLeft + dx) + 'px';
            controller.style.top = (startTop + dy) + 'px';
        };
        
        const handleMouseUp = () => {
            controller.classList.remove('dragging');
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
        
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    };
    
    VideoController.prototype.remove = function() {
        // Stop the speed guardian
        this.stopSpeedGuardian();
        
        if (this.wrapper) {
            this.wrapper.remove();
            this.wrapper = null;
            this.shadow = null;
            this.controller = null;
            this.speedDisplay = null;
        }
        if (this.media) {
            this.media.removeEventListener('ratechange', this.handleRateChange);
        }
        if (this.observer) {
            this.observer.disconnect();
        }
        if (this.blinkTimeout) {
            clearTimeout(this.blinkTimeout);
        }
    };
    
    // Keyboard handling
    function initializeKeyboardListeners() {
        // Remove any existing listeners first
        document.removeEventListener('keydown', handleKeydown, { capture: true });
        
        // Use capture phase with highest priority to intercept events before other handlers
        // Add to document and window for maximum coverage
        document.addEventListener('keydown', handleKeydown, { capture: true, passive: false });
        window.addEventListener('keydown', handleKeydown, { capture: true, passive: false });
        
        // Also add to document.documentElement for even earlier capture
        if (document.documentElement) {
            document.documentElement.addEventListener('keydown', handleKeydown, { capture: true, passive: false });
        }
    }
    
    function handleKeydown(e) {
        // Check if this is a video speed controller binding first
        const binding = keyBindings.find(kb => kb.key === e.keyCode);
        if (!binding) return;
        
        // Only process if we have active video controllers
        if (controllers.size === 0) return;
        
        // For force flagged shortcuts, always process regardless of context
        const isForceShortcut = binding.force === true;
        
        if (!isForceShortcut) {
            // SMART BLOCKING STRATEGY: Only block for actual input scenarios
            
            const activeElement = document.activeElement;
            const target = e.target;
            
            if (debugMode) {
                console.log('Video Speed Controller: 🔍 DETAILED ANALYSIS - Checking blocking conditions...', {
                    key: e.key,
                    keyCode: e.keyCode,
                    activeElement: activeElement ? {
                        tag: activeElement.tagName,
                        id: activeElement.id || 'none',
                        class: activeElement.className || 'none',
                        isContentEditable: activeElement.isContentEditable,
                        role: activeElement.getAttribute('role') || 'none'
                    } : 'none',
                    target: target ? {
                        tag: target.tagName,
                        id: target.id || 'none', 
                        class: target.className || 'none',
                        isContentEditable: target.isContentEditable,
                        role: target.getAttribute('role') || 'none'
                    } : 'none'
                });
            }
            
            // 1. PRIMARY CHECK: Active element is an actual input field
            if (activeElement && activeElement !== document.body && activeElement !== document.documentElement) {
                const activeTag = activeElement.tagName.toLowerCase();
                const isInput = activeTag === 'input' || activeTag === 'textarea' || activeTag === 'select';
                const isContentEditable = activeElement.isContentEditable || 
                                        activeElement.contentEditable === 'true' ||
                                        activeElement.getAttribute('contenteditable') === 'true';
                
                if (isInput || isContentEditable) {
                    if (debugMode) {
                        console.log('Video Speed Controller: 🚫 BLOCK - Active element accepts text input:', activeTag);
                    }
                    return;
                }
                
                // Check for input-related roles on focused element
                const role = activeElement.getAttribute('role');
                if (role === 'textbox' || role === 'combobox' || role === 'searchbox') {
                    if (debugMode) {
                        console.log('Video Speed Controller: 🚫 BLOCK - Active element has input role:', role);
                    }
                    return;
                }
            }
            
            // 2. SECONDARY CHECK: Target is an input field
            if (target) {
                const targetTag = target.tagName.toLowerCase();
                const isInput = targetTag === 'input' || targetTag === 'textarea' || targetTag === 'select';
                const isContentEditable = target.isContentEditable || 
                                        target.contentEditable === 'true' ||
                                        target.getAttribute('contenteditable') === 'true';
                
                if (isInput || isContentEditable) {
                    if (debugMode) {
                        console.log('Video Speed Controller: 🚫 BLOCK - Target is input field:', targetTag);
                    }
                    return;
                }
            }
            
            // 3. NESTED INPUT CHECK: Target is inside an input container
            if (target.closest('input, textarea, select, [contenteditable="true"], [role="textbox"], [role="combobox"], [role="searchbox"]')) {
                if (debugMode) {
                    console.log('Video Speed Controller: 🚫 BLOCK - Target inside input container');
                }
                return;
            }
            
            // 3.5. TARGET CUSTOM COMPONENT CHECK: Check if target is extension component
            if (target) {
                const targetTag = target.tagName.toLowerCase();
                const targetClass = target.className || '';
                const targetId = target.id || '';
                
                // Check if target is a custom component (Shadow DOM host)
                if (targetTag.includes('-')) {
                    const extensionComponents = ['merlin-component', 'recall-component', 'grammarly-'];
                    const isExtensionComponent = extensionComponents.some(comp => targetTag.includes(comp));
                    
                    if (isExtensionComponent) {
                        if (debugMode) {
                            console.log('Video Speed Controller: 🚫 BLOCK - Target is extension component:', targetTag);
                        }
                        return;
                    }
                }
                
                // Check if target is inside extension component
                if (target.closest('merlin-component, recall-component, [class*="merlin"], [class*="recall"], [class*="grammarly"]')) {
                    if (debugMode) {
                        console.log('Video Speed Controller: 🚫 BLOCK - Target inside extension component');
                    }
                    return;
                }
            }
            
            // 4. CUSTOM COMPONENT / SHADOW DOM CHECK: Handle modern extension components
            if (activeElement && activeElement !== document.body && activeElement !== document.documentElement) {
                const tagName = activeElement.tagName.toLowerCase();
                const className = activeElement.className || '';
                const id = activeElement.id || '';
                
                // Check for custom elements (contain hyphens) which often use Shadow DOM
                if (tagName.includes('-')) {
                    // Known extension components that likely contain input fields
                    const extensionComponents = ['merlin-component', 'recall-component', 'grammarly-', 'notion-'];
                    const isExtensionComponent = extensionComponents.some(comp => tagName.includes(comp));
                    
                    if (isExtensionComponent) {
                        if (debugMode) {
                            console.log('Video Speed Controller: 🚫 BLOCK - Extension custom component focused:', tagName);
                        }
                        return;
                    }
                    
                    // Generic custom element with extension-like classes
                    const extensionClasses = ['merlin', 'recall', 'grammarly', 'assistant', 'chatbot', 'ai-', 'extension'];
                    const hasExtensionClass = extensionClasses.some(cls => className.includes(cls) || id.includes(cls));
                    
                    if (hasExtensionClass) {
                        if (debugMode) {
                            console.log('Video Speed Controller: 🚫 BLOCK - Custom element with extension classes:', {
                                tag: tagName,
                                class: className,
                                id: id
                            });
                        }
                        return;
                    }
                }
                
                // Check for extension-related classes on any focused element
                const extensionClasses = ['merlin', 'recall', 'grammarly', 'notion'];
                const hasExtensionClass = extensionClasses.some(cls => 
                    className.includes(cls) || id.includes(cls)
                );
                
                if (hasExtensionClass) {
                    if (debugMode) {
                        console.log('Video Speed Controller: 🚫 BLOCK - Element with extension classes focused:', {
                            tag: tagName,
                            class: className,
                            id: id
                        });
                    }
                    return;
                }
                
                // Check if the focused element is in an extension popup that accepts text
                const extensionInputs = activeElement.closest('[class*="merlin"], [class*="recall"], [class*="grammarly"], #contenteditable-root, yt-formatted-string[contenteditable], merlin-component, recall-component');
                if (extensionInputs) {
                    if (debugMode) {
                        console.log('Video Speed Controller: 🚫 BLOCK - Element inside extension container');
                    }
                    return;
                }
            }
            
            // 5. ACTIVE TEXT SELECTION CHECK
            const selection = window.getSelection();
            if (selection && selection.toString().length > 0) {
                if (debugMode) {
                    console.log('Video Speed Controller: 🚫 BLOCK - Text is selected');
                }
                return;
            }
            
            // 6. MODIFIER KEYS CHECK
            if (e.altKey || e.ctrlKey || e.metaKey) {
                if (debugMode) {
                    console.log('Video Speed Controller: 🚫 BLOCK - Modifier keys pressed');
                }
                return;
            }
        }
        
        if (debugMode) {
            console.log('Video Speed Controller: ✅ SHORTCUT ALLOWED - Processing keyboard shortcut', {
                key: e.key,
                keyCode: e.keyCode,
                action: binding.action,
                force: binding.force,
                controllersCount: controllers.size,
                target: e.target.nodeName,
                activeElement: document.activeElement ? document.activeElement.tagName : 'none',
                targetClasses: e.target.className || 'none'
            });
        }
        
        // AGGRESSIVE event suppression - stop all propagation immediately
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        // For force shortcuts, also clear the event completely
        if (isForceShortcut) {
            Object.defineProperty(e, 'defaultPrevented', { value: true });
            if (e.returnValue !== undefined) {
                e.returnValue = false;
            }
        }
        
        // Apply action to all videos
        controllers.forEach((controller, media) => {
            controller.handleAction(binding.action);
        });
        
        if (debugMode) {
            console.log('Video Speed Controller: Keyboard shortcut processed successfully');
        }
        
        return false; // Extra prevention
    }
    
    // Settings change listener
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'updateSettings' && message.settings) {
                const wasEnabled = isEnabled;
                const oldShowOverlay = showOverlay;
                
                // Update settings following extension pattern
                isEnabled = message.settings.videoSpeedControllerEnabled === true;
                showOverlay = true; // Always show overlay when enabled
                controllerOpacity = message.settings.videoSpeedControllerOpacity || 0.3;
                startHidden = message.settings.videoSpeedControllerStartHidden === true;
                rememberSpeed = message.settings.videoSpeedControllerRememberSpeed === true;
                audioSupport = message.settings.videoSpeedControllerAudioSupport === true;
                lastSpeed = parseFloat(message.settings.videoSpeedControllerLastSpeed) || 1.0;
                keyBindings = message.settings.videoSpeedControllerKeyBindings || getDefaultKeyBindings();
                blacklist = message.settings.videoSpeedControllerBlacklist || '';
                debugMode = message.settings.debugMode === true;
                
                
                // Handle enable/disable changes for entire YouTube Tools suite
                if (wasEnabled !== isEnabled) {
                    if (isEnabled && !isBlacklisted()) {
                        // Initialize all components
                        initializeVideoSpeedController();
                    } else {
                        // DISABLE ALL COMPONENTS - complete cleanup
                        disableVideoSpeedController();
                        
                        if (debugMode) {
                            console.log('Video Speed Controller: Video controller functionality disabled');
                        }
                    }
                } else if (isEnabled && !isBlacklisted()) {
                    // Update opacity for existing controllers
                    controllers.forEach(controller => {
                        if (controller.controller) {
                            controller.controller.style.opacity = controllerOpacity;
                        }
                    });
                }
                
                if (sendResponse) {
                    sendResponse({ received: true });
                }
            }
        });
    }
    
    // Initialize settings on page load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadSettings);
    } else {
        loadSettings();
    }
})();
