// UTM Tracking Cleaner - Remove UTM parameters and tracking links
(function() {
    'use strict';
    
    let isEnabled = true;
    let debugMode = false;
    let currentSettings = {};
    let whitelistDomains = [];
    let isTemporarilyDisabled = false; // New variable for temporary disable state
    
    // UTM Tracking Cleaner constants
    const UTM_CONSTANTS = {
        STRIPPING_METHOD_HISTORY_CHANGE: 1,
        STRIPPING_METHOD_BLOCK_AND_RELOAD: 3,
        STRIPPING_METHOD_BLOCK_AND_RELOAD_SKIP_REDIRECTS: 4,
        CONTEXT_MENU_COPY_CLEAN_ID: 'COPY_AND_<PERSON>LEAN',
        CONTEXT_MENU_CLEAN_AND_GO_ID: 'C<PERSON>AN_AND_GO',
        CONTEXT_MENU_COPY_CLEAN_TEXT: 'Copy and Clean Link',
        CONTEXT_MENU_CLEAN_AND_GO_TEXT: 'Clean and Open in New Tab'
    };
    
    const DEFAULT_STRIPPING_METHOD = UTM_CONSTANTS.STRIPPING_METHOD_BLOCK_AND_RELOAD_SKIP_REDIRECTS;
    
    // Recommended default settings for optimal privacy and functionality
    const RECOMMENDED_SETTINGS = {
        strippingMethod: UTM_CONSTANTS.STRIPPING_METHOD_BLOCK_AND_RELOAD_SKIP_REDIRECTS,
        copyCleanEnabled: true,
        cleanAndGoEnabled: true
    };
    
    // All known tracking parameters
    const TRACKING_PARAMS = {
        utm_: ['source', 'medium', 'term', 'campaign', 'content', 'name', 'cid', 'reader', 'viz_id', 'pubreferrer', 'swu'],
        IC: ['ID'],
        ic: ['id'],
        _hs: ['enc', 'mi'],
        mkt_: ['tok'],
        mc_: ['cid', 'eid'],
        ns_: ['source', 'mchannel', 'campaign', 'linkname', 'fee'],
        sr_: ['share'],
        vero_: ['conv', 'id'],
        '': ['fbclid', 'igshid', 'srcid', 'gclid', 'ocid', 'ncid', 'nr_email_referer', 'ref', 'spm']
    };
    
    // Generate list of all tracking parameters
    const ALL_TRACKERS = Object.keys(TRACKING_PARAMS).reduce((acc, prefix) => {
        TRACKING_PARAMS[prefix].forEach(param => acc.push(prefix + param));
        return acc;
    }, []);
    
    // Generate regex patterns for each tracker
    const TRACKER_REGEXES = ALL_TRACKERS.reduce((acc, tracker) => {
        acc[tracker] = new RegExp(`((^|&)${tracker}=[^&#]*)`, 'ig');
        return acc;
    }, {});
    
    // Known redirect patterns
    const REDIRECT_PATTERNS = [
        { pattern: /google\.com\/url\?.*[?&]url=([^&]*)/i, param: 'url' },
        { pattern: /google\.com\/url\?.*[?&]q=([^&]*)/i, param: 'q' },
        { pattern: /youtube\.com\/redirect\?.*[?&]q=([^&]*)/i, param: 'q' },
        { pattern: /facebook\.com\/l\.php\?.*[?&]u=([^&]*)/i, param: 'u' },
        { pattern: /\.redirectingat\.com\/\?.*[?&]url=([^&]*)/i, param: 'url' },
        { pattern: /amazon\.ca\/gp\/redirect\.html\?.*[?&]location=([^&]*)/i, param: 'location' }
    ];
    
    // Initialize settings
    async function loadSettings() {
        try {
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            
            console.log('UTM Cleaner: Raw settings from storage:', settings);
            
            // Explicit boolean check - CRITICAL
            isEnabled = settings.utmTrackingCleanerEnabled !== false;
            debugMode = settings.debugMode || false;
            
            // Load temporary disable state from storage
            isTemporarilyDisabled = settings.utmCleanerTemporarilyDisabled || false;
            
            // Load UTM cleaner specific settings with recommended defaults
            currentSettings = {
                strippingMethod: settings.utmStrippingMethod !== undefined ? settings.utmStrippingMethod : RECOMMENDED_SETTINGS.strippingMethod,
                copyCleanEnabled: settings.utmCopyCleanEnabled !== undefined ? settings.utmCopyCleanEnabled : RECOMMENDED_SETTINGS.copyCleanEnabled,
                cleanAndGoEnabled: settings.utmCleanAndGoEnabled !== undefined ? settings.utmCleanAndGoEnabled : RECOMMENDED_SETTINGS.cleanAndGoEnabled
            };
            
            // Load whitelist domains
            whitelistDomains = settings.utmCleanerWhitelistDomains || [];
            
            console.log('UTM Tracking Cleaner: Settings loaded', {
                enabled: isEnabled,
                debugMode: debugMode,
                settings: currentSettings,
                whitelistDomains: whitelistDomains
            });
            
            if (isEnabled) {
                initializeUTMCleaner();
            }
        } catch (error) {
            console.error('UTM Tracking Cleaner: Error loading settings:', error);
            // Set safe defaults using recommended settings
            isEnabled = true;
            currentSettings = {
                strippingMethod: RECOMMENDED_SETTINGS.strippingMethod,
                copyCleanEnabled: RECOMMENDED_SETTINGS.copyCleanEnabled,
                cleanAndGoEnabled: RECOMMENDED_SETTINGS.cleanAndGoEnabled
            };
            whitelistDomains = [];
            if (isEnabled) {
                initializeUTMCleaner();
            }
        }
    }
    
    // Function to normalize domain for comparison
    function normalizeDomain(domain) {
        return domain.toLowerCase().replace(/^www\./, '');
    }

    // Function to extract domain from URL
    function extractDomainFromUrl(url) {
        try {
            const urlObj = new URL(url);
            return normalizeDomain(urlObj.hostname);
        } catch (error) {
            // If URL parsing fails, try to extract domain from href text
            const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^\/\s]+)/);
            return match ? normalizeDomain(match[1]) : '';
        }
    }

    // Function to check if a domain is whitelisted
    function isWhitelistedDomain(url) {
        if (!url || whitelistDomains.length === 0) return false;
        
        const domain = extractDomainFromUrl(url);
        if (!domain) return false;
        
        const isWhitelisted = whitelistDomains.some(whitelistDomain => {
            const normalizedWhitelist = normalizeDomain(whitelistDomain);
            // Exact match: flexclip.com matches flexclip.com
            // Subdomain match: flexclip.com matches www.flexclip.com, app.flexclip.com, etc.
            const isMatch = domain === normalizedWhitelist || domain.endsWith('.' + normalizedWhitelist);
            
            if (debugMode && isMatch) {
                console.log(`UTM Tracking Cleaner: Domain "${domain}" matches whitelist entry "${normalizedWhitelist}"`);
            }
            
            return isMatch;
        });
        
        if (debugMode) {
            console.log(`UTM Tracking Cleaner: Checking whitelist for "${domain}" - Result: ${isWhitelisted ? 'WHITELISTED' : 'NOT WHITELISTED'}`);
            console.log('UTM Tracking Cleaner: Current whitelist domains:', whitelistDomains);
        }
        
        return isWhitelisted;
    }

    // Remove all query parameters from URL (everything after "?")
    function removeTrackersFromUrl(url, trackers = ALL_TRACKERS) {
        if (!url) return url;
        
        // Check if temporarily disabled - this overrides everything including whitelist
        if (isTemporarilyDisabled) {
            if (debugMode) {
                console.log('UTM Tracking Cleaner: Temporarily disabled, skipping URL cleaning for:', url);
            }
            return url; // Return original URL without cleaning
        }
        
        // Check if domain is whitelisted (only if not temporarily disabled)
        if (isWhitelistedDomain(url)) {
            if (debugMode) {
                console.log('UTM Tracking Cleaner: Skipping whitelisted domain:', extractDomainFromUrl(url));
            }
            return url; // Return original URL without cleaning
        }
        
        const urlParts = url.split('?');
        if (urlParts.length === 1) return url;
        
        // Simply return the base URL without any query parameters
        return urlParts[0];
    }
    
    // Extract redirect target from URL
    function extractRedirectTarget(url) {
        for (const redirect of REDIRECT_PATTERNS) {
            const match = url.match(redirect.pattern);
            if (match && match[1]) {
                try {
                    return decodeURIComponent(match[1]);
                } catch (e) {
                    return match[1];
                }
            }
        }
        return null;
    }
    
    // Follow redirects to get final URL
    function followRedirect(url) {
        if (!url) return url;
        
        let currentUrl = url;
        let iterations = 0;
        const maxIterations = 5;
        
        while (iterations < maxIterations) {
            const redirectTarget = extractRedirectTarget(currentUrl);
            if (!redirectTarget || redirectTarget === currentUrl) {
                break;
            }
            currentUrl = redirectTarget;
            iterations++;
        }
        
        return currentUrl;
    }
    
    // Clean URL (remove trackers and follow redirects)
    function cleanUrl(url) {
        if (!url) return url;
        
        // Check if temporarily disabled - return original URL without any processing
        if (isTemporarilyDisabled) {
            if (debugMode) {
                console.log('UTM Tracking Cleaner: Temporarily disabled, skipping URL cleaning and redirect following for:', url);
            }
            return url;
        }
        
        // First follow redirects
        const redirectedUrl = followRedirect(url);
        
        // Then remove tracking parameters
        return removeTrackersFromUrl(redirectedUrl);
    }
    
    // Initialize UTM cleaner based on selected method
    function initializeUTMCleaner() {
        if (!isEnabled) return;
        
        const method = currentSettings.strippingMethod;
        
        if (debugMode) {
            console.log('UTM Tracking Cleaner: Initializing with method:', method);
        }
        
        switch (method) {
            case UTM_CONSTANTS.STRIPPING_METHOD_HISTORY_CHANGE:
                initializeHistoryChangeMethod();
                break;
            case UTM_CONSTANTS.STRIPPING_METHOD_BLOCK_AND_RELOAD:
                initializeBlockAndReloadMethod();
                break;
            case UTM_CONSTANTS.STRIPPING_METHOD_BLOCK_AND_RELOAD_SKIP_REDIRECTS:
                initializeBlockAndReloadSkipRedirectsMethod();
                break;
        }
        
        // Initialize context menu options
        initializeContextMenus();
    }
    
    // Check if we're on a CMS admin area or using a page builder (skip cleaning on these paths/URLs)
    function isCMSAdminArea() {
        const pathname = window.location.pathname.toLowerCase();
        const search = window.location.search.toLowerCase();
        
        const adminPaths = [
            // WordPress admin paths
            '/wp-admin/',
            '/wp-login.php',
            '/wp-cron.php',
            '/wp-content/plugins/',
            '/wp-content/themes/',
            '/wp-includes/',
            // Drupal admin paths
            '/admin/',
            '/user/login',
            '/user/register',
            '/user/password',
            '/batch',
            '/update.php',
            '/install.php',
            // Joomla admin paths
            '/administrator/',
            '/installation/',
            '/components/',
            '/modules/',
            '/plugins/',
            '/templates/',
            // Magento admin paths
            '/admin_',
            '/downloader/',
            '/setup/',
            // Shopify admin paths
            '/admin',
            // PrestaShop admin paths
            '/adminps/',
            '/admin-dev/',
            // TYPO3 admin paths
            '/typo3/',
            '/typo3conf/',
            // MediaWiki admin paths
            '/mw-config/',
            '/maintenance/',
            // Ghost admin paths
            '/ghost/',
            // Craft CMS admin paths
            '/admin',
            '/cpresources/',
            // Laravel admin paths
            '/nova/',
            '/horizon/',
            '/telescope/',
            // Django admin paths
            '/admin/',
            '/django-admin/',
            // Generic admin patterns
            '/backend/',
            '/dashboard/',
            '/panel/',
            '/manage/',
            '/control/',
            '/cp/'
        ];
        
        // WordPress Page Builder query parameters
        const pageBuilderParams = [
            // Bricks Builder
            '?bricks=run',
            '&bricks=run',
            '?bricks=',
            '&bricks=',
            // Oxygen Builder
            '?ct_builder=true',
            '&ct_builder=true',
            '?ct_inner=true',
            '&ct_inner=true',
            '?oxygen_iframe=true',
            '&oxygen_iframe=true',
            // Elementor
            '?elementor-preview=',
            '&elementor-preview=',
            '?elementor_library=',
            '&elementor_library=',
            '?action=elementor',
            '&action=elementor',
            '?elementor_maintenance_mode_exclude_me',
            '&elementor_maintenance_mode_exclude_me',
            // Divi Builder
            '?et_fb=1',
            '&et_fb=1',
            '?et_pb_preview=true',
            '&et_pb_preview=true',
            '?divi=true',
            '&divi=true',
            // Visual Composer (WPBakery)
            '?vc_editable=true',
            '&vc_editable=true',
            '?vc_action=vc_inline',
            '&vc_action=vc_inline',
            '?wpb_vc_js_status=true',
            '&wpb_vc_js_status=true',
            // Beaver Builder
            '?fl_builder',
            '&fl_builder',
            '?fl_builder_data',
            '&fl_builder_data',
            '?fl_builder_ui_iframe=true',
            '&fl_builder_ui_iframe=true',
            // Gutenberg/Block Editor
            '?action=edit',
            '&action=edit',
            '?post_type=',
            '&post_type=',
            '?legacy=0',
            '&legacy=0',
            // Thrive Architect
            '?tve=true',
            '&tve=true',
            '?tcbf=1',
            '&tcbf=1',
            // Brizy
            '?brizy-edit',
            '&brizy-edit',
            '?brizy-edit-iframe',
            '&brizy-edit-iframe',
            // Live Composer
            '?dslc=active',
            '&dslc=active',
            '?dslc_iframe=true',
            '&dslc_iframe=true',
            // Cornerstone (Pro theme)
            '?cs_preview_state=',
            '&cs_preview_state=',
            '?cornerstone=true',
            '&cornerstone=true',
            // Fusion Builder (Avada)
            '?fb-edit=1',
            '&fb-edit=1',
            '?fusion_load_nonce=',
            '&fusion_load_nonce=',
            // King Composer
            '?kc-css-speak=1',
            '&kc-css-speak=1',
            '?kc_action=',
            '&kc_action=',
            // SiteOrigin Page Builder
            '?so_live_editor=1',
            '&so_live_editor=1',
            '?siteorigin_panels_live_editor=',
            '&siteorigin_panels_live_editor=',
            // Themify Builder
            '?tb_load_nonce=',
            '&tb_load_nonce=',
            '?themify_builder_active=',
            '&themify_builder_active=',
            // Zion Builder
            '?zion_builder_active=true',
            '&zion_builder_active=true',
            '?znpb_preview=',
            '&znpb_preview=',
            // Breakdance
            '?breakdance=builder',
            '&breakdance=builder',
            '?breakdance_editor=',
            '&breakdance_editor=',
            // Kadence Blocks
            '?kadence_blocks_editor=',
            '&kadence_blocks_editor=',
            '?kb_preview=',
            '&kb_preview=',
            // Stackable
            '?stackable_preview=',
            '&stackable_preview=',
            // Essential Addons
            '?eael_editor_mode=',
            '&eael_editor_mode=',
            // Page Builder Framework
            '?wpbf_preview=',
            '&wpbf_preview=',
            // GeneratePress (GP Premium)
            '?generate_preview=',
            '&generate_preview=',
            // Astra Pro
            '?astra_editor=',
            '&astra_editor=',
            // OceanWP
            '?ocean_preview=',
            '&ocean_preview='
        ];
        
        // Check admin paths
        const isAdminPath = adminPaths.some(path => pathname.startsWith(path));
        
        // Check page builder parameters
        const isPageBuilder = pageBuilderParams.some(param => search.includes(param));
        
        if (debugMode && isPageBuilder) {
            console.log('UTM Tracking Cleaner: Detected WordPress page builder, skipping URL cleaning');
        }
        
        return isAdminPath || isPageBuilder;
    }

    // Check if we're on a protected domain (skip cleaning on these sites)
    function isProtectedDomain() {
        const hostname = window.location.hostname.toLowerCase();
        const protectedDomains = [
            // Google domains
            'google.com', 'www.google.com', 'maps.google.com', 'search.google.com',
            'google.co.uk', 'www.google.co.uk', 'maps.google.co.uk',
            'google.ca', 'www.google.ca', 'maps.google.ca',
            'google.com.au', 'www.google.com.au', 'maps.google.com.au',
            'google.de', 'google.fr', 'google.es', 'google.it',
            'googleadservices.com', 'www.googleadservices.com',
            'googlesyndication.com', 'www.googlesyndication.com',
            'doubleclick.net', 'www.doubleclick.net',
            // YouTube domains
            'youtube.com', 'www.youtube.com', 'm.youtube.com',
            'youtu.be', 'www.youtu.be',
            // Facebook domains
            'facebook.com', 'www.facebook.com', 'm.facebook.com',
            'fb.com', 'www.fb.com',
            'instagram.com', 'www.instagram.com',
            'l.facebook.com', 'l.messenger.com', 'lm.facebook.com',
            'fbcdn.net', 'www.fbcdn.net',
            // Major hosting companies and domain registrars
            'namecheap.com', 'www.namecheap.com', 'ap.www.namecheap.com',
            'godaddy.com', 'www.godaddy.com', 'sso.godaddy.com', 'account.godaddy.com',
            'bluehost.com', 'www.bluehost.com', 'my.bluehost.com',
            'hostgator.com', 'www.hostgator.com', 'portal.hostgator.com',
            'siteground.com', 'www.siteground.com', 'tools.siteground.com',
            'dreamhost.com', 'www.dreamhost.com', 'panel.dreamhost.com',
            'a2hosting.com', 'www.a2hosting.com', 'my.a2hosting.com',
            'inmotion-hosting.com', 'www.inmotion-hosting.com', 'amp.inmotionhosting.com',
            'hostinger.com', 'www.hostinger.com', 'hpanel.hostinger.com',
            'wpengine.com', 'www.wpengine.com', 'my.wpengine.com',
            'kinsta.com', 'www.kinsta.com', 'my.kinsta.com',
            'cloudflare.com', 'www.cloudflare.com', 'dash.cloudflare.com',
            'digitalocean.com', 'www.digitalocean.com', 'cloud.digitalocean.com',
            'linode.com', 'www.linode.com', 'cloud.linode.com',
            'vultr.com', 'www.vultr.com', 'my.vultr.com',
            'ovh.com', 'www.ovh.com', 'ca.ovh.com', 'us.ovhcloud.com',
            'ionos.com', 'www.ionos.com', '1and1.com', 'www.1and1.com',
            'network-solutions.com', 'www.networksolutions.com',
            'web.com', 'www.web.com', 'register.web.com',
            'squarespace.com', 'www.squarespace.com', 'account.squarespace.com',
            'wix.com', 'www.wix.com', 'manage.wix.com',
            'weebly.com', 'www.weebly.com', 'www.weebly.com',
            'wordpress.com', 'www.wordpress.com', 'wordpress.org',
            'shopify.com', 'www.shopify.com', 'admin.shopify.com',
            'bigcommerce.com', 'www.bigcommerce.com', 'login.bigcommerce.com'
        ];
        
        const isDomainProtected = protectedDomains.some(domain => 
            hostname === domain || hostname.endsWith('.' + domain)
        );
        
        // Also check if we're in a CMS admin area
        const isCMSAdmin = isCMSAdminArea();
        
        if (debugMode && isCMSAdmin) {
            console.log('UTM Tracking Cleaner: Detected CMS admin area, skipping URL cleaning');
        }
        
        return isDomainProtected || isCMSAdmin;
    }

        // History change method (cosmetic only) - Clean links on click AND current page URL after landing
    function initializeHistoryChangeMethod() {
        // Always initialize the click listener (works on ALL sites)
        // But only clean links going TO non-Google destinations
        
        // Listen for link clicks and clean their URLs
        document.addEventListener('click', (event) => {
            const link = event.target.closest('a[href]');
            if (link && link.href) {
                try {
                    // Check if the destination link is a protected platform and skip cleaning
                    const linkUrl = new URL(link.href);
                    const linkHostname = linkUrl.hostname.toLowerCase();
                    const linkPathname = linkUrl.pathname.toLowerCase();
                    const linkSearch = linkUrl.search.toLowerCase();
                    
                    const protectedDomains = [
                        // Google domains
                        'google.com', 'www.google.com', 'maps.google.com', 'search.google.com',
                        'google.co.uk', 'www.google.co.uk', 'maps.google.co.uk',
                        'google.ca', 'www.google.ca', 'maps.google.ca',
                        'google.com.au', 'www.google.com.au', 'maps.google.com.au',
                        'google.de', 'google.fr', 'google.es', 'google.it',
                        'googleadservices.com', 'www.googleadservices.com',
                        'googlesyndication.com', 'www.googlesyndication.com',
                        'doubleclick.net', 'www.doubleclick.net',
                        // YouTube domains
                        'youtube.com', 'www.youtube.com', 'm.youtube.com',
                        'youtu.be', 'www.youtu.be',
                        // Facebook domains
                        'facebook.com', 'www.facebook.com', 'm.facebook.com',
                        'fb.com', 'www.fb.com',
                        'instagram.com', 'www.instagram.com',
                        'l.facebook.com', 'l.messenger.com', 'lm.facebook.com',
                        'fbcdn.net', 'www.fbcdn.net',
                        // Major hosting companies and domain registrars
                        'namecheap.com', 'www.namecheap.com', 'ap.www.namecheap.com',
                        'godaddy.com', 'www.godaddy.com', 'sso.godaddy.com', 'account.godaddy.com',
                        'bluehost.com', 'www.bluehost.com', 'my.bluehost.com',
                        'hostgator.com', 'www.hostgator.com', 'portal.hostgator.com',
                        'siteground.com', 'www.siteground.com', 'tools.siteground.com',
                        'dreamhost.com', 'www.dreamhost.com', 'panel.dreamhost.com',
                        'a2hosting.com', 'www.a2hosting.com', 'my.a2hosting.com',
                        'inmotion-hosting.com', 'www.inmotion-hosting.com', 'amp.inmotionhosting.com',
                        'hostinger.com', 'www.hostinger.com', 'hpanel.hostinger.com',
                        'wpengine.com', 'www.wpengine.com', 'my.wpengine.com',
                        'kinsta.com', 'www.kinsta.com', 'my.kinsta.com',
                        'cloudflare.com', 'www.cloudflare.com', 'dash.cloudflare.com',
                        'digitalocean.com', 'www.digitalocean.com', 'cloud.digitalocean.com',
                        'linode.com', 'www.linode.com', 'cloud.linode.com',
                        'vultr.com', 'www.vultr.com', 'my.vultr.com',
                        'ovh.com', 'www.ovh.com', 'ca.ovh.com', 'us.ovhcloud.com',
                        'ionos.com', 'www.ionos.com', '1and1.com', 'www.1and1.com',
                        'network-solutions.com', 'www.networksolutions.com',
                        'web.com', 'www.web.com', 'register.web.com',
                        'squarespace.com', 'www.squarespace.com', 'account.squarespace.com',
                        'wix.com', 'www.wix.com', 'manage.wix.com',
                        'weebly.com', 'www.weebly.com', 'www.weebly.com',
                        'wordpress.com', 'www.wordpress.com', 'wordpress.org',
                        'shopify.com', 'www.shopify.com', 'admin.shopify.com',
                        'bigcommerce.com', 'www.bigcommerce.com', 'login.bigcommerce.com'
                    ];
                    
                    const adminPaths = [
                        // WordPress admin paths
                        '/wp-admin/',
                        '/wp-login.php',
                        '/wp-cron.php',
                        '/wp-content/plugins/',
                        '/wp-content/themes/',
                        '/wp-includes/',
                        // Drupal admin paths
                        '/admin/',
                        '/user/login',
                        '/user/register',
                        '/user/password',
                        '/batch',
                        '/update.php',
                        '/install.php',
                        // Joomla admin paths
                        '/administrator/',
                        '/installation/',
                        '/components/',
                        '/modules/',
                        '/plugins/',
                        '/templates/',
                        // Magento admin paths
                        '/admin_',
                        '/downloader/',
                        '/setup/',
                        // Shopify admin paths
                        '/admin',
                        // PrestaShop admin paths
                        '/adminps/',
                        '/admin-dev/',
                        // TYPO3 admin paths
                        '/typo3/',
                        '/typo3conf/',
                        // MediaWiki admin paths
                        '/mw-config/',
                        '/maintenance/',
                        // Ghost admin paths
                        '/ghost/',
                        // Craft CMS admin paths
                        '/admin',
                        '/cpresources/',
                        // Laravel admin paths
                        '/nova/',
                        '/horizon/',
                        '/telescope/',
                        // Django admin paths
                        '/admin/',
                        '/django-admin/',
                        // Generic admin patterns
                        '/backend/',
                        '/dashboard/',
                        '/panel/',
                        '/manage/',
                        '/control/',
                        '/cp/'
                    ];
                    
                    const pageBuilderParams = [
                        // Bricks Builder
                        '?bricks=run',
                        '&bricks=run',
                        '?bricks=',
                        '&bricks=',
                        // Oxygen Builder
                        '?ct_builder=true',
                        '&ct_builder=true',
                        '?ct_inner=true',
                        '&ct_inner=true',
                        '?oxygen_iframe=true',
                        '&oxygen_iframe=true',
                        // Elementor
                        '?elementor-preview=',
                        '&elementor-preview=',
                        '?elementor_library=',
                        '&elementor_library=',
                        '?action=elementor',
                        '&action=elementor',
                        '?elementor_maintenance_mode_exclude_me',
                        '&elementor_maintenance_mode_exclude_me',
                        // Divi Builder
                        '?et_fb=1',
                        '&et_fb=1',
                        '?et_pb_preview=true',
                        '&et_pb_preview=true',
                        '?divi=true',
                        '&divi=true',
                        // Visual Composer (WPBakery)
                        '?vc_editable=true',
                        '&vc_editable=true',
                        '?vc_action=vc_inline',
                        '&vc_action=vc_inline',
                        '?wpb_vc_js_status=true',
                        '&wpb_vc_js_status=true',
                        // Beaver Builder
                        '?fl_builder',
                        '&fl_builder',
                        '?fl_builder_data',
                        '&fl_builder_data',
                        '?fl_builder_ui_iframe=true',
                        '&fl_builder_ui_iframe=true',
                        // Gutenberg/Block Editor
                        '?action=edit',
                        '&action=edit',
                        '?post_type=',
                        '&post_type=',
                        '?legacy=0',
                        '&legacy=0',
                        // Thrive Architect
                        '?tve=true',
                        '&tve=true',
                        '?tcbf=1',
                        '&tcbf=1',
                        // Brizy
                        '?brizy-edit',
                        '&brizy-edit',
                        '?brizy-edit-iframe',
                        '&brizy-edit-iframe',
                        // Live Composer
                        '?dslc=active',
                        '&dslc=active',
                        '?dslc_iframe=true',
                        '&dslc_iframe=true',
                        // Cornerstone (Pro theme)
                        '?cs_preview_state=',
                        '&cs_preview_state=',
                        '?cornerstone=true',
                        '&cornerstone=true',
                        // Fusion Builder (Avada)
                        '?fb-edit=1',
                        '&fb-edit=1',
                        '?fusion_load_nonce=',
                        '&fusion_load_nonce=',
                        // King Composer
                        '?kc-css-speak=1',
                        '&kc-css-speak=1',
                        '?kc_action=',
                        '&kc_action=',
                        // SiteOrigin Page Builder
                        '?so_live_editor=1',
                        '&so_live_editor=1',
                        '?siteorigin_panels_live_editor=',
                        '&siteorigin_panels_live_editor=',
                        // Themify Builder
                        '?tb_load_nonce=',
                        '&tb_load_nonce=',
                        '?themify_builder_active=',
                        '&themify_builder_active=',
                        // Zion Builder
                        '?zion_builder_active=true',
                        '&zion_builder_active=true',
                        '?znpb_preview=',
                        '&znpb_preview=',
                        // Breakdance
                        '?breakdance=builder',
                        '&breakdance=builder',
                        '?breakdance_editor=',
                        '&breakdance_editor=',
                        // Kadence Blocks
                        '?kadence_blocks_editor=',
                        '&kadence_blocks_editor=',
                        '?kb_preview=',
                        '&kb_preview=',
                        // Stackable
                        '?stackable_preview=',
                        '&stackable_preview=',
                        // Essential Addons
                        '?eael_editor_mode=',
                        '&eael_editor_mode=',
                        // Page Builder Framework
                        '?wpbf_preview=',
                        '&wpbf_preview=',
                        // GeneratePress (GP Premium)
                        '?generate_preview=',
                        '&generate_preview=',
                        // Astra Pro
                        '?astra_editor=',
                        '&astra_editor=',
                        // OceanWP
                        '?ocean_preview=',
                        '&ocean_preview='
                    ];
                    
                    const isDestinationProtected = protectedDomains.some(domain => 
                        linkHostname === domain || linkHostname.endsWith('.' + domain)
                    );
                    
                    const isDestinationCMSAdmin = adminPaths.some(path => linkPathname.startsWith(path));
                    
                    const isDestinationPageBuilder = pageBuilderParams.some(param => linkSearch.includes(param));
                    
                    if (isDestinationProtected) {
                        if (debugMode) {
                        console.log('UTM Tracking Cleaner: Preserving protected platform destination link:', link.href);
                        }
                        return;
                    }
                    
                    if (isDestinationCMSAdmin) {
                        if (debugMode) {
                            console.log('UTM Tracking Cleaner: Preserving CMS admin destination link:', link.href);
                        }
                        return;
                    }
                    
                    if (isDestinationPageBuilder) {
                        if (debugMode) {
                            console.log('UTM Tracking Cleaner: Preserving WordPress page builder destination link:', link.href);
                        }
                        return;
                    }
                    
                    const cleanedUrl = removeTrackersFromUrl(link.href);
                    if (cleanedUrl !== link.href) {
                        console.log('UTM Tracking Cleaner: Cleaning link on click (from ' + window.location.hostname + ' to ' + linkHostname + ')', {
                            original: link.href,
                            cleaned: cleanedUrl
                        });
                        link.href = cleanedUrl;
                        showNotification('Link cleaned for privacy', 'success');
                    }
                } catch (error) {
                    // If URL parsing fails, skip cleaning to be safe
                    console.log('UTM Tracking Cleaner: Skipping link due to URL parsing error:', link.href);
                }
            }
        }, true); // Use capture phase to catch all clicks
        
        // Clean current page URL after landing (only on non-protected sites)
        if (!isProtectedDomain()) {
            setTimeout(() => {
                cleanCurrentPageUrl();
            }, 2000); // Wait 2 seconds to let the site process any tracking parameters it needs
        }
        
        const currentDomain = isProtectedDomain() ? 'protected platform' : 'regular';
        console.log(`UTM Tracking Cleaner: Initialized on ${currentDomain} site - will clean links to regular sites only`);
    }
    
    // Clean the current page URL (for when you land on a site from ads)
    function cleanCurrentPageUrl() {
        const currentUrl = window.location.href;
        const cleanedUrl = removeTrackersFromUrl(currentUrl);
        
        if (cleanedUrl !== currentUrl) {
            console.log('UTM Tracking Cleaner: Cleaning current page URL after landing', {
                original: currentUrl,
                cleaned: cleanedUrl
            });
            
            // Use replaceState to clean the URL without reloading the page
            history.replaceState(history.state, document.title, cleanedUrl);
            
            // Show a subtle notification that the URL was cleaned
            showNotification('Page URL cleaned for privacy', 'success');
        }
    }
    
    // Block and reload method
    function initializeBlockAndReloadMethod() {
        // This method requires background script integration
        // For now, we'll use the history change method as fallback
        initializeHistoryChangeMethod();
    }
    
    // Block and reload + skip redirects method
    function initializeBlockAndReloadSkipRedirectsMethod() {
        // This method requires background script integration
        // For now, we'll use the history change method as fallback
        initializeHistoryChangeMethod();
    }
    
    // Initialize context menu functionality
    function initializeContextMenus() {
        // Add context menu functionality for links
        document.addEventListener('contextmenu', (e) => {
            const link = e.target.closest('a[href]');
            if (link) {
                // Store the link for context menu actions
                window.utmCleanerSelectedLink = link.href;
            }
        });
    }
    
    // Copy and clean link functionality
    function copyAndCleanLink(url) {
        const cleanedUrl = cleanUrl(url);
        
        // Create temporary element to copy to clipboard
        const tempElement = document.createElement('textarea');
        tempElement.value = cleanedUrl;
        document.body.appendChild(tempElement);
        tempElement.select();
        
        try {
            document.execCommand('copy');
            if (debugMode) {
                console.log('UTM Tracking Cleaner: Copied cleaned URL:', cleanedUrl);
            }
            showNotification('Cleaned URL copied to clipboard!', 'success');
        } catch (err) {
            console.error('UTM Tracking Cleaner: Copy failed:', err);
            showNotification('Failed to copy URL', 'error');
        } finally {
            document.body.removeChild(tempElement);
        }
        
        return cleanedUrl;
    }
    
    // Clean and open in new tab functionality
    function cleanAndOpenInNewTab(url) {
        const cleanedUrl = cleanUrl(url);
        
        try {
            window.open(cleanedUrl, '_blank');
            if (debugMode) {
                console.log('UTM Tracking Cleaner: Opened cleaned URL in new tab:', cleanedUrl);
            }
            showNotification('Opened cleaned URL in new tab', 'success');
        } catch (err) {
            console.error('UTM Tracking Cleaner: Failed to open new tab:', err);
            showNotification('Failed to open new tab', 'error');
        }
        
        return cleanedUrl;
    }
    
    // Settings configuration panel
    function createSettingsPanel() {
        return new Promise((resolve) => {
            // Check if panel already exists
            const existingPanel = document.querySelector('.utm-cleaner-settings-panel');
            if (existingPanel) {
                existingPanel.remove();
            }
            
            // Create panel HTML
            const panel = document.createElement('div');
            panel.className = 'utm-cleaner-settings-panel';
            panel.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #0a0a0a;
                border: 2px solid #7C3AED;
                border-radius: 12px;
                padding: 24px;
                z-index: 9999999;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                box-shadow: 0 8px 32px rgba(0,0,0,0.6);
                max-width: 520px;
                width: 90%;
                color: #d1d5db;
            `;
            
            panel.innerHTML = `
                <div style="color: #7C3AED; font-size: 18px; font-weight: 700; margin-bottom: 20px; text-align: center;">
                    🧹 UTM Tracking Cleaner Options
                </div>
                
                <!-- Privacy Information -->
                <div style="background: #111111; border-radius: 8px; padding: 16px; margin-bottom: 20px; border: 1px solid #2a2a2a;">
                    <div style="font-size: 14px; line-height: 1.5; color: #9ca3af;">
                        This Extension is in no way is trying to keep tabs on you. The cleaner removes ALL query parameters (everything after "?") from links when you click them on regular websites. It automatically skips Google, YouTube, and other Google services to ensure they work properly. You can add additional domains to the whitelist below.
                    </div>
                </div>
                
                <!-- Stripping Method -->
                <div style="margin-bottom: 24px;">
                    <label style="display: block; font-weight: 600; margin-bottom: 12px; color: #d1d5db;">
                        Stripping Method:
                    </label>
                    <select id="utm-stripping-method" style="
                        width: 100%;
                        padding: 8px 12px;
                        background: #1a1a1a;
                        border: 1px solid #374151;
                        border-radius: 6px;
                        color: #d1d5db;
                        font-size: 14px;
                    ">
                        <option value="${UTM_CONSTANTS.STRIPPING_METHOD_HISTORY_CHANGE}">Clean Links on Click (recommended)</option>
                        <option value="${UTM_CONSTANTS.STRIPPING_METHOD_BLOCK_AND_RELOAD}">Block and Re-load (increased privacy)</option>
                        <option value="${UTM_CONSTANTS.STRIPPING_METHOD_BLOCK_AND_RELOAD_SKIP_REDIRECTS}">Block and Re-load + Skip Redirects (most privacy!)</option>
                    </select>
                </div>
                
                <!-- Context Menu Options -->
                <div style="margin-bottom: 24px;">
                    <div style="font-weight: 600; margin-bottom: 12px; color: #d1d5db;">
                        Context Menu Options:
                    </div>
                    
                    <label style="display: flex; align-items: center; margin-bottom: 12px; color: #d1d5db; cursor: pointer;">
                        <input type="checkbox" id="utm-copy-clean" style="margin-right: 12px;">
                        <div>
                            <div style="font-weight: 500;">"Copy & Clean"</div>
                            <div style="font-size: 12px; color: #9ca3af;">Add context menu option to copy cleaned links</div>
                        </div>
                    </label>
                    
                    <label style="display: flex; align-items: center; margin-bottom: 12px; color: #d1d5db; cursor: pointer;">
                        <input type="checkbox" id="utm-clean-go" style="margin-right: 12px;">
                        <div>
                            <div style="font-weight: 500;">"Clean & Open in New Tab"</div>
                            <div style="font-size: 12px; color: #9ca3af;">Add context menu option to open cleaned links in new tab</div>
                        </div>
                    </label>
                </div>
                
                
                <div style="display: flex; gap: 12px; justify-content: flex-end;">
                    <button id="utm-cleaner-cancel" style="
                        background: transparent;
                        border: 1px solid #6b7280;
                        color: #d1d5db;
                        padding: 8px 16px;
                        border-radius: 6px;
                        font-size: 14px;
                        cursor: pointer;
                    ">Cancel</button>
                    <button id="utm-cleaner-save" style="
                        background: #7C3AED;
                        border: 1px solid #7C3AED;
                        color: white;
                        padding: 8px 16px;
                        border-radius: 6px;
                        font-size: 14px;
                        cursor: pointer;
                        font-weight: 600;
                    ">Save Settings</button>
                </div>
            `;
            
            document.body.appendChild(panel);
            
            // Set current values with small delay to ensure DOM is ready
            setTimeout(() => {
                console.log('UTM Cleaner: Setting UI values from currentSettings:', currentSettings);
                
                const methodSelect = document.getElementById('utm-stripping-method');
                const copyCleanCheckbox = document.getElementById('utm-copy-clean');
                const cleanGoCheckbox = document.getElementById('utm-clean-go');
                
                if (methodSelect) {
                    methodSelect.value = currentSettings.strippingMethod;
                    console.log('UTM Cleaner: Set stripping method to:', methodSelect.value, '(should be:', currentSettings.strippingMethod, ')');
                } else {
                    console.error('UTM Cleaner: Could not find stripping method select element');
                }
                
                if (copyCleanCheckbox) {
                    copyCleanCheckbox.checked = currentSettings.copyCleanEnabled;
                    console.log('UTM Cleaner: Set copy clean to:', copyCleanCheckbox.checked, '(should be:', currentSettings.copyCleanEnabled, ')');
                } else {
                    console.error('UTM Cleaner: Could not find copy clean checkbox');
                }
                
                if (cleanGoCheckbox) {
                    cleanGoCheckbox.checked = currentSettings.cleanAndGoEnabled;
                    console.log('UTM Cleaner: Set clean and go to:', cleanGoCheckbox.checked, '(should be:', currentSettings.cleanAndGoEnabled, ')');
                } else {
                    console.error('UTM Cleaner: Could not find clean and go checkbox');
                }
            }, 100);
            
            // Handle save button
            document.getElementById('utm-cleaner-save').addEventListener('click', () => {
                const newSettings = {
                    strippingMethod: parseInt(document.getElementById('utm-stripping-method').value),
                    copyCleanEnabled: document.getElementById('utm-copy-clean').checked,
                    cleanAndGoEnabled: document.getElementById('utm-clean-go').checked
                };
                
                console.log('UTM Cleaner: Attempting to save settings:', newSettings);
                
                // Save to chrome storage
                chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
                    if (chrome.runtime.lastError) {
                        console.error('UTM Cleaner: Error reading storage:', chrome.runtime.lastError);
                        showNotification('Error reading settings!', 'error');
                        return;
                    }
                    
                    const settings = result.gmbExtractorSettings || {};
                    settings.utmStrippingMethod = newSettings.strippingMethod;
                    settings.utmCopyCleanEnabled = newSettings.copyCleanEnabled;
                    settings.utmCleanAndGoEnabled = newSettings.cleanAndGoEnabled;
                    // Note: Whitelist domains are now managed through the accordion in settings, not this popup
                    
                    console.log('UTM Cleaner: Complete settings object to save:', settings);
                    
                    chrome.storage.local.set({ gmbExtractorSettings: settings }, () => {
                        if (chrome.runtime.lastError) {
                            console.error('UTM Cleaner: Error saving settings:', chrome.runtime.lastError);
                            showNotification('Error saving settings!', 'error');
                            return;
                        }
                        
                        console.log('UTM Cleaner: Settings saved successfully');
                        currentSettings = newSettings;
                        panel.remove();
                        showNotification('UTM Tracking Cleaner settings saved!', 'success');
                        
                        // Reinitialize with new settings
                        if (isEnabled) {
                            initializeUTMCleaner();
                        }
                        
                        resolve(newSettings);
                    });
                });
            });
            
            // Handle cancel button
            document.getElementById('utm-cleaner-cancel').addEventListener('click', () => {
                panel.remove();
                resolve(null);
            });
            
            // Handle escape key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    panel.remove();
                    document.removeEventListener('keydown', handleEscape);
                    resolve(null);
                }
            };
            document.addEventListener('keydown', handleEscape);
        });
    }
    
    // Show notification
    function showNotification(message, type = 'info') {
        // Create notification with black background and colored dots matching clicktocopy.js style
        const notification = document.createElement('div');
        
        // Add colored dot and border based on notification type
        let dotColor = '#7C3AED'; // Default purple for info
        let borderColor = 'rgba(124, 58, 237, 0.3)'; // Default purple border
        
        if (type === 'success') {
            dotColor = '#22c55e'; // Green
            borderColor = 'rgba(34, 197, 94, 0.5)'; // Green border
        } else if (type === 'error') {
            dotColor = '#ef4444'; // Red
            borderColor = 'rgba(239, 68, 68, 0.5)'; // Red border
        } else if (type === 'disable') {
            dotColor = '#ef4444'; // Red dot for disable
            borderColor = 'rgba(239, 68, 68, 0.5)'; // Red border for disable
        }
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: #fff;
            padding: 8px 12px;
            border-radius: 6px;
            z-index: 10000000;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(5px);
            border: 1px solid ${borderColor};
            opacity: 0;
            transform: translateX(20px);
            transition: all 0.3s ease-out;
            max-width: 300px;
            pointer-events: none;
            white-space: nowrap;
        `;
        
        notification.innerHTML = `<span style="color: ${dotColor}; font-size: 16px;">●</span> ${message}`;
        
        document.body.appendChild(notification);
        
        // Animate in
        requestAnimationFrame(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        });
        
        // Animate out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(20px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2500);
    }
    
    // Disable feature
    function disableUTMCleaner() {
        // Remove any event listeners or observers
        // Clean up any modifications
        if (debugMode) {
            console.log('UTM Tracking Cleaner: Disabled');
        }
    }
    
    // Listen for settings changes
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'updateSettings' && message.settings) {
            const wasEnabled = isEnabled;
            isEnabled = message.settings.utmTrackingCleanerEnabled !== false;
            debugMode = message.settings.debugMode || false;
            whitelistDomains = message.settings.utmCleanerWhitelistDomains || [];
            
            // Load temporary disable state from storage
            isTemporarilyDisabled = message.settings.utmCleanerTemporarilyDisabled || false;
            
            // Update current settings
            currentSettings.strippingMethod = message.settings.utmStrippingMethod !== undefined ? message.settings.utmStrippingMethod : currentSettings.strippingMethod;
            currentSettings.copyCleanEnabled = message.settings.utmCopyCleanEnabled !== undefined ? message.settings.utmCopyCleanEnabled : currentSettings.copyCleanEnabled;
            currentSettings.cleanAndGoEnabled = message.settings.utmCleanAndGoEnabled !== undefined ? message.settings.utmCleanAndGoEnabled : currentSettings.cleanAndGoEnabled;
            
            if (wasEnabled !== isEnabled) {
                if (isEnabled) {
                    loadSettings();
                } else {
                    disableUTMCleaner();
                }
            } else if (isEnabled) {
                // Re-initialize with new settings
                initializeUTMCleaner();
            }
        }
        
        // Handle temporary disable toggle
        if (message.type === 'UTM_TOGGLE_TEMPORARY_DISABLE') {
            const newState = toggleTemporaryDisable();
            sendResponse({
                success: true,
                isTemporarilyDisabled: newState
            });
            return true; // Keep message channel open for async response
        }
    });
    
    // Check if URL returns 404 status
    function checkUrlFor404(url) {
        if (!url) {
            const targetUrl = prompt("Enter the URL to check for 404:");
            if (!targetUrl) return;
            url = targetUrl;
        }
        
        return fetch(url, { method: 'HEAD' })
            .then(response => {
                if (response.status === 404) {
                    showNotification(`The URL "${url}" returned a 404 (Not Found) status.`, 'error');
                    return { status: 404, url: url, found: false };
                } else {
                    showNotification(`The URL "${url}" returned status: ${response.status} ${response.statusText}`, 'success');
                    return { status: response.status, statusText: response.statusText, url: url, found: true };
                }
            })
            .catch(error => {
                showNotification(`An error occurred while fetching the URL: ${error.message}`, 'error');
                return { error: error.message, url: url, found: false };
            });
    }

    // Check domain availability (for whitelist validation)
    function checkDomainAvailability(domain, silent = false) {
        if (!domain || domain.trim() === '') return Promise.resolve({ found: true });
        
        // Clean domain (remove protocol, www, paths)
        const cleanDomain = domain.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0].trim();
        if (!cleanDomain) return Promise.resolve({ found: true });
        
        // Try both http and https
        const urls = [`https://${cleanDomain}`, `http://${cleanDomain}`];
        
        return Promise.allSettled(urls.map(url => 
            fetch(url, { 
                method: 'HEAD',
                mode: 'no-cors' // Allow checking cross-origin domains
            }).then(response => ({ url, success: true, status: response.status }))
        )).then(results => {
            // If any request succeeds, consider domain valid
            const hasSuccess = results.some(result => result.status === 'fulfilled');
            
            if (!silent) {
                if (hasSuccess) {
                    console.log(`UTM Cleaner: Domain "${cleanDomain}" is accessible`);
                } else {
                    console.log(`UTM Cleaner: Domain "${cleanDomain}" appears to be inaccessible`);
                }
            }
            
            return { 
                domain: cleanDomain, 
                found: hasSuccess,
                originalDomain: domain
            };
        }).catch(error => {
            if (!silent) {
                console.log(`UTM Cleaner: Error checking domain "${cleanDomain}":`, error.message);
            }
            return { 
                domain: cleanDomain, 
                found: false, 
                error: error.message,
                originalDomain: domain
            };
        });
    }

    // Validate and clean whitelist domains
    async function validateWhitelistDomains(domains, statusElement) {
        if (!Array.isArray(domains) || domains.length === 0) {
            return domains;
        }

        if (statusElement) {
            statusElement.textContent = 'Checking domains...';
            statusElement.style.color = '#fbbf24'; // Yellow
        }

        const validDomains = [];
        const invalidDomains = [];
        let checkedCount = 0;

        for (const domain of domains) {
            if (domain.trim() === '') continue;
            
            try {
                const result = await checkDomainAvailability(domain, true);
                checkedCount++;
                
                if (statusElement) {
                    statusElement.textContent = `Checking domains... (${checkedCount}/${domains.length})`;
                }
                
                if (result.found) {
                    validDomains.push(result.domain);
                } else {
                    invalidDomains.push(result.originalDomain);
                }
            } catch (error) {
                // If check fails, keep domain (benefit of doubt)
                validDomains.push(domain.trim());
            }
        }

        if (statusElement) {
            if (invalidDomains.length > 0) {
                statusElement.textContent = `Removed ${invalidDomains.length} invalid domain(s)`;
                statusElement.style.color = '#ef4444'; // Red
                setTimeout(() => {
                    statusElement.textContent = '';
                }, 3000);
            } else {
                statusElement.textContent = 'All domains validated ✓';
                statusElement.style.color = '#10b981'; // Green  
                setTimeout(() => {
                    statusElement.textContent = '';
                }, 2000);
            }
        }

        if (invalidDomains.length > 0 && !statusElement) {
            showNotification(`Removed ${invalidDomains.length} invalid domain(s) from whitelist`, 'error');
        }

        return validDomains;
    }

    // Function to toggle temporary disable state
    async function toggleTemporaryDisable() {
        isTemporarilyDisabled = !isTemporarilyDisabled;
        
        if (debugMode) {
            console.log('UTM Tracking Cleaner: Temporary disable toggled to:', isTemporarilyDisabled);
        }
        
        // Save the state to chrome.storage for persistence across pages
        try {
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            settings.utmCleanerTemporarilyDisabled = isTemporarilyDisabled;
            await chrome.storage.local.set({ gmbExtractorSettings: settings });
            
            if (debugMode) {
                console.log('UTM Tracking Cleaner: Temporary disable state saved to storage:', isTemporarilyDisabled);
            }
        } catch (error) {
            console.error('UTM Tracking Cleaner: Failed to save temporary disable state:', error);
        }
        
        // Show notification about the state change
        if (isTemporarilyDisabled) {
            showNotification('UTM Cleaner temporarily disabled', 'info');
        } else {
            showNotification('UTM Cleaner re-enabled', 'success');
        }
        
        return isTemporarilyDisabled;
    }

    // Make functions available globally for context menu integration
    window.utmTrackingCleaner = {
        copyAndCleanLink,
        cleanAndOpenInNewTab,
        cleanUrl,
        createSettingsPanel,
        checkUrlFor404,
        checkDomainAvailability,
        validateWhitelistDomains,
        isEnabled: () => isEnabled,
        toggleTemporaryDisable,
        isTemporarilyDisabled: () => isTemporarilyDisabled
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadSettings);
    } else {
        loadSettings();
    }
    
})();
