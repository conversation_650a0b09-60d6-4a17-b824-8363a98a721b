/**
 * Screenshot Shortcut Handler
 * Handles custom keyboard shortcuts for the Screenshot Tool
 */

class ScreenshotShortcutHandler {
    constructor() {
        this.isCapturing = false;
        this.shortcutInput = null;
        this.statusElement = null;
        this.isMac = navigator.platform.toLowerCase().includes('mac');
    }

    init() {
        console.log('[ScreenshotShortcut] Initializing screenshot shortcut handler...');
        
        // Only initialize if we're on the settings page or if the input field exists
        if (this.isOnSettingsPage() || document.querySelector('#screenshotCustomShortcut')) {
            this.setup();
        } else {
            console.log('[ScreenshotShortcut] Not on settings page, skipping initialization');
        }
    }
    
    isOnSettingsPage() {
        // Check if we're on the settings page
        return window.location.href.includes('settings.html') || 
               window.location.href.includes('extras-settings.html') ||
               document.title.includes('Settings');
    }

    setup() {
        // Find or create the shortcut UI in settings
        this.createCustomShortcutUI();
        this.setupEventListeners();
        this.loadSavedShortcut();
    }

    createCustomShortcutUI() {
        // Look for existing screenshot shortcut input field
        this.shortcutInput = document.querySelector('#screenshotCustomShortcut');
        this.statusElement = document.querySelector('#screenshotShortcutStatus');
        
        if (!this.shortcutInput) {
            console.warn('[ScreenshotShortcut] Screenshot shortcut input field not found');
            return;
        }

        // Update the existing input field attributes for capture mode
        this.shortcutInput.placeholder = 'Click here and press keys to set shortcut...';
        this.shortcutInput.spellcheck = false;
        this.shortcutInput.autocomplete = 'off';
        
        console.log('[ScreenshotShortcut] Custom shortcut handler initialized with existing input field');
    }

    setupEventListeners() {
        if (!this.shortcutInput) return;

        // Handle keydown events for shortcut capture
        this.shortcutInput.addEventListener('keydown', (e) => this.handleShortcutInput(e));
        
        // Handle manual text input
        this.shortcutInput.addEventListener('input', (e) => this.handleManualInput(e));
        
        // Handle focus/blur events
        this.shortcutInput.addEventListener('focus', () => this.startCapture());
        this.shortcutInput.addEventListener('blur', () => this.stopCapture());

        // Listen for storage changes
        chrome.storage.onChanged.addListener((changes) => {
            if (changes.screenshotShortcut) {
                this.updateInputValue(changes.screenshotShortcut.newValue || '');
            }
        });
    }

    handleShortcutInput(e) {
        if (!this.isCapturing) return;
        
        e.preventDefault();
        e.stopPropagation();
        
        // Ignore modifier keys alone
        if (['Control', 'Alt', 'Meta', 'Shift'].includes(e.key)) {
            return;
        }
        
        const shortcut = this.formatShortcut(e);
        if (shortcut) {
            this.updateInputValue(shortcut);
            // Immediately save the formatted shortcut
            this.saveShortcut(shortcut);
            
            // Stop capturing after successful shortcut capture
            setTimeout(() => this.stopCapture(), 100);
        }
    }

    handleManualInput(e) {
        // Handle manual typing of shortcuts
        const value = e.target.value.trim();
        
        if (value) {
            // Validate the manually typed shortcut
            if (this.isValidShortcutString(value)) {
                this.showStatus(`✓ Valid shortcut: ${value}`, 'success');
            } else {
                this.showStatus('Invalid shortcut format', 'error');
            }
        } else {
            this.showStatus('', 'info');
        }
    }

    isValidShortcutString(shortcut) {
        // Basic validation for shortcut format
        const validKeys = /^(Ctrl|Alt|Shift|Meta|Cmd)(\+(Ctrl|Alt|Shift|Meta|Cmd))*\+[A-Z0-9]$/i;
        const functionKeys = /^(Ctrl|Alt|Shift|Meta|Cmd)(\+(Ctrl|Alt|Shift|Meta|Cmd))*\+F[1-9]|F1[0-2]$/i;
        const specialKeys = /^(Ctrl|Alt|Shift|Meta|Cmd)(\+(Ctrl|Alt|Shift|Meta|Cmd))*\+(Enter|Space|Tab|Escape|Delete|Backspace|Insert|Home|End|PageUp|PageDown|ArrowUp|ArrowDown|ArrowLeft|ArrowRight)$/i;
        
        return validKeys.test(shortcut) || functionKeys.test(shortcut) || specialKeys.test(shortcut);
    }

    formatShortcut(e) {
        const parts = [];
        
        // Add modifiers in consistent order (EXACTLY like Copy Element and Global Shortcut Manager)
        if (e.ctrlKey) parts.push('Ctrl');
        if (e.altKey) parts.push('Alt');
        if (e.metaKey) parts.push('Cmd');  // Use 'Cmd' not 'Meta' for consistency
        if (e.shiftKey) parts.push('Shift');
        
        // Must have at least one modifier for global shortcuts
        if (parts.length === 0) {
            this.showStatus('Shortcuts must include at least one modifier key', 'error');
            return null;
        }
        
        // Special keys mapping (EXACTLY like Copy Element)
        const specialKeys = {
            'arrowup': '↑',
            'arrowdown': '↓',
            'arrowleft': '←',
            'arrowright': '→',
            'enter': '↵',
            'tab': '⇥',
            'escape': 'Esc',
            'backspace': '⌫',
            'delete': '⌦',
            'space': ' '
        };
        
        const key = e.key;
        const keyLower = key.toLowerCase();
        
        // Handle different types of keys (EXACTLY like Copy Element)
        if (specialKeys[keyLower]) {
            parts.push(specialKeys[keyLower]);
        } else if (/^f(1[0-2]|[1-9])$/i.test(keyLower)) {
            // Function keys
            parts.push(keyLower.toUpperCase());
        } else if (key.length === 1) {
            // Single characters - preserve exact character
            parts.push(key);
        } else {
            this.showStatus('Unsupported key: ' + key, 'error');
            return null;
        }
        
        return parts.join('+');
    }

    validateAndSaveShortcut(shortcut) {
        if (!shortcut) {
            this.showStatus('Please enter a shortcut first', 'error');
            return false;
        }
        
        if (!this.isValidShortcutString(shortcut)) {
            this.showStatus('Invalid shortcut format', 'error');
            return false;
        }
        
        // Enhanced conflict detection for common browser shortcuts
        const commonShortcuts = [
            // File operations
            'Ctrl+C', 'Ctrl+V', 'Ctrl+X', 'Ctrl+Z', 'Ctrl+Y', 'Ctrl+A', 'Ctrl+S', 'Ctrl+O', 'Ctrl+N', 'Ctrl+P',
            // Browser navigation
            'Ctrl+T', 'Ctrl+W', 'Ctrl+R', 'Ctrl+F', 'Ctrl+H', 'Ctrl+J', 'Ctrl+K', 'Ctrl+L', 'Ctrl+D', 'Ctrl+B',
            'Ctrl+Shift+T', 'Ctrl+Shift+N', 'Ctrl+Shift+Delete', 'Ctrl+Shift+R',
            // System shortcuts
            'Alt+F4', 'Alt+Tab', 'Ctrl+Alt+Delete',
            // Function keys often used by browsers
            'F5', 'F11', 'F12', 'Ctrl+F5', 'Shift+F5',
            // Mac equivalents 
            'Cmd+C', 'Cmd+V', 'Cmd+X', 'Cmd+Z', 'Cmd+Y', 'Cmd+A', 'Cmd+S', 'Cmd+O', 'Cmd+N', 'Cmd+P',
            'Cmd+T', 'Cmd+W', 'Cmd+R', 'Cmd+F', 'Cmd+Shift+T', 'Cmd+Shift+N'
        ];
        
        if (commonShortcuts.includes(shortcut)) {
            this.showStatus('This shortcut conflicts with common browser shortcuts. Please choose a different combination.', 'error');
            return false;
        }
        
        return true;
    }

    async saveShortcut(shortcut = null) {
        const shortcutToSave = shortcut || this.shortcutInput?.value || '';
        
        // If clearing shortcut, no validation needed
        if (!shortcutToSave) {
            try {
                await chrome.storage.sync.set({
                    screenshotShortcut: shortcutToSave
                });
                
                console.log('[ScreenshotShortcut] Shortcut cleared');
                this.showStatus('✓ Shortcut cleared', 'info');
                
                // Notify the global shortcut manager to refresh
                if (window.globalShortcutManager) {
                    window.globalShortcutManager.refreshShortcuts();
                }
                
            } catch (error) {
                console.error('[ScreenshotShortcut] Failed to clear shortcut:', error);
                this.showStatus('❌ Failed to clear shortcut', 'error');
            }
            return;
        }
        
        // Check for duplicates using global validation system
        if (window.validateShortcut && this.shortcutInput) {
            const isValid = await window.validateShortcut(
                this.shortcutInput, 
                shortcutToSave, 
                'Screenshot', 
                'screenshotShortcut',
                {
                    showInlineErrors: true,
                    allowOverwrite: true
                }
            );
            
            if (!isValid) {
                // Reset input to previous value on validation failure
                this.loadSavedShortcut();
                return;
            }
        }
        
        try {
            // Save to Chrome storage
            await chrome.storage.sync.set({
                screenshotShortcut: shortcutToSave
            });
            
            console.log('[ScreenshotShortcut] Shortcut saved:', shortcutToSave);
            this.showStatus(`✓ Shortcut saved: ${shortcutToSave}`, 'success');
            
            // Notify the global shortcut manager to refresh
            if (window.globalShortcutManager) {
                window.globalShortcutManager.refreshShortcuts();
            }
            
        } catch (error) {
            console.error('[ScreenshotShortcut] Failed to save shortcut:', error);
            this.showStatus('❌ Failed to save shortcut', 'error');
        }
    }

    async loadSavedShortcut() {
        try {
            const result = await chrome.storage.sync.get(['screenshotShortcut']);
            const savedShortcut = result.screenshotShortcut;
            
            if (savedShortcut && this.shortcutInput) {
                this.shortcutInput.value = savedShortcut;
                this.showStatus(`Loaded saved shortcut: ${savedShortcut}`, 'info');
            }
        } catch (error) {
            console.error('[ScreenshotShortcut] Failed to load saved shortcut:', error);
        }
    }

    getDefaultShortcut() {
        return 'Ctrl+Shift+S'; // Default shortcut for screenshot
    }

    updateInputValue(value) {
        if (this.shortcutInput) {
            this.shortcutInput.value = value;
        }
    }

    clearShortcut() {
        this.updateInputValue('');
        this.saveShortcut('');
    }

    startCapture() {
        this.isCapturing = true;
        this.showStatus('🎯 Press your desired key combination...', 'info');
        
        if (this.shortcutInput) {
            this.shortcutInput.classList.add('capturing');
            this.shortcutInput.placeholder = 'Press keys now...';
            this.shortcutInput.select(); // Select all text for easy replacement
        }
    }

    stopCapture() {
        this.isCapturing = false;
        
        if (this.shortcutInput) {
            this.shortcutInput.classList.remove('capturing');
            this.shortcutInput.placeholder = 'Click here and press keys to set shortcut...';
        }
    }

    showStatus(message, type = 'info') {
        if (!this.statusElement) return;
        
        this.statusElement.textContent = message;
        this.statusElement.className = `shortcut-status ${type}`;
        
        // Auto-clear after 5 seconds for non-error messages
        if (type !== 'error') {
            setTimeout(() => {
                if (this.statusElement) {
                    this.statusElement.textContent = '';
                    this.statusElement.className = 'shortcut-status';
                }
            }, 5000);
        }
    }
}