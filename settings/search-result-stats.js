// Search Result Stats Display - Independent toggle to show/hide Google's hidden result stats
(function() {
  'use strict';
  
  // Prevent multiple loads
  if (window.GMBSearchResultStatsLoaded) return;
  window.GMBSearchResultStatsLoaded = true;
  
  let isEnabled = false; // Default to OFF
  
  // Simple function to show/hide result stats
  function toggleResultStats() {
    console.log('Search Result Stats: toggleResultStats called, enabled:', isEnabled);
    
    // Only work on Google search pages
    if (!window.location.pathname.includes('/search')) {
      console.log('Search Result Stats: Not a search page, skipping');
      return;
    }
    
    // Always remove existing stats first
    const existingStats = document.getElementById('gmb-result-stats-clone');
    if (existingStats) {
      existingStats.remove();
      console.log('Search Result Stats: Removed existing stats');
    }
    
    // Only create new stats if enabled
    if (isEnabled) {
      const resultStats = document.getElementById('result-stats');
      if (resultStats && resultStats.textContent.trim()) {
        const clone = document.createElement('div');
        clone.id = 'gmb-result-stats-clone';
        clone.textContent = resultStats.textContent;
        clone.style.cssText = `
          color: #9aa0a6 !important;
          font-family: Google Sans,Roboto,Arial,sans-serif !important;
          font-size: 14px !important;
          margin: 8px 0 !important;
          display: block !important;
        `;
        
        const target = document.querySelector('.O4T6Pe.TPKH4e') || document.querySelector('#rso');
        if (target && target.parentNode) {
          target.parentNode.insertBefore(clone, target);
          console.log('Search Result Stats: Stats displayed');
        }
      }
    }
  }
  
  // Load settings and apply immediately  
  if (typeof chrome !== 'undefined' && chrome.storage) {
    chrome.storage.local.get(['gmbExtractorSettings'], function(result) {
      const settings = result.gmbExtractorSettings || {};
      isEnabled = settings.searchResultStats === true; // Only ON if explicitly enabled
      console.log('Search Result Stats: Settings loaded, enabled:', isEnabled);
      
      // Always call toggle to ensure proper state
      setTimeout(toggleResultStats, 100);
    });
  } else {
    // No chrome storage - stay disabled
    setTimeout(toggleResultStats, 100);
  }
  
  // Listen for settings updates
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'updateSettings') {
        const oldEnabled = isEnabled;
        isEnabled = message.settings.searchResultStats === true;
        console.log('Search Result Stats: Settings updated, enabled:', isEnabled, 'changed:', oldEnabled !== isEnabled);
        
        // Always toggle to ensure correct state
        toggleResultStats();
        sendResponse({received: true});
      }
    });
  }
  
})(); 