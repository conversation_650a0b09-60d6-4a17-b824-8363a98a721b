<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings</title>
    <link rel="stylesheet" href="../css/popup.css">
    <link rel="stylesheet" href="settings.css">
    <link rel="stylesheet" href="../css/pomodoro.css">
    <style>
        /* Window positioning and sizing */
        body {
            min-width: 600px;
            min-height: 500px;
            max-width: 1200px;
            margin: 0 auto;
            overflow-x: hidden;
        }
        
        .popup__container {
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }
        
        /* Responsive adjustments */
        @media (max-width: 800px) {
            body {
                min-width: 500px;
            }
            
            .popup__container {
                padding: 15px;
            }
            
            .settings-tabs {
                /* Keep horizontal layout even on smaller screens */
                flex-direction: row;
                flex-wrap: nowrap;
                gap: 2px;
            }
            
            .settings-tab {
                padding: 8px 10px;
                font-size: 14px;
                min-width: 80px;
            }
        }
        
        @media (max-height: 600px) {
            .popup__container {
                padding: 10px;
            }
            
            .settings-content {
                max-height: calc(100vh - 200px);
                overflow-y: auto;
            }
        }
        
        /* Ensure content doesn't overflow */
        .settings-content {
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        /* Window title for better identification */
        .header__title::after {
            content: " - SEO Time Machines";
            font-size: 0.8em;
            opacity: 0.7;
        }
        
        
        /* Emergency reset button (hidden by default) */
        .emergency-reset-button {
            display: none;
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 10px;
            font-weight: bold;
            animation: pulse 1s infinite;
        }
        
        .emergency-reset-button.visible {
            display: block;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
        }
    </style>
</head>
<body class="popup">
    <div class="popup__container">
        <header class="header">
            <img src="../images/icon128.png" alt="STM Logo" class="header__logo">
            <div class="header__content">
                <h1 class="header__title">Settings</h1>
                <div class="header__status-wrapper">
                    <div class="header__status-container">
                        <span class="status__text" id="settings-version-text" title="Click 10 times for emergency reset">Loading version...</span>
                    </div>
                    
                    <!-- Emergency Reset Section (hidden by default) -->
                    <div id="emergency-reset-section" style="text-align: center; margin-top: 10px;">
                        <button id="emergency-reset-button" class="emergency-reset-button">
                            🚨 FIX LOCATION CORRUPTION - Requires Browser Restart
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Settings Tabs -->
        <div class="settings-tabs">
            <button class="settings-tab" data-tab="general">General Settings</button>
            <button class="settings-tab settings-tab--active" data-tab="extras">Extras</button>
            <button class="settings-tab" data-tab="quick-actions">Quick Actions</button>
        </div>

        <!-- Tab Content -->
        <div class="settings-content">
            <!-- General Settings Tab -->
            <div class="settings-panel" id="general-tab">
                <div id="general-settings-content"></div>
            </div>

            <!-- Extras Tab -->
            <div class="settings-panel settings-panel--active" id="extras-tab">
                <div id="extras-settings-content"></div>
            </div>

            <!-- Quick Actions Tab -->
            <div class="settings-panel" id="quick-actions-tab">
                <div id="quick-actions-settings-content"></div>
            </div>
        </div>


    </div>

    <script src="logging-utility.js"></script>
    <script src="extension-reload-utility.js"></script>
    <script src="quick-actions-reorder.js"></script>
    <script src="global-shortcut-manager.js"></script>
    <script src="colorpicker-shortcut-handler.js"></script>
    <script src="screenshot-shortcut-handler.js"></script>
    <script src="profiles.js"></script>
    <script src="../js/pomodoro/sound-preview.js"></script>
    <!-- Tooltip System -->
    <script src="../js/tooltip-system.js"></script>
    <script src="../js/info-icon-initializer.js"></script>
    <script src="settings.js"></script>
</body>
</html> 