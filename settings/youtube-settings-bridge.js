// YouTube Settings Bridge - Syncs Chrome storage settings to localStorage for MAIN world scripts
(function() {
    'use strict';
    
    // This script runs in ISOLATED world and has access to Chrome APIs
    // It syncs settings to localStorage for YouTube scripts that run in MAIN world
    
    // Function to sync settings to localStorage
    function syncSettingsToLocalStorage() {
        chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
            const settings = result.gmbExtractorSettings || {};
            
            // YouTube Ads Skipper settings
            localStorage.setItem('youtubeAdsSkipperEnabled', settings.youtubeAdsSkipperEnabled === true ? 'true' : 'false');
            
            // YouTube Screenshot settings
            localStorage.setItem('screenshotYouTubeEnabled', settings.screenshotYouTubeEnabled === true ? 'true' : 'false');
            localStorage.setItem('screenshotKeyEnabled', settings.screenshotKeyEnabled !== false ? 'true' : 'false');
            localStorage.setItem('screenshotFunctionality', settings.screenshotFunctionality !== undefined ? settings.screenshotFunctionality.toString() : '2');
            localStorage.setItem('screenshotFileFormat', settings.screenshotFileFormat || 'png');
            
            // YouTube Thumbnail Viewer settings
            localStorage.setItem('youtubeThumbnailViewerEnabled', settings.youtubeThumbnailViewerEnabled === true ? 'true' : 'false');
            
            // YouTube Frames settings
            localStorage.setItem('youtubeFramesEnabled', settings.youtubeFramesEnabled === true ? 'true' : 'false');
            
            // YouTube GIF settings
            localStorage.setItem('youtubeGifEnabled', settings.youtubeGifEnabled === true ? 'true' : 'false');
            
            // Debug mode
            localStorage.setItem('debugMode', settings.debugMode === true ? 'true' : 'false');
            
            console.log('[YouTube Settings Bridge] Settings synced to localStorage');
            
            // Dispatch storage events to notify YouTube scripts of changes
            ['screenshotYouTubeEnabled', 'youtubeFramesEnabled', 'youtubeThumbnailViewerEnabled', 'youtubeGifEnabled'].forEach(key => {
                window.dispatchEvent(new StorageEvent('storage', {
                    key: key,
                    newValue: localStorage.getItem(key),
                    url: window.location.href
                }));
            });
        });
    }
    
    // Sync on page load
    syncSettingsToLocalStorage();
    
    // Listen for settings updates
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'updateSettings' && message.settings) {
            // Sync new settings to localStorage
            const settings = message.settings;
            
            // YouTube Ads Skipper settings
            localStorage.setItem('youtubeAdsSkipperEnabled', settings.youtubeAdsSkipperEnabled === true ? 'true' : 'false');
            
            // YouTube Screenshot settings
            localStorage.setItem('screenshotYouTubeEnabled', settings.screenshotYouTubeEnabled === true ? 'true' : 'false');
            localStorage.setItem('screenshotKeyEnabled', settings.screenshotKeyEnabled !== false ? 'true' : 'false');
            localStorage.setItem('screenshotFunctionality', settings.screenshotFunctionality !== undefined ? settings.screenshotFunctionality.toString() : '2');
            localStorage.setItem('screenshotFileFormat', settings.screenshotFileFormat || 'png');
            
            // YouTube Thumbnail Viewer settings
            localStorage.setItem('youtubeThumbnailViewerEnabled', settings.youtubeThumbnailViewerEnabled === true ? 'true' : 'false');
            
            // YouTube Frames settings
            localStorage.setItem('youtubeFramesEnabled', settings.youtubeFramesEnabled === true ? 'true' : 'false');
            
            // YouTube GIF settings
            localStorage.setItem('youtubeGifEnabled', settings.youtubeGifEnabled === true ? 'true' : 'false');
            
            // Debug mode
            localStorage.setItem('debugMode', settings.debugMode === true ? 'true' : 'false');
            
            console.log('[YouTube Settings Bridge] Settings updated in localStorage');
            
            // Dispatch storage events to notify YouTube scripts of changes
            ['screenshotYouTubeEnabled', 'youtubeFramesEnabled', 'youtubeThumbnailViewerEnabled', 'youtubeGifEnabled'].forEach(key => {
                window.dispatchEvent(new StorageEvent('storage', {
                    key: key,
                    newValue: localStorage.getItem(key),
                    url: window.location.href
                }));
            });
        }
    });
})();