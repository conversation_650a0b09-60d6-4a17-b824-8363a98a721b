(function() {
    'use strict';
    
    // Prevent multiple initializations
    if (window.dragSelectLinksInitialized) {
        console.log('DragSelectLinks: Already initialized, skipping duplicate initialization');
        return;
    }
    
    // Mark as initialized
    window.dragSelectLinksInitialized = true;
    
    // Global flag for DOM cleanup
    window.DragSelectLinksActive = true;
    
    // Global variables for storing original body styles and scroll position
    let originalBodyStyles = null;
    let originalScrollPosition = 0;
    
    // Core variables for drag selection functionality
    let hotkey, mouseButton, zIndex = 1e18;
    const boxColor = '#7C3AED'; // Fixed purple color
    let isDragging = false, isKeyPressed = false;
    let selectionBox = null, counterElement = null;
    let selectedLinks = [], filterMode = "", filterText = "";
    let isCreatingPopup = false; // Prevent multiple popup creation
    let activePopup = null; // Track the single active popup instance
    
    // Helper function to force scroll restoration
    function forceScrollRestoration() {
        console.log('DragSelectLinks: Force scroll restoration called');
        
        // Remove all body style restrictions
        document.body.style.overflow = '';
        document.body.style.position = '';
        document.body.style.width = '';
        document.body.style.top = '';
        
        // Force document to be scrollable again
        document.documentElement.style.overflow = '';
        document.documentElement.style.position = '';
        
        // If we have stored scroll position, restore it
        if (originalScrollPosition > 0) {
            window.scrollTo(0, originalScrollPosition);
            originalScrollPosition = 0;
        }
        
        // Clear stored styles
        originalBodyStyles = null;
        
        console.log('DragSelectLinks: Force scroll restoration completed');
    }
    
    // Global cleanup function for DOM restoration
    window.dragSelectLinksCleanup = function() {
        try {
            // Clear highlights
            clearHighlights();
            
            // Hide selection box
            if (selectionBox) {
                selectionBox.style.visibility = 'hidden';
                if (selectionBox.parentNode) {
                    selectionBox.parentNode.removeChild(selectionBox);
                }
                selectionBox = null;
                counterElement = null;
            }
            
            // Remove all popups if they exist
            const allPopups = document.querySelectorAll('[data-primary-popup="true"], #drag-select-links-popup, [id*="drag-select-links"]');
            allPopups.forEach(popup => {
                if (popup.parentNode) {
                    popup.parentNode.removeChild(popup);
                }
            });
            
            // Reset popup tracking
            activePopup = null;
            
            // Remove drag select boxes from all links
            for (let i = 0; i < document.links.length; i++) {
                const link = document.links[i];
                if (link.dragSelectBox) {
                    if (link.dragSelectBox.parentNode) {
                        link.dragSelectBox.parentNode.removeChild(link.dragSelectBox);
                    }
                    delete link.dragSelectBox;
                }
                // Clean up position properties
                delete link.x1;
                delete link.x2;
                delete link.y1;
                delete link.y2;
            }
            
            // Always force scroll restoration to ensure page is scrollable again
            forceScrollRestoration();
            
            // Reset state variables
            isDragging = false;
            isKeyPressed = false;
            selectedLinks = [];
            isCreatingPopup = false; // Reset popup creation flag
            activePopup = null; // Reset popup tracking
            
            // Remove event listeners
            window.removeEventListener("mousemove", handleMouseMove, true);
            window.removeEventListener("mouseup", handleMouseUp, true);
            window.removeEventListener("contextmenu", preventContextMenu, true);
            
            // Remove escape key listener if it exists
            if (window.dragSelectLinksEscapeListener) {
                document.removeEventListener('keydown', window.dragSelectLinksEscapeListener);
                delete window.dragSelectLinksEscapeListener;
            }
            
            // Keep initialization flag as true since the script is still active
            // Only reset internal state variables, not the initialization status
            // window.dragSelectLinksInitialized stays true to prevent duplicate loading
            
            console.log('DragSelectLinks: Cleanup completed');
            
        } catch (error) {
            console.error('DragSelectLinks: Error during cleanup:', error);
            // Even if there's an error, force scroll restoration
            forceScrollRestoration();
        }
    };
    
    // Prevent event propagation
    function preventEvent(e) {
        e.stopPropagation();
        e.preventDefault();
    }
    
    // Update selection box dimensions and position
    function updateSelectionBox(pageX, pageY) {
        const maxWidth = Math.max(
            document.documentElement.clientWidth,
            document.body.scrollWidth,
            document.documentElement.scrollWidth,
            document.body.offsetWidth,
            document.documentElement.offsetWidth
        );
        const maxHeight = Math.max(
            document.documentElement.clientHeight,
            document.body.scrollHeight,
            document.documentElement.scrollHeight,
            document.body.offsetHeight,
            document.documentElement.offsetHeight
        );
        
        pageX = Math.min(pageX, maxWidth - 7);
        pageY = Math.min(pageY, maxHeight - 7);
        
        if (pageX > selectionBox.startX) {
            selectionBox.x1 = selectionBox.startX;
            selectionBox.x2 = pageX;
        } else {
            selectionBox.x1 = pageX;
            selectionBox.x2 = selectionBox.startX;
        }
        
        if (pageY > selectionBox.startY) {
            selectionBox.y1 = selectionBox.startY;
            selectionBox.y2 = pageY;
        } else {
            selectionBox.y1 = pageY;
            selectionBox.y2 = selectionBox.startY;
        }
        
        selectionBox.style.left = selectionBox.x1 + "px";
        selectionBox.style.width = (selectionBox.x2 - selectionBox.x1) + "px";
        selectionBox.style.top = selectionBox.y1 + "px";
        selectionBox.style.height = (selectionBox.y2 - selectionBox.y1) + "px";
    }
    
    // Clear highlights
    function clearHighlights() {
        for (let i = 0; i < document.links.length; i++) {
            const link = document.links[i];
            if (link.dragSelectBox) {
                link.dragSelectBox.style.visibility = "hidden";
            }
        }
    }
    
    // Highlight link
    function highlightLink(linkElement) {
        selectedLinks.push(linkElement);
        
        if (linkElement.dragSelectBox) {
            linkElement.dragSelectBox.style.visibility = "visible";
        } else {
            const box = document.createElement("span");
            box.style.margin = "0px auto";
            box.style.border = "2px solid " + (boxColor || "#2386ea");
            box.style.background = (boxColor || "#2386ea") + "20";
            box.style.position = "absolute";
            box.style.width = linkElement.offsetWidth + "px";
            box.style.height = linkElement.offsetHeight + "px";
            box.style.top = linkElement.y1 + "px";
            box.style.left = linkElement.x1 + "px";
            box.style.zIndex = zIndex;
            box.style.pointerEvents = "none";
            document.body.appendChild(box);
            linkElement.dragSelectBox = box;
        }
    }
    
    // Get element position with transform and scroll offset calculation
    function getElementPosition(element) {
        let x = 0, y = 0;
        let elem = element;
        
        do {
            const style = window.getComputedStyle(elem);
            const matrix = new WebKitCSSMatrix(style.webkitTransform);
            x += elem.offsetLeft + matrix.m41;
            y += elem.offsetTop + matrix.m42;
        } while (elem = elem.offsetParent);
        
        elem = element;
        while (elem && elem !== document.body) {
            if (elem.scrollLeft) x -= elem.scrollLeft;
            if (elem.scrollTop) y -= elem.scrollTop;
            elem = elem.parentNode;
        }
        
        return { x: x, y: y };
    }
    
    // Check if link should be included
    function shouldIncludeLink(linkElement) {
        // Exclude links inside Show Links popup
        if (linkElement.closest('.showlinks-panel') || 
            linkElement.closest('[data-primary-popup="true"]') ||
            linkElement.closest('#drag-select-links-popup') ||
            linkElement.closest('[id*="drag-select-links"]')) {
            return false;
        }
        
        const linkContent = linkElement.outerHTML;
        
        if (filterText === "" || filterMode === "") {
            return true;
        }
        
        let hasFilterText = false;
        filterText.split(",").forEach(function(filterItem) {
            if (linkContent.indexOf(filterItem) >= 0) {
                hasFilterText = true;
            }
        });
        
        if (filterMode === "1") { // exclude mode
            return filterText === "" || !hasFilterText;
        } else { // include mode
            return filterText === "" || hasFilterText;
        }
    }
    
    // Mouse move handler for drag selection
    function handleMouseMove(e) {
        // Don't process if mouse is over any popup
        if (e.target.closest('.showlinks-panel') || 
            e.target.closest('[data-primary-popup="true"]') ||
            e.target.closest('#drag-select-links-popup') ||
            e.target.closest('[id*="drag-select-links"]')) {
            return;
        }
        
        if (isDragging) {
            updateSelectionBox(e.pageX, e.pageY);
        selectedLinks = [];
            
            for (let i = 0; i < document.links.length; i++) {
                const link = document.links[i];
                
                if (link.x2 > selectionBox.x1 && 
                    link.x1 < selectionBox.x2 && 
                    link.y2 > selectionBox.y1 && 
                    link.y1 < selectionBox.y2 &&
                    shouldIncludeLink(link) &&
                    link.href.indexOf('javascript') !== 0) {
                    highlightLink(link);
                } else if (link.dragSelectBox) {
                    link.dragSelectBox.style.visibility = "hidden";
                }
            }
            
            if (counterElement) {
                counterElement.innerText = selectedLinks.length;
            }
        }
    }
    
    // Mouse up handler for ending drag selection
    function handleMouseUp(e) {
        // Don't process if mouse is over any popup
        if (e.target.closest('.showlinks-panel') || 
            e.target.closest('[data-primary-popup="true"]') ||
            e.target.closest('#drag-select-links-popup') ||
            e.target.closest('[id*="drag-select-links"]')) {
            return;
        }
        
        if (isDragging) {
            preventEvent(e);
        isDragging = false;
        
            if (isKeyPressed) {
                isKeyPressed = false;
                clearHighlights();
        if (selectionBox) {
                    selectionBox.style.visibility = "hidden";
                }
                window.removeEventListener("mousemove", handleMouseMove, true);
                window.removeEventListener("mouseup", handleMouseUp, true);
                window.removeEventListener("contextmenu", preventContextMenu, true);
                
                if (selectedLinks.length > 0 && !isCreatingPopup && !activePopup) {
                    // Single call to showLinksPopup with all necessary checks
                    showLinksPopup();
                }
            }
        }
    }
    
    // Prevent context menu
    function preventContextMenu(e) {
        if (isKeyPressed) {
            e.preventDefault();
        }
    }
    
    // Mouse down listener for starting drag selection
    window.addEventListener("mousedown", function(e) {
        // Don't start drag selection if clicking inside Show Links popup or any extension popup
        if (e.target.closest('.showlinks-panel') || 
            e.target.closest('[data-primary-popup="true"]') ||
            e.target.closest('#drag-select-links-popup') ||
            e.target.closest('[id*="drag-select-links"]')) {
            return;
        }
        
        if (isKeyPressed && e.button === (mouseButton || 0)) { // Left click by default
            window.addEventListener("mousemove", handleMouseMove, true);
            window.addEventListener("mouseup", handleMouseUp, true);
            window.addEventListener("contextmenu", preventContextMenu, true);
            
            isDragging = true;
            preventEvent(e);
            
            if (!selectionBox) {
                selectionBox = document.createElement("span");
                selectionBox.style.margin = "0px auto";
                selectionBox.style.border = "2px dotted " + (boxColor || "#2386ea");
                selectionBox.style.position = "absolute";
                selectionBox.style.zIndex = zIndex;
                selectionBox.style.pointerEvents = "none";
                
                counterElement = document.createElement("span");
                counterElement.style.position = "absolute";
                counterElement.style.bottom = "0";
                counterElement.style.right = "4px";
                counterElement.style.background = (boxColor || "#2386ea");
                counterElement.style.color = "white";
                counterElement.style.padding = "2px 6px";
                counterElement.style.borderRadius = "3px";
                counterElement.style.fontSize = "12px";
                counterElement.style.fontFamily = "Arial, sans-serif";
                counterElement.innerText = "0";
                counterElement.style.zIndex = zIndex;
                selectionBox.appendChild(counterElement);
                
                document.body.appendChild(selectionBox);
            } else {
                selectionBox.style.border = "2px dotted " + (boxColor || "#2386ea");
                selectionBox.style.visibility = "visible";
            }
            
            // Prepare links with calculated positions for intersection detection
            for (let i = 0; i < document.links.length; i++) {
                const link = document.links[i];
                
                // Skip links inside popups
                if (link.closest('.showlinks-panel') || 
                    link.closest('[data-primary-popup="true"]') ||
                    link.closest('#drag-select-links-popup') ||
                    link.closest('[id*="drag-select-links"]')) {
                    continue;
                }
                
                const position = getElementPosition(link);
                link.x1 = position.x;
                link.y1 = position.y;
                link.x2 = position.x + link.offsetWidth;
                link.y2 = position.y + link.offsetHeight;
            }
            
            selectionBox.startX = e.pageX;
            selectionBox.startY = e.pageY;
            updateSelectionBox(e.pageX, e.pageY);
            
            console.log('DragSelectLinks: Drag started');
        }
    }, true);
    
    // Key down listener for hotkey activation
    window.addEventListener("keydown", function(e) {
        // Don't activate drag select if focused inside Show Links popup or any extension popup
        if (e.target.closest('.showlinks-panel') || 
            e.target.closest('[data-primary-popup="true"]') ||
            e.target.closest('#drag-select-links-popup') ||
            e.target.closest('[id*="drag-select-links"]') ||
            (document.activeElement && document.activeElement.closest && document.activeElement.closest('.showlinks-panel')) ||
            (document.activeElement && document.activeElement.closest && document.activeElement.closest('[data-primary-popup="true"]')) ||
            (document.activeElement && document.activeElement.closest && document.activeElement.closest('#drag-select-links-popup')) ||
            (document.activeElement && document.activeElement.closest && document.activeElement.closest('[id*="drag-select-links"]'))) {
            return;
        }
        
        // Safety check for chrome extension context
        if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.sync) {
            return;
        }
        
        try {
            // Load settings synchronously from extension storage
            chrome.storage.local.get(['gmbExtractorSettings'], function(result) {
                // Check for chrome runtime errors
                if (chrome.runtime.lastError) {
                    return;
                }
                
                const settings = result.gmbExtractorSettings || {};
                
                hotkey = parseInt(settings.dragSelectLinksHotkey) || 90;
                mouseButton = 0; // Always left click
                filterMode = settings.dragSelectLinksFilterMode === 'exclude' ? "1" : "0";
                filterText = settings.dragSelectLinksFilterText || "";
                const isEnabled = settings.dragSelectLinksEnabled !== false;
                
                // Only log and activate if it's actually the hotkey being pressed
                if (e.keyCode === hotkey && isEnabled) {
                    isKeyPressed = true;
                    console.log('DragSelectLinks: Selection mode ACTIVATED (Z key pressed)');
                }
            });
        } catch (error) {
            // Silent error handling - don't log every keypress error
        }
    }, true);
    
    // Key up listener for hotkey deactivation
    window.addEventListener("keyup", function(e) {
        // Don't deactivate drag select if focused inside Show Links popup or any extension popup
        if (e.target.closest('.showlinks-panel') || 
            e.target.closest('[data-primary-popup="true"]') ||
            e.target.closest('#drag-select-links-popup') ||
            e.target.closest('[id*="drag-select-links"]') ||
            (document.activeElement && document.activeElement.closest && document.activeElement.closest('.showlinks-panel')) ||
            (document.activeElement && document.activeElement.closest && document.activeElement.closest('[data-primary-popup="true"]')) ||
            (document.activeElement && document.activeElement.closest && document.activeElement.closest('#drag-select-links-popup')) ||
            (document.activeElement && document.activeElement.closest && document.activeElement.closest('[id*="drag-select-links"]'))) {
            return;
        }
        
        // Only process if it's the Z key (90) and we're currently in selection mode
        if (e.keyCode === 90 && isKeyPressed) {
            isKeyPressed = false;
            clearHighlights();
            if (selectionBox) {
                selectionBox.style.visibility = "hidden";
            }
            window.removeEventListener("mousemove", handleMouseMove, true);
            window.removeEventListener("mouseup", handleMouseUp, true);
            window.removeEventListener("contextmenu", preventContextMenu, true);
            
            console.log('DragSelectLinks: Selection mode DEACTIVATED (Z key released)');
        }
    }, true);
    
    console.log('DragSelectLinks: Drag selection implementation loaded');
    
    // Helper functions for checkbox functionality
    function getSelectedLinks() {
        const popup = document.querySelector('[data-primary-popup="true"]');
        if (!popup) return [];
        
        const selectedLinks = [];
        const originalLinks = popup._originalLinks || [];
        
        // Get only visible checkboxes (not filtered out)
        const visibleCheckboxes = popup.querySelectorAll('tbody tr:not([style*="display: none"]) input[type="checkbox"][data-link-index]');
        
        visibleCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const linkIndex = parseInt(checkbox.dataset.linkIndex);
                if (originalLinks[linkIndex]) {
                    selectedLinks.push(originalLinks[linkIndex]);
                }
            }
        });
        
        return selectedLinks;
    }
    
    function updateActionButtons() {
        const popup = document.querySelector('[data-primary-popup="true"]');
        if (!popup) return;
        
        const selectedCount = getSelectedLinks().length;
        const openBtn = popup.querySelector('button');
        const formatButtons = popup.querySelectorAll('.format-button');
        
        // Update button text to show selected count
        if (openBtn) {
            openBtn.textContent = `Open Selected Links (${selectedCount})`;
            openBtn.disabled = selectedCount === 0;
            openBtn.style.opacity = selectedCount === 0 ? '0.5' : '1';
        }
        
        // Update format buttons
        formatButtons.forEach(btn => {
            btn.disabled = selectedCount === 0;
            btn.style.opacity = selectedCount === 0 ? '0.5' : '1';
        });
    }
    
    // Advanced settings variables
    let removeDuplicates = true;
    let includeText = true;
    let textFormat = 'text-combined';
    let htmlFormat = 'html-url';
    let jsonFormat = 'json-url';
    let customFormat = '<a href="{url}">{title}</a>{br}';
    
    // Format processing functions
    function formatLinks(links, format) {
        switch (format) {
            case 'text-url':
                return links.map(link => link.href).join('\n');
                
            case 'text-title':
                return links.map(link => link.textContent.trim() || 'Untitled').join('\n');
                
            case 'text-combined':
                return links.map(link => {
                    const title = link.textContent.trim() || 'Untitled';
                    return `${title}    ${link.href}`;
                }).join('\n');
                
            case 'html-url':
                return links.map(link => 
                    `<a href="${link.href}">${link.href}</a>`
                ).join('\n');
                
            case 'html-title':
                return links.map(link => {
                    const title = link.textContent.trim() || link.href;
                    return `<a href="${link.href}">${title}</a>`;
                }).join('\n');
                
            case 'json-url':
                return JSON.stringify(links.map(link => ({ url: link.href })), null, 2);
                
            case 'json-title':
                return JSON.stringify(links.map(link => ({ title: link.textContent.trim() || 'Untitled' })), null, 2);
                
            case 'json-both':
                return JSON.stringify(links.map(link => ({ 
                    url: link.href, 
                    title: link.textContent.trim() || 'Untitled' 
                })), null, 2);
                
            case 'custom':
                return links.map(link => {
                    const title = link.textContent.trim() || 'Untitled';
                    return customFormat
                        .replace(/{url}/g, link.href)
                        .replace(/{title}/g, title)
                        .replace(/{br}/g, '\n');
                }).join('');
                
            default:
                return links.map(link => {
                    const title = link.textContent.trim() || 'Untitled';
                    return `${title}    ${link.href}`;
                }).join('\n');
        }
    }
    
    function getFormatName(format) {
        const formatNames = {
            'text-url': 'URLs only',
            'text-title': 'Titles only',
            'text-combined': 'Combined Text',
            'html-url': 'HTML (URL text)',
            'html-title': 'HTML (Title text)',
            'json-url': 'JSON (URLs)',
            'json-title': 'JSON (Titles)',
            'json-both': 'JSON (Both)',
            'custom': 'Custom Format'
        };
        return formatNames[format] || 'Default Format';
    }

    // Comprehensive popup creation following Quick Actions design system
    async function showLinksPopup() {
        // Absolute prevention - if popup exists or is being created, exit immediately
        if (isCreatingPopup || activePopup || document.querySelector('[data-primary-popup="true"]')) {
            console.log('DragSelectLinks: Popup already exists or being created, skipping');
            return;
        }
        
        console.log('DragSelectLinks: Starting single popup creation');
        isCreatingPopup = true;
        
        // Force cleanup of any existing popups first - more aggressive
        const existingPopups = document.querySelectorAll('[data-primary-popup="true"], #drag-select-links-popup, [id*="drag-select-links"]');
        console.log(`DragSelectLinks: Found ${existingPopups.length} existing popups to clean up`);
        existingPopups.forEach(popup => {
            if (popup.parentNode) {
                popup.parentNode.removeChild(popup);
            }
        });
        
        // Reset tracking
        activePopup = null;
        
        // Force scroll restoration after cleanup
        forceScrollRestoration();
        
        // Reload settings before showing popup to ensure we have the latest format settings
        await updateAdvancedSettings();
        console.log('DragSelectLinks: Settings reloaded before popup creation');
        
        // Create popup immediately without setTimeout to avoid timing issues
        try {
            createLinksPopupWithSettings();
        } catch (error) {
            console.error('DragSelectLinks: Error creating popup:', error);
        } finally {
            isCreatingPopup = false;
        }
    }
    
    function createLinksPopupWithSettings() {
        // Process links
        let processedLinks = [...selectedLinks];
        
        // Inject CSS override to fix Microthemer styling conflicts
        const styleOverride = document.createElement('style');
        styleOverride.id = 'drag-select-links-style-override';
        styleOverride.textContent = `
            .links-section-table-container > th {
                background-color: #3a3a3a !important;
            }
            [data-primary-popup="true"] .links-section-table-container > th {
                background-color: #3a3a3a !important;
                color: #9ca3af !important;
            }
            [data-primary-popup="true"] thead {
                background-color: #0a0a0a !important;
            }
        `;
        
        // Remove existing override if it exists
        const existingOverride = document.getElementById('drag-select-links-style-override');
        if (existingOverride) {
            existingOverride.remove();
        }
        
        document.head.appendChild(styleOverride);
        
        // Create popup with dark styling and unique ID
        const popup = document.createElement('div');
        const popupId = 'drag-select-links-popup-' + Date.now();
        popup.id = popupId;
        popup.setAttribute('data-primary-popup', 'true');
        popup.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            width: 60%;
            max-width: 800px;
            max-height: 85vh;
            z-index: 9999999;
            background: #0a0a0a;
            color: #d1d5db;
            border: 1px solid #3a3a3a;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
            padding: 20px;
            overflow: hidden;
            min-width: 400px;
            min-height: 500px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            outline: none;
            user-select: none;
        `;
        
        // Add resize handles for all directions
        addResizeHandles(popup);
        
        // Create header
        const header = document.createElement('div');
        header.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #2a2a2a;
            cursor: move;
        `;
        
        const title = document.createElement('h1');
        title.textContent = `Selected Links (${processedLinks.length})`;
        title.style.cssText = `
            margin: 0;
            color: #d1d5db;
            font-size: 18px;
            font-weight: 600;
            letter-spacing: -0.5px;
        `;
        
        const closeBtn = document.createElement('button');
        closeBtn.textContent = '✕';
        closeBtn.style.cssText = `
            padding: 8px 12px;
            background: #374151;
            color: #d1d5db;
            border: 1px solid #4b5563;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            min-width: 40px;
        `;
        

        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.background = '#4b5563';
        });
        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.background = '#374151';
        });
        
        header.appendChild(title);
        header.appendChild(closeBtn);
        
        // Create filter controls section
        const filterSection = createFilterSection();
        
        // Create actions section
        const actionsSection = createActionsSection(processedLinks);
        
        // Create links section
        const linksSection = createLinksSection(processedLinks);
        
        // Store references for dynamic updates
        popup._actionsSection = actionsSection;
        popup._linksSection = linksSection;
        popup._title = title;
        popup._originalLinks = [...selectedLinks];
        popup._currentFilteredLinks = [...processedLinks];
        
        // Set as the active popup
        activePopup = popup;
        
        // Create top section container for filters and actions side by side
        const topSection = document.createElement('div');
        topSection.style.cssText = `
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: flex-start;
        `;
        
        // Make filter section flex to take remaining space
        filterSection.style.flex = '1';
        filterSection.style.minWidth = '300px';
        
        // Make actions section fixed width and compact
        actionsSection.style.flex = '0 0 auto';
        actionsSection.style.minWidth = '280px';
        
        topSection.appendChild(filterSection);
        topSection.appendChild(actionsSection);
        
        popup.appendChild(header);
        popup.appendChild(topSection);
        popup.appendChild(linksSection);
        
        document.body.appendChild(popup);
        
        // Prevent background scrolling - store original values globally
        originalBodyStyles = {
            overflow: document.body.style.overflow,
            position: document.body.style.position,
            width: document.body.style.width,
            top: document.body.style.top
        };
        
        // Get current scroll position before fixing
        originalScrollPosition = window.scrollY;
        
        console.log('DragSelectLinks: Storing original body styles:', originalBodyStyles);
        console.log('DragSelectLinks: Storing original scroll position:', originalScrollPosition);
        
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.width = '100%';
        document.body.style.top = `-${originalScrollPosition}px`;
        
        // Make draggable
        makeDraggable(popup, header);
        
        // Cleanup function with proper DOM restoration
        const cleanup = () => {
            // Remove popup first
            popup.remove();
            
            // Remove injected CSS override
            const styleOverride = document.getElementById('drag-select-links-style-override');
            if (styleOverride) {
                styleOverride.remove();
            }
            
            // Clear highlights and selection box
            clearHighlights();
            if (selectionBox) {
                selectionBox.style.visibility = 'hidden';
            }
            
            // Reset popup tracking
            activePopup = null;
            isCreatingPopup = false;
            
            // Always force scroll restoration
            forceScrollRestoration();
            
            console.log('DragSelectLinks: Popup cleanup completed');
        };
        
        // Close button functionality
        closeBtn.addEventListener('click', cleanup);
        
        // Escape key handling
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                cleanup();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
        
        // Store escape listener for cleanup
        window.dragSelectLinksEscapeListener = handleEscape;
        
        console.log('DragSelectLinks: Comprehensive popup shown with', processedLinks.length, 'links');
    }
    
    function createFilterSection() {
        const section = document.createElement('div');
        section.style.cssText = `
            background: #111111;
            border-radius: 8px;
            border: 1px solid #2a2a2a;
            margin-bottom: 20px;
            overflow: hidden;
        `;
        
        const sectionHeader = document.createElement('div');
        sectionHeader.textContent = 'Filters';
        sectionHeader.style.cssText = `
            background: #0a0a0a;
            padding: 12px 16px;
            border-bottom: 1px solid #3a3a3a;
            font-weight: 500;
            color: #9ca3af;
            font-size: 13px;
        `;
        
        const content = document.createElement('div');
        content.style.cssText = `padding: 16px;`;
        
        // Filter controls container - single row layout
        const filtersContainer = document.createElement('div');
        filtersContainer.style.cssText = `
            display: flex;
            flex-wrap: wrap;
            gap: 16px 24px;
            align-items: center;
            margin-bottom: 16px;
        `;
        
        // Remove duplicates toggle
        const duplicatesControl = document.createElement('div');
        duplicatesControl.style.cssText = `display: flex; align-items: center; gap: 8px;`;
        
        const duplicatesCheckbox = document.createElement('input');
        duplicatesCheckbox.type = 'checkbox';
        duplicatesCheckbox.id = 'remove-duplicates';
        duplicatesCheckbox.checked = removeDuplicates;
        duplicatesCheckbox.style.cssText = `
            width: 16px; height: 16px; accent-color: #7c3aed;
            background: #262626; border: 1px solid #404040;
            border-radius: 3px; cursor: pointer;
        `;
        
        const duplicatesLabel = document.createElement('label');
        duplicatesLabel.htmlFor = 'remove-duplicates';
        duplicatesLabel.textContent = 'Remove duplicates';
        duplicatesLabel.style.cssText = `
            color: #d1d5db; font-size: 13px; cursor: pointer;
            user-select: none; white-space: nowrap;
        `;
        
        duplicatesControl.appendChild(duplicatesCheckbox);
        duplicatesControl.appendChild(duplicatesLabel);
        
        // Group by domain toggle
        const domainControl = document.createElement('div');
        domainControl.style.cssText = `display: flex; align-items: center; gap: 8px;`;
        
        const domainCheckbox = document.createElement('input');
        domainCheckbox.type = 'checkbox';
        domainCheckbox.id = 'group-by-domain';
        domainCheckbox.checked = false;
        domainCheckbox.style.cssText = `
            width: 16px; height: 16px; accent-color: #7c3aed;
            background: #262626; border: 1px solid #404040;
            border-radius: 3px; cursor: pointer;
        `;
        
        const domainLabel = document.createElement('label');
        domainLabel.htmlFor = 'group-by-domain';
        domainLabel.textContent = 'Group by Domain';
        domainLabel.style.cssText = `
            color: #d1d5db; font-size: 13px; cursor: pointer;
            user-select: none; white-space: nowrap;
        `;
        
        domainControl.appendChild(domainCheckbox);
        domainControl.appendChild(domainLabel);
        
        // Content filter input - inline
        const filterInputContainer = document.createElement('div');
        filterInputContainer.style.cssText = `
            display: flex; align-items: center; gap: 8px; flex: 1; min-width: 200px;
        `;
        
        const filterLabel = document.createElement('label');
        filterLabel.textContent = 'Filter:';
        filterLabel.style.cssText = `
            color: #9ca3af; font-size: 13px; font-weight: 500;
            white-space: nowrap;
        `;
        
        const filterInput = document.createElement('input');
        filterInput.type = 'text';
        filterInput.placeholder = 'Filter by URL or text...';
        filterInput.style.cssText = `
            flex: 1; padding: 6px 10px; background: #262626;
            border: 1px solid #404040; border-radius: 6px;
            color: #d1d5db; font-size: 13px;
            transition: border-color 0.2s; min-width: 150px;
        `;
        
        filterInput.addEventListener('focus', () => {
            filterInput.style.borderColor = '#7c3aed';
        });
        filterInput.addEventListener('blur', () => {
            filterInput.style.borderColor = '#404040';
        });
        
        filterInputContainer.appendChild(filterLabel);
        filterInputContainer.appendChild(filterInput);
        
        filtersContainer.appendChild(duplicatesControl);
        filtersContainer.appendChild(domainControl);
        filtersContainer.appendChild(filterInputContainer);
        
        content.appendChild(filtersContainer);
        section.appendChild(sectionHeader);
        section.appendChild(content);
        
        // Add event listeners for real-time filtering
        const applyFilters = () => {
            console.log('DragSelectLinks: applyFilters called');
            const popup = document.querySelector('[data-primary-popup="true"]');
            if (!popup) {
                console.log('DragSelectLinks: No popup found, skipping filter application');
                return;
            }
            
            const removeDups = duplicatesCheckbox.checked;
            const groupByDomain = domainCheckbox.checked;
            const filterText = filterInput.value.toLowerCase();
            
            console.log('DragSelectLinks: Filter settings - removeDups:', removeDups, 'groupByDomain:', groupByDomain, 'filterText:', filterText);
            
            let filteredLinks = [...popup._originalLinks];
            
            // Apply content filter
            if (filterText) {
                filteredLinks = filteredLinks.filter(link => 
                    link.href.toLowerCase().includes(filterText) || 
                    (link.textContent && link.textContent.toLowerCase().includes(filterText))
                );
            }
            
            // Apply remove duplicates
            if (removeDups) {
                const urlSet = new Set();
                filteredLinks = filteredLinks.filter(link => {
                    if (urlSet.has(link.href)) {
                        return false;
                    }
                    urlSet.add(link.href);
                    return true;
                });
            }
            
            console.log('DragSelectLinks: Filtered links count:', filteredLinks.length);
            
            // Filter table rows directly instead of recreating sections
            filterTableRows(popup, filteredLinks, groupByDomain);
        };
        
        duplicatesCheckbox.addEventListener('change', (e) => {
            e.stopPropagation();
            applyFilters();
        });
        domainCheckbox.addEventListener('change', (e) => {
            e.stopPropagation();
            console.log('DragSelectLinks: Group by Domain checkbox clicked, checked:', domainCheckbox.checked);
            applyFilters();
        });
        filterInput.addEventListener('input', (e) => {
            e.stopPropagation();
            applyFilters();
        });
        
        return section;
    }
    
    // Rebuild table body with filtered links - based on old version approach
    function filterTableRows(popup, filteredLinks, groupByDomain = false) {
        try {
            // Update title
            if (popup._title) {
                popup._title.textContent = `Selected Links (${filteredLinks.length})`;
            }
            
            // Store current filtered links for actions
            popup._currentFilteredLinks = filteredLinks;
            
            // Find the table container
            const tableContainer = popup.querySelector('.links-section-table-container');
            if (!tableContainer) {
                console.error('DragSelectLinks: Could not find table container');
                return;
            }
            
            console.log('DragSelectLinks: Rebuilding table with', filteredLinks.length, 'filtered links');
            
            // Clear existing content completely to prevent duplicates
            while (tableContainer.firstChild) {
                tableContainer.removeChild(tableContainer.firstChild);
            }
            console.log('DragSelectLinks: Table container completely cleared');
            
            if (groupByDomain) {
                // Group links by domain
                const domainGroups = {};
                filteredLinks.forEach(link => {
                    try {
                        const domain = new URL(link.href).hostname;
                        if (!domainGroups[domain]) {
                            domainGroups[domain] = [];
                        }
                        domainGroups[domain].push(link);
                    } catch (e) {
                        // Handle invalid URLs
                        if (!domainGroups['Invalid URLs']) {
                            domainGroups['Invalid URLs'] = [];
                        }
                        domainGroups['Invalid URLs'].push(link);
                    }
                });
                
                // Create grouped display
                Object.keys(domainGroups).sort().forEach(domain => {
                    const domainSection = document.createElement('div');
                    
                    const domainHeader = document.createElement('div');
                    domainHeader.textContent = `${domain} (${domainGroups[domain].length})`;
                    domainHeader.style.cssText = `
                        background: #1a1a1a;
                        padding: 12px 20px;
                        border-bottom: 1px solid #2a2a2a;
                        font-weight: 600;
                        color: #7c3aed;
                        font-size: 13px;
                    `;
                    
                    const domainTable = createLinksTable(domainGroups[domain]);
                    
                    domainSection.appendChild(domainHeader);
                    domainSection.appendChild(domainTable);
                    tableContainer.appendChild(domainSection);
                });
            } else {
                // Create regular table with filtered links
                console.log('DragSelectLinks: Creating new table with', filteredLinks.length, 'links');
                const newTable = createLinksTable(filteredLinks);
                console.log('DragSelectLinks: New table created:', newTable);
                tableContainer.appendChild(newTable);
                console.log('DragSelectLinks: Table appended to container, new content length:', tableContainer.innerHTML.length);
            }
            
            // Force browser repaint/reflow - fixed variable shadowing issue
            console.log('DragSelectLinks: Forcing browser repaint...');
            tableContainer.style.display = 'none';
            tableContainer.offsetHeight; // Force reflow
            tableContainer.style.display = '';
            
            // Alternative force repaint methods - use the popup parameter instead of creating new const
            if (popup) {
                popup.style.transform = 'translateZ(0)';
                setTimeout(() => {
                    popup.style.transform = '';
                }, 1);
            }
            
            console.log('DragSelectLinks: Successfully rebuilt table with filtered links');
            
        } catch (error) {
            console.error('DragSelectLinks: Error rebuilding table:', error);
        }
    }
    
    function addResizeHandles(popup) {
        // Enable browser native resize functionality
        popup.style.resize = 'both';
        popup.style.overflow = 'hidden'; // Changed from 'auto' to prevent scrolling issues
        
        // Add proper constraints for minimum size
        popup.style.minWidth = '400px';
        popup.style.minHeight = '500px';
        popup.style.maxWidth = '95vw';
        popup.style.maxHeight = '95vh';
        
        // Add a subtle resize indicator in bottom right
        const resizeIndicator = document.createElement('div');
        resizeIndicator.style.cssText = `
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 15px;
            height: 15px;
            cursor: se-resize;
            z-index: 10;
            opacity: 0.4;
            background-image: 
                linear-gradient(45deg, transparent 40%, #666 40%, #666 60%, transparent 60%),
                linear-gradient(45deg, transparent 50%, #666 50%, #666 70%, transparent 70%);
            background-size: 8px 8px, 4px 4px;
            background-position: 0 0, 2px 2px;
            pointer-events: none;
        `;
        
        // Add resize observer to maintain scroll functionality
        if (window.ResizeObserver) {
            const resizeObserver = new ResizeObserver(entries => {
                for (let entry of entries) {
                    const target = entry.target;
                    const linksSection = target.querySelector('.links-section-table-container');
                    if (linksSection) {
                        // Calculate available height for links section
                        const popupHeight = target.offsetHeight;
                        const header = target.querySelector('div:first-child');
                        const filtersSection = target.querySelector('div:nth-child(2)');
                        const actionsSection = target.querySelector('div:nth-child(3)');
                        
                        let usedHeight = 40; // padding
                        if (header) usedHeight += header.offsetHeight + 20;
                        if (filtersSection) usedHeight += filtersSection.offsetHeight + 20;
                        if (actionsSection) usedHeight += actionsSection.offsetHeight + 20;
                        
                        const availableHeight = popupHeight - usedHeight;
                        const newHeight = Math.max(150, Math.min(availableHeight, 600));
                        
                        linksSection.style.maxHeight = `${newHeight}px`;
                        linksSection.style.height = `${newHeight}px`;
                    }
                }
            });
            resizeObserver.observe(popup);
            
            // Initial height calculation
            setTimeout(() => {
                const linksSection = popup.querySelector('.links-section-table-container');
                if (linksSection) {
                    resizeObserver.disconnect();
                    resizeObserver.observe(popup);
                }
            }, 100);
        }
        
        popup.appendChild(resizeIndicator);
    }
    

    

    
    function createLinksSection(links, groupByDomain = false) {
        const section = document.createElement('div');
        section.style.cssText = `
            background: #111111;
            border-radius: 8px;
            border: 1px solid #2a2a2a;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 300px;
        `;
        
        const sectionHeader = document.createElement('div');
        sectionHeader.textContent = 'Links';
        sectionHeader.style.cssText = `
            background: #1a1a1a;
            padding: 16px 20px;
            border-bottom: 1px solid #2a2a2a;
            font-weight: 500;
            color: #9ca3af;
            font-size: 14px;
        `;
        
        const tableContainer = document.createElement('div');
        tableContainer.className = 'links-section-table-container';
        tableContainer.style.cssText = `
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 400px;
            min-height: 200px;
            border: 1px solid #2a2a2a;
            border-radius: 6px;
        `;
        
        if (groupByDomain) {
            // Group links by domain
            const domainGroups = {};
            links.forEach(link => {
                try {
                    const domain = new URL(link.href).hostname;
                    if (!domainGroups[domain]) {
                        domainGroups[domain] = [];
                    }
                    domainGroups[domain].push(link);
                } catch (e) {
                    // Handle invalid URLs
                    if (!domainGroups['Invalid URLs']) {
                        domainGroups['Invalid URLs'] = [];
                    }
                    domainGroups['Invalid URLs'].push(link);
                }
            });
            
            // Create grouped display
            Object.keys(domainGroups).sort().forEach(domain => {
                const domainSection = document.createElement('div');
                
                const domainHeader = document.createElement('div');
                domainHeader.textContent = `${domain} (${domainGroups[domain].length})`;
                domainHeader.style.cssText = `
                    background: #0a0a0a;
                    padding: 12px 20px;
                    border-bottom: 1px solid #3a3a3a;
                    font-weight: 600;
                    color: #7c3aed;
                    font-size: 13px;
                `;
                
                const domainTable = createLinksTable(domainGroups[domain]);
                
                domainSection.appendChild(domainHeader);
                domainSection.appendChild(domainTable);
                tableContainer.appendChild(domainSection);
            });
        } else {
            // Create regular table
            tableContainer.appendChild(createLinksTable(links));
        }
        
        section.appendChild(sectionHeader);
        section.appendChild(tableContainer);
        
        return section;
    }
    
    function createLinksTable(links) {
        const table = document.createElement('table');
        table.style.cssText = `
            width: 100%;
            border-collapse: collapse;
        `;
        
        // Table header
        const thead = document.createElement('thead');
        thead.style.cssText = `
            background-color: #0a0a0a!important;
            position: sticky;
            top: 0;
            z-index: 10;
        `;
        
        const headerRow = document.createElement('tr');
        
        // Add checkbox header
        const checkboxTh = document.createElement('th');
        checkboxTh.style.cssText = `
            padding: 12px 20px;
            color: #9ca3af !important;
            font-size: 13px;
            font-weight: 500;
            text-align: center;
            background-color: #3a3a3a !important;
            border-bottom: 1px solid #3a3a3a;
            border-right: 1px solid #3a3a3a;
            width: 50px;
        `;
        
        // Master checkbox to select/deselect all
        const masterCheckbox = document.createElement('input');
        masterCheckbox.type = 'checkbox';
        masterCheckbox.checked = true; // Default all selected
        masterCheckbox.style.cssText = `
            width: 16px; height: 16px; accent-color: #7c3aed;
            cursor: pointer;
        `;
        
        checkboxTh.appendChild(masterCheckbox);
        headerRow.appendChild(checkboxTh);
        
        const headers = includeText ? ['Title', 'Link'] : ['Link'];
        headers.forEach(headerText => {
            const th = document.createElement('th');
            th.textContent = headerText;
            th.style.cssText = `
                padding: 12px 20px;
                color: #9ca3af !important;
                font-size: 13px;
                font-weight: 500;
                text-align: left;
                background-color: #3a3a3a !important;
                border-bottom: 1px solid #3a3a3a;
                border-right: 1px solid #3a3a3a;
            `;
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // Table body
        const tbody = document.createElement('tbody');
        
        links.forEach((link, index) => {
            const row = document.createElement('tr');
            row.style.cssText = `
                border-bottom: 1px solid #3a3a3a;
                transition: background 0.2s;
            `;
            
            row.addEventListener('mouseenter', () => {
                row.style.background = '#0a0a0a';
            });
            row.addEventListener('mouseleave', () => {
                row.style.background = '';
            });
            
            // Checkbox cell
            const checkboxCell = document.createElement('td');
            checkboxCell.style.cssText = `
                padding: 16px 20px;
                vertical-align: top;
                border-right: 1px solid #3a3a3a;
                text-align: center;
            `;
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.checked = true; // Default selected
            checkbox.dataset.linkIndex = index;
            checkbox.style.cssText = `
                width: 16px; height: 16px; accent-color: #7c3aed;
                cursor: pointer;
            `;
            
            checkboxCell.appendChild(checkbox);
            row.appendChild(checkboxCell);
            
            // Title cell (if enabled) - moved before URL
            if (includeText) {
                const textCell = document.createElement('td');
                textCell.textContent = link.textContent.trim() || '(no text)';
                textCell.style.cssText = `
                    padding: 16px 20px;
                    vertical-align: top;
                    border-right: 1px solid #3a3a3a;
                    line-height: 1.5;
                    color: #d1d5db;
                    cursor: pointer;
                `;
                
                textCell.addEventListener('click', () => {
                    if (link.textContent.trim()) {
                        navigator.clipboard.writeText(link.textContent.trim()).then(() => {
                            row.style.background = '#2a2a2a';
                            setTimeout(() => {
                                row.style.background = '';
                            }, 800);
                        });
                    }
                });
                
                row.appendChild(textCell);
            }
            
            // URL cell
            const urlCell = document.createElement('td');
            urlCell.textContent = link.href;
            urlCell.style.cssText = `
                padding: 16px 20px;
                vertical-align: top;
                border-right: 1px solid #3a3a3a;
                line-height: 1.5;
                color: #e5e7eb;
                word-break: break-all;
                cursor: pointer;
            `;
            
            urlCell.addEventListener('click', () => {
                navigator.clipboard.writeText(link.href).then(() => {
                    row.style.background = '#2a2a2a';
                    setTimeout(() => {
                        row.style.background = '';
                    }, 800);
                });
            });
            
            row.appendChild(urlCell);
            tbody.appendChild(row);
        });
        
        // Master checkbox functionality
        masterCheckbox.addEventListener('change', () => {
            // Only affect visible checkboxes
            const visibleCheckboxes = tbody.querySelectorAll('tr:not([style*="display: none"]) input[type="checkbox"]');
            visibleCheckboxes.forEach(cb => {
                cb.checked = masterCheckbox.checked;
            });
            updateActionButtons();
        });
        
        // Individual checkbox listeners
        tbody.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox') {
                updateActionButtons();
                
                // Update master checkbox state based on visible rows only
                const visibleCheckboxes = tbody.querySelectorAll('tr:not([style*="display: none"]) input[type="checkbox"]');
                const checkedCount = tbody.querySelectorAll('tr:not([style*="display: none"]) input[type="checkbox"]:checked').length;
                
                if (checkedCount === 0) {
                    masterCheckbox.checked = false;
                    masterCheckbox.indeterminate = false;
                } else if (checkedCount === visibleCheckboxes.length) {
                    masterCheckbox.checked = true;
                    masterCheckbox.indeterminate = false;
                } else {
                    masterCheckbox.checked = false;
                    masterCheckbox.indeterminate = true;
                }
            }
        });
        
        table.appendChild(tbody);
        return table;
    }
    
    // Make popup draggable
    function makeDraggable(element, handle) {
        let isDraggingPopup = false;
        let currentX, currentY, initialX, initialY, xOffset = 0, yOffset = 0;
        
        handle.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
        
        function dragStart(e) {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;
            
            if (e.target === handle || handle.contains(e.target)) {
                isDraggingPopup = true;
                handle.style.cursor = 'grabbing';
            }
        }
        
        function drag(e) {
            if (isDraggingPopup) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                xOffset = currentX;
                yOffset = currentY;
                
                element.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        }
        
        function dragEnd() {
            isDraggingPopup = false;
            handle.style.cursor = 'move';
        }
    }
    
    // Context validation for drag select links
    function isExtensionContextValid() {
        try {
            return !!(chrome && chrome.storage && chrome.storage.local && chrome.runtime && chrome.runtime.id);
        } catch (error) {
            return false;
        }
    }

    // Safe Chrome storage wrapper for drag select links
    async function safeChromeStorageGet(keys) {
        try {
            if (!isExtensionContextValid()) {
                return {};
            }
            return await chrome.storage.local.get(keys);
        } catch (error) {
            if (error.message && error.message.includes('Extension context invalidated')) {
                return {};
            }
            throw error;
        }
    }

    // Load advanced settings from storage
    async function updateAdvancedSettings(callback) {
        try {
            // Enhanced context validation
            if (!isExtensionContextValid()) {
                // Use defaults if chrome APIs not available - silent handling
                removeDuplicates = true;
                includeText = true;
                textFormat = 'text-combined';
                htmlFormat = 'html-url';
                jsonFormat = 'json-url';
                customFormat = '<a href="{url}">{title}</a>{br}';
                
                if (callback && typeof callback === 'function') {
                    callback();
                }
                return;
            }
            
            const result = await safeChromeStorageGet(['gmbExtractorSettings']);
            
            const settings = result.gmbExtractorSettings || {};
            
            // Settings loaded from storage (reduced logging for less spam)
            
            removeDuplicates = settings.dragSelectLinksRemoveDuplicates !== false;
            includeText = settings.dragSelectLinksIncludeText !== false;
            textFormat = settings.dragSelectTextFormat || 'text-combined';
            htmlFormat = settings.dragSelectHtmlFormat || 'html-url';
            jsonFormat = settings.dragSelectJsonFormat || 'json-url';
            customFormat = settings.dragSelectCustomFormat || '<a href="{url}">{title}</a>{br}';
            
            // Format settings processed (reduced logging for less spam)
            
            // Call callback if provided
            if (callback && typeof callback === 'function') {
                callback();
            }
        } catch (error) {
            // Silent handling for context invalidation errors
            if (error.message && error.message.includes('Extension context invalidated')) {
                // Context invalidated - use defaults silently
            } else {
                console.error('DragSelectLinks: Error loading settings:', error);
            }
            
            // Use defaults if loading fails
            removeDuplicates = true;
            includeText = true;
            textFormat = 'text-combined';
            htmlFormat = 'html-url';
            jsonFormat = 'json-url';
            customFormat = '<a href="{url}">{title}</a>{br}';
            
            if (callback && typeof callback === 'function') {
                callback();
            }
        }
    }
    
    // Load advanced settings initially
    (async () => {
        await updateAdvancedSettings();
        console.log('DragSelectLinks: Initial settings loaded');
    })();
    
    // Also reload after a delay to catch any settings that weren't loaded initially
    setTimeout(async () => {
        await updateAdvancedSettings();
        // Delayed settings reload completed (reduced logging for less spam)
    }, 1000);
    
    // Listen for settings updates for real-time changes
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
        try {
            chrome.runtime.onMessage.addListener((message) => {
                if (message.action === 'updateSettings' && message.settings) {
                    removeDuplicates = message.settings.dragSelectLinksRemoveDuplicates !== false;
                    includeText = message.settings.dragSelectLinksIncludeText !== false;
                    textFormat = message.settings.dragSelectTextFormat || 'text-combined';
                    htmlFormat = message.settings.dragSelectHtmlFormat || 'html-url';
                    jsonFormat = message.settings.dragSelectJsonFormat || 'json-url';
                    customFormat = message.settings.dragSelectCustomFormat || '<a href="{url}">{title}</a>{br}';
                    
                    console.log('DragSelectLinks: Settings updated in real-time', {
                        textFormat,
                        htmlFormat,
                        jsonFormat,
                        customFormat
                    });
                }
            });
        } catch (error) {
            console.log('DragSelectLinks: Error setting up runtime message listener:', error.message);
        }
    } else {
        console.log('DragSelectLinks: Chrome runtime APIs not available for message listening');
    }

    function createActionsSection(links) {
        const section = document.createElement('div');
        section.style.cssText = `
            background: #111111;
            border-radius: 8px;
            border: 1px solid #3a3a3a;
            margin-bottom: 20px;
            overflow: hidden;
        `;
        
        const sectionHeader = document.createElement('div');
        sectionHeader.textContent = 'Actions';
        sectionHeader.style.cssText = `
            background: #0a0a0a;
            padding: 16px 20px;
            border-bottom: 1px solid #3a3a3a;
            font-weight: 500;
            color: #9ca3af;
            font-size: 14px;
        `;
        
        const content = document.createElement('div');
        content.style.cssText = `padding: 20px;`;
        
        // All buttons section - single row
        const allButtonsContainer = document.createElement('div');
        allButtonsContainer.style.cssText = `
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            align-items: center;
        `;
        
        // Open Selected button with lazy loading
        const openAllBtn = createUniformButton('Open Selected Links', () => {
            const selectedLinks = getSelectedLinks();
            if (selectedLinks.length > 0) {
                openLinksWithLazyLoading(selectedLinks);
            }
        });
        
        allButtonsContainer.appendChild(openAllBtn);
        
        // Format buttons using settings-based formats
        const formatButtons = [
            {
                label: 'TEXT',
                action: () => {
                    const selectedLinks = getSelectedLinks();
                    console.log('DragSelectLinks: Using TEXT format:', textFormat);
                    const text = formatLinks(selectedLinks, textFormat);
                    navigator.clipboard.writeText(text);
                }
            },
            {
                label: 'URLs Only',
                action: () => {
                    const selectedLinks = getSelectedLinks();
                    console.log('DragSelectLinks: Copying URLs as clickable links for WordPress');
                    
                    // Create HTML with line breaks for WordPress compatibility
                    const htmlUrls = selectedLinks.map(link => 
                        `<a href="${link.href}">${link.href}</a>`
                    ).join('<br>\n');
                    
                    const plainUrls = formatLinks(selectedLinks, 'text-url');
                    
                    // Copy both HTML and plain text formats for rich text editor compatibility
                    if (navigator.clipboard && navigator.clipboard.write) {
                        const data = [
                            new ClipboardItem({
                                'text/html': new Blob([htmlUrls], { type: 'text/html' }),
                                'text/plain': new Blob([plainUrls], { type: 'text/plain' })
                            })
                        ];
                        navigator.clipboard.write(data).catch(() => {
                            // Fallback to simple text copy
                            navigator.clipboard.writeText(plainUrls);
                        });
                    } else {
                        // Fallback for older browsers
                        navigator.clipboard.writeText(plainUrls);
                    }
                }
            },
            {
                label: 'HTML',
                action: () => {
                    const selectedLinks = getSelectedLinks();
                    console.log('DragSelectLinks: Using HTML format:', htmlFormat);
                    const html = formatLinks(selectedLinks, htmlFormat);
                    navigator.clipboard.writeText(html);
                }
            },
            {
                label: 'JSON',
                action: () => {
                    const selectedLinks = getSelectedLinks();
                    console.log('DragSelectLinks: Using JSON format:', jsonFormat);
                    const json = formatLinks(selectedLinks, jsonFormat);
                    navigator.clipboard.writeText(json);
                }
            },
            {
                label: 'Markdown',
                action: () => {
                    const selectedLinks = getSelectedLinks();
                    const markdown = selectedLinks.map(link => 
                        `[${link.textContent.trim() || 'Link'}](${link.href})`
                    ).join('\n');
                    navigator.clipboard.writeText(markdown);
                }
            },
            {
                label: 'Custom',
                action: () => {
                    const selectedLinks = getSelectedLinks();
                    console.log('DragSelectLinks: Using custom format:', customFormat);
                    const custom = formatLinks(selectedLinks, 'custom');
                    navigator.clipboard.writeText(custom);
                }
            }
        ];
        
        formatButtons.forEach(({ label, action }) => {
            const btn = createUniformButton(label, action);
            allButtonsContainer.appendChild(btn);
        });
        
        content.appendChild(allButtonsContainer);
        section.appendChild(sectionHeader);
        section.appendChild(content);
        
        return section;
    }
    
    function openLinksWithLazyLoading(links) {
        console.log(`DragSelectLinks: Opening ${links.length} links with lazy loading`);
        
        // Use placeholder tab approach for consistent lazy loading
        if (window.chrome && chrome.runtime) {
            try {
                // Send message to background script to create lazy loading tabs
                chrome.runtime.sendMessage({
                    action: 'createDragSelectTabs',
                    urls: links.map(link => link.href)
                }, (response) => {
                    if (chrome.runtime.lastError || !response || !response.success) {
                        console.log('DragSelectLinks: Background script not available, using fallback');
                        openLinksWithFallback(links);
                    } else {
                        console.log(`DragSelectLinks: Successfully created ${response.successCount} lazy loading tabs`);
                        
                        // Auto-cleanup after successful link opening to allow subsequent drag operations
                        setTimeout(() => {
                            const popup = document.querySelector('[data-primary-popup="true"]');
                            if (popup) {
                                // Find and trigger the close button to ensure proper cleanup
                                const closeBtn = popup.querySelector('button[style*="✕"], button:last-child');
                                if (closeBtn && closeBtn.textContent.includes('✕')) {
                                    console.log('DragSelectLinks: Auto-closing popup after successful link opening');
                                    closeBtn.click();
                                } else {
                                    // Fallback to global cleanup if close button not found
                                    console.log('DragSelectLinks: Using fallback cleanup after successful link opening');
                                    if (window.dragSelectLinksCleanup) {
                                        window.dragSelectLinksCleanup();
                                    }
                                }
                            } else {
                                // No popup found, use global cleanup
                                console.log('DragSelectLinks: Using global cleanup after successful link opening');
                                if (window.dragSelectLinksCleanup) {
                                    window.dragSelectLinksCleanup();
                                }
                            }
                        }, 500); // Small delay to ensure tabs are created before cleanup
                    }
                });
            } catch (error) {
                console.log('DragSelectLinks: Chrome runtime error, using fallback');
                openLinksWithFallback(links);
            }
        } else {
            openLinksWithFallback(links);
        }
    }
    
    function openLinksWithFallback(links) {
        // Fallback: Open links in small batches with delays
        const batchSize = 3; // Smaller batches to prevent browser freezing
        const delay = 800; // Delay between batches
        
        let batchIndex = 0;
        const openBatch = () => {
            const startIndex = batchIndex * batchSize;
            const endIndex = Math.min(startIndex + batchSize, links.length);
            const batch = links.slice(startIndex, endIndex);
            
            batch.forEach((link, index) => {
                setTimeout(() => {
                    window.open(link.href, '_blank');
                }, index * 100); // Small delay between individual links
            });
            
            batchIndex++;
            if (startIndex < links.length - 1) {
                setTimeout(openBatch, delay);
            } else {
                // All batches complete - trigger cleanup after fallback opening
                setTimeout(() => {
                    console.log('DragSelectLinks: Auto-cleanup after fallback link opening completed');
                    const popup = document.querySelector('[data-primary-popup="true"]');
                    if (popup) {
                        const closeBtn = popup.querySelector('button[style*="✕"], button:last-child');
                        if (closeBtn && closeBtn.textContent.includes('✕')) {
                            console.log('DragSelectLinks: Auto-closing popup after fallback completion');
                            closeBtn.click();
                        } else if (window.dragSelectLinksCleanup) {
                            window.dragSelectLinksCleanup();
                        }
                    } else if (window.dragSelectLinksCleanup) {
                        window.dragSelectLinksCleanup();
                    }
                }, delay + 200); // Extra delay after final batch
            }
        };
        
        openBatch();
    }
    

    
    function createUniformButton(text, onClick) {
        const btn = document.createElement('button');
        btn.textContent = text;
        btn.className = 'format-button';
        
        // Determine if this is the main action button or a format button
        const isMainAction = text.includes('Open Selected Links');
        
        btn.style.cssText = `
            padding: 10px 16px;
            background: ${isMainAction ? '#7c3aed' : '#374151'};
            color: ${isMainAction ? '#ffffff' : '#d1d5db'};
            border: ${isMainAction ? 'none' : '1px solid #4b5563'};
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s;
            min-width: 80px;
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
        `;
        
        btn.addEventListener('click', () => {
            onClick();
            if (!isMainAction) {
                showCopyFeedback(btn, '✓');
            }
        });
        
        btn.addEventListener('mouseenter', () => {
            if (isMainAction) {
                btn.style.background = '#8b5cf6';
            } else {
                btn.style.background = '#4b5563';
                btn.style.borderColor = '#6b7280';
            }
        });
        
        btn.addEventListener('mouseleave', () => {
            if (isMainAction) {
                btn.style.background = '#7c3aed';
            } else {
                btn.style.background = '#374151';
                btn.style.borderColor = '#4b5563';
            }
        });
        
        return btn;
    }
    
    function showCopyFeedback(button, message) {
        const originalText = button.textContent;
        button.textContent = message;
        button.style.background = '#10b981';
        
        setTimeout(() => {
            button.textContent = originalText;
            button.style.background = '#374151';
        }, 1500);
    }

})(); 