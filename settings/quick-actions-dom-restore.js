// Quick Actions DOM Restore Utility
// Ensures clean DOM state before executing any Quick Action

class QuickActionsDOMRestore {
    constructor() {
        // Remove individual debugMode - now controlled by global logging utility
    }

    log(message, ...args) {
        // Always call console.log - global logging utility controls visibility
        console.log(`[Quick Actions DOM Restore]`, message, ...args);
    }

    error(message, ...args) {
        console.error(`[Quick Actions DOM Restore ERROR]`, message, ...args);
    }

    // Main function to restore DOM before Quick Action execution
    async restoreBeforeAction(actionName = 'Unknown Action') {
        try {
            this.log(`Preparing DOM for ${actionName}...`);

            // Get current tab
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!tabs[0]) {
                this.error('No active tab found');
                return false;
            }

            const currentTab = tabs[0];

            // Inject DOM Snapshot Utility and attempt restoration
            try {
                // Inject the shared config and utility
                await chrome.scripting.executeScript({
                    target: { tabId: currentTab.id },
                    files: ['settings/shared-cleanup-config.js', 'settings/dom-snapshot-utility.js']
                });

                // Attempt restoration
                const restorationResult = await chrome.scripting.executeScript({
                    target: { tabId: currentTab.id },
                    func: function(actionName) {
                        console.log(`[Content] Preparing DOM for ${actionName}...`);
                        
                        if (window.domSnapshotUtility && window.domSnapshotUtility.isSnapshotReady()) {
                            console.log(`[Content] Restoring DOM to initial state for ${actionName}...`);
                            return window.domSnapshotUtility.restoreToInitialState();
                        } else {
                            console.log(`[Content] DOM snapshot not ready, performing manual cleanup for ${actionName}...`);
                            
                            // Manual cleanup fallback using shared configuration
                            try {
                                // Use shared cleanup configuration
                                const selectors = window.SharedCleanupConfig ? 
                                    window.SharedCleanupConfig.getPanelSelectors() : 
                                    [
                                        // Fallback selectors if shared config not available
                                        '.htags-panel', '.schema-panel', '.images-panel', '.metadata-panel',
                                        '#quick-edit-notice', '.color-palette-extractor-panel', '.robots-txt-panel',
                                        '[data-quick-action]', '.extension-panel'
                                    ];

                                selectors.forEach(selector => {
                                    const elements = document.querySelectorAll(selector);
                                    elements.forEach(el => el.remove());
                                });

                                // Reset document.designMode if modified
                                if (document.designMode === 'on') {
                                    document.designMode = 'off';
                                }

                                // Use shared cleanup configuration for global properties
                                const globalProps = window.SharedCleanupConfig ? 
                                    window.SharedCleanupConfig.getGlobalProperties() : 
                                    [
                                        // Fallback properties if shared config not available
                                        'HtagsAction', 'ShowLinksAction', 'SchemaAction', 'ImagesAction',
                                        'MetadataAction', 'QuickEditAction', 'ColorPaletteExtractorAction',
                                        'DragSelectLinksActive', 'dragSelectLinksCleanup'
                                    ];

                                globalProps.forEach(prop => {
                                    if (window[prop]) {
                                        delete window[prop];
                                    }
                                });

                                // Call specific cleanup functions if they exist
                                if (typeof window.dragSelectLinksCleanup === 'function') {
                                    try {
                                        window.dragSelectLinksCleanup();
                                    } catch (cleanupError) {
                                        console.error('[Content] Drag Select Links cleanup error:', cleanupError);
                                    }
                                }

                                console.log(`[Content] Manual cleanup completed for ${actionName}`);
                                return Promise.resolve(true);
                                
                            } catch (cleanupError) {
                                console.error(`[Content] Manual cleanup failed for ${actionName}:`, cleanupError);
                                return Promise.resolve(false);
                            }
                        }
                    },
                    args: [actionName]
                });

                const success = restorationResult?.[0]?.result;
                if (success) {
                    this.log(`DOM preparation successful for ${actionName}`);
                    return true;
                } else {
                    this.error(`DOM preparation failed for ${actionName}`);
                    return false;
                }

            } catch (restorationError) {
                this.error(`DOM restoration error for ${actionName}:`, restorationError);
                return false;
            }

        } catch (error) {
            this.error(`Error preparing DOM for ${actionName}:`, error);
            return false;
        }
    }

    // Legacy method maintained for backward compatibility but no longer used
    setDebugMode(enabled) {
        console.log('QuickActionsDOMRestore: setDebugMode is deprecated - use global debug mode setting instead');
    }
}

// Create global instance
if (typeof window !== 'undefined') {
    window.QuickActionsDOMRestore = QuickActionsDOMRestore;
    
    // Initialize the utility
    if (!window.quickActionsDOMRestore) {
        window.quickActionsDOMRestore = new QuickActionsDOMRestore();
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QuickActionsDOMRestore;
} 