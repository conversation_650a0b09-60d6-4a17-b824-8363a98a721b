// Color Palette Extractor Action - Comprehensive color analysis for web pages
class ColorPaletteExtractorAction {
    static execute() {
        try {
            // Remove existing color palette panel if present
            document.querySelectorAll('.color-palette-extractor-panel').forEach(panel => panel.remove());
            
            // Helper functions
            function escape(str) {
                if (!str) return '';
                return str.replace(/&/g, '&amp;')
                         .replace(/</g, '&lt;')
                         .replace(/>/g, '&gt;')
                         .replace(/"/g, '&quot;')
                         .replace(/'/g, '&#39;');
            }
            
            function hlCode(str) {
                return '<code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + str + '</code>';
            }
            
            // RGB to HEX conversion
            function rgbToHex(rgb) {
                const match = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
                if (match) {
                    const r = parseInt(match[1]);
                    const g = parseInt(match[2]);
                    const b = parseInt(match[3]);
                    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
                }
                
                const matchRgba = rgb.match(/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)$/);
                if (matchRgba) {
                    const r = parseInt(matchRgba[1]);
                    const g = parseInt(matchRgba[2]);
                    const b = parseInt(matchRgba[3]);
                    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
                }
                
                return rgb;
            }
            
            // RGB to HSL conversion
            function rgbToHsl(rgb) {
                const match = rgb.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)$/);
                if (!match) return null;
                
                let r = parseInt(match[1]) / 255;
                let g = parseInt(match[2]) / 255;
                let b = parseInt(match[3]) / 255;
                
                const max = Math.max(r, g, b);
                const min = Math.min(r, g, b);
                let h, s, l = (max + min) / 2;
                
                if (max === min) {
                    h = s = 0;
                } else {
                    const d = max - min;
                    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                    switch (max) {
                        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                        case g: h = (b - r) / d + 2; break;
                        case b: h = (r - g) / d + 4; break;
                    }
                    h /= 6;
                }
                
                return `hsl(${Math.round(h * 360)}, ${Math.round(s * 100)}%, ${Math.round(l * 100)}%)`;
            }
            
            // Copy to clipboard with visual feedback
            function copyToClipboard(text, element) {
                navigator.clipboard.writeText(text).then(() => {
                    // Visual feedback
                    element.style.backgroundColor = '#10b981';
                    element.style.transform = 'scale(0.95)';
                    
                    // Show copied message
                    const originalContent = element.innerHTML;
                    const copiedMsg = '<div style="display:flex;align-items:center;justify-content:center;height:100%;color:#fff;font-weight:600;font-size:14px;"><span>✓ Copied!</span></div>';
                    element.innerHTML = copiedMsg;
                    
                    setTimeout(() => {
                        element.style.backgroundColor = '#1a1a1a';
                        element.style.transform = 'scale(1)';
                        element.innerHTML = originalContent;
                    }, 1200);
                }).catch(err => {
                    console.error('Failed to copy: ', err);
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    
                    // Visual feedback for fallback
                    element.style.backgroundColor = '#10b981';
                    setTimeout(() => element.style.backgroundColor = '#1a1a1a', 800);
                });
            }
            
            // Export to CSV function
            function exportToCSV(colorData) {
                const csvHeader = 'HEX,RGB,HSL,Usage Count,Usage Percentage\n';
                const csvRows = colorData.map(([color, frequency, percentage]) => {
                    const hex = rgbToHex(color);
                    const hsl = rgbToHsl(color);
                    return `"${hex}","${color}","${hsl || 'N/A'}",${frequency},"${percentage}%"`;
                }).join('\n');
                
                const csvContent = csvHeader + csvRows;
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                
                // Create download link
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `color-palette-${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
            
            // Extract colors from all elements
            function extractColors() {
                const colorMap = new Map();
                const elements = document.getElementsByTagName('*');
                
                Array.from(elements).forEach(element => {
                    const computedStyle = window.getComputedStyle(element);
                    const color = computedStyle.color;
                    const backgroundColor = computedStyle.backgroundColor;
                    
                    // Add text color
                    if (color && color !== 'transparent' && color !== 'rgba(0, 0, 0, 0)') {
                        colorMap.set(color, (colorMap.get(color) || 0) + 1);
                    }
                    
                    // Add background color
                    if (backgroundColor && backgroundColor !== 'transparent' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
                        colorMap.set(backgroundColor, (colorMap.get(backgroundColor) || 0) + 1);
                    }
                });
                
                return colorMap;
            }
            
            // Create main panel
            var panel = document.createElement('div');
            panel.className = 'color-palette-extractor-panel';
            panel.style.cssText = `position:fixed;top:20px;right:20px;width:50%;max-width:600px;max-height:85vh;z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;overflow:auto;resize:both;min-width:400px;min-height:500px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5`;
            
            let html = `
                <div id="color-palette-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;">
                    <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;"><span style="color: #7C3AED; font-size: 18px;">●</span> Color Palette Extractor</h2>
                    <button onclick="this.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
                </div>
            `;
            
            // Show loading message first
            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Extracting Colors...</div>';
            html += '<div style="padding:20px;text-align:center;color:#6b7280;font-style:italic;">Analyzing page colors, please wait...</div>';
            html += '</div>';
            
            panel.innerHTML = html;
            document.body.appendChild(panel);
            
            // Drag functionality helper function
            function setupDragFunctionality() {
                var isDragging = false;
                var currentX;
                var currentY;
                var initialX;
                var initialY;
                var xOffset = 0;
                var yOffset = 0;
                
                var header = panel.querySelector('#color-palette-header');
                if (!header) return; // Safety check
                
                function dragStart(e) {
                    if (e.target.tagName === 'BUTTON' || e.target.id === 'export-csv-btn') return; // Don't drag when clicking buttons
                    
                    if (e.type === "touchstart") {
                        initialX = e.touches[0].clientX - xOffset;
                        initialY = e.touches[0].clientY - yOffset;
                    } else {
                        initialX = e.clientX - xOffset;
                        initialY = e.clientY - yOffset;
                    }
                    
                    if (e.target === header || header.contains(e.target)) {
                        isDragging = true;
                        panel.style.cursor = 'grabbing';
                        header.style.cursor = 'grabbing';
                    }
                }
                
                function dragEnd(e) {
                    initialX = currentX;
                    initialY = currentY;
                    isDragging = false;
                    panel.style.cursor = 'default';
                    if (header) header.style.cursor = 'move';
                }
                
                function drag(e) {
                    if (isDragging) {
                        e.preventDefault();
                        
                        if (e.type === "touchmove") {
                            currentX = e.touches[0].clientX - initialX;
                            currentY = e.touches[0].clientY - initialY;
                        } else {
                            currentX = e.clientX - initialX;
                            currentY = e.clientY - initialY;
                        }
                        
                        xOffset = currentX;
                        yOffset = currentY;
                        
                        // Constrain to viewport
                        var rect = panel.getBoundingClientRect();
                        var maxX = window.innerWidth - rect.width;
                        var maxY = window.innerHeight - rect.height;
                        
                        currentX = Math.max(0, Math.min(currentX, maxX));
                        currentY = Math.max(0, Math.min(currentY, maxY));
                        
                        // Clear any existing positioning and use absolute positioning
                        panel.style.right = '';
                        panel.style.left = currentX + 'px';
                        panel.style.top = currentY + 'px';
                    }
                }
                
                // Clean up existing event listeners (in case of re-setup)
                header.removeEventListener('mousedown', dragStart);
                document.removeEventListener('mousemove', drag);
                document.removeEventListener('mouseup', dragEnd);
                header.removeEventListener('touchstart', dragStart);
                document.removeEventListener('touchmove', drag);
                document.removeEventListener('touchend', dragEnd);
                
                // Add event listeners for drag functionality
                header.addEventListener('mousedown', dragStart);
                document.addEventListener('mousemove', drag);
                document.addEventListener('mouseup', dragEnd);
                
                // Touch events for mobile
                header.addEventListener('touchstart', dragStart);
                document.addEventListener('touchmove', drag);
                document.addEventListener('touchend', dragEnd);
                
                // Get initial position for offset calculation
                var rect = panel.getBoundingClientRect();
                xOffset = rect.left;
                yOffset = rect.top;
            }
            
            // Set up initial drag functionality
            setupDragFunctionality();
            
            // Handle Escape key to close panel
            function handleKeyDown(e) {
                if (e.key === 'Escape') {
                    panel.remove();
                    document.removeEventListener('keydown', handleKeyDown);
                }
            }
            
            document.addEventListener('keydown', handleKeyDown);
            
            // Extract colors and update panel
            setTimeout(() => {
                const colorMap = extractColors();
                const topColors = Array.from(colorMap.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 24); // Show top 24 colors
                
                const totalColors = colorMap.size;
                const totalUsage = Array.from(colorMap.values()).reduce((sum, count) => sum + count, 0);
                
                // Prepare data for CSV export (all colors, not just top 24)
                const allColorsForExport = Array.from(colorMap.entries())
                    .sort((a, b) => b[1] - a[1])
                    .map(([color, frequency]) => [color, frequency, ((frequency / totalUsage) * 100).toFixed(1)]);
                
                // Update panel with results
                let newHtml = `
                    <div id="color-palette-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;">
                        <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;"><span style="color: #7C3AED; font-size: 18px;">●</span> Color Palette Extractor</h2>
                        <div style="display:flex;gap:8px;">
                            <button id="export-csv-btn" style="padding:6px 12px;background:#10b981;color:#fff;border:1px solid #059669;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;"><span style="color: #fff; font-size: 12px;">●</span> Export CSV</button>
                            <button onclick="this.parentNode.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
                        </div>
                    </div>
                `;
                
                // Summary
                newHtml += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                newHtml += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Color Analysis Summary</div>';
                newHtml += '<div style="padding:20px;">';
                newHtml += '<div style="display:grid;grid-template-columns:repeat(auto-fit,minmax(150px,1fr));gap:16px;margin-bottom:16px;">';
                newHtml += `<div style="text-align:center;"><div style="color:#10b981;font-size:24px;font-weight:600;">${totalColors}</div><div style="color:#6b7280;font-size:12px;">Unique Colors</div></div>`;
                newHtml += `<div style="text-align:center;"><div style="color:#6366f1;font-size:24px;font-weight:600;">${totalUsage}</div><div style="color:#6b7280;font-size:12px;">Total Usage</div></div>`;
                newHtml += `<div style="text-align:center;"><div style="color:#f59e0b;font-size:24px;font-weight:600;">${topColors.length}</div><div style="color:#6b7280;font-size:12px;">Top Colors</div></div>`;
                newHtml += '</div>';
                newHtml += '</div>';
                newHtml += '</div>';
                
                // Color grid
                newHtml += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                newHtml += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Top Colors Found (Click to Copy HEX)</div>';
                newHtml += '<div style="padding:20px;">';
                newHtml += '<div style="display:grid;grid-template-columns:repeat(auto-fill,minmax(180px,1fr));gap:16px;">';
                
                topColors.forEach(([color, frequency]) => {
                    const hex = rgbToHex(color);
                    const hsl = rgbToHsl(color);
                    const percentage = ((frequency / totalUsage) * 100).toFixed(1);
                    
                    newHtml += `
                        <div class="color-card" style="background:#1a1a1a;border-radius:8px;padding:16px;border:1px solid #2a2a2a;cursor:pointer;transition:all 0.2s;position:relative;" 
                             data-hex="${hex}">
                            <div style="width:100%;height:60px;background:${color};border-radius:6px;margin-bottom:12px;border:1px solid #444;"></div>
                            <div style="font-size:11px;line-height:1.4;">
                                <div style="margin-bottom:4px;"><strong style="color:#9ca3af;">HEX:</strong> ${hlCode(hex)}</div>
                                <div style="margin-bottom:4px;"><strong style="color:#9ca3af;">RGB:</strong> ${hlCode(color)}</div>
                                ${hsl ? `<div style="margin-bottom:4px;"><strong style="color:#9ca3af;">HSL:</strong> ${hlCode(hsl)}</div>` : ''}
                                <div style="margin-bottom:4px;"><strong style="color:#9ca3af;">Usage:</strong> <span style="color:#10b981;">${frequency} (${percentage}%)</span></div>
                            </div>
                        </div>
                    `;
                });
                
                newHtml += '</div>';
                newHtml += '</div>';
                newHtml += '</div>';
                
                // Instructions
                newHtml += '<div style="background:#1a1a1a;border-radius:6px;border:1px solid #2a2a2a;padding:16px;text-align:center;color:#6b7280;font-size:12px;">';
                newHtml += '💡 Click any color card to copy its HEX value to clipboard • Export CSV to get all color data';
                newHtml += '</div>';
                
                panel.innerHTML = newHtml;
                
                // Re-setup drag functionality after content update
                setupDragFunctionality();
                
                // Add event listeners after panel is updated
                // CSV Export button
                const exportBtn = panel.querySelector('#export-csv-btn');
                if (exportBtn) {
                    exportBtn.addEventListener('click', () => {
                        exportToCSV(allColorsForExport);
                        exportBtn.style.backgroundColor = '#059669';
                        exportBtn.innerHTML = '✓ Downloaded!';
                        setTimeout(() => {
                            exportBtn.style.backgroundColor = '#10b981';
                            exportBtn.innerHTML = '📊 Export CSV';
                        }, 2000);
                    });
                }
                
                // Color card click events
                const colorCards = panel.querySelectorAll('.color-card');
                colorCards.forEach(card => {
                    card.addEventListener('click', function() {
                        const hex = this.getAttribute('data-hex');
                        copyToClipboard(hex, this);
                    });
                    
                    // Add hover effect
                    card.addEventListener('mouseenter', function() {
                        this.style.borderColor = '#6366f1';
                        this.style.transform = 'translateY(-2px)';
                    });
                    
                    card.addEventListener('mouseleave', function() {
                        this.style.borderColor = '#2a2a2a';
                        this.style.transform = 'translateY(0)';
                    });
                });
                
            }, 100);
        } catch (error) {
            console.error('Color Palette Extractor error:', error);
        }
    }

    static reset() {
        try {
            const existingPanel = document.querySelector('.color-palette-extractor-panel');
            if (existingPanel) {
                existingPanel.remove();
                console.log('Color Palette Extractor panel removed');
            }
            console.log('Color Palette Extractor reset completed');
        } catch (error) {
            console.error('Color Palette Extractor reset error:', error);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ColorPaletteExtractorAction;
} else {
    window.ColorPaletteExtractorAction = ColorPaletteExtractorAction;
} 