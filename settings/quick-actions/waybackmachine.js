// Wayback Machine Action - Navigate to Internet Archive for current page
class WaybackMachineAction {
    static execute() {
        try {
            // Wayback Machine functionality - direct execution for context menu
            const currentUrl = encodeURI(window.location.href);
            const waybackUrl = "http://web.archive.org/web/*/" + currentUrl;
            window.location.href = waybackUrl;
            console.log('Navigating to Wayback Machine for:', currentUrl);
        } catch (error) {
            console.error('Wayback Machine error:', error);
        }
    }

    static reset() {
        try {
            console.log('Wayback Machine reset completed (no action needed)');
        } catch (error) {
            console.error('Wayback Machine reset error:', error);
        }
    }

    // Instance methods for popup/shortcuts usage
    execute() {
        return new Promise((resolve, reject) => {
            try {
                console.log('Wayback Machine activated (instance method)');
                
                // Check if we're in a Chrome extension context
                if (typeof chrome !== 'undefined' && chrome.tabs) {
                    // We're in popup context, need to execute on the current tab
                    chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                        if (tabs[0]) {
                            // First inject the file, then execute the static method
                            chrome.scripting.executeScript({
                                target: {tabId: tabs[0].id},
                                files: ['settings/quick-actions/waybackmachine.js']
                            }, () => {
                                if (chrome.runtime.lastError) {
                                    console.error('Error injecting Wayback Machine script:', chrome.runtime.lastError);
                                    reject(chrome.runtime.lastError);
                                    return;
                                }
                                
                                // Now execute the static method
                                chrome.scripting.executeScript({
                                    target: {tabId: tabs[0].id},
                                    func: function() {
                                        if (typeof WaybackMachineAction !== 'undefined') {
                                            return WaybackMachineAction.execute();
                                        } else {
                                            return { error: 'WaybackMachineAction not found after injection' };
                                        }
                                    }
                                }, (result) => {
                                    if (chrome.runtime.lastError) {
                                        console.error('Error executing Wayback Machine script:', chrome.runtime.lastError);
                                        reject(chrome.runtime.lastError);
                                    } else {
                                        console.log('Wayback Machine script executed successfully:', result);
                                        resolve(result);
                                    }
                                });
                            });
                        } else {
                            reject(new Error('No active tab found'));
                        }
                    });
                } else {
                    // We're in page context, execute directly
                    const result = WaybackMachineAction.execute();
                    resolve(result);
                }
            } catch (error) {
                console.error('Error in Wayback Machine execute:', error);
                reject(error);
            }
        });
    }

    reset() {
        return new Promise((resolve, reject) => {
            try {
                console.log('Wayback Machine reset (instance method)');
                
                // Check if we're in a Chrome extension context
                if (typeof chrome !== 'undefined' && chrome.tabs) {
                    // We're in popup context, need to execute on the current tab
                    chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                        if (tabs[0]) {
                            // First inject the file, then execute the static method
                            chrome.scripting.executeScript({
                                target: {tabId: tabs[0].id},
                                files: ['settings/quick-actions/waybackmachine.js']
                            }, () => {
                                if (chrome.runtime.lastError) {
                                    console.error('Error injecting Wayback Machine script for reset:', chrome.runtime.lastError);
                                    reject(chrome.runtime.lastError);
                                    return;
                                }
                                
                                // Now execute the static reset method
                                chrome.scripting.executeScript({
                                    target: {tabId: tabs[0].id},
                                    func: function() {
                                        if (typeof WaybackMachineAction !== 'undefined') {
                                            return WaybackMachineAction.reset();
                                        } else {
                                            return { error: 'WaybackMachineAction not found after injection' };
                                        }
                                    }
                                }, (result) => {
                                    if (chrome.runtime.lastError) {
                                        console.error('Error resetting Wayback Machine script:', chrome.runtime.lastError);
                                        reject(chrome.runtime.lastError);
                                    } else {
                                        console.log('Wayback Machine reset executed successfully:', result);
                                        resolve(result);
                                    }
                                });
                            });
                        } else {
                            reject(new Error('No active tab found'));
                        }
                    });
                } else {
                    // We're in page context, execute directly
                    const result = WaybackMachineAction.reset();
                    resolve(result);
                }
            } catch (error) {
                console.error('Error in Wayback Machine reset:', error);
                reject(error);
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WaybackMachineAction;
} else {
    window.WaybackMachineAction = WaybackMachineAction;
} 