// Show Hidden Quick Action - Toggles visibility of hidden elements on the page
// Works exactly like the bookmarklet that shows/hides elements with display:none or aria-hidden

// Auto-detection settings and state management
let autoDetectionEnabled = false;
let debugMode = false;
let hasShownNotificationThisPage = false;

// Settings loading for auto-detection
async function loadAutoDetectionSettings() {
    try {
        // Safety check for chrome extension context
        if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.local) {
            console.log('ShowHidden Auto-Detection: Chrome extension APIs not available, disabling auto-detection');
            autoDetectionEnabled = false;
            return;
        }
        
        const result = await chrome.storage.local.get(['gmbExtractorSettings']);
        
        // Check for chrome runtime errors
        if (chrome.runtime.lastError) {
            console.log('ShowHidden Auto-Detection: Chrome runtime error:', chrome.runtime.lastError.message);
            throw new Error(chrome.runtime.lastError.message);
        }
        
        const settings = result.gmbExtractorSettings || {};
        
        // Auto-detection is enabled when separate auto-detection toggle is enabled
        autoDetectionEnabled = settings.showhiddenAutoDetectionEnabled !== false;
        debugMode = settings.debugMode || false;
        
        if (debugMode) {
            console.log('ShowHidden Auto-Detection: Settings loaded, enabled:', autoDetectionEnabled);
        }
        
        // Run auto-detection if enabled
        if (autoDetectionEnabled && !hasShownNotificationThisPage) {
            setTimeout(autoDetectHiddenDivs, 1000); // Small delay for dynamic content
        }
    } catch (error) {
        console.error('ShowHidden Auto-Detection: Error loading settings:', error);
        autoDetectionEnabled = false;
    }
}

// Auto-detection function defined at bottom - uses shared getFilteredHiddenElements() function

// Notification popup function - shows purple-themed notification about hidden content
function showHiddenDivsNotification(count) {
    try {
        // Remove any existing auto-detection notifications
        document.querySelectorAll('.show-hidden-auto-detection-notification').forEach(notification => notification.remove());
        
        // Calculate position (top-right corner, non-intrusive)
        const notificationX = 20; // 20px from right edge
        const notificationY = 20; // 20px from top
        
        // Add CSS animation if not already present
        if (!document.getElementById('show-hidden-auto-detection-styles')) {
            const styles = document.createElement('style');
            styles.id = 'show-hidden-auto-detection-styles';
            styles.textContent = `
                @keyframes showHiddenSlideIn {
                    from { 
                        opacity: 0; 
                        transform: translateX(100%) scale(0.9); 
                    }
                    to { 
                        opacity: 1; 
                        transform: translateX(0) scale(1); 
                    }
                }
                @keyframes showHiddenSlideOut {
                    from { 
                        opacity: 1; 
                        transform: translateX(0) scale(1); 
                    }
                    to { 
                        opacity: 0; 
                        transform: translateX(100%) scale(0.9); 
                    }
                }
                @keyframes showHiddenPulse {
                    0%, 100% { 
                        box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3); 
                    }
                    50% { 
                        box-shadow: 0 4px 20px rgba(124, 58, 237, 0.5); 
                    }
                }
            `;
            document.head.appendChild(styles);
        }
        
        // Create notification with purple brand styling
        const notification = document.createElement('div');
        notification.className = 'show-hidden-auto-detection-notification';
        notification.style.cssText = `
            position: fixed;
            right: ${notificationX}px;
            top: ${notificationY}px;
            transform: translateX(0);
            z-index: 2147483647;
            background: rgba(10, 10, 10, 0.95);
            color: #d1d5db;
            padding: 16px 20px;
            border: 2px solid #7C3AED;
            border-radius: 12px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
            backdrop-filter: blur(8px);
            pointer-events: auto;
            animation: showHiddenPulse 2s ease-in-out;
            white-space: nowrap;
            max-width: 400px;
            cursor: pointer;
            user-select: none;
        `;
        
        // Create notification content with purple accent and call-to-action
        const pluralText = count === 1 ? 'div' : 'divs';
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 12px;">
                <div style="
                    width: 8px; 
                    height: 8px; 
                    background: #7C3AED; 
                    border-radius: 50%; 
                    flex-shrink: 0;
                "></div>
                <div style="flex: 1;">
                    <div style="font-weight: 600; color: #fff; margin-bottom: 2px;">
                        ${count} Hidden ${pluralText} Found
                    </div>
                    <div style="font-size: 12px; color: #a1a1aa;">
                        Use Quick Action to show • Click to dismiss
                    </div>
                </div>
                <div style="
                    width: 20px; 
                    height: 20px; 
                    background: rgba(124, 58, 237, 0.2); 
                    border-radius: 50%; 
                    display: flex; 
                    align-items: center; 
                    justify-content: center; 
                    cursor: pointer;
                    transition: all 0.2s ease;
                    margin-left: 8px;
                    flex-shrink: 0;
                " class="close-btn">
                    <span style="color: #7C3AED; font-size: 14px; font-weight: bold;">✕</span>
                </div>
            </div>
        `;
        
        // Add click handler to dismiss notification (only on close button)
        const closeBtn = notification.querySelector('.close-btn');
        closeBtn.addEventListener('click', function(event) {
            event.stopPropagation();
            if (notification.parentNode) {
                notification.style.animation = 'showHiddenSlideOut 0.3s ease-in';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        });
        
        // Add hover effect for close button
        closeBtn.addEventListener('mouseenter', function() {
            closeBtn.style.background = 'rgba(124, 58, 237, 0.4)';
        });
        
        closeBtn.addEventListener('mouseleave', function() {
            closeBtn.style.background = 'rgba(124, 58, 237, 0.2)';
        });
        
        // Removed hover effects to prevent jumping - notification stays perfectly still
        
        document.body.appendChild(notification);
        
        // Notification stays open until manually closed (no auto-dismiss)
        
        if (debugMode) {
            console.log(`ShowHidden Auto-Detection: Notification shown for ${count} hidden ${pluralText}`);
        }
        
    } catch (error) {
        console.error('ShowHidden Auto-Detection: Error showing notification:', error);
    }
}

// Prevent duplicate class declaration
if (typeof window.ShowHiddenAction === 'undefined') {

class ShowHiddenAction {
    constructor() {
        this.name = 'Show Hidden';
        this.description = 'Toggles the visibility of hidden elements on the page (display:none, aria-hidden). Use Shift+Up/Down arrows to navigate between revealed elements';
    }

    // Execute the show hidden toggle - exactly like the bookmarklet
    execute() {
        return new Promise((resolve, reject) => {
            chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                const currentTab = tabs[0];
                
                try {
                    chrome.scripting.executeScript({
                        target: {tabId: currentTab.id},
                        func: function() {
                            // Check if already active and handle gracefully
                            if (window.showHiddenActive) {
                                console.log('Show Hidden already active - performing quick reset');
                                // Quick synchronous cleanup instead of full async reset
                                if (window.showHiddenCleanup) {
                                    window.showHiddenCleanup();
                                }
                                // Clear the active flag temporarily
                                window.showHiddenActive = false;
                                // Small delay to ensure cleanup is complete
                                setTimeout(() => {
                                    if (!window.showHiddenActive) { // Only restart if not activated again
                                        // Restart the function
                                        arguments.callee();
                                    }
                                }, 10);
                                return;
                            }
                            
                            // Mark as active immediately to prevent race conditions
                            window.showHiddenActive = true;
                            
                            // This is the exact bookmarklet function with comprehensive content area detection
                            (function(){
                                // Define a comprehensive selector for main content areas across all CMS platforms and page builders
                                const mainContentSelector = [
                                    // Semantic HTML5
                                    'main', 'article', 'section[role="main"]',
                                    
                                    // General conventions
                                    '.main-content', '#main-content', '#content', '.content', '.page-content',
                                    '.main', '#main', '.site-main', '.primary-content', '.main-wrapper',
                                    '.content-wrapper', '.page-wrapper', '.content-container',
                                    
                                    // WordPress themes and page builders
                                    '.site-content', '#primary', '.entry-content', '.content-area', '.post-content',
                                    '.page-content', '.single-content', '.archive-content', '.blog-content',
                                    '.wp-content', '.wp-post-content', '.elementor-section', '.elementor-container',
                                    '.elementor-widget-container', '.elementor-column', '.elementor-row',
                                    '.vc_row', '.vc_column', '.vc_column_container', // Visual Composer
                                    '.fusion-row', '.fusion-builder-row', '.fusion-layout-column', // Avada/Fusion
                                    '.et_pb_section', '.et_pb_row', '.et_pb_column', '.et_pb_module', // Divi
                                    '.fl-row', '.fl-col', '.fl-module', // Beaver Builder
                                    '.themify_builder', '.themify_builder_content', // Themify
                                    '.ct-section', '.ct-section-inner-wrap', '.ct-container', // Oxygen Builder
                                    '.bricks-container', '.brxe-section', '.brxe-container', // Bricks Builder
                                    '.breakdance-section', '.breakdance-container', // Breakdance
                                    '.kadence-section', '.kadence-row', '.kadence-column', // Kadence
                                    '.generate-columns-container', '.generate-columns', // GeneratePress
                                    '.ast-container', '.ast-row', '.ast-col', // Astra
                                    '.neve-main', '.nv-content-wrap', // Neve
                                    '.ocean-container', '.oceanwp-row', // OceanWP
                                    '.hestia-main-content', '.hestia-top-bar', // Hestia
                                    '.twentytwenty-main', '.twentynineteen-main', '.twentytwentyone-main', // Default WP themes
                                    
                                    // HubSpot
                                    '.cpx-main-content-area', '.hs-page-wrapper', '.hs-content-wrapper',
                                    '.dnd-section', '.dnd-row', '.dnd-column', '.dnd-module',
                                    
                                    // Shopify
                                    '#shopify-section', '.shopify-section', '.template-index', '.template-product',
                                    '.template-collection', '.template-page', '.template-blog', '.template-article',
                                    '.main-content', '.page-width', '.grid', '.collection-grid',
                                    
                                    // Magento
                                    '.cms-content', '#cms-main', '.page-main', '.page-wrapper',
                                    '.content-wrapper', '.main-container', '.page-layout',
                                    
                                    // Joomla
                                    '.joomla-content', '.com-content-article', '.item-page', '.blog',
                                    '.category-list', '.featured', '.component-content',
                                    
                                    // Drupal
                                    '.drupal-content', '#block-system-main', '.region-content',
                                    '.node-content', '.field-content', '.page-content',
                                    
                                    // Squarespace
                                    '.squarespace-main-content', '.sqs-layout', '.sqs-row', '.sqs-col',
                                    '.content-wrapper', '.page-section', '.index-section',
                                    
                                    // Weebly
                                    '.weebly-main-content', '.container', '.content-wrap',
                                    
                                    // Wix
                                    '.wix-content', '.main-content', '.page-content',
                                    
                                    // Webflow
                                    '.w-container', '.w-row', '.w-col', '.section-wrapper',
                                    
                                    // Ghost
                                    '.post-content', '.page-content', '.content-wrapper', '.site-main',
                                    
                                    // Medium/other blog platforms
                                    '.post-article', '.article-content', '.post-body', '.entry-body',
                                    
                                    // E-commerce platforms
                                    '.product-content', '.product-description', '.product-details',
                                    '.checkout-content', '.cart-content', '.account-content',
                                    
                                    // Landing page builders
                                    '.unbounce-main', '.leadpages-main', '.instapage-main',
                                    '.clickfunnels-main', '.convertkit-main',
                                    
                                    // Generic containers that are likely main content
                                    '.container', '.wrapper', '.inner', '.section', '.row', '.column',
                                    '.grid-container', '.flex-container', '.layout-container',
                                    '.page-body', '.site-body', '.app-content', '.main-section'
                                ].join(',');

                                // Track toggle state
                                const toggleState = window.toggleHiddenState = !window.toggleHiddenState;

                                // Function to check if element has display:none (computed style)
                                function hasDisplayNone(element) {
                                    const style = window.getComputedStyle(element);
                                    return style.display === 'none';
                                }

                                // Function to check if element has aria-hidden
                                function hasAriaHidden(element) {
                                    return element.hasAttribute('aria-hidden') && element.getAttribute('aria-hidden') === 'true';
                                }

                                // Function to check if element is within main content areas
                                function isInMainContent(element) {
                                    // Check if element itself matches main content selector
                                    if (element.matches && element.matches(mainContentSelector)) {
                                        return true;
                                    }
                                    
                                    // Check if element is contained within any main content area
                                    const mainContentAreas = document.querySelectorAll(mainContentSelector);
                                    for (let area of mainContentAreas) {
                                        if (area.contains(element)) {
                                            return true;
                                        }
                                    }
                                    
                                    return false;
                                }

                                // Function to check if element should be excluded from revealing
                                function shouldExcludeElement(element) {
                                    // Get element classes, id, and text content for checking
                                    const className = element.className ? element.className.toLowerCase() : '';
                                    const elementId = element.id ? element.id.toLowerCase() : '';
                                    const textContent = element.textContent ? element.textContent.toLowerCase() : '';
                                    const tagName = element.tagName ? element.tagName.toLowerCase() : '';
                                    
                                    // Specific exclusion patterns
                                    const excludePatterns = [
                                        'vime-youtube',
                                        'video-id', 
                                        'vime-embed',
                                        'btn-submit',
                                        'form_'
                                    ];
                                    
                                    // Check if element or its attributes contain exclusion patterns
                                    for (const pattern of excludePatterns) {
                                        if (className.includes(pattern) || 
                                            elementId.includes(pattern) ||
                                            textContent.includes(pattern)) {
                                            console.log(`Excluding element with pattern "${pattern}":`, element);
                                            return true;
                                        }
                                    }
                                    
                                    // Check if element is a video tag or has video-related attributes
                                    if (tagName === 'video' || tagName === 'iframe') {
                                        // Only exclude if it contains our specific patterns
                                        for (const pattern of excludePatterns) {
                                            if (className.includes(pattern) || elementId.includes(pattern)) {
                                                console.log(`Excluding video/iframe element with pattern "${pattern}":`, element);
                                                return true;
                                            }
                                        }
                                    }
                                    
                                    // Check parent elements for exclusion patterns (up to 3 levels)
                                    let parent = element.parentElement;
                                    let level = 0;
                                    while (parent && level < 3) {
                                        const parentClassName = parent.className ? parent.className.toLowerCase() : '';
                                        const parentId = parent.id ? parent.id.toLowerCase() : '';
                                        
                                        for (const pattern of excludePatterns) {
                                            if (parentClassName.includes(pattern) || parentId.includes(pattern)) {
                                                console.log(`Excluding element with parent pattern "${pattern}":`, element, 'Parent:', parent);
                                                return true;
                                            }
                                        }
                                        
                                        parent = parent.parentElement;
                                        level++;
                                    }
                                    
                                    return false;
                                }

                                // Use the same detection function as auto-detection - include function definition
                                const filteredHiddenElements = (function() {
                                    // This is the EXACT same function as getFilteredHiddenElements()
                                    // Step 1: Find ALL hidden elements on the page
                                    const allElements = document.querySelectorAll('*');
                                    const hiddenElements = [];

                                    allElements.forEach(function(element) {
                                        if (hasDisplayNone(element) || hasAriaHidden(element)) {
                                            hiddenElements.push(element);
                                        }
                                    });

                                    console.log(`Found ${hiddenElements.length} hidden elements total`);

                                    // Step 2: Filter to only elements within main content areas
                                    const mainContentHiddenElements = hiddenElements.filter(function(element) {
                                        return isInMainContent(element);
                                    });

                                    console.log(`Found ${mainContentHiddenElements.length} hidden elements in main content areas`);

                                    // Step 3: Filter out excluded video and form elements
                                    const filteredHiddenElements = mainContentHiddenElements.filter(function(element) {
                                        return !shouldExcludeElement(element);
                                    });

                                    console.log(`After excluding video/form elements: ${filteredHiddenElements.length} elements remaining`);

                                    return filteredHiddenElements;
                                })();

                                // Step 4: Toggle visibility of filtered elements
                                filteredHiddenElements.forEach(function(element) {
                                            if (toggleState) {
                                                // Save the original styles and make visible
                                        if (!element.dataset.originalDisplay) {
                                            const computedStyle = window.getComputedStyle(element);
                                            element.dataset.originalDisplay = computedStyle.display;
                                            element.dataset.originalInlineDisplay = element.style.display || '';
                                        }
                                        if (!element.dataset.originalAriaHidden) {
                                                    element.dataset.originalAriaHidden = element.getAttribute('aria-hidden') || '';
                                                }
                                        if (!element.dataset.originalVisibility) {
                                            const computedStyle = window.getComputedStyle(element);
                                            element.dataset.originalVisibility = computedStyle.visibility;
                                        }
                                        if (!element.dataset.originalOpacity) {
                                            const computedStyle = window.getComputedStyle(element);
                                            element.dataset.originalOpacity = computedStyle.opacity;
                                        }
                                        
                                        // Determine the best display value to use
                                        let displayValue = 'block';
                                        const tagName = element.tagName.toLowerCase();
                                        
                                        // Use appropriate display values for different elements
                                        if (['span', 'a', 'strong', 'em', 'b', 'i', 'code', 'small'].includes(tagName)) {
                                            displayValue = 'inline';
                                        } else if (['img', 'input', 'button'].includes(tagName)) {
                                            displayValue = 'inline-block';
                                        } else if (['table'].includes(tagName)) {
                                            displayValue = 'table';
                                        } else if (['tr'].includes(tagName)) {
                                            displayValue = 'table-row';
                                        } else if (['td', 'th'].includes(tagName)) {
                                            displayValue = 'table-cell';
                                        } else if (['ul', 'ol'].includes(tagName)) {
                                            displayValue = 'block';
                                        } else if (['li'].includes(tagName)) {
                                            displayValue = 'list-item';
                                        }
                                        
                                        // Force visibility with high specificity
                                        element.style.setProperty('display', displayValue, 'important');
                                        element.style.setProperty('visibility', 'visible', 'important');
                                        element.style.setProperty('opacity', '1', 'important');
                                        element.removeAttribute('aria-hidden');
                                        
                                        // Also check and reveal any hidden parent elements within main content
                                        let parent = element.parentElement;
                                        while (parent && parent !== document.body) {
                                            if (isInMainContent(parent)) {
                                                const parentStyle = window.getComputedStyle(parent);
                                                if (parentStyle.display === 'none') {
                                                    if (!parent.dataset.originalDisplayParent) {
                                                        parent.dataset.originalDisplayParent = parentStyle.display;
                                                        parent.dataset.originalInlineDisplayParent = parent.style.display || '';
                                                    }
                                                    parent.style.setProperty('display', 'block', 'important');
                                                    parent.setAttribute('data-revealed-parent', 'true');
                                                }
                                            }
                                            parent = parent.parentElement;
                                        }
                                        
                                        // Add a purple dashed outline indicator that this element was revealed
                                        element.style.setProperty('outline', '2px dashed rgba(147, 51, 234, 0.8)', 'important');
                                        element.style.setProperty('outline-offset', '8px', 'important');
                                        element.setAttribute('data-revealed-hidden', 'true');
                                    } else {
                                        // Restore the original styles and hidden state
                                        if (element.dataset.originalDisplay !== undefined) {
                                            if (element.dataset.originalDisplay === 'none') {
                                                element.style.setProperty('display', 'none', 'important');
                                            } else if (element.dataset.originalInlineDisplay) {
                                                element.style.display = element.dataset.originalInlineDisplay;
                                            } else {
                                                element.style.removeProperty('display');
                                            }
                                            delete element.dataset.originalDisplay;
                                            delete element.dataset.originalInlineDisplay;
                                        }
                                        
                                        if (element.dataset.originalVisibility !== undefined) {
                                            if (element.dataset.originalVisibility === 'hidden') {
                                                element.style.setProperty('visibility', 'hidden', 'important');
                                            } else {
                                                element.style.removeProperty('visibility');
                                            }
                                            delete element.dataset.originalVisibility;
                                        }
                                        
                                        if (element.dataset.originalOpacity !== undefined) {
                                            if (element.dataset.originalOpacity === '0') {
                                                element.style.setProperty('opacity', '0', 'important');
                                            } else {
                                                element.style.removeProperty('opacity');
                                            }
                                            delete element.dataset.originalOpacity;
                                        }
                                        
                                        if (element.dataset.originalAriaHidden !== undefined) {
                                                if (element.dataset.originalAriaHidden) {
                                                    element.setAttribute('aria-hidden', element.dataset.originalAriaHidden);
                                                } else {
                                                    element.removeAttribute('aria-hidden');
                                                }
                                                delete element.dataset.originalAriaHidden;
                                            }
                                        
                                        // Remove reveal indicators
                                        element.style.removeProperty('outline');
                                        element.style.removeProperty('outline-offset');
                                        element.style.removeProperty('box-shadow');
                                        element.removeAttribute('data-revealed-hidden');
                                        
                                        // Restore any parent elements that were revealed
                                        const revealedParents = document.querySelectorAll('[data-revealed-parent]');
                                        revealedParents.forEach(function(parent) {
                                            if (parent.dataset.originalDisplayParent !== undefined) {
                                                if (parent.dataset.originalDisplayParent === 'none') {
                                                    parent.style.setProperty('display', 'none', 'important');
                                                } else if (parent.dataset.originalInlineDisplayParent) {
                                                    parent.style.display = parent.dataset.originalInlineDisplayParent;
                                                } else {
                                                    parent.style.removeProperty('display');
                                                }
                                                delete parent.dataset.originalDisplayParent;
                                                delete parent.dataset.originalInlineDisplayParent;
                                            }
                                            parent.removeAttribute('data-revealed-parent');
                                        });
                                    }
                                });
                                
                                // Show status message and scroll to first element
                                if (toggleState) {
                                    console.log(`Revealed ${filteredHiddenElements.length} hidden elements in main content`);
                                    
                                    // Create persistent status message
                                    const statusMsg = document.createElement('div');
                                    statusMsg.innerHTML = `Revealed ${filteredHiddenElements.length} hidden elements<br><small>Shift+↑/↓ to navigate • Esc to hide</small>`;
                                    statusMsg.style.cssText = 'position:fixed;bottom:10px;right:10px;background:#7C3AED;color:white;padding:12px;border:2px solid #7C3AED;border-radius:5px;z-index:999999;font-family:Arial,sans-serif;font-size:12px;box-shadow:0 4px 12px rgba(0,0,0,0.3);line-height:1.4;';
                                    statusMsg.className = 'show-hidden-status-msg';
                                    statusMsg.setAttribute('data-show-hidden-status', 'true');
                                    document.body.appendChild(statusMsg);
                                    
                                    // Scroll to and flash the first revealed element
                                    if (filteredHiddenElements.length > 0) {
                                        const firstElement = filteredHiddenElements[0];
                                        
                                        // Scroll to the first element
                                        firstElement.scrollIntoView({ 
                                            behavior: 'smooth', 
                                            block: 'center',
                                            inline: 'center'
                                        });
                                        
                                        // Add flashing animation to the first element
                                        setTimeout(() => {
                                            let flashCount = 0;
                                            const maxFlashes = 6;
                                            
                                            const flashInterval = setInterval(() => {
                                                if (flashCount >= maxFlashes) {
                                                    clearInterval(flashInterval);
                                                    // Restore to normal purple outline
                                                    firstElement.style.setProperty('outline', '2px dashed rgba(147, 51, 234, 0.8)', 'important');
                                                    firstElement.style.setProperty('outline-offset', '8px', 'important');
                                                    return;
                                                }
                                                
                                                // Alternate between bright and normal purple
                                                if (flashCount % 2 === 0) {
                                                    firstElement.style.setProperty('outline', '3px dashed rgba(147, 51, 234, 1)', 'important');
                                                    firstElement.style.setProperty('outline-offset', '8px', 'important');
                                                    firstElement.style.setProperty('box-shadow', '0 0 10px rgba(147, 51, 234, 0.6)', 'important');
                                                } else {
                                                    firstElement.style.setProperty('outline', '2px dashed rgba(147, 51, 234, 0.4)', 'important');
                                                    firstElement.style.setProperty('outline-offset', '8px', 'important');
                                                    firstElement.style.removeProperty('box-shadow');
                                                }
                                                flashCount++;
                                            }, 200);
                                        }, 800); // Delay to allow scroll to complete
                                    }
                                    
                                    // Set up navigation between revealed elements
                                    window.revealedHiddenElements = filteredHiddenElements;
                                    window.currentHiddenElementIndex = 0;
                                    
                                } else {
                                    console.log('Hidden elements restored to original state');
                                    // Clean up navigation variables
                                    window.revealedHiddenElements = null;
                                    window.currentHiddenElementIndex = 0;
                                    // Remove status message
                                    const statusMsg = document.querySelector('.show-hidden-status-msg');
                                    if (statusMsg) statusMsg.remove();
                                }
                                
                                // Function to navigate to a specific hidden element
                                window.navigateToHiddenElement = function navigateToHiddenElement() {
                                    if (!window.revealedHiddenElements || window.revealedHiddenElements.length === 0) return;
                                    
                                    // Remove previous highlight from all elements
                                    window.revealedHiddenElements.forEach(function(element) {
                                        element.style.setProperty('outline', '2px dashed rgba(147, 51, 234, 0.8)', 'important');
                                        element.style.setProperty('outline-offset', '8px', 'important');
                                        element.style.removeProperty('box-shadow');
                                    });
                                    
                                    // Highlight current element
                                    const currentElement = window.revealedHiddenElements[window.currentHiddenElementIndex];
                                    currentElement.style.setProperty('outline', '3px dashed #7C3AED', 'important');
                                    currentElement.style.setProperty('outline-offset', '8px', 'important');
                                    currentElement.style.setProperty('box-shadow', '0 0 15px rgba(124, 58, 237, 0.6)', 'important');
                                    
                                    // Scroll to current element
                                    currentElement.scrollIntoView({ 
                                        behavior: 'smooth', 
                                        block: 'center',
                                        inline: 'center'
                                    });
                                    
                                    // Show navigation status
                                    const navStatus = document.createElement('div');
                                    navStatus.textContent = `Element ${window.currentHiddenElementIndex + 1} of ${window.revealedHiddenElements.length}`;
                                    navStatus.style.cssText = 'position:fixed;top:60px;right:10px;background:#7C3AED;color:white;padding:8px 12px;border:2px solid #7C3AED;border-radius:5px;z-index:999999;font-family:Arial,sans-serif;font-size:11px;box-shadow:0 4px 12px rgba(0,0,0,0.3);';
                                    navStatus.setAttribute('data-show-hidden-nav-status', 'true');
                                    document.body.appendChild(navStatus);
                                    setTimeout(() => navStatus.remove(), 2000);
                                }
                                
                                // Handle navigation and Escape key
                                window.showHiddenKeyDownHandler = function handleKeyDown(e) {
                                    // Handle Shift + Up/Down arrow navigation
                                    if (e.shiftKey && window.revealedHiddenElements && window.revealedHiddenElements.length > 0) {
                                        if (e.key === 'ArrowUp') {
                                            e.preventDefault();
                                            window.currentHiddenElementIndex = Math.max(0, window.currentHiddenElementIndex - 1);
                                            window.navigateToHiddenElement();
                                        } else if (e.key === 'ArrowDown') {
                                            e.preventDefault();
                                            window.currentHiddenElementIndex = Math.min(window.revealedHiddenElements.length - 1, window.currentHiddenElementIndex + 1);
                                            window.navigateToHiddenElement();
                                        }
                                    }
                                    
                                    if (e.key === 'Escape') {
                                        e.preventDefault();
                                        console.log('ShowHiddenAction: Escape key pressed, performing full cleanup');
                                        
                                        // Call comprehensive cleanup
                                        if (window.showHiddenCleanup) {
                                            window.showHiddenCleanup();
                                        }
                                    }
                                }
                                
                                // Comprehensive cleanup function following golden rules
                                window.showHiddenCleanup = function cleanup() {
                                    console.log('Starting thorough Show Hidden cleanup');
                                    
                                    // Reset the toggle state first
                                        window.toggleHiddenState = false;
                                    
                                    // Remove event listener
                                    document.removeEventListener('keydown', window.showHiddenKeyDownHandler);
                                        
                                    // Find all elements with saved original styles and restore them
                                    const modifiedElements = document.querySelectorAll('[data-original-display], [data-original-aria-hidden], [data-revealed-hidden], [data-original-visibility], [data-original-opacity]');
                                        
                                        modifiedElements.forEach(function(element) {
                                            // Restore original display style
                                        if (element.dataset.originalDisplay !== undefined) {
                                            if (element.dataset.originalDisplay === 'none') {
                                                element.style.setProperty('display', 'none', 'important');
                                            } else if (element.dataset.originalInlineDisplay) {
                                                element.style.display = element.dataset.originalInlineDisplay;
                                            } else {
                                                element.style.removeProperty('display');
                                            }
                                            delete element.dataset.originalDisplay;
                                            delete element.dataset.originalInlineDisplay;
                                        }
                                        
                                        // Restore original visibility
                                        if (element.dataset.originalVisibility !== undefined) {
                                            if (element.dataset.originalVisibility === 'hidden') {
                                                element.style.setProperty('visibility', 'hidden', 'important');
                                            } else {
                                                element.style.removeProperty('visibility');
                                            }
                                            delete element.dataset.originalVisibility;
                                        }
                                        
                                        // Restore original opacity
                                        if (element.dataset.originalOpacity !== undefined) {
                                            if (element.dataset.originalOpacity === '0') {
                                                element.style.setProperty('opacity', '0', 'important');
                                            } else {
                                                element.style.removeProperty('opacity');
                                            }
                                            delete element.dataset.originalOpacity;
                                            }
                                            
                                            // Restore original aria-hidden attribute
                                            if (element.dataset.originalAriaHidden !== undefined) {
                                                if (element.dataset.originalAriaHidden) {
                                                    element.setAttribute('aria-hidden', element.dataset.originalAriaHidden);
                                                } else {
                                                    element.removeAttribute('aria-hidden');
                                                }
                                                delete element.dataset.originalAriaHidden;
                                            }
                                        
                                        // Remove reveal indicators
                                        element.style.removeProperty('outline');
                                        element.style.removeProperty('outline-offset');
                                        element.style.removeProperty('box-shadow');
                                        element.removeAttribute('data-revealed-hidden');
                                    });
                                    
                                    // Restore any parent elements that were revealed
                                    const revealedParents = document.querySelectorAll('[data-revealed-parent]');
                                    revealedParents.forEach(function(parent) {
                                        if (parent.dataset.originalDisplayParent !== undefined) {
                                            if (parent.dataset.originalDisplayParent === 'none') {
                                                parent.style.setProperty('display', 'none', 'important');
                                            } else if (parent.dataset.originalInlineDisplayParent) {
                                                parent.style.display = parent.dataset.originalInlineDisplayParent;
                                            } else {
                                                parent.style.removeProperty('display');
                                            }
                                            delete parent.dataset.originalDisplayParent;
                                            delete parent.dataset.originalInlineDisplayParent;
                                        }
                                        parent.removeAttribute('data-revealed-parent');
                                    });
                                    
                                    // Remove ALL show hidden related elements
                                    const selectorsToRemove = [
                                        '.show-hidden-status-msg',
                                        '[data-show-hidden-status]',
                                        '[data-show-hidden-nav-status]'
                                    ];
                                    
                                    selectorsToRemove.forEach(selector => {
                                        document.querySelectorAll(selector).forEach(el => {
                                            el.remove();
                                        });
                                    });
                                    
                                    // Clean up navigation variables
                                    window.revealedHiddenElements = null;
                                    window.currentHiddenElementIndex = 0;
                                    
                                    // Clear ALL global variables and functions we created
                                    window.navigateToHiddenElement = null;
                                    window.showHiddenKeyDownHandler = null;
                                    window.showHiddenCleanup = null;
                                    
                                    // Clear active flag to allow fresh restart
                                    window.showHiddenActive = false;
                                    
                                    console.log('Thorough Show Hidden cleanup completed');
                                    }
                                
                                // Add event listener
                                document.addEventListener('keydown', window.showHiddenKeyDownHandler);
                                
                                return true;
                            })();
                            
                            return true;
                        }
                    }, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('Error executing Show Hidden script:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            console.log('Show Hidden script executed successfully');
                            resolve(result);
                        }
                    });
                } catch (error) {
                    console.error('Error setting up Show Hidden script execution:', error);
                    reject(error);
                }
            });
        });
    }

    // Reset the show hidden functionality (restore all elements to their original state)
    reset() {
        return new Promise((resolve, reject) => {
            chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                const currentTab = tabs[0];
                
                try {
                    chrome.scripting.executeScript({
                        target: {tabId: currentTab.id},
                        func: function() {
                            console.log('ShowHiddenAction: Starting comprehensive DOM reset');
                            
                            // Use global cleanup function if available
                            if (window.showHiddenCleanup) {
                                window.showHiddenCleanup();
                            } else {
                                // Fallback cleanup if global function not available
                                
                            // Reset the toggle state
                            window.toggleHiddenState = false;
                            
                                // Remove event listeners
                                if (window.showHiddenKeyDownHandler) {
                                    document.removeEventListener('keydown', window.showHiddenKeyDownHandler);
                                }
                                
                            // Find all elements with saved original styles
                                const modifiedElements = document.querySelectorAll('[data-original-display], [data-original-aria-hidden], [data-revealed-hidden], [data-original-visibility], [data-original-opacity]');
                            
                            modifiedElements.forEach(function(element) {
                                // Restore original display style
                                    if (element.dataset.originalDisplay !== undefined) {
                                        if (element.dataset.originalDisplay === 'none') {
                                            element.style.setProperty('display', 'none', 'important');
                                        } else if (element.dataset.originalInlineDisplay) {
                                            element.style.display = element.dataset.originalInlineDisplay;
                                        } else {
                                            element.style.removeProperty('display');
                                        }
                                        delete element.dataset.originalDisplay;
                                        delete element.dataset.originalInlineDisplay;
                                    }
                                    
                                    // Restore original visibility
                                    if (element.dataset.originalVisibility !== undefined) {
                                        if (element.dataset.originalVisibility === 'hidden') {
                                            element.style.setProperty('visibility', 'hidden', 'important');
                                        } else {
                                            element.style.removeProperty('visibility');
                                        }
                                        delete element.dataset.originalVisibility;
                                    }
                                    
                                    // Restore original opacity
                                    if (element.dataset.originalOpacity !== undefined) {
                                        if (element.dataset.originalOpacity === '0') {
                                            element.style.setProperty('opacity', '0', 'important');
                                        } else {
                                            element.style.removeProperty('opacity');
                                        }
                                        delete element.dataset.originalOpacity;
                                }
                                
                                // Restore original aria-hidden attribute
                                if (element.dataset.originalAriaHidden !== undefined) {
                                    if (element.dataset.originalAriaHidden) {
                                        element.setAttribute('aria-hidden', element.dataset.originalAriaHidden);
                                    } else {
                                        element.removeAttribute('aria-hidden');
                                    }
                                    delete element.dataset.originalAriaHidden;
                                }
                                    
                                    // Remove reveal indicators
                                    element.style.removeProperty('outline');
                                    element.style.removeProperty('outline-offset');
                                    element.style.removeProperty('box-shadow');
                                    element.removeAttribute('data-revealed-hidden');
                                });
                                
                                // Restore any parent elements that were revealed
                                const revealedParents = document.querySelectorAll('[data-revealed-parent]');
                                revealedParents.forEach(function(parent) {
                                    if (parent.dataset.originalDisplayParent !== undefined) {
                                        if (parent.dataset.originalDisplayParent === 'none') {
                                            parent.style.setProperty('display', 'none', 'important');
                                        } else if (parent.dataset.originalInlineDisplayParent) {
                                            parent.style.display = parent.dataset.originalInlineDisplayParent;
                                        } else {
                                            parent.style.removeProperty('display');
                                        }
                                        delete parent.dataset.originalDisplayParent;
                                        delete parent.dataset.originalInlineDisplayParent;
                                    }
                                    parent.removeAttribute('data-revealed-parent');
                                });
                                
                                // Remove ALL show hidden related elements
                                const selectorsToRemove = [
                                    '.show-hidden-status-msg',
                                    '[data-show-hidden-status]',
                                    '[data-show-hidden-nav-status]'
                                ];
                                
                                selectorsToRemove.forEach(selector => {
                                    document.querySelectorAll(selector).forEach(el => {
                                        el.remove();
                                    });
                                });
                                
                                // Clean up ALL global variables and functions we created
                                window.revealedHiddenElements = null;
                                window.currentHiddenElementIndex = 0;
                                window.navigateToHiddenElement = null;
                                window.showHiddenKeyDownHandler = null;
                                window.showHiddenCleanup = null;
                                window.showHiddenActive = false;
                            }
                            
                            console.log('ShowHiddenAction: Comprehensive DOM reset completed');
                            return true;
                        }
                    }, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('Error resetting Show Hidden:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            console.log('Show Hidden reset successfully');
                            resolve(result);
                        }
                    });
                } catch (error) {
                    console.error('Error setting up Show Hidden reset:', error);
                    reject(error);
                }
            });
        });
    }

    // Static execute method for global access (used by context menu)
    static execute() {
        console.log('ShowHiddenAction: Static execute called');
        const action = new ShowHiddenAction();
        return action.execute();
    }

    // Static reset method for global access (used by reset utilities)
    static reset() {
        console.log('ShowHiddenAction: Static reset called');
        const action = new ShowHiddenAction();
        return action.reset();
    }
}

// Make available globally
if (typeof window !== 'undefined') {
    window.ShowHiddenAction = ShowHiddenAction;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ShowHiddenAction;
} else {
    window.ShowHiddenAction = ShowHiddenAction;
} 

// Auto-detection page load integration and settings listener
(function() {
    'use strict';
    
    // Listen for settings changes - MANDATORY for real-time toggle updates
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        try {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                if (message.action === 'updateSettings' && message.settings) {
                    const wasEnabled = autoDetectionEnabled;
                    autoDetectionEnabled = message.settings.showhiddenAutoDetectionEnabled !== false;
                    debugMode = message.settings.debugMode || false;
                    
                    if (debugMode) {
                        console.log(`ShowHidden Auto-Detection: Settings updated, enabled: ${autoDetectionEnabled}`);
                    }
                    
                    // If just enabled, run detection (but don't show duplicate notifications)
                    if (!wasEnabled && autoDetectionEnabled && !hasShownNotificationThisPage) {
                        setTimeout(autoDetectHiddenDivs, 500);
                    }
                }
            });
        } catch (error) {
            console.log('ShowHidden Auto-Detection: Error setting up runtime message listener:', error.message);
        }
    } else {
        console.log('ShowHidden Auto-Detection: Chrome runtime APIs not available');
    }
    
    // Handle page navigation changes - reset notification flag for new pages
    function handlePageNavigation() {
        hasShownNotificationThisPage = false;
        if (debugMode) {
            console.log('ShowHidden Auto-Detection: Page navigation detected, reset notification flag');
        }
        
        // Run auto-detection on new page if enabled
        if (autoDetectionEnabled) {
            setTimeout(autoDetectHiddenDivs, 1500); // Longer delay for page navigation
        }
    }
    
    // Listen for page navigation events
    if ('navigation' in window) {
        // Modern Navigation API
        window.navigation.addEventListener('navigate', handlePageNavigation);
    } else {
        // Fallback for older browsers - listen for popstate and pushstate/replacestate
        window.addEventListener('popstate', handlePageNavigation);
        
        // Monkey patch pushState and replaceState to detect navigation
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;
        
        history.pushState = function() {
            originalPushState.apply(history, arguments);
            setTimeout(handlePageNavigation, 100);
        };
        
        history.replaceState = function() {
            originalReplaceState.apply(history, arguments);
            setTimeout(handlePageNavigation, 100);
        };
    }
    
    // Initialize auto-detection on page load
    function initializeAutoDetection() {
        // Only run auto-detection if we're not in a popup context
        if (document.body.classList.contains('popup') || window.location.protocol === 'chrome-extension:') {
            if (debugMode) {
                console.log('ShowHidden Auto-Detection: Skipping initialization - running in popup context');
            }
            return;
        }
        
        if (debugMode) {
            console.log('ShowHidden Auto-Detection: Initializing...');
        }
        
        // Load settings and potentially run auto-detection
        loadAutoDetectionSettings();
    }
    
    // Handle different loading states
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeAutoDetection);
    } else if (document.readyState === 'interactive') {
        // DOM is ready but resources may still be loading
        setTimeout(initializeAutoDetection, 100);
    } else {
        // Document and resources are fully loaded
        setTimeout(initializeAutoDetection, 100);
    }
    
    // Also listen for window load as a fallback for dynamic content
    window.addEventListener('load', function() {
        if (autoDetectionEnabled && !hasShownNotificationThisPage) {
            setTimeout(autoDetectHiddenDivs, 2000); // Extra delay for dynamic content after load
        }
    });
})();

} // End of duplicate prevention check 

// Shared detection function - used by both auto-detection and Quick Action execute
function getFilteredHiddenElements() {
    // Define a comprehensive selector for main content areas across all CMS platforms and page builders
    const mainContentSelector = [
        // Semantic HTML5
        'main', 'article', 'section[role="main"]',
        
        // General conventions
        '.main-content', '#main-content', '#content', '.content', '.page-content',
        '.main', '#main', '.site-main', '.primary-content', '.main-wrapper',
        '.content-wrapper', '.page-wrapper', '.content-container',
        
        // WordPress themes and page builders
        '.site-content', '#primary', '.entry-content', '.content-area', '.post-content',
        '.page-content', '.single-content', '.archive-content', '.blog-content',
        '.wp-content', '.wp-post-content', '.elementor-section', '.elementor-container',
        '.elementor-widget-container', '.elementor-column', '.elementor-row',
        '.vc_row', '.vc_column', '.vc_column_container', // Visual Composer
        '.fusion-row', '.fusion-builder-row', '.fusion-layout-column', // Avada/Fusion
        '.et_pb_section', '.et_pb_row', '.et_pb_column', '.et_pb_module', // Divi
        '.fl-row', '.fl-col', '.fl-module', // Beaver Builder
        '.themify_builder', '.themify_builder_content', // Themify
        '.ct-section', '.ct-section-inner-wrap', '.ct-container', // Oxygen Builder
        '.bricks-container', '.brxe-section', '.brxe-container', // Bricks Builder
        '.breakdance-section', '.breakdance-container', // Breakdance
        '.kadence-section', '.kadence-row', '.kadence-column', // Kadence
        '.generate-columns-container', '.generate-columns', // GeneratePress
        '.ast-container', '.ast-row', '.ast-col', // Astra
        '.neve-main', '.nv-content-wrap', // Neve
        '.ocean-container', '.oceanwp-row', // OceanWP
        '.hestia-main-content', '.hestia-top-bar', // Hestia
        '.twentytwenty-main', '.twentynineteen-main', '.twentytwentyone-main', // Default WP themes
        
        // HubSpot
        '.cpx-main-content-area', '.hs-page-wrapper', '.hs-content-wrapper',
        '.dnd-section', '.dnd-row', '.dnd-column', '.dnd-module',
        
        // Shopify
        '#shopify-section', '.shopify-section', '.template-index', '.template-product',
        '.template-collection', '.template-page', '.template-blog', '.template-article',
        '.main-content', '.page-width', '.grid', '.collection-grid',
        
        // Magento
        '.cms-content', '#cms-main', '.page-main', '.page-wrapper',
        '.content-wrapper', '.main-container', '.page-layout',
        
        // Joomla
        '.joomla-content', '.com-content-article', '.item-page', '.blog',
        '.category-list', '.featured', '.component-content',
        
        // Drupal
        '.drupal-content', '#block-system-main', '.region-content',
        '.node-content', '.field-content', '.page-content',
        
        // Squarespace
        '.squarespace-main-content', '.sqs-layout', '.sqs-row', '.sqs-col',
        '.content-wrapper', '.page-section', '.index-section',
        
        // Weebly
        '.weebly-main-content', '.container', '.content-wrap',
        
        // Wix
        '.wix-content', '.main-content', '.page-content',
        
        // Webflow
        '.w-container', '.w-row', '.w-col', '.section-wrapper',
        
        // Ghost
        '.post-content', '.page-content', '.content-wrapper', '.site-main',
        
        // Medium/other blog platforms
        '.post-article', '.article-content', '.post-body', '.entry-body',
        
        // E-commerce platforms
        '.product-content', '.product-description', '.product-details',
        '.checkout-content', '.cart-content', '.account-content',
        
        // Landing page builders
        '.unbounce-main', '.leadpages-main', '.instapage-main',
        '.clickfunnels-main', '.convertkit-main',
        
        // Generic containers that are likely main content
        '.container', '.wrapper', '.inner', '.section', '.row', '.column',
        '.grid-container', '.flex-container', '.layout-container',
        '.page-body', '.site-body', '.app-content', '.main-section'
    ].join(',');

    // Function to check if element has display:none (computed style)
    function hasDisplayNone(element) {
        const style = window.getComputedStyle(element);
        return style.display === 'none';
    }

    // Function to check if element has aria-hidden
    function hasAriaHidden(element) {
        return element.hasAttribute('aria-hidden') && element.getAttribute('aria-hidden') === 'true';
    }

    // Function to check if element is within main content areas
    function isInMainContent(element) {
        // Check if element itself matches main content selector
        if (element.matches && element.matches(mainContentSelector)) {
            return true;
        }
        
        // Check if element is contained within any main content area
        const mainContentAreas = document.querySelectorAll(mainContentSelector);
        for (let area of mainContentAreas) {
            if (area.contains(element)) {
                return true;
            }
        }
        
        return false;
    }

    // Function to check if element should be excluded from revealing
    function shouldExcludeElement(element) {
        // Get element classes, id, and text content for checking
        const className = element.className ? element.className.toLowerCase() : '';
        const elementId = element.id ? element.id.toLowerCase() : '';
        const textContent = element.textContent ? element.textContent.toLowerCase() : '';
        const tagName = element.tagName ? element.tagName.toLowerCase() : '';
        
        // Specific exclusion patterns
        const excludePatterns = [
            'vime-youtube',
            'video-id', 
            'vime-embed',
            'btn-submit',
            'form_'
        ];
        
        // Check if element or its attributes contain exclusion patterns
        for (const pattern of excludePatterns) {
            if (className.includes(pattern) || 
                elementId.includes(pattern) ||
                textContent.includes(pattern)) {
                console.log(`Excluding element with pattern "${pattern}":`, element);
                return true;
            }
        }
        
        // Check if element is a video tag or has video-related attributes
        if (tagName === 'video' || tagName === 'iframe') {
            // Only exclude if it contains our specific patterns
            for (const pattern of excludePatterns) {
                if (className.includes(pattern) || elementId.includes(pattern)) {
                    console.log(`Excluding video/iframe element with pattern "${pattern}":`, element);
                    return true;
                }
            }
        }
        
        // Check parent elements for exclusion patterns (up to 3 levels)
        let parent = element.parentElement;
        let level = 0;
        while (parent && level < 3) {
            const parentClassName = parent.className ? parent.className.toLowerCase() : '';
            const parentId = parent.id ? parent.id.toLowerCase() : '';
            
            for (const pattern of excludePatterns) {
                if (parentClassName.includes(pattern) || parentId.includes(pattern)) {
                    console.log(`Excluding element with parent pattern "${pattern}":`, element, 'Parent:', parent);
                    return true;
                }
            }
            
            parent = parent.parentElement;
            level++;
        }
        
        return false;
    }

    // Step 1: Find ALL hidden elements on the page
    const allElements = document.querySelectorAll('*');
    const hiddenElements = [];

    allElements.forEach(function(element) {
        if (hasDisplayNone(element) || hasAriaHidden(element)) {
            hiddenElements.push(element);
        }
    });

    console.log(`Found ${hiddenElements.length} hidden elements total`);

    // Step 2: Filter to only elements within main content areas
    const mainContentHiddenElements = hiddenElements.filter(function(element) {
        return isInMainContent(element);
    });

    console.log(`Found ${mainContentHiddenElements.length} hidden elements in main content areas`);

    // Step 3: Filter out excluded video and form elements
    const filteredHiddenElements = mainContentHiddenElements.filter(function(element) {
        return !shouldExcludeElement(element);
    });

    console.log(`After excluding video/form elements: ${filteredHiddenElements.length} elements remaining`);

    return filteredHiddenElements;
}

// Auto-detection function - calls the exact same function as Quick Action
function autoDetectHiddenDivs() {
    if (!autoDetectionEnabled || hasShownNotificationThisPage) {
        if (debugMode) {
            console.log('ShowHidden Auto-Detection: Skipping - enabled:', autoDetectionEnabled, 'already shown:', hasShownNotificationThisPage);
        }
        return;
    }
    
    try {
        if (debugMode) {
            console.log('ShowHidden Auto-Detection: Starting detection using shared function...');
        }
        
        // Call the EXACT same function that Quick Action uses
        const filteredHiddenElements = getFilteredHiddenElements();
        
        // Show notification if hidden elements found
        if (filteredHiddenElements.length > 0) {
            showHiddenDivsNotification(filteredHiddenElements.length);
            hasShownNotificationThisPage = true;
        }
        
    } catch (error) {
        console.error('ShowHidden Auto-Detection: Error during detection:', error);
    }
}