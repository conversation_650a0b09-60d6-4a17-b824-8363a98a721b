// HTML Cleaning Functions

// Helper function to strip HTML tags and extract clean text content
function stripHtmlTags(html) {
    try {
        // Create a temporary DOM element to parse HTML and extract text
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        
        // Get text content and clean up extra whitespace
        let textContent = tempDiv.textContent || tempDiv.innerText || '';
        
        // Remove extra whitespace and normalize
        textContent = textContent.replace(/\s+/g, ' ').trim();
        
        return textContent;
    } catch (error) {
        console.warn('Error stripping HTML tags:', error);
        // Fallback: simple regex-based tag removal
        return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
    }
}

function getHTMLCleanerSettings() {
    return new Promise((resolve) => {
        const defaultSettings = {
            removeStyles: true,
            removeClasses: true,
            removeIds: true,
            removeComments: true,
            removeEmptyTags: true,
            removeExtraSpaces: true,
            removeDataAttributes: true,
            removeWrapperDivs: true,
            removeCitationNumbers: true,
            removeAllAttributes: true
        };

        try {
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                chrome.storage.local.get(['htmlCleanerSettings'], (result) => {
                    if (chrome.runtime.lastError) {
                        console.warn('CopyElementAction: Chrome storage access failed, using default settings:', chrome.runtime.lastError);
                        resolve(defaultSettings);
                    } else {
                        resolve(result.htmlCleanerSettings || defaultSettings);
                    }
                });
            } else {
                console.warn('CopyElementAction: Chrome storage not available, using default settings');
                resolve(defaultSettings);
            }
        } catch (error) {
            console.warn('CopyElementAction: Error accessing chrome storage:', error);
            resolve(defaultSettings);
        }
    });
}

function cleanHTMLContent(htmlContent, options = {}) {
    let text = htmlContent;
    
    // Helper function: Find and replace with loop until no more matches
    function helyettesit(search, replace) {
        while (text.indexOf(search) !== -1) {
            text = text.replace(search, replace);
        }
    }
    
    // Helper function: Remove content between start and end markers
    function torolTagbanKettoKozt(startMarker, endMarker) {
        const startLen = startMarker.length;
        const endLen = endMarker.length;
        let result = '';
        let i = 0;
        let inside = false;

        while (i < text.length) {
            // Check for start marker
            if (!inside && text.substr(i, startLen) === startMarker) {
                inside = true;
                i += startLen;
                continue;
            }

            // Check for end marker
            if (inside && text.substr(i, endLen) === endMarker) {
                inside = false;
                i += endLen;
                continue;
            }

            // Add character if not inside marker pair
            if (!inside) {
                result += text[i];
            }

            i++;
        }

        text = result;
    }
    
    // Clean up single &nbsp; occurrences
    function csakEgyNbspTagotTorul() {
        helyettesit('&nbsp;&nbsp;', '&nbsp;');
        helyettesit(' &nbsp;', ' ');
        helyettesit('&nbsp; ', ' ');
        helyettesit('<p>&nbsp;</p>', '');
        helyettesit('<div>&nbsp;</div>', '');
        helyettesit('<span>&nbsp;</span>', '');
    }

    // Remove line breaks and tabs from content
    function csakEnteresTagotTorul() {
        helyettesit('\n', '');
        helyettesit('\t', '');
        helyettesit('\r', '');
    }

    // Apply cleaning options based on what's selected
    if (options.removeStyles) {
        helyettesit(' style="', '"STYLE_MARKER"');
        torolTagbanKettoKozt('"STYLE_MARKER"', '"');
        helyettesit('"STYLE_MARKER"', '');
        
        // Also remove style attributes without quotes
        helyettesit(' style=', '=STYLE_MARKER');
        torolTagbanKettoKozt('=STYLE_MARKER', ' ');
        torolTagbanKettoKozt('=STYLE_MARKER', '>');
        helyettesit('=STYLE_MARKER', '');
    }

    if (options.removeClasses) {
        helyettesit(' class="', '"CLASS_MARKER"');
        torolTagbanKettoKozt('"CLASS_MARKER"', '"');
        helyettesit('"CLASS_MARKER"', '');
        
        // Also remove class attributes without quotes
        helyettesit(' class=', '=CLASS_MARKER');
        torolTagbanKettoKozt('=CLASS_MARKER', ' ');
        torolTagbanKettoKozt('=CLASS_MARKER', '>');
        helyettesit('=CLASS_MARKER', '');
    }

    if (options.removeIds) {
        helyettesit(' id="', '"ID_MARKER"');
        torolTagbanKettoKozt('"ID_MARKER"', '"');
        helyettesit('"ID_MARKER"', '');
        
        // Also remove id attributes without quotes
        helyettesit(' id=', '=ID_MARKER');
        torolTagbanKettoKozt('=ID_MARKER', ' ');
        torolTagbanKettoKozt('=ID_MARKER', '>');
        helyettesit('=ID_MARKER', '');
    }

    if (options.removeDataAttributes) {
        // Remove data-* attributes with quotes
        let dataAttrPattern = / data-[^=]*="[^"]*"/g;
        text = text.replace(dataAttrPattern, '');
        
        // Remove data-* attributes without quotes (single word values)
        dataAttrPattern = / data-[^=]*=[^\s>]*/g;
        text = text.replace(dataAttrPattern, '');
        
        // Clean up any remaining data- patterns
        helyettesit(' data-', ' DATA_MARKER');
        torolTagbanKettoKozt(' DATA_MARKER', '"');
        torolTagbanKettoKozt(' DATA_MARKER', ' ');
        torolTagbanKettoKozt(' DATA_MARKER', '>');
        helyettesit(' DATA_MARKER', '');
    }

    // 🆕 WRAPPER DIV REMOVAL - Remove outer wrapper divs that only contain meaningful content
    if (options.removeWrapperDivs) {
        let cleaned = false;
        let iterations = 0;
        let beforeText = '';
        const maxIterations = 10;
        
        do {
            beforeText = text;
            iterations++;
            
            // Remove wrapper divs pattern: <div><div><div>CONTENT</div></div></div> -> CONTENT
            // This regex matches any number of opening divs followed by content and matching closing divs
            const wrapperPattern = /^(\s*<div[^>]*>\s*)+(.+?)(\s*<\/div>\s*)+$/s;
            
            if (wrapperPattern.test(text)) {
                const match = text.match(wrapperPattern);
                if (match && match[2]) {
                    const innerContent = match[2].trim();
                    
                    // Only unwrap if inner content starts with meaningful tags
                    if (innerContent.match(/^<(?:ul|ol|li|p|h[1-6]|table|blockquote|article|section|nav|header|footer|main|aside|span|strong|em|a)\b/)) {
                        text = innerContent;
                        cleaned = true;
                    } else {
                        cleaned = false;
                    }
                } else {
                    cleaned = false;
                }
            } else {
                cleaned = false;
            }
            
        } while (cleaned && iterations < maxIterations && text !== beforeText);
    }

    if (options.removeComments) {
        torolTagbanKettoKozt('<!--', '-->');
    }

    // Remove citation numbers and citation links
    if (options.removeCitationNumbers) {
        // Remove citation elements (links that contain only numbers)
        // Pattern: <a ...>1</a>, <a ...>23</a>, etc.
        let citationPattern = /<a[^>]*>\s*\d+\s*<\/a>/g;
        text = text.replace(citationPattern, '');
        
        // Remove span citations with complex structures like the examples
        // Pattern: <span class="citation"...>complex content with numbers</span>
        citationPattern = /<span[^>]*class="[^"]*citation[^"]*"[^>]*>.*?<\/span>/g;
        text = text.replace(citationPattern, '');
        
        // Remove other citation-like elements
        citationPattern = /<[^>]*class="[^"]*citation[^"]*"[^>]*>.*?<\/[^>]*>/g;
        text = text.replace(citationPattern, '');
        
        // Remove trailing numbers from text content (but preserve punctuation)
        // This handles cases like "text123." or "text456," or "text789)"
        // Pattern: one or more digits followed by optional punctuation at the end of sentences
        let trailingNumberPattern = /(\w)\d+([.!?;,)]?\s*$)/gm;
        text = text.replace(trailingNumberPattern, '$1$2');
        
        // Handle cases where numbers appear before closing tags
        // Pattern: numbers right before closing paragraph/div tags
        trailingNumberPattern = /\d+(\s*<\/(p|div|span|h[1-6])>)/g;
        text = text.replace(trailingNumberPattern, '$1');
        
        // Clean up trailing numbers that appear at the end of lines within content
        // This catches patterns like "content123\n" or "content456 "
        trailingNumberPattern = /(\w)\d+(\s*[\n\r])/g;
        text = text.replace(trailingNumberPattern, '$1$2');
        
        // Final cleanup: remove isolated numbers at the end of text blocks
        // This handles remaining standalone numbers
        trailingNumberPattern = /\s+\d+\s*([.!?;,)]?\s*)$/gm;
        text = text.replace(trailingNumberPattern, '$1');
        
        console.log('Citation numbers removal completed');
    }

    // Remove ALL attributes from ALL tags (except preserve title attributes on heading tags)
    if (options.removeAllAttributes) {
        // Single-pass approach to handle all tags safely
        text = text.replace(/<(\w+)([^>]*)>/g, (match, tagName, attributes) => {
            const tagLower = tagName.toLowerCase();
            
            // Special handling for heading tags (h1, h2, h3, h4, h5, h6)
            if (tagLower.match(/^h[1-6]$/)) {
                // Extract title attribute if it exists
                const titleMatch = attributes.match(/\s+title\s*=\s*["']([^"']*)["']/i);
                if (titleMatch) {
                    // Keep only the title attribute for heading tags
                    return `<${tagName} title="${titleMatch[1]}">`;
                } else {
                    // No title attribute, remove all attributes but keep the tag structure
                    return `<${tagName}>`;
                }
            } else {
                // For all other tags, remove all attributes
                return `<${tagName}>`;
            }
        });
        
        console.log('All tag attributes removal completed (preserved title attributes on headings)');
    }

    if (options.removeEmptyTags) {
        // Remove tags with only whitespace or &nbsp;
        csakEgyNbspTagotTorul();
        csakEnteresTagotTorul();
        
        // Remove completely empty tags
        const emptyTagPatterns = [
            /<([^>]+)>\s*<\/\1>/g,
            /<([^>]+)>&nbsp;<\/\1>/g,
            /<([^>]+)>&nbsp;\s*<\/\1>/g,
            /<([^>]+)>\s*&nbsp;<\/\1>/g
        ];

        emptyTagPatterns.forEach(pattern => {
            let matches;
            do {
                matches = text.match(pattern);
                if (matches) {
                    text = text.replace(pattern, '');
                }
            } while (matches);
        });

        // Specific empty div/span cleanup
        helyettesit('<div></div>', '');
        helyettesit('<span></span>', '');
        helyettesit('<p></p>', '');
        helyettesit('<div> </div>', '');
        helyettesit('<span> </span>', '');
        helyettesit('<p> </p>', '');
    }

    if (options.removeExtraSpaces) {
        // Remove multiple consecutive spaces
        helyettesit('  ', ' ');
        helyettesit('   ', ' ');
        helyettesit('    ', ' ');
        
        // Clean up spaces around tags
        helyettesit('> <', '><');
        helyettesit(' >', '>');
        helyettesit('< ', '<');
    }

    // Always clean up extra whitespace and normalize
    helyettesit('\n', ' ');
    helyettesit('\r', ' ');
    helyettesit('\t', ' ');
    
    // Clean up multiple spaces created by line break removal
    while (text.includes('  ')) {
        helyettesit('  ', ' ');
    }

    return text.trim();
}

// Copy Element Action - Interactive element content copying tool
// Prevent duplicate class declaration
if (typeof window.CopyElementAction === 'undefined') {

class CopyElementAction {
    constructor() {
        this.name = 'Copy Element';
        this.description = 'Interactive tool to copy element content by clicking';
    }

    // Execute the copy element functionality directly on the page
    execute() {
        return new Promise((resolve, reject) => {
            try {
                // Check if already active and handle gracefully
                if (window.copyElementActive) {
                    console.log('Copy Element already active - performing quick reset');
                    // Quick synchronous cleanup instead of full async reset
                    this.quickCleanup();
                    // Clear the active flag temporarily
                    window.copyElementActive = false;
                    // Small delay to ensure cleanup is complete
                    setTimeout(() => {
                        if (!window.copyElementActive) { // Only restart if not activated again
                            this.execute().then(resolve).catch(reject);
                        }
                    }, 10); // Reduced from 100ms to 10ms for faster response
                    return;
                }
                
                // Mark as active immediately to prevent race conditions
                window.copyElementActive = true;
                
                // Remove existing copy element panels and styles if present
                document.querySelectorAll('.copy-element-notification, .copy-element-styles').forEach(el => el.remove());
                
                // Remove any existing hover effects and labels
                document.querySelectorAll('.copy-element-hover').forEach(el => {
                    el.classList.remove('copy-element-hover');
                    
                    // Restore original positioning if we changed it
                    if (el.getAttribute('data-copy-element-positioned') === 'true') {
                        el.style.position = '';
                        el.removeAttribute('data-copy-element-positioned');
                    }
                });
                
                // Remove any existing labels
                document.querySelectorAll('[data-copy-element-label]').forEach(el => el.remove());
                
                // Remove any existing event listeners
                if (window.copyElementCleanup) {
                    window.copyElementCleanup();
                }
                
                // Add styles for hover effect, labels, and notifications
                const style = document.createElement('style');
                style.className = 'copy-element-styles';
                style.setAttribute('data-copy-element-style', 'true');
                style.textContent = `
                    .copy-element-hover { 
                        outline: 2px solid #7C3AED !important; 
                        outline-offset: 2px !important;
                        cursor: pointer !important;
                        position: relative !important;
                    }
                    .copy-element-label {
                        position: absolute !important;
                        top: -2px !important;
                        left: -2px !important;
                        background: #7C3AED !important;
                        color: white !important;
                        padding: 2px 6px !important;
                        font-size: 10px !important;
                        font-weight: bold !important;
                        font-family: Arial, sans-serif !important;
                        line-height: 1 !important;
                        z-index: 10001 !important;
                        border-radius: 0 0 4px 0 !important;
                        pointer-events: none !important;
                        text-transform: uppercase !important;
                        white-space: nowrap !important;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
                    }
                    .copy-element-notification {
                        position: fixed;
                        top: 20px;
                        left: 50%;
                        transform: translateX(-50%);
                        background: #0a0a0a;
                        color: #7C3AED;
                        border: 1px solid #7C3AED;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 500;
                        opacity: 0;
                        z-index: 9999999;
                        pointer-events: none;
                        transition: opacity 0.4s ease;
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                        box-shadow: 0 4px 16px rgba(0,0,0,0.4);
                    }
                    .copy-element-notification.show {
                        opacity: 1;
                    }
                    .copy-element-instructions {
                        position: fixed;
                        bottom: 20px;
                        right: 20px;
                        background: #0a0a0a;
                        color: #d1d5db;
                        border: 1px solid #7C3AED;
                        padding: 16px 20px;
                        border-radius: 8px;
                        font-size: 13px;
                        z-index: 9999999;
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                        box-shadow: 0 4px 16px rgba(0,0,0,0.4);
                        max-width: 320px;
                        line-height: 1.4;
                    }
                `;
                document.head.appendChild(style);

                // Create instructions panel
                const instructions = document.createElement('div');
                instructions.className = 'copy-element-instructions';
                instructions.setAttribute('data-copy-element-instructions', 'true');
                instructions.innerHTML = `
                    <div style="margin-bottom: 8px; font-weight: 600; color: #7C3AED;">Copy Element Active</div>
                    <div style="margin-bottom: 6px;">🖱️ <strong>Click:</strong> Copy text content</div>
                    <div style="margin-bottom: 6px;">🔗 <strong>Link Element:</strong> Copy URL</div>
                    <div style="margin-bottom: 6px;">🖼️ <strong>Image:</strong> Copy image URL</div>
                    <div style="margin-bottom: 6px;">🎨 <strong>SVG:</strong> Copy SVG code</div>
                    <div style="margin-bottom: 6px;">🎬 <strong>Video/iframe:</strong> Copy clean URL</div>
                    <div style="margin-bottom: 6px;">⬆️ <strong>Shift+Click:</strong> Copy full HTML code</div>
                    <div style="margin-bottom: 6px;">🔥 <strong>Cmd+Shift+Click (Mac) / Ctrl+Shift+Click (PC):</strong> Copy RAW HTML (no cleaning)</div>
                    <div style="font-size: 11px; color: #9ca3af; margin-top: 8px;">Stays active - Press ESC to close</div>
                `;
                document.body.appendChild(instructions);

                // Check if Auto Clean & Title is enabled and add conditional reminder
                chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
                    const settings = result.gmbExtractorSettings || {};
                    const isAutoCleanAndTitleEnabled = settings.autoCleanAndTitleEnabled === true;
                    
                    if (isAutoCleanAndTitleEnabled) {
                        console.log('CopyElementAction: Auto Clean & Title enabled - adding reminder box');
                        
                        // Create Clean & Title reminder box
                        const cleanAndTitleReminder = document.createElement('div');
                        cleanAndTitleReminder.className = 'copy-element-instructions clean-and-title-reminder';
                        cleanAndTitleReminder.setAttribute('data-copy-element-clean-title-reminder', 'true');
                        cleanAndTitleReminder.style.cssText = `
                            position: fixed;
                            bottom: 300px;
                            right: 20px;
                            background: rgba(0, 0, 0, 0.95);
                            color: #d1d5db;
                            border: 1px solid #7C3AED;
                            border-radius: 8px;
                            padding: 16px 20px;
                            font-size: 12px;
                            z-index: 9999999;
                            pointer-events: none;
                            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                            box-shadow: 0 4px 16px rgba(0,0,0,0.4);
                            max-width: 320px;
                            line-height: 1.4;
                        `;
                        cleanAndTitleReminder.innerHTML = `
                            <div style="margin-bottom: 8px; font-weight: 600; color: #10B981;">🧹 Clean & Title Active</div>
                            <div style="margin-bottom: 6px;">⬆️ <strong>Shift+Click:</strong> Applies HTML cleaning with configurable options</div>
                            <div style="margin-bottom: 6px;">🏷️ <strong>Auto Title:</strong> Adds title attributes to heading tags</div>
                            <div style="font-size: 11px; color: #9ca3af; margin-top: 8px;">Configure cleaning options in Settings > HTML Cleaner</div>
                        `;
                        document.body.appendChild(cleanAndTitleReminder);
                    }
                });

                function findSVGParent(element) {
                    // Check if current element is SVG
                    if (element.tagName && element.tagName.toLowerCase() === 'svg') {
                        return element;
                    }
                    
                    // Check if element is inside an SVG
                    let parent = element.parentElement;
                    while (parent) {
                        if (parent.tagName && parent.tagName.toLowerCase() === 'svg') {
                            return parent;
                        }
                        parent = parent.parentElement;
                    }
                    
                    return null;
                }

                function getVideoUrl(element) {
                    const tag = element.tagName.toLowerCase();
                    
                    if (tag === 'video') {
                        // For HTML5 video elements
                        if (element.src) {
                            return element.src;
                        }
                        // Check for source elements within video
                        const source = element.querySelector('source');
                        if (source && source.src) {
                            return source.src;
                        }
                    } else if (tag === 'iframe') {
                        // For embedded videos (YouTube, etc.)
                        return element.src;
                    } else if (tag === 'a' && element.href) {
                        // For links that might point to video files
                        const href = element.href;
                        const videoExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv'];
                        const hasVideoExtension = videoExtensions.some(ext => href.toLowerCase().includes(ext));
                        const isYouTubeLink = href.includes('youtube.com') || href.includes('youtu.be');
                        
                        if (hasVideoExtension || isYouTubeLink) {
                            return href;
                        }
                    } else {
                        // Check if element contains an iframe with video content
                        const iframe = element.querySelector('iframe');
                        if (iframe && iframe.src) {
                            return iframe.src;
                        }
                        
                        // Check if element contains a video element
                        const video = element.querySelector('video');
                        if (video) {
                            if (video.src) {
                                return video.src;
                            }
                            const source = video.querySelector('source');
                            if (source && source.src) {
                                return source.src;
                            }
                        }
                    }
                    
                    return null;
                }

                function isVideoContainer(element) {
                    // Check if element contains video-related content
                    const iframe = element.querySelector('iframe');
                    if (iframe && iframe.src) {
                        const src = iframe.src.toLowerCase();
                        if (src.includes('youtube.com') || src.includes('youtu.be') || 
                            src.includes('vimeo.com') || src.includes('dailymotion.com') ||
                            src.includes('.mp4') || src.includes('.mov') || src.includes('.webm')) {
                            return true;
                        }
                    }
                    
                    const video = element.querySelector('video');
                    if (video) {
                        return true;
                    }
                    
                    // Check class names and attributes for video-related keywords
                    const className = element.className ? element.className.toLowerCase() : '';
                    const id = element.id ? element.id.toLowerCase() : '';
                    const videoKeywords = ['video', 'player', 'embed', 'youtube', 'vimeo'];
                    
                    return videoKeywords.some(keyword => 
                        className.includes(keyword) || id.includes(keyword)
                    );
                }

                function cleanVideoUrl(url) {
                    if (!url) return url;
                    
                    // Extract clean YouTube URLs
                    if (url.includes('youtube.com/embed/')) {
                        const videoId = url.match(/youtube\.com\/embed\/([^?&]+)/);
                        if (videoId) {
                            return `https://www.youtube.com/watch?v=${videoId[1]}`;
                        }
                    }
                    
                    // Extract clean Vimeo URLs
                    if (url.includes('vimeo.com')) {
                        const videoId = url.match(/vimeo\.com\/(?:video\/)?(\d+)/);
                        if (videoId) {
                            return `https://vimeo.com/${videoId[1]}`;
                        }
                    }
                    
                    // For direct video files, return as-is
                    const videoExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv'];
                    if (videoExtensions.some(ext => url.toLowerCase().includes(ext))) {
                        return url;
                    }
                    
                    // Return original URL if no cleaning rules apply
                    return url;
                }

                function createElementLabel(element) {
                    const tag = element.tagName.toLowerCase();
                    let labelText = tag;
                    
                    console.log('Creating label for element:', element.tagName, element);
                    
                    // Check if element is SVG or inside SVG
                    const svgParent = findSVGParent(element);
                    if (svgParent) {
                        labelText = 'svg';
                        console.log('SVG detected, setting label to svg');
                    } else if (tag === 'svg') {
                        labelText = 'svg';
                        console.log('Direct SVG element, setting label to svg');
                    } else if (isVideoContainer(element)) {
                        // Check what type of video it contains
                        const iframe = element.querySelector('iframe');
                        if (iframe && iframe.src) {
                            const src = iframe.src.toLowerCase();
                            if (src.includes('youtube.com') || src.includes('youtu.be')) {
                                labelText = 'youtube';
                            } else if (src.includes('vimeo.com')) {
                                labelText = 'vimeo';
                            } else {
                                labelText = 'video';
                            }
                        } else {
                            labelText = 'video';
                        }
                    } else if (tag === 'a' && element.href) {
                        // Check if it's a JavaScript link
                        if (element.href.toLowerCase().startsWith('javascript:')) {
                            labelText = 'js-link';
                        } else {
                            // Check if it's a video link
                            const videoUrl = getVideoUrl(element);
                            if (videoUrl) {
                                if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
                                    labelText = 'youtube';
                                } else {
                                    labelText = 'video';
                                }
                            } else {
                                labelText = 'link';
                            }
                        }
                    } else if (tag === 'img') {
                        labelText = 'image';
                    } else if (tag === 'video') {
                        labelText = 'video';
                    } else if (tag === 'iframe') {
                        // Check if it's a YouTube iframe
                        const src = element.src || '';
                        if (src.includes('youtube.com') || src.includes('youtu.be')) {
                            labelText = 'youtube';
                        } else {
                            labelText = 'iframe';
                        }
                    } else if (tag === 'span') {
                        // Check if span is inside a link for better labeling
                        const parentLink = element.closest('a');
                        if (parentLink) {
                            labelText = 'span-text';
                        } else {
                            labelText = 'span';
                        }
                    } else if (tag === 'button' || tag === 'input') {
                        // Check if button has URL-like attributes or onclick handlers
                        const hasUrlData = element.getAttribute('data-href') || 
                                          element.getAttribute('data-url') || 
                                          element.getAttribute('data-link');
                        const hasOnclickUrl = element.getAttribute('onclick')?.includes('location') || 
                                            element.getAttribute('onclick')?.includes('window.open') ||
                                            element.getAttribute('onclick')?.includes('href');
                        
                        if (hasUrlData || hasOnclickUrl) {
                            labelText = 'link-btn';
                        } else if (tag === 'button') {
                            labelText = 'button';
                        } else {
                            labelText = element.type ? `${element.type}` : 'input';
                        }
                    }
                    
                    console.log('Final label text:', labelText);
                    
                    const label = document.createElement('div');
                    label.className = 'copy-element-label';
                    label.setAttribute('data-copy-element-label', 'true');
                    label.textContent = labelText;
                    
                    return label;
                }

                function showNotification(msg, type = 'success') {
                    const existing = document.querySelector('.copy-element-notification');
                    if (existing) {
                        existing.remove();
                    }

                    const notif = document.createElement('div');
                    notif.className = 'copy-element-notification';
                    notif.setAttribute('data-copy-element-notification', 'true');
                    
                    if (type === 'error') {
                        notif.style.color = '#ef4444';
                        notif.style.borderColor = '#ef4444';
                    }
                    
                    notif.textContent = msg;
                    document.body.appendChild(notif);

                    setTimeout(() => notif.classList.add('show'), 10);
                    setTimeout(() => {
                        notif.classList.remove('show');
                        setTimeout(() => {
                            if (notif.parentNode) {
                                notif.remove();
                            }
                        }, 400);
                    }, 2500); // Extended from 1500ms to 2500ms for longer visibility
                }

                // Add debouncing and performance optimization for mouseover
                // Declare variables in window scope to persist across calls
                window.copyElementHoverTimeout = null;
                window.copyElementCurrentHoverElement = null;
                window.copyElementIsProcessing = false;

                window.copyElementMouseOverHandler = function mouseOverHandler(e) {
                    // Don't add hover effect to copy element components
                    if (e.target.closest('[data-copy-element-instructions], [data-copy-element-notification], [data-copy-element-label]')) {
                        return;
                    }
                    
                    // Aggressive throttling - skip if already processing
                    if (window.copyElementIsProcessing) {
                        return;
                    }
                    
                    // Check if we're dealing with SVG - use the SVG parent for styling if found
                    const svgParent = findSVGParent(e.target);
                    const targetElement = svgParent || e.target;
                    
                    // Skip if same element to avoid unnecessary processing
                    if (window.copyElementCurrentHoverElement === targetElement) {
                        return;
                    }
                    
                    // Set processing flag to prevent rapid-fire events
                    window.copyElementIsProcessing = true;
                    
                    // Clear any existing timeout
                    if (window.copyElementHoverTimeout) {
                        clearTimeout(window.copyElementHoverTimeout);
                        window.copyElementHoverTimeout = null;
                    }
                    
                    // Clean up previous element immediately
                    if (window.copyElementCurrentHoverElement) {
                        window.copyElementCurrentHoverElement.classList.remove('copy-element-hover');
                        
                        // Remove label efficiently - check multiple locations
                        const existingLabel = window.copyElementCurrentHoverElement.querySelector('[data-copy-element-label]');
                        if (existingLabel) {
                            existingLabel.remove();
                        }
                        
                        // Also check parent for absolutely positioned labels
                        if (window.copyElementCurrentHoverElement.parentElement) {
                            const parentLabel = window.copyElementCurrentHoverElement.parentElement.querySelector('[data-copy-element-label]');
                            if (parentLabel) {
                                parentLabel.remove();
                            }
                        }
                        
                        // Restore original positioning if we changed it
                        if (window.copyElementCurrentHoverElement.getAttribute('data-copy-element-positioned') === 'true') {
                            window.copyElementCurrentHoverElement.style.position = '';
                            window.copyElementCurrentHoverElement.removeAttribute('data-copy-element-positioned');
                        }
                    }
                    
                    // Set new current element
                    window.copyElementCurrentHoverElement = targetElement;
                    
                    // Add hover effect immediately for responsiveness
                    targetElement.classList.add('copy-element-hover');
                    
                    // Reset processing flag immediately for hover effect, but delay label creation
                    setTimeout(() => {
                        window.copyElementIsProcessing = false;
                    }, 50);
                    
                    // Only create label after 500ms of stationary hovering
                    window.copyElementHoverTimeout = setTimeout(() => {
                        // Triple-check element hasn't changed and user is still hovering
                        if (window.copyElementCurrentHoverElement !== targetElement) {
                            return;
                        }
                        
                        // Check if element still has hover class (user might have moved away)
                        if (!targetElement.classList.contains('copy-element-hover')) {
                            return;
                        }
                        
                        try {
                            // Create and add label (use targetElement for consistent labeling)
                            const label = createElementLabel(targetElement);
                            
                            // Optimize positioning - avoid getBoundingClientRect when possible
                            const voidElements = ['IMG', 'INPUT', 'BR', 'HR'];
                            const isSVG = targetElement.tagName.toLowerCase() === 'svg';
                            
                            if (voidElements.includes(targetElement.tagName)) {
                                // For void elements, use simpler positioning
                                if (targetElement.parentElement) {
                                    targetElement.parentElement.appendChild(label);
                                    label.style.position = 'absolute';
                                    label.style.left = '0px';
                                    label.style.top = '0px';
                                }
                            } else if (isSVG) {
                                // For SVGs, try simple insertion first
                                try {
                                    targetElement.insertBefore(label, targetElement.firstChild);
                                } catch (error) {
                                    // Fallback to parent positioning
                                    if (targetElement.parentElement) {
                                        targetElement.parentElement.appendChild(label);
                                        label.style.position = 'absolute';
                                        label.style.left = '0px';
                                        label.style.top = '0px';
                                    }
                                }
                            } else {
                                // For regular elements, use simple insertion
                                try {
                                    // Ensure relative positioning only when needed
                                    const computedStyle = window.getComputedStyle(targetElement);
                                    if (computedStyle.position === 'static') {
                                        targetElement.style.position = 'relative';
                                        targetElement.setAttribute('data-copy-element-positioned', 'true');
                                    }
                                    targetElement.insertBefore(label, targetElement.firstChild);
                                } catch (error) {
                                    // Fallback to parent positioning
                                    if (targetElement.parentElement) {
                                        targetElement.parentElement.appendChild(label);
                                        label.style.position = 'absolute';
                                        label.style.left = '0px';
                                        label.style.top = '0px';
                                    }
                                }
                            }
                        } catch (error) {
                            console.warn('Copy Element: Label creation failed:', error);
                        }
                    }, 500); // 300ms delay for label creation - only after stationary hovering
                }

                window.copyElementMouseOutHandler = function mouseOutHandler(e) {
                    // Simplified mouseout - the mouseover handler now manages cleanup efficiently
                    // This prevents interference with the optimized hover management
                    
                    // Only clear timeout if mouse leaves the current hover element
                    const svgParent = findSVGParent(e.target);
                    const targetElement = svgParent || e.target;
                    
                    if (window.copyElementCurrentHoverElement === targetElement) {
                        // Clear the timeout to prevent delayed label creation
                        if (window.copyElementHoverTimeout) {
                            clearTimeout(window.copyElementHoverTimeout);
                            window.copyElementHoverTimeout = null;
                        }
                        // Reset processing flag
                        window.copyElementIsProcessing = false;
                    }
                }

                window.copyElementClickHandler = function clickHandler(e) {
                    // Don't handle clicks on copy element components
                    if (e.target.closest('[data-copy-element-instructions], [data-copy-element-notification], [data-copy-element-label]')) {
                        return;
                    }

                    // Prevent all default behaviors immediately and stop event propagation
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    
                    // Additional prevention for any other event handlers
                    const target = e.target;
                    const targetTag = target.tagName.toLowerCase();
                    
                    // Store original href for later use (but don't modify the element)
                    let originalLinkHref = null;
                    let linkElement = null;
                    
                    if (targetTag === 'a' || target.closest('a')) {
                        linkElement = targetTag === 'a' ? target : target.closest('a');
                        if (linkElement) {
                            // Store original href for our copy logic, but DON'T modify the element
                            originalLinkHref = linkElement.href;
                        }
                    }

                    const tag = e.target.tagName;
                    let contentToCopy = '';
                    let message = '';
                    
                    console.log('Click handler triggered for:', {
                        tag: tag,
                        element: e.target,
                        textContent: e.target.textContent,
                        innerText: e.target.innerText,
                        innerHTML: e.target.innerHTML,
                        parentElement: e.target.parentElement ? e.target.parentElement.tagName : 'none',
                        isInsideLink: !!e.target.closest('a')
                    });

                    try {
                        // General cleanup function for any element
                        function cleanupElement(element) {
                            // Remove our classes and attributes
                            element.classList.remove('copy-element-hover');
                            element.removeAttribute('data-copy-element-positioned');
                            
                            // Remove inline styles we added
                            if (element.style.position === 'relative' && element.hasAttribute('data-copy-element-positioned')) {
                                element.style.position = '';
                            }
                            
                            // Clean up all child elements recursively
                            const children = element.children;
                            for (let i = 0; i < children.length; i++) {
                                cleanupElement(children[i]);
                            }
                        }
                        
                        function createCleanClone(element) {
                            // Clone the element to avoid copying our modifications
                            const clone = element.cloneNode(true);
                            
                            // Clean up the clone and all child elements
                            cleanupElement(clone);
                            
                            // Remove any label elements we added
                            const labels = clone.querySelectorAll('[data-copy-element-label]');
                            labels.forEach(label => label.remove());
                            
                            return clone;
                        }
                        
                        // Check if element is SVG or inside SVG first
                        const svgParent = findSVGParent(e.target);
                        
                        if (e.shiftKey) {
                            // Copy full HTML (use SVG parent if inside SVG, otherwise use target element)
                            const elementToClone = svgParent || e.target;
                            
                            if (e.metaKey || e.ctrlKey) {
                                // Command+Shift+Click (Mac) / Ctrl+Shift+Click (PC) = RAW HTML without cleaning
                                contentToCopy = elementToClone.outerHTML;
                                if (svgParent) {
                                    message = `✓ Copied RAW SVG HTML (no cleaning)`;
                                } else {
                                    message = `✓ Copied RAW ${tag} HTML (no cleaning)`;
                                }
                            } else {
                                // Shift+Click = cleaned HTML
                                const cleanClone = createCleanClone(elementToClone);
                                contentToCopy = cleanClone.outerHTML;
                                if (svgParent) {
                                    message = `✓ Copied SVG HTML`;
                                } else {
                                    message = `✓ Copied ${tag} HTML`;
                                }
                            }
                        } else if (svgParent) {
                            // Copy SVG code (for SVG or any element inside SVG)
                            const cleanClone = createCleanClone(svgParent);
                            contentToCopy = cleanClone.outerHTML;
                            message = `✓ Copied SVG code`;
                        } else if (isVideoContainer(e.target)) {
                            // Handle video containers (like divs containing iframes) - Shift+Click copies HTML, regular click copies URL
                            if (e.shiftKey) {
                                if (e.metaKey || e.ctrlKey) {
                                    // Command+Shift+Click (Mac) / Ctrl+Shift+Click (PC) = RAW HTML without cleaning
                                    contentToCopy = e.target.outerHTML;
                                    message = `✓ Copied RAW video container HTML (no cleaning)`;
                                } else {
                                    // Shift+Click = cleaned HTML
                                    const cleanClone = createCleanClone(e.target);
                                    contentToCopy = cleanClone.outerHTML;
                                    message = `✓ Copied video container HTML`;
                                }
                            } else {
                                // Handle video containers (like divs containing iframes)
                                const videoUrl = getVideoUrl(e.target);
                                if (videoUrl) {
                                    const cleanUrl = cleanVideoUrl(videoUrl);
                                    contentToCopy = cleanUrl;
                                    
                                    if (cleanUrl.includes('youtube.com') || cleanUrl.includes('youtu.be')) {
                                        message = `✓ Copied YouTube URL`;
                                    } else if (cleanUrl.includes('vimeo.com')) {
                                        message = `✓ Copied Vimeo URL`;
                                    } else {
                                        message = `✓ Copied video URL`;
                                    }
                                } else {
                                    showNotification('⚠️ No video URL found in container', 'error');
                                    return;
                                }
                            }
                        } else if (tag === 'VIDEO') {
                            // Handle VIDEO elements - Shift+Click copies HTML, regular click copies URL
                            if (e.shiftKey) {
                                if (e.metaKey || e.ctrlKey) {
                                    // Command+Shift+Click (Mac) / Ctrl+Shift+Click (PC) = RAW HTML without cleaning
                                    contentToCopy = e.target.outerHTML;
                                    message = `✓ Copied RAW VIDEO HTML (no cleaning)`;
                                } else {
                                    // Shift+Click = cleaned HTML
                                    const cleanClone = createCleanClone(e.target);
                                    contentToCopy = cleanClone.outerHTML;
                                    message = `✓ Copied VIDEO HTML`;
                                }
                            } else {
                                // Copy video URL
                                const videoUrl = getVideoUrl(e.target);
                                if (videoUrl) {
                                    const cleanUrl = cleanVideoUrl(videoUrl);
                                    contentToCopy = cleanUrl;
                                    message = `✓ Copied video URL`;
                                } else {
                                    showNotification('⚠️ No video URL found', 'error');
                                    return;
                                }
                            }
                        } else if (tag === 'IFRAME') {
                            // Handle IFRAME elements - Shift+Click copies HTML, regular click copies URL
                            if (e.shiftKey) {
                                if (e.metaKey || e.ctrlKey) {
                                    // Command+Shift+Click (Mac) / Ctrl+Shift+Click (PC) = RAW HTML without cleaning
                                    contentToCopy = e.target.outerHTML;
                                    message = `✓ Copied RAW IFRAME HTML (no cleaning)`;
                                } else {
                                    // Shift+Click = cleaned HTML
                                    const cleanClone = createCleanClone(e.target);
                                    contentToCopy = cleanClone.outerHTML;
                                    message = `✓ Copied IFRAME HTML`;
                                }
                            } else {
                                // Copy iframe src (useful for YouTube and other embedded content)
                                const src = e.target.src;
                                if (src) {
                                    const cleanUrl = cleanVideoUrl(src);
                                    contentToCopy = cleanUrl;
                                    
                                    if (cleanUrl.includes('youtube.com') || cleanUrl.includes('youtu.be')) {
                                        message = `✓ Copied YouTube URL`;
                                    } else if (cleanUrl.includes('vimeo.com')) {
                                        message = `✓ Copied Vimeo URL`;
                                    } else {
                                        message = `✓ Copied iframe URL`;
                                    }
                                } else {
                                    showNotification('⚠️ No iframe URL found', 'error');
                                    return;
                                }
                            }
                        } else if (tag === 'SPAN' && e.target.closest('a')) {
                            // Special handling for SPAN elements inside links
                            console.log('SPAN inside link detected');
                            
                            // Use clean clone to avoid copying labels
                            const cleanClone = createCleanClone(e.target);
                            let spanText = cleanClone.textContent?.trim() || cleanClone.innerText?.trim() || '';
                            
                            console.log('SPAN text extraction from clean clone:', {
                                originalTextContent: e.target.textContent,
                                cleanText: spanText,
                                cleanCloneHTML: cleanClone.outerHTML
                            });
                            
                            if (spanText) {
                                contentToCopy = spanText;
                                message = `✓ Copied span text`;
                                console.log('Final content to copy:', JSON.stringify(contentToCopy));
                            } else {
                                showNotification('⚠️ No text found in span', 'error');
                                return;
                            }
                        } else if (tag === 'A' || e.target.href || e.target.closest('a')) {
                            // Handle links - use Shift+Click to toggle between URL and text
                            const linkElement = e.target.closest('a') || e.target;
                            const isDirectLinkClick = (tag === 'A');
                            
                            // Get the link URL
                            const href = originalLinkHref || linkElement.href || e.target.href;
                            
                            if (href) {
                                // Check if it's a JavaScript link (but not our temporary javascript:void(0))
                                if (href.toLowerCase().startsWith('javascript:') && href !== 'javascript:void(0)') {
                                    showNotification('⚠️ Link contains JavaScript - not copied', 'error');
                                    return;
                                }
                                
                                // Shift+Click always copies full HTML
                                if (e.shiftKey) {
                                    const elementToClone = linkElement;
                                    if (e.metaKey || e.ctrlKey) {
                                        // Command+Shift+Click (Mac) / Ctrl+Shift+Click (PC) = RAW HTML without cleaning
                                        contentToCopy = elementToClone.outerHTML;
                                        message = `✓ Copied RAW link HTML (no cleaning)`;
                                    } else {
                                        // Shift+Click = cleaned HTML
                                        const cleanClone = createCleanClone(elementToClone);
                                        contentToCopy = cleanClone.outerHTML;
                                        message = `✓ Copied link HTML`;
                                    }
                                }
                                // Direct click on anchor tag copies URL
                                else if (isDirectLinkClick) {
                                    // Check if it's a video link first
                                    const videoUrl = getVideoUrl(linkElement);
                                    if (videoUrl) {
                                        const cleanUrl = cleanVideoUrl(videoUrl);
                                        contentToCopy = cleanUrl;
                                        
                                        if (cleanUrl.includes('youtube.com') || cleanUrl.includes('youtu.be')) {
                                            message = `✓ Copied YouTube link`;
                                        } else if (cleanUrl.includes('vimeo.com')) {
                                            message = `✓ Copied Vimeo link`;
                                        } else {
                                            message = `✓ Copied video link`;
                                        }
                                    } else {
                                        // Regular link
                                        contentToCopy = href;
                                        message = `✓ Copied link URL`;
                                    }
                                }
                                // Click on elements inside link copies element text
                                else {
                                    // Get text content from the clicked element using clean clone
                                    const cleanClone = createCleanClone(e.target);
                                    let elementText = cleanClone.textContent?.trim() || cleanClone.innerText?.trim() || '';
                                    
                                    console.log('Element text extraction from clean clone:', {
                                        tag: e.target.tagName,
                                        originalTextContent: e.target.textContent,
                                        cleanText: elementText,
                                        cleanCloneHTML: cleanClone.outerHTML
                                    });
                                    
                                    if (elementText) {
                                        contentToCopy = elementText;
                                        message = `✓ Copied ${e.target.tagName} text`;
                                    } else {
                                        // If still no text, fall back to copying the link URL
                                        contentToCopy = href;
                                        message = `✓ Copied link URL (no text found in ${e.target.tagName})`;
                                    }
                                }
                            } else {
                                // Check for data attributes that might contain URLs
                                const dataHref = e.target.getAttribute('data-href') || 
                                                e.target.getAttribute('data-url') || 
                                                e.target.getAttribute('data-link');
                                if (dataHref) {
                                    contentToCopy = dataHref;
                                    message = `✓ Copied data URL`;
                                } else {
                                    // Fall back to text content
                                    const cleanClone = createCleanClone(e.target);
                                    contentToCopy = cleanClone.textContent?.trim() || cleanClone.innerText?.trim() || '';
                                    message = `✓ Copied ${tag} text`;
                                }
                            }
                        } else if (tag === 'BUTTON' || tag === 'INPUT') {
                            // Handle buttons - check for URL data attributes first
                            const buttonUrl = e.target.getAttribute('data-href') || 
                                            e.target.getAttribute('data-url') || 
                                            e.target.getAttribute('data-link');
                            
                            // Check onclick for URLs
                            const onclickAttr = e.target.getAttribute('onclick') || '';
                            const onclickUrl = onclickAttr.match(/(?:window\.)?(?:open|location\.href)\s*=\s*['"`]([^'"`]+)['"`]/)?.pop();
                            
                            // Check if button has form-related attributes (likely not a JS button)
                            const hasFormAction = e.target.type === 'submit' || e.target.getAttribute('formaction') || e.target.closest('form');
                            
                            if (buttonUrl) {
                                // Check if data URL is JavaScript protocol
                                if (buttonUrl.toLowerCase().startsWith('javascript:')) {
                                    showNotification('⚠️ Button contains JavaScript - not copied', 'error');
                                    return;
                                }
                                contentToCopy = buttonUrl;
                                message = `✓ Copied button URL`;
                            } else if (onclickUrl) {
                                // Check if onclick URL is JavaScript protocol
                                if (onclickUrl.toLowerCase().startsWith('javascript:')) {
                                    showNotification('⚠️ Button contains JavaScript - not copied', 'error');
                                    return;
                                }
                                contentToCopy = onclickUrl;
                                message = `✓ Copied button URL`;
                            } else if (onclickAttr && onclickAttr.toLowerCase().trim().startsWith('javascript:')) {
                                // Only block if onclick attribute starts with javascript: (actual JavaScript protocol)
                                showNotification('⚠️ Button contains JavaScript - not copied', 'error');
                                return;
                            } else {
                                // Check for common URL patterns in onclick even if not in quotes
                                const urlPatterns = [
                                    /window\.open\s*\(\s*['"`]([^'"`]+)['"`]/i,
                                    /location\.href\s*=\s*['"`]([^'"`]+)['"`]/i,
                                    /window\.location\s*=\s*['"`]([^'"`]+)['"`]/i,
                                    /location\.assign\s*\(\s*['"`]([^'"`]+)['"`]/i
                                ];
                                
                                let foundUrl = null;
                                for (const pattern of urlPatterns) {
                                    const match = onclickAttr.match(pattern);
                                    if (match && match[1] && !match[1].toLowerCase().startsWith('javascript:')) {
                                        foundUrl = match[1];
                                        break;
                                    }
                                }
                                
                                if (foundUrl) {
                                    contentToCopy = foundUrl;
                                    message = `✓ Copied button URL`;
                                } else if (!hasFormAction && !buttonUrl && !onclickUrl && !foundUrl) {
                                    // This is likely a JavaScript button (no URL indicators and not a form button)
                                    showNotification('⚠️ Button appears to use JavaScript - not copied', 'error');
                                    return;
                                } else {
                                    // Fall back to button text (for form buttons or other legitimate buttons)
                                    const cleanClone = createCleanClone(e.target);
                                    contentToCopy = cleanClone.textContent?.trim() || cleanClone.innerText?.trim() || e.target.value || '';
                                    message = `✓ Copied button text`;
                                }
                            }
                        } else if (tag === 'IMG') {
                            // Copy image source
                            contentToCopy = e.target.src || e.target.currentSrc || '';
                            message = `✓ Copied IMG URL`;
                        } else if (tag === 'SPAN') {
                            // Handle standalone SPAN elements (not inside links)
                            console.log('Standalone SPAN detected');
                            
                            // Use clean clone to avoid copying labels
                            const cleanClone = createCleanClone(e.target);
                            let spanText = cleanClone.textContent?.trim() || cleanClone.innerText?.trim() || '';
                            
                            console.log('Standalone SPAN text extraction from clean clone:', {
                                originalTextContent: e.target.textContent,
                                cleanText: spanText,
                                cleanCloneHTML: cleanClone.outerHTML
                            });
                            
                            if (spanText) {
                                contentToCopy = spanText;
                                message = `✓ Copied span text`;
                                console.log('Final content to copy:', JSON.stringify(contentToCopy));
                            } else {
                                showNotification('⚠️ No text found in span', 'error');
                                return;
                            }
                        } else {
                            // Copy text content (use clean clone to avoid copying labels)
                            const cleanClone = createCleanClone(e.target);
                            contentToCopy = cleanClone.textContent?.trim() || cleanClone.innerText?.trim() || '';
                            message = `✓ Copied ${tag} text`;
                        }

                        if (!contentToCopy) {
                            showNotification(`⚠️ No content to copy from ${tag}`, 'error');
                            return;
                        }

                        // For Command+Shift+Click (Mac) / Ctrl+Shift+Click (PC) with HTML content - copy RAW HTML without ANY cleaning
                        if ((e.metaKey || e.ctrlKey) && e.shiftKey && contentToCopy.includes('<')) {
                            console.log('CopyElementAction: Command+Shift+Click (Mac) / Ctrl+Shift+Click (PC) detected with HTML content - copying RAW HTML without cleaning');
                            
                            let finalMessage = message.replace('HTML', 'RAW HTML (no cleaning)');
                            
                            console.log('CopyElementAction: 🔥 RAW HTML mode - bypassing all cleaning functions');
                            performClipboardCopy(contentToCopy, finalMessage);
                            
                            return; // Exit here to avoid duplicate copying
                        }
                        
                        // For Shift+Click with HTML content - conditionally apply Auto Clean & Title if enabled
                        if (e.shiftKey && !e.metaKey && !e.ctrlKey && contentToCopy.includes('<')) {
                            console.log('CopyElementAction: Shift+Click detected with HTML content - checking Auto Clean & Title setting...');
                            
                            // Check setting FIRST before any processing
                            try {
                                chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
                                    try {
                                        const settings = result.gmbExtractorSettings || {};
                                        const isEnabled = settings.autoCleanAndTitleEnabled === true;
                                        
                                        console.log('CopyElementAction: Auto Clean & Title setting:', {
                                            rawSetting: settings.autoCleanAndTitleEnabled,
                                            isEnabled: isEnabled
                                        });
                                        
                                        let finalContent = contentToCopy;
                                        let finalMessage = message;
                                        
                                        // ONLY process if explicitly enabled
                                        if (isEnabled) {
                                            console.log('CopyElementAction: ✅ Auto Clean & Title ENABLED - applying configurable HTML cleaning');
                                            
                                            try {
                                                // Get HTML cleaner settings and apply cleaning
                                                getHTMLCleanerSettings().then(cleanerSettings => {
                                                    console.log('CopyElementAction: Using HTML cleaner settings:', cleanerSettings);
                                                    
                                                    const cleanedContent = cleanHTMLContent(contentToCopy, cleanerSettings);
                                                    
                                                    // Add title attributes to heading tags with proper HTML tag stripping
                                                    const regex = /<(h\d)([^>]*?)>(.*?)<\/h\d>/gi;
                                                    let finalCleanedContent = cleanedContent.replace(regex, (match, tagName, existingAttributes, content) => {
                                                        // Extract clean text content for the title attribute using stripHtmlTags
                                                        const cleanTextContent = stripHtmlTags(content);
                                                        
                                                        // Skip if no meaningful text content
                                                        if (!cleanTextContent) {
                                                            return match;
                                                        }
                                                        
                                                        // Escape quotes in the title content to prevent attribute corruption
                                                        const escapedTitle = cleanTextContent.replace(/"/g, '&quot;');
                                                        
                                                        // Check if title attribute already exists
                                                        if (existingAttributes && existingAttributes.includes('title=')) {
                                                            // Replace existing title attribute
                                                            const updatedAttributes = existingAttributes.replace(
                                                                /title\s*=\s*["'][^"']*["']/i, 
                                                                `title="${escapedTitle}"`
                                                            );
                                                            return `<${tagName}${updatedAttributes}>${content}</${tagName}>`;
                                                        } else {
                                                            // Add new title attribute
                                                            const attributes = existingAttributes ? existingAttributes + ' ' : ' ';
                                                            return `<${tagName}${attributes}title="${escapedTitle}">${content}</${tagName}>`;
                                                        }
                                                    });
                                                    
                                                    finalContent = finalCleanedContent.trim();
                                                    finalMessage = message.replace('HTML', 'Cleaned HTML with Configurable Options');
                                                    
                                                    console.log('CopyElementAction: ✅ Configurable HTML cleaning completed');
                                                    performClipboardCopy(finalContent, finalMessage);
                                                }).catch(error => {
                                                    console.error('CopyElementAction: Error getting HTML cleaner settings:', error);
                                                    // Fallback to original content
                                                    performClipboardCopy(contentToCopy, message);
                                                });
                                                
                                            } catch (cleanError) {
                                                console.error('CopyElementAction: Error during HTML cleaning:', cleanError);
                                                // Keep original content if processing fails
                                                finalContent = contentToCopy;
                                                finalMessage = message;
                                                console.log('CopyElementAction: Cleaning failed - using original HTML content');
                                                performClipboardCopy(finalContent, finalMessage);
                                            }
                                        } else {
                                            console.log('CopyElementAction: ❌ Auto Clean & Title DISABLED - using original HTML (no processing)');
                                            finalContent = contentToCopy;
                                            finalMessage = message;
                                            performClipboardCopy(finalContent, finalMessage);
                                        }
                                        
                                    } catch (processingError) {
                                        console.error('CopyElementAction: Error in settings processing:', processingError);
                                        // Fallback: copy original content without processing
                                        performClipboardCopy(contentToCopy, message);
                                    }
                                });
                            } catch (storageError) {
                                console.error('CopyElementAction: Error accessing chrome storage:', storageError);
                                // Fallback: copy original content without processing
                                performClipboardCopy(contentToCopy, message);
                            }
                            
                            return; // Exit here to avoid duplicate copying
                        }

                        // For non-HTML content or regular click, apply citation removal to text and copy
                        if (!contentToCopy.includes('<')) {
                            // Remove citation numbers at the end of lines (before line breaks)
                            contentToCopy = contentToCopy.replace(/(\w)\d+([.!?;,)]?)(\s*\n)/g, '$1$2$3');
                            // Remove citation numbers at the end of sentences (before periods, etc.)
                            contentToCopy = contentToCopy.replace(/(\w)\d+([.!?;,)])/g, '$1$2');
                            // Remove citation numbers at the very end of text blocks
                            contentToCopy = contentToCopy.replace(/(\w)\d+([.!?;,)]?\s*)$/g, '$1$2');
                            // Final cleanup: remove any remaining isolated numbers at line endings
                            contentToCopy = contentToCopy.replace(/\d+\.?\s*$/gm, '');
                            contentToCopy = contentToCopy.trim();
                        }
                        performClipboardCopy(contentToCopy, message);

                                                 function performClipboardCopy(content, msg) {
                             // Copy to clipboard
                             if (navigator.clipboard && window.isSecureContext) {
                                navigator.clipboard.writeText(content).then(() => {
                                    showNotification(msg);
                                    // Wait 1 second for notification visibility before cleanup and restart
                                    setTimeout(() => {
                                        cleanup();
                                        // Restart copy element functionality immediately
                                        setTimeout(() => {
                                            const copyElementAction = new CopyElementAction();
                                            copyElementAction.execute().catch(error => {
                                                console.error('CopyElementAction: Error restarting after copy:', error);
                                            });
                                        }, 50); // Small delay to ensure cleanup is complete
                                    }, 1000); // Wait 1 second for notification visibility
                                }).catch(() => {
                                    fallbackCopy(content, msg);
                                });
                            } else {
                                fallbackCopy(content, msg);
                            }
                        }
                    } catch (error) {
                        console.error('Copy Element error:', error);
                        showNotification('❌ Copy failed', 'error');
                        // Don't cleanup on error - stay active for retry
                    }
                }

                function fallbackCopy(text, message) {
                    try {
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        textArea.style.position = 'fixed';
                        textArea.style.left = '-999999px';
                        textArea.style.top = '-999999px';
                        document.body.appendChild(textArea);
                        textArea.focus();
                        textArea.select();
                        
                        const successful = document.execCommand('copy');
                        textArea.remove();
                        
                        if (successful) {
                            showNotification(message);
                            // Wait 1 second for notification visibility before cleanup and restart
                            setTimeout(() => {
                                cleanup();
                                // Restart copy element functionality immediately
                                setTimeout(() => {
                                    const copyElementAction = new CopyElementAction();
                                    copyElementAction.execute().catch(error => {
                                        console.error('CopyElementAction: Error restarting after fallback copy:', error);
                                    });
                                }, 50); // Small delay to ensure cleanup is complete
                            }, 1000); // Wait 1 second for notification visibility
                        } else {
                            showNotification('❌ Copy failed', 'error');
                            // Don't cleanup on failure - stay active for retry
                        }
                    } catch (error) {
                        console.error('Fallback copy failed:', error);
                        showNotification('❌ Copy failed', 'error');
                        // Don't cleanup on error - stay active for retry
                    }
                }

                window.copyElementKeyDownHandler = function handleKeyDown(e) {
                    if (e.key === 'Escape') {
                        e.preventDefault();
                        console.log('CopyElementAction: Escape key pressed, performing full reset');
                        
                        // Call the full reset method for complete DOM restoration
                        const copyElementAction = new CopyElementAction();
                        copyElementAction.reset().then(() => {
                            console.log('CopyElementAction: Full reset completed via Escape key');
                        }).catch((error) => {
                            console.error('CopyElementAction: Error during Escape reset:', error);
                            // Fallback to local cleanup if reset fails
                            cleanup();
                        });
                    }
                };

                function cleanup() {
                    console.log('Starting thorough Copy Element cleanup');
                    
                    // Clear any pending timeouts
                    if (window.copyElementHoverTimeout) {
                        clearTimeout(window.copyElementHoverTimeout);
                        window.copyElementHoverTimeout = null;
                    }
                    
                    // Clear hover element reference and processing flag
                    window.copyElementCurrentHoverElement = null;
                    window.copyElementIsProcessing = false;
                    
                    // Remove event listeners using global references
                    document.body.removeEventListener('mouseover', window.copyElementMouseOverHandler);
                    document.body.removeEventListener('mouseout', window.copyElementMouseOutHandler);
                    document.body.removeEventListener('click', window.copyElementClickHandler);
                    document.removeEventListener('keydown', window.copyElementKeyDownHandler);

                    // Remove hover classes and restore all modified elements
                    document.querySelectorAll('.copy-element-hover, [data-copy-element-positioned]').forEach(el => {
                        el.classList.remove('copy-element-hover');
                        
                        // Restore original positioning if we changed it
                        if (el.getAttribute('data-copy-element-positioned') === 'true') {
                            el.style.position = '';
                            el.removeAttribute('data-copy-element-positioned');
                        }
                        
                        // Remove any other copy-element data attributes
                        Array.from(el.attributes).forEach(attr => {
                            if (attr.name.startsWith('data-copy-element')) {
                                el.removeAttribute(attr.name);
                            }
                        });
                    });

                    // Remove ALL copy element related elements
                    const selectorsToRemove = [
                        '.copy-element-notification',
                        '.copy-element-styles', 
                        '.copy-element-instructions',
                        '[data-copy-element-style]',
                        '[data-copy-element-instructions]',
                        '[data-copy-element-clean-title-reminder]',
                        '[data-copy-element-notification]',
                        '[data-copy-element-label]'
                    ];
                    
                    selectorsToRemove.forEach(selector => {
                        document.querySelectorAll(selector).forEach(el => {
                            el.remove();
                        });
                    });

                    // Additional cleanup: find any elements with class names containing "copy-element"
                    document.querySelectorAll('[class*="copy-element"]').forEach(el => {
                        // Remove copy-element classes specifically
                        const classList = Array.from(el.classList);
                        classList.forEach(className => {
                            if (className.includes('copy-element')) {
                                el.classList.remove(className);
                            }
                        });
                    });

                    // Clear ALL global variables and force garbage collection
                    window.copyElementCleanup = null;
                    window.copyElementMouseOverHandler = null;
                    window.copyElementMouseOutHandler = null;
                    window.copyElementClickHandler = null;
                    window.copyElementKeyDownHandler = null;
                    
                    // Clear active flag to allow fresh restart
                    window.copyElementActive = false;

                    // Ensure no copy-element related inline styles remain
                    document.querySelectorAll('*').forEach(el => {
                        if (el.style && el.style.cssText && el.style.cssText.includes('copy-element')) {
                            // Remove any inline styles that contain copy-element references
                            const styles = el.style.cssText.split(';');
                            const cleanStyles = styles.filter(style => !style.includes('copy-element'));
                            el.style.cssText = cleanStyles.join(';');
                        }
                    });

                    console.log('Thorough Copy Element cleanup completed');
                }

                // Store cleanup function globally for reset
                window.copyElementCleanup = cleanup;

                // Add event listeners using global references
                document.body.addEventListener('mouseover', window.copyElementMouseOverHandler);
                document.body.addEventListener('mouseout', window.copyElementMouseOutHandler);
                document.body.addEventListener('click', window.copyElementClickHandler);
                document.addEventListener('keydown', window.copyElementKeyDownHandler);

                console.log('Copy Element activated - hover and click elements to copy content');
                resolve({ success: true, message: 'Copy Element activated' });

            } catch (error) {
                console.error('Copy Element error:', error);
                window.copyElementActive = false; // Reset flag on error
                reject(error);
            }
        });
    }

    // Quick synchronous cleanup for faster reactivation
    quickCleanup() {
        try {
            // Clear timeouts
            if (window.copyElementHoverTimeout) {
                clearTimeout(window.copyElementHoverTimeout);
                window.copyElementHoverTimeout = null;
            }
            
            // Clear hover state
            window.copyElementCurrentHoverElement = null;
            window.copyElementIsProcessing = false;
            
            // Remove event listeners if they exist
            if (window.copyElementMouseOverHandler) {
                document.body.removeEventListener('mouseover', window.copyElementMouseOverHandler);
            }
            if (window.copyElementMouseOutHandler) {
                document.body.removeEventListener('mouseout', window.copyElementMouseOutHandler);
            }
            if (window.copyElementClickHandler) {
                document.body.removeEventListener('click', window.copyElementClickHandler);
            }
            if (window.copyElementKeyDownHandler) {
                document.removeEventListener('keydown', window.copyElementKeyDownHandler);
            }
            
            // Quick DOM cleanup - only essential elements
            const quickSelectors = [
                '.copy-element-notification',
                '.copy-element-styles',
                '.copy-element-instructions',
                '[data-copy-element-label]'
            ];
            
            quickSelectors.forEach(selector => {
                document.querySelectorAll(selector).forEach(el => el.remove());
            });
            
            // Remove hover classes without restoration (will be cleaned up properly later)
            document.querySelectorAll('.copy-element-hover').forEach(el => {
                el.classList.remove('copy-element-hover');
            });
            
            console.log('Quick cleanup completed');
        } catch (error) {
            console.warn('Quick cleanup error:', error);
        }
    }

    // Reset the copy element functionality (remove all modifications)
    reset() {
        return new Promise((resolve, reject) => {
            try {
                console.log('CopyElementAction: Starting comprehensive DOM reset');
                
                // Remove all event listeners first
                document.body.removeEventListener('mouseover', window.copyElementMouseOverHandler);
                document.body.removeEventListener('mouseout', window.copyElementMouseOutHandler);  
                document.body.removeEventListener('click', window.copyElementClickHandler);
                document.removeEventListener('keydown', window.copyElementKeyDownHandler);

                // Use global cleanup function if available
                if (window.copyElementCleanup) {
                    window.copyElementCleanup();
                }

                // Comprehensive cleanup - remove ALL copy element related elements
                const selectorsToRemove = [
                    '.copy-element-notification',
                    '.copy-element-styles', 
                    '.copy-element-instructions',
                    '[data-copy-element-style]',
                    '[data-copy-element-instructions]',
                    '[data-copy-element-clean-title-reminder]',
                    '[data-copy-element-notification]',
                    '[data-copy-element-label]'
                ];
                
                selectorsToRemove.forEach(selector => {
                    document.querySelectorAll(selector).forEach(el => {
                        console.log('CopyElementAction: Removing element:', selector, el);
                        el.remove();
                    });
                });

                // Remove ALL hover classes and restore positioning
                document.querySelectorAll('.copy-element-hover, [data-copy-element-positioned]').forEach(el => {
                    el.classList.remove('copy-element-hover');
                    
                    // Restore original positioning if we changed it
                    if (el.getAttribute('data-copy-element-positioned') === 'true') {
                        el.style.position = '';
                        el.removeAttribute('data-copy-element-positioned');
                    }
                    
                    // Remove any other copy-element data attributes
                    Array.from(el.attributes).forEach(attr => {
                        if (attr.name.startsWith('data-copy-element')) {
                            el.removeAttribute(attr.name);
                        }
                    });
                });

                // Clear all global variables related to copy element
                window.copyElementCleanup = null;
                window.copyElementMouseOverHandler = null;
                window.copyElementMouseOutHandler = null;
                window.copyElementClickHandler = null;
                window.copyElementKeyDownHandler = null;

                // Force garbage collection of any remaining references
                setTimeout(() => {
                    // Final cleanup pass - remove any elements that might have been missed
                    document.querySelectorAll('[class*="copy-element"], [data-copy-element]').forEach(el => {
                        console.log('CopyElementAction: Final cleanup removing:', el);
                        el.remove();
                    });
                }, 50);

                console.log('CopyElementAction: Comprehensive DOM reset completed');
                resolve({ success: true, message: 'Copy Element completely reset' });
            } catch (error) {
                console.error('CopyElementAction: Reset error:', error);
                reject(error);
            }
        });
    }

    // Static execute method for global access (used by context menu)
    static execute() {
        console.log('CopyElementAction: Static execute called');
        const action = new CopyElementAction();
        return action.execute();
    }

    // Static reset method for global access (used by reset utilities)
    static reset() {
        console.log('CopyElementAction: Static reset called');
        const action = new CopyElementAction();
        return action.reset();
    }
}

// Make available globally for context menu execution
if (typeof window !== 'undefined') {
    window.CopyElementAction = CopyElementAction;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CopyElementAction;
} else {
    window.CopyElementAction = CopyElementAction;
}

} // End of duplicate prevention check