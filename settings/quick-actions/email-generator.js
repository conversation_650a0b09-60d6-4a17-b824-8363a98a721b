// Email Generator Action - Generate email variations with dots
class EmailGeneratorAction {
    static execute() {
        try {
            console.log('Email Generator activated');
            
            // Get email input from user
            var email = prompt('Enter an email address:');
            if (email !== null && email.trim() !== '') {
                var emailParts = email.split('@');
                if (emailParts.length !== 2) {
                    alert('Invalid email address');
                    return;
                }
                
                var username = emailParts[0];
                var domain = emailParts[1];
                
                var variations = [];
                
                function generateVariations(username, index) {
                    if (index >= username.length - 1) {
                        variations.push(username + '@' + domain);
                        return;
                    }
                    username = username.slice(0, index) + '.' + username.slice(index);
                    generateVariations(username, index + 2);
                    username = username.slice(0, index) + username.slice(index + 1);
                    generateVariations(username, index + 1);
                }
                
                generateVariations(username, 1);
                
                // Remove duplicates
                variations = [...new Set(variations)];
                
                // Show results in popup instead of alert
                EmailGeneratorAction.showResultsPopup(variations, email);
            }
        } catch (error) {
            console.error('Email Generator error:', error);
            alert('Error generating email variations: ' + error.message);
        }
    }

    static showResultsPopup(variations, originalEmail) {
        // Remove any existing popup
        EmailGeneratorAction.reset();
        
        // Store original email for CSV export
        window.emailGeneratorOriginalEmail = originalEmail;
        
        // Create popup
        const popup = document.createElement('div');
        popup.className = 'email-generator-popup';
        popup.style.cssText = `
            position: fixed;
            background: #0a0a0a;
            border: 2px solid #7C3AED;
            border-radius: 12px;
            color: #d1d5db;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto";
            font-size: 14px;
            padding: 20px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2147483647;
            max-width: 500px;
            min-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            cursor: move;
        `;

        // Create header
        const header = document.createElement('div');
        header.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #374151;
        `;

        const title = document.createElement('h3');
        title.textContent = 'Email Generator Results';
        title.style.cssText = `
            margin: 0;
            color: #7C3AED;
            font-size: 16px;
            font-weight: 600;
        `;

        const closeBtn = document.createElement('button');
        closeBtn.textContent = '×';
        closeBtn.style.cssText = `
            background: none;
            border: none;
            color: #9ca3af;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        closeBtn.onclick = () => EmailGeneratorAction.reset();

        header.appendChild(title);
        header.appendChild(closeBtn);

        // Create variations display
        const variationsContainer = document.createElement('div');
        variationsContainer.style.cssText = `
            margin-bottom: 20px;
            max-height: 300px;
            overflow-y: auto;
            background: #111827;
            border: 1px solid #374151;
            border-radius: 8px;
            padding: 10px;
        `;

        const variationsList = document.createElement('div');
        variations.forEach(variation => {
            const item = document.createElement('div');
            item.textContent = variation;
            item.style.cssText = `
                padding: 5px 0;
                border-bottom: 1px solid #374151;
                font-family: monospace;
                font-size: 13px;
            `;
            variationsList.appendChild(item);
        });

        variationsContainer.appendChild(variationsList);

        // Create buttons container
        const buttonsContainer = document.createElement('div');
        buttonsContainer.style.cssText = `
            display: flex;
            gap: 10px;
            justify-content: center;
        `;

        // Copy button
        const copyBtn = document.createElement('button');
        copyBtn.textContent = 'Click to Copy All';
        copyBtn.style.cssText = `
            background: #7C3AED;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.2s;
        `;
        copyBtn.onmouseover = () => copyBtn.style.background = '#6D28D9';
        copyBtn.onmouseout = () => copyBtn.style.background = '#7C3AED';
        copyBtn.onclick = () => EmailGeneratorAction.copyToClipboard(variations);

        // CSV Export button
        const csvBtn = document.createElement('button');
        csvBtn.textContent = 'Export to CSV';
        csvBtn.style.cssText = `
            background: #059669;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.2s;
        `;
        csvBtn.onmouseover = () => csvBtn.style.background = '#047857';
        csvBtn.onmouseout = () => csvBtn.style.background = '#059669';
        csvBtn.onclick = () => EmailGeneratorAction.exportToCSV(variations);

        buttonsContainer.appendChild(copyBtn);
        buttonsContainer.appendChild(csvBtn);

        // Assemble popup
        popup.appendChild(header);
        popup.appendChild(variationsContainer);
        popup.appendChild(buttonsContainer);

        // Add to page
        document.body.appendChild(popup);

        // Make draggable
        EmailGeneratorAction.makeDraggable(popup, header);

        // Add ESC key listener
        const escapeListener = (e) => {
            if (e.key === 'Escape') {
                EmailGeneratorAction.reset();
            }
        };
        document.addEventListener('keydown', escapeListener);
        window.emailGeneratorEscapeListener = escapeListener;

        console.log(`Email Generator: Generated ${variations.length} variations`);
    }

    static copyToClipboard(variations) {
        const text = variations.join('\n');
        navigator.clipboard.writeText(text).then(() => {
            // Show success feedback
            const copyBtn = document.querySelector('.email-generator-popup button');
            if (copyBtn) {
                const originalText = copyBtn.textContent;
                copyBtn.textContent = 'Copied!';
                copyBtn.style.background = '#059669';
                setTimeout(() => {
                    copyBtn.textContent = originalText;
                    copyBtn.style.background = '#7C3AED';
                }, 1500);
            }
        }).catch(error => {
            console.error('Failed to copy to clipboard:', error);
            alert('Failed to copy to clipboard');
        });
    }

    static exportToCSV(variations) {
        // Get the original email for filename
        const originalEmail = window.emailGeneratorOriginalEmail || 'unknown';
        
        // Sanitize email for filename (keep alphanumeric, dots, hyphens, and @)
        const sanitizedEmail = originalEmail.replace(/[^a-zA-Z0-9@.-]/g, '');
        
        // Create dynamic filename
        const filename = `${sanitizedEmail}-email-variations.csv`;
        
        const csvContent = 'Email Variations\n' + variations.join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        console.log(`Email variations exported to CSV: ${filename}`);
    }

    static makeDraggable(element, handle) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        
        handle.onmousedown = dragMouseDown;
        
        function dragMouseDown(e) {
            e = e || window.event;
            e.preventDefault();
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;
        }
        
        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            element.style.top = (element.offsetTop - pos2) + "px";
            element.style.left = (element.offsetLeft - pos1) + "px";
        }
        
        function closeDragElement() {
            document.onmouseup = null;
            document.onmousemove = null;
        }
    }

    static reset() {
        try {
            // Remove popup
            const popup = document.querySelector('.email-generator-popup');
            if (popup) {
                popup.remove();
            }

            // Remove ESC listener
            if (window.emailGeneratorEscapeListener) {
                document.removeEventListener('keydown', window.emailGeneratorEscapeListener);
                delete window.emailGeneratorEscapeListener;
            }

            // Clean up stored original email
            if (window.emailGeneratorOriginalEmail) {
                delete window.emailGeneratorOriginalEmail;
            }

            console.log('Email Generator reset completed');
        } catch (error) {
            console.error('Email Generator reset error:', error);
        }
    }

    // Instance methods for popup/shortcuts usage
    execute() {
        return new Promise((resolve, reject) => {
            try {
                console.log('Email Generator activated (instance method)');
                
                if (typeof chrome !== 'undefined' && chrome.tabs) {
                    // We're in popup context, need to execute on the current tab
                    chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                        if (tabs[0]) {
                            chrome.scripting.executeScript({
                                target: {tabId: tabs[0].id},
                                files: ['settings/quick-actions/email-generator.js']
                            }, () => {
                                if (chrome.runtime.lastError) {
                                    console.error('Error injecting Email Generator script:', chrome.runtime.lastError);
                                    reject(chrome.runtime.lastError);
                                    return;
                                }
                                
                                chrome.scripting.executeScript({
                                    target: {tabId: tabs[0].id},
                                    func: function() {
                                        if (typeof EmailGeneratorAction !== 'undefined') {
                                            return EmailGeneratorAction.execute();
                                        } else {
                                            return { error: 'EmailGeneratorAction not found after injection' };
                                        }
                                    }
                                }, (result) => {
                                    if (chrome.runtime.lastError) {
                                        console.error('Error executing Email Generator script:', chrome.runtime.lastError);
                                        reject(chrome.runtime.lastError);
                                    } else {
                                        console.log('Email Generator script executed successfully:', result);
                                        resolve(result);
                                    }
                                });
                            });
                        } else {
                            reject(new Error('No active tab found'));
                        }
                    });
                } else {
                    // We're in page context, execute directly
                    const result = EmailGeneratorAction.execute();
                    resolve(result);
                }
            } catch (error) {
                console.error('Error in Email Generator execute:', error);
                reject(error);
            }
        });
    }

    reset() {
        return new Promise((resolve, reject) => {
            try {
                console.log('Email Generator reset (instance method)');
                
                if (typeof chrome !== 'undefined' && chrome.tabs) {
                    // We're in popup context, need to execute on the current tab
                    chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                        if (tabs[0]) {
                            chrome.scripting.executeScript({
                                target: {tabId: tabs[0].id},
                                files: ['settings/quick-actions/email-generator.js']
                            }, () => {
                                if (chrome.runtime.lastError) {
                                    console.error('Error injecting Email Generator script for reset:', chrome.runtime.lastError);
                                    reject(chrome.runtime.lastError);
                                    return;
                                }
                                
                                chrome.scripting.executeScript({
                                    target: {tabId: tabs[0].id},
                                    func: function() {
                                        if (typeof EmailGeneratorAction !== 'undefined') {
                                            return EmailGeneratorAction.reset();
                                        } else {
                                            return { error: 'EmailGeneratorAction not found after injection' };
                                        }
                                    }
                                }, (result) => {
                                    if (chrome.runtime.lastError) {
                                        console.error('Error resetting Email Generator script:', chrome.runtime.lastError);
                                        reject(chrome.runtime.lastError);
                                    } else {
                                        console.log('Email Generator reset executed successfully:', result);
                                        resolve(result);
                                    }
                                });
                            });
                        } else {
                            reject(new Error('No active tab found'));
                        }
                    });
                } else {
                    // We're in page context, execute directly
                    const result = EmailGeneratorAction.reset();
                    resolve(result);
                }
            } catch (error) {
                console.error('Error in Email Generator reset:', error);
                reject(error);
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EmailGeneratorAction;
} else {
    window.EmailGeneratorAction = EmailGeneratorAction;
}