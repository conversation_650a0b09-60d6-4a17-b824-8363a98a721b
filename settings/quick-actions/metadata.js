// Metadata Action - Comprehensive metadata analyzer for web pages
class MetadataAction {
    static execute() {
        try {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                    target: {tabId: tabs[0].id},
                    func: function() {
                        // Metadata analyzer functionality
                        (function() {
                            // Remove existing metadata panel if present
                            document.querySelectorAll('.metadata-audit-panel').forEach(panel => panel.remove());
                            
                            // Helper functions
                            function escape(str) {
                                if (!str) return '';
                                return str.replace(/&/g, '&amp;')
                                         .replace(/</g, '&lt;')
                                         .replace(/>/g, '&gt;')
                                         .replace(/"/g, '&quot;')
                                         .replace(/'/g, '&#39;');
                            }
                            
                            function hlCode(str) {
                                return '<code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + str + '</code>';
                            }
                            
                            function words(str, delimiter = ' ') {
                                if (!str) return 0;
                                return str.split(delimiter).filter(word => word.trim().length > 0).length;
                            }
                            
                            function getCharset() {
                                const charset = document.characterSet || document.charset;
                                return charset || 'UTF-8';
                            }
                            
                            function getDomain(url) {
                                try {
                                    return new URL(url).hostname;
                                } catch {
                                    return '';
                                }
                            }
                            
                            function getLocation() {
                                return decodeURIComponent(document.location).split('#')[0];
                            }
                            
                            function createRow(title, info, content, copyValue) {
                                const escapedCopyValue = copyValue ? escape(copyValue).replace(/'/g, "\\'") : '';
                                return `
                                    <div style="display:flex;padding:16px 0;border-bottom:1px solid #2a2a2a;${copyValue ? 'cursor:pointer;' : ''}" ${copyValue ? `onclick="navigator.clipboard.writeText('${escapedCopyValue}').then(() => { this.style.backgroundColor='#2a2a2a'; setTimeout(() => this.style.backgroundColor='', 800); })"` : ''}>
                                        <div style="min-width:140px;font-weight:500;color:#9ca3af;font-size:13px;text-transform:uppercase;margin-right:20px;">${title}</div>
                                        <div style="flex:1;">
                                            ${info ? `<div style="font-size:12px;color:#6b7280;margin-bottom:8px;">${info}</div>` : ''}
                                            <div style="color:#d1d5db;line-height:1.5;">${content}</div>
                                        </div>
                                    </div>
                                `;
                            }
                            
                            // Collect metadata
                            const url = getLocation();
                            const lang = document.documentElement.lang;
                            const charset = getCharset();
                            
                            // Basic metadata
                            let titleTag = '';
                            let titleCount = 0;
                            let metaDescription = '';
                            let descriptionCount = 0;
                            let canonical = '';
                            let canonicalFlag = '';
                            let metaRobots = '';
                            let metaRobotsFlag = '';
                            
                            try {
                                titleTag = document.title;
                                titleCount = document.title.length;
                            } catch(e) {
                                console.log("Error finding title");
                            }
                            
                            try {
                                const descMeta = document.querySelector("meta[name='description']");
                                if (descMeta) {
                                    metaDescription = descMeta.content;
                                    descriptionCount = descMeta.content.length;
                                }
                            } catch(e) {
                                console.log("Error finding Meta Description");
                            }
                            
                            try {
                                const canonicalEl = document.querySelector("link[rel='canonical']");
                                if (canonicalEl) {
                                    canonical = canonicalEl.href.toLowerCase();
                                    canonicalFlag = canonical === window.location.href.toLowerCase() ? "Pass" : "Fail";
                                }
                            } catch(e) {
                                console.log("Error finding Canonical");
                            }
                            
                            try {
                                const robotsMeta = document.querySelector("meta[name='robots']");
                                if (robotsMeta) {
                                    metaRobots = robotsMeta.content.toLowerCase();
                                    metaRobotsFlag = (metaRobots.includes("noindex") || metaRobots.includes("nofollow")) ? "Fail" : "Pass";
                                }
                            } catch(e) {
                                console.log("Error finding Meta Robots");
                            }
                            
                            // Get saved position and size from localStorage
                            function getSavedPanelSettings() {
                                try {
                                    const saved = localStorage.getItem('metadata-audit-panel-settings');
                                    if (saved) {
                                        return JSON.parse(saved);
                                    }
                                } catch (e) {
                                    console.log('Error loading saved panel settings:', e);
                                }
                                // Default settings
                                return {
                                    top: '20px',
                                    left: '20px', 
                                    width: '50%',
                                    height: '85vh'
                                };
                            }
                            
                            // Save panel position and size to localStorage
                            function savePanelSettings(panel) {
                                try {
                                    const rect = panel.getBoundingClientRect();
                                    const settings = {
                                        top: panel.style.top || '20px',
                                        left: panel.style.left || '20px',
                                        right: panel.style.right || '',
                                        width: panel.style.width || '50%',
                                        height: panel.style.height || '85vh'
                                    };
                                    localStorage.setItem('metadata-audit-panel-settings', JSON.stringify(settings));
                                } catch (e) {
                                    console.log('Error saving panel settings:', e);
                                }
                            }

                            // Create panel
                            var panel = document.createElement('div');
                            panel.className = 'metadata-audit-panel';
                            
                            // Load saved settings
                            const savedSettings = getSavedPanelSettings();
                            const positionStyle = savedSettings.right ? 
                                `right:${savedSettings.right};` : 
                                `left:${savedSettings.left};`;
                            
                            panel.style.cssText = `position:fixed;top:${savedSettings.top};${positionStyle}width:${savedSettings.width};max-height:${savedSettings.height};z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;overflow:auto;resize:both;min-width:400px;min-height:500px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5`;
                            
                            let html = `
                                <div id="metadata-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;">
                                    <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;">Metadata Analyzer</h2>
                                    <button onclick="this.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
                                </div>
                            `;
                            
                            // Basic Metadata Section
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Basic Metadata</div>';
                            
                            // Title
                            const titleStatus = titleCount >= 30 && titleCount <= 60 ? 'Pass' : 'Fail';
                            html += '<div style="padding:0 20px;">';
                            html += createRow(
                                'Title',
                                `${titleCount} chars, ${words(titleTag)} words · <span style="color:${titleStatus === 'Pass' ? '#10b981' : '#f59e0b'};font-weight:500;">${titleStatus}</span>`,
                                titleTag ? hlCode(escape(titleTag)) : '<span style="color:#6b7280;">No Title Found</span>',
                                titleTag
                            );
                            
                            // Description
                            const descStatus = descriptionCount >= 120 && descriptionCount <= 160 ? 'Pass' : 'Fail';
                            html += createRow(
                                'Description',
                                `${descriptionCount} chars, ${words(metaDescription)} words · <span style="color:${descStatus === 'Pass' ? '#10b981' : '#f59e0b'};font-weight:500;">${descStatus}</span>`,
                                metaDescription ? hlCode(escape(metaDescription)) : '<span style="color:#6b7280;">No Meta Description Found</span>',
                                metaDescription
                            );
                            
                            // Robots
                            html += createRow(
                                'Robots',
                                `<span style="color:${metaRobotsFlag === 'Pass' ? '#10b981' : '#f59e0b'};font-weight:500;">${metaRobotsFlag === 'Pass' ? 'Indexable' : 'Non-Indexable'}</span>`,
                                metaRobots ? hlCode(escape(metaRobots).replace('noindex', '<span style="color:#f59e0b;">noindex</span>')) : '<span style="color:#6b7280;">No Meta Robots Found</span>',
                                metaRobots
                            );
                            
                            // URL & Canonical
                            let urlContent = `<a href="${escape(url)}" target="_blank" style="color:#e5e7eb;text-decoration:none;">${escape(url)}</a>`;
                            if (canonical) {
                                urlContent += `<div style="margin-top:8px;"><span style="color:#6b7280;">Canonical: </span><span style="color:${canonicalFlag === 'Pass' ? '#10b981' : '#f59e0b'};"><a href="${escape(canonical)}" target="_blank" style="color:inherit;text-decoration:none;">${escape(canonical)}</a></span></div>`;
                            } else {
                                urlContent += `<div style="margin-top:8px;"><span style="color:#6b7280;">Canonical: </span><span style="color:#f59e0b;">Not found</span></div>`;
                            }
                            html += createRow('URL', `${url.length} chars`, urlContent, url);
                            html += '</div>';
                            
                            html += '</div>';
                            
                            // Other Meta Tags Section
                            const excludeNames = new Set(['description', 'keywords', 'robots']);
                            const otherMetas = Array.from(document.querySelectorAll('meta[name]'))
                                .filter(meta => {
                                    const name = meta.getAttribute('name');
                                    return !excludeNames.has(name) && !name.startsWith('twitter:');
                                })
                                .sort((a, b) => {
                                    const nameA = a.getAttribute('name').toLowerCase();
                                    const nameB = b.getAttribute('name').toLowerCase();
                                    return nameA.localeCompare(nameB);
                                });
                            
                            // Keywords
                            const keywordsMetas = document.querySelectorAll('meta[name="keywords"]');
                            
                            if (keywordsMetas.length > 0 || otherMetas.length > 0 || lang || charset) {
                                html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                                html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Other Meta Tags</div>';
                                html += '<div style="padding:0 20px;">';
                                
                                // Keywords
                                keywordsMetas.forEach((element, ind) => {
                                    const title = 'Keywords' + (ind ? ` #${ind+1}` : '');
                                    const value = element.content;
                                    html += createRow(title, `${words(value, ',')} keywords, ${value.length} chars`, hlCode(escape(value)), value);
                                });
                                
                                // Language
                                if (lang) {
                                    html += createRow('Language', '', hlCode(escape(lang)), lang);
                                }
                                
                                // Charset
                                if (charset) {
                                    html += createRow('Charset', '', hlCode(escape(charset)), charset);
                                }
                                
                                // Other metas
                                otherMetas.forEach((item) => {
                                    const name = item.getAttribute('name').toLowerCase();
                                    const content = item.getAttribute('content') ?? '';
                                    let isLink = false;
                                    
                                    try {
                                        new URL(content);
                                        if (/^https?:\/\//.test(content)) isLink = true;
                                    } catch (error) {}
                                    
                                    const displayContent = isLink 
                                        ? `<a href="${escape(content)}" target="_blank" rel="noopener noreferrer" style="color:#e5e7eb;text-decoration:none;">${escape(content)}</a>` 
                                        : hlCode(escape(content));
                                    
                                    const formattedName = name.charAt(0).toUpperCase() + name.slice(1).replace(/-/g, ' ');
                                    html += createRow(formattedName, '', displayContent, content);
                                });
                                
                                html += '</div>';
                                html += '</div>';
                            }
                            
                            // Social Media Tags
                            const ogMetas = document.querySelectorAll('meta[property^="og:"]');
                            const twMetas = document.querySelectorAll('meta[name^="twitter:"]');
                            
                            if (ogMetas.length > 0 || twMetas.length > 0) {
                                html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                                html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Social Media Tags</div>';
                                
                                let og = {};
                                
                                // Open Graph
                                if (ogMetas.length > 0) {
                                    html += '<div style="padding:16px 20px 0;"><div style="font-weight:500;color:#6b7280;margin-bottom:12px;font-size:13px;">Open Graph <span style="font-size:11px;font-weight:normal;">[<a href="https://ogp.me/" target="_blank" style="color:#e5e7eb;text-decoration:none;">docs</a>]</span></div></div>';
                                    
                                    const ogData = {};
                                    ogMetas.forEach(meta => {
                                        ogData[meta.getAttribute('property').replace(/^og:/g, '')] = meta.getAttribute('content');
                                    });
                                    
                                    og.image = ogData.image;
                                    og.url = ogData.url;
                                    og.title = ogData.title;
                                    og.description = ogData.description;
                                    
                                    html += '<div style="padding:0 20px;">';
                                    ogMetas.forEach(meta => {
                                        const value = meta.getAttribute('content');
                                        const name = meta.getAttribute('property');
                                        let isLink = false;
                                        
                                        try {
                                            new URL(value);
                                            if (/^https?:\/\//.test(value)) isLink = true;
                                        } catch (error) {}
                                        
                                        const displayContent = isLink 
                                            ? `<a href="${escape(value)}" target="_blank" rel="noopener noreferrer" style="color:#e5e7eb;text-decoration:none;">${escape(value)}</a>` 
                                            : hlCode(escape(value));
                                        
                                        const formattedName = name.replace('og:', '').charAt(0).toUpperCase() + name.replace('og:', '').slice(1);
                                        html += createRow(formattedName, '', displayContent, value);
                                    });
                                    html += '</div>';
                                    
                                    // OG Preview
                                    const ogImage = ogData.image || ogData['image:url'] || ogData['image:secure_url'];
                                    const ogUrl = ogData.url || getLocation();
                                    const ogHost = ogData.site_name || getDomain(ogUrl);
                                    const ogTitle = ogData.title || document.title;
                                    const ogDesc = ogData.description || '';
                                    
                                    if (ogImage || ogTitle || ogDesc) {
                                        html += '<div style="padding:0 20px;">';
                                        html += createRow('Preview', '', `
                                            <div style="border:1px solid #2a2a2a;border-radius:6px;overflow:hidden;max-width:400px;background:#1a1a1a;">
                                                ${ogImage ? `<div><img style="width:100%;height:auto;max-height:180px;object-fit:cover;" src="${ogImage}"></div>` : ''}
                                                <div style="padding:16px;">
                                                    ${ogHost ? `<div style="color:#6b7280;font-size:11px;margin-bottom:6px;">${escape(ogHost)}</div>` : ''}
                                                    ${ogTitle ? `<div style="font-weight:500;margin-bottom:6px;color:#d1d5db;">${escape(ogTitle)}</div>` : ''}
                                                    ${ogDesc ? `<div style="color:#9ca3af;font-size:12px;line-height:1.4;">${escape(ogDesc)}</div>` : ''}
                                                </div>
                                            </div>
                                        `);
                                        html += '</div>';
                                    }
                                }
                                
                                // Twitter Cards
                                if (twMetas.length > 0) {
                                    html += '<div style="padding:16px 20px 0;margin-top:20px;"><div style="font-weight:500;color:#6b7280;margin-bottom:12px;font-size:13px;">Twitter Cards <span style="font-size:11px;font-weight:normal;">[<a href="https://developer.x.com/en/docs/x-for-websites/cards/guides/getting-started#started" target="_blank" style="color:#e5e7eb;text-decoration:none;">docs</a>]</span></div></div>';
                                    
                                    const twData = {};
                                    twMetas.forEach(meta => {
                                        twData[meta.getAttribute('name').replace(/^twitter:/g, '')] = meta.getAttribute('content');
                                    });
                                    
                                    html += '<div style="padding:0 20px;">';
                                    twMetas.forEach(meta => {
                                        const value = meta.getAttribute('content');
                                        const name = meta.getAttribute('name');
                                        let isLink = false;
                                        
                                        try {
                                            new URL(value);
                                            if (/^https?:\/\//.test(value)) isLink = true;
                                        } catch (error) {}
                                        
                                        const displayContent = isLink 
                                            ? `<a href="${escape(value)}" target="_blank" rel="noopener noreferrer" style="color:#e5e7eb;text-decoration:none;">${escape(value)}</a>` 
                                            : hlCode(escape(value));
                                        
                                        const formattedName = name.replace('twitter:', '').charAt(0).toUpperCase() + name.replace('twitter:', '').slice(1);
                                        html += createRow(formattedName, '', displayContent, value);
                                    });
                                    html += '</div>';
                                    
                                    // Twitter Preview
                                    const twImage = twData.image || twData['image:url'] || twData['image:secure_url'] || og.image;
                                    const twUrl = twData.url || og.url || getLocation();
                                    const twHost = twData.site || getDomain(twUrl);
                                    const twTitle = twData.title || og.title || document.title;
                                    const twDesc = twData.description || og.description || '';
                                    
                                    if (twImage || twTitle || twDesc) {
                                        html += '<div style="padding:0 20px;">';
                                        html += createRow('Preview', '', `
                                            <div style="border:1px solid #2a2a2a;border-radius:6px;overflow:hidden;max-width:400px;background:#000000;">
                                                ${twImage ? `<div><img style="width:100%;height:auto;max-height:180px;object-fit:cover;" src="${twImage}"></div>` : ''}
                                                <div style="padding:16px;">
                                                    ${twTitle ? `<div style="font-weight:500;margin-bottom:6px;color:#ffffff;">${escape(twTitle)}</div>` : ''}
                                                    ${twDesc ? `<div style="color:#6b7280;font-size:12px;margin-bottom:6px;line-height:1.4;">${escape(twDesc)}</div>` : ''}
                                                    ${twHost ? `<div style="color:#6b7280;font-size:11px;">${escape(twHost)}</div>` : ''}
                                                </div>
                                            </div>
                                        `);
                                        html += '</div>';
                                    }
                                }
                                
                                html += '</div>';
                            } else {
                                // No social tags found
                                html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                                html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Social Media Tags</div>';
                                html += '<div style="padding:20px;">';
                                html += '<div style="background:#1a1a1a;border-radius:6px;padding:16px;color:#9ca3af;font-size:13px;">';
                                html += '<div style="margin-bottom:12px;color:#d1d5db;font-weight:500;">No social media tags found</div>';
                                html += '<div style="color:#6b7280;line-height:1.5;margin-bottom:12px;">Consider adding Open Graph and Twitter Card tags for better social media sharing.</div>';
                                html += '<div style="color:#6b7280;line-height:1.5;">Learn more:</div>';
                                html += '<ul style="margin:8px 0 0 0;padding-left:18px;color:#6b7280;">';
                                html += '<li style="margin-bottom:4px;"><a href="https://ogp.me" target="_blank" style="color:#e5e7eb;text-decoration:none;">Open Graph Protocol</a></li>';
                                html += '<li style="margin-bottom:4px;"><a href="https://developer.x.com/en/docs/x-for-websites/cards/overview/abouts-cards" target="_blank" style="color:#e5e7eb;text-decoration:none;">Twitter Card Documentation</a></li>';
                                html += '<li style="margin-bottom:0;"><a href="https://metatags.io" target="_blank" style="color:#e5e7eb;text-decoration:none;">Social Meta Tags Generator</a></li>';
                                html += '</ul>';
                                html += '</div>';
                                html += '</div>';
                                html += '</div>';
                            }
                            
                            // Info note
                            html += '<div style="background:#1a1a1a;border-radius:6px;border:1px solid #2a2a2a;padding:16px;text-align:center;color:#6b7280;font-size:12px;">Click any row to copy its value to clipboard</div>';
                            
                            panel.innerHTML = html;
                            document.body.appendChild(panel);
                            
                            // Handle Escape key to close panel
                            function handleKeyDown(e) {
                                if (e.key === 'Escape') {
                                    panel.remove();
                                    document.removeEventListener('keydown', handleKeyDown);
                                }
                            }
                            document.addEventListener('keydown', handleKeyDown);
                            
                            // Add drag functionality
                            var isDragging = false;
                            var currentX;
                            var currentY;
                            var initialX;
                            var initialY;
                            var xOffset = 0;
                            var yOffset = 0;
                            
                            var header = panel.querySelector('#metadata-header');
                            
                            function dragStart(e) {
                                if (e.target.tagName === 'BUTTON') return;
                                
                                if (e.type === "touchstart") {
                                    initialX = e.touches[0].clientX - xOffset;
                                    initialY = e.touches[0].clientY - yOffset;
                                } else {
                                    initialX = e.clientX - xOffset;
                                    initialY = e.clientY - yOffset;
                                }
                                
                                if (e.target === header || header.contains(e.target)) {
                                    isDragging = true;
                                    panel.style.cursor = 'grabbing';
                                    header.style.cursor = 'grabbing';
                                }
                            }
                            
                            function dragEnd(e) {
                                initialX = currentX;
                                initialY = currentY;
                                isDragging = false;
                                panel.style.cursor = 'default';
                                header.style.cursor = 'move';
                                // Save position when drag ends
                                savePanelSettings(panel);
                            }
                            
                            function drag(e) {
                                if (isDragging) {
                                    e.preventDefault();
                                    
                                    if (e.type === "touchmove") {
                                        currentX = e.touches[0].clientX - initialX;
                                        currentY = e.touches[0].clientY - initialY;
                                    } else {
                                        currentX = e.clientX - initialX;
                                        currentY = e.clientY - initialY;
                                    }
                                    
                                    xOffset = currentX;
                                    yOffset = currentY;
                                    
                                    var rect = panel.getBoundingClientRect();
                                    var maxX = window.innerWidth - rect.width;
                                    var maxY = window.innerHeight - rect.height;
                                    
                                    currentX = Math.max(0, Math.min(currentX, maxX));
                                    currentY = Math.max(0, Math.min(currentY, maxY));
                                    
                                    // Clear any existing positioning
                                    panel.style.right = '';
                                    panel.style.left = currentX + 'px';
                                    panel.style.top = currentY + 'px';
                                }
                            }
                            
                            header.addEventListener('mousedown', dragStart);
                            document.addEventListener('mousemove', drag);
                            document.addEventListener('mouseup', dragEnd);
                            header.addEventListener('touchstart', dragStart);
                            document.addEventListener('touchmove', drag);
                            document.addEventListener('touchend', dragEnd);
                            
                            // Add resize observer to save size changes
                            if (window.ResizeObserver) {
                                const resizeObserver = new ResizeObserver(function(entries) {
                                    // Save settings when panel is resized
                                    savePanelSettings(panel);
                                });
                                resizeObserver.observe(panel);
                            }
                            
                            var rect = panel.getBoundingClientRect();
                            xOffset = rect.left;
                            yOffset = rect.top;
                        })();
                    }
                });
            });
        } catch (error) {
            console.error('Metadata error:', error);
        }
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                function resetMetadata() {
                    const existingPanel = document.querySelector('.metadata-audit-panel');
                    if (existingPanel) {
                        existingPanel.remove();
                        console.log('Metadata audit panel removed');
                    }
                    console.log('Metadata reset completed');
                }

                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetMetadata
                    }, () => {
                        resolve();
                    });
                });
            } catch (error) {
                console.error('Metadata reset error:', error);
                resolve();
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MetadataAction;
} else {
    window.MetadataAction = MetadataAction;
} 