// Namespace protection - prevent multiple declarations
if (typeof BulkLinkOpenAction !== 'undefined') {
    console.log('BulkLinkOpenAction already declared, skipping redeclaration');
} else {

// Bulk Link Open Action - Allows pasting, editing, and opening multiple links with lazy loading
class BulkLinkOpenAction {
    static execute() {
        try {
            // This runs directly in the content script context
            // Bulk Link Open functionality
            (async function() {
                            // Remove existing panel if present
                            document.querySelectorAll('.bulk-link-open-panel').forEach(panel => panel.remove());
                            
                            // Helper function to validate URL
                            function isValidUrl(string) {
                                try {
                                    // If no protocol, add http://
                                    if (!string.match(/^https?:\/\//)) {
                                        string = 'http://' + string;
                                    }
                                    new URL(string);
                                    return true;
                                } catch (_) {
                                    return false;
                                }
                            }
                            
                            // Helper function to normalize URL
                            function normalizeUrl(url) {
                                if (!url.match(/^https?:\/\//)) {
                                    return 'http://' + url;
                                }
                                return url;
                            }
                            
                            // Saved Lists Management System
                            async function loadSavedLists() {
                                try {
                                    const result = await chrome.storage.local.get(['bulkLinkOpenSavedLists']);
                                    return result.bulkLinkOpenSavedLists || {};
                                } catch (error) {
                                    console.error('Error loading saved lists:', error);
                                    return {};
                                }
                            }
                            
                            async function saveLists(listsData) {
                                try {
                                    await chrome.storage.local.set({ bulkLinkOpenSavedLists: listsData });
                                } catch (error) {
                                    console.error('Error saving lists:', error);
                                }
                            }
                            
                            async function loadCurrentSessionLinks() {
                                try {
                                    const result = await chrome.storage.local.get(['bulkLinkOpenCurrentSession']);
                                    return result.bulkLinkOpenCurrentSession || '';
                                } catch (error) {
                                    console.error('Error loading current session:', error);
                                    return '';
                                }
                            }
                            
                            async function saveCurrentSessionLinks(linksText) {
                                try {
                                    await chrome.storage.local.set({ bulkLinkOpenCurrentSession: linksText });
                                } catch (error) {
                                    console.error('Error saving current session:', error);
                                }
                            }
                            
                            // Load saved opening settings
                            async function loadOpeningSettings() {
                                try {
                                    const result = await chrome.storage.local.get(['bulkLinkOpenSettings']);
                                    return result.bulkLinkOpenSettings || {
                                        delay: 0.5,
                                        batchSize: 3,
                                        openInNewWindow: false
                                    };
                                } catch (error) {
                                    console.error('Error loading opening settings:', error);
                                    return { delay: 0.5, batchSize: 3, openInNewWindow: false };
                                }
                            }
                            
                            async function saveOpeningSettings(settings) {
                                try {
                                    await chrome.storage.local.set({ bulkLinkOpenSettings: settings });
                                } catch (error) {
                                    console.error('Error saving opening settings:', error);
                                }
                            }

                            
                            // Load saved window state
                            async function loadWindowState() {
                                try {
                                    const result = await chrome.storage.local.get(['bulkLinkOpenWindowState']);
                                    return result.bulkLinkOpenWindowState || {
                                        x: 20,
                                        y: 20,
                                        width: 600,
                                        height: 400
                                    };
                                } catch (error) {
                                    console.error('Error loading window state:', error);
                                    return { x: 20, y: 20, width: 600, height: 400 };
                                }
                            }
                            
                            async function saveWindowState(state) {
                                try {
                                    await chrome.storage.local.set({ bulkLinkOpenWindowState: state });
                                } catch (error) {
                                    console.error('Error saving window state:', error);
                                }
                            }
                            
                            // Debounced save function to prevent excessive storage writes
                            let saveWindowStateTimeout;
                            function debouncedSaveWindowState(state) {
                                clearTimeout(saveWindowStateTimeout);
                                saveWindowStateTimeout = setTimeout(() => {
                                    const validatedState = validateWindowState(state);
                                    saveWindowState(validatedState);
                                }, 500); // Wait 500ms after last change
                            }

                            
                            // Validate window position is within screen bounds
                            function validateWindowState(state) {
                                const screenWidth = window.screen.availWidth || window.innerWidth;
                                const screenHeight = window.screen.availHeight || window.innerHeight;
                                
                                // Ensure window is at least partially visible
                                const minVisibleSize = 100;
                                const maxX = screenWidth - minVisibleSize;
                                const maxY = screenHeight - minVisibleSize;
                                
                                return {
                                    x: Math.max(0, Math.min(state.x, maxX)),
                                    y: Math.max(0, Math.min(state.y, maxY)),
                                    width: Math.max(500, Math.min(state.width, screenWidth)),
                                    height: Math.max(400, Math.min(state.height, screenHeight))
                                };
                            }
                            
                            // Open links with lazy loading and configurable settings
                            async function openLinksWithLazyLoading(links) {
                                const settings = await loadOpeningSettings();
                                console.log('Bulk Link Open: Opening ' + links.length + ' links with lazy loading');
                                
                                // Show progress for lazy loading
                                showProgress();
                                updateProgress(0, links.length, 'Creating lazy loading tabs...');
                                
                                if (window.chrome && chrome.runtime) {
                                    try {
                                        // Send message to background script to create lazy loading tabs
                                        chrome.runtime.sendMessage({
                                            action: 'createDragSelectTabs',
                                            urls: links.map(link => normalizeUrl(link))
                                        }, (response) => {
                                            if (chrome.runtime.lastError || !response || !response.success) {
                                                console.log('Bulk Link Open: Background script not available, using fallback');
                                                openLinksWithFallback(links);
                                            } else {
                                                console.log('Bulk Link Open: Successfully created ' + response.successCount + ' lazy loading tabs');
                                                
                                                // Show progress completion
                                                updateProgress(response.successCount, links.length, 'All tabs created!');
                                                
                                                // Show success message after brief delay
                                                setTimeout(() => {
                                                    hideProgress();
                                                    statusEl.textContent = 'Successfully opened ' + response.successCount + ' links';
                                                    statusEl.style.color = '#10b981';
                                                    
                                                    // Auto-close popup after success
                                                    setTimeout(() => {
                                                        const panel = document.querySelector('.bulk-link-open-panel');
                                                        if (panel) panel.remove();
                                                    }, 2000);
                                                }, 1000);
                                            }
                                        });
                                    } catch (error) {
                                        console.log('Bulk Link Open: Chrome runtime error, using fallback');
                                        openLinksWithFallback(links);
                                    }
                                } else {
                                    openLinksWithFallback(links);
                                }
                            }
                            
                            // Fallback method for opening links with configurable settings
                            async function openLinksWithFallback(links) {
                                const settings = await loadOpeningSettings();
                                const batchSize = settings.batchSize;
                                const delay = settings.delay * 1000; // Convert seconds to milliseconds
                                
                                // Initialize progress tracking
                                showProgress();
                                updateProgress(0, links.length, 'Preparing to open links...');
                                
                                let batchIndex = 0;
                                let openedCount = 0;
                                const openBatch = () => {
                                    const startIndex = batchIndex * batchSize;
                                    const endIndex = Math.min(startIndex + batchSize, links.length);
                                    const batch = links.slice(startIndex, endIndex);
                                    
                                    batch.forEach((link, index) => {
                                        setTimeout(() => {
                                            if (settings.openInNewWindow && batchIndex === 0 && index === 0) {
                                                // Open first link in new window if enabled
                                                window.open(normalizeUrl(link), '_blank', 'noopener,noreferrer');
                                            } else {
                                                window.open(normalizeUrl(link), '_blank');
                                            }
                                            
                                            // Update progress
                                            openedCount++;
                                            updateProgress(openedCount, links.length, 'Opening batch ' + (batchIndex + 1) + '...');
                                        }, index * 100);
                                    });
                                    
                                    batchIndex++;
                                    if (startIndex < links.length - 1) {
                                        setTimeout(openBatch, delay);
                                    } else {
                                        // Wait for all links to finish opening, then show completion
                                        setTimeout(() => {
                                            updateProgress(links.length, links.length, 'All links opened!');
                                            
                                            // Show completion message after brief delay
                                            setTimeout(() => {
                                                hideProgress();
                                                statusEl.textContent = 'Successfully opened ' + links.length + ' links';
                                                statusEl.style.color = '#10b981';
                                                
                                                // Auto-close after completion
                                                setTimeout(() => {
                                                    const panel = document.querySelector('.bulk-link-open-panel');
                                                    if (panel) panel.remove();
                                                }, 2000);
                                            }, 1000);
                                        }, batch.length * 100 + 200); // Wait for last batch to finish
                                    }
                                };
                                
                                openBatch();
                            }
                            
                            // Create panel with saved window state
                            const savedState = await loadWindowState();
                            const windowState = validateWindowState(savedState);
                            
                            var panel = document.createElement('div');
                            panel.className = 'bulk-link-open-panel';
                            panel.style.cssText = 'position:fixed;z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;overflow:auto;resize:both;min-width:500px;min-height:400px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5';
                            
                            // Apply saved position and size
                            panel.style.left = windowState.x + 'px';
                            panel.style.top = windowState.y + 'px';
                            panel.style.width = Math.max(windowState.width, 500) + 'px';
                            panel.style.height = Math.max(windowState.height, 400) + 'px';
                            panel.style.maxHeight = '85vh';
                            
                            let html = `
                                <div id="bulk-link-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;">
                                    <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;">Bulk Link Open</h2>
                                    <button onclick="this.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
                                </div>
                            `;
                            
                            // Saved Lists Management Section
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Saved Lists</div>';
                            html += '<div style="padding:20px;">';
                            html += '<div style="display:flex;gap:12px;margin-bottom:16px;align-items:center;">';
                            html += '<select id="saved-lists-dropdown" style="flex:1;background:#1f1f1f;border:1px solid #374151;border-radius:6px;color:#d1d5db;padding:8px 12px;font-size:13px;">';
                            html += '<option value="">Select a saved list...</option>';
                            html += '</select>';
                            html += '<button id="load-list-btn" style="padding:8px 16px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;opacity:0.5;" disabled>Load</button>';
                            html += '<button id="delete-list-btn" style="padding:8px 16px;background:#dc2626;color:#ffffff;border:none;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;opacity:0.5;" disabled>Delete</button>';
                            html += '</div>';
                            html += '<div style="display:flex;gap:12px;align-items:center;">';
                            html += '<input type="text" id="new-list-name" placeholder="Enter list name..." style="flex:1;background:#1f1f1f;border:1px solid #374151;border-radius:6px;color:#d1d5db;padding:8px 12px;font-size:13px;" maxlength="50">';
                            html += '<button id="save-list-btn" style="padding:8px 16px;background:#7c3aed;color:#ffffff;border:none;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;">Save Current</button>';
                            html += '</div>';
                            html += '<div id="lists-status" style="margin-top:8px;font-size:12px;color:#9ca3af;"></div>';
                            html += '</div>';
                            html += '</div>';
                            
                            // Links Input Section
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Links Input</div>';
                            html += '<div style="padding:20px;">';
                            html += '<div style="margin-bottom:12px;font-size:13px;color:#9ca3af;">Paste links below (one per line):</div>';
                            html += '<textarea id="bulk-links-input" style="width:100%;height:200px;background:#1f1f1f;border:1px solid #374151;border-radius:6px;color:#d1d5db;padding:12px;font-family:monospace;font-size:13px;resize:vertical;line-height:1.6;" placeholder="https://example.com\nhttps://another-site.com\nwww.third-site.com"></textarea>';
                            html += '<div id="link-counter" style="margin-top:8px;font-size:12px;color:#9ca3af;">0 valid links</div>';
                            html += '</div>';
                            html += '</div>';
                            
                            // Actions Section
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Actions</div>';
                            html += '<div style="padding:20px;display:flex;gap:12px;flex-wrap:wrap;">';
                            
                            // Open All Links button
                            html += '<button id="open-all-links" style="padding:10px 20px;background:#7c3aed;color:#ffffff;border:none;border-radius:6px;cursor:pointer;font-size:13px;font-weight:500;transition:all 0.2s;">Open All Links</button>';
                            
                            // Clear button
                            html += '<button id="clear-links" style="padding:10px 20px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:13px;font-weight:500;transition:all 0.2s;">Clear</button>';
                            
                            // Remove Duplicates button
                            html += '<button id="remove-duplicates" style="padding:10px 20px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:13px;font-weight:500;transition:all 0.2s;">Remove Duplicates</button>';
                            
                            // Sort Links button
                            html += '<button id="sort-links" style="padding:10px 20px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:13px;font-weight:500;transition:all 0.2s;">Sort A-Z</button>';
                            
                            // Extract URLs button
                            html += '<button id="extract-urls" style="padding:10px 20px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:13px;font-weight:500;transition:all 0.2s;">Extract URLs</button>';
                            
                            // Get Current Tabs button
                            html += '<button id="get-current-tabs" style="padding:10px 20px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:13px;font-weight:500;transition:all 0.2s;">Get list of currently open tabs</button>';
                            
                            html += '</div>';
                            html += '<div id="bulk-link-status" style="padding:0 20px 20px;font-size:13px;color:#9ca3af;"></div>';
                            html += '<div id="progress-container" style="padding:0 20px 20px;display:none;">';
                            html += '<div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:8px;">';
                            html += '<div id="progress-text" style="font-size:12px;color:#9ca3af;">Opening links...</div>';
                            html += '<div id="progress-counter" style="font-size:12px;color:#9ca3af;">0 / 0</div>';
                            html += '</div>';
                            html += '<div style="background:#1f1f1f;border-radius:6px;height:8px;overflow:hidden;">';
                            html += '<div id="progress-bar" style="background:#7c3aed;height:100%;width:0%;transition:width 0.3s ease;"></div>';
                            html += '</div>';
                            html += '</div>';
                            html += '</div>';
                            
                            // Opening Settings Section
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Opening Settings</div>';
                            html += '<div style="padding:20px;">';
                            html += '<div style="display:grid;grid-template-columns:1fr 1fr;gap:16px;margin-bottom:16px;">';
                            html += '<div>';
                            html += '<label style="display:block;font-size:12px;color:#9ca3af;margin-bottom:6px;">Delay Between Batches (seconds)</label>';
                            html += '<input type="number" id="delay-setting" min="0" max="10" step="0.1" value="0.5" style="width:100%;background:#1f1f1f;border:1px solid #374151;border-radius:6px;color:#d1d5db;padding:8px 12px;font-size:13px;">';
                            html += '</div>';
                            html += '<div>';
                            html += '<label style="display:block;font-size:12px;color:#9ca3af;margin-bottom:6px;">Batch Size (tabs per batch)</label>';
                            html += '<input type="number" id="batch-size-setting" min="1" max="20" value="3" style="width:100%;background:#1f1f1f;border:1px solid #374151;border-radius:6px;color:#d1d5db;padding:8px 12px;font-size:13px;">';
                            html += '</div>';
                            html += '</div>';
                            html += '<div style="display:flex;align-items:center;gap:8px;">';
                            html += '<input type="checkbox" id="new-window-setting" style="margin:0;">';
                            html += '<label for="new-window-setting" style="font-size:13px;color:#d1d5db;cursor:pointer;">Open in new window</label>';
                            html += '</div>';
                            html += '</div>';
                            html += '</div>';
                            
                            panel.innerHTML = html;
                            document.body.appendChild(panel);
                            
                            // Get elements
                            const textarea = document.getElementById('bulk-links-input');
                            const linkCounter = document.getElementById('link-counter');
                            const openAllBtn = document.getElementById('open-all-links');
                            const clearBtn = document.getElementById('clear-links');
                            const removeDupBtn = document.getElementById('remove-duplicates');
                            const sortBtn = document.getElementById('sort-links');
                            const extractUrlsBtn = document.getElementById('extract-urls');
                            const getCurrentTabsBtn = document.getElementById('get-current-tabs');
                            const statusEl = document.getElementById('bulk-link-status');
                            
                            // Get new elements for saved lists and settings
                            const savedListsDropdown = document.getElementById('saved-lists-dropdown');
                            const loadListBtn = document.getElementById('load-list-btn');
                            const deleteListBtn = document.getElementById('delete-list-btn');
                            const newListNameInput = document.getElementById('new-list-name');
                            const saveListBtn = document.getElementById('save-list-btn');
                            const listsStatusEl = document.getElementById('lists-status');
                            const delaySettingInput = document.getElementById('delay-setting');
                            const batchSizeSettingInput = document.getElementById('batch-size-setting');
                            const newWindowSettingCheckbox = document.getElementById('new-window-setting');
                            
                            // Get progress elements
                            const progressContainer = document.getElementById('progress-container');
                            const progressText = document.getElementById('progress-text');
                            const progressCounter = document.getElementById('progress-counter');
                            const progressBar = document.getElementById('progress-bar');
                            
                            // Update link counter
                            function updateLinkCounter() {
                                const lines = textarea.value.split('\n').filter(line => line.trim());
                                const validLinks = lines.filter(link => isValidUrl(link.trim()));
                                linkCounter.textContent = validLinks.length + ' valid links';
                                
                                // Enable/disable open button
                                openAllBtn.disabled = validLinks.length === 0;
                                openAllBtn.style.opacity = validLinks.length === 0 ? '0.5' : '1';
                                
                                // Auto-save current session on change
                                saveCurrentSessionLinks(textarea.value);
                            }
                            
                            // Progress tracking functions
                            function showProgress() {
                                progressContainer.style.display = 'block';
                                statusEl.style.display = 'none';
                            }
                            
                            function hideProgress() {
                                progressContainer.style.display = 'none';
                                statusEl.style.display = 'block';
                            }
                            
                            function updateProgress(current, total, text = 'Opening links...') {
                                const percentage = total > 0 ? (current / total) * 100 : 0;
                                progressBar.style.width = percentage + '%';
                                progressCounter.textContent = current + ' / ' + total;
                                progressText.textContent = text;
                            }
                            
                            function resetProgress() {
                                updateProgress(0, 0, 'Opening links...');
                                hideProgress();
                            }
                            
                            // URL extraction function
                            function extractUrlsFromText(text) {
                                // Enhanced URL regex pattern that matches various URL formats
                                const urlRegex = /(https?:\/\/[^\s]+|www\.[^\s]+|[a-zA-Z0-9-]+\.[a-zA-Z]{2,}[^\s]*)/gi;
                                
                                const matches = text.match(urlRegex) || [];
                                const urls = [];
                                
                                matches.forEach(match => {
                                    // Clean up the URL
                                    let url = match.trim();
                                    
                                    // Remove trailing punctuation that's not part of the URL
                                    url = url.replace(/[.,;:!?)"'>}]+$/, '');
                                    
                                    // Add protocol if missing
                                    if (!url.match(/^https?:\/\//)) {
                                        if (url.startsWith('www.') || url.includes('.')) {
                                            url = 'http://' + url;
                                        }
                                    }
                                    
                                    // Validate the cleaned URL
                                    if (isValidUrl(url)) {
                                        urls.push(url);
                                    }
                                });
                                
                                // Remove duplicates while preserving order
                                return [...new Set(urls)];
                            }
                            
                            // Initialize saved lists dropdown and settings
                            async function initializeInterface() {
                                // Load saved lists and populate dropdown
                                const savedLists = await loadSavedLists();
                                populateDropdown(savedLists);
                                
                                // Load opening settings
                                const settings = await loadOpeningSettings();
                                delaySettingInput.value = settings.delay;
                                batchSizeSettingInput.value = settings.batchSize;
                                newWindowSettingCheckbox.checked = settings.openInNewWindow;
                                
                                // Load current session
                                const currentSession = await loadCurrentSessionLinks();
                                if (currentSession) {
                                    textarea.value = currentSession;
                                    updateLinkCounter();
                                }
                            }
                            
                            function populateDropdown(savedLists) {
                                // Clear existing options (keep placeholder)
                                savedListsDropdown.innerHTML = '<option value="">Select a saved list...</option>';
                                
                                Object.keys(savedLists).forEach(listName => {
                                    const option = document.createElement('option');
                                    option.value = listName;
                                    option.textContent = listName;
                                    savedListsDropdown.appendChild(option);
                                });
                            }
                            
                            // Initialize interface
                            initializeInterface();
                            
                            // Function to get all currently open tabs
                            async function getCurrentTabs() {
                                try {
                                    if (window.chrome && chrome.runtime) {
                                        return new Promise((resolve, reject) => {
                                            chrome.runtime.sendMessage({
                                                action: 'getCurrentTabs'
                                            }, (response) => {
                                                if (chrome.runtime.lastError) {
                                                    reject(new Error(chrome.runtime.lastError.message));
                                                } else if (!response || !response.success) {
                                                    reject(new Error(response ? response.error : 'Failed to get tabs from background script'));
                                                } else {
                                                    console.log(`Bulk Link Open: Got ${response.urls.length} tabs from background script`);
                                                    resolve(response.urls);
                                                }
                                            });
                                        });
                                    } else {
                                        throw new Error('Chrome runtime API not available');
                                    }
                                } catch (error) {
                                    console.error('Error getting current tabs:', error);
                                    throw error;
                                }
                            }
                            
                            // Event listeners
                            textarea.addEventListener('input', updateLinkCounter);
                            textarea.addEventListener('paste', () => {
                                setTimeout(updateLinkCounter, 10);
                            });
                            
                            openAllBtn.addEventListener('click', () => {
                                const lines = textarea.value.split('\n').filter(line => line.trim());
                                const validLinks = lines.filter(link => isValidUrl(link.trim())).map(link => link.trim());
                                
                                if (validLinks.length > 0) {
                                    statusEl.textContent = 'Opening ' + validLinks.length + ' links...';
                                    statusEl.style.color = '#9ca3af';
                                    openLinksWithLazyLoading(validLinks);
                                }
                            });
                            
                            clearBtn.addEventListener('click', () => {
                                textarea.value = '';
                                updateLinkCounter();
                                statusEl.textContent = 'Links cleared';
                                statusEl.style.color = '#9ca3af';
                                saveCurrentSessionLinks('');
                            });
                            
                            removeDupBtn.addEventListener('click', () => {
                                const lines = textarea.value.split('\n').filter(line => line.trim());
                                const uniqueLinks = [...new Set(lines)];
                                const removedCount = lines.length - uniqueLinks.length;
                                textarea.value = uniqueLinks.join('\n');
                                updateLinkCounter();
                                statusEl.textContent = 'Removed ' + removedCount + ' duplicate' + (removedCount !== 1 ? 's' : '');
                                statusEl.style.color = '#10b981';
                            });
                            
                            sortBtn.addEventListener('click', () => {
                                const lines = textarea.value.split('\n').filter(line => line.trim());
                                lines.sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()));
                                textarea.value = lines.join('\n');
                                updateLinkCounter();
                                statusEl.textContent = 'Links sorted alphabetically';
                                statusEl.style.color = '#10b981';
                            });
                            
                            extractUrlsBtn.addEventListener('click', () => {
                                const currentText = textarea.value;
                                if (!currentText.trim()) {
                                    statusEl.textContent = 'Please paste some text first';
                                    statusEl.style.color = '#dc2626';
                                    setTimeout(() => {
                                        statusEl.textContent = '';
                                    }, 3000);
                                    return;
                                }
                                
                                const extractedUrls = extractUrlsFromText(currentText);
                                
                                if (extractedUrls.length === 0) {
                                    statusEl.textContent = 'No valid URLs found in the text';
                                    statusEl.style.color = '#dc2626';
                                    setTimeout(() => {
                                        statusEl.textContent = '';
                                    }, 3000);
                                    return;
                                }
                                
                                // Replace textarea content with extracted URLs
                                textarea.value = extractedUrls.join('\n');
                                updateLinkCounter();
                                statusEl.textContent = 'Extracted ' + extractedUrls.length + ' URL' + (extractedUrls.length !== 1 ? 's' : '');
                                statusEl.style.color = '#10b981';
                                setTimeout(() => {
                                    statusEl.textContent = '';
                                }, 3000);
                            });
                            
                            getCurrentTabsBtn.addEventListener('click', async () => {
                                try {
                                    statusEl.textContent = 'Loading tabs...';
                                    statusEl.style.color = '#9ca3af';
                                    
                                    const urls = await getCurrentTabs();
                                    
                                    if (urls.length === 0) {
                                        statusEl.textContent = 'No valid tabs found to load';
                                        statusEl.style.color = '#dc2626';
                                        setTimeout(() => {
                                            statusEl.textContent = '';
                                        }, 3000);
                                        return;
                                    }
                                    
                                    // Add urls to textarea (append to existing content if any)
                                    const existingContent = textarea.value.trim();
                                    const newContent = existingContent ? existingContent + '\n' + urls.join('\n') : urls.join('\n');
                                    textarea.value = newContent;
                                    
                                    updateLinkCounter();
                                    statusEl.textContent = 'Loaded ' + urls.length + ' tab' + (urls.length !== 1 ? 's' : '');
                                    statusEl.style.color = '#10b981';
                                    setTimeout(() => {
                                        statusEl.textContent = '';
                                    }, 3000);
                                    
                                } catch (error) {
                                    console.error('Error loading tabs:', error);
                                    statusEl.textContent = 'Error loading tabs. Please check permissions.';
                                    statusEl.style.color = '#dc2626';
                                    setTimeout(() => {
                                        statusEl.textContent = '';
                                    }, 3000);
                                }
                            });
                            
                            // Event listeners for saved lists functionality
                            savedListsDropdown.addEventListener('change', () => {
                                const selectedList = savedListsDropdown.value;
                                loadListBtn.disabled = !selectedList;
                                loadListBtn.style.opacity = selectedList ? '1' : '0.5';
                                deleteListBtn.disabled = !selectedList;
                                deleteListBtn.style.opacity = selectedList ? '1' : '0.5';
                            });
                            
                            loadListBtn.addEventListener('click', async () => {
                                const selectedList = savedListsDropdown.value;
                                if (selectedList) {
                                    const savedLists = await loadSavedLists();
                                    if (savedLists[selectedList]) {
                                        textarea.value = savedLists[selectedList];
                                        updateLinkCounter();
                                        listsStatusEl.textContent = 'Loaded "' + selectedList + '"';
                                        listsStatusEl.style.color = '#10b981';
                                        setTimeout(() => {
                                            listsStatusEl.textContent = '';
                                        }, 3000);
                                    }
                                }
                            });
                            
                            deleteListBtn.addEventListener('click', async () => {
                                const selectedList = savedListsDropdown.value;
                                if (selectedList && confirm('Delete list "' + selectedList + '"?')) {
                                    const savedLists = await loadSavedLists();
                                    delete savedLists[selectedList];
                                    await saveLists(savedLists);
                                    populateDropdown(savedLists);
                                    savedListsDropdown.value = '';
                                    loadListBtn.disabled = true;
                                    loadListBtn.style.opacity = '0.5';
                                    deleteListBtn.disabled = true;
                                    deleteListBtn.style.opacity = '0.5';
                                    listsStatusEl.textContent = 'Deleted "' + selectedList + '"';
                                    listsStatusEl.style.color = '#dc2626';
                                    setTimeout(() => {
                                        listsStatusEl.textContent = '';
                                    }, 3000);
                                }
                            });
                            
                            saveListBtn.addEventListener('click', async () => {
                                const listName = newListNameInput.value.trim();
                                const currentLinks = textarea.value.trim();
                                
                                if (!listName) {
                                    listsStatusEl.textContent = 'Please enter a list name';
                                    listsStatusEl.style.color = '#dc2626';
                                    setTimeout(() => {
                                        listsStatusEl.textContent = '';
                                    }, 3000);
                                    return;
                                }
                                
                                if (!currentLinks) {
                                    listsStatusEl.textContent = 'Please add some links first';
                                    listsStatusEl.style.color = '#dc2626';
                                    setTimeout(() => {
                                        listsStatusEl.textContent = '';
                                    }, 3000);
                                    return;
                                }
                                
                                const savedLists = await loadSavedLists();
                                savedLists[listName] = currentLinks;
                                await saveLists(savedLists);
                                populateDropdown(savedLists);
                                newListNameInput.value = '';
                                listsStatusEl.textContent = 'Saved as "' + listName + '"';
                                listsStatusEl.style.color = '#10b981';
                                setTimeout(() => {
                                    listsStatusEl.textContent = '';
                                }, 3000);
                            });
                            
                            // Event listeners for settings
                            delaySettingInput.addEventListener('change', async () => {
                                const settings = await loadOpeningSettings();
                                settings.delay = parseFloat(delaySettingInput.value);
                                await saveOpeningSettings(settings);
                            });
                            
                            batchSizeSettingInput.addEventListener('change', async () => {
                                const settings = await loadOpeningSettings();
                                settings.batchSize = parseInt(batchSizeSettingInput.value);
                                await saveOpeningSettings(settings);
                            });
                            
                            newWindowSettingCheckbox.addEventListener('change', async () => {
                                const settings = await loadOpeningSettings();
                                settings.openInNewWindow = newWindowSettingCheckbox.checked;
                                await saveOpeningSettings(settings);
                            });
                            
                            // Make panel draggable
                            var isDragging = false;
                            var currentX, currentY, initialX, initialY, xOffset = 0, yOffset = 0;
                            var header = document.getElementById('bulk-link-header');
                            
                            function dragStart(e) {
                                initialX = e.clientX - xOffset;
                                initialY = e.clientY - yOffset;
                                
                                if (e.target === header || header.contains(e.target)) {
                                    isDragging = true;
                                }
                            }
                            
                            function dragEnd(e) {
                                if (isDragging) {
                                    initialX = currentX;
                                    initialY = currentY;
                                    isDragging = false;
                                    
                                    // Convert transform position to actual CSS position
                                    const currentLeft = parseInt(panel.style.left) || 0;
                                    const currentTop = parseInt(panel.style.top) || 0;
                                    const newLeft = currentLeft + currentX;
                                    const newTop = currentTop + currentY;
                                    
                                    // Apply the new position and reset transform
                                    panel.style.left = Math.max(0, newLeft) + 'px';
                                    panel.style.top = Math.max(0, newTop) + 'px';
                                    panel.style.transform = 'translate(0px, 0px)';
                                    
                                    // Reset transform tracking
                                    xOffset = 0;
                                    yOffset = 0;
                                    currentX = 0;
                                    currentY = 0;
                                    
                                    // Save the new position
                                    const rect = panel.getBoundingClientRect();
                                    const newState = {
                                        x: Math.max(0, rect.left),
                                        y: Math.max(0, rect.top),
                                        width: rect.width,
                                        height: rect.height
                                    };
                                    debouncedSaveWindowState(newState);
                                }
                            }

                            
                            // Add resize observer to save size when panel is resized
                            const resizeObserver = new ResizeObserver(entries => {
                                // Save size changes using CSS style properties (like other popups)
                                loadWindowState().then(savedState => {
                                    const newState = {
                                        x: savedState.x, // Preserve saved position
                                        y: savedState.y, // Preserve saved position  
                                        width: parseInt(panel.style.width) || savedState.width, // Use CSS property
                                        height: parseInt(panel.style.height) || savedState.height // Use CSS property
                                    };
                                    debouncedSaveWindowState(newState);
                                }).catch(() => {
                                    // Fallback: save current CSS properties
                                    const newState = {
                                        x: parseInt(panel.style.left) || 20,
                                        y: parseInt(panel.style.top) || 20,
                                        width: parseInt(panel.style.width) || 600,
                                        height: parseInt(panel.style.height) || 400
                                    };
                                    debouncedSaveWindowState(newState);
                                });
                            });
                            resizeObserver.observe(panel);
                            
                            function drag(e) {
                                if (e.preventDefault) {
                                    e.preventDefault();
                                }
                                
                                if (isDragging) {
                                    currentX = e.clientX - initialX;
                                    currentY = e.clientY - initialY;
                                    xOffset = currentX;
                                    yOffset = currentY;
                                    
                                    panel.style.transform = 'translate(' + currentX + 'px, ' + currentY + 'px)';
                                }
                            }
                            
                            header.addEventListener('mousedown', dragStart);
                            document.addEventListener('mousemove', drag);
                            document.addEventListener('mouseup', dragEnd);
                            
                            // Escape key handler
                            function handleKeyDown(e) {
                                if (e.key === 'Escape') {
                                    panel.remove();
                                    document.removeEventListener('keydown', handleKeyDown);
                                }
                            }
                            document.addEventListener('keydown', handleKeyDown);
                            
                            // Focus textarea
                            setTimeout(() => textarea.focus(), 100);
            })();
        } catch (error) {
            console.error('Bulk Link Open error:', error);
        }
    }

    static reset() {
        try {
            // This runs directly in the content script context (Pattern B)
            // Remove all bulk link open panels
            const existingPanels = document.querySelectorAll('.bulk-link-open-panel');
            existingPanels.forEach(panel => {
                if (panel.parentNode) {
                    panel.parentNode.removeChild(panel);
                }
            });
            
            // Clean up any global variables if needed
            if (window.BulkLinkOpenActive) {
                delete window.BulkLinkOpenActive;
            }
            
            // Clean up window state timeout if it exists
            if (typeof saveWindowStateTimeout !== 'undefined') {
                clearTimeout(saveWindowStateTimeout);
            }
            
            console.log('Bulk Link Open reset completed');
        } catch (error) {
            console.error('Bulk Link Open reset error:', error);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BulkLinkOpenAction;
} else {
    window.BulkLinkOpenAction = BulkLinkOpenAction;
}

} // End of namespace protection