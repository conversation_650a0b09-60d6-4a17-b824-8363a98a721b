// SEO Tests Action - Run SEO testing tools on the current URL
class SeoTestsAction {
    static execute() {
        try {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                    target: {tabId: tabs[0].id},
                    func: function() {
                        // SEO Tests functionality
                        (function() {
                            // Remove existing SEO Tests panel if present
                            document.querySelectorAll('.seo-tests-panel').forEach(panel => panel.remove());
                            
                            // Helper functions
                            function escape(str) {
                                if (!str) return '';
                                return str.replace(/&/g, '&amp;')
                                         .replace(/</g, '&lt;')
                                         .replace(/>/g, '&gt;')
                                         .replace(/"/g, '&quot;')
                                         .replace(/'/g, '&#39;');
                            }
                            
                            // Get current URL and host for testing
                            const currentUrl = window.location.href;
                            const currentHost = window.location.host;
                            
                            // Get saved panel settings (similar to UTM Builder)
                            function getSavedPanelSettings() {
                                try {
                                    const saved = localStorage.getItem('seo-tests-panel-settings');
                                    if (saved) {
                                        return JSON.parse(saved);
                                    }
                                } catch (e) {
                                    console.log('Error loading saved panel settings:', e);
                                }
                                                                 return {
                                     top: '20px',
                                     left: '20px',
                                     width: '700px'
                                 };
                                                         }
                             
                             // Get saved test selections
                             function getSavedTestSelections() {
                                 try {
                                     const saved = localStorage.getItem('seo-tests-selections');
                                     if (saved) {
                                         return JSON.parse(saved);
                                     }
                                 } catch (e) {
                                     console.log('Error loading saved test selections:', e);
                                 }
                                 return [];
                             }
                             
                             // Save current test selections
                             function saveTestSelections(selectedTests) {
                                 try {
                                     localStorage.setItem('seo-tests-selections', JSON.stringify(selectedTests));
                                     console.log('SEO Tests: Saved', selectedTests.length, 'test selections');
                                 } catch (e) {
                                     console.log('Error saving test selections:', e);
                                 }
                             }
                             
                             // Save panel settings
                            function savePanelSettings(panel) {
                                try {
                                                                         const settings = {
                                         top: panel.style.top || '20px',
                                         left: panel.style.left || '20px',
                                         width: panel.style.width || '700px'
                                     };
                                    localStorage.setItem('seo-tests-panel-settings', JSON.stringify(settings));
                                } catch (e) {
                                    console.log('Error saving panel settings:', e);
                                                                 }
                             }
                             
                             // Apply saved test selections to checkboxes
                             function applySavedSelections() {
                                 const savedSelections = getSavedTestSelections();
                                 if (savedSelections.length > 0) {
                                     console.log('SEO Tests: Applying', savedSelections, 'saved selections');
                                     savedSelections.forEach(testId => {
                                         const checkbox = document.querySelector(`input[name="seoTests"][value="${testId}"]`);
                                         if (checkbox) {
                                             checkbox.checked = true;
                                         }
                                     });
                                     
                                     // Update category select buttons
                                     updateCategoryButtons();
                                 }
                             }
                             
                             // Update category select all buttons based on current selections
                             function updateCategoryButtons() {
                                 document.querySelectorAll('.category-select-all').forEach(button => {
                                     const categoryDiv = button.closest('div').nextElementSibling;
                                     const checkboxes = categoryDiv.querySelectorAll('input[type="checkbox"]');
                                     const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                                     button.textContent = allChecked ? 'Deselect All' : 'Select All';
                                 });
                                 
                                 // Update main select all button
                                 const form = document.getElementById('seo-tests-form');
                                 const selectAllButton = document.getElementById('select-all-tests');
                                 if (form && selectAllButton) {
                                     const allCheckboxes = form.querySelectorAll('input[type="checkbox"]');
                                     const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
                                     selectAllButton.textContent = allChecked ? 'Deselect All Tests' : 'Select All Tests';
                                 }
                             }
                             
                             // Test configurations
                             const testCategories = [
                                 {
                                     name: 'Performance & Speed',
                                     tests: [
                                         { id: 'gtmetrix', name: 'GTmetrix', url: 'https://gtmetrix.com/analyze.html?bm', method: 'POST' },
                                         { id: 'yellowlab', name: 'YellowLab Tools', url: `https://yellowlab.tools?url=${encodeURIComponent(currentUrl)}&run=1` }
                                     ]
                                 },
                                 {
                                     name: 'SEO & Structure',
                                     tests: [
                                         { id: 'structureddata', name: 'Validate Schema', url: `https://validator.schema.org/#url=${encodeURIComponent(currentUrl)}` },
                                         { id: 'richresults-mobile', name: 'Google Rich Results Test (Mobile)', url: `https://search.google.com/test/rich-results?user_agent=1&url=${encodeURIComponent(currentUrl)}` },
                                         { id: 'richresults-desktop', name: 'Google Rich Results Test (Desktop)', url: `https://search.google.com/test/rich-results?user_agent=2&url=${encodeURIComponent(currentUrl)}` },
                                         { id: 'amptest', name: 'Google AMP Test', url: `https://search.google.com/test/amp?url=${encodeURIComponent(currentUrl)}` }
                                     ]
                                 },
                                 {
                                     name: 'Mobile & Accessibility',
                                     tests: [
                                         { id: 'mobilefriendly', name: 'Google Mobile-Friendly Test', url: `https://search.google.com/test/mobile-friendly?url=${encodeURIComponent(currentUrl)}` }
                                     ]
                                 },
                                 {
                                     name: 'Technical',
                                     tests: [
                                         { id: 'googlecache', name: 'Google Cache', url: `https://webcache.googleusercontent.com/search?strip=1&vwsrc=0&q=cache:${encodeURIComponent(currentUrl)}` },
                                         { id: 'sslchecker', name: 'SSL Checker', url: `https://decoder.link/sslchecker/${currentHost}/443` }
                                     ]
                                 }
                             ];
                            
                            // Create panel
                            var panel = document.createElement('div');
                            panel.className = 'seo-tests-panel';
                            
                                                         const savedSettings = getSavedPanelSettings();
                             panel.style.cssText = `position:fixed;top:${savedSettings.top};left:${savedSettings.left};width:${savedSettings.width};z-index:2147483647;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:8px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:16px;overflow:auto;resize:both;min-width:600px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.4`;
                            
                            let html = `
                                                                 <div id="seo-tests-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:16px;padding-bottom:12px;border-bottom:1px solid #2a2a2a;cursor:move;">
                                     <h1 style="margin:0;color:#d1d5db;font-size:16px;font-weight:600;">SEO Tests</h1>
                                     <div style="display:flex;gap:8px;align-items:center;">
                                         <button id="seo-tests-close-btn" style="padding:4px 8px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:4px;cursor:pointer;font-size:11px;font-weight:500;transition:all 0.2s;" onmouseover="this.style.background='#4b5563'" onmouseout="this.style.background='#374151'">✕</button>
                                     </div>
                                 </div>
                                
                                <!-- Current URL Section -->
                                <div style="background:#111111;border-radius:4px;border:1px solid #2a2a2a;margin-bottom:16px;overflow:hidden;">
                                    <div style="background:#1a1a1a;padding:6px 8px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Current URL</div>
                                    <div style="padding:6px 8px;">
                                        <div style="background:#1f1f1f;padding:6px;border-radius:2px;font-family:monospace;font-size:12px;line-height:1.3;word-break:break-all;color:#d1d5db;">${escape(currentUrl)}</div>
                                    </div>
                                </div>
                                
                                <!-- SEO Tests Form -->
                                <form id="seo-tests-form" style="display:flex;flex-direction:column;gap:16px;">
                            `;
                            
                                                         // Add test categories
                             testCategories.forEach(category => {
                                 html += `
                                     <div style="background:#111111;border-radius:4px;border:1px solid #2a2a2a;overflow:hidden;">
                                         <div style="background:#1a1a1a;padding:6px 8px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;display:flex;justify-content:space-between;align-items:center;">
                                             ${escape(category.name)}
                                             <button type="button" class="category-select-all" data-category="${escape(category.name)}" style="padding:3px 6px;background:transparent;color:#6b7280;border:1px solid #374151;border-radius:2px;cursor:pointer;font-size:14px;font-weight:400;transition:all 0.2s;" onmouseover="this.style.background='#374151';this.style.color='#d1d5db'" onmouseout="this.style.background='transparent';this.style.color='#6b7280'">Select All</button>
                                         </div>
                                         <div style="padding:8px;display:grid;grid-template-columns:1fr 1fr;gap:4px 8px;">
                                 `;
                                 
                                 category.tests.forEach(test => {
                                     html += `
                                         <label style="display:flex;align-items:center;padding:4px;cursor:pointer;transition:all 0.2s;border-radius:3px;" onmouseover="this.style.background='#1a1a1a'" onmouseout="this.style.background=''">
                                             <input type="checkbox" name="seoTests" value="${escape(test.id)}" style="margin-right:6px;accent-color:#7C3AED;flex-shrink:0;" />
                                             <span style="color:#d1d5db;font-size:13px;line-height:1.2;word-break:break-word;">${escape(test.name)}</span>
                                         </label>
                                     `;
                                 });
                                 
                                 html += `
                                         </div>
                                     </div>
                                 `;
                             });
                            
                            // Add control buttons
                            html += `
                                    <div style="display:flex;justify-content:space-between;align-items:center;gap:12px;padding:12px 0;border-top:1px solid #2a2a2a;">
                                        <button type="button" id="select-all-tests" style="padding:8px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:4px;cursor:pointer;font-size:14px;font-weight:500;transition:all 0.2s;" onmouseover="this.style.background='#4b5563'" onmouseout="this.style.background='#374151'">Select All Tests</button>
                                        <button type="submit" style="padding:8px 16px;background:#7C3AED;color:white;border:1px solid #7C3AED;border-radius:4px;cursor:pointer;font-size:14px;font-weight:500;transition:all 0.2s;" onmouseover="this.style.background='#6D28D9'" onmouseout="this.style.background='#7C3AED'">Run Selected Tests</button>
                                    </div>
                                </form>
                            `;
                            
                                                         panel.innerHTML = html;
                             document.body.appendChild(panel);
                             
                             // Add event listeners
                             const form = document.getElementById('seo-tests-form');
                             const selectAllButton = document.getElementById('select-all-tests');
                             const closeButton = document.getElementById('seo-tests-close-btn');
                             
                             // Close button event listener
                             closeButton.addEventListener('click', function() {
                                 panel.remove();
                                 // Clean up global functions
                                 if (window.closeSeoTests) window.closeSeoTests = null;
                             });
                             
                             // Apply saved test selections
                             applySavedSelections();
                            
                                                         // Category select all buttons
                             document.querySelectorAll('.category-select-all').forEach(button => {
                                 button.addEventListener('click', function() {
                                     const categoryName = this.dataset.category;
                                     const categoryDiv = this.closest('div').nextElementSibling;
                                     const checkboxes = categoryDiv.querySelectorAll('input[type="checkbox"]');
                                     const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                                     
                                     checkboxes.forEach(cb => cb.checked = !allChecked);
                                     updateCategoryButtons();
                                 });
                             });
                            
                                                         // Main select all button
                             selectAllButton.addEventListener('click', function() {
                                 const allCheckboxes = form.querySelectorAll('input[type="checkbox"]');
                                 const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
                                 
                                 allCheckboxes.forEach(cb => cb.checked = !allChecked);
                                 updateCategoryButtons();
                             });
                            
                                                         // Form submission
                             form.addEventListener('submit', function(e) {
                                 e.preventDefault();
                                 
                                 const selectedTests = Array.from(form.querySelectorAll('input[name="seoTests"]:checked'));
                                 
                                 if (selectedTests.length === 0) {
                                     alert('Please select at least one test to run.');
                                     return;
                                 }
                                 
                                 // Save current selections for next time
                                 const selectedTestIds = selectedTests.map(test => test.value);
                                 saveTestSelections(selectedTestIds);
                                
                                // Test configurations map
                                const testConfigs = {};
                                testCategories.forEach(category => {
                                    category.tests.forEach(test => {
                                        testConfigs[test.id] = test;
                                    });
                                });
                                
                                // Run selected tests
                                selectedTests.forEach(testCheckbox => {
                                    const config = testConfigs[testCheckbox.value];
                                    if (config) {
                                        if (config.method === 'POST' && testCheckbox.value === 'gtmetrix') {
                                            // Special handling for GTmetrix POST form
                                            const newWindow = window.open();
                                            const doc = newWindow.document;
                                            const postForm = doc.createElement('form');
                                            postForm.action = config.url;
                                            postForm.method = 'post';
                                            const input = doc.createElement('input');
                                            input.type = 'hidden';
                                            input.name = 'url';
                                            input.value = currentUrl;
                                            postForm.appendChild(input);
                                            doc.body.appendChild(postForm);
                                            postForm.submit();
                                        } else {
                                            window.open(config.url, '_blank');
                                        }
                                    }
                                });
                                
                                // Close panel after running tests
                                panel.remove();
                            });
                            
                            // Close function
                            window.closeSeoTests = function() {
                                panel.remove();
                                // Clean up global functions
                                window.closeSeoTests = null;
                            };
                            
                            // Dragging functionality (copied from UTM Builder)
                            var isDragging = false;
                            var dragOffsetX = 0, dragOffsetY = 0;
                            var header = document.getElementById('seo-tests-header');
                            
                            function dragStart(e) {
                                e = e.type === "mousedown" ? e : e.touches[0];
                                if (e.target.tagName === 'BUTTON') return;
                                
                                if (e.target === header || header.contains(e.target)) {
                                    isDragging = true;
                                    
                                    // Get current panel position
                                    const rect = panel.getBoundingClientRect();
                                    
                                    // Calculate offset between mouse and panel's top-left corner
                                    dragOffsetX = e.clientX - rect.left;
                                    dragOffsetY = e.clientY - rect.top;
                                    
                                    header.style.cursor = 'grabbing';
                                    e.preventDefault();
                                }
                            }
                            
                            function dragEnd(e) {
                                if (isDragging) {
                                    isDragging = false;
                                    header.style.cursor = 'move';
                                    savePanelSettings(panel);
                                }
                            }
                            
                            function drag(e) {
                                if (isDragging) {
                                    e.preventDefault();
                                    e = e.type === "mousemove" ? e : e.touches[0];
                                    
                                    // Calculate new position
                                    const newX = e.clientX - dragOffsetX;
                                    const newY = e.clientY - dragOffsetY;
                                    
                                    // Keep panel within viewport bounds
                                    const maxX = window.innerWidth - panel.offsetWidth;
                                    const maxY = window.innerHeight - panel.offsetHeight;
                                    
                                    const constrainedX = Math.max(0, Math.min(newX, maxX));
                                    const constrainedY = Math.max(0, Math.min(newY, maxY));
                                    
                                    panel.style.left = constrainedX + "px";
                                    panel.style.top = constrainedY + "px";
                                }
                            }
                            
                            header.addEventListener('mousedown', dragStart);
                            document.addEventListener('mousemove', drag);
                            document.addEventListener('mouseup', dragEnd);
                            
                            // Escape key to close
                            function handleKeyDown(e) {
                                if (e.key === 'Escape') {
                                    window.closeSeoTests();
                                    document.removeEventListener('keydown', handleKeyDown);
                                }
                            }
                            document.addEventListener('keydown', handleKeyDown);
                            
                            // Auto-save panel settings when resized
                            const resizeObserver = new ResizeObserver(entries => {
                                savePanelSettings(panel);
                            });
                            resizeObserver.observe(panel);
                            
                        })();
                    }
                });
            });
        } catch (error) {
            console.error('SEO Tests error:', error);
        }
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                function resetSeoTests() {
                    // Clean up all modifications made by this action
                    const existingPanel = document.querySelector('.seo-tests-panel');
                    if (existingPanel) {
                        existingPanel.remove();
                    }
                    
                    // Clean up global functions
                    if (window.closeSeoTests) window.closeSeoTests = null;
                    
                    console.log('SEO Tests reset completed');
                }

                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetSeoTests
                    }, () => {
                        resolve();
                    });
                });
            } catch (error) {
                console.error('SEO Tests reset error:', error);
                resolve();
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SeoTestsAction;
} else {
    window.SeoTestsAction = SeoTestsAction;
} 