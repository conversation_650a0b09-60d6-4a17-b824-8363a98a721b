/**
 * MOZ DA Checker Action
 * Opens the current domain in MOZ Domain Analysis tool
 */
class MOZDACheckerAction {
  static execute() {
    try {
      console.log('MOZ DA Checker activated');
      
      // Get current hostname
      const hostname = window.location.hostname;
      
      // Open MOZ Domain Analysis page for the current site
      window.open(`https://moz.com/domain-analysis?site=${hostname}`, "_blank");
      
      console.log(`MOZ DA checker opened for: ${hostname}`);
      
      return { 
        success: true, 
        message: 'MOZ DA checker executed successfully',
        hostname: hostname 
      };
    } catch (error) {
      console.error('Error executing MOZ DA checker:', error);
      alert("Error opening MOZ DA checker: " + error.message);
      return { error: error.message };
    }
  }

  static reset() {
    // MOZ DA checker doesn't need reset functionality
    // as it just opens a new window
    console.log('MOZ DA checker reset called (no action needed)');
  }
}

// Make it available globally
if (typeof window !== 'undefined') {
  window.MOZDACheckerAction = MOZDACheckerAction;
} 