// Word Counter Action - Count words and characters in selected text
class WordCounterAction {
    static execute(selectedText) {
        try {
            // Remove existing word counter panel if present
            document.querySelectorAll('.word-counter-panel').forEach(panel => panel.remove());
            
            const count = this.countText(selectedText);
            
            // Helper functions
            function escape(str) {
                if (!str) return '';
                return str.replace(/&/g, '&amp;')
                         .replace(/</g, '&lt;')
                         .replace(/>/g, '&gt;')
                         .replace(/"/g, '&quot;')
                         .replace(/'/g, '&#39;');
            }
            
            function hlCode(str) {
                return '<code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + str + '</code>';
            }
            
            function createRow(title, info, content) {
                return `
                    <div style="display:flex;padding:16px 0;border-bottom:1px solid #2a2a2a;">
                        <div style="min-width:120px;font-weight:500;color:#9ca3af;font-size:13px;text-transform:uppercase;margin-right:20px;">${title}</div>
                        <div style="flex:1;">
                            ${info ? `<div style="font-size:12px;color:#6b7280;margin-bottom:8px;">${info}</div>` : ''}
                            <div style="color:#d1d5db;line-height:1.5;">${content}</div>
                        </div>
                    </div>
                `;
            }
            
            // Create panel
            var panel = document.createElement('div');
            panel.className = 'word-counter-panel';
            panel.style.cssText = `position:fixed;top:20px;right:20px;width:400px;max-height:85vh;z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;overflow:auto;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5`;
            
            let html = `
                <div id="word-counter-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;">
                    <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;"><span style="color: #7C3AED; font-size: 18px;">●</span> Word Counter</h2>
                    <button onclick="this.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
                </div>
            `;
            
            // Text Analysis Section
            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Text Analysis</div>';
            html += '<div style="padding:0 20px;">';
            
            html += createRow(
                'Words',
                'Individual words separated by spaces',
                `<span style="color:#10b981;font-size:18px;font-weight:600;">${count.words}</span>`
            );
            
            html += createRow(
                'Characters',
                'Including spaces and punctuation',
                `<span style="color:#6366f1;font-size:18px;font-weight:600;">${count.characters}</span>`
            );
            
            html += createRow(
                'Characters',
                'Excluding spaces',
                `<span style="color:#f59e0b;font-size:16px;font-weight:500;">${count.charactersNoSpaces}</span>`
            );
            
            if (count.sentences > 0) {
                html += createRow(
                    'Sentences',
                    'Based on sentence-ending punctuation',
                    `<span style="color:#8b5cf6;font-size:16px;font-weight:500;">${count.sentences}</span>`
                );
            }
            
            if (count.paragraphs > 0) {
                html += createRow(
                    'Paragraphs',
                    'Based on line breaks',
                    `<span style="color:#ec4899;font-size:16px;font-weight:500;">${count.paragraphs}</span>`
                );
            }
            
            if (count.words > 0) {
                const avgWordsPerSentence = count.sentences > 0 ? (count.words / count.sentences).toFixed(1) : 'N/A';
                const avgCharsPerWord = (count.characters / count.words).toFixed(1);
                
                html += createRow(
                    'Avg Word Length',
                    'Characters per word',
                    hlCode(avgCharsPerWord + ' chars')
                );
                
                if (count.sentences > 0) {
                    html += createRow(
                        'Avg Sentence Length',
                        'Words per sentence',
                        hlCode(avgWordsPerSentence + ' words')
                    );
                }
            }
            
            html += '</div>';
            html += '</div>';
            
            // Selected Text Preview Section
            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Selected Text Preview</div>';
            html += '<div style="padding:20px;">';
            
            const truncatedText = selectedText.length > 200 ? selectedText.substring(0, 200) + '...' : selectedText;
            html += '<div style="background:#1a1a1a;border-radius:6px;padding:16px;color:#d1d5db;font-size:13px;line-height:1.6;max-height:150px;overflow-y:auto;border:1px solid #2a2a2a;">';
            html += escape(truncatedText);
            html += '</div>';
            
            if (selectedText.length > 200) {
                html += '<div style="color:#6b7280;font-size:12px;margin-top:8px;text-align:center;">Text truncated for display...</div>';
            }
            
            html += '</div>';
            html += '</div>';
            
            // Info note
            html += '<div style="background:#1a1a1a;border-radius:6px;border:1px solid #2a2a2a;padding:16px;text-align:center;color:#6b7280;font-size:12px;">Select any text on the page and right-click to count words • Press Escape to close this panel</div>';
            
            panel.innerHTML = html;
            document.body.appendChild(panel);
            
            // Add drag functionality
            var isDragging = false;
            var currentX;
            var currentY;
            var initialX;
            var initialY;
            var xOffset = 0;
            var yOffset = 0;
            
            var header = panel.querySelector('#word-counter-header');
            
            function dragStart(e) {
                if (e.target.tagName === 'BUTTON') return; // Don't drag when clicking close button
                
                if (e.type === "touchstart") {
                    initialX = e.touches[0].clientX - xOffset;
                    initialY = e.touches[0].clientY - yOffset;
                } else {
                    initialX = e.clientX - xOffset;
                    initialY = e.clientY - yOffset;
                }
                
                if (e.target === header || header.contains(e.target)) {
                    isDragging = true;
                    panel.style.cursor = 'grabbing';
                    header.style.cursor = 'grabbing';
                }
            }
            
            function dragEnd(e) {
                initialX = currentX;
                initialY = currentY;
                isDragging = false;
                panel.style.cursor = 'default';
                header.style.cursor = 'move';
            }
            
            function drag(e) {
                if (isDragging) {
                    e.preventDefault();
                    
                    if (e.type === "touchmove") {
                        currentX = e.touches[0].clientX - initialX;
                        currentY = e.touches[0].clientY - initialY;
                    } else {
                        currentX = e.clientX - initialX;
                        currentY = e.clientY - initialY;
                    }
                    
                    xOffset = currentX;
                    yOffset = currentY;
                    
                    // Constrain to viewport
                    var rect = panel.getBoundingClientRect();
                    var maxX = window.innerWidth - rect.width;
                    var maxY = window.innerHeight - rect.height;
                    
                    currentX = Math.max(0, Math.min(currentX, maxX));
                    currentY = Math.max(0, Math.min(currentY, maxY));
                    
                    // Clear any existing positioning and use absolute positioning
                    panel.style.right = '';
                    panel.style.left = currentX + 'px';
                    panel.style.top = currentY + 'px';
                }
            }
            
            // Add event listeners for drag functionality
            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);
            
            // Touch events for mobile
            header.addEventListener('touchstart', dragStart);
            document.addEventListener('touchmove', drag);
            document.addEventListener('touchend', dragEnd);
            
            // Get initial position for offset calculation
            var rect = panel.getBoundingClientRect();
            xOffset = rect.left;
            yOffset = rect.top;
            
            // Handle Escape key to close panel
            function handleKeyDown(e) {
                if (e.key === 'Escape') {
                    panel.remove();
                    document.removeEventListener('keydown', handleKeyDown);
                    // Clean up drag event listeners
                    document.removeEventListener('mousemove', drag);
                    document.removeEventListener('mouseup', dragEnd);
                    document.removeEventListener('touchmove', drag);
                    document.removeEventListener('touchend', dragEnd);
                }
            }
            document.addEventListener('keydown', handleKeyDown);
            
            console.log('Word Counter Action executed');
            return { success: true, message: 'Word counter executed successfully', count };
        } catch (error) {
            console.error('Word Counter error:', error);
            return { error: error.message };
        }
    }

    // 🚀 NEW: Quick Tooltip for instant word count on selection + shortcut
    static showQuickTooltip(selectedText) {
        try {
            // Remove any existing quick tooltips
            document.querySelectorAll('.word-counter-quick-tooltip').forEach(tooltip => tooltip.remove());
            
            if (!selectedText || selectedText.trim().length === 0) {
                return { error: 'No text selected' };
            }
            
            const count = this.countText(selectedText);
            
            // Get selection position
            const selection = window.getSelection();
            let tooltipX = 10;
            let tooltipY = 10;
            
            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                const rect = range.getBoundingClientRect();
                
                // Position tooltip above the selection, centered horizontally
                tooltipX = rect.left + (rect.width / 2);
                tooltipY = rect.top - 50; // 50px above the selection (higher)
                
                // Ensure tooltip stays within viewport bounds
                const tooltipWidth = 200; // estimated tooltip width
                const tooltipHeight = 40; // estimated tooltip height
                
                // Adjust horizontal position if too far right
                if (tooltipX + tooltipWidth / 2 > window.innerWidth - 10) {
                    tooltipX = window.innerWidth - tooltipWidth / 2 - 10;
                }
                // Adjust horizontal position if too far left
                if (tooltipX - tooltipWidth / 2 < 10) {
                    tooltipX = tooltipWidth / 2 + 10;
                }
                
                // If tooltip would appear above viewport, show it below selection instead
                if (tooltipY - tooltipHeight < 10) {
                    tooltipY = rect.bottom + 20;
                }
            }
            
            // Create lightweight tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'word-counter-quick-tooltip';
            tooltip.style.cssText = `
                position: fixed;
                left: ${tooltipX}px;
                top: ${tooltipY}px;
                transform: translateX(-50%);
                z-index: 10000000;
                background: rgba(0, 0, 0, 0.9);
                color: #fff;
                padding: 8px 12px;
                border-radius: 6px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                font-size: 13px;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                backdrop-filter: blur(5px);
                border: 1px solid rgba(124, 58, 237, 0.3);
                pointer-events: none;
                animation: wordCounterFadeIn 0.2s ease-out;
                white-space: nowrap;
            `;
            
            // Add CSS animation if not already present
            if (!document.getElementById('word-counter-quick-styles')) {
                const styles = document.createElement('style');
                styles.id = 'word-counter-quick-styles';
                styles.textContent = `
                    @keyframes wordCounterFadeIn {
                        from { 
                            opacity: 0; 
                            transform: translateX(-50%) scale(0.8); 
                        }
                        to { 
                            opacity: 1; 
                            transform: translateX(-50%) scale(1); 
                        }
                    }
                    @keyframes wordCounterFadeOut {
                        from { 
                            opacity: 1; 
                            transform: translateX(-50%) scale(1); 
                        }
                        to { 
                            opacity: 0; 
                            transform: translateX(-50%) scale(0.8); 
                        }
                    }
                `;
                document.head.appendChild(styles);
            }
            
            // Create compact content
            let content = `<span style="color: #7C3AED; font-size: 16px;">●</span> ${count.words} word${count.words !== 1 ? 's' : ''} • ${count.characters} char${count.characters !== 1 ? 's' : ''}`;
            
            // Add sentences if present
            if (count.sentences > 0) {
                content += ` • ${count.sentences} sentence${count.sentences !== 1 ? 's' : ''}`;
            }
            
            tooltip.innerHTML = content;
            document.body.appendChild(tooltip);
            
            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.style.animation = 'wordCounterFadeOut 0.2s ease-in';
                    setTimeout(() => {
                        tooltip.remove();
                    }, 200);
                }
            }, 3000);
            
            console.log('Word Counter Quick Tooltip displayed');
            return { success: true, message: 'Quick tooltip shown', count };
        } catch (error) {
            console.error('Word Counter Quick Tooltip error:', error);
            return { error: error.message };
        }
    }

    // 🎯 Initialize quick shortcut listener (call this once when page loads)
    static initializeQuickShortcut() {
        // Remove existing listener if present
        if (window.wordCounterQuickListener) {
            document.removeEventListener('keydown', window.wordCounterQuickListener);
        }
        
        // Create the shortcut listener
        window.wordCounterQuickListener = function(e) {
            // Trigger on Shift+W (or customize as needed)
            if (e.shiftKey && e.key === 'W') {
                // Get current selection
                const selection = window.getSelection();
                const selectedText = selection.toString().trim();
                
                // ONLY trigger if there is selected text.
                if (selectedText) {
                    e.preventDefault();
                    WordCounterAction.showQuickTooltip(selectedText);
                }
            }
        };
        
        // Add the listener
        document.addEventListener('keydown', window.wordCounterQuickListener);
        
        console.log('Word Counter Quick Shortcut initialized (Shift+W)');
        return { success: true, message: 'Quick shortcut listener added' };
    }

    static reset() {
        try {
            const existingPanel = document.querySelector('.word-counter-panel');
            if (existingPanel) {
                existingPanel.remove();
                console.log('Word Counter panel removed');
            }
            
            // Clean up quick tooltips
            document.querySelectorAll('.word-counter-quick-tooltip').forEach(tooltip => tooltip.remove());
            
            // Clean up quick shortcut listener
            if (window.wordCounterQuickListener) {
                document.removeEventListener('keydown', window.wordCounterQuickListener);
                delete window.wordCounterQuickListener;
                console.log('Word Counter quick shortcut listener removed');
            }
            
            // Clean up any global event listeners that might be left over
            // Note: Individual panel event listeners are cleaned up when the panel is removed
            
            console.log('Word Counter reset completed');
            return { success: true };
        } catch (error) {
            console.error('Word Counter reset error:', error);
            return { error: error.message };
        }
    }

    // Utility function to count words and characters in text
    static countText(text) {
        if (!text || typeof text !== 'string') {
            return { words: 0, characters: 0, charactersNoSpaces: 0, sentences: 0, paragraphs: 0 };
        }

        const trimmedText = text.trim();
        if (trimmedText === '') {
            return { words: 0, characters: 0, charactersNoSpaces: 0, sentences: 0, paragraphs: 0 };
        }

        // Count words - split by whitespace and filter out empty strings
        const words = trimmedText.split(/\s+/).filter(word => word.length > 0);
        const wordCount = words.length;

        // Count characters (including spaces)
        const characterCount = text.length;
        
        // Count characters (excluding spaces)
        const charactersNoSpaces = text.replace(/\s/g, '').length;

        // Count sentences - look for sentence-ending punctuation
        const sentences = text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).length;
        
        // Count paragraphs - split by double line breaks or single line breaks
        const paragraphs = text.split(/\n\s*\n|\n/).filter(paragraph => paragraph.trim().length > 0).length;

        return {
            words: wordCount,
            characters: characterCount,
            charactersNoSpaces: charactersNoSpaces,
            sentences: sentences,
            paragraphs: paragraphs
        };
    }

    // Generate the context menu title with word/character count
    static generateMenuTitle(selectedText) {
        const count = this.countText(selectedText);
        
        if (count.words === 0 && count.characters === 0) {
            return "● Word Counter";
        }

        return `● ${count.words} Word${count.words !== 1 ? 's' : ''} - ${count.characters} Character${count.characters !== 1 ? 's' : ''}`;
    }
}

// Auto-initialize quick shortcut when script loads
if (typeof window !== 'undefined') {
    // Initialize after a brief delay to ensure DOM is ready
    setTimeout(() => {
        WordCounterAction.initializeQuickShortcut();
    }, 100);
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WordCounterAction;
} else {
    window.WordCounterAction = WordCounterAction;
} 