// YouTube Embed URL Scraper Action - Extract YouTube embed URLs, thumbnails, duration, views, and schema markup from channel pages
class YouTubeEmbedScraperAction {
    static execute() {
        try {
            // Inject the YouTube Embed Scraper script into the active tab
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                    target: {tabId: tabs[0].id},
                    func: function() {
                        // The main scraper function to inject
                        (function() {
                            // Create TrustedTypes policy to handle security restrictions
                            if(window.trustedTypes && window.trustedTypes.createPolicy){
                              window.trustedTypes.createPolicy('default',{
                                createHTML: (string) => string,
                                createScriptURL: (string) => string,
                                createScript: (string) => string
                              });
                            }
                          
                            // Process control flag
                            let stopProcessing = false;
                          
                            // Create a global variable to track processing start time
                            let processingStartTime = null;
                            
                            // Batch processing configuration
                            const BATCH_SIZE = 20; // Process 20 videos in parallel
                            const BATCH_DELAY = 500; // Delay between batches in milliseconds
                          
                                                        // Create UI elements
                            const container = document.createElement('div');
                            container.style.position = 'fixed';
                            container.style.top = '10px';
                            container.style.right = '10px';
                            container.style.width = '420px';
                            container.style.maxHeight = '80%';
                            container.style.backgroundColor = '#0f0f0f';
                            container.style.color = '#e5e5e5';
                            container.style.padding = '16px';
                            container.style.zIndex = '9999999';
                            container.style.borderRadius = '12px';
                            container.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.4)';
                            container.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif';
                            container.style.overflow = 'auto';
                            container.style.border = '1px solid #2a2a2a';
                            document.body.appendChild(container);
                            
                            // Handle Escape key to close container
                            function handleKeyDown(e) {
                                if (e.key === 'Escape') {
                                    container.remove();
                                    document.removeEventListener('keydown', handleKeyDown);
                                }
                            }
                            document.addEventListener('keydown', handleKeyDown);
    
                                // Create header
                            const header = document.createElement('h2');
                            header.textContent = 'YouTube Embed URL Scraper - SEO Time Machines';
                            header.style.margin = '0 0 16px 0';
                            header.style.color = '#ffffff';
                            header.style.fontSize = '18px';
                            header.style.fontWeight = '700';
                            header.style.letterSpacing = '-0.5px';
                            container.appendChild(header);
    
                                // Create log area
                            const logContainer = document.createElement('div');
                            logContainer.style.backgroundColor = '#1a1a1a';
                            logContainer.style.padding = '12px';
                            logContainer.style.borderRadius = '8px';
                            logContainer.style.marginBottom = '16px';
                            logContainer.style.maxHeight = '150px';
                            logContainer.style.overflow = 'auto';
                            logContainer.style.fontSize = '12px';
                            logContainer.style.fontFamily = 'monospace';
                            logContainer.style.border = '1px solid #333333';
                            logContainer.style.color = '#d1d5db';
                            container.appendChild(logContainer);
    
    // Create range selector
    const rangeContainer = document.createElement('div');
    rangeContainer.style.marginBottom = '15px';
    rangeContainer.style.display = 'flex';
    rangeContainer.style.flexDirection = 'column';
    rangeContainer.style.gap = '10px';
    
    // Create a container for all ranges
    const rangesContainer = document.createElement('div');
    rangesContainer.id = 'ranges-container';
    rangesContainer.style.display = 'flex';
    rangesContainer.style.flexDirection = 'column';
    rangesContainer.style.gap = '10px';
    rangeContainer.appendChild(rangesContainer);
    
    // Function to create a new range row
    function createRangeRow() {
      const rangeRow = document.createElement('div');
      rangeRow.className = 'range-row';
      rangeRow.style.display = 'flex';
      rangeRow.style.alignItems = 'center';
      rangeRow.style.gap = '10px';
      
                                  const rangeLabel = document.createElement('label');
                            rangeLabel.textContent = 'Process videos:';
                            rangeLabel.style.color = '#e5e5e5';
                            rangeLabel.style.width = '100px';
                            rangeLabel.style.fontSize = '13px';
                            rangeLabel.style.fontWeight = '500';
      
                                  const startInput = document.createElement('input');
                            startInput.type = 'number';
                            startInput.min = '1';
                            startInput.value = '1';
                            startInput.className = 'range-start';
                            startInput.style.width = '60px';
                            startInput.style.padding = '6px 8px';
                            startInput.style.borderRadius = '6px';
                            startInput.style.border = '1px solid #333333';
                            startInput.style.backgroundColor = '#1a1a1a';
                            startInput.style.color = '#e5e5e5';
                            startInput.style.fontSize = '13px';
      
                                  const rangeText = document.createElement('span');
                            rangeText.textContent = 'to';
                            rangeText.style.color = '#e5e5e5';
                            rangeText.style.fontSize = '13px';
      
                                  const endInput = document.createElement('input');
                            endInput.type = 'number';
                            endInput.min = '1';
                            endInput.value = '10';
                            endInput.className = 'range-end';
                            endInput.style.width = '60px';
                            endInput.style.padding = '6px 8px';
                            endInput.style.borderRadius = '6px';
                            endInput.style.border = '1px solid #333333';
                            endInput.style.backgroundColor = '#1a1a1a';
                            endInput.style.color = '#e5e5e5';
                            endInput.style.fontSize = '13px';
      
                                  const removeButton = document.createElement('button');
                            removeButton.textContent = '✕';
                            removeButton.style.padding = '5px 10px';
                            removeButton.style.backgroundColor = '#ef4444';
                            removeButton.style.color = '#ffffff';
                            removeButton.style.border = '1px solid #dc2626';
                            removeButton.style.borderRadius = '6px';
                            removeButton.style.cursor = 'pointer';
                            removeButton.style.display = document.querySelectorAll('.range-row').length > 0 ? 'block' : 'none';
                            removeButton.style.fontSize = '12px';
                            removeButton.style.fontWeight = '600';
      
      removeButton.addEventListener('click', () => {
        if (document.querySelectorAll('.range-row').length > 1) {
          rangesContainer.removeChild(rangeRow);
        }
      });
      
      rangeRow.appendChild(rangeLabel);
      rangeRow.appendChild(startInput);
      rangeRow.appendChild(rangeText);
      rangeRow.appendChild(endInput);
      rangeRow.appendChild(removeButton);
      
      return rangeRow;
    }
    
    // Add first range row
    rangesContainer.appendChild(createRangeRow());
    
                                // Add range button
                            const addRangeButton = document.createElement('button');
                            addRangeButton.textContent = '+ Add Another Range';
                            addRangeButton.style.padding = '8px 15px';
                            addRangeButton.style.backgroundColor = '#262626';
                            addRangeButton.style.color = '#ffffff';
                            addRangeButton.style.border = '1px solid #333333';
                            addRangeButton.style.borderRadius = '8px';
                            addRangeButton.style.cursor = 'pointer';
                            addRangeButton.style.alignSelf = 'flex-start';
                            addRangeButton.style.fontSize = '13px';
                            addRangeButton.style.fontWeight = '600';
                            addRangeButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
    
    addRangeButton.addEventListener('click', () => {
      const newRow = createRangeRow();
      rangesContainer.appendChild(newRow);
      
      // Show remove buttons when we have more than one range
      document.querySelectorAll('.range-row button').forEach(btn => {
        btn.style.display = 'block';
      });
    });
    
    rangeContainer.appendChild(addRangeButton);
    container.appendChild(rangeContainer);
    
    // Create batch size configuration
    const batchContainer = document.createElement('div');
    batchContainer.style.marginBottom = '15px';
    batchContainer.style.display = 'flex';
    batchContainer.style.alignItems = 'center';
    batchContainer.style.gap = '10px';
    
    const batchLabel = document.createElement('label');
    batchLabel.textContent = 'Parallel batch size:';
    batchLabel.style.color = '#e5e5e5';
    batchLabel.style.fontSize = '13px';
    batchLabel.style.fontWeight = '500';
    batchLabel.style.width = '130px';
    
    const batchSizeInput = document.createElement('input');
    batchSizeInput.type = 'number';
    batchSizeInput.min = '1';
    batchSizeInput.max = '25';
    batchSizeInput.value = BATCH_SIZE.toString();
    batchSizeInput.id = 'batch-size-input';
    batchSizeInput.style.width = '60px';
    batchSizeInput.style.padding = '6px 8px';
    batchSizeInput.style.borderRadius = '6px';
    batchSizeInput.style.border = '1px solid #333333';
    batchSizeInput.style.backgroundColor = '#1a1a1a';
    batchSizeInput.style.color = '#e5e5e5';
    batchSizeInput.style.fontSize = '13px';
    
    const batchInfo = document.createElement('span');
    batchInfo.textContent = '(Recommended: 10-20 for best performance)';
    batchInfo.style.color = '#888';
    batchInfo.style.fontSize = '11px';
    batchInfo.style.fontStyle = 'italic';
    
    batchContainer.appendChild(batchLabel);
    batchContainer.appendChild(batchSizeInput);
    batchContainer.appendChild(batchInfo);
    container.appendChild(batchContainer);
    
                                // Create numbering button
                            const numberButton = document.createElement('button');
                            numberButton.textContent = 'Number Videos';
                            numberButton.style.padding = '10px 16px';
                            numberButton.style.backgroundColor = '#262626';
                            numberButton.style.color = '#ffffff';
                            numberButton.style.border = '1px solid #333333';
                            numberButton.style.borderRadius = '8px';
                            numberButton.style.cursor = 'pointer';
                            numberButton.style.marginBottom = '16px';
                            numberButton.style.marginRight = '10px';
                            numberButton.style.fontSize = '13px';
                            numberButton.style.fontWeight = '600';
                            numberButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                            container.appendChild(numberButton);
    
                                // Create start button
                            const startButton = document.createElement('button');
                            startButton.textContent = 'Extract Selected Range';
                            startButton.style.padding = '10px 16px';
                            startButton.style.background = '#010101';
                            startButton.style.color = '#ffffff';
                            startButton.style.border = '1px solid #8b5cf6';
                            startButton.style.borderRadius = '8px';
                            startButton.style.cursor = 'pointer';
                            startButton.style.marginBottom = '16px';
                            startButton.style.fontSize = '13px';
                            startButton.style.fontWeight = '600';
                            startButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                            container.appendChild(startButton);
    
    // Create table container
    const tableContainer = document.createElement('div');
    tableContainer.style.marginBottom = '15px';
    tableContainer.style.maxHeight = '300px';
    tableContainer.style.overflow = 'auto';
    container.appendChild(tableContainer);
    
    // Create table
    const table = document.createElement('table');
    table.style.width = '100%';
    table.style.borderCollapse = 'collapse';
    tableContainer.appendChild(table);
    
    // Create table header
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    const selectHeader = document.createElement('th');
    selectHeader.textContent = 'Select';
    selectHeader.style.textAlign = 'left';
    selectHeader.style.padding = '8px';
    selectHeader.style.borderBottom = '1px solid #444';
    
    const titleHeader = document.createElement('th');
    titleHeader.textContent = 'Video Title';
    titleHeader.style.textAlign = 'left';
    titleHeader.style.padding = '8px';
    titleHeader.style.borderBottom = '1px solid #444';
    
    const urlHeader = document.createElement('th');
    urlHeader.textContent = 'Embed URL';
    urlHeader.style.textAlign = 'left';
    urlHeader.style.padding = '8px';
    urlHeader.style.borderBottom = '1px solid #444';
    
    const thumbnailHeader = document.createElement('th');
    thumbnailHeader.textContent = 'Thumbnail URL';
    thumbnailHeader.style.textAlign = 'left';
    thumbnailHeader.style.padding = '8px';
    thumbnailHeader.style.borderBottom = '1px solid #444';
    
    const durationFullHeader = document.createElement('th');
    durationFullHeader.textContent = 'Duration Full';
    durationFullHeader.style.textAlign = 'left';
    durationFullHeader.style.padding = '8px';
    durationFullHeader.style.borderBottom = '1px solid #444';
    
    const durationTimeHeader = document.createElement('th');
    durationTimeHeader.textContent = 'Duration Time';
    durationTimeHeader.style.textAlign = 'left';
    durationTimeHeader.style.padding = '8px';
    durationTimeHeader.style.borderBottom = '1px solid #444';
    
    const durationSecondsHeader = document.createElement('th');
    durationSecondsHeader.textContent = 'Duration Seconds';
    durationSecondsHeader.style.textAlign = 'left';
    durationSecondsHeader.style.padding = '8px';
    durationSecondsHeader.style.borderBottom = '1px solid #444';
    
    const viewsHeader = document.createElement('th');
    viewsHeader.textContent = 'Views';
    viewsHeader.style.textAlign = 'left';
    viewsHeader.style.padding = '8px';
    viewsHeader.style.borderBottom = '1px solid #444';
    
    const schemaHeader = document.createElement('th');
    schemaHeader.textContent = 'Schema';
    schemaHeader.style.textAlign = 'left';
    schemaHeader.style.padding = '8px';
    schemaHeader.style.borderBottom = '1px solid #444';
    
    headerRow.appendChild(selectHeader);
    headerRow.appendChild(titleHeader);
    headerRow.appendChild(urlHeader);
    headerRow.appendChild(thumbnailHeader);
    headerRow.appendChild(durationFullHeader);
    headerRow.appendChild(durationTimeHeader);
    headerRow.appendChild(durationSecondsHeader);
    headerRow.appendChild(viewsHeader);
    headerRow.appendChild(schemaHeader);
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // Create table body
    const tbody = document.createElement('tbody');
    table.appendChild(tbody);
    
    // Create "Select All" checkbox
    const selectAllContainer = document.createElement('div');
    selectAllContainer.style.marginBottom = '10px';
    const selectAllCheckbox = document.createElement('input');
    selectAllCheckbox.type = 'checkbox';
    selectAllCheckbox.checked = true;
    selectAllCheckbox.id = 'select-all';
                                const selectAllLabel = document.createElement('label');
                            selectAllLabel.textContent = 'Select All';
                            selectAllLabel.style.color = '#e5e5e5';
                            selectAllLabel.style.marginLeft = '5px';
                            selectAllLabel.htmlFor = 'select-all';
                            selectAllLabel.style.fontSize = '13px';
                            selectAllLabel.style.fontWeight = '500';
    selectAllContainer.appendChild(selectAllCheckbox);
    selectAllContainer.appendChild(selectAllLabel);
    container.insertBefore(selectAllContainer, tableContainer);
    
    // Create button container
    const buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.justifyContent = 'space-between';
    container.appendChild(buttonContainer);
    
                                // Create export button
                            const exportButton = document.createElement('button');
                            exportButton.textContent = 'Export to CSV';
                            exportButton.style.padding = '8px 15px';
                            exportButton.style.backgroundColor = '#262626';
                            exportButton.style.color = '#ffffff';
                            exportButton.style.border = '1px solid #333333';
                            exportButton.style.borderRadius = '8px';
                            exportButton.style.cursor = 'pointer';
                            exportButton.style.fontSize = '13px';
                            exportButton.style.fontWeight = '600';
                            exportButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                            buttonContainer.appendChild(exportButton);
    
                                // Create stop button
                            const stopButton = document.createElement('button');
                            stopButton.textContent = 'Stop Process';
                            stopButton.style.padding = '8px 15px';
                            stopButton.style.backgroundColor = '#262626';
                            stopButton.style.color = '#ffffff';
                            stopButton.style.border = '1px solid #333333';
                            stopButton.style.borderRadius = '8px';
                            stopButton.style.cursor = 'pointer';
                            stopButton.style.fontSize = '13px';
                            stopButton.style.fontWeight = '600';
                            stopButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                            buttonContainer.appendChild(stopButton);
    
                                // Create close button
                            const closeButton = document.createElement('button');
                            closeButton.textContent = 'Close';
                            closeButton.style.padding = '8px 15px';
                            closeButton.style.backgroundColor = '#010101';
                            closeButton.style.color = '#ffffff';
                            closeButton.style.border = '1px solid #dc2626';
                            closeButton.style.borderRadius = '8px';
                            closeButton.style.cursor = 'pointer';
                            closeButton.style.fontSize = '13px';
                            closeButton.style.fontWeight = '600';
                            closeButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                            buttonContainer.appendChild(closeButton);
    
    // Logger function
    function log(message) {
      console.log(message);
      const logItem = document.createElement('div');
      logItem.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
      logContainer.appendChild(logItem);
      logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    // Add video data to table
    function addVideoToTable(index, title, url, shareUrl, fullUrl, thumbnailUrl, durationFull, durationTime, durationSeconds, viewCount, schema) {
      const row = document.createElement('tr');
      row.style.borderBottom = '1px solid #333';
      
      const selectCell = document.createElement('td');
      selectCell.style.padding = '8px';
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.checked = true;
      checkbox.className = 'video-select';
      checkbox.dataset.index = index;
      selectCell.appendChild(checkbox);
      
      const titleCell = document.createElement('td');
      titleCell.textContent = `#${index}: ${title}`;
      titleCell.style.padding = '8px';
      
      const urlCell = document.createElement('td');
      const urlText = document.createElement('div');
      urlText.textContent = url;
      urlText.style.maxWidth = '180px';
      urlText.style.overflow = 'hidden';
      urlText.style.textOverflow = 'ellipsis';
      urlText.style.whiteSpace = 'nowrap';
      urlText.title = url; // Show full URL on hover
      if (shareUrl) urlText.dataset.shareUrl = shareUrl;
      if (fullUrl) urlText.dataset.fullUrl = fullUrl;
      urlCell.style.padding = '8px';
      urlCell.appendChild(urlText);
      
      const thumbnailCell = document.createElement('td');
      const thumbnailText = document.createElement('div');
      thumbnailText.textContent = thumbnailUrl;
      thumbnailText.style.maxWidth = '180px';
      thumbnailText.style.overflow = 'hidden';
      thumbnailText.style.textOverflow = 'ellipsis';
      thumbnailText.style.whiteSpace = 'nowrap';
      thumbnailText.title = thumbnailUrl; // Show full URL on hover
      thumbnailText.dataset.thumbnailUrl = thumbnailUrl;
      thumbnailCell.style.padding = '8px';
      thumbnailCell.appendChild(thumbnailText);
      
      const durationFullCell = document.createElement('td');
      const durationFullText = document.createElement('div');
      durationFullText.textContent = durationFull;
      durationFullText.style.maxWidth = '150px';
      durationFullText.style.overflow = 'hidden';
      durationFullText.style.textOverflow = 'ellipsis';
      durationFullText.style.whiteSpace = 'nowrap';
      durationFullText.title = durationFull; // Show full text on hover
      durationFullText.dataset.durationFull = durationFull;
      durationFullCell.style.padding = '8px';
      durationFullCell.appendChild(durationFullText);
      
      const durationTimeCell = document.createElement('td');
      const durationTimeText = document.createElement('div');
      durationTimeText.textContent = durationTime;
      durationTimeText.style.maxWidth = '80px';
      durationTimeText.style.overflow = 'hidden';
      durationTimeText.style.textOverflow = 'ellipsis';
      durationTimeText.style.whiteSpace = 'nowrap';
      durationTimeText.title = durationTime; // Show full text on hover
      durationTimeText.dataset.durationTime = durationTime;
      durationTimeCell.style.padding = '8px';
      durationTimeCell.appendChild(durationTimeText);
      
      const durationSecondsCell = document.createElement('td');
      const durationSecondsText = document.createElement('div');
      durationSecondsText.textContent = durationSeconds;
      durationSecondsText.style.maxWidth = '80px';
      durationSecondsText.style.overflow = 'hidden';
      durationSecondsText.style.textOverflow = 'ellipsis';
      durationSecondsText.style.whiteSpace = 'nowrap';
      durationSecondsText.title = durationSeconds; // Show full text on hover
      durationSecondsText.dataset.durationSeconds = durationSeconds;
      durationSecondsCell.style.padding = '8px';
      durationSecondsCell.appendChild(durationSecondsText);
      
      const viewCountCell = document.createElement('td');
      const viewCountText = document.createElement('div');
      viewCountText.textContent = viewCount;
      viewCountText.style.maxWidth = '100px';
      viewCountText.style.overflow = 'hidden';
      viewCountText.style.textOverflow = 'ellipsis';
      viewCountText.style.whiteSpace = 'nowrap';
      viewCountText.title = viewCount; // Show full text on hover
      viewCountText.dataset.viewCount = viewCount;
      viewCountCell.style.padding = '8px';
      viewCountCell.appendChild(viewCountText);
      
      const schemaCell = document.createElement('td');
      const schemaText = document.createElement('div');
      if (!schema) {
        schemaText.textContent = 'Not Available';
        schemaText.title = 'Schema not generated';
      } else if (schema === 'API_KEY_NOT_FOUND') {
        schemaText.textContent = 'API Key not found';
        schemaText.title = 'Configure YouTube API key in General Settings to enable schema generation';
      } else {
        schemaText.textContent = 'Generated';
        schemaText.title = schema.substring(0, 200) + '...';
      }
      schemaText.style.maxWidth = '120px';
      schemaText.style.overflow = 'hidden';
      schemaText.style.textOverflow = 'ellipsis';
      schemaText.style.whiteSpace = 'nowrap';
      schemaText.dataset.schema = schema || '';
      schemaCell.style.padding = '8px';
      schemaCell.appendChild(schemaText);
      
      row.appendChild(selectCell);
      row.appendChild(titleCell);
      row.appendChild(urlCell);
      row.appendChild(thumbnailCell);
      row.appendChild(durationFullCell);
      row.appendChild(durationTimeCell);
      row.appendChild(durationSecondsCell);
      row.appendChild(viewCountCell);
      row.appendChild(schemaCell);
      tbody.appendChild(row);
    }
    
    // Sleep function
    function sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Wait for element to appear
    function waitForElement(selector, timeout=10000) {
      log(`Waiting for: ${selector}`);
      return new Promise((resolve, reject) => {
        if(document.querySelector(selector)) {
          return resolve(document.querySelector(selector));
        }
        
        const observer = new MutationObserver(() => {
          if(document.querySelector(selector)) {
            observer.disconnect();
            resolve(document.querySelector(selector));
          }
        });
        
        observer.observe(document.body, {childList: true, subtree: true});
        
        setTimeout(() => {
          observer.disconnect();
          reject(new Error(`Timeout waiting for ${selector}`));
        }, timeout);
      });
    }
    
    // Click element with visual feedback and focus handling
    async function clickElement(element, description) {
      log(`Clicking: ${description}`);
      if(!element) {
        log(`ERROR: Element not found - ${description}`);
        return false;
      }
      
      try {
        // Make sure the window is focused
        window.focus();
        
        // Scroll element into view with offset to ensure visibility
        const rect = element.getBoundingClientRect();
        const isInViewport = (
          rect.top >= 0 &&
          rect.left >= 0 &&
          rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
          rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
        
        if (!isInViewport) {
          element.scrollIntoView({behavior: 'instant', block: 'center'});
          await sleep(300);
        }
        
        // Visual highlight
        const originalBackground = element.style.backgroundColor;
        element.style.transition = 'background-color 0.3s';
        element.style.backgroundColor = 'rgba(255,0,0,0.3)';
        
        // Try multiple click methods for better reliability
        try {
          // Method 1: Standard click
          element.click();
        } catch(e) {
          log(`Standard click failed, trying alternate methods: ${e.message}`);
          
          // Method 2: MouseEvent
          try {
            const evt = new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
              view: window
            });
            element.dispatchEvent(evt);
          } catch(e2) {
            log(`MouseEvent failed: ${e2.message}`);
            
            // Method 3: Direct event handler call if available
            if (typeof element.onclick === 'function') {
              element.onclick();
            }
          }
        }
        
        await sleep(1500); // Required wait
        element.style.backgroundColor = originalBackground;
        return true;
      } catch(error) {
        log(`ERROR: Failed to click ${description}: ${error.message}`);
        return false;
      }
    }
    
    // Find and click the three dots menu
    async function clickThreeDots(videoElement) {
      log('Looking for three dots menu button...');
      try {
        // Find the three dots button inside the current video element
        const threeDots = videoElement.querySelector('ytd-menu-renderer button');
        if(!threeDots) {
          log('ERROR: Three dots button not found');
          return false;
        }
        return await clickElement(threeDots, 'Three dots menu button');
      } catch(error) {
        log(`ERROR: Failed to find three dots: ${error.message}`);
        return false;
      }
    }
    
    // Find and click the Share button
    async function clickShareButton() {
      log('Looking for Share button...');
      try {
        await sleep(1000); // Wait for menu to appear
        // Use document-wide selector since the popup might not be a direct child
        const shareItems = Array.from(document.querySelectorAll('ytd-menu-service-item-renderer'));
        // Find the one with "Share" text
        const shareButton = shareItems.find(item => {
          const text = item.textContent.trim();
          return text.includes('Share');
        });
        
        if(!shareButton) {
          log('ERROR: Share button not found');
          return false;
        }
        
        return await clickElement(shareButton, 'Share button');
      } catch(error) {
        log(`ERROR: Failed to find Share button: ${error.message}`);
        return false;
      }
    }
    
    // Find and click the Embed button
    async function clickEmbedButton() {
      log('Looking for Embed button...');
      try {
        await sleep(1000); // Wait for share dialog
        
        // Try multiple selectors to find the Embed button
        let embedButton = null;
        
        // Method 1: Try to find by title attribute
        const buttonsByTitle = Array.from(document.querySelectorAll('button[title="Embed"]'));
        if(buttonsByTitle.length > 0) {
          embedButton = buttonsByTitle[0];
          log('Found Embed button by title attribute');
        }
        
        // Method 2: Try to find by id
        if(!embedButton) {
          const buttonsById = Array.from(document.querySelectorAll('button#target'));
          const embedButtonById = buttonsById.find(btn => {
            return btn.textContent.trim().includes('Embed');
          });
          
          if(embedButtonById) {
            embedButton = embedButtonById;
            log('Found Embed button by id="target"');
          }
        }
        
        // Method 3: Try to find by parent class and text
        if(!embedButton) {
          const shareTargets = Array.from(document.querySelectorAll('.style-scope.yt-share-target-renderer'));
          for(const target of shareTargets) {
            if(target.tagName === 'BUTTON' && target.textContent.trim().includes('Embed')) {
              embedButton = target;
              log('Found Embed button by class and text');
              break;
            }
          }
        }
        
        // Method 4: Find by parent element then look for Embed text
        if(!embedButton) {
          const shareRenderer = document.querySelector('yt-share-panel-renderer');
          if(shareRenderer) {
            const allButtons = shareRenderer.querySelectorAll('button');
            const found = Array.from(allButtons).find(btn => {
              return btn.textContent.trim().includes('Embed') || 
                    (btn.title && btn.title.includes('Embed'));
            });
            
            if(found) {
              embedButton = found;
              log('Found Embed button within share panel');
            }
          }
        }
        
        // Last resort: any button with Embed text
        if(!embedButton) {
          const allButtons = document.querySelectorAll('button');
          const embedButtons = Array.from(allButtons).filter(btn => {
            const text = btn.textContent.trim();
            const title = btn.title || '';
            return text.includes('Embed') || title.includes('Embed');
          });
          
          if(embedButtons.length > 0) {
            embedButton = embedButtons[0];
            log('Found Embed button as last resort');
          }
        }
        
        if(!embedButton) {
          log('ERROR: Embed button not found after trying multiple methods');
          // Log all buttons for debugging
          const allButtons = document.querySelectorAll('button');
          log(`Found ${allButtons.length} buttons in total`);
          Array.from(allButtons).forEach((btn, i) => {
            if(i < 10) { // Log only first 10 to avoid flooding
              log(`Button ${i}: Text="${btn.textContent.trim()}", Title="${btn.title || 'none'}", ID="${btn.id || 'none'}"`);
            }
          });
          return false;
        }
        
        return await clickElement(embedButton, 'Embed button');
      } catch(error) {
        log(`ERROR: Failed to find Embed button: ${error.message}`);
        return false;
      }
    }
    
    // Extract embed URL from the iframe code
    async function extractEmbedUrl() {
      log('Extracting embed URL...');
      try {
        await sleep(1000); // Wait for embed code to appear
        
        // Find the textarea with the iframe code
        const embedCodeElement = document.querySelector('#embed-code, tp-yt-paper-textarea#embed-code');
        
        if(!embedCodeElement) {
          log('ERROR: Embed code element not found');
          return null;
        }
        
        // Find mirror element which contains the iframe code
        const mirrorElement = document.querySelector('#mirror');
        let iframeCode = '';
        
        if(mirrorElement) {
          iframeCode = mirrorElement.textContent;
        } else if(embedCodeElement.value) {
          iframeCode = embedCodeElement.value;
        } else {
          log('ERROR: Could not find iframe code');
          return null;
        }
        
        // Extract URL from iframe code using regex
        const urlMatch = iframeCode.match(/src="(https:\/\/www\.youtube\.com\/embed\/[^"]+)"/);
        if(urlMatch && urlMatch[1]) {
          log(`Found embed URL: ${urlMatch[1]}`);
          return urlMatch[1];
        }
        
        log('ERROR: Could not extract URL from iframe code');
        return null;
      } catch(error) {
        log(`ERROR: Failed to extract embed URL: ${error.message}`);
        return null;
      }
    }
    
    // Close the current dialog
    async function closeDialog() {
      log('Closing dialog...');
      try {
        const closeButtons = document.querySelectorAll('button[aria-label="Close"]');
        if(closeButtons.length > 0) {
          // Click the last one (most likely the current dialog)
          await clickElement(closeButtons[closeButtons.length-1], 'Close dialog button');
          return true;
        }
        
        log('WARNING: No close button found');
        return false;
      } catch(error) {
        log(`ERROR: Failed to close dialog: ${error.message}`);
        return false;
      }
    }
    
    // Get video title
    function getVideoTitle(videoElement) {
      try {
        const titleElement = videoElement.querySelector('#video-title');
        if(titleElement) {
          return titleElement.textContent.trim();
        }
        return 'Unknown Title';
      } catch(error) {
        log(`ERROR: Failed to get video title: ${error.message}`);
        return 'Unknown Title';
      }
    }
    
                                // Add number overlay to video
                            function addNumberOverlay(videoElement, index) {
                              try {
                                // Check if already numbered
                                if(videoElement.querySelector('.yt-embed-scraper-number')) {
                                  return;
                                }
                                
                                // Create number badge with purple accent color
                                const badge = document.createElement('div');
                                badge.className = 'yt-embed-scraper-number';
                                badge.textContent = index;
                                badge.style.position = 'absolute';
                                badge.style.top = '5px';
                                badge.style.left = '5px';
                                badge.style.background = 'linear-gradient(135deg, #7c3aed, #a855f7)';
                                badge.style.color = '#ffffff';
                                badge.style.padding = '4px 8px';
                                badge.style.borderRadius = '6px';
                                badge.style.fontWeight = 'bold';
                                badge.style.fontSize = '14px';
                                badge.style.zIndex = '999';
                                badge.style.border = '1px solid #8b5cf6';
                                badge.style.boxShadow = '0 2px 8px rgba(139, 92, 246, 0.3)';
        
        // Find the thumbnail
        const thumbnail = videoElement.querySelector('#thumbnail');
        if(thumbnail) {
          // Make sure thumbnail has position relative for absolute positioning of badge
          if(window.getComputedStyle(thumbnail).position !== 'relative') {
            thumbnail.style.position = 'relative';
          }
          thumbnail.appendChild(badge);
        }
      } catch (error) {
        console.error('Error adding number overlay:', error);
      }
    }
    
    // Number all videos
    function numberVideos() {
      log('Numbering all visible videos...');
      const videoElements = document.querySelectorAll('ytd-rich-item-renderer, ytd-grid-video-renderer, ytd-video-renderer');
      
      if(videoElements.length === 0) {
        log('No videos found on page');
        return;
      }
      
      log(`Found ${videoElements.length} videos`);
      
      // Update the end input value to match total videos for the first range row
      const firstEndInput = document.querySelector('.range-end');
      if (firstEndInput) {
        firstEndInput.value = videoElements.length;
      }
      
      // Add number overlay to each video
      videoElements.forEach((video, index) => {
        addNumberOverlay(video, index + 1);
      });
      
      log('All videos numbered');
    }
    
    // Extract video ID from URL
    function extractVideoId(videoElement) {
      try {
        // First method: Try to get from thumbnail URL
        const thumbnail = videoElement.querySelector('#thumbnail');
        if (thumbnail && thumbnail.href) {
          const match = thumbnail.href.match(/(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([^&]+)/);
          if (match) return match[1];
        }
        
        // Second method: Try to find in video-id attribute
        const videoIdAttr = videoElement.querySelector('[video-id]');
        if (videoIdAttr && videoIdAttr.getAttribute('video-id')) {
          return videoIdAttr.getAttribute('video-id');
        }
        
        // Third method: Try to find in any href attribute
        const links = videoElement.querySelectorAll('a[href*="watch?v="]');
        for (const link of links) {
          const match = link.href.match(/(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([^&]+)/);
          if (match) return match[1];
        }
        
        return null;
      } catch (error) {
        log(`ERROR: Failed to extract video ID: ${error.message}`);
        return null;
      }
    }
    
    // Generate embed URL directly from video element
    function generateEmbedUrl(videoElement) {
      const videoId = extractVideoId(videoElement);
      if (videoId) {
        return `https://www.youtube.com/embed/${videoId}`;
      }
      return null;
    }
    
    // Generate thumbnail URL from video ID
    function generateThumbnailUrl(videoId) {
      if (videoId) {
        return `https://i.ytimg.com/vi/${videoId}/maxresdefault.jpg`;
      }
      return '';
    }

    // Generate JSON-LD schema markup using YouTube Data API v3
    async function generateYouTubeSchema(videoId) {
      try {
        // Check if we have an API key using chrome.storage.local for cross-context access
        const result = await chrome.storage.local.get(['youtubeApiKey']);
        const apiKey = result.youtubeApiKey;
        if (!apiKey) {
          log('YouTube API key not found - skipping schema generation');
          return 'API_KEY_NOT_FOUND';
        }

        // Make API request to YouTube Data API v3
        const apiUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics,contentDetails&id=${videoId}&key=${apiKey}`;
        
        const response = await fetch(apiUrl);
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        if (!data.items || data.items.length === 0) {
          log(`No video data found for ID: ${videoId}`);
          return '';
        }

        const video = data.items[0];
        const snippet = video.snippet;
        const statistics = video.statistics;
        const contentDetails = video.contentDetails;

        // Parse ISO 8601 duration to seconds
        function parseISO8601Duration(duration) {
          const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
          if (!match) return 0;
          const hours = parseInt(match[1]) || 0;
          const minutes = parseInt(match[2]) || 0;
          const seconds = parseInt(match[3]) || 0;
          return hours * 3600 + minutes * 60 + seconds;
        }

        const durationInSeconds = parseISO8601Duration(contentDetails.duration);

        // Create JSON-LD schema markup
        const schema = {
          "@context": "https://schema.org",
          "@type": "VideoObject",
          "name": snippet.title,
          "description": snippet.description || "",
          "thumbnailUrl": [
            `https://i.ytimg.com/vi/${videoId}/maxresdefault.jpg`,
            `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`
          ],
          "uploadDate": snippet.publishedAt,
          "duration": contentDetails.duration,
          "embedUrl": `https://www.youtube.com/embed/${videoId}`,
          "interactionStatistic": [
            {
              "@type": "InteractionCounter",
              "interactionType": { "@type": "WatchAction" },
              "userInteractionCount": parseInt(statistics.viewCount) || 0
            },
            {
              "@type": "InteractionCounter", 
              "interactionType": { "@type": "LikeAction" },
              "userInteractionCount": parseInt(statistics.likeCount) || 0
            }
          ],
          "author": {
            "@type": "Person",
            "name": snippet.channelTitle,
            "url": `https://www.youtube.com/channel/${snippet.channelId}`
          }
        };

        // Add additional properties if available
        if (snippet.tags && snippet.tags.length > 0) {
          schema.keywords = snippet.tags.join(', ');
        }

        if (durationInSeconds > 0) {
          schema.timeRequired = `PT${durationInSeconds}S`;
        }

        // Return JSON wrapped in script tags for HTML header usage
        const schemaJson = JSON.stringify(schema, null, 4);
        const wrappedSchema = `<script type="application/ld+json">${schemaJson}</script>`;
        log(`Generated schema for video ${videoId} (${wrappedSchema.length} characters)`);
        return wrappedSchema;

      } catch (error) {
        log(`ERROR generating schema for video ${videoId}: ${error.message}`);
        return '';
      }
    }
    
    // Extract duration data in 3 formats from video element
    function extractDurationData(videoElement) {
      try {
        const durationElement = videoElement.querySelector('.ytd-thumbnail-overlay-time-status-renderer #text');
        if (!durationElement) {
          return {
            durationFull: '',
            durationTime: '',
            durationSeconds: ''
          };
        }
        
        const durationFull = durationElement.getAttribute('aria-label') || '';
        const durationTime = durationElement.textContent.trim() || '';
        
        // Convert time to seconds
        let durationSeconds = '';
        if (durationTime) {
          const parts = durationTime.split(':');
          if (parts.length === 2) {
            // MM:SS format
            const minutes = parseInt(parts[0]) || 0;
            const seconds = parseInt(parts[1]) || 0;
            durationSeconds = (minutes * 60 + seconds).toString();
          } else if (parts.length === 3) {
            // HH:MM:SS format
            const hours = parseInt(parts[0]) || 0;
            const minutes = parseInt(parts[1]) || 0;
            const seconds = parseInt(parts[2]) || 0;
            durationSeconds = (hours * 3600 + minutes * 60 + seconds).toString();
          }
        }
        
        return {
          durationFull: durationFull,
          durationTime: durationTime,
          durationSeconds: durationSeconds
        };
      } catch (error) {
        log(`ERROR: Failed to extract duration: ${error.message}`);
        return {
          durationFull: '',
          durationTime: '',
          durationSeconds: ''
        };
      }
    }
    
    // Extract view count from video element
    function extractViewCount(videoElement) {
      try {
        const viewElement = videoElement.querySelector('.inline-metadata-item.style-scope.ytd-video-meta-block');
        if (!viewElement) {
          return '';
        }
        
        const viewText = viewElement.textContent.trim();
        if (!viewText) {
          return '';
        }
        
        // Extract number from text like "58 views", "1.2K views", "1M views"
        const match = viewText.match(/^([\d.]+)([KMB]?)\s+views?/i);
        if (!match) {
          return '';
        }
        
        let number = parseFloat(match[1]);
        const suffix = match[2].toUpperCase();
        
        // Convert K/M/B to actual numbers
        switch (suffix) {
          case 'K':
            number = Math.round(number * 1000);
            break;
          case 'M':
            number = Math.round(number * 1000000);
            break;
          case 'B':
            number = Math.round(number * 1000000000);
            break;
        }
        
        return Math.round(number).toString();
      } catch (error) {
        log(`ERROR: Failed to extract view count: ${error.message}`);
        return '';
      }
    }
    
    // Try direct extraction without UI interaction (parallel-safe)
    async function tryDirectExtraction(videoElement, index) {
      try {
        log(`Direct extraction for video ${index}...`);
        
        const videoTitle = getVideoTitle(videoElement);
        const directEmbed = generateEmbedUrl(videoElement);
        
        if (directEmbed) {
          const videoId = extractVideoId(videoElement);
          const shareUrl = videoId ? `https://youtu.be/${videoId}` : '';
          const fullUrl = videoId ? `https://www.youtube.com/watch?v=${videoId}` : '';
          const thumbnailUrl = generateThumbnailUrl(videoId);
          const durationData = extractDurationData(videoElement);
          const viewCount = extractViewCount(videoElement);
          const schema = videoId ? await generateYouTubeSchema(videoId) : '';
          
          log(`Direct extraction successful for video ${index}: ${directEmbed}`);
          
          return {
            success: true,
            index: index,
            title: videoTitle,
            embedUrl: directEmbed,
            shareUrl: shareUrl,
            fullUrl: fullUrl,
            thumbnailUrl: thumbnailUrl,
            durationFull: durationData.durationFull,
            durationTime: durationData.durationTime,
            durationSeconds: durationData.durationSeconds,
            viewCount: viewCount,
            schema: schema
          };
        }
        
        log(`Direct extraction failed for video ${index}, will need UI method`);
        return { success: false, index: index, videoElement: videoElement, title: videoTitle };
      } catch (error) {
        log(`ERROR in direct extraction for video ${index}: ${error.message}`);
        return { success: false, index: index, videoElement: videoElement, title: getVideoTitle(videoElement) };
      }
    }
    
    // Try UI-based extraction for videos that failed direct method (sequential only)
    async function tryUIExtraction(videoElement, index, videoTitle) {
      // Check if processing should stop
      if(stopProcessing) {
        log('Processing stopped by user');
        return null;
      }
      
      log(`UI extraction for video ${index}...`);
      
      // Click three dots
      if(!await clickThreeDots(videoElement)) {
        return null;
      }
      
      // Check if stopped
      if(stopProcessing) return null;
      
      // Click Share
      if(!await clickShareButton()) {
        return null;
      }
      
      // Check if stopped
      if(stopProcessing) {
        await closeDialog(); // Close any open dialog
        return null;
      }
      
      // Click Embed
      if(!await clickEmbedButton()) {
        await closeDialog(); // Close the share dialog
        return null;
      }
      
      // Check if stopped
      if(stopProcessing) {
        await closeDialog(); // Close embed dialog
        await sleep(500);
        await closeDialog(); // Close share dialog
        return null;
      }
      
      // Extract embed URL
      const embedUrl = await extractEmbedUrl();
      
      // Close dialogs
      await closeDialog(); // Close embed dialog
      await sleep(500);
      await closeDialog(); // Close share dialog
      
      let shareUrl = '';
      try {
        // Click the copy button to ensure the input is populated/visible
        const copyBtn = document.querySelector('#copy-button.yt-copy-link-renderer');
        if (copyBtn) {
          copyBtn.click();
          await sleep(200); // Wait for clipboard/input to update
          // Try to find the input field next to the button
          let input = copyBtn.parentElement && copyBtn.parentElement.querySelector('input');
          if (!input) {
            // Fallback: try a common selector
            input = document.querySelector('input[aria-label][value^="https://youtu.be/"]');
          }
          if (input && input.value) {
            shareUrl = input.value;
            log('Found share URL: ' + shareUrl);
          } else {
            log('ERROR: Could not find share URL input after clicking copy');
          }
        } else {
          log('ERROR: Share copy button not found');
        }
      } catch (e) {
        log('ERROR extracting share URL: ' + e.message);
      }
      
      if(embedUrl) {
        // Generate full URL from embed URL
        const videoIdMatch = embedUrl.match(/\/embed\/([a-zA-Z0-9_-]+)/);
        const fullUrl = videoIdMatch && videoIdMatch[1] ? `https://www.youtube.com/watch?v=${videoIdMatch[1]}` : '';
        const thumbnailUrl = videoIdMatch && videoIdMatch[1] ? generateThumbnailUrl(videoIdMatch[1]) : '';
        const durationData = extractDurationData(videoElement);
        const viewCount = extractViewCount(videoElement);
        const videoId = videoIdMatch && videoIdMatch[1] ? videoIdMatch[1] : null;
        const schema = videoId ? await generateYouTubeSchema(videoId) : '';
        
        log(`UI extraction successful for video ${index}: ${embedUrl}`);
        
        return {
          success: true,
          index: index,
          title: videoTitle,
          embedUrl: embedUrl,
          shareUrl: shareUrl,
          fullUrl: fullUrl,
          thumbnailUrl: thumbnailUrl,
          durationFull: durationData.durationFull,
          durationTime: durationData.durationTime,
          durationSeconds: durationData.durationSeconds,
          viewCount: viewCount,
          schema: schema
        };
      }
      
      log(`UI extraction failed for video ${index}`);
      return null;
    }
    
    // Attempt direct embed URL extraction first, fall back to UI interaction
    async function processVideo(videoElement, index) {
      // Check if processing should stop
      if(stopProcessing) {
        log('Processing stopped by user');
        return null;
      }
      
      log(`Processing video ${index}...`);
      
      const videoTitle = getVideoTitle(videoElement);
      log(`Video title: ${videoTitle}`);
      
      // First try direct method (no UI interaction needed)
      const directEmbed = generateEmbedUrl(videoElement);
      if (directEmbed) {
        log(`Got embed URL directly: ${directEmbed}`);
        // Generate share URL and full URL from the same video ID
        const videoId = extractVideoId(videoElement);
        const shareUrl = videoId ? `https://youtu.be/${videoId}` : '';
        const fullUrl = videoId ? `https://www.youtube.com/watch?v=${videoId}` : '';
        const thumbnailUrl = generateThumbnailUrl(videoId);
        const durationData = extractDurationData(videoElement);
        const viewCount = extractViewCount(videoElement);
        const schema = await generateYouTubeSchema(videoId);
        if (shareUrl) {
          log(`Generated share URL: ${shareUrl}`);
        }
        if (fullUrl) {
          log(`Generated full URL: ${fullUrl}`);
        }
        addVideoToTable(index, videoTitle, directEmbed, shareUrl, fullUrl, thumbnailUrl, durationData.durationFull, durationData.durationTime, durationData.durationSeconds, viewCount, schema);
        return {
          title: videoTitle,
          embedUrl: directEmbed,
          shareUrl: shareUrl,
          fullUrl: fullUrl,
          thumbnailUrl: thumbnailUrl,
          durationFull: durationData.durationFull,
          durationTime: durationData.durationTime,
          durationSeconds: durationData.durationSeconds,
          viewCount: viewCount,
          schema: schema
        };
      }
      
      log('Direct embed URL extraction failed, falling back to UI method...');
      
      // Fall back to the UI interaction method
      // Click three dots
      if(!await clickThreeDots(videoElement)) {
        return null;
      }
      
      // Check if stopped
      if(stopProcessing) return null;
      
      // Click Share
      if(!await clickShareButton()) {
        return null;
      }
      
      // Check if stopped
      if(stopProcessing) {
        await closeDialog(); // Close any open dialog
        return null;
      }
      
      // Click Embed
      if(!await clickEmbedButton()) {
        await closeDialog(); // Close the share dialog
        return null;
      }
      
      // Check if stopped
      if(stopProcessing) {
        await closeDialog(); // Close embed dialog
        await sleep(500);
        await closeDialog(); // Close share dialog
        return null;
      }
      
      // Extract embed URL
      const embedUrl = await extractEmbedUrl();
      
      // Close dialogs
      await closeDialog(); // Close embed dialog
      await sleep(500);
      await closeDialog(); // Close share dialog
      
      let shareUrl = '';
      try {
        // Click the copy button to ensure the input is populated/visible
        const copyBtn = document.querySelector('#copy-button.yt-copy-link-renderer');
        if (copyBtn) {
          copyBtn.click();
          await sleep(200); // Wait for clipboard/input to update
          // Try to find the input field next to the button
          let input = copyBtn.parentElement && copyBtn.parentElement.querySelector('input');
          if (!input) {
            // Fallback: try a common selector
            input = document.querySelector('input[aria-label][value^="https://youtu.be/"]');
          }
          if (input && input.value) {
            shareUrl = input.value;
            log('Found share URL: ' + shareUrl);
          } else {
            log('ERROR: Could not find share URL input after clicking copy');
          }
        } else {
          log('ERROR: Share copy button not found');
        }
      } catch (e) {
        log('ERROR extracting share URL: ' + e.message);
      }
      
              if(embedUrl) {
          // Add to table directly
          const videoIdMatch = embedUrl.match(/\/embed\/([a-zA-Z0-9_-]+)/);
          const fullUrl = videoIdMatch && videoIdMatch[1] ? `https://www.youtube.com/watch?v=${videoIdMatch[1]}` : '';
          const thumbnailUrl = videoIdMatch && videoIdMatch[1] ? generateThumbnailUrl(videoIdMatch[1]) : '';
          const durationData = extractDurationData(videoElement);
          const viewCount = extractViewCount(videoElement);
          const videoId = videoIdMatch && videoIdMatch[1] ? videoIdMatch[1] : null;
          const schema = videoId ? await generateYouTubeSchema(videoId) : '';
          addVideoToTable(index, videoTitle, embedUrl, shareUrl, fullUrl, thumbnailUrl, durationData.durationFull, durationData.durationTime, durationData.durationSeconds, viewCount, schema);
          return {
            title: videoTitle,
            embedUrl: embedUrl,
            shareUrl: shareUrl,
            fullUrl: fullUrl,
            thumbnailUrl: thumbnailUrl,
            durationFull: durationData.durationFull,
            durationTime: durationData.durationTime,
            durationSeconds: durationData.durationSeconds,
            viewCount: viewCount,
            schema: schema
          };
        }
      
      return null;
    }
    
    // Export to CSV
    function exportToCSV() {
      log('Exporting to CSV...');
      const rows = Array.from(tbody.querySelectorAll('tr'));
      const selectedRows = rows.filter(row => row.querySelector('.video-select').checked);
      
      if(selectedRows.length === 0) {
        log('No videos selected for export');
        return;
      }
      
      // Create CSV content with all URL columns
      let csvContent = 'Index,Title,Embed URL,ID,Share URL,Full URL,Thumbnail URL,Duration Full,Duration Time,Duration Seconds,Views,Schema\n';
      
      selectedRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const index = cells[1].textContent.split(':')[0].replace('#', '');
        // Escape quotes in title
        const safeTitle = cells[1].textContent.split(':').slice(1).join(':').trim().replace(/"/g, '""');
        const url = cells[2].querySelector('div').textContent;
        // Extract ID from embed URL (after last slash)
        let id = '';
        if (url) {
          const match = url.match(/\/embed\/([a-zA-Z0-9_-]+)/);
          if (match && match[1]) {
            id = match[1];
          }
        }
        // Retrieve share URL and full URL from data attributes
        let shareUrl = '';
        let fullUrl = '';
        if (cells[2].querySelector('div').dataset && cells[2].querySelector('div').dataset.shareUrl) {
          shareUrl = cells[2].querySelector('div').dataset.shareUrl;
        }
        if (cells[2].querySelector('div').dataset && cells[2].querySelector('div').dataset.fullUrl) {
          fullUrl = cells[2].querySelector('div').dataset.fullUrl;
        }
        // Retrieve thumbnail URL from data attributes
        let thumbnailUrl = '';
        if (cells[3].querySelector('div').dataset && cells[3].querySelector('div').dataset.thumbnailUrl) {
          thumbnailUrl = cells[3].querySelector('div').dataset.thumbnailUrl;
        }
        // Retrieve duration and view data from data attributes
        let durationFull = '';
        let durationTime = '';
        let durationSeconds = '';
        let viewCount = '';
        if (cells[4].querySelector('div').dataset && cells[4].querySelector('div').dataset.durationFull) {
          durationFull = cells[4].querySelector('div').dataset.durationFull;
        }
        if (cells[5].querySelector('div').dataset && cells[5].querySelector('div').dataset.durationTime) {
          durationTime = cells[5].querySelector('div').dataset.durationTime;
        }
        if (cells[6].querySelector('div').dataset && cells[6].querySelector('div').dataset.durationSeconds) {
          durationSeconds = cells[6].querySelector('div').dataset.durationSeconds;
        }
        if (cells[7].querySelector('div').dataset && cells[7].querySelector('div').dataset.viewCount) {
          viewCount = cells[7].querySelector('div').dataset.viewCount;
        }
        // Retrieve schema from data attributes
        let schema = '';
        if (cells[8].querySelector('div').dataset && cells[8].querySelector('div').dataset.schema) {
          schema = cells[8].querySelector('div').dataset.schema;
        }
        // Handle special error cases for CSV export
        if (schema === 'API_KEY_NOT_FOUND') {
          schema = 'API Key not found';
        }
        // Escape quotes in schema for CSV (handles script-wrapped JSON content)
        const safeSchema = schema.replace(/"/g, '""');
        csvContent += `${index},"${safeTitle}","${url}","${id}","${shareUrl}","${fullUrl}","${thumbnailUrl}","${durationFull}","${durationTime}","${durationSeconds}","${viewCount}","${safeSchema}"\n`;
      });
      
      // Create download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', 'youtube_embed_urls.csv');
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      log('CSV exported successfully');
    }
    
    // Process a batch of videos in parallel (direct extraction) then sequential (UI extraction)
    async function processBatch(videoElementsBatch, startIndex, batchNumber, totalBatches) {
      log(`Processing batch ${batchNumber}/${totalBatches} (videos ${startIndex} to ${startIndex + videoElementsBatch.length - 1})...`);
      
      // Phase 1: Try direct extraction for all videos in parallel
      log(`Phase 1: Parallel direct extraction for batch ${batchNumber}...`);
      const directExtractionPromises = videoElementsBatch.map((videoElement, i) => {
        const actualIndex = startIndex + i;
        return tryDirectExtraction(videoElement, actualIndex);
      });
      
      const directResults = await Promise.all(directExtractionPromises);
      
      // Separate successful and failed extractions
      const successfulResults = [];
      const failedExtractions = [];
      
      directResults.forEach(result => {
        if (result.success) {
          successfulResults.push(result);
          // Add successful results to table immediately
          addVideoToTable(result.index, result.title, result.embedUrl, result.shareUrl, result.fullUrl, result.thumbnailUrl, result.durationFull, result.durationTime, result.durationSeconds, result.viewCount, result.schema);
        } else {
          failedExtractions.push(result);
        }
      });
      
      log(`Batch ${batchNumber}: ${successfulResults.length} direct successes, ${failedExtractions.length} need UI extraction`);
      
      // Phase 2: Try UI extraction for failed videos (sequential)
      if (failedExtractions.length > 0 && !stopProcessing) {
        log(`Phase 2: Sequential UI extraction for ${failedExtractions.length} failed videos in batch ${batchNumber}...`);
        
        for (const failedResult of failedExtractions) {
          if (stopProcessing) {
            log('Processing stopped by user during UI extraction');
            break;
          }
          
          const uiResult = await tryUIExtraction(failedResult.videoElement, failedResult.index, failedResult.title);
          
          if (uiResult && uiResult.success) {
            addVideoToTable(uiResult.index, uiResult.title, uiResult.embedUrl, uiResult.shareUrl, uiResult.fullUrl, uiResult.thumbnailUrl, uiResult.durationFull, uiResult.durationTime, uiResult.durationSeconds, uiResult.viewCount, uiResult.schema);
            successfulResults.push(uiResult);
          }
          
          // Brief pause between UI extractions to avoid overwhelming the interface
          if (!stopProcessing && failedExtractions.indexOf(failedResult) < failedExtractions.length - 1) {
            await sleep(1000);
          }
        }
      }
      
      log(`Batch ${batchNumber} completed: ${successfulResults.length} total successes`);
      return successfulResults;
    }
    
    // Process videos in ranges
    async function processVideosInRanges() {
      log('Starting to process videos in ranges...');
      
      // Start the timer
      processingStartTime = new Date();
      
      // Get all video elements
      const videoElements = document.querySelectorAll('ytd-rich-item-renderer, ytd-grid-video-renderer, ytd-video-renderer');
      
      if(videoElements.length === 0) {
        log('ERROR: No videos found on page');
        return;
      }
      
      log(`Found ${videoElements.length} videos on page`);
      
      // Clear existing table
      tbody.innerHTML = '';
      
      // Get all range rows
      const rangeRows = document.querySelectorAll('.range-row');
      
      // Process each range
      for (const rangeRow of rangeRows) {
        // Check if processing should stop
        if(stopProcessing) {
          log('Processing stopped by user');
          break;
        }
        
        const startInput = rangeRow.querySelector('.range-start');
        const endInput = rangeRow.querySelector('.range-end');
        
        // Get start and end values from inputs
        const start = parseInt(startInput.value, 10);
        const end = parseInt(endInput.value, 10);
        
        // Validate range
        if(isNaN(start) || isNaN(end) || start < 1 || end > videoElements.length || start > end) {
          log(`ERROR: Invalid range ${start}-${end}. Please enter values between 1 and ${videoElements.length}`);
          continue; // Skip this range and move to the next
        }
        
        log(`Processing range: ${start} to ${end}`);
        
        // Get videos for this range
        const rangeVideos = [];
        for(let i = start - 1; i < end; i++) {
          rangeVideos.push({
            element: videoElements[i],
            index: i + 1
          });
        }
        
        // Get current batch size from UI input
        const batchSizeInput = document.getElementById('batch-size-input');
        const currentBatchSize = batchSizeInput ? parseInt(batchSizeInput.value, 10) || BATCH_SIZE : BATCH_SIZE;
        
        // Process videos in batches
        const totalVideosInRange = rangeVideos.length;
        const totalBatches = Math.ceil(totalVideosInRange / currentBatchSize);
        
        log(`Range ${start}-${end}: ${totalVideosInRange} videos, ${totalBatches} batches of up to ${currentBatchSize} videos`);
        
        for(let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
          // Check if processing should stop
          if(stopProcessing) {
            log('Processing stopped by user');
            break;
          }
          
          const startIdx = batchIndex * currentBatchSize;
          const endIdx = Math.min(startIdx + currentBatchSize, totalVideosInRange);
          const batch = rangeVideos.slice(startIdx, endIdx);
          
          const videoElementsBatch = batch.map(v => v.element);
          const firstVideoIndex = batch[0].index;
          
          await processBatch(videoElementsBatch, firstVideoIndex, batchIndex + 1, totalBatches);
          
          // Brief pause between batches (but not after the last batch)
          if(!stopProcessing && batchIndex < totalBatches - 1) {
            log(`Waiting ${BATCH_DELAY}ms before next batch...`);
            await sleep(BATCH_DELAY);
          }
        }
      }
      
      log('All selected videos processed');
      stopButton.textContent = 'Processing Completed';
      stopButton.disabled = true;
      stopButton.style.backgroundColor = '#888';
      stopButton.style.display = 'none';
      stopProcessing = true; // Ensure we stop the process
      
      // Show completion notification and tab marker
      showCompletionNotification();
    }
    
    // Format time in MM:SS format
    function formatTime(timeInSeconds) {
      const minutes = Math.floor(timeInSeconds / 60);
      const seconds = Math.floor(timeInSeconds % 60);
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    
    // Calculate elapsed time
    function getElapsedTime() {
      if (!processingStartTime) return "0:00";
      const elapsedSeconds = (new Date() - processingStartTime) / 1000;
      return formatTime(elapsedSeconds);
    }
    
    // Request notification permission
    async function requestNotificationPermission() {
      if (!("Notification" in window)) {
        log("This browser does not support notifications");
        return false;
      }
      
      if (Notification.permission === "granted") {
        log("Notification permission already granted");
        return true;
      }
      
      if (Notification.permission !== "denied") {
        log("Requesting notification permission");
        try {
          const permission = await Notification.requestPermission();
          log(`Notification permission response: ${permission}`);
          return permission === "granted";
        } catch (err) {
          log(`Error requesting notification permission: ${err.message}`);
          return false;
        }
      }
      
      return false;
    }
    
    // Show completion notification and tab marker
    async function showCompletionNotification() {
      // Get elapsed time
      const elapsedTime = getElapsedTime();
      
      // Show the completion banner in the UI with elapsed time
      completionBanner.innerHTML = `✅ PROCESSING COMPLETE! <span style="margin-left: 15px; background: rgba(0,0,0,0.3); padding: 2px 8px; border-radius: 10px; font-size: 0.9em;">Time: ${elapsedTime}</span>`;
      completionBanner.style.display = 'block';
      
      // Change page title to include marker
      const originalTitle = document.title;
      document.title = "🔴 COMPLETE: " + originalTitle;
      
      // Flash the title
      let flashCount = 0;
      const flashInterval = setInterval(() => {
        if (flashCount >= 10) {
          clearInterval(flashInterval);
          return;
        }
        
        document.title = flashCount % 2 === 0 
          ? "🔴 COMPLETE: " + originalTitle 
          : "⭕ COMPLETE: " + originalTitle;
        
        flashCount++;
      }, 500);
      
      // Browser notification
      try {
        log("Setting up browser notification");
        const hasPermission = await requestNotificationPermission();
        log(`Has notification permission: ${hasPermission}`);
        
        if (hasPermission) {
          // Forcing a small delay sometimes helps with notification visibility
          await sleep(300);
          
          log("Creating notification");
          const notification = new Notification("YouTube Embed URL Scraper", {
            body: `All videos have been processed! (${elapsedTime}) URLs are ready for export.`,
            icon: "https://www.youtube.com/favicon.ico",
            requireInteraction: false
          });
          
          notification.onshow = () => log("Notification shown");
          notification.onerror = (err) => log(`Notification error: ${err}`);
          
          // Close notification after 8 seconds
          setTimeout(() => {
            try {
              notification.close();
            } catch (e) {
              // Ignore close errors
            }
          }, 8000);
        } else {
          log("Notification permission not granted");
        }
      } catch (error) {
        log(`Notification error: ${error.message}`);
      }
      
      // Play a sound (works even when tab is not focused)
      try {
        const audio = new Audio("data:audio/mp3;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFTb25vdGhlcXVlLm9yZwBURU5DAAAAHQAAA1N3aXRjaCBQbHVzIMKpIE5DSCBTb2Z0d2FyZQBUSVQyAAAABgAAAzIyMzUAVFNTRQAAAA8AAANMYXZmNTcuODMuMTAwAAAAAAAAAAAAAAD/80DEAAAAA0gAAAAATEFNRTMuMTAwVVVVVVVVVVVVVUxBTUUzLjEwMFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/zQsRbAAADSAAAAABVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/zQMSkAAADSAAAAABVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV");
        audio.play();
      } catch (e) {
        log("Could not play notification sound");
      }
    }
  
                                // Create a banner for completion status
                            const completionBanner = document.createElement('div');
                            completionBanner.style.background = '#010101';
                            completionBanner.style.color = '#ffffff';
                            completionBanner.style.border = '1px solid #16a34a';
                            completionBanner.style.padding = '12px';
                            completionBanner.style.borderRadius = '8px';
                            completionBanner.style.marginBottom = '16px';
                            completionBanner.style.display = 'none';
                            completionBanner.style.fontWeight = 'bold';
                            completionBanner.style.textAlign = 'center';
                            completionBanner.textContent = '<span style="color:#16a34a ; font-size: 18px;">●</span> PROCESSING COMPLETE!';
                            completionBanner.style.border = '1px solid #16a34a';
                            completionBanner.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';
                            container.insertBefore(completionBanner, rangeContainer);
    
    // Add event listeners
    numberButton.addEventListener('click', numberVideos);
    
    startButton.addEventListener('click', () => {
      stopProcessing = false;
      stopButton.textContent = 'Stop Process';
      stopButton.disabled = false;
      stopButton.style.backgroundColor = '#f4b400';
      completionBanner.style.display = 'none'; // Hide banner when starting
      processVideosInRanges();
    });
    
    selectAllCheckbox.addEventListener('change', () => {
      const checkboxes = document.querySelectorAll('.video-select');
      checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
      });
    });
    
    exportButton.addEventListener('click', exportToCSV);
    
    stopButton.addEventListener('click', () => {
      stopProcessing = true;
      stopButton.textContent = 'Stopping...';
      stopButton.style.backgroundColor = '#888';
      log('Stopping process... (Will complete current operation)');
    });
    
    closeButton.addEventListener('click', () => {
      document.body.removeChild(container);
      
      // Reset the page title back to original if we modified it
      if (document.title.includes('🔴') || document.title.includes('⭕')) {
        document.title = document.title.replace(/[🔴⭕]\s*COMPLETE:\s*/, '');
      }
    });
    
                            // Start by numbering videos
                            log('YouTube Embed URL Scraper started');
                            numberVideos();
                        })();
                    }
                }, (result) => {
                    if (chrome.runtime.lastError) {
                        console.error('Error executing YouTube Embed Scraper script:', chrome.runtime.lastError);
                    } else {
                        console.log('YouTube Embed Scraper script executed successfully');
                    }
                });
            });
        } catch (error) {
            console.error('YouTube Embed Scraper error:', error);
        }
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                // Function to reset YouTube Embed Scraper effects
                function resetYouTubeEmbedScraper() {
                    // Remove any existing scraper UI
                    const existingContainer = document.querySelector('div[style*="position: fixed"][style*="top: 10px"][style*="right: 10px"]');
                    if (existingContainer) {
                        existingContainer.remove();
                        console.log('YouTube Embed Scraper UI removed');
                    }
                    
                    // Remove any number overlays
                    const numberOverlays = document.querySelectorAll('.yt-embed-scraper-number');
                    numberOverlays.forEach(overlay => overlay.remove());
                    
                    // Reset page title if modified
                    if (document.title.includes('🔴') || document.title.includes('⭕')) {
                        document.title = document.title.replace(/[🔴⭕]\s*COMPLETE:\s*/, '');
                    }
                    
                    console.log('YouTube Embed Scraper reset completed');
                }

                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetYouTubeEmbedScraper
                    }, () => {
                        resolve();
                    });
                });
            } catch (error) {
                console.error('YouTube Embed Scraper reset error:', error);
                resolve(); // Resolve anyway to not block other resets
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = YouTubeEmbedScraperAction;
} else {
    window.YouTubeEmbedScraperAction = YouTubeEmbedScraperAction;
}