// Links Extractor Action - Interactive tool to extract links from selected elements
// Prevent duplicate class declaration
if (typeof window.LinksExtractorAction === 'undefined') {

class LinksExtractorAction {
    constructor() {
        this.name = 'Links Extractor';
        this.description = 'Interactive tool to extract and analyze links from selected page elements';
    }

    // Execute the links extractor functionality directly on the page
    execute() {
        return new Promise((resolve, reject) => {
            try {
                // Check if already active and handle gracefully
                if (window.linksExtractorActive) {
                    console.log('Links Extractor already active - performing quick reset');
                    this.quickCleanup();
                    window.linksExtractorActive = false;
                    setTimeout(() => {
                        if (!window.linksExtractorActive) {
                            this.execute().then(resolve).catch(reject);
                        }
                    }, 10);
                    return;
                }
                
                // Mark as active immediately to prevent race conditions
                window.linksExtractorActive = true;
                
                // Remove existing panels and styles if present
                document.querySelectorAll('.links-extractor-panel, .links-extractor-notification, .links-extractor-styles').forEach(el => el.remove());
                
                // Remove any existing hover effects and labels
                document.querySelectorAll('.links-extractor-hover').forEach(el => {
                    el.classList.remove('links-extractor-hover');
                    if (el.getAttribute('data-links-extractor-positioned') === 'true') {
                        el.style.position = '';
                        el.removeAttribute('data-links-extractor-positioned');
                    }
                });
                
                // Remove any existing event listeners
                if (window.linksExtractorCleanup) {
                    window.linksExtractorCleanup();
                }
                
                // Add styles for hover effect and panels
                const style = document.createElement('style');
                style.className = 'links-extractor-styles';
                style.setAttribute('data-links-extractor-style', 'true');
                style.textContent = `
                    .links-extractor-hover { 
                        outline: 2px solid #7C3AED !important; 
                        outline-offset: 2px !important;
                        cursor: pointer !important;
                        position: relative !important;
                    }
                    .links-extractor-notification {
                        position: fixed;
                        top: 20px;
                        left: 50%;
                        transform: translateX(-50%);
                        background: rgba(0, 0, 0, 0.9);
                        color: white;
                        border: 1px solid #7C3AED;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 500;
                        opacity: 0;
                        z-index: 9999999;
                        pointer-events: none;
                        transition: opacity 0.4s ease;
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                        box-shadow: 0 4px 16px rgba(0,0,0,0.4);
                    }
                    .links-extractor-notification.show {
                        opacity: 1;
                    }
                    .links-extractor-instructions {
                        position: fixed;
                        bottom: 20px;
                        right: 20px;
                        background: #0a0a0a;
                        color: #d1d5db;
                        border: 1px solid #7C3AED;
                        padding: 16px 20px;
                        border-radius: 8px;
                        font-size: 13px;
                        z-index: 9999999;
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                        box-shadow: 0 4px 16px rgba(0,0,0,0.4);
                        max-width: 300px;
                        line-height: 1.4;
                    }
                    .links-extractor-panel {
                        position: fixed;
                        top: 50px;
                        left: 50px;
                        width: 700px;
                        max-height: 600px;
                        background: #0a0a0a;
                        color: #d1d5db;
                        border: 1px solid #3a3a3a;
                        border-radius: 10px;
                        box-shadow: 0 8px 32px rgba(0,0,0,0.6);
                        z-index: 9999999;
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                        display: flex;
                        flex-direction: column;
                        resize: both;
                        overflow: hidden;
                        min-width: 500px;
                        min-height: 300px;
                    }
                    .links-extractor-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 20px 20px 20px 20px;
                        border-bottom: 1px solid #2a2a2a;
                        cursor: move;
                        font-size: 18px;
                        font-weight: 600;
                        letter-spacing: -0.5px;
                        color: #d1d5db;
                    }
                    .links-extractor-close {
                        padding: 6px 12px;
                        background: #374151;
                        color: #d1d5db;
                        border: 1px solid #4b5563;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                        font-weight: 500;
                        transition: all 0.2s;
                    }
                    .links-extractor-close:hover {
                        background: #4b5563;
                        border-color: #6b7280;
                    }
                    .links-extractor-content {
                        padding: 20px;
                        overflow-y: auto;
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        gap: 12px;
                        font-size: 14px;
                        line-height: 1.5;
                    }
                    .links-extractor-controls {
                        display: flex;
                        gap: 8px;
                        margin-bottom: 12px;
                    }
                    .links-extractor-button {
                        background: #374151;
                        color: #d1d5db;
                        border: 1px solid #4b5563;
                        padding: 8px 16px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                        transition: all 0.2s;
                    }
                    .links-extractor-button:hover {
                        background: #4b5563;
                        border-color: #6b7280;
                    }
                    .links-extractor-button.primary {
                        background: #7C3AED;
                        border-color: #7C3AED;
                        color: white;
                    }
                    .links-extractor-button.primary:hover {
                        background: #6D28D9;
                    }
                    .links-extractor-stats {
                        background: #111111;
                        border: 1px solid #2a2a2a;
                        border-radius: 8px;
                        padding: 16px 20px;
                        font-size: 13px;
                        color: #9ca3af;
                        margin-bottom: 24px;
                        font-weight: 500;
                    }
                    .links-extractor-links-section {
                        background: #111111;
                        border-radius: 8px;
                        border: 1px solid #2a2a2a;
                        overflow: hidden;
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                    }
                    .links-extractor-section-header {
                        background: #0a0a0a;
                        padding: 16px 20px;
                        border-bottom: 1px solid #2a2a2a;
                        font-weight: 500;
                        color: #9ca3af;
                        font-size: 14px;
                    }
                    .links-extractor-table-container {
                        flex: 1;
                        overflow-y: auto;
                    }
                    .links-extractor-table {
                        width: 100%;
                        border-collapse: collapse;
                    }
                    .links-extractor-table-header {
                        background: #0a0a0a;
                        position: sticky;
                        top: 0;
                        z-index: 10;
                    }
                    .links-extractor-table-header > th {
                        padding: 12px 20px;
                        color: #3a3a3a!important;
                        background-color: #0a0a0a!important;
                        font-size: 13px;
                        font-weight: 500;
                        text-align: left;
                        border-bottom: 1px solid #3a3a3a!important;
                        border-right: 1px solid #3a3a3a!important;
                    }
                    .links-extractor-table-header > th:last-child {
                        border-right: none;
                    }
                    .links-extractor-table-row {
                        border-bottom: 1px solid #2a2a2a;
                        transition: background 0.2s;
                        cursor: pointer;
                    }
                    .links-extractor-table-row:hover {
                        background: #0a0a0a;
                    }
                    .links-extractor-table-row:last-child {
                        border-bottom: none;
                    }
                    .links-extractor-table-cell {
                        padding: 16px 20px;
                        vertical-align: top;
                        border-right: 1px solid #2a2a2a;
                        line-height: 1.5;
                    }
                    .links-extractor-table-cell:last-child {
                        border-right: none;
                    }
                    .links-extractor-anchor {
                        color: #d1d5db;
                        text-decoration: none;
                        display: block;
                        font-size: 14px;
                        word-break: break-word;
                    }
                    .links-extractor-url {
                        color: #6b7280;
                        font-family: monospace;
                        font-size: 12px;
                        background: #0a0a0a;
                        padding: 4px 8px;
                        border-radius: 4px;
                        word-break: break-all;
                        display: inline-block;
                        max-width: 100%;
                        box-sizing: border-box;
                    }
                    /* Override Microthemer styling conflicts */
                    .links-extractor-table-header > th {
                        background-color: #0a0a0a !important;
                        color: #9ca3af !important;
                    }
                `;
                document.head.appendChild(style);

                // Create instructions panel
                const instructions = document.createElement('div');
                instructions.className = 'links-extractor-instructions';
                instructions.setAttribute('data-links-extractor-instructions', 'true');
                instructions.innerHTML = `
                    <div style="margin-bottom: 8px; font-weight: 600; color: #7C3AED;">Links Extractor Active</div>
                    <div style="margin-bottom: 6px;">🖱️ <strong>Hover:</strong> Purple outline preview</div>
                    <div style="margin-bottom: 6px;">👆 <strong>Click:</strong> Extract links from element</div>
                    <div style="font-size: 11px; color: #9ca3af; margin-top: 8px;">Press ESC to close</div>
                `;
                document.body.appendChild(instructions);

                function showNotification(msg, type = 'success') {
                    const existing = document.querySelector('.links-extractor-notification');
                    if (existing) {
                        existing.remove();
                    }

                    const notif = document.createElement('div');
                    notif.className = 'links-extractor-notification';
                    notif.setAttribute('data-links-extractor-notification', 'true');
                    
                    // Add purple dot on black background for small notifications
                    notif.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span style="color: #7C3AED; font-size: 16px; background: #000; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">●</span>
                            <span>${msg}</span>
                        </div>
                    `;
                    document.body.appendChild(notif);

                    setTimeout(() => notif.classList.add('show'), 10);
                    setTimeout(() => {
                        notif.classList.remove('show');
                        setTimeout(() => {
                            if (notif.parentNode) {
                                notif.remove();
                            }
                        }, 400);
                    }, 2500);
                }

                // Hover management variables
                window.linksExtractorHoverTimeout = null;
                window.linksExtractorCurrentHoverElement = null;
                window.linksExtractorIsProcessing = false;

                window.linksExtractorMouseOverHandler = function(e) {
                    // Don't add hover effect to links extractor components
                    if (e.target.closest('[data-links-extractor-instructions], [data-links-extractor-notification], .links-extractor-panel')) {
                        return;
                    }
                    
                    if (window.linksExtractorIsProcessing) {
                        return;
                    }
                    
                    const targetElement = e.target;
                    
                    if (window.linksExtractorCurrentHoverElement === targetElement) {
                        return;
                    }
                    
                    window.linksExtractorIsProcessing = true;
                    
                    if (window.linksExtractorHoverTimeout) {
                        clearTimeout(window.linksExtractorHoverTimeout);
                        window.linksExtractorHoverTimeout = null;
                    }
                    
                    // Clean up previous element
                    if (window.linksExtractorCurrentHoverElement) {
                        window.linksExtractorCurrentHoverElement.classList.remove('links-extractor-hover');
                        if (window.linksExtractorCurrentHoverElement.getAttribute('data-links-extractor-positioned') === 'true') {
                            window.linksExtractorCurrentHoverElement.style.position = '';
                            window.linksExtractorCurrentHoverElement.removeAttribute('data-links-extractor-positioned');
                        }
                    }
                    
                    window.linksExtractorCurrentHoverElement = targetElement;
                    targetElement.classList.add('links-extractor-hover');
                    
                    setTimeout(() => {
                        window.linksExtractorIsProcessing = false;
                    }, 50);
                };

                window.linksExtractorMouseOutHandler = function(e) {
                    const targetElement = e.target;
                    
                    if (window.linksExtractorCurrentHoverElement === targetElement) {
                        if (window.linksExtractorHoverTimeout) {
                            clearTimeout(window.linksExtractorHoverTimeout);
                            window.linksExtractorHoverTimeout = null;
                        }
                        window.linksExtractorIsProcessing = false;
                    }
                };

                window.linksExtractorClickHandler = function(e) {
                    // Don't handle clicks on links extractor components
                    if (e.target.closest('[data-links-extractor-instructions], [data-links-extractor-notification], .links-extractor-panel')) {
                        return;
                    }

                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    
                    const container = e.target;
                    
                    // Add visual feedback to selected element
                    container.style.outline = '2px solid #7C3AED';
                    container.style.outlineOffset = '2px';
                    
                    try {
                        scanLinks(container);
                    } catch (error) {
                        console.error('Links Extractor error:', error);
                        showNotification('❌ Error extracting links', 'error');
                    }
                };

                // Create purple pulsing highlight effect (similar to Show Links)
                function startPurplePulsingHighlight(targetElement) {
                    targetElement.classList.add('linksextractor-highlight');
                    
                    // Store original styles
                    var originalBorder = targetElement.style.border || '';
                    var originalBoxShadow = targetElement.style.boxShadow || '';
                    var originalTransition = targetElement.style.transition || '';
                    var originalTransform = targetElement.style.transform || '';
                    
                    targetElement.setAttribute('data-linksextractor-original-border', originalBorder);
                    targetElement.setAttribute('data-linksextractor-original-box-shadow', originalBoxShadow);
                    targetElement.setAttribute('data-linksextractor-original-transition', originalTransition);
                    targetElement.setAttribute('data-linksextractor-original-transform', originalTransform);
                    
                    console.log('LinksExtractor: Starting purple pulsing highlight');
                    
                    // Add smooth transition for pulsing
                    targetElement.style.transition = 'all 0.4s ease-in-out';
                    
                    var pulseCount = 0;
                    var maxPulses = 6; // 3 complete on/off cycles
                    
                    function pulse() {
                        if (pulseCount < maxPulses) {
                            if (pulseCount % 2 === 0) {
                                // Pulse ON - bright purple
                                targetElement.style.border = '4px solid #8b5cf6';
                                targetElement.style.boxShadow = '0 0 25px rgba(139, 92, 246, 0.8)';
                                targetElement.style.transform = 'scale(1.02)';
                            } else {
                                // Pulse OFF - return to original state but keep class
                                targetElement.style.border = originalBorder;
                                targetElement.style.boxShadow = originalBoxShadow;
                                targetElement.style.transform = originalTransform;
                            }
                            pulseCount++;
                            setTimeout(pulse, 400); // 400ms per pulse state
                        } else {
                            // Cleanup after pulsing
                            console.log('LinksExtractor: Purple pulsing complete, cleaning up');
                            targetElement.style.border = originalBorder;
                            targetElement.style.boxShadow = originalBoxShadow;
                            targetElement.style.transform = originalTransform;
                            targetElement.style.transition = originalTransition;
                            targetElement.classList.remove('linksextractor-highlight');
                            targetElement.removeAttribute('data-linksextractor-original-border');
                            targetElement.removeAttribute('data-linksextractor-original-box-shadow');
                            targetElement.removeAttribute('data-linksextractor-original-transition');
                            targetElement.removeAttribute('data-linksextractor-original-transform');
                        }
                    }
                    
                    // Start pulsing immediately
                    pulse();
                }

                function scanLinks(container) {
                    const excludeList = [
                        'facebook', 'youtube', 'whatsapp', 'instagram', 'tiktok', 'wechat', 
                        'linkedin', 'snapchat', 'pinterest', 'twitter', 'reddit', 'telegram', 
                        'quora', 'discord', 'tumblr', 'medium', 'line', 'viber', 'signal', 
                        'clubhouse', 'follow', 'followers', 'share', 'likes', 'connections', 
                        'connect', 'subscribe', 'edit template', 'edit with oxygen', 
                        'titles settings', 'social', 'advanced', 
                        'redirection global settings of the plugin.', 'save permalink', 
                        'update image attributes', 'classic editor plugin', '/wp-admin/'
                    ];
                    
                    const links = [];
                    const elements = container.querySelectorAll('a');

                    if (elements) {
                        elements.forEach(element => {
                            const anchorText = element.textContent.trim();
                            const url = element.href.trim();
                            const lowerCaseAnchorText = anchorText.toLowerCase();
                            const parentTag = element.closest('header, footer, nav');
                            
                            if (anchorText && url && !parentTag && 
                                !excludeList.some(exclude => 
                                    lowerCaseAnchorText.includes(exclude) || url.includes(exclude)
                                )) {
                                links.push({ element, anchorText, url });
                            }
                        });
                    }

                    showLinksPanel(links, container);
                }

                function showLinksPanel(links, container) {
                    // Remove any existing panel
                    const existingPanel = document.querySelector('.links-extractor-panel');
                    if (existingPanel) {
                        existingPanel.remove();
                    }

                    // Create the main panel
                    const panel = document.createElement('div');
                    panel.className = 'links-extractor-panel';
                    panel.setAttribute('data-links-extractor-panel', 'true');

                    // Create header
                    const header = document.createElement('div');
                    header.className = 'links-extractor-header';
                    header.id = 'links-extractor-header';
                    header.innerHTML = `
                        <span>Links Extractor</span>
                        <button class="links-extractor-close" onclick="this.closest('.links-extractor-panel').remove(); document.querySelector('.links-extractor-selected-element')?.style.removeProperty('outline'); document.querySelector('.links-extractor-selected-element')?.style.removeProperty('outline-offset'); document.querySelector('.links-extractor-selected-element')?.classList.remove('links-extractor-selected-element');">✕</button>
                    `;

                    // Create content container
                    const content = document.createElement('div');
                    content.className = 'links-extractor-content';

                    // Create controls
                    const controls = document.createElement('div');
                    controls.className = 'links-extractor-controls';
                    
                    const copyButton = document.createElement('button');
                    copyButton.className = 'links-extractor-button primary';
                    copyButton.textContent = 'Copy All Links';
                    copyButton.addEventListener('click', () => {
                        const textToCopy = links.map(link => `${link.anchorText}: ${link.url}`).join('\n');
                        navigator.clipboard.writeText(textToCopy).then(() => {
                            showNotification('✓ Links copied to clipboard!');
                        }).catch(err => {
                            console.error('Unable to copy links: ', err);
                            showNotification('❌ Failed to copy links', 'error');
                        });
                    });

                    const copyUrlsButton = document.createElement('button');
                    copyUrlsButton.className = 'links-extractor-button';
                    copyUrlsButton.textContent = 'Copy URLs Only';
                    copyUrlsButton.addEventListener('click', () => {
                        const textToCopy = links.map(link => link.url).join('\n');
                        navigator.clipboard.writeText(textToCopy).then(() => {
                            showNotification('✓ URLs copied to clipboard!');
                        }).catch(err => {
                            console.error('Unable to copy URLs: ', err);
                            showNotification('❌ Failed to copy URLs', 'error');
                        });
                    });

                    controls.appendChild(copyButton);
                    controls.appendChild(copyUrlsButton);

                    // Create stats
                    const stats = document.createElement('div');
                    stats.className = 'links-extractor-stats';
                    stats.innerHTML = `
                        Found ${links.length} links • ${new Set(links.map(link => new URL(link.url).hostname)).size} unique domains
                    `;

                    // Create links section container
                    const linksSection = document.createElement('div');
                    linksSection.className = 'links-extractor-links-section';

                    // Create section header
                    const sectionHeader = document.createElement('div');
                    sectionHeader.className = 'links-extractor-section-header';
                    sectionHeader.textContent = 'Extracted Links';

                    // Create table container
                    const tableContainer = document.createElement('div');
                    tableContainer.className = 'links-extractor-table-container';

                    // Create table
                    const table = document.createElement('table');
                    table.className = 'links-extractor-table';

                    // Create table header
                    const thead = document.createElement('thead');
                    thead.className = 'links-extractor-table-header';
                    const headerRow = document.createElement('tr');
                    
                    const anchorHeaderCell = document.createElement('th');
                    anchorHeaderCell.textContent = 'Link Text';
                    anchorHeaderCell.style.cssText = 'width: 40% !important; background-color: #3a3a3a !important; color: #9ca3af !important;';
                    
                    const urlHeaderCell = document.createElement('th');
                    urlHeaderCell.textContent = 'URL';
                    urlHeaderCell.style.cssText = 'width: 60% !important; background-color: #3a3a3a !important; color: #9ca3af !important;';
                    
                    headerRow.appendChild(anchorHeaderCell);
                    headerRow.appendChild(urlHeaderCell);
                    thead.appendChild(headerRow);
                    table.appendChild(thead);

                    // Create table body
                    const tbody = document.createElement('tbody');

                    // Populate table rows
                    links.forEach((link, index) => {
                        const row = document.createElement('tr');
                        row.className = 'links-extractor-table-row';
                        
                        // Anchor text cell
                        const anchorCell = document.createElement('td');
                        anchorCell.className = 'links-extractor-table-cell';
                        const anchorLink = document.createElement('a');
                        anchorLink.className = 'links-extractor-anchor';
                        anchorLink.textContent = link.anchorText;
                        anchorLink.href = "#";
                        anchorLink.addEventListener('click', (e) => {
                            e.preventDefault();
                            
                            // Clear any existing highlights first
                            document.querySelectorAll('.linksextractor-highlight').forEach(function(el) {
                                el.classList.remove('linksextractor-highlight');
                                // Restore original styles if they were stored
                                if (el.hasAttribute('data-linksextractor-original-border')) {
                                    el.style.border = el.getAttribute('data-linksextractor-original-border');
                                    el.removeAttribute('data-linksextractor-original-border');
                                }
                                if (el.hasAttribute('data-linksextractor-original-box-shadow')) {
                                    el.style.boxShadow = el.getAttribute('data-linksextractor-original-box-shadow');
                                    el.removeAttribute('data-linksextractor-original-box-shadow');
                                }
                                if (el.hasAttribute('data-linksextractor-original-transition')) {
                                    el.style.transition = el.getAttribute('data-linksextractor-original-transition');
                                    el.removeAttribute('data-linksextractor-original-transition');
                                }
                                if (el.hasAttribute('data-linksextractor-original-transform')) {
                                    el.style.transform = el.getAttribute('data-linksextractor-original-transform');
                                    el.removeAttribute('data-linksextractor-original-transform');
                                }
                            });
                            
                            link.element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            startPurplePulsingHighlight(link.element);
                        });
                        anchorCell.appendChild(anchorLink);

                        // URL cell
                        const urlCell = document.createElement('td');
                        urlCell.className = 'links-extractor-table-cell';
                        const urlDiv = document.createElement('div');
                        urlDiv.className = 'links-extractor-url';
                        urlDiv.textContent = link.url;
                        urlDiv.addEventListener('click', () => {
                            navigator.clipboard.writeText(link.url).then(() => {
                                showNotification('✓ URL copied!');
                            });
                        });
                        urlCell.appendChild(urlDiv);

                        row.appendChild(anchorCell);
                        row.appendChild(urlCell);
                        tbody.appendChild(row);
                    });

                    table.appendChild(tbody);
                    tableContainer.appendChild(table);

                    linksSection.appendChild(sectionHeader);
                    linksSection.appendChild(tableContainer);

                    content.appendChild(controls);
                    content.appendChild(stats);
                    content.appendChild(linksSection);

                    panel.appendChild(header);
                    panel.appendChild(content);

                    // Prevent background page scrolling when scrolling inside the panel
                    panel.addEventListener('wheel', function(e) {
                        e.stopPropagation();
                        
                        // Check if we're at the scroll boundaries to allow panel scrolling
                        var scrollableElement = e.target.closest('[style*="overflow"]') || panel.querySelector('.links-extractor-table-container');
                        if (scrollableElement) {
                            var atTop = scrollableElement.scrollTop === 0;
                            var atBottom = scrollableElement.scrollTop >= (scrollableElement.scrollHeight - scrollableElement.clientHeight);
                            
                            // Only prevent default if we're not at boundaries or if scrolling in the direction that stays within bounds
                            if ((!atTop && e.deltaY < 0) || (!atBottom && e.deltaY > 0)) {
                                // Allow scrolling within the panel
                                return;
                            }
                        }
                        e.preventDefault();
                    }, { passive: false });
                    
                    panel.addEventListener('touchmove', function(e) {
                        e.stopPropagation();
                    }, { passive: true });

                    // Add drag functionality
                    let isDragging = false;
                    let currentX, currentY, initialX, initialY, xOffset = 0, yOffset = 0;

                    function dragStart(e) {
                        if (e.target === header || header.contains(e.target)) {
                            if (e.type === "touchstart") {
                                initialX = e.touches[0].clientX - xOffset;
                                initialY = e.touches[0].clientY - yOffset;
                            } else {
                                initialX = e.clientX - xOffset;
                                initialY = e.clientY - yOffset;
                            }

                            if (e.target === header || header.contains(e.target)) {
                                isDragging = true;
                                panel.style.cursor = 'grabbing';
                            }
                        }
                    }

                    function dragEnd(e) {
                        initialX = currentX;
                        initialY = currentY;
                        isDragging = false;
                        panel.style.cursor = 'default';
                    }

                    function drag(e) {
                        if (isDragging) {
                            e.preventDefault();
                            
                            if (e.type === "touchmove") {
                                currentX = e.touches[0].clientX - initialX;
                                currentY = e.touches[0].clientY - initialY;
                            } else {
                                currentX = e.clientX - initialX;
                                currentY = e.clientY - initialY;
                            }

                            xOffset = currentX;
                            yOffset = currentY;

                            panel.style.transform = `translate(${currentX}px, ${currentY}px)`;
                        }
                    }

                    header.addEventListener('mousedown', dragStart);
                    document.addEventListener('mousemove', drag);
                    document.addEventListener('mouseup', dragEnd);

                    // Mark container as selected for cleanup
                    container.classList.add('links-extractor-selected-element');

                    document.body.appendChild(panel);

                    if (links.length === 0) {
                        content.innerHTML = `
                            <div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;padding:40px;text-align:center;">
                                <div style="color:#d1d5db;font-size:16px;font-weight:500;margin-bottom:8px;">No links found</div>
                                <div style="color:#6b7280;font-size:12px;line-height:1.5;">Try selecting a different element that contains links</div>
                            </div>
                        `;
                    }
                }

                window.linksExtractorKeyDownHandler = function(e) {
                    if (e.key === 'Escape') {
                        e.preventDefault();
                        console.log('LinksExtractorAction: Escape key pressed, performing full reset');
                        
                        const linksExtractorAction = new LinksExtractorAction();
                        linksExtractorAction.reset().then(() => {
                            console.log('LinksExtractorAction: Full reset completed via Escape key');
                        }).catch((error) => {
                            console.error('LinksExtractorAction: Error during Escape reset:', error);
                            cleanup();
                        });
                    }
                };

                function cleanup() {
                    console.log('Starting thorough Links Extractor cleanup');
                    
                    if (window.linksExtractorHoverTimeout) {
                        clearTimeout(window.linksExtractorHoverTimeout);
                        window.linksExtractorHoverTimeout = null;
                    }
                    
                    window.linksExtractorCurrentHoverElement = null;
                    window.linksExtractorIsProcessing = false;
                    
                    document.body.removeEventListener('mouseover', window.linksExtractorMouseOverHandler);
                    document.body.removeEventListener('mouseout', window.linksExtractorMouseOutHandler);
                    document.body.removeEventListener('click', window.linksExtractorClickHandler);
                    document.removeEventListener('keydown', window.linksExtractorKeyDownHandler);

                    document.querySelectorAll('.links-extractor-hover, [data-links-extractor-positioned]').forEach(el => {
                        el.classList.remove('links-extractor-hover');
                        if (el.getAttribute('data-links-extractor-positioned') === 'true') {
                            el.style.position = '';
                            el.removeAttribute('data-links-extractor-positioned');
                        }
                    });

                    // Clean up purple pulsing highlights
                    document.querySelectorAll('.linksextractor-highlight').forEach(function(el) {
                        el.classList.remove('linksextractor-highlight');
                        // Restore original styles if they were stored
                        if (el.hasAttribute('data-linksextractor-original-border')) {
                            el.style.border = el.getAttribute('data-linksextractor-original-border');
                            el.removeAttribute('data-linksextractor-original-border');
                        }
                        if (el.hasAttribute('data-linksextractor-original-box-shadow')) {
                            el.style.boxShadow = el.getAttribute('data-linksextractor-original-box-shadow');
                            el.removeAttribute('data-linksextractor-original-box-shadow');
                        }
                        if (el.hasAttribute('data-linksextractor-original-transition')) {
                            el.style.transition = el.getAttribute('data-linksextractor-original-transition');
                            el.removeAttribute('data-linksextractor-original-transition');
                        }
                        if (el.hasAttribute('data-linksextractor-original-transform')) {
                            el.style.transform = el.getAttribute('data-linksextractor-original-transform');
                            el.removeAttribute('data-linksextractor-original-transform');
                        }
                    });

                    // Remove selected element outline
                    document.querySelectorAll('.links-extractor-selected-element').forEach(el => {
                        el.style.removeProperty('outline');
                        el.style.removeProperty('outline-offset');
                        el.classList.remove('links-extractor-selected-element');
                    });

                    const selectorsToRemove = [
                        '.links-extractor-panel',
                        '.links-extractor-notification',
                        '.links-extractor-styles', 
                        '.links-extractor-instructions',
                        '[data-links-extractor-style]',
                        '[data-links-extractor-instructions]',
                        '[data-links-extractor-notification]',
                        '[data-links-extractor-panel]'
                    ];
                    
                    selectorsToRemove.forEach(selector => {
                        document.querySelectorAll(selector).forEach(el => {
                            el.remove();
                        });
                    });

                    window.linksExtractorCleanup = null;
                    window.linksExtractorMouseOverHandler = null;
                    window.linksExtractorMouseOutHandler = null;
                    window.linksExtractorClickHandler = null;
                    window.linksExtractorKeyDownHandler = null;
                    
                    window.linksExtractorActive = false;

                    console.log('Thorough Links Extractor cleanup completed');
                }

                window.linksExtractorCleanup = cleanup;

                // Add event listeners
                document.body.addEventListener('mouseover', window.linksExtractorMouseOverHandler);
                document.body.addEventListener('mouseout', window.linksExtractorMouseOutHandler);
                document.body.addEventListener('click', window.linksExtractorClickHandler);
                document.addEventListener('keydown', window.linksExtractorKeyDownHandler);

                console.log('Links Extractor activated - hover and click elements to extract links');
                resolve({ success: true, message: 'Links Extractor activated' });

            } catch (error) {
                console.error('Links Extractor error:', error);
                window.linksExtractorActive = false;
                reject(error);
            }
        });
    }

    // Quick synchronous cleanup for faster reactivation
    quickCleanup() {
        try {
            if (window.linksExtractorHoverTimeout) {
                clearTimeout(window.linksExtractorHoverTimeout);
                window.linksExtractorHoverTimeout = null;
            }
            
            window.linksExtractorCurrentHoverElement = null;
            window.linksExtractorIsProcessing = false;
            
            if (window.linksExtractorMouseOverHandler) {
                document.body.removeEventListener('mouseover', window.linksExtractorMouseOverHandler);
            }
            if (window.linksExtractorMouseOutHandler) {
                document.body.removeEventListener('mouseout', window.linksExtractorMouseOutHandler);
            }
            if (window.linksExtractorClickHandler) {
                document.body.removeEventListener('click', window.linksExtractorClickHandler);
            }
            if (window.linksExtractorKeyDownHandler) {
                document.removeEventListener('keydown', window.linksExtractorKeyDownHandler);
            }
            
            const quickSelectors = [
                '.links-extractor-panel',
                '.links-extractor-notification',
                '.links-extractor-styles',
                '.links-extractor-instructions'
            ];
            
            quickSelectors.forEach(selector => {
                document.querySelectorAll(selector).forEach(el => el.remove());
            });
            
            document.querySelectorAll('.links-extractor-hover').forEach(el => {
                el.classList.remove('links-extractor-hover');
            });
            
            console.log('Quick cleanup completed');
        } catch (error) {
            console.warn('Quick cleanup error:', error);
        }
    }

    // Reset the links extractor functionality (remove all modifications)
    reset() {
        return new Promise((resolve, reject) => {
            try {
                console.log('LinksExtractorAction: Starting comprehensive DOM reset');
                
                document.body.removeEventListener('mouseover', window.linksExtractorMouseOverHandler);
                document.body.removeEventListener('mouseout', window.linksExtractorMouseOutHandler);  
                document.body.removeEventListener('click', window.linksExtractorClickHandler);
                document.removeEventListener('keydown', window.linksExtractorKeyDownHandler);

                if (window.linksExtractorCleanup) {
                    window.linksExtractorCleanup();
                }

                const selectorsToRemove = [
                    '.links-extractor-panel',
                    '.links-extractor-notification',
                    '.links-extractor-styles', 
                    '.links-extractor-instructions',
                    '[data-links-extractor-style]',
                    '[data-links-extractor-instructions]',
                    '[data-links-extractor-notification]',
                    '[data-links-extractor-panel]'
                ];
                
                selectorsToRemove.forEach(selector => {
                    document.querySelectorAll(selector).forEach(el => {
                        console.log('LinksExtractorAction: Removing element:', selector, el);
                        el.remove();
                    });
                });

                document.querySelectorAll('.links-extractor-hover, [data-links-extractor-positioned]').forEach(el => {
                    el.classList.remove('links-extractor-hover');
                    if (el.getAttribute('data-links-extractor-positioned') === 'true') {
                        el.style.position = '';
                        el.removeAttribute('data-links-extractor-positioned');
                    }
                });

                // Remove selected element outlines
                document.querySelectorAll('.links-extractor-selected-element').forEach(el => {
                    el.style.removeProperty('outline');
                    el.style.removeProperty('outline-offset');
                    el.classList.remove('links-extractor-selected-element');
                });

                window.linksExtractorCleanup = null;
                window.linksExtractorMouseOverHandler = null;
                window.linksExtractorMouseOutHandler = null;
                window.linksExtractorClickHandler = null;
                window.linksExtractorKeyDownHandler = null;

                setTimeout(() => {
                    document.querySelectorAll('[class*="links-extractor"], [data-links-extractor]').forEach(el => {
                        console.log('LinksExtractorAction: Final cleanup removing:', el);
                        el.remove();
                    });
                }, 50);

                console.log('LinksExtractorAction: Comprehensive DOM reset completed');
                resolve({ success: true, message: 'Links Extractor completely reset' });
            } catch (error) {
                console.error('LinksExtractorAction: Reset error:', error);
                reject(error);
            }
        });
    }

    // Static execute method for global access
    static execute() {
        console.log('LinksExtractorAction: Static execute called');
        const action = new LinksExtractorAction();
        return action.execute();
    }

    // Static reset method for global access
    static reset() {
        console.log('LinksExtractorAction: Static reset called');
        const action = new LinksExtractorAction();
        return action.reset();
    }
}

// Make available globally
if (typeof window !== 'undefined') {
    window.LinksExtractorAction = LinksExtractorAction;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LinksExtractorAction;
} else {
    window.LinksExtractorAction = LinksExtractorAction;
}

} // End of duplicate prevention check 