// Show Links Quick Action - Highlights links with dashed borders based on rel attributes
// Green dashed borders for regular links, red dashed borders for nofollow links

class ShowLinksAction {
    constructor() {
        this.name = 'Show Links';
        this.description = 'Highlights all links on the page with dashed borders - green for regular links, red for nofollow links';
        this.colors = {
            link: '#28a745',        // Regular links - green
            nofollow: '#dc3545'     // Nofollow links - red
        };
    }

    // Execute the show links highlighting with dashed borders
    execute() {
        return new Promise((resolve, reject) => {
            chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                const currentTab = tabs[0];
                
                try {
                    chrome.scripting.executeScript({
                        target: {tabId: currentTab.id},
                        func: function() {
                            // Show links with dashed borders and analysis panel
                            (function() {
                                var links = document.getElementsByTagName('a');
                                var dofollowLinks = [];
                                var nofollowLinks = [];
                                
                                // Analyze and style links
                                var linkCounter = 0; // Use separate counter for indexing to ensure uniqueness
                                for (var i = 0; i < links.length; i++) {
                                    var link = links[i];
                                    var attributes = link.attributes;
                                    var hasNofollow = false;
                                    var hasHref = false;
                                    
                                    // Check for rel="nofollow" and href attributes
                                    for (var j = 0; j < attributes.length; j++) {
                                        var attr = attributes[j];
                                        var attrName = attr.name.toLowerCase();
                                        
                                        if (attrName === 'rel' && attr.value.toLowerCase().indexOf('nofollow') !== -1) {
                                            hasNofollow = true;
                                        }
                                        if (attrName === 'href') {
                                            hasHref = true;
                                        }
                                    }
                                    
                                    // Only style links that have href attribute
                                    if (hasHref) {
                                        // Skip wp-admin links - exclude from detection and reporting
                                        if (link.href && link.href.indexOf('/wp-admin/') !== -1) {
                                            continue; // Skip this link completely
                                        }
                                        
                                        // Remove any existing background colors
                                        link.style.backgroundColor = '';
                                        
                                        // Always add unique ID for jumping (using linkCounter for consistency)
                                        link.setAttribute('data-showlinks-index', linkCounter);
                                        
                                        // Get base anchor text
                                        var baseText = link.textContent.trim() || link.innerText.trim() || '[No Text]';
                                        
                                        // Check for special link types and add prefixes
                                        var prefixes = [];
                                        
                                        // Check if link is in navigation, footer, or aside
                                        var isNavLink = false;
                                        var isFooterLink = false;
                                        var isAsideLink = false;
                                        var parentElement = link.parentElement;
                                        
                                        while (parentElement && parentElement !== document.body) {
                                            var tagName = parentElement.tagName.toLowerCase();
                                            var className = parentElement.className.toLowerCase();
                                            var id = (parentElement.id || '').toLowerCase();
                                            
                                            // Check for nav-related elements and classes
                                            if (tagName === 'nav' || 
                                                tagName === 'header' ||
                                                className.includes('nav') || 
                                                className.includes('menu') || 
                                                className.includes('header') ||
                                                className.includes('navigation') ||
                                                id.includes('nav') ||
                                                id.includes('menu') ||
                                                id.includes('header')) {
                                                isNavLink = true;
                                            }
                                            
                                            // Check for footer-related elements and classes
                                            if (tagName === 'footer' ||
                                                className.includes('footer') ||
                                                className.includes('foot') ||
                                                id.includes('footer') ||
                                                id.includes('foot')) {
                                                isFooterLink = true;
                                            }
                                            
                                            // Check for aside-related elements and classes
                                            if (tagName === 'aside' ||
                                                className.includes('aside') ||
                                                className.includes('sidebar') ||
                                                className.includes('side-bar') ||
                                                id.includes('aside') ||
                                                id.includes('sidebar')) {
                                                isAsideLink = true;
                                            }
                                            
                                            parentElement = parentElement.parentElement;
                                        }
                                        
                                        if (isNavLink) {
                                            prefixes.push('NAV');
                                        }
                                        if (isFooterLink) {
                                            prefixes.push('FOOTER');
                                        }
                                        if (isAsideLink) {
                                            prefixes.push('ASIDE');
                                        }
                                        
                                        // Check if link contains an image
                                        var hasImage = link.querySelector('img') !== null;
                                        if (hasImage) {
                                            prefixes.push('IMG');
                                        }
                                        
                                        // Build final display text with prefixes
                                        var displayText = baseText;
                                        if (prefixes.length > 0) {
                                            displayText = '(' + prefixes.join(', ') + ') ' + baseText;
                                        }
                                        
                                        // Apply dashed border based on nofollow status
                                        if (hasNofollow) {
                                            // Red dashed border for nofollow links
                                            link.style.border = '4px dashed #dc3545';
                                            nofollowLinks.push({
                                                element: link,
                                                text: displayText,
                                                href: link.href,
                                                index: linkCounter
                                            });
                                        } else {
                                            // Green dashed border for regular links
                                            link.style.border = '4px dashed #28a745';
                                            dofollowLinks.push({
                                                element: link,
                                                text: displayText,
                                                href: link.href,
                                                index: linkCounter
                                            });
                                        }
                                        
                                        // Add padding so borders don't hug the text
                                        link.style.padding = '4px 8px';
                                        
                                        // Ensure text remains readable
                                        link.style.borderRadius = '3px';
                                        
                                        linkCounter++; // Increment counter for each valid link
                                    }
                                }
                                
                                console.log(`Show Links: Processed ${linkCounter} links (${dofollowLinks.length} dofollow, ${nofollowLinks.length} nofollow)`);
                                
                                // Helper functions for button actions
                                function jumpToLink(index) {
                                    console.log('ShowLinks: jumpToLink called with index:', index);
                                    
                                    // Clear any existing highlights first
                                    document.querySelectorAll('.showlinks-highlight').forEach(function(el) {
                                        console.log('ShowLinks: Clearing existing highlight from element:', el);
                                        el.classList.remove('showlinks-highlight');
                                        // Restore original styles if they were stored
                                        if (el.hasAttribute('data-showlinks-original-border')) {
                                            el.style.border = el.getAttribute('data-showlinks-original-border');
                                            el.removeAttribute('data-showlinks-original-border');
                                            console.log('ShowLinks: Restored original border');
                                        }
                                        if (el.hasAttribute('data-showlinks-original-box-shadow')) {
                                            el.style.boxShadow = el.getAttribute('data-showlinks-original-box-shadow');
                                            el.removeAttribute('data-showlinks-original-box-shadow');
                                            console.log('ShowLinks: Restored original box shadow');
                                        }
                                    });
                                    
                                    // More reliable element finding - try multiple methods
                                    var targetLink = null;
                                    
                                    // Method 1: Try data attribute (original method)
                                    targetLink = document.querySelector(`[data-showlinks-index="${index}"]`);
                                    console.log('ShowLinks: Method 1 - Target by data attr:', !!targetLink);
                                    
                                    // Method 2: Try to find by href from our stored data (more reliable)
                                    if (!targetLink) {
                                        var allLinksArray = [...dofollowLinks, ...nofollowLinks];
                                        var linkData = allLinksArray.find(l => l.index === index);
                                        console.log('ShowLinks: Method 2 - Found link data:', !!linkData, linkData);
                                        
                                        if (linkData && linkData.href) {
                                            // Try exact href match first
                                            targetLink = document.querySelector(`a[href="${linkData.href}"]`);
                                            console.log('ShowLinks: Method 2a - Target by exact href:', !!targetLink);
                                            
                                            // If exact href fails, try finding by text content and href contains
                                            if (!targetLink && linkData.text) {
                                                var allLinks = document.querySelectorAll('a[href]');
                                                for (var i = 0; i < allLinks.length; i++) {
                                                    var link = allLinks[i];
                                                    var linkText = link.textContent.trim() || link.innerText.trim();
                                                    var linkHref = link.href;
                                                    
                                                    // Remove prefixes from stored text for comparison
                                                    var cleanStoredText = linkData.text.replace(/^\([^)]+\)\s*/, '');
                                                    
                                                    if (linkText === cleanStoredText && linkHref.includes(linkData.href.split('?')[0])) {
                                                        targetLink = link;
                                                        console.log('ShowLinks: Method 2b - Found by text+href match:', targetLink);
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    
                                    // Method 3: Direct array access as last resort
                                    if (!targetLink) {
                                        var allLinksArray = [...dofollowLinks, ...nofollowLinks];
                                        if (allLinksArray[index] && allLinksArray[index].element) {
                                            targetLink = allLinksArray[index].element;
                                            console.log('ShowLinks: Method 3 - Target by array index:', !!targetLink);
                                        }
                                    }
                                    
                                    if (targetLink) {
                                        console.log('ShowLinks: Target found, performing jump sequence');
                                        performJumpSequence(targetLink);
                                    } else {
                                        console.log('ShowLinks: No target found with any method for index:', index);
                                        // Show user feedback that jump failed
                                        var feedback = document.createElement('div');
                                        feedback.style.cssText = 'position:fixed;top:20px;left:50%;transform:translateX(-50%);background:#ef4444;color:white;padding:8px 16px;border-radius:6px;z-index:10000000;font-family:Arial,sans-serif;font-size:14px;font-weight:500;';
                                        feedback.textContent = 'Link not found - it may have been dynamically loaded';
                                        document.body.appendChild(feedback);
                                        setTimeout(function() {
                                            if (feedback.parentNode) {
                                                feedback.remove();
                                            }
                                        }, 3000);
                                    }
                                }
                                
                                // Perform the smooth jump sequence: scroll -> hide panel -> pulse -> show panel
                                function performJumpSequence(targetElement) {
                                    var panel = document.querySelector('.showlinks-panel');
                                    var wasHidden = isPanelHidden;
                                    
                                    // Check if target is in a navigation element
                                    var navContainer = findNavContainer(targetElement);
                                    var elementToFlash = navContainer || targetElement;
                                    
                                    // Step 1: Scroll to target
                                    console.log('ShowLinks: Scrolling to target link');
                                    targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    
                                    // Step 2: Wait for scroll to complete, then hide panel temporarily
                                    setTimeout(function() {
                                        if (panel && !wasHidden) {
                                            panel.style.transition = 'opacity 0.2s ease-out';
                                            panel.style.opacity = '0';
                                            console.log('ShowLinks: Panel hidden for jump sequence');
                                        }
                                        
                                        // Step 3: Start pulsing after panel is hidden
                                        setTimeout(function() {
                                            if (navContainer) {
                                                console.log('ShowLinks: Flashing nav container instead of individual link');
                                                startPulsingHighlight(navContainer, function() {
                                                    // Step 4: Show panel again after pulsing is complete
                                                    if (panel && !wasHidden) {
                                                        panel.style.opacity = '1';
                                                        setTimeout(function() {
                                                            panel.style.transition = '';
                                                        }, 200);
                                                        console.log('ShowLinks: Panel shown after jump sequence');
                                                    }
                                                });
                                            } else {
                                                startPulsingHighlight(targetElement, function() {
                                                    // Step 4: Show panel again after pulsing is complete
                                                    if (panel && !wasHidden) {
                                                        panel.style.opacity = '1';
                                                        setTimeout(function() {
                                                            panel.style.transition = '';
                                                        }, 200);
                                                        console.log('ShowLinks: Panel shown after jump sequence');
                                                    }
                                                });
                                            }
                                        }, 200);
                                    }, 800); // Wait for scroll animation to complete
                                }
                                
                                // Find the navigation container for a link, prioritizing visible parent menu items
                                function findNavContainer(linkElement) {
                                    var parent = linkElement.parentElement;
                                    var navContainer = null;
                                    var bestContainer = null;
                                    var allContainers = [];
                                    
                                    // First, collect all potential navigation containers from link to body
                                    var currentParent = parent;
                                    while (currentParent && currentParent !== document.body) {
                                        var tagName = currentParent.tagName.toLowerCase();
                                        var className = currentParent.className.toLowerCase();
                                        var id = (currentParent.id || '').toLowerCase();
                                        
                                        // Check for nav-related elements and classes
                                        if (tagName === 'nav' || 
                                            tagName === 'header' ||
                                            className.includes('nav') || 
                                            className.includes('menu') || 
                                            className.includes('header') ||
                                            className.includes('navigation') ||
                                            className.includes('dropdown') ||
                                            className.includes('submenu') ||
                                            id.includes('nav') ||
                                            id.includes('menu') ||
                                            id.includes('header') ||
                                            id.includes('dropdown')) {
                                            
                                            allContainers.push({
                                                element: currentParent,
                                                isVisible: isElementVisible(currentParent),
                                                isSubmenu: className.includes('dropdown') || className.includes('submenu') || 
                                                          id.includes('dropdown') || id.includes('submenu'),
                                                level: allContainers.length
                                            });
                                        }
                                        currentParent = currentParent.parentElement;
                                    }
                                    
                                    // Check if link itself is in a list that might be a menu
                                    var listParent = linkElement.closest('ul, ol');
                                    if (listParent && !allContainers.some(c => c.element === listParent)) {
                                        var listClass = listParent.className.toLowerCase();
                                        var listId = (listParent.id || '').toLowerCase();
                                        
                                        if (listClass.includes('menu') || 
                                            listClass.includes('nav') || 
                                            listClass.includes('dropdown') ||
                                            listId.includes('menu') || 
                                            listId.includes('nav') ||
                                            listId.includes('dropdown')) {
                                            allContainers.unshift({
                                                element: listParent,
                                                isVisible: isElementVisible(listParent),
                                                isSubmenu: listClass.includes('dropdown') || listClass.includes('submenu') || 
                                                          listId.includes('dropdown') || listId.includes('submenu'),
                                                level: -1
                                            });
                                        }
                                    }
                                    
                                    console.log('ShowLinks: Found navigation containers:', allContainers.map(c => ({
                                        tag: c.element.tagName,
                                        class: c.element.className,
                                        visible: c.isVisible,
                                        isSubmenu: c.isSubmenu
                                    })));
                                    
                                    // Strategy 1: If the link is in a hidden submenu, find the visible parent menu item
                                    var hiddenContainer = allContainers.find(c => !c.isVisible && c.isSubmenu);
                                    if (hiddenContainer) {
                                        console.log('ShowLinks: Link is in hidden submenu, looking for visible parent');
                                        
                                        // Find the visible parent container that might contain the menu trigger
                                        var visibleParent = allContainers.find(c => c.isVisible && c.level > hiddenContainer.level);
                                        if (visibleParent) {
                                            console.log('ShowLinks: Found visible parent menu container');
                                            return visibleParent.element;
                                        }
                                        
                                        // If no visible parent found, try to find the menu trigger element
                                        var menuTrigger = findMenuTrigger(hiddenContainer.element);
                                        if (menuTrigger && isElementVisible(menuTrigger)) {
                                            console.log('ShowLinks: Found visible menu trigger');
                                            return menuTrigger.closest('li') || menuTrigger.parentElement || menuTrigger;
                                        }
                                    }
                                    
                                    // Strategy 2: Prefer visible containers over hidden ones
                                    var visibleContainer = allContainers.find(c => c.isVisible);
                                    if (visibleContainer) {
                                        console.log('ShowLinks: Using visible container');
                                        return visibleContainer.element;
                                    }
                                    
                                    // Strategy 3: Use the first container if all else fails
                                    if (allContainers.length > 0) {
                                        console.log('ShowLinks: Using first container as fallback');
                                        return allContainers[0].element;
                                    }
                                    
                                    console.log('ShowLinks: No navigation container found');
                                    return null;
                                }
                                
                                // Check if an element is visible (not hidden by CSS)
                                function isElementVisible(element) {
                                    if (!element) return false;
                                    
                                    var style = window.getComputedStyle(element);
                                    var rect = element.getBoundingClientRect();
                                    
                                    return style.display !== 'none' && 
                                           style.visibility !== 'hidden' && 
                                           style.opacity !== '0' &&
                                           rect.width > 0 && 
                                           rect.height > 0;
                                }
                                
                                // Find the menu trigger element for a hidden submenu
                                function findMenuTrigger(submenuElement) {
                                    if (!submenuElement) return null;
                                    
                                    // Look for common menu trigger patterns
                                    var triggers = [
                                        // Previous sibling that might be the trigger
                                        submenuElement.previousElementSibling,
                                        // Parent's first child (common pattern)
                                        submenuElement.parentElement ? submenuElement.parentElement.firstElementChild : null,
                                        // Look for links or buttons near the submenu
                                        submenuElement.parentElement ? submenuElement.parentElement.querySelector('a, button') : null
                                    ];
                                    
                                    // Check each potential trigger
                                    for (var i = 0; i < triggers.length; i++) {
                                        var trigger = triggers[i];
                                        if (trigger && trigger !== submenuElement && isElementVisible(trigger)) {
                                            var triggerClass = trigger.className.toLowerCase();
                                            var triggerTag = trigger.tagName.toLowerCase();
                                            
                                            // Common menu trigger indicators
                                            if (triggerTag === 'a' || triggerTag === 'button' ||
                                                triggerClass.includes('menu') ||
                                                triggerClass.includes('dropdown') ||
                                                triggerClass.includes('toggle') ||
                                                trigger.hasAttribute('aria-haspopup') ||
                                                trigger.hasAttribute('data-toggle')) {
                                                return trigger;
                                            }
                                        }
                                    }
                                    
                                    return null;
                                }
                                
                                // Create pulsing purple highlight effect
                                function startPulsingHighlight(targetElement, onComplete) {
                                    targetElement.classList.add('showlinks-highlight');
                                    
                                    // Store original styles
                                    var originalBorder = targetElement.style.border || '';
                                    var originalBoxShadow = targetElement.style.boxShadow || '';
                                    var originalTransition = targetElement.style.transition || '';
                                    
                                    targetElement.setAttribute('data-showlinks-original-border', originalBorder);
                                    targetElement.setAttribute('data-showlinks-original-box-shadow', originalBoxShadow);
                                    targetElement.setAttribute('data-showlinks-original-transition', originalTransition);
                                    
                                    console.log('ShowLinks: Starting pulsing highlight');
                                    
                                    // Add smooth transition for pulsing
                                    targetElement.style.transition = 'all 0.4s ease-in-out';
                                    
                                    var pulseCount = 0;
                                    var maxPulses = 6; // 3 complete on/off cycles
                                    
                                    function pulse() {
                                        if (pulseCount < maxPulses) {
                                            if (pulseCount % 2 === 0) {
                                                // Pulse ON - bright purple
                                                targetElement.style.border = '4px solid #8b5cf6';
                                                targetElement.style.boxShadow = '0 0 25px rgba(139, 92, 246, 0.8)';
                                                targetElement.style.transform = 'scale(1.02)';
                                            } else {
                                                // Pulse OFF - return to original state but keep class
                                                targetElement.style.border = originalBorder;
                                                targetElement.style.boxShadow = originalBoxShadow;
                                                targetElement.style.transform = 'scale(1)';
                                            }
                                            pulseCount++;
                                            setTimeout(pulse, 400); // 400ms per pulse state
                                        } else {
                                            // Cleanup after pulsing
                                            console.log('ShowLinks: Pulsing complete, cleaning up');
                                            targetElement.style.border = originalBorder;
                                            targetElement.style.boxShadow = originalBoxShadow;
                                            targetElement.style.transform = 'scale(1)';
                                            targetElement.style.transition = originalTransition;
                                            targetElement.classList.remove('showlinks-highlight');
                                            targetElement.removeAttribute('data-showlinks-original-border');
                                            targetElement.removeAttribute('data-showlinks-original-box-shadow');
                                            targetElement.removeAttribute('data-showlinks-original-transition');
                                            
                                            // Call completion callback
                                            if (onComplete) {
                                                onComplete();
                                            }
                                        }
                                    }
                                    
                                    // Start pulsing immediately
                                    pulse();
                                }
                                
                                function copyWithPulse(text, element) {
                                    navigator.clipboard.writeText(text).then(function() {
                                        // Create pulse effect with lighter color
                                        var originalBackground = element.style.background || '';
                                        var pulseCount = 0;
                                        var maxPulses = 4; // 2 on, 2 off = 2 complete pulses
                                        
                                        function pulse() {
                                            if (pulseCount < maxPulses) {
                                                if (pulseCount % 2 === 0) {
                                                    // Pulse on (lighter purple)
                                                    element.style.background = '#a78bfa';
                                                    element.style.transform = 'scale(1.02)';
                                                } else {
                                                    // Pulse off (original background)
                                                    element.style.background = originalBackground;
                                                    element.style.transform = 'scale(1)';
                                                }
                                                pulseCount++;
                                                setTimeout(pulse, 200); // 200ms between pulses
                                            } else {
                                                // Reset to original state
                                                element.style.background = originalBackground;
                                                element.style.transform = 'scale(1)';
                                            }
                                        }
                                        
                                        pulse();
                                    }).catch(function(err) {
                                        console.error('Failed to copy: ', err);
                                    });
                                }
                                
                                function copyAllLinks(btn) {
                                    var filteredRows = document.querySelectorAll('#showlinks-table tbody tr:not([style*="display: none"])');
                                    var visibleLinks = [];
                                    
                                    filteredRows.forEach(function(row) {
                                        var typeCell = row.cells[0];
                                        var textCell = row.cells[1];
                                        var urlCell = row.cells[2];
                                        
                                        if (typeCell && textCell && urlCell) {
                                            var type = typeCell.textContent.trim();
                                            var text = textCell.textContent.trim();
                                            var url = urlCell.textContent.trim();
                                            visibleLinks.push({ text: text, href: url, type: type });
                                        }
                                    });
                                    
                                    var allLinksText = visibleLinks.map(l => `${l.text}\t${l.href}\t${l.type}`).join('\n');
                                    var headerText = 'Anchor Text\tURL\tType\n';
                                    var finalText = visibleLinks.length > 0 ? headerText + allLinksText : 'No visible links to copy';
                                    
                                    navigator.clipboard.writeText(finalText).then(function() {
                                        var originalText = btn.textContent;
                                        btn.textContent = visibleLinks.length > 0 ? `Copied ${visibleLinks.length}!` : 'No data!';
                                        btn.style.background = visibleLinks.length > 0 ? '#10b981' : '#dc3545';
                                        setTimeout(function() {
                                            btn.textContent = originalText;
                                            btn.style.background = '#374151';
                                        }, 1500);
                                        console.log(`${visibleLinks.length} visible links copied to clipboard`);
                                    }).catch(function(err) {
                                        console.error('Failed to copy all links: ', err);
                                    });
                                }
                                
                                function exportLinks() {
                                    var filteredRows = document.querySelectorAll('#showlinks-table tbody tr:not([style*="display: none"])');
                                    var visibleLinks = [];
                                    
                                    filteredRows.forEach(function(row) {
                                        var typeCell = row.cells[0];
                                        var textCell = row.cells[1];
                                        var urlCell = row.cells[2];
                                        
                                        if (typeCell && textCell && urlCell) {
                                            var type = typeCell.textContent.trim();
                                            var text = textCell.textContent.trim();
                                            var url = urlCell.textContent.trim();
                                            visibleLinks.push({ text: text, href: url, type: type });
                                        }
                                    });
                                    
                                    if (visibleLinks.length === 0) {
                                        alert('No visible links to export!');
                                        return;
                                    }
                                    
                                    // Create filename based on current URL
                                    var currentUrl = window.location.hostname;
                                    var sanitizedUrl = currentUrl.replace(/[^a-zA-Z0-9.-]/g, '_'); // Remove invalid filename characters
                                    var timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD format
                                    var filename = `links-analysis-${sanitizedUrl}-${timestamp}-${visibleLinks.length}.csv`;
                                    
                                    var csvContent = 'Anchor Text,URL,Type\n';
                                    csvContent += visibleLinks.map(l => `"${l.text.replace(/"/g, '""')}","${l.href}","${l.type}"`).join('\n');
                                    
                                    var blob = new Blob([csvContent], { type: 'text/csv' });
                                    var url = window.URL.createObjectURL(blob);
                                    var a = document.createElement('a');
                                    a.href = url;
                                    a.download = filename;
                                    a.click();
                                    window.URL.revokeObjectURL(url);
                                    console.log(`Exported ${visibleLinks.length} visible links to CSV: ${filename}`);
                                }
                                
                                function filterTable() {
                                    var filterText = document.getElementById('showlinks-filter-input').value.toLowerCase();
                                    var showDofollow = document.getElementById('showlinks-dofollow-checkbox').checked;
                                    var showNofollow = document.getElementById('showlinks-nofollow-checkbox').checked;
                                    var showImg = document.getElementById('showlinks-img-checkbox').checked;
                                    var showNav = document.getElementById('showlinks-nav-checkbox').checked;
                                    var showAside = document.getElementById('showlinks-aside-checkbox').checked;
                                    var showFooter = document.getElementById('showlinks-footer-checkbox').checked;
                                    var hideNav = document.getElementById('showlinks-hidenav-checkbox').checked;
                                    var hideFooter = document.getElementById('showlinks-hidefooter-checkbox').checked;
                                    var table = document.getElementById('showlinks-table');
                                    var tbody = table.querySelector('tbody');
                                    var rows = tbody.querySelectorAll('tr');
                                    var visibleCount = 0;
                                    
                                    rows.forEach(function(row) {
                                        var cells = row.querySelectorAll('td');
                                        var shouldShow = false;
                                        
                                        if (cells.length >= 3) {
                                            var typeCell = cells[0];
                                            var anchorText = cells[1].textContent.toLowerCase();
                                            var anchorTextFull = cells[1].textContent; // Keep original case for prefix checking
                                            var url = cells[2].textContent.toLowerCase();
                                            var linkType = typeCell.textContent.trim();
                                            
                                            // Check if link has specific prefixes
                                            var hasImg = anchorTextFull.includes('IMG');
                                            var hasNav = anchorTextFull.includes('NAV');
                                            var hasAside = anchorTextFull.includes('ASIDE');
                                            var hasFooter = anchorTextFull.includes('FOOTER');
                                            
                                            // Determine if this link should be shown based on follow type
                                            var followTypeMatches = false;
                                            if (linkType === 'Dofollow' && showDofollow) {
                                                followTypeMatches = true;
                                            } else if (linkType === 'Nofollow' && showNofollow) {
                                                followTypeMatches = true;
                                            }
                                            
                                            // If follow type doesn't match, don't show regardless of other filters
                                            if (!followTypeMatches) {
                                                shouldShow = false;
                                            } else {
                                                // Now check category filters
                                                var categoryMatches = false;
                                                
                                                // If IMG filter is on and link has IMG, show it
                                                if (showImg && hasImg) {
                                                    categoryMatches = true;
                                                }
                                                
                                                // If NAV filter is on and link has NAV, show it
                                                if (showNav && hasNav) {
                                                    categoryMatches = true;
                                                }
                                                
                                                // If ASIDE filter is on and link has ASIDE, show it
                                                if (showAside && hasAside) {
                                                    categoryMatches = true;
                                                }
                                                
                                                // If FOOTER filter is on and link has FOOTER, show it
                                                if (showFooter && hasFooter) {
                                                    categoryMatches = true;
                                                }
                                                
                                                // If none of the special category filters are active, show all other links
                                                // (this covers regular content links)
                                                if (!showImg && !showNav && !showAside && !showFooter) {
                                                    categoryMatches = true;
                                                } else if ((showImg || showNav || showAside || showFooter) && 
                                                          !hasImg && !hasNav && !hasAside && !hasFooter) {
                                                    // If some category filters are active but this link doesn't have any special categories,
                                                    // only show it if ALL category filters are active (showing everything)
                                                    if (showImg && showNav && showAside && showFooter) {
                                                        categoryMatches = true;
                                                    }
                                                }
                                                
                                                // Apply hide filters as exclusions
                                                if (hideNav && hasNav) {
                                                    categoryMatches = false;
                                                }
                                                if (hideFooter && hasFooter) {
                                                    categoryMatches = false;
                                                }
                                                
                                                // Check text filter
                                                var textMatches = filterText === '' || anchorText.includes(filterText) || url.includes(filterText);
                                                
                                                // Final decision: follow type + category + text filter
                                                shouldShow = categoryMatches && textMatches;
                                            }
                                        }
                                        
                                        if (shouldShow) {
                                            row.style.display = '';
                                            visibleCount++;
                                        } else {
                                            row.style.display = 'none';
                                        }
                                    });
                                    
                                    // Update title with filtered count
                                    var title = document.querySelector('.showlinks-panel h1');
                                    if (title) {
                                        var totalCount = dofollowLinks.length + nofollowLinks.length;
                                        var allFiltersDefault = filterText === '' && showDofollow && showNofollow && 
                                                               showImg && showNav && showAside && showFooter &&
                                                               !hideNav && !hideFooter;
                                        if (allFiltersDefault) {
                                            title.textContent = `Links Analysis (${totalCount})`;
                                        } else {
                                            title.textContent = `Links Analysis (${visibleCount} of ${totalCount})`;
                                        }
                                    }
                                }
                                
                                // Create analysis panel
                                function createAnalysisPanel() {
                                    // Remove existing panel if any
                                    var existingPanel = document.querySelector('.showlinks-panel');
                                    if (existingPanel) {
                                        existingPanel.remove();
                                    }
                                    
                                    var panel = document.createElement('div');
                                    panel.className = 'showlinks-panel';
                                    panel.style.cssText = `
                                        position: fixed;
                                        top: 20px;
                                        right: 20px;
                                        z-index: 9999999;
                                        background: #0a0a0a;
                                        color: #d1d5db;
                                        border: 1px solid #3a3a3a;
                                        border-radius: 10px;
                                        box-shadow: 0 8px 32px rgba(0,0,0,0.6);
                                        padding: 20px;
                                        overflow: hidden;
                                        resize: both;
                                        min-width: 500px;
                                        max-width: 80vw;
                                        max-height: 85vh;
                                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                                        font-size: 14px;
                                        line-height: 1.5;
                                    `;
                                    
                                    // Header with drag capability
                                    var header = document.createElement('div');
                                    header.style.cssText = `
                                        display: flex;
                                        justify-content: space-between;
                                        align-items: center;
                                        margin-bottom: 20px;
                                        padding-bottom: 15px;
                                        border-bottom: 1px solid #2a2a2a;
                                        cursor: move;
                                    `;
                                    
                                    var title = document.createElement('h1');
                                    title.textContent = `Links Analysis (${dofollowLinks.length + nofollowLinks.length})`;
                                    title.style.cssText = `
                                        margin: 0;
                                        color: #d1d5db;
                                        font-size: 18px;
                                        font-weight: 600;
                                        letter-spacing: -0.5px;
                                    `;
                                    
                                    // Controls container (eye icon and close button)
                                    var controlsContainer = document.createElement('div');
                                    controlsContainer.style.cssText = `
                                        display: flex;
                                        align-items: center;
                                        gap: 8px;
                                    `;
                                    
                                    // Hide panel button with text below
                                    var eyeBtnContainer = document.createElement('div');
                                    eyeBtnContainer.style.cssText = `
                                        display: flex;
                                        flex-direction: column;
                                        align-items: center;
                                        gap: 4px;
                                    `;
                                    
                                    var eyeBtn = document.createElement('button');
                                    eyeBtn.innerHTML = 'HIDE PANEL';
                                    eyeBtn.style.cssText = `
                                        padding: 8px 12px;
                                        background: #6366f1;
                                        color: white;
                                        border: none;
                                        border-radius: 6px;
                                        cursor: pointer;
                                        font-size: 12px;
                                        font-weight: 500;
                                        transition: all 0.2s;
                                        text-transform: uppercase;
                                        letter-spacing: 0.5px;
                                    `;
                                    
                                    var shortcutText = document.createElement('div');
                                    shortcutText.textContent = 'Shift+Esc to toggle';
                                    shortcutText.style.cssText = `
                                        color: #9ca3af;
                                        font-size: 10px;
                                        text-align: center;
                                        white-space: nowrap;
                                    `;
                                    
                                    eyeBtn.addEventListener('mouseenter', function() {
                                        eyeBtn.style.background = '#5856eb';
                                    });
                                    eyeBtn.addEventListener('mouseleave', function() {
                                        eyeBtn.style.background = '#6366f1';
                                    });
                                    
                                    eyeBtn.onclick = function() {
                                        togglePanelVisibility();
                                    };
                                    
                                    eyeBtnContainer.appendChild(eyeBtn);
                                    eyeBtnContainer.appendChild(shortcutText);
                                    
                                    var closeBtn = document.createElement('button');
                                    closeBtn.innerHTML = '&times;';
                                    closeBtn.style.cssText = `
                                        padding: 8px 12px;
                                        background: #374151;
                                        color: #d1d5db;
                                        border: 1px solid #4b5563;
                                        border-radius: 6px;
                                        cursor: pointer;
                                        font-size: 14px;
                                        font-weight: 500;
                                        transition: all 0.2s;
                                        min-width: 40px;
                                        margin-bottom: 17px;
                                    `;
                                    closeBtn.onclick = function() {
                                        // Same as ESC key - reset everything
                                        const links = document.getElementsByTagName('a');
                                        for (let i = 0; i < links.length; i++) {
                                            const link = links[i];
                                            link.style.backgroundColor = '';
                                            link.style.border = '';
                                            link.style.borderColor = '';
                                            link.style.color = '';
                                            link.style.padding = '';
                                            link.style.borderRadius = '';
                                            link.removeAttribute('data-showlinks-index');
                                        }
                                        
                                        // Clear any remaining jump highlights
                                        document.querySelectorAll('.showlinks-highlight').forEach(function(el) {
                                            el.classList.remove('showlinks-highlight');
                                            if (el.hasAttribute('data-showlinks-original-border')) {
                                                el.style.border = el.getAttribute('data-showlinks-original-border');
                                                el.removeAttribute('data-showlinks-original-border');
                                            }
                                            if (el.hasAttribute('data-showlinks-original-box-shadow')) {
                                                el.style.boxShadow = el.getAttribute('data-showlinks-original-box-shadow');
                                                el.removeAttribute('data-showlinks-original-box-shadow');
                                            }
                                        });
                                        
                                        panel.remove();
                                        document.removeEventListener('keydown', handleKeyDown);
                                        console.log('Show Links highlighting cleared via close button');
                                    };
                                    
                                    closeBtn.addEventListener('mouseenter', function() {
                                        closeBtn.style.background = '#4b5563';
                                        closeBtn.style.borderColor = '#6b7280';
                                    });
                                    closeBtn.addEventListener('mouseleave', function() {
                                        closeBtn.style.background = '#374151';
                                        closeBtn.style.borderColor = '#4b5563';
                                    });
                                    
                                    controlsContainer.appendChild(eyeBtnContainer);
                                    controlsContainer.appendChild(closeBtn);
                                    
                                    header.appendChild(title);
                                    header.appendChild(controlsContainer);
                                    panel.appendChild(header);
                                    
                                    // Summary section
                                    var summarySection = document.createElement('div');
                                    summarySection.style.cssText = `
                                        background: #111111;
                                        border-radius: 8px;
                                        border: 1px solid #2a2a2a;
                                        margin-bottom: 20px;
                                        overflow: hidden;
                                    `;
                                    
                                    var summaryHeader = document.createElement('div');
                                    summaryHeader.textContent = 'Summary';
                                    summaryHeader.style.cssText = `
                                        background: #1a1a1a;
                                        padding: 16px 20px;
                                        border-bottom: 1px solid #2a2a2a;
                                        font-weight: 500;
                                        color: #9ca3af;
                                        font-size: 14px;
                                    `;
                                    summarySection.appendChild(summaryHeader);
                                    
                                    var summaryContent = document.createElement('div');
                                    summaryContent.style.padding = '20px';
                                    
                                    // Stats row with checkboxes
                                    var statsRow = document.createElement('div');
                                    statsRow.style.cssText = `
                                        display: flex; 
                                        gap: 40px; 
                                        margin-bottom: 20px; 
                                        align-items: flex-end;
                                        flex-wrap: wrap;
                                    `;
                                    
                                    // Dofollow stat
                                    var dofollowStat = document.createElement('div');
                                    dofollowStat.innerHTML = `
                                        <div style="font-size: 24px; font-weight: 600; color: #28a745;">${dofollowLinks.length}</div>
                                        <div style="font-size: 13px; color: #9ca3af;">Dofollow Links</div>
                                    `;
                                    
                                    // Nofollow stat
                                    var nofollowStat = document.createElement('div');
                                    nofollowStat.innerHTML = `
                                        <div style="font-size: 24px; font-weight: 600; color: #dc3545;">${nofollowLinks.length}</div>
                                        <div style="font-size: 13px; color: #9ca3af;">Nofollow Links</div>
                                    `;
                                    
                                    // Count different types of links
                                    var allLinks = [...dofollowLinks, ...nofollowLinks];
                                    var totalLinksCount = allLinks.length;
                                    
                                    // Count individual prefix types (these can overlap - same link can be counted multiple times)
                                    var imgLinksCount = allLinks.filter(link => link.text.includes('IMG')).length;
                                    var navLinksCount = allLinks.filter(link => link.text.includes('NAV')).length;
                                    var footerLinksCount = allLinks.filter(link => link.text.includes('FOOTER')).length;
                                    var asideLinksCount = allLinks.filter(link => link.text.includes('ASIDE')).length;
                                    
                                    // First row of stats
                                    var firstStatsRow = document.createElement('div');
                                    firstStatsRow.style.cssText = `
                                        display: flex; 
                                        gap: 40px; 
                                        margin-bottom: 16px; 
                                        align-items: flex-end;
                                        flex-wrap: wrap;
                                    `;
                                    
                                    // IMG stat
                                    var imgStat = document.createElement('div');
                                    imgStat.innerHTML = `
                                        <div style="font-size: 24px; font-weight: 600; color: #f59e0b;">${imgLinksCount}</div>
                                        <div style="font-size: 13px; color: #9ca3af;">IMG Links</div>
                                    `;
                                    
                                    // NAV stat
                                    var navStat = document.createElement('div');
                                    navStat.innerHTML = `
                                        <div style="font-size: 24px; font-weight: 600; color: #8b5cf6;">${navLinksCount}</div>
                                        <div style="font-size: 13px; color: #9ca3af;">NAV Links</div>
                                    `;
                                    
                                    // ASIDE stat
                                    var asideStat = document.createElement('div');
                                    asideStat.innerHTML = `
                                        <div style="font-size: 24px; font-weight: 600; color: #06b6d4;">${asideLinksCount}</div>
                                        <div style="font-size: 13px; color: #9ca3af;">ASIDE Links</div>
                                    `;
                                    
                                    // FOOTER stat
                                    var footerStat = document.createElement('div');
                                    footerStat.innerHTML = `
                                        <div style="font-size: 24px; font-weight: 600; color: #64748b;">${footerLinksCount}</div>
                                        <div style="font-size: 13px; color: #9ca3af;">FOOTER Links</div>
                                    `;
                                    
                                    // Total stat
                                    var totalStat = document.createElement('div');
                                    totalStat.innerHTML = `
                                        <div style="font-size: 24px; font-weight: 600; color: #d1d5db;">${totalLinksCount}</div>
                                        <div style="font-size: 13px; color: #9ca3af;">Total Links</div>
                                    `;
                                    
                                    firstStatsRow.appendChild(dofollowStat);
                                    firstStatsRow.appendChild(nofollowStat);
                                    firstStatsRow.appendChild(imgStat);
                                    firstStatsRow.appendChild(navStat);
                                    firstStatsRow.appendChild(asideStat);
                                    firstStatsRow.appendChild(footerStat);
                                    firstStatsRow.appendChild(totalStat);
                                    
                                    // Update the original statsRow to use firstStatsRow
                                    statsRow = firstStatsRow;
                                    
                                    // Buttons row
                                    var buttonsRow = document.createElement('div');
                                    buttonsRow.style.cssText = `
                                        display: flex; 
                                        gap: 10px; 
                                        align-items: center;
                                        margin-bottom: 16px;
                                    `;
                                    
                                    var exportCsvBtn = document.createElement('button');
                                    exportCsvBtn.id = 'exportCsvBtn';
                                    exportCsvBtn.textContent = 'Export CSV';
                                    exportCsvBtn.style.cssText = `
                                        padding: 8px 16px; background: #6366f1; color: white; border: none; 
                                        border-radius: 6px; cursor: pointer; font-size: 13px; transition: all 0.2s;
                                    `;
                                    
                                    var copyAllBtn = document.createElement('button');
                                    copyAllBtn.id = 'copyAllBtn';
                                    copyAllBtn.textContent = 'Copy All';
                                    copyAllBtn.style.cssText = `
                                        padding: 8px 16px; background: #374151; color: #d1d5db; 
                                        border: 1px solid #4b5563; border-radius: 6px; cursor: pointer; 
                                        font-size: 13px; transition: all 0.2s;
                                    `;
                                    
                                    buttonsRow.appendChild(exportCsvBtn);
                                    buttonsRow.appendChild(copyAllBtn);
                                    
                                    // Filters container - separate section with better spacing
                                    var filtersContainer = document.createElement('div');
                                    filtersContainer.style.cssText = `
                                        background: #1a1a1a;
                                        border-radius: 8px;
                                        border: 1px solid #2a2a2a;
                                        padding: 16px;
                                        margin-top: 4px;
                                    `;
                                    
                                    var filtersTitle = document.createElement('div');
                                    filtersTitle.textContent = 'Filters';
                                    filtersTitle.style.cssText = `
                                        color: #9ca3af;
                                        font-size: 13px;
                                        font-weight: 500;
                                        margin-bottom: 8px;
                                    `;
                                    
                                    var filtersNote = document.createElement('div');
                                    filtersNote.innerHTML = `
                                        <span style="color: #9ca3af; font-size: 11px; font-style: italic;">
                                            Uses AND logic: Select multiple filters to narrow results (e.g., Dofollow + IMG shows only dofollow image links)
                                        </span>
                                    `;
                                    filtersNote.style.cssText = `margin-bottom: 12px;`;
                                    
                                    var filtersControls = document.createElement('div');
                                    filtersControls.style.cssText = `
                                        display: flex;
                                        gap: 24px;
                                        align-items: center;
                                        flex-wrap: wrap;
                                    `;
                                    
                                    // Show Dofollow toggle
                                    var dofollowControl = document.createElement('div');
                                    dofollowControl.style.cssText = `display: flex; align-items: center; gap: 8px;`;
                                    
                                    var dofollowCheckbox = document.createElement('input');
                                    dofollowCheckbox.type = 'checkbox';
                                    dofollowCheckbox.id = 'showlinks-dofollow-checkbox';
                                    dofollowCheckbox.checked = true;
                                    dofollowCheckbox.style.cssText = `
                                        width: 16px; height: 16px; accent-color: #7c3aed;
                                        background: #262626; border: 1px solid #404040;
                                        border-radius: 3px; cursor: pointer;
                                    `;
                                    
                                    var dofollowLabel = document.createElement('label');
                                    dofollowLabel.htmlFor = 'showlinks-dofollow-checkbox';
                                    dofollowLabel.textContent = 'Show Dofollow';
                                    dofollowLabel.style.cssText = `
                                        color: #d1d5db; font-size: 13px; cursor: pointer;
                                        user-select: none; white-space: nowrap;
                                    `;
                                    
                                    dofollowControl.appendChild(dofollowCheckbox);
                                    dofollowControl.appendChild(dofollowLabel);
                                    
                                    // Show Nofollow toggle
                                    var nofollowControl = document.createElement('div');
                                    nofollowControl.style.cssText = `display: flex; align-items: center; gap: 8px;`;
                                    
                                    var nofollowCheckbox = document.createElement('input');
                                    nofollowCheckbox.type = 'checkbox';
                                    nofollowCheckbox.id = 'showlinks-nofollow-checkbox';
                                    nofollowCheckbox.checked = true;
                                    nofollowCheckbox.style.cssText = `
                                        width: 16px; height: 16px; accent-color: #7c3aed;
                                        background: #262626; border: 1px solid #404040;
                                        border-radius: 3px; cursor: pointer;
                                    `;
                                    
                                    var nofollowLabel = document.createElement('label');
                                    nofollowLabel.htmlFor = 'showlinks-nofollow-checkbox';
                                    nofollowLabel.textContent = 'Show Nofollow';
                                    nofollowLabel.style.cssText = `
                                        color: #d1d5db; font-size: 13px; cursor: pointer;
                                        user-select: none; white-space: nowrap;
                                    `;
                                    
                                    nofollowControl.appendChild(nofollowCheckbox);
                                    nofollowControl.appendChild(nofollowLabel);
                                    
                                    // Show IMG toggle
                                    var imgControl = document.createElement('div');
                                    imgControl.style.cssText = `display: flex; align-items: center; gap: 8px;`;
                                    
                                    var imgCheckbox = document.createElement('input');
                                    imgCheckbox.type = 'checkbox';
                                    imgCheckbox.id = 'showlinks-img-checkbox';
                                    imgCheckbox.checked = true;
                                    imgCheckbox.style.cssText = `
                                        width: 16px; height: 16px; accent-color: #7c3aed;
                                        background: #262626; border: 1px solid #404040;
                                        border-radius: 3px; cursor: pointer;
                                    `;
                                    
                                    var imgLabel = document.createElement('label');
                                    imgLabel.htmlFor = 'showlinks-img-checkbox';
                                    imgLabel.textContent = 'Show IMG';
                                    imgLabel.style.cssText = `
                                        color: #d1d5db; font-size: 13px; cursor: pointer;
                                        user-select: none; white-space: nowrap;
                                    `;
                                    
                                    imgControl.appendChild(imgCheckbox);
                                    imgControl.appendChild(imgLabel);
                                    
                                    // Show NAV toggle
                                    var navControl = document.createElement('div');
                                    navControl.style.cssText = `display: flex; align-items: center; gap: 8px;`;
                                    
                                    var navCheckbox = document.createElement('input');
                                    navCheckbox.type = 'checkbox';
                                    navCheckbox.id = 'showlinks-nav-checkbox';
                                    navCheckbox.checked = true;
                                    navCheckbox.style.cssText = `
                                        width: 16px; height: 16px; accent-color: #7c3aed;
                                        background: #262626; border: 1px solid #404040;
                                        border-radius: 3px; cursor: pointer;
                                    `;
                                    
                                    var navLabel = document.createElement('label');
                                    navLabel.htmlFor = 'showlinks-nav-checkbox';
                                    navLabel.textContent = 'Show NAV';
                                    navLabel.style.cssText = `
                                        color: #d1d5db; font-size: 13px; cursor: pointer;
                                        user-select: none; white-space: nowrap;
                                    `;
                                    
                                    navControl.appendChild(navCheckbox);
                                    navControl.appendChild(navLabel);
                                    
                                    // Hide NAV toggle
                                    var hideNavControl = document.createElement('div');
                                    hideNavControl.style.cssText = `display: flex; align-items: center; gap: 8px;`;
                                    
                                    var hideNavCheckbox = document.createElement('input');
                                    hideNavCheckbox.type = 'checkbox';
                                    hideNavCheckbox.id = 'showlinks-hidenav-checkbox';
                                    hideNavCheckbox.checked = false;
                                    hideNavCheckbox.style.cssText = `
                                        width: 16px; height: 16px; accent-color: #7c3aed;
                                        background: #262626; border: 1px solid #404040;
                                        border-radius: 3px; cursor: pointer;
                                    `;
                                    
                                    var hideNavLabel = document.createElement('label');
                                    hideNavLabel.htmlFor = 'showlinks-hidenav-checkbox';
                                    hideNavLabel.textContent = 'Hide NAV';
                                    hideNavLabel.style.cssText = `
                                        color: #d1d5db; font-size: 13px; cursor: pointer;
                                        user-select: none; white-space: nowrap;
                                    `;
                                    
                                    hideNavControl.appendChild(hideNavCheckbox);
                                    hideNavControl.appendChild(hideNavLabel);
                                    
                                    // Show ASIDE toggle
                                    var asideControl = document.createElement('div');
                                    asideControl.style.cssText = `display: flex; align-items: center; gap: 8px;`;
                                    
                                    var asideCheckbox = document.createElement('input');
                                    asideCheckbox.type = 'checkbox';
                                    asideCheckbox.id = 'showlinks-aside-checkbox';
                                    asideCheckbox.checked = true;
                                    asideCheckbox.style.cssText = `
                                        width: 16px; height: 16px; accent-color: #7c3aed;
                                        background: #262626; border: 1px solid #404040;
                                        border-radius: 3px; cursor: pointer;
                                    `;
                                    
                                    var asideLabel = document.createElement('label');
                                    asideLabel.htmlFor = 'showlinks-aside-checkbox';
                                    asideLabel.textContent = 'Show ASIDE';
                                    asideLabel.style.cssText = `
                                        color: #d1d5db; font-size: 13px; cursor: pointer;
                                        user-select: none; white-space: nowrap;
                                    `;
                                    
                                    asideControl.appendChild(asideCheckbox);
                                    asideControl.appendChild(asideLabel);
                                    
                                    // Show FOOTER toggle
                                    var footerControl = document.createElement('div');
                                    footerControl.style.cssText = `display: flex; align-items: center; gap: 8px;`;
                                    
                                    var footerCheckbox = document.createElement('input');
                                    footerCheckbox.type = 'checkbox';
                                    footerCheckbox.id = 'showlinks-footer-checkbox';
                                    footerCheckbox.checked = true;
                                    footerCheckbox.style.cssText = `
                                        width: 16px; height: 16px; accent-color: #7c3aed;
                                        background: #262626; border: 1px solid #404040;
                                        border-radius: 3px; cursor: pointer;
                                    `;
                                    
                                    var footerLabel = document.createElement('label');
                                    footerLabel.htmlFor = 'showlinks-footer-checkbox';
                                    footerLabel.textContent = 'Show FOOTER';
                                    footerLabel.style.cssText = `
                                        color: #d1d5db; font-size: 13px; cursor: pointer;
                                        user-select: none; white-space: nowrap;
                                    `;
                                    
                                    footerControl.appendChild(footerCheckbox);
                                    footerControl.appendChild(footerLabel);
                                    
                                    // Hide FOOTER toggle
                                    var hideFooterControl = document.createElement('div');
                                    hideFooterControl.style.cssText = `display: flex; align-items: center; gap: 8px;`;
                                    
                                    var hideFooterCheckbox = document.createElement('input');
                                    hideFooterCheckbox.type = 'checkbox';
                                    hideFooterCheckbox.id = 'showlinks-hidefooter-checkbox';
                                    hideFooterCheckbox.checked = false;
                                    hideFooterCheckbox.style.cssText = `
                                        width: 16px; height: 16px; accent-color: #7c3aed;
                                        background: #262626; border: 1px solid #404040;
                                        border-radius: 3px; cursor: pointer;
                                    `;
                                    
                                    var hideFooterLabel = document.createElement('label');
                                    hideFooterLabel.htmlFor = 'showlinks-hidefooter-checkbox';
                                    hideFooterLabel.textContent = 'Hide FOOTER';
                                    hideFooterLabel.style.cssText = `
                                        color: #d1d5db; font-size: 13px; cursor: pointer;
                                        user-select: none; white-space: nowrap;
                                    `;
                                    
                                    hideFooterControl.appendChild(hideFooterCheckbox);
                                    hideFooterControl.appendChild(hideFooterLabel);
                                    
                                    // Filter input container
                                    var filterInputContainer = document.createElement('div');
                                    filterInputContainer.style.cssText = `
                                        display: flex; align-items: center; gap: 8px; 
                                        flex: 1; min-width: 280px;
                                    `;
                                    
                                    var filterLabel = document.createElement('label');
                                    filterLabel.textContent = 'Filter:';
                                    filterLabel.style.cssText = `
                                        color: #9ca3af; font-size: 13px; font-weight: 500;
                                        white-space: nowrap;
                                    `;
                                    
                                    var filterInput = document.createElement('input');
                                    filterInput.type = 'text';
                                    filterInput.id = 'showlinks-filter-input';
                                    filterInput.placeholder = 'Filter by anchor text or URL...';
                                    filterInput.style.cssText = `
                                        flex: 1; padding: 8px 12px; background: #262626;
                                        border: 1px solid #404040; border-radius: 6px;
                                        color: #d1d5db; font-size: 13px;
                                        transition: border-color 0.2s; min-width: 200px;
                                    `;
                                    
                                    filterInput.addEventListener('focus', function() {
                                        filterInput.style.borderColor = '#7c3aed';
                                    });
                                    
                                    filterInput.addEventListener('blur', function() {
                                        filterInput.style.borderColor = '#404040';
                                    });
                                    
                                    filterInputContainer.appendChild(filterLabel);
                                    filterInputContainer.appendChild(filterInput);
                                    
                                    filtersControls.appendChild(dofollowControl);
                                    filtersControls.appendChild(nofollowControl);
                                    filtersControls.appendChild(imgControl);
                                    filtersControls.appendChild(navControl);
                                    filtersControls.appendChild(hideNavControl);
                                    filtersControls.appendChild(asideControl);
                                    filtersControls.appendChild(footerControl);
                                    filtersControls.appendChild(hideFooterControl);
                                    filtersControls.appendChild(filterInputContainer);
                                    
                                    filtersContainer.appendChild(filtersTitle);
                                    filtersContainer.appendChild(filtersNote);
                                    filtersContainer.appendChild(filtersControls);
                                    
                                    summaryContent.appendChild(statsRow);
                                    summaryContent.appendChild(buttonsRow);
                                    summaryContent.appendChild(filtersContainer);
                                    summarySection.appendChild(summaryContent);
                                    
                                    // Global filter states variable - persists across pages
                                    if (!window.showLinksGlobalFilters) {
                                        // Initialize with defaults or load from localStorage
                                        try {
                                            var savedFilters = localStorage.getItem('showlinks-filter-states');
                                            if (savedFilters) {
                                                window.showLinksGlobalFilters = JSON.parse(savedFilters);
                                                console.log('ShowLinks: Loaded global filter states from localStorage', window.showLinksGlobalFilters);
                                            } else {
                                                // Default filter states
                                                window.showLinksGlobalFilters = {
                                                    dofollow: true,
                                                    nofollow: true,
                                                    img: true,
                                                    nav: true,
                                                    aside: true,
                                                    footer: true,
                                                    hideNav: false,
                                                    hideFooter: false,
                                                    text: ''
                                                };
                                                console.log('ShowLinks: Initialized default global filter states');
                                            }
                                        } catch (e) {
                                            console.log('ShowLinks: Error loading filter states, using defaults', e);
                                            window.showLinksGlobalFilters = {
                                                dofollow: true,
                                                nofollow: true,
                                                img: true,
                                                nav: true,
                                                aside: true,
                                                footer: true,
                                                hideNav: false,
                                                hideFooter: false,
                                                text: ''
                                            };
                                        }
                                    }
                                    
                                    // Load saved filter states from global variable
                                    function loadFilterStates() {
                                        try {
                                            var filters = window.showLinksGlobalFilters;
                                            dofollowCheckbox.checked = filters.dofollow;
                                            nofollowCheckbox.checked = filters.nofollow;
                                            imgCheckbox.checked = filters.img;
                                            navCheckbox.checked = filters.nav;
                                            asideCheckbox.checked = filters.aside;
                                            footerCheckbox.checked = filters.footer;
                                            hideNavCheckbox.checked = filters.hideNav || false;
                                            hideFooterCheckbox.checked = filters.hideFooter || false;
                                            filterInput.value = filters.text || '';
                                            console.log('ShowLinks: Applied filter states from global variable', filters);
                                            return true;
                                        } catch (e) {
                                            console.log('ShowLinks: Error applying filter states', e);
                                            return false;
                                        }
                                    }
                                    
                                    // Save filter states to both global variable and localStorage
                                    function saveFilterStates() {
                                        try {
                                            var filters = {
                                                dofollow: dofollowCheckbox.checked,
                                                nofollow: nofollowCheckbox.checked,
                                                img: imgCheckbox.checked,
                                                nav: navCheckbox.checked,
                                                aside: asideCheckbox.checked,
                                                footer: footerCheckbox.checked,
                                                hideNav: hideNavCheckbox.checked,
                                                hideFooter: hideFooterCheckbox.checked,
                                                text: filterInput.value
                                            };
                                            
                                            // Save to global variable for immediate use
                                            window.showLinksGlobalFilters = filters;
                                            
                                            // Save to localStorage for persistence across browser sessions
                                            localStorage.setItem('showlinks-filter-states', JSON.stringify(filters));
                                            
                                            console.log('ShowLinks: Saved filter states to global variable and localStorage', filters);
                                        } catch (e) {
                                            console.log('ShowLinks: Error saving filter states', e);
                                        }
                                    }
                                    
                                    // Update visual styling based on current filter states
                                    function updateVisualStyling() {
                                        var hideNav = document.getElementById('showlinks-hidenav-checkbox').checked;
                                        var hideFooter = document.getElementById('showlinks-hidefooter-checkbox').checked;
                                        
                                        // Get all styled links
                                        var styledLinks = document.querySelectorAll('[data-showlinks-index]');
                                        
                                        styledLinks.forEach(function(link) {
                                            var index = parseInt(link.getAttribute('data-showlinks-index'));
                                            var allLinksArray = [...dofollowLinks, ...nofollowLinks];
                                            var linkData = allLinksArray.find(l => l.index === index);
                                            
                                            if (linkData) {
                                                var hasNav = linkData.text.includes('NAV');
                                                var hasFooter = linkData.text.includes('FOOTER');
                                                var shouldHideVisually = (hideNav && hasNav) || (hideFooter && hasFooter);
                                                
                                                if (shouldHideVisually) {
                                                    // Hide visual styling
                                                    link.style.border = '';
                                                    link.style.padding = '';
                                                    link.style.borderRadius = '';
                                                } else {
                                                    // Restore original styling based on follow type
                                                    var isNofollow = link.getAttribute('rel') && 
                                                        link.getAttribute('rel').toLowerCase().includes('nofollow');
                                                    var borderColor = isNofollow ? '#dc3545' : '#28a745';
                                                    link.style.border = `4px dashed ${borderColor}`;
                                                    link.style.padding = '4px 8px';
                                                    link.style.borderRadius = '3px';
                                                }
                                            }
                                        });
                                    }
                                    
                                    // Load saved states first
                                    var filtersLoaded = loadFilterStates();
                                    
                                    // Apply initial filter after loading states
                                    if (filtersLoaded) {
                                        // Small delay to ensure DOM is ready
                                        setTimeout(function() {
                                            filterTable();
                                            updateVisualStyling();
                                            console.log('ShowLinks: Initial filter applied with loaded states');
                                        }, 50);
                                    }
                                    
                                    // Add event listeners for real-time filtering with state saving
                                    dofollowCheckbox.addEventListener('change', function() { filterTable(); saveFilterStates(); });
                                    nofollowCheckbox.addEventListener('change', function() { filterTable(); saveFilterStates(); });
                                    imgCheckbox.addEventListener('change', function() { filterTable(); saveFilterStates(); });
                                    navCheckbox.addEventListener('change', function() { 
                                        if (navCheckbox.checked) {
                                            hideNavCheckbox.checked = false;
                                        }
                                        filterTable(); 
                                        updateVisualStyling();
                                        saveFilterStates(); 
                                    });
                                    asideCheckbox.addEventListener('change', function() { filterTable(); saveFilterStates(); });
                                    footerCheckbox.addEventListener('change', function() { 
                                        if (footerCheckbox.checked) {
                                            hideFooterCheckbox.checked = false;
                                        }
                                        filterTable(); 
                                        updateVisualStyling();
                                        saveFilterStates(); 
                                    });
                                    filterInput.addEventListener('input', function() { filterTable(); saveFilterStates(); });
                                    hideNavCheckbox.addEventListener('change', function() { 
                                        if (hideNavCheckbox.checked) {
                                            navCheckbox.checked = false;
                                        }
                                        filterTable(); 
                                        updateVisualStyling();
                                        saveFilterStates(); 
                                    });
                                    hideFooterCheckbox.addEventListener('change', function() { 
                                        if (hideFooterCheckbox.checked) {
                                            footerCheckbox.checked = false;
                                        }
                                        filterTable(); 
                                        updateVisualStyling();
                                        saveFilterStates(); 
                                    });
                                    
                                    // Add event listeners for summary buttons
                                    exportCsvBtn.addEventListener('click', function() {
                                        exportLinks();
                                    });
                                    
                                    copyAllBtn.addEventListener('click', function() {
                                        copyAllLinks(copyAllBtn);
                                    });
                                    
                                    // Add hover effects
                                    exportCsvBtn.addEventListener('mouseenter', function() {
                                        exportCsvBtn.style.background = '#5856eb';
                                    });
                                    exportCsvBtn.addEventListener('mouseleave', function() {
                                        exportCsvBtn.style.background = '#6366f1';
                                    });
                                    
                                    copyAllBtn.addEventListener('mouseenter', function() {
                                        copyAllBtn.style.background = '#4b5563';
                                        copyAllBtn.style.borderColor = '#6b7280';
                                    });
                                    copyAllBtn.addEventListener('mouseleave', function() {
                                        copyAllBtn.style.background = '#374151';
                                        copyAllBtn.style.borderColor = '#4b5563';
                                    });
                                    
                                    panel.appendChild(summarySection);
                                    
                                    // Prevent background page scrolling when scrolling inside the panel
                                    panel.addEventListener('wheel', function(e) {
                                        e.stopPropagation();
                                        
                                        // Check if we're at the scroll boundaries to allow panel scrolling
                                        var scrollableElement = e.target.closest('[style*="overflow"]') || panel.querySelector('#showlinks-table').closest('div');
                                        if (scrollableElement) {
                                            var atTop = scrollableElement.scrollTop === 0;
                                            var atBottom = scrollableElement.scrollTop >= (scrollableElement.scrollHeight - scrollableElement.clientHeight);
                                            
                                            // Only prevent default if we're not at boundaries or if scrolling in the direction that stays within bounds
                                            if ((!atTop && e.deltaY < 0) || (!atBottom && e.deltaY > 0)) {
                                                // Allow scrolling within the panel
                                                return;
                                            }
                                        }
                                        e.preventDefault();
                                    }, { passive: false });
                                    
                                    panel.addEventListener('touchmove', function(e) {
                                        e.stopPropagation();
                                    }, { passive: true });
                                    
                                    // Links table
                                    var tableSection = document.createElement('div');
                                    tableSection.style.cssText = `
                                        background: #111111;
                                        border-radius: 8px;
                                        border: 1px solid #2a2a2a;
                                        overflow: hidden;
                                        display: flex;
                                        flex-direction: column;
                                    `;
                                    
                                    var tableHeader = document.createElement('div');
                                    tableHeader.textContent = 'All Links';
                                    tableHeader.style.cssText = `
                                        background: #1a1a1a;
                                        padding: 16px 20px;
                                        border-bottom: 1px solid #2a2a2a;
                                        font-weight: 500;
                                        color: #9ca3af;
                                        font-size: 14px;
                                    `;
                                    tableSection.appendChild(tableHeader);
                                    
                                    var tableContainer = document.createElement('div');
                                    tableContainer.style.cssText = `
                                        flex: 1;
                                        overflow-y: auto;
                                        overflow-x: hidden;
                                        max-height: 400px;
                                        min-height: 200px;
                                    `;
                                    
                                    var table = document.createElement('table');
                                    table.id = 'showlinks-table';
                                    table.style.cssText = `
                                        width: 100%;
                                        border-collapse: collapse;
                                    `;
                                    
                                    // Table header
                                    var thead = document.createElement('thead');
                                    thead.style.cssText = `
                                        background: #1a1a1a;
                                        position: sticky;
                                        top: 0;
                                        z-index: 10;
                                    `;
                                    thead.innerHTML = `
                                        <tr>
                                            <th style="padding: 12px 20px; color: #9ca3af; font-size: 13px; font-weight: 500; text-align: left; border-bottom: 1px solid #2a2a2a; border-right: 1px solid #2a2a2a; background: #1a1a1a;">Type</th>
                                            <th style="padding: 12px 20px; color: #9ca3af; font-size: 13px; font-weight: 500; text-align: left; border-bottom: 1px solid #2a2a2a; border-right: 1px solid #2a2a2a; background: #1a1a1a;">Anchor Text</th>
                                            <th style="padding: 12px 20px; color: #9ca3af; font-size: 13px; font-weight: 500; text-align: left; border-bottom: 1px solid #2a2a2a; border-right: 1px solid #2a2a2a; background: #1a1a1a;">URL (Click to Copy)</th>
                                            <th style="padding: 12px 20px; color: #9ca3af; font-size: 13px; font-weight: 500; text-align: left; border-bottom: 1px solid #2a2a2a; background: #1a1a1a;">Actions</th>
                                        </tr>
                                    `;
                                    table.appendChild(thead);
                                    
                                    // Table body
                                    var tbody = document.createElement('tbody');
                                    var allLinks = [...dofollowLinks.map(l => ({...l, type: 'Dofollow'})), ...nofollowLinks.map(l => ({...l, type: 'Nofollow'}))];
                                    
                                    allLinks.forEach(function(linkData, index) {
                                        var row = document.createElement('tr');
                                        row.style.cssText = `
                                            border-bottom: 1px solid #2a2a2a;
                                            transition: background 0.2s;
                                        `;
                                        row.onmouseover = function() { this.style.background = '#0a0a0a'; };
                                        row.onmouseout = function() { this.style.background = ''; };
                                        
                                        var typeColor = linkData.type === 'Dofollow' ? '#28a745' : '#dc3545';
                                        
                                        row.innerHTML = `
                                            <td style="padding: 16px 20px; vertical-align: top; border-right: 1px solid #2a2a2a; line-height: 1.5;">
                                                <span style="color: ${typeColor}; font-weight: 500; font-size: 12px; text-transform: uppercase;">${linkData.type}</span>
                                            </td>
                                            <td class="anchor-text-cell" style="padding: 16px 20px; vertical-align: top; border-right: 1px solid #2a2a2a; line-height: 1.5; max-width: 200px; word-break: break-word; color: #d1d5db; cursor: pointer; transition: all 0.2s;">${linkData.text}</td>
                                            <td class="url-cell" style="padding: 16px 20px; vertical-align: top; border-right: 1px solid #2a2a2a; line-height: 1.5; max-width: 300px; word-break: break-all; font-family: monospace; font-size: 12px; cursor: pointer; transition: all 0.2s;">${linkData.href}</td>
                                            <td style="padding: 16px 20px; vertical-align: top; line-height: 1.5;">
                                                <button class="jump-btn" style="padding: 4px 8px; background: #6366f1; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px; transition: all 0.2s;">Jump</button>
                                            </td>
                                        `;
                                        tbody.appendChild(row);
                                        
                                        // Add event listeners
                                        var jumpBtn = row.querySelector('.jump-btn');
                                        var urlCell = row.querySelector('.url-cell');
                                        var anchorCell = row.querySelector('.anchor-text-cell');
                                        
                                        jumpBtn.addEventListener('click', function() {
                                            jumpToLink(linkData.index);
                                        });
                                        
                                        jumpBtn.addEventListener('mouseenter', function() {
                                            jumpBtn.style.background = '#5856eb';
                                        });
                                        jumpBtn.addEventListener('mouseleave', function() {
                                            jumpBtn.style.background = '#6366f1';
                                        });
                                        
                                        // Make URL cell clickable to copy URL
                                        urlCell.addEventListener('click', function() {
                                            copyWithPulse(linkData.href, urlCell);
                                        });
                                        
                                        urlCell.addEventListener('mouseenter', function() {
                                            urlCell.style.background = '#1a1a1a';
                                            urlCell.style.color = '#7c3aed';
                                        });
                                        urlCell.addEventListener('mouseleave', function() {
                                            urlCell.style.background = '';
                                            urlCell.style.color = '#6366f1';
                                        });
                                        
                                        // Make anchor text cell clickable to copy text
                                        anchorCell.addEventListener('click', function() {
                                            copyWithPulse(linkData.text, anchorCell);
                                        });
                                        
                                        anchorCell.addEventListener('mouseenter', function() {
                                            anchorCell.style.background = '#1a1a1a';
                                            anchorCell.style.color = '#7c3aed';
                                        });
                                        anchorCell.addEventListener('mouseleave', function() {
                                            anchorCell.style.background = '';
                                            anchorCell.style.color = '#d1d5db';
                                        });
                                    });
                                    
                                    table.appendChild(tbody);
                                    tableContainer.appendChild(table);
                                    tableSection.appendChild(tableContainer);
                                    panel.appendChild(tableSection);
                                    
                                    // Make panel draggable
                                    var isDragging = false;
                                    var currentX, currentY, initialX, initialY, xOffset = 0, yOffset = 0;
                                    
                                    header.addEventListener('mousedown', function(e) {
                                        initialX = e.clientX - xOffset;
                                        initialY = e.clientY - yOffset;
                                        isDragging = true;
                                    });
                                    
                                    document.addEventListener('mousemove', function(e) {
                                        if (isDragging) {
                                            currentX = e.clientX - initialX;
                                            currentY = e.clientY - initialY;
                                            xOffset = currentX;
                                            yOffset = currentY;
                                            panel.style.transform = `translate(${currentX}px, ${currentY}px)`;
                                        }
                                    });
                                    
                                    document.addEventListener('mouseup', function() {
                                        isDragging = false;
                                    });
                                    
                                    document.body.appendChild(panel);
                                }
                                
                                // Panel visibility state
                                var isPanelHidden = false;
                                
                                // Toggle panel visibility function
                                function togglePanelVisibility() {
                                    var panel = document.querySelector('.showlinks-panel');
                                    if (panel) {
                                        if (isPanelHidden) {
                                            // Show panel
                                            panel.style.display = '';
                                            isPanelHidden = false;
                                            console.log('Show Links panel shown');
                                        } else {
                                            // Hide panel
                                            panel.style.display = 'none';
                                            isPanelHidden = true;
                                            console.log('Show Links panel hidden');
                                        }
                                    }
                                }
                                
                                // Create the analysis panel
                                createAnalysisPanel();
                                
                                // Handle keyboard shortcuts
                                function handleKeyDown(e) {
                                    if (e.key === 'Escape' && e.shiftKey) {
                                        // SHIFT+ESC: Toggle panel visibility
                                        e.preventDefault();
                                        togglePanelVisibility();
                                    } else if (e.key === 'Escape' && !e.shiftKey) {
                                        // ESC alone: Reset link highlighting and remove panel completely
                                        const links = document.getElementsByTagName('a');
                                        for (let i = 0; i < links.length; i++) {
                                            const link = links[i];
                                            // Remove styles applied by this action
                                            link.style.backgroundColor = '';
                                            link.style.border = '';
                                            link.style.borderColor = '';
                                            link.style.color = '';
                                            link.style.padding = '';
                                            link.style.borderRadius = '';
                                            link.removeAttribute('data-showlinks-index');
                                        }
                                        
                                        // Clear any remaining jump highlights
                                        document.querySelectorAll('.showlinks-highlight').forEach(function(el) {
                                            el.classList.remove('showlinks-highlight');
                                            if (el.hasAttribute('data-showlinks-original-border')) {
                                                el.style.border = el.getAttribute('data-showlinks-original-border');
                                                el.removeAttribute('data-showlinks-original-border');
                                            }
                                            if (el.hasAttribute('data-showlinks-original-box-shadow')) {
                                                el.style.boxShadow = el.getAttribute('data-showlinks-original-box-shadow');
                                                el.removeAttribute('data-showlinks-original-box-shadow');
                                            }
                                        });
                                        
                                        // Remove panel
                                        var panel = document.querySelector('.showlinks-panel');
                                        if (panel) panel.remove();
                                        document.removeEventListener('keydown', handleKeyDown);
                                        console.log('Show Links highlighting cleared');
                                    }
                                }
                                document.addEventListener('keydown', handleKeyDown);
                                
                                console.log('Show Links highlighting applied - Green: regular links, Red: nofollow links');
                                return true;
                            })();
                        }
                    }, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('Error executing Show Links script:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            console.log('Show Links script executed successfully');
                            resolve(result);
                        }
                    });
                } catch (error) {
                    console.error('Error setting up Show Links script execution:', error);
                    reject(error);
                }
            });
        });
    }

    // Reset the show links highlighting (remove all modifications)
    reset() {
        return new Promise((resolve, reject) => {
            chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                const currentTab = tabs[0];
                
                try {
                    chrome.scripting.executeScript({
                        target: {tabId: currentTab.id},
                        func: function() {
                            // Reset function to undo the show links changes
                            const links = document.getElementsByTagName('a');
                            for (let i = 0; i < links.length; i++) {
                                const link = links[i];
                                // Remove all styles applied by this action
                                link.style.backgroundColor = '';
                                link.style.border = '';
                                link.style.borderColor = '';
                                link.style.color = '';
                                link.style.padding = '';
                                link.style.borderRadius = '';
                                link.removeAttribute('data-showlinks-index');
                            }
                            
                            // Clear any remaining jump highlights
                            document.querySelectorAll('.showlinks-highlight').forEach(function(el) {
                                el.classList.remove('showlinks-highlight');
                                if (el.hasAttribute('data-showlinks-original-border')) {
                                    el.style.border = el.getAttribute('data-showlinks-original-border');
                                    el.removeAttribute('data-showlinks-original-border');
                                }
                                if (el.hasAttribute('data-showlinks-original-box-shadow')) {
                                    el.style.boxShadow = el.getAttribute('data-showlinks-original-box-shadow');
                                    el.removeAttribute('data-showlinks-original-box-shadow');
                                }
                            });
                            
                            // Remove analysis panel if it exists
                            var panel = document.querySelector('.showlinks-panel');
                            if (panel) panel.remove();
                            console.log('Show Links reset completed');
                            return true;
                        }
                    }, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('Error resetting Show Links:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            console.log('Show Links reset successfully');
                            resolve(result);
                        }
                    });
                } catch (error) {
                    console.error('Error setting up Show Links reset:', error);
                    reject(error);
                }
            });
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ShowLinksAction;
} else {
    window.ShowLinksAction = ShowLinksAction;
} 