// Font Styles Action - Live viewport resizing with real-time font analysis
class FontStylesAction {
    static execute() {
        try {
            // Execute directly in the page context (like a bookmarklet)
            // Remove existing font styles elements if present
            document.querySelectorAll('.font-styles-viewport, .font-styles-overlay, .font-styles-results').forEach(el => el.remove());
            
            // Comprehensive breakpoints for responsive analysis
            const breakpoints = [1920, 1440, 1200, 992, 768, 576, 480, 375, 320];
            const breakpointNames = ['Desktop XL', 'Desktop L', 'Desktop', 'Tablet L', 'Tablet', 'Mobile L', 'Mobile M', 'Mobile S', 'Mobile XS'];
            
            let currentIndex = 0;
            let fontData = [];
            let isAnalyzing = true;
            let viewport, overlay, resultsPanel;
            
            // Create viewport iframe exactly like the bookmarklet
            viewport = document.createElement('iframe');
            viewport.className = 'font-styles-viewport';
            viewport.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border: 0;
                z-index: 999998;
                transition: width 0.8s cubic-bezier(0.4,0,0.2,1);
            `;
            
            // Create overlay with controls and info
            overlay = document.createElement('div');
            overlay.className = 'font-styles-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                width: 320px;
                background: #0a0a0a;
                color: #d1d5db;
                border: 1px solid #1f1f1f;
                border-radius: 10px;
                padding: 20px;
                z-index: 999999;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                font-size: 14px;
                line-height: 1.5;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0,0,0,0.6);
            `;
            
            // Clone the current page content (exactly like bookmarklet)
            const headContent = document.head.innerHTML;
            const bodyContent = document.body.innerHTML.replace(/<div[^>]*class="font-styles-[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '');
            
            // Set iframe content exactly like the bookmarklet
            viewport.srcdoc = `<!DOCTYPE html><html><head>${headContent}</head><body>${bodyContent}</body></html>`;
            
            // Initial overlay content - showing analysis in progress
            overlay.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; padding-bottom: 8px; border-bottom: 1px solid #374151;">
                    <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: #f59e0b;">Font Styles Analyzer</h3>
                    <button id="font-styles-close" style="padding:6px 12px;background:#6b7280;color:#d1d5db;border:1px solid #9ca3af;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">×</button>
                </div>
                <div id="font-styles-status">
                    <div style="text-align: center; margin-bottom: 16px;">
                        <div style="font-size: 16px; font-weight: 600; color: #f59e0b; margin-bottom: 8px;">Starting Analysis...</div>
                        <div style="font-size: 12px; color: #9ca3af;">Analyzing responsive fonts across ${breakpoints.length} breakpoints</div>
                    </div>
                </div>
            `;
            
            // Add elements to page
            document.body.appendChild(viewport);
            document.body.appendChild(overlay);
            
            // Function to collect font data from current viewport
            function collectCurrentFontData(breakpointName, width) {
                try {
                    const iframeDoc = viewport.contentDocument || viewport.contentWindow.document;
                    const iframeWindow = viewport.contentWindow;
                    
                    if (!iframeDoc || !iframeWindow) {
                        console.log('No iframe document available');
                        return [];
                    }
                    
                    const elements = Array.from(iframeDoc.querySelectorAll('*')).filter(el => {
                        try {
                            // Exclude font-styles analyzer elements and popups
                            if (el.className && typeof el.className === 'string') {
                                const className = el.className.toLowerCase();
                                // Exclude font-styles related elements
                                if (className.includes('font-styles') || 
                                    className.includes('font-analysis') ||
                                    className.includes('breakpoint-')) {
                                    return false;
                                }
                                
                                // Exclude other popup/overlay elements that might interfere
                                if (className.includes('overlay') ||
                                    className.includes('popup') ||
                                    className.includes('modal') ||
                                    className.includes('tooltip') ||
                                    className.includes('notification') ||
                                    className.includes('analyzer') ||
                                    className.includes('panel') ||
                                    className.includes('results')) {
                                    return false;
                                }
                            }
                            
                            // Exclude elements by tag name (tables used in results)
                            const tagName = el.tagName.toLowerCase();
                            if (tagName === 'table' || tagName === 'thead' || tagName === 'tbody' || 
                                tagName === 'tr' || tagName === 'td' || tagName === 'th') {
                                // Only exclude if it's inside a font-styles related container
                                const fontStylesContainer = el.closest('[class*="font-styles"], [class*="breakpoint-"], [class*="analyzer"]');
                                if (fontStylesContainer) {
                                    return false;
                                }
                            }
                            
                            // Exclude elements with data attributes related to font analysis
                            if (el.hasAttribute('data-font-styles') ||
                                el.hasAttribute('data-breakpoint') ||
                                el.hasAttribute('data-analysis')) {
                                return false;
                            }
                            
                            // Exclude if parent element is a font-styles container
                            const parentContainer = el.closest('.font-styles-overlay, .font-styles-results, .font-styles-viewport, [class*="font-analysis"], [class*="breakpoint-"]');
                            if (parentContainer) {
                                return false;
                            }
                            
                            const style = iframeWindow.getComputedStyle(el);
                            const text = (el.innerText || el.textContent || '').trim();
                            return style && style.fontFamily && text && text.length > 0;
                        } catch (e) {
                            return false;
                        }
                    });
                    
                    const fontMap = new Map();
                    const seenCombinations = new Set();
                    
                    elements.forEach(el => {
                        try {
                            const style = iframeWindow.getComputedStyle(el);
                            const elementTag = el.tagName.toLowerCase();
                            
                            const fontKey = `${style.fontFamily}|${style.fontSize}|${style.fontWeight}|${elementTag}`;
                            if (seenCombinations.has(fontKey)) return;
                            seenCombinations.add(fontKey);
                            
                            const font = {
                                family: style.fontFamily.split(',')[0].replace(/['"]/g, '').trim(),
                                size: style.fontSize,
                                weight: style.fontWeight,
                                lineHeight: style.lineHeight,
                                element: elementTag,
                                breakpoint: breakpointName,
                                width: width
                            };
                            
                            const mapKey = `${font.family}|${font.element}`;
                            if (!fontMap.has(mapKey)) {
                                fontMap.set(mapKey, font);
                            }
                        } catch (e) {
                            // Skip elements that can't be styled
                        }
                    });
                    
                    return Array.from(fontMap.values());
                } catch (error) {
                    console.error('Error collecting font data:', error);
                    return [];
                }
            }
            
            // Function to update overlay with current analysis
            function updateAnalysisStatus(breakpointName, width, collected) {
                const statusDiv = overlay.querySelector('#font-styles-status');
                if (statusDiv) {
                    statusDiv.innerHTML = `
                        <div style="margin-bottom: 8px;">
                            <div style="font-weight: 600; color: #f59e0b;">${breakpointName}</div>
                            <div style="font-size: 12px; color: #9ca3af;">${width}px viewport</div>
                        </div>
                        <div style="margin-bottom: 12px;">
                            <div style="font-size: 12px; color: #10b981;">Fonts detected: ${collected}</div>
                        </div>
                        <div style="width: 100%; height: 6px; background: #374151; border-radius: 3px; overflow: hidden;">
                            <div style="width: ${((currentIndex + 1) / breakpoints.length) * 100}%; height: 100%; background: linear-gradient(90deg, #f59e0b, #10b981); transition: width 0.3s;"></div>
                        </div>
                        <div style="font-size: 11px; color: #6b7280; margin-top: 4px; text-align: center;">
                            ${currentIndex + 1} of ${breakpoints.length} breakpoints
                        </div>
                        <button onclick="FontStylesAction.reset()" style="width: 100%; padding:6px 12px;background:#6b7280;color:#d1d5db;border:1px solid #9ca3af;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s; margin-top: 12px;">Stop Analysis</button>
                    `;
                }
            }
            
            // Function to animate through breakpoints (like the bookmarklet)
            function animateToNextBreakpoint() {
                if (currentIndex >= breakpoints.length) {
                    showResults();
                    return;
                }
                
                const width = breakpoints[currentIndex];
                const name = breakpointNames[currentIndex];
                
                // Update viewport width with animation (like bookmarklet)
                viewport.style.width = width + 'px';
                
                updateAnalysisStatus(name, width, '...');
                
                // Wait for animation to complete, then collect data
                setTimeout(() => {
                    const collected = collectCurrentFontData(name, width);
                    fontData.push(...collected);
                    updateAnalysisStatus(name, width, collected.length);
                    
                    currentIndex++;
                    setTimeout(animateToNextBreakpoint, 600);
                }, 400);
            }
            
            // Function to organize data by breakpoint for slider
            function organizeDataByBreakpoint() {
                const breakpointData = {};
                
                // Initialize breakpoint data structure
                breakpoints.forEach((width, index) => {
                    breakpointData[width] = {
                        name: breakpointNames[index],
                        width: width,
                        fontFamilies: new Set(),
                        headings: {},
                        bodyText: {},
                        elements: {}
                    };
                });
                
                // Populate with font data
                fontData.forEach(font => {
                    const bpData = breakpointData[font.width];
                    if (!bpData) return;
                    
                    const fontFamily = font.family;
                    const element = font.element;
                    const size = font.size;
                    const isHeading = /^h[1-6]$/i.test(element);
                    const isBodyText = ['p', 'div', 'span', 'li', 'td', 'th'].includes(element);
                    
                    // Track font families used at this breakpoint
                    bpData.fontFamilies.add(fontFamily);
                    
                    // Track element-specific data
                    const elementKey = `${element}_${fontFamily}`;
                    if (!bpData.elements[elementKey]) {
                        bpData.elements[elementKey] = {
                            family: fontFamily,
                            size: size,
                            element: element,
                            isHeading: isHeading,
                            isBodyText: isBodyText
                        };
                    }
                    
                    // Group by categories
                    if (isHeading) {
                        bpData.headings[elementKey] = {
                            family: fontFamily,
                            size: size,
                            element: element
                        };
                    }
                    
                    if (isBodyText) {
                        bpData.bodyText[elementKey] = {
                            family: fontFamily,
                            size: size,
                            element: element
                        };
                    }
                });
                
                return breakpointData;
            }
            
            // Function to render a single breakpoint tab content
            function renderBreakpointTab(bpData) {
                let html = '';
                
                // Font families summary for this breakpoint
                html += '<div style="margin-bottom:20px;">';
                html += '<div style="font-weight:500;color:#f59e0b;margin-bottom:8px;font-size:13px;">Font Families Used</div>';
                html += '<div style="background:#1a1a1a;padding:12px;border-radius:6px;border:1px solid #2a2a2a;">';
                const families = Array.from(bpData.fontFamilies);
                if (families.length > 0) {
                    families.forEach(family => {
                        html += `<div style="font-size:12px;color:#d1d5db;margin-bottom:4px;font-family:${family}">${family}</div>`;
                    });
                } else {
                    html += '<div style="font-size:12px;color:#6b7280;">No fonts detected</div>';
                }
                html += '</div>';
                html += '</div>';
                
                // Headings section
                if (Object.keys(bpData.headings).length > 0) {
                    html += '<div style="margin-bottom:20px;">';
                    html += '<div style="font-weight:500;color:#10b981;margin-bottom:8px;font-size:13px;">Heading Elements</div>';
                    html += '<div style="overflow-x:auto;">';
                    html += '<table style="width:100%;border-collapse:collapse;font-size:12px;background:#1a1a1a;border-radius:6px;overflow:hidden;">';
                    html += '<thead><tr style="background:#262626;">';
                    html += '<th style="padding:8px;text-align:left;border:1px solid #2a2a2a;color:#9ca3af;font-weight:500;">Element</th>';
                    html += '<th style="padding:8px;text-align:left;border:1px solid #2a2a2a;color:#9ca3af;font-weight:500;">Font Family</th>';
                    html += '<th style="padding:8px;text-align:left;border:1px solid #2a2a2a;color:#9ca3af;font-weight:500;">Size</th>';
                    html += '</tr></thead><tbody>';
                    
                    // Sort headings by h1, h2, h3, h4, h5, h6 order
                    const sortedHeadings = Object.entries(bpData.headings).sort(([keyA, dataA], [keyB, dataB]) => {
                        const getHeadingOrder = (element) => {
                            const match = element.match(/h([1-6])/i);
                            return match ? parseInt(match[1]) : 999;
                        };
                        return getHeadingOrder(dataA.element) - getHeadingOrder(dataB.element);
                    });
                    
                    sortedHeadings.forEach(([key, data]) => {
                        html += '<tr style="border-bottom:1px solid #2a2a2a;">';
                        html += `<td style="padding:6px 8px;border:1px solid #2a2a2a;font-weight:500;color:#d1d5db;">${data.element.toUpperCase()}</td>`;
                        html += `<td style="padding:6px 8px;border:1px solid #2a2a2a;font-family:monospace;color:#9ca3af;font-size:11px;">${data.family}</td>`;
                        html += `<td style="padding:6px 8px;border:1px solid #2a2a2a;font-family:monospace;color:#10b981;font-weight:500;">${data.size}</td>`;
                        html += '</tr>';
                    });
                    
                    html += '</tbody></table>';
                    html += '</div>';
                    html += '</div>';
                }
                
                // Body text section
                if (Object.keys(bpData.bodyText).length > 0) {
                    html += '<div style="margin-bottom:20px;">';
                    html += '<div style="font-weight:500;color:#e5e7eb;margin-bottom:8px;font-size:13px;">Body Text Elements</div>';
                    html += '<div style="overflow-x:auto;">';
                    html += '<table style="width:100%;border-collapse:collapse;font-size:12px;background:#1a1a1a;border-radius:6px;overflow:hidden;">';
                    html += '<thead><tr style="background:#262626;">';
                    html += '<th style="padding:8px;text-align:left;border:1px solid #2a2a2a;color:#9ca3af;font-weight:500;">Element</th>';
                    html += '<th style="padding:8px;text-align:left;border:1px solid #2a2a2a;color:#9ca3af;font-weight:500;">Font Family</th>';
                    html += '<th style="padding:8px;text-align:left;border:1px solid #2a2a2a;color:#9ca3af;font-weight:500;">Size</th>';
                    html += '</tr></thead><tbody>';
                    
                    Object.entries(bpData.bodyText).forEach(([key, data]) => {
                        html += '<tr style="border-bottom:1px solid #2a2a2a;">';
                        html += `<td style="padding:6px 8px;border:1px solid #2a2a2a;font-weight:500;color:#d1d5db;">&lt;${data.element}&gt;</td>`;
                        html += `<td style="padding:6px 8px;border:1px solid #2a2a2a;font-family:monospace;color:#9ca3af;font-size:11px;">${data.family}</td>`;
                        html += `<td style="padding:6px 8px;border:1px solid #2a2a2a;font-family:monospace;color:#e5e7eb;font-weight:500;">${data.size}</td>`;
                        html += '</tr>';
                    });
                
                    html += '</tbody></table>';
                    html += '</div>';
                    html += '</div>';
                }
                
                return html;
            }
            
            // Function to show final results with breakpoint slider
            function showResults() {
                isAnalyzing = false;
                
                // Restore full viewport (like bookmarklet cleanup)
                viewport.style.width = '100%';
                
                // Organize data by breakpoint
                const breakpointData = {};
                fontData.forEach(font => {
                    if (!breakpointData[font.breakpoint]) {
                        breakpointData[font.breakpoint] = [];
                    }
                    breakpointData[font.breakpoint].push(font);
                });
                
                // Find responsive fonts (fonts that change size across breakpoints)
                const responsiveFonts = new Map();
                const fontTracker = new Map();
                
                fontData.forEach(font => {
                    const key = `${font.family}|${font.element}`;
                    if (!fontTracker.has(key)) {
                        fontTracker.set(key, []);
                    }
                    fontTracker.get(key).push({
                        breakpoint: font.breakpoint,
                        size: font.size,
                        width: font.width
                    });
                });
                
                fontTracker.forEach((sizes, key) => {
                    const uniqueSizes = [...new Set(sizes.map(s => s.size))];
                    if (uniqueSizes.length > 1) {
                        responsiveFonts.set(key, sizes);
                    }
                });
                
                // Get organized breakpoint data for slider
                const organizedBreakpointData = organizeDataByBreakpoint();
                
                // Create results panel
                resultsPanel = document.createElement('div');
                resultsPanel.className = 'font-styles-results';
                resultsPanel.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 90%;
                    max-width: 1200px;
                    max-height: 80vh;
                    background: #0a0a0a;
                    color: #d1d5db;
                    border: 1px solid #1f1f1f;
                    border-radius: 10px;
                    z-index: 999999;
                    overflow: hidden;
                    backdrop-filter: blur(20px);
                    box-shadow: 0 8px 32px rgba(0,0,0,0.6);
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                    font-size: 14px;
                    line-height: 1.5;
                `;
                
                let resultsHTML = `
                    <div style="padding: 20px; border-bottom: 1px solid #2a2a2a; display: flex; justify-content: space-between; align-items: center;">
                        <h2 style="margin: 0; font-size: 18px; font-weight: 600; color: #d1d5db; letter-spacing: -0.5px;">Font Analysis Results</h2>
                        <button id="font-results-close" style="padding:6px 12px;background:#6b7280;color:#d1d5db;border:1px solid #9ca3af;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">×</button>
                    </div>
                    <div style="padding: 20px; overflow-y: auto; max-height: calc(80vh - 80px);">
                `;
                
                // Font Usage by Breakpoint section moved to top
                resultsHTML += `
                    <div style="background:#111111;border-radius:6px;border:1px solid #2a2a2a;margin-bottom:20px;overflow:hidden;">
                        <div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Font Usage by Breakpoint</div>
                        <div style="padding:16px;">
                            <div style="margin-bottom:30px;">
                                <div style="margin-bottom:16px;text-align:center;color:#9ca3af;font-size:13px;font-weight:500;">Breakpoint Selector</div>
                                <div id="breakpoint-slider-container" style="position:relative;padding:20px 40px;">
                                    <div id="breakpoint-slider-track" style="position:relative;height:8px;background:linear-gradient(to right, #6366f1 0%, #6366f1 12.5%, #4b5563 12.5%, #4b5563 100%);border-radius:4px;margin:20px 0;">
                                        <div id="breakpoint-slider-handle" style="position:absolute;top:50%;left:0%;width:20px;height:20px;background:#6366f1;border:3px solid #ffffff;border-radius:50%;cursor:grab;transform:translate(-50%, -50%);box-shadow:0 2px 8px rgba(0,0,0,0.3);transition:all 0.2s;z-index:10;"></div>
                                    </div>
                                    <div style="position:relative;display:flex;justify-content:space-between;margin-top:8px;">
                `;
                
                // Add breakpoint labels (smallest to largest, left to right)
                const sortedBreakpointsForSlider = Object.values(organizedBreakpointData).sort((a, b) => a.width - b.width);
                sortedBreakpointsForSlider.forEach((bp, index) => {
                    const position = (index / (sortedBreakpointsForSlider.length - 1)) * 100;
                    resultsHTML += `<div class="breakpoint-label" data-index="${index}" style="position:absolute;left:${position}%;transform:translateX(-50%);font-size:11px;color:#9ca3af;font-weight:500;cursor:pointer;padding:4px 8px;border-radius:4px;transition:all 0.2s;white-space:nowrap;">${bp.name}</div>`;
                });
                
                resultsHTML += `
                                    </div>
                                </div>
                            </div>
                `;
                
                // Create tab content containers
                sortedBreakpointsForSlider.forEach((bp, index) => {
                    const isActive = index === 0;
                    resultsHTML += `<div class="breakpoint-tab-content" data-content="bp-${bp.width}" style="display:${isActive ? 'block' : 'none'};">`;
                    resultsHTML += renderBreakpointTab(bp);
                    resultsHTML += '</div>';
                });
                
                resultsHTML += `</div></div>`;
                
                // Responsive fonts summary
                if (responsiveFonts.size > 0) {
                    resultsHTML += `
                        <div style="margin-bottom: 30px;">
                            <h3 style="color: #10b981; margin-bottom: 16px; font-size: 16px;">Responsive Fonts</h3>
                            <div style="background: #0f1419; border: 1px solid #374151; border-radius: 8px; padding: 16px;">
                    `;
                    
                    responsiveFonts.forEach((sizes, key) => {
                        const [family, element] = key.split('|');
                        sizes.sort((a, b) => b.width - a.width);
                        
                        resultsHTML += `
                            <div style="margin-bottom: 16px; padding-bottom: 16px; border-bottom: 1px solid #374151;">
                                <div style="font-weight: 600; margin-bottom: 8px; color: #d1d5db;">
                                    ${family} <span style="color: #6b7280; font-weight: normal; font-size: 12px;">&lt;${element}&gt;</span>
                                </div>
                                <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                        `;
                        
                        sizes.forEach(size => {
                            resultsHTML += `
                                <span style="background: #374151; padding: 4px 8px; border-radius: 4px; font-size: 11px; font-family: monospace;">
                                    ${size.breakpoint}: ${size.size}
                                </span>
                            `;
                        });
                        
                        resultsHTML += `</div></div>`;
                    });
                    
                    resultsHTML += `</div></div>`;
                } else {
                    resultsHTML += `
                        <div style="margin-bottom: 30px;">
                            <h3 style="color: #6b7280; margin-bottom: 16px; font-size: 16px;">No Responsive Fonts Found</h3>
                            <div style="background: #0f1419; border: 1px solid #374151; border-radius: 8px; padding: 16px; text-align: center; color: #6b7280;">
                                All fonts maintain the same size across breakpoints
                            </div>
                        </div>
                    `;
                }
                
                resultsHTML += `</div>`;
                
                resultsPanel.innerHTML = resultsHTML;
                document.body.appendChild(resultsPanel);
                
                // Add slider functionality
                const sliderHandle = resultsPanel.querySelector('#breakpoint-slider-handle');
                const sliderTrack = resultsPanel.querySelector('#breakpoint-slider-track');
                const breakpointLabels = resultsPanel.querySelectorAll('.breakpoint-label');
                const tabContents = resultsPanel.querySelectorAll('.breakpoint-tab-content');
                
                let currentBreakpointIndex = 0;
                let isSliderDragging = false;
                
                // Function to update slider position and content
                function updateSlider(index) {
                    currentBreakpointIndex = Math.max(0, Math.min(index, sortedBreakpointsForSlider.length - 1));
                    const position = (currentBreakpointIndex / (sortedBreakpointsForSlider.length - 1)) * 100;
                    
                    // Update handle position
                    sliderHandle.style.left = position + '%';
                    
                    // Update track gradient
                    sliderTrack.style.background = `linear-gradient(to right, #6366f1 0%, #6366f1 ${position}%, #4b5563 ${position}%, #4b5563 100%)`;
                    
                    // Update label states
                    breakpointLabels.forEach((label, i) => {
                        if (i === currentBreakpointIndex) {
                            label.style.color = '#6366f1';
                            label.style.background = 'rgba(99, 102, 241, 0.1)';
                        } else {
                            label.style.color = '#9ca3af';
                            label.style.background = 'transparent';
                        }
                    });
                    
                    // Update content visibility
                    const targetBreakpoint = sortedBreakpointsForSlider[currentBreakpointIndex];
                    tabContents.forEach(content => {
                        content.style.display = 'none';
                    });
                    const targetContent = resultsPanel.querySelector(`[data-content="bp-${targetBreakpoint.width}"]`);
                    if (targetContent) {
                        targetContent.style.display = 'block';
                    }
                }
                
                // Slider events
                function handleSliderMouseDown(e) {
                    isSliderDragging = true;
                    sliderHandle.style.transform = 'translate(-50%, -50%) scale(1.1)';
                    sliderHandle.style.cursor = 'grabbing';
                    document.body.style.userSelect = 'none';
                    e.preventDefault();
                    e.stopPropagation();
                }
                
                function handleSliderMouseMove(e) {
                    if (!isSliderDragging) return;
                    
                    const rect = sliderTrack.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
                    const index = Math.round((percentage / 100) * (sortedBreakpointsForSlider.length - 1));
                    
                    updateSlider(index);
                    e.preventDefault();
                }
                
                function handleSliderMouseUp(e) {
                    if (isSliderDragging) {
                        isSliderDragging = false;
                        sliderHandle.style.transform = 'translate(-50%, -50%) scale(1)';
                        sliderHandle.style.cursor = 'grab';
                        document.body.style.userSelect = '';
                        e.preventDefault();
                    }
                }
                
                // Attach slider events
                sliderHandle.addEventListener('mousedown', handleSliderMouseDown);
                document.addEventListener('mousemove', handleSliderMouseMove);
                document.addEventListener('mouseup', handleSliderMouseUp);
                
                // Click on track to jump to position
                sliderTrack.addEventListener('click', (e) => {
                    if (isSliderDragging) return;
                    
                    const rect = sliderTrack.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
                    const index = Math.round((percentage / 100) * (sortedBreakpointsForSlider.length - 1));
                    
                    updateSlider(index);
                    e.preventDefault();
                    e.stopPropagation();
                });
                
                // Click on labels to jump to that breakpoint
                breakpointLabels.forEach((label, index) => {
                    label.addEventListener('click', () => {
                        updateSlider(index);
                    });
                });
                
                // Initialize slider
                updateSlider(0);
                
                // Add event listener for results panel close button
                const resultsCloseBtn = resultsPanel.querySelector('#font-results-close');
                if (resultsCloseBtn) {
                    resultsCloseBtn.addEventListener('click', function() {
                        FontStylesAction.reset();
                    });
                }
                
                // Close the overlay automatically when results are shown
                if (overlay) {
                    overlay.remove();
                }
            }
            
            // Handle escape key
            function handleEscape(e) {
                if (e.key === 'Escape') {
                    FontStylesAction.reset();
                }
            }
            document.addEventListener('keydown', handleEscape);
            window.fontStylesEscapeListener = handleEscape;
            
            // Event listeners for controls
            overlay.addEventListener('click', (e) => {
                if (e.target.id === 'font-styles-close') {
                    FontStylesAction.reset();
                }
            });
            
            // Auto-start analysis immediately
            setTimeout(() => {
                animateToNextBreakpoint();
            }, 100);
            
        } catch (error) {
            console.error('Font Styles Action error:', error);
            alert('Font Styles Action failed: ' + error.message);
        }
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                // Clean up all elements directly
                document.querySelectorAll('.font-styles-viewport, .font-styles-overlay, .font-styles-results').forEach(el => el.remove());
                
                // Clean up global escape listener
                if (window.fontStylesEscapeListener) {
                    document.removeEventListener('keydown', window.fontStylesEscapeListener);
                    delete window.fontStylesEscapeListener;
                }
                
                console.log('Font Styles Action reset completed');
                resolve();
            } catch (error) {
                console.error('Font Styles Action reset error:', error);
                resolve();
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FontStylesAction;
} else {
    window.FontStylesAction = FontStylesAction;
} 