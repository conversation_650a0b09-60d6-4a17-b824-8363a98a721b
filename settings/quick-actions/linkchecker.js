/**
 * Link Checker Action (404 Checker)
 * Checks for broken links on the current page, exactly like the original bookmarklet
 */
class LinkCheckerAction {
  
  static execute() {
    try {
      console.log('404 Link Checker activated');
      
      // Remove any existing link checker elements - following golden rules pattern
      LinkCheckerAction.reset();
      
      // Copy the working implementation EXACTLY
      var linkChecker = (function() {
        "use strict";
        let links, errors = [], completed = [], errorCounter = 0;
        
        // Track XHR requests for proper cleanup
        const xhrRequests = [];

        function addStyle(styleString) {
          const style = document.createElement("style");
          style.id = "linkCheckerStyles";
          style.textContent = styleString;
          document.head.append(style);
        }

        function finished(counter) {
          completed.push(counter);

          if (completed.length === links.length) {
            errors = errors.filter(e => typeof e === "object");
            if (errors.length > 0) {
              // Create sidebar with broken links list
              addStyle(`
                #linkCheckerSidebar {
                  position:fixed;
                  left:20px;
                  top:20px;
                  width:350px;
                  max-height:calc(100vh - 40px);
                  background:#0a0a0a;
                  border:1px solid #f87171;
                  border-radius:8px;
                  color:#f87171;
                  z-index:9999999;
                  font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;
                  box-shadow:0 4px 16px rgba(0,0,0,0.4);
                  overflow:hidden;
                  display:flex;
                  flex-direction:column;
                }
                #linkCheckerSidebar .header {
                  background:#1f2937;
                  padding:12px 16px;
                  border-bottom:1px solid #374151;
                  display:flex;
                  justify-content:space-between;
                  align-items:center;
                  font-weight:600;
                  font-size:14px;
                  cursor:move;
                  user-select:none;
                }
                #linkCheckerSidebar .header button {
                  background:#374151;
                  color:#d1d5db;
                  border:1px solid #4b5563;
                  border-radius:4px;
                  padding:4px 8px;
                  cursor:pointer;
                  font-size:11px;
                  transition:all 0.2s;
                }
                #linkCheckerSidebar .header button:hover {
                  background:#4b5563;
                  color:#f9fafb;
                }
                #linkCheckerSidebar .links-list {
                  flex:1;
                  overflow-y:auto;
                  padding:8px;
                  max-height:400px;
                }
                #linkCheckerSidebar .link-item {
                  padding:8px 12px;
                  margin:4px 0;
                  background:#1f2937;
                  border:1px solid #374151;
                  border-radius:6px;
                  cursor:pointer;
                  transition:all 0.2s;
                }
                #linkCheckerSidebar .link-item:hover {
                  background:#374151;
                  border-color:#4b5563;
                }
                #linkCheckerSidebar .link-text {
                  font-size:12px;
                  font-weight:500;
                  margin-bottom:4px;
                  color:#f9fafb;
                  white-space:nowrap;
                  overflow:hidden;
                  text-overflow:ellipsis;
                }
                #linkCheckerSidebar .link-url {
                  font-size:10px;
                  color:#9ca3af;
                  font-family:monospace;
                  white-space:nowrap;
                  overflow:hidden;
                  text-overflow:ellipsis;
                }
                .linkError {
                  border:2px solid #f87171 !important;
                  border-radius:4px !important;
                  padding:2px 4px !important;
                  background:rgba(248,113,113,0.1) !important;
                  transition:all 0.3s !important;
                }
                .linkError:target {
                  background:rgba(248,113,113,0.3) !important;
                  color:#ffffff !important;
                  box-shadow:0 0 10px rgba(248,113,113,0.5) !important;
                }
                .linkError:target::after {
                  content:attr(href);
                  color:#ffffff;
                  position:fixed;
                  bottom:20px;
                  right:20px;
                  background:#0a0a0a;
                  border:1px solid #f87171;
                  border-radius:6px;
                  padding:8px 12px;
                  font-family:monospace;
                  font-size:12px;
                  z-index:9999999;
                  max-width:300px;
                  word-break:break-all;
                }
              `);
              
              // Build sidebar HTML
              let linksListHTML = '';
              errors.forEach((error, index) => {
                const element = document.getElementById(error[0]);
                const linkText = element ? (element.textContent.trim() || element.innerHTML.trim() || 'No text') : 'Unknown';
                const truncatedText = linkText.length > 40 ? linkText.substring(0, 40) + '...' : linkText;
                const truncatedUrl = error[1].length > 50 ? error[1].substring(0, 50) + '...' : error[1];
                
                linksListHTML += `
                  <div class="link-item" onclick="document.location.hash='#${error[0]}'; document.getElementById('${error[0]}').scrollIntoView({behavior: 'smooth', block: 'center'});">
                    <div class="link-text">${truncatedText}</div>
                    <div class="link-url">${truncatedUrl}</div>
                  </div>
                `;
              });
              
              let sidebar = document.createElement("div");
              sidebar.id = "linkCheckerSidebar";
              sidebar.innerHTML = `
                <div class="header" id="linkCheckerHeader">
                  <span>❌ ${errors.length} Broken Link${errors.length === 1 ? '' : 's'}</span>
                  <button onclick="LinkCheckerAction.reset()">✕</button>
                </div>
                <div class="links-list">
                  ${linksListHTML}
                </div>
                <div style="padding:8px 12px;border-top:1px solid #374151;font-size:11px;color:#9ca3af;text-align:center;">
                  Click any link to jump to it • Drag header to move • Press ESC to close
                </div>
              `;
              document.body.appendChild(sidebar);
              
              // Make sidebar draggable
              let isDragging = false;
              let currentX;
              let currentY;
              let initialX;
              let initialY;
              let xOffset = 0;
              let yOffset = 0;
              
              const header = document.getElementById('linkCheckerHeader');
              
              function dragStart(e) {
                if (e.target.tagName === 'BUTTON') return; // Don't drag on button clicks
                
                if (e.type === "touchstart") {
                  initialX = e.touches[0].clientX - xOffset;
                  initialY = e.touches[0].clientY - yOffset;
                } else {
                  initialX = e.clientX - xOffset;
                  initialY = e.clientY - yOffset;
                }

                if (e.target === header || header.contains(e.target)) {
                  isDragging = true;
                  sidebar.style.cursor = 'grabbing';
                }
              }

              function dragEnd(e) {
                initialX = currentX;
                initialY = currentY;
                isDragging = false;
                sidebar.style.cursor = 'default';
              }

              function drag(e) {
                if (isDragging) {
                  e.preventDefault();
                  
                  if (e.type === "touchmove") {
                    currentX = e.touches[0].clientX - initialX;
                    currentY = e.touches[0].clientY - initialY;
                  } else {
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;
                  }

                  xOffset = currentX;
                  yOffset = currentY;

                  // Constrain to viewport
                  const rect = sidebar.getBoundingClientRect();
                  const maxX = window.innerWidth - rect.width;
                  const maxY = window.innerHeight - rect.height;
                  
                  xOffset = Math.max(0, Math.min(maxX, xOffset));
                  yOffset = Math.max(0, Math.min(maxY, yOffset));

                  sidebar.style.transform = `translate(${xOffset}px, ${yOffset}px)`;
                }
              }

              // Add event listeners for dragging
              header.addEventListener("mousedown", dragStart);
              document.addEventListener("mousemove", drag);
              document.addEventListener("mouseup", dragEnd);
              
              // Touch events for mobile
              header.addEventListener("touchstart", dragStart);
              document.addEventListener("touchmove", drag);
              document.addEventListener("touchend", dragEnd);
              
              // Store drag event listeners for cleanup - following golden rules
              window.linkCheckerDragListeners = {
                dragStart,
                drag,
                dragEnd,
                header
              };
              
              // Simple notification
              const notification = document.createElement('div');
              notification.className = 'linkchecker-notification';
              notification.style.cssText = `
                position:fixed;
                top:20px;
                right:20px;
                background:#0a0a0a;
                color:#f87171;
                border:1px solid #f87171;
                border-radius:8px;
                padding:12px 16px;
                z-index:9999999;
                font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;
                font-size:13px;
                box-shadow:0 4px 16px rgba(0,0,0,0.4);
                max-width:250px;
              `;
              
              notification.innerHTML = `⚠️ Found ${errors.length} broken link${errors.length === 1 ? '' : 's'}`;
              document.body.appendChild(notification);
              
              setTimeout(() => {
                if (notification.parentNode) {
                  notification.remove();
                }
              }, 4000);
            } else {
              // Success notification  
              const notification = document.createElement('div');
              notification.className = 'linkchecker-notification';
              notification.style.cssText = `
                position:fixed;
                top:20px;
                right:20px;
                background:#0a0a0a;
                color:#10b981;
                border:1px solid #10b981;
                border-radius:8px;
                padding:12px 16px;
                z-index:9999999;
                font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;
                font-size:13px;
                box-shadow:0 4px 16px rgba(0,0,0,0.4);
                max-width:250px;
              `;
              
              notification.innerHTML = `✅ All ${links.length} links OK`;
              document.body.appendChild(notification);
              
              setTimeout(() => {
                if (notification.parentNode) {
                  notification.remove();
                }
              }, 3000);
            }
          }
        }

        var linkChecker = {
          currentError: -1,
          errors: errors, // Reference the actual errors array
          nextError: function() {
            const filteredErrors = errors.filter(e => typeof e === "object" && e[0]);
            console.log('nextError called, filteredErrors:', filteredErrors, 'currentError:', linkChecker.currentError);
            if (filteredErrors.length === 0) return;
            
            let err = linkChecker.currentError;
            if (err >= filteredErrors.length - 1) {
              err = 0;
            } else {
              err += 1;
            }
            let target = "#" + filteredErrors[err][0];
            console.log('Navigating to:', target);
            document.location.hash = target;
            linkChecker.currentError = err;
          },
          prevError: function() {
            const filteredErrors = errors.filter(e => typeof e === "object" && e[0]);
            console.log('prevError called, filteredErrors:', filteredErrors, 'currentError:', linkChecker.currentError);
            if (filteredErrors.length === 0) return;
            
            let err = linkChecker.currentError;
            if (err <= 0) {
              err = filteredErrors.length - 1;
            } else {
              err -= 1;
            }
            let target = "#" + filteredErrors[err][0];
            console.log('Navigating to:', target);
            document.location.hash = target;
            linkChecker.currentError = err;
          },
          check: function() {
            links = document.querySelectorAll("a[href]");

            if (links.length === 0) {
              const notification = document.createElement('div');
              notification.className = 'linkchecker-notification';
              notification.style.cssText = `
                position:fixed;
                top:20px;
                right:20px;
                background:#0a0a0a;
                color:#f59e0b;
                border:1px solid #f59e0b;
                border-radius:8px;
                padding:12px 16px;
                z-index:9999999;
                font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;
                font-size:13px;
                box-shadow:0 4px 16px rgba(0,0,0,0.4);
                max-width:250px;
              `;
              
              notification.innerHTML = `ℹ️ No links found to check`;
              document.body.appendChild(notification);
              
              setTimeout(() => {
                if (notification.parentNode) {
                  notification.remove();
                }
              }, 3000);
              
              return;
            }

            // Show progress
            const progressNotification = document.createElement('div');
            progressNotification.className = 'linkchecker-notification';
            progressNotification.id = 'linkCheckerProgress';
            progressNotification.style.cssText = `
              position:fixed;
              top:20px;
              right:20px;
              background:#0a0a0a;
              color:#e5e7eb;
              border:1px solid #6366f1;
              border-radius:8px;
              padding:12px 16px;
              z-index:9999999;
              font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;
              font-size:13px;
              box-shadow: 0 4px 16px rgba(0,0,0,0.4);
              max-width:250px;
            `;
            
            progressNotification.innerHTML = `<span style="color: #7C3AED; font-size: 18px;">●</span> Checking ${links.length} links...`;
            document.body.appendChild(progressNotification);

            // EXACT same logic as working version with XHR tracking
            links.forEach((i, idx) => {
              var xhr = new XMLHttpRequest();
              
              // Track XHR for cleanup - following golden rules
              xhrRequests.push(xhr);
              
              xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                  if (xhr.status >= 400) {
                    i.classList += " linkError";
                    i.id = "error-" + errorCounter;
                    errors[idx] = [i.id, i.href];
                    errorCounter += 1;
                  }
                  finished(idx);
                }
              };
              xhr.open("HEAD", i.href);
              xhr.send();
            });
            
            // Store XHR requests globally for cleanup - following golden rules
            window.linkCheckerXHRs = xhrRequests;
            
            // Remove progress after delay
            setTimeout(() => {
              const progressEl = document.getElementById('linkCheckerProgress');
              if (progressEl) {
                progressEl.remove();
              }
            }, 3000);
          }
        };
        return linkChecker;
      })();

      // Make linkChecker globally available for navigation (CRITICAL for button functionality)
      window.linkChecker = linkChecker;
      
      // Debug: Verify global exposure
      console.log('linkChecker exposed globally:', window.linkChecker);
      
      // Add escape key listener following golden rules
      function linkCheckerEscapeListener(e) {
        if (e.key === 'Escape') {
          console.log('Link Checker: Escape key pressed, resetting...');
          LinkCheckerAction.reset();
        }
      }
      
      // Store escape listener globally for cleanup - following golden rules
      window.linkCheckerEscapeListener = linkCheckerEscapeListener;
      document.addEventListener('keyup', linkCheckerEscapeListener);
      
      // Start checking links
      linkChecker.check();
      
    } catch (error) {
      console.error('Error executing Link Checker:', error);
    }
  }
  
  static reset() {
    try {
      console.log('Resetting Link Checker...');
      
      // Remove escape key listener following golden rules
      if (window.linkCheckerEscapeListener) {
        document.removeEventListener('keyup', window.linkCheckerEscapeListener);
        delete window.linkCheckerEscapeListener;
      }
      
      // Clean up drag event listeners following golden rules
      if (window.linkCheckerDragListeners) {
        const { dragStart, drag, dragEnd, header } = window.linkCheckerDragListeners;
        if (header && header.removeEventListener) {
          header.removeEventListener("mousedown", dragStart);
          header.removeEventListener("touchstart", dragStart);
        }
        document.removeEventListener("mousemove", drag);
        document.removeEventListener("mouseup", dragEnd);
        document.removeEventListener("touchmove", drag);
        document.removeEventListener("touchend", dragEnd);
        delete window.linkCheckerDragListeners;
      }
      
      // Remove all link checker elements following golden rules
      document.querySelectorAll('.linkchecker-notification, #linkCheckerErrorDialog, #linkCheckerSidebar, #linkCheckerProgress').forEach(el => el.remove());
      document.querySelectorAll('#linkCheckerStyles').forEach(el => el.remove());
      
      // Reset all link styling following golden rules
      document.querySelectorAll('.linkError').forEach(el => {
        el.classList.remove('linkError');
        if (el.id && el.id.startsWith('error-')) {
          el.removeAttribute('id');
        }
      });
      
      // Clear URL hash if it was set by link checker
      if (window.location.hash && window.location.hash.startsWith('#error-')) {
        window.location.hash = '';
      }
      
      // Clear all global variables following golden rules
      if (window.linkChecker) {
        delete window.linkChecker;
      }
      
      // Clear any pending XHR requests following golden rules
      if (window.linkCheckerXHRs) {
        window.linkCheckerXHRs.forEach(xhr => {
          try {
            xhr.abort();
          } catch (e) {
            // Ignore errors when aborting
          }
        });
        delete window.linkCheckerXHRs;
      }
      
      console.log('Link Checker reset completed');
      // Return synchronously for context menu compatibility - following golden rules
      return true;
    } catch (error) {
      console.error('Error resetting Link Checker:', error);
      return false;
    }
  }
}

// Make LinkCheckerAction globally available for context menu execution - following golden rules
window.LinkCheckerAction = LinkCheckerAction; 