// Images Action - Image SEO Audit tool for web pages
class ImagesAction {
    static execute() {
        try {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                    target: {tabId: tabs[0].id},
                    func: function() {
                        // Image SEO audit functionality
                        (function() {
                            // Function to scroll to bottom and trigger lazy loading
                            function triggerLazyLoadingAndStartAudit() {
                                console.log('Triggering lazy loading by scrolling to bottom...');
                                
                                // Scroll to bottom of page
                                window.scrollTo(0, document.body.scrollHeight);
                                
                                // Wait 2 seconds for images to load, then start audit
                                setTimeout(function() {
                                    console.log('Starting image audit after lazy loading trigger...');
                                    startImageAudit();
                                }, 2000);
                            }
                            
                            // Main audit function containing all the existing logic
                            function startImageAudit() {
                            
                            function getFileName(src, element) {
                                try {
                                    // Handle favicon case
                                    if (element && element.isFavicon) {
                                        if (element.faviconType === 'default') {
                                            return 'favicon.ico';
                                        }
                                        var sizes = element.getAttribute('sizes');
                                        var faviconTypeName = element.faviconType.replace('apple-touch-', '').replace('-precomposed', '');
                                        var fileName = faviconTypeName + (sizes ? '-' + sizes : '') + '.ico';
                                        
                                        // Try to extract actual filename from URL
                                        var urlFileName = src.split('/').pop().split('?')[0];
                                        if (urlFileName && urlFileName.includes('.')) {
                                            return urlFileName;
                                        }
                                        return fileName;
                                    }
                                    
                                    // Handle video case
                                    if (element && element.isVideo) {
                                        if (element.videoType === 'embedded') {
                                            var platform = element.videoPlatform || 'embedded';
                                            return platform + ' video';
                                        } else if (element.videoType === 'audio') {
                                            // Try to extract actual filename from URL
                                            var urlFileName = src.split('/').pop().split('?')[0];
                                            if (urlFileName && urlFileName.includes('.')) {
                                                return urlFileName;
                                            }
                                            return 'audio file';
                                        } else {
                                            // HTML5 video
                                            var urlFileName = src.split('/').pop().split('?')[0];
                                            if (urlFileName && urlFileName.includes('.')) {
                                                return urlFileName;
                                            }
                                            return 'video file';
                                        }
                                    }
                                    
                                    // If the URL has a 'url=' parameter, extract and decode it
                                    var urlMatch = src.match(/[?&]url=([^&#]+)/);
                                    var filePath = src;
                                    if (urlMatch && urlMatch[1]) {
                                        filePath = decodeURIComponent(urlMatch[1]);
                                    } else {
                                        // Decode the main src
                                        filePath = decodeURIComponent(src);
                                    }
                                    // Remove any query/hash
                                    filePath = filePath.split('?')[0].split('#')[0];
                                    // Get the file name after the last '/'
                                    var fileName = filePath.split('/').pop();
                                    // If fileName is empty, fallback to 'Unknown'
                                    if (!fileName) return 'Unknown';
                                    return fileName;
                                } catch (e) {
                                    return src;
                                }
                            }
                            
                            function isBroken(img) {
                                return img.complete && img.naturalWidth === 0;
                            }
                            
                            function getFileFormat(src, element) {
                                try {
                                    // Handle favicon case
                                    if (element && element.isFavicon) {
                                        var type = element.getAttribute('type');
                                        if (type) {
                                            if (type.includes('png')) return 'png';
                                            if (type.includes('svg')) return 'svg';
                                            if (type.includes('gif')) return 'gif';
                                            if (type.includes('jpeg') || type.includes('jpg')) return 'jpg';
                                            if (type.includes('webp')) return 'webp';
                                            if (type.includes('vnd.microsoft.icon') || type.includes('x-icon')) return 'ico';
                                        }
                                        
                                        // Fallback to URL extension analysis
                                        var urlExt = src.split('.').pop().toLowerCase().split('?')[0];
                                        var knownFaviconExts = ['ico', 'png', 'svg', 'jpg', 'jpeg', 'gif', 'webp'];
                                        if (knownFaviconExts.includes(urlExt)) return urlExt;
                                        
                                        // Default assumption for favicons
                                        return 'ico';
                                    }
                                    
                                    // Handle video case
                                    if (element && element.isVideo) {
                                        if (element.videoType === 'embedded') {
                                            return 'embedded';
                                        } else {
                                            var type = element.getAttribute('type');
                                            if (type) {
                                                if (type.includes('mp4')) return 'mp4';
                                                if (type.includes('webm')) return 'webm';
                                                if (type.includes('ogg')) return 'ogg';
                                                if (type.includes('avi')) return 'avi';
                                                if (type.includes('mov')) return 'mov';
                                                if (type.includes('mpeg')) return 'mpeg';
                                                if (type.includes('wav')) return 'wav';
                                                if (type.includes('mp3')) return 'mp3';
                                                if (type.includes('aac')) return 'aac';
                                                if (type.includes('flac')) return 'flac';
                                            }
                                            
                                            // Fallback to URL extension analysis
                                            var urlExt = src.split('.').pop().toLowerCase().split('?')[0];
                                            var knownVideoExts = ['mp4', 'webm', 'ogg', 'avi', 'mov', 'mpeg', 'mpg', 'wmv', 'flv', 'm4v'];
                                            var knownAudioExts = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma'];
                                            
                                            if (knownVideoExts.includes(urlExt)) return urlExt;
                                            if (knownAudioExts.includes(urlExt)) return urlExt;
                                            
                                            // Default based on element type
                                            return element.videoType === 'audio' ? 'audio' : 'video';
                                        }
                                    }
                                    
                                    // If the URL has a 'url=' parameter, extract and decode it
                                    var urlMatch = src.match(/[?&]url=([^&#]+)/);
                                    var filePath = src;
                                    if (urlMatch && urlMatch[1]) {
                                        filePath = decodeURIComponent(urlMatch[1]);
                                    } else {
                                        filePath = decodeURIComponent(src);
                                    }
                                    // Remove any query/hash
                                    filePath = filePath.split('?')[0].split('#')[0];
                                    // Get the extension
                                    var extMatch = filePath.match(/\.([a-zA-Z0-9]+)$/);
                                    if (extMatch && extMatch[1]) {
                                        var ext = extMatch[1].toLowerCase();
                                        var known = ['jpg','jpeg','png','gif','webp','avif','svg','bmp','tiff','ico'];
                                        if (known.includes(ext)) return ext;
                                    }
                                    return 'UNKNOWN';
                                } catch (e) {
                                    return 'UNKNOWN';
                                }
                            }
                            
                            function hasExplicitDimensions(img) {
                                return img.hasAttribute('width') && img.hasAttribute('height');
                            }
                            
                            function isSVG(element) {
                                return element.tagName.toLowerCase() === 'svg';
                            }
                            
                            function isFavicon(element) {
                                return element.isFavicon === true;
                            }
                            
                            function isVideo(element) {
                                return element.isVideo === true;
                            }
                            
                            function createFaviconPreview(favicon) {
                                try {
                                    var sizes = favicon.getAttribute('sizes') || '16x16';
                                    var faviconType = favicon.faviconType || 'icon';
                                    
                                    // Create favicon preview with proper fallback
                                    var imgElement = '<img src="' + favicon.src + '" style="width:auto;height:auto;max-width:32px;max-height:32px;min-width:16px;min-height:16px;border:1px solid #2a2a2a;border-radius:4px;object-fit:contain;background:#1a1a1a;" loading="lazy" title="' + faviconType + ' (' + sizes + ')" alt="Favicon Preview" onerror="this.style.background=\'#ef4444\';this.alt=\'Broken Favicon\';">';
                                    
                                    return imgElement;
                                } catch (error) {
                                    console.warn('Failed to create favicon preview:', error);
                                    
                                    // Fallback to simple placeholder
                                    var container = '<div style="width:32px;height:32px;border:1px solid #2a2a2a;border-radius:4px;background:#ef4444;display:flex;align-items:center;justify-content:center;color:white;font-size:10px;">ICO</div>';
                                    return container;
                                }
                            }
                            
                            function createVideoPreview(video) {
                                try {
                                    var videoType = video.videoType || 'html5';
                                    var platform = video.videoPlatform || '';
                                    
                                    if (videoType === 'embedded') {
                                        // Create platform-specific preview
                                        var platformLabel = platform.toUpperCase();
                                        var container = '<div style="width:80px;height:60px;border:1px solid #2a2a2a;border-radius:4px;background:#1a1a1a;display:flex;align-items:center;justify-content:center;color:#7C3AED;font-size:10px;font-weight:bold;">' + platformLabel + '</div>';
                                        return container;
                                    } else if (videoType === 'audio') {
                                        // Create audio preview
                                        var container = '<div style="width:80px;height:40px;border:1px solid #2a2a2a;border-radius:4px;background:#1a1a1a;display:flex;align-items:center;justify-content:center;color:#22c55e;font-size:10px;font-weight:bold;">AUDIO</div>';
                                        return container;
                                    } else {
                                        // HTML5 video - try to show poster or video thumbnail
                                        var poster = video.getAttribute('poster');
                                        if (poster) {
                                            var imgElement = '<img src="' + poster + '" style="width:auto;height:auto;max-width:80px;max-height:60px;min-width:60px;min-height:45px;border:1px solid #2a2a2a;border-radius:4px;object-fit:cover;background:#1a1a1a;" loading="lazy" title="Video Poster" alt="Video Preview" onerror="this.style.background=\'#ef4444\';this.alt=\'Broken Poster\';">';
                                            return imgElement;
                                        } else {
                                            // Fallback video icon
                                            var container = '<div style="width:80px;height:60px;border:1px solid #2a2a2a;border-radius:4px;background:#1a1a1a;display:flex;align-items:center;justify-content:center;color:#3b82f6;font-size:10px;font-weight:bold;">VIDEO</div>';
                                            return container;
                                        }
                                    }
                                } catch (error) {
                                    console.warn('Failed to create video preview:', error);
                                    
                                    // Fallback to simple placeholder
                                    var container = '<div style="width:80px;height:60px;border:1px solid #2a2a2a;border-radius:4px;background:#ef4444;display:flex;align-items:center;justify-content:center;color:white;font-size:10px;">VIDEO</div>';
                                    return container;
                                }
                            }
                            
                            function getSVGAccessibilityText(svg) {
                                // Check for title element
                                var title = svg.querySelector('title');
                                if (title && title.textContent.trim()) {
                                    return title.textContent.trim();
                                }
                                
                                // Check for desc element
                                var desc = svg.querySelector('desc');
                                if (desc && desc.textContent.trim()) {
                                    return desc.textContent.trim();
                                }
                                
                                // Check for aria-label
                                var ariaLabel = svg.getAttribute('aria-label');
                                if (ariaLabel && ariaLabel.trim()) {
                                    return ariaLabel.trim();
                                }
                                
                                // Check for aria-labelledby
                                var ariaLabelledBy = svg.getAttribute('aria-labelledby');
                                if (ariaLabelledBy) {
                                    var labelElement = document.getElementById(ariaLabelledBy);
                                    if (labelElement && labelElement.textContent.trim()) {
                                        return labelElement.textContent.trim();
                                    }
                                }
                                
                                return null;
                            }
                            
                            function getSVGDimensions(svg) {
                                // Try to get dimensions from attributes
                                var width = svg.getAttribute('width');
                                var height = svg.getAttribute('height');
                                
                                if (width && height) {
                                    return {
                                        width: parseInt(width) || width,
                                        height: parseInt(height) || height,
                                        naturalWidth: parseInt(width) || width,
                                        naturalHeight: parseInt(height) || height
                                    };
                                }
                                
                                // Try to get from viewBox
                                var viewBox = svg.getAttribute('viewBox');
                                if (viewBox) {
                                    var values = viewBox.split(/\s+|,/);
                                    if (values.length >= 4) {
                                        return {
                                            width: parseFloat(values[2]) || 'auto',
                                            height: parseFloat(values[3]) || 'auto',
                                            naturalWidth: parseFloat(values[2]) || 'auto',
                                            naturalHeight: parseFloat(values[3]) || 'auto'
                                        };
                                    }
                                }
                                
                                // Get computed dimensions
                                var rect = svg.getBoundingClientRect();
                                return {
                                    width: Math.round(rect.width) || 'auto',
                                    height: Math.round(rect.height) || 'auto',
                                    naturalWidth: Math.round(rect.width) || 'auto',
                                    naturalHeight: Math.round(rect.height) || 'auto'
                                };
                            }
                            
                            function createSVGPreview(svg) {
                                try {
                                    // Clone the SVG for processing
                                    var svgClone = svg.cloneNode(true);
                                    
                                    // Ensure SVG has proper dimensions for data URL
                                    if (!svgClone.getAttribute('width') && !svgClone.getAttribute('height') && !svgClone.getAttribute('viewBox')) {
                                        svgClone.setAttribute('width', '100');
                                        svgClone.setAttribute('height', '100');
                                    }
                                    
                                    // Add XML namespace if missing (required for data URLs)
                                    if (!svgClone.getAttribute('xmlns')) {
                                        svgClone.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                                    }
                                    
                                    // Serialize the SVG to string
                                    var serializer = new XMLSerializer();
                                    var svgString = serializer.serializeToString(svgClone);
                                    
                                    // Create data URL
                                    var svgDataUrl = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svgString);
                                    
                                    // Create an img element that can be right-clicked and saved
                                    var imgElement = '<img src="' + svgDataUrl + '" style="width:100%;height:auto;max-width:300px;min-width:60px;border:1px solid #2a2a2a;border-radius:4px;object-fit:contain;background:#1a1a1a;" loading="lazy" title="Right-click to save SVG" alt="SVG Preview">';
                                    
                                    return imgElement;
                                } catch (error) {
                                    console.warn('Failed to create downloadable SVG preview:', error);
                                    
                                    // Fallback to original method if data URL creation fails
                                    var container = document.createElement('div');
                                    container.style.cssText = 'width:100%;height:auto;max-width:300px;min-width:60px;border:1px solid #2a2a2a;border-radius:4px;background:#1a1a1a;display:flex;align-items:center;justify-content:center;padding:8px;';
                                    
                                    var svgClone = svg.cloneNode(true);
                                    svgClone.style.cssText = 'max-width:100%;max-height:60px;width:auto;height:auto;';
                                    
                                    if (!svgClone.getAttribute('width') && !svgClone.getAttribute('height') && !svgClone.getAttribute('viewBox')) {
                                        svgClone.setAttribute('width', '50');
                                        svgClone.setAttribute('height', '50');
                                    }
                                    
                                    container.appendChild(svgClone);
                                    return container.outerHTML;
                                }
                            }
                            
                            function jumpToImage(imgId) {
                                var img = document.getElementById(imgId);
                                if (img) {
                                    // Clear any existing highlights first
                                    document.querySelectorAll('.seo-highlight').forEach(function(el) {
                                        el.classList.remove('seo-highlight');
                                        // Restore original styles if they were stored
                                        if (el.hasAttribute('data-original-border')) {
                                            el.style.border = el.getAttribute('data-original-border');
                                            el.removeAttribute('data-original-border');
                                        }
                                        if (el.hasAttribute('data-original-box-shadow')) {
                                            el.style.boxShadow = el.getAttribute('data-original-box-shadow');
                                            el.removeAttribute('data-original-box-shadow');
                                        }
                                    });
                                    
                                    img.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    img.classList.add('seo-highlight');
                                    
                                    // Store original styles before modifying
                                    var originalBorder = img.style.border || '';
                                    var originalBoxShadow = img.style.boxShadow || '';
                                    img.setAttribute('data-original-border', originalBorder);
                                    img.setAttribute('data-original-box-shadow', originalBoxShadow);
                                    
                                    img.style.border = '4px solid #8b5cf6';
                                    img.style.boxShadow = '0 0 20px rgba(139, 92, 246, 0.6)';
                                    
                                    setTimeout(function() {
                                        if (img && img.classList.contains('seo-highlight')) {
                                            img.style.border = originalBorder;
                                            img.style.boxShadow = originalBoxShadow;
                                            img.classList.remove('seo-highlight');
                                            img.removeAttribute('data-original-border');
                                            img.removeAttribute('data-original-box-shadow');
                                        }
                                    }, 3000);
                                }
                            }
                            
                            function copyToClipboard(text) {
                                if (navigator.clipboard && window.isSecureContext) {
                                    navigator.clipboard.writeText(text).then(function() {
                                        // Show temporary feedback
                                        var feedback = document.createElement('div');
                                        feedback.style.cssText = 'position:fixed;top:20px;left:50%;transform:translateX(-50%);background:#22c55e;color:white;padding:8px 16px;border-radius:6px;z-index:10000000;font-family:Arial,sans-serif;font-size:14px;font-weight:500;';
                                        feedback.textContent = 'URL copied to clipboard!';
                                        document.body.appendChild(feedback);
                                        setTimeout(function() {
                                            feedback.remove();
                                        }, 2000);
                                    }).catch(function(err) {
                                        console.error('Failed to copy: ', err);
                                    });
                                } else {
                                    // Fallback for older browsers
                                    var textArea = document.createElement('textarea');
                                    textArea.value = text;
                                    textArea.style.position = 'fixed';
                                    textArea.style.left = '-999999px';
                                    textArea.style.top = '-999999px';
                                    document.body.appendChild(textArea);
                                    textArea.focus();
                                    textArea.select();
                                    try {
                                        document.execCommand('copy');
                                        // Show temporary feedback
                                        var feedback = document.createElement('div');
                                        feedback.style.cssText = 'position:fixed;top:20px;left:50%;transform:translateX(-50%);background:#22c55e;color:white;padding:8px 16px;border-radius:6px;z-index:10000000;font-family:Arial,sans-serif;font-size:14px;font-weight:500;';
                                        feedback.textContent = 'URL copied to clipboard!';
                                        document.body.appendChild(feedback);
                                        setTimeout(function() {
                                            feedback.remove();
                                        }, 2000);
                                    } catch (err) {
                                        console.error('Fallback: Failed to copy', err);
                                    }
                                    textArea.remove();
                                }
                            }
                            
                            function truncateUrl(url, maxLength) {
                                if (url.length <= maxLength) return url;
                                return url.substring(0, maxLength) + '...';
                            }
                            
                            var style = document.createElement('style');
                            style.textContent = '.seo-highlight { animation: pulse 1s infinite; } @keyframes pulse { 0% { opacity: 1; } 50% { opacity: 0.7; } 100% { opacity: 1; } }';
                            document.head.appendChild(style);
                            
                            // Get saved position and size from localStorage
                            function getSavedPanelSettings() {
                                try {
                                    const saved = localStorage.getItem('images-audit-panel-settings');
                                    if (saved) {
                                        return JSON.parse(saved);
                                    }
                                } catch (e) {
                                    console.log('Error loading saved panel settings:', e);
                                }
                                // Default settings
                                return {
                                    top: '0px',
                                    right: '0px', 
                                    width: '40%',
                                    height: '50vh'
                                };
                            }
                            
                            // Save panel position and size to localStorage
                            function savePanelSettings(panel) {
                                try {
                                    const rect = panel.getBoundingClientRect();
                                    const settings = {
                                        top: panel.style.top || '0px',
                                        right: panel.style.right || '0px',
                                        left: panel.style.left || '',
                                        width: panel.style.width || '40%',
                                        height: panel.style.height || '50vh'
                                    };
                                    localStorage.setItem('images-audit-panel-settings', JSON.stringify(settings));
                                } catch (e) {
                                    console.log('Error saving panel settings:', e);
                                }
                            }
                            
                            var images = document.querySelectorAll('img');
                            var svgs = document.querySelectorAll('svg');
                            
                            // Find favicon links and create synthetic objects
                            var favicons = [];
                            var faviconSelectors = [
                                'link[rel="icon"]',
                                'link[rel="shortcut icon"]', 
                                'link[rel="apple-touch-icon"]',
                                'link[rel="apple-touch-icon-precomposed"]',
                                'link[rel="mask-icon"]'
                            ];
                            
                            faviconSelectors.forEach(function(selector) {
                                var faviconLinks = document.querySelectorAll(selector);
                                faviconLinks.forEach(function(link) {
                                    var href = link.getAttribute('href');
                                    if (href) {
                                        // Convert relative URLs to absolute
                                        var faviconUrl = href;
                                        if (href.startsWith('/')) {
                                            faviconUrl = window.location.origin + href;
                                        } else if (!href.startsWith('http')) {
                                            faviconUrl = window.location.origin + '/' + href;
                                        }
                                        
                                        // Create synthetic favicon object
                                        var syntheticFavicon = {
                                            src: faviconUrl,
                                            currentSrc: faviconUrl,
                                            getAttribute: function(attr) {
                                                if (attr === 'alt') return link.getAttribute('alt') || '';
                                                if (attr === 'sizes') return link.getAttribute('sizes') || '';
                                                if (attr === 'type') return link.getAttribute('type') || '';
                                                return link.getAttribute(attr);
                                            },
                                            hasAttribute: function(attr) { 
                                                return link.hasAttribute(attr); 
                                            },
                                            complete: true,
                                            naturalWidth: 0,
                                            naturalHeight: 0,
                                            width: 0,
                                            height: 0,
                                            loading: null,
                                            parentElement: link.parentElement,
                                            tagName: 'LINK',
                                            isFavicon: true,
                                            faviconType: link.getAttribute('rel') || 'icon',
                                            faviconLinkRef: link,
                                            scrollIntoView: function(options) {
                                                link.scrollIntoView(options);
                                            },
                                            classList: {
                                                add: function() { 
                                                    console.warn('Favicon element modification prevented');
                                                },
                                                remove: function() { 
                                                    console.warn('Favicon element modification prevented');
                                                }
                                            },
                                            style: {},
                                            setAttribute: function() { 
                                                console.warn('Favicon element modification prevented');
                                            }
                                        };
                                        
                                        favicons.push(syntheticFavicon);
                                    }
                                });
                            });
                            
                            // Also check for default favicon.ico
                            var defaultFaviconUrl = window.location.origin + '/favicon.ico';
                            // Only add if not already detected via link tags
                            var hasDefaultFavicon = favicons.some(function(fav) {
                                return fav.src === defaultFaviconUrl;
                            });
                            
                            if (!hasDefaultFavicon) {
                                var syntheticDefaultFavicon = {
                                    src: defaultFaviconUrl,
                                    currentSrc: defaultFaviconUrl,
                                    getAttribute: function(attr) {
                                        if (attr === 'alt') return 'Default Favicon';
                                        return null;
                                    },
                                    hasAttribute: function() { return false; },
                                    complete: true,
                                    naturalWidth: 0,
                                    naturalHeight: 0,
                                    width: 0,
                                    height: 0,
                                    loading: null,
                                    parentElement: document.head,
                                    tagName: 'LINK',
                                    isFavicon: true,
                                    faviconType: 'default',
                                    faviconLinkRef: null,
                                    scrollIntoView: function() {
                                        window.scrollTo(0, 0);
                                    },
                                    classList: {
                                        add: function() { 
                                            console.warn('Favicon element modification prevented');
                                        },
                                        remove: function() { 
                                            console.warn('Favicon element modification prevented');
                                        }
                                    },
                                    style: {},
                                    setAttribute: function() { 
                                        console.warn('Favicon element modification prevented');
                                    }
                                };
                                
                                favicons.push(syntheticDefaultFavicon);
                            }
                            
                            // Find video and audio elements and create synthetic objects
                            var videos = [];
                            
                            // HTML5 Video elements
                            var videoElements = document.querySelectorAll('video');
                            videoElements.forEach(function(video) {
                                var videoSrc = video.currentSrc || video.src;
                                if (!videoSrc && video.children.length > 0) {
                                    // Check for source elements
                                    var sourceEl = video.querySelector('source');
                                    if (sourceEl) {
                                        videoSrc = sourceEl.src;
                                    }
                                }
                                
                                if (videoSrc) {
                                    var syntheticVideo = {
                                        src: videoSrc,
                                        currentSrc: videoSrc,
                                        getAttribute: function(attr) {
                                            return video.getAttribute(attr);
                                        },
                                        hasAttribute: function(attr) { 
                                            return video.hasAttribute(attr); 
                                        },
                                        complete: true,
                                        naturalWidth: video.videoWidth || 0,
                                        naturalHeight: video.videoHeight || 0,
                                        width: video.offsetWidth || 0,
                                        height: video.offsetHeight || 0,
                                        duration: video.duration || 0,
                                        loading: null,
                                        parentElement: video.parentElement,
                                        tagName: 'VIDEO',
                                        isVideo: true,
                                        videoType: 'html5',
                                        videoElementRef: video,
                                        scrollIntoView: function(options) {
                                            video.scrollIntoView(options);
                                        },
                                        classList: {
                                            add: function() { 
                                                console.warn('Video element modification prevented');
                                            },
                                            remove: function() { 
                                                console.warn('Video element modification prevented');
                                            }
                                        },
                                        style: {},
                                        setAttribute: function() { 
                                            console.warn('Video element modification prevented');
                                        }
                                    };
                                    
                                    videos.push(syntheticVideo);
                                }
                            });
                            
                            // HTML5 Audio elements
                            var audioElements = document.querySelectorAll('audio');
                            audioElements.forEach(function(audio) {
                                var audioSrc = audio.currentSrc || audio.src;
                                if (!audioSrc && audio.children.length > 0) {
                                    var sourceEl = audio.querySelector('source');
                                    if (sourceEl) {
                                        audioSrc = sourceEl.src;
                                    }
                                }
                                
                                if (audioSrc) {
                                    var syntheticAudio = {
                                        src: audioSrc,
                                        currentSrc: audioSrc,
                                        getAttribute: function(attr) {
                                            return audio.getAttribute(attr);
                                        },
                                        hasAttribute: function(attr) { 
                                            return audio.hasAttribute(attr); 
                                        },
                                        complete: true,
                                        naturalWidth: 0,
                                        naturalHeight: 0,
                                        width: audio.offsetWidth || 200,
                                        height: audio.offsetHeight || 50,
                                        duration: audio.duration || 0,
                                        loading: null,
                                        parentElement: audio.parentElement,
                                        tagName: 'AUDIO',
                                        isVideo: true,
                                        videoType: 'audio',
                                        videoElementRef: audio,
                                        scrollIntoView: function(options) {
                                            audio.scrollIntoView(options);
                                        },
                                        classList: {
                                            add: function() { 
                                                console.warn('Audio element modification prevented');
                                            },
                                            remove: function() { 
                                                console.warn('Audio element modification prevented');
                                            }
                                        },
                                        style: {},
                                        setAttribute: function() { 
                                            console.warn('Audio element modification prevented');
                                        }
                                    };
                                    
                                    videos.push(syntheticAudio);
                                }
                            });
                            
                            // Embedded Video iframes (YouTube, Vimeo, etc.)
                            var videoIframes = document.querySelectorAll('iframe');
                            videoIframes.forEach(function(iframe) {
                                var src = iframe.src;
                                if (src && (src.includes('youtube') || src.includes('vimeo') || src.includes('dailymotion') || 
                                           src.includes('twitch') || src.includes('tiktok') || src.includes('facebook.com/plugins/video'))) {
                                    var platform = 'unknown';
                                    if (src.includes('youtube')) platform = 'youtube';
                                    else if (src.includes('vimeo')) platform = 'vimeo';
                                    else if (src.includes('dailymotion')) platform = 'dailymotion';
                                    else if (src.includes('twitch')) platform = 'twitch';
                                    else if (src.includes('tiktok')) platform = 'tiktok';
                                    else if (src.includes('facebook')) platform = 'facebook';
                                    
                                    var syntheticEmbedded = {
                                        src: src,
                                        currentSrc: src,
                                        getAttribute: function(attr) {
                                            return iframe.getAttribute(attr);
                                        },
                                        hasAttribute: function(attr) { 
                                            return iframe.hasAttribute(attr); 
                                        },
                                        complete: true,
                                        naturalWidth: 0,
                                        naturalHeight: 0,
                                        width: iframe.offsetWidth || 0,
                                        height: iframe.offsetHeight || 0,
                                        duration: 0,
                                        loading: null,
                                        parentElement: iframe.parentElement,
                                        tagName: 'IFRAME',
                                        isVideo: true,
                                        videoType: 'embedded',
                                        videoPlatform: platform,
                                        videoElementRef: iframe,
                                        scrollIntoView: function(options) {
                                            iframe.scrollIntoView(options);
                                        },
                                        classList: {
                                            add: function() { 
                                                console.warn('Embedded video element modification prevented');
                                            },
                                            remove: function() { 
                                                console.warn('Embedded video element modification prevented');
                                            }
                                        },
                                        style: {},
                                        setAttribute: function() { 
                                            console.warn('Embedded video element modification prevented');
                                        }
                                    };
                                    
                                    videos.push(syntheticEmbedded);
                                }
                            });
                            
                            // Find elements with background images and add them to images array
                            var allPageElements = document.querySelectorAll('*');
                            var backgroundImages = [];
                            
                            allPageElements.forEach(function(element) {
                                var computedStyle = window.getComputedStyle(element);
                                var backgroundImage = computedStyle.getPropertyValue('background-image');
                                
                                if (backgroundImage && backgroundImage !== 'none') {
                                    var urlMatches = backgroundImage.match(/url\(['"]?([^'"]+)['"]?\)/g);
                                    if (urlMatches) {
                                        urlMatches.forEach(function(urlMatch) {
                                            var url = urlMatch.replace(/url\(['"]?([^'"]+)['"]?\)/, '$1');
                                            
                                            // Create completely isolated synthetic image object
                                            // NO direct DOM manipulation - read-only analysis
                                            var syntheticImg = {
                                                src: url,
                                                currentSrc: url,
                                                getAttribute: function(attr) {
                                                    if (attr === 'alt') return '';
                                                    return null;
                                                },
                                                hasAttribute: function() { return false; },
                                                complete: true,
                                                naturalWidth: 0,
                                                naturalHeight: 0,
                                                width: element.offsetWidth || 0,
                                                height: element.offsetHeight || 0,
                                                loading: null,
                                                parentElement: element.parentElement,
                                                tagName: 'IMG',
                                                isBackgroundImage: true,
                                                // Store element reference but never modify it
                                                backgroundElementRef: element,
                                                // Override methods to prevent DOM modification
                                                scrollIntoView: function(options) {
                                                    // Safe scroll to view without modification
                                                    element.scrollIntoView(options);
                                                },
                                                classList: {
                                                    add: function() { 
                                                        // NO-OP - do not modify background elements
                                                        console.warn('Background image element modification prevented');
                                                    },
                                                    remove: function() { 
                                                        // NO-OP - do not modify background elements
                                                        console.warn('Background image element modification prevented');
                                                    }
                                                },
                                                style: {}, // Isolated empty style object
                                                setAttribute: function() { 
                                                    // NO-OP - do not modify background elements
                                                    console.warn('Background image element modification prevented');
                                                }
                                            };
                                            
                                            backgroundImages.push(syntheticImg);
                                        });
                                    }
                                }
                            });
                            
                            // Add background images to the images array
                            var allImagesArray = [...Array.from(images), ...backgroundImages];
                            var allElements = [...allImagesArray, ...Array.from(svgs), ...favicons, ...videos];
                            var panel = document.createElement('div');
                            panel.className = 'images-audit-panel';
                            
                            // Load saved settings
                            const savedSettings = getSavedPanelSettings();
                            const positionStyle = savedSettings.left ? 
                                `left:${savedSettings.left};` : 
                                `right:${savedSettings.right};`;
                            
                            panel.style.cssText = `position:fixed;top:${savedSettings.top};${positionStyle}width:${savedSettings.width};height:${savedSettings.height};z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);overflow:hidden;resize:both;min-width:400px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5;display:flex;flex-direction:column;`;
                            
                            // Export function
                            function exportToCSV() {
                                try {
                                    // Get current page URL for filename
                                    var currentUrl = window.location.href;
                                    var urlObj = new URL(currentUrl);
                                    var domain = urlObj.hostname.replace(/[^a-zA-Z0-9-]/g, '_');
                                    var pathname = urlObj.pathname.replace(/[^a-zA-Z0-9-_]/g, '_');
                                    var filename = domain + pathname + '_images.csv';
                                    
                                    // Get filter state
                                    const showBrokenOnly = document.getElementById('filter-broken').checked;
                                    const showMissingAltOnly = document.getElementById('filter-missing-alt').checked;
                                    const showMissingTitleDescOnly = document.getElementById('filter-missing-title-desc').checked;
                                    
                                    // CSV headers
                                    var headers = ['Index', 'Type', 'File Name', 'URL', 'Alt Text / Accessibility', 'Display Width', 'Display Height', 'Natural Width', 'Natural Height', 'Format', 'Issues', 'Status'];
                                    
                                    // CSV data
                                    var csvData = [headers];
                                    
                                    allElements.forEach(function(element, i) {
                                        var isElementSVG = isSVG(element);
                                        var isElementFavicon = isFavicon(element);
                                        var isElementVideo = isVideo(element);
                                        var src, fileName, alt, format, issues = [];
                                        var dimensions;
                                        
                                        if (isElementSVG) {
                                            // Handle SVG elements
                                            src = 'inline SVG';
                                            fileName = 'Inline SVG';
                                            alt = getSVGAccessibilityText(element) || '';
                                            format = 'svg';
                                            dimensions = getSVGDimensions(element);
                                            
                                            // SVG-specific issues
                                            if (!alt || alt.trim() === '') issues.push('Missing Title/Desc');
                                            if (!element.getAttribute('role') && !element.getAttribute('aria-hidden')) issues.push('No Role/Hidden');
                                            if (!dimensions.width || !dimensions.height || dimensions.width === 'auto' || dimensions.height === 'auto') issues.push('No Dimensions');
                                            if (element.parentElement && element.parentElement.tagName === 'A') issues.push('Linked');
                                            if (element.getAttribute('aria-hidden') === 'true') issues.push('Decorative');
                                            
                                        } else if (isElementFavicon) {
                                            // Handle Favicon elements
                                            src = element.currentSrc || element.src;
                                            fileName = getFileName(src, element);
                                            alt = element.getAttribute('alt') || 'Favicon';
                                            format = getFileFormat(src, element);
                                            dimensions = {
                                                width: 0,
                                                height: 0,
                                                naturalWidth: 0,
                                                naturalHeight: 0
                                            };
                                            var sizes = element.getAttribute('sizes');
                                            if (sizes) {
                                                var sizeMatch = sizes.match(/(\d+)x(\d+)/);
                                                if (sizeMatch) {
                                                    dimensions.width = parseInt(sizeMatch[1]);
                                                    dimensions.height = parseInt(sizeMatch[2]);
                                                    dimensions.naturalWidth = parseInt(sizeMatch[1]);
                                                    dimensions.naturalHeight = parseInt(sizeMatch[2]);
                                                }
                                            }
                                            
                                            // Favicon-specific issues
                                            if (!element.getAttribute('sizes')) issues.push('No Sizes');
                                            if (!element.getAttribute('type')) issues.push('No Type');
                                            if (element.faviconType === 'default') issues.push('Default Only');
                                            if (format === 'ico' && dimensions.naturalWidth && dimensions.naturalWidth < 32) issues.push('Small Size');
                                            if (format !== 'ico' && format !== 'png' && format !== 'svg') issues.push('Format Issue');
                                            
                                        } else if (isElementVideo) {
                                            // Handle Video elements
                                            src = element.currentSrc || element.src;
                                            fileName = getFileName(src, element);
                                            alt = element.getAttribute('alt') || 'Video';
                                            format = getFileFormat(src, element);
                                            dimensions = {
                                                width: element.width || 0,
                                                height: element.height || 0,
                                                naturalWidth: element.naturalWidth || 0,
                                                naturalHeight: element.naturalHeight || 0
                                            };
                                            
                                            // Video-specific issues
                                            if (element.videoType === 'html5') {
                                                if (!element.getAttribute('poster')) issues.push('No Poster');
                                                if (!element.getAttribute('controls')) issues.push('No Controls');
                                                if (element.hasAttribute('autoplay') && !element.hasAttribute('muted')) issues.push('Autoplay Not Muted');
                                                if (!element.getAttribute('preload') || element.getAttribute('preload') === 'auto') issues.push('Preload Auto');
                                                var trackElements = element.videoElementRef ? element.videoElementRef.querySelectorAll('track') : [];
                                                if (trackElements.length === 0) issues.push('No Captions');
                                            } else if (element.videoType === 'audio') {
                                                if (!element.getAttribute('controls')) issues.push('No Controls');
                                                if (element.hasAttribute('autoplay')) issues.push('Autoplay Audio');
                                            } else if (element.videoType === 'embedded') {
                                                if (!element.getAttribute('title')) issues.push('No Title');
                                                if (element.src && element.src.includes('autoplay=1')) issues.push('Autoplay Embedded');
                                            }
                                            if (format !== 'embedded' && format !== 'mp4' && format !== 'webm' && format !== 'ogg') {
                                                issues.push('Format Issue');
                                            }
                                            
                                        } else {
                                            // Handle IMG elements
                                            src = element.currentSrc || element.src;
                                            fileName = getFileName(src, element);
                                            alt = element.getAttribute('alt') || '';
                                            format = getFileFormat(src, element);
                                            dimensions = {
                                                width: element.width,
                                                height: element.height,
                                                naturalWidth: element.naturalWidth,
                                                naturalHeight: element.naturalHeight
                                            };
                                            
                                            // IMG-specific issues
                                            if (!alt || alt.trim() === '') issues.push('Missing Alt');
                                            if (isBroken(element)) issues.push('Broken');
                                            if (!hasExplicitDimensions(element)) issues.push('No W/H');
                                            if (fileName.match(/^img\d+|image\d+|pic\d+|photo\d+|^$/i)) issues.push('Generic Name');
                                            if (element.loading === 'lazy') issues.push('Lazy');
                                            if (element.parentElement && element.parentElement.tagName === 'A') issues.push('Linked');
                                            if (element.naturalWidth > 2000 || element.naturalHeight > 2000) issues.push('Oversized');
                                            
                                            // Compression and format optimization checks
                                            if (format === 'png' && dimensions.naturalWidth && dimensions.naturalHeight) {
                                                var pixelCount = dimensions.naturalWidth * dimensions.naturalHeight;
                                                if (pixelCount > 100000) {
                                                    issues.push('Consider PNG to JPG conversion');
                                                }
                                            }
                                            if (format === 'gif') issues.push('Convert GIF to modern format');
                                            if (format === 'bmp' || format === 'tiff') issues.push('Convert to web format');
                                            if (!['jpg','jpeg','png','webp','avif','svg'].includes(format.toLowerCase())) {
                                                issues.push('Non-standard format');
                                            }
                                            
                                            // Check for size optimization opportunities
                                            if (dimensions.naturalWidth && dimensions.naturalHeight && dimensions.width && dimensions.height) {
                                                var intrinsicPixels = dimensions.naturalWidth * dimensions.naturalHeight;
                                                var renderedPixels = dimensions.width * dimensions.height;
                                                
                                                if (intrinsicPixels > renderedPixels && 
                                                    (dimensions.naturalWidth - dimensions.width > 50 || dimensions.naturalHeight - dimensions.height > 50)) {
                                                    
                                                    var potentialSavings = ((intrinsicPixels - renderedPixels) / intrinsicPixels * 100).toFixed(0);
                                                    
                                                    if (potentialSavings > 20) {
                                                        issues.push('Reduce to ' + dimensions.width + 'x' + dimensions.height + ' (' + potentialSavings + '% smaller)');
                                                    }
                                                }
                                            }
                                        }
                                        
                                        // Skip if filters are active and element doesn't match
                                        if ((showBrokenOnly || showMissingAltOnly || showMissingTitleDescOnly)) {
                                            let matchesAllFilters = true;
                                            if (showBrokenOnly && !issues.includes('Broken')) matchesAllFilters = false;
                                            if (showMissingAltOnly && !issues.includes('Missing Alt')) matchesAllFilters = false;
                                            if (showMissingTitleDescOnly && !issues.includes('Missing Title/Desc')) matchesAllFilters = false;
                                            
                                            if (!matchesAllFilters) {
                                                return;
                                            }
                                        }
                                        
                                        var status = issues.length === 0 || (issues.length === 1 && (issues[0] === 'Lazy' || issues[0] === 'Linked' || issues[0] === 'Decorative')) ? 'Good' : 'Issues';
                                        
                                        // Escape CSV values and handle commas/quotes
                                        function escapeCSV(value) {
                                            if (typeof value !== 'string') value = String(value);
                                            if (value.includes(',') || value.includes('"') || value.includes('\n')) {
                                                return '"' + value.replace(/"/g, '""') + '"';
                                            }
                                            return value;
                                        }
                                        
                                        var row = [
                                            i + 1,
                                            isElementSVG ? 'SVG' : (isElementFavicon ? 'Favicon' : (isElementVideo ? 'Video' : 'Image')),
                                            escapeCSV(fileName),
                                            escapeCSV(src),
                                            escapeCSV(alt),
                                            dimensions.width || '',
                                            dimensions.height || '',
                                            dimensions.naturalWidth || '',
                                            dimensions.naturalHeight || '',
                                            format.toUpperCase(),
                                            escapeCSV(issues.join('; ')),
                                            status
                                        ];
                                        
                                        csvData.push(row);
                                    });
                                    
                                    // Convert to CSV string
                                    var csvString = csvData.map(function(row) {
                                        return row.join(',');
                                    }).join('\n');
                                    
                                    // Create and download file
                                    var blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
                                    var link = document.createElement('a');
                                    
                                    if (link.download !== undefined) {
                                        var url = URL.createObjectURL(blob);
                                        link.setAttribute('href', url);
                                        link.setAttribute('download', filename);
                                        link.style.visibility = 'hidden';
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                        URL.revokeObjectURL(url);
                                        
                                        // Show success feedback
                                        var feedback = document.createElement('div');
                                        feedback.style.cssText = 'position:fixed;top:20px;left:50%;transform:translateX(-50%);background:#22c55e;color:white;padding:8px 16px;border-radius:6px;z-index:10000000;font-family:Arial,sans-serif;font-size:14px;font-weight:500;';
                                        feedback.textContent = 'CSV exported successfully!';
                                        document.body.appendChild(feedback);
                                        setTimeout(function() {
                                            feedback.remove();
                                        }, 2000);
                                    }
                                } catch (error) {
                                    console.error('Export error:', error);
                                    alert('Error exporting CSV: ' + error.message);
                                }
                            }
                            
                            var html = '<div id="images-sticky-top" style="position:sticky;top:0;z-index:10;background:#0a0a0a;border-bottom:1px solid #2a2a2a;padding:20px 20px 0 20px;"><div id="images-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:24px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;"><h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;">Media SEO Audit (' + allImagesArray.length + ' images, ' + svgs.length + ' SVGs, ' + favicons.length + ' favicons, ' + videos.length + ' videos)</h2><div style="display:flex;gap:8px;align-items:center;"><button id="export-csv-btn" style="padding:6px 12px;background:#7C3AED;color:white;border:1px solid #8B5CF6;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">EXPORT CSV</button><button onclick="this.parentNode.parentNode.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button></div></div>';
                            
                                        // Add filter controls
            html += '<div style="background:#1a1a1a;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:20px;padding:16px 20px;"><div style="display:flex;align-items:center;gap:24px;flex-wrap:wrap;"><span style="color:#9ca3af;font-weight:500;font-size:14px;">Show:</span><label style="display:flex;align-items:center;gap:8px;color:#d1d5db;font-size:14px;cursor:pointer;"><input type="checkbox" id="filter-images" checked style="margin:0;accent-color:#7C3AED;"><span>Images (' + allImagesArray.length + ')</span></label><label style="display:flex;align-items:center;gap:8px;color:#d1d5db;font-size:14px;cursor:pointer;"><input type="checkbox" id="filter-svgs" checked style="margin:0;accent-color:#7C3AED;"><span>SVGs (' + svgs.length + ')</span></label><label style="display:flex;align-items:center;gap:8px;color:#d1d5db;font-size:14px;cursor:pointer;"><input type="checkbox" id="filter-favicons" checked style="margin:0;accent-color:#7C3AED;"><span>Favicons (' + favicons.length + ')</span></label><label style="display:flex;align-items:center;gap:8px;color:#d1d5db;font-size:14px;cursor:pointer;"><input type="checkbox" id="filter-videos" checked style="margin:0;accent-color:#7C3AED;"><span>Videos (' + videos.length + ')</span></label><label style="display:flex;align-items:center;gap:8px;color:#d1d5db;font-size:14px;cursor:pointer;"><input type="checkbox" id="filter-broken" style="margin:0;accent-color:#7C3AED;"><span>Broken Images Only</span></label><label style="display:flex;align-items:center;gap:8px;color:#d1d5db;font-size:14px;cursor:pointer;"><input type="checkbox" id="filter-missing-alt" style="margin:0;accent-color:#7C3AED;"><span>Missing Alt Only</span></label><label style="display:flex;align-items:center;gap:8px;color:#d1d5db;font-size:14px;cursor:pointer;"><input type="checkbox" id="filter-missing-title-desc" style="margin:0;accent-color:#7C3AED;"><span>Missing Title/Desc Only</span></label><label style="display:flex;align-items:center;gap:8px;color:#d1d5db;font-size:14px;cursor:pointer;"><input type="checkbox" id="filter-needs-resizing" style="margin:0;accent-color:#f59e0b;"><span>Images Need Resizing Only</span></label><div id="filter-count" style="color:#6b7280;font-size:13px;margin-left:auto;">Showing ' + (allImagesArray.length + svgs.length + favicons.length + videos.length) + ' elements</div></div></div></div></div>';
                            
                            // Add scrollable content container
                            html += '<div id="images-scrollable-content" style="flex:1;overflow:auto;padding:0 20px 20px 20px;">';
                            
                            // Add optimization summary section
                            var totalOptimizableImages = 0;
                            var totalPotentialSavings = 0;
                            var totalCurrentSize = 0;
                            var totalOptimizedSize = 0;
                            var compressionIssues = 0;
                            
                            // Calculate optimization potential
                            allElements.forEach(function(element) {
                                var isElementSVG = isSVG(element);
                                if (!isElementSVG && !element.isBackgroundImage) {
                                    var dimensions = {
                                        width: element.width,
                                        height: element.height,
                                        naturalWidth: element.naturalWidth,
                                        naturalHeight: element.naturalHeight
                                    };
                                    
                                    if (dimensions.naturalWidth && dimensions.naturalHeight && dimensions.width && dimensions.height) {
                                        var intrinsicPixels = dimensions.naturalWidth * dimensions.naturalHeight;
                                        var renderedPixels = dimensions.width * dimensions.height;
                                        
                                        if (intrinsicPixels > renderedPixels && 
                                            (dimensions.naturalWidth - dimensions.width > 50 || dimensions.naturalHeight - dimensions.height > 50)) {
                                            
                                            var potentialSavings = ((intrinsicPixels - renderedPixels) / intrinsicPixels * 100);
                                            
                                            if (potentialSavings > 20) {
                                                totalOptimizableImages++;
                                                totalPotentialSavings += potentialSavings;
                                                
                                                // Estimate file sizes (rough calculation)
                                                var estimatedCurrentSize = (intrinsicPixels * 3) / 1024; // 3 bytes per pixel estimate
                                                var estimatedOptimizedSize = (renderedPixels * 3) / 1024;
                                                totalCurrentSize += estimatedCurrentSize;
                                                totalOptimizedSize += estimatedOptimizedSize;
                                            }
                                        }
                                    }
                                    
                                    // Check for potential compression issues
                                    var src = element.currentSrc || element.src;
                                    var format = getFileFormat(src);
                                    if (format === 'png' && dimensions.naturalWidth && dimensions.naturalHeight) {
                                        // PNG might be better as JPG for photos
                                        if (dimensions.naturalWidth * dimensions.naturalHeight > 100000) { // Large images
                                            compressionIssues++;
                                        }
                                    }
                                }
                            });
                            
                            var averageSavings = totalOptimizableImages > 0 ? (totalPotentialSavings / totalOptimizableImages).toFixed(1) : 0;
                            var totalSizeReduction = totalCurrentSize > 0 ? (((totalCurrentSize - totalOptimizedSize) / totalCurrentSize) * 100).toFixed(1) : 0;
                            
                            if (totalOptimizableImages > 0 || compressionIssues > 0) {
                                html += '<div style="background:#1a1a1a;border:2px solid #f59e0b;border-radius:8px;margin-bottom:20px;overflow:hidden;"><div style="background:#f59e0b;color:#000;padding:2px 8px;font-weight:600;font-size:14px;">Optimization Opportunities</div><div style="padding:10px;"><div style="display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:16px;">';
                                
                                if (totalOptimizableImages > 0) {
                                    html += '<div style="text-align:center;"><div style="font-size:20px;font-weight:bold;color:#f59e0b;">' + averageSavings + '%</div><div style="color:#9ca3af;font-size:12px;">Average Size Reduction</div></div>';
                                    html += '<div style="text-align:center;"><div style="font-size:20px;font-weight:bold;color:#f59e0b;">' + totalOptimizableImages + '</div><div style="color:#9ca3af;font-size:12px;">Images Need Resizing</div></div>';
                                    html += '<div style="text-align:center;"><div style="font-size:20px;font-weight:bold;color:#f59e0b;">' + totalSizeReduction + '%</div><div style="color:#9ca3af;font-size:12px;">Total Size Reduction</div></div>';
                                }
                                
                                if (compressionIssues > 0) {
                                    html += '<div style="text-align:center;"><div style="font-size:20px;font-weight:bold;color:#ef4444;">' + compressionIssues + '</div><div style="color:#9ca3af;font-size:12px;">Compression Issues</div></div>';
                                }
                                
                                html += '</div></div></div>';
                            }
                            
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;"><div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Element Analysis</div><div style="overflow:auto;max-height:500px;"><table id="elements-table" style="width:100%;border-collapse:collapse;font-size:13px;table-layout:fixed;"><thead><tr style="background:#111111;">'+
'<th class="col-jump" style="position:sticky;top:0;z-index:2;background:#3a3a3a;padding:12px 16px;text-align:left;color:#9ca3af;font-weight:500;font-size:12px;border-bottom:1px solid #2a2a2a;width:80px;min-width:80px;">Jump<div class="resize-handle"></div></th>'+ 
'<th class="col-num" style="position:sticky;top:0;z-index:2;background:#3a3a3a;padding:12px 16px;text-align:left;color:#9ca3af;font-weight:500;font-size:12px;border-bottom:1px solid #2a2a2a;width:40px;min-width:40px;">#<div class="resize-handle"></div></th>'+ 
'<th class="col-preview" style="position:sticky;top:0;z-index:2;background:#3a3a3a;padding:12px 16px;text-align:left;color:#9ca3af;font-weight:500;font-size:12px;border-bottom:1px solid #2a2a2a;width:auto;min-width:120px;">Preview<div class="resize-handle"></div></th>'+ 
'<th class="col-filename" style="position:sticky;top:0;z-index:2;background:#3a3a3a;padding:12px 16px;text-align:left;color:#9ca3af;font-weight:500;font-size:12px;border-bottom:1px solid #2a2a2a;width:150px;min-width:150px;">File Name<div class="resize-handle"></div></th>'+ 
'<th class="col-url" style="position:sticky;top:0;z-index:2;background:#3a3a3a;padding:12px 16px;text-align:left;color:#9ca3af;font-weight:500;font-size:12px;border-bottom:1px solid #2a2a2a;width:120px;min-width:120px;">URL<div class="resize-handle"></div></th>'+ 
'<th class="col-alt" style="position:sticky;top:0;z-index:2;background:#3a3a3a;padding:12px 16px;text-align:left;color:#9ca3af;font-weight:500;font-size:12px;border-bottom:1px solid #2a2a2a;width:150px;min-width:150px;">Alt Text<div class="resize-handle"></div></th>'+ 
'<th class="col-dimensions" style="position:sticky;top:0;z-index:2;background:#3a3a3a;padding:12px 16px;text-align:left;color:#9ca3af;font-weight:500;font-size:12px;border-bottom:1px solid #2a2a2a;width:200px;min-width:160px;">Dimensions<div class="resize-handle"></div></th>'+ 
'<th class="col-format" style="position:sticky;top:0;z-index:2;background:#3a3a3a;padding:12px 16px;text-align:left;color:#9ca3af;font-weight:500;font-size:12px;border-bottom:1px solid #2a2a2a;width:120px;min-width:120px;">Format<div class="resize-handle"></div></th>'+ 
'<th class="col-issues" style="position:sticky;top:0;z-index:2;background:#3a3a3a;padding:12px 16px;text-align:left;color:#9ca3af;font-weight:500;font-size:12px;border-bottom:1px solid #2a2a2a;width:200px;min-width:120px;white-space:normal;word-break:break-word;">Issues<div class="resize-handle"></div></th>'+ 
'</tr></thead><tbody>';
                            
                            allElements.forEach(function(element, i) {
                                var elementId = 'seo-element-' + i;
                                // Store original ID if it exists, otherwise store null
                                var originalId = element.id || null;
                                element.setAttribute('data-original-id', originalId || 'none');
                                element.setAttribute('id', elementId);
                                
                                var isElementSVG = isSVG(element);
                                var isElementFavicon = isFavicon(element);
                                var src, fileName, alt, format, flags = [];
                                var dimensions, preview;
                                
                                if (isElementSVG) {
                                    // Handle SVG elements
                                    src = 'inline SVG';
                                    fileName = 'Inline SVG';
                                    alt = getSVGAccessibilityText(element);
                                    format = 'svg';
                                    dimensions = getSVGDimensions(element);
                                    preview = createSVGPreview(element);
                                    
                                    // SVG-specific flags
                                    if (!alt || alt.trim() === '') flags.push('<span style="color:#ef4444;">Add title and desc elements to SVG for accessibility</span>');
                                    if (!element.getAttribute('role') && !element.getAttribute('aria-hidden')) flags.push('<span style="color:#f59e0b;">Add role="img" or aria-hidden="true" to SVG for screen readers</span>');
                                    if (!dimensions.width || !dimensions.height || dimensions.width === 'auto' || dimensions.height === 'auto') flags.push('<span style="color:#f59e0b;">Add explicit width and height attributes to SVG</span>');
                                    if (element.parentElement && element.parentElement.tagName === 'A') flags.push('<span style="color:#3b82f6;">SVG is linked - ensure link purpose is clear</span>');
                                    if (element.getAttribute('aria-hidden') === 'true') flags.push('<span style="color:#9ca3af;">SVG is decorative (hidden from screen readers)</span>');
                                    
                                } else if (isElementFavicon) {
                                    // Handle Favicon elements
                                    src = element.currentSrc || element.src;
                                    fileName = getFileName(src, element);
                                    alt = element.getAttribute('alt') || 'Favicon';
                                    format = getFileFormat(src, element);
                                    dimensions = {
                                        width: 0,
                                        height: 0,
                                        naturalWidth: 0,
                                        naturalHeight: 0
                                    };
                                    var sizes = element.getAttribute('sizes');
                                    if (sizes) {
                                        var sizeMatch = sizes.match(/(\d+)x(\d+)/);
                                        if (sizeMatch) {
                                            dimensions.width = parseInt(sizeMatch[1]);
                                            dimensions.height = parseInt(sizeMatch[2]);
                                            dimensions.naturalWidth = parseInt(sizeMatch[1]);
                                            dimensions.naturalHeight = parseInt(sizeMatch[2]);
                                        }
                                    }
                                    preview = createFaviconPreview(element);
                                    
                                    // Favicon-specific flags
                                    if (!element.getAttribute('sizes')) flags.push('<span style="color:#f59e0b;">Add sizes attribute to favicon for better browser selection</span>');
                                    if (!element.getAttribute('type')) flags.push('<span style="color:#f59e0b;">Add type attribute to favicon for format specification</span>');
                                    if (element.faviconType === 'default') flags.push('<span style="color:#3b82f6;">Default favicon - consider adding explicit favicon link tags</span>');
                                    if (format === 'ico' && dimensions.naturalWidth && dimensions.naturalWidth < 32) flags.push('<span style="color:#f59e0b;">Consider providing larger favicon sizes (32x32, 64x64) for better quality</span>');
                                    if (format !== 'ico' && format !== 'png' && format !== 'svg') flags.push('<span style="color:#f59e0b;">Consider using ICO, PNG, or SVG format for better browser compatibility</span>');
                                    
                                } else if (isVideo(element)) {
                                    // Handle Video elements
                                    src = element.currentSrc || element.src;
                                    fileName = getFileName(src, element);
                                    alt = element.getAttribute('alt') || 'Video';
                                    format = getFileFormat(src, element);
                                    dimensions = {
                                        width: element.width || 0,
                                        height: element.height || 0,
                                        naturalWidth: element.naturalWidth || 0,
                                        naturalHeight: element.naturalHeight || 0
                                    };
                                    preview = createVideoPreview(element);
                                    
                                    // Video-specific flags
                                    if (element.videoType === 'html5') {
                                        if (!element.getAttribute('poster')) flags.push('<span style="color:#f59e0b;">Add poster attribute to video for better loading experience</span>');
                                        if (!element.getAttribute('controls')) flags.push('<span style="color:#f59e0b;">Add controls attribute to video for user accessibility</span>');
                                        if (element.hasAttribute('autoplay') && !element.hasAttribute('muted')) flags.push('<span style="color:#ef4444;">Autoplay videos should be muted for better UX</span>');
                                        if (!element.getAttribute('preload') || element.getAttribute('preload') === 'auto') flags.push('<span style="color:#f59e0b;">Consider preload="metadata" for better performance</span>');
                                        var trackElements = element.videoElementRef.querySelectorAll('track');
                                        if (trackElements.length === 0) flags.push('<span style="color:#f59e0b;">Add captions/subtitles track for accessibility</span>');
                                    } else if (element.videoType === 'audio') {
                                        if (!element.getAttribute('controls')) flags.push('<span style="color:#f59e0b;">Add controls attribute to audio for user accessibility</span>');
                                        if (element.hasAttribute('autoplay')) flags.push('<span style="color:#ef4444;">Autoplay audio can be disruptive to users</span>');
                                    } else if (element.videoType === 'embedded') {
                                        flags.push('<span style="color:#3b82f6;">Embedded video from ' + (element.videoPlatform || 'external') + ' platform</span>');
                                        if (!element.getAttribute('title')) flags.push('<span style="color:#f59e0b;">Add title attribute to iframe for accessibility</span>');
                                        if (element.src && element.src.includes('autoplay=1')) flags.push('<span style="color:#ef4444;">Embedded video has autoplay enabled</span>');
                                    }
                                    if (format !== 'embedded' && format !== 'mp4' && format !== 'webm' && format !== 'ogg') {
                                        flags.push('<span style="color:#f59e0b;">Consider modern video format (MP4/WebM) for better browser support</span>');
                                    }
                                    
                                } else {
                                    // Handle IMG elements
                                    src = element.currentSrc || element.src;
                                    fileName = getFileName(src, element);
                                    alt = element.getAttribute('alt');
                                    format = getFileFormat(src, element);
                                    dimensions = {
                                        width: element.width,
                                        height: element.height,
                                        naturalWidth: element.naturalWidth,
                                        naturalHeight: element.naturalHeight
                                    };
                                    preview = '<img src="' + src + '" style="width:100%;height:auto;max-width:300px;min-width:60px;border:1px solid #2a2a2a;border-radius:4px;object-fit:contain;" loading="lazy">';
                                    
                                    // IMG-specific flags
                                    if (!alt || alt.trim() === '') flags.push('<span style="color:#ef4444;">Add missing alt text to image for better SEO</span>');
                                    if (isBroken(element)) flags.push('<span style="color:#ef4444;">Fix broken image source URL</span>');
                                    if (!hasExplicitDimensions(element)) flags.push('<span style="color:#f59e0b;">Add width and height attributes to prevent layout shift</span>');
                                    if (fileName.match(/^img\d+|image\d+|pic\d+|photo\d+|^$/i)) flags.push('<span style="color:#f59e0b;">Rename image file with descriptive keywords</span>');
                                    if (element.loading === 'lazy') flags.push('<span style="color:#22c55e;">Image uses lazy loading (good for performance)</span>');
                                    if (element.parentElement && element.parentElement.tagName === 'A') flags.push('<span style="color:#3b82f6;">Image is linked - ensure link purpose is clear</span>');
                                    if (element.naturalWidth > 2000 || element.naturalHeight > 2000) flags.push('<span style="color:#ef4444;">Reduce image resolution - extremely large dimensions</span>');
                                    
                                    // Compression and format optimization checks
                                    if (format === 'png' && dimensions.naturalWidth && dimensions.naturalHeight) {
                                        var pixelCount = dimensions.naturalWidth * dimensions.naturalHeight;
                                        if (pixelCount > 100000) { // Large images over ~300x300
                                            flags.push('<span style="color:#f59e0b;">Consider converting PNG to JPG for better compression</span>');
                                        }
                                    }
                                    if (format === 'gif' && dimensions.naturalWidth && dimensions.naturalHeight) {
                                        flags.push('<span style="color:#f59e0b;">Consider converting GIF to modern format (WebP/PNG)</span>');
                                    }
                                    if (format === 'bmp' || format === 'tiff') {
                                        flags.push('<span style="color:#ef4444;">Convert ' + format.toUpperCase() + ' to web-optimized format (JPG/PNG/WebP)</span>');
                                    }
                                    if (!['jpg','jpeg','png','webp','avif','svg'].includes(format.toLowerCase())) {
                                        flags.push('<span style="color:#f59e0b;">Unknown or non-standard image format - verify browser support</span>');
                                    }
                                    
                                    // Check for oversized images (intrinsic vs rendered size)
                                    if (dimensions.naturalWidth && dimensions.naturalHeight && dimensions.width && dimensions.height) {
                                        var intrinsicPixels = dimensions.naturalWidth * dimensions.naturalHeight;
                                        var renderedPixels = dimensions.width * dimensions.height;
                                        
                                        // Only flag if image is significantly larger than needed (more than 10% difference and at least 50px difference in either dimension)
                                        if (intrinsicPixels > renderedPixels && 
                                            (dimensions.naturalWidth - dimensions.width > 50 || dimensions.naturalHeight - dimensions.height > 50)) {
                                            
                                            var potentialSavings = ((intrinsicPixels - renderedPixels) / intrinsicPixels * 100).toFixed(0);
                                            
                                            // Only show significant savings (>20%)
                                            if (potentialSavings > 20) {
                                                flags.push('<span style="color:#f59e0b;">Resize image to ' + dimensions.width + '×' + dimensions.height + ' pixels to reduce file size by ' + potentialSavings + '%</span>');
                                            }
                                        }
                                    }
                                }
                                
                                var jumpButton = '<button onclick="(' + jumpToImage.toString() + ')(\''+elementId+'\')" style="background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;padding:4px 8px;cursor:pointer;font-size:11px;font-weight:500;transition:all 0.2s;">JUMP</button>';
                                
                                var urlCell;
                                if (isElementSVG) {
                                    urlCell = '<span style="color:#9ca3af;font-style:italic;font-size:11px;">Inline SVG</span>';
                                } else {
                                    var truncatedUrl = truncateUrl(src, 30);
                                    urlCell = '<span onclick="(' + copyToClipboard.toString() + ')(\''+src.replace(/'/g, "\\'")+'\')" style="cursor:pointer;color:#3b82f6;text-decoration:underline;font-size:11px;" title="Click to copy full URL: ' + src.replace(/'/g, "\\'") + '">' + truncatedUrl + '</span>';
                                }
                                
                                // Function to get file size
                                function getImageSize(imageUrl, callback) {
                                    try {
                                        fetch(imageUrl, { method: 'HEAD' })
                                            .then(response => {
                                                const contentLength = response.headers.get('content-length');
                                                if (contentLength) {
                                                    const sizeInBytes = parseInt(contentLength);
                                                    const sizeInKB = (sizeInBytes / 1024).toFixed(1);
                                                    callback(sizeInKB + ' kB');
                                                } else {
                                                    callback('Unknown');
                                                }
                                            })
                                            .catch(() => callback('Unknown'));
                                    } catch (e) {
                                        callback('Unknown');
                                    }
                                }
                                
                                var dimensionText;
                                var elementId = 'seo-element-' + i;
                                
                                if (isElementSVG) {
                                    dimensionText = '<div style="font-size:11px;line-height:1.4;"><strong>Rendered:</strong> ' + dimensions.width + '×' + dimensions.height + ' px<br><strong>Intrinsic:</strong> ' + dimensions.naturalWidth + '×' + dimensions.naturalHeight + ' px<br><strong>File size:</strong> <span id="size-' + elementId + '">Loading...</span></div>';
                                    
                                    // Try to get SVG size (for inline SVGs this won't work)
                                    setTimeout(() => {
                                        document.getElementById('size-' + elementId).textContent = 'Inline SVG';
                                    }, 100);
                                } else {
                                    var renderedAspectRatio = dimensions.width && dimensions.height ? (dimensions.width / dimensions.height).toFixed(2) : 'N/A';
                                    var intrinsicAspectRatio = dimensions.naturalWidth && dimensions.naturalHeight ? (dimensions.naturalWidth / dimensions.naturalHeight).toFixed(2) : 'N/A';
                                    
                                    dimensionText = '<div style="font-size:11px;line-height:1.4;">';
                                    dimensionText += '<strong>Rendered:</strong> ' + (dimensions.width || 0) + '×' + (dimensions.height || 0) + ' px<br>';
                                    dimensionText += '<strong>Ratio:</strong> ' + renderedAspectRatio + '<br>';
                                    dimensionText += '<strong>Intrinsic:</strong> ' + (dimensions.naturalWidth || 'N/A') + '×' + (dimensions.naturalHeight || 'N/A') + ' px<br>';
                                    dimensionText += '<strong>Intrinsic Ratio:</strong> ' + intrinsicAspectRatio + '<br>';
                                    dimensionText += '<strong>File size:</strong> <span id="size-' + elementId + '">Loading...</span>';
                                    
                                    if (element.isBackgroundImage) {
                                        dimensionText += '<br><span style="color:#9ca3af;">Background Image</span>';
                                    }
                                    
                                    dimensionText += '</div>';
                                    
                                    // Get file size asynchronously
                                    setTimeout(() => {
                                        getImageSize(src, (size) => {
                                            const sizeElement = document.getElementById('size-' + elementId);
                                            if (sizeElement) {
                                                sizeElement.textContent = size;
                                            }
                                        });
                                    }, 100);
                                }
                                
                                // Check if this image needs resizing for border styling
                                var needsResizing = false;
                                if (!isElementSVG && dimensions.naturalWidth && dimensions.naturalHeight && dimensions.width && dimensions.height) {
                                    var intrinsicPixels = dimensions.naturalWidth * dimensions.naturalHeight;
                                    var renderedPixels = dimensions.width * dimensions.height;
                                    
                                    if (intrinsicPixels > renderedPixels && 
                                        (dimensions.naturalWidth - dimensions.width > 50 || dimensions.naturalHeight - dimensions.height > 50)) {
                                        
                                        var potentialSavings = ((intrinsicPixels - renderedPixels) / intrinsicPixels * 100);
                                        if (potentialSavings > 20) {
                                            needsResizing = true;
                                        }
                                    }
                                }
                                
                                var rowStyle = needsResizing ? 'background:#111111;border:2px solid #f59e0b;' : 'background:#111111;';
                                
                                html += '<tr data-type="' + (isElementSVG ? 'svg' : (isElementFavicon ? 'favicon' : (isVideo(element) ? 'video' : 'image'))) + '" data-needs-resizing="' + needsResizing + '" style="' + rowStyle + '">' +
                                    '<td style="padding:12px 16px;text-align:center;border-bottom:1px solid #2a2a2a;width:80px;min-width:80px;">' + jumpButton + '</td>' +
                                    '<td style="padding:12px 16px;text-align:center;border-bottom:1px solid #2a2a2a;color:#6b7280;width:40px;min-width:40px;">' + (i+1) + '</td>' +
                                    '<td style="padding:12px 16px;text-align:center;border-bottom:1px solid #2a2a2a;width:auto;min-width:120px;">' + preview + '</td>' +
                                    '<td style="padding:12px 16px;border-bottom:1px solid #2a2a2a;width:150px;min-width:150px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#d1d5db;" title="' + (isElementSVG ? 'Inline SVG' : src) + '">' + fileName + '</td>' +
                                    '<td style="padding:12px 16px;border-bottom:1px solid #2a2a2a;width:120px;min-width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">' + urlCell + '</td>' +
                                    '<td style="padding:12px 16px;border-bottom:1px solid #2a2a2a;width:150px;min-width:150px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#d1d5db;" title="' + (alt || '') + '">' + (alt ? alt : '<span style="color:#ef4444;">(missing)</span>') + '</td>' +
                                    '<td style="padding:12px 16px;border-bottom:1px solid #2a2a2a;width:200px;min-width:160px;color:#d1d5db;vertical-align:top;">' + dimensionText + '</td>' +
                                    '<td style="padding:12px 16px;border-bottom:1px solid #2a2a2a;width:60px;min-width:60px;text-align:center;color:#d1d5db;font-weight:500;">' + format.toUpperCase() + '</td>' +
                                    '<td style="padding:12px 16px;border-bottom:1px solid #2a2a2a;width:200px;min-width:180px;color:#d1d5db;font-size:12px;">' + (flags.length ? '<ul style="margin:0;padding:0;list-style:none;">' + flags.map(flag => '<li style="margin:0 0 4px 0;padding:0 0 0 12px;position:relative;"><span style="position:absolute;left:0;top:0;color:#fff;">•</span>' + flag + '</li>').join('') + '</ul>' : '<span style="color:#22c55e;">✓ Good</span>') + '</td>' +
                                    '</tr>';
                            });
                            
                            html += '</tbody></table></div></div>';
                            
                            var brokenCount = Array.from(allImagesArray).filter(isBroken).length;
                            var missingAltCount = Array.from(allImagesArray).filter(function(img) { 
                                var alt = img.getAttribute('alt'); 
                                return !alt || alt.trim() === ''; 
                            }).length;
                            var noDimensionsCount = Array.from(allImagesArray).filter(function(img) { 
                                return !hasExplicitDimensions(img); 
                            }).length;
                            
                            // SVG-specific counts
                            var missingTitleDescCount = Array.from(svgs).filter(function(svg) {
                                var accessibilityText = getSVGAccessibilityText(svg);
                                return !accessibilityText || accessibilityText.trim() === '';
                            }).length;
                            var noSVGDimensionsCount = Array.from(svgs).filter(function(svg) {
                                var dims = getSVGDimensions(svg);
                                return !dims.width || !dims.height || dims.width === 'auto' || dims.height === 'auto';
                            }).length;
                            
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;overflow:hidden;"><div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Summary</div><div style="padding:20px;">';
                            html += '<strong style="color:#d1d5db;">Image Results:</strong> ';
                            html += '<span style="color:' + (brokenCount > 0 ? '#ef4444' : '#22c55e') + ';">' + brokenCount + ' broken</span>, ';
                            html += '<span style="color:' + (missingAltCount > 0 ? '#ef4444' : '#22c55e') + ';">' + missingAltCount + ' missing alt</span>, ';
                            html += '<span style="color:' + (noDimensionsCount > 0 ? '#f59e0b' : '#22c55e') + ';">' + noDimensionsCount + ' no dimensions</span>';
                            
                            if (svgs.length > 0) {
                                html += '<br><br><strong style="color:#d1d5db;">SVG Results:</strong> ';
                                html += '<span style="color:' + (missingTitleDescCount > 0 ? '#ef4444' : '#22c55e') + ';">' + missingTitleDescCount + ' missing accessibility</span>, ';
                                html += '<span style="color:' + (noSVGDimensionsCount > 0 ? '#f59e0b' : '#22c55e') + ';">' + noSVGDimensionsCount + ' no dimensions</span>';
                            }
                            
                            html += '</div></div>';
                            
                            // Info note
                            html += '<div style="background:#1a1a1a;border-radius:6px;border:1px solid #2a2a2a;padding:16px;text-align:center;color:#6b7280;font-size:12px;margin-top:16px;">Click JUMP to scroll to any image/SVG on the page • Click image URLs to copy to clipboard • SVGs show accessibility info in Alt Text column</div>';
                            
                            // Close scrollable content container
                            html += '</div>';
                            
                            panel.innerHTML = html;
                            // --- COLUMN RESIZE LOGIC (runs after table is rendered) ---
                            try {
                                var resizeStyle = document.createElement('style');
                                resizeStyle.textContent = `.resize-handle { position: absolute; right: 0; top: 0; width: 8px; height: 100%; cursor: col-resize; z-index: 10; background: rgba(255,255,255,0.15); border-right: 2px solid #e5e5e5; transition: background 0.2s; }\n.images-audit-panel #elements-table > thead > tr > th { position: relative; }\n.images-audit-panel #elements-table > thead > tr > th { background-color: #3a3a3a !important; color: #9ca3af !important; }\n#elements-table > thead > tr > th { background-color: #3a3a3a !important; color: #9ca3af !important; }\n#elements-table > th { background-color: #3a3a3a !important; color: #9ca3af !important; }\n.resize-handle:hover { background: #fff; border-right: 2px solid #7c3aed; }`;
                                document.head.appendChild(resizeStyle);
                                var table = panel.querySelector('#elements-table');
                                if (table) {
                                    var ths = table.querySelectorAll('th');
                                    var startX, startWidth, colIndex, resizingTh;
                                    function onMouseMove(e) {
                                        if (!resizingTh) return;
                                        var dx = e.clientX - startX;
                                        var newWidth = Math.max(40, startWidth + dx);
                                        resizingTh.style.width = newWidth + 'px';
                                        // Set width for all cells in this column
                                        var idx = colIndex;
                                        table.querySelectorAll('tr').forEach(function(row) {
                                            var cell = row.children[idx];
                                            if (cell) cell.style.width = newWidth + 'px';
                                        });
                                    }
                                    function onMouseUp() {
                                        document.removeEventListener('mousemove', onMouseMove);
                                        document.removeEventListener('mouseup', onMouseUp);
                                        resizingTh = null;
                                    }
                                    ths.forEach(function(th, i) {
                                        var handle = th.querySelector('.resize-handle');
                                        if (!handle) return;
                                        handle.addEventListener('mousedown', function(e) {
                                            e.preventDefault();
                                            startX = e.clientX;
                                            startWidth = th.offsetWidth;
                                            colIndex = i;
                                            resizingTh = th;
                                            document.addEventListener('mousemove', onMouseMove);
                                            document.addEventListener('mouseup', onMouseUp);
                                        });
                                    });
                                }
                            } catch (err) {}
                            
                            // Debounce function to prevent rapid consecutive calls
                            function debounce(func, wait) {
                                let timeout;
                                return function executedFunction(...args) {
                                    const later = () => {
                                        clearTimeout(timeout);
                                        func(...args);
                                    };
                                    clearTimeout(timeout);
                                    timeout = setTimeout(later, wait);
                                };
                            }
                            
                            // Add filter functionality after DOM is ready
                            function filterElements() {
                                try {
                                    const imagesCheckbox = document.getElementById('filter-images');
                                    const svgsCheckbox = document.getElementById('filter-svgs');
                                    const faviconsCheckbox = document.getElementById('filter-favicons');
                                    const videosCheckbox = document.getElementById('filter-videos');
                                    const brokenCheckbox = document.getElementById('filter-broken');
                                    const missingAltCheckbox = document.getElementById('filter-missing-alt');
                                    const missingTitleDescCheckbox = document.getElementById('filter-missing-title-desc');
                                    const needsResizingCheckbox = document.getElementById('filter-needs-resizing');
                                    
                                    // Safety checks for all checkboxes
                                    if (!imagesCheckbox || !svgsCheckbox || !faviconsCheckbox || !videosCheckbox || !brokenCheckbox || !missingAltCheckbox || !missingTitleDescCheckbox || !needsResizingCheckbox) {
                                        console.error('One or more filter checkboxes not found');
                                        return;
                                    }
                                    
                                    const showImages = imagesCheckbox.checked;
                                    const showSVGs = svgsCheckbox.checked;
                                    const showFavicons = faviconsCheckbox.checked;
                                    const showVideos = videosCheckbox.checked;
                                    const showBrokenOnly = brokenCheckbox.checked;
                                    const showMissingAltOnly = missingAltCheckbox.checked;
                                    const showMissingTitleDescOnly = missingTitleDescCheckbox.checked;
                                    const showNeedsResizingOnly = needsResizingCheckbox.checked;
                                    const table = document.getElementById('elements-table');
                                    const filterCount = document.getElementById('filter-count');
                                    
                                    // Safety check - make sure table exists
                                    if (!table) {
                                        console.error('Elements table not found');
                                        return;
                                    }
                                
                                const rows = table.querySelectorAll('tbody tr');
                                
                                console.log('Filtering - Images:', showImages, 'SVGs:', showSVGs, 'Favicons:', showFavicons, 'Videos:', showVideos, 'Broken Only:', showBrokenOnly, 'Missing Alt Only:', showMissingAltOnly, 'Missing Title/Desc Only:', showMissingTitleDescOnly, 'Needs Resizing Only:', showNeedsResizingOnly);
                                
                                // Reset all rows to be visible first (fresh start)
                                rows.forEach(function(row) {
                                    row.style.display = 'table-row';
                                    row.style.visibility = 'visible';
                                });
                                
                                let visibleCount = 0;
                                let visibleNumber = 1;
                                
                                rows.forEach(function(row) {
                                    const type = row.getAttribute('data-type');
                                    let shouldShow = (type === 'image' && showImages) || (type === 'svg' && showSVGs) || (type === 'favicon' && showFavicons) || (type === 'video' && showVideos);
                                    
                                    // Apply specific filters if any are active
                                    if (shouldShow && (showBrokenOnly || showMissingAltOnly || showMissingTitleDescOnly || showNeedsResizingOnly)) {
                                        const issuesCell = row.querySelector('td:last-child');
                                        if (issuesCell) {
                                            const hasBrokenFlag = issuesCell.innerHTML.includes('Broken');
                                            const hasMissingAltFlag = issuesCell.innerHTML.includes('Missing Alt');
                                            const hasMissingTitleDescFlag = issuesCell.innerHTML.includes('Missing Title/Desc');
                                            const hasResizeFlag = issuesCell.innerHTML.includes('Resize image to');
                                            const needsResizingDataAttr = row.getAttribute('data-needs-resizing') === 'true';
                                            
                                            // Check each filter condition - ALL active filters must match (AND logic)
                                            let matchesAllFilters = true;
                                            if (showBrokenOnly && !hasBrokenFlag) matchesAllFilters = false;
                                            if (showMissingAltOnly && !hasMissingAltFlag) matchesAllFilters = false;
                                            if (showMissingTitleDescOnly && !hasMissingTitleDescFlag) matchesAllFilters = false;
                                            if (showNeedsResizingOnly && !needsResizingDataAttr) matchesAllFilters = false;
                                            
                                            shouldShow = matchesAllFilters;
                                        } else {
                                            shouldShow = false;
                                        }
                                    }
                                    // If no specific filters are active, show all elements that match type filters
                                    
                                    console.log('Row type:', type, 'Should show:', shouldShow, 'Display:', row.style.display, 'Visibility:', row.style.visibility);
                                    
                                    if (shouldShow) {
                                        row.style.display = 'table-row';
                                        row.style.visibility = 'visible';
                                        // Update the row number
                                        const numberCell = row.querySelector('td:nth-child(2)');
                                        if (numberCell) {
                                            numberCell.textContent = visibleNumber;
                                        }
                                        visibleNumber++;
                                        visibleCount++;
                                    } else {
                                        row.style.display = 'none';
                                        row.style.visibility = 'hidden';
                                    }
                                });
                                
                                console.log('Visible count:', visibleCount);
                                
                                // Update the counter
                                let filterText = 'Showing ' + visibleCount + ' elements';
                                if (showBrokenOnly && showMissingAltOnly && showMissingTitleDescOnly && showNeedsResizingOnly) {
                                    filterText = 'Showing ' + visibleCount + ' elements with issues';
                                } else if (showNeedsResizingOnly) {
                                    filterText = 'Showing ' + visibleCount + ' images needing resizing';
                                } else if (showBrokenOnly && showMissingAltOnly) {
                                    filterText = 'Showing ' + visibleCount + ' broken/missing alt elements';
                                } else if (showBrokenOnly && showMissingTitleDescOnly) {
                                    filterText = 'Showing ' + visibleCount + ' broken/missing title-desc elements';
                                } else if (showMissingAltOnly && showMissingTitleDescOnly) {
                                    filterText = 'Showing ' + visibleCount + ' missing alt/title-desc elements';
                                } else if (showBrokenOnly) {
                                    filterText = 'Showing ' + visibleCount + ' broken images';
                                } else if (showMissingAltOnly) {
                                    filterText = 'Showing ' + visibleCount + ' missing alt images';
                                } else if (showMissingTitleDescOnly) {
                                    filterText = 'Showing ' + visibleCount + ' missing title/desc SVGs';
                                } else {
                                    var activeTypes = [];
                                    if (showImages) activeTypes.push('images');
                                    if (showSVGs) activeTypes.push('SVGs');
                                    if (showFavicons) activeTypes.push('favicons');
                                    if (showVideos) activeTypes.push('videos');
                                    
                                    if (activeTypes.length === 0) {
                                        filterText = 'No elements selected';
                                    } else if (activeTypes.length === 1) {
                                        filterText = 'Showing ' + visibleCount + ' ' + activeTypes[0];
                                    } else {
                                        filterText = 'Showing ' + visibleCount + ' ' + activeTypes.join(', ');
                                    }
                                }
                                
                                if (filterCount) {
                                    filterCount.textContent = filterText;
                                }
                                
                                // Update table visibility and show/hide no results message
                                const tableContainer = table.closest('div[style*="background:#111111"]');
                                if (tableContainer) {
                                    // Always show the table container
                                    tableContainer.style.display = 'block';
                                    
                                    // Add or update "no results" message
                                    let noResultsMsg = tableContainer.querySelector('.no-results-message');
                                    if (visibleCount === 0) {
                                        if (!noResultsMsg) {
                                            noResultsMsg = document.createElement('div');
                                            noResultsMsg.className = 'no-results-message';
                                            noResultsMsg.style.cssText = 'padding:40px 20px;text-align:center;color:#9ca3af;font-size:14px;font-style:italic;';
                                            noResultsMsg.textContent = 'No elements match the current filters';
                                            tableContainer.appendChild(noResultsMsg);
                                        }
                                        noResultsMsg.style.display = 'block';
                                        table.style.display = 'none';
                                        console.log('Showing no results message');
                                    } else {
                                        if (noResultsMsg) {
                                            noResultsMsg.style.display = 'none';
                                        }
                                        table.style.display = 'table';
                                        table.style.visibility = 'visible';
                                        console.log('Table shown -', visibleCount, 'visible elements');
                                    }
                                }
                                } catch (error) {
                                    console.error('Error in filterElements:', error);
                                }
                            }
                            
                            // Create debounced version of filterElements
                            const debouncedFilterElements = debounce(filterElements, 100);
                            
                            // Use event delegation for more robust event handling
                            panel.addEventListener('change', function(event) {
                                const target = event.target;
                                if (target && target.type === 'checkbox' && 
                                    (target.id === 'filter-images' || 
                                     target.id === 'filter-svgs' || 
                                     target.id === 'filter-favicons' || 
                                     target.id === 'filter-videos' || 
                                     target.id === 'filter-broken' || 
                                     target.id === 'filter-missing-alt' || 
                                     target.id === 'filter-missing-title-desc' || 
                                     target.id === 'filter-needs-resizing')) {
                                    console.log('Filter checkbox changed:', target.id, target.checked);
                                    debouncedFilterElements();
                                }
                            });
                            document.body.appendChild(panel);
                            
                            // Add export button event listener
                            var exportBtn = panel.querySelector('#export-csv-btn');
                            if (exportBtn) {
                                exportBtn.addEventListener('click', exportToCSV);
                            }
                            
                            // Handle Escape key to close panel
                            function imagesActionEscapeListener(e) {
                                if (e.key === 'Escape') {
                                    panel.remove();
                                    document.removeEventListener('keydown', imagesActionEscapeListener);
                                    delete window.imagesActionEscapeListener;
                                }
                            }
                            // Store reference globally for cleanup
                            window.imagesActionEscapeListener = imagesActionEscapeListener;
                            document.addEventListener('keydown', imagesActionEscapeListener);
                            
                            // Add drag functionality
                            var isDragging = false;
                            var currentX;
                            var currentY;
                            var initialX;
                            var initialY;
                            var xOffset = 0;
                            var yOffset = 0;
                            
                            var header = panel.querySelector('#images-header');
                            var stickyTop = panel.querySelector('#images-sticky-top');
                            
                            function dragStart(e) {
                                if (e.target.tagName === 'BUTTON') return; // Don't drag when clicking close button
                                
                                if (e.type === "touchstart") {
                                    initialX = e.touches[0].clientX - xOffset;
                                    initialY = e.touches[0].clientY - yOffset;
                                } else {
                                    initialX = e.clientX - xOffset;
                                    initialY = e.clientY - yOffset;
                                }
                                
                                if (e.target === header || header.contains(e.target) || e.target === stickyTop || stickyTop.contains(e.target)) {
                                    isDragging = true;
                                    panel.style.cursor = 'grabbing';
                                    if (header) header.style.cursor = 'grabbing';
                                    if (stickyTop) stickyTop.style.cursor = 'grabbing';
                                }
                            }
                            
                            function dragEnd(e) {
                                initialX = currentX;
                                initialY = currentY;
                                isDragging = false;
                                panel.style.cursor = 'default';
                                if (header) header.style.cursor = 'move';
                                if (stickyTop) stickyTop.style.cursor = 'move';
                                // Save position when drag ends
                                savePanelSettings(panel);
                            }
                            
                            function drag(e) {
                                if (isDragging) {
                                    e.preventDefault();
                                    
                                    if (e.type === "touchmove") {
                                        currentX = e.touches[0].clientX - initialX;
                                        currentY = e.touches[0].clientY - initialY;
                                    } else {
                                        currentX = e.clientX - initialX;
                                        currentY = e.clientY - initialY;
                                    }
                                    
                                    xOffset = currentX;
                                    yOffset = currentY;
                                    
                                    // Constrain to viewport
                                    var rect = panel.getBoundingClientRect();
                                    var maxX = window.innerWidth - rect.width;
                                    var maxY = window.innerHeight - rect.height;
                                    
                                    currentX = Math.max(0, Math.min(currentX, maxX));
                                    currentY = Math.max(0, Math.min(currentY, maxY));
                                    
                                    // Clear any existing positioning
                                    panel.style.right = '';
                                    panel.style.left = currentX + 'px';
                                    panel.style.top = currentY + 'px';
                                }
                            }
                            
                            // Add event listeners
                            if (header) {
                                header.addEventListener('mousedown', dragStart);
                                header.addEventListener('touchstart', dragStart);
                            }
                            if (stickyTop) {
                                stickyTop.addEventListener('mousedown', dragStart);
                                stickyTop.addEventListener('touchstart', dragStart);
                            }
                            document.addEventListener('mousemove', drag);
                            document.addEventListener('mouseup', dragEnd);
                            document.addEventListener('touchmove', drag);
                            document.addEventListener('touchend', dragEnd);
                            
                            // Add resize observer to save size changes
                            if (window.ResizeObserver) {
                                const resizeObserver = new ResizeObserver(function(entries) {
                                    // Save settings when panel is resized
                                    savePanelSettings(panel);
                                });
                                resizeObserver.observe(panel);
                            }
                            
                            // Get initial position
                            var rect = panel.getBoundingClientRect();
                            xOffset = rect.left;
                            yOffset = rect.top;
                            } // End of startImageAudit function
                            
                            // Start the process: trigger lazy loading first, then audit
                            triggerLazyLoadingAndStartAudit();
                        })();
                    }
                }, (result) => {
                    if (chrome.runtime.lastError) {
                        console.error('Error executing Images script:', chrome.runtime.lastError);
                    } else {
                        console.log('Images script executed successfully');
                    }
                });
            });
        } catch (error) {
            console.error('Images error:', error);
        }
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                // Function to reset Images effects
                function resetImages() {
                    // Remove any existing images audit panel
                    const existingPanel = document.querySelector('.images-audit-panel');
                    if (existingPanel) {
                        existingPanel.remove();
                        console.log('Images audit panel removed');
                    }
                    
                    // Remove any highlights and restore original styles
                    document.querySelectorAll('.seo-highlight').forEach(function(el) {
                        el.classList.remove('seo-highlight');
                        // Restore original styles if they were stored
                        if (el.hasAttribute('data-original-border')) {
                            el.style.border = el.getAttribute('data-original-border');
                            el.removeAttribute('data-original-border');
                        } else {
                            el.style.border = '';
                        }
                        if (el.hasAttribute('data-original-box-shadow')) {
                            el.style.boxShadow = el.getAttribute('data-original-box-shadow');
                            el.removeAttribute('data-original-box-shadow');
                        } else {
                            el.style.boxShadow = '';
                        }
                    });
                    
                    // Clean up all elements that were modified by the Images action
                    document.querySelectorAll('[data-original-id]').forEach(function(el) {
                        var originalId = el.getAttribute('data-original-id');
                        if (originalId === 'none') {
                            el.removeAttribute('id');
                        } else {
                            el.setAttribute('id', originalId);
                        }
                        el.removeAttribute('data-original-id');
                    });
                    
                    // Background images no longer modify DOM, so no cleanup needed
                    
                    // Remove any styles that were added for highlighting or effects
                    var styleElements = document.querySelectorAll('style');
                    styleElements.forEach(function(style) {
                        if (style.textContent && (style.textContent.includes('.seo-highlight') || 
                            style.textContent.includes('.resize-handle') || 
                            style.textContent.includes('pulse'))) {
                            style.remove();
                        }
                    });
                    
                    // Remove any global event listeners that might have been added
                    if (window.imagesActionEscapeListener) {
                        document.removeEventListener('keydown', window.imagesActionEscapeListener);
                        delete window.imagesActionEscapeListener;
                    }
                    
                    console.log('Images reset completed - all DOM modifications restored');
                }

                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetImages
                    }, () => {
                        resolve();
                    });
                });
            } catch (error) {
                console.error('Images reset error:', error);
                resolve(); // Resolve anyway to not block other resets
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImagesAction;
} else {
    window.ImagesAction = ImagesAction;
}

// Add style for .resize-handle
var resizeStyle = document.createElement('style');
resizeStyle.textContent = `.resize-handle { position: absolute; right: 0; top: 0; width: 8px; height: 100%; cursor: col-resize; z-index: 10; background: rgba(255,255,255,0.15); border-right: 2px solid #e5e5e5; transition: background 0.2s; }\n.images-audit-panel #elements-table > thead > tr > th { position: relative; }\n.images-audit-panel #elements-table > thead > tr > th { background-color: #3a3a3a !important; color: #9ca3af !important; }\n#elements-table > thead > tr > th { background-color: #3a3a3a !important; color: #9ca3af !important; }\n#elements-table > th { background-color: #3a3a3a !important; color: #9ca3af !important; }\n.resize-handle:hover { background: #fff; border-right: 2px solid #7c3aed; }`;
document.head.appendChild(resizeStyle);

// Add column resize logic after the table is rendered
setTimeout(function() {
    var table = document.getElementById('elements-table');
    if (!table) { console.log('[Resize Debug] Table not found'); return; }
    var ths = table.querySelectorAll('th');
    console.log('[Resize Debug] Found', ths.length, 'th elements');
    var startX, startWidth, colIndex, resizingTh;
    function onMouseMove(e) {
        if (!resizingTh) return;
        var dx = e.clientX - startX;
        var newWidth = Math.max(40, startWidth + dx);
        resizingTh.style.width = newWidth + 'px';
        // Set width for all cells in this column
        var idx = colIndex;
        table.querySelectorAll('tr').forEach(function(row) {
            var cell = row.children[idx];
            if (cell) cell.style.width = newWidth + 'px';
        });
        console.log('[Resize Debug] MouseMove: col', idx, 'newWidth', newWidth);
    }
    function onMouseUp() {
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
        console.log('[Resize Debug] MouseUp');
        resizingTh = null;
    }
    ths.forEach(function(th, i) {
        var handle = th.querySelector('.resize-handle');
        if (!handle) { console.log('[Resize Debug] No handle in th', i); return; }
        handle.addEventListener('mousedown', function(e) {
            e.preventDefault();
            startX = e.clientX;
            startWidth = th.offsetWidth;
            colIndex = i;
            resizingTh = th;
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
            console.log('[Resize Debug] MouseDown on handle for col', i);
        });
    });
}, 0); 