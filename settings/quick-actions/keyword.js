// Keyword Density Quick Action - Calculates keyword density for selected text on the page

class KeywordAction {
    constructor() {
        this.name = 'Keyword Density';
        this.description = 'Calculates keyword density for selected text on the page';
    }

    // Execute the keyword density calculation
    execute() {
        return new Promise((resolve, reject) => {
            chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                const currentTab = tabs[0];
                
                try {
                    chrome.scripting.executeScript({
                        target: {tabId: currentTab.id},
                        func: function() {
                            // Get selected text function
                            const getSelectedText = () => {
                                let text = '';
                                if (window.getSelection) {
                                    text = window.getSelection().toString();
                                } else if (document.selection && document.selection.type !== 'Control') {
                                    text = document.selection.createRange().text;
                                }
                                return text;
                            };

                            // Count keyword density function
                            const countKeywordDensity = (html, keyword) => {
                                const keywordPattern = new RegExp('\\b' + keyword.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&') + '\\b', 'gi');
                                const keywordMatches = html.match(keywordPattern) || [];
                                const keywordCount = keywordMatches.length;
                                const totalWords = html.replace(/[\W_]+/g, ' ').split(' ').length;
                                return (keywordCount / totalWords) * 100;
                            };

                            // Main function
                            const keyword = getSelectedText();
                            if (keyword.length === 0) {
                                alert('Please select a keyword to calculate its density on this page.');
                                return false;
                            }

                            const html = document.body.innerText;
                            const density = countKeywordDensity(html, keyword);
                            alert(`The keyword density of "${keyword}" is ${density.toFixed(2)}%.`);
                            
                            return true;
                        }
                    }, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('Error executing Keyword Density script:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            console.log('Keyword Density script executed successfully');
                            resolve(result);
                        }
                    });
                } catch (error) {
                    console.error('Error in Keyword Density execute:', error);
                    reject(error);
                }
            });
        });
    }

    // Reset function (not needed for density calculation, but keeping for consistency)
    reset() {
        return new Promise((resolve) => {
            console.log('Keyword Density tool does not require reset');
            resolve(true);
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = KeywordAction;
} else {
    window.KeywordAction = KeywordAction;
} 