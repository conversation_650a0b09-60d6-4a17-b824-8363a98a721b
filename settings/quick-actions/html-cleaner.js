// HTML Cleaner Action - Configurable HTML cleaning options panel
class HTMLCleanerAction {
    constructor() {
        this.text = '';
        this.originalText = '';
    }

    // Main cleaning function that applies selected options
    cleanHTML(htmlContent, options = {}) {
        this.originalText = htmlContent;
        this.text = htmlContent;

        // Apply cleaning options based on what's selected
        
        // Remove data attributes first
        if (options.removeDataAttributes) {
            this.removeDataAttributes();
        }

        // Remove wrapper divs early in the process
        if (options.removeWrapperDivs) {
            this.removeWrapperDivs();
        }

        if (options.removeStyles) {
            this.removeStyles();
        }

        if (options.removeClasses) {
            this.removeClasses();
        }

        if (options.removeIds) {
            this.removeIds();
        }

        if (options.removeComments) {
            this.removeComments();
        }

        if (options.removeCitationNumbers) {
            this.removeCitationNumbers();
        }

        if (options.removeAllAttributes) {
            this.removeAllAttributes();
        }

        if (options.removeEmptyTags) {
            this.removeEmptyTags();
        }

        if (options.removeExtraSpaces) {
            this.removeExtraSpaces();
        }

        // Always clean up extra whitespace and normalize
        this.normalizeWhitespace();

        return this.text.trim();
    }

    // Remove inline styles
    removeStyles() {
        this.helyettesit(' style="', '"STYLE_MARKER"');
        this.torolTagbanKettoKozt('"STYLE_MARKER"', '"');
        this.helyettesit('"STYLE_MARKER"', '');
        
        // Also remove style attributes without quotes
        this.helyettesit(' style=', '=STYLE_MARKER');
        this.torolTagbanKettoKozt('=STYLE_MARKER', ' ');
        this.torolTagbanKettoKozt('=STYLE_MARKER', '>');
        this.helyettesit('=STYLE_MARKER', '');
    }

    // Remove class attributes
    removeClasses() {
        this.helyettesit(' class="', '"CLASS_MARKER"');
        this.torolTagbanKettoKozt('"CLASS_MARKER"', '"');
        this.helyettesit('"CLASS_MARKER"', '');
        
        // Also remove class attributes without quotes
        this.helyettesit(' class=', '=CLASS_MARKER');
        this.torolTagbanKettoKozt('=CLASS_MARKER', ' ');
        this.torolTagbanKettoKozt('=CLASS_MARKER', '>');
        this.helyettesit('=CLASS_MARKER', '');
    }

    // Remove ID attributes
    removeIds() {
        this.helyettesit(' id="', '"ID_MARKER"');
        this.torolTagbanKettoKozt('"ID_MARKER"', '"');
        this.helyettesit('"ID_MARKER"', '');
        
        // Also remove id attributes without quotes
        this.helyettesit(' id=', '=ID_MARKER');
        this.torolTagbanKettoKozt('=ID_MARKER', ' ');
        this.torolTagbanKettoKozt('=ID_MARKER', '>');
        this.helyettesit('=ID_MARKER', '');
    }

    // Remove HTML comments
    removeComments() {
        this.torolTagbanKettoKozt('<!--', '-->');
    }

    // Remove data attributes (data-id, data-element_type, etc.)
    removeDataAttributes() {
        // Pattern for data attributes with quoted values
        let dataAttrPattern = / data-[^=]*="[^"]*"/g;
        this.text = this.text.replace(dataAttrPattern, '');
        
        // Pattern for data attributes with unquoted values
        dataAttrPattern = / data-[^=]*=[^\s>]*/g;
        this.text = this.text.replace(dataAttrPattern, '');
        
        // Use marker-based cleanup for complex cases
        this.helyettesit(' data-', ' DATA_MARKER');
        this.torolTagbanKettoKozt(' DATA_MARKER', '"');
        this.torolTagbanKettoKozt(' DATA_MARKER', ' ');
        this.torolTagbanKettoKozt(' DATA_MARKER', '>');
        this.helyettesit(' DATA_MARKER', '');
    }

    // Remove citation numbers and citation links
    removeCitationNumbers() {
        // Remove citation elements (links that contain only numbers)
        // Pattern: <a ...>1</a>, <a ...>23</a>, etc.
        let citationPattern = /<a[^>]*>\s*\d+\s*<\/a>/g;
        this.text = this.text.replace(citationPattern, '');
        
        // Remove span citations with complex structures like the examples
        // Pattern: <span class="citation"...>complex content with numbers</span>
        citationPattern = /<span[^>]*class="[^"]*citation[^"]*"[^>]*>.*?<\/span>/g;
        this.text = this.text.replace(citationPattern, '');
        
        // Remove other citation-like elements
        citationPattern = /<[^>]*class="[^"]*citation[^"]*"[^>]*>.*?<\/[^>]*>/g;
        this.text = this.text.replace(citationPattern, '');
        
        // Remove trailing numbers from text content (but preserve punctuation)
        // This handles cases like "text123." or "text456," or "text789)"
        // Pattern: one or more digits followed by optional punctuation at the end of sentences
        let trailingNumberPattern = /(\w)\d+([.!?;,)]?\s*$)/gm;
        this.text = this.text.replace(trailingNumberPattern, '$1$2');
        
        // Handle cases where numbers appear before closing tags
        // Pattern: numbers right before closing paragraph/div tags
        trailingNumberPattern = /\d+(\s*<\/(p|div|span|h[1-6])>)/g;
        this.text = this.text.replace(trailingNumberPattern, '$1');
        
        // Clean up trailing numbers that appear at the end of lines within content
        // This catches patterns like "content123\n" or "content456 "
        trailingNumberPattern = /(\w)\d+(\s*[\n\r])/g;
        this.text = this.text.replace(trailingNumberPattern, '$1$2');
        
        // Final cleanup: remove isolated numbers at the end of text blocks
        // This handles remaining standalone numbers
        trailingNumberPattern = /\s+\d+\s*([.!?;,)]?\s*)$/gm;
        this.text = this.text.replace(trailingNumberPattern, '$1');
        
        console.log('Citation numbers removal completed');
    }

    // Remove wrapper divs that only contain meaningful content
    removeWrapperDivs() {
        let cleaned = false;
        let iterations = 0;
        let beforeText = '';
        const maxIterations = 10;
        
        console.log('🔧 Starting wrapper div removal...');
        
        do {
            beforeText = this.text;
            iterations++;
            
            // Remove wrapper divs pattern: <div><div><div>CONTENT</div></div></div> -> CONTENT
            // This regex matches any number of opening divs followed by content and matching closing divs
            const wrapperPattern = /^(\s*<div[^>]*>\s*)+(.+?)(\s*<\/div>\s*)+$/s;
            
            if (wrapperPattern.test(this.text)) {
                const match = this.text.match(wrapperPattern);
                if (match && match[2]) {
                    const innerContent = match[2].trim();
                    
                    // Only unwrap if inner content starts with meaningful tags
                    if (innerContent.match(/^<(?:ul|ol|li|p|h[1-6]|table|blockquote|article|section|nav|header|footer|main|aside|span|strong|em|a)\b/)) {
                        this.text = innerContent;
                        console.log(`  Iteration ${iterations}: Unwrapped to "${this.text.substring(0, 50)}..."`);
                        cleaned = true;
                    } else {
                        cleaned = false;
                    }
                } else {
                    cleaned = false;
                }
            } else {
                cleaned = false;
            }
            
        } while (cleaned && iterations < maxIterations && this.text !== beforeText);
        
        console.log(`🔧 Wrapper div removal completed after ${iterations} iterations`);
    }

    // Remove empty tags and whitespace-only tags
    removeEmptyTags() {
        // Remove tags with only whitespace or &nbsp;
        this.csakEgyNbspTagotTorul();
        this.csakEnteresTagotTorul();
        
        // Remove completely empty tags
        const emptyTagPatterns = [
            /<([^>]+)>\s*<\/\1>/g,
            /<([^>]+)>&nbsp;<\/\1>/g,
            /<([^>]+)>&nbsp;\s*<\/\1>/g,
            /<([^>]+)>\s*&nbsp;<\/\1>/g
        ];

        emptyTagPatterns.forEach(pattern => {
            let matches;
            do {
                matches = this.text.match(pattern);
                if (matches) {
                    this.text = this.text.replace(pattern, '');
                }
            } while (matches);
        });

        // Specific empty div/span cleanup
        this.helyettesit('<div></div>', '');
        this.helyettesit('<span></span>', '');
        this.helyettesit('<p></p>', '');
        this.helyettesit('<div> </div>', '');
        this.helyettesit('<span> </span>', '');
        this.helyettesit('<p> </p>', '');
    }

    // Remove extra spaces and normalize whitespace
    removeExtraSpaces() {
        // Remove multiple consecutive spaces
        this.helyettesit('  ', ' ');
        this.helyettesit('   ', ' ');
        this.helyettesit('    ', ' ');
        
        // Clean up spaces around tags
        this.helyettesit('> <', '><');
        this.helyettesit(' >', '>');
        this.helyettesit('< ', '<');
    }

    // Normalize whitespace (always applied)
    normalizeWhitespace() {
        // Remove line breaks and tabs that can cause formatting issues
        this.helyettesit('\n', ' ');
        this.helyettesit('\r', ' ');
        this.helyettesit('\t', ' ');
        
        // Clean up multiple spaces created by line break removal
        while (this.text.includes('  ')) {
            this.helyettesit('  ', ' ');
        }
    }

    // Helper function: Find and replace with loop until no more matches
    helyettesit(search, replace) {
        while (this.text.indexOf(search) !== -1) {
            this.text = this.text.replace(search, replace);
        }
    }

    // Helper function: Remove content between start and end markers
    torolTagbanKettoKozt(startMarker, endMarker) {
        const startLen = startMarker.length;
        const endLen = endMarker.length;
        let result = '';
        let i = 0;
        let inside = false;

        while (i < this.text.length) {
            // Check for start marker
            if (!inside && this.text.substr(i, startLen) === startMarker) {
                inside = true;
                i += startLen;
                continue;
            }

            // Check for end marker
            if (inside && this.text.substr(i, endLen) === endMarker) {
                inside = false;
                i += endLen;
                continue;
            }

            // Add character if not inside marker pair
            if (!inside) {
                result += this.text[i];
            }

            i++;
        }

        this.text = result;
    }

    // Clean up single &nbsp; occurrences
    csakEgyNbspTagotTorul() {
        this.helyettesit('&nbsp;&nbsp;', '&nbsp;');
        this.helyettesit(' &nbsp;', ' ');
        this.helyettesit('&nbsp; ', ' ');
        this.helyettesit('<p>&nbsp;</p>', '');
        this.helyettesit('<div>&nbsp;</div>', '');
        this.helyettesit('<span>&nbsp;</span>', '');
    }

    // Remove line breaks and tabs from content
    csakEnteresTagotTorul() {
        this.helyettesit('\n', '');
        this.helyettesit('\t', '');
        this.helyettesit('\r', '');
    }

    // Remove ALL attributes from ALL tags (except preserve title attributes on heading tags)
    removeAllAttributes() {
        // Special handling for heading tags - preserve title attributes
        // Pattern explanation:
        // <(h[1-6]) - matches opening bracket and heading tag name (captured in group 1)
        // ([^>]*) - matches all attributes (captured in group 2)
        // > - matches closing bracket
        this.text = this.text.replace(/<(h[1-6])([^>]*)>/gi, (match, tagName, attributes) => {
            // Extract title attribute if it exists
            const titleMatch = attributes.match(/\s+title\s*=\s*["']([^"']*)["']/i);
            if (titleMatch) {
                // Keep only the title attribute for heading tags
                return `<${tagName} title="${titleMatch[1]}">`;
            } else {
                // No title attribute, remove all attributes
                return `<${tagName}>`;
            }
        });
        
        // For all other tags, remove all attributes
        // Pattern explanation:
        // <(\w+) - matches opening bracket and tag name (captured in group 1) 
        // [^>]* - matches any attributes (everything until closing bracket)
        // > - matches closing bracket
        // Replace with <$1> to keep only the tag name
        // Negative lookbehind to exclude heading tags already processed
        this.text = this.text.replace(/<(\w+)(?<!h[1-6])([^>]*)>/gi, (match, tagName, attributes) => {
            // Skip heading tags as they were already processed above
            if (tagName.toLowerCase().match(/^h[1-6]$/)) {
                return match;
            }
            return `<${tagName}>`;
        });
        
        console.log('All tag attributes removal completed (preserved title attributes on headings)');
    }

    // Get available cleaning options
    static getCleaningOptions() {
        return {
            removeStyles: {
                id: 'removeStyles',
                label: 'Remove Inline Styles',
                description: 'Remove all style attributes from HTML elements'
            },
            removeClasses: {
                id: 'removeClasses', 
                label: 'Remove CSS Classes',
                description: 'Remove all class attributes from HTML elements'
            },
            removeIds: {
                id: 'removeIds',
                label: 'Remove Element IDs', 
                description: 'Remove all id attributes from HTML elements'
            },
            removeComments: {
                id: 'removeComments',
                label: 'Remove HTML Comments',
                description: 'Remove all <!-- --> comment blocks'
            },
            removeEmptyTags: {
                id: 'removeEmptyTags',
                label: 'Remove Empty Tags',
                description: 'Remove tags that contain no content or only whitespace'
            },
            removeExtraSpaces: {
                id: 'removeExtraSpaces',
                label: 'Remove Extra Spaces',
                description: 'Remove multiple consecutive spaces and normalize whitespace'
            },
            removeDataAttributes: {
                id: 'removeDataAttributes',
                label: 'Remove Data Attributes',
                description: 'Remove all data-* attributes (data-id, data-element_type, etc.)'
            },
            removeWrapperDivs: {
                id: 'removeWrapperDivs',
                label: 'Remove Wrapper Divs',
                description: 'Remove outer wrapper divs that only contain meaningful content'
            },
            removeCitationNumbers: {
                id: 'removeCitationNumbers',
                label: 'Remove Citation Numbers',
                description: 'Remove citation numbers and citation links from HTML content'
            },
            removeAllAttributes: {
                id: 'removeAllAttributes',
                label: 'Remove All Tag Attributes',
                description: 'Remove ALL attributes from ALL HTML tags (class, id, style, data-*, etc.)'
            }
        };
    }

    // Execute the HTML cleaner settings panel
    static execute() {
        return new Promise((resolve, reject) => {
            try {
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: function() {
                            // Create settings panel directly in page context
                            return HTMLCleanerAction.createSettingsPanel();
                        }
                    }, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('HTML Cleaner execution error:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            resolve(result);
                        }
                    });
                });
            } catch (error) {
                console.error('HTML Cleaner error:', error);
                reject(error);
            }
        });
    }

    // Reset function for Quick Actions compliance
    static reset() {
        return new Promise((resolve) => {
            try {
                function resetHTMLCleaner() {
                    // Remove any existing HTML cleaner panels
                    const existingPanel = document.querySelector('.html-cleaner-settings-panel');
                    if (existingPanel) {
                        existingPanel.remove();
                    }
                    
                    // Clear any global variables
                    if (window.htmlCleanerEscapeListener) {
                        document.removeEventListener('keydown', window.htmlCleanerEscapeListener);
                        delete window.htmlCleanerEscapeListener;
                    }
                    
                    console.log('HTML Cleaner reset completed');
                }

                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetHTMLCleaner
                    }, () => {
                        resolve();
                    });
                });
            } catch (error) {
                console.error('HTML Cleaner reset error:', error);
                resolve();
            }
        });
    }

    // Create settings panel UI for cleaning options (runs in page context)
    static createSettingsPanel() {
        return new Promise((resolve) => {
            // Check if panel already exists
            const existingPanel = document.querySelector('.html-cleaner-settings-panel');
            if (existingPanel) {
                existingPanel.remove();
            }

            // Get current settings
            chrome.storage.local.get(['htmlCleanerSettings'], (result) => {
                const currentSettings = result.htmlCleanerSettings || {
                    removeStyles: true,
                    removeClasses: true, 
                    removeIds: true,
                    removeComments: true,
                    removeEmptyTags: true,
                    removeExtraSpaces: true,
                    removeDataAttributes: true,
                    removeWrapperDivs: true,
                    removeCitationNumbers: true,
                    removeAllAttributes: true
                };

                // Create panel HTML
                const panel = document.createElement('div');
                panel.className = 'html-cleaner-settings-panel';
                panel.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: #0a0a0a;
                    border: 2px solid #7C3AED;
                    border-radius: 12px;
                    padding: 24px;
                    z-index: 9999999;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.6);
                    max-width: 480px;
                    width: 90%;
                `;

                const options = HTMLCleanerAction.getCleaningOptions();
                let checkboxHTML = '';

                Object.values(options).forEach(option => {
                    const checked = currentSettings[option.id] ? 'checked' : '';
                    checkboxHTML += `
                        <div style="margin-bottom: 16px;">
                            <label style="display: flex; align-items: flex-start; color: #d1d5db; cursor: pointer; font-size: 14px;">
                                <input type="checkbox" id="${option.id}" ${checked} style="margin-right: 12px; margin-top: 2px;">
                                <div>
                                    <div style="font-weight: 600; margin-bottom: 4px;">${option.label}</div>
                                    <div style="font-size: 12px; color: #9ca3af; line-height: 1.4;">${option.description}</div>
                                </div>
                            </label>
                        </div>
                    `;
                });

                panel.innerHTML = `
                    <div style="color: #7C3AED; font-size: 18px; font-weight: 700; margin-bottom: 20px; text-align: center;">
                        🧹 HTML Cleaning Options
                    </div>
                    <div style="margin-bottom: 24px;">
                        ${checkboxHTML}
                    </div>
                    <div style="display: flex; gap: 12px; justify-content: flex-end;">
                        <button id="html-cleaner-cancel" style="
                            background: transparent;
                            border: 1px solid #6b7280;
                            color: #d1d5db;
                            padding: 8px 16px;
                            border-radius: 6px;
                            font-size: 14px;
                            cursor: pointer;
                        ">Cancel</button>
                        <button id="html-cleaner-save" style="
                            background: #7C3AED;
                            border: 1px solid #7C3AED;
                            color: white;
                            padding: 8px 16px;
                            border-radius: 6px;
                            font-size: 14px;
                            cursor: pointer;
                            font-weight: 600;
                        ">Save Settings</button>
                    </div>
                `;

                document.body.appendChild(panel);

                // Handle save button
                document.getElementById('html-cleaner-save').addEventListener('click', () => {
                    const newSettings = {};
                    Object.keys(options).forEach(optionId => {
                        const checkbox = document.getElementById(optionId);
                        newSettings[optionId] = checkbox.checked;
                    });

                    chrome.storage.local.set({ htmlCleanerSettings: newSettings }, () => {
                        panel.remove();
                        resolve(newSettings);
                    });
                });

                // Handle cancel button
                document.getElementById('html-cleaner-cancel').addEventListener('click', () => {
                    panel.remove();
                    resolve(null);
                });

                // Handle escape key
                const handleEscape = (e) => {
                    if (e.key === 'Escape') {
                        panel.remove();
                        document.removeEventListener('keydown', handleEscape);
                        delete window.htmlCleanerEscapeListener;
                        resolve(null);
                    }
                };
                window.htmlCleanerEscapeListener = handleEscape;
                document.addEventListener('keydown', handleEscape);
            });
        });
    }

    // Get current settings from storage
    static getCurrentSettings() {
        return new Promise((resolve) => {
            chrome.storage.local.get(['htmlCleanerSettings'], (result) => {
                const defaultSettings = {
                    removeStyles: true,
                    removeClasses: true,
                    removeIds: true, 
                    removeComments: true,
                    removeEmptyTags: true,
                    removeExtraSpaces: true,
                    removeDataAttributes: true,
                    removeWrapperDivs: true,
                    removeCitationNumbers: true,
                    removeAllAttributes: true
                };
                
                resolve(result.htmlCleanerSettings || defaultSettings);
            });
        });
    }
}

// Make available globally
if (typeof window !== 'undefined') {
    window.HTMLCleanerAction = HTMLCleanerAction;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HTMLCleanerAction;
} 