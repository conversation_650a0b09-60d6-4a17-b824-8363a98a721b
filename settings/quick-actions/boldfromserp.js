// Bold From SERP Action - Extract unique n-grams from em tags
class BoldFromSerpAction {
    static execute() {
        try {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                    target: {tabId: tabs[0].id},
                    func: function() {
                        // Bold From SERP extraction functionality
                        (function() {
                            function extractBoldFromSerp() {
                                const emTags = document.querySelectorAll('em');
                                
                                if (emTags.length === 0) {
                                    // Create a styled notification instead of alert
                                    showNotification('No <em> tags found on the page.', 'error');
                                    return;
                                }
                                
                                const uniqueBoldedWords = new Set();
                                
                                emTags.forEach(emTag => {
                                    const words = emTag.textContent.toLowerCase().split(/\s+/);
                                    for (let i = 0; i < words.length; i++) {
                                        for (let n = 1; n <= words.length - i; n++) {
                                            const ngram = words.slice(i, i + n).join(' ');
                                            if (ngram.trim()) { // Only add non-empty n-grams
                                                uniqueBoldedWords.add(ngram);
                                            }
                                        }
                                    }
                                });
                                
                                const boldedText = Array.from(uniqueBoldedWords).sort().join('\n');
                                
                                // Copy to clipboard using modern API
                                if (navigator.clipboard && window.isSecureContext) {
                                    navigator.clipboard.writeText(boldedText).then(function() {
                                        showResults(boldedText, emTags.length);
                                    }).catch(function(err) {
                                        console.error('Failed to copy: ', err);
                                        fallbackCopy(boldedText, emTags.length);
                                    });
                                } else {
                                    fallbackCopy(boldedText, emTags.length);
                                }
                            }
                            
                            function fallbackCopy(boldedText, emTagsCount) {
                                // Fallback for older browsers
                                const tempTextArea = document.createElement('textarea');
                                tempTextArea.value = boldedText;
                                tempTextArea.style.position = 'fixed';
                                tempTextArea.style.left = '-999999px';
                                tempTextArea.style.top = '-999999px';
                                document.body.appendChild(tempTextArea);
                                tempTextArea.focus();
                                tempTextArea.select();
                                try {
                                    document.execCommand('copy');
                                    showResults(boldedText, emTagsCount);
                                } catch (err) {
                                    console.error('Fallback: Failed to copy', err);
                                    showNotification('Failed to copy to clipboard', 'error');
                                }
                                document.body.removeChild(tempTextArea);
                            }
                            
                            function showResults(boldedText, emTagsCount) {
                                // Parse n-grams into structured data
                                const allNgrams = boldedText.split('\n').map(ngram => ({
                                    text: ngram.trim(),
                                    wordCount: ngram.trim().split(/\s+/).length
                                })).filter(item => item.text); // Remove empty items
                                
                                // Create results panel
                                var panel = document.createElement('div');
                                panel.className = 'bold-from-serp-panel';
                                panel.style.cssText = `
                                    position:fixed;
                                    top:50px;
                                    left:50%;
                                    transform:translateX(-50%);
                                    width:450px;
                                    min-width:350px;
                                    max-width:90vw;
                                    height:600px;
                                    min-height:300px;
                                    max-height:80vh;
                                    z-index:9999999;
                                    background:#0a0a0a;
                                    color:#d1d5db;
                                    border:1px solid #1f1f1f;
                                    border-radius:10px;
                                    box-shadow:0 8px 32px rgba(0,0,0,0.6);
                                    overflow:hidden;
                                    font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;
                                    font-size:14px;
                                    line-height:1.5;
                                    display:flex;
                                    flex-direction:column;
                                `;
                                
                                var html = `
                                    <div id="bold-from-serp-header" style="display:flex;justify-content:space-between;align-items:center;padding:20px;border-bottom:1px solid #2a2a2a;cursor:move;user-select:none;">
                                        <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;">Bold From SERP Results</h2>
                                        <button id="close-panel-btn" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;">✕</button>
                                    </div>
                                    <div style="padding:20px;flex:1;overflow:auto;">
                                        <div style="background:#1a1a1a;border-radius:8px;border:1px solid #2a2a2a;padding:16px;margin-bottom:16px;">
                                            <div style="color:#22c55e;font-weight:600;margin-bottom:8px;">✓ Success!</div>
                                            <div style="color:#9ca3af;font-size:13px;">
                                                Found <strong>${emTagsCount}</strong> &lt;em&gt; tags and extracted <strong>${allNgrams.length}</strong> unique n-grams.
                                                <br>Results copied to clipboard!
                                            </div>
                                        </div>
                                        <div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;overflow:hidden;">
                                            <div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;">
                                                <div style="font-weight:500;color:#9ca3af;font-size:14px;margin-bottom:12px;">
                                                    <span id="ngram-count">Extracted N-grams (${allNgrams.length} total)</span>
                                                </div>
                                                <div style="display:flex;flex-wrap:wrap;gap:12px;align-items:center;">
                                                    <div style="display:flex;align-items:center;gap:6px;">
                                                        <label style="color:#9ca3af;font-size:12px;white-space:nowrap;">Word Length:</label>
                                                        <input type="text" id="word-length-filter" placeholder="e.g. 3 or 3-5" 
                                                               style="background:#0a0a0a;border:1px solid #374151;border-radius:4px;padding:4px 8px;color:#d1d5db;font-size:12px;width:70px;" 
                                                               title="Enter single number (e.g. 3) or range (e.g. 3-5)">
                                                    </div>
                                                    <div style="display:flex;align-items:center;gap:6px;">
                                                        <label style="color:#9ca3af;font-size:12px;white-space:nowrap;">Keywords:</label>
                                                        <input type="text" id="keyword-filter" placeholder="e.g. coffee, shop" 
                                                               style="background:#0a0a0a;border:1px solid #374151;border-radius:4px;padding:4px 8px;color:#d1d5db;font-size:12px;width:120px;" 
                                                               title="Enter keywords separated by commas">
                                                    </div>
                                                    <button id="clear-filters-btn" style="padding:4px 8px;background:#4b5563;color:#d1d5db;border:1px solid #6b7280;border-radius:4px;cursor:pointer;font-size:11px;">Clear</button>
                                                </div>
                                            </div>
                                            <div style="padding:16px;max-height:300px;overflow:auto;">
                                                <div id="ngrams-display" style="background:#0a0a0a;border:1px solid #2a2a2a;border-radius:6px;padding:12px;font-family:monospace;font-size:12px;line-height:1.4;white-space:pre-wrap;color:#d1d5db;">${boldedText}</div>
                                            </div>
                                        </div>
                                        <div style="margin-top:16px;text-align:center;">
                                            <button id="copy-filtered-btn" style="padding:8px 16px;background:#7C3AED;color:white;border:1px solid #8B5CF6;border-radius:6px;cursor:pointer;font-size:13px;font-weight:500;">Copy Filtered</button>
                                        </div>
                                    </div>
                                    <div id="resize-handle" style="position:absolute;bottom:0;right:0;width:20px;height:20px;cursor:nw-resize;background:linear-gradient(-45deg, transparent 0%, transparent 30%, #4b5563 30%, #4b5563 40%, transparent 40%, transparent 60%, #4b5563 60%, #4b5563 70%, transparent 70%);"></div>
                                `;
                                
                                panel.innerHTML = html;
                                document.body.appendChild(panel);
                                
                                // Add filtering functionality
                                function parseFilter(filterValue) {
                                    if (!filterValue.trim()) return null;
                                    
                                    const trimmed = filterValue.trim();
                                    if (trimmed.includes('-')) {
                                        const [min, max] = trimmed.split('-').map(n => parseInt(n.trim()));
                                        if (isNaN(min) || isNaN(max) || min < 1 || max < 1 || min > max) return null;
                                        return { min: Math.max(1, min), max: Math.min(10, max) };
                                    } else {
                                        const num = parseInt(trimmed);
                                        if (isNaN(num) || num < 1) return null;
                                        return { min: num, max: num };
                                    }
                                }
                                
                                function parseKeywords(keywordValue) {
                                    if (!keywordValue.trim()) return [];
                                    return keywordValue.split(',')
                                        .map(keyword => keyword.trim().toLowerCase())
                                        .filter(keyword => keyword.length > 0);
                                }
                                
                                function filterNgrams(lengthFilter, keywords) {
                                    let filtered = allNgrams;
                                    
                                    // Apply length filter
                                    if (lengthFilter) {
                                        filtered = filtered.filter(ngram => 
                                            ngram.wordCount >= lengthFilter.min && ngram.wordCount <= lengthFilter.max
                                        );
                                    }
                                    
                                    // Apply keyword filter (AND logic)
                                    if (keywords.length > 0) {
                                        filtered = filtered.filter(ngram => {
                                            const ngramLower = ngram.text.toLowerCase();
                                            return keywords.every(keyword => ngramLower.includes(keyword));
                                        });
                                    }
                                    
                                    return filtered;
                                }
                                
                                function updateDisplay() {
                                    const lengthInput = panel.querySelector('#word-length-filter');
                                    const keywordInput = panel.querySelector('#keyword-filter');
                                    const ngramsDisplay = panel.querySelector('#ngrams-display');
                                    const ngramCount = panel.querySelector('#ngram-count');
                                    
                                    const lengthFilter = parseFilter(lengthInput.value);
                                    const keywords = parseKeywords(keywordInput.value);
                                    const filteredNgrams = filterNgrams(lengthFilter, keywords);
                                    const filteredText = filteredNgrams.map(item => item.text).join('\n');
                                    
                                    ngramsDisplay.textContent = filteredText || 'No results match the filter criteria.';
                                    ngramCount.textContent = `Extracted N-grams (${filteredNgrams.length} of ${allNgrams.length} shown)`;
                                    
                                    // Update input border colors based on validity
                                    if (lengthInput.value.trim() && !lengthFilter) {
                                        lengthInput.style.borderColor = '#ef4444';
                                    } else {
                                        lengthInput.style.borderColor = '#374151';
                                    }
                                    
                                    // Keyword input is always valid (no special validation needed)
                                    keywordInput.style.borderColor = '#374151';
                                }
                                
                                function copyFilteredResults() {
                                    const lengthInput = panel.querySelector('#word-length-filter');
                                    const keywordInput = panel.querySelector('#keyword-filter');
                                    const lengthFilter = parseFilter(lengthInput.value);
                                    const keywords = parseKeywords(keywordInput.value);
                                    const filteredNgrams = filterNgrams(lengthFilter, keywords);
                                    const textToCopy = filteredNgrams.map(item => item.text).join('\n');
                                    
                                    if (navigator.clipboard && window.isSecureContext) {
                                        navigator.clipboard.writeText(textToCopy).then(() => {
                                            showNotification(`Copied ${filteredNgrams.length} filtered results!`, 'success');
                                        }).catch(err => {
                                            console.error('Failed to copy: ', err);
                                            showNotification('Failed to copy to clipboard', 'error');
                                        });
                                    } else {
                                        // Fallback for older browsers
                                        const tempTextArea = document.createElement('textarea');
                                        tempTextArea.value = textToCopy;
                                        tempTextArea.style.position = 'fixed';
                                        tempTextArea.style.left = '-999999px';
                                        tempTextArea.style.top = '-999999px';
                                        document.body.appendChild(tempTextArea);
                                        tempTextArea.focus();
                                        tempTextArea.select();
                                        try {
                                            document.execCommand('copy');
                                            showNotification(`Copied ${filteredNgrams.length} filtered results!`, 'success');
                                        } catch (err) {
                                            console.error('Fallback: Failed to copy', err);
                                            showNotification('Failed to copy to clipboard', 'error');
                                        }
                                        document.body.removeChild(tempTextArea);
                                    }
                                }
                                
                                function clearFilters() {
                                    const lengthInput = panel.querySelector('#word-length-filter');
                                    const keywordInput = panel.querySelector('#keyword-filter');
                                    lengthInput.value = '';
                                    keywordInput.value = '';
                                    updateDisplay();
                                }
                                
                                function closePanel() {
                                    panel.remove();
                                }
                                
                                // Add event listeners for all buttons and inputs
                                const lengthInput = panel.querySelector('#word-length-filter');
                                const keywordInput = panel.querySelector('#keyword-filter');
                                const copyBtn = panel.querySelector('#copy-filtered-btn');
                                const clearBtn = panel.querySelector('#clear-filters-btn');
                                const closeBtnTop = panel.querySelector('#close-panel-btn');
                                
                                // Set default filter value
                                lengthInput.value = '3-5';
                                
                                // Filter input event listeners
                                lengthInput.addEventListener('input', updateDisplay);
                                lengthInput.addEventListener('keypress', function(e) {
                                    if (e.key === 'Enter') {
                                        updateDisplay();
                                    }
                                });
                                
                                keywordInput.addEventListener('input', updateDisplay);
                                keywordInput.addEventListener('keypress', function(e) {
                                    if (e.key === 'Enter') {
                                        updateDisplay();
                                    }
                                });
                                
                                // Button event listeners
                                copyBtn.addEventListener('click', copyFilteredResults);
                                clearBtn.addEventListener('click', clearFilters);
                                closeBtnTop.addEventListener('click', closePanel);
                                
                                // Apply default filter on load
                                updateDisplay();
                                
                                // Add drag and resize functionality
                                const header = panel.querySelector('#bold-from-serp-header');
                                const resizeHandle = panel.querySelector('#resize-handle');
                                let isDragging = false;
                                let isResizing = false;
                                let startX = 0, startY = 0;
                                let startLeft = 0, startTop = 0;
                                let startWidth = 0, startHeight = 0;
                                
                                // Helper function to get current panel position
                                function getCurrentPosition() {
                                    const rect = panel.getBoundingClientRect();
                                    return {
                                        left: rect.left,
                                        top: rect.top,
                                        width: rect.width,
                                        height: rect.height
                                    };
                                }
                                
                                // Drag functionality
                                function startDrag(e) {
                                    if (e.target.tagName === 'BUTTON' || e.target.id === 'resize-handle') return;
                                    e.preventDefault();
                                    isDragging = true;
                                    const pos = getCurrentPosition();
                                    startX = e.clientX;
                                    startY = e.clientY;
                                    startLeft = pos.left;
                                    startTop = pos.top;
                                    
                                    panel.style.transition = 'none';
                                    document.body.style.userSelect = 'none';
                                    header.style.cursor = 'grabbing';
                                }
                                
                                // Resize functionality
                                function startResize(e) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    isResizing = true;
                                    const pos = getCurrentPosition();
                                    startX = e.clientX;
                                    startY = e.clientY;
                                    startWidth = pos.width;
                                    startHeight = pos.height;
                                    
                                    panel.style.transition = 'none';
                                    document.body.style.userSelect = 'none';
                                }
                                
                                // Mouse move handler
                                function handleMouseMove(e) {
                                    if (isDragging) {
                                        const deltaX = e.clientX - startX;
                                        const deltaY = e.clientY - startY;
                                        const newLeft = startLeft + deltaX;
                                        const newTop = startTop + deltaY;
                                        
                                        // Constrain to viewport
                                        const maxLeft = window.innerWidth - panel.offsetWidth;
                                        const maxTop = window.innerHeight - panel.offsetHeight;
                                        const constrainedLeft = Math.max(0, Math.min(newLeft, maxLeft));
                                        const constrainedTop = Math.max(0, Math.min(newTop, maxTop));
                                        
                                        panel.style.left = constrainedLeft + 'px';
                                        panel.style.top = constrainedTop + 'px';
                                        panel.style.transform = 'none';
                                    } else if (isResizing) {
                                        const deltaX = e.clientX - startX;
                                        const deltaY = e.clientY - startY;
                                        const newWidth = Math.max(350, Math.min(startWidth + deltaX, window.innerWidth - panel.offsetLeft));
                                        const newHeight = Math.max(300, Math.min(startHeight + deltaY, window.innerHeight - panel.offsetTop));
                                        
                                        panel.style.width = newWidth + 'px';
                                        panel.style.height = newHeight + 'px';
                                    }
                                }
                                
                                // End drag/resize
                                function endDragResize() {
                                    isDragging = false;
                                    isResizing = false;
                                    document.body.style.userSelect = '';
                                    header.style.cursor = 'move';
                                    panel.style.transition = '';
                                }
                                
                                // Add event listeners
                                header.addEventListener('mousedown', startDrag);
                                resizeHandle.addEventListener('mousedown', startResize);
                                document.addEventListener('mousemove', handleMouseMove);
                                document.addEventListener('mouseup', endDragResize);
                                
                                // Touch support
                                function getTouchPos(e) {
                                    return {
                                        clientX: e.touches[0].clientX,
                                        clientY: e.touches[0].clientY
                                    };
                                }
                                
                                header.addEventListener('touchstart', function(e) {
                                    const touch = getTouchPos(e);
                                    startDrag(touch);
                                });
                                
                                resizeHandle.addEventListener('touchstart', function(e) {
                                    const touch = getTouchPos(e);
                                    startResize(touch);
                                });
                                
                                document.addEventListener('touchmove', function(e) {
                                    if (isDragging || isResizing) {
                                        e.preventDefault();
                                        const touch = getTouchPos(e);
                                        handleMouseMove(touch);
                                    }
                                });
                                
                                document.addEventListener('touchend', endDragResize);
                                
                                // Handle Escape key
                                function handleKeyDown(e) {
                                    if (e.key === 'Escape') {
                                        panel.remove();
                                        document.removeEventListener('keydown', handleKeyDown);
                                    }
                                }
                                document.addEventListener('keydown', handleKeyDown);
                            }
                            
                            function showNotification(message, type) {
                                var notification = document.createElement('div');
                                notification.style.cssText = `
                                    position:fixed;
                                    top:20px;
                                    left:50%;
                                    transform:translateX(-50%);
                                    background:rgba(0, 0, 0, 0.9);
                                    color:white;
                                    padding:12px 20px;
                                    border-radius:8px;
                                    z-index:10000000;
                                    font-family:Arial,sans-serif;
                                    font-size:14px;
                                    font-weight:500;
                                    box-shadow:0 4px 12px rgba(0,0,0,0.3);
                                    border:1px solid #7C3AED;
                                `;
                                
                                // Add purple dot on black background for small notifications
                                notification.innerHTML = `
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="color: #7C3AED; font-size: 16px; background: #000; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">●</span>
                                        <span>${message}</span>
                                    </div>
                                `;
                                document.body.appendChild(notification);
                                setTimeout(function() {
                                    notification.remove();
                                }, 3000);
                            }
                            
                            // Start the extraction
                            extractBoldFromSerp();
                        })();
                    }
                }, (result) => {
                    if (chrome.runtime.lastError) {
                        console.error('Error executing Bold From SERP script:', chrome.runtime.lastError);
                    } else {
                        console.log('Bold From SERP script executed successfully');
                    }
                });
            });
        } catch (error) {
            console.error('Bold From SERP error:', error);
        }
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                // Function to reset Bold From SERP effects
                function resetBoldFromSerp() {
                    // Remove any existing Bold From SERP panels
                    const existingPanels = document.querySelectorAll('.bold-from-serp-panel');
                    existingPanels.forEach(panel => {
                        panel.remove();
                    });
                    
                    console.log('Bold From SERP reset completed');
                }

                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetBoldFromSerp
                    }, () => {
                        resolve();
                    });
                });
            } catch (error) {
                console.error('Bold From SERP reset error:', error);
                resolve(); // Resolve anyway to not block other resets
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BoldFromSerpAction;
} else {
    window.BoldFromSerpAction = BoldFromSerpAction;
} 