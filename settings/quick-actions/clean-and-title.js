// Clean and Title Action - HTML cleaning and title attribute enhancement tool
class CleanAndTitleAction {
    constructor() {
        this.name = 'Clean and Title';
        this.description = 'Clean HTML and add title attributes to heading tags';
    }

    // Get HTML Cleaner settings and apply them
    async getHtmlCleanerSettings() {
        return new Promise((resolve) => {
            chrome.storage.local.get(['htmlCleanerSettings'], (result) => {
                const defaultSettings = {
                    removeStyles: true,
                    removeClasses: true,
                    removeIds: true,
                    removeComments: true,
                    removeEmptyTags: true,
                    removeExtraSpaces: true,
                    removeDataAttributes: true,
                    removeWrapperDivs: true,
                    removeCitationNumbers: true,
                    removeAllAttributes: true
                };
                
                resolve(result.htmlCleanerSettings || defaultSettings);
            });
        });
    }

    // Use HTML Cleaner with current settings for comprehensive cleaning
    async cleanWithHtmlCleaner(htmlContent) {
        try {
            // Get current HTML Cleaner settings
            const settings = await this.getHtmlCleanerSettings();
            
            // Use HTMLCleanerAction to clean the content
            if (window.HTMLCleanerAction) {
                const cleaner = new window.HTMLCleanerAction();
                return cleaner.cleanHTML(htmlContent, settings);
            } else {
                // Fallback to basic cleaning if HTMLCleanerAction not available
                console.warn('HTMLCleanerAction not available, using basic cleaning');
                return this.cleanHTML(htmlContent);
            }
        } catch (error) {
            console.error('Error using HTML Cleaner:', error);
            // Fallback to basic cleaning
            return this.cleanHTML(htmlContent);
        }
    }

    // Clean HTML content by removing styles, classes, IDs, and empty tags while preserving essential media attributes
    cleanHTML(html) {
        // Remove inline styles
        html = html.replace(/ style="[^"]*"/g, '');
        
        // Delete classes and IDs (but preserve data attributes for media elements)
        html = html.replace(/ (class|id)="[^"]*"/g, '');
        
        // Delete empty tags and tags with one space
        html = html.replace(/<([^>]+)>\s*<\/\1>/g, '');
        
        // Delete successive spaces
        html = html.replace(/\s{2,}/g, ' ');
        
        // Delete all comments
        html = html.replace(/<!--[\s\S]*?-->/g, '');
        
        // Smart attribute cleanup - preserve essential attributes for media elements
        html = html.replace(/<([a-z][a-z0-9]*)[^>]*>/gi, function(m, tag) {
            const tagLower = tag.toLowerCase();
            
            // Preserve all attributes for links
            if (tagLower === 'a') {
                return m;
            }
            
            // Preserve essential attributes for images
            if (tagLower === 'img') {
                const srcMatch = m.match(/ src="[^"]*"/i);
                const altMatch = m.match(/ alt="[^"]*"/i);
                const widthMatch = m.match(/ width="[^"]*"/i);
                const heightMatch = m.match(/ height="[^"]*"/i);
                const titleMatch = m.match(/ title="[^"]*"/i);
                
                let preserved = '<' + tag;
                if (srcMatch) preserved += srcMatch[0];
                if (altMatch) preserved += altMatch[0];
                if (widthMatch) preserved += widthMatch[0];
                if (heightMatch) preserved += heightMatch[0];
                if (titleMatch) preserved += titleMatch[0];
                preserved += '>';
                
                return preserved;
            }
            
            // Preserve essential attributes for videos
            if (tagLower === 'video') {
                const srcMatch = m.match(/ src="[^"]*"/i);
                const widthMatch = m.match(/ width="[^"]*"/i);
                const heightMatch = m.match(/ height="[^"]*"/i);
                const controlsMatch = m.match(/ controls(?:="[^"]*")?/i);
                const autoplayMatch = m.match(/ autoplay(?:="[^"]*")?/i);
                const loopMatch = m.match(/ loop(?:="[^"]*")?/i);
                const mutedMatch = m.match(/ muted(?:="[^"]*")?/i);
                const posterMatch = m.match(/ poster="[^"]*"/i);
                
                let preserved = '<' + tag;
                if (srcMatch) preserved += srcMatch[0];
                if (widthMatch) preserved += widthMatch[0];
                if (heightMatch) preserved += heightMatch[0];
                if (controlsMatch) preserved += controlsMatch[0];
                if (autoplayMatch) preserved += autoplayMatch[0];
                if (loopMatch) preserved += loopMatch[0];
                if (mutedMatch) preserved += mutedMatch[0];
                if (posterMatch) preserved += posterMatch[0];
                preserved += '>';
                
                return preserved;
            }
            
            // Preserve essential attributes for iframes
            if (tagLower === 'iframe') {
                const srcMatch = m.match(/ src="[^"]*"/i);
                const widthMatch = m.match(/ width="[^"]*"/i);
                const heightMatch = m.match(/ height="[^"]*"/i);
                const frameborderMatch = m.match(/ frameborder="[^"]*"/i);
                const allowfullscreenMatch = m.match(/ allowfullscreen(?:="[^"]*")?/i);
                const titleMatch = m.match(/ title="[^"]*"/i);
                const allowMatch = m.match(/ allow="[^"]*"/i);
                
                let preserved = '<' + tag;
                if (srcMatch) preserved += srcMatch[0];
                if (widthMatch) preserved += widthMatch[0];
                if (heightMatch) preserved += heightMatch[0];
                if (frameborderMatch) preserved += frameborderMatch[0];
                if (allowfullscreenMatch) preserved += allowfullscreenMatch[0];
                if (titleMatch) preserved += titleMatch[0];
                if (allowMatch) preserved += allowMatch[0];
                preserved += '>';
                
                return preserved;
            }
            
            // For all other tags, remove all attributes
            return '<' + tag + '>';
        });
        
        return html.trim();
    }

    // Helper function to strip HTML tags and extract clean text content
    stripHtmlTags(html) {
        // Create a temporary DOM element to parse HTML and extract text
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        
        // Get text content and clean up extra whitespace
        let textContent = tempDiv.textContent || tempDiv.innerText || '';
        
        // Remove extra whitespace and normalize
        textContent = textContent.replace(/\s+/g, ' ').trim();
        
        return textContent;
    }

    // Add title attributes to heading tags
    processHeadingTitles(inputText) {
        // More robust regex to capture heading tag structure
        var regex = /<(h\d)([^>]*?)>(.*?)<\/h\d>/gi;
        
        return inputText.replace(regex, (match, tagName, existingAttributes, content) => {
            // Extract clean text content for the title attribute
            const cleanTextContent = this.stripHtmlTags(content);
            
            // Skip if no meaningful text content
            if (!cleanTextContent) {
                return match;
            }
            
            // Escape quotes in the title content to prevent attribute corruption
            const escapedTitle = cleanTextContent.replace(/"/g, '&quot;');
            
            // Check if title attribute already exists
            if (existingAttributes && existingAttributes.includes('title=')) {
                // Replace existing title attribute
                const updatedAttributes = existingAttributes.replace(
                    /title\s*=\s*["'][^"']*["']/i, 
                    `title="${escapedTitle}"`
                );
                return `<${tagName}${updatedAttributes}>${content}</${tagName}>`;
            } else {
                // Add new title attribute
                const attributes = existingAttributes ? existingAttributes + ' ' : ' ';
                return `<${tagName}${attributes}title="${escapedTitle}">${content}</${tagName}>`;
            }
        });
    }

    // Execute the clean and title functionality
    execute() {
        return new Promise((resolve, reject) => {
            try {
                console.log('CleanAndTitleAction: Starting HTML clean and title processing');

                // Create notification for processing
                const notification = document.createElement('div');
                notification.className = 'clean-and-title-notification';
                notification.setAttribute('data-clean-and-title-notification', 'true');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(0, 0, 0, 0.9);
                    color: white;
                    border: 1px solid #7C3AED;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    z-index: 9999999;
                    pointer-events: none;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                    box-shadow: 0 4px 16px rgba(0,0,0,0.4);
                    opacity: 0;
                    transition: opacity 0.4s ease;
                `;
                
                // Add purple dot on black background for small notifications
                notification.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="color: #7C3AED; font-size: 16px; background: #000; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">●</span>
                        <span>Processing HTML cleanup and titles...</span>
                    </div>
                `;
                document.body.appendChild(notification);

                // Show notification
                setTimeout(() => notification.style.opacity = '1', 10);

                // Read clipboard content
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.readText()
                        .then(async (text) => {
                            if (!text || text.trim() === '') {
                                throw new Error('No content found in clipboard');
                            }

                            // First: Clean the HTML using HTML Cleaner settings
                            const cleanedText = await this.cleanWithHtmlCleaner(text);
                            
                            // Second: Add title attributes to headings (this now works with clean HTML)
                            const processedText = this.processHeadingTitles(cleanedText);
                            
                            // Write back to clipboard
                            return navigator.clipboard.writeText(processedText);
                        })
                        .then(() => {
                            // Update notification with success message
                            notification.innerHTML = `
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span style="color: #7C3AED; font-size: 16px; background: #000; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">●</span>
                                    <span>HTML cleaned, auto titles applied, and copied to clipboard!</span>
                                </div>
                            `;
                            
                            // Remove notification after delay
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    if (notification.parentNode) {
                                        notification.remove();
                                    }
                                }, 400);
                            }, 3000);

                            console.log('CleanAndTitleAction: Processing completed successfully');
                            resolve({ success: true, message: 'Clean and Title processing completed' });
                        })
                        .catch(error => {
                            console.error('CleanAndTitleAction: Error during processing:', error);
                            
                            // Update notification with error message
                            notification.style.color = '#ef4444';
                            notification.style.borderColor = '#ef4444';
                            notification.textContent = '❌ Error: ' + (error.message || 'Processing failed');
                            
                            // Remove notification after delay
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    if (notification.parentNode) {
                                        notification.remove();
                                    }
                                }, 400);
                            }, 3000);

                            reject(error);
                        });
                } else {
                    // Fallback for browsers without clipboard API or non-secure contexts
                    throw new Error('Clipboard API not available - please use a secure context (HTTPS)');
                }

            } catch (error) {
                console.error('CleanAndTitleAction: Execution error:', error);
                reject(error);
            }
        });
    }

    // Reset the clean and title functionality (remove all modifications)
    reset() {
        return new Promise((resolve, reject) => {
            try {
                console.log('CleanAndTitleAction: Starting reset');
                
                // Remove all clean-and-title related elements
                const selectorsToRemove = [
                    '.clean-and-title-notification',
                    '[data-clean-and-title-notification]',
                    '[data-clean-and-title]'
                ];
                
                selectorsToRemove.forEach(selector => {
                    document.querySelectorAll(selector).forEach(el => {
                        console.log('CleanAndTitleAction: Removing element:', selector, el);
                        el.remove();
                    });
                });

                // Clear any global variables related to clean and title
                if (window.CleanAndTitleAction) {
                    // Only clear instance variables, not the class itself
                    console.log('CleanAndTitleAction: Cleared global references');
                }

                console.log('CleanAndTitleAction: Reset completed');
                resolve({ success: true, message: 'Clean and Title completely reset' });
            } catch (error) {
                console.error('CleanAndTitleAction: Reset error:', error);
                reject(error);
            }
        });
    }

    // Static execute method for global access (used by context menu)
    static execute() {
        console.log('CleanAndTitleAction: Static execute called');
        const action = new CleanAndTitleAction();
        return action.execute();
    }

    // Static reset method for global access (used by reset utilities)
    static reset() {
        console.log('CleanAndTitleAction: Static reset called');
        const action = new CleanAndTitleAction();
        return action.reset();
    }
}

// Make available globally for context menu execution
if (typeof window !== 'undefined') {
    window.CleanAndTitleAction = CleanAndTitleAction;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CleanAndTitleAction;
} 