/**
 * Page Structure Action
 * Toggles visual X-ray overlay for page structure analysis with hover inspection
 */
class PageStructureAction {
  
  constructor() {
    this.name = 'Page Structure';
    this.description = 'Toggles visual X-ray overlay for page structure analysis with hover inspection';
  }
  
  // Instance method for popup/shortcuts usage
  execute() {
    return new Promise((resolve, reject) => {
      try {
        console.log('Page Structure X-ray activated (instance method)');
        
        // Check if we're in a Chrome extension context
        if (typeof chrome !== 'undefined' && chrome.tabs) {
          // We're in popup context, need to execute on the current tab
          chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0]) {
              // First inject the pagestructure.js file, then execute the static method
              chrome.scripting.executeScript({
                target: {tabId: tabs[0].id},
                files: ['settings/quick-actions/pagestructure.js']
              }, () => {
                if (chrome.runtime.lastError) {
                  console.error('Error injecting Page Structure script:', chrome.runtime.lastError);
                  reject(chrome.runtime.lastError);
                  return;
                }
                
                // Now execute the static method
                chrome.scripting.executeScript({
                  target: {tabId: tabs[0].id},
                  func: function() {
                    if (typeof PageStructureAction !== 'undefined') {
                      return PageStructureAction.execute();
                    } else {
                      return { error: 'PageStructureAction not found after injection' };
                    }
                  }
                }, (result) => {
                  if (chrome.runtime.lastError) {
                    console.error('Error executing Page Structure script:', chrome.runtime.lastError);
                    reject(chrome.runtime.lastError);
                  } else {
                    console.log('Page Structure script executed successfully:', result);
                    resolve(result);
                  }
                });
              });
            } else {
              reject(new Error('No active tab found'));
            }
          });
        } else {
          // We're in page context, execute directly
          const result = PageStructureAction._executeXRayToggle();
          resolve(result);
        }
      } catch (error) {
        console.error('Error in Page Structure execute:', error);
        reject(error);
      }
    });
  }
  
  // Instance method for popup/shortcuts usage
  reset() {
    return new Promise((resolve, reject) => {
      try {
        console.log('Page Structure X-ray reset (instance method)');
        
        // Check if we're in a Chrome extension context
        if (typeof chrome !== 'undefined' && chrome.tabs) {
          // We're in popup context, need to execute on the current tab
          chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0]) {
              // First inject the pagestructure.js file, then execute the static method
              chrome.scripting.executeScript({
                target: {tabId: tabs[0].id},
                files: ['settings/quick-actions/pagestructure.js']
              }, () => {
                if (chrome.runtime.lastError) {
                  console.error('Error injecting Page Structure script for reset:', chrome.runtime.lastError);
                  reject(chrome.runtime.lastError);
                  return;
                }
                
                // Now execute the static reset method
                chrome.scripting.executeScript({
                  target: {tabId: tabs[0].id},
                  func: function() {
                    if (typeof PageStructureAction !== 'undefined') {
                      return PageStructureAction.reset();
                    } else {
                      return { error: 'PageStructureAction not found after injection' };
                    }
                  }
                }, (result) => {
                  if (chrome.runtime.lastError) {
                    console.error('Error resetting Page Structure script:', chrome.runtime.lastError);
                    reject(chrome.runtime.lastError);
                  } else {
                    console.log('Page Structure reset executed successfully:', result);
                    resolve(result);
                  }
                });
              });
            } else {
              reject(new Error('No active tab found'));
            }
          });
        } else {
          // We're in page context, execute directly
          const result = PageStructureAction._executeXRayReset();
          resolve(result);
        }
      } catch (error) {
        console.error('Error in Page Structure reset:', error);
        reject(error);
      }
    });
  }
  
  // Static method for background script injection
  static execute() {
    try {
      console.log('Page Structure X-ray activated (static method)');
      return PageStructureAction._executeXRayToggle();
    } catch (error) {
      console.error('Error executing Page Structure X-ray:', error);
      alert("Error applying Page Structure X-ray: " + error.message);
      return { error: error.message };
    }
  }
  
  // Static method for background script injection
  static reset() {
    try {
      console.log('Page Structure X-ray reset (static method)');
      return PageStructureAction._executeXRayReset();
    } catch (error) {
      console.error('Error resetting Page Structure X-ray:', error);
      return { error: error.message };
    }
  }
  
  // Store the event listeners so they can be properly removed
  static _escapeKeyListener = null;
  static _hoverInspectorListeners = {
    mouseover: null,
    mousemove: null,
    mouseout: null,
    click: null,
    contextmenu: null
  };
  static _hoverInspectorTooltip = null;
  static _currentHoveredElement = null;
  static _originalOutline = '';
  static _frozenTooltips = new Set(); // Track frozen tooltips
  static _frozenTooltipCounter = 0; // Counter for unique IDs
  
  // Core X-ray toggle functionality (shared by both static and instance methods)
  static _executeXRayToggle() {
    try {
      // Page Structure X-ray overlay function
      const xray = document.createElement('style');
      xray.innerHTML = "*{background:#000!important;color:#0f0!important;outline:solid #f00 1px!important;}";
      const xraysInPage = [...document.body.getElementsByTagName("style")].filter(style => style.innerHTML === xray.innerHTML);
      
      if (xraysInPage.length > 0) {
        xraysInPage.forEach(style => document.body.removeChild(style));
        // Remove both escape key listener and hover inspector when X-ray is removed
        PageStructureAction._removeEscapeKeyListener();
        PageStructureAction._removeHoverInspector();
        console.log('Page Structure X-ray removed');
        return { success: true, action: 'removed', message: 'Page Structure X-ray removed' };
      } else {
        document.body.appendChild(xray);
        // Add both escape key listener and hover inspector when X-ray is applied
        PageStructureAction._addEscapeKeyListener();
        PageStructureAction._addHoverInspector();
        console.log('Page Structure X-ray applied');
        return { success: true, action: 'added', message: 'Page Structure X-ray applied with hover inspection' };
      }
    } catch (error) {
      console.error('Error in X-ray toggle:', error);
      return { error: error.message };
    }
  }
  
  // Core X-ray reset functionality (shared by both static and instance methods)
  static _executeXRayReset() {
    try {
      // Remove all X-ray overlays
      const xraysInPage = [...document.body.getElementsByTagName("style")].filter(style => 
        style.innerHTML === "*{background:#000!important;color:#0f0!important;outline:solid #f00 1px!important;}"
      );
      xraysInPage.forEach(style => document.body.removeChild(style));
      
      // Remove both escape key listener and hover inspector when X-ray is reset
      PageStructureAction._removeEscapeKeyListener();
      PageStructureAction._removeHoverInspector();
      
      console.log('Page Structure X-ray reset completed');
      return { success: true, message: 'Page Structure X-ray reset completed' };
    } catch (error) {
      console.error('Error in X-ray reset:', error);
      return { error: error.message };
    }
  }
  
  // Add hover inspector functionality
  static _addHoverInspector() {
    // Remove any existing hover inspector first to avoid duplicates
    PageStructureAction._removeHoverInspector();
    
    // Style properties to display in tooltip
    const styleProps = ['display', 'position', 'font-size', 'color', 
                       'background-color', 'margin', 'padding', 'border'];
    
    // Create tooltip element
    const tooltip = document.createElement('div');
    tooltip.id = 'page-structure-hover-tooltip';
    Object.assign(tooltip.style, {
      position: 'fixed',
      pointerEvents: 'none',
      background: 'rgba(0,0,0,0.85)',
      color: '#fff',
      padding: '8px',
      borderRadius: '6px',
      fontSize: '12px',
      fontFamily: 'Monaco, Consolas, "Lucida Console", monospace',
      zIndex: '999999',
      display: 'none',
      maxWidth: '350px',
      whiteSpace: 'pre',
      lineHeight: '1.4',
      boxShadow: '0 4px 12px rgba(0,0,0,0.4)',
      border: '1px solid rgba(255,255,255,0.1)'
    });
    document.body.appendChild(tooltip);
    PageStructureAction._hoverInspectorTooltip = tooltip;
    
    // Helper function to format style values
    function formatStyleValue(value) {
      return value.replace(/(rgb\(.*?\))/g, '$1\n').replace(/, /g, ',\n ');
    }
    
    // Helper function to create tooltip content
    function createTooltipContent(el) {
      const rect = el.getBoundingClientRect();
      const styles = window.getComputedStyle(el);
      
      // Build tooltip content
      const styleInfo = styleProps.map(prop => {
        const value = styles.getPropertyValue(prop);
        return `${prop}: ${formatStyleValue(value)}`;
      }).join('\n');
      
      return [
        `${el.tagName.toLowerCase()}${el.id ? '#' + el.id : ''}${el.className ? '.' + Array.from(el.classList).join('.') : ''}`,
        `Dimensions: ${Math.round(rect.width)}px × ${Math.round(rect.height)}px`,
        '─'.repeat(40),
        styleInfo
      ].join('\n');
    }
    
    // Mouseover event listener
    PageStructureAction._hoverInspectorListeners.mouseover = function(e) {
      const el = e.target;
      
      // Skip if hovering over tooltip or frozen tooltips
      if (!el || el === tooltip || el === document.body || 
          el.id === 'page-structure-hover-tooltip' ||
          el.classList.contains('page-structure-frozen-tooltip') ||
          el.closest('.page-structure-frozen-tooltip')) return;
      
      // Store and modify outline (but don't override X-ray outline)
      PageStructureAction._currentHoveredElement = el;
      PageStructureAction._originalOutline = el.style.outline;
      
      // Only add hover outline if X-ray isn't already providing one
      const hasXrayOutline = el.style.outline && el.style.outline.includes('#f00');
      if (!hasXrayOutline) {
        el.style.outline = '2px solid #ff4444';
      }
      
      const elementInfo = createTooltipContent(el);
      tooltip.textContent = elementInfo;
      tooltip.style.display = 'block';
    };
    
    // Mousemove event listener
    PageStructureAction._hoverInspectorListeners.mousemove = function(e) {
      if (tooltip.style.display === 'block') {
        const x = e.clientX + 15;
        const y = e.clientY + 15;
        
        // Ensure tooltip doesn't go off screen
        const tooltipRect = tooltip.getBoundingClientRect();
        const maxX = window.innerWidth - tooltipRect.width - 10;
        const maxY = window.innerHeight - tooltipRect.height - 10;
        
        tooltip.style.left = `${Math.min(x, maxX)}px`;
        tooltip.style.top = `${Math.min(y, maxY)}px`;
      }
    };
    
    // Mouseout event listener
    PageStructureAction._hoverInspectorListeners.mouseout = function(e) {
      if (PageStructureAction._currentHoveredElement) {
        // Only restore outline if we added it (not the X-ray outline)
        const hasXrayOutline = PageStructureAction._currentHoveredElement.style.outline && 
                              PageStructureAction._currentHoveredElement.style.outline.includes('#f00');
        if (!hasXrayOutline) {
          PageStructureAction._currentHoveredElement.style.outline = PageStructureAction._originalOutline;
        }
        PageStructureAction._currentHoveredElement = null;
      }
      tooltip.style.display = 'none';
    };
    
    // Click event listener to freeze tooltip
    PageStructureAction._hoverInspectorListeners.click = function(e) {
      const el = e.target;
      
      // Skip if clicking on tooltip or frozen tooltips
      if (!el || el === tooltip || el === document.body || 
          el.id === 'page-structure-hover-tooltip' ||
          el.classList.contains('page-structure-frozen-tooltip') ||
          el.closest('.page-structure-frozen-tooltip')) return;
      
      // Create frozen tooltip
      PageStructureAction._createFrozenTooltip(el, e.clientX, e.clientY);
      
      // Prevent default click behavior
      e.preventDefault();
      e.stopPropagation();
    };
    
    // Right-click event listener to copy class and ID info
    PageStructureAction._hoverInspectorListeners.contextmenu = function(e) {
      const el = e.target;
      
      // Skip if right-clicking on tooltip or frozen tooltips
      if (!el || el === tooltip || el === document.body || 
          el.id === 'page-structure-hover-tooltip' ||
          el.classList.contains('page-structure-frozen-tooltip') ||
          el.closest('.page-structure-frozen-tooltip')) return;
      
      // Extract class and ID information
      const tagName = el.tagName.toLowerCase();
      const id = el.id ? `#${el.id}` : '';
      const classes = el.className ? `.${Array.from(el.classList).join('.')}` : '';
      
      // Build selector string
      const selector = `${tagName}${id}${classes}`;
      
      // Copy to clipboard
      PageStructureAction._copyToClipboard(selector, el);
      
      // Prevent default context menu
      e.preventDefault();
      e.stopPropagation();
    };
    
    // Add event listeners
    document.addEventListener('mouseover', PageStructureAction._hoverInspectorListeners.mouseover, true);
    document.addEventListener('mousemove', PageStructureAction._hoverInspectorListeners.mousemove, true);
    document.addEventListener('mouseout', PageStructureAction._hoverInspectorListeners.mouseout, true);
    document.addEventListener('click', PageStructureAction._hoverInspectorListeners.click, true);
    document.addEventListener('contextmenu', PageStructureAction._hoverInspectorListeners.contextmenu, true);
    
    console.log('Hover inspector added for Page Structure X-ray');
  }
  
  // Copy text to clipboard with visual feedback
  static _copyToClipboard(text, element) {
    try {
      // Use the modern clipboard API if available
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
          PageStructureAction._showCopyFeedback(element, text);
          console.log('Copied to clipboard:', text);
        }).catch(err => {
          console.error('Failed to copy with clipboard API:', err);
          PageStructureAction._fallbackCopyToClipboard(text, element);
        });
      } else {
        // Fallback for older browsers or non-secure contexts
        PageStructureAction._fallbackCopyToClipboard(text, element);
      }
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      PageStructureAction._showCopyFeedback(element, text, false);
    }
  }
  
  // Fallback copy method for older browsers
  static _fallbackCopyToClipboard(text, element) {
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (successful) {
        PageStructureAction._showCopyFeedback(element, text);
        console.log('Copied to clipboard (fallback):', text);
      } else {
        PageStructureAction._showCopyFeedback(element, text, false);
      }
    } catch (err) {
      console.error('Fallback copy failed:', err);
      PageStructureAction._showCopyFeedback(element, text, false);
    }
  }
  
  // Show visual feedback when copying
  static _showCopyFeedback(element, text, success = true) {
    const feedback = document.createElement('div');
    feedback.className = 'page-structure-copy-feedback';
    
    Object.assign(feedback.style, {
      position: 'fixed',
      background: success ? 'rgba(0, 128, 0, 0.9)' : 'rgba(128, 0, 0, 0.9)',
      color: '#fff',
      padding: '8px 12px',
      borderRadius: '4px',
      fontSize: '12px',
      fontFamily: 'Monaco, Consolas, "Lucida Console", monospace',
      zIndex: '9999999',
      pointerEvents: 'none',
      boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
      transition: 'opacity 0.3s ease'
    });
    
    feedback.textContent = success ? `Copied: ${text}` : `Failed to copy: ${text}`;
    
    // Position near the cursor
    const rect = element.getBoundingClientRect();
    feedback.style.left = `${rect.left + 10}px`;
    feedback.style.top = `${rect.top - 30}px`;
    
    document.body.appendChild(feedback);
    
    // Remove feedback after 2 seconds
    setTimeout(() => {
      feedback.style.opacity = '0';
      setTimeout(() => {
        try {
          document.body.removeChild(feedback);
        } catch (e) {
          // Element might already be removed
        }
      }, 300);
    }, 2000);
  }
  
  // Create a frozen tooltip
  static _createFrozenTooltip(element, x, y) {
    const tooltipId = `frozen-tooltip-${++PageStructureAction._frozenTooltipCounter}`;
    
    // Style properties to display in tooltip
    const styleProps = ['display', 'position', 'font-size', 'color', 
                       'background-color', 'margin', 'padding', 'border'];
    
    // Helper function to format style values
    function formatStyleValue(value) {
      return value.replace(/(rgb\(.*?\))/g, '$1\n').replace(/, /g, ',\n ');
    }
    
    // Helper function to create tooltip content
    function createTooltipContent(el) {
      const rect = el.getBoundingClientRect();
      const styles = window.getComputedStyle(el);
      
      // Build tooltip content
      const styleInfo = styleProps.map(prop => {
        const value = styles.getPropertyValue(prop);
        return `${prop}: ${formatStyleValue(value)}`;
      }).join('\n');
      
      return [
        `${el.tagName.toLowerCase()}${el.id ? '#' + el.id : ''}${el.className ? '.' + Array.from(el.classList).join('.') : ''}`,
        `Dimensions: ${Math.round(rect.width)}px × ${Math.round(rect.height)}px`,
        '─'.repeat(40),
        styleInfo
      ].join('\n');
    }
    
    // Create frozen tooltip container
    const frozenTooltip = document.createElement('div');
    frozenTooltip.id = tooltipId;
    frozenTooltip.className = 'page-structure-frozen-tooltip';
    
    Object.assign(frozenTooltip.style, {
      position: 'absolute',
      background: 'rgba(0,0,0,0.75)',
      color: '#fff',
      padding: '8px',
      paddingTop: '28px', // Make room for close button
      borderRadius: '6px',
      fontSize: '12px',
      fontFamily: 'Monaco, Consolas, "Lucida Console", monospace',
      zIndex: '999998', // Slightly lower than hover tooltip
      maxWidth: '350px',
      whiteSpace: 'pre',
      lineHeight: '1.4',
      boxShadow: '0 4px 12px rgba(0,0,0,0.5)',
      border: '2px solid #4444ff',
      cursor: 'move'
    });
    
    // Create close button
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '×';
    Object.assign(closeButton.style, {
      position: 'absolute',
      top: '4px',
      right: '6px',
      background: '#ff4444',
      color: '#fff',
      border: 'none',
      borderRadius: '50%',
      width: '18px',
      height: '18px',
      fontSize: '12px',
      cursor: 'pointer',
      lineHeight: '1',
      fontWeight: 'bold'
    });
    
    // Add close button click handler
    closeButton.addEventListener('click', (e) => {
      e.stopPropagation();
      PageStructureAction._removeFrozenTooltip(tooltipId);
    });
    
    // Add content
    const content = document.createElement('div');
    content.textContent = createTooltipContent(element);
    
    frozenTooltip.appendChild(closeButton);
    frozenTooltip.appendChild(content);
    
    // Position tooltip (accounting for page scroll since we're using absolute positioning)
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollY = window.pageYOffset || document.documentElement.scrollTop;
    
    const absoluteX = x + scrollX + 15;
    const absoluteY = y + scrollY + 15;
    
    // Ensure tooltip stays within viewport bounds
    const maxX = scrollX + window.innerWidth - 350 - 10;
    const maxY = scrollY + window.innerHeight - 200 - 10;
    
    frozenTooltip.style.left = `${Math.min(absoluteX, maxX)}px`;
    frozenTooltip.style.top = `${Math.min(absoluteY, maxY)}px`;
    
    // Make tooltip draggable
    PageStructureAction._makeDraggable(frozenTooltip);
    
    // Add to page and track it
    document.body.appendChild(frozenTooltip);
    PageStructureAction._frozenTooltips.add(tooltipId);
    
    console.log(`Created frozen tooltip: ${tooltipId}`);
  }
  
  // Remove a specific frozen tooltip
  static _removeFrozenTooltip(tooltipId) {
    const tooltip = document.getElementById(tooltipId);
    if (tooltip) {
      document.body.removeChild(tooltip);
      PageStructureAction._frozenTooltips.delete(tooltipId);
      console.log(`Removed frozen tooltip: ${tooltipId}`);
    }
  }
  
  // Remove all frozen tooltips
  static _removeAllFrozenTooltips() {
    PageStructureAction._frozenTooltips.forEach(tooltipId => {
      const tooltip = document.getElementById(tooltipId);
      if (tooltip) {
        try {
          document.body.removeChild(tooltip);
        } catch (e) {
          // Element might already be removed
        }
      }
    });
    PageStructureAction._frozenTooltips.clear();
    console.log('Removed all frozen tooltips');
  }
  
  // Make element draggable
  static _makeDraggable(element) {
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
    
    element.onmousedown = dragMouseDown;
    
    function dragMouseDown(e) {
      e = e || window.event;
      e.preventDefault();
      pos3 = e.clientX;
      pos4 = e.clientY;
      document.onmouseup = closeDragElement;
      document.onmousemove = elementDrag;
    }
    
    function elementDrag(e) {
      e = e || window.event;
      e.preventDefault();
      pos1 = pos3 - e.clientX;
      pos2 = pos4 - e.clientY;
      pos3 = e.clientX;
      pos4 = e.clientY;
      
      const newTop = element.offsetTop - pos2;
      const newLeft = element.offsetLeft - pos1;
      
      // Keep within page bounds (considering scroll position for absolute positioning)
      const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
      const scrollY = window.pageYOffset || document.documentElement.scrollTop;
      const docWidth = Math.max(document.documentElement.scrollWidth, window.innerWidth);
      const docHeight = Math.max(document.documentElement.scrollHeight, window.innerHeight);
      
      const minLeft = scrollX;
      const minTop = scrollY;
      const maxLeft = Math.min(docWidth - element.offsetWidth, scrollX + window.innerWidth - element.offsetWidth);
      const maxTop = Math.min(docHeight - element.offsetHeight, scrollY + window.innerHeight - element.offsetHeight);
      
      element.style.top = Math.max(minTop, Math.min(newTop, maxTop)) + "px";
      element.style.left = Math.max(minLeft, Math.min(newLeft, maxLeft)) + "px";
    }
    
    function closeDragElement() {
      document.onmouseup = null;
      document.onmousemove = null;
    }
  }
  
  // Remove hover inspector functionality
  static _removeHoverInspector() {
    // Remove event listeners
    if (PageStructureAction._hoverInspectorListeners.mouseover) {
      document.removeEventListener('mouseover', PageStructureAction._hoverInspectorListeners.mouseover, true);
      PageStructureAction._hoverInspectorListeners.mouseover = null;
    }
    if (PageStructureAction._hoverInspectorListeners.mousemove) {
      document.removeEventListener('mousemove', PageStructureAction._hoverInspectorListeners.mousemove, true);
      PageStructureAction._hoverInspectorListeners.mousemove = null;
    }
    if (PageStructureAction._hoverInspectorListeners.mouseout) {
      document.removeEventListener('mouseout', PageStructureAction._hoverInspectorListeners.mouseout, true);
      PageStructureAction._hoverInspectorListeners.mouseout = null;
    }
    if (PageStructureAction._hoverInspectorListeners.click) {
      document.removeEventListener('click', PageStructureAction._hoverInspectorListeners.click, true);
      PageStructureAction._hoverInspectorListeners.click = null;
    }
    if (PageStructureAction._hoverInspectorListeners.contextmenu) {
      document.removeEventListener('contextmenu', PageStructureAction._hoverInspectorListeners.contextmenu, true);
      PageStructureAction._hoverInspectorListeners.contextmenu = null;
    }
    
    // Remove tooltip element
    if (PageStructureAction._hoverInspectorTooltip) {
      try {
        document.body.removeChild(PageStructureAction._hoverInspectorTooltip);
      } catch (e) {
        // Element might already be removed
      }
      PageStructureAction._hoverInspectorTooltip = null;
    }
    
    // Remove all frozen tooltips
    PageStructureAction._removeAllFrozenTooltips();
    
    // Reset current hovered element outline if needed
    if (PageStructureAction._currentHoveredElement) {
      const hasXrayOutline = PageStructureAction._currentHoveredElement.style.outline && 
                            PageStructureAction._currentHoveredElement.style.outline.includes('#f00');
      if (!hasXrayOutline) {
        PageStructureAction._currentHoveredElement.style.outline = PageStructureAction._originalOutline;
      }
      PageStructureAction._currentHoveredElement = null;
    }
    
    console.log('Hover inspector removed for Page Structure X-ray');
  }
  
  // Add escape key listener
  static _addEscapeKeyListener() {
    // Remove any existing listener first to avoid duplicates
    PageStructureAction._removeEscapeKeyListener();
    
    // Create the event listener function
    PageStructureAction._escapeKeyListener = function(event) {
      if (event.key === 'Escape' || event.keyCode === 27) {
        console.log('Escape key pressed - resetting Page Structure X-ray');
        PageStructureAction._executeXRayReset();
        event.preventDefault();
        event.stopPropagation();
      }
    };
    
    // Add the event listener
    document.addEventListener('keydown', PageStructureAction._escapeKeyListener, true);
    console.log('Escape key listener added for Page Structure X-ray');
  }
  
  // Remove escape key listener
  static _removeEscapeKeyListener() {
    if (PageStructureAction._escapeKeyListener) {
      document.removeEventListener('keydown', PageStructureAction._escapeKeyListener, true);
      PageStructureAction._escapeKeyListener = null;
      console.log('Escape key listener removed for Page Structure X-ray');
    }
  }
}

// Make it available globally
if (typeof window !== 'undefined') {
  window.PageStructureAction = PageStructureAction;
} 