/**
 * Screen Reader Simulation Action
 * Simulates how a screen reader would interpret the current webpage
 */
class ScreenReaderSimulationAction {
  
  static execute() {
    try {
      console.log('Screen Reader Simulation activated');
      
      let showElementRole = false;
      let removeAllExistingStyling = false;
      
      // Ask user preferences
      if (confirm("Do you want to include roles for interactive elements?\n[Only links and buttons included at present]\n\nOK - Include roles\nCancel - Leave them out")) {
        showElementRole = true;
      }
      
      if (confirm("Do you want to strip all existing styles?\n[This may reveal content/sections that are currently NOT visible to screen reader users and would be dynamically show/hidden]\n\nOK - Strip styles\nCancel - Leave as-is")) {
        removeAllExistingStyling = true;
      }
      
      // Apply element role styling if requested
      if (showElementRole) {
        document.querySelector("body").classList.add("append-element-role");
      }
      
      // Execute all simulation functions
      this.removeAllContentHiddenToAT();
      this.replaceAriaLabelledByElsWithText();
      this.replaceAriaLabelsWithText();
      this.removeDecorativeIMGs();
      this.removeDecorativeSVGs();
      this.identifyIMGsWithoutAlts();
      this.replaceIMGsWithAltsWithText();
      this.identifyAllVisualllyHiddenElements();
      this.findAllVisuallyHiddenText();
      
      if (removeAllExistingStyling) {
        this.voidStylesheets(document);
      }
      
      this.restrictImageHeights(document);
      this.addAppStyles();
      this.voidStylesheetsInIframes();
      this.addImageIconsToSwappedImageText();
      
      console.log('Screen Reader Simulation applied successfully');
      
      return { 
        success: true, 
        message: 'Screen Reader Simulation applied successfully'
      };
    } catch (error) {
      console.error('Error executing Screen Reader Simulation:', error);
      alert("Error applying Screen Reader Simulation: " + error.message);
      return { error: error.message };
    }
  }
  
  static reset() {
    try {
      // Reload the page to reset all changes
      window.location.reload();
      console.log('Screen Reader Simulation reset (page reloaded)');
    } catch (error) {
      console.error('Error resetting Screen Reader Simulation:', error);
    }
  }
  
  // Helper function to insert element after another
  static insertAfter(newElement, referenceElement) {
    referenceElement.parentNode.insertBefore(newElement, referenceElement.nextSibling);
  }
  
  // Remove all stylesheets
  static voidStylesheets(doc) {
    for (let i = 0; i < doc.styleSheets.length; i++) {
      doc.styleSheets.item(i).disabled = true;
    }
    
    const elements = doc.getElementsByTagName("*");
    for (let i = 0; i < elements.length; i++) {
      elements[i].style.cssText = "";
    }
  }
  
  // Restrict image heights for better readability
  static restrictImageHeights(doc) {
    const style = doc.createElement("style");
    doc.head.appendChild(style);
    const sheet = style.sheet;
    
    sheet.insertRule("img,figure {max-height:200px!important;width:auto!important;}", 0);
    sheet.insertRule("svg {max-height:40px!important;width:auto!important;}", 0);
    sheet.insertRule("video {max-width:500px!important;height:auto!important;}", 0);
  }
  
  // Add application-specific styles
  static addAppStyles() {
    const style = document.createElement("style");
    document.head.appendChild(style);
    const sheet = style.sheet;
    
    sheet.insertRule("* {font-size:20px}", 0);
    sheet.insertRule(".visually-hidden-revealed {outline:3px dashed purple;}", 0);
    sheet.insertRule(".img-warning {outline:3px dashed red;}", 0);
    sheet.insertRule(".swapped-text {outline:3px dashed green;background:#dbefdb;padding:3px;line-height:1.5;}", 0);
    sheet.insertRule(".img-type-indicator {display:inline-block;margin-left:5px}", 0);
    sheet.insertRule("table,th,td {outline:1px dotted black;}", 0);
    sheet.insertRule("th,td {padding:3px;}", 0);
    sheet.insertRule("button,[role=button] {padding:3px 20px;background:#dedede;border:3px solid black;display:inline-block;text-align:center;border-radius:5px;}", 0);
    sheet.insertRule("a,[role=link] {text-decoration:underline;color:blue;font-weight:normal;}", 0);
    sheet.insertRule('.append-element-role a:after,.append-element-role [role=link]:after {display:inline-block;margin-left:5px;content: "LINK"}', 0);
    sheet.insertRule('.append-element-role button:after,.append-element-role [role=button]:after {display:inline-block;margin-left:5px;content: "BUTTON"}', 0);
  }
  
  // Identify and reveal visually hidden elements
  static identifyAllVisualllyHiddenElements() {
    Array.from(document.querySelectorAll(".visually-hidden")).forEach(element => {
      const span = document.createElement("span");
      span.textContent = element.textContent;
      span.classList.add("visually-hidden-revealed");
      this.insertAfter(span, element);
      element.remove();
    });
  }
  
  // Find elements that are visually hidden using CSS
  static findAllVisuallyHiddenText() {
    Array.from(document.querySelectorAll("*")).forEach(element => {
      const cs = getComputedStyle(element);
      let hasClip = false, hasClipPath = false, hasHeight = false, hasOverflowX = false;
      let hasOverflowY = false, hasPosition = false, hasWhiteSpace = false, hasWidth = false;
      
      for (let i = 0; i < cs.length; i++) {
        const property = cs[i];
        const value = cs.getPropertyValue(property);
        
        if (property === "clip" && value === "rect(1px, 1px, 1px, 1px)") hasClip = true;
        if (property === "clip-path" && value === "inset(100%)") hasClipPath = true;
        if (property === "height" && value === "1px") hasHeight = true;
        if (property === "overflow-x" && value === "hidden") hasOverflowX = true;
        if (property === "overflow-y" && value === "hidden") hasOverflowY = true;
        if (property === "position" && value === "absolute") hasPosition = true;
        if (property === "white-space" && value === "nowrap") hasWhiteSpace = true;
        if (property === "width" && value === "1px") hasWidth = true;
      }
      
      if (hasClip && hasClipPath && hasHeight && hasOverflowX && hasOverflowY && hasPosition && hasWhiteSpace && hasWidth) {
        element.classList.add("visually-hidden");
      }
    });
  }
  
  // Handle iframes
  static voidStylesheetsInIframes() {
    Array.from(document.querySelectorAll("iframe")).forEach(iframe => {
      try {
        this.voidStylesheets(iframe.contentWindow.document);
        this.restrictImageHeights(iframe.contentWindow.document);
      } catch (error) {
        // Cross-origin iframe access blocked
        console.log('Cannot access iframe content (cross-origin)');
      }
    });
  }
  
  // Remove decorative images
  static removeDecorativeIMGs() {
    Array.from(document.querySelectorAll('img[alt=""]:not(img[aria-label])')).forEach(img => {
      img.remove();
    });
  }
  
  // Remove decorative SVGs
  static removeDecorativeSVGs() {
    Array.from(document.querySelectorAll("svg:not([role=img][aria-label],[role=img][aria-labelledby])")).forEach(svg => {
      svg.remove();
    });
  }
  
  // Identify images without alt text
  static identifyIMGsWithoutAlts() {
    Array.from(document.querySelectorAll("img:not(img[alt])")).forEach(img => {
      const span = document.createElement("span");
      span.textContent = img.getAttribute("src");
      span.classList.add("img-warning");
      this.insertAfter(span, img);
      img.remove();
    });
  }
  
  // Replace images with their alt text
  static replaceIMGsWithAltsWithText() {
    Array.from(document.querySelectorAll("img[alt]")).forEach(img => {
      const span = document.createElement("span");
      span.textContent = img.getAttribute("alt");
      span.classList.add("swapped-text");
      span.classList.add("img");
      this.insertAfter(span, img);
      img.remove();
    });
  }
  
  // Replace aria-labels with text
  static replaceAriaLabelsWithText() {
    Array.from(document.querySelectorAll("a[aria-label],[role=link][aria-label],button[aria-label],[role=button][aria-label]")).forEach(element => {
      element.textContent = element.getAttribute("aria-label");
      element.classList.add("swapped-text");
      element.classList.add("svg");
    });
  }
  
  // Add image icons to swapped text
  static addImageIconsToSwappedImageText() {
    Array.from(document.querySelectorAll(".swapped-text.img")).forEach(element => {
      const span = document.createElement("span");
      span.textContent = "🏞️";
      span.setAttribute("aria-hidden", "true");
      span.classList.add("img-type-indicator");
      span.setAttribute("title", "Alternative text from IMG element");
      element.appendChild(span);
    });
    
    Array.from(document.querySelectorAll(".swapped-text.svg")).forEach(element => {
      const span = document.createElement("span");
      span.textContent = "✳️";
      span.setAttribute("aria-hidden", "true");
      span.classList.add("img-type-indicator");
      span.setAttribute("title", "Alternative text from SVG element");
      element.appendChild(span);
    });
  }
  
  // Replace aria-labelledby elements with text
  static replaceAriaLabelledByElsWithText() {
    const labelledByElements = document.querySelectorAll("[aria-labelledby]");
    const focusableElements = document.querySelectorAll('a[href],button,select,input:not([type="hidden"]),textarea,summary,area,[tabindex]:not([tabindex^="-1"]),[contenteditable]:not([contenteditable="false"])');
    
    Array.from(labelledByElements).forEach(element => {
      let isFocusable = false;
      
      Array.from(focusableElements).forEach(focusable => {
        if (element === focusable) {
          isFocusable = true;
        }
      });
      
      if (isFocusable) {
        let labelText = "";
        const labelIds = element.getAttribute("aria-labelledby").split(" ");
        
        Array.from(labelIds).forEach(id => {
          const labelElement = document.querySelector("#" + id);
          if (labelElement) {
            const text = labelElement.textContent;
            if (text) {
              labelText += text + " ";
            }
          }
        });
        
        labelText = labelText.trim();
        element.textContent = labelText;
        element.classList.add("swapped-text");
        element.removeAttribute("aria-labelledby");
      }
    });
  }
  
  // Remove content hidden from assistive technology
  static removeAllContentHiddenToAT() {
    Array.from(document.querySelectorAll("[aria-hidden]")).forEach(element => {
      element.remove();
    });
  }
}

// Make it available globally
if (typeof window !== 'undefined') {
  window.ScreenReaderSimulationAction = ScreenReaderSimulationAction;
} 