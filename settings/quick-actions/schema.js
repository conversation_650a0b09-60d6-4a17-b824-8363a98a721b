// Schema Action - Extract structured data (JSON-LD) from web pages
class SchemaAction {
    static execute() {
        try {
            // Inject the Schema extraction script into the active tab
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                target: {tabId: tabs[0].id},
                func: function() {
                    // The main schema extraction function to inject
                    (function() {
                        var structuredData = document.querySelectorAll('script[type="application/ld+json"]');
                        
                        if (structuredData.length === 0) {
                            alert("No structured data (JSON-LD) found on this page.");
                            return;
                        }
                        
                        var jsonData = [];
                        structuredData.forEach(function(script) {
                            try {
                                var data = JSON.parse(script.textContent);
                                jsonData.push(data);
                            } catch (error) {
                                console.error("Error parsing JSON-LD:", error);
                            }
                        });
                        
                        if (jsonData.length === 0) {
                            alert("No valid structured data (JSON-LD) found on this page.");
                            return;
                        }
                        
                        var info = JSON.stringify(jsonData, null, 2);
                            
                        // Create modal with dark theme styling
                        var modal = document.createElement('div');
                        modal.className = 'schema-modal';
                        modal.style.position = 'fixed';
                        modal.style.top = '50%';
                        modal.style.left = '50%';
                        modal.style.transform = 'translate(-50%, -50%)';
                        modal.style.padding = '20px';
                        modal.style.backgroundColor = '#0f0f0f';
                        modal.style.color = '#e5e5e5';
                        modal.style.border = '1px solid #2a2a2a';
                        modal.style.borderRadius = '12px';
                        modal.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.4)';
                        modal.style.zIndex = '9999999';
                        modal.style.width = '80%';
                        modal.style.height = '80%';
                        modal.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif';
                        modal.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                        
                        // Fullscreen state
                        var isFullscreen = false;
                        
                        // Create header container
                        var headerContainer = document.createElement('div');
                        headerContainer.style.display = 'flex';
                        headerContainer.style.justifyContent = 'space-between';
                        headerContainer.style.alignItems = 'center';
                        headerContainer.style.marginBottom = '16px';
                        
                        // Create header
                        var header = document.createElement('h2');
                        header.textContent = 'Structured Data (JSON-LD) - Schema Extractor';
                        header.style.margin = '0';
                        header.style.color = '#ffffff';
                        header.style.fontSize = '18px';
                        header.style.fontWeight = '700';
                        header.style.letterSpacing = '-0.5px';
                        headerContainer.appendChild(header);
                        
                        // Create fullscreen button
                        var fullscreenButton = document.createElement('button');
                        fullscreenButton.innerHTML = '⛶';
                        fullscreenButton.title = 'Toggle Fullscreen';
                        fullscreenButton.style.padding = '8px 12px';
                        fullscreenButton.style.background = 'linear-gradient(135deg, #7c3aed, #a855f7)';
                        fullscreenButton.style.color = '#ffffff';
                        fullscreenButton.style.border = '1px solid #8b5cf6';
                        fullscreenButton.style.borderRadius = '8px';
                        fullscreenButton.style.cursor = 'pointer';
                        fullscreenButton.style.fontSize = '16px';
                        fullscreenButton.style.fontWeight = '600';
                        fullscreenButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                        fullscreenButton.style.minWidth = '44px';
                        fullscreenButton.style.height = '36px';
                        
                        // Fullscreen toggle functionality
                        function toggleFullscreen() {
                            if (isFullscreen) {
                                // Return to normal modal
                                modal.style.width = '80%';
                                modal.style.height = '80%';
                                modal.style.top = '50%';
                                modal.style.left = '50%';
                                modal.style.transform = 'translate(-50%, -50%)';
                                modal.style.borderRadius = '12px';
                                fullscreenButton.innerHTML = '⛶';
                                fullscreenButton.title = 'Toggle Fullscreen';
                            } else {
                                // Switch to fullscreen
                                modal.style.width = '100vw';
                                modal.style.height = '100vh';
                                modal.style.top = '0';
                                modal.style.left = '0';
                                modal.style.transform = 'none';
                                modal.style.borderRadius = '0';
                                fullscreenButton.innerHTML = '⇱';
                                fullscreenButton.title = 'Exit Fullscreen';
                            }
                            isFullscreen = !isFullscreen;
                            
                            // Resize canvas if in visualization mode
                            setTimeout(function() {
                                if (window.vizResize) {
                                    window.vizResize();
                                }
                            }, 350); // Wait for transition
                        }
                        
                        fullscreenButton.onclick = toggleFullscreen;
                        
                        // Add hover effects to fullscreen button
                        fullscreenButton.addEventListener('mouseenter', function() {
                            this.style.transform = 'translateY(-1px)';
                            this.style.boxShadow = '0 8px 24px rgba(139, 92, 246, 0.3)';
                        });
                        
                        fullscreenButton.addEventListener('mouseleave', function() {
                            this.style.transform = 'translateY(0)';
                            this.style.boxShadow = 'none';
                        });
                        
                        headerContainer.appendChild(fullscreenButton);
                        modal.appendChild(headerContainer);
                        
                        // Create textarea
                        var textArea = document.createElement("textarea");
                        textArea.value = info;
                        textArea.style.width = '100%';
                        textArea.style.height = 'calc(100% - 96px)'; // Adjusted for header container
                        textArea.style.resize = 'none';
                        textArea.style.backgroundColor = '#1a1a1a';
                        textArea.style.color = '#e5e5e5';
                        textArea.style.border = '1px solid #333333';
                        textArea.style.borderRadius = '8px';
                        textArea.style.padding = '12px';
                        textArea.style.fontSize = '13px';
                        textArea.style.fontFamily = 'monospace';
                        textArea.style.lineHeight = '1.4';
                        modal.appendChild(textArea);
                        
                        // Create visualization container (initially hidden)
                        var vizContainer = document.createElement('div');
                        vizContainer.style.width = '100%';
                        vizContainer.style.height = 'calc(100% - 96px)'; // Adjusted for header container
                        vizContainer.style.display = 'none';
                        vizContainer.style.position = 'relative';
                        
                        // Create controls panel
                        var controlsPanel = document.createElement('div');
                        controlsPanel.style.width = '220px';
                        controlsPanel.style.height = '100%';
                        controlsPanel.style.backgroundColor = '#1a1a1a';
                        controlsPanel.style.border = '1px solid #333333';
                        controlsPanel.style.borderRadius = '8px';
                        controlsPanel.style.padding = '16px';
                        controlsPanel.style.position = 'absolute';
                        controlsPanel.style.left = '0';
                        controlsPanel.style.top = '0';
                        controlsPanel.style.overflowY = 'auto';
                        controlsPanel.style.zIndex = '10';
                        
                        // Create canvas for visualization
                        var canvas = document.createElement('canvas');
                        canvas.style.width = 'calc(100% - 240px)';
                        canvas.style.height = '100%';
                        canvas.style.backgroundColor = '#000';
                        canvas.style.border = '1px solid #333333';
                        canvas.style.borderRadius = '8px';
                        canvas.style.position = 'absolute';
                        canvas.style.right = '0';
                        canvas.style.top = '0';
                        canvas.style.cursor = 'grab';
                        
                        vizContainer.appendChild(controlsPanel);
                        vizContainer.appendChild(canvas);
                        modal.appendChild(vizContainer);
                        
                        // Create button container
                        var buttonContainer = document.createElement('div');
                        buttonContainer.style.display = 'flex';
                        buttonContainer.style.gap = '8px';
                        buttonContainer.style.marginTop = '12px';
                        buttonContainer.style.justifyContent = 'flex-end';
                        
                        // Check if visualizer is enabled and add toggle button
                        var currentView = 'json'; // Track current view state
                        var toggleButton = null;
                        
                        // Get visualizer setting from storage
                        chrome.storage.sync.get(['schemaVisualizerEnabled'], (result) => {
                            // Default to enabled if undefined, respect explicit false setting
                            const isEnabled = result.schemaVisualizerEnabled !== false;
                            if (isEnabled) {
                                // Create toggle button
                                toggleButton = document.createElement("button");
                                toggleButton.textContent = "View Visualization";
                                toggleButton.style.padding = '10px 16px';
                                toggleButton.style.background = 'linear-gradient(135deg, #7c3aed, #a855f7)';
                                toggleButton.style.color = '#ffffff';
                                toggleButton.style.border = '1px solid #8b5cf6';
                                toggleButton.style.borderRadius = '8px';
                                toggleButton.style.cursor = 'pointer';
                                toggleButton.style.fontSize = '13px';
                                toggleButton.style.fontWeight = '600';
                                toggleButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                                toggleButton.style.marginRight = '8px';
                                
                                toggleButton.onclick = function() {
                                    if (currentView === 'json') {
                                        // Switch to visualization
                                        textArea.style.display = 'none';
                                        vizContainer.style.display = 'block';
                                        toggleButton.textContent = 'View JSON';
                                        currentView = 'visualization';
                                        
                                        // Initialize controls and render visualization
                                        initializeControls();
                                        renderSchemaVisualization(canvas, jsonData);
                                    } else {
                                        // Switch to JSON
                                        textArea.style.display = 'block';
                                        vizContainer.style.display = 'none';
                                        toggleButton.textContent = 'View Visualization';
                                        currentView = 'json';
                                    }
                                };
                                
                                buttonContainer.appendChild(toggleButton);
                            }
                        });
                        
                        // Initialize controls panel function
                        function initializeControls() {
                            controlsPanel.innerHTML = 
                                '<div style="color: #ffffff; font-weight: 700; font-size: 16px; margin-bottom: 16px; text-align: center;">Controls</div>' +
                                
                                '<div style="margin-bottom: 16px;">' +
                                    '<label style="color: #d1d5db; font-size: 12px; font-weight: 600; margin-bottom: 4px; display: block;">Link Distance</label>' +
                                    '<input type="range" id="linkDistance" min="50" max="500" value="150" style="width: 100%; margin-bottom: 4px; accent-color: #7C3AED;">' +
                                    '<span id="linkDistanceValue" style="color: #a1a1aa; font-size: 10px;">150px</span>' +
                                '</div>' +
                                
                                '<div style="margin-bottom: 16px;">' +
                                    '<label style="color: #d1d5db; font-size: 12px; font-weight: 600; margin-bottom: 4px; display: block;">Node Size</label>' +
                                    '<input type="range" id="nodeSize" min="15" max="40" value="25" style="width: 100%; margin-bottom: 4px; accent-color: #7C3AED;">' +
                                    '<span id="nodeSizeValue" style="color: #a1a1aa; font-size: 10px;">25px</span>' +
                                '</div>' +
                                
                                '<div style="margin-bottom: 16px;">' +
                                    '<label style="color: #d1d5db; font-size: 12px; font-weight: 600; margin-bottom: 4px; display: block;">Charge Strength</label>' +
                                    '<input type="range" id="chargeStrength" min="-500" max="-100" value="-300" style="width: 100%; margin-bottom: 4px; accent-color: #7C3AED;">' +
                                    '<span id="chargeStrengthValue" style="color: #a1a1aa; font-size: 10px;">-300</span>' +
                                '</div>' +
                                
                                '<div style="margin-bottom: 16px;">' +
                                    '<label style="color: #d1d5db; font-size: 12px; font-weight: 600; margin-bottom: 4px; display: block;">Font Size</label>' +
                                    '<input type="range" id="fontSize" min="8" max="16" value="10" style="width: 100%; margin-bottom: 4px; accent-color: #7C3AED;">' +
                                    '<span id="fontSizeValue" style="color: #a1a1aa; font-size: 10px;">10px</span>' +
                                '</div>' +
                                
                                '<div style="margin-bottom: 20px;">' +
                                    '<label style="color: #d1d5db; font-size: 12px; font-weight: 600; margin-bottom: 4px; display: block;">Max Depth</label>' +
                                    '<input type="range" id="maxDepth" min="1" max="5" value="1" style="width: 100%; margin-bottom: 4px; accent-color: #7C3AED;">' +
                                    '<span id="maxDepthValue" style="color: #a1a1aa; font-size: 10px;">1 levels</span>' +
                                '</div>' +
                                
                                '<div style="border-top: 1px solid #333; padding-top: 16px; margin-bottom: 16px;">' +
                                    '<div style="color: #ffffff; font-weight: 600; font-size: 14px; margin-bottom: 8px;">Zoom & Pan</div>' +
                                    '<div id="zoomLevel" style="color: #a1a1aa; font-size: 10px; margin-bottom: 8px;">Zoom: 100%</div>' +
                                    '<button id="resetView" style="width: 100%; padding: 8px; background: #262626; color: #ffffff; border: 1px solid #333333; border-radius: 6px; cursor: pointer; font-size: 11px; font-weight: 600;">Reset View</button>' +
                                '</div>' +
                                
                                '<div style="border-top: 1px solid #333; padding-top: 16px; margin-bottom: 16px;">' +
                                    '<div style="color: #ffffff; font-weight: 600; font-size: 14px; margin-bottom: 8px;">Export</div>' +
                                    '<button id="exportPNG" style="width: 100%; padding: 8px; margin-bottom: 8px; background: linear-gradient(135deg, #7c3aed, #a855f7); color: #ffffff; border: 1px solid #8b5cf6; border-radius: 6px; cursor: pointer; font-size: 11px; font-weight: 600;">Export PNG</button>' +
                                    '<button id="exportSVG" style="width: 100%; padding: 8px; background: #262626; color: #ffffff; border: 1px solid #333333; border-radius: 6px; cursor: pointer; font-size: 11px; font-weight: 600;">Export SVG</button>' +
                                '</div>' +
                                
                                '<div style="border-top: 1px solid #333; padding-top: 16px;">' +
                                    '<div style="color: #ffffff; font-weight: 600; font-size: 14px; margin-bottom: 8px;">Selected Node</div>' +
                                    '<div id="nodeInfo" style="background: #0a0a0a; border: 1px solid #333; border-radius: 6px; padding: 8px; font-size: 12px; color: #d1d5db;">' +
                                        'Click a node to view details<br><br>' +
                                        '<div style="font-size: 10px; color: #6b7280; margin-top: 8px;">' +
                                        '• Mouse wheel: Zoom<br>' +
                                        '• Drag empty area: Pan<br>' +
                                        '• Drag nodes: Reposition' +
                                        '</div>' +
                                    '</div>' +
                                '</div>';
                            
                            // Add event listeners for controls
                            function setupControl(id, property, suffix) {
                                suffix = suffix || 'px';
                                var slider = document.getElementById(id);
                                var valueSpan = document.getElementById(id + 'Value');
                                
                                if (slider && valueSpan) {
                                    slider.addEventListener('input', function() {
                                        var value = parseFloat(this.value);
                                        valueSpan.textContent = value + suffix;
                                        
                                        if (window.vizControls) {
                                            var params = {};
                                            params[property] = value;
                                            window.vizControls.updateParams(params);
                                        }
                                    });
                                }
                            }
                            
                            setupControl('linkDistance', 'linkDistance', 'px');
                            setupControl('nodeSize', 'nodeSize', 'px');
                            setupControl('chargeStrength', 'chargeStrength', '');
                            setupControl('fontSize', 'fontSize', 'px');
                            setupControl('maxDepth', 'maxDepth', ' levels');
                            
                            // Reset View button
                            var resetViewButton = document.getElementById('resetView');
                            if (resetViewButton) {
                                resetViewButton.addEventListener('click', function() {
                                    if (window.vizControls) {
                                        window.vizControls.resetView();
                                    }
                                });
                            }
                            
                            // Export buttons
                            var exportPNG = document.getElementById('exportPNG');
                            var exportSVG = document.getElementById('exportSVG');
                            
                            if (exportPNG) {
                                exportPNG.addEventListener('click', function() {
                                    if (window.vizControls) {
                                        window.vizControls.exportPNG();
                                    }
                                });
                            }
                            
                            if (exportSVG) {
                                exportSVG.addEventListener('click', function() {
                                    if (window.vizControls) {
                                        window.vizControls.exportSVG();
                                    }
                                });
                            }
                        }
                        
                        // Enhanced visualization engine with zoom/pan support
                        function renderSchemaVisualization(canvas, schemaData) {
                            var ctx = canvas.getContext('2d');
                            var width = canvas.clientWidth;
                            var height = canvas.clientHeight;
                            canvas.width = width;
                            canvas.height = height;
                            
                            // Enhanced visualization state with zoom/pan
                            var vizState = {
                                nodes: [],
                                links: [],
                                selectedNode: null,
                                isDragging: false,
                                dragNode: null,
                                isPanning: false,
                                panStart: { x: 0, y: 0 },
                                zoom: 1,
                                panX: 0,
                                panY: 0,
                                animationId: null
                            };
                            
                            // Control parameters with updated defaults
                            var params = {
                                linkDistance: 150,
                                nodeSize: 25,
                                chargeStrength: -300,
                                fontSize: 10,
                                maxDepth: 1,
                                minZoom: 0.1,
                                maxZoom: 5
                            };
                            
                            // Coordinate transformation functions
                            function transformPoint(x, y) {
                                return {
                                    x: (x + vizState.panX) * vizState.zoom + width / 2,
                                    y: (y + vizState.panY) * vizState.zoom + height / 2
                                };
                            }
                            
                            function inverseTransformPoint(screenX, screenY) {
                                return {
                                    x: (screenX - width / 2) / vizState.zoom - vizState.panX,
                                    y: (screenY - height / 2) / vizState.zoom - vizState.panY
                                };
                            }
                            
                            // Update zoom level display
                            function updateZoomDisplay() {
                                var zoomDisplay = document.getElementById('zoomLevel');
                                if (zoomDisplay) {
                                    zoomDisplay.textContent = 'Zoom: ' + Math.round(vizState.zoom * 100) + '%';
                                }
                            }
                            
                            // Reset view to fit all nodes
                            function resetView() {
                                if (vizState.nodes.length === 0) return;
                                
                                // Calculate bounds of all nodes
                                var minX = Math.min.apply(Math, vizState.nodes.map(function(n) { return n.x; }));
                                var maxX = Math.max.apply(Math, vizState.nodes.map(function(n) { return n.x; }));
                                var minY = Math.min.apply(Math, vizState.nodes.map(function(n) { return n.y; }));
                                var maxY = Math.max.apply(Math, vizState.nodes.map(function(n) { return n.y; }));
                                
                                var boundsWidth = maxX - minX + 100; // Add padding
                                var boundsHeight = maxY - minY + 100;
                                
                                // Calculate zoom to fit
                                var zoomX = (width * 0.8) / boundsWidth;
                                var zoomY = (height * 0.8) / boundsHeight;
                                vizState.zoom = Math.min(zoomX, zoomY, 2); // Cap at 2x zoom
                                
                                // Center the view
                                var centerX = (minX + maxX) / 2;
                                var centerY = (minY + maxY) / 2;
                                vizState.panX = -centerX;
                                vizState.panY = -centerY;
                                
                                updateZoomDisplay();
                            }
                            
                            // Canvas resize function
                            function resizeCanvas() {
                                width = canvas.clientWidth;
                                height = canvas.clientHeight;
                                canvas.width = width;
                                canvas.height = height;
                            }
                            
                            // Expose resize function globally
                            window.vizResize = resizeCanvas;
                            
                            // Process schema data into nodes and links
                            function processSchemaData(data) {
                                var nodeMap = new Map();
                                var links = [];
                                var nodeId = 0;
                                
                                function addNode(obj, parentId, depth) {
                                    if (typeof parentId === 'undefined') parentId = null;
                                    if (typeof depth === 'undefined') depth = 0;
                                    if (depth > params.maxDepth) return null;
                                    
                                    var id = nodeId++;
                                    var type = obj['@type'] || 'Unknown';
                                    var name = obj.name || obj.title || type;
                                    
                                    var node = {
                                        id: id,
                                        type: type,
                                        name: name,
                                        data: obj,
                                        x: Math.random() * 400 - 200, // Center around origin
                                        y: Math.random() * 400 - 200,
                                        vx: 0,
                                        vy: 0,
                                        radius: params.nodeSize,
                                        color: getSchemaColor(type),
                                        depth: depth
                                    };
                                    
                                    nodeMap.set(id, node);
                                    
                                    if (parentId !== null) {
                                        links.push({
                                            source: parentId,
                                            target: id,
                                            distance: params.linkDistance
                                        });
                                    }
                                    
                                    // Process nested objects
                                    Object.keys(obj).forEach(function(key) {
                                        var value = obj[key];
                                        if (value && typeof value === 'object' && key !== '@context') {
                                            if (Array.isArray(value)) {
                                                value.forEach(function(item) {
                                                    if (item && typeof item === 'object' && item['@type']) {
                                                        addNode(item, id, depth + 1);
                                                    }
                                                });
                                            } else if (value['@type']) {
                                                addNode(value, id, depth + 1);
                                            }
                                        }
                                    });
                                    
                                    return node;
                                }
                                
                                data.forEach(function(schema) {
                                    addNode(schema);
                                });
                                
                                return {
                                    nodes: Array.from(nodeMap.values()),
                                    links: links
                                };
                            }
                            
                            // Force simulation
                            function applyForces() {
                                var nodes = vizState.nodes;
                                var links = vizState.links;
                                
                                // Reset forces
                                nodes.forEach(function(node) {
                                    node.vx = 0;
                                    node.vy = 0;
                                });
                                
                                // Charge force (repulsion)
                                for (var i = 0; i < nodes.length; i++) {
                                    for (var j = i + 1; j < nodes.length; j++) {
                                        var a = nodes[i];
                                        var b = nodes[j];
                                        var dx = b.x - a.x;
                                        var dy = b.y - a.y;
                                        var distance = Math.sqrt(dx * dx + dy * dy);
                                        
                                        if (distance > 0) {
                                            var force = params.chargeStrength / (distance * distance);
                                            var fx = force * dx / distance;
                                            var fy = force * dy / distance;
                                            
                                            a.vx -= fx;
                                            a.vy -= fy;
                                            b.vx += fx;
                                            b.vy += fy;
                                        }
                                    }
                                }
                                
                                // Link force (attraction)
                                links.forEach(function(link) {
                                    var source = nodes[link.source];
                                    var target = nodes[link.target];
                                    
                                    if (source && target) {
                                        var dx = target.x - source.x;
                                        var dy = target.y - source.y;
                                        var distance = Math.sqrt(dx * dx + dy * dy);
                                        
                                        if (distance > 0) {
                                            var force = (distance - link.distance) * 0.1;
                                            var fx = force * dx / distance;
                                            var fy = force * dy / distance;
                                            
                                            source.vx += fx;
                                            source.vy += fy;
                                            target.vx -= fx;
                                            target.vy -= fy;
                                        }
                                    }
                                });
                                
                                // Apply velocity and damping
                                nodes.forEach(function(node) {
                                    if (!vizState.isDragging || vizState.dragNode !== node) {
                                        node.x += node.vx * 0.1;
                                        node.y += node.vy * 0.1;
                                    }
                                });
                            }
                            
                            // Enhanced render function with zoom/pan
                            function render() {
                                ctx.save();
                                
                                // Clear canvas
                                ctx.fillStyle = '#000';
                                ctx.fillRect(0, 0, width, height);
                                
                                // Apply zoom and pan transformations
                                ctx.translate(width / 2, height / 2);
                                ctx.scale(vizState.zoom, vizState.zoom);
                                ctx.translate(vizState.panX, vizState.panY);
                                
                                // Draw links
                                vizState.links.forEach(function(link) {
                                    var source = vizState.nodes[link.source];
                                    var target = vizState.nodes[link.target];
                                    
                                    if (source && target) {
                                        ctx.strokeStyle = '#444';
                                        ctx.lineWidth = 1 / vizState.zoom; // Maintain consistent line width
                                        ctx.beginPath();
                                        ctx.moveTo(source.x, source.y);
                                        ctx.lineTo(target.x, target.y);
                                        ctx.stroke();
                                    }
                                });
                                
                                // Draw nodes
                                vizState.nodes.forEach(function(node) {
                                    // Node circle
                                    ctx.fillStyle = node === vizState.selectedNode ? '#ffffff' : node.color;
                                    ctx.beginPath();
                                    ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);
                                    ctx.fill();
                                    
                                    // Node border
                                    ctx.strokeStyle = node === vizState.selectedNode ? node.color : '#fff';
                                    ctx.lineWidth = 2 / vizState.zoom;
                                    ctx.stroke();
                                    
                                    // Node label
                                    ctx.fillStyle = '#fff';
                                    ctx.font = (params.fontSize / vizState.zoom) + 'px -apple-system, sans-serif';
                                    ctx.textAlign = 'center';
                                    ctx.fillText(node.type, node.x, node.y - node.radius - 8 / vizState.zoom);
                                });
                                
                                ctx.restore();
                            }
                            
                            // Animation loop
                            function animate() {
                                applyForces();
                                render();
                                vizState.animationId = requestAnimationFrame(animate);
                            }
                            
                            // Initialize visualization
                            var graphData = processSchemaData(schemaData);
                            vizState.nodes = graphData.nodes;
                            vizState.links = graphData.links;
                            
                            // Start animation
                            animate();
                            
                            // Reset view to fit nodes
                            setTimeout(function() {
                                resetView();
                            }, 100);
                            
                            // Node info display function
                            function updateNodeInfo(node) {
                                var infoPanel = document.getElementById('nodeInfo');
                                if (infoPanel) {
                                    infoPanel.innerHTML = 
                                        '<div style="color: #7C3AED; font-weight: 600; margin-bottom: 8px;">' + node.type + '</div>' +
                                        '<div style="font-size: 11px; color: #a1a1aa; margin-bottom: 4px;">Name: ' + node.name + '</div>' +
                                        '<div style="font-size: 11px; color: #a1a1aa;">Depth: ' + node.depth + '</div>' +
                                        '<div style="font-size: 10px; color: #6b7280; margin-top: 12px;">' +
                                        '• Mouse wheel: Zoom<br>' +
                                        '• Drag empty area: Pan<br>' +
                                        '• Drag nodes: Reposition' +
                                        '</div>';
                                }
                            }
                            
                            // Enhanced mouse interactions
                            function getMousePos(e) {
                                var rect = canvas.getBoundingClientRect();
                                return {
                                    x: e.clientX - rect.left,
                                    y: e.clientY - rect.top
                                };
                            }
                            
                            function getNodeAt(screenPos) {
                                var worldPos = inverseTransformPoint(screenPos.x, screenPos.y);
                                return vizState.nodes.find(function(node) {
                                    var dx = worldPos.x - node.x;
                                    var dy = worldPos.y - node.y;
                                    return dx * dx + dy * dy <= node.radius * node.radius;
                                });
                            }
                            
                            // Mouse wheel zoom
                            canvas.addEventListener('wheel', function(e) {
                                e.preventDefault();
                                
                                var mousePos = getMousePos(e);
                                var worldPosBeforeZoom = inverseTransformPoint(mousePos.x, mousePos.y);
                                
                                // Zoom in/out
                                var zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
                                var newZoom = vizState.zoom * zoomFactor;
                                
                                // Clamp zoom
                                newZoom = Math.max(params.minZoom, Math.min(params.maxZoom, newZoom));
                                
                                if (newZoom !== vizState.zoom) {
                                    vizState.zoom = newZoom;
                                    
                                    // Adjust pan to keep mouse position fixed
                                    var worldPosAfterZoom = inverseTransformPoint(mousePos.x, mousePos.y);
                                    vizState.panX += worldPosAfterZoom.x - worldPosBeforeZoom.x;
                                    vizState.panY += worldPosAfterZoom.y - worldPosBeforeZoom.y;
                                    
                                    updateZoomDisplay();
                                }
                            });
                            
                            // Mouse down - start dragging node or panning
                            canvas.addEventListener('mousedown', function(e) {
                                var pos = getMousePos(e);
                                var node = getNodeAt(pos);
                                
                                if (node) {
                                    // Start dragging node
                                    vizState.isDragging = true;
                                    vizState.dragNode = node;
                                    vizState.selectedNode = node;
                                    canvas.style.cursor = 'grabbing';
                                    updateNodeInfo(node);
                                } else {
                                    // Start panning
                                    vizState.isPanning = true;
                                    vizState.panStart = { x: pos.x, y: pos.y };
                                    canvas.style.cursor = 'grabbing';
                                }
                                
                                e.preventDefault();
                            });
                            
                            // Mouse move - drag node or pan canvas
                            canvas.addEventListener('mousemove', function(e) {
                                var pos = getMousePos(e);
                                
                                if (vizState.isDragging && vizState.dragNode) {
                                    // Drag node
                                    var worldPos = inverseTransformPoint(pos.x, pos.y);
                                    vizState.dragNode.x = worldPos.x;
                                    vizState.dragNode.y = worldPos.y;
                                } else if (vizState.isPanning) {
                                    // Pan canvas
                                    var dx = (pos.x - vizState.panStart.x) / vizState.zoom;
                                    var dy = (pos.y - vizState.panStart.y) / vizState.zoom;
                                    
                                    vizState.panX += dx;
                                    vizState.panY += dy;
                                    
                                    vizState.panStart = pos;
                                } else {
                                    // Update cursor based on hover
                                    var node = getNodeAt(pos);
                                    canvas.style.cursor = node ? 'grab' : 'grab';
                                }
                            });
                            
                            // Mouse up - stop dragging/panning
                            canvas.addEventListener('mouseup', function() {
                                vizState.isDragging = false;
                                vizState.dragNode = null;
                                vizState.isPanning = false;
                                canvas.style.cursor = 'grab';
                            });
                            
                            // Prevent context menu
                            canvas.addEventListener('contextmenu', function(e) {
                                e.preventDefault();
                            });
                            
                            // Expose controls interface
                            window.vizControls = {
                                updateParams: function(newParams) {
                                    Object.assign(params, newParams);
                                    // Update node sizes
                                    vizState.nodes.forEach(function(node) {
                                        node.radius = params.nodeSize;
                                    });
                                    // Update link distances
                                    vizState.links.forEach(function(link) {
                                        link.distance = params.linkDistance;
                                    });
                                },
                                resetView: resetView,
                                exportPNG: function() {
                                    var link = document.createElement('a');
                                    link.download = 'schema-visualization.png';
                                    link.href = canvas.toDataURL();
                                    link.click();
                                },
                                exportSVG: function() {
                                    alert('SVG export not yet implemented');
                                }
                            };
                            
                            // Initialize zoom display
                            updateZoomDisplay();
                        }
                        
                        function getSchemaColor(type) {
                            var colors = {
                                'Organization': '#7C3AED',
                                'Person': '#22c55e', 
                                'Product': '#f59e0b',
                                'Article': '#06b6d4',
                                'WebPage': '#8b5cf6',
                                'Review': '#f97316',
                                'Rating': '#84cc16',
                                'LocalBusiness': '#7C3AED',
                                'Thing': '#64748b',
                                'CreativeWork': '#06b6d4',
                                'Event': '#f97316',
                                'Place': '#22c55e',
                                'Offer': '#f59e0b',
                                'AggregateRating': '#84cc16'
                            };
                            return colors[type] || '#64748b';
                        }
                        
                        // Create copy button
                        var copyButton = document.createElement("button");
                        copyButton.textContent = "Copy to Clipboard";
                        copyButton.style.padding = '10px 16px';
                        copyButton.style.background = 'linear-gradient(135deg, #7c3aed, #a855f7)';
                        copyButton.style.color = '#ffffff';
                        copyButton.style.border = '1px solid #8b5cf6';
                        copyButton.style.borderRadius = '8px';
                        copyButton.style.cursor = 'pointer';
                        copyButton.style.fontSize = '13px';
                        copyButton.style.fontWeight = '600';
                        copyButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                        copyButton.onclick = function() {
                            textArea.select();
                            document.execCommand("copy");
                            
                            // Visual feedback
                            var originalText = copyButton.textContent;
                            copyButton.textContent = "Copied!";
                            copyButton.style.background = 'linear-gradient(135deg, #22c55e, #16a34a)';
                            copyButton.style.borderColor = '#16a34a';
                            
                            setTimeout(function() {
                                copyButton.textContent = originalText;
                                copyButton.style.background = 'linear-gradient(135deg, #7c3aed, #a855f7)';
                                copyButton.style.borderColor = '#8b5cf6';
                            }, 2000);
                        };
                        buttonContainer.appendChild(copyButton);
                        
                        // Create close button
                        var closeButton = document.createElement("button");
                        closeButton.textContent = "✕";
                        closeButton.style.padding = '10px 16px';
                        closeButton.style.backgroundColor = '#262626';
                        closeButton.style.color = '#ffffff';
                        closeButton.style.border = '1px solid #333333';
                        closeButton.style.borderRadius = '8px';
                        closeButton.style.cursor = 'pointer';
                        closeButton.style.fontSize = '13px';
                        closeButton.style.fontWeight = '600';
                        closeButton.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                        closeButton.onclick = function() {
                            document.body.removeChild(modal);
                        };
                        buttonContainer.appendChild(closeButton);
                        
                        modal.appendChild(buttonContainer);
                        
                        // Add hover effects
                        copyButton.addEventListener('mouseenter', function() {
                            if (this.textContent !== "Copied!") {
                                this.style.transform = 'translateY(-1px)';
                                this.style.boxShadow = '0 8px 24px rgba(139, 92, 246, 0.3)';
                            }
                        });
                        
                        copyButton.addEventListener('mouseleave', function() {
                            this.style.transform = 'translateY(0)';
                            this.style.boxShadow = 'none';
                        });
                        
                        closeButton.addEventListener('mouseenter', function() {
                            this.style.backgroundColor = '#333333';
                            this.style.transform = 'translateY(-1px)';
                            this.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.3)';
                        });
                        
                        closeButton.addEventListener('mouseleave', function() {
                            this.style.backgroundColor = '#262626';
                            this.style.transform = 'translateY(0)';
                            this.style.boxShadow = 'none';
                        });
                        
                        // Enhanced keyboard handlers
                        function handleKeyDown(e) {
                            if (e.key === 'Escape') {
                                if (isFullscreen) {
                                    toggleFullscreen();
                                } else {
                                    document.body.removeChild(modal);
                                    document.removeEventListener('keydown', handleKeyDown);
                                }
                            } else if (e.key === 'f' || e.key === 'F') {
                                toggleFullscreen();
                            }
                        }
                        document.addEventListener('keydown', handleKeyDown);
                        
                        document.body.appendChild(modal);
                    })();
                }
            }, (result) => {
                if (chrome.runtime.lastError) {
                    console.error('Error executing Schema script:', chrome.runtime.lastError);
                } else {
                    console.log('Schema script executed successfully');
                }
            });
        });
        } catch (error) {
            console.error('Schema error:', error);
        }
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                // Function to reset Schema modal effects
                function resetSchemaModal() {
                    // Remove any existing schema modal
                    const existingModal = document.querySelector('.schema-modal');
                    if (existingModal) {
                        existingModal.remove();
                        console.log('Schema modal removed');
                    }
                    
                    console.log('Schema modal reset completed');
                }

                // Clean up modals in current tab
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetSchemaModal
                    }, () => {
                        // Also close any schema visualizer tabs
                        chrome.tabs.query({url: chrome.runtime.getURL("schema-visualizer.html*")}, (visualizerTabs) => {
                            if (visualizerTabs && visualizerTabs.length > 0) {
                                visualizerTabs.forEach(tab => {
                                    chrome.tabs.remove(tab.id);
                                    console.log('Schema visualizer tab closed:', tab.id);
                                });
                            }
                            resolve();
                        });
                    });
                });
            } catch (error) {
                console.error('Schema reset error:', error);
                resolve(); // Resolve anyway to not block other resets
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SchemaAction;
} else {
    window.SchemaAction = SchemaAction;
}