// Htags Quick Action - Highlights and color-codes all heading tags (H1-H6)
// Works exactly like the bookmarklet: javascript:void((function()%7Bvar a,b,c,d,e,f%3Bf%3Dnew Array(%27pink%27,%27orange%27,%27yellow%27,%27aquamarine%27,%27lightskyblue%27,%27plum%27)%3Bfor(a%3D1%3Ba<%3D6%3Ba%2B%2B)%7Bb%3Ddocument.getElementsByTagName(%27h%27%2Ba)%3Bfor(c%3D0%3Bc<b.length%3Bc%2B%2B)%7Bd%3Db%5Bc%5D%3Be%3Dd.style%3Be.backgroundColor%3Df%5Ba-1%5D%3Be.border%3D%27solid%27%3Be.padding%3D%272px%27%3Be.color%3D%27black%27%3Bd.innerHTML%3D%27H%27%2Ba%2B%27 - %27%2Bd.innerHTML%3B%7D%7D%7D)())

class HtagsAction {
    constructor() {
        this.name = 'Htags';
        this.description = 'Highlights and color-codes all heading tags (H1-H6) on the current page';
        this.colors = ['pink', 'orange', 'yellow', 'aquamarine', 'lightskyblue', 'plum'];
    }

    // Execute the htags highlighting - exactly like the bookmarklet
    execute() {
        return new Promise((resolve, reject) => {
            chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                const currentTab = tabs[0];
                
                try {
                    chrome.scripting.executeScript({
                        target: {tabId: currentTab.id},
                        func: function() {
                            // This is the exact bookmarklet function
                            void((function(){
                                var a,b,c,d,e,f;
                                f=new Array('pink','orange','yellow','aquamarine','lightskyblue','plum');
                                for(a=1;a<=6;a++){
                                    b=document.getElementsByTagName('h'+a);
                                    for(c=0;c<b.length;c++){
                                        d=b[c];
                                        e=d.style;
                                        e.backgroundColor=f[a-1];
                                        e.border='solid';
                                        e.padding='2px';
                                        e.color='black';
                                        d.innerHTML='H'+a+' - '+d.innerHTML;
                                    }
                                }
                                
                                // Handle Escape key to reset htags highlighting
                                function handleKeyDown(e) {
                                    if (e.key === 'Escape') {
                                        // Reset htags highlighting
                                        for (let a = 1; a <= 6; a++) {
                                            const headings = document.getElementsByTagName('h' + a);
                                            for (let c = 0; c < headings.length; c++) {
                                                const heading = headings[c];
                                                // Remove styles
                                                heading.style.backgroundColor = '';
                                                heading.style.border = '';
                                                heading.style.padding = '';
                                                heading.style.color = '';
                                                
                                                // Remove the H# prefix if it exists
                                                const regex = new RegExp('^H' + a + ' - ');
                                                if (regex.test(heading.innerHTML)) {
                                                    heading.innerHTML = heading.innerHTML.replace(regex, '');
                                                }
                                            }
                                        }
                                        document.removeEventListener('keydown', handleKeyDown);
                                    }
                                }
                                document.addEventListener('keydown', handleKeyDown);
                                
                                return true;
                            })());
                        }
                    }, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('Error executing Htags script:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            console.log('Htags script executed successfully');
                            resolve(result);
                        }
                    });
                } catch (error) {
                    console.error('Error setting up Htags script execution:', error);
                    reject(error);
                }
            });
        });
    }

    // Reset the htags highlighting (remove all modifications)
    reset() {
        return new Promise((resolve, reject) => {
            chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                const currentTab = tabs[0];
                
                try {
                    chrome.scripting.executeScript({
                        target: {tabId: currentTab.id},
                        func: function() {
                            // Reset function to undo the bookmarklet changes
                            for (let a = 1; a <= 6; a++) {
                                const headings = document.getElementsByTagName('h' + a);
                                for (let c = 0; c < headings.length; c++) {
                                    const heading = headings[c];
                                    // Remove styles
                                    heading.style.backgroundColor = '';
                                    heading.style.border = '';
                                    heading.style.padding = '';
                                    heading.style.color = '';
                                    
                                    // Remove the H# prefix if it exists
                                    const regex = new RegExp('^H' + a + ' - ');
                                    if (regex.test(heading.innerHTML)) {
                                        heading.innerHTML = heading.innerHTML.replace(regex, '');
                                    }
                                }
                            }
                            return true;
                        }
                    }, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('Error resetting Htags:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            console.log('Htags reset successfully');
                            resolve(result);
                        }
                    });
                } catch (error) {
                    console.error('Error setting up Htags reset:', error);
                    reject(error);
                }
            });
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HtagsAction;
} else {
    window.HtagsAction = HtagsAction;
} 