class TrackerDetectionAction {
    static execute() {
        try {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                    target: {tabId: tabs[0].id},
                    func: function() {
                        // Remove any existing panel
                        const existingPanel = document.querySelector('.tracker-detection-panel');
                        if (existingPanel) {
                            existingPanel.remove();
                        }

                        // Wait for DOM and dynamic content to load
                        function performComprehensiveScan() {
                            console.log('TrackerDetection: Starting comprehensive scan...');

                            // --- ENHANCED TRACKER DEFINITIONS FOR SEO/MARKETING ---
                            const trackers = {
                                // === ADVERTISING (Most Critical for Marketing) ===
                                'Google Ads': {
                                    patterns: [
                                        /googleadservices\.com/,
                                        /doubleclick\.net/,
                                        /stats\.g\.doubleclick\.net/,
                                        /google\.com\/ads/,
                                        /googlesyndication\.com/
                                    ],
                                    category: 'Advertising',
                                    icon: '📢'
                                },
                                'Facebook Pixel': {
                                    patterns: [
                                        /connect\.facebook\.net/,
                                        /facebook\.com\/tr/,
                                        /facebook\.com\/signals/,
                                        /fbcdn\.net/
                                    ],
                                    category: 'Advertising',
                                    icon: '📢'
                                },
                                'Amazon Ads': {
                                    patterns: [
                                        /amazon-adsystem\.com/,
                                        /assoc-amazon\.com/,
                                        /amazon\.com\/gp\/aw\/cr/
                                    ],
                                    category: 'Advertising',
                                    icon: '📢'
                                },
                                'Criteo': {
                                    patterns: [
                                        /criteo\.com/,
                                        /criteo\.net/
                                    ],
                                    category: 'Advertising',
                                    icon: '📢'
                                },

                                // === SITE ANALYTICS (Critical for SEO) ===
                                'Google Analytics': {
                                    patterns: [
                                        /google-analytics\.com/,
                                        /googleanalytics\.com/,
                                        /region1\.google-analytics\.com/,
                                        /ga\.js/,
                                        /analytics\.js/,
                                        /collect/
                                    ],
                                    category: 'Site Analytics',
                                    icon: '📈'
                                },
                                'Adobe Analytics': {
                                    patterns: [
                                        /omtrdc\.net/,
                                        /2o7\.net/,
                                        /adobe\.com.*analytics/,
                                        /adobedc\.net/
                                    ],
                                    category: 'Site Analytics',
                                    icon: '📈'
                                },
                                'Hotjar': {
                                    patterns: [
                                        /hotjar\.com/,
                                        /hjcdn\.com/,
                                        /static\.hotjar\.com/
                                    ],
                                    category: 'Site Analytics',
                                    icon: '📈'
                                },
                                'Microsoft Clarity': {
                                    patterns: [
                                        /clarity\.ms/
                                    ],
                                    category: 'Site Analytics',
                                    icon: '📈'
                                },
                                'Crazy Egg': {
                                    patterns: [
                                        /script\.crazyegg\.com/
                                    ],
                                    category: 'Site Analytics',
                                    icon: '📈'
                                },
                                'FullStory': {
                                    patterns: [
                                        /fullstory\.com/
                                    ],
                                    category: 'Site Analytics',
                                    icon: '📈'
                                },
                                'Heap': {
                                    patterns: [
                                        /heapanalytics\.com/
                                    ],
                                    category: 'Site Analytics',
                                    icon: '📈'
                                },
                                'Pendo': {
                                    patterns: [
                                        /cdn\.pendo\.io/
                                    ],
                                    category: 'Site Analytics',
                                    icon: '📈'
                                },
                                'Mixpanel': {
                                    patterns: [
                                        /mixpanel\.com/,
                                        /mxpnl\.com/
                                    ],
                                    category: 'Site Analytics',
                                    icon: '📈'
                                },
                                'Segment': {
                                    patterns: [
                                        /segment\.com/,
                                        /segment\.io/,
                                        /cdn\.segment\.com/
                                    ],
                                    category: 'Site Analytics',
                                    icon: '📈'
                                },

                                // === UTILITIES (Essential for Marketing) ===
                                'Google Tag Manager': {
                                    patterns: [
                                        /googletagmanager\.com\/gtm\.js/,
                                        /googletagmanager\.com\/gtag\/js/,
                                        /googletagmanager\.com\/a\?/,
                                        /googletagmanager\.com\/td\?/
                                    ],
                                    category: 'Utilities',
                                    icon: '🔧'
                                },
                                'Google Optimize': {
                                    patterns: [
                                        /optimize\.google\.com/,
                                        /googleoptimize\.com/
                                    ],
                                    category: 'Utilities',
                                    icon: '🔧'
                                },
                                'Optimizely': {
                                    patterns: [
                                        /optimizely\.com/,
                                        /optimizely\.s3\.amazonaws\.com/,
                                        /cdn\.optimizely\.com/
                                    ],
                                    category: 'Utilities',
                                    icon: '🔧'
                                },
                                'Google reCAPTCHA': {
                                    patterns: [
                                        /google\.com\/recaptcha/,
                                        /recaptcha\.net/,
                                        /gstatic\.com\/recaptcha/
                                    ],
                                    category: 'Utilities',
                                    icon: '🔧'
                                },

                                // === CUSTOMER INTERACTION (Important for Marketing) ===
                                'HubSpot': {
                                    patterns: [
                                        /hubspot\.com/,
                                        /hs-scripts\.com/,
                                        /hsforms\.net/,
                                        /hubapi\.com/
                                    ],
                                    category: 'Customer Interaction',
                                    icon: '💬'
                                },
                                'MailChimp': {
                                    patterns: [
                                        /mailchimp\.com/,
                                        /chimpstatic\.com/,
                                        /mc\.yandex\.ru/
                                    ],
                                    category: 'Customer Interaction',
                                    icon: '💬'
                                },
                                'Intercom': {
                                    patterns: [
                                        /intercom\.io/,
                                        /intercomcdn\.com/,
                                        /widget\.intercom\.io/
                                    ],
                                    category: 'Customer Interaction',
                                    icon: '💬'
                                },
                                'Drift': {
                                    patterns: [
                                        /drift\.com/,
                                        /driftcdn\.com/,
                                        /js\.driftt\.com/
                                    ],
                                    category: 'Customer Interaction',
                                    icon: '💬'
                                },

                                // === SOCIAL MEDIA (Important for Marketing) ===
                                'Twitter Pixel': {
                                    patterns: [
                                        /analytics\.twitter\.com/,
                                        /platform\.twitter\.com/,
                                        /twimg\.com/
                                    ],
                                    category: 'Social Media',
                                    icon: '🔗'
                                },
                                'LinkedIn Insights': {
                                    patterns: [
                                        /linkedin\.com\/px/,
                                        /snap\.licdn\.com/,
                                        /platform\.linkedin\.com/
                                    ],
                                    category: 'Social Media',
                                    icon: '🔗'
                                },
                                'Pinterest Tag': {
                                    patterns: [
                                        /pinterest\.com.*\/ct/,
                                        /pinimg\.com/,
                                        /pinterest\.com.*analytics/
                                    ],
                                    category: 'Social Media',
                                    icon: '🔗'
                                },

                                // === HOSTING/CDN (Important for SEO Performance) ===
                                'Cloudflare': {
                                    patterns: [
                                        /cloudflare\.com/,
                                        /cdnjs\.cloudflare\.com/,
                                        /cf-assets\.com/
                                    ],
                                    category: 'Hosting',
                                    icon: '📦'
                                },
                                'Google Fonts': {
                                    patterns: [
                                        /fonts\.googleapis\.com/,
                                        /fonts\.gstatic\.com/
                                    ],
                                    category: 'Hosting',
                                    icon: '📦'
                                },
                                'jsDelivr': {
                                    patterns: [
                                        /cdn\.jsdelivr\.net/
                                    ],
                                    category: 'Hosting',
                                    icon: '📦'
                                }
                            };

                            // Category icons
                            const categoryIcons = {
                                'Advertising': '📢',
                                'Site Analytics': '📈',
                                'Utilities': '🔧',
                                'Hosting': '📦',
                                'Customer Interaction': '💬',
                                'Social Media': '🔗'
                            };

                            // --- ENHANCED DETECTION LOGIC ---
                            const detected = {};
                            const trackingIds = {};
                            const duplicates = new Set();
                            const duplicateDetails = {};

                            // Enhanced function to extract tracking IDs
                            function extractId(url, tracker) {
                                try {
                                    const urlObj = new URL(url);
                                    
                                    if (tracker === 'Google Tag Manager') {
                                        const id = urlObj.searchParams.get('id');
                                        if (id) {
                                            // Check if it's a GA measurement ID (G-XXXXXXXX) or GTM container ID (GTM-XXXXXXX)
                                            return id;
                                        }
                                        return null;
                                    }
                                    
                                    if (tracker === 'Google Analytics') {
                                        const id = urlObj.searchParams.get('id') || url.match(/UA-\d+-\d+|G-[A-Z0-9]+/);
                                        return Array.isArray(id) ? id[0] : id;
                                    }
                                    
                                    if (tracker === 'Facebook Pixel') {
                                        // Look for Facebook pixel ID in various formats
                                        const pixelMatch = url.match(/\/(\d{10,})\//);
                                        if (pixelMatch) return pixelMatch[1];
                                        
                                        const configMatch = url.match(/config\/(\d+)/);
                                        if (configMatch) return configMatch[1];
                                        
                                        // Check for pixel ID in URL parameters
                                        const pixelIdMatch = url.match(/[?&]id=(\d{10,})/);
                                        if (pixelIdMatch) return pixelIdMatch[1];
                                        
                                        // Check for pixel ID in signals URL
                                        const signalsMatch = url.match(/signals\/config\/(\d+)/);
                                        if (signalsMatch) return signalsMatch[1];
                                        
                                        // Extract from fbevents URL pattern
                                        const fbeventsMatch = url.match(/\/(\d{10,})\?v=/);
                                        if (fbeventsMatch) return fbeventsMatch[1];
                                        
                                        return null;
                                    }

                                    if (tracker === 'Google Ads') {
                                        // Extract Google Ads conversion ID
                                        const conversionMatch = url.match(/[?&]id=(\d+)/);
                                        if (conversionMatch) return conversionMatch[1];
                                        
                                        const labelMatch = url.match(/[?&]label=([A-Za-z0-9_-]+)/);
                                        if (labelMatch) return labelMatch[1];
                                        
                                        return null;
                                    }

                                    if (tracker === 'Adobe Analytics') {
                                        // Extract Adobe Analytics report suite ID
                                        const rsidMatch = url.match(/\/b\/ss\/([^\/,]+)/);
                                        if (rsidMatch) return rsidMatch[1];
                                        
                                        return null;
                                    }

                                    if (tracker === 'HubSpot') {
                                        // Extract HubSpot portal ID
                                        const portalMatch = url.match(/\/(\d+)\//);
                                        if (portalMatch) return portalMatch[1];
                                        
                                        const hubspotMatch = url.match(/[?&]portalId=(\d+)/);
                                        if (hubspotMatch) return hubspotMatch[1];
                                        
                                        return null;
                                    }

                                    if (tracker === 'Hotjar') {
                                        // Extract Hotjar site ID
                                        const siteMatch = url.match(/\/(\d+)\//);
                                        if (siteMatch) return siteMatch[1];
                                        
                                        return null;
                                    }

                                    if (tracker === 'Microsoft Clarity') {
                                        const clarityMatch = url.match(/clarity\.ms\/tag\/([a-z0-9]+)/);
                                        if (clarityMatch) return clarityMatch[1];

                                        return null;
                                    }

                                    if (tracker === 'Mixpanel') {
                                        // Extract Mixpanel project token
                                        const tokenMatch = url.match(/[?&]token=([a-f0-9]+)/);
                                        if (tokenMatch) return tokenMatch[1];
                                        
                                        return null;
                                    }

                                    if (tracker === 'Pinterest Tag') {
                                        const idMatch = url.match(/[?&]token_create,(.+?)(?:[&,]|$)/);
                                        if (idMatch) return idMatch[1];
                                        
                                        const pixelMatch = url.match(/\/v3\/(\d+)\//);
                                        if (pixelMatch) return pixelMatch[1];
                                        
                                        return null;
                                    }

                                    if (tracker === 'LinkedIn Insights') {
                                        // Extract LinkedIn partner ID
                                        const partnerMatch = url.match(/pid=(\d+)/);
                                        if (partnerMatch) return partnerMatch[1];
                                        
                                        return null;
                                    }

                                    if (tracker === 'Google Optimize') {
                                        // Extract Optimize container ID
                                        const containerMatch = url.match(/[?&]id=(GTM-[A-Z0-9]+|OPT-[A-Z0-9]+)/);
                                        if (containerMatch) return containerMatch[1];
                                        
                                        return null;
                                    }

                                    if (tracker === 'Optimizely') {
                                        // Extract Optimizely project ID
                                        const projectMatch = url.match(/\/(\d+)\.js/);
                                        if (projectMatch) return projectMatch[1];
                                        
                                        return null;
                                    }
                                    
                                    return null;
                                } catch (e) {
                                    return null;
                                }
                            }

                            // Scan all elements for trackers
                            document.querySelectorAll('script[src], img[src], iframe[src], link[href]').forEach(el => {
                                        const src = el.src || el.href;
                                        if (!src) return;
                                
                                for (const [name, config] of Object.entries(trackers)) {
                                    if (config.patterns.some(regex => regex.test(src))) {
                                        detected[name] = detected[name] || [];
                                        detected[name].push({
                                            type: el.tagName.toLowerCase(),
                                            url: src
                                        });
                                        
                                        // Extract and track IDs
                                        const trackingId = extractId(src, name);
                                        if (trackingId) {
                                            trackingIds[name] = trackingIds[name] || [];
                                            trackingIds[name].push(trackingId);
                                        }
                                    }
                                }
                            });

                            // Check for global objects
                            if (typeof fbq === 'function') {
                                detected['Facebook Pixel'] = detected['Facebook Pixel'] || [];
                                detected['Facebook Pixel'].push({
                                    type: 'global',
                                    url: 'fbq() function detected'
                                });
                            }
                            
                            if (typeof gtag === 'function') {
                                detected['Google Tag Manager'] = detected['Google Tag Manager'] || [];
                                detected['Google Tag Manager'].push({
                                    type: 'global',
                                    url: 'gtag() function detected'
                                });
                            }
                            
                            if (typeof ga === 'function') {
                                detected['Google Analytics'] = detected['Google Analytics'] || [];
                                detected['Google Analytics'].push({
                                    type: 'global',
                                    url: 'ga() function detected'
                                });
                            }

                            if (typeof mixpanel !== 'undefined') {
                                detected['Mixpanel'] = detected['Mixpanel'] || [];
                                detected['Mixpanel'].push({
                                    type: 'global',
                                    url: 'mixpanel object detected'
                                });
                            }

                            if (typeof hj === 'function') {
                                detected['Hotjar'] = detected['Hotjar'] || [];
                                detected['Hotjar'].push({
                                    type: 'global',
                                    url: 'hj() function detected'
                                });
                            }

                            if (typeof optimizely !== 'undefined') {
                                detected['Optimizely'] = detected['Optimizely'] || [];
                                detected['Optimizely'].push({
                                    type: 'global',
                                    url: 'optimizely object detected'
                                });
                            }

                            if (typeof analytics !== 'undefined') {
                                detected['Segment'] = detected['Segment'] || [];
                                detected['Segment'].push({
                                    type: 'global',
                                    url: 'analytics object detected'
                                });
                            }

                            if (typeof _hsq !== 'undefined' || typeof __hstc !== 'undefined') {
                                detected['HubSpot'] = detected['HubSpot'] || [];
                                detected['HubSpot'].push({
                                    type: 'global',
                                    url: 'HubSpot tracking detected'
                                });
                            }

                            if (window.clarity) {
                                detected['Microsoft Clarity'] = detected['Microsoft Clarity'] || [];
                                detected['Microsoft Clarity'].push({
                                    type: 'global',
                                    url: 'Clarity object detected'
                                });
                            }

                            if (window.FS) {
                                detected['FullStory'] = detected['FullStory'] || [];
                                detected['FullStory'].push({
                                    type: 'global',
                                    url: 'FullStory object detected'
                                });
                            }

                            if (window.heap) {
                                detected['Heap'] = detected['Heap'] || [];
                                detected['Heap'].push({
                                    type: 'global',
                                    url: 'Heap object detected'
                                });
                            }

                            // Enhanced duplicate detection with details - CROSS-TRACKER AND WITHIN-TRACKER
                            const allDetectedTrackers = Object.keys(detected);
                            console.log('TrackerDetection: Checking duplicates across all trackers:', allDetectedTrackers);
                            
                            // Define priority trackers for marketing/SEO focus
                            const priorityTrackers = new Set([
                                'Google Tag Manager', 'Google Analytics', 'Google Ads', 'Facebook Pixel', 
                                'HubSpot', 'Hotjar', 'Mixpanel', 'Adobe Analytics', 'Segment', 'Microsoft Clarity', 'Crazy Egg', 'FullStory', 'Heap', 'Pendo'
                            ]);
                            
                            // Step 1: Check for cross-tracker duplicate IDs (most important for marketing tags)
                            const allTrackingIds = {};
                            const crossTrackerDuplicates = {};
                            
                            for (const [tracker, ids] of Object.entries(trackingIds)) {
                                if (ids && ids.length > 0) {
                                    ids.forEach(id => {
                                        if (!allTrackingIds[id]) {
                                            allTrackingIds[id] = [];
                                        }
                                        allTrackingIds[id].push(tracker);
                                    });
                                }
                            }
                            
                            // Find IDs that appear across multiple trackers or multiple times in same tracker
                            for (const [id, trackersList] of Object.entries(allTrackingIds)) {
                                const trackerCounts = {};
                                trackersList.forEach(tracker => {
                                    trackerCounts[tracker] = (trackerCounts[tracker] || 0) + 1;
                                });
                                
                                // Check if ID appears multiple times total (cross-tracker or within-tracker)
                                const totalCount = trackersList.length;
                                const uniqueTrackers = Object.keys(trackerCounts);
                                
                                if (totalCount > 1) {
                                    console.log(`TrackerDetection: Found duplicate ID "${id}" across:`, trackerCounts);
                                    
                                    uniqueTrackers.forEach(tracker => {
                                        duplicates.add(tracker);
                                        if (!duplicateDetails[tracker]) {
                                            duplicateDetails[tracker] = [];
                                        }
                                        
                                        // Check if this duplicate already exists
                                        const existingDup = duplicateDetails[tracker].find(d => d.id === id);
                                        if (!existingDup) {
                                            duplicateDetails[tracker].push({
                                                id: id,
                                                count: trackerCounts[tracker],
                                                crossTracker: uniqueTrackers.length > 1
                                            });
                                        }
                                    });
                                }
                            }

                            // Step 2: Check for identical URLs (only for priority marketing/SEO trackers)
                            for (const [trackerName, instances] of Object.entries(detected)) {
                                if (instances.length > 1 && !duplicates.has(trackerName) && priorityTrackers.has(trackerName)) {
                                    const urls = instances.map(d => d.url);
                                    const urlCounts = {};
                                    
                                    urls.forEach(url => {
                                        urlCounts[url] = (urlCounts[url] || 0) + 1;
                                    });
                                    
                                    const duplicatedUrls = Object.keys(urlCounts).filter(url => urlCounts[url] > 1);
                                    
                                    if (duplicatedUrls.length > 0) {
                                        console.log(`TrackerDetection: Found duplicate URLs for ${trackerName}:`, duplicatedUrls);
                                        duplicates.add(trackerName);
                                        duplicateDetails[trackerName] = duplicateDetails[trackerName] || [];
                                        duplicatedUrls.forEach(url => {
                                            duplicateDetails[trackerName].push({ 
                                                id: `Duplicate URL: ${url.substring(0, 50)}...`, 
                                                count: urlCounts[url],
                                                crossTracker: false
                                            });
                                        });
                                    }
                                }
                            }

                            // Check for click tracking
                            function hasClickTracking(trackerName) {
                                const clickPatterns = [
                                    /gtag\(['"]event['"],\s*['"]click['"]/,
                                    /fbq\(['"]track['"],\s*['"]click['"]/,
                                    /mixpanel\.track\(['"]click['"]/,
                                    /analytics\.track\(['"]click['"]/,
                                    /dataLayer\.push\(\{.*event.*click.*\}\)/i
                                ];
                                const scripts = Array.from(document.querySelectorAll('script')).map(s => s.textContent || '');
                                for (let script of scripts) {
                                    if (clickPatterns.some(pat => pat.test(script))) return true;
                                }
                                const onclickEls = document.querySelectorAll('[onclick]');
                                for (let el of onclickEls) {
                                    const val = el.getAttribute('onclick');
                                    if (val && /track|click|gtag|fbq/.test(val)) return true;
                                }
                                return false;
                            }

                            // Calculate totals
                            let totalTrackers = Object.keys(detected).length;
                            let totalDuplicates = duplicates.size;

                            console.log('TrackerDetection: Found', totalTrackers, 'trackers,', totalDuplicates, 'duplicates');

                            // Create panel with proper positioning
                                                    var panel = document.createElement('div');
                        panel.className = 'tracker-detection-panel';
                        panel.style.cssText = `
                                position: fixed !important;
                                top: 50px !important;
                                right: 50px !important;
                                width: 480px !important;
                                max-height: 90vh !important;
                                z-index: 2147483647 !important;
                                background: #1a1a1a !important;
                                color: #e5e5e5 !important;
                                border: 1px solid #2d2d2d !important;
                                border-radius: 12px !important;
                                box-shadow: 0 10px 40px rgba(0,0,0,0.8) !important;
                                overflow: hidden !important;
                                resize: both !important;
                                min-width: 400px !important;
                                min-height: 300px !important;
                                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif !important;
                                font-size: 13px !important;
                                line-height: 1.4 !important;
                                display: block !important;
                                visibility: visible !important;
                                opacity: 1 !important;
                                transform: none !important;
                        `;
                        
                        // Prevent page scrolling when scrolling inside panel
                        panel.addEventListener('wheel', function(e) {
                            e.stopPropagation();
                        }, { passive: true });

                            // Create header with drag functionality
                            var header = document.createElement('div');
                            header.id = 'tracker-detection-header';
                            header.style.cssText = `
                                display: flex !important;
                                justify-content: space-between !important;
                                align-items: center !important;
                                padding: 16px 20px !important;
                                background: #222 !important;
                                border-bottom: 1px solid #2d2d2d !important;
                                cursor: move !important;
                            `;

                                                    var title = document.createElement('h1');
                        title.textContent = 'Tracker Detection';
                        title.style.cssText = `
                                margin: 0 !important;
                                color: #e5e5e5 !important;
                                font-size: 16px !important;
                                font-weight: 600 !important;
                        `;

                        var headerButtons = document.createElement('div');
                        headerButtons.style.cssText = `
                                display: flex !important;
                                align-items: center !important;
                                gap: 8px !important;
                        `;

                        var exportBtn = document.createElement('button');
                        exportBtn.textContent = 'EXPORT CSV';
                        exportBtn.style.cssText = `
                                background: #7C3AED !important;
                                color: white !important;
                                border: none !important;
                                padding: 6px 12px !important;
                                border-radius: 4px !important;
                                font-size: 10px !important;
                                cursor: pointer !important;
                                font-weight: 500 !important;
                                transition: all 0.2s !important;
                        `;
                        exportBtn.onmouseover = function() { this.style.background = '#6D28D9'; };
                        exportBtn.onmouseout = function() { this.style.background = '#7C3AED'; };

                        var closeBtn = document.createElement('button');
                            closeBtn.innerHTML = '×';
                            closeBtn.style.cssText = `
                                width: 24px !important;
                                height: 24px !important;
                                background: #333 !important;
                                color: #e5e5e5 !important;
                                border: none !important;
                                border-radius: 50% !important;
                                cursor: pointer !important;
                                font-size: 16px !important;
                                font-weight: 400 !important;
                                transition: all 0.2s !important;
                                display: flex !important;
                                align-items: center !important;
                                justify-content: center !important;
                            `;
                            closeBtn.onmouseover = function() { this.style.background = '#555'; };
                            closeBtn.onmouseout = function() { this.style.background = '#333'; };

                                                    headerButtons.appendChild(exportBtn);
                        headerButtons.appendChild(closeBtn);
                        
                        header.appendChild(title);
                        header.appendChild(headerButtons);
                        panel.appendChild(header);

                            // Add duplicate warning section if duplicates exist
                            if (totalDuplicates > 0) {
                                var duplicateWarning = document.createElement('div');
                                duplicateWarning.style.cssText = `
                                    background: #2d1a1a !important;
                                    border-left: 4px solid #dc2626 !important;
                                    border-bottom: 1px solid #2d2d2d !important;
                                    padding: 16px 20px !important;
                                    color: #fca5a5 !important;
                                `;

                                let warningHTML = `
                                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span style="color: #dc2626; font-size: 16px;">⚠</span>
                                            <h3 style="margin: 0; color: #dc2626; font-size: 14px; font-weight: 600;">Duplicate Tags Detected</h3>
                                        </div>
                                        <button id="copy-duplicates-btn" style="background: #333; color: #e5e5e5; border: 1px solid #7C3AED; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500;">
                                            COPY DETAILS
                                        </button>
                                    </div>
                                    <div style="margin-bottom: 8px; font-size: 12px; color: #ffffff;">
                                        The following trackers have duplicate instances:
                                    </div>
                                `;

                                const duplicateList = Array.from(duplicates).map(tracker => {
                                    const details = duplicateDetails[tracker] || [];
                                    if (details.length > 0) {
                                        const idsText = details.map(d => {
                                            const crossText = d.crossTracker ? ' [CROSS-TRACKER]' : '';
                                            return `${d.id} (${d.count}x)${crossText}`;
                                        }).join(', ');
                                        return `${tracker}: ${idsText}`;
                                    }
                                    return tracker;
                                }).join(', ');

                                warningHTML += `<div style="font-size: 12px; color: #ffffff;">${duplicateList}</div>`;
                                duplicateWarning.innerHTML = warningHTML;
                                
                                // Add copy functionality to the duplicates button
                                duplicateWarning.querySelector('#copy-duplicates-btn').onclick = function() {
                                    let duplicateText = 'DUPLICATE TRACKERS DETECTED:\n\n';
                                    
                                    Array.from(duplicates).forEach(tracker => {
                                        const details = duplicateDetails[tracker] || [];
                                        const instances = detected[tracker] || [];
                                        
                                        duplicateText += `${tracker}:\n`;
                                        if (details.length > 0) {
                                            details.forEach(d => {
                                                const crossText = d.crossTracker ? ' [CROSS-TRACKER]' : '';
                                                duplicateText += `  - ID: ${d.id} (${d.count}x)${crossText}\n`;
                                            });
                                        }
                                        
                                        duplicateText += '  URLs:\n';
                                        instances.forEach(instance => {
                                            duplicateText += `    ${instance.type.toUpperCase()}: ${instance.url}\n`;
                                        });
                                        duplicateText += '\n';
                                    });
                                    
                                    navigator.clipboard.writeText(duplicateText).then(() => {
                                        const btn = this;
                                        const originalText = btn.textContent;
                                        btn.textContent = 'COPIED!';
                                        btn.style.background = '#16a34a';
                                        btn.style.borderColor = '#16a34a';
                                        setTimeout(() => {
                                            btn.textContent = originalText;
                                            btn.style.background = '#333';
                                            btn.style.borderColor = '#7C3AED';
                                        }, 2000);
                                    }).catch(err => {
                                        console.error('Failed to copy duplicates:', err);
                                    });
                                };
                                
                                panel.appendChild(duplicateWarning);
                            }

                            // Create content container
                            var content = document.createElement('div');
                            content.style.cssText = `
                                padding: 20px !important;
                                overflow-y: auto !important;
                                max-height: calc(90vh - ${totalDuplicates > 0 ? '140px' : '80px'}) !important;
                                scrollbar-width: thin !important;
                                scrollbar-color: #555 #2a2a2a !important;
                            `;
                            
                            // Custom scrollbar styles for webkit browsers
                            const scrollbarStyle = document.createElement('style');
                            scrollbarStyle.textContent = `
                                .tracker-detection-panel *::-webkit-scrollbar {
                                    width: 8px !important;
                                }
                                .tracker-detection-panel *::-webkit-scrollbar-track {
                                    background: #2a2a2a !important;
                                    border-radius: 4px !important;
                                }
                                .tracker-detection-panel *::-webkit-scrollbar-thumb {
                                    background: #555 !important;
                                    border-radius: 4px !important;
                                }
                                .tracker-detection-panel *::-webkit-scrollbar-thumb:hover {
                                    background: #666 !important;
                                }
                            `;
                            document.head.appendChild(scrollbarStyle);
                            
                            // Prevent page scrolling when scrolling inside content
                            content.addEventListener('wheel', function(e) {
                                e.stopPropagation();
                            }, { passive: true });

                            // Add summary section with duplicates highlighted
                            var summarySection = document.createElement('div');
                            summarySection.style.cssText = `
                                background: ${totalDuplicates > 0 ? '#2d1a1a' : '#1f1f1f'} !important;
                                border: 1px solid ${totalDuplicates > 0 ? '#dc2626' : '#333'} !important;
                                border-radius: 8px !important;
                                padding: 16px !important;
                                margin-bottom: 20px !important;
                            `;

                            let summaryHTML = `
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                    <h3 style="margin: 0; color: #e5e5e5; font-size: 14px; font-weight: 600;">Detection Summary</h3>
                                    ${totalDuplicates > 0 ? '<span style="color: #dc2626; font-size: 12px; font-weight: 500;">⚠ DUPLICATES FOUND</span>' : ''}
                                </div>
                                <div style="display: flex; gap: 20px; align-items: center;">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="color: #6366f1; font-size: 18px; font-weight: 600;">${totalTrackers}</span>
                                        <span style="color: #9ca3af; font-size: 12px;">Trackers</span>
                                    </div>
                            `;

                            if (totalDuplicates > 0) {
                                summaryHTML += `
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="color: #dc2626; font-size: 18px; font-weight: 600;">${totalDuplicates}</span>
                                        <span style="color: #fca5a5; font-size: 12px;">Duplicates</span>
                                    </div>
                                `;
                                    }

                            summaryHTML += `</div>`;
                            summarySection.innerHTML = summaryHTML;
                            content.appendChild(summarySection);

                            if (totalTrackers === 0) {
                                // No trackers found message
                                var noTrackersSection = document.createElement('div');
                                noTrackersSection.style.cssText = `
                                    background: #1f1f1f !important;
                                    border: 1px solid #333 !important;
                                    border-radius: 8px !important;
                                    padding: 30px 20px !important;
                                    text-align: center !important;
                                    color: #9ca3af !important;
                                `;
                                noTrackersSection.innerHTML = `
                                    <div style="font-size: 14px; margin-bottom: 6px;">No Trackers Detected</div>
                                    <div style="font-size: 12px;">This page appears to be clean of common tracking scripts.</div>
                                `;
                                content.appendChild(noTrackersSection);
                                    } else {
                                // Process by categories
                                const categories = {};
                                
                                // Group trackers by category
                                for (const [trackerName, instances] of Object.entries(detected)) {
                                    const category = trackers[trackerName]?.category || 'Other';
                                    if (!categories[category]) {
                                        categories[category] = {};
                                    }
                                    categories[category][trackerName] = instances;
                                }

                                // Add "Open All" button above the sections
                                var openAllSection = document.createElement('div');
                                openAllSection.style.cssText = `
                                    display: flex !important;
                                    justify-content: flex-end !important;
                                    margin-bottom: 12px !important;
                                `;
                                
                                var openAllBtn = document.createElement('button');
                                openAllBtn.id = 'open-all-btn';
                                openAllBtn.textContent = 'OPEN ALL';
                                openAllBtn.style.cssText = `
                                    background: #2a2a2a !important;
                                    color: #9ca3af !important;
                                    border: 1px solid #404040 !important;
                                    padding: 6px 12px !important;
                                    border-radius: 4px !important;
                                    font-size: 11px !important;
                                    cursor: pointer !important;
                                    font-weight: 500 !important;
                                    transition: all 0.2s !important;
                                `;
                                openAllBtn.onmouseover = function() { 
                                    this.style.background = '#333'; 
                                    this.style.color = '#e5e5e5';
                                };
                                openAllBtn.onmouseout = function() { 
                                    this.style.background = '#2a2a2a'; 
                                    this.style.color = '#9ca3af';
                                };
                                
                                openAllSection.appendChild(openAllBtn);
                                content.appendChild(openAllSection);

                                // Store section references for "Open All" functionality
                                const sectionToggles = [];
                                
                                // Create sections for each category
                                Object.entries(categories).forEach(([categoryName, categoryTrackers]) => {
                                    // Check if this category has any duplicates
                                    const categoryHasDuplicates = Object.keys(categoryTrackers).some(trackerName => duplicates.has(trackerName));
                                    
                                    var section = document.createElement('div');
                                    section.style.cssText = `
                                        background: ${categoryHasDuplicates ? '#2d1a1a' : '#1f1f1f'} !important;
                                        border: 1px solid ${categoryHasDuplicates ? '#dc2626' : '#333'} !important;
                                        border-radius: 8px !important;
                                        margin-bottom: 12px !important;
                                        overflow: hidden !important;
                                    `;

                                    var sectionHeader = document.createElement('div');
                                    sectionHeader.style.cssText = `
                                        display: flex !important;
                                        justify-content: space-between !important;
                                        align-items: center !important;
                                        padding: 12px 16px !important;
                                        background: ${categoryHasDuplicates ? '#3d1e1e' : '#252525'} !important;
                                        border-bottom: 1px solid ${categoryHasDuplicates ? '#dc2626' : '#333'} !important;
                                        cursor: pointer !important;
                                        transition: background 0.2s !important;
                                    `;
                                    
                                    sectionHeader.innerHTML = `
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span style="color: ${categoryHasDuplicates ? '#dc2626' : '#e5e5e5'}; font-size: 13px; font-weight: 500;">${categoryName}</span>
                                            ${categoryHasDuplicates ? '<span style="color: #dc2626; font-size: 11px; font-weight: 600;">⚠ DUPLICATES</span>' : ''}
                                        </div>
                                        <span style="background: ${categoryHasDuplicates ? '#dc2626' : '#333'}; color: #e5e5e5; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: 500;">${Object.keys(categoryTrackers).length}</span>
                                    `;

                                    // Add hover effect
                                    sectionHeader.onmouseover = function() { 
                                        this.style.background = categoryHasDuplicates ? '#4d2424' : '#2a2a2a'; 
                                    };
                                    sectionHeader.onmouseout = function() { 
                                        this.style.background = categoryHasDuplicates ? '#3d1e1e' : '#252525'; 
                                    };

                                    var sectionContent = document.createElement('div');
                                    sectionContent.style.cssText = `
                                        padding: 0 !important;
                                        max-height: ${categoryHasDuplicates ? '500px' : '0'} !important;
                                        overflow: hidden !important;
                                        transition: max-height 0.3s ease !important;
                                    `;

                                    // Auto-open duplicate categories, manual toggle for others
                                    let isExpanded = categoryHasDuplicates;
                                    if (categoryHasDuplicates) {
                                        sectionContent.style.padding = '8px 0';
                                    }
                                    
                                    const toggleSection = function(forceOpen = false) {
                                        if (forceOpen) {
                                            isExpanded = true;
                                        } else {
                                            isExpanded = !isExpanded;
                                        }
                                        
                                        if (isExpanded) {
                                            sectionContent.style.maxHeight = '500px';
                                            sectionContent.style.padding = '8px 0';
                                        } else {
                                            sectionContent.style.maxHeight = '0';
                                            sectionContent.style.padding = '0';
                                        }
                                    };
                                    
                                    // Store toggle function for "Open All" functionality
                                    sectionToggles.push({
                                        categoryName: categoryName,
                                        toggle: toggleSection,
                                        isExpanded: () => isExpanded,
                                        hasDuplicates: categoryHasDuplicates
                                    });
                                    
                                    sectionHeader.onclick = function() {
                                        toggleSection();
                                    };

                                    // Add each tracker in this category
                                    Object.entries(categoryTrackers).forEach(([trackerName, instances]) => {
                                        const isDuplicate = duplicates.has(trackerName);
                                        const hasClick = hasClickTracking(trackerName);
                                        
                                        var trackerDiv = document.createElement('div');
                                        trackerDiv.style.cssText = `
                                            padding: 8px 16px !important;
                                            border-bottom: 1px solid #2a2a2a !important;
                                            transition: background 0.2s !important;
                                        `;
                                        
                                        trackerDiv.onmouseover = function() { this.style.background = '#242424'; };
                                        trackerDiv.onmouseout = function() { this.style.background = 'transparent'; };

                                        let trackerHTML = `
                                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                                <div style="display: flex; align-items: center; gap: 8px;">
                                                    <span style="color: #e5e5e5; font-size: 12px; font-weight: 500;">${trackerName}</span>
                                                    ${isDuplicate ? '<span style="color: #dc2626; font-size: 10px; background: #2d1a1a; padding: 1px 4px; border-radius: 3px;">DUP</span>' : ''}
                                                    ${hasClick ? '<span style="color: #f59e0b; font-size: 10px; background: #2d2416; padding: 1px 4px; border-radius: 3px;">CLICK</span>' : ''}
                                                </div>
                                                <span style="color: #9ca3af; font-size: 11px;">${instances.length}</span>
                                            </div>
                                            <div style="margin-left: 16px;">
                                        `;

                                        instances.forEach((instance, index) => {
                                            const isUrl = instance.url.startsWith('http');
                                            trackerHTML += `
                                                <div style="margin-bottom: 6px; display: flex; align-items: center; gap: 8px;">
                                                    <span style="background: #333; padding: 2px 6px; border-radius: 3px; font-size: 9px; color: #9ca3af; text-transform: uppercase; min-width: 45px; text-align: center;">
                                                        ${instance.type}
                                                    </span>
                                                    <span style="color: ${isUrl ? '#b3b3b3' : '#9ca3af'}; font-size: 13px; word-break: break-all; ${isUrl ? 'cursor: pointer; text-decoration: underline;' : ''}" 
                                                          ${isUrl ? `onclick="window.open('${instance.url}', '_blank')" title="Click to open"` : ''}>
                                                    ${instance.url.length > 60 ? instance.url.substring(0, 60) + '...' : instance.url}
                                                </span>
                                            </div>
                                        `;
                                        });

                                        trackerHTML += `</div>`;
                                        trackerDiv.innerHTML = trackerHTML;
                                        sectionContent.appendChild(trackerDiv);
                                    });

                                    section.appendChild(sectionHeader);
                                    section.appendChild(sectionContent);
                                    content.appendChild(section);
                                });
                                
                                // Add "Open All" button functionality
                                let allOpen = false;
                                openAllBtn.onclick = function() {
                                    allOpen = !allOpen;
                                    
                                    if (allOpen) {
                                        // Open all sections
                                        sectionToggles.forEach(section => {
                                            if (!section.isExpanded()) {
                                                section.toggle(true);
                                            }
                                        });
                                        this.textContent = 'CLOSE ALL';
                                        this.style.background = '#4a4a4a';
                                        this.style.borderColor = '#666';
                                    } else {
                                        // Close all sections (except those with duplicates)
                                        sectionToggles.forEach(section => {
                                            if (section.isExpanded() && !section.hasDuplicates) {
                                                section.toggle();
                                            }
                                        });
                                        this.textContent = 'OPEN ALL';
                                        this.style.background = '#2a2a2a';
                                        this.style.borderColor = '#404040';
                                    }
                                };
                            }

                            panel.appendChild(content);

                            // Close functionality
                            function closePanel() {
                                panel.remove();
                                document.removeEventListener('keydown', handleKeyDown);
                            }

                            function handleKeyDown(e) {
                                if (e.key === 'Escape') {
                                    closePanel();
                                }
                            }

                                                    // Export CSV functionality
                        exportBtn.onclick = function() {
                            const currentDate = new Date().toISOString().split('T')[0];
                            const currentTime = new Date().toLocaleTimeString();
                            const pageUrl = window.location.href;
                            const pageTitle = document.title;
                            
                            let csvContent = 'Tracker Detection Report\n';
                            csvContent += `Generated: ${currentDate} ${currentTime}\n`;
                            csvContent += `Page: ${pageTitle}\n`;
                            csvContent += `URL: ${pageUrl}\n\n`;
                            
                            csvContent += 'Tracker Name,Category,Type,URL,Tracking ID,Is Duplicate,Cross-Tracker Duplicate,Has Click Tracking\n';
                            
                            // Process all detected trackers
                            for (const [trackerName, instances] of Object.entries(detected)) {
                                const category = trackers[trackerName]?.category || 'Other';
                                const isDuplicate = duplicates.has(trackerName);
                                const hasClick = hasClickTracking(trackerName);
                                const trackerDuplicateDetails = duplicateDetails[trackerName] || [];
                                
                                instances.forEach(instance => {
                                    // Extract tracking ID for this specific instance
                                    const trackingId = extractId(instance.url, trackerName) || 'N/A';
                                    
                                    // Check if this specific ID is a cross-tracker duplicate
                                    const isCrossTracker = trackerDuplicateDetails.some(d => d.crossTracker && d.id === trackingId);
                                    
                                    // Escape commas and quotes in CSV
                                    const escapeCsv = (str) => {
                                        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
                                            return '"' + str.replace(/"/g, '""') + '"';
                                        }
                                        return str;
                                    };
                                    
                                    csvContent += `${escapeCsv(trackerName)},${escapeCsv(category)},${escapeCsv(instance.type)},${escapeCsv(instance.url)},${escapeCsv(trackingId)},${isDuplicate ? 'Yes' : 'No'},${isCrossTracker ? 'Yes' : 'No'},${hasClick ? 'Yes' : 'No'}\n`;
                                });
                            }
                            
                            // Create and download CSV file
                            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                            const link = document.createElement('a');
                            const url = URL.createObjectURL(blob);
                            link.setAttribute('href', url);
                            link.setAttribute('download', `tracker-detection-${currentDate}.csv`);
                            link.style.visibility = 'hidden';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            
                            // Show feedback
                            const btn = this;
                            const originalText = btn.textContent;
                            btn.textContent = 'EXPORTED!';
                            btn.style.background = '#16a34a';
                            setTimeout(() => {
                                btn.textContent = originalText;
                                btn.style.background = '#7C3AED';
                            }, 2000);
                        };

                        closeBtn.onclick = closePanel;
                        document.addEventListener('keydown', handleKeyDown);

                            // Simplified drag functionality
                            let isDragging = false;
                            let startX, startY, startLeft, startTop;

                            header.addEventListener('mousedown', function(e) {
                                if (e.target === closeBtn) return;
                                
                                isDragging = true;
                                startX = e.clientX;
                                startY = e.clientY;
                                startLeft = parseInt(panel.style.left);
                                startTop = parseInt(panel.style.top);
                                
                                    header.style.cursor = 'grabbing';
                                e.preventDefault();
                            });

                            document.addEventListener('mousemove', function(e) {
                                if (!isDragging) return;
                                
                                const deltaX = e.clientX - startX;
                                const deltaY = e.clientY - startY;
                                
                                let newLeft = startLeft + deltaX;
                                let newTop = startTop + deltaY;
                                
                                // Keep within bounds
                                newLeft = Math.max(0, Math.min(newLeft, window.innerWidth - panel.offsetWidth));
                                newTop = Math.max(0, Math.min(newTop, window.innerHeight - panel.offsetHeight));
                                
                                panel.style.left = newLeft + 'px';
                                panel.style.top = newTop + 'px';
                            });

                            document.addEventListener('mouseup', function() {
                                isDragging = false;
                                header.style.cursor = 'move';
                            });

                            // Append to body
                            document.body.appendChild(panel);
                            
                            console.log('TrackerDetection: Panel appended to body, should be visible');
                            console.log('TrackerDetection: Panel dimensions:', panel.offsetWidth, 'x', panel.offsetHeight);
                            console.log('TrackerDetection: Panel position:', panel.style.left, panel.style.top);
                        }

                        // Perform single comprehensive scan
                        performComprehensiveScan();
                    }
                });
            });
        } catch (error) {
            console.error('Tracker Detection error:', error);
        }
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                function resetTrackerDetection() {
                    const existingPanel = document.querySelector('.tracker-detection-panel');
                    if (existingPanel) {
                        existingPanel.remove();
                    }
                    console.log('Tracker Detection reset completed');
                }

                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetTrackerDetection
                    }, () => {
                        resolve();
                    });
                });
            } catch (error) {
                console.error('Tracker Detection reset error:', error);
                resolve();
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TrackerDetectionAction;
} else {
    window.TrackerDetectionAction = TrackerDetectionAction;
} 