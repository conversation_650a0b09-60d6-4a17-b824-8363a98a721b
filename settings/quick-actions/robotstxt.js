/**
 * Robots.txt Action
 * Analyzes robots.txt file for current page
 */
class RobotsTxtAction {
  
  static execute() {
    try {
      console.log('Robots.txt Analyzer activated');
      
      // Remove existing robots.txt panel if present
      document.querySelectorAll('.robotstxt-audit-panel').forEach(panel => panel.remove());
      
      // Helper functions
      function escape(str) {
        if (!str) return '';
        return str.replace(/&/g, '&amp;')
                 .replace(/</g, '&lt;')
                 .replace(/>/g, '&gt;')
                 .replace(/"/g, '&quot;')
                 .replace(/'/g, '&#39;');
      }
      
      function hlCode(str) {
        return '<code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + str + '</code>';
      }
      
      function createRow(title, info, content) {
        return `
          <div style="display:flex;padding:16px 0;border-bottom:1px solid #2a2a2a;">
            <div style="min-width:140px;font-weight:500;color:#9ca3af;font-size:13px;text-transform:uppercase;margin-right:20px;">${title}</div>
            <div style="flex:1;">
              ${info ? `<div style="font-size:12px;color:#6b7280;margin-bottom:8px;">${info}</div>` : ''}
              <div style="color:#d1d5db;line-height:1.5;">${content}</div>
            </div>
          </div>
        `;
      }
      
      // Get saved position and size from localStorage
      function getSavedPanelSettings() {
        try {
          const saved = localStorage.getItem('robotstxt-audit-panel-settings');
          if (saved) {
            return JSON.parse(saved);
          }
        } catch (e) {
          console.log('Error loading saved panel settings:', e);
        }
        // Default settings
        return {
          top: '20px',
          left: '20px', 
          width: '50%',
          height: '85vh'
        };
      }
      
      // Save panel position and size to localStorage
      function savePanelSettings(panel) {
        try {
          const settings = {
            top: panel.style.top || '20px',
            left: panel.style.left || '20px',
            right: panel.style.right || '',
            width: panel.style.width || '50%',
            height: panel.style.height || '85vh'
          };
          localStorage.setItem('robotstxt-audit-panel-settings', JSON.stringify(settings));
        } catch (e) {
          console.log('Error saving panel settings:', e);
        }
      }

      // Analyze robots.txt
      const currentUrl = new URL(window.location.href);
      const robotsUrl = `${currentUrl.protocol}//${currentUrl.hostname}/robots.txt`;
      const currentPath = currentUrl.pathname;
      
      fetch(robotsUrl)
        .then(response => response.text())
        .then(robotsContent => {
          // Parse robots.txt
          const lines = robotsContent.split('\n').map(line => line.trim()).filter(line => line);
          const userAgents = [];
          const disallows = [];
          const allows = [];
          const sitemaps = [];
          const others = [];
          
          let currentUserAgent = '*';
          
          lines.forEach(line => {
            if (line.startsWith('#')) return; // Skip comments
            
            const [directive, ...valueParts] = line.split(':');
            const value = valueParts.join(':').trim();
            
            switch (directive.toLowerCase()) {
              case 'user-agent':
                currentUserAgent = value;
                userAgents.push({ userAgent: value, line });
                break;
              case 'disallow':
                disallows.push({ userAgent: currentUserAgent, path: value, line });
                break;
              case 'allow':
                allows.push({ userAgent: currentUserAgent, path: value, line });
                break;
              case 'sitemap':
                sitemaps.push({ url: value, line });
                break;
              default:
                others.push({ directive: directive.toLowerCase(), value, line });
            }
          });
          
          // Check if current page is allowed
          let isDisallowed = false;
          let isAllowed = false;
          let matchingRule = '';
          
          // Check disallow rules
          disallows.forEach(rule => {
            if ((rule.userAgent === '*' || rule.userAgent.toLowerCase() === 'googlebot') && rule.path) {
              if (currentPath.startsWith(rule.path)) {
                isDisallowed = true;
                matchingRule = rule.line;
              }
            }
          });
          
          // Check allow rules (override disallow)
          allows.forEach(rule => {
            if ((rule.userAgent === '*' || rule.userAgent.toLowerCase() === 'googlebot') && rule.path) {
              if (currentPath.startsWith(rule.path)) {
                isAllowed = true;
                matchingRule = rule.line;
              }
            }
          });
          
          const finalStatus = isAllowed ? 'allowed' : (isDisallowed ? 'disallowed' : 'allowed');
          
          // Create panel
          var panel = document.createElement('div');
          panel.className = 'robotstxt-audit-panel';
          
          // Load saved settings
          const savedSettings = getSavedPanelSettings();
          const positionStyle = savedSettings.right ? 
            `right:${savedSettings.right};` : 
            `left:${savedSettings.left};`;
          
          panel.style.cssText = `position:fixed;top:${savedSettings.top};${positionStyle}width:${savedSettings.width};max-height:${savedSettings.height};z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;overflow:auto;resize:both;min-width:400px;min-height:500px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5`;
          
          let html = `
            <div id="robotstxt-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;">
              <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;"><span style="color: #7C3AED; font-size: 18px;">●</span> Robots.txt Analyzer</h2>
              <button id="robotstxt-close-btn" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
            </div>
          `;
          
          // Current Page Status
          const statusColor = finalStatus === 'allowed' ? '#10b981' : '#f59e0b';
          html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
          html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Current Page Status</div>';
          html += '<div style="padding:0 20px;">';
          html += createRow(
            'Page Status',
            `<span style="color:${statusColor};font-weight:500;">${finalStatus.toUpperCase()}</span>`,
            `<div style="margin-bottom:8px;">Path: ${hlCode(escape(currentPath))}</div>${matchingRule ? `<div>Matching rule: ${hlCode(escape(matchingRule))}</div>` : '<div style="color:#6b7280;">No specific rule found (default allowed)</div>'}`
          );
          html += createRow(
            'Robots.txt URL',
            '',
            `<a href="${escape(robotsUrl)}" target="_blank" style="color:#e5e7eb;text-decoration:none;">${escape(robotsUrl)}</a>`
          );
          html += '</div>';
          html += '</div>';
          
          // Rules Analysis
          html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
          html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Rules Analysis</div>';
          html += '<div style="padding:0 20px;">';
          
          if (userAgents.length > 0) {
            html += createRow('User Agents', `${userAgents.length} found`, userAgents.map(ua => hlCode(escape(ua.line))).join('<br style="margin-bottom:4px;">'));
          }
          
          if (disallows.length > 0) {
            html += createRow('Disallow Rules', `${disallows.length} found`, disallows.map(rule => hlCode(escape(rule.line))).join('<br style="margin-bottom:4px;">'));
          }
          
          if (allows.length > 0) {
            html += createRow('Allow Rules', `${allows.length} found`, allows.map(rule => hlCode(escape(rule.line))).join('<br style="margin-bottom:4px;">'));
          }
          
          if (sitemaps.length > 0) {
            html += createRow('Sitemaps', `${sitemaps.length} found`, sitemaps.map(sitemap => `<a href="${escape(sitemap.url)}" target="_blank" style="color:#e5e7eb;text-decoration:none;">${escape(sitemap.url)}</a>`).join('<br style="margin-bottom:4px;">'));
          }
          
          if (others.length > 0) {
            html += createRow('Other Directives', `${others.length} found`, others.map(other => hlCode(escape(other.line))).join('<br style="margin-bottom:4px;">'));
          }
          
          html += '</div>';
          html += '</div>';
          
          // Full robots.txt content
          html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
          html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Full robots.txt Content</div>';
          html += '<div style="padding:20px;">';
          html += `<pre style="background:#000000;padding:16px;border-radius:6px;color:#d1d5db;font-family:monospace;font-size:12px;line-height:1.6;overflow-x:auto;white-space:pre-wrap;margin:0;">${escape(robotsContent)}</pre>`;
          html += '</div>';
          html += '</div>';
          
          // Info note
          html += '<div style="background:#1a1a1a;border-radius:6px;border:1px solid #2a2a2a;padding:16px;text-align:center;color:#6b7280;font-size:12px;">Learn more about robots.txt at <a href="https://www.robotstxt.org/" target="_blank" style="color:#e5e7eb;text-decoration:none;">robotstxt.org</a></div>';
          
          panel.innerHTML = html;
          document.body.appendChild(panel);
          
          // Add close button functionality
          const closeBtn = panel.querySelector('#robotstxt-close-btn');
          function closePanel() {
            // Clean up all event listeners before removing panel
            document.removeEventListener('keydown', handleKeyDown);
            if (window.ResizeObserver && resizeObserver) {
              resizeObserver.disconnect();
            }
            header.removeEventListener('mousedown', dragStart);
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', dragEnd);
            header.removeEventListener('touchstart', dragStart);
            document.removeEventListener('touchmove', drag);
            document.removeEventListener('touchend', dragEnd);
            
            // Remove the panel
            panel.remove();
            
            console.log('Robots.txt analyzer panel closed');
          }
          
          if (closeBtn) {
            closeBtn.addEventListener('click', function(e) {
              e.stopPropagation();
              closePanel();
            });
          }
          
          // Add drag functionality
          var isDragging = false;
          var currentX;
          var currentY;
          var initialX;
          var initialY;
          var xOffset = 0;
          var yOffset = 0;
          
          var header = panel.querySelector('#robotstxt-header');
          var resizeObserver;
          
          // Handle Escape key to close panel
          function handleKeyDown(e) {
            if (e.key === 'Escape') {
              closePanel();
            }
          }
          
          function dragStart(e) {
            if (e.target.tagName === 'BUTTON' || e.target.id === 'robotstxt-close-btn') return;
            
            if (e.type === "touchstart") {
              initialX = e.touches[0].clientX - xOffset;
              initialY = e.touches[0].clientY - yOffset;
            } else {
              initialX = e.clientX - xOffset;
              initialY = e.clientY - yOffset;
            }
            
            if (e.target === header || header.contains(e.target)) {
              isDragging = true;
              panel.style.cursor = 'grabbing';
              header.style.cursor = 'grabbing';
            }
          }
          
          function dragEnd(e) {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            panel.style.cursor = 'default';
            header.style.cursor = 'move';
            savePanelSettings(panel);
          }
          
          function drag(e) {
            if (isDragging) {
              e.preventDefault();
              
              if (e.type === "touchmove") {
                currentX = e.touches[0].clientX - initialX;
                currentY = e.touches[0].clientY - initialY;
              } else {
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
              }
              
              xOffset = currentX;
              yOffset = currentY;
              
              var rect = panel.getBoundingClientRect();
              var maxX = window.innerWidth - rect.width;
              var maxY = window.innerHeight - rect.height;
              
              currentX = Math.max(0, Math.min(currentX, maxX));
              currentY = Math.max(0, Math.min(currentY, maxY));
              
              panel.style.right = '';
              panel.style.left = currentX + 'px';
              panel.style.top = currentY + 'px';
            }
          }
          
          header.addEventListener('mousedown', dragStart);
          document.addEventListener('mousemove', drag);
          document.addEventListener('mouseup', dragEnd);
          header.addEventListener('touchstart', dragStart);
          document.addEventListener('touchmove', drag);
          document.addEventListener('touchend', dragEnd);
          document.addEventListener('keydown', handleKeyDown);
          
          // Add resize observer to save size changes
          if (window.ResizeObserver) {
            resizeObserver = new ResizeObserver(function(entries) {
              savePanelSettings(panel);
            });
            resizeObserver.observe(panel);
          }
          
          var rect = panel.getBoundingClientRect();
          xOffset = rect.left;
          yOffset = rect.top;
        })
        .catch(error => {
          console.error('Error fetching robots.txt:', error);
          alert('Error fetching robots.txt: ' + error.message);
        });
      
      return { 
        success: true, 
        message: 'Robots.txt Analyzer activated successfully'
      };
    } catch (error) {
      console.error('Error executing Robots.txt Analyzer:', error);
      alert("Error applying Robots.txt Analyzer: " + error.message);
      return { error: error.message };
    }
  }
  
  static reset() {
    try {
      const existingPanel = document.querySelector('.robotstxt-audit-panel');
      if (existingPanel) {
        existingPanel.remove();
        console.log('Robots.txt audit panel removed');
      }
      console.log('Robots.txt reset completed');
      return { success: true, message: 'Robots.txt reset completed' };
    } catch (error) {
      console.error('Error resetting Robots.txt Analyzer:', error);
      return { error: error.message };
    }
  }
}

// Make it available globally
if (typeof window !== 'undefined') {
  window.RobotsTxtAction = RobotsTxtAction;
} 