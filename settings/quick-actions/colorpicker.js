/**
 * Color Picker Action - Interactive color picking tool with EyeDropper API
 * Provides color picking functionality with stored colors history
 */
class ColorPickerAction {
    static execute() {
        // Remove existing color picker panels if present
        document.querySelectorAll('.color-picker-panel, .color-picker-tooltip').forEach(panel => panel.remove());
        
        // Remove any existing event listeners
        if (window.colorPickerCleanup) {
            window.colorPickerCleanup();
        }
        
        // Check if EyeDropper API is supported
        if (!window.EyeDropper) {
            alert('Your browser does not support the EyeDropper API. Please use a modern browser like Chrome or Edge.');
            return;
        }
        
        // Helper functions
        function escape(str) {
            if (!str) return '';
            return str.replace(/&/g, '&amp;')
                     .replace(/</g, '&lt;')
                     .replace(/>/g, '&gt;')
                     .replace(/"/g, '&quot;')
                     .replace(/'/g, '&#39;');
        }
        
        function hlCode(str) {
            return '<code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + str + '</code>';
        }
        
        function lightOrDark(color) {
            var r, g, b;
            if (color.match(/^rgb/)) {
                var rgb = color.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/);
                r = rgb[1];
                g = rgb[2]; 
                b = rgb[3];
            } else {
                var hex = +("0x" + color.slice(1).replace(color.length < 5 && /./g, "$&$&"));
                r = hex >> 16;
                g = (hex >> 8) & 255;
                b = 255 & hex;
            }
            return Math.sqrt(r * r * 0.299 + g * g * 0.587 + b * b * 0.114) > 127.5 ? "light" : "dark";
        }
        
        function hexToRGB(hex, returnArray) {
            hex = hex.replace("#", "").match(/.{1,2}/g);
            hex = [parseInt(hex[0], 16), parseInt(hex[1], 16), parseInt(hex[2], 16)];
            return returnArray ? hex : "rgb(" + hex + ")";
        }
        
        function rgbToHsl(r, g, b) {
            r /= 255;
            g /= 255;
            b /= 255;
            const max = Math.max(r, g, b);
            const min = Math.min(r, g, b);
            let h, s, l = (max + min) / 2;
            
            if (max === min) {
                h = s = 0; // achromatic
            } else {
                const d = max - min;
                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                switch (max) {
                    case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                    case g: h = (b - r) / d + 2; break;
                    case b: h = (r - g) / d + 4; break;
                }
                h /= 6;
            }
            
            return {
                h: Math.round(h * 360),
                s: Math.round(s * 100),
                l: Math.round(l * 100)
            };
        }
        
        // Get saved position and size from localStorage
        function getSavedPanelSettings() {
            try {
                const saved = localStorage.getItem('color-picker-panel-settings');
                if (saved) {
                    return JSON.parse(saved);
                }
            } catch (e) {
                console.log('Error loading saved panel settings:', e);
            }
            return {
                top: '20px',
                left: '20px',
                width: '450px',
                height: '85vh'
            };
        }
        
        // Save panel position and size to localStorage
        function savePanelSettings(panel) {
            try {
                const settings = {
                    top: panel.style.top || '20px',
                    left: panel.style.left || '20px',
                    right: panel.style.right || '',
                    width: panel.style.width || '450px',
                    height: panel.style.height || '85vh'
                };
                localStorage.setItem('color-picker-panel-settings', JSON.stringify(settings));
            } catch (e) {
                console.log('Error saving panel settings:', e);
            }
        }
        
        // Create main control panel
        var panel = document.createElement('div');
        panel.className = 'color-picker-panel';
        
        // Load saved settings
        const savedSettings = getSavedPanelSettings();
        const positionStyle = savedSettings.right ? 
            `right:${savedSettings.right};` : 
            `left:${savedSettings.left};`;
        
        panel.style.cssText = `position:fixed;top:${savedSettings.top};${positionStyle}width:${savedSettings.width};max-height:${savedSettings.height};z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5;overflow:auto;resize:both;min-width:400px;min-height:500px;`;
        
        let html = `
            <div id="color-picker-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid #2a2a2a;cursor:move;">
                <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;">Color Picker</h2>
                <button onclick="this.parentNode.parentNode.remove();if(window.colorPickerCleanup)window.colorPickerCleanup();" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
            </div>
        `;
        
        // Current Color Section
        html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:20px;overflow:hidden;">';
        html += '<div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:13px;">Current Color</div>';
        html += '<div style="padding:16px;">';
        
        // Color picker input and display
        html += '<div style="display:flex;gap:16px;align-items:center;margin-bottom:16px;">';
        html += '<input type="color" id="color-input" style="width:60px;height:60px;border:2px solid #2a2a2a;border-radius:8px;cursor:pointer;background:none;" value="#7C3AED">';
        html += '<div style="flex:1;">';
        html += '<div style="display:flex;gap:8px;margin-bottom:8px;">';
        html += '<div id="eyedropper-status" style="padding:8px 16px;background:#1a1a1a;border:1px solid #2a2a2a;border-radius:6px;color:#9ca3af;font-size:12px;display:flex;align-items:center;gap:6px;">EyeDropper Active - Click anywhere to pick colors</div>';
        html += '<button id="pick-another-btn" style="padding:8px 16px;background:#6366f1;color:#fff;border:none;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;display:none;">Pick Another Color</button>';
        html += '</div>';

        html += '</div>';
        html += '</div>';
        
        html += '</div>';
        html += '</div>';
        
        // Stored Colors Section
        html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;overflow:hidden;">';
        html += '<div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:13px;cursor:pointer;display:flex;justify-content:space-between;align-items:center;" id="stored-colors-header">';
        html += '<span>Stored Colors</span>';
        html += '<span id="stored-toggle" style="color:#e5e7eb;">▼</span>';
        html += '</div>';
        html += '<div id="stored-colors-content">';
        html += '<div id="stored-colors-list" style="padding:16px;color:#6b7280;font-style:italic;">Colors will be automatically stored here when you pick them.</div>';
        html += '</div>';
        html += '<div style="padding:12px 16px;border-top:1px solid #2a2a2a;background:#0f0f0f;display:flex;gap:8px;" id="export-buttons" style="display:none;">';
        html += '<button id="copy-text-btn" style="flex:1;padding:8px 12px;background:#8b5cf6;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:11px;font-weight:500;transition:all 0.2s;">Copy Text</button>';
        html += '<button id="export-csv-btn" style="flex:1;padding:8px 12px;background:#059669;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:11px;font-weight:500;transition:all 0.2s;">Export CSV</button>';
        html += '</div>';
        html += '</div>';
        
        // Options Section
        html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-top:20px;overflow:hidden;">';
        html += '<details style="padding:0;">';
        html += '<summary style="padding:12px 16px;background:#1a1a1a;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:13px;cursor:pointer;list-style:none;">⚙️ Options</summary>';
        html += '<div style="padding:16px;">';
        html += '<p style="margin:0 0 16px 0;font-size:12px;color:#6b7280;">Auto-copying will copy the color value to clipboard when you pick a color.</p>';
        
        html += '<fieldset style="border:1px solid #2a2a2a;border-radius:6px;margin:0;padding:12px;">';
        html += '<legend style="padding:0 8px;font-weight:500;color:#9ca3af;font-size:12px;">Auto-copying</legend>';
        
        html += '<div style="display:flex;flex-direction:column;gap:8px;">';
        html += '<label style="display:flex;align-items:center;gap:8px;color:#d1d5db;cursor:pointer;">';
        html += '<input type="radio" name="color_picker_auto_copying" id="auto_copying_disabled" value="disabled" style="width:16px;height:16px;">';
        html += '<span>Disabled</span>';
        html += '</label>';
        
        html += '<label style="display:flex;align-items:center;gap:8px;color:#d1d5db;cursor:pointer;">';
        html += '<input type="radio" name="color_picker_auto_copying" id="auto_copying_hex" value="hex" checked style="width:16px;height:16px;">';
        html += '<span>Enable Hex auto-copying</span>';
        html += '</label>';
        
        html += '<label style="display:flex;align-items:center;gap:8px;color:#d1d5db;cursor:pointer;">';
        html += '<input type="radio" name="color_picker_auto_copying" id="auto_copying_rgb" value="rgb" style="width:16px;height:16px;">';
        html += '<span>Enable RGB auto-copying</span>';
        html += '</label>';
        
        html += '<label style="display:flex;align-items:center;gap:8px;color:#d1d5db;cursor:pointer;">';
        html += '<input type="radio" name="color_picker_auto_copying" id="auto_copying_hsl" value="hsl" style="width:16px;height:16px;">';
        html += '<span>Enable HSL auto-copying</span>';
        html += '</label>';
        
        html += '<label style="display:flex;align-items:center;gap:8px;color:#d1d5db;cursor:pointer;">';
        html += '<input type="radio" name="color_picker_auto_copying" id="auto_copying_css" value="css" style="width:16px;height:16px;">';
        html += '<span>Enable CSS auto-copying</span>';
        html += '</label>';
        html += '</div>';
        
        html += '</fieldset>';
        html += '</div>';
        html += '</details>';
        html += '</div>';
        
        panel.innerHTML = html;
        document.body.appendChild(panel);
        
        // Get DOM elements
        const colorInput = panel.querySelector('#color-input');
        const storedColorsList = panel.querySelector('#stored-colors-list');
        const storedColorsHeader = panel.querySelector('#stored-colors-header');
        const storedToggle = panel.querySelector('#stored-toggle');
        const storedContent = panel.querySelector('#stored-colors-content');
        const eyedropperStatus = panel.querySelector('#eyedropper-status');
        const pickAnotherBtn = panel.querySelector('#pick-another-btn');
        const exportButtons = panel.querySelector('#export-buttons');
        const copyTextBtn = panel.querySelector('#copy-text-btn');
        const exportCsvBtn = panel.querySelector('#export-csv-btn');
        
        // Auto-copying option elements
        const autoCopyingDisabled = panel.querySelector('#auto_copying_disabled');
        const autoCopyingHex = panel.querySelector('#auto_copying_hex');
        const autoCopyingRgb = panel.querySelector('#auto_copying_rgb');
        const autoCopyingHsl = panel.querySelector('#auto_copying_hsl');
        const autoCopyingCss = panel.querySelector('#auto_copying_css');
        
        let storedColors = [];
        let isStoredSectionExpanded = true;
        let autoCopyingSetting = 'hex';
        
        // Load auto-copying setting from localStorage
        function loadAutoCopyingSetting() {
            try {
                const saved = localStorage.getItem('color_picker_auto_copying');
                if (saved && ['disabled', 'hex', 'rgb', 'hsl', 'css'].includes(saved)) {
                    autoCopyingSetting = saved;
                    
                    // Set the radio button
                    if (saved === 'hex') {
                        autoCopyingHex.checked = true;
                    } else if (saved === 'rgb') {
                        autoCopyingRgb.checked = true;
                    } else if (saved === 'hsl') {
                        autoCopyingHsl.checked = true;
                    } else if (saved === 'css') {
                        autoCopyingCss.checked = true;
                    } else {
                        autoCopyingDisabled.checked = true;
                    }
                } else {
                    // Default to hex if no saved setting exists
                    autoCopyingSetting = 'hex';
                    autoCopyingHex.checked = true;
                }
            } catch (e) {
                console.log('Error loading auto-copying setting:', e);
                // Default to hex on error
                autoCopyingSetting = 'hex';
                autoCopyingHex.checked = true;
            }
        }
        
        // Save auto-copying setting to localStorage
        function saveAutoCopyingSetting(value) {
            try {
                autoCopyingSetting = value;
                localStorage.setItem('color_picker_auto_copying', value);
            } catch (e) {
                console.log('Error saving auto-copying setting:', e);
            }
        }
        
        // Update color display and auto-store
        function updateColorDisplay(color, autoStore = true) {
            colorInput.value = color;
            
            // Automatically store the color if it's new and autoStore is true
            if (autoStore) {
                addColorToStorage(color);
                
                // Auto-copy based on user setting
                if (autoCopyingSetting === 'hex') {
                    copyToClipboardSilent(color);
                } else if (autoCopyingSetting === 'rgb') {
                    copyToClipboardSilent(hexToRGB(color));
                } else if (autoCopyingSetting === 'hsl') {
                    const rgb = hexToRGB(color, true);
                    const hsl = rgbToHsl(rgb[0], rgb[1], rgb[2]);
                    copyToClipboardSilent(`hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`);
                } else if (autoCopyingSetting === 'css') {
                    copyToClipboardSilent(`color: ${color};`);
                }
            }
        }
        
        // Auto-store color function
        function addColorToStorage(hexColor) {
            const currentHex = hexColor;
            const currentRgb = hexToRGB(hexColor);
            
            // Check if color already exists
            const exists = storedColors.some(color => color.hex === currentHex);
            if (exists) {
                return; // Don't add duplicates, but don't show error
            }
            
            const colorData = {
                id: Date.now(),
                hex: currentHex,
                rgb: currentRgb,
                timestamp: new Date().toLocaleTimeString()
            };
            
            storedColors.unshift(colorData);
            updateStoredColorsList();
        }
        
        // Copy to clipboard function
        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '✓';
                button.style.background = '#10b981';
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#10b981';
                }, 1000);
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            });
        }
        
        // Silent copy to clipboard (for auto-copying)
        function copyToClipboardSilent(text) {
            navigator.clipboard.writeText(text).then(() => {
                console.log('Auto-copied:', text);
            }).catch(() => {
                // Fallback for older browsers
                try {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    console.log('Auto-copied (fallback):', text);
                } catch (e) {
                    console.log('Auto-copy failed:', e);
                }
            });
        }
        
        // Toggle stored colors section
        storedColorsHeader.addEventListener('click', function() {
            isStoredSectionExpanded = !isStoredSectionExpanded;
            storedContent.style.display = isStoredSectionExpanded ? 'block' : 'none';
            storedToggle.textContent = isStoredSectionExpanded ? '▼' : '▶';
        });
        
        // Color input change handler
        colorInput.addEventListener('input', function() {
            updateColorDisplay(this.value);
        });
        
        // Copy button handlers
        // Add event listener for "Pick Another Color" button
        pickAnotherBtn.addEventListener('click', function() {
            activateEyeDropper();
        });
        
        // Export functions
        function generateTextExport() {
            if (storedColors.length === 0) return '';
            
            const currentUrl = window.location.href;
            const timestamp = new Date().toLocaleString();
            
            let text = `Color Picker Export\n`;
            text += `===================\n`;
            text += `URL: ${currentUrl}\n`;
            text += `Exported: ${timestamp}\n`;
            text += `Total Colors: ${storedColors.length}\n\n`;
            
            storedColors.forEach((colorData, index) => {
                const rgb = hexToRGB(colorData.hex, true);
                const hsl = rgbToHsl(rgb[0], rgb[1], rgb[2]);
                const brightness = lightOrDark(colorData.hex);
                
                text += `Color ${index + 1}:\n`;
                text += `  HEX: ${colorData.hex.toUpperCase()}\n`;
                text += `  RGB: rgb(${rgb.join(', ')})\n`;
                text += `  HSL: hsl(${hsl.h}°, ${hsl.s}%, ${hsl.l}%)\n`;
                text += `  Brightness: ${brightness}\n`;
                text += `  Picked: ${colorData.timestamp}\n\n`;
            });
            
            return text;
        }
        
        function generateCsvExport() {
            if (storedColors.length === 0) return '';
            
            const headers = ['Color_Index', 'HEX', 'RGB_R', 'RGB_G', 'RGB_B', 'RGB_String', 'HSL_H', 'HSL_S', 'HSL_L', 'HSL_String', 'Brightness', 'Timestamp', 'URL'];
            let csv = headers.join(',') + '\n';
            
            const currentUrl = window.location.href;
            
            storedColors.forEach((colorData, index) => {
                const rgb = hexToRGB(colorData.hex, true);
                const hsl = rgbToHsl(rgb[0], rgb[1], rgb[2]);
                const brightness = lightOrDark(colorData.hex);
                
                const row = [
                    index + 1,
                    `"${colorData.hex.toUpperCase()}"`,
                    rgb[0],
                    rgb[1], 
                    rgb[2],
                    `"rgb(${rgb.join(', ')})"`,
                    hsl.h,
                    hsl.s,
                    hsl.l,
                    `"hsl(${hsl.h}°, ${hsl.s}%, ${hsl.l}%)"`,
                    `"${brightness}"`,
                    `"${colorData.timestamp}"`,
                    `"${currentUrl}"`
                ];
                
                csv += row.join(',') + '\n';
            });
            
            return csv;
        }
        
        function downloadCsv(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
            }
        }
        
        // Export button event listeners
        copyTextBtn.addEventListener('click', function() {
            const textExport = generateTextExport();
            if (textExport) {
                copyToClipboard(textExport, this);
            } else {
                this.textContent = 'No Colors!';
                this.style.background = '#f59e0b';
                setTimeout(() => {
                    this.textContent = 'Copy Text';
                    this.style.background = '#8b5cf6';
                }, 1000);
            }
        });
        
        exportCsvBtn.addEventListener('click', function() {
            const csvContent = generateCsvExport();
            if (csvContent) {
                // Generate filename from current URL
                const currentUrl = new URL(window.location.href);
                const domain = currentUrl.hostname.replace(/[^a-z0-9]/gi, '_');
                const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
                const filename = `${domain}_${timestamp}_colours.csv`;
                
                downloadCsv(csvContent, filename);
                
                // Visual feedback
                const originalText = this.textContent;
                this.textContent = '✓ Downloaded!';
                this.style.background = '#10b981';
                setTimeout(() => {
                    this.textContent = originalText;
                    this.style.background = '#059669';
                }, 2000);
            } else {
                this.textContent = 'No Colors!';
                this.style.background = '#f59e0b';
                setTimeout(() => {
                    this.textContent = 'Export CSV';
                    this.style.background = '#059669';
                }, 1000);
            }
        });
        
        // Automatic EyeDropper functionality - activate immediately on panel open
        function activateEyeDropper() {
            if (!window.EyeDropper) {
                console.log('EyeDropper API not supported in this browser');
                eyedropperStatus.textContent = 'EyeDropper not supported in this browser';
                eyedropperStatus.style.color = '#f59e0b';
                return;
            }
            
            // Update UI to show EyeDropper is active
            eyedropperStatus.innerHTML = 'EyeDropper Active - Click anywhere to pick colors';
            eyedropperStatus.style.color = '#9ca3af';
            pickAnotherBtn.style.display = 'none';
            
            const eyeDropper = new EyeDropper();
            eyeDropper.open().then(result => {
                updateColorDisplay(result.sRGBHex);
                
                // Update UI to show EyeDropper completed and offer to pick another
                eyedropperStatus.innerHTML = 'Color picked! Click the button below to pick another';
                eyedropperStatus.style.color = '#10b981';
                pickAnotherBtn.style.display = 'block';
                
            }).catch(error => {
                console.log('EyeDropper cancelled or failed:', error);
                // Update UI to show cancelled state
                eyedropperStatus.innerHTML = 'Pick cancelled - Click the button below to try again';
                eyedropperStatus.style.color = '#f59e0b';
                pickAnotherBtn.style.display = 'block';
            });
        }
        
        // Auto-copying option event listeners
        autoCopyingDisabled.addEventListener('change', function() {
            if (this.checked) saveAutoCopyingSetting('disabled');
        });
        
        autoCopyingHex.addEventListener('change', function() {
            if (this.checked) saveAutoCopyingSetting('hex');
        });
        
        autoCopyingRgb.addEventListener('change', function() {
            if (this.checked) saveAutoCopyingSetting('rgb');
        });
        
        autoCopyingHsl.addEventListener('change', function() {
            if (this.checked) saveAutoCopyingSetting('hsl');
        });
        
        autoCopyingCss.addEventListener('change', function() {
            if (this.checked) saveAutoCopyingSetting('css');
        });
        
        // Load saved auto-copying setting
        loadAutoCopyingSetting();
        
        // Start EyeDropper immediately when panel opens
        setTimeout(() => activateEyeDropper(), 500);
        
        function updateStoredColorsList() {
            if (storedColors.length === 0) {
                storedColorsList.innerHTML = '<div style="padding:16px;color:#6b7280;font-style:italic;">Colors will be automatically stored here when you pick them.</div>';
                exportButtons.style.display = 'none';
                return;
            }
            
            // Show export buttons when there are colors
            exportButtons.style.display = 'flex';
            
            let html = '';
            storedColors.forEach((colorData, index) => {
                // Calculate additional color information
                const rgb = hexToRGB(colorData.hex, true);
                const hsl = rgbToHsl(rgb[0], rgb[1], rgb[2]);
                const brightness = lightOrDark(colorData.hex);
                const contrast = brightness === 'light' ? '#000000' : '#ffffff';
                
                html += `
                    <div style="padding:16px;border-bottom:1px solid #2a2a2a;${index === storedColors.length - 1 ? 'border-bottom:none;' : ''}">
                        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;">
                            <div style="display:flex;align-items:center;gap:12px;">
                                <div style="width:32px;height:32px;border-radius:6px;border:1px solid #2a2a2a;background:${colorData.hex};position:relative;overflow:hidden;">
                                    <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:${contrast};font-size:10px;font-weight:bold;text-shadow:0 0 2px rgba(0,0,0,0.8);">●</div>
                                </div>
                                <div>
                                    <div style="color:#d1d5db;font-weight:500;">${colorData.hex.toUpperCase()}</div>
                                </div>
                            </div>
                            <button onclick="event.stopPropagation();" data-color="${escape(colorData.hex)}" class="copy-hex-btn" style="padding:6px 12px;background:#10b981;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:11px;font-weight:500;transition:all 0.2s;">Copy HEX</button>
                        </div>
                        
                        <div style="display:grid;grid-template-columns:1fr 1fr 1fr;gap:12px;margin-bottom:12px;">
                            <div style="background:#1a1a1a;padding:8px;border-radius:4px;border:1px solid #2a2a2a;">
                                <div style="color:#9ca3af;font-size:10px;margin-bottom:4px;font-weight:500;">HEX</div>
                                <div style="font-family:monospace;color:#d1d5db;font-size:11px;">${escape(colorData.hex.toUpperCase())}</div>
                            </div>
                            <div style="background:#1a1a1a;padding:8px;border-radius:4px;border:1px solid #2a2a2a;">
                                <div style="color:#9ca3af;font-size:10px;margin-bottom:4px;font-weight:500;">RGB</div>
                                <div style="font-family:monospace;color:#d1d5db;font-size:11px;">rgb(${rgb.join(', ')})</div>
                            </div>
                            <div style="background:#1a1a1a;padding:8px;border-radius:4px;border:1px solid #2a2a2a;">
                                <div style="color:#9ca3af;font-size:10px;margin-bottom:4px;font-weight:500;">HSL</div>
                                <div style="font-family:monospace;color:#d1d5db;font-size:11px;">hsl(${hsl.h}°, ${hsl.s}%, ${hsl.l}%)</div>
                            </div>
                        </div>
                        
                        <div style="display:grid;grid-template-columns:1fr auto 1fr auto;gap:8px;">
                            <button onclick="event.stopPropagation();" data-color="${escape(colorData.hex)}" class="copy-color-btn" style="padding:6px 8px;background:#6366f1;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:10px;transition:all 0.2s;">Copy HEX</button>
                            <button onclick="event.stopPropagation();" data-color="rgb(${rgb.join(', ')})" class="copy-color-btn" style="padding:6px 8px;background:#6366f1;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:10px;transition:all 0.2s;">Copy RGB</button>
                            <button onclick="event.stopPropagation();" data-color="hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)" class="copy-color-btn" style="padding:6px 8px;background:#6366f1;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:10px;transition:all 0.2s;">Copy HSL</button>
                            <button onclick="event.stopPropagation();" data-color="${escape(colorData.hex)}" class="copy-css-btn" style="padding:6px 8px;background:#8b5cf6;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:10px;transition:all 0.2s;">Copy CSS</button>
                        </div>
                    </div>
                `;
            });
            
            storedColorsList.innerHTML = html;
            
            // Add event listeners to copy buttons
            const copyButtons = storedColorsList.querySelectorAll('.copy-color-btn, .copy-hex-btn');
            copyButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const color = this.getAttribute('data-color');
                    copyToClipboard(color, this);
                });
            });
            
            // Add event listeners to CSS copy buttons (copies CSS color property)
            const copyCssButtons = storedColorsList.querySelectorAll('.copy-css-btn');
            copyCssButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const color = this.getAttribute('data-color');
                    const cssProperty = `color: ${color};`;
                    copyToClipboard(cssProperty, this);
                });
            });
        }
        
        // Initialize with purple color (don't auto-store default)
        updateColorDisplay('#7C3AED', false);
        
        // Add drag functionality
        var isDragging = false;
        var currentX;
        var currentY;
        var initialX;
        var initialY;
        var xOffset = 0;
        var yOffset = 0;
        
        var header = panel.querySelector('#color-picker-header');
        
        function dragStart(e) {
            if (e.target.tagName === 'BUTTON') return;
            
            if (e.type === "touchstart") {
                initialX = e.touches[0].clientX - xOffset;
                initialY = e.touches[0].clientY - yOffset;
            } else {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;
            }
            
            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
                panel.style.cursor = 'grabbing';
                header.style.cursor = 'grabbing';
            }
        }
        
        function dragEnd(e) {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            panel.style.cursor = 'default';
            header.style.cursor = 'move';
            savePanelSettings(panel);
        }
        
        function drag(e) {
            if (isDragging) {
                e.preventDefault();
                
                if (e.type === "touchmove") {
                    currentX = e.touches[0].clientX - initialX;
                    currentY = e.touches[0].clientY - initialY;
                } else {
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;
                }
                
                xOffset = currentX;
                yOffset = currentY;
                
                var rect = panel.getBoundingClientRect();
                var maxX = window.innerWidth - rect.width;
                var maxY = window.innerHeight - rect.height;
                
                currentX = Math.max(0, Math.min(currentX, maxX));
                currentY = Math.max(0, Math.min(currentY, maxY));
                
                panel.style.right = '';
                panel.style.left = currentX + 'px';
                panel.style.top = currentY + 'px';
            }
        }
        
        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
        header.addEventListener('touchstart', dragStart);
        document.addEventListener('touchmove', drag);
        document.addEventListener('touchend', dragEnd);
        
        var rect = panel.getBoundingClientRect();
        xOffset = rect.left;
        yOffset = rect.top;
        
        // Add resize observer to save size changes
        if (window.ResizeObserver) {
            const resizeObserver = new ResizeObserver(function(entries) {
                savePanelSettings(panel);
            });
            resizeObserver.observe(panel);
        }
        
        function colorPickerEscapeListener(e) {
            if (e.key === 'Escape') {
                cleanup();
            }
        }
        
        function cleanup() {
            document.removeEventListener('keydown', colorPickerEscapeListener);
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', dragEnd);
            document.removeEventListener('touchmove', drag);
            document.removeEventListener('touchend', dragEnd);
            
            if (panel && panel.parentNode) {
                panel.remove();
            }
            
            // Clear the escape listener from global scope
            if (window.colorPickerEscapeListener) {
                delete window.colorPickerEscapeListener;
            }
            
            // Keep the old cleanup method for backward compatibility
            window.colorPickerCleanup = null;
        }
        
        // Store escape listener globally for cleanup system access
        window.colorPickerEscapeListener = colorPickerEscapeListener;
        window.colorPickerCleanup = cleanup;
        document.addEventListener('keydown', colorPickerEscapeListener);
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                // Remove existing panels
                const existingPanels = document.querySelectorAll('.color-picker-panel, .color-picker-tooltip');
                existingPanels.forEach(panel => panel.remove());
                
                // Clean up escape listener
                if (window.colorPickerEscapeListener) {
                    document.removeEventListener('keydown', window.colorPickerEscapeListener);
                    delete window.colorPickerEscapeListener;
                }
                
                // Clean up legacy cleanup function
                if (window.colorPickerCleanup) {
                    window.colorPickerCleanup();
                }
                
                console.log('Color Picker reset completed');
                resolve();
            } catch (error) {
                console.error('Color Picker reset error:', error);
                resolve();
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ColorPickerAction;
} else {
    window.ColorPickerAction = ColorPickerAction;
} 