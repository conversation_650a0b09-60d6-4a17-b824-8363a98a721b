// Quick Actions Keyboard Shortcuts
// Handles Control+Shift+1-9 shortcuts for quick action buttons dynamically

class QuickActionsShortcuts {
    constructor() {
        this.shortcuts = new Map();
        this.updating = false; // Flag to prevent infinite loops
        this.shortcutsEnabled = true; // Will be loaded from settings
        this.defaultQuickActionButtons = [
            { id: 'htagsBtn', name: 'Htags', action: () => this.executeHtagsScript() },
            { id: 'headingStructureBtn', name: 'Heading Structure', action: () => this.executeHeadingStructureScript() },
            { id: 'showLinksBtn', name: 'Show Links', action: () => this.executeShowLinksScript() },
            { id: 'showHiddenBtn', name: 'Show Hidden', action: () => this.executeShowHiddenScript() },
            { id: 'boldFromSerpBtn', name: 'Bold From SERP', action: () => this.executeBoldFromSerpScript() },
            { id: 'schemaBtn', name: 'Schema', action: () => this.executeSchemaScript() },
            { id: 'imagesBtn', name: 'Images', action: () => this.executeImagesScript() },
            { id: 'metadataBtn', name: 'Metadata', action: () => this.executeMetadataScript() },
            { id: 'utmBuilderBtn', name: 'UTM Builder', action: () => this.executeUTMBuilderScript() },
            { id: 'pageStructureBtn', name: 'Page Structure', action: () => this.executePageStructureScript() },
            { id: 'linksExtractorBtn', name: 'Links Extractor', action: () => this.executeLinksExtractorScript() },
            { id: 'bulkLinkOpenBtn', name: 'Bulk Link Open', action: () => this.executeBulkLinkOpenScript() },
            { id: 'responsiveBtn', name: 'Responsive', action: () => this.executeResponsiveScript() },
            { id: 'seoTestsBtn', name: 'SEO Tests', action: () => this.executeSeoTestsScript() },
            { id: 'trackerDetectionBtn', name: 'Tracker Detection', action: () => this.executeTrackerDetectionScript() },
            { id: 'copyElementBtn', name: 'Copy Element', action: () => this.executeCopyElementScript() },
            { id: 'colorPickerBtn', name: 'Color Picker', action: () => this.executeColorPickerScript() },
            { id: 'youtubeEmbedScraperBtn', name: 'YouTube Embed Scraper', action: () => this.executeYouTubeEmbedScraperScript() }
        ];
        
        // BULLETPROOF-FIX: Validate and enforce YouTube Embed Scraper is always last
        this.enforceYouTubeLastInArray(this.defaultQuickActionButtons);
        this.quickActionButtons = [...this.defaultQuickActionButtons];
        this.initAsync();
    }

    // BULLETPROOF-FIX: Array validator to ensure YouTube Embed Scraper is always last
    enforceYouTubeLastInArray(array) {
        const youtubeIndex = array.findIndex(item => item.id === 'youtubeEmbedScraperBtn');
        if (youtubeIndex !== -1 && youtubeIndex !== array.length - 1) {
            console.warn('🚨 CRITICAL: YouTube Embed Scraper was not last in shortcuts array. Force-fixing...');
            const youtubeItem = array.splice(youtubeIndex, 1)[0];
            array.push(youtubeItem);
            console.log('✅ YouTube Embed Scraper moved to last position in shortcuts array');
        }
    }

    async initAsync() {
        await this.loadSettings();
        this.init();
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['gmbExtractorSettings', 'quickActionsOrder']);
            const settings = result.gmbExtractorSettings || {};
            this.shortcutsEnabled = settings.keyboardShortcutsEnabled !== false; // Default to true
            
            // Load custom order if available
            if (result.quickActionsOrder && Array.isArray(result.quickActionsOrder)) {
                this.updateOrderFromSaved(result.quickActionsOrder);
            } else {
                // Make sure we have the default order set
                this.quickActionButtons = [...this.defaultQuickActionButtons];
            }
        } catch (error) {
            console.error('Error loading shortcuts settings:', error);
            this.shortcutsEnabled = true; // Default to enabled
            this.quickActionButtons = [...this.defaultQuickActionButtons];
        }
    }

    init() {
        // Add keyboard event listener only when popup is focused
        document.addEventListener('keydown', (event) => this.handleKeyDown(event));
        
        // Add focus/blur listeners to track popup focus state
        window.addEventListener('focus', () => {
            // Don't refresh shortcuts on focus to avoid loops
        });
        window.addEventListener('blur', () => {
            // Don't clear shortcuts on blur to avoid loops
        });
        
        // Update shortcuts when quick actions visibility changes - delay to ensure DOM is ready
        setTimeout(() => {
            // Force update to ensure proper order is applied
            this.updateShortcuts(true);
        }, 200);
        
        // Watch for changes in quick actions visibility
        this.observeQuickActionsChanges();
        
        // Listen for storage changes to update order
        this.listenForOrderChanges();
    }

    handleKeyDown(event) {
        // Only work if popup is focused and quick actions are visible
        if (!this.isPopupFocused() || !this.areQuickActionsVisible()) {
            return;
        }
        
        // Check if Control+Shift is pressed with a number key or R
        if (event.ctrlKey && event.shiftKey && !event.altKey && !event.metaKey) {
            
            // Use event.code to detect physical key pressed (not the character produced)
            const keyCode = event.code;
            const keyLetter = event.key.toLowerCase();
            
            // Check if it's a number key 1-9 using the physical key code
            const digitMatch = keyCode.match(/^Digit(\d)$/);
            if (digitMatch) {
                const keyNumber = parseInt(digitMatch[1]);
                
                // Skip 0 key - not used for shortcuts
                if (keyNumber === 0) {
                    return;
                }
                
                event.preventDefault();
                event.stopPropagation();
                
                // Only execute if shortcuts are enabled
                if (!this.shortcutsEnabled) {
                    return;
                }
                
                
                // Get the mapped action for this number
                const action = this.shortcuts.get(keyNumber);
                if (action) {
                    try {
                        action.execute();
                        this.showShortcutFeedback(keyNumber, action.name);
                        
                        // Close the popup after executing the action
                        setTimeout(() => {
                            window.close();
                        }, 100);
                    } catch (error) {
                        console.error(`Error executing shortcut ${keyNumber}:`, error);
                    }
                }
            }
            // Check if it's the R key for Reset All
            else if (keyLetter === 'r') {
                event.preventDefault();
                event.stopPropagation();
                
                // Execute reset all quick actions
                this.executeResetAllQuickActions();
                this.showShortcutFeedback('R', 'Reset All Quick Actions');
            }
        }
    }

    updateShortcuts(force = false) {
        
        // Prevent infinite loops
        if (this.updating) {
            return;
        }
        
        // Only update if popup is focused and quick actions are visible (unless forced)
        if (!force && (!this.isPopupFocused() || !this.areQuickActionsVisible())) {
            return;
        }
        
        this.updating = true;
        
        try {
            // Clear existing shortcuts and remove all old numbers first
            this.clearShortcuts();
        
        // Get all visible quick action buttons (or all available if forced)
        const visibleButtons = force ? this.getAllAvailableQuickActionButtons() : this.getVisibleQuickActionButtons();
        
        // Map numbers 1-9 to visible buttons and add numbers to button text
        // Only first 9 items get keyboard shortcuts
        visibleButtons.slice(0, 9).forEach((button, index) => {
            const shortcutNumber = index + 1;
                
                
                this.shortcuts.set(shortcutNumber, {
                    name: button.name,
                    buttonId: button.id,
                    execute: button.action
                });
                
                // Add number to button text only if shortcuts are enabled
                if (this.shortcutsEnabled) {
                    this.addNumberToButton(button.id, shortcutNumber, button.name);
                }
        });
        
        // Note: Reset button does not get a visual indicator
        
        } finally {
            // Always reset the updating flag
            this.updating = false;
        }
    }

    // Method to update shortcuts enabled state from settings
    updateShortcutsEnabled(enabled) {
        const wasEnabled = this.shortcutsEnabled;
        this.shortcutsEnabled = enabled;
        
        if (wasEnabled !== enabled) {
            // Force refresh to update number visibility
            this.forceRefresh();
        }
    }

    updateOrder(orderedActions) {
        
        // Clear existing shortcuts first
        this.clearShortcuts();
        
        // Update the order
        this.quickActionButtons = orderedActions;
        
        // Force update shortcuts with new order
        setTimeout(() => {
            this.updateShortcuts(true);
        }, 50);
    }

    updateOrderFromSaved(savedOrder) {
        
        // Convert saved order to action buttons with proper action functions
        this.quickActionButtons = [];
        
        savedOrder.forEach(savedItem => {
            const defaultAction = this.defaultQuickActionButtons.find(action => action.id === savedItem.id);
            if (defaultAction) {
                this.quickActionButtons.push({
                    id: defaultAction.id,
                    name: defaultAction.name,
                    action: defaultAction.action
                });
            } else {
            }
        });
        
        // Add any missing default actions at the end (but before YouTube)
        this.defaultQuickActionButtons.forEach(defaultAction => {
            if (!this.quickActionButtons.find(action => action.id === defaultAction.id)) {
                // BULLETPROOF-FIX: Never add items after YouTube, always insert before it
                if (defaultAction.id === 'youtubeEmbedScraperBtn') {
                    this.quickActionButtons.push(defaultAction);
                } else {
                    // Find YouTube's position and insert before it
                    const youtubeIndex = this.quickActionButtons.findIndex(action => action.id === 'youtubeEmbedScraperBtn');
                    if (youtubeIndex !== -1) {
                        this.quickActionButtons.splice(youtubeIndex, 0, defaultAction);
                    } else {
                        this.quickActionButtons.push(defaultAction);
                    }
                }
            }
        });
        
        // BULLETPROOF-FIX: YouTube Embed Scraper must ALWAYS be last (strengthened)
        this.enforceYouTubeLastInArray(this.quickActionButtons);
        
        // Immediately update shortcuts to reflect new order
        setTimeout(() => {
            this.updateShortcuts(true);
        }, 100);
    }

    listenForOrderChanges() {
        // Listen for changes in chrome storage
        if (chrome && chrome.storage && chrome.storage.onChanged) {
                            chrome.storage.onChanged.addListener((changes, namespace) => {
                if (namespace === 'sync' && changes.quickActionsOrder) {
                    if (changes.quickActionsOrder.newValue && Array.isArray(changes.quickActionsOrder.newValue)) {
                        this.updateOrderFromSaved(changes.quickActionsOrder.newValue);
                        // The updateOrderFromSaved method will handle updating shortcuts
                    }
                }
            });
        }
    }

    getVisibleQuickActionButtons() {
        const visibleButtons = [];
        
        this.quickActionButtons.forEach(button => {
            const element = document.getElementById(button.id);
            const isVisible = element && this.isElementVisible(element);
            if (isVisible) {
                visibleButtons.push(button);
            }
        });
        
        return visibleButtons;
    }

    // Alternative method that checks if buttons exist even if not initially visible
    getAllAvailableQuickActionButtons() {
        const availableButtons = [];
        
        this.quickActionButtons.forEach(button => {
            const element = document.getElementById(button.id);
            if (element) {
                availableButtons.push(button);
            } else {
            }
        });
        
        return availableButtons;
    }

    isElementVisible(element) {
        // Check if element exists and is displayed (not display: none)
        return element && 
               element.style.display !== 'none' && 
               window.getComputedStyle(element).display !== 'none' &&
               element.offsetParent !== null;
    }

    observeQuickActionsChanges() {
        // Use MutationObserver to watch for changes in quick actions visibility
        const quickActionsContainer = document.getElementById('quickActionsControls');
        if (!quickActionsContainer) return;

        const observer = new MutationObserver((mutations) => {
            // Skip updates if we're currently updating (prevents infinite loops)
            if (this.updating) {
                return;
            }
            
            let shouldUpdate = false;
            
            mutations.forEach((mutation) => {
                // Only trigger on style changes that affect visibility
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    shouldUpdate = true;
                }
                // Completely ignore childList changes to prevent loops from number badge modifications
                // We only care about visibility changes, not content changes
            });
            
            if (shouldUpdate) {
                // Debounce updates to avoid excessive calls
                clearTimeout(this.updateTimeout);
                this.updateTimeout = setTimeout(() => {
                    this.updateShortcuts();
                }, 150); // Slightly longer delay
            }
        });

        // Only observe the container for style changes (no childList or subtree to avoid loops)
        observer.observe(quickActionsContainer, {
            attributes: true,
            attributeFilter: ['style']
        });

        // Also observe individual buttons for style changes only
        this.quickActionButtons.forEach(button => {
            const element = document.getElementById(button.id);
            if (element) {
                observer.observe(element, {
                    attributes: true,
                    attributeFilter: ['style']
                });
            }
        });
    }

    showShortcutFeedback(key, actionName) {
        // Create visual feedback when shortcut is used
        const feedback = document.createElement('div');
        feedback.textContent = `⚡ ${actionName} (Ctrl+Shift+${key})`;
        feedback.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 600;
            z-index: 10000;
            box-shadow: 0 8px 24px rgba(139, 92, 246, 0.3);
            border: 1px solid #8b5cf6;
            animation: shortcutFeedback 2s ease-out forwards;
        `;

        // Add animation keyframes if not already added
        if (!document.getElementById('shortcut-feedback-styles')) {
            const style = document.createElement('style');
            style.id = 'shortcut-feedback-styles';
            style.textContent = `
                @keyframes shortcutFeedback {
                    0% {
                        opacity: 0;
                        transform: translateX(100px) scale(0.8);
                    }
                    10% {
                        opacity: 1;
                        transform: translateX(0) scale(1.05);
                    }
                    20% {
                        transform: translateX(0) scale(1);
                    }
                    80% {
                        opacity: 1;
                        transform: translateX(0) scale(1);
                    }
                    100% {
                        opacity: 0;
                        transform: translateX(100px) scale(0.8);
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(feedback);

        // Remove feedback after animation
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 2000);
    }

    // Helper methods for popup focus and visibility checks
    isPopupFocused() {
        // Check if the current window (popup) is focused
        return document.hasFocus();
    }

    areQuickActionsVisible() {
        const quickActionsContainer = document.getElementById('quickActionsControls');
        return quickActionsContainer && 
               quickActionsContainer.style.display !== 'none' && 
               window.getComputedStyle(quickActionsContainer).display !== 'none' &&
               quickActionsContainer.offsetParent !== null;
    }

    clearShortcuts() {
        // Clear all shortcuts and remove numbers from buttons
        this.shortcuts.clear();
        this.removeNumbersFromAllButtons();
    }

    addNumberToButton(buttonId, number, originalName) {
        const button = document.getElementById(buttonId);
        if (button) {
            // Create styled number badge
            const numberBadge = `<span class="shortcut-number">${number}</span>`;
            button.innerHTML = `${numberBadge}${originalName}`;
            
            // Add CSS for the number badge if not already added
            this.addShortcutNumberStyles();
        }
    }

    removeNumbersFromAllButtons() {
        this.quickActionButtons.forEach(button => {
            const element = document.getElementById(button.id);
            if (element) {
                // Remove any existing number badges and restore original text
                const numberBadge = element.querySelector('.shortcut-number');
                if (numberBadge) {
                    numberBadge.remove();
                }
                // Ensure the button text is clean
                const textContent = element.textContent || element.innerText || '';
                const cleanText = textContent.replace(/^\d+\s*/, ''); // Remove any leading numbers
                element.innerHTML = cleanText || button.name;
            }
        });
        
        // Note: Reset button does not have indicators to remove
    }

    addShortcutNumberStyles() {
        if (!document.getElementById('shortcut-number-styles')) {
            const style = document.createElement('style');
            style.id = 'shortcut-number-styles';
            style.textContent = `
                .shortcut-number {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 18px;
                    height: 18px;
                    background: #7c3aed;
                    color: white;
                    border-radius: 50%;
                    font-size: 11px;
                    font-weight: 700;
                    margin-right: 6px;
                    line-height: 1;
                    border: 1px solid #8b5cf6;
                    box-shadow: 0 2px 4px rgba(124, 58, 237, 0.3);
                }
                .btn:hover .shortcut-number {
                    background: #8b5cf6;
                    border-color: #a855f7;
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Wrapper methods for executing actions - these will call the global functions
    executeHtagsScript() {
        if (typeof executeHtagsScript === 'function') {
            executeHtagsScript();
        } else if (typeof HtagsAction !== 'undefined') {
            HtagsAction.execute();
        }
    }

    executeHeadingStructureScript() {
        if (typeof executeHeadingStructureScript === 'function') {
            executeHeadingStructureScript();
        } else if (typeof HeadingStructureAction !== 'undefined') {
            HeadingStructureAction.execute();
        }
    }

    executeShowLinksScript() {
        if (typeof executeShowLinksScript === 'function') {
            executeShowLinksScript();
        } else if (typeof ShowLinksAction !== 'undefined') {
            ShowLinksAction.execute();
        }
    }

    executeShowHiddenScript() {
        if (typeof executeShowHiddenScript === 'function') {
            executeShowHiddenScript();
        } else if (typeof ShowHiddenAction !== 'undefined') {
            ShowHiddenAction.execute();
        }
    }

    // executeKeywordScript() removed - Keyword functionality is now available as right-click context menu

    executeBoldFromSerpScript() {
        if (typeof executeBoldFromSerpScript === 'function') {
            executeBoldFromSerpScript();
        } else if (typeof BoldFromSerpAction !== 'undefined') {
            BoldFromSerpAction.execute();
        }
    }

    executeSchemaScript() {
        if (typeof executeSchemaScript === 'function') {
            executeSchemaScript();
        } else if (typeof SchemaAction !== 'undefined') {
            SchemaAction.execute();
        }
    }

    executeImagesScript() {
        if (typeof executeImagesScript === 'function') {
            executeImagesScript();
        } else if (typeof ImagesAction !== 'undefined') {
            ImagesAction.execute();
        }
    }

    executeMetadataScript() {
        if (typeof executeMetadataScript === 'function') {
            executeMetadataScript();
        } else if (typeof MetadataAction !== 'undefined') {
            MetadataAction.execute();
        }
    }

    executeUTMBuilderScript() {
        if (typeof executeUTMBuilderScript === 'function') {
            executeUTMBuilderScript();
        } else {
            console.error('executeUTMBuilderScript function not found');
        }
    }

    executePageStructureScript() {
        if (typeof executePageStructureScript === 'function') {
            executePageStructureScript();
        } else if (typeof PageStructureAction !== 'undefined') {
            const pageStructureAction = new PageStructureAction();
            pageStructureAction.execute();
        }
    }

    executeYouTubeEmbedScraperScript() {
        if (typeof executeYouTubeEmbedScraperScript === 'function') {
            executeYouTubeEmbedScraperScript();
        } else if (typeof YouTubeEmbedScraperAction !== 'undefined') {
            YouTubeEmbedScraperAction.execute();
        }
    }

    executeCopyElementScript() {
        if (typeof executeCopyElementScript === 'function') {
            executeCopyElementScript();
        } else if (typeof CopyElementAction !== 'undefined') {
            const copyElementAction = new CopyElementAction();
            copyElementAction.execute();
        }
    }

    executeLinksExtractorScript() {
        if (typeof executeLinksExtractorScript === 'function') {
            executeLinksExtractorScript();
        } else {
            console.error('executeLinksExtractorScript function not found');
        }
    }

    executeBulkLinkOpenScript() {
        if (typeof executeBulkLinkOpenScript === 'function') {
            executeBulkLinkOpenScript();
        } else {
            console.error('executeBulkLinkOpenScript function not found');
        }
    }

    executeColorPickerScript() {
        if (typeof ColorPickerAction !== 'undefined' && typeof ColorPickerAction.execute === 'function') {
            ColorPickerAction.execute();
        } else {
            console.error('ColorPickerAction is not available or not loaded.');
        }
    }

    executeResponsiveScript() {
        if (typeof executeResponsiveScript === 'function') {
            executeResponsiveScript();
        } else {
            console.error('executeResponsiveScript function not found');
        }
    }

    executeSeoTestsScript() {
        if (typeof executeSeoTestsScript === 'function') {
            executeSeoTestsScript();
        } else {
            console.error('executeSeoTestsScript function not found');
        }
    }

    executeTrackerDetectionScript() {
        if (typeof executeTrackerDetectionScript === 'function') {
            executeTrackerDetectionScript();
        } else {
            console.error('executeTrackerDetectionScript function not found');
        }
    }

    executeResetAllQuickActions() {
        if (typeof resetAllQuickActions === 'function') {
            resetAllQuickActions();
        } else {
            console.error('resetAllQuickActions function not found');
        }
    }

    // Method to get current shortcut mappings (useful for showing help)
    getCurrentShortcuts() {
        const shortcuts = Array.from(this.shortcuts.entries()).map(([key, action]) => ({
            key: `Ctrl+Shift+${key}`,
            name: action.name,
            buttonId: action.buttonId
        }));
        

        
        return shortcuts;
    }

    // Method to manually refresh shortcuts (can be called from other parts of the app)
    refresh() {
        // Add a small delay to ensure DOM updates are complete
        setTimeout(() => {
            this.updateShortcuts();
        }, 50);
    }

    // Method to reload order from storage and refresh
    async reloadOrder() {
        try {
            const result = await chrome.storage.local.get('quickActionsOrder');
            if (result.quickActionsOrder && Array.isArray(result.quickActionsOrder)) {
                this.updateOrderFromSaved(result.quickActionsOrder);
            } else {
                this.quickActionButtons = [...this.defaultQuickActionButtons];
                this.forceRefresh();
            }
        } catch (error) {
            console.error('Error reloading order:', error);
        }
    }
    
    // Force refresh - useful when settings change
    forceRefresh() {
        this.clearShortcuts();
        
        // Multiple refreshes with increasing delays to handle timing issues - use force parameter
        setTimeout(() => {
            this.updateShortcuts(true);
        }, 100);
        
        setTimeout(() => {
            this.updateShortcuts(true);
        }, 300);
        
        setTimeout(() => {
            this.updateShortcuts(true);
        }, 500);
    }
    
    // Debug method to show current state
    debugCurrentState() {
        console.log('=== SHORTCUTS DEBUG ===');
        console.log('Popup focused:', this.isPopupFocused());
        console.log('Quick Actions visible:', this.areQuickActionsVisible());
        console.log('Shortcuts enabled:', this.shortcutsEnabled);
        console.log('Current shortcuts map:', Array.from(this.shortcuts.entries()));
        console.log('Current order:', this.quickActionButtons.map(b => `${b.name} (${b.id})`));
        
        console.log('Button visibility:');
        this.quickActionButtons.forEach((button, index) => {
            const element = document.getElementById(button.id);
            const isVisible = element && this.isElementVisible(element);
            console.log(`  ${index + 1}. ${button.name} (${button.id}): ${isVisible ? 'VISIBLE' : 'HIDDEN'}`);
        });
        
        const visibleButtons = this.getVisibleQuickActionButtons();
        console.log('Visible buttons array:', visibleButtons.map(b => b.name));
        console.log('Shortcut number assignments:');
        visibleButtons.slice(0, 9).forEach((button, index) => {
            console.log(`  Ctrl+Shift+${index + 1} -> ${button.name} (${button.id})`);
        });
        console.log('========================');
    }
}

// Initialize shortcuts when DOM is ready
function initializeShortcuts() {
    try {
        if (!window.quickActionsShortcuts) {
            window.quickActionsShortcuts = new QuickActionsShortcuts();
            
            // Wait a bit for other scripts to load, then refresh
            setTimeout(() => {
                if (window.quickActionsShortcuts) {
                    window.quickActionsShortcuts.refresh();
                }
            }, 500);
        }
    } catch (error) {
        console.error('Error initializing Quick Actions Shortcuts:', error);
    }
}

// Debug function for console - define it immediately
window.debugShortcuts = function() {
    console.log('=== SHORTCUTS SYSTEM DEBUG ===');
    console.log('window.quickActionsShortcuts exists:', !!window.quickActionsShortcuts);
    console.log('QuickActionsShortcuts class exists:', typeof QuickActionsShortcuts !== 'undefined');
    
    if (window.quickActionsShortcuts) {
        window.quickActionsShortcuts.debugCurrentState();
    } else {
        console.log('Shortcuts system not initialized');
        console.log('Trying to initialize now...');
        initializeShortcuts();
        
        setTimeout(() => {
            if (window.quickActionsShortcuts) {
                console.log('Initialization successful!');
                window.quickActionsShortcuts.debugCurrentState();
            } else {
                console.log('Failed to initialize shortcuts system');
            }
        }, 1000);
    }
};

// Test order function for console
window.testShortcutsOrder = async function() {
    console.log('=== TESTING SHORTCUTS ORDER ===');
    if (window.quickActionsShortcuts) {
        await window.quickActionsShortcuts.reloadOrder();
        setTimeout(() => {
            window.quickActionsShortcuts.debugCurrentState();
        }, 200);
    } else {
        console.log('Shortcuts system not available');
    }
};

// Simple test function for shortcuts
window.testShortcut2 = function() {
    console.log('Testing shortcut 2 manually...');
    if (window.quickActionsShortcuts && window.quickActionsShortcuts.shortcuts) {
        const action = window.quickActionsShortcuts.shortcuts.get(2);
        if (action) {
            console.log('Shortcut 2 found:', action);
            console.log('Executing action...');
            action.execute();
        } else {
            console.log('Shortcut 2 not found in shortcuts map');
            console.log('Current shortcuts:', Array.from(window.quickActionsShortcuts.shortcuts.entries()));
        }
    } else {
        console.log('Shortcuts system not available');
    }
};

// Simple test to verify keyboard events work at all
window.testKeyboardEvents = function() {
    console.log('Setting up basic keyboard event listener...');
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.shiftKey && e.key === '2') {
            console.log('Basic keyboard test: Ctrl+Shift+2 detected!');
            e.preventDefault();
        }
    });
    console.log('Basic keyboard listener attached');
};

// Initialize on different events to ensure it loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeShortcuts);
} else {
    initializeShortcuts();
}

// Also try to initialize when the window loads
window.addEventListener('load', () => {
    initializeShortcuts();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QuickActionsShortcuts;
} else {
    window.QuickActionsShortcuts = QuickActionsShortcuts;
} 