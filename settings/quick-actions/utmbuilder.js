// UTM Builder Action - Create UTM tracking URLs with template management
class UTMBuilderAction {
    static execute() {
        try {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                    target: {tabId: tabs[0].id},
                    func: function() {
                        // UTM Builder functionality
                        (function() {
                            // Remove existing UTM Builder panel if present
                            document.querySelectorAll('.utm-builder-panel').forEach(panel => panel.remove());
                            
                            // Helper functions
                            function escape(str) {
                                if (!str) return '';
                                return str.replace(/&/g, '&amp;')
                                         .replace(/</g, '&lt;')
                                         .replace(/>/g, '&gt;')
                                         .replace(/"/g, '&quot;')
                                         .replace(/'/g, '&#39;');
                            }
                            
                            // Get saved templates from localStorage
                            function getSavedTemplates() {
                                try {
                                    const saved = localStorage.getItem('utm-builder-templates');
                                    return saved ? JSON.parse(saved) : [];
                                } catch (e) {
                                    console.log('Error loading templates:', e);
                                    return [];
                                }
                            }
                            
                            // Save templates to localStorage
                            function saveTemplates(templates) {
                                try {
                                    localStorage.setItem('utm-builder-templates', JSON.stringify(templates));
                                } catch (e) {
                                    console.log('Error saving templates:', e);
                                }
                            }
                            
                            // Get default templates (preloaded common UTM formulas)
                            function getDefaultTemplates() {
                                return [
                                    {
                                        name: "Facebook Ads",
                                        campaign: "facebook-campaign",
                                        medium: "cpc",
                                        source: "facebook",
                                        term: "",
                                        content: "ad-creative",
                                        created: new Date().toISOString()
                                    },
                                    {
                                        name: "Google Ads",
                                        campaign: "google-search",
                                        medium: "cpc",
                                        source: "google",
                                        term: "",
                                        content: "",
                                        created: new Date().toISOString()
                                    },
                                    {
                                        name: "YouTube Marketing",
                                        campaign: "video-campaign",
                                        medium: "video",
                                        source: "youtube",
                                        term: "",
                                        content: "video-description",
                                        created: new Date().toISOString()
                                    },
                                    {
                                        name: "GMB Posts",
                                        campaign: "local-seo",
                                        medium: "organic",
                                        source: "gmb",
                                        term: "",
                                        content: "post",
                                        created: new Date().toISOString()
                                    },
                                    {
                                        name: "Email Newsletter",
                                        campaign: "newsletter",
                                        medium: "email",
                                        source: "mailchimp",
                                        term: "",
                                        content: "cta-button",
                                        created: new Date().toISOString()
                                    },
                                    {
                                        name: "LinkedIn Ads",
                                        campaign: "linkedin-campaign",
                                        medium: "cpc",
                                        source: "linkedin",
                                        term: "",
                                        content: "sponsored-content",
                                        created: new Date().toISOString()
                                    }
                                ];
                            }
                            
                            // Initialize templates with defaults if none exist
                            function initializeTemplates() {
                                const existing = getSavedTemplates();
                                if (existing.length === 0) {
                                    const defaults = getDefaultTemplates();
                                    saveTemplates(defaults);
                                    console.log('UTM Builder: Preloaded', defaults.length, 'default templates');
                                } else {
                                    console.log('UTM Builder: Found', existing.length, 'existing templates');
                                    // Always ensure default templates are available
                                    loadDefaultTemplates();
                                }
                            }
                            
                            // Force load default templates (merge with existing)
                            function loadDefaultTemplates() {
                                const defaults = getDefaultTemplates();
                                const existing = getSavedTemplates();
                                
                                // Filter out any defaults that already exist (by name)
                                const existingNames = existing.map(t => t.name.toLowerCase());
                                const newDefaults = defaults.filter(d => !existingNames.includes(d.name.toLowerCase()));
                                
                                if (newDefaults.length > 0) {
                                    // Add new defaults to the end of the list (after user templates)
                                    const updatedTemplates = [...existing, ...newDefaults];
                                    saveTemplates(updatedTemplates);
                                    renderTemplates();
                                    console.log('UTM Builder: Added', newDefaults.length, 'missing default templates');
                                    return newDefaults.length;
                                } else {
                                    console.log('UTM Builder: All default templates already exist');
                                    return 0;
                                }
                            }
                            
                            // Get saved panel settings
                            function getSavedPanelSettings() {
                                try {
                                    const saved = localStorage.getItem('utm-builder-panel-settings');
                                    if (saved) {
                                        return JSON.parse(saved);
                                    }
                                } catch (e) {
                                    console.log('Error loading saved panel settings:', e);
                                }
                                return {
                                    top: '20px',
                                    left: '20px',
                                    width: '700px'
                                };
                            }
                            
                            // Get saved UTM values
                            function getSavedUTMValues() {
                                try {
                                    const saved = localStorage.getItem('utm-builder-current-values');
                                    return saved ? JSON.parse(saved) : {};
                                } catch (e) {
                                    console.log('Error loading saved UTM values:', e);
                                    return {};
                                }
                            }
                            
                            // Save current UTM values
                            function saveCurrentUTMValues() {
                                try {
                                    const values = {
                                        url: document.getElementById('utm-url-input')?.value?.trim() || '',
                                        campaign: document.getElementById('utm-campaign')?.value?.trim() || '',
                                        medium: document.getElementById('utm-medium')?.value?.trim() || '',
                                        source: document.getElementById('utm-source')?.value?.trim() || '',
                                        term: document.getElementById('utm-term')?.value?.trim() || '',
                                        content: document.getElementById('utm-content')?.value?.trim() || ''
                                    };
                                    localStorage.setItem('utm-builder-current-values', JSON.stringify(values));
                                } catch (e) {
                                    console.log('Error saving UTM values:', e);
                                }
                            }
                            
                            // Clear all fields
                            function clearAllFields() {
                                document.getElementById('utm-url-input').value = window.location.href;
                                document.getElementById('utm-campaign').value = '';
                                document.getElementById('utm-medium').value = '';
                                document.getElementById('utm-source').value = '';
                                document.getElementById('utm-term').value = '';
                                document.getElementById('utm-content').value = '';
                                updateResult();
                                saveCurrentUTMValues();
                            }
                            
                            // Export templates to JSON file
                            function exportTemplates() {
                                const templates = getSavedTemplates();
                                if (templates.length === 0) {
                                    alert('No templates to export');
                                    return;
                                }
                                
                                const exportData = {
                                    version: '1.0',
                                    exported: new Date().toISOString(),
                                    templates: templates
                                };
                                
                                const dataStr = JSON.stringify(exportData, null, 2);
                                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                                
                                const link = document.createElement('a');
                                link.href = URL.createObjectURL(dataBlob);
                                link.download = `utm-templates-${new Date().toISOString().split('T')[0]}.json`;
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                                URL.revokeObjectURL(link.href);
                                
                                console.log('UTM templates exported successfully');
                            }
                            
                            // Import templates from JSON file
                            function importTemplates() {
                                const input = document.createElement('input');
                                input.type = 'file';
                                input.accept = '.json';
                                input.style.display = 'none';
                                
                                input.onchange = function(event) {
                                    const file = event.target.files[0];
                                    if (!file) return;
                                    
                                    const reader = new FileReader();
                                    reader.onload = function(e) {
                                        try {
                                            const importData = JSON.parse(e.target.result);
                                            
                                            // Validate import data structure
                                            if (!importData.templates || !Array.isArray(importData.templates)) {
                                                throw new Error('Invalid template file format');
                                            }
                                            
                                            const existingTemplates = getSavedTemplates();
                                            const importCount = importData.templates.length;
                                            
                                            // Ask user about merge strategy
                                            const shouldReplace = existingTemplates.length > 0 && 
                                                confirm(`You have ${existingTemplates.length} existing templates. Click OK to replace them with ${importCount} imported templates, or Cancel to merge them.`);
                                            
                                            let finalTemplates;
                                            if (shouldReplace) {
                                                finalTemplates = importData.templates;
                                            } else {
                                                // Merge: add imported templates to the top of existing ones
                                                finalTemplates = [...importData.templates, ...existingTemplates];
                                            }
                                            
                                            // Validate each template
                                            const validTemplates = finalTemplates.filter(template => {
                                                return template && typeof template === 'object' && template.name;
                                            });
                                            
                                            if (validTemplates.length === 0) {
                                                throw new Error('No valid templates found in import file');
                                            }
                                            
                                            // Save templates
                                            saveTemplates(validTemplates);
                                            renderTemplates();
                                            updateTemplateButtons();
                                            
                                            const message = shouldReplace 
                                                ? `Successfully replaced templates with ${validTemplates.length} imported templates`
                                                : `Successfully imported ${importData.templates.length} templates (${validTemplates.length} total)`;
                                            
                                            alert(message);
                                            console.log('UTM templates imported successfully:', validTemplates);
                                            
                                        } catch (error) {
                                            console.error('Import error:', error);
                                            alert('Error importing templates: ' + error.message);
                                        }
                                    };
                                    
                                    reader.readAsText(file);
                                    document.body.removeChild(input);
                                };
                                
                                document.body.appendChild(input);
                                input.click();
                            }
                            
                            // Save panel settings
                            function savePanelSettings(panel) {
                                try {
                                    const settings = {
                                        top: panel.style.top || '20px',
                                        left: panel.style.left || '20px',
                                        width: panel.style.width || '700px'
                                    };
                                    localStorage.setItem('utm-builder-panel-settings', JSON.stringify(settings));
                                } catch (e) {
                                    console.log('Error saving panel settings:', e);
                                }
                            }
                            
                            // Generate UTM URL
                            function generateUTMUrl() {
                                const baseUrl = document.getElementById('utm-url-input')?.value?.trim() || '';
                                const campaign = document.getElementById('utm-campaign')?.value?.trim() || '';
                                const medium = document.getElementById('utm-medium')?.value?.trim() || '';
                                const source = document.getElementById('utm-source')?.value?.trim() || '';
                                const term = document.getElementById('utm-term')?.value?.trim() || '';
                                const content = document.getElementById('utm-content')?.value?.trim() || '';
                                
                                console.log('UTM Builder - Generating URL with:', { baseUrl, campaign, medium, source, term, content });
                                
                                if (!baseUrl) {
                                    return '';
                                }
                                
                                try {
                                    const url = new URL(baseUrl);
                                    const params = new URLSearchParams(url.search);
                                    
                                    if (campaign) params.set('utm_campaign', campaign);
                                    if (medium) params.set('utm_medium', medium);
                                    if (source) params.set('utm_source', source);
                                    if (term) params.set('utm_term', term);
                                    if (content) params.set('utm_content', content);
                                    
                                    url.search = params.toString();
                                    const finalUrl = url.toString();
                                    console.log('UTM Builder - Generated URL:', finalUrl);
                                    return finalUrl;
                                } catch (error) {
                                    console.error('UTM Builder - URL generation error:', error);
                                    return '';
                                }
                            }
                            
                            // Update result URL
                            function updateResult() {
                                console.log('UTM Builder - updateResult called');
                                const resultUrl = generateUTMUrl();
                                const resultElement = document.getElementById('utm-result');
                                const copyButton = document.getElementById('utm-copy-btn');
                                
                                console.log('UTM Builder - Result URL:', resultUrl);
                                
                                if (resultUrl && resultElement && copyButton) {
                                    resultElement.style.color = '#d1d5db';
                                    resultElement.style.cursor = 'pointer';
                                    resultElement.style.transition = 'all 0.2s';
                                    resultElement.textContent = resultUrl;
                                    resultElement.title = 'Click to copy URL';
                                    copyButton.style.opacity = '1';
                                    copyButton.style.pointerEvents = 'auto';
                                } else if (resultElement && copyButton) {
                                    resultElement.style.color = '#6b7280';
                                    resultElement.style.cursor = 'default';
                                    resultElement.style.transition = 'all 0.2s';
                                    resultElement.textContent = 'Enter a valid URL to see the UTM result...';
                                    resultElement.title = '';
                                    copyButton.style.opacity = '0.5';
                                    copyButton.style.pointerEvents = 'none';
                                }
                                
                                // Save current values for persistence
                                saveCurrentUTMValues();
                            }
                            
                            // Load template into form
                            function loadTemplate(template) {
                                document.getElementById('utm-campaign').value = template.campaign || '';
                                document.getElementById('utm-medium').value = template.medium || '';
                                document.getElementById('utm-source').value = template.source || '';
                                document.getElementById('utm-term').value = template.term || '';
                                document.getElementById('utm-content').value = template.content || '';
                                updateResult();
                            }
                            
                            // Save current form as template
                            function saveCurrentAsTemplate() {
                                const name = prompt('Enter template name:');
                                if (!name) return;
                                
                                // Check if template name already exists
                                const templates = getSavedTemplates();
                                const existingIndex = templates.findIndex(t => t.name.toLowerCase() === name.toLowerCase());
                                
                                if (existingIndex !== -1) {
                                    if (!confirm(`A template named "${name}" already exists. Do you want to replace it?`)) {
                                        return;
                                    }
                                    // Remove existing template
                                    templates.splice(existingIndex, 1);
                                }
                                
                                const template = {
                                    name: name,
                                    campaign: document.getElementById('utm-campaign').value.trim(),
                                    medium: document.getElementById('utm-medium').value.trim(),
                                    source: document.getElementById('utm-source').value.trim(),
                                    term: document.getElementById('utm-term').value.trim(),
                                    content: document.getElementById('utm-content').value.trim(),
                                    created: new Date().toISOString()
                                };
                                
                                templates.unshift(template); // Add to the beginning of the array (top of list)
                                saveTemplates(templates);
                                renderTemplates();
                                
                                // Show success feedback
                                alert(`Template "${name}" saved successfully!`);
                                console.log('UTM Builder: Template saved:', template);
                            }
                            
                            // Delete template
                            function deleteTemplate(index) {
                                if (confirm('Delete this template?')) {
                                    const templates = getSavedTemplates();
                                    templates.splice(index, 1);
                                    saveTemplates(templates);
                                    renderTemplates();
                                }
                            }
                            
                            // Render templates dropdown (called by renderTemplateOptions)
                            function renderTemplates() {
                                renderTemplateOptions('');
                            }
                            
                            // Render template options with filtering
                            function renderTemplateOptions(searchTerm) {
                                const templates = getSavedTemplates();
                                const optionsContainer = document.getElementById('utm-dropdown-options');
                                
                                // Clear existing options
                                optionsContainer.innerHTML = '';
                                
                                if (templates.length === 0) {
                                    optionsContainer.innerHTML = '<div style="padding:8px 12px;color:#6b7280;font-style:italic;">No templates saved yet</div>';
                                    return;
                                }
                                
                                // Filter templates based on search term
                                const filteredTemplates = templates.filter((template, index) => {
                                    if (!searchTerm || searchTerm.trim() === '') return true;
                                    
                                    const searchLower = searchTerm.toLowerCase().trim();
                                    const name = (template.name || '').toLowerCase();
                                    const campaign = (template.campaign || '').toLowerCase();
                                    const medium = (template.medium || '').toLowerCase();
                                    const source = (template.source || '').toLowerCase();
                                    const term = (template.term || '').toLowerCase();
                                    const content = (template.content || '').toLowerCase();
                                    
                                    return name.includes(searchLower) || 
                                           campaign.includes(searchLower) || 
                                           medium.includes(searchLower) || 
                                           source.includes(searchLower) ||
                                           term.includes(searchLower) ||
                                           content.includes(searchLower);
                                }).map((template, filteredIndex) => {
                                    // Find original index
                                    const originalIndex = templates.findIndex(t => t === template);
                                    return { template, originalIndex };
                                });
                                
                                if (filteredTemplates.length === 0) {
                                    optionsContainer.innerHTML = '<div style="padding:8px 12px;color:#6b7280;font-style:italic;">No templates match your search</div>';
                                    return;
                                }
                                
                                // Populate dropdown with filtered templates
                                filteredTemplates.forEach(({ template, originalIndex }) => {
                                    const campaignInfo = template.campaign ? `Campaign: ${template.campaign}` : '';
                                    const mediumInfo = template.medium ? `Medium: ${template.medium}` : '';
                                    const sourceInfo = template.source ? `Source: ${template.source}` : '';
                                    const details = [campaignInfo, mediumInfo, sourceInfo].filter(info => info).join(' • ');
                                    
                                    const option = document.createElement('div');
                                    option.className = 'utm-dropdown-option';
                                    option.style.cssText = 'display:flex;justify-content:space-between;align-items:center;padding:8px 12px;cursor:pointer;transition:background 0.2s;border-bottom:1px solid #2a2a2a;';
                                    option.setAttribute('data-template-index', originalIndex);
                                    
                                    option.innerHTML = `
                                        <div style="flex:1;min-width:0;" class="utm-option-content">
                                            <div style="font-weight:500;color:#d1d5db;margin-bottom:2px;font-size:11px;">${escape(template.name)}</div>
                                            ${details ? `<div style="font-size:14px;color:#9ca3af;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">${escape(details)}</div>` : ''}
                                        </div>
                                        <button class="utm-delete-template" data-template-index="${originalIndex}" style="padding:2px 4px;background:transparent;color:#6b7280;border:1px solid #374151;border-radius:3px;cursor:pointer;font-size:14px;margin-left:8px;transition:all 0.2s;opacity:0.7;" onmouseover="this.style.background='#dc2626';this.style.color='white';this.style.opacity='1';" onmouseout="this.style.background='transparent';this.style.color='#6b7280';this.style.opacity='0.7';">✕</button>
                                    `;
                                    
                                    // Add hover effect for option
                                    option.addEventListener('mouseenter', function() {
                                        this.style.background = '#2a2a2a';
                                    });
                                    option.addEventListener('mouseleave', function() {
                                        this.style.background = '';
                                    });
                                    
                                    // Load template when option content is clicked
                                    option.querySelector('.utm-option-content').addEventListener('click', function(e) {
                                        e.stopPropagation();
                                        loadTemplate(template);
                                        closeDropdown();
                                        updateInputDisplay(`Loaded: ${template.name}`, true);
                                    });
                                    
                                    // Delete template when delete button is clicked
                                    option.querySelector('.utm-delete-template').addEventListener('click', function(e) {
                                        e.stopPropagation();
                                        if (confirm(`Delete template "${template.name}"?`)) {
                                            deleteTemplate(originalIndex);
                                        }
                                    });
                                    
                                    optionsContainer.appendChild(option);
                                });
                            }
                            

                            
                            // Typeahead-style dropdown helper functions
                            function toggleDropdown() {
                                const optionsContainer = document.getElementById('utm-dropdown-options');
                                const mainInput = document.getElementById('utm-template-input');
                                const isOpen = optionsContainer.style.display === 'block';
                                
                                if (isOpen) {
                                    closeDropdown();
                                } else {
                                    const searchTerm = mainInput.value || '';
                                    renderTemplateOptions(searchTerm);
                                    openDropdown();
                                    mainInput.focus();
                                }
                            }
                            
                            function openDropdown() {
                                const optionsContainer = document.getElementById('utm-dropdown-options');
                                optionsContainer.style.display = 'block';
                                
                                // Ensure the panel can accommodate the dropdown
                                expandPanelForDropdown();
                                // Don't re-render here as it will override the current filter
                            }
                            
                            function closeDropdown() {
                                const optionsContainer = document.getElementById('utm-dropdown-options');
                                optionsContainer.style.display = 'none';
                                
                                // Restore panel to original size if needed
                                restorePanelSize();
                            }
                            
                            // Expand panel to accommodate dropdown if needed
                            function expandPanelForDropdown() {
                                const panel = document.querySelector('.utm-builder-panel');
                                const optionsContainer = document.getElementById('utm-dropdown-options');
                                const templateInput = document.getElementById('utm-template-input');
                                
                                if (!panel || !optionsContainer || !templateInput) return;
                                
                                // Store original height if not already stored
                                if (!panel.dataset.originalHeight) {
                                    panel.dataset.originalHeight = panel.style.height || panel.offsetHeight + 'px';
                                }
                                
                                // Calculate required space
                                const panelRect = panel.getBoundingClientRect();
                                const inputRect = templateInput.getBoundingClientRect();
                                const dropdownHeight = 200; // max-height of dropdown
                                const padding = 20; // extra padding
                                
                                const requiredBottom = inputRect.bottom - panelRect.top + dropdownHeight + padding;
                                const currentPanelHeight = panelRect.height;
                                
                                // Only expand if needed
                                if (requiredBottom > currentPanelHeight) {
                                    const newHeight = Math.max(requiredBottom, currentPanelHeight);
                                    panel.style.height = newHeight + 'px';
                                    
                                    // Ensure panel doesn't go off screen
                                    const viewportHeight = window.innerHeight;
                                    const panelTop = panelRect.top;
                                    
                                    if (panelTop + newHeight > viewportHeight - 20) {
                                        // Move panel up if it would go off screen
                                        const newTop = Math.max(20, viewportHeight - newHeight - 20);
                                        panel.style.top = newTop + 'px';
                                    }
                                }
                            }
                            
                            // Restore panel to original size
                            function restorePanelSize() {
                                const panel = document.querySelector('.utm-builder-panel');
                                if (!panel || !panel.dataset.originalHeight) return;
                                
                                // Delay restoration to allow for smooth transition
                                setTimeout(() => {
                                    panel.style.height = panel.dataset.originalHeight;
                                }, 150);
                            }
                            
                            function updateInputDisplay(text, isTemporary = false) {
                                const mainInput = document.getElementById('utm-template-input');
                                mainInput.value = text;
                                mainInput.style.color = isTemporary ? '#10b981' : '#d1d5db';
                                
                                if (isTemporary) {
                                    // Reset to placeholder after 2 seconds
                                    setTimeout(() => {
                                        mainInput.value = '';
                                        mainInput.style.color = '#d1d5db';
                                        mainInput.blur();
                                    }, 2000);
                                }
                            }
                            

                            
                            // Create panel
                            var panel = document.createElement('div');
                            panel.className = 'utm-builder-panel';
                            
                            const savedSettings = getSavedPanelSettings();
                            panel.style.cssText = `position:fixed;top:${savedSettings.top};left:${savedSettings.left};width:${savedSettings.width};z-index:2147483647;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:8px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:16px;overflow:visible;resize:both;min-width:600px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.4`;
                            
                            let html = `
                                <div id="utm-builder-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:16px;padding-bottom:12px;border-bottom:1px solid #2a2a2a;cursor:move;">
                                    <h1 style="margin:0;color:#d1d5db;font-size:16px;font-weight:600;">UTM Builder</h1>
                                    <div style="display:flex;gap:8px;align-items:center;">
                                        <button id="utm-clear-all-btn" style="padding:4px 8px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:4px;cursor:pointer;font-size:11px;font-weight:500;transition:all 0.2s;" onmouseover="this.style.background='#4b5563'" onmouseout="this.style.background='#374151'">Clear All</button>
                                        <button onclick="closeUTMBuilder()" style="padding:4px 8px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:4px;cursor:pointer;font-size:11px;font-weight:500;transition:all 0.2s;">✕</button>
                                    </div>
                                </div>
                                
                                <!-- URL Input -->
                                <div style="margin-bottom:8px;">
                                    <input type="text" id="utm-url-input" placeholder="https://yourwebsite.com" style="width:100%;padding:10px 12px;background:#1f1f1f;border:1px solid #374151;border-radius:4px;color:#d1d5db;font-size:14px;outline:none;height:44px;box-sizing:border-box;" />
                                </div>
                                
                                <!-- UTM Parameters in compact grid -->
                                <div style="display:grid;grid-template-columns:80px 1fr;gap:8px 12px;margin-bottom:12px;">
                                    <label style="color:#9ca3af;font-size:14px;font-weight:500;align-self:center;">Campaign *</label>
                                    <input type="text" id="utm-campaign" placeholder="e.g. holiday-special" style="padding:10px 12px;background:#1f1f1f;border:1px solid #374151;border-radius:4px;color:#d1d5db;font-size:14px;outline:none;height:44px;box-sizing:border-box;" />
                                    
                                    <label style="color:#9ca3af;font-size:14px;font-weight:500;align-self:center;">Medium *</label>
                                    <input type="text" id="utm-medium" placeholder="e.g. organic, cpc, email" style="padding:10px 12px;background:#1f1f1f;border:1px solid #374151;border-radius:4px;color:#d1d5db;font-size:14px;outline:none;height:44px;box-sizing:border-box;" />
                                    
                                    <label style="color:#9ca3af;font-size:14px;font-weight:500;align-self:center;">Source *</label>
                                    <input type="text" id="utm-source" placeholder="e.g. google, facebook" style="padding:10px 12px;background:#1f1f1f;border:1px solid #374151;border-radius:4px;color:#d1d5db;font-size:14px;outline:none;height:44px;box-sizing:border-box;" />
                                    
                                    <label style="color:#9ca3af;font-size:14px;font-weight:500;align-self:center;">Term</label>
                                    <input type="text" id="utm-term" placeholder="PPC keywords" style="padding:10px 12px;background:#1f1f1f;border:1px solid #374151;border-radius:4px;color:#d1d5db;font-size:14px;outline:none;height:44px;box-sizing:border-box;" />
                                    
                                    <label style="color:#9ca3af;font-size:14px;font-weight:500;align-self:center;">Content</label>
                                    <input type="text" id="utm-content" placeholder="Differentiate ads" style="padding:10px 12px;background:#1f1f1f;border:1px solid #374151;border-radius:4px;color:#d1d5db;font-size:14px;outline:none;height:44px;box-sizing:border-box;" />
                                </div>
                                
                                <!-- Result Section -->
                                <div style="background:#111111;border-radius:4px;border:1px solid #2a2a2a;margin-bottom:8px;overflow:hidden;">
                                    <div style="background:#1a1a1a;padding:6px 8px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;display:flex;justify-content:space-between;align-items:center;">
                                        Generated URL
                                        <button id="utm-copy-btn" style="padding:3px 6px;background:#7C3AED;color:white;border:none;border-radius:2px;cursor:pointer;font-size:14px;font-weight:500;transition:all 0.2s;opacity:0.5;pointer-events:none;" onmouseover="if(this.style.pointerEvents !== 'none') this.style.background='#6D28D9'" onmouseout="if(this.style.pointerEvents !== 'none') this.style.background='#7C3AED'">Copy</button>
                                    </div>
                                    <div style="padding:6px 8px;">
                                        <div id="utm-result" style="background:#1f1f1f;padding:6px;border-radius:2px;font-family:monospace;font-size:14px;line-height:1.3;word-break:break-all;color:#6b7280;min-height:16px;">Enter a URL to see the UTM result...</div>
                                    </div>
                                </div>
                                
                                <!-- Templates Section -->
                                <div style="background:#111111;border-radius:4px;border:1px solid #2a2a2a;overflow:visible;">
                                    <div style="background:#1a1a1a;padding:6px 8px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;display:flex;justify-content:space-between;align-items:center;">
                                        Saved Templates
                                        <div style="display:flex;gap:3px;align-items:center;">
                                            <button id="utm-load-defaults-btn" style="padding:3px 5px;background:transparent;color:#6b7280;border:1px solid #374151;border-radius:2px;cursor:pointer;font-size:14px;font-weight:400;transition:all 0.2s;" onmouseover="this.style.background='#374151';this.style.color='#d1d5db'" onmouseout="this.style.background='transparent';this.style.color='#6b7280'">Defaults</button>
                                            <button id="utm-import-btn" style="padding:3px 5px;background:transparent;color:#6b7280;border:1px solid #374151;border-radius:2px;cursor:pointer;font-size:14px;font-weight:400;transition:all 0.2s;" onmouseover="this.style.background='#374151';this.style.color='#d1d5db'" onmouseout="this.style.background='transparent';this.style.color='#6b7280'">Import</button>
                                            <button id="utm-export-btn" style="padding:3px 5px;background:transparent;color:#6b7280;border:1px solid #374151;border-radius:2px;cursor:pointer;font-size:14px;font-weight:400;transition:all 0.2s;" onmouseover="this.style.background='#374151';this.style.color='#d1d5db'" onmouseout="this.style.background='transparent';this.style.color='#6b7280'">Export</button>
                                            <button id="utm-add-template-btn" style="padding:3px 6px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:2px;cursor:pointer;font-size:14px;font-weight:500;transition:all 0.2s;" onmouseover="this.style.background='#4b5563'" onmouseout="this.style.background='#374151'">+ Add Template</button>
                                        </div>
                                    </div>
                                    <div style="padding:6px 8px;">
                                        <div style="position:relative;">
                                            <!-- Typeahead-style single input field -->
                                            <div style="position:relative;">
                                                <input type="text" id="utm-template-input" placeholder="Search templates or type to filter..." style="width:100%;padding:10px 32px 10px 12px;background:#1f1f1f;border:1px solid #374151;border-radius:4px;color:#d1d5db;font-size:14px;outline:none;height:44px;box-sizing:border-box;" />
                                                <span id="utm-dropdown-arrow" style="position:absolute;right:8px;top:50%;transform:translateY(-50%);color:#6b7280;font-size:14px;cursor:pointer;user-select:none;pointer-events:all;">▼</span>
                                                <div id="utm-dropdown-options" style="position:absolute;top:100%;left:0;right:0;background:#1f1f1f;border:1px solid #374151;border-top:none;border-radius:0 0 4px 4px;max-height:200px;overflow-y:auto;z-index:2147483647;display:none;box-shadow:0 4px 12px rgba(0,0,0,0.4);">
                                                    <!-- Options will be populated here -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                            
                            panel.innerHTML = html;
                            document.body.appendChild(panel);
                            

                            
                            // Add event listeners for all inputs
                            document.getElementById('utm-url-input').addEventListener('input', updateResult);
                            document.getElementById('utm-campaign').addEventListener('input', updateResult);
                            document.getElementById('utm-medium').addEventListener('input', updateResult);
                            document.getElementById('utm-source').addEventListener('input', updateResult);
                            document.getElementById('utm-term').addEventListener('input', updateResult);
                            document.getElementById('utm-content').addEventListener('input', updateResult);
                            
                            // Copy URL functionality (shared between button and URL click)
                            function copyURLToClipboard() {
                                const resultText = document.getElementById('utm-result').textContent;
                                const button = document.getElementById('utm-copy-btn');
                                const resultElement = document.getElementById('utm-result');
                                
                                if (resultText && !resultText.includes('Enter a URL')) {
                                    navigator.clipboard.writeText(resultText).then(() => {
                                        // Update button feedback
                                        const originalButtonText = button.textContent;
                                        const originalButtonBg = button.style.background;
                                        button.textContent = 'Copied!';
                                        button.style.background = '#10b981';
                                        
                                        // Update URL element feedback
                                        const originalResultColor = resultElement.style.color;
                                        const originalResultBg = resultElement.style.background;
                                        resultElement.style.color = '#10b981';
                                        resultElement.style.background = '#0f2f1f';
                                        resultElement.style.transform = 'scale(1.02)';
                                        
                                        setTimeout(() => {
                                            button.textContent = 'Copy';
                                            button.style.background = '#7C3AED';
                                            resultElement.style.color = '#d1d5db';
                                            resultElement.style.background = '#1f1f1f';
                                            resultElement.style.transform = 'scale(1)';
                                        }, 1000);
                                    }).catch(() => {
                                        button.textContent = 'Error';
                                        setTimeout(() => button.textContent = 'Copy', 1000);
                                    });
                                }
                            }

                            // Add event listeners for buttons
                            document.getElementById('utm-copy-btn').addEventListener('click', copyURLToClipboard);
                            
                            // Add click event listener for the result URL
                            document.getElementById('utm-result').addEventListener('click', function() {
                                const resultText = this.textContent;
                                if (resultText && !resultText.includes('Enter a URL')) {
                                    copyURLToClipboard();
                                }
                            });
                            
                            // Add hover effects for the result URL
                            document.getElementById('utm-result').addEventListener('mouseenter', function() {
                                const resultText = this.textContent;
                                if (resultText && !resultText.includes('Enter a URL')) {
                                    this.style.background = '#2a2a2a';
                                    this.style.transform = 'scale(1.01)';
                                }
                            });
                            
                            document.getElementById('utm-result').addEventListener('mouseleave', function() {
                                const resultText = this.textContent;
                                if (resultText && !resultText.includes('Enter a URL')) {
                                    this.style.background = '#1f1f1f';
                                    this.style.transform = 'scale(1)';
                                }
                            });
                            
                            document.getElementById('utm-add-template-btn').addEventListener('click', saveCurrentAsTemplate);
                            
                            // Add template management button event listeners
                            document.getElementById('utm-load-defaults-btn').addEventListener('click', function() {
                                const added = loadDefaultTemplates();
                                if (added > 0) {
                                    alert(`Added ${added} missing default templates!`);
                                } else {
                                    alert('All default templates are already loaded.');
                                }
                            });
                            document.getElementById('utm-import-btn').addEventListener('click', importTemplates);
                            document.getElementById('utm-export-btn').addEventListener('click', exportTemplates);
                            
                            // Add Clear All button event listener
                            document.getElementById('utm-clear-all-btn').addEventListener('click', clearAllFields);
                            
                            // Add typeahead input listeners
                            const templateInput = document.getElementById('utm-template-input');
                            const dropdownArrow = document.getElementById('utm-dropdown-arrow');
                            
                            // Show dropdown and filter on input
                            templateInput.addEventListener('input', function() {
                                const searchTerm = this.value;
                                renderTemplateOptions(searchTerm);
                                openDropdown();
                            });
                            
                            // Show all templates on focus
                            templateInput.addEventListener('focus', function() {
                                const searchTerm = this.value;
                                renderTemplateOptions(searchTerm);
                                openDropdown();
                            });
                            
                            // Hide dropdown on blur (with delay to allow clicks)
                            templateInput.addEventListener('blur', function() {
                                setTimeout(() => {
                                    closeDropdown();
                                }, 150);
                            });
                            
                            // Toggle dropdown when arrow is clicked
                            dropdownArrow.addEventListener('click', function(e) {
                                e.preventDefault();
                                e.stopPropagation();
                                templateInput.focus();
                                toggleDropdown();
                            });
                            
                            // Close dropdown when clicking outside
                            document.addEventListener('click', function(e) {
                                if (!e.target.closest('#utm-template-input') && 
                                    !e.target.closest('#utm-dropdown-options') && 
                                    !e.target.closest('#utm-dropdown-arrow')) {
                                    closeDropdown();
                                }
                            });
                            
                            // Close dropdown on Escape key
                            document.addEventListener('keydown', function(e) {
                                if (e.key === 'Escape') {
                                    closeDropdown();
                                    templateInput.blur();
                                }
                            });
                            
                            // Initialize current page URL and load saved values
                            const savedValues = getSavedUTMValues();
                            document.getElementById('utm-url-input').value = savedValues.url || window.location.href;
                            document.getElementById('utm-campaign').value = savedValues.campaign || '';
                            document.getElementById('utm-medium').value = savedValues.medium || '';
                            document.getElementById('utm-source').value = savedValues.source || '';
                            document.getElementById('utm-term').value = savedValues.term || '';
                            document.getElementById('utm-content').value = savedValues.content || '';
                            
                            // Initialize templates with defaults if none exist
                            initializeTemplates();
                            
                            updateResult();
                            renderTemplates();
                            window.closeUTMBuilder = function() {
                                panel.remove();
                                // Clean up global functions
                                window.closeUTMBuilder = null;
                            };
                            
                            // Dragging functionality
                            var isDragging = false;
                            var dragOffsetX = 0, dragOffsetY = 0;
                            var header = document.getElementById('utm-builder-header');
                            
                            function dragStart(e) {
                                e = e.type === "mousedown" ? e : e.touches[0];
                                if (e.target.tagName === 'BUTTON') return;
                                
                                if (e.target === header || header.contains(e.target)) {
                                    isDragging = true;
                                    
                                    // Get current panel position
                                    const rect = panel.getBoundingClientRect();
                                    
                                    // Calculate offset between mouse and panel's top-left corner
                                    dragOffsetX = e.clientX - rect.left;
                                    dragOffsetY = e.clientY - rect.top;
                                    
                                    header.style.cursor = 'grabbing';
                                    e.preventDefault();
                                }
                            }
                            
                            function dragEnd(e) {
                                if (isDragging) {
                                    isDragging = false;
                                    header.style.cursor = 'move';
                                    savePanelSettings(panel);
                                }
                            }
                            
                            function drag(e) {
                                if (isDragging) {
                                    e.preventDefault();
                                    e = e.type === "mousemove" ? e : e.touches[0];
                                    
                                    // Calculate new position
                                    const newX = e.clientX - dragOffsetX;
                                    const newY = e.clientY - dragOffsetY;
                                    
                                    // Keep panel within viewport bounds
                                    const maxX = window.innerWidth - panel.offsetWidth;
                                    const maxY = window.innerHeight - panel.offsetHeight;
                                    
                                    const constrainedX = Math.max(0, Math.min(newX, maxX));
                                    const constrainedY = Math.max(0, Math.min(newY, maxY));
                                    
                                    panel.style.left = constrainedX + "px";
                                    panel.style.top = constrainedY + "px";
                                }
                            }
                            
                            header.addEventListener('mousedown', dragStart);
                            document.addEventListener('mousemove', drag);
                            document.addEventListener('mouseup', dragEnd);
                            
                            // Escape key to close
                            function handleKeyDown(e) {
                                if (e.key === 'Escape') {
                                    window.closeUTMBuilder();
                                    document.removeEventListener('keydown', handleKeyDown);
                                }
                            }
                            document.addEventListener('keydown', handleKeyDown);
                            
                            // Auto-save panel settings when resized
                            const resizeObserver = new ResizeObserver(entries => {
                                savePanelSettings(panel);
                            });
                            resizeObserver.observe(panel);
                            
                        })();
                    }
                });
            });
        } catch (error) {
            console.error('UTM Builder error:', error);
        }
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                function resetUTMBuilder() {
                    // Clean up all modifications made by this action
                    const existingPanel = document.querySelector('.utm-builder-panel');
                    if (existingPanel) {
                        existingPanel.remove();
                    }
                    
                    // Clean up global functions
                    if (window.closeUTMBuilder) window.closeUTMBuilder = null;
                    
                    console.log('UTM Builder reset completed');
                }

                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetUTMBuilder
                    }, () => {
                        resolve();
                    });
                });
            } catch (error) {
                console.error('UTM Builder reset error:', error);
                resolve();
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UTMBuilderAction;
} else {
    window.UTMBuilderAction = UTMBuilderAction;
} 