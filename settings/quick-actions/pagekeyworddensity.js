/**
 * Page Keyword Density Action
 * Analyzes keyword density on current page
 */
class PageKeywordDensityAction {
  
  static execute() {
    try {
      console.log('Page Keyword Density Analyzer activated');
      
      // Remove existing keyword density panel if present
      document.querySelectorAll('.keyword-density-audit-panel').forEach(panel => panel.remove());
      
      // Helper functions
      function escape(str) {
        if (!str) return '';
        return str.replace(/&/g, '&amp;')
                 .replace(/</g, '&lt;')
                 .replace(/>/g, '&gt;')
                 .replace(/"/g, '&quot;')
                 .replace(/'/g, '&#39;');
      }
      
      function hlCode(str) {
        return '<code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + str + '</code>';
      }
      
      function createRow(title, info, content) {
        return `
          <div style="display:flex;padding:16px 0;border-bottom:1px solid #2a2a2a;">
            <div style="min-width:140px;font-weight:500;color:#9ca3af;font-size:13px;text-transform:uppercase;margin-right:20px;">${title}</div>
            <div style="flex:1;">
              ${info ? `<div style="font-size:12px;color:#6b7280;margin-bottom:8px;">${info}</div>` : ''}
              <div style="color:#d1d5db;line-height:1.5;">${content}</div>
            </div>
          </div>
        `;
      }
      
      // Analyze keyword density
      const pageText = document.body.innerText.toLowerCase();
      const words = pageText.match(/\b\w+\b/g) || [];
      const totalWords = words.length;
      const wordCounts = {};
      
      // Count words longer than 3 characters
      words.forEach(word => {
        if (word.length > 3) {
          wordCounts[word] = (wordCounts[word] || 0) + 1;
        }
      });
      
      // Get top keywords
      const topKeywords = Object.entries(wordCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20)
        .map(([word, count]) => ({
          word,
          count,
          percentage: ((count / totalWords) * 100).toFixed(2)
        }));
      
      // Create panel
      var panel = document.createElement('div');
      panel.className = 'keyword-density-audit-panel';
      panel.style.cssText = `position:fixed;top:20px;left:20px;width:50%;max-height:85vh;z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;overflow:auto;resize:both;min-width:400px;min-height:500px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5`;
      
      let html = `
        <div id="page-keyword-density-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;">
                          <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;"><span style="color: #7C3AED; font-size: 18px;">●</span> Page Keyword Density</h2>
          <button onclick="this.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
        </div>
      `;
      
      // Page Statistics
      html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
      html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Page Statistics</div>';
      html += '<div style="padding:0 20px;">';
      html += createRow('Total Words', '', hlCode(totalWords.toLocaleString()));
      html += createRow('Unique Keywords', 'Words longer than 3 characters', hlCode(Object.keys(wordCounts).length.toLocaleString()));
      html += createRow('Page Title', '', hlCode(escape(document.title)));
      html += '</div>';
      html += '</div>';
      
      // Top Keywords
      if (topKeywords.length > 0) {
        html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
        html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Top 20 Keywords by Density</div>';
        html += '<div style="padding:0 20px;">';
        
        topKeywords.forEach((keyword, index) => {
          const rank = index + 1;
          const densityColor = keyword.percentage >= 2 ? '#f59e0b' : (keyword.percentage >= 1 ? '#10b981' : '#6b7280');
          html += createRow(
            `#${rank}`,
            `<span style="color:${densityColor};font-weight:500;">${keyword.percentage}% density</span>`,
            `${hlCode(escape(keyword.word))} <span style="color:#6b7280;">• ${keyword.count} occurrences</span>`
          );
        });
        
        html += '</div>';
        html += '</div>';
      }
      
      panel.innerHTML = html;
      document.body.appendChild(panel);
      
      // Add drag functionality
      var isDragging = false;
      var currentX;
      var currentY;
      var initialX;
      var initialY;
      var xOffset = 0;
      var yOffset = 0;
      
      var header = panel.querySelector('#page-keyword-density-header');
      
      function dragStart(e) {
        if (e.target.tagName === 'BUTTON') return; // Don't drag when clicking close button
        
        if (e.type === "touchstart") {
          initialX = e.touches[0].clientX - xOffset;
          initialY = e.touches[0].clientY - yOffset;
        } else {
          initialX = e.clientX - xOffset;
          initialY = e.clientY - yOffset;
        }
        
        if (e.target === header || header.contains(e.target)) {
          isDragging = true;
          panel.style.cursor = 'grabbing';
          header.style.cursor = 'grabbing';
        }
      }
      
      function dragEnd(e) {
        initialX = currentX;
        initialY = currentY;
        isDragging = false;
        panel.style.cursor = 'default';
        header.style.cursor = 'move';
      }
      
      function drag(e) {
        if (isDragging) {
          e.preventDefault();
          
          if (e.type === "touchmove") {
            currentX = e.touches[0].clientX - initialX;
            currentY = e.touches[0].clientY - initialY;
          } else {
            currentX = e.clientX - initialX;
            currentY = e.clientY - initialY;
          }
          
          xOffset = currentX;
          yOffset = currentY;
          
          // Constrain to viewport
          var rect = panel.getBoundingClientRect();
          var maxX = window.innerWidth - rect.width;
          var maxY = window.innerHeight - rect.height;
          
          currentX = Math.max(0, Math.min(currentX, maxX));
          currentY = Math.max(0, Math.min(currentY, maxY));
          
          // Clear any existing positioning and use absolute positioning
          panel.style.right = '';
          panel.style.left = currentX + 'px';
          panel.style.top = currentY + 'px';
        }
      }
      
      // Add event listeners for drag functionality
      header.addEventListener('mousedown', dragStart);
      document.addEventListener('mousemove', drag);
      document.addEventListener('mouseup', dragEnd);
      
      // Touch events for mobile
      header.addEventListener('touchstart', dragStart);
      document.addEventListener('touchmove', drag);
      document.addEventListener('touchend', dragEnd);
      
      // Get initial position for offset calculation
      var rect = panel.getBoundingClientRect();
      xOffset = rect.left;
      yOffset = rect.top;
      
      // Handle Escape key to close panel
      function handleKeyDown(e) {
        if (e.key === 'Escape') {
          panel.remove();
          document.removeEventListener('keydown', handleKeyDown);
          // Clean up drag event listeners
          document.removeEventListener('mousemove', drag);
          document.removeEventListener('mouseup', dragEnd);
          document.removeEventListener('touchmove', drag);
          document.removeEventListener('touchend', dragEnd);
        }
      }
      
      document.addEventListener('keydown', handleKeyDown);
      
      return { 
        success: true, 
        message: 'Page Keyword Density Analyzer activated successfully'
      };
    } catch (error) {
      console.error('Error executing Page Keyword Density Analyzer:', error);
      alert("Error applying Page Keyword Density Analyzer: " + error.message);
      return { error: error.message };
    }
  }
  
  static reset() {
    try {
      const existingPanel = document.querySelector('.keyword-density-audit-panel');
      if (existingPanel) {
        existingPanel.remove();
        console.log('Page keyword density audit panel removed');
      }
      
      // Clean up any global event listeners that might be left over
      // Note: Individual panel event listeners are cleaned up when the panel is removed
      
      return { success: true, message: 'Page keyword density reset completed' };
    } catch (error) {
      console.error('Error resetting Page Keyword Density Analyzer:', error);
      return { error: error.message };
    }
  }
}

// Make it available globally
if (typeof window !== 'undefined') {
  window.PageKeywordDensityAction = PageKeywordDensityAction;
} 