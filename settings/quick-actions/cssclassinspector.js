/**
 * CSS Class Inspector Action - Interactive CSS class analysis tool
 * Provides detailed inspection of CSS classes and their applied styles
 */
class CSSClassInspectorAction {
    static execute() {
        // Remove existing class inspector panels if present
        document.querySelectorAll('.css-class-inspector-panel, .css-class-tooltip').forEach(panel => panel.remove());
        
        // Remove any existing event listeners
        if (window.cssClassInspectorCleanup) {
            window.cssClassInspectorCleanup();
        }
        
        // Helper functions
        function escape(str) {
            if (!str) return '';
            return str.replace(/&/g, '&amp;')
                     .replace(/</g, '&lt;')
                     .replace(/>/g, '&gt;')
                     .replace(/"/g, '&quot;')
                     .replace(/'/g, '&#39;');
        }
        
        function hlCode(str) {
            return '<code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + str + '</code>';
        }
        
        // Get styles that differ from default for a class
        function getClassStyles(element, className) {
            const computedStyle = window.getComputedStyle(element);
            const tempElement = document.createElement(element.tagName);
            tempElement.className = className;
            document.body.appendChild(tempElement);
            const classStyle = window.getComputedStyle(tempElement);
            document.body.removeChild(tempElement);
            
            const differences = {};
            const properties = [
                'display', 'position', 'flex', 'grid', 'margin', 'padding', 
                'width', 'height', 'color', 'background', 'border', 
                'font-size', 'font-weight', 'transform', 'animation', 
                'box-shadow', 'opacity', 'z-index', 'overflow', 'text-align',
                'line-height', 'letter-spacing', 'text-decoration'
            ];
            
            properties.forEach(prop => {
                const currentValue = classStyle.getPropertyValue(prop);
                if (currentValue && currentValue !== computedStyle.getPropertyValue(prop)) {
                    differences[prop] = currentValue;
                }
            });
            
            return differences;
        }
        
        // Create main control panel
        var panel = document.createElement('div');
        panel.className = 'css-class-inspector-panel';
        panel.style.cssText = `position:fixed;top:20px;right:20px;width:400px;z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5;max-height:80vh;overflow-y:auto;resize:both;min-width:350px;`;
        
        let html = `
            <div id="css-class-inspector-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid #2a2a2a;cursor:move;">
                <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;"><span style="color: #7C3AED; font-size: 18px;">●</span> CSS Class Inspector</h2>
                <button onclick="this.parentNode.parentNode.remove();if(window.cssClassInspectorCleanup)window.cssClassInspectorCleanup();" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
            </div>
        `;
        
        html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:20px;overflow:hidden;">';
        html += '<div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:13px;">How to Use</div>';
        html += '<div style="padding:16px;color:#d1d5db;font-size:13px;line-height:1.6;">';
        html += '🖱️ <strong>Hover</strong> over any element to see its CSS classes<br>';
        html += '🎯 <strong>Click</strong> any element to store its selectors<br>';
        html += '📋 <strong>Copy buttons</strong> let you copy selectors easily<br>';
        html += '✨ <strong>Only shows styles</strong> that differ from defaults';
        html += '</div></div>';
        
        html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:20px;overflow:hidden;">';
        html += '<div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:13px;">Inspector Control</div>';
        html += '<div style="padding:16px;">';
        html += '<label style="display:flex;align-items:center;gap:8px;color:#d1d5db;cursor:pointer;">';
        html += '<input type="checkbox" id="css-inspector-toggle" checked style="width:16px;height:16px;">';
        html += '<span>Enable Class Inspection</span>';
        html += '</label>';
        html += '</div></div>';
        
        html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;overflow:hidden;">';
        html += '<div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:13px;">Current Element</div>';
        html += '<div id="current-element-info" style="padding:16px;min-height:60px;color:#6b7280;font-style:italic;">Hover over elements to see their CSS classes • Click to store</div>';
        html += '</div>';
        
        html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-top:20px;overflow:hidden;">';
        html += '<div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:13px;cursor:pointer;display:flex;justify-content:space-between;align-items:center;" id="stored-elements-header">';
        html += '<span>Stored Elements</span>';
        html += '<span id="stored-toggle" style="color:#e5e7eb;">▼</span>';
        html += '</div>';
        html += '<div id="stored-elements-content" style="padding:0;max-height:300px;overflow-y:auto;">';
        html += '<div id="stored-elements-list" style="padding:16px;color:#6b7280;font-style:italic;">Click elements to store their selectors here</div>';
        html += '</div>';
        html += '</div>';
        
        panel.innerHTML = html;
        document.body.appendChild(panel);
        
        // Add drag functionality
        var isDragging = false;
        var currentX;
        var currentY;
        var initialX;
        var initialY;
        var xOffset = 0;
        var yOffset = 0;
        
        var header = panel.querySelector('#css-class-inspector-header');
        
        function dragStart(e) {
            if (e.target.tagName === 'BUTTON') return; // Don't drag when clicking close button
            
            if (e.type === "touchstart") {
                initialX = e.touches[0].clientX - xOffset;
                initialY = e.touches[0].clientY - yOffset;
            } else {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;
            }
            
            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
                panel.style.cursor = 'grabbing';
                header.style.cursor = 'grabbing';
            }
        }
        
        function dragEnd(e) {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            panel.style.cursor = 'default';
            header.style.cursor = 'move';
        }
        
        function drag(e) {
            if (isDragging) {
                e.preventDefault();
                
                if (e.type === "touchmove") {
                    currentX = e.touches[0].clientX - initialX;
                    currentY = e.touches[0].clientY - initialY;
                } else {
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;
                }
                
                xOffset = currentX;
                yOffset = currentY;
                
                // Constrain to viewport
                var rect = panel.getBoundingClientRect();
                var maxX = window.innerWidth - rect.width;
                var maxY = window.innerHeight - rect.height;
                
                currentX = Math.max(0, Math.min(currentX, maxX));
                currentY = Math.max(0, Math.min(currentY, maxY));
                
                // Clear any existing positioning and use absolute positioning
                panel.style.right = '';
                panel.style.left = currentX + 'px';
                panel.style.top = currentY + 'px';
            }
        }
        
        // Add event listeners for drag functionality
        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
        
        // Touch events for mobile
        header.addEventListener('touchstart', dragStart);
        document.addEventListener('touchmove', drag);
        document.addEventListener('touchend', dragEnd);
        
        // Get initial position for offset calculation
        var rect = panel.getBoundingClientRect();
        xOffset = rect.left;
        yOffset = rect.top;
        
        // Create tooltip for hover display
        const tooltip = document.createElement('div');
        tooltip.className = 'css-class-tooltip';
        tooltip.style.cssText = `position:absolute;background:#0a0a0a;color:#d1d5db;padding:12px;border-radius:6px;font-size:12px;z-index:10000000;pointer-events:none;display:none;max-width:400px;box-shadow:0 4px 20px rgba(0,0,0,0.6);border:1px solid #1f1f1f;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;line-height:1.4;`;
        document.body.appendChild(tooltip);
        
        const toggle = panel.querySelector('#css-inspector-toggle');
        const currentElementInfo = panel.querySelector('#current-element-info');
        const storedElementsList = panel.querySelector('#stored-elements-list');
        const storedElementsHeader = panel.querySelector('#stored-elements-header');
        const storedToggle = panel.querySelector('#stored-toggle');
        const storedContent = panel.querySelector('#stored-elements-content');
        let isEnabled = true;
        let storedElements = [];
        let isStoredSectionExpanded = true;
        
        // Copy to clipboard function
        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '✓';
                button.style.background = '#10b981';
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#6366f1';
                }, 1000);
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            });
        }
        
        // Toggle stored elements section
        storedElementsHeader.addEventListener('click', function() {
            isStoredSectionExpanded = !isStoredSectionExpanded;
            storedContent.style.display = isStoredSectionExpanded ? 'block' : 'none';
            storedToggle.textContent = isStoredSectionExpanded ? '▼' : '▶';
        });
        
        toggle.addEventListener('change', function() {
            isEnabled = this.checked;
            if (!isEnabled) {
                tooltip.style.display = 'none';
                currentElementInfo.innerHTML = '<span style="color:#6b7280;font-style:italic;">Inspector disabled</span>';
            } else {
                currentElementInfo.innerHTML = '<span style="color:#6b7280;font-style:italic;">Hover over elements to see their CSS classes • Click to store</span>';
            }
        });
        
        function handleMouseOver(e) {
            if (!isEnabled) return;
            if (e.target.closest('.css-class-inspector-panel, .css-class-tooltip')) return;
            
            const element = e.target;
            const className = element.className;
            
            if (!className) {
                tooltip.style.display = 'none';
                currentElementInfo.innerHTML = '<span style="color:#6b7280;font-style:italic;">Element has no CSS classes</span>';
                return;
            }
            
            const classes = className.split(' ').filter(cls => cls.trim());
            const tagName = element.tagName.toLowerCase();
            const elementId = element.id;
            
            // Update main panel
            let infoHtml = '<div style="padding:0;">';
            infoHtml += '<div style="margin-bottom:12px;">';
            infoHtml += '<strong style="color:#9ca3af;">Element:</strong> ';
            infoHtml += hlCode(tagName + (elementId ? `#${elementId}` : ''));
            infoHtml += '</div>';
            infoHtml += '<div style="margin-bottom:8px;"><strong style="color:#9ca3af;">Classes:</strong></div>';
            classes.forEach(cls => {
                infoHtml += '<div style="margin-bottom:8px;">' + hlCode(`.${cls}`) + '</div>';
            });
            infoHtml += '</div>';
            currentElementInfo.innerHTML = infoHtml;
            
            // Build tooltip content
            const classesWithStyles = classes.map(cls => {
                const styles = getClassStyles(element, cls);
                return { name: cls, styles };
            }).filter(cls => Object.keys(cls.styles).length > 0);
            
            if (classesWithStyles.length === 0) {
                tooltip.style.display = 'none';
                return;
            }
            
            let tooltipContent = `
                <div style="margin-bottom:8px;border-bottom:1px solid #2a2a2a;padding-bottom:6px;">
                    <div style="color:#10b981;font-weight:500;margin-bottom:2px;">
                        ${tagName}${elementId ? `#${elementId}` : ''}
                    </div>
                    <div style="color:#6b7280;font-size:11px;">
                        CSS classes with active styles
                    </div>
                </div>
            `;
            
            classesWithStyles.forEach(cls => {
                tooltipContent += `
                    <div style="margin-bottom:12px;">
                        <div style="color:#e5e7eb;font-weight:500;margin-bottom:4px;">
                            .${escape(cls.name)}
                        </div>
                        <div style="color:#d1d5db;font-family:monospace;font-size:11px;line-height:1.3;">
                            ${Object.entries(cls.styles).map(([prop, value]) => 
                                `<span style="color:#fbbf24;">${escape(prop)}</span>: ${escape(value)};`
                            ).join('<br>')}
                        </div>
                    </div>
                `;
            });
            
            tooltip.innerHTML = tooltipContent;
            tooltip.style.display = 'block';
            
            // Position tooltip
            const rect = element.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            
            let left = rect.right + 10;
            let top = rect.top;
            
            // Adjust if tooltip goes off screen
            if (left + tooltipRect.width > windowWidth) {
                left = rect.left - tooltipRect.width - 10;
            }
            if (top + tooltipRect.height > windowHeight) {
                top = windowHeight - tooltipRect.height - 10;
            }
            if (left < 0) left = 10;
            if (top < 0) top = 10;
            
            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';
        }
        
        function handleMouseOut(e) {
            if (!isEnabled) return;
            if (e.target.closest('.css-class-inspector-panel, .css-class-tooltip')) return;
            tooltip.style.display = 'none';
        }
        
        function handleClick(e) {
            if (!isEnabled) return;
            if (e.target.closest('.css-class-inspector-panel, .css-class-tooltip')) return;
            
            e.preventDefault();
            e.stopPropagation();
            
            const element = e.target;
            const tagName = element.tagName.toLowerCase();
            const elementId = element.id;
            const className = element.className;
            const classes = className ? className.split(' ').filter(cls => cls.trim()) : [];
            
            // Generate selectors
            const selectors = [];
            
            // Tag selector
            selectors.push({ type: 'Tag', value: tagName });
            
            // ID selector
            if (elementId) {
                selectors.push({ type: 'ID', value: `#${elementId}` });
            }
            
            // Class selectors
            classes.forEach(cls => {
                selectors.push({ type: 'Class', value: `.${cls}` });
            });
            
            // Combined class selector
            if (classes.length > 1) {
                selectors.push({ type: 'Combined Classes', value: `.${classes.join('.')}` });
            }
            
            // Tag + ID
            if (elementId) {
                selectors.push({ type: 'Tag + ID', value: `${tagName}#${elementId}` });
            }
            
            // Tag + Classes
            if (classes.length > 0) {
                selectors.push({ type: 'Tag + Classes', value: `${tagName}.${classes.join('.')}` });
            }
            
            // Store the element data
            const elementData = {
                id: Date.now(),
                tagName,
                elementId,
                classes,
                selectors,
                timestamp: new Date().toLocaleTimeString()
            };
            
            storedElements.unshift(elementData); // Add to beginning
            updateStoredElementsList();
            
            // Visual feedback
            element.style.outline = '2px solid #10b981';
            element.style.outlineOffset = '2px';
            setTimeout(() => {
                element.style.outline = '';
                element.style.outlineOffset = '';
            }, 1000);
        }
        
        function updateStoredElementsList() {
            if (storedElements.length === 0) {
                storedElementsList.innerHTML = '<div style="padding:16px;color:#6b7280;font-style:italic;">Click elements to store their selectors here</div>';
                return;
            }
            
            let html = '';
            storedElements.forEach((elementData, index) => {
                html += `
                    <div style="padding:16px;border-bottom:1px solid #2a2a2a;${index === storedElements.length - 1 ? 'border-bottom:none;' : ''}">
                        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;">
                            <div style="color:#10b981;font-weight:500;">
                                ${elementData.tagName}${elementData.elementId ? `#${elementData.elementId}` : ''}
                                ${elementData.classes.length > 0 ? `.${elementData.classes.join('.')}` : ''}
                            </div>
                            <div style="color:#6b7280;font-size:11px;">${elementData.timestamp}</div>
                        </div>
                        <div style="display:grid;gap:8px;">
                `;
                
                elementData.selectors.forEach(selector => {
                    html += `
                        <div style="display:flex;justify-content:space-between;align-items:center;background:#1a1a1a;padding:8px 12px;border-radius:4px;border:1px solid #2a2a2a;">
                            <div style="flex:1;">
                                <div style="color:#9ca3af;font-size:11px;margin-bottom:2px;">${selector.type}</div>
                                <div style="font-family:monospace;color:#d1d5db;font-size:12px;">${escape(selector.value)}</div>
                            </div>
                            <button onclick="event.stopPropagation();" data-selector="${escape(selector.value)}" class="copy-selector-btn" style="padding:4px 8px;background:#6366f1;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:11px;margin-left:8px;transition:all 0.2s;">Copy</button>
                        </div>
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            });
            
            storedElementsList.innerHTML = html;
            
            // Add event listeners to copy buttons
            const copyButtons = storedElementsList.querySelectorAll('.copy-selector-btn');
            copyButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const selector = this.getAttribute('data-selector');
                    copyToClipboard(selector, this);
                });
            });
        }
        
        function handleKeyDown(e) {
            if (e.key === 'Escape') {
                cleanup();
            }
        }
        
        function cleanup() {
            document.body.removeEventListener('mouseover', handleMouseOver);
            document.body.removeEventListener('mouseout', handleMouseOut);
            document.body.removeEventListener('click', handleClick);
            document.removeEventListener('keydown', handleKeyDown);
            
            // Clean up drag event listeners
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', dragEnd);
            document.removeEventListener('touchmove', drag);
            document.removeEventListener('touchend', dragEnd);
            
            if (tooltip && tooltip.parentNode) {
                tooltip.remove();
            }
            if (panel && panel.parentNode) {
                panel.remove();
            }
            window.cssClassInspectorCleanup = null;
        }
        
        window.cssClassInspectorCleanup = cleanup;
        
        document.body.addEventListener('mouseover', handleMouseOver);
        document.body.addEventListener('mouseout', handleMouseOut);
        document.body.addEventListener('click', handleClick);
        document.addEventListener('keydown', handleKeyDown);
    }

    static reset() {
        const existingPanels = document.querySelectorAll('.css-class-inspector-panel, .css-class-tooltip');
        existingPanels.forEach(panel => panel.remove());
        
        if (window.cssClassInspectorCleanup) {
            window.cssClassInspectorCleanup();
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CSSClassInspectorAction;
} else {
    window.CSSClassInspectorAction = CSSClassInspectorAction;
} 