// Keyword Density Action - Analyze keyword density from selected text
class KeywordDensityAction {
    static execute(selectedText) {
        try {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                    target: {tabId: tabs[0].id},
                    func: function(keyword) {
                        try {
                            // Count keyword density function (reusing logic from original keyword.js)
                            const countKeywordDensity = (html, keyword) => {
                                const keywordPattern = new RegExp('\\b' + keyword.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&') + '\\b', 'gi');
                                const keywordMatches = html.match(keywordPattern) || [];
                                const keywordCount = keywordMatches.length;
                                const totalWords = html.replace(/[\W_]+/g, ' ').split(' ').filter(word => word.length > 0).length;
                                return (keywordCount / totalWords) * 100;
                            };

                            const pageContent = document.body.innerText;
                            const density = countKeywordDensity(pageContent, keyword);
                            
                            // Count total occurrences for more detailed info
                            const keywordPattern = new RegExp('\\b' + keyword.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&') + '\\b', 'gi');
                            const keywordMatches = pageContent.match(keywordPattern) || [];
                            const totalWords = pageContent.replace(/[\W_]+/g, ' ').split(' ').filter(word => word.length > 0).length;
                            
                            // Helper functions for styled panel
                            function escape(str) {
                                if (!str) return '';
                                return str.replace(/&/g, '&amp;')
                                         .replace(/</g, '&lt;')
                                         .replace(/>/g, '&gt;')
                                         .replace(/"/g, '&quot;')
                                         .replace(/'/g, '&#39;');
                            }
                            
                            function hlCode(str) {
                                return '<code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + str + '</code>';
                            }
                            
                            function createRow(title, info, content) {
                                return `
                                    <div style="display:flex;padding:16px 0;border-bottom:1px solid #2a2a2a;">
                                        <div style="min-width:140px;font-weight:500;color:#9ca3af;font-size:13px;text-transform:uppercase;margin-right:20px;">${title}</div>
                                        <div style="flex:1;">
                                            ${info ? `<div style="font-size:12px;color:#6b7280;margin-bottom:8px;">${info}</div>` : ''}
                                            <div style="color:#d1d5db;line-height:1.5;">${content}</div>
                                        </div>
                                    </div>
                                `;
                            }
                            
                            // Remove existing keyword density panel if present
                            document.querySelectorAll('.keyword-density-analysis-panel').forEach(panel => panel.remove());
                            
                            // Create styled panel
                            var panel = document.createElement('div');
                            panel.className = 'keyword-density-analysis-panel';
                            panel.style.cssText = `position:fixed;top:20px;right:20px;width:50%;max-width:500px;max-height:85vh;z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;overflow:auto;resize:both;min-width:400px;min-height:300px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5`;
                            
                            const densityColor = density >= 2 ? '#f59e0b' : (density >= 1 ? '#10b981' : '#6b7280');
                            const statusText = density >= 3 ? 'High' : (density >= 1 ? 'Good' : 'Low');
                            
                            let html = `
                                <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;">
                                    <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;"><span style="color: #7C3AED; font-size: 18px;">●</span> Keyword Density Analysis</h2>
                                    <button onclick="this.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
                                </div>
                            `;
                            
                            // Keyword Analysis
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Keyword Analysis</div>';
                            html += '<div style="padding:0 20px;">';
                            
                            html += createRow(
                                'Keyword',
                                '',
                                hlCode(escape(keyword))
                            );
                            
                            html += createRow(
                                'Density',
                                `<span style="color:${densityColor};font-weight:500;">${statusText}</span>`,
                                `<span style="color:${densityColor};font-size:18px;font-weight:600;">${density.toFixed(2)}%</span>`
                            );
                            
                            html += createRow(
                                'Occurrences',
                                'Total matches found',
                                `<span style="color:#10b981;font-size:16px;font-weight:500;">${keywordMatches.length}</span>`
                            );
                            
                            html += createRow(
                                'Total Words',
                                'In page content',
                                hlCode(totalWords.toLocaleString())
                            );
                            
                            html += '</div>';
                            html += '</div>';
                            
                            // Page Information
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Page Information</div>';
                            html += '<div style="padding:0 20px;">';
                            
                            html += createRow(
                                'Page URL',
                                '',
                                `<a href="${escape(window.location.href)}" target="_blank" style="color:#e5e7eb;text-decoration:none;">${escape(window.location.href)}</a>`
                            );
                            
                            html += createRow(
                                'Page Title',
                                '',
                                hlCode(escape(document.title))
                            );
                            
                            html += '</div>';
                            html += '</div>';
                            
                            // SEO Recommendations
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">SEO Recommendations</div>';
                            html += '<div style="padding:20px;">';
                            
                            let recommendations = [];
                            
                            if (density < 1) {
                                recommendations.push('📈 Consider increasing keyword usage. Aim for 1-3% density for better SEO.');
                            } else if (density > 3) {
                                recommendations.push('⚠️ Keyword density is high. Consider reducing usage to avoid keyword stuffing.');
                            } else {
                                recommendations.push('✅ Keyword density is within the optimal range (1-3%).');
                            }
                            
                            if (keywordMatches.length < 5) {
                                recommendations.push('🎯 Consider adding more instances of the keyword in natural contexts.');
                            }
                            
                            if (totalWords < 300) {
                                recommendations.push('📝 Page content is short. Consider adding more content for better SEO.');
                            }
                            
                            html += '<ul style="margin:0;padding-left:18px;color:#d1d5db;line-height:1.6;">';
                            recommendations.forEach(rec => {
                                html += `<li style="margin-bottom:8px;">${rec}</li>`;
                            });
                            html += '</ul>';
                            
                            html += '</div>';
                            html += '</div>';
                            
                            // Info note
                            html += '<div style="background:#1a1a1a;border-radius:6px;border:1px solid #2a2a2a;padding:16px;text-align:center;color:#6b7280;font-size:12px;">Optimal keyword density is typically 1-3% for primary keywords. Analysis is case-insensitive and matches whole words only.</div>';
                            
                            panel.innerHTML = html;
                            document.body.appendChild(panel);
                            
                            // Handle Escape key to close panel
                            function handleKeyDown(e) {
                                if (e.key === 'Escape') {
                                    panel.remove();
                                    document.removeEventListener('keydown', handleKeyDown);
                                }
                            }
                            document.addEventListener('keydown', handleKeyDown);
                            
                            return { 
                                keyword, 
                                occurrences: keywordMatches.length, 
                                totalWords, 
                                density: density.toFixed(2) 
                            };
                        } catch (error) {
                            console.error('Error calculating keyword density:', error);
                            alert('Error calculating keyword density: ' + error.message);
                            return { error: error.message };
                        }
                    },
                    args: [selectedText]
                });
            });
        } catch (error) {
            console.error('Keyword Density error:', error);
        }
    }

    static showSelectionError() {
        try {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                    target: {tabId: tabs[0].id},
                    func: function() {
                        alert('Please select some text first, then right-click to calculate keyword density.');
                    }
                });
            });
        } catch (error) {
            console.error('Keyword Density selection error:', error);
        }
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                function resetKeywordDensity() {
                    const existingPanel = document.querySelector('.keyword-density-analysis-panel');
                    if (existingPanel) {
                        existingPanel.remove();
                        console.log('Keyword Density panel removed');
                    }
                    console.log('Keyword Density reset completed');
                }

                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetKeywordDensity
                    }, () => {
                        resolve();
                    });
                });
            } catch (error) {
                console.error('Keyword Density reset error:', error);
                resolve();
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = KeywordDensityAction;
} else {
    window.KeywordDensityAction = KeywordDensityAction;
} 