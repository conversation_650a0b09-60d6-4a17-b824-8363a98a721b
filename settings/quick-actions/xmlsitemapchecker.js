/**
 * XML Sitemap Checker Action - Analyzes XML sitemap availability and structure
 * Checks common sitemap locations and provides SEO recommendations
 */
class XMLSitemapCheckerAction {
    static execute() {
        // Remove existing sitemap panel if present
        document.querySelectorAll('.sitemap-audit-panel').forEach(panel => panel.remove());
        
        // Helper functions
        function escape(str) {
            if (!str) return '';
            return str.replace(/&/g, '&amp;')
                     .replace(/</g, '&lt;')
                     .replace(/>/g, '&gt;')
                     .replace(/"/g, '&quot;')
                     .replace(/'/g, '&#39;');
        }
        
        function hlCode(str) {
            return '<code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + str + '</code>';
        }
        
        function createRow(title, info, content) {
            return `
                <div style="display:flex;padding:16px 0;border-bottom:1px solid #2a2a2a;">
                    <div style="min-width:140px;font-weight:500;color:#9ca3af;font-size:13px;text-transform:uppercase;margin-right:20px;">${title}</div>
                    <div style="flex:1;">
                        ${info ? `<div style="font-size:12px;color:#6b7280;margin-bottom:8px;">${info}</div>` : ''}
                        <div style="color:#d1d5db;line-height:1.5;">${content}</div>
                    </div>
                </div>
            `;
        }
        
        // Get current URL and potential sitemap locations
        const currentUrl = new URL(window.location.href);
        const sitemapUrls = [
            `${currentUrl.protocol}//${currentUrl.hostname}/sitemap.xml`,
            `${currentUrl.protocol}//${currentUrl.hostname}/sitemap-index.xml`,
            `${currentUrl.protocol}//${currentUrl.hostname}/sitemap_index.xml`,
            `${currentUrl.protocol}//${currentUrl.hostname}/sitemaps.xml`
        ];
        
        // Create panel immediately
        var panel = document.createElement('div');
        panel.className = 'sitemap-audit-panel';
        panel.style.cssText = `position:fixed;top:20px;right:20px;width:50%;max-height:85vh;z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;overflow:auto;resize:both;min-width:400px;min-height:500px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5`;
        
        let html = `
            <div id="xml-sitemap-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;">
                <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;"><span style="color: #7C3AED; font-size: 18px;">●</span> XML Sitemap Checker</h2>
                <button onclick="this.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
            </div>
        `;
        
        html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
        html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Checking Sitemap Locations...</div>';
        html += '<div style="padding:20px;text-align:center;color:#6b7280;font-style:italic;">Please wait while we check for XML sitemaps...</div>';
        html += '</div>';
        
        panel.innerHTML = html;
        document.body.appendChild(panel);
        
        // Drag functionality helper function
        function setupDragFunctionality() {
            var isDragging = false;
            var currentX;
            var currentY;
            var initialX;
            var initialY;
            var xOffset = 0;
            var yOffset = 0;
            
            var header = panel.querySelector('#xml-sitemap-header');
            if (!header) return; // Safety check
            
            function dragStart(e) {
                if (e.target.tagName === 'BUTTON') return; // Don't drag when clicking close button
                
                if (e.type === "touchstart") {
                    initialX = e.touches[0].clientX - xOffset;
                    initialY = e.touches[0].clientY - yOffset;
                } else {
                    initialX = e.clientX - xOffset;
                    initialY = e.clientY - yOffset;
                }
                
                if (e.target === header || header.contains(e.target)) {
                    isDragging = true;
                    panel.style.cursor = 'grabbing';
                    header.style.cursor = 'grabbing';
                }
            }
            
            function dragEnd(e) {
                initialX = currentX;
                initialY = currentY;
                isDragging = false;
                panel.style.cursor = 'default';
                if (header) header.style.cursor = 'move';
            }
            
            function drag(e) {
                if (isDragging) {
                    e.preventDefault();
                    
                    if (e.type === "touchmove") {
                        currentX = e.touches[0].clientX - initialX;
                        currentY = e.touches[0].clientY - initialY;
                    } else {
                        currentX = e.clientX - initialX;
                        currentY = e.clientY - initialY;
                    }
                    
                    xOffset = currentX;
                    yOffset = currentY;
                    
                    // Constrain to viewport
                    var rect = panel.getBoundingClientRect();
                    var maxX = window.innerWidth - rect.width;
                    var maxY = window.innerHeight - rect.height;
                    
                    currentX = Math.max(0, Math.min(currentX, maxX));
                    currentY = Math.max(0, Math.min(currentY, maxY));
                    
                    // Clear any existing positioning and use absolute positioning
                    panel.style.right = '';
                    panel.style.left = currentX + 'px';
                    panel.style.top = currentY + 'px';
                }
            }
            
            // Clean up existing event listeners (in case of re-setup)
            header.removeEventListener('mousedown', dragStart);
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', dragEnd);
            header.removeEventListener('touchstart', dragStart);
            document.removeEventListener('touchmove', drag);
            document.removeEventListener('touchend', dragEnd);
            
            // Add event listeners for drag functionality
            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);
            
            // Touch events for mobile
            header.addEventListener('touchstart', dragStart);
            document.addEventListener('touchmove', drag);
            document.addEventListener('touchend', dragEnd);
            
            // Get initial position for offset calculation
            var rect = panel.getBoundingClientRect();
            xOffset = rect.left;
            yOffset = rect.top;
        }
        
        // Set up initial drag functionality
        setupDragFunctionality();
        
        // Handle Escape key to close panel
        function handleKeyDown(e) {
            if (e.key === 'Escape') {
                panel.remove();
                document.removeEventListener('keydown', handleKeyDown);
            }
        }
        
        document.addEventListener('keydown', handleKeyDown);
        
        // Check all sitemap URLs
        Promise.all(sitemapUrls.map(url => 
            fetch(url)
                .then(response => ({
                    url: url,
                    status: response.ok,
                    statusCode: response.status
                }))
                .catch(() => ({
                    url: url,
                    status: false,
                    statusCode: 404
                }))
        )).then(results => {
            const foundSitemaps = results.filter(result => result.status);
            const overallStatus = foundSitemaps.length > 0 ? 'Pass' : 'Fail';
            
            // Update panel with results
            let newHtml = `
                <div id="xml-sitemap-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;">
                    <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;"><span style="color: #7C3AED; font-size: 18px;">●</span> XML Sitemap Checker</h2>
                    <button onclick="this.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
                </div>
            `;
            
            // Status Overview
            newHtml += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
            newHtml += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Sitemap Status</div>';
            newHtml += '<div style="padding:0 20px;">';
            
            const statusColor = overallStatus === 'Pass' ? '#10b981' : '#f59e0b';
            newHtml += createRow(
                'Overall Status',
                `<span style="color:${statusColor};font-weight:500;">${overallStatus}</span>`,
                foundSitemaps.length > 0 
                    ? `Found ${foundSitemaps.length} XML sitemap file(s)` 
                    : 'No XML sitemap files found at standard locations'
            );
            
            newHtml += createRow(
                'Domain',
                '',
                `<a href="${escape(currentUrl.protocol + '//' + currentUrl.hostname)}" target="_blank" style="color:#e5e7eb;text-decoration:none;">${escape(currentUrl.hostname)}</a>`
            );
            
            newHtml += '</div>';
            newHtml += '</div>';
            
            // Sitemap Results
            newHtml += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
            newHtml += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Sitemap Locations Checked</div>';
            newHtml += '<div style="padding:0 20px;">';
            
            results.forEach((result, index) => {
                const statusText = result.status ? 'Found' : 'Not Found';
                const statusColor = result.status ? '#10b981' : '#6b7280';
                newHtml += createRow(
                    `Location ${index + 1}`,
                    `<span style="color:${statusColor};font-weight:500;">${statusText} (${result.statusCode})</span>`,
                    result.status 
                        ? `<a href="${escape(result.url)}" target="_blank" style="color:#e5e7eb;text-decoration:none;">${escape(result.url)}</a>`
                        : hlCode(escape(result.url))
                );
            });
            
            newHtml += '</div>';
            newHtml += '</div>';
            
            // Found Sitemaps Details
            if (foundSitemaps.length > 0) {
                newHtml += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                newHtml += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Available Sitemaps</div>';
                newHtml += '<div style="padding:0 20px;">';
                
                foundSitemaps.forEach((sitemap, index) => {
                    newHtml += createRow(
                        `Sitemap ${index + 1}`,
                        'Active sitemap found',
                        `<a href="${escape(sitemap.url)}" target="_blank" style="color:#e5e7eb;text-decoration:none;">${escape(sitemap.url)}</a><br><div style="margin-top:8px;color:#6b7280;">Click to view XML content</div>`
                    );
                });
                
                newHtml += '</div>';
                newHtml += '</div>';
            } else {
                // Recommendations for missing sitemaps
                newHtml += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                newHtml += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Recommendations</div>';
                newHtml += '<div style="padding:20px;">';
                newHtml += '<div style="background:#1a1a1a;border-radius:6px;padding:16px;color:#9ca3af;font-size:13px;">';
                newHtml += '<div style="margin-bottom:12px;color:#d1d5db;font-weight:500;">No XML sitemaps found</div>';
                newHtml += '<div style="color:#6b7280;line-height:1.5;margin-bottom:12px;">XML sitemaps help search engines discover and index your content more efficiently.</div>';
                newHtml += '<div style="color:#6b7280;line-height:1.5;">Consider creating an XML sitemap and submitting it to:</div>';
                newHtml += '<ul style="margin:8px 0 0 0;padding-left:18px;color:#6b7280;">';
                newHtml += '<li style="margin-bottom:4px;"><a href="https://search.google.com/search-console" target="_blank" style="color:#e5e7eb;text-decoration:none;">Google Search Console</a></li>';
            newHtml += '<li style="margin-bottom:4px;"><a href="https://www.bing.com/webmasters" target="_blank" style="color:#e5e7eb;text-decoration:none;">Bing Webmaster Tools</a></li>';
            newHtml += '<li style="margin-bottom:0;"><a href="https://sitemaps.org" target="_blank" style="color:#e5e7eb;text-decoration:none;">Learn more about XML sitemaps</a></li>';
                newHtml += '</ul>';
                newHtml += '</div>';
                newHtml += '</div>';
                newHtml += '</div>';
            }
            
            // Info note
            newHtml += '<div style="background:#1a1a1a;border-radius:6px;border:1px solid #2a2a2a;padding:16px;text-align:center;color:#6b7280;font-size:12px;">XML sitemaps should be submitted to search engines and updated regularly. <a href="https://developers.google.com/search/docs/crawling-indexing/sitemaps/overview" target="_blank" style="color:#e5e7eb;text-decoration:none;">Learn more</a></div>';
            
            panel.innerHTML = newHtml;
            
            // Re-setup drag functionality after content update
            setupDragFunctionality();
        }).catch(error => {
            console.error('Error checking sitemaps:', error);
            panel.innerHTML = `
                <div id="xml-sitemap-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;">
                    <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;"><span style="color: #7C3AED; font-size: 18px;">●</span> XML Sitemap Checker</h2>
                    <button onclick="this.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
                </div>
                <div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;padding:20px;text-align:center;color:#f59e0b;">
                    Error checking XML sitemaps: ${escape(error.message)}
                </div>
            `;
            
            // Re-setup drag functionality after error content update
            setupDragFunctionality();
        });
    }

    static reset() {
        try {
            // Remove existing sitemap panels
            document.querySelectorAll('.sitemap-audit-panel').forEach(panel => panel.remove());
            console.log('XML Sitemap Checker panels removed');
            
            // Clean up any global event listeners that might be left over
            // Note: Individual panel event listeners are cleaned up when the panel is removed
            
            return { success: true, message: 'XML Sitemap Checker reset completed' };
        } catch (error) {
            console.error('Error resetting XML Sitemap Checker:', error);
            return { error: error.message };
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = XMLSitemapCheckerAction;
} else {
    window.XMLSitemapCheckerAction = XMLSitemapCheckerAction;
} 