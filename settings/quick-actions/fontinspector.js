// Font Inspector Action - Interactive font inspection tool for web pages
class FontInspectorAction {
    static execute() {
        try {
                            // Remove existing font inspector panel if present
                            document.querySelectorAll('.font-inspector-panel').forEach(panel => panel.remove());
                            
                            // Remove any existing event listeners
                            if (window.fontInspectorCleanup) {
                                window.fontInspectorCleanup();
                            }
                            
                            // Create panel
                            var panel = document.createElement('div');
                            panel.className = 'font-inspector-panel';
                            panel.style.cssText = 'position:fixed;top:20px;right:20px;width:400px;z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5;max-height:80vh;overflow-y:auto;resize:both;min-width:350px;';
                            
                            let html = '<div id="font-inspector-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid #2a2a2a;cursor:move;"><h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;"><span style="color: #7C3AED; font-size: 18px;">●</span> Font Inspector</h2><button id="close-inspector" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button></div>';
                            
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:20px;overflow:hidden;"><div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:13px;">How to Use</div><div style="padding:16px;color:#d1d5db;font-size:13px;line-height:1.6;">🖱️ <strong>Hover</strong> over any text element to inspect its font properties<br>🎯 <strong>Click</strong> on an element to store font details and log to console<br>✨ Elements will be <span style="color:#10b981;">highlighted</span> when inspected</div></div>';
                            
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:20px;overflow:hidden;"><div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:13px;">Font Properties</div><div id="font-details" style="padding:16px;min-height:120px;color:#6b7280;font-style:italic;">Hover over text elements to see font information</div></div>';
                            
                            // Add stored elements section
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:13px;cursor:pointer;display:flex;justify-content:space-between;align-items:center;" id="stored-fonts-header">';
                            html += '<span>Stored Font Information</span>';
                            html += '<span id="stored-fonts-toggle" style="color:#e5e7eb;">▼</span>';
                            html += '</div>';
                            html += '<div id="stored-fonts-content" style="padding:0;max-height:300px;overflow-y:auto;">';
                            html += '<div id="stored-fonts-list" style="padding:16px;color:#6b7280;font-style:italic;">Click elements to store their font information here</div>';
                            html += '</div>';
                            html += '</div>';
                            
                            panel.innerHTML = html;
                            document.body.appendChild(panel);
                            
                            // Add drag functionality
                            var isDragging = false;
                            var currentX;
                            var currentY;
                            var initialX;
                            var initialY;
                            var xOffset = 0;
                            var yOffset = 0;
                            
                            var header = panel.querySelector('#font-inspector-header');
                            
                            function dragStart(e) {
                                if (e.target.tagName === 'BUTTON' || e.target.id === 'close-inspector') return; // Don't drag when clicking close button
                                
                                if (e.type === "touchstart") {
                                    initialX = e.touches[0].clientX - xOffset;
                                    initialY = e.touches[0].clientY - yOffset;
                                } else {
                                    initialX = e.clientX - xOffset;
                                    initialY = e.clientY - yOffset;
                                }
                                
                                if (e.target === header || header.contains(e.target)) {
                                    isDragging = true;
                                    panel.style.cursor = 'grabbing';
                                    header.style.cursor = 'grabbing';
                                }
                            }
                            
                            function dragEnd(e) {
                                initialX = currentX;
                                initialY = currentY;
                                isDragging = false;
                                panel.style.cursor = 'default';
                                header.style.cursor = 'move';
                            }
                            
                            function drag(e) {
                                if (isDragging) {
                                    e.preventDefault();
                                    
                                    if (e.type === "touchmove") {
                                        currentX = e.touches[0].clientX - initialX;
                                        currentY = e.touches[0].clientY - initialY;
                                    } else {
                                        currentX = e.clientX - initialX;
                                        currentY = e.clientY - initialY;
                                    }
                                    
                                    xOffset = currentX;
                                    yOffset = currentY;
                                    
                                    // Constrain to viewport
                                    var rect = panel.getBoundingClientRect();
                                    var maxX = window.innerWidth - rect.width;
                                    var maxY = window.innerHeight - rect.height;
                                    
                                    currentX = Math.max(0, Math.min(currentX, maxX));
                                    currentY = Math.max(0, Math.min(currentY, maxY));
                                    
                                    // Clear any existing positioning and use absolute positioning
                                    panel.style.right = '';
                                    panel.style.left = currentX + 'px';
                                    panel.style.top = currentY + 'px';
                                }
                            }
                            
                            // Add event listeners for drag functionality
                            header.addEventListener('mousedown', dragStart);
                            document.addEventListener('mousemove', drag);
                            document.addEventListener('mouseup', dragEnd);
                            
                            // Touch events for mobile
                            header.addEventListener('touchstart', dragStart);
                            document.addEventListener('touchmove', drag);
                            document.addEventListener('touchend', dragEnd);
                            
                            // Get initial position for offset calculation
                            var rect = panel.getBoundingClientRect();
                            xOffset = rect.left;
                            yOffset = rect.top;
                            
                            let currentElement = null;
                            const fontDetails = panel.querySelector('#font-details');
                            const closeBtn = panel.querySelector('#close-inspector');
                            const storedFontsList = panel.querySelector('#stored-fonts-list');
                            const storedFontsHeader = panel.querySelector('#stored-fonts-header');
                            const storedFontsToggle = panel.querySelector('#stored-fonts-toggle');
                            const storedFontsContent = panel.querySelector('#stored-fonts-content');
                            let storedFonts = [];
                            let isStoredSectionExpanded = true;
                            
                            // Copy to clipboard function
                            function copyToClipboard(text, button) {
                                navigator.clipboard.writeText(text).then(() => {
                                    const originalText = button.textContent;
                                    button.textContent = '✓';
                                    button.style.background = '#10b981';
                                    setTimeout(() => {
                                        button.textContent = originalText;
                                        button.style.background = '#6366f1';
                                    }, 1000);
                                }).catch(() => {
                                    // Fallback for older browsers
                                    const textArea = document.createElement('textarea');
                                    textArea.value = text;
                                    document.body.appendChild(textArea);
                                    textArea.select();
                                    document.execCommand('copy');
                                    document.body.removeChild(textArea);
                                });
                            }
                            
                            // Toggle stored fonts section
                            storedFontsHeader.addEventListener('click', function() {
                                isStoredSectionExpanded = !isStoredSectionExpanded;
                                storedFontsContent.style.display = isStoredSectionExpanded ? 'block' : 'none';
                                storedFontsToggle.textContent = isStoredSectionExpanded ? '▼' : '▶';
                            });
                            
                            function getFontInfo(element) {
                                const style = window.getComputedStyle(element);
                                return {
                                    family: style.fontFamily,
                                    size: style.fontSize,
                                    weight: style.fontWeight,
                                    style: style.fontStyle,
                                    lineHeight: style.lineHeight,
                                    letterSpacing: style.letterSpacing,
                                    color: style.color,
                                    backgroundColor: style.backgroundColor
                                };
                            }
                            
                            function updateStoredFontsList() {
                                if (storedFonts.length === 0) {
                                    storedFontsList.innerHTML = '<div style="padding:16px;color:#6b7280;font-style:italic;">Click elements to store their font information here</div>';
                                    return;
                                }
                                
                                // Add "Copy All Stored Fonts" button at the top
                                let html = `
                                    <div style="padding:16px;border-bottom:2px solid #374151;background:#0f172a;">
                                        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:8px;">
                                            <div style="color:#fbbf24;font-weight:600;font-size:13px;">
                                                📋 ${storedFonts.length} Font${storedFonts.length > 1 ? 's' : ''} Stored
                                            </div>
                                            <button id="copy-all-stored-fonts" style="padding:6px 12px;background:#059669;color:#fff;border:none;border-radius:6px;cursor:pointer;font-size:12px;font-weight:600;transition:all 0.2s;">
                                                📋 Copy All Stored Fonts
                                            </button>
                                        </div>
                                        <div style="color:#94a3b8;font-size:11px;">
                                            Copies CSS for all stored font information
                                        </div>
                                    </div>
                                `;
                                storedFonts.forEach((fontData, index) => {
                                    const fontInfo = fontData.fontInfo;
                                    html += `
                                        <div style="padding:16px;border-bottom:1px solid #2a2a2a;${index === storedFonts.length - 1 ? 'border-bottom:none;' : ''}">
                                            <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;">
                                                <div style="color:#10b981;font-weight:500;">
                                                    ${fontData.tagName}${fontData.elementId ? `#${fontData.elementId}` : ''}
                                                    ${fontData.textContent ? ` - "${fontData.textContent.substring(0, 30)}${fontData.textContent.length > 30 ? '...' : ''}"` : ''}
                                                </div>
                                                <div style="color:#6b7280;font-size:11px;">${fontData.timestamp}</div>
                                            </div>
                                            <div style="display:grid;gap:8px;">
                                    `;
                                    
                                    // Create copyable font properties
                                    const fontProperties = [
                                        { label: 'Font Family', value: fontInfo.family, cssProperty: 'font-family' },
                                        { label: 'Font Size', value: fontInfo.size, cssProperty: 'font-size' },
                                        { label: 'Font Weight', value: fontInfo.weight, cssProperty: 'font-weight' },
                                        { label: 'Font Style', value: fontInfo.style, cssProperty: 'font-style' },
                                        { label: 'Line Height', value: fontInfo.lineHeight, cssProperty: 'line-height' },
                                        { label: 'Letter Spacing', value: fontInfo.letterSpacing, cssProperty: 'letter-spacing' },
                                        { label: 'Color', value: fontInfo.color, cssProperty: 'color' }
                                    ];
                                    
                                    fontProperties.forEach(prop => {
                                        const cssRule = `${prop.cssProperty}: ${prop.value};`;
                                        html += `
                                            <div style="display:flex;justify-content:space-between;align-items:center;background:#1a1a1a;padding:8px 12px;border-radius:4px;border:1px solid #2a2a2a;">
                                                <div style="flex:1;">
                                                    <div style="color:#9ca3af;font-size:11px;margin-bottom:2px;">${prop.label}</div>
                                                    <div style="font-family:monospace;color:#d1d5db;font-size:12px;">${prop.value}</div>
                                                </div>
                                                <button onclick="event.stopPropagation();" data-css-rule="${cssRule.replace(/"/g, '&quot;')}" class="copy-font-btn" style="padding:4px 8px;background:#6366f1;color:#fff;border:none;border-radius:4px;cursor:pointer;font-size:11px;margin-left:8px;transition:all 0.2s;">Copy CSS</button>
                                            </div>
                                        `;
                                    });
                                    
                                    // Add complete CSS rule copy button
                                    const completeCss = fontProperties.map(prop => `${prop.cssProperty}: ${prop.value};`).join('\n');
                                    html += `
                                        <div style="display:flex;justify-content:space-between;align-items:center;background:#111111;padding:8px 12px;border-radius:4px;border:1px solid #374151;">
                                            <div style="flex:1;">
                                                <div style="color:#fbbf24;font-size:11px;margin-bottom:2px;">Complete Font CSS</div>
                                                <div style="font-family:monospace;color:#d1d5db;font-size:12px;">All font properties</div>
                                            </div>
                                            <button onclick="event.stopPropagation();" data-css-rule="${completeCss.replace(/"/g, '&quot;')}" class="copy-font-btn" style="padding:4px 8px;background:#fbbf24;color:#000;border:none;border-radius:4px;cursor:pointer;font-size:11px;margin-left:8px;transition:all 0.2s;font-weight:500;">Copy All</button>
                                        </div>
                                    `;
                                    
                                    html += `
                                            </div>
                                        </div>
                                    `;
                                });
                                
                                storedFontsList.innerHTML = html;
                                
                                // Add event listeners to copy buttons
                                const copyButtons = storedFontsList.querySelectorAll('.copy-font-btn');
                                copyButtons.forEach(button => {
                                    button.addEventListener('click', function(e) {
                                        e.stopPropagation();
                                        const cssRule = this.getAttribute('data-css-rule').replace(/&quot;/g, '"');
                                        copyToClipboard(cssRule, this);
                                    });
                                });
                                
                                // Add event listener for "Copy All Stored Fonts" button
                                const copyAllStoredBtn = storedFontsList.querySelector('#copy-all-stored-fonts');
                                if (copyAllStoredBtn) {
                                    copyAllStoredBtn.addEventListener('click', function(e) {
                                        e.stopPropagation();
                                        
                                        // Generate comprehensive CSS for all stored fonts
                                        let allFontsCSS = '/* Font Inspector - All Stored Font Information */\n';
                                        allFontsCSS += `/* Generated on ${new Date().toLocaleString()} */\n`;
                                        allFontsCSS += `/* Total elements: ${storedFonts.length} */\n\n`;
                                        
                                        storedFonts.forEach((fontData, index) => {
                                            const fontInfo = fontData.fontInfo;
                                            const elementIdentifier = `${fontData.tagName}${fontData.elementId ? `#${fontData.elementId}` : ''}`;
                                            const textPreview = fontData.textContent ? ` (Text: "${fontData.textContent.substring(0, 50)}${fontData.textContent.length > 50 ? '...' : ''}")` : '';
                                            
                                            allFontsCSS += `/* Element ${index + 1}: ${elementIdentifier}${textPreview} */\n`;
                                            allFontsCSS += `/* Captured at: ${fontData.timestamp} */\n`;
                                            allFontsCSS += `.font-style-${index + 1} {\n`;
                                            allFontsCSS += `  font-family: ${fontInfo.family};\n`;
                                            allFontsCSS += `  font-size: ${fontInfo.size};\n`;
                                            allFontsCSS += `  font-weight: ${fontInfo.weight};\n`;
                                            allFontsCSS += `  font-style: ${fontInfo.style};\n`;
                                            allFontsCSS += `  line-height: ${fontInfo.lineHeight};\n`;
                                            allFontsCSS += `  letter-spacing: ${fontInfo.letterSpacing};\n`;
                                            allFontsCSS += `  color: ${fontInfo.color};\n`;
                                            allFontsCSS += `}\n\n`;
                                        });
                                        
                                        // Add summary section
                                        allFontsCSS += '/* ========== SUMMARY ========== */\n';
                                        allFontsCSS += '/* All font families used: */\n';
                                        const uniqueFamilies = [...new Set(storedFonts.map(f => f.fontInfo.family))];
                                        uniqueFamilies.forEach(family => {
                                            allFontsCSS += `/* - ${family} */\n`;
                                        });
                                        
                                        allFontsCSS += '\n/* All font sizes used: */\n';
                                        const uniqueSizes = [...new Set(storedFonts.map(f => f.fontInfo.size))];
                                        uniqueSizes.forEach(size => {
                                            allFontsCSS += `/* - ${size} */\n`;
                                        });
                                        
                                        copyToClipboard(allFontsCSS, this);
                                    });
                                }
                            }
                            
                            function handleMouseOver(e) {
                                if (e.target.closest('.font-inspector-panel')) return;
                                
                                if (e.target.innerText && e.target !== currentElement) {
                                    if (currentElement && currentElement.style) {
                                        currentElement.style.outline = '';
                                    }
                                    
                                    currentElement = e.target;
                                    const fontInfo = getFontInfo(e.target);
                                    
                                    fontDetails.innerHTML = '<div style="padding:0;"><div style="margin-bottom:12px;"><strong style="color:#9ca3af;">Font Family:</strong> <code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + fontInfo.family + '</code></div><div style="margin-bottom:12px;"><strong style="color:#9ca3af;">Size:</strong> <code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + fontInfo.size + '</code></div><div style="margin-bottom:12px;"><strong style="color:#9ca3af;">Weight:</strong> <code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + fontInfo.weight + '</code></div><div style="margin-bottom:12px;"><strong style="color:#9ca3af;">Style:</strong> <code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + fontInfo.style + '</code></div><div style="margin-bottom:12px;"><strong style="color:#9ca3af;">Line Height:</strong> <code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + fontInfo.lineHeight + '</code></div><div style="margin-bottom:12px;"><strong style="color:#9ca3af;">Color:</strong> <code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + fontInfo.color + '</code> <div style="display:inline-block;width:16px;height:16px;background:' + fontInfo.color + ';border:1px solid #444;border-radius:2px;margin-left:8px;vertical-align:middle;"></div></div></div>';
                                    
                                    e.target.style.outline = '2px solid #10b981';
                                    e.target.style.outlineOffset = '1px';
                                }
                            }
                            
                            function handleMouseOut(e) {
                                if (e.target.closest('.font-inspector-panel')) return;
                                if (e.target.innerText && e.target.style) {
                                    e.target.style.outline = '';
                                    e.target.style.outlineOffset = '';
                                }
                            }
                            
                            function handleClick(e) {
                                if (e.target.closest('.font-inspector-panel')) return;
                                if (e.target.innerText) {
                                    e.preventDefault();
                                    const fontInfo = getFontInfo(e.target);
                                    console.log('🔍 Font Inspector - Font Information:', fontInfo);
                                    
                                    // Store the font information
                                    const elementData = {
                                        id: Date.now(),
                                        tagName: e.target.tagName.toLowerCase(),
                                        elementId: e.target.id,
                                        textContent: e.target.innerText.trim(),
                                        fontInfo: fontInfo,
                                        timestamp: new Date().toLocaleTimeString()
                                    };
                                    
                                    storedFonts.unshift(elementData); // Add to beginning
                                    updateStoredFontsList();
                                    
                                    // Visual feedback
                                    e.target.style.outline = '3px solid #10b981';
                                    e.target.style.outlineOffset = '2px';
                                    setTimeout(() => {
                                        e.target.style.outline = '2px solid #10b981';
                                        e.target.style.outlineOffset = '1px';
                                    }, 500);
                                }
                            }
                            
                            function handleKeyDown(e) {
                                if (e.key === 'Escape') {
                                    cleanup();
                                }
                            }
                            
                            function cleanup() {
                                document.body.removeEventListener('mouseover', handleMouseOver);
                                document.body.removeEventListener('mouseout', handleMouseOut);
                                document.body.removeEventListener('click', handleClick);
                                document.removeEventListener('keydown', handleKeyDown);
                                
                                // Clean up drag event listeners
                                document.removeEventListener('mousemove', drag);
                                document.removeEventListener('mouseup', dragEnd);
                                document.removeEventListener('touchmove', drag);
                                document.removeEventListener('touchend', dragEnd);
                                
                                document.querySelectorAll('*').forEach(el => {
                                    if (el.style) {
                                        el.style.outline = '';
                                        el.style.outlineOffset = '';
                                    }
                                });
                                if (panel && panel.parentNode) {
                                    panel.remove();
                                }
                                window.fontInspectorCleanup = null;
                            }
                            
                            window.fontInspectorCleanup = cleanup;
                            closeBtn.onclick = cleanup;
                            
                            document.body.addEventListener('mouseover', handleMouseOver);
                            document.body.addEventListener('mouseout', handleMouseOut);
                            document.body.addEventListener('click', handleClick);
                            document.addEventListener('keydown', handleKeyDown);
        } catch (error) {
            console.error('Font Inspector error:', error);
        }
    }

    static reset() {
        try {
            if (window.fontInspectorCleanup) {
                window.fontInspectorCleanup();
            }
            console.log('Font Inspector reset completed');
        } catch (error) {
            console.error('Font Inspector reset error:', error);
        }
    }
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = FontInspectorAction;
} else {
    window.FontInspectorAction = FontInspectorAction;
} 