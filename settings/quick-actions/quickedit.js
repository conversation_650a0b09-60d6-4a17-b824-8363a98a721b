/**
 * Quick Edit Action - Enable/disable contentEditable mode on web pages
 * Allows users to temporarily edit content on any website
 * Enhanced with bulk selection and deletion functionality
 * Follows Context Menu pattern with direct execution
 */
class QuickEditAction {
    static selectionState = {
        isSelecting: false,
        startX: 0,
        startY: 0,
        currentX: 0,
        currentY: 0,
        selectionBox: null,
        selectedElements: new Set(),
        isShiftPressed: false,
        isDragThresholdMet: false
    };

    static execute() {
        try {
            // Remove any existing Quick Edit elements first (Context Menu pattern)
            document.querySelectorAll('#quick-edit-notice, .quick-edit-selection-box, #quick-edit-animation-style').forEach(el => el.remove());
            
            // Clear any existing selections
            this.clearSelections();

            // Toggle edit mode
            if (document.designMode === 'on') {
                // Turn off edit mode
                this.disableEditMode();
                return { action: 'disabled', message: 'Quick Edit mode disabled' };
            } else {
                // Turn on edit mode
                document.designMode = 'on';
                this.setupEditMode();
                return { action: 'enabled', message: 'Quick Edit mode enabled' };
            }
        } catch (error) {
            console.error('QuickEditAction error:', error);
            return { action: 'error', message: 'Failed to toggle edit mode: ' + error.message };
        }
    }

    static setupEditMode() {
        // Create styled notice with dark purple theme
        const notice = document.createElement('div');
        notice.id = 'quick-edit-notice';
        notice.innerHTML = '<span style="color: #7C3AED; font-size: 18px;">●</span> Edit Mode <span style="font-size: 12px; opacity: 0.8;">(ESC to end | Shift+Drag to select | DEL to delete)</span>';
        notice.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 999999;
            padding: 12px 20px;
            background: #010101;
            color: #ffffff;
            border-radius: 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 8px 24px rgba(124, 58, 237, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            animation: slideIn 0.3s ease-out;
        `;
        
        // Add animation styles
        const animationStyle = document.createElement('style');
        animationStyle.id = 'quick-edit-animation-style';
        animationStyle.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            .quick-edit-selected {
                outline: 2px solid #7c3aed !important;
                outline-offset: 2px !important;
                background-color: rgba(124, 58, 237, 0.1) !important;
            }
            .quick-edit-selection-box {
                position: fixed;
                border: 2px solid #7c3aed;
                background: rgba(124, 58, 237, 0.2);
                z-index: 999998;
                pointer-events: none;
                border-radius: 4px;
            }
        `;
        document.head.appendChild(animationStyle);
        document.body.appendChild(notice);
        
        this.setupEventListeners();
    }

    static setupEventListeners() {
        // Remove any existing listeners first
        this.removeEventListeners();

        // Keyboard listeners
        const keydownListener = (e) => {
            if (e.key === 'Escape') {
                this.disableEditMode();
            } else if (e.key === 'Shift') {
                this.selectionState.isShiftPressed = true;
                document.body.style.cursor = 'crosshair';
            } else if (e.key === 'Delete' || e.key === 'Backspace') {
                if (this.selectionState.selectedElements.size > 0) {
                    e.preventDefault();
                    this.deleteSelectedElements();
                }
            }
        };

        const keyupListener = (e) => {
            if (e.key === 'Shift') {
                this.selectionState.isShiftPressed = false;
                if (!this.selectionState.isSelecting) {
                    document.body.style.cursor = '';
                }
            }
        };

        // Mouse listeners for selection
        const mousedownListener = (e) => {
            if (this.selectionState.isShiftPressed && !this.selectionState.isSelecting) {
                e.preventDefault();
                this.selectionState.isDragThresholdMet = false;
                this.startSelection(e);
            }
        };

        const mousemoveListener = (e) => {
            if (this.selectionState.isSelecting) {
                e.preventDefault();
                
                // Check if we've moved enough to start actual selection (prevents accidental selections)
                const deltaX = Math.abs(e.clientX - this.selectionState.startX);
                const deltaY = Math.abs(e.clientY - this.selectionState.startY);
                
                if (!this.selectionState.isDragThresholdMet && (deltaX > 5 || deltaY > 5)) {
                    this.selectionState.isDragThresholdMet = true;
                }
                
                if (this.selectionState.isDragThresholdMet) {
                    this.updateSelection(e);
                }
            }
        };

        const mouseupListener = (e) => {
            if (this.selectionState.isSelecting) {
                e.preventDefault();
                this.endSelection();
            }
        };

        // Store listeners for cleanup
        window.quickEditListeners = {
            keydown: keydownListener,
            keyup: keyupListener,
            mousedown: mousedownListener,
            mousemove: mousemoveListener,
            mouseup: mouseupListener
        };

        // Add event listeners
        document.addEventListener('keydown', keydownListener);
        document.addEventListener('keyup', keyupListener);
        document.addEventListener('mousedown', mousedownListener, { capture: true });
        document.addEventListener('mousemove', mousemoveListener, { capture: true });
        document.addEventListener('mouseup', mouseupListener, { capture: true });
    }

    static startSelection(e) {
        this.selectionState.isSelecting = true;
        this.selectionState.startX = e.clientX;
        this.selectionState.startY = e.clientY;
        this.selectionState.currentX = e.clientX;
        this.selectionState.currentY = e.clientY;

        // Create selection box
        const selectionBox = document.createElement('div');
        selectionBox.className = 'quick-edit-selection-box';
        selectionBox.style.left = e.clientX + 'px';
        selectionBox.style.top = e.clientY + 'px';
        selectionBox.style.width = '0px';
        selectionBox.style.height = '0px';
        
        document.body.appendChild(selectionBox);
        this.selectionState.selectionBox = selectionBox;

        // Clear previous selections
        this.clearSelections();
    }

    static updateSelection(e) {
        if (!this.selectionState.selectionBox || !this.selectionState.isDragThresholdMet) return;

        this.selectionState.currentX = e.clientX;
        this.selectionState.currentY = e.clientY;

        const left = Math.min(this.selectionState.startX, this.selectionState.currentX);
        const top = Math.min(this.selectionState.startY, this.selectionState.currentY);
        const width = Math.abs(this.selectionState.currentX - this.selectionState.startX);
        const height = Math.abs(this.selectionState.currentY - this.selectionState.startY);

        this.selectionState.selectionBox.style.left = left + 'px';
        this.selectionState.selectionBox.style.top = top + 'px';
        this.selectionState.selectionBox.style.width = width + 'px';
        this.selectionState.selectionBox.style.height = height + 'px';

        // Update selected elements
        this.updateSelectedElements(left, top, width, height);
    }

    static updateSelectedElements(left, top, width, height) {
        // Clear previous selections
        this.clearSelections();

        // Get all visible elements on the page, excluding our own UI
        const allElements = document.querySelectorAll('*:not(#quick-edit-notice):not(.quick-edit-selection-box):not(#quick-edit-animation-style):not(script):not(style):not(meta):not(link):not(title)');
        
        allElements.forEach(element => {
            // Skip elements that are not visible
            const rect = element.getBoundingClientRect();
            if (rect.width === 0 || rect.height === 0) return;
            
            // Check if element is within selection box
            if (this.isElementInSelection(rect, left, top, width, height)) {
                this.selectionState.selectedElements.add(element);
                element.classList.add('quick-edit-selected');
            }
        });
    }

    static isElementInSelection(elementRect, selectionLeft, selectionTop, selectionWidth, selectionHeight) {
        const selectionRight = selectionLeft + selectionWidth;
        const selectionBottom = selectionTop + selectionHeight;
        
        // Calculate the overlap area
        const overlapLeft = Math.max(elementRect.left, selectionLeft);
        const overlapRight = Math.min(elementRect.right, selectionRight);
        const overlapTop = Math.max(elementRect.top, selectionTop);
        const overlapBottom = Math.min(elementRect.bottom, selectionBottom);
        
        // Check if there's any overlap
        if (overlapLeft >= overlapRight || overlapTop >= overlapBottom) {
            return false;
        }
        
        // Calculate overlap area and element area
        const overlapArea = (overlapRight - overlapLeft) * (overlapBottom - overlapTop);
        const elementArea = (elementRect.right - elementRect.left) * (elementRect.bottom - elementRect.top);
        
        // More precise selection - require 70% overlap or complete containment for better precision
        const overlapPercentage = overlapArea / elementArea;
        const isCompletelyContained = elementRect.left >= selectionLeft && 
                                    elementRect.right <= selectionRight && 
                                    elementRect.top >= selectionTop && 
                                    elementRect.bottom <= selectionBottom;
        
        return isCompletelyContained || overlapPercentage >= 0.7;
    }

    static endSelection() {
        this.selectionState.isSelecting = false;
        this.selectionState.isDragThresholdMet = false;
        
        // Remove selection box
        if (this.selectionState.selectionBox) {
            this.selectionState.selectionBox.remove();
            this.selectionState.selectionBox = null;
        }

        // Reset cursor if shift is not pressed
        if (!this.selectionState.isShiftPressed) {
            document.body.style.cursor = '';
        }
    }

    static clearSelections() {
        this.selectionState.selectedElements.forEach(element => {
            if (element && element.classList) {
                element.classList.remove('quick-edit-selected');
            }
        });
        this.selectionState.selectedElements.clear();
    }

    static deleteSelectedElements() {
        if (this.selectionState.selectedElements.size === 0) return;

        const elementsToDelete = Array.from(this.selectionState.selectedElements);
        let deletedCount = 0;
        
        // Filter out critical elements that shouldn't be deleted
        const safeToDelete = elementsToDelete.filter(element => {
            if (!element || !element.tagName) return false;
            
            const tagName = element.tagName.toLowerCase();
            const hasImportantId = element.id && (
                element.id.includes('quick-edit') || 
                element.id === 'root' || 
                element.id === 'app' ||
                element.id === 'main' ||
                element.id.includes('header') ||
                element.id.includes('nav')
            );
            
            const isImportantTag = ['body', 'html', 'head', 'script', 'style', 'meta', 'link', 'title'].includes(tagName);
            const hasImportantClass = element.className && typeof element.className === 'string' && 
                (element.className.includes('header') || element.className.includes('nav') || element.className.includes('main'));
            
            return !hasImportantId && !isImportantTag && !hasImportantClass;
        });

        // Delete elements safely
        safeToDelete.forEach(element => {
            try {
                if (element && element.parentNode) {
                    element.remove();
                    deletedCount++;
                }
            } catch (error) {
                console.warn('Could not delete element:', element, error);
            }
        });

        // Clear selections
        this.clearSelections();

        console.log(`Deleted ${deletedCount} elements out of ${elementsToDelete.length} selected`);
        
        // Show brief feedback
        this.showDeletionFeedback(deletedCount);
    }

    static showDeletionFeedback(count) {
        const notice = document.querySelector('#quick-edit-notice');
        if (notice) {
            const originalHTML = notice.innerHTML;
            notice.innerHTML = `🗑️ Deleted ${count} elements`;
            notice.style.background = 'linear-gradient(135deg, #10b981, #34d399)';
            
            setTimeout(() => {
                notice.innerHTML = originalHTML;
                notice.style.background = 'linear-gradient(135deg, #7c3aed, #8b5cf6)';
            }, 2000);
        }
    }

    static disableEditMode() {
        document.designMode = 'off';
        this.cleanup();
    }

    static removeEventListeners() {
        if (window.quickEditListeners) {
            document.removeEventListener('keydown', window.quickEditListeners.keydown);
            document.removeEventListener('keyup', window.quickEditListeners.keyup);
            document.removeEventListener('mousedown', window.quickEditListeners.mousedown, { capture: true });
            document.removeEventListener('mousemove', window.quickEditListeners.mousemove, { capture: true });
            document.removeEventListener('mouseup', window.quickEditListeners.mouseup, { capture: true });
            delete window.quickEditListeners;
        }
    }

    static cleanup() {
        // Remove notices and styles
        const elementsToRemove = document.querySelectorAll('#quick-edit-notice, .quick-edit-selection-box, #quick-edit-animation-style');
        elementsToRemove.forEach(el => el.remove());

        // Clear selections
        this.clearSelections();

        // Reset cursor
        document.body.style.cursor = '';

        // Remove event listeners
        this.removeEventListeners();

        // Reset selection state
        this.selectionState = {
            isSelecting: false,
            startX: 0,
            startY: 0,
            currentX: 0,
            currentY: 0,
            selectionBox: null,
            selectedElements: new Set(),
            isShiftPressed: false,
            isDragThresholdMet: false
        };
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                // Turn off edit mode
                document.designMode = 'off';
                this.cleanup();
                
                console.log('Quick Edit mode disabled and cleanup completed');
                resolve({ action: 'reset', message: 'Quick Edit mode reset' });
            } catch (error) {
                console.error('QuickEditAction reset error:', error);
                resolve({ action: 'error', message: 'Failed to reset edit mode: ' + error.message });
            }
        });
    }
}

// Make sure the class is available globally for context menu execution
window.QuickEditAction = QuickEditAction;

// Export for use in other modules if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QuickEditAction;
} 