// Location Quick Set - Context Menu Location Setter
// This script handles setting location data from context menu favorite selections

class LocationQuickSet {
    static setLocation(favorite) {
        try {
            console.log('LocationQuickSet: Setting location to', favorite.name);
            
            // Comprehensive validation
            if (!favorite) {
                throw new Error('No favorite data provided');
            }
            
            if (!favorite.name) {
                throw new Error('Favorite name is missing');
            }
            
            if (!favorite.data) {
                throw new Error('Favorite location data is missing');
            }

            const locationData = favorite.data;
            
            // Validate that we have at least some location data
            if (!locationData.place && !locationData.latitude && !locationData.longitude) {
                throw new Error('No valid location data found');
            }
            
            // Set the location data using the same logic as LocationFavorites
            LocationQuickSet.setLocationData(locationData);
            
            // Show success notification
            LocationQuickSet.showNotification(`Location set to "${favorite.name}"`, 'success');
            
            return { success: true, message: `Location set to ${favorite.name}` };
            
        } catch (error) {
            console.error('LocationQuickSet: Error setting location:', error);
            const errorMessage = error.message || 'Unknown error occurred';
            LocationQuickSet.showNotification(`Error: ${errorMessage}`, 'error');
            return { success: false, error: errorMessage };
        }
    }

    static setLocationData(data) {
        const fieldMappings = {
            place: 'place',
            hl: 'hl', 
            gl: 'gl',
            latitude: 'latitude',
            longitude: 'longitude',
            enabled: 'enabled'
        };
        
        // Update form fields if they exist (for popup integration)
        Object.entries(fieldMappings).forEach(([dataKey, elementId]) => {
            const element = document.getElementById(elementId);
            if (element && data[dataKey] !== undefined) {
                if (element.type === 'checkbox') {
                    element.checked = data[dataKey];
                } else {
                    element.value = data[dataKey];
                }
                // Trigger change event to update any dependencies
                element.dispatchEvent(new Event('change', { bubbles: true }));
                element.dispatchEvent(new Event('input', { bubbles: true }));
            }
        });

        // Update background settings and save to storage
        LocationQuickSet.updateBackgroundSettings(data);
    }

    static updateBackgroundSettings(data) {
        try {
            // Get or create background settings object
            if (typeof background === 'undefined') {
                window.background = { settings: {} };
            }
            if (!background.settings) {
                background.settings = {};
            }

            // Update background settings object with proper validation
            if (data.latitude) {
                const lat = parseFloat(data.latitude);
                if (!isNaN(lat) && lat >= -90 && lat <= 90) {
                    background.settings.latitude = lat;
                }
            }
            if (data.longitude) {
                const lng = parseFloat(data.longitude);
                if (!isNaN(lng) && lng >= -180 && lng <= 180) {
                    background.settings.longitude = lng;
                }
            }
            if (data.place && typeof data.place === 'string') {
                background.settings.location = data.place;
                background.settings.name = data.place;
            }
            if (data.hl && typeof data.hl === 'string') background.settings.hl = data.hl;
            if (data.gl && typeof data.gl === 'string') background.settings.gl = data.gl;
            if (data.enabled !== undefined) background.settings.enabled = Boolean(data.enabled);
            
            // Add timestamp
            background.settings.timestamp = new Date().getTime();
            
            // Save to chrome storage with error handling
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
                chrome.storage.local.set({settings: background.settings}).catch(error => {
                    console.error('LocationQuickSet: Error saving to chrome storage:', error);
                });
            }

            // Update placeholders if jQuery is available
            LocationQuickSet.updatePlaceholders();

            // Trigger the enabler function if it exists to update UI state
            if (typeof enabler === 'function') {
                try {
                    enabler();
                } catch (error) {
                    console.error('LocationQuickSet: Error calling enabler function:', error);
                }
            }

            // Notify popup of location change if it's open
            LocationQuickSet.notifyPopupLocationChange(data);
            
        } catch (error) {
            console.error('LocationQuickSet: Error updating background settings:', error);
            throw error;
        }
    }

    static updatePlaceholders() {
        // Update placeholders using jQuery if available
        if (typeof $ !== 'undefined') {
            const placeElement = $('#place');
            const latElement = $('#latitude');
            const lngElement = $('#longitude');
            const hlElement = $('#hl');
            const glElement = $('#gl');

            if (background && background.settings) {
                if (background.settings.location) placeElement.prop('placeholder', background.settings.location);
                if (background.settings.latitude) latElement.prop('placeholder', background.settings.latitude);
                if (background.settings.longitude) lngElement.prop('placeholder', background.settings.longitude);
                if (background.settings.hl) hlElement.prop('placeholder', background.settings.hl);
                if (background.settings.gl) glElement.prop('placeholder', background.settings.gl);
            }
        }
    }

    static notifyPopupLocationChange(data) {
        // Send message to popup if it's open
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.sendMessage({
                type: 'locationChanged',
                data: data
            }).catch(() => {
                // Popup might not be open - this is fine
            });
        }
    }

    static showNotification(message, type = 'info') {
        // Create notification popup following universal popup standards
        const notification = document.createElement('div');
        notification.className = 'location-quick-set-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #0a0a0a;
            border: 2px solid #7C3AED;
            border-radius: 12px;
            color: #d1d5db;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto";
            font-size: 14px;
            padding: 12px 16px;
            z-index: 10000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
            max-width: 300px;
            word-wrap: break-word;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        // Set color based on type
        if (type === 'success') {
            notification.style.borderColor = '#10b981';
            notification.style.color = '#10b981';
        } else if (type === 'error') {
            notification.style.borderColor = '#ef4444';
            notification.style.color = '#ef4444';
        }

        notification.textContent = message;
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Reset function for DOM cleanup compliance
    static reset() {
        // Remove any notification elements
        const notifications = document.querySelectorAll('.location-quick-set-notification');
        notifications.forEach(notification => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
        
        console.log('LocationQuickSet: Reset completed');
    }
}

// Add to global DOM cleanup if the system exists
if (typeof window.quickActionsCleanupFunctions !== 'undefined') {
    window.quickActionsCleanupFunctions.push(() => LocationQuickSet.reset());
}

// Handle ESC key for notification dismissal
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        const notifications = document.querySelectorAll('.location-quick-set-notification');
        notifications.forEach(notification => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        });
    }
});

console.log('LocationQuickSet: Script loaded');