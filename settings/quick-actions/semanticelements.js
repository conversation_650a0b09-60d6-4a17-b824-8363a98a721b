// Semantic Elements Quick Action - Highlights semantic HTML elements with labels and summary display
// Enhanced version with dark UI summary panel, dragability, and proper cleanup

class SemanticElementsAction {
    constructor() {
        this.name = 'Semantic Elements';
        this.description = 'Highlights semantic HTML elements and shows labels with summary display';
        this.labelPrefix = 'semantic-elements-label-';
        this.highlightClass = 'semantic-elements-highlight';
        this.styleId = 'semantic-elements-styles';
        this.panelId = 'semantic-elements-summary-panel';
    }

    // Execute the semantic elements highlighting with labels and summary display
    execute() {
        return new Promise((resolve, reject) => {
            try {
                console.log('SemanticElementsAction: Starting execution');
                
                // Remove existing panel if present
                document.querySelectorAll(`#${this.panelId}`).forEach(panel => panel.remove());
                
                // Define semantic elements to highlight
                const semanticElements = document.querySelectorAll('article, aside, details, figcaption, figure, footer, header, main, mark, nav, section, summary, time');
                
                if (semanticElements.length === 0) {
                    console.log('SemanticElementsAction: No semantic HTML elements found');
                    this.showSummaryPanel([], {});
                    resolve({ success: true, message: 'No semantic elements found' });
                    return;
                }

                // Add styles for highlighting and labels
                this.addStyles();
                
                // Initialize tooltips system
                if (typeof SemanticTooltips !== 'undefined') {
                    SemanticTooltips.init();
                }
                
                let elementCount = 0;
                const elementCounts = {};
                
                semanticElements.forEach((element, index) => {
                    const tagName = element.tagName.toLowerCase();
                    
                    // Count elements by type
                    elementCounts[tagName] = (elementCounts[tagName] || 0) + 1;
                    
                    // Add highlight class with proper naming convention
                    element.classList.add(this.highlightClass);
                    element.setAttribute('data-semantic-elements-highlighted', 'true');
                    element.style.border = '2px dotted green';
                    element.style.position = 'relative';
                    
                    // Create label element with proper data attributes
                    const label = document.createElement('div');
                    label.className = `${this.labelPrefix}${index}`;
                    label.setAttribute('data-semantic-elements-label', 'true');
                    label.setAttribute('data-semantic-elements-element', 'true');
                    label.textContent = tagName;
                    
                    // Style the label
                    label.style.cssText = `
                        position: absolute;
                        top: -2px;
                        left: -2px;
                        background: green;
                        color: white;
                        padding: 2px 6px;
                        font-size: 10px;
                        font-weight: bold;
                        font-family: Arial, sans-serif;
                        line-height: 1;
                        z-index: 10000;
                        border-radius: 0 0 4px 0;
                        pointer-events: none;
                        text-transform: uppercase;
                    `;
                    
                    // Insert label at the beginning of the element
                    element.insertBefore(label, element.firstChild);
                    elementCount++;
                });

                // Show summary panel with element counts
                this.showSummaryPanel(semanticElements, elementCounts);

                console.log(`SemanticElementsAction: Highlighted ${elementCount} semantic elements`);
                resolve({ success: true, message: `${elementCount} semantic elements highlighted` });
                
            } catch (error) {
                console.error('SemanticElementsAction: Error executing highlighting:', error);
                reject(error);
            }
        });
    }

    // Show summary panel with dark UI theme and dragability
    showSummaryPanel(elements, elementCounts) {
        // Create panel
        const panel = document.createElement('div');
        panel.id = this.panelId;
        panel.className = 'semantic-elements-summary-panel';
        panel.setAttribute('data-semantic-elements-element', 'true');
        
        // Panel styling with dark theme and resize functionality
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 420px;
            max-height: 85vh;
            min-width: 350px;
            min-height: 400px;
            background: #0f0f0f;
            color: #e5e5e5;
            border: 1px solid #2a2a2a;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
            z-index: 9999999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            overflow: hidden;
            backdrop-filter: blur(10px);
            resize: both;
        `;

        const totalElements = elements.length;
        
        // Define all semantic HTML elements
        const allSemanticElements = [
            'article', 'aside', 'details', 'figcaption', 'figure', 'footer',
            'header', 'main', 'mark', 'nav', 'section', 'summary', 'time'
        ];
        
        const foundElements = Object.keys(elementCounts);
        const elementTypes = foundElements.length;

        // Create HTML content
        let html = `
            <div id="semantic-elements-header" style="display:flex;justify-content:space-between;align-items:center;padding:16px 20px;background:#1a1a1a;border-bottom:1px solid #2a2a2a;cursor:move;">
                <h2 style="margin:0;color:#ffffff;font-size:16px;font-weight:600;"><span style="color: #7C3AED; font-size: 16px;">●</span> Semantic Elements</h2>
                <button onclick="this.closest('#${this.panelId}').remove()" style="background:#262626;border:none;border-radius:50%;width:30px;height:30px;display:flex;align-items:center;justify-content:center;cursor:pointer;font-size:12px;color:#ffffff;font-weight:bold;transition:all 0.2s ease;">✕</button>
            </div>
            
            <div style="padding:16px;max-height:calc(85vh - 80px);overflow-y:auto;">
        `;

        // Summary stats
        html += `
            <div style="display:grid;grid-template-columns:1fr 1fr;gap:12px;margin-bottom:20px;padding:16px;background:#262626;border-radius:8px;border:1px solid #333333;">
                <div style="display:flex;flex-direction:column;align-items:center;padding:8px 4px;">
                    <div style="font-size:11px;color:#9ca3af;margin-bottom:4px;text-transform:uppercase;font-weight:600;letter-spacing:0.5px;">Total Elements</div>
                    <div style="font-size:18px;font-weight:700;color:#22c55e;">${totalElements}</div>
                </div>
                <div style="display:flex;flex-direction:column;align-items:center;padding:8px 4px;">
                    <div style="font-size:11px;color:#9ca3af;margin-bottom:4px;text-transform:uppercase;font-weight:600;letter-spacing:0.5px;">Element Types</div>
                    <div style="font-size:18px;font-weight:700;color:#60a5fa;">${elementTypes}/13</div>
                </div>
            </div>
        `;

        // Element breakdown (only show if elements exist)
        if (totalElements > 0) {
            html += `
                <div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:20px;overflow:hidden;">
                    <div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Found Elements Details</div>
                    <div style="padding:0;">
            `;

            // Sort elements by count (highest first)
            const sortedElements = Object.entries(elementCounts).sort(([,a], [,b]) => b - a);
            
            sortedElements.forEach(([tagName, count]) => {
                const percentage = ((count / totalElements) * 100).toFixed(1);
                html += `
                    <div style="display:flex;justify-content:space-between;align-items:center;padding:12px 16px;border-bottom:1px solid #2a2a2a;">
                        <div style="display:flex;align-items:center;gap:8px;">
                            <code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">&lt;${tagName}&gt;</code>
                        </div>
                        <div style="display:flex;align-items:center;gap:8px;">
                            <span style="color:#9ca3af;font-size:12px;">${percentage}%</span>
                            <span style="background:#22c55e;color:#ffffff;padding:2px 6px;border-radius:4px;font-size:11px;font-weight:600;">${count}</span>
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        }

        // Semantic Elements Grid
        html += `
            <div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:20px;overflow:hidden;">
                <div style="background:#1a1a1a;padding:12px 16px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">HTML Semantic Elements</div>
                <div style="padding:16px;">
                    <div style="display:grid;grid-template-columns:repeat(3, 1fr);gap:12px;">
        `;

        // Create grid items for each semantic element
        allSemanticElements.forEach((tagName) => {
            const isFound = foundElements.includes(tagName);
            const count = elementCounts[tagName] || 0;
            const iconColor = isFound ? '#22c55e' : '#ef4444';
            const icon = isFound ? '✓' : '✗';
            const borderColor = isFound ? '#22c55e' : '#2a2a2a';
            
            html += `
                <div class="semantic-element-card" data-element="${tagName}" style="display:flex;flex-direction:column;align-items:center;padding:12px 8px;background:#1a1a1a;border:1px solid ${borderColor};border-radius:6px;text-align:center;position:relative;cursor:pointer;transition:all 0.2s ease;" title="Click for more information about &lt;${tagName}&gt;">
                    <code style="background:#1f1f1f;padding:4px 6px;border-radius:3px;font-family:monospace;font-size:11px;color:#d1d5db;margin-bottom:8px;">&lt;${tagName}&gt;</code>
                    <div style="font-size:20px;color:${iconColor};font-weight:bold;margin-bottom:4px;">${icon}</div>
                    ${isFound ? `<div style="font-size:10px;color:#9ca3af;">${count} found</div>` : '<div style="font-size:10px;color:#6b7280;">not found</div>'}
                </div>
            `;
        });

        html += `
                    </div>
                </div>
            </div>
        `;

        // Instructions
        html += `
            <div style="background:#1a1a1a;border-radius:6px;border:1px solid #2a2a2a;padding:12px;text-align:center;color:#6b7280;font-size:12px;">
                <div style="margin-bottom:4px;">✨ Found elements are highlighted with green borders and labels</div>
                <div style="margin-bottom:4px;">💡 Click on any element card above to see detailed usage guidance</div>
                <div>Press <kbd style="background:#333;padding:2px 4px;border-radius:2px;font-size:11px;">ESC</kbd> to close this panel and remove highlighting</div>
            </div>
        `;

        html += `
            </div>
        `;

        panel.innerHTML = html;
        document.body.appendChild(panel);

        // Add drag functionality
        this.addDragFunctionality(panel);

        // Setup click info system
        this.setupClickInfo(panel);

        // Handle Escape key to reset highlighting
        this.setupEscapeHandler();
    }

    // Add drag functionality to the panel
    addDragFunctionality(panel) {
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        const header = panel.querySelector('#semantic-elements-header');
        if (!header) return;

        header.addEventListener('mousedown', (e) => {
            if (e.target.tagName === 'BUTTON') return; // Don't drag when clicking close button
            
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
                header.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                const maxX = window.innerWidth - panel.offsetWidth;
                const maxY = window.innerHeight - panel.offsetHeight;
                
                const boundedX = Math.max(0, Math.min(currentX, maxX));
                const boundedY = Math.max(0, Math.min(currentY, maxY));

                panel.style.transform = `translate(${boundedX}px, ${boundedY}px)`;
                panel.style.top = '0px';
                panel.style.right = 'auto';
                panel.style.left = '0px';
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                header.style.cursor = 'move';
            }
        });
    }

    // Setup escape key handler
    setupEscapeHandler() {
        // Remove any existing escape handlers
        if (window.semanticElementsEscapeListener) {
            document.removeEventListener('keydown', window.semanticElementsEscapeListener);
        }

        // Create new escape handler
        window.semanticElementsEscapeListener = (e) => {
            if (e.key === 'Escape') {
                console.log('SemanticElementsAction: Escape key pressed, resetting');
                this.resetFromKeyboard();
                document.removeEventListener('keydown', window.semanticElementsEscapeListener);
                delete window.semanticElementsEscapeListener;
            }
        };

        document.addEventListener('keydown', window.semanticElementsEscapeListener);
    }

    // Setup click info system
    setupClickInfo(panel) {
        if (typeof SemanticTooltips === 'undefined') return;

        const cards = panel.querySelectorAll('.semantic-element-card');
        
        cards.forEach(card => {
            // Add hover effect
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'scale(1.02)';
                card.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'scale(1)';
                card.style.boxShadow = 'none';
            });

            // Add click handler to show info box
            card.addEventListener('click', (e) => {
                e.stopPropagation();
                const elementName = card.dataset.element;
                this.showElementInfo(elementName);
            });
        });
    }

    // Show centralized info box for an element
    showElementInfo(elementName) {
        // Remove existing info box if present
        const existingBox = document.getElementById('semantic-element-info-box');
        if (existingBox) {
            existingBox.remove();
        }

        const tooltipData = SemanticTooltips.getTooltip(elementName);
        if (!tooltipData) return;

        // Get the semantic panel position for relative positioning
        const semanticPanel = document.getElementById('semantic-elements-summary-panel');
        if (!semanticPanel) return;

        const panelRect = semanticPanel.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Calculate position relative to the semantic panel
        let left, top;
        const infoBoxWidth = 400;
        const infoBoxHeight = 350; // approximate height

        // Try to position to the right of the panel first
        if (panelRect.right + infoBoxWidth + 20 <= viewportWidth) {
            // Position to the right
            left = panelRect.right + 20;
            top = panelRect.top;
        } else if (panelRect.left - infoBoxWidth - 20 >= 0) {
            // Position to the left
            left = panelRect.left - infoBoxWidth - 20;
            top = panelRect.top;
        } else {
            // Position below the panel
            left = Math.max(20, Math.min(viewportWidth - infoBoxWidth - 20, panelRect.left));
            top = panelRect.bottom + 20;
        }

        // Ensure the info box stays within viewport bounds
        left = Math.max(20, Math.min(viewportWidth - infoBoxWidth - 20, left));
        top = Math.max(20, Math.min(viewportHeight - infoBoxHeight - 20, top));

        const infoBox = document.createElement('div');
        infoBox.id = 'semantic-element-info-box';
        infoBox.setAttribute('data-semantic-elements-element', 'true');
        infoBox.style.cssText = `
            position: fixed;
            left: ${left}px;
            top: ${top}px;
            background: #000000;
            color: #ffffff;
            border: 1px solid #333333;
            border-radius: 12px;
            padding: 24px;
            width: 400px;
            max-width: calc(100vw - 40px);
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            z-index: 1000000;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            animation: fadeInScale 0.2s ease-out;
        `;

                 // Escape HTML in tooltip data to prevent issues with < and > characters
         const escapeHtml = (text) => {
             const div = document.createElement('div');
             div.textContent = text;
             return div.innerHTML;
         };

         infoBox.innerHTML = `
             <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                 <h3 style="margin: 0; color: #22c55e; font-size: 18px; font-weight: 600;">
                     ${escapeHtml(tooltipData.title)}
                 </h3>
                 <button id="close-info-box" style="
                     background: none;
                     border: none;
                     color: #9ca3af;
                     font-size: 24px;
                     cursor: pointer;
                     padding: 0;
                     width: 30px;
                     height: 30px;
                     display: flex;
                     align-items: center;
                     justify-content: center;
                     border-radius: 50%;
                     transition: all 0.2s ease;
                 " onmouseover="this.style.background='#333333'; this.style.color='#ffffff';" onmouseout="this.style.background='none'; this.style.color='#9ca3af';">×</button>
             </div>
             
             <div style="margin-bottom: 16px;">
                 <code style="background: #1f1f1f; padding: 6px 10px; border-radius: 4px; font-family: monospace; font-size: 14px; color: #22c55e;">&lt;${elementName}&gt;</code>
             </div>
             
             <div style="margin-bottom: 16px; color: #e5e5e5; font-size: 14px; line-height: 1.5;">
                 ${escapeHtml(tooltipData.description)}
             </div>
             
             <div style="margin-bottom: 16px;">
                 <div style="color: #22c55e; font-weight: 600; margin-bottom: 6px; font-size: 13px;">Usage Examples:</div>
                 <div style="color: #d1d5db; font-size: 13px; line-height: 1.4;">
                     ${escapeHtml(tooltipData.usage)}
                 </div>
             </div>
             
             <div style="margin-bottom: 16px;">
                 <div style="color: #22c55e; font-weight: 600; margin-bottom: 6px; font-size: 13px;">Typical Location:</div>
                 <div style="color: #d1d5db; font-size: 13px; line-height: 1.4; word-wrap: break-word;">
                     ${escapeHtml(tooltipData.location)}
                 </div>
             </div>
             
             <div style="margin-bottom: 0;">
                 <div style="color: #ef4444; font-weight: 600; margin-bottom: 6px; font-size: 13px;">Common Mistakes to Avoid:</div>
                 <div style="color: #fca5a5; font-size: 13px; line-height: 1.4; word-wrap: break-word;">
                     ${escapeHtml(tooltipData.misuse)}
                 </div>
             </div>
         `;

        // Add animation keyframes
        if (!document.getElementById('semantic-info-animations')) {
            const animationStyle = document.createElement('style');
            animationStyle.id = 'semantic-info-animations';
            animationStyle.textContent = `
                @keyframes fadeInScale {
                    from {
                        opacity: 0;
                        transform: translate(-50%, -50%) scale(0.9);
                    }
                    to {
                        opacity: 1;
                        transform: translate(-50%, -50%) scale(1);
                    }
                }
            `;
            document.head.appendChild(animationStyle);
        }

        document.body.appendChild(infoBox);

        // Close button handler
        const closeBtn = infoBox.querySelector('#close-info-box');
        closeBtn.addEventListener('click', () => {
            infoBox.remove();
        });

        // Close on outside click
        setTimeout(() => {
            document.addEventListener('click', function closeOnOutside(e) {
                if (!infoBox.contains(e.target)) {
                    infoBox.remove();
                    document.removeEventListener('click', closeOnOutside);
                }
            });
        }, 100);

        // Close on Escape key
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                infoBox.remove();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
    }

    // Add CSS styles for highlighting
    addStyles() {
        // Remove existing styles if they exist
        const existingStyle = document.getElementById(this.styleId);
        if (existingStyle) {
            existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = this.styleId;
        style.setAttribute('data-semantic-elements-element', 'true');
        style.textContent = `
            .${this.highlightClass} {
                position: relative !important;
            }
            [data-semantic-elements-label] {
                user-select: none !important;
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
            }
            .semantic-elements-summary-panel::-webkit-scrollbar {
                width: 6px;
            }
            .semantic-elements-summary-panel::-webkit-scrollbar-track {
                background: #262626;
                border-radius: 3px;
            }
            .semantic-elements-summary-panel::-webkit-scrollbar-thumb {
                background: #404040;
                border-radius: 3px;
            }
            .semantic-elements-summary-panel kbd {
                background: #333333;
                color: #e5e5e5;
                padding: 2px 4px;
                border-radius: 2px;
                font-size: 11px;
                font-family: monospace;
            }
            .semantic-elements-summary-panel {
                overflow: auto;
            }
            .semantic-elements-summary-panel .semantic-element-card {
                position: relative;
            }
        `;
        document.head.appendChild(style);
    }

    // Reset from keyboard shortcut
    resetFromKeyboard() {
        try {
            this.reset().then(() => {
                console.log('SemanticElementsAction: Reset via keyboard completed');
            }).catch((error) => {
                console.error('SemanticElementsAction: Error resetting via keyboard:', error);
            });
        } catch (error) {
            console.error('SemanticElementsAction: Error in keyboard reset:', error);
        }
    }

    // Reset the semantic elements highlighting (remove all modifications)
    reset() {
        return new Promise((resolve, reject) => {
            try {
                console.log('SemanticElementsAction: Starting reset');
                
                // Remove summary panel
                document.querySelectorAll(`#${this.panelId}`).forEach(panel => panel.remove());
                
                // Remove highlight class and restore original styles
                const highlightedElements = document.querySelectorAll(`.${this.highlightClass}, [data-semantic-elements-highlighted]`);
                highlightedElements.forEach(element => {
                    element.classList.remove(this.highlightClass);
                    element.removeAttribute('data-semantic-elements-highlighted');
                    element.style.border = '';
                    element.style.position = '';
                });

                // Remove all labels using comprehensive selectors
                const labels = document.querySelectorAll(`
                    [data-semantic-elements-label],
                    [data-semantic-elements-element],
                    [class*="${this.labelPrefix}"]
                `);
                labels.forEach(label => {
                    label.remove();
                });

                // Remove styles
                const existingStyle = document.getElementById(this.styleId);
                if (existingStyle) {
                    existingStyle.remove();
                }

                // Remove any additional elements that might have been created
                const allSemanticElementsElements = document.querySelectorAll('[data-semantic-elements-element]');
                allSemanticElementsElements.forEach(element => {
                    element.remove();
                });

                // Clean up escape listener
                if (window.semanticElementsEscapeListener) {
                    document.removeEventListener('keydown', window.semanticElementsEscapeListener);
                    delete window.semanticElementsEscapeListener;
                }

                // Cleanup tooltips system
                if (typeof SemanticTooltips !== 'undefined') {
                    SemanticTooltips.cleanup();
                }

                console.log('SemanticElementsAction: Reset completed successfully');
                resolve({ success: true, message: 'Semantic elements highlighting reset' });
                
            } catch (error) {
                console.error('SemanticElementsAction: Error resetting:', error);
                reject(error);
            }
        });
    }

    // Static execute method for global access (used by context menu)
    static execute() {
        console.log('SemanticElementsAction: Static execute called');
        const action = new SemanticElementsAction();
        return action.execute();
    }

    // Static reset method for global access (used by reset utilities)
    static reset() {
        console.log('SemanticElementsAction: Static reset called');
        const action = new SemanticElementsAction();
        return action.reset();
    }
}

// Make available globally for context menu execution
if (typeof window !== 'undefined') {
    window.SemanticElementsAction = SemanticElementsAction;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SemanticElementsAction;
} 