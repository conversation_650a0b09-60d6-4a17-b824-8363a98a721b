// Heading Structure Quick Action - Analyzes and displays the page's heading structure (H1-H6) with statistics, visual hierarchy, and interactive navigation

class HeadingStructureAction {
    constructor() {
        this.name = 'Heading Structure';
        this.description = 'Analyzes and displays the page\'s heading structure (H1-H6) with statistics, visual hierarchy, and interactive navigation in a new window';
    }

    // Execute the heading structure analysis
    execute() {
        return new Promise((resolve, reject) => {
            chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                const currentTab = tabs[0];
                
                try {
                    chrome.scripting.executeScript({
                        target: {tabId: currentTab.id},
                        func: function() {
                            // Remove existing heading structure panel if present
                            document.querySelectorAll('.heading-structure-panel').forEach(panel => panel.remove());
                            
                            // Helper functions
                            function escape(str) {
                                if (!str) return '';
                                return str.replace(/&/g, '&amp;')
                                         .replace(/</g, '&lt;')
                                         .replace(/>/g, '&gt;')
                                         .replace(/"/g, '&quot;')
                                         .replace(/'/g, '&#39;');
                            }
                            
                            function hlCode(str) {
                                return '<code style="background:#1f1f1f;padding:4px 8px;border-radius:4px;font-family:monospace;font-size:12px;color:#d1d5db;">' + str + '</code>';
                            }
                            
                            function createRow(title, info, content, clickAction) {
                                return `
                                    <div style="display:flex;padding:16px 0;border-bottom:1px solid #2a2a2a;${clickAction ? 'cursor:pointer;' : ''}" ${clickAction ? `onclick="${clickAction}"` : ''} onmouseover="this.style.backgroundColor='#1a1a1a'" onmouseout="this.style.backgroundColor=''">
                                        <div style="min-width:140px;font-weight:500;color:#9ca3af;font-size:13px;text-transform:uppercase;margin-right:20px;">${title}</div>
                                        <div style="flex:1;">
                                            ${info ? `<div style="font-size:12px;color:#6b7280;margin-bottom:8px;">${info}</div>` : ''}
                                            <div style="color:#d1d5db;line-height:1.5;">${content}</div>
                                        </div>
                                    </div>
                                `;
                            }
                            
                            function createHeadingRow(content, headerTarget, index) {
                                return `
                                    <div class="heading-row" data-header-index="${index}" style="display:flex;padding:16px 0;border-bottom:1px solid #2a2a2a;cursor:pointer;" onmouseover="this.style.backgroundColor='#1a1a1a'" onmouseout="this.style.backgroundColor=''">
                                        <div style="flex:1;">
                                            <div style="color:#d1d5db;line-height:1.5;">${content}</div>
                                        </div>
                                    </div>
                                `;
                            }
                            const headers = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
                            const headerLevelsCount = [];

                            // Calculate visible text content for ratio
                            const cleanTextContent = Array.from(document.body.querySelectorAll("*"))
                            .filter(el => el.offsetParent !== null && getComputedStyle(el).visibility !== "hidden" && getComputedStyle(el).opacity !== "0")
                            .filter((el, _, allElements) => !allElements.some(parent => parent !== el && parent.contains(el)))
                            .map(el => (el.innerText || "").trim())
                            .filter(Boolean)
                            .join(" ");

                            const headerData = Array.from(headers).map(header => {
                                const text = header.textContent.trim();
                                const isHidden = window.getComputedStyle(header).display === 'none' || header.offsetParent === null;
                                const level = parseInt(header.tagName.slice(1), 10);
                                
                                const levelKey = `H${level}`;
                                const found = headerLevelsCount.find(item => item[0] === levelKey);
                                if (found) {
                                    found[1]++;
                                } else {
                                    headerLevelsCount.push([levelKey, 1]);
                                }
                                
                                return {
                                    target: header,
                                    level: level,
                                    textLength: text.length,
                                    wordCount: text.split(/\s+/).length,
                                    text: text,
                                    hidden: isHidden
                                };
                            });

                            headerLevelsCount.sort((a, b) => parseInt(a[0].slice(1), 10) - parseInt(b[0].slice(1), 10));

                            const statistics = {
                                totalHeaders: headerData.length,
                                shortestWords: headerData.length ? Math.min(...headerData.map(h => h.wordCount)) : 0,
                                longestWords: headerData.length ? Math.max(...headerData.map(h => h.wordCount)) : 0,
                                averageWords: headerData.length ? (headerData.reduce((sum, h) => sum + h.wordCount, 0) / headerData.length).toFixed(1) : 0,
                                totalCharacters: headerData.reduce((sum, h) => sum + h.textLength, 0)
                            };

                            statistics.hRatio = (100 * statistics.totalCharacters / cleanTextContent.length).toFixed(1);

                            // Create copy text
                            const copyText = [];
                            headerData.forEach(header => {
                                const spaces = '\t'.repeat(header.level-1);
                                copyText.push(`${spaces}<H${header.level}> - ${header.text}`);
                            });

                            // Get saved position and size from localStorage
                            function getSavedPanelSettings() {
                                try {
                                    const saved = localStorage.getItem('heading-structure-panel-settings');
                                    if (saved) {
                                        return JSON.parse(saved);
                                    }
                                } catch (e) {
                                    console.log('Error loading saved panel settings:', e);
                                }
                                // Default settings
                                return {
                                    top: '20px',
                                    left: '20px', 
                                    width: '25%',
                                    height: '85vh'
                                };
                            }
                            
                            // Save panel position and size to localStorage
                            function savePanelSettings(panel) {
                                try {
                                    const rect = panel.getBoundingClientRect();
                                    const settings = {
                                        top: panel.style.top || '20px',
                                        left: panel.style.left || '20px',
                                        right: panel.style.right || '',
                                        width: panel.style.width || '25%',
                                        height: panel.style.height || '85vh'
                                    };
                                    localStorage.setItem('heading-structure-panel-settings', JSON.stringify(settings));
                                } catch (e) {
                                    console.log('Error saving panel settings:', e);
                                }
                            }

                            // Create panel
                            var panel = document.createElement('div');
                            panel.className = 'heading-structure-panel';
                            
                            // Load saved settings
                            const savedSettings = getSavedPanelSettings();
                            const positionStyle = savedSettings.right ? 
                                `right:${savedSettings.right};` : 
                                `left:${savedSettings.left};`;
                            
                            panel.style.cssText = `position:fixed;top:${savedSettings.top};${positionStyle}width:${savedSettings.width};max-height:${savedSettings.height};z-index:9999999;background:#0a0a0a;color:#d1d5db;border:1px solid #1f1f1f;border-radius:10px;box-shadow:0 8px 32px rgba(0,0,0,0.6);padding:20px;overflow:auto;resize:both;min-width:300px;min-height:500px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;font-size:14px;line-height:1.5`;
                            
                            let html = `
                                <div id="heading-header" style="display:flex;justify-content:space-between;align-items:center;margin-bottom:32px;padding-bottom:20px;border-bottom:1px solid #2a2a2a;cursor:move;">
                                    <h2 style="margin:0;color:#d1d5db;font-size:18px;font-weight:600;letter-spacing:-0.5px;">Heading Structure</h2>
                                    <div style="display:flex;gap:8px;">
                                        <button onclick="copyHeadingStructure()" style="padding:6px 12px;background:#7C3AED;color:#ffffff;border:none;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">Copy</button>
                                        <button onclick="this.parentNode.parentNode.parentNode.remove()" style="padding:6px 12px;background:#374151;color:#d1d5db;border:1px solid #4b5563;border-radius:6px;cursor:pointer;font-size:12px;font-weight:500;transition:all 0.2s;">✕</button>
                                    </div>
                                </div>
                            `;
                            
                            // Heading Structure Section
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Document Structure</div>';
                            html += '<div style="padding:0 20px;">';

                            headerData.forEach((header, index) => {
                                const colors = {
                                    1: '#f7b40a', 
                                    2: '#4a90e2', 
                                    3: '#50e3c2', 
                                    4: '#9013fe', 
                                    5: '#417505', 
                                    6: '#d0021b'
                                };
                                
                                const indentLevel = (header.level - 1) * 15;
                                const hiddenStyle = header.hidden ? 'opacity:0.6;' : '';
                                const hiddenText = header.hidden ? ' <span style="color:#6b7280;">(hidden)</span>' : '';
                                
                                const headingContent = `
                                    <div style="margin-left:${indentLevel}px;border-left:4px solid ${colors[header.level]};padding-left:12px;">
                                        <code style="background:#1f1f1f;padding:2px 6px;border-radius:3px;font-family:monospace;font-size:11px;color:#9ca3af;margin-right:8px;">H${header.level}</code>
                                        <span style="${hiddenStyle}">${escape(header.text)}${hiddenText}</span>
                                    </div>
                                `;
                                
                                html += createHeadingRow(headingContent, header.target, index);
                            });

                            html += '</div>';
                            html += '</div>';
                            
                            // Statistics Section
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Statistics</div>';
                            html += '<div style="padding:0 20px;">';
                            
                            html += createRow('Total Headers', '', hlCode(statistics.totalHeaders.toString()), '');
                            html += createRow('Shortest', '', hlCode(statistics.shortestWords + ' words'), '');
                            html += createRow('Longest', '', hlCode(statistics.longestWords + ' words'), '');
                            html += createRow('Average Size', '', hlCode(statistics.averageWords + ' words'), '');
                            html += createRow('Text Ratio', '', hlCode(statistics.hRatio + '%'), '');
                            
                            html += '</div>';
                            html += '</div>';
                            
                            // Chart Section
                            html += '<div style="background:#111111;border-radius:8px;border:1px solid #2a2a2a;margin-bottom:24px;overflow:hidden;">';
                            html += '<div style="background:#1a1a1a;padding:16px 20px;border-bottom:1px solid #2a2a2a;font-weight:500;color:#9ca3af;font-size:14px;">Distribution</div>';
                            html += '<div style="padding:20px;text-align:center;">';
                            html += '<canvas width="400" height="200" id="headingChart" style="border-radius:4px;background:#1a1a1a;"></canvas>';
                            html += '</div>';
                            html += '</div>';
                            
                            // Info note
                            html += '<div style="background:#1a1a1a;border-radius:6px;border:1px solid #2a2a2a;padding:16px;text-align:center;color:#6b7280;font-size:12px;">Click any heading to scroll to it on the page</div>';
                            
                            panel.innerHTML = html;
                            document.body.appendChild(panel);
                            
                            // Handle Escape key to close panel
                            function handleKeyDown(e) {
                                if (e.key === 'Escape') {
                                    panel.remove();
                                    document.removeEventListener('keydown', handleKeyDown);
                                }
                            }
                            document.addEventListener('keydown', handleKeyDown);
                            
                            // Add click event listeners to heading rows
                            const headingRows = panel.querySelectorAll('.heading-row');
                            headingRows.forEach((row, index) => {
                                row.addEventListener('click', () => {
                                    scrollToHeader(index);
                                });
                            });

                            // Draw chart
                            setTimeout(() => {
                                const canvas = document.getElementById('headingChart');
                                if (canvas) {
                                    const ctx = canvas.getContext('2d');
                                    const barHeight = 25;
                                    const gap = 10;
                                    const titleHeight = 30;
                                    const maxCount = headerLevelsCount.length > 0 ? Math.max(...headerLevelsCount.map(item => item[1])) : 1;
                                    const maxBarWidth = 280;

                                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                                    ctx.fillStyle = "#d1d5db";
                                    ctx.font = "14px Arial";
                                    ctx.textAlign = "center";
                                    ctx.fillText("Headings Count", canvas.width / 2, 20);

                                    const colors = ["#f7b40a", "#4a90e2", "#50e3c2", "#9013fe", "#417505", "#d0021b"];

                                    headerLevelsCount.forEach((item, index) => {
                                        const [category, count] = item;
                                        const barWidth = (count / maxCount) * maxBarWidth;
                                        const barY = titleHeight + index * (barHeight + gap);
                                        
                                        ctx.fillStyle = colors[index % colors.length];
                                        ctx.fillRect(80, barY, barWidth, barHeight);
                                        
                                        ctx.fillStyle = "#d1d5db";
                                        ctx.font = "12px Arial";
                                        ctx.textAlign = "left";
                                        ctx.fillText(count, 85 + barWidth, barY + barHeight/2 + 4);
                                        
                                        ctx.textAlign = "right";
                                        ctx.fillText(category, 75, barY + barHeight/2 + 4);
                                    });
                                }
                            }, 100);

                            // Add functions to window scope
                            window.copyHeadingStructure = function() {
                                const textarea = document.createElement('textarea');
                                textarea.value = copyText.join('\n');
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                                
                                // Visual feedback
                                const button = document.querySelector('[onclick="copyHeadingStructure()"]');
                                const originalText = button.textContent;
                                button.textContent = '✓ Copied!';
                                button.style.backgroundColor = '#10b981';
                                setTimeout(() => {
                                    button.textContent = originalText;
                                    button.style.backgroundColor = '#6366f1';
                                }, 1500);
                            };

                            window.scrollToHeader = function(index) {
                                const header = headerData[index].target;
                                header.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                
                                // Gentle wiggle animation
                                const animateSlide = (element) => {
                                    const animation = element.animate(
                                        [
                                            { transform: 'translateX(0)' },
                                            { transform: 'translateX(15px)' },
                                            { transform: 'translateX(0)' },
                                            { transform: 'translateX(-15px)' },
                                            { transform: 'translateX(0)' }
                                        ],
                                        {
                                            duration: 800,
                                            iterations: 2,
                                            easing: 'ease-in-out'
                                        }
                                    );

                                    setTimeout(() => {
                                        animation.cancel();
                                        element.style.transform = '';
                                    }, 1600);
                                };

                                animateSlide(header);
                            };
                            
                            // Add drag functionality similar to metadata panel
                            var isDragging = false;
                            var currentX;
                            var currentY;
                            var initialX;
                            var initialY;
                            var xOffset = 0;
                            var yOffset = 0;
                            
                            var header = panel.querySelector('#heading-header');
                            
                            function dragStart(e) {
                                if (e.target.tagName === 'BUTTON') return;
                                
                                if (e.type === "touchstart") {
                                    initialX = e.touches[0].clientX - xOffset;
                                    initialY = e.touches[0].clientY - yOffset;
                                } else {
                                    initialX = e.clientX - xOffset;
                                    initialY = e.clientY - yOffset;
                                }
                                
                                if (e.target === header || header.contains(e.target)) {
                                    isDragging = true;
                                    panel.style.cursor = 'grabbing';
                                }
                            }
                            
                            function dragEnd(e) {
                                initialX = currentX;
                                initialY = currentY;
                                isDragging = false;
                                panel.style.cursor = 'default';
                                // Save position when drag ends
                                savePanelSettings(panel);
                            }
                            
                            function drag(e) {
                                if (isDragging) {
                                    e.preventDefault();
                                    
                                    if (e.type === "touchmove") {
                                        currentX = e.touches[0].clientX - initialX;
                                        currentY = e.touches[0].clientY - initialY;
                                    } else {
                                        currentX = e.clientX - initialX;
                                        currentY = e.clientY - initialY;
                                    }
                                    
                                    xOffset = currentX;
                                    yOffset = currentY;
                                    
                                    // Constrain to viewport
                                    var rect = panel.getBoundingClientRect();
                                    var maxX = window.innerWidth - rect.width;
                                    var maxY = window.innerHeight - rect.height;
                                    
                                    currentX = Math.max(0, Math.min(currentX, maxX));
                                    currentY = Math.max(0, Math.min(currentY, maxY));
                                    
                                    // Clear any existing positioning and use absolute positioning
                                    panel.style.right = '';
                                    panel.style.left = currentX + 'px';
                                    panel.style.top = currentY + 'px';
                                    panel.style.transform = '';
                                }
                            }
                            
                            // Event listeners for drag functionality
                            header.addEventListener("mousedown", dragStart, false);
                            document.addEventListener("mouseup", dragEnd, false);
                            document.addEventListener("mousemove", drag, false);
                            
                            header.addEventListener("touchstart", dragStart, false);
                            document.addEventListener("touchend", dragEnd, false);
                            document.addEventListener("touchmove", drag, false);
                            
                            // Add resize observer to save size changes
                            if (window.ResizeObserver) {
                                const resizeObserver = new ResizeObserver(function(entries) {
                                    // Save settings when panel is resized
                                    savePanelSettings(panel);
                                });
                                resizeObserver.observe(panel);
                            }
                            
                            return true;
                        }
                    }, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('Error executing Heading Structure script:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            console.log('Heading Structure script executed successfully');
                            resolve(result);
                        }
                    });
                } catch (error) {
                    console.error('Error in Heading Structure execute:', error);
                    reject(error);
                }
            });
        });
    }

    // Reset function to close any open heading structure windows
    reset() {
        return new Promise((resolve, reject) => {
            try {
                // Note: We can't directly control windows opened by the script from here
                // The reset functionality would need to be implemented differently if needed
                console.log('Heading Structure reset called');
                resolve({ success: true });
            } catch (error) {
                console.error('Error in Heading Structure reset:', error);
                reject(error);
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HeadingStructureAction;
} else {
    window.HeadingStructureAction = HeadingStructureAction;
} 