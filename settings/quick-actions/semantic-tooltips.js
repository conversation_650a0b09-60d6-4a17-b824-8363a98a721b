// Semantic HTML Elements Tooltips
// Comprehensive guide for all semantic HTML elements with usage descriptions

class SemanticTooltips {
    static tooltipDefinitions = {
        'article': {
            title: 'Article Element',
            description: 'Standalone content that could be distributed independently',
            usage: 'Blog posts, news articles, forum posts, product cards',
            location: 'Main content areas, inside <main> or <section>',
            misuse: 'Don\'t use for non-standalone content or as generic wrapper'
        },
        'aside': {
            title: 'Aside Element', 
            description: 'Content tangentially related to surrounding content',
            usage: 'Sidebars, pull quotes, advertisements, author bios',
            location: 'Sidebar areas, within articles for related content',
            misuse: 'Don\'t use for unrelated content or main navigation'
        },
        'details': {
            title: 'Details Element',
            description: 'Disclosure widget with additional details on demand',
            usage: 'FAQ sections, expandable content, show/hide panels',
            location: 'Any content area needing progressive disclosure',
            misuse: 'Don\'t use without <summary> or for non-interactive content'
        },
        'figcaption': {
            title: 'Figure Caption Element',
            description: 'Caption or legend for a figure element',
            usage: 'Image captions, chart descriptions, code examples',
            location: 'First or last child of <figure> element',
            misuse: 'Don\'t use outside <figure> or for decorative text'
        },
        'figure': {
            title: 'Figure Element',
            description: 'Self-contained content with optional caption',
            usage: 'Images, diagrams, code listings, videos, charts',
            location: 'Content areas where media needs context/caption',
            misuse: 'Don\'t use for decorative images or layout purposes'
        },
        'footer': {
            title: 'Footer Element',
            description: 'Footer for nearest sectioning content or page',
            usage: 'Copyright info, contact details, related links',
            location: 'Bottom of page, articles, or sections',
            misuse: 'Don\'t use multiple footers in same section or for headers'
        },
        'header': {
            title: 'Header Element',
            description: 'Introductory content for nearest sectioning element',
            usage: 'Site headers, article headlines, section introductions',
            location: 'Top of page, articles, or sections',
            misuse: 'Don\'t use multiple headers in same section or for footers'
        },
        'main': {
            title: 'Main Element',
            description: 'Primary content of the page body',
            usage: 'Central topic, primary functionality, main content',
            location: 'Direct child of <body>, only one per page',
            misuse: 'Don\'t use multiple <main> elements or inside other landmarks'
        },
        'mark': {
            title: 'Mark Element',
            description: 'Text highlighted for reference or relevance',
            usage: 'Search results highlighting, important text',
            location: 'Within text content that needs emphasis',
            misuse: 'Don\'t use for styling purposes or permanent highlighting'
        },
        'nav': {
            title: 'Navigation Element',
            description: 'Section with navigation links',
            usage: 'Main navigation, breadcrumbs, pagination, menus',
            location: 'Headers, sidebars, or dedicated navigation areas',
            misuse: 'Don\'t use for non-navigation links or single links'
        },
        'section': {
            title: 'Section Element',
            description: 'Thematic grouping of content with heading',
            usage: 'Chapters, tabs, distinct content groups',
            location: 'Content areas that form logical sections',
            misuse: 'Don\'t use without headings or as generic wrapper'
        },
        'summary': {
            title: 'Summary Element',
            description: 'Summary, caption, or legend for details element',
            usage: 'Clickable heading for collapsible content',
            location: 'First child of <details> element',
            misuse: 'Don\'t use outside <details> or for regular headings'
        },
        'time': {
            title: 'Time Element',
            description: 'Machine-readable date or time',
            usage: 'Publication dates, event times, durations',
            location: 'Anywhere dates/times are displayed to users',
            misuse: 'Don\'t use for non-temporal content or arbitrary numbers'
        }
    };

    // Get tooltip data for a specific element
    static getTooltip(elementName) {
        return this.tooltipDefinitions[elementName] || null;
    }

    // Get all tooltip definitions
    static getAllTooltips() {
        return this.tooltipDefinitions;
    }

    // Generate tooltip HTML with smart positioning
    static generateTooltipHTML(elementName) {
        const tooltip = this.getTooltip(elementName);
        if (!tooltip) return '';

        return `
            <div class="semantic-tooltip semantic-tooltip-${elementName}" data-element="${elementName}">
                <div style="font-weight: 600; color: #22c55e; margin-bottom: 6px; font-size: 13px;">
                    ${tooltip.title}
                </div>
                <div style="color: #e5e5e5; margin-bottom: 6px; font-size: 12px;">
                    ${tooltip.description}
                </div>
                <div style="color: #9ca3af; margin-bottom: 4px; font-size: 11px;">
                    <strong>Usage:</strong> ${tooltip.usage}
                </div>
                <div style="color: #9ca3af; margin-bottom: 4px; font-size: 11px;">
                    <strong>Location:</strong> ${tooltip.location}
                </div>
                <div style="color: #ef4444; font-size: 11px;">
                    <strong>Avoid:</strong> ${tooltip.misuse}
                </div>
                <div class="tooltip-arrow"></div>
            </div>
        `;
    }

    // Smart positioning logic for tooltips
    static positionTooltip(card, tooltip) {
        if (!card || !tooltip) return;

        // First make tooltip visible to get accurate dimensions
        tooltip.style.opacity = '1';
        tooltip.style.visibility = 'visible';
        tooltip.style.position = 'fixed';
        
        const cardRect = card.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const tooltipWidth = tooltipRect.width || 200;
        const tooltipHeight = tooltipRect.height || 120;
        const arrow = tooltip.querySelector('.tooltip-arrow');
        
        // Reset any previous positioning
        tooltip.style.top = '';
        tooltip.style.bottom = '';
        tooltip.style.left = '';
        tooltip.style.right = '';
        tooltip.style.transform = '';
        
        // Calculate available space in each direction
        const spaceAbove = cardRect.top;
        const spaceBelow = viewportHeight - cardRect.bottom;
        const spaceLeft = cardRect.left;
        const spaceRight = viewportWidth - cardRect.right;
        
        let position = 'bottom'; // Default position
        let left, top;
        
        // Determine best position based on available space
        if (spaceAbove >= tooltipHeight + 20 && spaceAbove > spaceBelow) {
            // Show above
            position = 'top';
            top = cardRect.top - tooltipHeight - 10;
            left = cardRect.left + (cardRect.width / 2) - (tooltipWidth / 2);
            
        } else if (spaceBelow >= tooltipHeight + 20) {
            // Show below
            position = 'bottom';
            top = cardRect.bottom + 10;
            left = cardRect.left + (cardRect.width / 2) - (tooltipWidth / 2);
            
        } else if (spaceRight >= tooltipWidth + 20) {
            // Show to the right
            position = 'right';
            left = cardRect.right + 10;
            top = cardRect.top + (cardRect.height / 2) - (tooltipHeight / 2);
            
        } else if (spaceLeft >= tooltipWidth + 20) {
            // Show to the left
            position = 'left';
            left = cardRect.left - tooltipWidth - 10;
            top = cardRect.top + (cardRect.height / 2) - (tooltipHeight / 2);
            
        } else {
            // Fallback to below if no space is ideal
            position = 'bottom';
            top = cardRect.bottom + 10;
            left = cardRect.left + (cardRect.width / 2) - (tooltipWidth / 2);
        }
        
        // Adjust horizontal position to stay within viewport
        if (position === 'top' || position === 'bottom') {
            left = Math.max(10, Math.min(viewportWidth - tooltipWidth - 10, left));
        }
        
        // Adjust vertical position to stay within viewport
        if (position === 'left' || position === 'right') {
            top = Math.max(10, Math.min(viewportHeight - tooltipHeight - 10, top));
        }
        
        // Apply positioning
        tooltip.style.left = `${left}px`;
        tooltip.style.top = `${top}px`;
        
        // Position arrow
        if (arrow) {
            // Reset arrow styles
            arrow.style.top = '';
            arrow.style.bottom = '';
            arrow.style.left = '';
            arrow.style.right = '';
            arrow.style.transform = '';
            arrow.style.borderTop = '';
            arrow.style.borderBottom = '';
            arrow.style.borderLeft = '';
            arrow.style.borderRight = '';
            
            switch (position) {
                case 'top':
                    arrow.style.top = '100%';
                    arrow.style.left = `${Math.max(10, Math.min(tooltipWidth - 20, cardRect.left + (cardRect.width / 2) - left))}px`;
                    arrow.style.transform = 'translateX(-50%)';
                    arrow.style.borderLeft = '6px solid transparent';
                    arrow.style.borderRight = '6px solid transparent';
                    arrow.style.borderTop = '6px solid #000000';
                    break;
                    
                case 'bottom':
                    arrow.style.bottom = '100%';
                    arrow.style.left = `${Math.max(10, Math.min(tooltipWidth - 20, cardRect.left + (cardRect.width / 2) - left))}px`;
                    arrow.style.transform = 'translateX(-50%)';
                    arrow.style.borderLeft = '6px solid transparent';
                    arrow.style.borderRight = '6px solid transparent';
                    arrow.style.borderBottom = '6px solid #000000';
                    break;
                    
                case 'left':
                    arrow.style.left = '100%';
                    arrow.style.top = `${Math.max(10, Math.min(tooltipHeight - 20, cardRect.top + (cardRect.height / 2) - top))}px`;
                    arrow.style.transform = 'translateY(-50%)';
                    arrow.style.borderTop = '6px solid transparent';
                    arrow.style.borderBottom = '6px solid transparent';
                    arrow.style.borderLeft = '6px solid #000000';
                    break;
                    
                case 'right':
                    arrow.style.right = '100%';
                    arrow.style.top = `${Math.max(10, Math.min(tooltipHeight - 20, cardRect.top + (cardRect.height / 2) - top))}px`;
                    arrow.style.transform = 'translateY(-50%)';
                    arrow.style.borderTop = '6px solid transparent';
                    arrow.style.borderBottom = '6px solid transparent';
                    arrow.style.borderRight = '6px solid #000000';
                    break;
            }
        }
    }

    // Add tooltip styles to page
    static addTooltipStyles() {
        const existingStyle = document.getElementById('semantic-tooltips-styles');
        if (existingStyle) return;

        const style = document.createElement('style');
        style.id = 'semantic-tooltips-styles';
        style.textContent = `
            .semantic-element-card {
                position: relative;
            }
            
            /* Tooltip positioning styles - using fixed positioning for smart placement */
            .semantic-tooltip {
                position: fixed !important;
                background: #000000;
                color: #ffffff;
                padding: 14px 18px;
                border-radius: 8px;
                font-size: 12px;
                line-height: 1.5;
                z-index: 999999;
                border: 1px solid #333333;
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.7);
                width: 200px;
                text-align: left;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                pointer-events: none;
                opacity: 0;
                visibility: hidden;
                transition: all 0.2s ease;
                white-space: normal;
                word-wrap: break-word;
            }
            
            .tooltip-arrow {
                position: absolute;
                width: 0;
                height: 0;
            }
            
            /* Responsive tooltip positioning */
            @media (max-width: 768px) {
                .semantic-tooltip {
                    width: 180px;
                    font-size: 11px;
                    padding: 12px 14px;
                }
            }
            
            @media (max-width: 480px) {
                .semantic-tooltip {
                    width: 160px;
                    font-size: 10px;
                    padding: 10px 12px;
                }
            }
        `;
        
        document.head.appendChild(style);
    }

    // Remove tooltip styles from page
    static removeTooltipStyles() {
        const existingStyle = document.getElementById('semantic-tooltips-styles');
        if (existingStyle) {
            existingStyle.remove();
        }
    }

    // Initialize tooltips system
    static init() {
        this.addTooltipStyles();
    }

    // Cleanup tooltips system
    static cleanup() {
        this.removeTooltipStyles();
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SemanticTooltips;
} else if (typeof window !== 'undefined') {
    window.SemanticTooltips = SemanticTooltips;
} 