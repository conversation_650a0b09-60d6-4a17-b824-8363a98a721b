// Clean Selected Content Action - Context menu item for cleaning highlighted HTML content
class CleanSelectedContentAction {
    
    // Get HTML cleaner settings from storage
    static getHTMLCleanerSettings() {
        return new Promise((resolve) => {
            const defaultSettings = {
                removeStyles: true,
                removeClasses: true,
                removeIds: true,
                removeComments: true,
                removeEmptyTags: true,
                removeExtraSpaces: true,
                removeDataAttributes: true,
                removeWrapperDivs: true
            };

            try {
                if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                    chrome.storage.sync.get(['htmlCleanerSettings'], (result) => {
                        if (chrome.runtime.lastError) {
                            console.warn('CleanSelectedContent: Chrome storage access failed, using default settings:', chrome.runtime.lastError);
                            resolve(defaultSettings);
                        } else {
                            resolve(result.htmlCleanerSettings || defaultSettings);
                        }
                    });
                } else {
                    console.warn('CleanSelectedContent: Chrome storage not available, using default settings');
                    resolve(defaultSettings);
                }
            } catch (error) {
                console.warn('CleanSelectedContent: Error accessing chrome storage:', error);
                resolve(defaultSettings);
            }
        });
    }

    // Clean HTML content using the same logic as html-cleaner.js
    static cleanHTMLContent(htmlContent, options = {}) {
        let text = htmlContent;
        
        // Helper function: Find and replace with loop until no more matches
        function helyettesit(search, replace) {
            while (text.indexOf(search) !== -1) {
                text = text.replace(search, replace);
            }
        }
        
        // Helper function: Remove content between start and end markers
        function torolTagbanKettoKozt(startMarker, endMarker) {
            const startLen = startMarker.length;
            const endLen = endMarker.length;
            let result = '';
            let i = 0;
            let inside = false;

            while (i < text.length) {
                // Check for start marker
                if (!inside && text.substr(i, startLen) === startMarker) {
                    inside = true;
                    i += startLen;
                    continue;
                }

                // Check for end marker
                if (inside && text.substr(i, endLen) === endMarker) {
                    inside = false;
                    i += endLen;
                    continue;
                }

                // Add character if not inside marker pair
                if (!inside) {
                    result += text[i];
                }

                i++;
            }

            text = result;
        }
        
        // Clean up single &nbsp; occurrences
        function csakEgyNbspTagotTorul() {
            helyettesit('&nbsp;&nbsp;', '&nbsp;');
            helyettesit(' &nbsp;', ' ');
            helyettesit('&nbsp; ', ' ');
            helyettesit('<p>&nbsp;</p>', '');
            helyettesit('<div>&nbsp;</div>', '');
            helyettesit('<span>&nbsp;</span>', '');
        }

        // Remove line breaks and tabs from content
        function csakEnteresTagotTorul() {
            helyettesit('\n', '');
            helyettesit('\t', '');
            helyettesit('\r', '');
        }

        // Apply cleaning options based on what's selected
        if (options.removeStyles) {
            helyettesit(' style="', '"STYLE_MARKER"');
            torolTagbanKettoKozt('"STYLE_MARKER"', '"');
            helyettesit('"STYLE_MARKER"', '');
            
            // Also remove style attributes without quotes
            helyettesit(' style=', '=STYLE_MARKER');
            torolTagbanKettoKozt('=STYLE_MARKER', ' ');
            torolTagbanKettoKozt('=STYLE_MARKER', '>');
            helyettesit('=STYLE_MARKER', '');
        }

        if (options.removeClasses) {
            helyettesit(' class="', '"CLASS_MARKER"');
            torolTagbanKettoKozt('"CLASS_MARKER"', '"');
            helyettesit('"CLASS_MARKER"', '');
            
            // Also remove class attributes without quotes
            helyettesit(' class=', '=CLASS_MARKER');
            torolTagbanKettoKozt('=CLASS_MARKER', ' ');
            torolTagbanKettoKozt('=CLASS_MARKER', '>');
            helyettesit('=CLASS_MARKER', '');
        }

        if (options.removeIds) {
            helyettesit(' id="', '"ID_MARKER"');
            torolTagbanKettoKozt('"ID_MARKER"', '"');
            helyettesit('"ID_MARKER"', '');
            
            // Also remove id attributes without quotes
            helyettesit(' id=', '=ID_MARKER');
            torolTagbanKettoKozt('=ID_MARKER', ' ');
            torolTagbanKettoKozt('=ID_MARKER', '>');
            helyettesit('=ID_MARKER', '');
        }

        if (options.removeDataAttributes) {
            // Remove data-* attributes with quotes
            let dataAttrPattern = / data-[^=]*="[^"]*"/g;
            text = text.replace(dataAttrPattern, '');
            
            // Remove data-* attributes without quotes (single word values)
            dataAttrPattern = / data-[^=]*=[^\s>]*/g;
            text = text.replace(dataAttrPattern, '');
            
            // Clean up any remaining data- patterns
            helyettesit(' data-', ' DATA_MARKER');
            torolTagbanKettoKozt(' DATA_MARKER', '"');
            torolTagbanKettoKozt(' DATA_MARKER', ' ');
            torolTagbanKettoKozt(' DATA_MARKER', '>');
            helyettesit(' DATA_MARKER', '');
        }

        // Wrapper DIV removal - Remove outer wrapper divs that only contain meaningful content
        if (options.removeWrapperDivs) {
            let cleaned = false;
            let iterations = 0;
            let beforeText = '';
            const maxIterations = 10;
            
            do {
                beforeText = text;
                iterations++;
                
                // Remove wrapper divs pattern: <div><div><div>CONTENT</div></div></div> -> CONTENT
                const wrapperPattern = /^(\s*<div[^>]*>\s*)+(.+?)(\s*<\/div>\s*)+$/s;
                
                if (wrapperPattern.test(text)) {
                    const match = text.match(wrapperPattern);
                    if (match && match[2]) {
                        const innerContent = match[2].trim();
                        
                        // Only unwrap if inner content starts with meaningful tags
                        if (innerContent.match(/^<(?:ul|ol|li|p|h[1-6]|table|blockquote|article|section|nav|header|footer|main|aside|span|strong|em|a)\b/)) {
                            text = innerContent;
                            cleaned = true;
                        } else {
                            cleaned = false;
                        }
                    } else {
                        cleaned = false;
                    }
                } else {
                    cleaned = false;
                }
                
            } while (cleaned && iterations < maxIterations && text !== beforeText);
        }

        if (options.removeComments) {
            torolTagbanKettoKozt('<!--', '-->');
        }

        if (options.removeEmptyTags) {
            // Remove tags with only whitespace or &nbsp;
            csakEgyNbspTagotTorul();
            csakEnteresTagotTorul();
            
            // Remove completely empty tags
            const emptyTagPatterns = [
                /<([^>]+)>\s*<\/\1>/g,
                /<([^>]+)>&nbsp;<\/\1>/g,
                /<([^>]+)>&nbsp;\s*<\/\1>/g,
                /<([^>]+)>\s*&nbsp;<\/\1>/g
            ];

            emptyTagPatterns.forEach(pattern => {
                let matches;
                do {
                    matches = text.match(pattern);
                    if (matches) {
                        text = text.replace(pattern, '');
                    }
                } while (matches);
            });

            // Specific empty div/span cleanup
            helyettesit('<div></div>', '');
            helyettesit('<span></span>', '');
            helyettesit('<p></p>', '');
            helyettesit('<div> </div>', '');
            helyettesit('<span> </span>', '');
            helyettesit('<p> </p>', '');
        }

        if (options.removeExtraSpaces) {
            // Remove multiple consecutive spaces
            helyettesit('  ', ' ');
            helyettesit('   ', ' ');
            helyettesit('    ', ' ');
            
            // Clean up spaces around tags
            helyettesit('> <', '><');
            helyettesit(' >', '>');
            helyettesit('< ', '<');
        }

        // Always clean up extra whitespace and normalize
        helyettesit('\n', ' ');
        helyettesit('\r', ' ');
        helyettesit('\t', ' ');
        
        // Clean up multiple spaces created by line break removal
        while (text.includes('  ')) {
            helyettesit('  ', ' ');
        }

        return text.trim();
    }

    // Show notification similar to html-cleaner.js
    static showNotification(message, type = 'success') {
        // Remove any existing notifications
        document.querySelectorAll('.clean-content-notification').forEach(notif => notif.remove());

        const notification = document.createElement('div');
        notification.className = 'clean-content-notification';
        notification.setAttribute('data-clean-content-notification', 'true');
        
        const bgColor = type === 'success' ? '#10b981' : '#ef4444';
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            border: 1px solid #7C3AED;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            z-index: 9999999;
            pointer-events: none;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            box-shadow: 0 4px 16px rgba(0,0,0,0.4);
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        // Add purple dot on black background for small notifications
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="color: #7C3AED; font-size: 16px; background: #000; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">●</span>
                <span>${message}</span>
            </div>
        `;
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => notification.style.opacity = '1', 10);
        
        // Hide notification after 2 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 2000);
    }

    // Main execute function - runs directly in page context (context menu pattern)
    static execute(selectedText) {
        try {
            console.log('CleanSelectedContent: Starting execution with text:', selectedText ? selectedText.substring(0, 100) + '...' : 'null');
            
            if (!selectedText || selectedText.trim() === '') {
                CleanSelectedContentAction.showNotification('⚠️ No text selected', 'error');
                return;
            }

            console.log('CleanSelectedContent: Selected text found, getting settings...');
            
            // Get HTML cleaner settings and process
            CleanSelectedContentAction.getHTMLCleanerSettings().then(settings => {
                console.log('CleanSelectedContent: Using settings:', settings);
                
                // Clean the selected content using the same function as Copy Element
                const cleanedContent = CleanSelectedContentAction.cleanHTMLContent(selectedText, settings);
                
                console.log('CleanSelectedContent: Content cleaned, copying to clipboard...');
                
                // Copy cleaned content to clipboard
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(cleanedContent).then(() => {
                        CleanSelectedContentAction.showNotification('✓ Content cleaned and copied to clipboard');
                        console.log('CleanSelectedContent: Successfully completed');
                    }).catch(error => {
                        console.error('CleanSelectedContent: Clipboard error:', error);
                        CleanSelectedContentAction.showNotification('❌ Failed to copy to clipboard', 'error');
                    });
                } else {
                    // Fallback for older browsers or non-secure contexts
                    try {
                        const textArea = document.createElement('textarea');
                        textArea.value = cleanedContent;
                        textArea.style.position = 'fixed';
                        textArea.style.left = '-999999px';
                        textArea.style.top = '-999999px';
                        document.body.appendChild(textArea);
                        textArea.focus();
                        textArea.select();
                        
                        const successful = document.execCommand('copy');
                        textArea.remove();
                        
                        if (successful) {
                            CleanSelectedContentAction.showNotification('✓ Content cleaned and copied to clipboard');
                        } else {
                            CleanSelectedContentAction.showNotification('❌ Failed to copy to clipboard', 'error');
                        }
                    } catch (fallbackError) {
                        console.error('CleanSelectedContent: Fallback copy failed:', fallbackError);
                        CleanSelectedContentAction.showNotification('❌ Failed to copy to clipboard', 'error');
                    }
                }
            }).catch(error => {
                console.error('CleanSelectedContent: Settings error:', error);
                CleanSelectedContentAction.showNotification('❌ Failed to load cleaning settings', 'error');
            });
            
        } catch (error) {
            console.error('CleanSelectedContent: Execution error:', error);
            CleanSelectedContentAction.showNotification('❌ Failed to clean content', 'error');
        }
    }

    // Reset function for cleanup
    static reset() {
        return new Promise((resolve) => {
            try {
                // Remove any notifications
                document.querySelectorAll('.clean-content-notification, [data-clean-content-notification]').forEach(el => {
                    el.remove();
                });
                
                console.log('CleanSelectedContent: Reset completed');
                resolve();
            } catch (error) {
                console.error('CleanSelectedContent: Reset error:', error);
                resolve();
            }
        });
    }
}

// Make available globally
if (typeof window !== 'undefined') {
    window.CleanSelectedContentAction = CleanSelectedContentAction;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CleanSelectedContentAction;
} else {
    window.CleanSelectedContentAction = CleanSelectedContentAction;
} 