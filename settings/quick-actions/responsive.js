class ResponsiveAction {
    static execute() {
        return new Promise((resolve) => {
            try {
                console.log('ResponsiveAction: Starting execution...');
                
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    if (chrome.runtime.lastError) {
                        console.error('ResponsiveAction: Chrome tabs query error:', chrome.runtime.lastError);
                        resolve();
                        return;
                    }
                    
                    if (!tabs || !tabs[0]) {
                        console.error('ResponsiveAction: No active tab found');
                        resolve();
                        return;
                    }
                    
                    console.log('ResponsiveAction: Injecting script into tab:', tabs[0].id);
                    
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: function() {
                            try {
                                console.log('[Content] Responsive Simulator script starting...');
                                console.log('[Content] Preparing DOM for Responsive Simulator...');
                                
                                // Manual cleanup for responsive elements - always do this first
                                console.log('[Content] Performing cleanup for Responsive Simulator...');
                                const responsiveElements = document.querySelectorAll('.responsive-panel, #responsive-overlay, #responsive-device-container, #responsive-device-frame, #responsive-iframe');
                                responsiveElements.forEach(element => {
                                    if (element && element.parentNode) {
                                        element.parentNode.removeChild(element);
                                    }
                                });
                                
                                // Restore body scroll
                                document.body.style.overflow = '';
                                
                                console.log('[Content] Cleanup completed for Responsive Simulator');

                                // Device library with strategic grouping - sorted by width within each group
                                const devices = [
                                    // Custom - for manual sizing
                                    {name: 'Custom Size', w: 270, h: 400, group: 'Custom'},
                                    
                                    // Popular - 10 key devices covering 90% of use cases (sorted by width)
                                    {name: 'Small Android', w: 360, h: 640, group: 'Popular'},
                                    {name: 'iPhone SE', w: 375, h: 667, group: 'Popular'},
                                    {name: 'iPhone 12/13/14', w: 390, h: 844, group: 'Popular'},
                                    {name: 'iPhone 15/16', w: 393, h: 852, group: 'Popular'},
                                    {name: 'Android Flagship', w: 412, h: 915, group: 'Popular'},
                                    {name: 'iPad Mini', w: 768, h: 1024, group: 'Popular'},
                                    {name: 'iPad Air', w: 820, h: 1180, group: 'Popular'},
                                    {name: 'Standard Laptop', w: 1366, h: 768, group: 'Popular'},
                                    {name: 'Desktop FHD', w: 1920, h: 1080, group: 'Popular'},
                                    {name: 'Desktop QHD', w: 2560, h: 1440, group: 'Popular'},
                                    
                                    // Android - remaining unique sizes (sorted by width)
                                    {name: 'Android Compact', w: 320, h: 568, group: 'Android'},
                                    {name: 'Android Large', w: 450, h: 800, group: 'Android'},
                                    
                                    // iPhone - remaining unique sizes (sorted by width)
                                    {name: 'iPhone X/11', w: 375, h: 812, group: 'iPhone'},
                                    {name: 'iPhone 12/13/14 Pro Max', w: 428, h: 926, group: 'iPhone'},
                                    {name: 'iPhone 15/16 Pro Max', w: 430, h: 932, group: 'iPhone'},
                                    
                                    // iPad - remaining unique sizes (sorted by width)
                                    {name: 'iPad Pro 11"', w: 834, h: 1194, group: 'iPad'},
                                    
                                    // Laptop - remaining unique sizes (sorted by width)
                                    {name: 'MacBook Pro 13"', w: 1280, h: 800, group: 'Laptop'},
                                    {name: 'MacBook Air', w: 1440, h: 900, group: 'Laptop'},
                                    {name: 'Surface Laptop', w: 1504, h: 1000, group: 'Laptop'},
                                    {name: 'MacBook Pro 14"', w: 1512, h: 982, group: 'Laptop'},
                                    {name: 'Gaming Laptop', w: 1600, h: 900, group: 'Laptop'},
                                    {name: 'MacBook Pro 16"', w: 1728, h: 1117, group: 'Laptop'},
                                    
                                    // Desktop - remaining unique sizes (sorted by width)
                                    {name: 'Desktop Large', w: 2048, h: 1152, group: 'Desktop'},
                                    {name: 'Desktop Ultrawide', w: 3440, h: 1440, group: 'Desktop'},
                                    {name: 'Desktop 4K', w: 3840, h: 2160, group: 'Desktop'}
                                ];

                                // Get or set last selected device
                                let lastDevice = localStorage.getItem('responsiveLastDevice');
                                let selectedDeviceIndex = 0;
                                
                                if (lastDevice) {
                                    try {
                                        const lastDeviceData = JSON.parse(lastDevice);
                                        console.log('[Responsive] Found saved device:', lastDeviceData.name);
                                        const foundIndex = devices.findIndex(d => 
                                            d.name === lastDeviceData.name && 
                                            d.w === lastDeviceData.w && 
                                            d.h === lastDeviceData.h
                                        );
                                        if (foundIndex !== -1) {
                                            selectedDeviceIndex = foundIndex;
                                            console.log('[Responsive] Restored device:', devices[selectedDeviceIndex].name);
                                        } else {
                                            console.log('[Responsive] Saved device not found in current list, using default');
                                        }
                                    } catch (e) {
                                        console.log('[Responsive] Error parsing last device, using default:', e);
                                    }
                                } else {
                                    console.log('[Responsive] No saved device found, using default');
                                }

                                // Remove existing overlay
                                if(document.getElementById('responsive-overlay')) {
                                    document.getElementById('responsive-overlay').remove();
                                }

                                // Create overlay with sleek dark theme and scroll support
                                const overlay = document.createElement('div');
                                overlay.id = 'responsive-overlay';
                                overlay.className = 'responsive-panel';
                                overlay.style.cssText = `
                                    position: fixed;
                                    top: 0;
                                    left: 0;
                                    width: 100%;
                                    height: 100%;
                                    background: rgba(0, 0, 0, 0.95);
                                    z-index: 999999;
                                    overflow: auto;
                                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                                    padding: 20px;
                                    box-sizing: border-box;
                                `;

                                // Create draggable container for controls and device together
                                const deviceContainer = document.createElement('div');
                                deviceContainer.style.cssText = `
                                    display: flex;
                                    flex-direction: column;
                                    align-items: center;
                                    transform-origin: center;
                                    transition: transform 0.3s ease;
                                    position: relative;
                                    margin: 20px auto;
                                `;

                                // Create debug info panel (initially hidden) - positioned to the right side
                                const debugInfoPanel = document.createElement('div');
                                debugInfoPanel.id = 'debug-info-panel';
                                debugInfoPanel.style.cssText = `
                                    position: fixed !important;
                                    top: 50% !important;
                                    right: 20px !important;
                                    transform: translateY(-50%) !important;
                                    width: 400px !important;
                                    max-width: 25vw !important;
                                    max-height: 80vh !important;
                                    background: #0a0a0a !important;
                                    border: 1px solid #3a3a3a !important;
                                    border-radius: 8px !important;
                                    padding: 16px !important;
                                    color: #ffffff !important;
                                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
                                    font-size: 12px !important;
                                    overflow-y: auto !important;
                                    z-index: 9999999 !important;
                                    display: none !important;
                                    box-shadow: 0 8px 32px rgba(0,0,0,0.6) !important;
                                    backdrop-filter: blur(8px) !important;
                                `;

                                const debugInfoTitle = document.createElement('div');
                                debugInfoTitle.textContent = 'Debug Info - Overflow Elements';
                                debugInfoTitle.style.cssText = `
                                    font-weight: 600;
                                    margin-bottom: 12px;
                                    color: #d1d5db;
                                    border-bottom: 1px solid #3a3a3a;
                                    padding-bottom: 8px;
                                `;



                                const debugInfoContent = document.createElement('div');
                                debugInfoContent.id = 'debug-info-content';
                                debugInfoContent.style.cssText = `
                                    font-size: 11px;
                                    line-height: 1.4;
                                `;

                                debugInfoPanel.appendChild(debugInfoTitle);
                                debugInfoPanel.appendChild(debugInfoContent);

                                // Create notification tab that appears below device (separate from side panel)
                                const notificationTab = document.createElement('div');
                                notificationTab.id = 'debug-notification-tab';
                                notificationTab.style.cssText = `
                                    position: fixed !important;
                                    bottom: 20px !important;
                                    left: 50% !important;
                                    transform: translateX(-50%) !important;
                                    background: #0a0a0a !important;
                                    color: white !important;
                                    padding: 8px 16px !important;
                                    border: 1px solid #3a3a3a !important;
                                    border-radius: 6px !important;
                                    font-weight: bold !important;
                                    z-index: 9999998 !important;
                                    display: none !important;
                                    font-size: 14px !important;
                                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
                                    box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
                                    pointer-events: none !important;
                                `;

                                // Add drag handle area at the top
                                const dragHandle = document.createElement('div');
                                dragHandle.style.cssText = `
                                    position: absolute;
                                    top: -10px;
                                    left: 50%;
                                    transform: translateX(-50%);
                                    width: 60px;
                                    height: 20px;
                                    background: #4a4a4a;
                                    border-radius: 10px 10px 0 0;
                                    cursor: grab;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    z-index: 10;
                                `;
                                
                                // Add drag indicator dots
                                for (let i = 0; i < 3; i++) {
                                    const dot = document.createElement('div');
                                    dot.style.cssText = `
                                        width: 4px;
                                        height: 4px;
                                        background: #8a8a8a;
                                        border-radius: 50%;
                                        margin: 0 2px;
                                    `;
                                    dragHandle.appendChild(dot);
                                }
                                
                                deviceContainer.appendChild(dragHandle);

                                // Create sleek controls bar
                                const controlsBar = document.createElement('div');
                                controlsBar.id = 'responsive-controls-bar';
                                controlsBar.style.cssText = `
                                    display: flex;
                                    gap: 12px;
                                    align-items: center;
                                    margin-bottom: 8px;
                                    padding: 12px 16px;
                                    background: #2a2a2a;
                                    border-radius: 8px;
                                    color: #d1d5db;
                                    border: 1px solid #3a3a3a;
                                `;

                                // Sleek device selector
                                const deviceSelector = document.createElement('select');
                                deviceSelector.style.cssText = `
                                    background: #1a1a1a;
                                    color: #d1d5db;
                                    border: 1px solid #3a3a3a;
                                    border-radius: 6px;
                                    padding: 6px 10px;
                                    font-size: 13px;
                                    cursor: pointer;
                                    outline: none;
                                    min-width: 200px;
                                    font-family: inherit;
                                `;
                                
                                // Add focus styling for brand purple
                                deviceSelector.addEventListener('focus', () => {
                                    deviceSelector.style.borderColor = '#8b5cf6';
                                    deviceSelector.style.boxShadow = '0 0 0 2px rgba(139, 92, 246, 0.2)';
                                });
                                deviceSelector.addEventListener('blur', () => {
                                    deviceSelector.style.borderColor = '#3a3a3a';
                                    deviceSelector.style.boxShadow = 'none';
                                });

                                // Add device groups with sleek styling
                                const groups = {};
                                devices.forEach((d, i) => {
                                    if (!groups[d.group]) groups[d.group] = [];
                                    groups[d.group].push({...d, index: i});
                                });

                                Object.keys(groups).forEach(groupName => {
                                    const optgroup = document.createElement('optgroup');
                                    optgroup.label = groupName;
                                    optgroup.style.cssText = `
                                        background: #1a1a1a;
                                        color: #9ca3af;
                                        font-weight: 500;
                                    `;
                                    groups[groupName].forEach(d => {
                                        const opt = document.createElement('option');
                                        opt.value = d.index;
                                        // Don't show dimensions for Custom Size
                                        opt.textContent = d.name === 'Custom Size' ? d.name : `${d.name} (${d.w}×${d.h})`;
                                        opt.selected = d.index === selectedDeviceIndex;
                                        opt.style.cssText = `
                                            background: #1a1a1a;
                                            color: #d1d5db;
                                            padding: 6px;
                                        `;
                                        optgroup.appendChild(opt);
                                    });
                                    deviceSelector.appendChild(optgroup);
                                });

                                // Control buttons container
                                const controlButtons = document.createElement('div');
                                controlButtons.style.cssText = `
                                    display: flex;
                                    gap: 8px;
                                    align-items: center;
                                `;

                                // Sleek rotate button with just icon
                                const rotateBtn = document.createElement('button');
                                rotateBtn.innerHTML = '↻';
                                rotateBtn.title = 'Rotate (R)';
                                rotateBtn.style.cssText = `
                                    padding: 0;
                                    background: #3a3a3a;
                                    color: #d1d5db;
                                    border: 1px solid #4a4a4a;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 18px;
                                    font-weight: 500;
                                    transition: all 0.2s;
                                    width: 44px;
                                    height: 40px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                `;
                                
                                rotateBtn.addEventListener('mouseenter', () => {
                                    rotateBtn.style.background = '#4a4a4a';
                                    rotateBtn.style.borderColor = '#5a5a5a';
                                });
                                rotateBtn.addEventListener('mouseleave', () => {
                                    rotateBtn.style.background = '#3a3a3a';
                                    rotateBtn.style.borderColor = '#4a4a4a';
                                });
                                
                                let isRotated = false;

                                // Sleek zoom controls with proper sizing
                                const zoomContainer = document.createElement('div');
                                zoomContainer.style.cssText = `
                                    display: flex;
                                    align-items: center;
                                    background: #3a3a3a;
                                    border: 1px solid #4a4a4a;
                                    border-radius: 6px;
                                    overflow: hidden;
                                    height: 40px;
                                `;

                                const zoomOutBtn = document.createElement('button');
                                zoomOutBtn.innerHTML = '−';
                                zoomOutBtn.title = 'Zoom Out (-)';
                                zoomOutBtn.style.cssText = `
                                    padding: 0;
                                    background: transparent;
                                    color: #d1d5db;
                                    border: none;
                                    cursor: pointer;
                                    font-size: 20px;
                                    font-weight: 500;
                                    transition: all 0.2s;
                                    width: 44px;
                                    height: 38px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    border-right: 1px solid #4a4a4a;
                                `;
                                
                                const zoomDisplay = document.createElement('span');
                                zoomDisplay.textContent = '100%';
                                zoomDisplay.style.cssText = `
                                    color: #d1d5db;
                                    font-size: 13px;
                                    min-width: 60px;
                                    text-align: center;
                                    padding: 0 12px;
                                    background: #2a2a2a;
                                    height: 38px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    border-right: 1px solid #4a4a4a;
                                    font-weight: 500;
                                `;
                                
                                const zoomInBtn = document.createElement('button');
                                zoomInBtn.innerHTML = '+';
                                zoomInBtn.title = 'Zoom In (+)';
                                zoomInBtn.style.cssText = `
                                    padding: 0;
                                    background: transparent;
                                    color: #d1d5db;
                                    border: none;
                                    cursor: pointer;
                                    font-size: 20px;
                                    font-weight: 500;
                                    transition: all 0.2s;
                                    width: 44px;
                                    height: 38px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                `;
                                
                                // Hover effects for zoom buttons
                                [zoomOutBtn, zoomInBtn].forEach(btn => {
                                    btn.addEventListener('mouseenter', () => {
                                        btn.style.background = '#4a4a4a';
                                    });
                                    btn.addEventListener('mouseleave', () => {
                                        btn.style.background = 'transparent';
                                    });
                                });
                                
                                let zoomLevel = 1;
                                zoomContainer.appendChild(zoomOutBtn);
                                zoomContainer.appendChild(zoomDisplay);
                                zoomContainer.appendChild(zoomInBtn);

                                // Sleek close button
                                const closeBtn = document.createElement('button');
                                closeBtn.innerHTML = '✕';
                                closeBtn.title = 'Close (ESC)';
                                closeBtn.style.cssText = `
                                    padding: 0;
                                    background: #3a3a3a;
                                    color: #d1d5db;
                                    border: 1px solid #4a4a4a;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 16px;
                                    font-weight: 500;
                                    transition: all 0.2s;
                                    width: 44px;
                                    height: 40px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                `;
                                
                                closeBtn.addEventListener('mouseenter', () => {
                                    closeBtn.style.background = '#dc2626';
                                    closeBtn.style.borderColor = '#dc2626';
                                });
                                closeBtn.addEventListener('mouseleave', () => {
                                    closeBtn.style.background = '#3a3a3a';
                                    closeBtn.style.borderColor = '#4a4a4a';
                                });

                                // Shortcuts info
                                const shortcutsInfo = document.createElement('div');
                                shortcutsInfo.textContent = 'ESC=close, R=rotate, +/-=zoom, D=debug';
                                shortcutsInfo.style.cssText = `
                                    color: #6b7280;
                                    font-size: 11px;
                                    margin-left: 8px;
                                `;

                                // Red border debug button
                                const debugBtn = document.createElement('button');
                                debugBtn.innerHTML = '⚠';
                                debugBtn.title = 'Toggle Debug Borders (D)';
                                debugBtn.style.cssText = `
                                    padding: 0;
                                    background: #3a3a3a;
                                    color: #d1d5db;
                                    border: 1px solid #4a4a4a;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 16px;
                                    font-weight: 500;
                                    transition: all 0.2s;
                                    width: 44px;
                                    height: 40px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                `;
                                
                                let debugBordersActive = false;
                                
                                debugBtn.addEventListener('mouseenter', () => {
                                    if (!debugBordersActive) {
                                        debugBtn.style.background = '#4a4a4a';
                                        debugBtn.style.borderColor = '#5a5a5a';
                                    }
                                });
                                debugBtn.addEventListener('mouseleave', () => {
                                    if (!debugBordersActive) {
                                        debugBtn.style.background = '#3a3a3a';
                                        debugBtn.style.borderColor = '#4a4a4a';
                                    }
                                });

                                controlButtons.appendChild(rotateBtn);
                                controlButtons.appendChild(zoomContainer);
                                controlButtons.appendChild(debugBtn);
                                controlButtons.appendChild(shortcutsInfo);
                                controlButtons.appendChild(closeBtn);

                                controlsBar.appendChild(deviceSelector);
                                controlsBar.appendChild(controlButtons);

                                // Create resizable iframe container
                                const iframeContainer = document.createElement('div');
                                iframeContainer.id = 'responsive-iframe-container';
                                iframeContainer.style.cssText = `
                                    position: relative;
                                    border: 12px solid #333;
                                    border-radius: 28px;
                                    background: #111;
                                    box-shadow: 0 8px 40px rgba(0,0,0,0.6);
                                    transform-origin: center;
                                    transition: all 0.3s ease;
                                    display: block;
                                    resize: none;
                                    overflow: hidden;
                                `;

                                // Create iframe directly without frame container
                                const iframe = document.createElement('iframe');
                                iframe.id = 'responsive-iframe';
                                iframe.src = location.href;
                                
                                // CRITICAL: Prevent content scripts from running in iframe, but allow access for debug styles
                                iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation allow-top-navigation-by-user-activation');
                                
                                // Additional isolation attributes
                                iframe.setAttribute('referrerpolicy', 'no-referrer');
                                iframe.setAttribute('loading', 'lazy');
                                
                                iframe.style.cssText = `
                                    width: 100%;
                                    height: 100%;
                                    border: none;
                                    background: white;
                                    display: block;
                                `;

                                // Create width/height display
                                const dimensionsDisplay = document.createElement('div');
                                dimensionsDisplay.id = 'dimensions-display';
                                dimensionsDisplay.style.cssText = `
                                    position: absolute;
                                    top: -40px;
                                    left: 50%;
                                    transform: translateX(-50%);
                                    background: #1a1a1a;
                                    color: #d1d5db;
                                    padding: 4px 12px;
                                    border-radius: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                                    border: 1px solid #3a3a3a;
                                    white-space: nowrap;
                                    z-index: 10;
                                `;

                                // Create manual size input container (below device)
                                const manualSizeContainer = document.createElement('div');
                                manualSizeContainer.id = 'manual-size-container';
                                manualSizeContainer.style.cssText = `
                                    margin-top: 16px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    gap: 8px;
                                    padding: 12px 16px;
                                    background: #2a2a2a;
                                    border-radius: 8px;
                                    border: 1px solid #3a3a3a;
                                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                                `;

                                const manualSizeLabel = document.createElement('span');
                                manualSizeLabel.textContent = 'Custom Size:';
                                manualSizeLabel.style.cssText = `
                                    color: #d1d5db;
                                    font-size: 13px;
                                    font-weight: 500;
                                `;

                                const widthInput = document.createElement('input');
                                widthInput.type = 'number';
                                widthInput.placeholder = 'Width';
                                widthInput.min = '100';
                                widthInput.max = '4000';
                                widthInput.style.cssText = `
                                    width: 80px;
                                    padding: 6px 8px;
                                    background: #1a1a1a;
                                    border: 1px solid #3a3a3a;
                                    border-radius: 4px;
                                    color: #d1d5db;
                                    font-size: 12px;
                                    text-align: center;
                                    outline: none;
                                `;
                                
                                // Add focus styling for brand purple
                                widthInput.addEventListener('focus', () => {
                                    widthInput.style.borderColor = '#8b5cf6';
                                    widthInput.style.boxShadow = '0 0 0 2px rgba(139, 92, 246, 0.2)';
                                });
                                widthInput.addEventListener('blur', () => {
                                    widthInput.style.borderColor = '#3a3a3a';
                                    widthInput.style.boxShadow = 'none';
                                });

                                const xLabel = document.createElement('span');
                                xLabel.textContent = '×';
                                xLabel.style.cssText = `
                                    color: #6b7280;
                                    font-weight: bold;
                                `;

                                const heightInput = document.createElement('input');
                                heightInput.type = 'number';
                                heightInput.placeholder = 'Height';
                                heightInput.min = '100';
                                heightInput.max = '3000';
                                heightInput.style.cssText = `
                                    width: 80px;
                                    padding: 6px 8px;
                                    background: #1a1a1a;
                                    border: 1px solid #3a3a3a;
                                    border-radius: 4px;
                                    color: #d1d5db;
                                    font-size: 12px;
                                    text-align: center;
                                    outline: none;
                                `;
                                
                                // Add focus styling for brand purple
                                heightInput.addEventListener('focus', () => {
                                    heightInput.style.borderColor = '#8b5cf6';
                                    heightInput.style.boxShadow = '0 0 0 2px rgba(139, 92, 246, 0.2)';
                                });
                                heightInput.addEventListener('blur', () => {
                                    heightInput.style.borderColor = '#3a3a3a';
                                    heightInput.style.boxShadow = 'none';
                                });

                                const applyBtn = document.createElement('button');
                                applyBtn.textContent = 'Apply';
                                applyBtn.style.cssText = `
                                    padding: 6px 12px;
                                    background: #3a3a3a;
                                    color: #d1d5db;
                                    border: 1px solid #4a4a4a;
                                    border-radius: 4px;
                                    font-size: 12px;
                                    font-weight: 500;
                                    cursor: pointer;
                                    transition: all 0.2s;
                                    font-family: inherit;
                                    outline: none;
                                `;
                                
                                // Add focus styling for brand purple
                                applyBtn.addEventListener('focus', () => {
                                    applyBtn.style.borderColor = '#8b5cf6';
                                    applyBtn.style.boxShadow = '0 0 0 2px rgba(139, 92, 246, 0.2)';
                                });
                                applyBtn.addEventListener('blur', () => {
                                    applyBtn.style.borderColor = '#4a4a4a';
                                    applyBtn.style.boxShadow = 'none';
                                });

                                const resetBtn = document.createElement('button');
                                resetBtn.textContent = 'Reset';
                                resetBtn.style.cssText = `
                                    padding: 6px 12px;
                                    background: #3a3a3a;
                                    color: #d1d5db;
                                    border: 1px solid #4a4a4a;
                                    border-radius: 4px;
                                    font-size: 12px;
                                    font-weight: 500;
                                    cursor: pointer;
                                    transition: all 0.2s;
                                    font-family: inherit;
                                    outline: none;
                                `;
                                
                                // Add focus styling for brand purple
                                resetBtn.addEventListener('focus', () => {
                                    resetBtn.style.borderColor = '#8b5cf6';
                                    resetBtn.style.boxShadow = '0 0 0 2px rgba(139, 92, 246, 0.2)';
                                });
                                resetBtn.addEventListener('blur', () => {
                                    resetBtn.style.borderColor = '#4a4a4a';
                                    resetBtn.style.boxShadow = 'none';
                                });

                                // Hover effects to match other buttons
                                applyBtn.addEventListener('mouseenter', () => {
                                    applyBtn.style.background = '#4a4a4a';
                                    applyBtn.style.borderColor = '#5a5a5a';
                                });
                                applyBtn.addEventListener('mouseleave', () => {
                                    applyBtn.style.background = '#3a3a3a';
                                    applyBtn.style.borderColor = '#4a4a4a';
                                });
                                resetBtn.addEventListener('mouseenter', () => {
                                    resetBtn.style.background = '#4a4a4a';
                                    resetBtn.style.borderColor = '#5a5a5a';
                                });
                                resetBtn.addEventListener('mouseleave', () => {
                                    resetBtn.style.background = '#3a3a3a';
                                    resetBtn.style.borderColor = '#4a4a4a';
                                });

                                manualSizeContainer.appendChild(manualSizeLabel);
                                manualSizeContainer.appendChild(widthInput);
                                manualSizeContainer.appendChild(xLabel);
                                manualSizeContainer.appendChild(heightInput);
                                manualSizeContainer.appendChild(applyBtn);
                                manualSizeContainer.appendChild(resetBtn);

                                // Create resize handles
                                const rightResizeHandle = document.createElement('div');
                                rightResizeHandle.id = 'right-resize-handle';
                                rightResizeHandle.style.cssText = `
                                    position: absolute;
                                    top: 0;
                                    right: -6px;
                                    width: 12px;
                                    height: 100%;
                                    background: #4a4a4a;
                                    cursor: ew-resize;
                                    border-radius: 0 16px 16px 0;
                                    opacity: 0.7;
                                    transition: opacity 0.2s;
                                    z-index: 10;
                                `;

                                const bottomResizeHandle = document.createElement('div');
                                bottomResizeHandle.id = 'bottom-resize-handle';
                                bottomResizeHandle.style.cssText = `
                                    position: absolute;
                                    bottom: -6px;
                                    left: 0;
                                    width: 100%;
                                    height: 12px;
                                    background: #4a4a4a;
                                    cursor: ns-resize;
                                    border-radius: 0 0 16px 16px;
                                    opacity: 0.7;
                                    transition: opacity 0.2s;
                                    z-index: 10;
                                `;

                                const cornerResizeHandle = document.createElement('div');
                                cornerResizeHandle.id = 'corner-resize-handle';
                                cornerResizeHandle.style.cssText = `
                                    position: absolute;
                                    bottom: -6px;
                                    right: -6px;
                                    width: 12px;
                                    height: 12px;
                                    background: #6b7280;
                                    cursor: nwse-resize;
                                    border-radius: 0 0 16px 0;
                                    z-index: 15;
                                `;

                                // Hover effects for handles
                                [rightResizeHandle, bottomResizeHandle].forEach(handle => {
                                    handle.addEventListener('mouseenter', () => {
                                        handle.style.opacity = '1';
                                        handle.style.background = '#6b7280';
                                    });
                                    handle.addEventListener('mouseleave', () => {
                                        handle.style.opacity = '0.7';
                                        handle.style.background = '#4a4a4a';
                                    });
                                });

                                // Assemble the resizable iframe
                                iframeContainer.appendChild(iframe);
                                iframeContainer.appendChild(dimensionsDisplay);
                                iframeContainer.appendChild(rightResizeHandle);
                                iframeContainer.appendChild(bottomResizeHandle);
                                iframeContainer.appendChild(cornerResizeHandle);

                                // Manual size input functionality
                                function applyManualSize() {
                                    const width = parseInt(widthInput.value);
                                    const height = parseInt(heightInput.value);
                                    
                                    if (width >= 100 && width <= 4000 && height >= 100 && height <= 3000) {
                                        // Switch to Custom device
                                        deviceSelector.value = 0; // Custom is first in list
                                        isManuallyResized = true;
                                        manualWidth = width;
                                        manualHeight = height;
                                        
                                        // Save custom dimensions for future use
                                        customWidth = width;
                                        customHeight = height;
                                        localStorage.setItem('responsiveCustomSize', JSON.stringify({
                                            width: width,
                                            height: height
                                        }));
                                        
                                        // Apply size
                                        iframeContainer.style.width = width + 'px';
                                        iframeContainer.style.height = height + 'px';
                                        
                                        // Update dimensions display
                                        updateDimensionsDisplay();
                                        
                                        // Refresh iframe content and debug analysis if active
                                        if (debugBordersActive) {
                                            console.log('[Responsive] Debug is active, refreshing iframe and analysis after manual resize...');
                                            refreshIframeAndDebug();
                                        }
                                        
                                        console.log(`[Responsive] Applied and saved custom size: ${width}×${height}px`);
                                    } else {
                                        alert('Please enter valid dimensions:\nWidth: 100-4000px\nHeight: 100-3000px');
                                    }
                                }
                                
                                function resetToDevice() {
                                    isManuallyResized = false;
                                    manualWidth = 0;
                                    manualHeight = 0;
                                    
                                    // Clear saved custom dimensions
                                    customWidth = 0;
                                    customHeight = 0;
                                    localStorage.removeItem('responsiveCustomSize');
                                    
                                    // Clear inputs
                                    widthInput.value = '';
                                    heightInput.value = '';
                                    
                                    // Switch back to iPhone SE (index 1)
                                    deviceSelector.value = 1;
                                    updateDevice();
                                    
                                    console.log('[Responsive] Reset to device mode and cleared custom size memory');
                                }
                                
                                function updateManualInputs() {
                                    const d = devices[deviceSelector.value];
                                    if (d.name === 'Custom Size') {
                                        if (isManuallyResized) {
                                            // Use current manual dimensions
                                            widthInput.value = manualWidth;
                                            heightInput.value = manualHeight;
                                        } else if (customWidth > 0 && customHeight > 0) {
                                            // Use saved custom dimensions
                                            widthInput.value = customWidth;
                                            heightInput.value = customHeight;
                                        } else {
                                            // Use default custom size
                                            widthInput.value = d.w;
                                            heightInput.value = d.h;
                                        }
                                    } else {
                                        // Clear inputs for non-custom devices
                                        widthInput.value = '';
                                        heightInput.value = '';
                                    }
                                }
                                
                                // Add event listeners for manual size inputs
                                applyBtn.addEventListener('click', applyManualSize);
                                resetBtn.addEventListener('click', resetToDevice);
                                
                                // Apply on Enter key
                                [widthInput, heightInput].forEach(input => {
                                    input.addEventListener('keypress', (e) => {
                                        if (e.key === 'Enter') {
                                            applyManualSize();
                                        }
                                    });
                                });
                                


                                // Update device function
                                function updateDevice() {
                                    const d = devices[deviceSelector.value];
                                    let w, h;
                                    
                                    // Handle Custom Size differently - use stored custom dimensions
                                    if (d.name === 'Custom Size' && (isManuallyResized || (customWidth > 0 && customHeight > 0))) {
                                        // Use stored custom dimensions, applying rotation if needed
                                        const baseWidth = customWidth > 0 ? customWidth : manualWidth || d.w;
                                        const baseHeight = customHeight > 0 ? customHeight : manualHeight || d.h;
                                        w = isRotated ? baseHeight : baseWidth;
                                        h = isRotated ? baseWidth : baseHeight;
                                        
                                        // Update manual dimensions to match rotation
                                        isManuallyResized = true;
                                        manualWidth = w;
                                        manualHeight = h;
                                        
                                        console.log(`[Responsive] Custom device rotation: ${baseWidth}×${baseHeight} -> ${w}×${h} (rotated: ${isRotated})`);
                                    } else {
                                        // Standard device behavior
                                        w = isRotated ? d.h : d.w;
                                        h = isRotated ? d.w : d.h;
                                        
                                        // Reset manual resize when changing to non-custom devices
                                        if (d.name !== 'Custom Size') {
                                            isManuallyResized = false;
                                            manualWidth = 0;
                                            manualHeight = 0;
                                        }
                                    }
                                    
                                    iframeContainer.style.width = w + 'px';
                                    iframeContainer.style.height = h + 'px';
                                    
                                    // Update current device reference for resize functionality
                                    window.currentDevice = d;
                                    
                                    // Calculate auto-scale to ensure controls stay visible
                                    const controlsHeight = 60; // Approximate height of controls bar
                                    const padding = 40; // Extra padding for safety
                                    const maxWidth = window.innerWidth - padding;
                                    const maxHeight = window.innerHeight - controlsHeight - padding;
                                    
                                    // Calculate scale needed to fit device in viewport
                                    const scaleX = maxWidth / w;
                                    const scaleY = maxHeight / h;
                                    const autoScale = Math.min(scaleX, scaleY, 1); // Never scale up, only down
                                    
                                    // If device is too large, auto-scale it down
                                    if (autoScale < 1) {
                                        zoomLevel = Math.max(autoScale, 0.25); // Minimum zoom of 25%
                                        console.log('[Responsive] Auto-scaled large device to:', Math.round(zoomLevel * 100) + '%');
                                    }
                                    
                                    // Always update zoom display and apply zoom to container
                                    zoomDisplay.textContent = Math.round(zoomLevel * 100) + '%';
                                    
                                    // Apply zoom while preserving any drag position
                                    const currentTransform = deviceContainer.style.transform;
                                    if (currentTransform && currentTransform.includes('translate')) {
                                        // Extract translate values and combine with scale
                                        const translateMatch = currentTransform.match(/translate\(([^)]+)\)/);
                                        if (translateMatch) {
                                            deviceContainer.style.transform = `${translateMatch[0]} scale(${zoomLevel})`;
                                        } else {
                                            deviceContainer.style.transform = `scale(${zoomLevel})`;
                                        }
                                    } else {
                                        deviceContainer.style.transform = `scale(${zoomLevel})`;
                                    }
                                    
                                    // Update dimensions display
                                    updateDimensionsDisplay();
                                    
                                    console.log('[Responsive] Applied zoom:', Math.round(zoomLevel * 100) + '%', 'to device:', d.name);
                                    
                                    // Save last selected device
                                    localStorage.setItem('responsiveLastDevice', JSON.stringify(d));
                                }

                                // Update zoom function
                                function updateZoom() {
                                    zoomDisplay.textContent = Math.round(zoomLevel * 100) + '%';
                                    
                                    // Apply zoom while preserving any drag position
                                    const currentTransform = deviceContainer.style.transform;
                                    if (currentTransform && currentTransform.includes('translate')) {
                                        // Extract translate values and combine with scale
                                        const translateMatch = currentTransform.match(/translate\(([^)]+)\)/);
                                        if (translateMatch) {
                                            deviceContainer.style.transform = `${translateMatch[0]} scale(${zoomLevel})`;
                                        } else {
                                            deviceContainer.style.transform = `scale(${zoomLevel})`;
                                        }
                                    } else {
                                        deviceContainer.style.transform = `scale(${zoomLevel})`;
                                    }
                                    
                                    console.log('[Responsive] Zoom updated to:', Math.round(zoomLevel * 100) + '%');
                                }

                                // Event listeners
                                deviceSelector.addEventListener('change', function(e) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    console.log('[Responsive] Device selector changed to:', deviceSelector.value);
                                    
                                    // Update manual inputs based on selected device
                                    updateManualInputs();
                                    
                                    updateDevice();
                                    // Ensure device is saved when selector changes
                                    const d = devices[deviceSelector.value];
                                    localStorage.setItem('responsiveLastDevice', JSON.stringify(d));
                                    console.log('[Responsive] Device saved:', d.name);
                                });
                                
                                rotateBtn.addEventListener('click', function() {
                                    isRotated = !isRotated;
                                    updateDevice();
                                });
                                
                                zoomInBtn.addEventListener('click', function() {
                                    if (zoomLevel < 2) {
                                        zoomLevel += 0.1;
                                        updateZoom();
                                    }
                                });
                                
                                zoomOutBtn.addEventListener('click', function() {
                                    if (zoomLevel > 0.25) {
                                        zoomLevel -= 0.1;
                                        updateZoom();
                                    }
                                });
                                
                                // Debug borders toggle function
                                function toggleDebugBorders() {
                                    debugBordersActive = !debugBordersActive;
                                    
                                    try {
                                        if (debugBordersActive) {
                                            // Wait for iframe to be ready, then inject debug styles
                                            if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                                                addDebugStyles();
                                            } else {
                                                iframe.addEventListener('load', addDebugStyles, { once: true });
                                            }
                                            
                                            // Update button appearance
                                            debugBtn.style.background = '#dc2626';
                                            debugBtn.style.borderColor = '#dc2626';
                                            debugBtn.style.color = '#ffffff';
                                            debugBtn.title = 'Remove Debug Borders (D)';
                                            
                                            console.log('[Responsive] Debug borders enabled');
                                        } else {
                                            // Remove debug styles from iframe
                                            removeDebugStyles();
                                            
                                            // Reset button appearance
                                            debugBtn.style.background = '#3a3a3a';
                                            debugBtn.style.borderColor = '#4a4a4a';
                                            debugBtn.style.color = '#d1d5db';
                                            debugBtn.title = 'Toggle Debug Borders (D)';
                                            
                                            console.log('[Responsive] Debug borders disabled');
                                        }
                                    } catch (error) {
                                        console.error('[Responsive] Error toggling debug borders:', error);
                                        // Reset state on error
                                        debugBordersActive = false;
                                        debugBtn.style.background = '#3a3a3a';
                                        debugBtn.style.borderColor = '#4a4a4a';
                                        debugBtn.style.color = '#d1d5db';
                                    }
                                }
                                
                                function addDebugStyles() {
                                    try {
                                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                        if (!iframeDoc) return;
                                        
                                        // Remove existing debug style if any
                                        const existingStyle = iframeDoc.getElementById('debug-borders-style');
                                        if (existingStyle) {
                                            existingStyle.remove();
                                        }
                                        
                                        // Analyze overflow elements first
                                        const overflowResults = analyzeOverflowElements(iframeDoc);
                                        
                                        // Create and inject appropriate debug style based on results
                                        const debugStyle = iframeDoc.createElement('style');
                                        debugStyle.id = 'debug-borders-style';
                                        
                                        if (overflowResults.overflowElements.length === 0) {
                                            // No overflow - show green container outline and external success notification
                                            debugStyle.textContent = `
                                                body { outline: 3px solid #10b981 !important; outline-offset: -3px !important; }
                                            `;
                                            
                                            // Show green success notification below device
                                            notificationTab.textContent = 'No Overflow Issues Detected';
                                            notificationTab.style.background = '#10b981';
                                            notificationTab.style.display = 'block';
                                            
                                            // Keep debug info panel hidden when no issues
                                            debugInfoPanel.style.display = 'none';
                                            
                                            console.log('[Responsive] No overflow detected - applied green styling, hiding debug panel');
                                        } else {
                                            // Overflow detected - show debug info panel and highlight problematic elements
                                            debugInfoPanel.style.display = 'block';
                                            debugInfoPanel.style.visibility = 'visible';
                                            
                                            let css = '';
                                            overflowResults.overflowElements.forEach((item, index) => {
                                                css += `[data-debug-element="${index}"] { outline: 2px solid #dc2626 !important; outline-offset: 1px !important; } `;
                                            });
                                            
                                            debugStyle.textContent = css;
                                            
                                            // Show red warning notification below device
                                            notificationTab.textContent = `${overflowResults.overflowElements.length} Potential Overflow Issue(s) Found`;
                                            notificationTab.style.background = '#dc2626';
                                            notificationTab.style.display = 'block';
                                            
                                            // Mark problematic elements with data attributes
                                            overflowResults.overflowElements.forEach((item, index) => {
                                                if (item.element) {
                                                    item.element.setAttribute('data-debug-element', index);
                                                }
                                            });
                                            
                                            console.log('[Responsive] Overflow detected - highlighted problematic elements in red, showing debug panel');
                                        }
                                        
                                        iframeDoc.head.appendChild(debugStyle);
                                        
                                        console.log('[Responsive] Debug styles injected successfully');
                                    } catch (error) {
                                        console.error('[Responsive] Could not inject debug styles:', error);
                                    }
                                }
                                
                                function analyzeOverflowElements(iframeDoc) {
                                    try {
                                        // Get ACTUAL iframe viewport dimensions for accurate overflow detection
                                        const actualWidth = iframe.offsetWidth;
                                        const actualHeight = iframe.offsetHeight;
                                        
                                        // Determine device info
                                        const currentDevice = devices[deviceSelector.value];
                                        const deviceName = isManuallyResized ? `Custom (${actualWidth}×${actualHeight})` : 
                                                         (currentDevice.name === 'Custom Size' ? `Custom (${actualWidth}×${actualHeight})` : currentDevice.name);
                                        
                                        console.log(`[Responsive] Analyzing overflow for: ${deviceName} using ACTUAL iframe dimensions: ${actualWidth}×${actualHeight}px`);
                                        
                                        // STEP 1: Check if there are ANY scrollbars first - SIMPLE approach
                                        const iframeWindow = iframe.contentWindow;
                                        const body = iframeDoc.body;
                                        const html = iframeDoc.documentElement;
                                        
                                        // Get detailed scroll information
                                        console.log(`[Responsive] === SCROLL DETECTION DEBUG ===`);
                                        console.log(`[Responsive] Iframe dimensions: ${iframe.offsetWidth}×${iframe.offsetHeight}`);
                                        console.log(`[Responsive] HTML scroll: ${html.scrollWidth}×${html.scrollHeight}`);
                                        console.log(`[Responsive] HTML offset: ${html.offsetWidth}×${html.offsetHeight}`);
                                        console.log(`[Responsive] HTML client: ${html.clientWidth}×${html.clientHeight}`);
                                        console.log(`[Responsive] Body scroll: ${body.scrollWidth}×${body.scrollHeight}`);
                                        console.log(`[Responsive] Body offset: ${body.offsetWidth}×${body.offsetHeight}`);
                                        console.log(`[Responsive] Body client: ${body.clientWidth}×${body.clientHeight}`);
                                        
                                        // Test if we can actually scroll
                                        const canScrollRight = iframeWindow.scrollX < (html.scrollWidth - iframe.offsetWidth);
                                        const canScrollDown = iframeWindow.scrollY < (html.scrollHeight - iframe.offsetHeight);
                                        
                                        console.log(`[Responsive] Current scroll position: ${iframeWindow.scrollX}, ${iframeWindow.scrollY}`);
                                        console.log(`[Responsive] Can scroll right: ${canScrollRight}, Can scroll down: ${canScrollDown}`);
                                        
                                        // Simple check: content larger than iframe
                                        const hasHorizontalScroll = html.scrollWidth > iframe.offsetWidth;
                                        const hasVerticalScroll = html.scrollHeight > iframe.offsetHeight;
                                        
                                        console.log(`[Responsive] Final result: H=${hasHorizontalScroll}, V=${hasVerticalScroll}`);
                                        
                                        // If NO scrollbars exist, there's no overflow problem
                                        if (!hasHorizontalScroll && !hasVerticalScroll) {
                                            console.log(`[Responsive] No scrollbars detected - no overflow issues!`);
                                            const results = { overflowElements: [], allElements: [] };
                                            displayOverflowInfo([]);
                                            return results;
                                        }
                                        
                                        // TEMPORARY: Only check for HORIZONTAL overflow issues (as requested)
                                        if (!hasHorizontalScroll) {
                                            console.log(`[Responsive] Only vertical scrolling detected - ignoring since you want horizontal overflow detection only`);
                                            const results = { overflowElements: [], allElements: [] };
                                            displayOverflowInfo([]);
                                            return results;
                                        }
                                        
                                        // STEP 2: Only if scrollbars exist, find the ROOT CAUSE elements causing them
                                        console.log(`[Responsive] Scrollbars detected - finding ROOT CAUSE elements...`);
                                        const overflowElements = [];
                                        
                                        // Strategy: Find elements that are directly causing the overflow
                                        // These are typically elements that:
                                        // 1. Extend beyond the document boundaries
                                        // 2. Are not contained within an overflow:hidden parent
                                        // 3. Are positioned elements extending outside
                                        // 4. Have fixed widths that exceed viewport
                                        
                                        const allElements = Array.from(iframeDoc.querySelectorAll('*'));
                                        const rootCauseElements = [];
                                        
                                        // First pass: Find potential root cause elements
                                        allElements.forEach((element, index) => {
                                            // Skip certain elements that commonly overflow intentionally
                                            const skipTags = ['HTML', 'BODY', 'SCRIPT', 'STYLE', 'META', 'TITLE', 'HEAD'];
                                            if (skipTags.includes(element.tagName)) return;
                                            
                                            const rect = element.getBoundingClientRect();
                                            const computedStyle = iframeDoc.defaultView.getComputedStyle(element);
                                            
                                            // Check if element extends beyond document boundaries
                                            const extendsRight = rect.right > actualWidth;
                                            const extendsLeft = rect.left < 0;
                                            const extendsBottom = rect.bottom > actualHeight;
                                            const extendsTop = rect.top < 0;
                                            
                                            // Check if this element is likely a root cause
                                            let isRootCause = false;
                                            let causeType = '';
                                            
                                            if (hasHorizontalScroll) {
                                                // Check for horizontal overflow causes
                                                if (extendsRight || extendsLeft) {
                                                    // Element extends beyond viewport
                                                    isRootCause = true;
                                                    causeType += 'extends-horizontal ';
                                                } else if (rect.width > actualWidth && computedStyle.position !== 'absolute' && computedStyle.position !== 'fixed') {
                                                    // Element is wider than viewport (but not positioned)
                                                    isRootCause = true;
                                                    causeType += 'too-wide ';
                                                } else if (computedStyle.position === 'fixed' || computedStyle.position === 'absolute') {
                                                    // Positioned elements that might cause overflow
                                                    if (extendsRight || rect.width > actualWidth) {
                                                        isRootCause = true;
                                                        causeType += 'positioned-horizontal ';
                                                    }
                                                }
                                            }
                                            
                                            // For now, focus only on horizontal overflow (as requested)
                                            // Uncomment below for vertical overflow detection
                                            /*
                                            if (hasVerticalScroll) {
                                                if (extendsBottom || extendsTop) {
                                                    isRootCause = true;
                                                    causeType += 'extends-vertical ';
                                                } else if (rect.height > actualHeight && computedStyle.position !== 'absolute' && computedStyle.position !== 'fixed') {
                                                    isRootCause = true;
                                                    causeType += 'too-tall ';
                                                }
                                            }
                                            */
                                            
                                            if (isRootCause) {
                                                // Check if parent has overflow hidden (which would contain this element)
                                                let parentHasOverflowHidden = false;
                                                let parent = element.parentElement;
                                                while (parent && parent !== iframeDoc.body) {
                                                    const parentStyle = iframeDoc.defaultView.getComputedStyle(parent);
                                                    if (parentStyle.overflow === 'hidden' || 
                                                        (hasHorizontalScroll && parentStyle.overflowX === 'hidden')) {
                                                        parentHasOverflowHidden = true;
                                                        break;
                                                    }
                                                    parent = parent.parentElement;
                                                }
                                                
                                                // Only add if not contained by overflow:hidden parent
                                                if (!parentHasOverflowHidden) {
                                                    rootCauseElements.push({
                                                        element,
                                                        index,
                                                        causeType: causeType.trim(),
                                                        rect,
                                                        computedStyle
                                                    });
                                                }
                                            }
                                        });
                                        
                                        // Second pass: Remove child elements if their parent is already identified
                                        const finalRootCauses = [];
                                        rootCauseElements.forEach(candidate => {
                                            let hasParentInList = false;
                                            
                                            // Check if any parent element is already in our root causes list
                                            let parent = candidate.element.parentElement;
                                            while (parent) {
                                                const parentInList = rootCauseElements.find(other => other.element === parent);
                                                if (parentInList) {
                                                    hasParentInList = true;
                                                    break;
                                                }
                                                parent = parent.parentElement;
                                            }
                                            
                                            // Only add if no parent is already identified as root cause
                                            if (!hasParentInList) {
                                                finalRootCauses.push(candidate);
                                            }
                                        });
                                        
                                        // Convert to the expected format
                                        finalRootCauses.forEach(item => {
                                            overflowElements.push({
                                                element: item.element,
                                                tagName: item.element.tagName.toLowerCase(),
                                                id: item.element.id || '',
                                                classes: (typeof item.element.className === 'string' ? item.element.className : '') || '',
                                                width: item.rect.width,
                                                height: item.rect.height,
                                                scrollWidth: item.element.scrollWidth,
                                                scrollHeight: item.element.scrollHeight,
                                                clientWidth: item.element.clientWidth,
                                                clientHeight: item.element.clientHeight,
                                                position: item.computedStyle.position,
                                                overflow: item.computedStyle.overflow,
                                                overflowX: item.computedStyle.overflowX,
                                                overflowY: item.computedStyle.overflowY,
                                                marginLeft: item.computedStyle.marginLeft,
                                                marginRight: item.computedStyle.marginRight,
                                                deviceName: deviceName,
                                                deviceWidth: actualWidth,
                                                deviceHeight: actualHeight,
                                                deviceOrientation: isRotated ? 'Landscape' : 'Portrait',
                                                isDesktopDevice: actualWidth >= 1024,
                                                causingHorizontalScroll: hasHorizontalScroll,
                                                causingVerticalScroll: hasVerticalScroll,
                                                causeType: item.causeType,
                                                index: item.index
                                            });
                                        });
                                        
                                        console.log(`[Responsive] Found ${overflowElements.length} elements causing scrollbars for ${deviceName}`);
                                        
                                        const results = { overflowElements, allElements };
                                        displayOverflowInfo(overflowElements);
                                        return results;
                                        
                                    } catch (error) {
                                        console.error('[Responsive] Error analyzing overflow elements:', error);
                                        return { overflowElements: [], allElements: [] };
                                    }
                                }
                                
                                function displayOverflowInfo(overflowElements) {
                                    const content = debugInfoContent;
                                    
                                    if (overflowElements.length === 0) {
                                        content.innerHTML = `
                                            <div style="color: #10b981; padding: 12px; background: #064e3b; border-radius: 6px; text-align: center; font-weight: 600; border-left: 4px solid #10b981;">
                                                No overflow elements detected! Your layout is perfect.
                                            </div>
                                        `;
                                        return;
                                    }
                                    
                                    let html = `
                                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px; padding: 8px; background: #2a2a2a; border-radius: 6px; border-left: 4px solid #f59e0b;">
                                            <div style="color: #f59e0b; font-weight: 600;">Found ${overflowElements.length} potentially problematic elements</div>
                                            <div style="display: flex; gap: 8px;">
                                                                                                  <button id="copy-issues-btn" style="padding: 4px 8px; background: ##3a3a3; color: white; border: 1px solid #d1d5da; background-color: #3a3a3a; border-radius: 4px; cursor: pointer; font-size: 10px; font-weight: 500;">Copy Issues</button>
                                                  <button id="export-csv-btn" style="padding: 4px 8px; background: #3a3a3a; color: white; border: 1px solid #34d399; border-radius: 4px; cursor: pointer; font-size: 10px; font-weight: 500;">Export CSV</button>
                                            </div>
                                        </div>
                                    `;
                                    
                                    // Add individual element details
                                    overflowElements.forEach((item, i) => {
                                        const bgColor = i % 2 === 0 ? '#2a2a2a' : '#1e1e1e';
                                        const safeId = (item.id && typeof item.id === 'string') ? item.id.replace(/['"<>&]/g, '') : '';
                                        const safeClasses = (item.classes && typeof item.classes === 'string') ? item.classes.replace(/['"<>&]/g, '') : '';
                                        html += `
                                            <div style="background: ${bgColor}; padding: 8px; margin-bottom: 6px; border-radius: 4px; cursor: pointer; border: 1px solid #3a3a3a;" data-element-index="${item.index}">
                                                <div style="color: #60a5fa; font-weight: 600; margin-bottom: 4px;">&lt;${item.tagName}&gt;</div>
                                                ${safeId ? `<div style="color: #34d399; font-size: 11px;">ID: #${safeId}</div>` : ''}
                                                ${safeClasses ? `<div style="color: #fbbf24; font-size: 11px; margin-bottom: 4px;">Classes: .${safeClasses.split(' ').join(', .')}</div>` : ''}
                                                ${item.causeType ? `<div style="color: #f59e0b; font-size: 11px; font-weight: 600; margin-bottom: 4px; background: #451a03; padding: 2px 6px; border-radius: 3px;">ROOT CAUSE: ${item.causeType}</div>` : ''}
                                                <div style="color: #d1d5db; margin-top: 4px; font-size: 11px; line-height: 1.3;">
                                                    <div>Size: ${Math.round(item.width)}×${Math.round(item.height)}px</div>
                                                    <div>Scroll: ${item.scrollWidth}×${item.scrollHeight}px</div>
                                                    <div>Client: ${item.clientWidth}×${item.clientHeight}px</div>
                                                </div>
                                                ${item.position !== 'static' ? `<div style="color: #f87171; font-size: 11px; margin-top: 2px;">Position: ${item.position}</div>` : ''}
                                                ${item.overflow !== 'visible' ? `<div style="color: #a78bfa; font-size: 11px; margin-top: 2px;">Overflow: ${item.overflow}</div>` : ''}
                                                <div style="color: #6b7280; font-size: 10px; margin-top: 4px; font-style: italic;">Click to highlight in preview</div>
                                            </div>
                                        `;
                                    });
                                    

                                    
                                    content.innerHTML = html;
                                    
                                    // Add click handlers for highlighting elements
                                    content.querySelectorAll('[data-element-index]').forEach(div => {
                                        div.addEventListener('click', function() {
                                            const index = parseInt(this.getAttribute('data-element-index'));
                                            highlightElementInIframe(index);
                                        });
                                    });
                                    
                                    // Store current overflow data for export
                                    window.currentOverflowData = overflowElements;
                                    
                                    // Add copy functionality to new inline button
                                    const copyBtn = content.querySelector('#copy-issues-btn');
                                    if (copyBtn) {
                                        copyBtn.onclick = function() {
                                            const textData = generateOverflowReport(overflowElements, 'text');
                                            navigator.clipboard.writeText(textData).then(() => {
                                                const originalText = copyBtn.textContent;
                                                copyBtn.textContent = 'Copied!';
                                                copyBtn.style.background = '#10b981';
                                                setTimeout(() => {
                                                    copyBtn.textContent = originalText;
                                                    copyBtn.style.background = '#3b82f6';
                                                }, 2000);
                                            }).catch(err => {
                                                console.error('Failed to copy to clipboard:', err);
                                                alert('Failed to copy to clipboard');
                                            });
                                        };
                                    }
                                    
                                    // Add export functionality to new inline button
                                    const exportBtn = content.querySelector('#export-csv-btn');
                                    if (exportBtn) {
                                        exportBtn.onclick = function() {
                                            const csvData = generateOverflowReport(overflowElements, 'csv');
                                            downloadCSV(csvData, `overflow-issues-${new Date().toISOString().split('T')[0]}.csv`);
                                            
                                            // Show feedback
                                            const originalText = exportBtn.textContent;
                                            exportBtn.textContent = 'Downloaded!';
                                            exportBtn.style.background = '#059669';
                                            setTimeout(() => {
                                                exportBtn.textContent = originalText;
                                                exportBtn.style.background = '#10b981';
                                            }, 2000);
                                        };
                                    }
                                }
                                
                                function highlightElementInIframe(index) {
                                    try {
                                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                        if (!iframeDoc) return;
                                        
                                        // Remove previous highlight
                                        const prevHighlight = iframeDoc.querySelector('.temp-highlight-element');
                                        if (prevHighlight) {
                                            prevHighlight.classList.remove('temp-highlight-element');
                                        }
                                        
                                        // Add highlight to target element
                                        const allElements = iframeDoc.querySelectorAll('*');
                                        if (allElements[index]) {
                                            allElements[index].classList.add('temp-highlight-element');
                                            
                                            // Add temporary highlight style
                                            let highlightStyle = iframeDoc.getElementById('temp-highlight-style');
                                            if (!highlightStyle) {
                                                highlightStyle = iframeDoc.createElement('style');
                                                highlightStyle.id = 'temp-highlight-style';
                                                highlightStyle.textContent = '.temp-highlight-element { outline: 3px solid #00ff00 !important; outline-offset: 2px !important; }';
                                                iframeDoc.head.appendChild(highlightStyle);
                                            }
                                            
                                            // Scroll to element
                                            allElements[index].scrollIntoView({ behavior: 'smooth', block: 'center' });
                                            
                                            // Remove highlight after 3 seconds
                                            setTimeout(() => {
                                                if (allElements[index]) {
                                                    allElements[index].classList.remove('temp-highlight-element');
                                                }
                                            }, 3000);
                                        }
                                    } catch (error) {
                                        console.error('[Responsive] Error highlighting element:', error);
                                    }
                                }
                                
                                function generateOverflowReport(overflowElements, format) {
                                    const timestamp = new Date().toLocaleString();
                                    const url = window.location.href;
                                    
                                    // Get device info from first element (all should have same device info)
                                    const deviceInfo = overflowElements.length > 0 ? overflowElements[0] : null;
                                    
                                    if (format === 'text') {
                                        let report = `OVERFLOW ISSUES REPORT\n`;
                                        report += `Generated: ${timestamp}\n`;
                                        report += `URL: ${url}\n`;
                                        
                                        if (deviceInfo) {
                                            report += `Device: ${deviceInfo.deviceName}\n`;
                                            report += `Device Size: ${deviceInfo.deviceWidth}×${deviceInfo.deviceHeight}px (${deviceInfo.deviceOrientation})\n`;
                                            report += `Device Type: ${deviceInfo.isDesktopDevice ? 'Desktop' : 'Mobile/Tablet'}\n`;
                                        }
                                        
                                        report += `Total Issues: ${overflowElements.length}\n\n`;
                                        
                                        overflowElements.forEach((item, i) => {
                                            report += `Issue #${i + 1}:\n`;
                                            report += `  Element: <${item.tagName}>\n`;
                                            if (item.id && typeof item.id === 'string' && item.id.trim()) report += `  ID: #${item.id}\n`;
                                            if (item.classes && typeof item.classes === 'string' && item.classes.trim()) report += `  Classes: .${item.classes.split(' ').join(', .')}\n`;
                                            report += `  Element Dimensions: ${Math.round(item.width)}×${Math.round(item.height)}px\n`;
                                            report += `  Scroll Size: ${item.scrollWidth}×${item.scrollHeight}px\n`;
                                            report += `  Client Size: ${item.clientWidth}×${item.clientHeight}px\n`;
                                            report += `  Device Viewport: ${item.deviceWidth}×${item.deviceHeight}px\n`;
                                            if (item.position !== 'static') report += `  Position: ${item.position}\n`;
                                            if (item.overflow !== 'visible') report += `  Overflow: ${item.overflow}\n`;
                                            report += `\n`;
                                        });
                                        
                                        return report;
                                    } 
                                    
                                    if (format === 'csv') {
                                        let csv = 'Element,ID,Classes,Width,Height,ScrollWidth,ScrollHeight,ClientWidth,ClientHeight,Position,Overflow,OverflowX,OverflowY,MarginLeft,MarginRight,DeviceName,DeviceWidth,DeviceHeight,DeviceOrientation,DeviceType\n';
                                        
                                        overflowElements.forEach(item => {
                                            const cleanId = (item.id && typeof item.id === 'string' && item.id.trim()) ? item.id : '';
                                            const cleanClasses = (item.classes && typeof item.classes === 'string' && item.classes.trim()) ? item.classes.replace(/"/g, '""') : '';
                                            const deviceType = item.isDesktopDevice ? 'Desktop' : 'Mobile/Tablet';
                                            
                                            csv += `"${item.tagName}","${cleanId}","${cleanClasses}",${Math.round(item.width)},${Math.round(item.height)},${item.scrollWidth},${item.scrollHeight},${item.clientWidth},${item.clientHeight},"${item.position}","${item.overflow}","${item.overflowX}","${item.overflowY}","${item.marginLeft}","${item.marginRight}","${item.deviceName}",${item.deviceWidth},${item.deviceHeight},"${item.deviceOrientation}","${deviceType}"\n`;
                                        });
                                        
                                        return csv;
                                    }
                                }
                                
                                function downloadCSV(csvContent, filename) {
                                    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                                    const link = document.createElement('a');
                                    
                                    if (link.download !== undefined) {
                                        const url = URL.createObjectURL(blob);
                                        link.setAttribute('href', url);
                                        link.setAttribute('download', filename);
                                        link.style.visibility = 'hidden';
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                    }
                                }
                                
                                function removeDebugStyles() {
                                    try {
                                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                        if (!iframeDoc) return;
                                        
                                        const debugStyle = iframeDoc.getElementById('debug-borders-style');
                                        if (debugStyle) {
                                            debugStyle.remove();
                                            console.log('[Responsive] Debug styles removed successfully');
                                        }
                                        
                                        // Remove highlight style
                                        const highlightStyle = iframeDoc.getElementById('temp-highlight-style');
                                        if (highlightStyle) {
                                            highlightStyle.remove();
                                        }
                                        
                                        // Clean up data attributes and classes
                                        const markedElements = iframeDoc.querySelectorAll('[data-debug-element], .temp-highlight-element');
                                        markedElements.forEach(element => {
                                            element.removeAttribute('data-debug-element');
                                            element.classList.remove('temp-highlight-element');
                                        });
                                        
                                        // Hide debug info panel and notification tab
                                        debugInfoPanel.style.display = 'none';
                                        const notificationTab = document.getElementById('debug-notification-tab');
                                        if (notificationTab) {
                                            notificationTab.style.display = 'none';
                                        }
                                        
                                    } catch (error) {
                                        console.error('[Responsive] Could not remove debug styles:', error);
                                    }
                                }
                                
                                function refreshDebugAnalysis() {
                                    try {
                                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                        if (!iframeDoc) {
                                            console.log('[Responsive] Iframe document not ready, retrying...');
                                            setTimeout(() => refreshDebugAnalysis(), 200);
                                            return;
                                        }
                                        
                                        console.log('[Responsive] Refreshing debug analysis with current iframe dimensions:', iframe.offsetWidth, 'x', iframe.offsetHeight);
                                        
                                        // Remove existing debug styles first
                                        const existingStyle = iframeDoc.getElementById('debug-borders-style');
                                        if (existingStyle) {
                                            existingStyle.remove();
                                        }
                                        
                                        // Clean up old data attributes
                                        const markedElements = iframeDoc.querySelectorAll('[data-debug-element]');
                                        markedElements.forEach(element => {
                                            element.removeAttribute('data-debug-element');
                                        });
                                        
                                        // Hide current notification
                                        const notificationTab = document.getElementById('debug-notification-tab');
                                        if (notificationTab) {
                                            notificationTab.style.display = 'none';
                                        }
                                        
                                        // Re-run full debug analysis with current dimensions
                                        addDebugStyles();
                                        
                                        console.log('[Responsive] Debug analysis refreshed successfully');
                                    } catch (error) {
                                        console.error('[Responsive] Error refreshing debug analysis:', error);
                                    }
                                }
                                
                                function refreshIframeAndDebug() {
                                    try {
                                        console.log('[Responsive] Starting iframe content refresh and debug analysis...');
                                        
                                        // First, clean up any existing debug state
                                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                        if (iframeDoc) {
                                            // Remove all debug-related elements and styles
                                            const existingStyle = iframeDoc.getElementById('debug-borders-style');
                                            if (existingStyle) {
                                                existingStyle.remove();
                                            }
                                            
                                            const highlightStyle = iframeDoc.getElementById('temp-highlight-style');
                                            if (highlightStyle) {
                                                highlightStyle.remove();
                                            }
                                            
                                            // Clean up all data attributes and classes
                                            const markedElements = iframeDoc.querySelectorAll('[data-debug-element], .temp-highlight-element');
                                            markedElements.forEach(element => {
                                                element.removeAttribute('data-debug-element');
                                                element.classList.remove('temp-highlight-element');
                                            });
                                        }
                                        
                                        // Hide current notification and debug panel
                                        const notificationTab = document.getElementById('debug-notification-tab');
                                        if (notificationTab) {
                                            notificationTab.style.display = 'none';
                                        }
                                        
                                        debugInfoPanel.style.display = 'none';
                                        
                                        // Show temporary "refreshing" message
                                        dimensionsDisplay.textContent = 'Refreshing...';
                                        dimensionsDisplay.style.background = '#8b5cf6';
                                        dimensionsDisplay.style.color = 'white';
                                        
                                        // Force iframe to refresh its content by reloading it
                                        const currentSrc = iframe.src;
                                        iframe.src = 'about:blank'; // Clear first
                                        
                                        // Small delay then reload the actual page
                                        setTimeout(() => {
                                            iframe.src = currentSrc;
                                            
                                                                                         // Wait for iframe to fully load before running debug analysis
                                             const checkIframeReady = () => {
                                                 try {
                                                     const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                                     
                                                     if (iframeDoc && iframeDoc.readyState === 'complete' && iframeDoc.body) {
                                                         console.log('[Responsive] Iframe reloaded successfully, waiting for layout reflow...');
                                                         
                                                         // Restore dimensions display
                                                         updateDimensionsDisplay();
                                                         dimensionsDisplay.style.background = '#1a1a1a';
                                                         dimensionsDisplay.style.color = '#d1d5db';
                                                         
                                                         // CRITICAL: Wait for CSS reflow before analyzing
                                                         // The iframe content has loaded but CSS might still be applying
                                                         setTimeout(() => {
                                                             console.log('[Responsive] Running initial debug analysis...');
                                                             addDebugStyles();
                                                             
                                                             // Run analysis again after additional delay to catch late reflows
                                                             setTimeout(() => {
                                                                 console.log('[Responsive] Running secondary debug analysis to catch layout changes...');
                                                                 
                                                                 // Clean up previous analysis first
                                                                 const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                                                 if (iframeDoc) {
                                                                     const existingStyle = iframeDoc.getElementById('debug-borders-style');
                                                                     if (existingStyle) {
                                                                         existingStyle.remove();
                                                                     }
                                                                     
                                                                     const markedElements = iframeDoc.querySelectorAll('[data-debug-element]');
                                                                     markedElements.forEach(element => {
                                                                         element.removeAttribute('data-debug-element');
                                                                     });
                                                                     
                                                                     // Hide notification temporarily
                                                                     const notificationTab = document.getElementById('debug-notification-tab');
                                                                     if (notificationTab) {
                                                                         notificationTab.style.display = 'none';
                                                                     }
                                                                 }
                                                                 
                                                                 // Run fresh analysis
                                                                 addDebugStyles();
                                                             }, 800); // Additional delay for final analysis
                                                             
                                                         }, 300); // Initial delay for CSS to apply
                                                         
                                                     } else {
                                                         console.log('[Responsive] Iframe still loading, waiting...');
                                                         setTimeout(checkIframeReady, 200);
                                                     }
                                                 } catch (error) {
                                                     console.error('[Responsive] Error checking iframe readiness:', error);
                                                     // Fallback: restore display and try analysis anyway
                                                     updateDimensionsDisplay();
                                                     dimensionsDisplay.style.background = '#1a1a1a';
                                                     dimensionsDisplay.style.color = '#d1d5db';
                                                     
                                                     setTimeout(() => {
                                                         try {
                                                             addDebugStyles();
                                                         } catch (retryError) {
                                                             console.error('[Responsive] Fallback debug analysis failed:', retryError);
                                                         }
                                                     }, 500);
                                                 }
                                             };
                                            
                                            // Start checking for iframe readiness
                                            setTimeout(checkIframeReady, 100);
                                            
                                        }, 50); // Small delay before reloading
                                        
                                    } catch (error) {
                                        console.error('[Responsive] Error in refreshIframeAndDebug:', error);
                                        
                                        // Restore display on error
                                        updateDimensionsDisplay();
                                        dimensionsDisplay.style.background = '#1a1a1a';
                                        dimensionsDisplay.style.color = '#d1d5db';
                                    }
                                }
                                
                                debugBtn.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    toggleDebugBorders();
                                });

                                // Proper cleanup function
                                function cleanup() {
                                    try {
                                        // Remove drag event listeners
                                        document.removeEventListener('mousemove', drag);
                                        document.removeEventListener('mouseup', endDrag);
                                        document.removeEventListener('mouseleave', endDrag);
                                        
                                        // Remove overlay
                                        if (overlay && overlay.parentNode) {
                                            overlay.parentNode.removeChild(overlay);
                                        }
                                        
                                        // Remove debug info panel
                                        if (debugInfoPanel && debugInfoPanel.parentNode) {
                                            debugInfoPanel.parentNode.removeChild(debugInfoPanel);
                                        }
                                        
                                        // Remove notification tab
                                        const notificationTab = document.getElementById('debug-notification-tab');
                                        if (notificationTab && notificationTab.parentNode) {
                                            notificationTab.parentNode.removeChild(notificationTab);
                                        }
                                        
                                        // Restore body scroll
                                        document.body.style.overflow = '';
                                        
                                        // Remove keyboard listener
                                        document.removeEventListener('keydown', handleKeyDown, true);
                                        
                                        console.log('[Content] Responsive simulator cleaned up');
                                    } catch (error) {
                                        console.error('[Content] Error during cleanup:', error);
                                    }
                                }

                                closeBtn.addEventListener('click', cleanup);

                                // Dragging functionality
                                let isDragging = false;
                                let dragStartX = 0;
                                let dragStartY = 0;
                                let containerStartX = 0;
                                let containerStartY = 0;

                                function startDrag(e) {
                                    // Don't start drag if clicking on controls
                                    if (e.target.closest('#responsive-controls-bar') || 
                                        e.target === deviceSelector || 
                                        e.target.closest('select') || 
                                        e.target.closest('button')) {
                                        return;
                                    }
                                    
                                    isDragging = true;
                                    dragStartX = e.clientX;
                                    dragStartY = e.clientY;
                                    
                                    // Get current position
                                    const rect = deviceContainer.getBoundingClientRect();
                                    const overlayRect = overlay.getBoundingClientRect();
                                    containerStartX = rect.left - overlayRect.left;
                                    containerStartY = rect.top - overlayRect.top;
                                    
                                    dragHandle.style.cursor = 'grabbing';
                                    deviceContainer.style.cursor = 'grabbing';
                                    deviceContainer.style.transition = 'none'; // Disable transition during drag
                                    
                                    e.preventDefault();
                                }

                                function drag(e) {
                                    if (!isDragging) return;
                                    
                                    const deltaX = e.clientX - dragStartX;
                                    const deltaY = e.clientY - dragStartY;
                                    
                                    const newX = containerStartX + deltaX;
                                    const newY = containerStartY + deltaY;
                                    
                                    // Apply position with current zoom scale
                                    deviceContainer.style.transform = `translate(${newX}px, ${newY}px) scale(${zoomLevel})`;
                                    deviceContainer.style.transformOrigin = 'top left';
                                    
                                    e.preventDefault();
                                }

                                function endDrag() {
                                    if (!isDragging) return;
                                    
                                    isDragging = false;
                                    dragHandle.style.cursor = 'grab';
                                    deviceContainer.style.cursor = 'move';
                                    deviceContainer.style.transition = 'transform 0.3s ease'; // Re-enable transition
                                }

                                // Add drag event listeners - only on drag handle, not entire container
                                dragHandle.addEventListener('mousedown', startDrag);
                                document.addEventListener('mousemove', drag);
                                document.addEventListener('mouseup', endDrag);
                                document.addEventListener('mouseleave', endDrag);

                                // Keyboard shortcuts handler with proper cleanup
                                function handleKeyDown(e) {
                                    if (!document.getElementById('responsive-overlay')) return;
                                    
                                    // Only handle specific keys and only prevent default for those
                                    const handledKeys = ['Escape', 'r', 'R', '=', '+', '-', 'd', 'D'];
                                    if (!handledKeys.includes(e.key)) return;
                                    
                                    // Prevent shortcuts from affecting underlying page
                                    e.preventDefault();
                                    e.stopPropagation();
                                    
                                    if (e.key === 'Escape') {
                                        cleanup();
                                    } else if (e.key === 'r' || e.key === 'R') {
                                        isRotated = !isRotated;
                                        updateDevice();
                                    } else if (e.key === '=' || e.key === '+') {
                                        if (zoomLevel < 2) {
                                            zoomLevel += 0.1;
                                            updateZoom();
                                        }
                                    } else if (e.key === '-') {
                                        if (zoomLevel > 0.25) {
                                            zoomLevel -= 0.1;
                                            updateZoom();
                                        }
                                    } else if (e.key === 'd' || e.key === 'D') {
                                        toggleDebugBorders();
                                    }
                                }
                                
                                document.addEventListener('keydown', handleKeyDown, true);

                                // Variables for manual resizing
                                let isManuallyResized = false;
                                let manualWidth = 0;
                                let manualHeight = 0;
                                
                                // Variables for custom size memory
                                let customWidth = 0;
                                let customHeight = 0;
                                
                                // Load saved custom dimensions
                                try {
                                    const savedCustomSize = localStorage.getItem('responsiveCustomSize');
                                    if (savedCustomSize) {
                                        const customData = JSON.parse(savedCustomSize);
                                        customWidth = customData.width || 0;
                                        customHeight = customData.height || 0;
                                        console.log(`[Responsive] Loaded saved custom size: ${customWidth}×${customHeight}px`);
                                    }
                                } catch (e) {
                                    console.log('[Responsive] No saved custom size found or error parsing:', e);
                                }

                                // Resize functionality
                                function initializeResize() {
                                    let isResizing = false;
                                    let resizeType = '';
                                    let startX = 0;
                                    let startY = 0;
                                    let startWidth = 0;
                                    let startHeight = 0;

                                    function startResize(e, type) {
                                        isResizing = true;
                                        resizeType = type;
                                        startX = e.clientX;
                                        startY = e.clientY;
                                        startWidth = iframeContainer.offsetWidth;
                                        startHeight = iframeContainer.offsetHeight;
                                        
                                        // Disable transitions during resize
                                        iframeContainer.style.transition = 'none';
                                        
                                        // Change cursor to grabbing for active resize
                                        document.body.style.cursor = 'grabbing';
                                        
                                        // Add visual feedback to indicate manual resize mode
                                        iframeContainer.style.boxShadow = '0 0 0 3px #3b82f6, 0 8px 40px rgba(0,0,0,0.6)';
                                        
                                        // Update dimensions display to show "Resizing..."
                                        dimensionsDisplay.textContent = 'Resizing...';
                                        dimensionsDisplay.style.background = '#3b82f6';
                                        dimensionsDisplay.style.color = 'white';
                                        
                                        e.preventDefault();
                                        e.stopPropagation();
                                    }

                                    function doResize(e) {
                                        if (!isResizing) return;

                                        const deltaX = e.clientX - startX;
                                        const deltaY = e.clientY - startY;

                                        let newWidth = startWidth;
                                        let newHeight = startHeight;

                                        if (resizeType === 'right' || resizeType === 'corner') {
                                            newWidth = Math.max(100, startWidth + deltaX); // Min width 100px
                                        }
                                        if (resizeType === 'bottom' || resizeType === 'corner') {
                                            newHeight = Math.max(100, startHeight + deltaY); // Min height 100px
                                        }

                                        iframeContainer.style.width = newWidth + 'px';
                                        iframeContainer.style.height = newHeight + 'px';

                                        // Update manual dimensions
                                        isManuallyResized = true;
                                        manualWidth = newWidth;
                                        manualHeight = newHeight;

                                        // Show live dimensions during resize
                                        dimensionsDisplay.textContent = `${newWidth} × ${newHeight}px (Resizing...)`;

                                        e.preventDefault();
                                    }

                                    function endResize() {
                                        if (!isResizing) return;
                                        
                                        isResizing = false;
                                        resizeType = '';
                                        
                                        // Restore cursor
                                        document.body.style.cursor = '';
                                        
                                        // Remove visual feedback
                                        iframeContainer.style.boxShadow = '0 8px 40px rgba(0,0,0,0.6)';
                                        
                                        // Restore dimensions display styling
                                        dimensionsDisplay.style.background = '#1a1a1a';
                                        dimensionsDisplay.style.color = '#d1d5db';
                                        
                                        // Re-enable transitions
                                        iframeContainer.style.transition = 'all 0.3s ease';
                                        
                                        // Update dimensions display
                                        updateDimensionsDisplay();
                                        
                                        // CRITICAL: Refresh iframe content and debug analysis after resize
                                        if (debugBordersActive) {
                                            console.log('[Responsive] Manual resize completed, refreshing iframe and debug analysis...');
                                            refreshIframeAndDebug();
                                        }
                                    }

                                    // Add resize event listeners with proper cursor handling
                                    rightResizeHandle.addEventListener('mousedown', (e) => {
                                        rightResizeHandle.style.cursor = 'grabbing';
                                        startResize(e, 'right');
                                    });
                                    
                                    bottomResizeHandle.addEventListener('mousedown', (e) => {
                                        bottomResizeHandle.style.cursor = 'grabbing';
                                        startResize(e, 'bottom');
                                    });
                                    
                                    cornerResizeHandle.addEventListener('mousedown', (e) => {
                                        cornerResizeHandle.style.cursor = 'grabbing';
                                        startResize(e, 'corner');
                                    });

                                    // Global mouse move and up handlers
                                    document.addEventListener('mousemove', doResize);
                                    document.addEventListener('mouseup', () => {
                                        if (isResizing) {
                                            // Restore handle cursors
                                            rightResizeHandle.style.cursor = 'ew-resize';
                                            bottomResizeHandle.style.cursor = 'ns-resize';
                                            cornerResizeHandle.style.cursor = 'nwse-resize';
                                            endResize();
                                        }
                                    });
                                    
                                    // Handle mouse leave to prevent stuck resize
                                    document.addEventListener('mouseleave', () => {
                                        if (isResizing) {
                                            rightResizeHandle.style.cursor = 'ew-resize';
                                            bottomResizeHandle.style.cursor = 'ns-resize';
                                            cornerResizeHandle.style.cursor = 'nwse-resize';
                                            endResize();
                                        }
                                    });
                                }

                                // Function to update dimensions display
                                function updateDimensionsDisplay() {
                                    const width = isManuallyResized ? manualWidth : (isRotated ? window.currentDevice.h : window.currentDevice.w);
                                    const height = isManuallyResized ? manualHeight : (isRotated ? window.currentDevice.w : window.currentDevice.h);
                                    const mode = isManuallyResized ? 'Custom' : (window.currentDevice.name === 'Custom Size' ? 'Custom' : window.currentDevice.name);
                                    dimensionsDisplay.textContent = `${width} × ${height}px (${mode})`;
                                }

                                // Assemble UI
                                overlay.appendChild(deviceContainer);
                                deviceContainer.appendChild(controlsBar);
                                deviceContainer.appendChild(iframeContainer);
                                deviceContainer.appendChild(manualSizeContainer);
                                document.body.appendChild(overlay);
                                document.body.appendChild(debugInfoPanel);
                                document.body.appendChild(notificationTab);

                                // Initialize resize functionality
                                initializeResize();
                                
                                // Debug: Verify panel was added
                                console.log('[Responsive] Debug panel added to DOM:', document.getElementById('debug-info-panel'));

                                // Prevent body scrolling
                                document.body.style.overflow = 'hidden';

                                // Set initial device and update
                                updateManualInputs(); // Populate inputs based on initial device
                                updateDevice();

                                console.log('[Content] Responsive Device Simulator loaded successfully');
                            } catch (error) {
                                console.error('[Content] Responsive Simulator error:', error);
                                // Try to cleanup on error
                                try {
                                    const overlay = document.getElementById('responsive-overlay');
                                    if (overlay) overlay.remove();
                                    document.body.style.overflow = '';
                                } catch (cleanupError) {
                                    console.error('[Content] Cleanup error:', cleanupError);
                                }
                            }
                        }
                    }, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('ResponsiveAction: Script injection error:', chrome.runtime.lastError);
                        } else {
                            console.log('ResponsiveAction: Script injected successfully');
                        }
                        resolve();
                    });
                });
            } catch (error) {
                console.error('Responsive Action error:', error);
            }
            resolve();
        });
    }

    static reset() {
        return new Promise((resolve) => {
            try {
                function resetResponsive() {
                    // Remove responsive overlay
                    const existingOverlay = document.getElementById('responsive-overlay');
                    if (existingOverlay) {
                        existingOverlay.remove();
                    }
                    
                    // Remove any remaining responsive panels
                    const responsivePanels = document.querySelectorAll('.responsive-panel');
                    responsivePanels.forEach(panel => panel.remove());
                    
                    // Restore body scroll
                    document.body.style.overflow = '';
                    
                    console.log('Responsive Action reset completed');
                }

                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        func: resetResponsive
                    }, () => {
                        resolve();
                    });
                });
            } catch (error) {
                console.error('Responsive Action reset error:', error);
                resolve();
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ResponsiveAction;
} else {
    window.ResponsiveAction = ResponsiveAction;
} 