// Custom Shortcut Handler
// Based on proven STM extension architecture for robust cross-platform support

console.log('SEO Time Machines: Custom shortcut handler loading...');

class CustomShortcutHandler {
    constructor() {
        this.isMac = navigator.platform.toLowerCase().includes('mac');
        this.isCapturing = false;
        this.inputElement = null;
        
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.createCustomShortcutUI();
        this.loadSavedShortcut();
        this.setupEventListeners();
    }

    createCustomShortcutUI() {
        // Look for existing custom shortcut input field
        this.inputElement = document.querySelector('#copyElementCustomShortcut');
        
        if (!this.inputElement) {
            console.warn('Custom shortcut input field not found');
            return;
        }

        // Update the existing input field attributes
        this.inputElement.placeholder = 'Click here and press keys to set shortcut...';
        this.inputElement.spellcheck = false;
        this.inputElement.autocomplete = 'off';
        
        // Find or create status element
        let statusElement = document.querySelector('.shortcut-status');
        if (!statusElement) {
            statusElement = document.querySelector('#shortcutStatus');
        }
        
        // If no status element exists, create one
        if (!statusElement) {
            statusElement = document.createElement('div');
            statusElement.className = 'shortcut-status';
            statusElement.id = 'shortcutStatus';
            this.inputElement.parentNode.insertBefore(statusElement, this.inputElement.nextSibling);
        }

        console.log('Custom shortcut handler initialized with existing input field');
    }

    setupEventListeners() {
        if (!this.inputElement) return;

        // Handle keydown events for shortcut capture
        this.inputElement.addEventListener('keydown', (e) => this.handleShortcutInput(e));
        
        // Handle manual text input
        this.inputElement.addEventListener('input', (e) => this.handleManualInput(e));
        
        // Handle focus/blur events
        this.inputElement.addEventListener('focus', () => this.startCapture());
        this.inputElement.addEventListener('blur', () => this.stopCapture());

        // Handle clear button if it exists
        const clearBtn = document.querySelector('.shortcut-clear-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearShortcut());
        }

        // Listen for storage changes
        chrome.storage.onChanged.addListener((changes) => {
            if (changes.copyElementShortcut) {
                this.updateInputValue(changes.copyElementShortcut.newValue || '');
            }
        });
    }

    handleShortcutInput(e) {
        console.log('Shortcut input event:', {
            key: e.key,
            ctrlKey: e.ctrlKey,
            altKey: e.altKey,
            metaKey: e.metaKey,
            shiftKey: e.shiftKey,
            isCapturing: this.isCapturing
        });

        // Only handle during capture mode or if this is a potential shortcut
        if (!this.isCapturing && !(e.ctrlKey || e.altKey || e.metaKey || e.shiftKey)) {
            console.log('Not capturing and no modifiers, ignoring');
            return;
        }

        // Always prevent default for potential shortcuts
        e.preventDefault();
        e.stopPropagation();

        // Allow tab for navigation away from input (don't capture Tab shortcuts)
        if (e.key === 'Tab' && !e.ctrlKey && !e.altKey && !e.metaKey) {
            this.stopCapture();
            return;
        }

        // Handle clearing shortcuts
        if (['Backspace', 'Delete', 'Escape'].includes(e.key) && !e.ctrlKey && !e.altKey && !e.metaKey && !e.shiftKey) {
            this.clearShortcut();
            return;
        }

        // Ignore standalone modifier keys
        if (['Control', 'Alt', 'Meta', 'Shift'].includes(e.key)) {
            return;
        }

        const shortcut = this.formatShortcut(e);
        if (shortcut) {
            this.updateInputValue(shortcut);
            this.validateAndSaveShortcut(shortcut);
            
            // Stop capturing after successful shortcut capture
            setTimeout(() => this.stopCapture(), 100);
        }
    }

    handleManualInput(e) {
        // Handle manual typing of shortcuts
        const value = e.target.value.trim();
        
        if (value) {
            // Validate the manually typed shortcut
            if (this.isValidShortcutString(value)) {
                this.showStatus(`✓ Valid shortcut: ${value}`, 'success');
            } else {
                this.showStatus('Invalid shortcut format', 'warning');
            }
        } else {
            this.showStatus('', 'info');
        }
    }

    isValidShortcutString(shortcut) {
        // Basic validation for manually typed shortcuts
        if (!shortcut || typeof shortcut !== 'string') return false;
        
        const parts = shortcut.split('+');
        if (parts.length < 2) return false;
        
        const modifiers = ['Ctrl', 'Alt', 'Cmd', 'Shift', 'Meta'];
        const hasModifier = parts.some(part => modifiers.includes(part.trim()));
        
        return hasModifier;
    }

    formatShortcut(e) {
        const parts = [];
        
        // Add modifiers in consistent order
        if (e.ctrlKey) parts.push('Ctrl');
        if (e.altKey) parts.push('Alt');
        if (e.metaKey) parts.push('Cmd');
        if (e.shiftKey) parts.push('Shift');
        
        // Must have at least one modifier for global shortcuts
        if (parts.length === 0) {
            this.showStatus('Shortcuts must include at least one modifier key', 'error');
            return null;
        }

        // Special keys mapping
        const specialKeys = {
            'arrowup': '↑',
            'arrowdown': '↓',
            'arrowleft': '←',
            'arrowright': '→',
            'enter': '↵',
            'tab': '⇥',
            'escape': 'Esc',
            'backspace': '⌫',
            'delete': '⌦',
            'space': ' '
        };

        const key = e.key;
        const keyLower = key.toLowerCase();

        // Handle different types of keys
        if (specialKeys[keyLower]) {
            parts.push(specialKeys[keyLower]);
        } else if (/^f(1[0-2]|[1-9])$/i.test(keyLower)) {
            // Function keys
            parts.push(keyLower.toUpperCase());
        } else if (key.length === 1) {
            // Single characters - preserve exact character
            parts.push(key);
        } else {
            this.showStatus('Unsupported key: ' + key, 'error');
            return null;
        }
        
        return parts.join('+');
    }

    async validateAndSaveShortcut(shortcut) {
        // Basic validation
        if (!shortcut) {
            this.showStatus('Invalid shortcut', 'error');
            return;
        }

        // Check for duplicates using global validation system
        if (window.validateShortcut && this.inputElement) {
            const toolName = this.inputElement.id === 'copyElementCustomShortcut' ? 'Copy Element' : 'Color Picker';
            const storageKey = this.inputElement.id === 'copyElementCustomShortcut' ? 'copyElementShortcut' : 'colorpickerShortcut';
            
            const isValid = await window.validateShortcut(
                this.inputElement, 
                shortcut, 
                toolName, 
                storageKey,
                {
                    showInlineErrors: true,
                    allowOverwrite: true
                }
            );
            
            if (!isValid) {
                return; // Validation failed, don't save
            }
        }

        // Check for common browser shortcuts to warn user
        const browserShortcuts = [
            'Ctrl+T', 'Ctrl+W', 'Ctrl+N', 'Ctrl+R', 'Ctrl+L',
            'Cmd+T', 'Cmd+W', 'Cmd+N', 'Cmd+R', 'Cmd+L',
            'F5', 'F11', 'F12', 'Ctrl+F5', 'Cmd+R'
        ];

        if (browserShortcuts.includes(shortcut)) {
            this.showStatus(`Warning: "${shortcut}" is a common browser shortcut`, 'warning');
        } else {
            this.showStatus(`Shortcut set: ${shortcut}`, 'success');
        }

        // Save to storage
        this.saveShortcut(shortcut);
    }

    async saveShortcut(shortcut = null) {
        try {
            const shortcutToSave = shortcut || this.inputElement?.value || '';
            
            await chrome.storage.sync.set({ 
                copyElementShortcut: shortcutToSave 
            });

            if (shortcutToSave) {
                console.log('Custom shortcut saved:', shortcutToSave);
            } else {
                console.log('Custom shortcut cleared');
                this.showStatus('Shortcut cleared', 'info');
            }
        } catch (error) {
            console.error('Error saving custom shortcut:', error);
            this.showStatus('Error saving shortcut', 'error');
        }
    }

    async loadSavedShortcut() {
        try {
            const result = await chrome.storage.sync.get(['copyElementShortcut']);
            const savedShortcut = result.copyElementShortcut || this.getDefaultShortcut();
            this.updateInputValue(savedShortcut);
        } catch (error) {
            console.error('Error loading saved shortcut:', error);
            this.updateInputValue(this.getDefaultShortcut());
        }
    }

    getDefaultShortcut() {
        return this.isMac ? 'Cmd+Shift+C' : 'Ctrl+Shift+C';
    }

    updateInputValue(value) {
        if (this.inputElement) {
            this.inputElement.value = value;
            this.inputElement.setAttribute('data-original', value);
        }
    }

    clearShortcut() {
        this.updateInputValue('');
        this.saveShortcut('');
    }

    startCapture() {
        this.isCapturing = true;
        this.showStatus('🎯 Press your desired key combination...', 'info');
        
        if (this.inputElement) {
            this.inputElement.classList.add('capturing');
            this.inputElement.placeholder = 'Press keys now...';
            this.inputElement.select(); // Select all text for easy replacement
        }
    }

    stopCapture() {
        this.isCapturing = false;
        
        if (this.inputElement) {
            this.inputElement.classList.remove('capturing');
            this.inputElement.placeholder = 'Click here and press keys to set shortcut...';
        }

        // Save whatever is currently in the input after a short delay
        setTimeout(() => {
            if (this.inputElement && this.inputElement.value) {
                this.saveShortcut(this.inputElement.value);
            }
        }, 100);
    }

    showStatus(message, type = 'info') {
        const statusElement = document.querySelector('.shortcut-status');
        if (!statusElement) return;

        statusElement.textContent = message;
        statusElement.className = `shortcut-status status-${type}`;

        // Clear status after delay
        setTimeout(() => {
            statusElement.textContent = '';
            statusElement.className = 'shortcut-status';
        }, 3000);
    }

    // Debug method
    testShortcut(shortcutString) {
        console.log('Testing shortcut format:', shortcutString);
        return shortcutString;
    }
}

// Initialize when DOM is ready
let customShortcutHandler;

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        customShortcutHandler = new CustomShortcutHandler();
    });
} else {
    customShortcutHandler = new CustomShortcutHandler();
}

// Export for debugging
window.customShortcutHandler = customShortcutHandler; 