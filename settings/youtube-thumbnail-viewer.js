// YouTube Thumbnail Viewer - View and download thumbnails from YouTube videos
(function() {
    'use strict';
    
    
    let isEnabled = true; // Default to true
    let debugMode = false;
    
    // YouTube-specific elements
    let thumbnailButton = null;
    let isThumbnailButtonAppended = false;
    let thumbnailObserver = null;
    let currentThumbnailPopup = null;
    
    // Check if we're on a YouTube watch page
    function isYouTubePage() {
        return window.location.hostname.includes('youtube.com') && 
               window.location.pathname.includes('/watch');
    }
    
    // Load settings from localStorage (MAIN world doesn't have Chrome API access)
    function loadSettings() {
        try {
            const storedEnabled = localStorage.getItem('youtubeThumbnailViewerEnabled');
            const storedDebug = localStorage.getItem('debugMode');
            
            // Explicit boolean checks - only default to true if setting doesn't exist
            isEnabled = storedEnabled === null ? true : storedEnabled === 'true';
            debugMode = storedDebug === 'true';
            
            if (debugMode) {
                console.log('[YouTube Thumbnail Viewer] Settings loaded:', {
                    enabled: isEnabled
                });
            }
            
            return isEnabled;
        } catch (error) {
            console.error('[YouTube Thumbnail Viewer] Error loading settings:', error);
            return true; // Default to enabled on error
        }
    }
    
    // === THUMBNAIL BUTTON CREATION & MANAGEMENT ===
    
    function createThumbnailButton() {
        if (thumbnailButton) return; // Already created
        
        thumbnailButton = document.createElement("button");
        thumbnailButton.className = "thumbnailButton ytp-button";
        thumbnailButton.style.cssText = `
            width: auto;
            float: left;
            margin-left: 6px;
            border: 1px solid #374151;
            border-radius: 3px;
            color: #d1d5db;
            padding: 0px 8px;
            font-size: 12px;
            font-weight: 400;
            transition: all 0.2s ease;
        `;
        thumbnailButton.textContent = "Thumbnail";
        thumbnailButton.onclick = showThumbnailViewer;
        
        // Add CSS keyframe animation for pulse effect
        if (!document.getElementById('thumbnail-pulse-animation')) {
            const style = document.createElement('style');
            style.id = 'thumbnail-pulse-animation';
            style.textContent = `
                @keyframes thumbnail-pulse {
                    0% { transform: scale(1); opacity: 1; }
                    50% { transform: scale(1.1); opacity: 0.8; }
                    100% { transform: scale(1); opacity: 1; }
                }
                .thumbnail-pulsing {
                    animation: thumbnail-pulse 0.6s ease-out;
                }
            `;
            document.head.appendChild(style);
        }
        
        // Add hover animation
        thumbnailButton.addEventListener('mouseenter', () => {
            thumbnailButton.style.backgroundColor = '#4b5563';
        });
        thumbnailButton.addEventListener('mouseleave', () => {
            thumbnailButton.style.backgroundColor = '';
        });
        
        // Add pulse animation on button press
        thumbnailButton.addEventListener('mousedown', () => {
            thumbnailButton.classList.add('thumbnail-pulsing');
        });
        
        // Remove animation class when animation completes
        thumbnailButton.addEventListener('animationend', () => {
            thumbnailButton.classList.remove('thumbnail-pulsing');
        });
        
    }
    
    function addThumbnailButton() {
        if (!thumbnailButton || isThumbnailButtonAppended) return;
        
        const ytpRightControls = document.getElementsByClassName("ytp-right-controls")[0];
        if (!ytpRightControls) {
            isThumbnailButtonAppended = false;
            return;
        }
        
        ytpRightControls.prepend(thumbnailButton);
        isThumbnailButtonAppended = true;
        
        if (debugMode) {
            console.log('[YouTube Thumbnail Viewer] Button added to YouTube controls');
        }
    }
    
    function removeThumbnailButton() {
        if (thumbnailButton && thumbnailButton.parentNode) {
            thumbnailButton.parentNode.removeChild(thumbnailButton);
        }
        isThumbnailButtonAppended = false;
    }
    
    // === THUMBNAIL DATA EXTRACTION ===
    
    function extractThumbnailData() {
        try {
            // Get video ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const videoId = urlParams.get('v');
            
            if (!videoId) {
                return null;
            }
            
            // Get video title
            let title = '';
            const titleElement = document.querySelector('h1.ytd-video-primary-info-renderer yt-formatted-string, h1.watch-title-container');
            if (titleElement) {
                title = titleElement.textContent.trim();
            }
            
            // Generate thumbnail URLs (YouTube thumbnail format)
            const thumbnailUrls = {
                hqdefault: `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`,
                maxresdefault: `https://i.ytimg.com/vi/${videoId}/maxresdefault.jpg`,
                sddefault: `https://i.ytimg.com/vi/${videoId}/sddefault.jpg`,
                mqdefault: `https://i.ytimg.com/vi/${videoId}/mqdefault.jpg`
            };
            
            // Try to get the highest quality thumbnail from meta tags first
            const metaThumbnail = document.querySelector('meta[property="og:image"]');
            let primaryThumbnail = metaThumbnail ? metaThumbnail.getAttribute('content') : thumbnailUrls.maxresdefault;
            
            return {
                videoId,
                title: title || 'YouTube Video',
                primaryThumbnail,
                thumbnailUrls
            };
            
        } catch (error) {
            console.error('[YouTube Thumbnail Viewer] Error extracting thumbnail data:', error);
            return null;
        }
    }
    
    // === THUMBNAIL VIEWER POPUP ===
    
    function showThumbnailViewer() {
        // Close existing popup if any
        closeThumbnailPopup();
        
        // Extract thumbnail data
        const thumbnailData = extractThumbnailData();
        if (!thumbnailData) {
            showThumbnailError('Unable to extract thumbnail data from this YouTube video.');
            return;
        }
        
        // Create and show popup
        createThumbnailPopup(thumbnailData);
    }
    
    function createThumbnailPopup(thumbnailData) {
        // Create popup container with universal design standards
        const popup = document.createElement('div');
        popup.className = 'youtube-thumbnail-viewer-popup';
        popup.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #0a0a0a;
            border: 2px solid #7C3AED;
            border-radius: 12px;
            padding: 0;
            width: 700px;
            max-height: 90vh;
            z-index: 9999999;
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            color: #d1d5db;
            resize: both;
            overflow: hidden;
            min-width: 500px;
            min-height: 400px;
        `;
        
        // Create header with dark theme
        const header = document.createElement('div');
        header.style.cssText = `
            background: #1a1a1a;
            color: white;
            padding: 12px 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
            user-select: none;
            font-size: 16px;
            font-weight: 700;
        `;
        
        const titleText = document.createElement('span');
        titleText.textContent = 'YouTube Thumbnail Viewer'; // NO EMOJIS
        
        // Close button
        const closeButton = document.createElement('button');
        closeButton.textContent = '✕';
        closeButton.style.cssText = `
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        `;
        
        closeButton.onclick = closeThumbnailPopup;
        
        header.appendChild(titleText);
        header.appendChild(closeButton);
        
        // Create content area
        const content = document.createElement('div');
        content.style.cssText = `
            padding: 20px 20px 30px 20px;
            max-height: calc(90vh - 60px);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        `;
        
        // Video title
        const videoTitle = document.createElement('div');
        videoTitle.style.cssText = `
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
            word-wrap: break-word;
            text-align: center;
        `;
        videoTitle.textContent = thumbnailData.title;
        
        // Primary thumbnail display
        const thumbnailContainer = document.createElement('div');
        thumbnailContainer.style.cssText = `
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 15px;
        `;
        
        const thumbnailImg = document.createElement('img');
        thumbnailImg.src = thumbnailData.primaryThumbnail;
        thumbnailImg.style.cssText = `
            max-width: 100%;
            max-height: 300px;
            border: 1px solid #374151;
            border-radius: 8px;
            cursor: pointer;
            transition: opacity 0.2s ease;
        `;
        
        thumbnailImg.onclick = () => window.open(thumbnailData.primaryThumbnail, '_blank');
        
        // Instructional text for saving
        const instructionText = document.createElement('div');
        instructionText.textContent = 'Right click this image to save or simply drag it to your desktop...';
        instructionText.style.cssText = `
            font-size: 14px;
            color: #9ca3af;
            text-align: center;
            font-style: italic;
            margin-top: 10px;
        `;
        
        // Quality options
        const qualityOptions = document.createElement('div');
        qualityOptions.style.cssText = `
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 15px;
        `;
        
        Object.entries(thumbnailData.thumbnailUrls).forEach(([quality, url]) => {
            const qualityButton = document.createElement('button');
            qualityButton.textContent = quality.toUpperCase();
            qualityButton.style.cssText = `
                background: #7C3AED;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
                font-weight: 600;
                transition: background-color 0.2s ease;
            `;
            
            qualityButton.onclick = () => {
                thumbnailImg.src = url;
            };
            
            qualityOptions.appendChild(qualityButton);
        });
        
        // Copy URL button
        const copyButton = document.createElement('button');
        copyButton.textContent = 'Copy URL';
        copyButton.style.cssText = `
            background: #059669;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: background-color 0.2s ease;
            margin-top: 10px;
        `;
        
        copyButton.onclick = () => {
            navigator.clipboard.writeText(thumbnailImg.src).then(() => {
                const originalText = copyButton.textContent;
                copyButton.textContent = 'Copied!';
                setTimeout(() => {
                    copyButton.textContent = originalText;
                }, 1500);
            });
        };
        
        // Assemble popup
        thumbnailContainer.appendChild(thumbnailImg);
        thumbnailContainer.appendChild(instructionText);
        thumbnailContainer.appendChild(qualityOptions);
        thumbnailContainer.appendChild(copyButton);
        
        content.appendChild(videoTitle);
        content.appendChild(thumbnailContainer);
        
        popup.appendChild(header);
        popup.appendChild(content);
        
        // Make draggable
        makeThumbnailPopupDraggable(popup, header);
        
        // ESC key handler
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                closeThumbnailPopup();
            }
        };
        document.addEventListener('keydown', handleEscape);
        popup._escapeHandler = handleEscape;
        
        // Add to page
        document.body.appendChild(popup);
        currentThumbnailPopup = popup;
        
        // Handle image load error
        thumbnailImg.onerror = () => {
            thumbnailImg.style.display = 'none';
            const errorMsg = document.createElement('div');
            errorMsg.textContent = 'Unable to load thumbnail image';
            errorMsg.style.cssText = `
                color: #ef4444;
                text-align: center;
                padding: 20px;
                font-style: italic;
            `;
            thumbnailContainer.insertBefore(errorMsg, thumbnailImg);
        };
    }
    
    function makeThumbnailPopupDraggable(element, handle) {
        let isDragging = false;
        let dragStartX = 0;
        let dragStartY = 0;
        let initialTransform = element.style.transform;
        const DRAG_THRESHOLD = 5; // pixels
        
        handle.onmousedown = dragMouseDown;
        
        function dragMouseDown(e) {
            e = e || window.event;
            
            // Store initial mouse position
            dragStartX = e.clientX;
            dragStartY = e.clientY;
            isDragging = false;
            
            // Store initial transform for restoration
            initialTransform = element.style.transform;
            
            // Set up event listeners
            document.onmouseup = closeDragElement;
            document.onmousemove = checkDragThreshold;
        }
        
        function checkDragThreshold(e) {
            e = e || window.event;
            
            // Calculate distance moved
            const deltaX = Math.abs(e.clientX - dragStartX);
            const deltaY = Math.abs(e.clientY - dragStartY);
            
            // Only start dragging if moved beyond threshold
            if (!isDragging && (deltaX > DRAG_THRESHOLD || deltaY > DRAG_THRESHOLD)) {
                isDragging = true;
                
                // Convert to absolute positioning for dragging
                const rect = element.getBoundingClientRect();
                element.style.top = rect.top + 'px';
                element.style.left = rect.left + 'px';
                element.style.transform = 'none';
                
                // Switch to actual drag handler
                document.onmousemove = elementDrag;
                
                // Prevent text selection during drag
                document.body.style.userSelect = 'none';
            }
        }
        
        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            
            if (!isDragging) return;
            
            // Calculate new position
            const deltaX = e.clientX - dragStartX;
            const deltaY = e.clientY - dragStartY;
            
            const rect = element.getBoundingClientRect();
            const newTop = rect.top + deltaY;
            const newLeft = rect.left + deltaX;
            
            // Keep within viewport bounds
            const maxTop = window.innerHeight - element.offsetHeight;
            const maxLeft = window.innerWidth - element.offsetWidth;
            
            element.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
            element.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
            
            // Update drag start position for next move
            dragStartX = e.clientX;
            dragStartY = e.clientY;
        }
        
        function closeDragElement() {
            // Clean up event listeners
            document.onmouseup = null;
            document.onmousemove = null;
            
            // Restore text selection
            document.body.style.userSelect = '';
            
            // Reset drag state
            isDragging = false;
        }
    }
    
    function closeThumbnailPopup() {
        if (currentThumbnailPopup) {
            // Cleanup event listener
            if (currentThumbnailPopup._escapeHandler) {
                document.removeEventListener('keydown', currentThumbnailPopup._escapeHandler);
            }
            
            currentThumbnailPopup.remove();
            currentThumbnailPopup = null;
        }
    }
    
    function showThumbnailError(message) {
        // Simple error notification
        const errorDiv = document.createElement('div');
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ef4444;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10000000;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            errorDiv.remove();
        }, 3000);
    }
    
    // === MUTATION OBSERVER MANAGEMENT ===
    
    function setupThumbnailObserver() {
        if (thumbnailObserver) return;
        
        thumbnailObserver = new MutationObserver((mutations) => {
            let shouldCheck = false;
            for (let mutation of mutations) {
                if (mutation.type === 'childList') {
                    shouldCheck = true;
                    break;
                }
            }
            
            if (shouldCheck && isEnabled && isYouTubePage()) {
                const ytpRightControls = document.getElementsByClassName("ytp-right-controls")[0];
                
                if (ytpRightControls && !isThumbnailButtonAppended && thumbnailButton) {
                    // Verify no existing thumbnail buttons before adding
                    const existingButtons = document.querySelectorAll('.thumbnailButton');
                    if (existingButtons.length === 0) {
                        addThumbnailButton();
                    } else {
                        // Button exists but state is inconsistent - fix state
                        isThumbnailButtonAppended = true;
                    }
                }
            } else if (shouldCheck && !isEnabled) {
                // Feature is disabled - remove any existing buttons
                const existingButtons = document.querySelectorAll('.thumbnailButton');
                existingButtons.forEach(btn => {
                    if (btn.parentNode) {
                        btn.parentNode.removeChild(btn);
                    }
                });
                isThumbnailButtonAppended = false;
            }
        });
        
        thumbnailObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    function removeThumbnailObserver() {
        if (thumbnailObserver) {
            thumbnailObserver.disconnect();
            thumbnailObserver = null;
        }
    }
    
    // === INITIALIZATION & CLEANUP ===
    
    function initializeYouTubeThumbnailViewer() {
        if (!isYouTubePage()) return;
        
        // Force cleanup of any existing state before initializing
        if (thumbnailButton || isThumbnailButtonAppended) {
            if (debugMode) {
                console.log('[YouTube Thumbnail Viewer] Cleaning up existing state before initialization');
            }
            disableYouTubeThumbnailViewer();
        }
        
        createThumbnailButton();
        addThumbnailButton();
        setupThumbnailObserver();
        
        if (debugMode) {
            console.log('[YouTube Thumbnail Viewer] Functionality initialized');
        }
    }
    
    function disableYouTubeThumbnailViewer() {
        // Force state reset
        thumbnailButton = null;
        isThumbnailButtonAppended = false;
        
        // Remove button
        removeThumbnailButton();
        
        // Force remove any remaining thumbnail buttons
        document.querySelectorAll('.thumbnailButton').forEach(btn => {
            if (btn.parentNode) {
                btn.parentNode.removeChild(btn);
            }
        });
        
        // Close popup and clean up observer
        closeThumbnailPopup();
        removeThumbnailObserver();
        
        // Reset popup reference
        currentThumbnailPopup = null;
        
        if (debugMode) {
            console.log('[YouTube Thumbnail Viewer] Functionality disabled and cleaned up');
        }
    }
    
    // === SETTINGS CHANGE LISTENER ===
    
    // Settings change listener via localStorage events
    window.addEventListener('storage', (e) => {
        if (e.key === 'youtubeThumbnailViewerEnabled' || e.key === 'debugMode') {
            const wasEnabled = isEnabled;
            loadSettings();
            
            if (debugMode) {
                console.log('[YouTube Thumbnail Viewer] Settings updated:', { enabled: isEnabled });
            }
            
            // Handle enable/disable changes
            if (wasEnabled !== isEnabled) {
                if (isEnabled) {
                    initializeYouTubeThumbnailViewer();
                } else {
                    disableYouTubeThumbnailViewer();
                }
            }
        }
    });
    
    // === MAIN INITIALIZATION ===
    
    function initialize() {
        if (!isYouTubePage()) {
            console.log('[YouTube Thumbnail Viewer] Not a YouTube watch page, exiting');
            return;
        }
        
        const shouldRun = loadSettings();
        
        if (shouldRun) {
            console.log('[YouTube Thumbnail Viewer] Starting feature');
            initializeYouTubeThumbnailViewer();
        } else {
            console.log('[YouTube Thumbnail Viewer] Feature disabled in settings');
        }
    }
    
    // Start when DOM is ready with a small delay to ensure settings are loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(initialize, 100); // Small delay to ensure settings bridge runs first
        });
    } else {
        setTimeout(initialize, 100); // Small delay to ensure settings bridge runs first
    }
    
    // Handle page navigation on YouTube (SPA)
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            if (debugMode) {
                console.log('[YouTube Thumbnail Viewer] Page navigation detected');
            }
            
            // Re-initialize on navigation
            if (isEnabled) {
                setTimeout(() => {
                    if (isYouTubePage()) {
                        initializeYouTubeThumbnailViewer();
                    } else {
                        disableYouTubeThumbnailViewer();
                    }
                }, 500);
            }
        }
    }).observe(document, { subtree: true, childList: true });
})();