// Copy Replace Functionality
// Press Ctrl+Shift+V (Cmd+Shift+V on Mac) to replace domain in clipboard URL and paste

(function() {
    'use strict';
    
    console.log('CopyReplace: Script loaded on', window.location.href);
    
    // Check if Global Shortcut Manager is handling Copy Replace
    if (window.globalShortcutManager && window.globalShortcutManager.shortcuts && window.globalShortcutManager.shortcuts['copyReplace']) {
        console.log('CopyReplace: Global Shortcut Manager is handling Copy Replace, disabling standalone version');
        return; // Exit early to prevent conflicts
    }
    
    let isEnabled = true; // Default to enabled
    let debugMode = false;
    let sourceDomain = '';
    let targetDomain = '';
    let isMac = navigator.platform.toLowerCase().includes('mac');
    
    // Initialize settings
    async function loadSettings() {
        try {
            // Safety check for chrome extension context
            if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.sync) {
                console.log('CopyReplace: Chrome extension APIs not available, using defaults');
                isEnabled = true;
                debugMode = false;
                sourceDomain = '';
                targetDomain = '';
                if (isEnabled) {
                    initializeFeature();
                }
                return;
            }
            
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            
            // Check for chrome runtime errors
            if (chrome.runtime.lastError) {
                console.log('CopyReplace: Chrome runtime error:', chrome.runtime.lastError.message);
                throw new Error(chrome.runtime.lastError.message);
            }
            
            const settings = result.gmbExtractorSettings || {};
            
            isEnabled = settings.copyReplaceEnabled !== false;
            debugMode = settings.debugMode || false;
            sourceDomain = settings.copyReplaceSourceDomain || '';
            targetDomain = settings.copyReplaceTargetDomain || '';
            
            // Always log settings loading for debugging
            console.log('CopyReplace: Settings loaded', {
                enabled: isEnabled,
                debugMode: debugMode,
                sourceDomain: sourceDomain,
                targetDomain: targetDomain
            });
            
            if (isEnabled) {
                initializeFeature();
            }
        } catch (error) {
            console.error('CopyReplace: Error loading settings:', error);
            // Use defaults if loading fails
            isEnabled = true;
            debugMode = false;
            sourceDomain = '';
            targetDomain = '';
            if (isEnabled) {
                initializeFeature();
            }
        }
    }
    
    // Feature implementation
    function initializeFeature() {
        if (!isEnabled) return;
        
        // Setup keyboard listeners
        setupKeyboardListeners();
        
        // Always log initialization for debugging
        console.log('CopyReplace: Initialized with domains:', {
            enabled: isEnabled,
            source: sourceDomain,
            target: targetDomain,
            isMac: isMac
        });
    }
    
    function disableFeature() {
        // Remove keyboard listeners (they check enabled state internally)
        if (debugMode) {
            console.log('CopyReplace: Disabled');
        }
    }
    
    function setupKeyboardListeners() {
        // Keyboard listener is always attached, but checks enabled state in handler
        document.addEventListener('keydown', handleKeyDown, true);
    }
    
    function handleKeyDown(event) {
        if (!isEnabled) return;
        
        // Log keyboard events when shortcut keys are involved for debugging
        if (event.shiftKey && (event.ctrlKey || event.metaKey) && event.code === 'KeyV') {
            console.log('CopyReplace: Potential shortcut keydown event detected:', {
                target: event.target.tagName,
                shiftKey: event.shiftKey,
                ctrlKey: event.ctrlKey,
                metaKey: event.metaKey,
                code: event.code,
                isEnabled: isEnabled,
                isMac: isMac
            });
        }
        
        // Only exclude extension popups or chrome pages
        if (window.location.protocol === 'chrome-extension:' || 
            window.location.protocol === 'chrome:') {
            console.log('CopyReplace: Skipping - extension or chrome page');
            return;
        }
        
        // Check for Ctrl+Shift+V (Windows/Linux) or Cmd+Shift+V (Mac)
        const isCopyReplaceShortcut = event.shiftKey && 
            ((isMac && event.metaKey && event.code === 'KeyV') ||
             (!isMac && event.ctrlKey && event.code === 'KeyV'));
        
        if (isCopyReplaceShortcut) {
            // Always log shortcut detection for debugging
            console.log('CopyReplace: Keyboard shortcut DETECTED!', {
                enabled: isEnabled,
                sourceDomain: sourceDomain,
                targetDomain: targetDomain,
                activeElement: {
                    tag: event.target.tagName,
                    type: event.target.type,
                    id: event.target.id,
                    class: event.target.className,
                    isContentEditable: event.target.isContentEditable
                },
                event: {
                    shiftKey: event.shiftKey,
                    ctrlKey: event.ctrlKey,
                    metaKey: event.metaKey,
                    code: event.code
                }
            });
            
            // Check if domains are configured
            if (!sourceDomain || !targetDomain) {
                showNotification('⚠️ Copy Replace: Please configure source and target domains in settings');
                return;
            }
            
            // Prevent browser's default paste behavior
            event.preventDefault();
            event.stopImmediatePropagation();
            
            console.log('CopyReplace: Processing copy replace operation...');
            // Store the active element for pasting
            const targetElement = event.target;
            handleCopyReplace(targetElement);
        }
    }
    
    async function handleCopyReplace(targetElement) {
        try {
            // Check clipboard permissions
            if (!navigator.clipboard) {
                showNotification('❌ Copy Replace: Clipboard API not available');
                return;
            }
            
            // Read clipboard content
            const clipboardText = await navigator.clipboard.readText();
            
            if (!clipboardText) {
                showNotification('⚠️ Copy Replace: Clipboard is empty');
                return;
            }
            
            // Always log clipboard operations for debugging
            console.log('CopyReplace: Original clipboard content:', {
                length: clipboardText.length,
                preview: clipboardText.substring(0, 100),
                containsSourceDomain: clipboardText.includes(sourceDomain)
            });
            
            // Check if clipboard contains a URL with the source domain
            if (!clipboardText.includes(sourceDomain)) {
                showNotification(`⚠️ Copy Replace: Clipboard doesn't contain "${sourceDomain}"`);
                return;
            }
            
            // Perform domain replacement
            const replacedText = replaceUrlDomain(clipboardText);
            
            if (replacedText === clipboardText) {
                showNotification('⚠️ Copy Replace: No changes made to URL');
                return;
            }
            
            // Write back to clipboard
            await navigator.clipboard.writeText(replacedText);
            
            // Log target element for paste operation
            console.log('CopyReplace: Target element for paste:', {
                tag: targetElement?.tagName,
                type: targetElement?.type,
                id: targetElement?.id,
                isContentEditable: targetElement?.isContentEditable,
                hasFocus: targetElement === document.activeElement
            });
            
            // Simulate paste operation
            await simulatePaste(replacedText, targetElement);
            
            // Always log replacement result for debugging
            console.log('CopyReplace: Domain replacement result:', {
                originalLength: clipboardText.length,
                replacedLength: replacedText.length,
                changed: replacedText !== clipboardText,
                originalPreview: clipboardText.substring(0, 100),
                replacedPreview: replacedText.substring(0, 100)
            });
            
            // Success - no notification needed, operation completed silently
            
        } catch (error) {
            console.error('CopyReplace: Error during replace operation:', error);
            
            if (error.name === 'NotAllowedError') {
                showNotification('❌ Copy Replace: Clipboard access denied. Please allow clipboard permissions.');
            } else {
                showNotification('❌ Copy Replace: Operation failed');
            }
        }
    }
    
    function replaceUrlDomain(text) {
        try {
            // Handle various URL formats and replace domain while preserving paths
            
            // Clean up domains for comparison
            const cleanSourceDomain = sourceDomain.replace(/^https?:\/\//, '').replace(/\/$/, '');
            let cleanTargetDomain = targetDomain.replace(/^https?:\/\//, '').replace(/\/$/, '');
            
            // Pattern to match URLs with the source domain
            const urlPattern = new RegExp(`(https?://)?(www\\.)?${escapeRegExp(cleanSourceDomain)}(/.*)?(\\?.*?)?(#.*)?`, 'gi');
            
            const replacedText = text.replace(urlPattern, (match, protocol, www, path, query, hash) => {
                // Construct the replacement URL - ALWAYS start with HTTPS
                let replacement = 'https://' + cleanTargetDomain;
                
                // Add path, query, and hash if they exist
                if (path) replacement += path;
                if (query) replacement += query;
                if (hash) replacement += hash;
                
                return replacement;
            });
            
            return replacedText;
            
        } catch (error) {
            console.error('CopyReplace: Error during domain replacement:', error);
            return text; // Return original text if replacement fails
        }
    }
    
    function escapeRegExp(string) {
        // Escape special regex characters
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    async function simulatePaste(text, targetElement) {
        try {
            // Use the provided target element or fall back to the currently focused element
            const activeElement = targetElement || document.activeElement;
            
            console.log('CopyReplace: simulatePaste called with:', {
                text: text.substring(0, 50) + '...',
                targetProvided: !!targetElement,
                activeElementTag: activeElement?.tagName,
                activeElementType: activeElement?.type
            });
            
            if (activeElement && (activeElement.tagName === 'INPUT' || 
                                 activeElement.tagName === 'TEXTAREA' || 
                                 activeElement.isContentEditable)) {
                
                if (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA') {
                    // Handle input/textarea
                    const start = activeElement.selectionStart;
                    const end = activeElement.selectionEnd;
                    const currentValue = activeElement.value;
                    
                    activeElement.value = currentValue.substring(0, start) + text + currentValue.substring(end);
                    activeElement.selectionStart = activeElement.selectionEnd = start + text.length;
                    
                    // Trigger input event
                    activeElement.dispatchEvent(new Event('input', { bubbles: true }));
                    console.log('CopyReplace: Successfully pasted into INPUT/TEXTAREA:', {
                        tag: activeElement.tagName,
                        type: activeElement.type,
                        textLength: text.length
                    });
                    
                } else if (activeElement.isContentEditable) {
                    // Handle contenteditable
                    const selection = window.getSelection();
                    if (selection.rangeCount > 0) {
                        const range = selection.getRangeAt(0);
                        range.deleteContents();
                        range.insertNode(document.createTextNode(text));
                        range.collapse(false);
                        selection.removeAllRanges();
                        selection.addRange(range);
                        console.log('CopyReplace: Successfully pasted into contenteditable element');
                    } else {
                        console.log('CopyReplace: No selection range available for contenteditable');
                    }
                }
                
            } else {
                console.log('CopyReplace: No suitable paste target found:', {
                    activeElement: activeElement?.tagName,
                    isContentEditable: activeElement?.isContentEditable,
                    note: 'Text is still in clipboard for manual paste'
                });
            }
            
        } catch (error) {
            console.error('CopyReplace: Error during paste simulation:', error);
        }
    }
    
    function showNotification(message) {
        // Remove any existing copy replace notifications
        document.querySelectorAll('.copy-replace-notification').forEach(notification => notification.remove());
        
        // Get center of screen as fallback position
        let notificationX = window.innerWidth / 2;
        let notificationY = 100; // Top of screen
        
        // Add CSS animation if not already present
        if (!document.getElementById('copy-replace-styles')) {
            const styles = document.createElement('style');
            styles.id = 'copy-replace-styles';
            styles.textContent = `
                @keyframes copyReplaceFadeIn {
                    from { 
                        opacity: 0; 
                        transform: translateX(-50%) translateY(-20px) scale(0.9); 
                    }
                    to { 
                        opacity: 1; 
                        transform: translateX(-50%) translateY(0) scale(1); 
                    }
                }
                @keyframes copyReplaceFadeOut {
                    from { 
                        opacity: 1; 
                        transform: translateX(-50%) translateY(0) scale(1); 
                    }
                    to { 
                        opacity: 0; 
                        transform: translateX(-50%) translateY(-20px) scale(0.9); 
                    }
                }
            `;
            document.head.appendChild(styles);
        }
        
        // Create notification with consistent styling
        const notification = document.createElement('div');
        notification.className = 'copy-replace-notification';
        notification.style.cssText = `
            position: fixed;
            left: ${notificationX}px;
            top: ${notificationY}px;
            transform: translateX(-50%);
            z-index: 10000000;
            background: rgba(10, 10, 10, 0.95);
            color: #d1d5db;
            padding: 12px 24px;
            border-radius: 8px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            border: 2px solid #7C3AED;
            pointer-events: none;
            animation: copyReplaceFadeIn 0.3s ease-out;
            white-space: nowrap;
            max-width: 500px;
        `;
        
        // Set message with appropriate color
        if (message.startsWith('✓')) {
            notification.style.color = '#10b981';
            notification.style.borderColor = '#10b981';
        } else if (message.startsWith('⚠️')) {
            notification.style.color = '#f59e0b';
            notification.style.borderColor = '#f59e0b';
        } else if (message.startsWith('❌')) {
            notification.style.color = '#ef4444';
            notification.style.borderColor = '#ef4444';
        }
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // Auto-remove after 4 seconds with fade out animation
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'copyReplaceFadeOut 0.3s ease-in';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 4000);
    }
    
    // Listen for settings changes - MANDATORY
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        try {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                if (message.action === 'updateSettings' && message.settings) {
                    const wasEnabled = isEnabled;
                    const oldSourceDomain = sourceDomain;
                    const oldTargetDomain = targetDomain;
                    
                    isEnabled = message.settings.copyReplaceEnabled !== false;
                    debugMode = message.settings.debugMode || false;
                    sourceDomain = message.settings.copyReplaceSourceDomain || '';
                    targetDomain = message.settings.copyReplaceTargetDomain || '';
                    
                    // Always log settings updates for debugging
                    console.log('CopyReplace: Settings updated via runtime message', {
                        enabled: isEnabled,
                        debugMode: debugMode,
                        sourceDomain: sourceDomain,
                        targetDomain: targetDomain,
                        wasEnabled: wasEnabled,
                        oldSourceDomain: oldSourceDomain,
                        oldTargetDomain: oldTargetDomain
                    });
                    
                    if (wasEnabled !== isEnabled) {
                        if (isEnabled) {
                            initializeFeature();
                        } else {
                            disableFeature();
                        }
                    } else if (isEnabled && (oldSourceDomain !== sourceDomain || oldTargetDomain !== targetDomain)) {
                        // Domains changed, no action needed - they're used dynamically
                        if (debugMode) {
                            console.log('CopyReplace: Domain configuration updated');
                        }
                    }
                }
            });
        } catch (error) {
            console.log('CopyReplace: Error setting up runtime message listener:', error.message);
        }
    } else {
        console.log('CopyReplace: Chrome runtime APIs not available');
    }
    
    // Initialize on page load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadSettings);
    } else {
        loadSettings();
    }
})();