// DOM Snapshot Utility
// Captures initial page state and provides restoration functionality

class DOMSnapshotUtility {
    constructor() {
        this.initialSnapshot = null;
        this.snapshotTaken = false;
        this.isSnapshotting = false;
        
        this.log('DOM Snapshot Utility initialized');
        
        // Take initial snapshot when page loads
        this.initializeSnapshot();
    }

    log(message, ...args) {
        // Always call console.log - global logging utility controls visibility
        console.log(`[DOM Snapshot]`, message, ...args);
    }

    error(message, ...args) {
        console.error(`[DOM Snapshot ERROR]`, message, ...args);
    }

    async initializeSnapshot() {
        try {
            // Wait for page to be fully loaded
            if (document.readyState === 'loading') {
                this.log('Document still loading, waiting for DOMContentLoaded');
                document.addEventListener('DOMContentLoaded', () => this.takeInitialSnapshot());
            } else if (document.readyState === 'interactive') {
                this.log('Document interactive, waiting for full load');
                window.addEventListener('load', () => this.takeInitialSnapshot());
            } else {
                this.log('Document already loaded, taking snapshot immediately');
                this.takeInitialSnapshot();
            }
        } catch (error) {
            this.error('Error initializing snapshot:', error);
        }
    }

    async takeInitialSnapshot() {
        if (this.isSnapshotting || this.snapshotTaken) {
            this.log('Snapshot already taken or in progress');
            return;
        }

        try {
            this.isSnapshotting = true;
            this.log('Taking initial DOM snapshot...');

            // Wait for DOM, scripts, and styles to be fully ready
            await this.waitForDOMReady();

            this.initialSnapshot = {
                // Core document structure
                documentHTML: document.documentElement.outerHTML,
                
                // Document properties
                title: document.title,
                designMode: document.designMode,
                
                // Body content and attributes
                bodyHTML: document.body ? document.body.outerHTML : null,
                bodyClasses: document.body ? Array.from(document.body.classList) : [],
                
                // Head content
                headHTML: document.head ? document.head.outerHTML : null,
                
                // Style information
                computedStyles: this.captureComputedStyles(),
                
                // Event listeners state (we'll track what we can)
                eventListenersState: this.captureEventListenersState(),
                
                // Window properties
                windowProperties: this.captureWindowProperties(),
                
                // Timestamp
                timestamp: Date.now(),
                
                // Custom data attributes
                customDataAttributes: this.captureCustomDataAttributes()
            };

            this.snapshotTaken = true;
            this.log('Initial DOM snapshot completed successfully', {
                timestamp: this.initialSnapshot.timestamp,
                bodyClasses: this.initialSnapshot.bodyClasses,
                designMode: this.initialSnapshot.designMode
            });

        } catch (error) {
            this.error('Failed to take initial snapshot:', error);
        } finally {
            this.isSnapshotting = false;
        }
    }

    captureComputedStyles() {
        try {
            const styles = {};
            
            // Capture body styles
            if (document.body) {
                const bodyStyles = window.getComputedStyle(document.body);
                styles.body = {
                    overflow: bodyStyles.overflow,
                    position: bodyStyles.position,
                    transform: bodyStyles.transform,
                    filter: bodyStyles.filter,
                    opacity: bodyStyles.opacity
                };
            }

            // Capture document element styles
            if (document.documentElement) {
                const docStyles = window.getComputedStyle(document.documentElement);
                styles.documentElement = {
                    overflow: docStyles.overflow,
                    position: docStyles.position,
                    transform: docStyles.transform
                };
            }

            return styles;
        } catch (error) {
            this.error('Error capturing computed styles:', error);
            return {};
        }
    }

    captureEventListenersState() {
        try {
            // Track global event listeners that Quick Actions might add
            const state = {
                hasKeydownListeners: !!document.onkeydown,
                hasClickListeners: !!document.onclick,
                hasContextMenuListeners: !!document.oncontextmenu,
                documentEventListeners: []
            };

            return state;
        } catch (error) {
            this.error('Error capturing event listeners state:', error);
            return {};
        }
    }

    captureWindowProperties() {
        try {
            const properties = {};
            
            // Capture Quick Action related properties
            const quickActionProps = [
                'HtagsAction', 'ShowLinksAction', 'ShowHiddenAction', 'KeywordAction',
                'BoldFromSerpAction', 'SchemaAction', 'ImagesAction', 'MetadataAction',
                'PageStructureAction', 'YouTubeEmbedScraperAction', 'QuickEditAction',
                'ColorPaletteExtractorAction', 'ColorPickerAction', 'FontInspectorAction', 'FontStylesAction', 'CSSClassInspectorAction',
                'RobotsTxtAction', 'MOZDACheckerAction', 'XMLSitemapCheckerAction',
                'ScreenReaderSimulationAction', 'WaybackMachineAction', 'PageKeywordDensityAction',
                'LinkCheckerAction', 'CopyElementAction', 'CleanSelectedContentAction', 'CleanAndTitleAction', 'HTMLCleanerAction',
                'LinksExtractorAction', 'BulkLinkOpenAction', 'ResponsiveAction',
                // Drag Select Links properties
                'DragSelectLinksActive', 'dragSelectLinksCleanup',
                // Alert popup properties
                'alertPopupHandler', 'alertPopupInstance'
            ];

            quickActionProps.forEach(prop => {
                properties[prop] = typeof window[prop] !== 'undefined';
            });

            return properties;
        } catch (error) {
            this.error('Error capturing window properties:', error);
            return {};
        }
    }

    captureCustomDataAttributes() {
        try {
            const attributes = {};
            
            // Capture data attributes on body and document element
            if (document.body) {
                attributes.body = {};
                Array.from(document.body.attributes).forEach(attr => {
                    if (attr.name.startsWith('data-')) {
                        attributes.body[attr.name] = attr.value;
                    }
                });
            }

            if (document.documentElement) {
                attributes.documentElement = {};
                Array.from(document.documentElement.attributes).forEach(attr => {
                    if (attr.name.startsWith('data-')) {
                        attributes.documentElement[attr.name] = attr.value;
                    }
                });
            }

            return attributes;
        } catch (error) {
            this.error('Error capturing custom data attributes:', error);
            return {};
        }
    }

    async restoreToInitialState() {
        if (!this.snapshotTaken || !this.initialSnapshot) {
            this.error('No initial snapshot available for restoration');
            return false;
        }

        try {
            this.log('Restoring DOM to initial state...');

            // 1. Clear all Quick Action related global variables
            await this.clearQuickActionGlobals();

            // 2. Remove all added panels and overlays
            await this.removeAddedElements();

            // 3. Reset document properties
            await this.resetDocumentProperties();

            // 4. Reset body properties
            await this.resetBodyProperties();

            // 5. Clear event listeners
            await this.clearEventListeners();

            // 6. Reset styles
            await this.resetStyles();

            this.log('DOM restoration completed successfully');
            return true;

        } catch (error) {
            this.error('Failed to restore DOM to initial state:', error);
            return false;
        }
    }

    async clearQuickActionGlobals() {
        try {
            const globalProps = [
                'HtagsAction', 'ShowLinksAction', 'ShowHiddenAction', 'KeywordAction',
                'BoldFromSerpAction', 'SchemaAction', 'ImagesAction', 'MetadataAction', 'UTMBuilderAction',
                'PageStructureAction', 'YouTubeEmbedScraperAction', 'QuickEditAction',
                'ColorPaletteExtractorAction', 'ColorPickerAction', 'FontInspectorAction', 'FontStylesAction', 'CSSClassInspectorAction',
                'RobotsTxtAction', 'MOZDACheckerAction', 'XMLSitemapCheckerAction',
                'ScreenReaderSimulationAction', 'WaybackMachineAction', 'PageKeywordDensityAction',
                'CleanSelectedContentAction', 'LinksExtractorAction', 'BulkLinkOpenAction', 'ResponsiveAction',
                // Escape listeners
                'quickEditEscapeListener', 'quickEditListeners', 'colorPaletteEscapeListener', 'colorPickerEscapeListener', 'fontInspectorEscapeListener',
                'fontStylesEscapeListener', 'cssClassInspectorEscapeListener', 'pageStructureEscapeListener', 'robotsTxtEscapeListener',
                'mozdaCheckerEscapeListener', 'keywordDensityEscapeListener', 'xmlSitemapEscapeListener',
                'screenReaderEscapeListener', 'waybackMachineEscapeListener', 'linksExtractorEscapeListener', 'bulkLinkOpenEscapeListener',
                'htagsEscapeHandler',
                // Other global properties
                'linksExtractorActive', 'linksExtractorCleanup',
                // Drag Select Links properties
                'DragSelectLinksActive', 'dragSelectLinksCleanup', 'dragSelectLinksEscapeListener',
                // Tooltip System
                'UniversalTooltipSystem', 'InfoIconInitializer',
                // Alert popup
                'alertPopupHandler', 'alertPopupInstance',
                // Enhanced Sound Preview System
                'soundPreviewSystem', 'currentPreviewState', 'updatePreviewButtonUI', 'playEnhancedPreview', 'playFullAudioSound',
                'stopCurrentPreview', 'handlePreviewButtonClick', 'tickingPreviewInterval'
            ];

            globalProps.forEach(prop => {
                if (window[prop]) {
                    delete window[prop];
                }
            });

            this.log('Quick Action globals cleared');
        } catch (error) {
            this.error('Error clearing Quick Action globals:', error);
        }
    }

    async removeAddedElements() {
        try {
            const selectors = [
                // Quick Action panels
                '#quick-edit-notice', '.quick-edit-selection-box', '#quick-edit-animation-style',
                '.color-palette-extractor-panel', '.color-picker-panel', '.color-picker-tooltip', '.font-inspector-panel',
                '.font-styles-panel', '.css-class-inspector-panel', '.page-structure-panel', '.robots-txt-panel',
                '.mozda-checker-panel', '.keyword-density-panel', '.xml-sitemap-panel',
                '.screen-reader-panel', '.wayback-machine-panel',
                
                // Links Extractor elements
                '.links-extractor-panel', '.links-extractor-notification', '.links-extractor-styles',
                '.links-extractor-instructions', '[data-links-extractor-style]',
                
                // Bulk Link Open elements
                '.bulk-link-open-panel', '.bulk-link-open-popup', '[data-bulk-link-open]',
                
                // Drag Select Links elements
                '#drag-select-links-popup', '.drag-select-links-panel', '[data-drag-select-box]',
                '.drag-select-highlight', '.drag-select-counter',
                
                // Heading structure panels
                '.heading-structure-panel', '.heading-structure-overlay',
                
                // H-tags highlights
                '.htags-highlight', '.htags-panel', '.htags-overlay',
                
                // Schema panels
                '.schema-panel', '.schema-overlay',
                
                // Images panels
                '.images-panel', '.images-overlay', '.images-audit-panel',
                
                // Metadata panels
                '.metadata-panel', '.metadata-overlay',
                
                // UTM Builder panels
                '.utm-builder-panel',
                
                // YouTube scraper panels
                '.youtube-embed-scraper-panel', '.youtube-embed-scraper-overlay',
                
                // Show links highlights
                '.show-links-highlight', '.show-links-panel',
                
                // Show hidden reveals
                '.show-hidden-reveal', '.show-hidden-panel',
                
                // Bold from SERP highlights
                '.bold-from-serp-highlight',
                
                // Page structure overlays
                '.page-structure-overlay', '.page-structure-hover-outline',

                // Responsive simulator panels
                '.responsive-simulator-panel', '#responsive-simulator-panel',
                
                // Clean content notifications
                '.clean-content-notification', '[data-clean-content-notification]',
                
                // Generic overlays and panels that might be added
                '[data-quick-action]', '[data-context-action]',
                '.quick-action-overlay', '.context-action-overlay',
                '.extension-panel', '.extension-overlay',
                
                // Quick Edit selected elements
                '.quick-edit-selected',
                
                // Tooltip System elements
                '.universal-tooltip', '.info-icon-container', '.has-tooltip',
                '#universal-tooltip-styles',
                
                // Alert popup elements
                '#gmb-alert-popup',
                
                // YouTube Thumbnail Viewer elements
                '.youtube-thumbnail-viewer-popup',
                
                // Screenshot Selector elements (prevents purple overlay persistence)
                '[style*="z-index: 999999"]', // Screenshot overlay
                '[style*="z-index: 1000000"]', // Screenshot instruction banner
                // Enhanced Sound Preview System elements
                '#pomodoroWorkSoundPreview', '#pomodoroBreakSoundPreview', '#pomodoroTickingSoundPreview',
                '.pomodoro-preview-btn.playing' // Preview buttons in playing state
                
                // NOTE: Minimal Reader elements are NOT in general cleanup to prevent button disappearing
                // They are only cleaned up during context menu interference scenarios in background.js
            ];

            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    try {
                        el.remove();
                    } catch (err) {
                        this.log('Could not remove element:', selector, err);
                    }
                });
            });

            // Special cleanup for Quick Edit selected elements
            document.querySelectorAll('.quick-edit-selected').forEach(el => {
                el.classList.remove('quick-edit-selected');
            });

            // Special cleanup for Links Extractor hover elements
            document.querySelectorAll('.links-extractor-hover').forEach(el => {
                el.classList.remove('links-extractor-hover');
                if (el.getAttribute('data-links-extractor-positioned') === 'true') {
                    el.style.position = '';
                    el.removeAttribute('data-links-extractor-positioned');
                }
            });

            // Special cleanup for Tooltip System
            if (typeof InfoIconInitializer !== 'undefined') {
                InfoIconInitializer.cleanup();
            }
            if (typeof UniversalTooltipSystem !== 'undefined') {
                UniversalTooltipSystem.cleanup();
            }

            // NOTE: Minimal Reader cleanup is NOT done here to prevent button disappearing
            // It's only cleaned up during context menu interference scenarios in background.js

            this.log('Added elements removed');
        } catch (error) {
            this.error('Error removing added elements:', error);
        }
    }

    async resetDocumentProperties() {
        try {
            if (this.initialSnapshot.designMode !== undefined) {
                document.designMode = this.initialSnapshot.designMode;
            }

            if (this.initialSnapshot.title !== undefined) {
                document.title = this.initialSnapshot.title;
            }

            this.log('Document properties reset');
        } catch (error) {
            this.error('Error resetting document properties:', error);
        }
    }

    async resetBodyProperties() {
        try {
            if (document.body && this.initialSnapshot.bodyClasses) {
                // Reset body classes to initial state
                document.body.className = '';
                this.initialSnapshot.bodyClasses.forEach(className => {
                    document.body.classList.add(className);
                });
            }

            this.log('Body properties reset');
        } catch (error) {
            this.error('Error resetting body properties:', error);
        }
    }

    async clearEventListeners() {
        try {
            this.log('Clearing global event listeners...');
            
            // Remove tracked global event listeners that Quick Actions add
            const globalListeners = [
                'quickEditEscapeListener',
                'quickEditListeners',
                'colorPaletteEscapeListener', 
                'colorPickerEscapeListener',
                'fontInspectorEscapeListener',
                'fontStylesEscapeListener',
                'cssClassInspectorEscapeListener',
                'pageStructureEscapeListener',
                'robotsTxtEscapeListener',
                'mozdaCheckerEscapeListener',
                'keywordDensityEscapeListener',
                'xmlSitemapEscapeListener',
                'screenReaderEscapeListener',
                'waybackMachineEscapeListener',
                'linkCheckerEscapeListener',
                'dragSelectLinksEscapeListener',
                'linksExtractorEscapeListener',
                'bulkLinkOpenEscapeListener',
                'htagsEscapeHandler',
                'imagesActionEscapeListener',
                'responsiveEscapeListener'
            ];
            
            let removedCount = 0;
            
            globalListeners.forEach(listenerName => {
                if (window[listenerName]) {
                    try {
                        if (listenerName === 'quickEditListeners' && typeof window[listenerName] === 'object') {
                            // Special handling for quickEditListeners object
                            const listeners = window[listenerName];
                            if (listeners.keydown) {
                                document.removeEventListener('keydown', listeners.keydown);
                                removedCount++;
                            }
                            if (listeners.keyup) {
                                document.removeEventListener('keyup', listeners.keyup);
                                removedCount++;
                            }
                            if (listeners.mousedown) {
                                document.removeEventListener('mousedown', listeners.mousedown, { capture: true });
                                removedCount++;
                            }
                            if (listeners.mousemove) {
                                document.removeEventListener('mousemove', listeners.mousemove, { capture: true });
                                removedCount++;
                            }
                            if (listeners.mouseup) {
                                document.removeEventListener('mouseup', listeners.mouseup, { capture: true });
                                removedCount++;
                            }
                        } else if (typeof window[listenerName] === 'function') {
                            // Standard escape listeners (usually keydown for Escape key)
                            document.removeEventListener('keydown', window[listenerName]);
                            document.removeEventListener('keyup', window[listenerName]);
                            // Some actions may use other events, try common ones
                            document.removeEventListener('click', window[listenerName]);
                            document.removeEventListener('contextmenu', window[listenerName]);
                            removedCount++;
                        }
                        
                        // Clean up the global reference
                        delete window[listenerName];
                        
                    } catch (err) {
                        this.log('Error removing listener:', listenerName, err);
                    }
                }
            });
            
            // Additional cleanup for specific problematic listeners
            this.removeSpecificInterferingListeners();
            
            this.log(`Event listeners cleared: ${removedCount} global listeners removed`);
        } catch (error) {
            this.error('Error clearing event listeners:', error);
        }
    }
    
    removeSpecificInterferingListeners() {
        try {
            // Handle Drag Select Links context menu prevention
            if (typeof preventContextMenu === 'function') {
                window.removeEventListener("contextmenu", preventContextMenu, true);
                document.removeEventListener("contextmenu", preventContextMenu, true);
                this.log('Removed local preventContextMenu listener');
            }
            if (window.preventContextMenu && typeof window.preventContextMenu === 'function') {
                window.removeEventListener("contextmenu", window.preventContextMenu, true);
                document.removeEventListener("contextmenu", window.preventContextMenu, true);
                delete window.preventContextMenu;
                this.log('Removed window.preventContextMenu listener');
            }
            
            // Clean up any drag select state
            if (window.dragSelectLinksCleanup && typeof window.dragSelectLinksCleanup === 'function') {
                try {
                    window.dragSelectLinksCleanup();
                    this.log('Executed dragSelectLinksCleanup function');
                } catch (err) {
                    this.log('Error executing dragSelectLinksCleanup:', err);
                }
            }
            
            // Reset problematic global state variables
            const stateVars = [
                'isKeyPressed', 'isDragging', 'DragSelectLinksActive',
                'linksExtractorActive', 'imagesActionActive'
            ];
            
            stateVars.forEach(varName => {
                if (window[varName] !== undefined) {
                    window[varName] = false;
                    this.log(`Reset global state: ${varName} = false`);
                }
            });
            
        } catch (error) {
            this.error('Error in removeSpecificInterferingListeners:', error);
        }
    }

    async resetStyles() {
        try {
            // Remove any style elements that were added by Quick Actions
            const addedStyles = document.querySelectorAll('style[data-quick-action], style[data-context-action]');
            addedStyles.forEach(style => style.remove());

            // Reset specific styles that might have been modified
            if (document.body) {
                const bodyStyle = document.body.style;
                
                // Reset common properties that Quick Actions might modify
                const propertiesToReset = [
                    'overflow', 'position', 'transform', 'filter', 'opacity',
                    'userSelect', 'pointerEvents', 'cursor'
                ];

                propertiesToReset.forEach(prop => {
                    bodyStyle.removeProperty(prop);
                });
            }

            if (document.documentElement) {
                const docStyle = document.documentElement.style;
                
                const propertiesToReset = ['overflow', 'position', 'transform'];
                propertiesToReset.forEach(prop => {
                    docStyle.removeProperty(prop);
                });
            }

            this.log('Styles reset');
        } catch (error) {
            this.error('Error resetting styles:', error);
        }
    }

    // Wait for DOM, scripts, and styles to be fully ready
    async waitForDOMReady(timeout = 10000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const checkReady = () => {
                const currentTime = Date.now();
                
                // Check if timeout exceeded
                if (currentTime - startTime > timeout) {
                    this.log('DOM ready timeout exceeded, proceeding with snapshot');
                    resolve();
                    return;
                }
                
                // Check basic DOM readiness
                if (document.readyState !== 'complete') {
                    this.log('Document not yet complete, waiting...');
                    setTimeout(checkReady, 100);
                    return;
                }
                
                // Check if fonts are loaded (if supported)
                if (document.fonts && document.fonts.ready) {
                    document.fonts.ready.then(() => {
                        this.log('Fonts loaded, checking for pending stylesheets...');
                        this.checkStylesheetsReady(resolve, checkReady, currentTime, startTime, timeout);
                    }).catch(() => {
                        // If fonts.ready fails, proceed anyway
                        this.log('Font loading check failed, proceeding...');
                        this.checkStylesheetsReady(resolve, checkReady, currentTime, startTime, timeout);
                    });
                } else {
                    // Browser doesn't support document.fonts, skip font check
                    this.checkStylesheetsReady(resolve, checkReady, currentTime, startTime, timeout);
                }
            };
            
            checkReady();
        });
    }
    
    checkStylesheetsReady(resolve, checkReady, currentTime, startTime, timeout) {
        // Check if all stylesheets are loaded
        const stylesheets = Array.from(document.styleSheets);
        let allLoaded = true;
        
        for (let sheet of stylesheets) {
            try {
                // Try to access the stylesheet rules - if it's still loading, this will throw
                const rules = sheet.cssRules || sheet.rules;
                if (!rules) {
                    allLoaded = false;
                    break;
                }
            } catch (e) {
                // Stylesheet still loading or cross-origin
                allLoaded = false;
                break;
            }
        }
        
        if (allLoaded) {
            // Add a small delay to ensure any remaining async operations complete
            setTimeout(() => {
                this.log('DOM, fonts, and stylesheets are ready');
                resolve();
            }, 200);
        } else if (currentTime - startTime > timeout) {
            this.log('Stylesheet ready timeout exceeded, proceeding with snapshot');
            resolve();
        } else {
            // Reduce console spam: log less frequently and check less often
            if (!this.stylesheetLogCount) this.stylesheetLogCount = 0;
            this.stylesheetLogCount++;
            
            // Only log every 10th check (instead of every check)
            if (this.stylesheetLogCount % 10 === 0) {
                this.log(`Stylesheets still loading, waiting... (check ${this.stylesheetLogCount})`);
            }
            
            // Check every 500ms instead of 100ms to reduce spam
            setTimeout(checkReady, 500);
        }
    }

    // Method to check if snapshot is ready
    isSnapshotReady() {
        return this.snapshotTaken && this.initialSnapshot !== null;
    }

    // Method to get snapshot info
    getSnapshotInfo() {
        if (!this.isSnapshotReady()) {
            return { ready: false };
        }

        return {
            ready: true,
            timestamp: this.initialSnapshot.timestamp,
            age: Date.now() - this.initialSnapshot.timestamp,
            bodyClasses: this.initialSnapshot.bodyClasses,
            designMode: this.initialSnapshot.designMode
        };
    }

    // Legacy method maintained for backward compatibility but no longer used
    setDebugMode(enabled) {
        console.log('DOMSnapshotUtility: setDebugMode is deprecated - use global debug mode setting instead');
    }
}

// Create global instance
if (typeof window !== 'undefined') {
    window.DOMSnapshotUtility = DOMSnapshotUtility;
    
    // Initialize the snapshot utility
    if (!window.domSnapshotUtility) {
        window.domSnapshotUtility = new DOMSnapshotUtility();
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DOMSnapshotUtility;
} 