// Settings Management
class SettingsManager {
    constructor() {
        this.autoSaveTimeout = null; // For debouncing auto-save
        this.defaultSettings = {
            // General Settings
            autoSave: false,
            notifications: true,
            debugMode: false,
            exportFormat: 'csv',
            includeTimestamp: true,
            maxExportRecords: 1000,
            searchDelay: 1000,
            autoOpenFirst: false,
            
            // Extras Settings
            locationChangerEnabled: false,
            serpNumbering: false,
            sponsoredHighlighter: false,
            sponsoredHighlighterColor: '#ff0000',
            searchResultStats: false,
            citationHunter: false,
            openSingleListing: false,
            searchNAPInjector: false,
            currentLocationDisplayEnabled: false,
            trackedDomainsEnabled: false,
            trackedDomainsList: [],
            trackedDomainsColor: '#7c3aed',
            reverseGmailOrderEnabled: false,
            showGmailTimeEnabled: false,
            gmailTimeFormat: '%a - %d %b, %H:%M',
            showGmailIconsEnabled: false,
            massUnsubscribeEnabled: false,
            emailPinnerEnabled: false,
            gmailThreadExpanderEnabled: false,
            gmailEnhancedTimestampsEnabled: false,
            gmailJumpLinksEnabled: true,
            youtubeAdsSkipperEnabled: false,
            utmTrackingCleanerEnabled: false,
            utmStrippingMethod: 4, // Default to most privacy
            utmCopyCleanEnabled: false,
            utmCleanAndGoEnabled: false,
            utmCleanerWhitelistDomains: [],
            
            // Minimal Reader Settings
            minimalReaderEnabled: false,
            minimalReaderTheme: 'white',
            minimalReaderFontSize: 18,
            minimalReaderLineHeight: 1.6,
            minimalReaderFontWeight: 'normal',
            minimalReaderButtonPosition: 'top-left',
            minimalReaderSpeedReadingEnabled: false,
            minimalReaderStopWordFading: true,
            minimalReaderBlacklistDomains: [], // User-configurable blacklist domains
            experimental: false,
            enhancedExtraction: true,
            batchProcessing: false,
            concurrentRequests: 3,
            requestTimeout: 30,
            cacheResults: true,
            autoClean: false,
            retentionDays: 30,
            
            // Screenshot Tool Settings
            screenshotToolEnabled: false,
            screenshotShortcut: navigator.platform.toLowerCase().includes('mac') ? 'Cmd+Shift+S' : 'Ctrl+Shift+S',
            screenshotToolFormat: 'png',
            screenshotToolQuality: 0.9,
            screenshotToolDrawingColor: '#7C3AED',
            screenshotToolStrokeWidth: 3,
            screenshotToolShowUI: true,
            
            // New Tab Redirect Settings
            newTabRedirectEnabled: true,
            
            // Quick Actions Settings
            htagsEnabled: false,
            headingStructureEnabled: false,
            showLinksEnabled: false,
            showHiddenEnabled: false,
            keywordEnabled: false,
            boldFromSerpEnabled: false,
            schemaEnabled: true,
            schemaVisualizerEnabled: true,
            imagesEnabled: false,
            metadataEnabled: false,
            utmBuilderEnabled: false,
            pageStructureEnabled: false,
            copyElementEnabled: false,
            linksExtractorEnabled: false,
            youtubeEmbedScraperEnabled: false,
            keyboardShortcutsEnabled: false,
            responsiveEnabled: false,
            seoTestsEnabled: false,
            trackerDetectionEnabled: false,
            bulkLinkOpenEnabled: false,
            
            // Global Shortcuts Settings
            globalShortcutsEnabled: false,
            copyElementShortcut: 'Control+Shift+KeyC',
            
            // Copy Element Auto Clean & Title Settings
            autoCleanAndTitleEnabled: false,
            
            // Click to Copy Links Settings
            clickToCopyEnabled: false,
            
            // Copy Replace Settings
            copyReplaceEnabled: false,
            copyReplaceSourceDomain: '',
            copyReplaceTargetDomain: '',
            
            // Text Transformers Settings
            textTransformersEnabled: false,
            textTransformersAutoPaste: true,
            textTransformersCapitalCaseShortcut: '',
            textTransformersLowerCaseShortcut: '',
            textTransformersUpperCaseShortcut: '',
            textTransformersSentenceCaseShortcut: '',
            textTransformersSlugifyShortcut: '',
            textTransformersTrimToPageShortcut: '',
            textTransformersSortAlphabeticallyShortcut: '',
            textTransformersRemoveEmptyLinesShortcut: '',
            textTransformersRemoveDuplicateLinesShortcut: '',
            textTimeMachineLauncherShortcut: '',
            textTimeMachineUrl: '',
            newTabRedirectShortcut: '',
            newTabRedirectUrl: '',
            
            // Show Hidden Auto-Detection Settings
            showhiddenAutoDetectionEnabled: false,

            // Drag Select Links Settings
            dragSelectLinksEnabled: false,
            dragSelectLinksHotkey: 90, // Z key
            dragSelectLinksColor: '#7C3AED',
            dragSelectLinksFilterMode: 'exclude', // 'exclude' or 'include'
            dragSelectLinksFilterText: '',
            dragSelectLinksRemoveDuplicates: true,
            dragSelectLinksIncludeText: true,
            // Format-specific settings
            dragSelectTextFormat: 'text-combined', // 'text-url', 'text-title', 'text-combined'
            dragSelectHtmlFormat: 'html-url', // 'html-url', 'html-title'
            dragSelectJsonFormat: 'json-url', // 'json-url', 'json-both'
            dragSelectCustomFormat: '<a href="{url}">{title}</a>{br}',
            
            // Video Speed Controller Settings
            videoSpeedControllerEnabled: false,
            videoSpeedControllerOpacity: 0.3,
            videoSpeedControllerStartHidden: false,
            videoSpeedControllerRememberSpeed: false,
            videoSpeedControllerAudioSupport: false,
            videoSpeedControllerLastSpeed: 1.0,
            videoSpeedControllerKeyBindings: [
                { action: 'display', key: 86, value: 0, force: false }, // V - Show/hide controller
                { action: 'slower', key: 83, value: 0.1, force: false }, // S - Decrease speed
                { action: 'faster', key: 68, value: 0.1, force: false }, // D - Increase speed
                { action: 'rewind', key: 90, value: 10, force: false }, // Z - Rewind
                { action: 'advance', key: 88, value: 10, force: false }, // X - Advance
                { action: 'preferred', key: 71, value: 1.8, force: true } // G - Preferred speed
            ],
            videoSpeedControllerBlacklist: '', // Sites to exclude (one per line)
            
            // YouTube Tools Settings (bundled with video speed controller)
            // These are controlled by videoSpeedControllerEnabled toggle
            screenshotYouTubeEnabled: false,
            screenshotKeyEnabled: false,
            screenshotFileFormat: 'png',
            screenshotFunctionality: 2, // 0: save, 1: copy, 2: both
            youtubeThumbnailViewerEnabled: false,
            youtubeFramesEnabled: false,
            youtubeGifEnabled: false,
            
            // Minimal Reader Settings (General & Browser Tools)
            minimalReaderEnabled: false,
            minimalReaderTheme: 'white', // 'white', 'yellow', 'dark'
            minimalReaderFontSize: 18,
            minimalReaderLineHeight: 1.6,
            minimalReaderFontWeight: 'normal', // 'normal', 'bold'
            minimalReaderButtonPosition: 'top-left', // 'top-left', 'top-right', 'bottom-left', 'bottom-right'
            minimalReaderSpeedReadingEnabled: false,
            minimalReaderStopWordFading: true,
            minimalReaderBlacklistDomains: [], // User-configurable blacklist domains
            
            // Tasks System Settings (Independent of Pomodoro)
            tasksEnabled: false,
            
            // Pomodoro Timer Settings
            pomodoroEnabled: false,
            
            // Alert & Reminder Settings
            alertsEnabled: false,
            
            // Quick Timer Settings (Independent of Alerts)
            quickTimerEnabled: false,
            quickTimerNotifications: true,
            
            // Notification Utility Settings
            notificationUtilityEnabled: false,
            
            // Popup Shortcuts Settings
            popupShortcutsEnabled: true,
            
            pomodoroWorkDuration: 25,
            pomodoroShortBreak: 5,
            pomodoroLongBreak: 15,
            pomodoroNumberOfCycles: 8,
            pomodoroWorkCompletedSound: 'Bell Meditation',
            pomodoroEndBreakSound: 'Celestial Gong',
            pomodoroCompletionNotifications: true,
            pomodoroNotificationVolume: 70,
            pomodoroChronometerSound: true,
            pomodoroChronometerOnBreak: false,
            pomodoroChronometerFrequency: 2,
            pomodoroTickingSound: 'Clock Ticking 1',
            pomodoroChronometerVolume: 30,
            pomodoroTodoDisplayCount: 3,
            pomodoroBlockingEnabled: false,
        };
        
        this.settings = { ...this.defaultSettings };
        this.init();
    }

    async checkSettingsLockdown() {
        try {
            // Check if settings lockdown is active
            const response = await chrome.runtime.sendMessage({
                action: 'checkSettingsLockdown'
            });
            
            if (response && response.success && response.lockdownActive) {
                console.log('🔒 Settings: Lockdown is active - Timer values are protected');
                // Notification removed per user request - silent protection
            } else {
                console.log('🔓 Settings: No lockdown active');
            }
        } catch (error) {
            console.error('❌ Settings: Error checking lockdown status:', error);
        }
    }

    // Notification method removed per user request - silent protection only

    async init() {
        // Check lockdown status first
        await this.checkSettingsLockdown();
        
        await this.loadSettings();
        await this.loadTabContent();
        this.setupEventListeners();
        this.setupMessageListeners();
        this.updateUI();
        this.setupWindowPositioning();
    }

    // Multi-monitor detection utility for settings window
    async getActiveMonitorInfo() {
        try {
            const displays = await chrome.system.display.getInfo();
            
            // Get current window position (this is the settings window position)
            const windowCenterX = window.screenX + (window.outerWidth / 2);
            const windowCenterY = window.screenY + (window.outerHeight / 2);
            
            // Find which display contains the center of the current window
            let activeDisplay = displays.find(display => {
                const bounds = display.bounds;
                return windowCenterX >= bounds.left && 
                       windowCenterX < bounds.left + bounds.width &&
                       windowCenterY >= bounds.top && 
                       windowCenterY < bounds.top + bounds.height;
            });
            
            // Fallback to primary display if not found
            if (!activeDisplay) {
                activeDisplay = displays.find(display => display.isPrimary) || displays[0];
            }
            
            return {
                display: activeDisplay,
                allDisplays: displays,
                currentWindowCenter: { x: windowCenterX, y: windowCenterY }
            };
        } catch (error) {
            console.log('Multi-monitor detection failed, using primary display:', error);
            // Fallback to screen object for single monitor scenarios
            return {
                display: {
                    bounds: { left: 0, top: 0, width: screen.width, height: screen.height },
                    isPrimary: true
                },
                allDisplays: [{ bounds: { left: 0, top: 0, width: screen.width, height: screen.height }, isPrimary: true }],
                currentWindowCenter: { x: screen.width / 2, y: screen.height / 2 }
            };
        }
    }

    // Get saved window position and size from localStorage
    getSavedWindowSettings() {
        try {
            const saved = localStorage.getItem('gmb-settings-window-settings');
            if (saved) {
                return JSON.parse(saved);
            }
        } catch (e) {
            console.log('Error loading saved window settings:', e);
        }
        // Default settings - center the window on primary display
        const defaultWidth = 800;
        const defaultHeight = 700;
        return {
            width: defaultWidth,
            height: defaultHeight,
            x: Math.max(0, (screen.width - defaultWidth) / 2),
            y: Math.max(0, (screen.height - defaultHeight) / 2)
        };
    }

    // Get saved window position and size with multi-monitor awareness
    async getSavedWindowSettingsMultiMonitor() {
        try {
            const saved = localStorage.getItem('gmb-settings-window-settings');
            if (saved) {
                return JSON.parse(saved);
            }
        } catch (e) {
            console.log('Error loading saved window settings:', e);
        }
        
        // Default settings - center the window on active monitor
        const monitorInfo = await this.getActiveMonitorInfo();
        const displayBounds = monitorInfo.display.bounds;
        const defaultWidth = 800;
        const defaultHeight = 700;
        
        return {
            width: defaultWidth,
            height: defaultHeight,
            x: Math.max(displayBounds.left, displayBounds.left + (displayBounds.width - defaultWidth) / 2),
            y: Math.max(displayBounds.top, displayBounds.top + (displayBounds.height - defaultHeight) / 2)
        };
    }
    
    // Save window position and size to localStorage
    saveWindowSettings() {
        try {
            const settings = {
                width: window.outerWidth,
                height: window.outerHeight,
                x: window.screenX,
                y: window.screenY
            };
            localStorage.setItem('gmb-settings-window-settings', JSON.stringify(settings));
        } catch (e) {
            console.log('Error saving window settings:', e);
        }
    }

    // Setup window positioning and resize handling
    setupWindowPositioning() {
        // Skip initial positioning since window is already created at correct position
        // from popup.js to avoid jump effect
        console.log('Settings window positioning handled during creation');
        
        // Multi-monitor aware position validation
        const validatePosition = async () => {
            try {
                const monitorInfo = await this.getActiveMonitorInfo();
                const displayBounds = monitorInfo.display.bounds;
                const savedSettings = this.getSavedWindowSettings();
                
                const maxX = displayBounds.left + displayBounds.width - savedSettings.width;
                const maxY = displayBounds.top + displayBounds.height - savedSettings.height;
                const minX = displayBounds.left;
                const minY = displayBounds.top;
                
                const validX = Math.max(minX, Math.min(savedSettings.x, maxX));
                const validY = Math.max(minY, Math.min(savedSettings.y, maxY));
                
                // Only adjust if significantly different from current position
                const currentX = window.screenX;
                const currentY = window.screenY;
                
                if (Math.abs(currentX - validX) > 50 || Math.abs(currentY - validY) > 50) {
                    try {
                        window.resizeTo(savedSettings.width, savedSettings.height);
                        window.moveTo(validX, validY);
                    } catch (e) {
                        console.log('Could not resize/move window:', e);
                    }
                }
            } catch (e) {
                console.log('Multi-monitor position validation failed, using fallback:', e);
                // Fallback to single monitor logic
                const savedSettings = this.getSavedWindowSettings();
                const maxX = screen.width - savedSettings.width;
                const maxY = screen.height - savedSettings.height;
                
                const validX = Math.max(0, Math.min(savedSettings.x, maxX));
                const validY = Math.max(0, Math.min(savedSettings.y, maxY));
                
                const currentX = window.screenX;
                const currentY = window.screenY;
                
                if (Math.abs(currentX - validX) > 50 || Math.abs(currentY - validY) > 50) {
                    try {
                        window.resizeTo(savedSettings.width, savedSettings.height);
                        window.moveTo(validX, validY);
                    } catch (e) {
                        console.log('Could not resize/move window:', e);
                    }
                }
            }
        };
        
        // Run validation after a short delay to ensure window is fully loaded
        setTimeout(validatePosition, 100);
        
        // Save position when window is moved or resized
        let saveTimeout;
        const debouncedSave = () => {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                this.saveWindowSettings();
            }, 500);
        };
        
        // Listen for window move and resize events
        window.addEventListener('resize', debouncedSave);
        window.addEventListener('beforeunload', () => {
            this.saveWindowSettings();
        });
        
        // Add interval to check for position changes (for manual dragging)
        let lastX = window.screenX;
        let lastY = window.screenY;
        setInterval(() => {
            if (window.screenX !== lastX || window.screenY !== lastY) {
                lastX = window.screenX;
                lastY = window.screenY;
                debouncedSave();
            }
        }, 1000);
        
        // Center window if it goes beyond screen edges
        this.setupEdgeConstraints();
    }

    // Setup constraints to keep window within screen bounds and center if needed
    setupEdgeConstraints() {
        const checkBounds = async () => {
            try {
                const monitorInfo = await this.getActiveMonitorInfo();
                const displayBounds = monitorInfo.display.bounds;
                const width = window.outerWidth;
                const height = window.outerHeight;
                const x = window.screenX;
                const y = window.screenY;
                
                // Check if window is mostly off-screen (more than 50% outside active monitor)
                const offScreenRight = x > (displayBounds.left + displayBounds.width - width * 0.5);
                const offScreenLeft = (x + width) < (displayBounds.left + width * 0.5);
                const offScreenBottom = y > (displayBounds.top + displayBounds.height - height * 0.5);
                const offScreenTop = (y + height) < (displayBounds.top + height * 0.5);
                
                if (offScreenRight || offScreenLeft || offScreenBottom || offScreenTop) {
                    // Center the window on active monitor
                    const centerX = Math.max(displayBounds.left, displayBounds.left + (displayBounds.width - width) / 2);
                    const centerY = Math.max(displayBounds.top, displayBounds.top + (displayBounds.height - height) / 2);
                    
                    try {
                        window.moveTo(centerX, centerY);
                        this.showNotification('Window repositioned to center of active monitor', 'info');
                    } catch (e) {
                        console.log('Could not center window:', e);
                    }
                }
            } catch (e) {
                console.log('Multi-monitor edge constraint check failed, using fallback:', e);
                // Fallback to single monitor logic
                const width = window.outerWidth;
                const height = window.outerHeight;
                const x = window.screenX;
                const y = window.screenY;
                
                const offScreenRight = x > (screen.width - width * 0.5);
                const offScreenLeft = (x + width) < (width * 0.5);
                const offScreenBottom = y > (screen.height - height * 0.5);
                const offScreenTop = (y + height) < (height * 0.5);
                
                if (offScreenRight || offScreenLeft || offScreenBottom || offScreenTop) {
                    const centerX = Math.max(0, (screen.width - width) / 2);
                    const centerY = Math.max(0, (screen.height - height) / 2);
                    
                    try {
                        window.moveTo(centerX, centerY);
                        this.showNotification('Window repositioned to center', 'info');
                    } catch (e) {
                        console.log('Could not center window:', e);
                    }
                }
            }
        };
        
        // Check bounds periodically
        setInterval(checkBounds, 2000);
        
        // Also check when window regains focus
        window.addEventListener('focus', checkBounds);
    }

    async loadSettings() {
        try {
            // Load from both sync and local storage
            const [syncResult, localResult] = await Promise.all([
                chrome.storage.local.get('gmbExtractorSettings'),
                chrome.storage.local.get('gmbExtractorLargeSettings')
            ]);
            
            // Start with defaults
            this.settings = { ...this.defaultSettings };
            
            // Merge sync settings
            if (syncResult.gmbExtractorSettings) {
                this.settings = { ...this.settings, ...syncResult.gmbExtractorSettings };
            }
            
            // Merge large settings from local storage
            if (localResult.gmbExtractorLargeSettings) {
                this.settings = { ...this.settings, ...localResult.gmbExtractorLargeSettings };
            }
            
            // Migration: Check if sync storage contains large arrays and move them
            await this.migrateSettingsToProperStorage();
            
            // Check if secret developer mode is active
            const secretModeResult = await chrome.storage.local.get(['secretDeveloperModeActive']);
            const isSecretModeActive = secretModeResult.secretDeveloperModeActive === true;
            
            if (!isSecretModeActive && this.settings.debugMode === true) {
                // Only reset debug mode if secret developer mode is NOT active
                // This prevents normal users from having debug mode enabled accidentally
                this.settings.debugMode = false;
                await this.saveSettings(false); // Save without notification
                console.log('Settings: Debug mode reset to false (secret developer mode not active)');
            }
            // If secret mode IS active, preserve whatever debugMode setting the user had
            
            console.log('Settings: Loaded from both sync and local storage');
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }
    






    async saveSettings(showNotification = true) {
        console.log('Settings: Attempting to save settings:', this.settings);
        try {
            // Split settings into sync (small) and local (large) storage
            const { syncSettings, localSettings } = this.splitSettingsForStorage();
            
            console.log('Settings: Sync storage size:', JSON.stringify(syncSettings).length);
            console.log('Settings: Local storage size:', JSON.stringify(localSettings).length);
            
            // Save all settings to local storage (not sync) as one complete object
            // This prevents the issue where partial and full settings were being saved to the same key
            const allSettings = { ...this.settings };
            await chrome.storage.local.set({ 
                gmbExtractorSettings: allSettings
            });
            console.log('Settings: Successfully saved all settings to chrome.storage.local');
            
            // Also save large settings separately for backward compatibility
            await chrome.storage.local.set({ 
                gmbExtractorLargeSettings: localSettings
            });
            console.log('Settings: Successfully saved large settings separately');
            
            // Only show notification for manual saves (like reset to defaults)
            if (showNotification) {
                this.showNotification('Settings saved successfully', 'success');
            }
            
            // Force reload notification settings immediately
            if (window.GMBNotifications && window.GMBNotifications.reload) {
                window.GMBNotifications.reload();
            }
            
            // Notify content scripts about settings changes for immediate effect
            this.notifyContentScripts();
            
            // Special handling for keyboard shortcuts in popup
            this.notifyKeyboardShortcuts();
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showNotification('Error saving settings: ' + error.message, 'error');
        }
    }
    
    /**
     * Enhanced save settings with retry mechanism
     */
    async saveSettingsWithRetry(retries = 3) {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                await this.saveSettings(false);
                return; // Success
            } catch (error) {
                console.error(`Settings save attempt ${attempt} failed:`, error);
                if (attempt === retries) {
                    throw new Error(`Failed to save settings after ${retries} attempts: ${error.message}`);
                }
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 500 * attempt));
            }
        }
    }
    
    /**
     * Immediately broadcast settings changes to content scripts
     */
    broadcastSettingsChange() {
        // Get all tabs and send updated settings
        chrome.tabs.query({}, (tabs) => {
            tabs.forEach(tab => {
                try {
                    chrome.tabs.sendMessage(tab.id, {
                        action: 'updateSettings',
                        settings: this.settings
                    }, (response) => {
                        // Ignore errors for inactive tabs
                        if (chrome.runtime.lastError) {
                            // Silent ignore - tab might be inactive
                        }
                    });
                } catch (error) {
                    // Silent ignore - tab might not have content script
                }
            });
        });
    }
    
    /**
     * Split settings into sync (small) and local (large) storage
     */
    splitSettingsForStorage() {
        const largeSettingsKeys = [
            'trackedDomainsList',
            'utmCleanerWhitelistDomains',
            // ALL video speed controller settings go to local storage
            'videoSpeedControllerEnabled',
            'videoSpeedControllerOpacity',
            'videoSpeedControllerStartHidden',
            'videoSpeedControllerRememberSpeed',
            'videoSpeedControllerAudioSupport',
            'videoSpeedControllerLastSpeed',
            'videoSpeedControllerKeyBindings',
            'videoSpeedControllerBlacklist',
            // ALL Pomodoro settings go to local storage for consistency with background timer
            'pomodoroEnabled',
            'tasksEnabled',
            'pomodoroWorkDuration',
            'pomodoroShortBreak',
            'pomodoroLongBreak',
            'pomodoroWorkCompletedSound',
            'pomodoroEndBreakSound',
            'pomodoroCompletionNotifications',
            'pomodoroNotificationVolume',
            'pomodoroChronometerSound',
            'pomodoroChronometerOnBreak',
            'pomodoroChronometerFrequency',
            'pomodoroTickingSound',
            'pomodoroChronometerVolume',
            'pomodoroTodoDisplayCount',
            'pomodoroBlockingEnabled',
            'pomodoroNumberOfCycles'
        ];
        
        const syncSettings = { ...this.settings };
        const localSettings = {};
        
        // Move large arrays to local storage
        largeSettingsKeys.forEach(key => {
            if (this.settings[key] !== undefined) {
                localSettings[key] = this.settings[key];
                delete syncSettings[key];
            }
        });
        
        return { syncSettings, localSettings };
    }

    /**
     * Migrate large arrays from sync to local storage if they exist in sync
     */
    async migrateSettingsToProperStorage() {
        try {
            const syncResult = await chrome.storage.local.get('gmbExtractorSettings');
            if (!syncResult.gmbExtractorSettings) return;
            
            const currentSyncSettings = syncResult.gmbExtractorSettings;
            const largeKeys = [
                'trackedDomainsList', 
                'utmCleanerWhitelistDomains', 
                // ALL video speed controller settings
                'videoSpeedControllerEnabled',
                'videoSpeedControllerOpacity',
                'videoSpeedControllerStartHidden',
                'videoSpeedControllerRememberSpeed',
                'videoSpeedControllerAudioSupport',
                'videoSpeedControllerLastSpeed',
                'videoSpeedControllerKeyBindings', 
                'videoSpeedControllerBlacklist', 
            ];
            
            let needsMigration = false;
            const dataToMigrate = {};
            
            // Check if any large arrays are in sync storage
            largeKeys.forEach(key => {
                if (currentSyncSettings[key] !== undefined) {
                    dataToMigrate[key] = currentSyncSettings[key];
                    needsMigration = true;
                    console.log(`Migration: Found ${key} in sync storage, will move to local`);
                }
            });
            
            if (needsMigration) {
                console.log('Migration: Moving large arrays from sync to local storage...');
                
                // Save large data to local storage
                const existingLocal = await chrome.storage.local.get('gmbExtractorLargeSettings');
                const mergedLargeSettings = { ...(existingLocal.gmbExtractorLargeSettings || {}), ...dataToMigrate };
                await chrome.storage.local.set({ gmbExtractorLargeSettings: mergedLargeSettings });
                
                // Remove large arrays from sync storage
                const cleanedSyncSettings = { ...currentSyncSettings };
                largeKeys.forEach(key => {
                    delete cleanedSyncSettings[key];
                });
                
                await chrome.storage.local.set({ gmbExtractorSettings: cleanedSyncSettings });
                console.log('Migration: Successfully moved large arrays to local storage');
            }
        } catch (error) {
            console.error('Migration: Error migrating settings:', error);
        }
    }

    async autoSaveSettings() {
        // Auto-save without showing notifications (cleaner UX)
        await this.saveSettings(false);
    }

    notifyKeyboardShortcuts() {
        // Update keyboard shortcuts in the popup window itself
        if (window.quickActionsShortcuts && window.quickActionsShortcuts.updateShortcutsEnabled) {
            window.quickActionsShortcuts.updateShortcutsEnabled(this.settings.keyboardShortcutsEnabled);
        }
    }

    notifyContentScripts() {
        // Get all tabs and send settings update message
        if (typeof chrome !== 'undefined' && chrome.tabs) {
            chrome.tabs.query({}, (tabs) => {
                tabs.forEach(tab => {
                    // Send to Google pages and YouTube where our scripts might be running
                    if (tab.url && (tab.url.includes('google.') || tab.url.includes('youtube.com'))) {
                        chrome.tabs.sendMessage(tab.id, {
                            action: 'updateSettings',
                            settings: this.settings
                        }, (response) => {
                            // Ignore errors for tabs that don't have our content script
                            if (chrome.runtime.lastError) {
                                // Silent ignore
                                console.log('Settings: Tab message ignored (no content script):', chrome.runtime.lastError.message);
                            }
                        });
                    }
                });
            });
        }
    }

    setupMessageListeners() {
        // Message listeners for settings updates can be added here if needed
    }

    // Helper function to check if element should be excluded from auto-save
    isTimerDurationSetting(element) {
        const timerDurationSettings = [
            'pomodoroWorkDuration',
            'pomodoroShortBreak', 
            'pomodoroLongBreak',
            'pomodoroNumberOfCycles'
        ];
        
        return timerDurationSettings.includes(element.id) || 
               timerDurationSettings.includes(element.dataset.setting);
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Auto-save for all input fields (text inputs, selects, etc.)
        document.addEventListener('input', (e) => {
            if (e.target.matches('input:not([type="file"]), select, textarea') && 
                e.target.id !== 'advancedFeaturesSearch' &&
                !this.isTimerDurationSetting(e.target)) {
                // Debounce auto-save to avoid excessive calls
                clearTimeout(this.autoSaveTimeout);
                this.autoSaveTimeout = setTimeout(() => {
                    this.collectSettings();
                    this.autoSaveSettings();
                    console.log(`🔧 STM Settings: Auto-saved setting: ${e.target.name || e.target.id}`);
                }, 500); // 500ms delay
            } else if (this.isTimerDurationSetting(e.target)) {
                console.log(`🛡️ STM Settings: Skipped auto-save for timer duration setting: ${e.target.id}`);
            }
        });

        // Auto-save for change events (dropdowns, checkboxes, radio buttons)
        document.addEventListener('change', (e) => {
            if (e.target.matches('input:not([type="file"]), select, textarea') && 
                e.target.id !== 'advancedFeaturesSearch' &&
                !this.isTimerDurationSetting(e.target)) {
                this.collectSettings();
                this.autoSaveSettings();
                console.log(`🔧 STM Settings: Auto-saved setting: ${e.target.name || e.target.id}`);
            } else if (this.isTimerDurationSetting(e.target)) {
                console.log(`🛡️ STM Settings: Skipped auto-save for timer duration setting: ${e.target.id}`);
            }
        });

        // Reset button
        const resetBtn = document.getElementById('resetSettingsBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetToDefaults();
            });
        } else {
            console.warn('resetSettingsBtn not found in DOM');
        }

        // YouTube Ads Counter Reset button
        const resetAdsBtn = document.getElementById('resetAdsCounterBtn');
        if (resetAdsBtn) {
            resetAdsBtn.addEventListener('click', () => {
                this.resetYouTubeAdsCount();
            });
        }
    }

    async loadTabContent() {
        try {
            // Load general settings content
            const generalResponse = await fetch('general-settings.html');
            const generalContent = await generalResponse.text();
            document.getElementById('general-settings-content').innerHTML = generalContent;

            // Load extras settings content
            const extrasResponse = await fetch('extras-settings.html');
            const extrasContent = await extrasResponse.text();
            document.getElementById('extras-settings-content').innerHTML = extrasContent;

            // Load quick actions settings content
            const quickActionsResponse = await fetch('quick-actions-settings.html');
            const quickActionsContent = await quickActionsResponse.text();
            document.getElementById('quick-actions-settings-content').innerHTML = quickActionsContent;

            // Re-initialize tooltips after dynamic content is loaded
            if (typeof InfoIconInitializer !== 'undefined') {
                InfoIconInitializer.init();
            }

            // Load custom shortcut handler for quick actions
            await this.loadCustomShortcutHandler();

            // Setup controls after content is loaded
            this.setupControls();
        } catch (error) {
            console.error('Error loading tab content:', error);
        }
    }

    async loadCustomShortcutHandler() {
        try {
            // Load the custom shortcut handler script
            if (!window.CustomShortcutHandler) {
                const script = document.createElement('script');
                script.src = 'custom-shortcut-handler.js';
                script.onload = () => {
                    console.log('Custom shortcut handler loaded');
                    // Initialize with current shortcut value
                    setTimeout(() => {
                        if (window.customShortcutHandler && this.settings.copyElementShortcut) {
                            window.customShortcutHandler.updateInputValue(this.settings.copyElementShortcut);
                        }
                    }, 100);
                };
                document.head.appendChild(script);
            } else {
                // Already loaded, just set the value
                setTimeout(() => {
                    if (window.customShortcutHandler && this.settings.copyElementShortcut) {
                        window.customShortcutHandler.updateInputValue(this.settings.copyElementShortcut);
                    }
                }, 100);
            }
            
            // Load the colorpicker shortcut handler script (only on settings page)
            if (document.querySelector('#colorpickerCustomShortcut')) {
                if (!window.ColorpickerShortcutHandler) {
                    console.log('Loading colorpicker shortcut handler...');
                    if (!window.colorpickerShortcutHandler) {
                        window.colorpickerShortcutHandler = new ColorpickerShortcutHandler();
                        window.colorpickerShortcutHandler.init();
                        
                        // Initialize with current shortcut value
                        setTimeout(() => {
                            if (this.settings.colorpickerShortcut) {
                                window.colorpickerShortcutHandler.updateInputValue(this.settings.colorpickerShortcut);
                            }
                        }, 100);
                    }
                } else {
                                    // Already loaded, just set the value
                setTimeout(() => {
                    if (window.colorpickerShortcutHandler && this.settings.colorpickerShortcut) {
                        window.colorpickerShortcutHandler.updateInputValue(this.settings.colorpickerShortcut);
                    }
                }, 100);
                }
            } else {
                console.log('Colorpicker shortcut input not found, skipping handler initialization');
            }

            // Load the screenshot shortcut handler script (only on settings page)
            if (document.querySelector('#screenshotCustomShortcut')) {
                if (!window.ScreenshotShortcutHandler) {
                    console.log('Loading screenshot shortcut handler...');
                    if (!window.screenshotShortcutHandler) {
                        window.screenshotShortcutHandler = new ScreenshotShortcutHandler();
                        window.screenshotShortcutHandler.init();
                        
                        // Initialize with current shortcut value
                        setTimeout(() => {
                            if (this.settings.screenshotShortcut) {
                                window.screenshotShortcutHandler.updateInputValue(this.settings.screenshotShortcut);
                            }
                        }, 100);
                    }
                } else {
                    // Already loaded, just set the value
                    setTimeout(() => {
                        if (window.screenshotShortcutHandler && this.settings.screenshotShortcut) {
                            window.screenshotShortcutHandler.updateInputValue(this.settings.screenshotShortcut);
                        }
                    }, 100);
                }
            } else {
                console.log('Screenshot shortcut input not found, skipping handler initialization');
            }
        } catch (error) {
            console.error('Error loading custom shortcut handler:', error);
        }
    }

    async loadHTMLCleanerModule() {
        return new Promise((resolve, reject) => {
            try {
                if (!window.HTMLCleanerAction) {
                    const script = document.createElement('script');
                    script.src = 'quick-actions/html-cleaner.js';
                    script.onload = () => {
                        console.log('HTML Cleaner module loaded successfully');
                        resolve();
                    };
                    script.onerror = (error) => {
                        console.error('Failed to load HTML Cleaner module:', error);
                        reject(error);
                    };
                    document.head.appendChild(script);
                } else {
                    resolve();
                }
            } catch (error) {
                console.error('Error loading HTML Cleaner module:', error);
                reject(error);
            }
        });
    }

    async loadUTMCleanerModule() {
        return new Promise((resolve, reject) => {
            try {
                if (!window.utmTrackingCleaner) {
                    const script = document.createElement('script');
                    script.src = 'utm-tracking-cleaner.js';
                    script.onload = () => {
                        console.log('UTM Tracking Cleaner module loaded successfully');
                        resolve();
                    };
                    script.onerror = (error) => {
                        console.error('Failed to load UTM Tracking Cleaner module:', error);
                        reject(error);
                    };
                    document.head.appendChild(script);
                } else {
                    resolve();
                }
            } catch (error) {
                console.error('Error loading UTM Tracking Cleaner module:', error);
                reject(error);
            }
        });
    }

    async loadGmailTimeFormatterModule() {
        return new Promise((resolve, reject) => {
            try {
                if (!window.stmGmailTimeFormatter) {
                    const script = document.createElement('script');
                    script.src = 'gmail-time-formatter.js';
                    script.onload = () => {
                        console.log('Gmail Time Formatter module loaded successfully');
                        resolve();
                    };
                    script.onerror = (error) => {
                        console.error('Failed to load Gmail Time Formatter module:', error);
                        reject(error);
                    };
                    document.head.appendChild(script);
                } else {
                    resolve();
                }
            } catch (error) {
                console.error('Error loading Gmail Time Formatter module:', error);
                reject(error);
            }
        });
    }

    setupControls() {
        // Toggle switches
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                // Skip if this is a select all toggle - it has its own handler
                if (e.target.id && e.target.id.includes('SelectAll')) {
                    return;
                }
                
                e.target.classList.toggle('toggle-switch--active');
                
                // Auto-save settings immediately for better UX
                setTimeout(() => {
                    this.collectSettings();
                    this.autoSaveSettings();
                    
                    // Update select all toggle states after individual toggle changes
                    this.updateSelectAllToggleStates();
                    
                    // Log for debugging
                    const setting = e.target.dataset.setting;
                    if (setting) {
                        const isActive = e.target.classList.contains('toggle-switch--active');
                        console.log(`Setting ${setting} changed to: ${isActive}`);
                        
                        // Special logging for specific settings
                        if (setting === 'autoCleanAndTitleEnabled') {
                            console.log('Auto Clean & Title setting changed to:', isActive);
                        }
                        
                        if (setting === 'pomodoroEnabled') {
                            console.log('🍅 Pomodoro toggle clicked! New state:', isActive);
                            
                            // If disabling Pomodoro, stop the timer immediately
                            if (!isActive) {
                                console.log('🍅 Pomodoro disabled - sending stop message to background');
                                chrome.runtime.sendMessage({
                                    action: 'pomodoroStop'
                                }, (response) => {
                                    if (response && response.success) {
                                        console.log('🍅 Pomodoro timer stopped successfully');
                                    } else {
                                        console.error('🍅 Failed to stop Pomodoro timer:', response);
                                    }
                                });
                            }
                        }
                    }
                }, 50);
            });
        });

        // Secret Developer Mode - Track rapid clicks on notifications toggle
        this.setupSecretDeveloperMode();

        // Clear data button
        const clearDataBtn = document.getElementById('clearDataBtn');
        if (clearDataBtn) {
            clearDataBtn.addEventListener('click', () => {
                this.clearAllData();
            });
        }

        // Export settings button
        const exportBtn = document.getElementById('exportSettingsBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', async () => {
                await this.exportSettings();
            });
        }

        // Import settings button
        const importBtn = document.getElementById('importSettingsBtn');
        if (importBtn) {
            importBtn.addEventListener('click', () => {
                document.getElementById('importSettingsFile').click();
            });
        }

        // File input for import
        const fileInput = document.getElementById('importSettingsFile');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.importSettings(e.target.files[0]);
            });
        }

        // HTML Cleaner Options button
        const htmlCleanerOptionsBtn = document.getElementById('htmlCleanerOptionsBtn');
        if (htmlCleanerOptionsBtn) {
            htmlCleanerOptionsBtn.addEventListener('click', async () => {
                console.log('HTML Cleaner Options button clicked');
                
                try {
                    // First load the HTML cleaner module if not already loaded
                    if (typeof window.HTMLCleanerAction === 'undefined') {
                        await this.loadHTMLCleanerModule();
                    }
                    
                    // Show the settings panel
                    const result = await window.HTMLCleanerAction.createSettingsPanel();
                    if (result) {
                        console.log('HTML Cleaner settings updated:', result);
                        this.showNotification('HTML cleaning options updated successfully', 'success');
                    }
                } catch (error) {
                    console.error('Error opening HTML cleaner options:', error);
                    this.showNotification('Error opening HTML cleaner options', 'error');
                }
            });
        }

        // UTM Tracking Cleaner Configuration button
        const utmTrackingCleanerConfigBtn = document.getElementById('utmTrackingCleanerConfigBtn');
        if (utmTrackingCleanerConfigBtn) {
            utmTrackingCleanerConfigBtn.addEventListener('click', async () => {
                console.log('UTM Tracking Cleaner Configuration button clicked');
                
                try {
                    // Load the UTM cleaner module if not already loaded
                    if (typeof window.utmTrackingCleaner === 'undefined') {
                        await this.loadUTMCleanerModule();
                    }
                    
                    // Show the settings panel
                    const result = await window.utmTrackingCleaner.createSettingsPanel();
                    if (result) {
                        console.log('UTM Tracking Cleaner settings updated:', result);
                        this.showNotification('UTM Tracking Cleaner options updated successfully', 'success');
                    }
                } catch (error) {
                    console.error('Error opening UTM Tracking Cleaner options:', error);
                    this.showNotification('Error opening UTM Tracking Cleaner options', 'error');
                }
            });
        }

        // Gmail Time Formatter Configuration button
        const gmailTimeFormatterConfigBtn = document.getElementById('gmailTimeConfigBtn');
        if (gmailTimeFormatterConfigBtn) {
            gmailTimeFormatterConfigBtn.addEventListener('click', async () => {
                console.log('Gmail Time Formatter Configuration button clicked');
                
                try {
                    // Load the Gmail time formatter module if not already loaded
                    if (typeof window.stmGmailTimeFormatter === 'undefined') {
                        await this.loadGmailTimeFormatterModule();
                    }
                    
                    // Show the settings panel
                    const result = await window.stmGmailTimeFormatter.createSettingsPanel();
                    if (result) {
                        console.log('Gmail Time Formatter settings updated:', result);
                        this.showNotification('Gmail time formatting options updated successfully', 'success');
                    }
                } catch (error) {
                    console.error('Error opening Gmail Time Formatter options:', error);
                    this.showNotification('Error opening Gmail time formatting options', 'error');
                }
            });
        }

        // Screenshot YouTube Configuration button
        const screenshotYouTubeConfigBtn = document.getElementById('screenshotYouTubeConfigBtn');
        if (screenshotYouTubeConfigBtn) {
            screenshotYouTubeConfigBtn.addEventListener('click', () => {
                console.log('Screenshot YouTube Configuration button clicked');
                
                try {
                    // Open options page in new window
                    const optionsUrl = chrome.runtime.getURL('settings/screenshot-youtube-options.html');
                    window.open(optionsUrl, 'screenshotYouTubeOptions', 'width=600,height=700,scrollbars=yes,resizable=yes');
                } catch (error) {
                    console.error('Error opening Screenshot YouTube options:', error);
                    this.showNotification('Error opening Screenshot YouTube options', 'error');
                }
            });
        }

        // YouTube API Key Save button
        const saveApiKeyBtn = document.getElementById('save-api-key');
        if (saveApiKeyBtn) {
            saveApiKeyBtn.addEventListener('click', async () => {
                await this.saveYouTubeApiKey();
            });
        }

        // YouTube API Key Clear button
        const clearApiKeyBtn = document.getElementById('clear-api-key');
        if (clearApiKeyBtn) {
            clearApiKeyBtn.addEventListener('click', async () => {
                await this.clearYouTubeApiKey();
            });
        }

        // Load and display current API key status
        this.loadYouTubeApiKeyStatus().catch(console.error);

        // Tracked Domains Accordion
        this.setupTrackedDomainsAccordion();
        
        // Tracked Domains Color Picker
        this.setupTrackedDomainsColorPicker();
        
        // Sponsored Highlighter Color Picker
        this.setupSponsoredHighlighterColorPicker();
        
        // Global Shortcut Settings
        this.setupGlobalShortcutSettings();
        
        // Drag Select Links Accordion
        this.setupDragSelectLinksAccordion();
        
        // Copy Replace Accordion
        this.setupCopyReplaceAccordion();
        
        // New Tab Redirect Accordion
        this.setupNewTabRedirectAccordion();
        
        // Custom Shortcut Inputs
        this.setupCustomShortcutInputs();
        
        // Drag Select Links Color Picker
        this.setupDragSelectLinksColorPicker();
        
        // Drag Select Links Format Handler
        this.setupDragSelectLinksFormatHandler();
        
        // New Settings Accordions
        this.setupGoogleToolsAccordion();
        this.setupBusinessToolsAccordion();
        this.setupTextToolsAccordion();
        this.setupDeveloperToolsAccordion();
        this.setupGeneralToolsAccordion();
        this.setupGmailToolsAccordion();
        this.setupYouTubeToolsAccordion();
        
        // UTM Cleaner Accordion
        this.setupUTMCleanerAccordion();
        
        // Video Speed Controller Accordion
        this.setupVideoSpeedAccordion();
        
        // Minimal Reader Accordion
        this.setupMinimalReaderAccordion();
        
        // Minimal Reader Blacklist Accordion
        this.setupMinimalReaderBlacklistAccordion();
        
        // Text Transformers Accordion
        this.setupTextTransformersAccordion();
        
        // Screenshot Tool Accordion
        this.setupScreenshotToolAccordion();
        this.setupScreenshotToolColorPicker();
        
        // Productivity Accordion
        this.setupProductivityAccordion();
        
        // Setup Select All/Deselect All toggles
        this.setupSelectAllToggles();
        
        // Setup Advanced Features Search
        this.setupAdvancedFeaturesSearch();
        
        // Setup Quick Actions Search
        this.setupQuickActionsSearch();
        
        // Initialize Quick Actions Reordering
        if (window.QuickActionsReorder && !window.quickActionsReorder) {
            window.quickActionsReorder = new window.QuickActionsReorder();
        }

        // Initialize Profiles
        if (typeof initializeProfiles === 'function') {
            initializeProfiles();
        }
    }

    setupSecretDeveloperMode() {
        // Secret developer mode - 8 rapid clicks on notifications toggle to show/hide debug mode
        let clickCount = 0;
        let clickTimer = null;
        const CLICK_WINDOW = 2000; // 2 seconds
        const REQUIRED_CLICKS = 8; // 8 rapid physical clicks
        
        const notificationsToggle = document.getElementById('notificationsToggle');
        const debugModeSection = document.getElementById('debugModeSection');
        
        if (notificationsToggle && debugModeSection) {
            // Load persistent state on initialization
            this.loadSecretDeveloperModeState();
            
            // Use capture phase to handle clicks before the normal toggle handler
            notificationsToggle.addEventListener('click', (e) => {
                clickCount++;
                
                // Clear existing timer
                if (clickTimer) {
                    clearTimeout(clickTimer);
                }
                
                // Check if we've reached the required number of clicks
                if (clickCount >= REQUIRED_CLICKS) {
                    // Toggle debug mode visibility
                    const isVisible = debugModeSection.style.display !== 'none';
                    
                    if (isVisible) {
                        debugModeSection.style.display = 'none';
                        debugModeSection.removeAttribute('data-developer-mode-active');
                        this.saveSecretDeveloperModeState(false);
                        
                        // FORCE DEBUG MODE OFF when hiding secret developer mode
                        const debugToggle = document.getElementById('debugModeToggle');
                        if (debugToggle) {
                            // Only log if debug mode toggle is actually enabled
                            if (debugToggle.classList.contains('toggle-switch--active')) {
                                console.log('Developer Mode: Hidden - Debug mode automatically disabled');
                            }
                            
                            // Turn OFF debug mode toggle and save settings
                            debugToggle.classList.remove('toggle-switch--active');
                            this.settings.debugMode = false;
                            this.saveSettings(false); // Save without notification
                            
                            // CONFLICT RESOLUTION: Clear Universal Debugger's saved state
                            // When secret mode is hidden, Universal Debugger should not restore previous state
                            // because secret hide is an explicit user action to disable debugging
                            if (typeof chrome !== 'undefined' && chrome.storage) {
                                chrome.storage.local.remove(['originalDebugModeState']).then(() => {
                                    console.log('🧹 Cleared Universal Debugger state - secret hide takes precedence');
                                }).catch(() => {
                                    // Silent fail - not critical
                                });
                            }
                            
                            // Send message to update all content scripts immediately
                            this.notifyContentScripts();
                        }
                    } else {
                        debugModeSection.style.display = 'block';
                        debugModeSection.setAttribute('data-developer-mode-active', 'true');
                        this.saveSecretDeveloperModeState(true);
                        // Never log activation - keep it secret until debug toggle is enabled
                    }
                    
                    // Reset click counter
                    clickCount = 0;
                } else {
                    // Set timer to reset click count if not enough clicks in time window
                    clickTimer = setTimeout(() => {
                        clickCount = 0;
                        // No logging - keep it secret
                    }, CLICK_WINDOW);
                }
            }, true); // Use capture phase
        }
        // No warning message - keep it secret
    }

    async loadSecretDeveloperModeState() {
        try {
            const result = await chrome.storage.local.get(['secretDeveloperModeActive']);
            const isActive = result.secretDeveloperModeActive === true;
            const debugModeSection = document.getElementById('debugModeSection');
            
            if (debugModeSection && isActive) {
                debugModeSection.style.display = 'block';
                debugModeSection.setAttribute('data-developer-mode-active', 'true');
            }
        } catch (error) {
            // Silent fail - keep it secret
        }
    }

    async saveSecretDeveloperModeState(isActive) {
        try {
            await chrome.storage.local.set({ secretDeveloperModeActive: isActive });
        } catch (error) {
            // Silent fail - keep it secret
        }
    }

    setupTrackedDomainsAccordion() {
        const header = document.getElementById('trackedDomainsHeader');
        const content = document.getElementById('trackedDomainsContent');
        const icon = document.getElementById('trackedDomainsIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupTrackedDomainsColorPicker() {
        const colorPicker = document.getElementById('trackedDomainsColorPicker');
        const hexInput = document.getElementById('trackedDomainsColor');
        
        if (colorPicker && hexInput) {
            // Sync color picker to hex input
            colorPicker.addEventListener('input', (e) => {
                hexInput.value = e.target.value.toUpperCase();
            });
            
            // Sync hex input to color picker with validation
            hexInput.addEventListener('input', (e) => {
                let value = e.target.value;
                
                // Add # if missing
                if (value && !value.startsWith('#')) {
                    value = '#' + value;
                    e.target.value = value;
                }
                
                // Validate hex color format
                const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
                if (hexRegex.test(value)) {
                    colorPicker.value = value;
                    // Remove any error styling
                    e.target.style.borderColor = '';
                    e.target.style.color = '#e5e5e5';
                } else if (value.length > 1) {
                    // Show error styling for invalid hex
                    e.target.style.borderColor = '#ef4444';
                    e.target.style.color = '#ef4444';
                }
            });
            
            // Handle paste events
            hexInput.addEventListener('paste', (e) => {
                setTimeout(() => {
                    let value = e.target.value.trim();
                    
                    // Clean up pasted value
                    if (value && !value.startsWith('#')) {
                        value = '#' + value;
                    }
                    
                    // Validate and update
                    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
                    if (hexRegex.test(value)) {
                        e.target.value = value.toUpperCase();
                        colorPicker.value = value;
                        e.target.style.borderColor = '';
                        e.target.style.color = '#e5e5e5';
                    }
                }, 10);
            });
            
            // Convert to uppercase on blur
            hexInput.addEventListener('blur', (e) => {
                if (e.target.value) {
                    e.target.value = e.target.value.toUpperCase();
                }
            });
        }
    }

    setupGlobalShortcutSettings() {
        const shortcutSelect = document.getElementById('copyElementShortcutKey');
        
        if (shortcutSelect) {
            shortcutSelect.addEventListener('change', (e) => {
                this.settings.copyElementShortcut = e.target.value;
                this.saveSettings();
                console.log('Global shortcut updated to:', e.target.value);
            });
        }
        
        // Note: Global shortcuts are controlled by individual feature toggles like copyElementEnabled
        // No separate global shortcut toggle needed at this time since we only have Copy Element
    }

    setupSponsoredHighlighterColorPicker() {
        const colorPicker = document.getElementById('sponsoredHighlighterColorPicker');
        const hexInput = document.getElementById('sponsoredHighlighterColor');
        
        if (colorPicker && hexInput) {
            // Sync color picker to hex input
            colorPicker.addEventListener('input', (e) => {
                hexInput.value = e.target.value.toUpperCase();
            });
            
            // Sync hex input to color picker with validation
            hexInput.addEventListener('input', (e) => {
                let value = e.target.value;
                
                // Add # if missing
                if (value && !value.startsWith('#')) {
                    value = '#' + value;
                    e.target.value = value;
                }
                
                // Validate hex color format
                const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
                if (hexRegex.test(value)) {
                    colorPicker.value = value;
                    // Remove any error styling
                    e.target.style.borderColor = '';
                    e.target.style.color = '#e5e5e5';
                } else if (value.length > 1) {
                    // Show error styling for invalid hex
                    e.target.style.borderColor = '#ef4444';
                    e.target.style.color = '#ef4444';
                }
            });
            
            // Handle paste events
            hexInput.addEventListener('paste', (e) => {
                setTimeout(() => {
                    let value = e.target.value.trim();
                    
                    // Clean up pasted value
                    if (value && !value.startsWith('#')) {
                        value = '#' + value;
                    }
                    
                    // Validate and update
                    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
                    if (hexRegex.test(value)) {
                        e.target.value = value.toUpperCase();
                        colorPicker.value = value;
                        e.target.style.borderColor = '';
                        e.target.style.color = '#e5e5e5';
                    }
                }, 10);
            });
            
            // Convert to uppercase on blur
            hexInput.addEventListener('blur', (e) => {
                if (e.target.value) {
                    e.target.value = e.target.value.toUpperCase();
                }
            });
        }
    }

    setupDragSelectLinksAccordion() {
        const header = document.getElementById('dragSelectLinksHeader');
        const content = document.getElementById('dragSelectLinksContent');
        const icon = document.getElementById('dragSelectLinksIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupCopyReplaceAccordion() {
        const header = document.getElementById('copyReplaceHeader');
        const content = document.getElementById('copyReplaceContent');
        const icon = document.getElementById('copyReplaceIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupNewTabRedirectAccordion() {
        const header = document.getElementById('newTabRedirectHeader');
        const content = document.getElementById('newTabRedirectContent');
        const icon = document.getElementById('newTabRedirectIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupCustomShortcutInputs() {
        // Find all custom shortcut input elements
        const customShortcutInputs = document.querySelectorAll('.custom-shortcut-input');
        
        customShortcutInputs.forEach(input => {
            // Skip if already has handlers (to prevent duplicates)
            if (input.hasAttribute('data-shortcut-handler-attached')) return;
            
            // Mark as having handlers attached
            input.setAttribute('data-shortcut-handler-attached', 'true');
            
            // Set up properties
            input.spellcheck = false;
            input.autocomplete = 'off';
            
            // Track capture state per input
            let isCapturing = false;
            
            // Handle keydown events for shortcut capture
            input.addEventListener('keydown', (e) => {
                console.log('Custom shortcut input event:', {
                    inputId: input.id,
                    key: e.key,
                    ctrlKey: e.ctrlKey,
                    altKey: e.altKey,
                    metaKey: e.metaKey,
                    shiftKey: e.shiftKey,
                    isCapturing: isCapturing
                });

                // Only handle during capture mode or if this is a potential shortcut
                if (!isCapturing && !(e.ctrlKey || e.altKey || e.metaKey || e.shiftKey)) {
                    return;
                }

                // Always prevent default for potential shortcuts
                e.preventDefault();
                e.stopPropagation();

                // Allow tab for navigation (don't capture Tab shortcuts)
                if (e.key === 'Tab' && !e.ctrlKey && !e.altKey && !e.metaKey) {
                    isCapturing = false;
                    input.classList.remove('capturing');
                    return;
                }

                // Handle clearing shortcuts
                if (['Backspace', 'Delete', 'Escape'].includes(e.key) && !e.ctrlKey && !e.altKey && !e.metaKey && !e.shiftKey) {
                    input.value = '';
                    this.saveShortcutFromInput(input, '');
                    return;
                }

                // Ignore standalone modifier keys
                if (['Control', 'Alt', 'Meta', 'Shift'].includes(e.key)) {
                    return;
                }

                const shortcut = this.formatShortcutFromEvent(e);
                if (shortcut) {
                    input.value = shortcut;
                    this.validateAndSaveCustomShortcut(input, shortcut);
                    
                    // Stop capturing after successful shortcut capture
                    setTimeout(() => {
                        isCapturing = false;
                        input.classList.remove('capturing');
                        input.blur(); // Remove focus after successful capture
                    }, 100);
                }
            });
            
            // Handle focus to start capture
            input.addEventListener('focus', () => {
                isCapturing = true;
                input.classList.add('capturing');
                input.select(); // Select all text for easy replacement
            });
            
            // Handle blur to stop capture
            input.addEventListener('blur', () => {
                isCapturing = false;
                input.classList.remove('capturing');
            });
        });
    }

    formatShortcutFromEvent(e) {
        const parts = [];
        
        // Add modifiers in consistent order
        if (e.ctrlKey) parts.push('Ctrl');
        if (e.altKey) parts.push('Alt');
        if (e.metaKey) parts.push('Cmd');
        if (e.shiftKey) parts.push('Shift');
        
        // Must have at least one modifier for global shortcuts
        if (parts.length === 0) {
            return null;
        }

        // Special keys mapping
        const specialKeys = {
            'arrowup': '↑',
            'arrowdown': '↓',
            'arrowleft': '←',
            'arrowright': '→',
            'enter': '↵',
            'tab': '⇥',
            'escape': 'Esc',
            'backspace': '⌫',
            'delete': '⌦',
            'space': ' '
        };

        const key = e.key;
        const keyLower = key.toLowerCase();

        // Handle different types of keys
        if (specialKeys[keyLower]) {
            parts.push(specialKeys[keyLower]);
        } else if (/^f(1[0-2]|[1-9])$/i.test(keyLower)) {
            // Function keys
            parts.push(keyLower.toUpperCase());
        } else if (key.length === 1) {
            // Single characters - preserve exact character
            parts.push(key);
        } else {
            return null; // Unsupported key
        }
        
        return parts.join('+');
    }

    async validateAndSaveCustomShortcut(inputElement, shortcut) {
        if (!shortcut || !inputElement) return;

        // Get the storage key from data-setting attribute
        const storageKey = inputElement.getAttribute('data-setting');
        if (!storageKey) return;

        // Check for duplicates using global validation system
        if (window.validateShortcut && inputElement) {
            const toolName = this.getToolNameFromStorageKey(storageKey);
            
            const isValid = await window.validateShortcut(
                inputElement, 
                shortcut, 
                toolName, 
                storageKey,
                {
                    showInlineErrors: true,
                    allowOverwrite: true
                }
            );
            
            if (!isValid) {
                // Reset input to previous value on validation failure
                this.loadShortcut(storageKey, inputElement);
                return;
            }
        }

        // Save the shortcut
        this.saveShortcutFromInput(inputElement, shortcut);
    }

    saveShortcutFromInput(inputElement, shortcut) {
        const storageKey = inputElement.getAttribute('data-setting');
        if (!storageKey) return;

        // Save to settings object (LOCAL storage via autoSaveSettings)
        if (!this.settings) this.settings = {};
        this.settings[storageKey] = shortcut;

        // Auto-save the settings to LOCAL storage
        this.autoSaveSettings();
        
        // ALSO save to SYNC storage for global shortcuts that need to be accessible across all tabs
        if (storageKey === 'newTabRedirectShortcut') {
            chrome.storage.sync.set({ [storageKey]: shortcut });
            console.log(`Custom shortcut also saved to SYNC storage for ${storageKey}:`, shortcut);
        }
        
        console.log(`Custom shortcut saved for ${storageKey}:`, shortcut);
    }

    setupDragSelectLinksColorPicker() {
        const colorPicker = document.getElementById('dragSelectLinksColorPicker');
        const hexInput = document.getElementById('dragSelectLinksColor');
        
        if (colorPicker && hexInput) {
            // Sync color picker to hex input
            colorPicker.addEventListener('input', (e) => {
                hexInput.value = e.target.value.toUpperCase();
            });
            
            // Sync hex input to color picker with validation
            hexInput.addEventListener('input', (e) => {
                let value = e.target.value;
                
                // Add # if missing
                if (value && !value.startsWith('#')) {
                    value = '#' + value;
                    e.target.value = value;
                }
                
                // Validate hex color format
                const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
                if (hexRegex.test(value)) {
                    colorPicker.value = value;
                    // Remove any error styling
                    e.target.style.borderColor = '';
                    e.target.style.color = '#e5e5e5';
                } else if (value.length > 1) {
                    // Show error styling for invalid hex
                    e.target.style.borderColor = '#ef4444';
                    e.target.style.color = '#ef4444';
                }
            });
            
            // Handle paste events
            hexInput.addEventListener('paste', (e) => {
                setTimeout(() => {
                    let value = e.target.value.trim();
                    
                    // Clean up pasted value
                    if (value && !value.startsWith('#')) {
                        value = '#' + value;
                    }
                    
                    // Validate and update
                    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
                    if (hexRegex.test(value)) {
                        e.target.value = value.toUpperCase();
                        colorPicker.value = value;
                        e.target.style.borderColor = '';
                        e.target.style.color = '#e5e5e5';
                    }
                }, 10);
            });
            
            // Convert to uppercase on blur
            hexInput.addEventListener('blur', (e) => {
                if (e.target.value) {
                    e.target.value = e.target.value.toUpperCase();
                }
            });
        }
    }

    setupDragSelectLinksFormatHandler() {
        const formatSelect = document.getElementById('dragSelectLinksOutputFormat');
        const customFormatGroup = document.getElementById('dragSelectCustomFormatGroup');
        
        if (formatSelect && customFormatGroup) {
            const toggleCustomFormat = () => {
                const isCustom = formatSelect.value === 'custom';
                customFormatGroup.style.display = isCustom ? 'block' : 'none';
            };
            
            // Initial state
            toggleCustomFormat();
            
            // Handle format changes
            formatSelect.addEventListener('change', toggleCustomFormat);
        }
    }

    setupGoogleToolsAccordion() {
        const header = document.getElementById('googleToolsHeader');
        const content = document.getElementById('googleToolsContent');
        const icon = document.getElementById('googleToolsIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupBusinessToolsAccordion() {
        const header = document.getElementById('businessToolsHeader');
        const content = document.getElementById('businessToolsContent');
        const icon = document.getElementById('businessToolsIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupTextToolsAccordion() {
        const header = document.getElementById('textToolsHeader');
        const content = document.getElementById('textToolsContent');
        const icon = document.getElementById('textToolsIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupDeveloperToolsAccordion() {
        const header = document.getElementById('developerToolsHeader');
        const content = document.getElementById('developerToolsContent');
        const icon = document.getElementById('developerToolsIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupGeneralToolsAccordion() {
        const header = document.getElementById('generalToolsHeader');
        const content = document.getElementById('generalToolsContent');
        const icon = document.getElementById('generalToolsIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupGmailToolsAccordion() {
        const header = document.getElementById('gmailToolsHeader');
        const content = document.getElementById('gmailToolsContent');
        const icon = document.getElementById('gmailToolsIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupYouTubeToolsAccordion() {
        const header = document.getElementById('youtubeToolsHeader');
        const content = document.getElementById('youtubeToolsContent');
        const icon = document.getElementById('youtubeToolsIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupUTMCleanerAccordion() {
        const header = document.getElementById('utmCleanerHeader');
        const content = document.getElementById('utmCleanerContent');
        const icon = document.getElementById('utmCleanerIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
        
        // Setup UTM whitelist domain validation
        this.setupUTMWhitelistValidation();
    }
    
    setupVideoSpeedAccordion() {
        const header = document.getElementById('videoSpeedHeader');
        const content = document.getElementById('videoSpeedContent');
        const icon = document.getElementById('videoSpeedIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
        
        // Setup opacity slider
        this.setupVideoSpeedOpacitySlider();
        
        // Setup shortcuts button
        this.setupVideoSpeedShortcutsButton();
    }

    setupMinimalReaderAccordion() {
        const header = document.getElementById('minimalReaderHeader');
        const content = document.getElementById('minimalReaderContent');
        const icon = document.getElementById('minimalReaderIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
        
        // Setup font size slider
        this.setupMinimalReaderFontSizeSlider();
        
        // Setup line height slider
        this.setupMinimalReaderLineHeightSlider();
        
        // Setup minimal reader configuration button
        this.setupMinimalReaderConfigButton();
    }
    
    setupScreenshotToolAccordion() {
        const header = document.getElementById('screenshotToolHeader');
        const content = document.getElementById('screenshotToolContent');
        const icon = document.getElementById('screenshotToolIcon');
        const configBtn = document.getElementById('screenshotToolConfigBtn');
        
        if (header && content && icon) {
            const toggleAccordion = () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            };
            
            header.addEventListener('click', toggleAccordion);
            
            // Also open accordion when config button is clicked
            if (configBtn) {
                configBtn.addEventListener('click', () => {
                    if (!content.classList.contains('expanded')) {
                        toggleAccordion();
                    }
                });
            }
        }
        
    }
    
    setupScreenshotToolColorPicker() {
        const colorPicker = document.getElementById('screenshotToolColorPicker');
        const hexInput = document.getElementById('screenshotToolDrawingColor');
        
        if (!colorPicker || !hexInput) return;
        
        // Sync color picker with hex input
        colorPicker.addEventListener('change', (e) => {
            const color = e.target.value.toUpperCase();
            hexInput.value = color;
            this.autoSaveSettings();
        });
        
        // Sync hex input with color picker
        hexInput.addEventListener('input', (e) => {
            const color = e.target.value;
            if (/^#[0-9A-F]{6}$/i.test(color)) {
                colorPicker.value = color;
                this.autoSaveSettings();
            }
        });
    }

    setupMinimalReaderFontSizeSlider() {
        const slider = document.getElementById('minimalReaderFontSize');
        const valueDisplay = document.getElementById('minimalReaderFontSizeValue');
        
        if (slider && valueDisplay) {
            slider.addEventListener('input', (e) => {
                const value = e.target.value;
                valueDisplay.textContent = value + 'px';
            });
        }
    }

    setupMinimalReaderLineHeightSlider() {
        const slider = document.getElementById('minimalReaderLineHeight');
        const valueDisplay = document.getElementById('minimalReaderLineHeightValue');
        
        if (slider && valueDisplay) {
            slider.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                valueDisplay.textContent = value.toFixed(1);
            });
        }
    }

    setupMinimalReaderConfigButton() {
        // Configuration button removed - all settings are now in the accordion
        // This method is kept for compatibility but does nothing
    }

    setupMinimalReaderBlacklistAccordion() {
        const header = document.getElementById('minimalReaderBlacklistHeader');
        const content = document.getElementById('minimalReaderBlacklistContent');
        const icon = document.getElementById('minimalReaderBlacklistIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
        
        // Setup blacklist domain validation
        this.setupMinimalReaderBlacklistValidation();
    }

    setupMinimalReaderBlacklistValidation() {
        const textarea = document.getElementById('minimalReaderBlacklistDomains');
        if (textarea) {
            textarea.addEventListener('input', (e) => {
                const domains = e.target.value.split('\n').map(d => d.trim()).filter(d => d);
                console.log('Minimal Reader: Blacklist domains updated:', domains);
            });
        }
    }

    setupTextTransformersAccordion() {
        const header = document.getElementById('textTransformersHeader');
        const content = document.getElementById('textTransformersContent');
        const icon = document.getElementById('textTransformersIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
        
        // Setup shortcut recording for all Text Transformers inputs
        this.setupTextTransformersShortcutRecording();
    }

    setupTextTransformersShortcutRecording() {
        const shortcutInputs = [
            'textTransformersCapitalCaseShortcut',
            'textTransformersLowerCaseShortcut', 
            'textTransformersUpperCaseShortcut',
            'textTransformersSentenceCaseShortcut',
            'textTransformersSlugifyShortcut',
            'textTransformersTrimToPageShortcut',
            'textTransformersSortAlphabeticallyShortcut',
            'textTransformersRemoveEmptyLinesShortcut',
            'textTransformersRemoveDuplicateLinesShortcut',
            'textTimeMachineLauncherShortcut'
        ];

        shortcutInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                this.setupShortcutInput(input, inputId);
            }
        });
    }

    setupShortcutInput(inputElement, storageKey) {
        let isCapturing = false;
        const isMac = navigator.platform.toLowerCase().includes('mac');

        // Setup event listeners for shortcut capture
        inputElement.addEventListener('keydown', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Handle clearing shortcuts
            if (['Backspace', 'Delete', 'Escape'].includes(e.key) && !e.ctrlKey && !e.altKey && !e.metaKey && !e.shiftKey) {
                inputElement.value = '';
                this.saveShortcut(storageKey, '', inputElement);
                return;
            }

            // Ignore standalone modifier keys
            if (['Control', 'Alt', 'Meta', 'Shift'].includes(e.key)) {
                return;
            }

            const shortcut = this.formatShortcut(e);
            if (shortcut) {
                inputElement.value = shortcut;
                this.saveShortcut(storageKey, shortcut, inputElement);
            }
        });

        inputElement.addEventListener('focus', () => {
            isCapturing = true;
            inputElement.placeholder = 'Press keys now...';
            inputElement.select();
        });

        inputElement.addEventListener('blur', () => {
            isCapturing = false;
            inputElement.placeholder = 'Click and press keys...';
        });

        // Load saved shortcut
        this.loadShortcut(storageKey, inputElement);
    }

    formatShortcut(e) {
        const parts = [];
        
        // Add modifiers in consistent order
        if (e.ctrlKey) parts.push('Ctrl');
        if (e.altKey) parts.push('Alt');
        if (e.metaKey) parts.push('Cmd');
        if (e.shiftKey) parts.push('Shift');
        
        // Must have at least one modifier for global shortcuts
        if (parts.length === 0) {
            return null;
        }

        // Special keys mapping
        const specialKeys = {
            'arrowup': '↑',
            'arrowdown': '↓',
            'arrowleft': '←',
            'arrowright': '→',
            'enter': '↵',
            'tab': '⇥',
            'escape': 'Esc',
            'backspace': '⌫',
            'delete': '⌦',
            'space': ' '
        };

        const key = e.key;
        const keyLower = key.toLowerCase();

        // Handle different types of keys
        if (specialKeys[keyLower]) {
            parts.push(specialKeys[keyLower]);
        } else if (/^f(1[0-2]|[1-9])$/i.test(keyLower)) {
            // Function keys
            parts.push(keyLower.toUpperCase());
        } else if (key.length === 1) {
            // Single characters - preserve exact character
            parts.push(key);
        } else {
            return null;
        }
        
        return parts.join('+');
    }

    async saveShortcut(storageKey, shortcut, inputElement = null) {
        // If clearing shortcut, no validation needed
        if (!shortcut) {
            try {
                await chrome.storage.sync.set({ [storageKey]: shortcut });
                console.log(`Text Transformers: ${storageKey} cleared`);
            } catch (error) {
                console.error(`Error clearing ${storageKey}:`, error);
            }
            return;
        }
        
        // Check for duplicates using global validation system
        if (window.validateShortcut && inputElement) {
            const toolName = this.getToolNameFromStorageKey(storageKey);
            
            const isValid = await window.validateShortcut(
                inputElement, 
                shortcut, 
                toolName, 
                storageKey,
                {
                    showInlineErrors: true,
                    allowOverwrite: true
                }
            );
            
            if (!isValid) {
                // Reset input to previous value on validation failure
                this.loadShortcut(storageKey, inputElement);
                return;
            }
        }
        
        try {
            await chrome.storage.sync.set({ [storageKey]: shortcut });
            console.log(`Text Transformers: ${storageKey} saved:`, shortcut);
        } catch (error) {
            console.error(`Error saving ${storageKey}:`, error);
        }
    }

    getToolNameFromStorageKey(storageKey) {
        const toolNames = {
            'textTransformersCapitalCaseShortcut': 'Text Transformers - Capital Case',
            'textTransformersLowerCaseShortcut': 'Text Transformers - Lower Case',
            'textTransformersUpperCaseShortcut': 'Text Transformers - Upper Case',
            'textTransformersSentenceCaseShortcut': 'Text Transformers - Sentence Case',
            'textTransformersSlugifyShortcut': 'Text Transformers - Slugify',
            'textTransformersTrimToPageShortcut': 'Text Transformers - Trim to Page',
            'textTransformersSortAlphabeticallyShortcut': 'Text Transformers - Sort Alphabetically',
            'textTransformersRemoveEmptyLinesShortcut': 'Text Transformers - Remove Empty Lines',
            'textTransformersRemoveDuplicateLinesShortcut': 'Text Transformers - Remove Duplicate Lines',
            'textTimeMachineLauncherShortcut': 'Text Time Machine Launcher',
            'newTabRedirectShortcut': 'New Tab Redirect'
        };
        return toolNames[storageKey] || 'Text Transformers';
    }

    async loadShortcut(storageKey, inputElement) {
        try {
            const result = await chrome.storage.sync.get([storageKey]);
            const savedShortcut = result[storageKey] || '';
            inputElement.value = savedShortcut;
        } catch (error) {
            console.error(`Error loading ${storageKey}:`, error);
        }
    }

    setupProductivityAccordion() {
        const header = document.getElementById('productivityHeader');
        const content = document.getElementById('productivityContent');
        const icon = document.getElementById('productivityIcon');
        
        if (header && content && icon) {
            header.addEventListener('click', () => {
                const isExpanded = content.classList.contains('expanded');
                
                if (isExpanded) {
                    content.classList.remove('expanded');
                    header.classList.remove('expanded');
                    icon.classList.remove('expanded');
                } else {
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                    icon.classList.add('expanded');
                }
            });
        }
    }

    setupUTMWhitelistValidation() {
        const whitelistTextarea = document.getElementById('utmCleanerWhitelistDomains');
        
        if (!whitelistTextarea) {
            console.log('UTM whitelist textarea not found, skipping validation setup');
            return;
        }

        // Add status display element after the textarea
        let statusElement = whitelistTextarea.parentNode.querySelector('.utm-domain-validation-status');
        if (!statusElement) {
            statusElement = document.createElement('div');
            statusElement.className = 'utm-domain-validation-status';
            statusElement.style.cssText = `
                margin-top: 8px;
                font-size: 12px;
                color: #9ca3af;
                min-height: 16px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            `;
            whitelistTextarea.parentNode.insertBefore(statusElement, whitelistTextarea.nextSibling);
        }

        let validationTimeout;

        const validateDomains = async () => {
            const domains = whitelistTextarea.value
                .split('\n')
                .map(line => line.trim())
                .filter(line => line.length > 0);
            
            if (domains.length === 0) {
                statusElement.textContent = '';
                return;
            }
            
            try {
                // Load UTM cleaner module if not already loaded
                if (!window.utmTrackingCleaner) {
                    await this.loadUTMCleanerModule();
                }
                
                // Use the validation function from UTM cleaner
                if (window.utmTrackingCleaner && window.utmTrackingCleaner.validateWhitelistDomains) {
                    const validatedDomains = await window.utmTrackingCleaner.validateWhitelistDomains(domains, statusElement);
                    
                    // Update textarea with validated domains if any were removed
                    if (validatedDomains.length !== domains.length) {
                        whitelistTextarea.value = validatedDomains.join('\n');
                        // Trigger change event to save the updated domains
                        whitelistTextarea.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                } else {
                    console.warn('UTM cleaner validation functions not available');
                }
            } catch (error) {
                console.error('Error validating UTM whitelist domains:', error);
                if (statusElement) {
                    statusElement.textContent = 'Validation error occurred';
                    statusElement.style.color = '#ef4444';
                    setTimeout(() => {
                        statusElement.textContent = '';
                    }, 3000);
                }
            }
        };
        
        // Validate on Enter key
        whitelistTextarea.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                clearTimeout(validationTimeout);
                validationTimeout = setTimeout(validateDomains, 500);
            }
        });
        
        // Validate on blur (when user clicks away or closes)
        whitelistTextarea.addEventListener('blur', () => {
            clearTimeout(validationTimeout);
            validationTimeout = setTimeout(validateDomains, 100);
        });
        
        // Validate on paste
        whitelistTextarea.addEventListener('paste', () => {
            clearTimeout(validationTimeout);
            validationTimeout = setTimeout(validateDomains, 1000);
        });

        console.log('UTM whitelist domain validation setup completed');
    }
    
    setupVideoSpeedOpacitySlider() {
        const opacitySlider = document.getElementById('videoSpeedControllerOpacity');
        const opacityValue = document.getElementById('videoSpeedOpacityValue');
        
        if (opacitySlider && opacityValue) {
            // Update value display when slider changes
            opacitySlider.addEventListener('input', (e) => {
                const value = e.target.value;
                opacityValue.textContent = value;
                // Collect settings to ensure the new value is captured
                this.collectSettings();
                this.autoSaveSettings();
            });
            
            // Set initial value
            opacityValue.textContent = opacitySlider.value;
        }
    }
    
    setupVideoSpeedShortcutsButton() {
        const shortcutsBtn = document.getElementById('videoSpeedShortcutsBtn');
        
        if (shortcutsBtn) {
            shortcutsBtn.addEventListener('click', () => {
                // Show shortcuts customization popup
                this.showVideoSpeedShortcutsDialog();
            });
        }
    }
    
    showVideoSpeedShortcutsDialog() {
        // Create overlay
        const overlay = document.createElement('div');
        overlay.className = 'settings-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;
        
        // Create dialog
        const dialog = document.createElement('div');
        dialog.className = 'shortcuts-dialog';
        dialog.style.cssText = `
            background: #0a0a0a;
            border: 2px solid #7c3aed;
            border-radius: 12px;
            padding: 24px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            color: #d1d5db;
        `;
        
        // Get current shortcuts and filter/migrate deprecated actions
        let shortcuts = this.settings.videoSpeedControllerKeyBindings || [];
        
        // Filter out deprecated actions and migrate old ones
        shortcuts = shortcuts
            .filter(shortcut => shortcut.action !== 'reset') // Remove reset completely
            .map(shortcut => {
                // Convert old 'fast' action to 'preferred'
                if (shortcut.action === 'fast') {
                    return { ...shortcut, action: 'preferred' };
                }
                return shortcut;
            })
            .filter(shortcut => {
                // Only keep valid actions
                const validActions = ['display', 'slower', 'faster', 'rewind', 'advance', 'preferred'];
                return validActions.includes(shortcut.action);
            });
        
        // Save the cleaned shortcuts back to settings
        this.settings.videoSpeedControllerKeyBindings = shortcuts;
        
        // Create dialog content
        dialog.innerHTML = `
            <h2 style="margin: 0 0 20px 0; color: #fff; font-size: 20px;">Customize Video Speed Shortcuts</h2>
            <div id="shortcutsContainer">
                ${shortcuts.map((shortcut, index) => `
                    <div class="shortcut-item" style="margin-bottom: 16px; padding: 12px; background: #1a1a1a; border-radius: 8px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: 500;">${this.getActionLabel(shortcut.action)}</span>
                            <input type="text" 
                                   class="shortcut-key-input" 
                                   data-index="${index}"
                                   value="${this.getKeyLabel(shortcut.key)}" 
                                   readonly
                                   style="width: 80px; text-align: center; background: #262626; border: 1px solid #7c3aed; 
                                          color: #fff; padding: 4px 8px; border-radius: 4px; cursor: pointer;">
                        </div>
                        ${shortcut.action === 'slower' || shortcut.action === 'faster' ? `
                            <div style="margin-top: 8px;">
                                <label style="font-size: 12px; color: #9ca3af;">Speed change: </label>
                                <input type="number" 
                                       class="shortcut-value-input" 
                                       data-index="${index}"
                                       value="${shortcut.value}" 
                                       min="0.05" 
                                       max="1" 
                                       step="0.05"
                                       style="width: 60px; background: #262626; border: 1px solid #2a2a2a; 
                                              color: #fff; padding: 2px 4px; border-radius: 4px;">
                            </div>
                        ` : ''}
                        ${shortcut.action === 'rewind' || shortcut.action === 'advance' ? `
                            <div style="margin-top: 8px;">
                                <label style="font-size: 12px; color: #9ca3af;">Seconds: </label>
                                <input type="number" 
                                       class="shortcut-value-input" 
                                       data-index="${index}"
                                       value="${shortcut.value}" 
                                       min="1" 
                                       max="60" 
                                       step="1"
                                       style="width: 60px; background: #262626; border: 1px solid #2a2a2a; 
                                              color: #fff; padding: 2px 4px; border-radius: 4px;">
                            </div>
                        ` : ''}
                        ${shortcut.action === 'preferred' ? `
                            <div style="margin-top: 8px;">
                                <label style="font-size: 12px; color: #9ca3af;">Target speed: </label>
                                <input type="number" 
                                       class="shortcut-value-input" 
                                       data-index="${index}"
                                       value="${shortcut.value}" 
                                       min="0.5" 
                                       max="4" 
                                       step="0.1"
                                       style="width: 60px; background: #262626; border: 1px solid #2a2a2a; 
                                              color: #fff; padding: 2px 4px; border-radius: 4px;">
                            </div>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
            <div style="margin-top: 24px; display: flex; gap: 12px; justify-content: flex-end;">
                <button id="cancelShortcuts" style="padding: 8px 16px; background: #262626; color: #9ca3af; 
                        border: 1px solid #2a2a2a; border-radius: 6px; cursor: pointer;">Cancel</button>
                <button id="saveShortcuts" style="padding: 8px 16px; background: #7c3aed; color: #fff; 
                        border: none; border-radius: 6px; cursor: pointer;">Save Changes</button>
            </div>
        `;
        
        overlay.appendChild(dialog);
        document.body.appendChild(overlay);
        
        // Handle key input
        dialog.querySelectorAll('.shortcut-key-input').forEach(input => {
            input.addEventListener('click', (e) => {
                e.target.value = 'Press a key...';
                e.target.addEventListener('keydown', (event) => {
                    event.preventDefault();
                    const index = parseInt(e.target.dataset.index);
                    shortcuts[index].key = event.keyCode;
                    e.target.value = this.getKeyLabel(event.keyCode);
                    e.target.blur();
                }, { once: true });
            });
        });
        
        // Handle value inputs
        dialog.querySelectorAll('.shortcut-value-input').forEach(input => {
            input.addEventListener('change', (e) => {
                const index = parseInt(e.target.dataset.index);
                shortcuts[index].value = parseFloat(e.target.value);
            });
        });
        
        // Handle save
        document.getElementById('saveShortcuts').addEventListener('click', async () => {
            try {
                // Don't assign to this.settings directly since it's a large setting
                // Instead, save directly to the appropriate storage
                
                // Load current local settings
                const localResult = await chrome.storage.local.get('gmbExtractorLargeSettings');
                const localSettings = localResult.gmbExtractorLargeSettings || {};
                
                // Update keyBindings in local settings
                localSettings.videoSpeedControllerKeyBindings = shortcuts;
                
                // Save to local storage
                await chrome.storage.local.set({ gmbExtractorLargeSettings: localSettings });
                
                // Update the in-memory settings for immediate use
                this.settings.videoSpeedControllerKeyBindings = shortcuts;
                
                overlay.remove();
                this.showNotification('Video speed shortcuts updated', 'success');
                
                // Immediately notify content script of changes
                this.broadcastSettingsChange();
            } catch (error) {
                console.error('Error saving shortcuts:', error);
                this.showNotification('Failed to save shortcuts: ' + error.message, 'error');
            }
        });
        
        // Handle cancel
        document.getElementById('cancelShortcuts').addEventListener('click', () => {
            overlay.remove();
        });
        
        // Close on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }
    
    getActionLabel(action) {
        const labels = {
            'display': 'Show/Hide Overlay',
            'slower': 'Decrease Speed',
            'faster': 'Increase Speed',
            'rewind': 'Rewind',
            'advance': 'Fast Forward',
            'preferred': 'Preferred Speed'
        };
        return labels[action] || action;
    }
    
    getKeyLabel(keyCode) {
        const keyCodeMap = {
            8: 'Backspace', 9: 'Tab', 13: 'Enter', 16: 'Shift', 17: 'Ctrl', 18: 'Alt',
            19: 'Pause', 20: 'Caps Lock', 27: 'Esc', 32: 'Space', 33: 'Page Up',
            34: 'Page Down', 35: 'End', 36: 'Home', 37: 'Left', 38: 'Up', 39: 'Right',
            40: 'Down', 45: 'Insert', 46: 'Delete', 48: '0', 49: '1', 50: '2', 51: '3',
            52: '4', 53: '5', 54: '6', 55: '7', 56: '8', 57: '9', 65: 'A', 66: 'B',
            67: 'C', 68: 'D', 69: 'E', 70: 'F', 71: 'G', 72: 'H', 73: 'I', 74: 'J',
            75: 'K', 76: 'L', 77: 'M', 78: 'N', 79: 'O', 80: 'P', 81: 'Q', 82: 'R',
            83: 'S', 84: 'T', 85: 'U', 86: 'V', 87: 'W', 88: 'X', 89: 'Y', 90: 'Z',
            91: 'Left Win', 92: 'Right Win', 93: 'Context', 96: 'Num 0', 97: 'Num 1',
            98: 'Num 2', 99: 'Num 3', 100: 'Num 4', 101: 'Num 5', 102: 'Num 6',
            103: 'Num 7', 104: 'Num 8', 105: 'Num 9', 106: 'Num *', 107: 'Num +',
            109: 'Num -', 110: 'Num .', 111: 'Num /', 112: 'F1', 113: 'F2', 114: 'F3',
            115: 'F4', 116: 'F5', 117: 'F6', 118: 'F7', 119: 'F8', 120: 'F9',
            121: 'F10', 122: 'F11', 123: 'F12', 186: ';', 187: '=', 188: ',',
            189: '-', 190: '.', 191: '/', 192: '`', 219: '[', 220: '\\', 221: ']',
            222: "'"
        };
        return keyCodeMap[keyCode] || String.fromCharCode(keyCode);
    }

    setupSelectAllToggles() {
        // Define accordion mappings with their toggle selectors
        const accordionMappings = {
            googleTools: {
                selectAllId: 'googleToolsSelectAll',
                settingSelectors: [
                    '[data-setting="locationChangerEnabled"]',
                    '[data-setting="serpNumbering"]', 
                    '[data-setting="sponsoredHighlighter"]',
                    '[data-setting="searchResultStats"]',
                    '[data-setting="topUrlsCopierEnabled"]',
                    '[data-setting="currentLocationDisplayEnabled"]',
                    '[data-setting="trackedDomainsEnabled"]'
                ]
            },
            businessTools: {
                selectAllId: 'businessToolsSelectAll',
                settingSelectors: [
                    '[data-setting="citationHunter"]',
                    '[data-setting="openSingleListing"]',
                    '[data-setting="searchNAPInjector"]'
                ]
            },
            developerTools: {
                selectAllId: 'developerToolsSelectAll',
                settingSelectors: [
                    '[data-setting="autoCleanAndTitleEnabled"]',
                    '[data-setting="clickToCopyEnabled"]',
                    '[data-setting="copyReplaceEnabled"]',
                    '[data-setting="dragSelectLinksEnabled"]',
                    '[data-setting="showhiddenAutoDetectionEnabled"]'
                ]
            },
            generalTools: {
                selectAllId: 'generalToolsSelectAll',
                settingSelectors: [
                    '[data-setting="utmTrackingCleanerEnabled"]'
                ]
            },
            gmailTools: {
                selectAllId: 'gmailToolsSelectAll',
                settingSelectors: [
                    '[data-setting="reverseGmailOrderEnabled"]',
                    '[data-setting="showGmailTimeEnabled"]',
                    '[data-setting="showGmailIconsEnabled"]',
                    '[data-setting="gmailThreadExpanderEnabled"]',
                    '[data-setting="gmailEnhancedTimestampsEnabled"]',
                    '[data-setting="massUnsubscribeEnabled"]',
                    '[data-setting="emailPinnerEnabled"]',
                    '[data-setting="gmailJumpLinksEnabled"]'
                ]
            },
            youtubeTools: {
                selectAllId: 'youtubeToolsSelectAll',
                settingSelectors: [
                    '[data-setting="youtubeAdsSkipperEnabled"]',
                    '[data-setting="videoSpeedControllerEnabled"]',
                    '[data-setting="screenshotYouTubeEnabled"]',
                    '[data-setting="youtubeThumbnailViewerEnabled"]',
                    '[data-setting="youtubeFramesEnabled"]',
                    '[data-setting="youtubeGifEnabled"]'
                ]
            },
            productivity: {
                selectAllId: 'productivitySelectAll',
                settingSelectors: [
                    '[data-setting="pomodoroEnabled"]',
                    '[data-setting="tasksEnabled"]',
                    '[data-setting="alertsEnabled"]',
                    '[data-setting="quickTimerEnabled"]',
                    '[data-setting="notificationUtilityEnabled"]',
                    '[data-setting="popupShortcutsEnabled"]'
                ]
            },
            textTools: {
                selectAllId: 'textToolsSelectAll',
                settingSelectors: [
                    '[data-setting="textTransformersEnabled"]'
                ]
            },
            quickActions: {
                selectAllId: 'quickActionsSelectAll',
                settingSelectors: [
                    '[data-setting="htagsEnabled"]',
                    '[data-setting="headingStructureEnabled"]',
                    '[data-setting="showLinksEnabled"]',
                    '[data-setting="showHiddenEnabled"]',
                    '[data-setting="keywordEnabled"]',
                    '[data-setting="boldFromSerpEnabled"]',
                    '[data-setting="schemaEnabled"]',
                    '[data-setting="imagesEnabled"]',
                    '[data-setting="metadataEnabled"]',
                    '[data-setting="utmBuilderEnabled"]',
                    '[data-setting="pageStructureEnabled"]',
                    '[data-setting="copyElementEnabled"]',
                    '[data-setting="linksExtractorEnabled"]',
                    '[data-setting="bulkLinkOpenEnabled"]',
                    '[data-setting="youtubeEmbedScraperEnabled"]',
                    '[data-setting="keyboardShortcutsEnabled"]',
                    '[data-setting="responsiveEnabled"]',
                    '[data-setting="seoTestsEnabled"]',
                    '[data-setting="trackerDetectionEnabled"]',
                    '[data-setting="colorPickerEnabled"]'
                ]
            }
        };

        // Setup each select all toggle
        Object.entries(accordionMappings).forEach(([accordionName, config]) => {
            const selectAllToggle = document.getElementById(config.selectAllId);
            if (!selectAllToggle) {
                console.warn(`Select all toggle not found: ${config.selectAllId}`);
                return;
            }

            // Get all toggle switches for this accordion
            const getAccordionToggles = () => {
                return config.settingSelectors
                    .map(selector => document.querySelector(selector))
                    .filter(toggle => toggle && toggle.classList.contains('toggle-switch'));
            };

            // Update select all toggle state based on individual toggles
            const updateSelectAllState = () => {
                try {
                    const toggles = getAccordionToggles();
                    if (toggles.length === 0) return;

                    const activeCount = toggles.filter(toggle => 
                        toggle.classList.contains('toggle-switch--active')
                    ).length;

                    // FIXED: Add protective check to prevent unnecessary DOM manipulation
                    const currentlyActive = selectAllToggle.classList.contains('toggle-switch--active');
                    const shouldBeActive = activeCount === toggles.length;
                    
                    // Only update if state actually needs to change
                    if (currentlyActive !== shouldBeActive) {
                        if (shouldBeActive) {
                            selectAllToggle.classList.add('toggle-switch--active');
                        } else {
                            selectAllToggle.classList.remove('toggle-switch--active');
                        }
                        console.log(`🎯 STM: Select all state updated for ${accordionName}: ${shouldBeActive} (${activeCount}/${toggles.length})`);
                    }
                } catch (error) {
                    console.error('🚨 STM: Error in updateSelectAllState:', error);
                }
            };

            // Handle select all toggle click
            selectAllToggle.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent the normal toggle handler from running
                
                const toggles = getAccordionToggles();
                if (toggles.length === 0) return;

                const shouldActivateAll = !selectAllToggle.classList.contains('toggle-switch--active');
                
                // Update all toggles in this accordion
                toggles.forEach(toggle => {
                    if (shouldActivateAll) {
                        toggle.classList.add('toggle-switch--active');
                    } else {
                        toggle.classList.remove('toggle-switch--active');
                    }
                });

                // Update select all toggle state
                if (shouldActivateAll) {
                    selectAllToggle.classList.add('toggle-switch--active');
                } else {
                    selectAllToggle.classList.remove('toggle-switch--active');
                }

                // Save settings
                setTimeout(() => {
                    this.collectSettings();
                    this.autoSaveSettings();
                    console.log(`${accordionName} select all toggled to: ${shouldActivateAll}`);
                }, 50);
            });

            // Listen for individual toggle changes to update select all state
            config.settingSelectors.forEach(selector => {
                const toggle = document.querySelector(selector);
                if (toggle && toggle.classList.contains('toggle-switch')) {
                    // FIXED: Filter out pomodoro-related toggles to prevent timer reset cascade
                    const isPomodorroToggle = selector.includes('pomodoro') || 
                                            selector.includes('task') || 
                                            toggle.closest('[data-setting*="pomodoro"]') ||
                                            toggle.closest('[data-setting*="task"]');
                    
                    if (isPomodorroToggle) {
                        console.log('🛡️ STM: Skipping MutationObserver for pomodoro toggle:', selector);
                        return; // Skip pomodoro toggles to prevent timer reset cascade
                    }
                    
                    // FIXED: Add debounced MutationObserver to prevent rapid firing
                    let updateDebounceTimer = null;
                    const observer = new MutationObserver(() => {
                        // Clear existing timer
                        if (updateDebounceTimer) {
                            clearTimeout(updateDebounceTimer);
                        }
                        
                        // Debounce the update to prevent rapid firing
                        updateDebounceTimer = setTimeout(() => {
                            console.log('🔄 STM: Debounced select all state update for:', selector);
                            updateSelectAllState();
                        }, 100);
                    });
                    
                    observer.observe(toggle, {
                        attributes: true,
                        attributeFilter: ['class']
                    });
                }
            });

            // Initial state update
            updateSelectAllState();
            
            console.log(`Setup select all toggle for ${accordionName} with ${config.settingSelectors.length} toggles`);
        });

        console.log('Select all toggles setup completed');
    }

    updateSelectAllToggleStates() {
        // Define the same accordion mappings as in setupSelectAllToggles
        const accordionMappings = {
            googleTools: {
                selectAllId: 'googleToolsSelectAll',
                settingSelectors: [
                    '[data-setting="locationChangerEnabled"]',
                    '[data-setting="serpNumbering"]', 
                    '[data-setting="sponsoredHighlighter"]',
                    '[data-setting="searchResultStats"]',
                    '[data-setting="topUrlsCopierEnabled"]',
                    '[data-setting="currentLocationDisplayEnabled"]',
                    '[data-setting="trackedDomainsEnabled"]'
                ]
            },
            businessTools: {
                selectAllId: 'businessToolsSelectAll',
                settingSelectors: [
                    '[data-setting="citationHunter"]',
                    '[data-setting="openSingleListing"]',
                    '[data-setting="searchNAPInjector"]'
                ]
            },
            developerTools: {
                selectAllId: 'developerToolsSelectAll',
                settingSelectors: [
                    '[data-setting="autoCleanAndTitleEnabled"]',
                    '[data-setting="clickToCopyEnabled"]',
                    '[data-setting="copyReplaceEnabled"]',
                    '[data-setting="dragSelectLinksEnabled"]',
                    '[data-setting="showhiddenAutoDetectionEnabled"]'
                ]
            },
            generalTools: {
                selectAllId: 'generalToolsSelectAll',
                settingSelectors: [
                    '[data-setting="utmTrackingCleanerEnabled"]'
                ]
            },
            gmailTools: {
                selectAllId: 'gmailToolsSelectAll',
                settingSelectors: [
                    '[data-setting="reverseGmailOrderEnabled"]',
                    '[data-setting="showGmailTimeEnabled"]',
                    '[data-setting="showGmailIconsEnabled"]',
                    '[data-setting="gmailThreadExpanderEnabled"]',
                    '[data-setting="gmailEnhancedTimestampsEnabled"]',
                    '[data-setting="massUnsubscribeEnabled"]',
                    '[data-setting="emailPinnerEnabled"]',
                    '[data-setting="gmailJumpLinksEnabled"]'
                ]
            },
            quickActions: {
                selectAllId: 'quickActionsSelectAll',
                settingSelectors: [
                    '[data-setting="htagsEnabled"]',
                    '[data-setting="headingStructureEnabled"]',
                    '[data-setting="showLinksEnabled"]',
                    '[data-setting="showHiddenEnabled"]',
                    '[data-setting="keywordEnabled"]',
                    '[data-setting="boldFromSerpEnabled"]',
                    '[data-setting="schemaEnabled"]',
                    '[data-setting="imagesEnabled"]',
                    '[data-setting="metadataEnabled"]',
                    '[data-setting="utmBuilderEnabled"]',
                    '[data-setting="pageStructureEnabled"]',
                    '[data-setting="copyElementEnabled"]',
                    '[data-setting="linksExtractorEnabled"]',
                    '[data-setting="bulkLinkOpenEnabled"]',
                    '[data-setting="youtubeEmbedScraperEnabled"]',
                    '[data-setting="keyboardShortcutsEnabled"]',
                    '[data-setting="responsiveEnabled"]',
                    '[data-setting="seoTestsEnabled"]',
                    '[data-setting="trackerDetectionEnabled"]',
                    '[data-setting="colorPickerEnabled"]'
                ]
            }
        };

        // Update each select all toggle state
        Object.entries(accordionMappings).forEach(([accordionName, config]) => {
            const selectAllToggle = document.getElementById(config.selectAllId);
            if (!selectAllToggle) return;

            // Get all toggle switches for this accordion
            const toggles = config.settingSelectors
                .map(selector => document.querySelector(selector))
                .filter(toggle => toggle && toggle.classList.contains('toggle-switch'));

            if (toggles.length === 0) return;

            // Count active toggles
            const activeCount = toggles.filter(toggle => 
                toggle.classList.contains('toggle-switch--active')
            ).length;

            // Update select all toggle state based on individual toggles
            if (activeCount === toggles.length) {
                // All toggles are active - select all should be active
                selectAllToggle.classList.add('toggle-switch--active');
            } else {
                // Not all toggles are active - select all should be inactive
                selectAllToggle.classList.remove('toggle-switch--active');
            }
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.classList.remove('settings-tab--active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('settings-tab--active');

        // Update panels
        document.querySelectorAll('.settings-panel').forEach(panel => {
            panel.classList.remove('settings-panel--active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('settings-panel--active');
        
        // Auto-focus search bar when switching to extras tab
        if (tabName === 'extras') {
            setTimeout(() => {
                const searchInput = document.getElementById('advancedFeaturesSearch');
                if (searchInput) {
                    searchInput.focus();
                }
            }, 100); // Small delay to ensure tab transition completes
        }
        
        // Auto-focus search bar when switching to quick-actions tab
        if (tabName === 'quick-actions') {
            setTimeout(() => {
                const searchInput = document.getElementById('quickActionsSearch');
                if (searchInput) {
                    searchInput.focus();
                }
            }, 100); // Small delay to ensure tab transition completes
        }
    }

    updateUI() {
        // Only update elements that actually exist in the HTML
        // Get all elements with data-setting attributes first
        const availableSettings = new Set();
        document.querySelectorAll('[data-setting]').forEach(element => {
            availableSettings.add(element.dataset.setting);
        });

        // Update only the settings that have corresponding HTML elements
        Object.entries(this.settings).forEach(([key, value]) => {
            // 🛡️ CRITICAL FIX: Skip timer duration settings since they don't exist in settings page
            const timerDurationSettings = ['pomodoroWorkDuration', 'pomodoroShortBreak', 'pomodoroLongBreak', 'pomodoroNumberOfCycles'];
            if (timerDurationSettings.includes(key)) {
                console.log(`🛡️ STM Settings: Skipped updateUI for timer duration setting: ${key}`);
                return; // Skip timer duration settings
            }
            
            if (availableSettings.has(key)) {
                const element = document.querySelector(`[data-setting="${key}"]`);
                if (element.classList.contains('toggle-switch')) {
                    element.classList.toggle('toggle-switch--active', value);
                } else if (element.tagName === 'INPUT' || element.tagName === 'SELECT') {
                    if (element.type === 'radio') {
                        // For radio buttons, check if this specific radio's value matches the setting
                        element.checked = (element.value === value);
                    } else if (element.type === 'checkbox') {
                        // For checkboxes, set the checked property based on boolean value
                        element.checked = value === true;
                    } else {
                        element.value = value;
                    }
                } else if (element.tagName === 'TEXTAREA') {
                    // Handle textarea for tracked domains list
                    if (key === 'trackedDomainsList') {
                        element.value = Array.isArray(value) ? value.join('\n') : '';
                    } else if (key === 'utmCleanerWhitelistDomains') {
                        element.value = Array.isArray(value) ? value.join('\n') : '';
                    } else {
                        element.value = value;
                    }
                }
                
                // Special handling for tracked domains color to sync both inputs
                if (key === 'trackedDomainsColor') {
                    const colorPicker = document.getElementById('trackedDomainsColorPicker');
                    if (colorPicker) {
                        colorPicker.value = value;
                    }
                }
                
                // Special handling for sponsored highlighter color to sync both inputs
                if (key === 'sponsoredHighlighterColor') {
                    const colorPicker = document.getElementById('sponsoredHighlighterColorPicker');
                    if (colorPicker) {
                        colorPicker.value = value;
                    }
                }
                
                // Special handling for drag select links color to sync both inputs
                if (key === 'dragSelectLinksColor') {
                    const colorPicker = document.getElementById('dragSelectLinksColorPicker');
                    if (colorPicker) {
                        colorPicker.value = value;
                    }
                }
                
                // Special handling for screenshot tool color to sync both inputs
                if (key === 'screenshotToolDrawingColor') {
                    const colorPicker = document.getElementById('screenshotToolColorPicker');
                    const hexInput = document.getElementById('screenshotToolDrawingColor');
                    if (colorPicker && hexInput) {
                        colorPicker.value = value;
                        hexInput.value = value;
                    }
                }
                
                // Special handling for global shortcut settings
                if (key === 'copyElementShortcut') {
                    const shortcutSelect = document.getElementById('copyElementShortcutKey');
                    if (shortcutSelect) {
                        shortcutSelect.value = value;
                    }
                    
                    // Also update custom shortcut input if available
                    const customInput = document.getElementById('copyElementCustomShortcut');
                    if (customInput) {
                        customInput.value = value || '';
                    }
                    
                    // Update custom shortcut handler if loaded
                    if (window.customShortcutHandler) {
                        window.customShortcutHandler.updateInputValue(value);
                    }
                }
                
                // Special handling for colorpicker shortcut settings
                if (key === 'colorpickerShortcut') {
                    // Update colorpicker shortcut input if available
                    const colorpickerInput = document.getElementById('colorpickerCustomShortcut');
                    if (colorpickerInput) {
                        colorpickerInput.value = value || '';
                    }
                    
                    // Update colorpicker shortcut handler if loaded
                    if (window.colorpickerShortcutHandler) {
                        window.colorpickerShortcutHandler.updateInputValue(value);
                    }
                }
                
                // Special handling for screenshot shortcut settings
                if (key === 'screenshotShortcut') {
                    // Update screenshot shortcut input if available
                    const screenshotInput = document.getElementById('screenshotCustomShortcut');
                    if (screenshotInput) {
                        screenshotInput.value = value || '';
                    }
                    
                    // Update screenshot shortcut handler if loaded
                    if (window.screenshotShortcutHandler) {
                        window.screenshotShortcutHandler.updateInputValue(value);
                    }
                }
                

            }
        });

        // Ensure debug mode section respects persistent secret developer mode state
        const debugModeSection = document.getElementById('debugModeSection');
        if (debugModeSection) {
            // Check if secret developer mode is persistently active
            chrome.storage.local.get(['secretDeveloperModeActive'], (result) => {
                const isSecretModeActive = result.secretDeveloperModeActive === true;
                if (isSecretModeActive) {
                    debugModeSection.style.display = 'block';
                    debugModeSection.setAttribute('data-developer-mode-active', 'true');
                } else if (!debugModeSection.hasAttribute('data-developer-mode-active')) {
                    debugModeSection.style.display = 'none';
                }
            });
        }


        // Update select all toggle states after UI is updated
        this.updateSelectAllToggleStates();
    }

    collectSettings() {
        // IMPORTANT: Start with existing settings to preserve values for elements not in current DOM
        // This fixes the issue where tracked domains were lost when exporting from a different tab
        const updatedSettings = { ...this.settings };
        
        // Collect from toggle switches
        document.querySelectorAll('.toggle-switch[data-setting]').forEach(toggle => {
            const setting = toggle.dataset.setting;
            const isActive = toggle.classList.contains('toggle-switch--active');
            updatedSettings[setting] = isActive;
            
            // Debug logging for Pomodoro
            if (setting === 'pomodoroEnabled') {
                console.log('🍅 collectSettings: pomodoroEnabled =', isActive);
            }
        });

        // Collect from inputs and selects
        document.querySelectorAll('input[data-setting], select[data-setting]').forEach(input => {
            const setting = input.dataset.setting;
            
            // 🛡️ CRITICAL FIX: Exclude timer duration settings from settings page collection
            // Timer inputs are in main popup (popup.html), not in settings page
            if (this.isTimerDurationSetting(input)) {
                console.log(`🛡️ STM Settings: Skipped collecting timer duration setting: ${setting}`);
                return; // Skip timer duration settings entirely
            }
            
            if (input.type === 'radio') {
                // For radio buttons, only collect the value if this radio is checked
                if (input.checked) {
                    updatedSettings[setting] = input.value;
                }
            } else if (input.type === 'checkbox') {
                // For checkboxes, collect the checked state as boolean
                updatedSettings[setting] = input.checked;
            } else {
                const value = input.type === 'number' ? parseInt(input.value) : input.value;
                updatedSettings[setting] = value;
            }
        });

        // Collect from textareas
        document.querySelectorAll('textarea[data-setting]').forEach(textarea => {
            const setting = textarea.dataset.setting;
            
            // Exclude Pomodoro blocking sites - these are stored separately in chrome.storage.local
            if (setting === 'pomodoroBlockedSites' || setting === 'pomodoroWhitelistedSites') {
                return; // Skip these settings
            }
            
            if (setting === 'trackedDomainsList' || setting === 'utmCleanerWhitelistDomains') {
                // Convert textarea lines to array, filtering out empty lines
                const domains = textarea.value
                    .split('\n')
                    .map(line => line.trim())
                    .filter(line => line.length > 0);
                updatedSettings[setting] = domains;
            } else {
                updatedSettings[setting] = textarea.value;
            }
        });

        // Collect quick actions order
        if (window.quickActionsReorder) {
            updatedSettings.quickActionsOrder = window.quickActionsReorder.getCurrentOrder();
        }
        
        // Update the settings object with collected values
        this.settings = updatedSettings;
    }

    resetToDefaults() {
        if (confirm('Are you sure you want to reset Extras and Quick Actions to defaults? This will turn off all feature toggles but preserve your personal settings like domains, colors, and formats.')) {
            console.log('🔄 Starting Extras and Quick Actions reset...');
            
            // Get all toggle settings that should be reset (Extras + Quick Actions)
            const toggleSettings = this.getAllToggleSettings();
            const resetSettings = {};
            
            // Set all toggle settings to false (preserving user data like domains, audio settings, etc.)
            Object.keys(toggleSettings).forEach(key => {
                resetSettings[key] = false;
            });
            
            console.log(`📊 Resetting ${Object.keys(resetSettings).length} toggle settings to false`);
            console.log('🔧 Settings being reset:', Object.keys(resetSettings).join(', '));
            
            // Preserve user data by merging with existing settings
            const updatedSettings = { ...this.settings, ...resetSettings };
            
            // Update settings object
            this.settings = updatedSettings;
            
            // Update UI and save
            this.updateUI();
            this.saveSettings();
            
            console.log('✅ Extras and Quick Actions reset complete - user data preserved');
        }
    }

    // Get all toggle settings that should be reset (Extras + Quick Actions only)
    getAllToggleSettings() {
        return {
            // Extras Settings - Google & Search Tools
            locationChangerEnabled: false,
            serpNumbering: false,
            sponsoredHighlighter: false,
            searchResultStats: false,
            citationHunter: false,
            openSingleListing: false,
            searchNAPInjector: false,
            currentLocationDisplayEnabled: false,
            trackedDomainsEnabled: false,
            topUrlsCopierEnabled: false,
            
            // Extras Settings - Gmail Tools
            reverseGmailOrderEnabled: false,
            showGmailTimeEnabled: false,
            showGmailIconsEnabled: false,
            massUnsubscribeEnabled: false,
            emailPinnerEnabled: false,
            gmailThreadExpanderEnabled: false,
            gmailEnhancedTimestampsEnabled: false,
            gmailJumpLinksEnabled: true,
            
            // Extras Settings - YouTube Tools
            youtubeAdsSkipperEnabled: false,
            screenshotYouTubeEnabled: false,
            screenshotKeyEnabled: false,
            youtubeThumbnailViewerEnabled: false,
            youtubeFramesEnabled: false,
            youtubeGifEnabled: false,
            
            // Extras Settings - UTM & URL Tools
            utmTrackingCleanerEnabled: false,
            utmCopyCleanEnabled: false,
            utmCleanAndGoEnabled: false,
            
            // Extras Settings - Browser Tools
            minimalReaderEnabled: false,
            screenshotToolEnabled: false,
            newTabRedirectEnabled: true,
            
            // Extras Settings - Productivity Tools
            tasksEnabled: false,
            pomodoroEnabled: false,
            alertsEnabled: false,
            quickTimerEnabled: false,
            notificationUtilityEnabled: false,
            pomodoroBlockingEnabled: false,
            
            // Extras Settings - Video Tools
            videoSpeedControllerEnabled: false,
            
            // Quick Actions Settings
            htagsEnabled: false,
            headingStructureEnabled: false,
            showLinksEnabled: false,
            showHiddenEnabled: false,
            keywordEnabled: false,
            boldFromSerpEnabled: false,
            schemaEnabled: true,
            schemaVisualizerEnabled: true,
            imagesEnabled: false,
            metadataEnabled: false,
            utmBuilderEnabled: false,
            pageStructureEnabled: false,
            copyElementEnabled: false,
            linksExtractorEnabled: false,
            youtubeEmbedScraperEnabled: false,
            keyboardShortcutsEnabled: false,
            responsiveEnabled: false,
            seoTestsEnabled: false,
            trackerDetectionEnabled: false,
            
            // Global & Browser Settings
            globalShortcutsEnabled: false,
            clickToCopyEnabled: false,
            copyReplaceEnabled: false,
            showhiddenAutoDetectionEnabled: false,
            dragSelectLinksEnabled: false,
            autoCleanAndTitleEnabled: false
        };
    }

    async clearAllData() {
        if (confirm('Are you sure you want to clear all extracted data and cache? This action cannot be undone.')) {
            try {
                // CRITICAL FIX: Use the storage protection utility for safe clearing
                console.log('🛡️ STM: Using storage protection utility for safe data clearing');
                
                // Check if storage protection utility is available
                if (window.STMStorageProtection) {
                    const result = await window.STMStorageProtection.safeClearStorage();
                    
                    if (result.success) {
                        this.showNotification(
                            `Data cleared successfully (${result.clearedCount} items cleared, ${result.protectedCount} protected)`, 
                            'success'
                        );
                        console.log('✅ STM: Storage protection utility successfully cleared data while preserving authentication');
                    } else {
                        throw new Error(result.error || 'Storage protection utility failed');
                    }
                } else {
                    // Fallback to manual protection if utility isn't available
                    console.warn('⚠️ STM: Storage protection utility not available, using fallback protection');
                    
                    // Get all storage keys first
                    const allData = await chrome.storage.local.get(null);
                    
                    // Define critical keys that should NEVER be cleared (authentication, cookies, etc.)
                    const protectedKeys = [
                        // Google authentication and cookies
                        'google_auth', 'google_session', 'google_cookies', 'auth_tokens',
                        'login_state', 'session_data', 'user_credentials', 'oauth_tokens',
                        // Browser and extension critical data
                        'extension_id', 'installation_data', 'browser_session',
                        // System preferences that should persist
                        'user_preferences', 'language_settings', 'accessibility_settings',
                        // Pomodoro timer state (active timers shouldn't be lost)
                        'pomodoroTimerState', 'pomodoroAudioSettings', 'chronometerState',
                        // Settings and configurations
                        'settings', 'profileSettings', 'quickActionSettings',
                        // Developer mode and debugging state
                        'developerModeEnabled', 'debugModeState', 'secretModeState'
                    ];
                    
                    // Only clear data keys, not system/auth keys
                    const keysToRemove = Object.keys(allData).filter(key => {
                        // Keep any key that starts with protected prefixes or matches protected patterns
                        return !protectedKeys.some(protectedKey => 
                            key.includes(protectedKey) || 
                            key.startsWith('auth') || 
                            key.startsWith('login') ||
                            key.startsWith('session') ||
                            key.startsWith('google') ||
                            key.startsWith('oauth') ||
                            key.startsWith('user_') ||
                            key.startsWith('browser_') ||
                            key.includes('Settings') ||
                            key.includes('State') ||
                            key.includes('Mode')
                        );
                    });
                    
                    // Remove only the safe-to-clear keys
                    if (keysToRemove.length > 0) {
                        await chrome.storage.local.remove(keysToRemove);
                        console.log(`🧹 STM: Cleared ${keysToRemove.length} data keys, preserved ${Object.keys(allData).length - keysToRemove.length} system keys`);
                    } else {
                        console.log('🛡️ STM: No data keys found to clear, all storage appears to be system critical');
                    }
                    
                    this.showNotification('Data cleared successfully (authentication preserved)', 'success');
                }
                
            } catch (error) {
                console.error('❌ STM: Error clearing data:', error);
                this.showNotification('Error clearing data: ' + error.message, 'error');
            }
        }
    }

    async exportSettings() {
        // Collect all current settings before export to ensure everything is captured
        this.collectSettings();
        
        // Save the collected settings to storage first so export gets the latest values
        await this.saveSettings(false); // false = no notification
        
        try {
            // Get all extension settings from storage
            const allStorageData = await chrome.storage.local.get([
                'gmbExtractorSettings',
                'gmbExtractorLargeSettings', // Large settings like video speed controller
                'htmlCleanerSettings', 
                'copyElementShortcut',
                'colorpickerShortcut',
                'copyReplaceShortcut',
                'quickActionsOrder',
                'globalShortcutsEnabled',
                'copyElementEnabled',
                'colorPickerEnabled',
                'massUnsubscribeWhitelist',
                'settings', // Location changer settings
                'locationChangerFavorites', // Location changer favorites
                'blockedSites', // Pomodoro website blocking list
                'allowedUrls', // Pomodoro allowed URLs list
                'pomodoroTodos', // Pomodoro todo items
                'minimalReaderBlacklistDomains', // Minimal reader domain blacklist
                'videoSpeedControllerBlacklist', // Video speed controller site blacklist
                'gmailPinnedEmails', // Gmail pinned emails - time-consuming to rebuild
                'youtubeApiKey', // YouTube API key - requires manual configuration
                'activeAlerts', // User-created alerts and reminders
                'bulkLinkOpenSavedLists', // Bulk link open saved lists
                'pomodoroAudioSettings' // Pomodoro audio customization
            ]);

            // Also get screenshot shortcut and text transformers shortcuts from sync storage
            const syncStorageData = await chrome.storage.sync.get([
                'screenshotShortcut',
                'textTransformersCapitalCaseShortcut',
                'textTransformersLowerCaseShortcut',
                'textTransformersUpperCaseShortcut',
                'textTransformersSentenceCaseShortcut',
                'textTransformersSlugifyShortcut',
                'textTransformersTrimToPageShortcut',
                'textTransformersSortAlphabeticallyShortcut',
                'textTransformersRemoveEmptyLinesShortcut',
                'textTransformersRemoveDuplicateLinesShortcut',
                'textTimeMachineLauncherShortcut',
                'newTabRedirectShortcut'
            ]);

            const profiles = await chrome.storage.local.get('profiles');
            
            // Create comprehensive export data
            const settingsData = {
                version: '2.0', // Increased version for comprehensive export
                timestamp: new Date().toISOString(),
                extensionVersion: chrome.runtime.getManifest?.()?.version || 'unknown',
                
                // Main extension settings
                gmbExtractorSettings: allStorageData.gmbExtractorSettings || this.settings,
                
                // Large settings (video speed controller, etc.)
                gmbExtractorLargeSettings: allStorageData.gmbExtractorLargeSettings || {},
                
                // Profiles
                profiles: profiles.profiles || [],

                // HTML Cleaner settings (all 8 options)
                htmlCleanerSettings: allStorageData.htmlCleanerSettings || {
                    removeStyles: true,
                    removeClasses: true,
                    removeIds: true,
                    removeComments: true,
                    removeEmptyTags: true,
                    removeExtraSpaces: true,
                    removeDataAttributes: true,
                    removeWrapperDivs: true
                },
                
                // Keyboard shortcut settings
                copyElementShortcut: allStorageData.copyElementShortcut || null,
                colorpickerShortcut: allStorageData.colorpickerShortcut || null,
                copyReplaceShortcut: allStorageData.copyReplaceShortcut || null,
                screenshotShortcut: syncStorageData.screenshotShortcut || null,
                
                // Text Transformers shortcuts
                textTransformersCapitalCaseShortcut: syncStorageData.textTransformersCapitalCaseShortcut || null,
                textTransformersLowerCaseShortcut: syncStorageData.textTransformersLowerCaseShortcut || null,
                textTransformersUpperCaseShortcut: syncStorageData.textTransformersUpperCaseShortcut || null,
                textTransformersSentenceCaseShortcut: syncStorageData.textTransformersSentenceCaseShortcut || null,
                textTransformersSlugifyShortcut: syncStorageData.textTransformersSlugifyShortcut || null,
                textTransformersTrimToPageShortcut: syncStorageData.textTransformersTrimToPageShortcut || null,
                textTransformersSortAlphabeticallyShortcut: syncStorageData.textTransformersSortAlphabeticallyShortcut || null,
                textTransformersRemoveEmptyLinesShortcut: syncStorageData.textTransformersRemoveEmptyLinesShortcut || null,
                textTransformersRemoveDuplicateLinesShortcut: syncStorageData.textTransformersRemoveDuplicateLinesShortcut || null,
                textTimeMachineLauncherShortcut: syncStorageData.textTimeMachineLauncherShortcut || null,
                newTabRedirectShortcut: syncStorageData.newTabRedirectShortcut || null,
                globalShortcutsEnabled: allStorageData.globalShortcutsEnabled || true,
                copyElementEnabled: allStorageData.copyElementEnabled !== undefined ? allStorageData.copyElementEnabled : true,
                colorPickerEnabled: allStorageData.colorPickerEnabled !== undefined ? allStorageData.colorPickerEnabled : true,
                
                // Quick Actions order configuration
                quickActionsOrder: allStorageData.quickActionsOrder || null,
                
                // Mass Unsubscribe whitelist
                massUnsubscribeWhitelist: allStorageData.massUnsubscribeWhitelist || [],
                
                // Location changer settings (legacy)
                locationSettings: allStorageData.settings || null,
                
                // Location changer favorites
                locationChangerFavorites: allStorageData.locationChangerFavorites || [],
                
                // Pomodoro website blocking settings
                blockedSites: allStorageData.blockedSites || null,
                allowedUrls: allStorageData.allowedUrls || null,
                
                // Pomodoro todo items
                pomodoroTodos: allStorageData.pomodoroTodos || null,
                
                // Minimal Reader blacklist domains
                minimalReaderBlacklistDomains: allStorageData.minimalReaderBlacklistDomains || [],
                
                // Video Speed Controller blacklist
                videoSpeedControllerBlacklist: allStorageData.videoSpeedControllerBlacklist || '',
                
                // Gmail pinned emails - time-consuming to curate
                gmailPinnedEmails: allStorageData.gmailPinnedEmails || [],
                
                // YouTube API key - requires manual configuration
                youtubeApiKey: allStorageData.youtubeApiKey || null,
                
                // Active alerts - user-created custom alerts
                activeAlerts: allStorageData.activeAlerts || [],
                
                // Bulk Link Open saved lists - curated link collections
                bulkLinkOpenSavedLists: allStorageData.bulkLinkOpenSavedLists || {},
                
                // Pomodoro audio settings - customized audio preferences
                pomodoroAudioSettings: allStorageData.pomodoroAudioSettings || null,
                
                // Include current UI settings for compatibility
                settings: {
                    // Include all current settings
                    ...this.settings
                },
                
                metadata: {
                    totalSettingsKeys: Object.keys(allStorageData).length,
                    settingsCount: Object.keys(this.settings).length,
                    trackedDomainsCount: Array.isArray(this.settings.trackedDomainsList) ? this.settings.trackedDomainsList.length : 0,
                    utmWhitelistDomainsCount: Array.isArray(this.settings.utmCleanerWhitelistDomains) ? this.settings.utmCleanerWhitelistDomains.length : 0,
                    massUnsubscribeWhitelistCount: Array.isArray(allStorageData.massUnsubscribeWhitelist) ? allStorageData.massUnsubscribeWhitelist.length : 0,
                    htmlCleanerOptionsCount: allStorageData.htmlCleanerSettings ? Object.keys(allStorageData.htmlCleanerSettings).length : 8,
                    hasCustomShortcuts: !!(allStorageData.copyElementShortcut || allStorageData.colorpickerShortcut || syncStorageData.screenshotShortcut || 
                        syncStorageData.textTransformersCapitalCaseShortcut || syncStorageData.textTransformersLowerCaseShortcut || 
                        syncStorageData.textTransformersUpperCaseShortcut || syncStorageData.textTransformersSentenceCaseShortcut || 
                        syncStorageData.textTransformersSlugifyShortcut || syncStorageData.textTransformersTrimToPageShortcut || 
                        syncStorageData.textTransformersSortAlphabeticallyShortcut || syncStorageData.textTransformersRemoveEmptyLinesShortcut || 
                        syncStorageData.textTransformersRemoveDuplicateLinesShortcut || syncStorageData.textTimeMachineLauncherShortcut),
                    hasCopyElementShortcut: !!allStorageData.copyElementShortcut,
                    hasColorpickerShortcut: !!allStorageData.colorpickerShortcut,
                    hasQuickActionsOrder: !!allStorageData.quickActionsOrder,
                    pomodoroTodosCount: allStorageData.pomodoroTodos && allStorageData.pomodoroTodos.todos ? allStorageData.pomodoroTodos.todos.length : 0,
                    pomodoroActiveTodosCount: allStorageData.pomodoroTodos && allStorageData.pomodoroTodos.todos ? allStorageData.pomodoroTodos.todos.filter(todo => !todo.completed).length : 0,
                    hasPomodoroTodos: !!(allStorageData.pomodoroTodos && allStorageData.pomodoroTodos.todos && allStorageData.pomodoroTodos.todos.length > 0),
                    minimalReaderBlacklistCount: Array.isArray(allStorageData.minimalReaderBlacklistDomains) ? allStorageData.minimalReaderBlacklistDomains.length : 0,
                    hasMinimalReaderBlacklist: Array.isArray(allStorageData.minimalReaderBlacklistDomains) && allStorageData.minimalReaderBlacklistDomains.length > 0,
                    hasVideoSpeedControllerBlacklist: !!(allStorageData.videoSpeedControllerBlacklist && allStorageData.videoSpeedControllerBlacklist.trim()),
                    locationFavoritesCount: Array.isArray(allStorageData.locationChangerFavorites) ? allStorageData.locationChangerFavorites.length : 0,
                    hasLocationFavorites: Array.isArray(allStorageData.locationChangerFavorites) && allStorageData.locationChangerFavorites.length > 0,
                    textTransformersShortcutsCount: [
                        syncStorageData.textTransformersCapitalCaseShortcut,
                        syncStorageData.textTransformersLowerCaseShortcut,
                        syncStorageData.textTransformersUpperCaseShortcut,
                        syncStorageData.textTransformersSentenceCaseShortcut,
                        syncStorageData.textTransformersSlugifyShortcut,
                        syncStorageData.textTransformersTrimToPageShortcut,
                        syncStorageData.textTransformersSortAlphabeticallyShortcut,
                        syncStorageData.textTransformersRemoveEmptyLinesShortcut,
                        syncStorageData.textTransformersRemoveDuplicateLinesShortcut,
                        syncStorageData.textTimeMachineLauncherShortcut
                    ].filter(Boolean).length,
                    hasTextTransformersShortcuts: !!(syncStorageData.textTransformersCapitalCaseShortcut || syncStorageData.textTransformersLowerCaseShortcut || 
                        syncStorageData.textTransformersUpperCaseShortcut || syncStorageData.textTransformersSentenceCaseShortcut || 
                        syncStorageData.textTransformersSlugifyShortcut || syncStorageData.textTransformersTrimToPageShortcut || 
                        syncStorageData.textTransformersSortAlphabeticallyShortcut || syncStorageData.textTransformersRemoveEmptyLinesShortcut || 
                        syncStorageData.textTransformersRemoveDuplicateLinesShortcut || syncStorageData.textTimeMachineLauncherShortcut),
                    // New time-consuming data metadata
                    gmailPinnedEmailsCount: Array.isArray(allStorageData.gmailPinnedEmails) ? allStorageData.gmailPinnedEmails.length : 0,
                    hasGmailPinnedEmails: Array.isArray(allStorageData.gmailPinnedEmails) && allStorageData.gmailPinnedEmails.length > 0,
                    hasYouTubeApiKey: !!(allStorageData.youtubeApiKey && allStorageData.youtubeApiKey.trim()),
                    activeAlertsCount: Array.isArray(allStorageData.activeAlerts) ? allStorageData.activeAlerts.length : 0,
                    activeAlertsEnabledCount: Array.isArray(allStorageData.activeAlerts) ? allStorageData.activeAlerts.filter(a => a.enabled).length : 0,
                    hasActiveAlerts: Array.isArray(allStorageData.activeAlerts) && allStorageData.activeAlerts.length > 0,
                    bulkLinkOpenListsCount: allStorageData.bulkLinkOpenSavedLists ? Object.keys(allStorageData.bulkLinkOpenSavedLists).length : 0,
                    hasBulkLinkOpenLists: !!(allStorageData.bulkLinkOpenSavedLists && Object.keys(allStorageData.bulkLinkOpenSavedLists).length > 0),
                    hasPomodoroAudioSettings: !!(allStorageData.pomodoroAudioSettings),
                    exportedBy: 'SEO Time Machines Settings Manager v2.0'
                }
            };

            const blob = new Blob([JSON.stringify(settingsData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `seo-time-machines-settings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            // Show comprehensive success message
            const trackedDomainsCount = Array.isArray(this.settings.trackedDomainsList) ? this.settings.trackedDomainsList.length : 0;
            const utmWhitelistCount = Array.isArray(this.settings.utmCleanerWhitelistDomains) ? this.settings.utmCleanerWhitelistDomains.length : 0;
            const massUnsubscribeWhitelistCount = Array.isArray(allStorageData.massUnsubscribeWhitelist) ? allStorageData.massUnsubscribeWhitelist.length : 0;
            const htmlCleanerOptions = settingsData.htmlCleanerSettings ? Object.keys(settingsData.htmlCleanerSettings).length : 0;
            const shortcutCount = [
                allStorageData.copyElementShortcut, 
                allStorageData.colorpickerShortcut, 
                allStorageData.copyReplaceShortcut,
                syncStorageData.screenshotShortcut,
                syncStorageData.textTransformersCapitalCaseShortcut,
                syncStorageData.textTransformersLowerCaseShortcut,
                syncStorageData.textTransformersUpperCaseShortcut,
                syncStorageData.textTransformersSentenceCaseShortcut,
                syncStorageData.textTransformersSlugifyShortcut,
                syncStorageData.textTransformersTrimToPageShortcut,
                syncStorageData.textTransformersSortAlphabeticallyShortcut,
                syncStorageData.textTransformersRemoveEmptyLinesShortcut,
                syncStorageData.textTransformersRemoveDuplicateLinesShortcut,
                syncStorageData.textTimeMachineLauncherShortcut
            ].filter(Boolean).length;
            const locationFavoritesCount = Array.isArray(allStorageData.locationChangerFavorites) ? allStorageData.locationChangerFavorites.length : 0;
            const gmailPinnedCount = Array.isArray(allStorageData.gmailPinnedEmails) ? allStorageData.gmailPinnedEmails.length : 0;
            const activeAlertsCount = Array.isArray(allStorageData.activeAlerts) ? allStorageData.activeAlerts.length : 0;
            const bulkLinkListsCount = allStorageData.bulkLinkOpenSavedLists ? Object.keys(allStorageData.bulkLinkOpenSavedLists).length : 0;
            
            // Build detailed export message
            const exportDetails = [
                `${Object.keys(this.settings).length} main settings`,
                trackedDomainsCount > 0 && `${trackedDomainsCount} tracked domains`,
                utmWhitelistCount > 0 && `${utmWhitelistCount} UTM whitelist domains`,
                massUnsubscribeWhitelistCount > 0 && `${massUnsubscribeWhitelistCount} mass unsubscribe whitelist entries`,
                htmlCleanerOptions > 0 && `${htmlCleanerOptions} HTML cleaning options`,
                shortcutCount > 0 && `${shortcutCount} custom shortcuts`,
                locationFavoritesCount > 0 && `${locationFavoritesCount} location favorites`,
                gmailPinnedCount > 0 && `${gmailPinnedCount} Gmail pinned emails`,
                allStorageData.youtubeApiKey && `YouTube API key`,
                activeAlertsCount > 0 && `${activeAlertsCount} active alerts`,
                bulkLinkListsCount > 0 && `${bulkLinkListsCount} bulk link lists`,
                allStorageData.pomodoroAudioSettings && `Pomodoro audio settings`,
                `Quick Actions configuration`
            ].filter(Boolean);
            
            this.showNotification(
                `Settings exported successfully! Included ${exportDetails.join(', ')}.`, 
                'success'
            );
            
            console.log('Comprehensive settings export completed:', settingsData);
            
        } catch (error) {
            console.error('Error during settings export:', error);
            this.showNotification('Error exporting settings: ' + error.message, 'error');
        }
    }

    async importSettings(file) {
        if (!file) return;

        try {
            const text = await file.text();
            const data = JSON.parse(text);
            
            // Support both v1.0 and v2.0 format
            const isV2Format = data.version === '2.0' && data.gmbExtractorSettings;
            const isV1Format = data.settings && !data.gmbExtractorSettings;
            
            if (!isV2Format && !isV1Format) {
                throw new Error('Invalid settings file format - unable to detect valid settings structure');
            }
            
            console.log(`Importing settings (format: ${data.version || 'v1.0'})`, data);
            
            let importStats = {
                mainSettings: 0,
                trackedDomains: 0,
                utmWhitelistDomains: 0,
                massUnsubscribeWhitelist: 0,
                htmlCleanerOptions: 0,
                shortcuts: 0,
                quickActionsOrder: false,
                pomodoroTodosCount: 0,
                minimalReaderBlacklistCount: 0,
                hasVideoSpeedControllerBlacklist: false,
                locationFavoritesCount: 0
            };
            
            // Handle v2.0 format (comprehensive)
            if (isV2Format) {
                // Import main settings
                if (data.gmbExtractorSettings) {
                    const mergedSettings = { ...this.defaultSettings, ...data.gmbExtractorSettings };
                    this.settings = mergedSettings;
                    await this.saveSettings();
                    importStats.mainSettings = Object.keys(data.gmbExtractorSettings).length;
                    importStats.trackedDomains = Array.isArray(data.gmbExtractorSettings.trackedDomainsList) ? data.gmbExtractorSettings.trackedDomainsList.length : 0;
                    importStats.utmWhitelistDomains = Array.isArray(data.gmbExtractorSettings.utmCleanerWhitelistDomains) ? data.gmbExtractorSettings.utmCleanerWhitelistDomains.length : 0;
                }

                // Import large settings
                if (data.gmbExtractorLargeSettings) {
                    await chrome.storage.local.set({ gmbExtractorLargeSettings: data.gmbExtractorLargeSettings });
                    console.log('Imported large settings:', data.gmbExtractorLargeSettings);
                }
                
                // Import profiles
                if (data.profiles && Array.isArray(data.profiles)) {
                    await chrome.storage.local.set({ profiles: data.profiles });
                    console.log('Imported profiles:', data.profiles);
                }
                
                // Import HTML Cleaner settings
                if (data.htmlCleanerSettings) {
                    await chrome.storage.local.set({ htmlCleanerSettings: data.htmlCleanerSettings });
                    importStats.htmlCleanerOptions = Object.keys(data.htmlCleanerSettings).length;
                    console.log('Imported HTML Cleaner settings:', data.htmlCleanerSettings);
                }
                
                // Import keyboard shortcuts
                if (data.copyElementShortcut) {
                    await chrome.storage.local.set({ copyElementShortcut: data.copyElementShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Copy Element shortcut:', data.copyElementShortcut);
                }
                
                if (data.colorpickerShortcut) {
                    await chrome.storage.local.set({ colorpickerShortcut: data.colorpickerShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Color Picker shortcut:', data.colorpickerShortcut);
                }
                
                if (data.copyReplaceShortcut) {
                    await chrome.storage.local.set({ copyReplaceShortcut: data.copyReplaceShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Copy Replace shortcut:', data.copyReplaceShortcut);
                }
                
                if (data.screenshotShortcut) {
                    await chrome.storage.sync.set({ screenshotShortcut: data.screenshotShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Screenshot shortcut:', data.screenshotShortcut);
                }
                
                // Import Text Transformers shortcuts (sync storage)
                if (data.textTransformersCapitalCaseShortcut) {
                    await chrome.storage.sync.set({ textTransformersCapitalCaseShortcut: data.textTransformersCapitalCaseShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Text Transformers Capital Case shortcut:', data.textTransformersCapitalCaseShortcut);
                }
                
                if (data.textTransformersLowerCaseShortcut) {
                    await chrome.storage.sync.set({ textTransformersLowerCaseShortcut: data.textTransformersLowerCaseShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Text Transformers Lower Case shortcut:', data.textTransformersLowerCaseShortcut);
                }
                
                if (data.textTransformersUpperCaseShortcut) {
                    await chrome.storage.sync.set({ textTransformersUpperCaseShortcut: data.textTransformersUpperCaseShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Text Transformers Upper Case shortcut:', data.textTransformersUpperCaseShortcut);
                }
                
                if (data.textTransformersSentenceCaseShortcut) {
                    await chrome.storage.sync.set({ textTransformersSentenceCaseShortcut: data.textTransformersSentenceCaseShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Text Transformers Sentence Case shortcut:', data.textTransformersSentenceCaseShortcut);
                }
                
                if (data.textTransformersSlugifyShortcut) {
                    await chrome.storage.sync.set({ textTransformersSlugifyShortcut: data.textTransformersSlugifyShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Text Transformers Slugify shortcut:', data.textTransformersSlugifyShortcut);
                }
                
                if (data.textTransformersTrimToPageShortcut) {
                    await chrome.storage.sync.set({ textTransformersTrimToPageShortcut: data.textTransformersTrimToPageShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Text Transformers Trim to Page shortcut:', data.textTransformersTrimToPageShortcut);
                }
                
                if (data.textTransformersSortAlphabeticallyShortcut) {
                    await chrome.storage.sync.set({ textTransformersSortAlphabeticallyShortcut: data.textTransformersSortAlphabeticallyShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Text Transformers Sort Alphabetically shortcut:', data.textTransformersSortAlphabeticallyShortcut);
                }
                
                if (data.textTransformersRemoveEmptyLinesShortcut) {
                    await chrome.storage.sync.set({ textTransformersRemoveEmptyLinesShortcut: data.textTransformersRemoveEmptyLinesShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Text Transformers Remove Empty Lines shortcut:', data.textTransformersRemoveEmptyLinesShortcut);
                }
                
                if (data.textTransformersRemoveDuplicateLinesShortcut) {
                    await chrome.storage.sync.set({ textTransformersRemoveDuplicateLinesShortcut: data.textTransformersRemoveDuplicateLinesShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Text Transformers Remove Duplicate Lines shortcut:', data.textTransformersRemoveDuplicateLinesShortcut);
                }
                
                if (data.textTimeMachineLauncherShortcut) {
                    await chrome.storage.sync.set({ textTimeMachineLauncherShortcut: data.textTimeMachineLauncherShortcut });
                    importStats.shortcuts++;
                    console.log('Imported Text Time Machine Launcher shortcut:', data.textTimeMachineLauncherShortcut);
                }
                
                if (data.newTabRedirectShortcut) {
                    await chrome.storage.sync.set({ newTabRedirectShortcut: data.newTabRedirectShortcut });
                    importStats.shortcuts++;
                    console.log('Imported New Tab Redirect shortcut:', data.newTabRedirectShortcut);
                }
                
                if (typeof data.globalShortcutsEnabled === 'boolean') {
                    await chrome.storage.local.set({ globalShortcutsEnabled: data.globalShortcutsEnabled });
                    console.log('Imported global shortcuts setting:', data.globalShortcutsEnabled);
                }
                
                if (typeof data.copyElementEnabled === 'boolean') {
                    await chrome.storage.local.set({ copyElementEnabled: data.copyElementEnabled });
                    console.log('Imported copy element enabled setting:', data.copyElementEnabled);
                }
                
                if (typeof data.colorPickerEnabled === 'boolean') {
                    await chrome.storage.local.set({ colorPickerEnabled: data.colorPickerEnabled });
                    console.log('Imported color picker enabled setting:', data.colorPickerEnabled);
                }
                
                // Import Quick Actions order
                if (data.quickActionsOrder) {
                    await chrome.storage.local.set({ quickActionsOrder: data.quickActionsOrder });
                    importStats.quickActionsOrder = true;
                    console.log('Imported Quick Actions order:', data.quickActionsOrder);
                }
                
                // Import Mass Unsubscribe whitelist
                if (data.massUnsubscribeWhitelist && Array.isArray(data.massUnsubscribeWhitelist)) {
                    await chrome.storage.local.set({ massUnsubscribeWhitelist: data.massUnsubscribeWhitelist });
                    importStats.massUnsubscribeWhitelist = data.massUnsubscribeWhitelist.length;
                    console.log('Imported Mass Unsubscribe whitelist:', data.massUnsubscribeWhitelist);
                }
                
                // Import location settings if present
                if (data.locationSettings) {
                    await chrome.storage.local.set({ settings: data.locationSettings });
                    console.log('Imported location settings:', data.locationSettings);
                }
                
                // Import location favorites
                if (data.locationChangerFavorites && Array.isArray(data.locationChangerFavorites)) {
                    // Validate favorite structure
                    const validFavorites = data.locationChangerFavorites.filter(favorite => 
                        favorite && 
                        typeof favorite.name === 'string' && 
                        favorite.name.trim() &&
                        favorite.data &&
                        typeof favorite.data === 'object'
                    );
                    
                    await chrome.storage.local.set({ locationChangerFavorites: validFavorites });
                    importStats.locationFavoritesCount = validFavorites.length;
                    console.log('Imported location favorites:', validFavorites);
                }
                
                // Import Pomodoro website blocking settings
                if (data.blockedSites && Array.isArray(data.blockedSites)) {
                    await chrome.storage.local.set({ blockedSites: data.blockedSites });
                    console.log('Imported Pomodoro blocked sites:', data.blockedSites);
                }
                
                if (data.allowedUrls && Array.isArray(data.allowedUrls)) {
                    await chrome.storage.local.set({ allowedUrls: data.allowedUrls });
                    console.log('Imported Pomodoro allowed URLs:', data.allowedUrls);
                }
                
                // Import Pomodoro todos
                if (data.pomodoroTodos && data.pomodoroTodos.todos && Array.isArray(data.pomodoroTodos.todos)) {
                    // Validate todo structure similar to TodoManager's importTodos method
                    const validTodos = data.pomodoroTodos.todos.filter(todo => 
                        todo && 
                        typeof todo.text === 'string' && 
                        todo.text.trim() &&
                        typeof todo.completed === 'boolean'
                    );
                    
                    const pomodoroTodosData = {
                        todos: validTodos,
                        nextId: data.pomodoroTodos.nextId || validTodos.length + 1
                    };
                    
                    await chrome.storage.local.set({ pomodoroTodos: pomodoroTodosData });
                    importStats.pomodoroTodosCount = validTodos.length;
                    console.log('Imported Pomodoro todos:', pomodoroTodosData);
                }
                
                // Import Minimal Reader blacklist domains
                if (data.minimalReaderBlacklistDomains && Array.isArray(data.minimalReaderBlacklistDomains)) {
                    // Validate domain entries - remove empty strings and normalize
                    const validDomains = data.minimalReaderBlacklistDomains
                        .filter(domain => domain && typeof domain === 'string' && domain.trim())
                        .map(domain => domain.trim());
                    
                    await chrome.storage.local.set({ minimalReaderBlacklistDomains: validDomains });
                    importStats.minimalReaderBlacklistCount = validDomains.length;
                    console.log('Imported Minimal Reader blacklist domains:', validDomains);
                }
                
                // Import Video Speed Controller blacklist
                if (data.videoSpeedControllerBlacklist && typeof data.videoSpeedControllerBlacklist === 'string') {
                    await chrome.storage.local.set({ videoSpeedControllerBlacklist: data.videoSpeedControllerBlacklist });
                    importStats.hasVideoSpeedControllerBlacklist = !!data.videoSpeedControllerBlacklist.trim();
                    console.log('Imported Video Speed Controller blacklist:', data.videoSpeedControllerBlacklist);
                }
                
                // Import Gmail pinned emails
                if (data.gmailPinnedEmails && Array.isArray(data.gmailPinnedEmails)) {
                    // Validate email structure
                    const validEmails = data.gmailPinnedEmails.filter(email => 
                        email && 
                        typeof email.messageId === 'string' &&
                        typeof email.subject === 'string'
                    );
                    
                    await chrome.storage.local.set({ gmailPinnedEmails: validEmails });
                    importStats.gmailPinnedEmailsCount = validEmails.length;
                    console.log('Imported Gmail pinned emails:', validEmails.length);
                }
                
                // Import YouTube API key
                if (data.youtubeApiKey && typeof data.youtubeApiKey === 'string') {
                    await chrome.storage.local.set({ youtubeApiKey: data.youtubeApiKey });
                    importStats.hasYouTubeApiKey = true;
                    console.log('Imported YouTube API key (masked):', data.youtubeApiKey.substring(0, 6) + '...');
                }
                
                // Import active alerts
                if (data.activeAlerts && Array.isArray(data.activeAlerts)) {
                    // Validate alert structure
                    const validAlerts = data.activeAlerts.filter(alert =>
                        alert &&
                        typeof alert.id === 'string' &&
                        typeof alert.title === 'string' &&
                        typeof alert.enabled === 'boolean'
                    );
                    
                    await chrome.storage.local.set({ activeAlerts: validAlerts });
                    importStats.activeAlertsCount = validAlerts.length;
                    importStats.activeAlertsEnabledCount = validAlerts.filter(a => a.enabled).length;
                    console.log('Imported active alerts:', validAlerts.length);
                }
                
                // Import Bulk Link Open saved lists
                if (data.bulkLinkOpenSavedLists && typeof data.bulkLinkOpenSavedLists === 'object') {
                    await chrome.storage.local.set({ bulkLinkOpenSavedLists: data.bulkLinkOpenSavedLists });
                    importStats.bulkLinkOpenListsCount = Object.keys(data.bulkLinkOpenSavedLists).length;
                    console.log('Imported Bulk Link Open lists:', importStats.bulkLinkOpenListsCount);
                }
                
                // Import Pomodoro audio settings
                if (data.pomodoroAudioSettings && typeof data.pomodoroAudioSettings === 'object') {
                    await chrome.storage.local.set({ pomodoroAudioSettings: data.pomodoroAudioSettings });
                    importStats.hasPomodoroAudioSettings = true;
                    console.log('Imported Pomodoro audio settings');
                }
                
            } else if (isV1Format) {
                // Handle v1.0 format (legacy compatibility)
                const mergedSettings = { ...this.defaultSettings };
                
                Object.keys(this.defaultSettings).forEach(key => {
                    if (data.settings.hasOwnProperty(key)) {
                        mergedSettings[key] = data.settings[key];
                    }
                });
                
                Object.keys(data.settings).forEach(key => {
                    if (!mergedSettings.hasOwnProperty(key)) {
                        mergedSettings[key] = data.settings[key];
                        console.log(`Importing additional v1.0 setting: ${key} = ${data.settings[key]}`);
                    }
                });
                
                this.settings = mergedSettings;
                await this.saveSettings();
                
                importStats.mainSettings = Object.keys(data.settings).length;
                importStats.trackedDomains = Array.isArray(data.settings.trackedDomainsList) ? data.settings.trackedDomainsList.length : 0;
                importStats.utmWhitelistDomains = Array.isArray(data.settings.utmCleanerWhitelistDomains) ? data.settings.utmCleanerWhitelistDomains.length : 0;
            }
            
            // Validate critical settings
            if (!Array.isArray(this.settings.trackedDomainsList)) {
                this.settings.trackedDomainsList = [];
            }
            
            if (!Array.isArray(this.settings.utmCleanerWhitelistDomains)) {
                this.settings.utmCleanerWhitelistDomains = [];
            }
            
            if (!this.settings.trackedDomainsColor || !/^#[0-9A-Fa-f]{6}$/.test(this.settings.trackedDomainsColor)) {
                this.settings.trackedDomainsColor = '#7c3aed';
            }
            
            // Update UI
            this.updateUI();
            
            // Show comprehensive success message
            let successMessage = `Settings imported successfully! `;
            const messageParts = [];
            
            if (importStats.mainSettings > 0) {
                messageParts.push(`${importStats.mainSettings} main settings`);
            }
            if (importStats.trackedDomains > 0) {
                messageParts.push(`${importStats.trackedDomains} tracked domains`);
            }
            if (importStats.utmWhitelistDomains > 0) {
                messageParts.push(`${importStats.utmWhitelistDomains} UTM whitelist domains`);
            }
            if (importStats.massUnsubscribeWhitelist > 0) {
                messageParts.push(`${importStats.massUnsubscribeWhitelist} mass unsubscribe whitelist entries`);
            }
            if (importStats.htmlCleanerOptions > 0) {
                messageParts.push(`${importStats.htmlCleanerOptions} HTML cleaning options`);
            }
            if (importStats.shortcuts > 0) {
                messageParts.push(`${importStats.shortcuts} keyboard shortcuts`);
            }
            if (importStats.quickActionsOrder) {
                messageParts.push(`Quick Actions configuration`);
            }
            if (importStats.pomodoroTodosCount > 0) {
                messageParts.push(`${importStats.pomodoroTodosCount} Pomodoro todos`);
            }
            if (importStats.minimalReaderBlacklistCount > 0) {
                messageParts.push(`${importStats.minimalReaderBlacklistCount} minimal reader blacklist domains`);
            }
            if (importStats.hasVideoSpeedControllerBlacklist) {
                messageParts.push(`video speed controller blacklist`);
            }
            if (importStats.locationFavoritesCount > 0) {
                messageParts.push(`${importStats.locationFavoritesCount} location favorites`);
            }
            if (importStats.gmailPinnedEmailsCount > 0) {
                messageParts.push(`${importStats.gmailPinnedEmailsCount} Gmail pinned emails`);
            }
            if (importStats.hasYouTubeApiKey) {
                messageParts.push(`YouTube API key`);
            }
            if (importStats.activeAlertsCount > 0) {
                messageParts.push(`${importStats.activeAlertsCount} active alerts (${importStats.activeAlertsEnabledCount} enabled)`);
            }
            if (importStats.bulkLinkOpenListsCount > 0) {
                messageParts.push(`${importStats.bulkLinkOpenListsCount} bulk link open lists`);
            }
            if (importStats.hasPomodoroAudioSettings) {
                messageParts.push(`Pomodoro audio settings`);
            }
            
            successMessage += messageParts.join(', ') + '.';            
            this.showNotification(successMessage, 'success');
            
            // Comprehensive import log
            console.log('Comprehensive import completed:', {
                format: data.version || 'v1.0',
                importStats,
                finalSettings: this.settings,
                importMetadata: data.metadata
            });
            
        } catch (error) {
            console.error('Error importing settings:', error);
            this.showNotification('Error importing settings: ' + error.message, 'error');
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification--${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            color: '#fff',
            fontWeight: '500',
            zIndex: '9999',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });

        // Set background color based on type
        const colors = {
            success: '#22c55e',
            error: '#ef4444',
            info: '#3b82f6'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    setupAdvancedFeaturesSearch() {
        const searchInput = document.getElementById('advancedFeaturesSearch');
        const clearButton = document.getElementById('clearAdvancedFeaturesSearch');
        
        if (!searchInput || !clearButton) {
            console.log('Advanced Features search elements not found');
            return;
        }

        // Get all accordion containers
        const accordions = document.querySelectorAll('.settings-accordion');
        
        // Build search index for faster searching
        const searchIndex = [];
        accordions.forEach(accordion => {
            const titleElement = accordion.querySelector('.settings-accordion-title');
            const title = titleElement ? titleElement.textContent.trim() : '';
            
            // Get all setting labels and descriptions within this accordion
            const labels = Array.from(accordion.querySelectorAll('.settings-item__label')).map(el => el.textContent.trim());
            const descriptions = Array.from(accordion.querySelectorAll('.settings-item__description')).map(el => el.textContent.trim());
            
            searchIndex.push({
                element: accordion,
                title: title,
                labels: labels,
                descriptions: descriptions,
                searchText: [title, ...labels, ...descriptions].join(' ').toLowerCase()
            });
        });

        let lastSearchTerm = '';

        // Search functionality
        const performSearch = (searchTerm, shouldExpand = false) => {
            if (searchTerm.length < 3) {
                // Reset all accordions to visible when less than 3 characters
                accordions.forEach(accordion => {
                    accordion.classList.remove('search-dimmed', 'search-highlight');
                    accordion.style.display = '';
                    // Reset individual settings visibility
                    resetIndividualSettings(accordion);
                });
                clearButton.classList.remove('visible');
                return;
            }

            clearButton.classList.add('visible');
            const term = searchTerm.toLowerCase();
            let hasMatches = false;
            let exactMatches = [];
            let partialMatches = [];

            // First pass: Find exact and partial matches
            searchIndex.forEach(item => {
                const titleWords = item.title.toLowerCase().split(/\s+/);
                const labelWords = item.labels.join(' ').toLowerCase().split(/\s+/);
                const allWords = [...titleWords, ...labelWords];
                
                // Check for exact word matches
                const hasExactMatch = allWords.some(word => word === term);
                const hasPartialMatch = item.searchText.includes(term);
                
                if (hasExactMatch) {
                    exactMatches.push(item);
                } else if (hasPartialMatch) {
                    partialMatches.push(item);
                }
            });

            // Determine which matches to show
            const matchesToShow = exactMatches.length > 0 ? exactMatches : partialMatches;
            
            // Apply visibility based on matches
            searchIndex.forEach(item => {
                const shouldShow = matchesToShow.includes(item);
                
                if (shouldShow) {
                    hasMatches = true;
                    item.element.classList.remove('search-dimmed');
                    item.element.classList.add('search-highlight');
                    item.element.style.display = '';
                    
                    // Expand accordion if spacebar was pressed or word completed
                    if (shouldExpand) {
                        const content = item.element.querySelector('.settings-accordion-content');
                        const header = item.element.querySelector('.settings-accordion-header');
                        const icon = item.element.querySelector('.settings-accordion-icon');
                        
                        if (content && header && icon) {
                            content.classList.add('expanded');
                            header.classList.add('expanded');
                            icon.classList.add('expanded');
                        }
                    }
                } else {
                    item.element.classList.add('search-dimmed');
                    item.element.classList.remove('search-highlight');
                    item.element.style.display = 'none';
                }
            });

            // Second-level filtering: Filter individual settings within expanded accordions
            if (shouldExpand) {
                setTimeout(() => {
                    filterIndividualSettings(term, matchesToShow);
                }, 100); // Small delay to ensure expansion animation completes
            }
        };

        // Function to filter individual settings within accordions
        const filterIndividualSettings = (searchTerm, visibleAccordions) => {
            visibleAccordions.forEach(accordionItem => {
                const settingsItems = accordionItem.element.querySelectorAll('.settings-item');
                
                settingsItems.forEach(settingItem => {
                    const label = settingItem.querySelector('.settings-item__label');
                    const description = settingItem.querySelector('.settings-item__description');
                    
                    if (label || description) {
                        const labelText = label ? label.textContent.toLowerCase() : '';
                        const descText = description ? description.textContent.toLowerCase() : '';
                        const combinedText = (labelText + ' ' + descText).trim();
                        
                        // Check if setting matches search term
                        const words = combinedText.split(/\s+/);
                        const hasExactMatch = words.some(word => word === searchTerm);
                        const hasPartialMatch = combinedText.includes(searchTerm);
                        
                        if (hasExactMatch || hasPartialMatch) {
                            settingItem.style.display = '';
                            settingItem.classList.add('search-highlight-setting');
                        } else {
                            settingItem.style.display = 'none';
                            settingItem.classList.remove('search-highlight-setting');
                        }
                    }
                });
            });
        };

        // Function to reset individual settings visibility
        const resetIndividualSettings = (accordion) => {
            const settingsItems = accordion.querySelectorAll('.settings-item');
            settingsItems.forEach(item => {
                item.style.display = '';
                item.classList.remove('search-highlight-setting');
            });
        };

        // Input event listener
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.trim();
            performSearch(searchTerm, false);
            lastSearchTerm = searchTerm;
        });

        // Keydown event listener for spacebar detection
        searchInput.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && lastSearchTerm.length >= 3) {
                // Small delay to let the space character be added
                setTimeout(() => {
                    const searchTerm = searchInput.value.trim();
                    performSearch(searchTerm, true);
                }, 10);
            } else if (e.code === 'Escape') {
                // Clear search on escape
                searchInput.value = '';
                performSearch('', false);
                searchInput.blur();
            }
        });

        // Clear button functionality
        clearButton.addEventListener('click', () => {
            searchInput.value = '';
            performSearch('', false);
            searchInput.focus();
        });

        console.log('Advanced Features search initialized with', searchIndex.length, 'accordions');
        
        // Auto-focus the search input when settings page loads
        setTimeout(() => {
            searchInput.focus();
        }, 300); // Small delay to ensure all UI is loaded
    }

    setupQuickActionsSearch() {
        const searchInput = document.getElementById('quickActionsSearch');
        const clearButton = document.getElementById('clearQuickActionsSearch');
        
        if (!searchInput || !clearButton) {
            console.log('Quick Actions search elements not found');
            return;
        }

        // Get all Quick Actions settings items (excluding the select all toggle)
        const settingsItems = document.querySelectorAll('#quick-actions-tab .settings-item:not(.settings-item--select-all)');
        
        // Build search index for faster searching
        const searchIndex = [];
        settingsItems.forEach(item => {
            const titleElement = item.querySelector('.settings-item__title, .settings-item__label');
            const descriptionElement = item.querySelector('.settings-item__description');
            
            const title = titleElement ? titleElement.textContent.trim() : '';
            const description = descriptionElement ? descriptionElement.textContent.trim() : '';
            
            searchIndex.push({
                element: item,
                title: title,
                description: description,
                searchText: [title, description].join(' ').toLowerCase()
            });
        });

        let lastSearchTerm = '';

        // Search functionality
        const performSearch = (searchTerm) => {
            if (searchTerm.length < 3) {
                // Reset all items to visible when less than 3 characters
                settingsItems.forEach(item => {
                    item.classList.remove('quick-actions-search-dimmed', 'quick-actions-search-highlight');
                    item.style.display = '';
                });
                clearButton.classList.remove('visible');
                return;
            }

            clearButton.classList.add('visible');
            const term = searchTerm.toLowerCase();
            let hasMatches = false;
            let exactMatches = [];
            let partialMatches = [];

            // Find exact and partial matches
            searchIndex.forEach(item => {
                const titleWords = item.title.toLowerCase().split(/\s+/);
                const descWords = item.description.toLowerCase().split(/\s+/);
                const allWords = [...titleWords, ...descWords];
                
                // Check for exact word matches
                const hasExactMatch = allWords.some(word => word === term);
                const hasPartialMatch = item.searchText.includes(term);
                
                if (hasExactMatch) {
                    exactMatches.push(item);
                } else if (hasPartialMatch) {
                    partialMatches.push(item);
                }
            });

            // Determine which matches to show
            const matchesToShow = exactMatches.length > 0 ? exactMatches : partialMatches;
            
            // Apply visibility based on matches
            searchIndex.forEach(item => {
                const shouldShow = matchesToShow.includes(item);
                
                if (shouldShow) {
                    hasMatches = true;
                    item.element.classList.remove('quick-actions-search-dimmed');
                    item.element.classList.add('quick-actions-search-highlight');
                    item.element.style.display = '';
                } else {
                    item.element.classList.add('quick-actions-search-dimmed');
                    item.element.classList.remove('quick-actions-search-highlight');
                    item.element.style.display = 'none';
                }
            });
        };

        // Input event listener
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.trim();
            performSearch(searchTerm);
            lastSearchTerm = searchTerm;
        });

        // Keydown event listener for escape key
        searchInput.addEventListener('keydown', (e) => {
            if (e.code === 'Escape') {
                // Clear search on escape
                searchInput.value = '';
                performSearch('');
                searchInput.blur();
            }
        });

        // Clear button functionality
        clearButton.addEventListener('click', () => {
            searchInput.value = '';
            performSearch('');
            searchInput.focus();
        });

        console.log('Quick Actions search initialized with', searchIndex.length, 'items');
    }

    // Batch settings update for onboarding system
    async batchUpdateSettings(settingsToUpdate) {
        try {
            console.log('Settings: Batch updating settings for onboarding:', settingsToUpdate);
            
            // Merge new settings with existing settings
            const updatedSettings = {
                ...this.settings,
                ...settingsToUpdate
            };
            
            this.settings = updatedSettings;
            
            // Save the updated settings
            await this.saveSettings(false); // Save without notification during onboarding
            
            console.log('Settings: Batch update completed successfully');
            
            // Update UI elements if we're on the settings page
            this.updateUI();
            
            return { success: true, updatedCount: Object.keys(settingsToUpdate).length };
        } catch (error) {
            console.error('Settings: Error during batch update:', error);
            throw new Error(`Failed to batch update settings: ${error.message}`);
        }
    }

    // YouTube API Key Management Methods
    async saveYouTubeApiKey() {
        const apiKeyInput = document.getElementById('youtube-api-key');
        const statusDiv = document.getElementById('api-key-status');
        
        if (!apiKeyInput || !statusDiv) {
            console.error('YouTube API key input or status elements not found');
            return;
        }

        const apiKey = apiKeyInput.value.trim();
        
        if (!apiKey) {
            this.showNotification('Please enter a valid API key', 'error');
            statusDiv.style.display = 'none';
            return;
        }

        if (!apiKey.startsWith('AIza') || apiKey.length < 30) {
            this.showNotification('Invalid API key format. YouTube API keys start with "AIza" and are at least 30 characters long', 'error');
            statusDiv.style.display = 'none';
            return;
        }

        try {
            // Store in chrome.storage.local for cross-context access (content scripts can access this)
            await chrome.storage.local.set({ youtubeApiKey: apiKey });
            
            // Clear the input and show masked status
            apiKeyInput.value = '';
            this.updateApiKeyStatus(apiKey);
            
            this.showNotification('YouTube API key saved successfully', 'success');
            console.log('YouTube API key saved to chrome.storage.local');
        } catch (error) {
            console.error('Error saving YouTube API key:', error);
            this.showNotification('Error saving API key', 'error');
            statusDiv.style.display = 'none';
        }
    }

    async clearYouTubeApiKey() {
        const statusDiv = document.getElementById('api-key-status');
        
        if (confirm('Are you sure you want to clear the YouTube API key? This will disable automatic schema generation.')) {
            try {
                await chrome.storage.local.remove(['youtubeApiKey']);
                
                // Clear the input and hide status
                const apiKeyInput = document.getElementById('youtube-api-key');
                if (apiKeyInput) {
                    apiKeyInput.value = '';
                }
                
                if (statusDiv) {
                    statusDiv.style.display = 'none';
                }
                
                this.showNotification('YouTube API key cleared successfully', 'success');
                console.log('YouTube API key cleared from chrome.storage.local');
            } catch (error) {
                console.error('Error clearing YouTube API key:', error);
                this.showNotification('Error clearing API key', 'error');
            }
        }
    }

    async loadYouTubeApiKeyStatus() {
        try {
            const result = await chrome.storage.local.get(['youtubeApiKey']);
            if (result.youtubeApiKey) {
                this.updateApiKeyStatus(result.youtubeApiKey);
            }
        } catch (error) {
            console.error('Error loading YouTube API key status:', error);
        }
    }

    updateApiKeyStatus(apiKey) {
        const statusDiv = document.getElementById('api-key-status');
        if (!statusDiv) return;

        // Create masked version of API key for display
        const maskedKey = apiKey.substring(0, 6) + '•'.repeat(apiKey.length - 10) + apiKey.substring(apiKey.length - 4);
        
        statusDiv.innerHTML = `<span style="color: #22c55e;">✓ API Key Saved:</span> ${maskedKey}`;
        statusDiv.style.display = 'block';
    }

    // Utility method to get YouTube API key for use in other modules
    static async getYouTubeApiKey() {
        try {
            const result = await chrome.storage.local.get(['youtubeApiKey']);
            return result.youtubeApiKey || null;
        } catch (error) {
            console.error('Error retrieving YouTube API key:', error);
            return null;
        }
    }
}

// Emergency Reset System
class EmergencyResetManager {
    constructor() {
        this.clickCount = 0;
        this.clickTimeout = null;
        this.isResetting = false;
        this.initializeVersionClickHandler();
    }

    initializeVersionClickHandler() {
        const versionElement = document.getElementById('settings-version-text');
        const resetButton = document.getElementById('emergency-reset-button');
        
        if (versionElement) {
            versionElement.addEventListener('click', () => {
                this.handleVersionClick();
            });
        }
        
        if (resetButton) {
            resetButton.addEventListener('click', () => {
                this.performEmergencyReset();
            });
        }
    }

    handleVersionClick() {
        this.clickCount++;
        console.log(`🔍 Emergency Reset: Click ${this.clickCount}/10`);

        // Reset click count after 3 seconds of no clicks
        if (this.clickTimeout) {
            clearTimeout(this.clickTimeout);
        }
        
        this.clickTimeout = setTimeout(() => {
            this.clickCount = 0;
        }, 3000);

        // Show reset button after 10 clicks
        if (this.clickCount >= 10) {
            this.showEmergencyResetButton();
            this.clickCount = 0;
        }
    }

    showEmergencyResetButton() {
        const resetButton = document.getElementById('emergency-reset-button');
        if (resetButton) {
            resetButton.classList.add('visible');
            console.log('🚨 Emergency Reset: Button activated! Click to perform complete reset.');
            
            // Auto-hide after 30 seconds for safety
            setTimeout(() => {
                resetButton.classList.remove('visible');
            }, 30000);
        }
    }

    async performEmergencyReset() {
        if (this.isResetting) return;
        
        // Confirm with user
        const confirmed = confirm(
            '🚨 EMERGENCY RESET WARNING 🚨\n\n' +
            'This will:\n' +
            '• Clear ALL extension storage\n' +
            '• Reset ALL settings to defaults\n' +
            '• Rebuild context menus\n' +
            '• Reset location spoofing rules\n' +
            '• Reload the extension\n\n' +
            'This action cannot be undone.\n\n' +
            'Continue with emergency reset?'
        );
        
        if (!confirmed) return;
        
        this.isResetting = true;
        console.log('🚨 EMERGENCY RESET: Starting complete extension reset...');
        
        try {
            // Step 1: Clear ALL storage
            console.log('🧹 EMERGENCY RESET: Clearing all storage...');
            await chrome.storage.local.clear();
            await chrome.storage.sync.clear();
            
            // Step 2: Reset declarativeNetRequest rules (location spoofing)
            console.log('🌍 EMERGENCY RESET: Resetting location spoofing rules...');
            try {
                await chrome.declarativeNetRequest.updateSessionRules({
                    removeRuleIds: [1, 2, 3] // Remove all location rules
                });
            } catch (error) {
                console.warn('EMERGENCY RESET: Error resetting network rules:', error);
            }
            
            // Step 3: Clear context menus
            console.log('📋 EMERGENCY RESET: Clearing context menus...');
            try {
                await chrome.contextMenus.removeAll();
            } catch (error) {
                console.warn('EMERGENCY RESET: Error clearing context menus:', error);
            }
            
            // Step 4: Send reset message to background script
            console.log('📡 EMERGENCY RESET: Notifying background script...');
            try {
                await chrome.runtime.sendMessage({ action: 'emergencyReset' });
            } catch (error) {
                console.warn('EMERGENCY RESET: Error sending reset message:', error);
            }
            
            // Step 5: Show success message
            alert(
                '✅ EMERGENCY RESET COMPLETE!\n\n' +
                'CRITICAL: RESTART YOUR BROWSER COMPLETELY to finish clearing extension state.\n\n' +
                'After restart:\n' +
                '1. Open extension popup\n' +
                '2. Set your preferred location\n' +
                '3. Location spoofing will work!\n\n' +
                'Extension has been reset.\n' +
                'Reloading extension in 3 seconds...'
            );
            
            // Step 6: Reload extension after delay
            setTimeout(() => {
                chrome.runtime.reload();
            }, 3000);
            
        } catch (error) {
            console.error('❌ EMERGENCY RESET: Error during reset:', error);
            alert(
                '❌ EMERGENCY RESET FAILED!\n\n' +
                'Error: ' + error.message + '\n\n' +
                'Please try disabling and re-enabling the extension manually.'
            );
        } finally {
            this.isResetting = false;
        }
    }
}

// Initialize settings manager when page loads
document.addEventListener('DOMContentLoaded', () => {
    // Initialize version text dynamically from manifest
    const version = chrome.runtime.getManifest().version;
    const settingsVersionText = document.getElementById('settings-version-text');
    if (settingsVersionText) {
        settingsVersionText.textContent = 'SEO Time Machines Version: ' + version;
    }
    
    const settingsManager = new SettingsManager();
    
    // Initialize Emergency Reset System
    const emergencyResetManager = new EmergencyResetManager();
    
    // Expose managers globally for onboarding system access
    window.SettingsManager = settingsManager;
    window.EmergencyResetManager = emergencyResetManager;
    console.log('Settings: SettingsManager and EmergencyResetManager exposed globally');
}); 