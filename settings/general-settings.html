<div class="settings-section">
    <div class="settings-section__title">General Preferences</div>
    
    <div class="settings-group">
        <div class="settings-item">
            <div class="settings-item__content">
                <div class="settings-item__info">
                    <div class="settings-item__label">Show notifications</div>
                    <div class="settings-item__description">Display browser notifications for completed operations</div>
                </div>
                <div class="settings-item__control">
                    <div class="toggle-switch toggle-switch--active" id="notificationsToggle" data-setting="notifications"></div>
                </div>
            </div>
        </div>

        <div class="settings-item" id="debugModeSection" style="display: none;">
            <div class="settings-item__content">
                <div class="settings-item__info">
                    <div class="settings-item__label">Enable debug mode</div>
                    <div class="settings-item__description">Show additional debug information in console</div>
                </div>
                <div class="settings-item__control">
                    <div class="toggle-switch" id="debugModeToggle" data-setting="debugMode"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="settings-section">
    <div class="settings-section__title">YouTube API Configuration</div>
    
    <div class="settings-group">
        <div class="settings-item">
            <div class="settings-item__content" style="display: block !important; flex-direction: unset !important; width: 100% !important;">
                <div class="settings-item__info" style="width: 100% !important; margin-bottom: 12px !important; display: block !important;">
                    <div class="settings-item__label" style="display: block !important; width: 100% !important;">YouTube API Key</div>
                    <div class="settings-item__description" style="display: block !important; width: 100% !important; white-space: normal !important;">
                        Enter your YouTube Data API v3 key to enable automatic schema markup generation for the YouTube Embed Scraper. The scraper will work normally without an API key but won't generate schema markup.
                        <br><br>
                        <a href="https://console.developers.google.com/apis" target="_blank" style="color: #7C3AED; text-decoration: none;">Get your free API key from Google Cloud Console →</a>
                        <br><br>
                        <small style="color: #888; font-size: 12px;">Enable "YouTube Data API v3" and create credentials. Your key will be stored locally and securely.</small>
                    </div>
                </div>
                <div class="settings-item__control" style="width: 100% !important; display: block !important;">
                    <div class="api-key-container" style="display: flex !important; flex-direction: column !important; gap: 8px !important; width: 100% !important;">
                        <div style="display: flex !important; gap: 8px !important; align-items: center !important; flex-wrap: nowrap !important;">
                            <input type="password" id="youtube-api-key" placeholder="AIzaSy..." 
                                   style="flex: 1 !important; padding: 8px 12px !important; border: 1px solid #333 !important; border-radius: 6px !important; background: #1a1a1a !important; color: #e5e5e5 !important; font-size: 13px !important; font-family: monospace !important; min-width: 200px !important;">
                            <button id="save-api-key" class="settings-button" style="padding: 8px 16px !important; font-size: 13px !important; white-space: nowrap !important;">Save</button>
                            <button id="clear-api-key" class="settings-button" style="padding: 8px 16px !important; font-size: 13px !important; background: #ef4444 !important; border-color: #dc2626 !important; white-space: nowrap !important;">Clear</button>
                        </div>
                        <div id="api-key-status" style="font-size: 12px !important; color: #888 !important; font-family: monospace !important; display: none !important; width: 100% !important;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="settings-section">
    <div class="settings-accordion">
        <div class="settings-accordion-header" id="profilesHeader">
            <div class="settings-accordion-title">Profiles</div>
            <span class="settings-accordion-icon" id="profilesIcon">▼</span>
        </div>
        <div class="settings-accordion-content" id="profilesContent">
            <div class="settings-accordion-body">
                <div class="settings-item__description">Profiles allow you to save and quickly switch between different sets of settings, such as default locations, Extras menu items or preferred quick actions. Switching the profiles toggle off, will hide them from the UI, but they can still be saved and loaded from the settings page.</div>
                
                <div class="profiles-container">
                    <div id="profiles-repeater"></div>
                    <button class="settings-button" id="add-profile-btn">+ Add Profile</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="settings-section">
    <div class="onboarding-section">
        <button class="settings-button settings-button--onboarding" id="rerun-onboarding-btn" style="width: 100%!important;">Re-run Onboarding</button>
        <div class="settings-item__description settings-item__description--small" style="text-align: center;">🛡️ Run the initial setup wizard again to configure your toolsets. Your profiles will be preserved.</div>
    </div>
</div>

 