// Sponsored & Promoted Text Highlighter for GMB Data Extractor
// Highlights "Sponsored" and "Promoted" text on web pages.
(function() {
  'use strict';
  
  let isEnabled = true; // Default to enabled
  let highlightColor = '#ff0000'; // Default red color
  
  // Inject CSS styles
  function injectStyles() {
    // Remove existing styles if they exist
    const existingStyle = document.getElementById('gmb-sponsored-styles');
    if (existingStyle) {
      existingStyle.remove();
    }
    
    const style = document.createElement('style');
    style.id = 'gmb-sponsored-styles';
    style.textContent = `
      .gmb-sponsored-highlight {
        border: 2px dashed ${highlightColor} !important;
        padding: 2px 4px !important;
        display: inline-block !important;
      }
    `;
    document.head.appendChild(style);
    console.log('GMB Sponsored Highlighter: CSS injected with color:', highlightColor);
  }

  // Simple function to highlight all "Sponsored" and "Promoted" text
  function highlightSponsored() {
    if (!isEnabled) return;
    
    console.log('GMB Sponsored Highlighter: Starting highlighting...');

    const isGoogle = window.location.hostname.includes('google.');
    const nodeRegex = isGoogle ? /(sponsored|promoted)/i : /(promoted)/i;
    const highlightRegex = isGoogle ? /(sponsored|promoted)/gi : /(promoted)/gi;
    
    // Find all text nodes containing "sponsored" or "promoted" (case insensitive)
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: function(node) {
          // Skip if already processed
          if (node.parentNode && node.parentNode.classList && 
              node.parentNode.classList.contains('gmb-sponsored-highlight')) {
            return NodeFilter.FILTER_REJECT;
          }
          
          // Accept if contains "sponsored" or "promoted"
          return nodeRegex.test(node.nodeValue) ? 
            NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
        }
      }
    );

    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node);
    }

    // Only log if debug mode is enabled or if matches are found
    if (textNodes.length > 0) {
      console.log(`GMB Sponsored Highlighter: Found ${textNodes.length} text nodes with "Sponsored" or "Promoted"`);
    }

    let highlightCount = 0;
    textNodes.forEach(textNode => {
      try {
        const text = textNode.nodeValue;
        // Use regex to find and replace "sponsored" or "promoted" (case insensitive)
        
        if (highlightRegex.test(text)) {
          // Create new HTML with highlighted spans
          const newHTML = text.replace(highlightRegex, '<span class="gmb-sponsored-highlight">$1</span>');
          
          // Create temporary container
          const temp = document.createElement('div');
          temp.innerHTML = newHTML;
          
          // Replace text node with new content
          const fragment = document.createDocumentFragment();
          while (temp.firstChild) {
            fragment.appendChild(temp.firstChild);
          }
          
          textNode.parentNode.replaceChild(fragment, textNode);
          highlightCount++;
          console.log(`GMB Sponsored Highlighter: Highlighted occurrence ${highlightCount}`);
        }
      } catch (error) {
        console.error('GMB Sponsored Highlighter: Error processing node:', error);
      }
    });

    // Only log completion if highlights were made
    if (highlightCount > 0) {
      console.log(`GMB Sponsored Highlighter: Completed - highlighted ${highlightCount} instances`);
    }
    return highlightCount;
  }
  
  function initializeSponsoredHighlighter() {
    // Prevent multiple loads
    if (window.GMBSponsoredHighlighterLoaded) {
      console.log('GMB Sponsored Highlighter: Already loaded');
      return;
    }
    window.GMBSponsoredHighlighterLoaded = true;
    
    console.log('GMB Sponsored Highlighter: Loading on', window.location.href);

    // Load settings
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.get(['gmbExtractorSettings'], function(result) {
        const settings = result.gmbExtractorSettings || {};
        isEnabled = settings.sponsoredHighlighter !== false; // Default to true
        highlightColor = settings.sponsoredHighlighterColor || '#ff0000'; // Load custom color
        console.log('GMB Sponsored Highlighter: Settings loaded, enabled:', isEnabled, 'color:', highlightColor);
        
        if (isEnabled) {
          init();
        }
      });
    } else {
      // Fallback - just run
      init();
    }

    // Initialize
    function init() {
      // Inject styles first
      injectStyles();
      
      // Run highlighting when DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', highlightSponsored);
      } else {
        // DOM already ready
        setTimeout(highlightSponsored, 100);
      }
      
      // Also run on window load as backup
      window.addEventListener('load', () => {
        setTimeout(highlightSponsored, 500);
      });
    }

    // Export for manual testing
    window.highlightSponsored = highlightSponsored;
    
    console.log('GMB Sponsored Highlighter: Initialized and ready');
  }
  
  // Listen for settings updates from the settings page
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'updateSettings') {
        isEnabled = message.settings.sponsoredHighlighter !== false;
        highlightColor = message.settings.sponsoredHighlighterColor || '#ff0000';
        
        if (!isEnabled) {
          // Remove existing highlights if disabled
          const highlights = document.querySelectorAll('.gmb-sponsored-highlight');
          highlights.forEach(highlight => {
            const parent = highlight.parentNode;
            parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
            parent.normalize(); // Merge adjacent text nodes
          });
          console.log('GMB Sponsored Highlighter: Disabled via settings update - removed highlights');
        } else {
          // Re-enable and highlight if enabled
          console.log('GMB Sponsored Highlighter: Enabled via settings update');
          if (!window.GMBSponsoredHighlighterLoaded) {
            initializeSponsoredHighlighter();
          } else {
            // Re-inject styles with new color and re-highlight
            injectStyles();
            setTimeout(highlightSponsored, 100);
          }
        }
        sendResponse({received: true});
      }
    });
  }
  
  // Initialize immediately
  initializeSponsoredHighlighter();
})(); 