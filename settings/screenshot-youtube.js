(function() {
    'use strict';
    
    // Prevent multiple initializations
    if (window.screenshotYouTubeLoaded) {
        return;
    }
    window.screenshotYouTubeLoaded = true;
    
    let isEnabled = true;
    let screenshotKeyEnabled = true;
    let screenshotFunctionality = 2; // Both save and copy
    let screenshotFileFormat = 'png';
    let debugMode = false;
    
    // UI Elements
    let screenshotButton;
    let isAppended = false;
    
    // Initialize settings
    async function loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            
            // Explicit boolean checks
            isEnabled = settings.screenshotYouTubeEnabled !== false;
            screenshotKeyEnabled = settings.screenshotKeyEnabled !== false;
            screenshotFunctionality = settings.screenshotFunctionality !== undefined ? settings.screenshotFunctionality : 2;
            screenshotFileFormat = settings.screenshotFileFormat || 'png';
            debugMode = settings.debugMode || false;
            
            if (debugMode) {
                console.log('Screenshot YouTube: Settings loaded', {
                    isEnabled,
                    screenshotKeyEnabled,
                    screenshotFunctionality,
                    screenshotFileFormat
                });
            }
            
            if (isEnabled) {
                initializeFeature();
            } else {
                disableFeature();
            }
        } catch (error) {
            console.error('Screenshot YouTube: Error loading settings:', error);
            // Safe defaults
            isEnabled = true;
            screenshotKeyEnabled = true;
            screenshotFunctionality = 2;
            screenshotFileFormat = 'png';
        }
    }
    
    // Feature implementation
    function initializeFeature() {
        if (!isEnabled) return;
        
        createUIElements();
        addScreenshotButton();
        setupKeyboardListeners();
        
        if (debugMode) {
            console.log('Screenshot YouTube: Initialized');
        }
    }
    
    function disableFeature() {
        removeUIElements();
        removeKeyboardListeners();
        isAppended = false;
        
        if (debugMode) {
            console.log('Screenshot YouTube: Disabled');
        }
    }
    
    function createUIElements() {
        // Create screenshot button
        screenshotButton = document.createElement("button");
        screenshotButton.className = "screenshotButton ytp-button";
        screenshotButton.style.cssText = `
            width: auto;
            float: left;
            margin-left: 6px;
            border: 1px solid #374151;
            border-radius: 3px;
            color: #d1d5db;
            padding: 0px 8px;
            font-size: 12px;
            font-weight: 400;
            transition: all 0.2s ease;
        `;
        screenshotButton.innerHTML = "Screenshot";
        screenshotButton.onclick = captureScreenshot;
    }
    
    function addScreenshotButton() {
        const ytpRightControls = document.getElementsByClassName("ytp-right-controls")[0];
        if (!ytpRightControls) {
            isAppended = false;
            return;
        }
        
        // Add screenshot button
        ytpRightControls.prepend(screenshotButton);
        isAppended = true;
    }
    
    function removeUIElements() {
        if (screenshotButton && screenshotButton.parentNode) {
            screenshotButton.parentNode.removeChild(screenshotButton);
        }
        
        isAppended = false;
    }
    
    function captureScreenshot() {
        const video = document.getElementsByClassName("video-stream")[0];
        if (!video) {
            console.error('Screenshot YouTube: No video element found');
            return;
        }
        
        // Get video title
        let title = '';
        const headerEls = document.querySelectorAll("h1.title.ytd-video-primary-info-renderer, h1.watch-title-container");
        if (headerEls.length > 0) {
            title = headerEls[0].innerText.trim();
        }
        
        // Get current time
        const time = video.currentTime;
        let minutes = Math.floor(time / 60);
        let seconds = Math.floor(time - (minutes * 60));
        
        if (minutes >= 60) {
            const hours = Math.floor(minutes / 60);
            minutes = minutes - (hours * 60);
            title += ` ${hours}-${minutes}-${seconds}`;
        } else {
            title += ` ${minutes}-${seconds}`;
        }
        
        // Determine file extension
        const extension = screenshotFileFormat === 'jpeg' ? 'jpg' : screenshotFileFormat;
        const filename = `${title} screenshot.${extension}`;
        
        // Create canvas and capture
        const canvas = document.createElement("canvas");
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);
        
        // Handle different functionality modes
        if (screenshotFunctionality === 1 || screenshotFunctionality === 2) {
            // Copy to clipboard (always PNG for clipboard)
            canvas.toBlob(async function(blob) {
                try {
                    const clipboardItemInput = new ClipboardItem({ "image/png": blob });
                    await navigator.clipboard.write([clipboardItemInput]);
                    
                    if (debugMode) {
                        console.log('Screenshot YouTube: Image copied to clipboard');
                    }
                    
                    // Also download if both mode and format matches
                    if (screenshotFunctionality === 2 && screenshotFileFormat === 'png') {
                        downloadBlob(blob, filename);
                    }
                } catch (error) {
                    console.error('Screenshot YouTube: Failed to copy to clipboard:', error);
                }
            }, 'image/png');
        }
        
        // Save to file
        if (screenshotFunctionality === 0 || (screenshotFunctionality === 2 && screenshotFileFormat !== 'png')) {
            canvas.toBlob(function(blob) {
                downloadBlob(blob, filename);
            }, `image/${screenshotFileFormat}`);
        }
    }
    
    function downloadBlob(blob, filename) {
        const downloadLink = document.createElement("a");
        downloadLink.download = filename;
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.click();
        
        if (debugMode) {
            console.log('Screenshot YouTube: Image downloaded as', filename);
        }
    }
    
    function setupKeyboardListeners() {
        document.addEventListener('keydown', handleKeydown);
    }
    
    function removeKeyboardListeners() {
        document.removeEventListener('keydown', handleKeydown);
    }
    
    function handleKeydown(e) {
        // Skip if user is typing
        if (document.activeElement.contentEditable === 'true' || 
            document.activeElement.tagName === 'INPUT' || 
            document.activeElement.tagName === 'TEXTAREA') {
            return;
        }
        
        // Screenshot key
        if (screenshotKeyEnabled && e.key === 'p') {
            captureScreenshot();
            e.preventDefault();
            return false;
        }
    }
    
    // Settings change listener
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'updateSettings' && message.settings) {
                const wasEnabled = isEnabled;
                
                isEnabled = message.settings.screenshotYouTubeEnabled !== false;
                screenshotKeyEnabled = message.settings.screenshotKeyEnabled !== false;
                screenshotFunctionality = message.settings.screenshotFunctionality !== undefined ? message.settings.screenshotFunctionality : 2;
                screenshotFileFormat = message.settings.screenshotFileFormat || 'png';
                debugMode = message.settings.debugMode || false;
                
                if (wasEnabled !== isEnabled) {
                    if (isEnabled) {
                        initializeFeature();
                    } else {
                        disableFeature();
                    }
                }
            }
        });
    }
    
    // Page detection function
    function isYouTubePage() {
        return window.location.hostname.includes('youtube.com') && 
               window.location.pathname.includes('/watch');
    }
    
    // Mutation observer for dynamic content
    function onDomChange(mutationsList, observer) {
        let run = false;
        for (let mutation of mutationsList) {
            if (mutation.type === 'childList') {
                run = true;
                break;
            }
        }
        
        if (run && isEnabled) {
            const ytpRightControls = document.getElementsByClassName("ytp-right-controls")[0];
            if (ytpRightControls && !isAppended) {
                addScreenshotButton();
            }
        }
    }
    
    const observer = new MutationObserver(onDomChange);
    
    // Initialize on YouTube watch pages only
    if (isYouTubePage()) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadSettings);
        } else {
            loadSettings();
        }
        
        // Start observing for dynamic changes
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
})();