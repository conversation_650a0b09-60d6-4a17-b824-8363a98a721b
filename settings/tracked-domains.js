// Tracked Domains for GMB Data Extractor
// Underlines tracked domain URLs in Google search results with purple dashed border
(function() {
  'use strict';
  
  let isEnabled = true; // Default to enabled
  let trackedDomains = []; // Array to store tracked domains
  let highlightColor = '#7c3aed'; // Default purple color
  
  // Inject CSS styles - moved outside initializeTrackedDomains for global access
  function injectStyles() {
    // Remove existing styles if they exist
    const existingStyle = document.getElementById('gmb-tracked-domains-styles');
    if (existingStyle) {
      existingStyle.remove();
    }
    
    const style = document.createElement('style');
    style.id = 'gmb-tracked-domains-styles';
    // Convert hex color to rgb for background opacity
    const hexToRgb = (hex) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    };
    
    const rgb = hexToRgb(highlightColor);
    const backgroundColorLight = rgb ? `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.05)` : 'rgba(124, 58, 237, 0.05)';
    const backgroundColorHover = rgb ? `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)` : 'rgba(139, 92, 246, 0.1)';
    
    style.textContent = `
      .gmb-tracked-domain-title {
        border: 2px dashed ${highlightColor} !important;
        padding: 4px 8px !important;
        border-radius: 4px !important;
        display: inline-block !important;
        background-color: ${backgroundColorLight} !important;
      }
      
      .gmb-tracked-domain-title:hover {
        border-color: ${highlightColor} !important;
        background-color: ${backgroundColorHover} !important;
        filter: brightness(1.1) !important;
      }
    `;
    document.head.appendChild(style);
    console.log('GMB Tracked Domains: CSS injected');
  }

  function initializeTrackedDomains() {
    // Prevent multiple loads
    if (window.GMBTrackedDomainsLoaded) {
      console.log('GMB Tracked Domains: Already loaded');
      return;
    }
    window.GMBTrackedDomainsLoaded = true;
    
    console.log('GMB Tracked Domains: Loading on', window.location.href);

    // Load settings with retry mechanism
    if (typeof chrome !== 'undefined' && chrome.storage) {
      loadSettingsWithRetry();
    } else {
      // Fallback - always call init
      console.log('GMB Tracked Domains: Chrome storage not available, using defaults');
      init();
    }

    // Function to load settings with retry mechanism for race condition handling
    function loadSettingsWithRetry(maxRetries = 3, delay = 500, retryCount = 0) {
      console.log(`GMB Tracked Domains: Loading settings (attempt ${retryCount + 1}/${maxRetries + 1})...`);
      
      chrome.storage.local.get(['gmbExtractorSettings'], function(result) {
        const settings = result.gmbExtractorSettings || {};
        const domains = settings.trackedDomainsList || [];
        
        console.log('GMB Tracked Domains: Raw storage result:', {
          hasSettings: !!result.gmbExtractorSettings,
          settingsKeys: Object.keys(settings),
          domainsCount: domains.length,
          enabled: settings.trackedDomainsEnabled
        });
        
        // Check if we got meaningful data or if this might be a timing issue
        const hasSettings = !!result.gmbExtractorSettings;
        const hasTrackedDomainsKey = settings.hasOwnProperty('trackedDomainsList');
        const shouldRetry = !hasSettings || (!hasTrackedDomainsKey && retryCount < maxRetries);
        
        if (shouldRetry) {
          console.log(`GMB Tracked Domains: Storage appears incomplete (hasSettings: ${hasSettings}, hasTrackedDomainsKey: ${hasTrackedDomainsKey}), retrying in ${delay}ms... (${maxRetries - retryCount} retries left)`);
          setTimeout(() => loadSettingsWithRetry(maxRetries, delay, retryCount + 1), delay);
          return;
        }
        
        // Proceed with initialization - we either got good data or exhausted retries
        isEnabled = settings.trackedDomainsEnabled !== false; // Default to true
        trackedDomains = domains;
        highlightColor = settings.trackedDomainsColor || '#7c3aed';
        
        console.log('GMB Tracked Domains: Settings loaded after', retryCount + 1, 'attempts - enabled:', isEnabled, 'domains:', trackedDomains.length, 'color:', highlightColor);
        
        if (trackedDomains.length === 0) {
          console.log('GMB Tracked Domains: Warning - loaded 0 tracked domains. This may indicate a timing issue if domains are configured in settings.');
        }
        
        // Always call init() regardless of settings - let init() decide what to do
        init();
      });
    }

    // Function to normalize domain for comparison
    function normalizeDomain(domain) {
      return domain.toLowerCase().replace(/^www\./, '');
    }

    // Function to extract domain from URL
    function extractDomainFromUrl(url) {
      try {
        const urlObj = new URL(url);
        return normalizeDomain(urlObj.hostname);
      } catch (error) {
        // If URL parsing fails, try to extract domain from href text
        const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^\/\s]+)/);
        return match ? normalizeDomain(match[1]) : '';
      }
    }

    // Function to check if a domain matches any tracked domain
    function isTrackedDomain(domain) {
      const normalizedDomain = normalizeDomain(domain);
      return trackedDomains.some(trackedDomain => {
        const normalizedTracked = normalizeDomain(trackedDomain);
        return normalizedDomain === normalizedTracked || normalizedDomain.endsWith('.' + normalizedTracked);
      });
    }

    // Enhanced Multi-Method Sponsored Link Detection System
    function isSponsoredResult(resultElement) {
      if (!resultElement) return false;
      
      const detectionMethods = {
        textPattern: 0,
        structuralAnalysis: 0,
        positionBased: 0,
        dataAttributes: 0,
        urlPattern: 0,
        cssClasses: 0
      };
      
      let totalConfidence = 0;
      const maxConfidence = Object.keys(detectionMethods).length * 10; // Each method can contribute max 10 points
      
      // 1. Enhanced Text Pattern Matching
      const enhancedTextPatterns = [
        /\b(sponsored|ad|advertisement|promoted)\b/i,
        /\b(publicité|annonce|sponsorisé)\b/i, // French
        /\b(anuncio|patrocinado|promocionado)\b/i, // Spanish
        /\b(werbung|anzeige|gesponsert)\b/i, // German
        /\b(広告|スポンサー)\b/i, // Japanese
        /\b(广告|赞助)\b/i // Chinese
      ];
      
      const walker = document.createTreeWalker(
        resultElement,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );
      
      let textNode;
      while (textNode = walker.nextNode()) {
        const text = textNode.textContent.trim();
        if (text.length > 0) {
          for (const pattern of enhancedTextPatterns) {
            if (pattern.test(text)) {
              // Check context to avoid false positives
              const parent = textNode.parentElement;
              if (parent) {
                const parentClasses = parent.className || '';
                const parentText = parent.textContent || '';
                
                // Higher confidence if in ad-specific contexts
                if (parentClasses.includes('ad') || parentClasses.includes('sponsor') || 
                    parentText.length < 50) { // Short text more likely to be ad label
                  detectionMethods.textPattern = 10;
                } else {
                  detectionMethods.textPattern = 6;
                }
              } else {
                detectionMethods.textPattern = 4;
              }
              break;
            }
          }
        }
      }
      
      // 2. Structural Analysis Approach
      const adStructureSelectors = [
        '[data-text-ad]',
        '[data-hveid][data-ved*="ad"]',
        '.commercial-unit',
        '.ads-ad',
        '.mnr-c',
        '.ad-unit',
        '.shopping-ad',
        '.pla-unit',
        '[role="complementary"] .g',
        '.ads-visurl',
        '.pla-hovercard-content-ellip'
      ];
      
      for (const selector of adStructureSelectors) {
        if (resultElement.querySelector(selector) || resultElement.matches(selector)) {
          detectionMethods.structuralAnalysis = 10;
          break;
        }
      }
      
      // Check for ad-specific DOM patterns
      const adContainers = resultElement.querySelectorAll('div[id*="tads"], div[id*="bottomads"], .commercial-unit-desktop-top');
      if (adContainers.length > 0) {
        detectionMethods.structuralAnalysis = Math.max(detectionMethods.structuralAnalysis, 8);
      }
      
      // 3. Position-Based Detection
      const resultIndex = Array.from(resultElement.parentNode?.children || []).indexOf(resultElement);
      const totalResults = resultElement.parentNode?.children.length || 0;
      
      // Top positions (0-2) more likely to be ads
      if (resultIndex <= 2 && totalResults > 3) {
        detectionMethods.positionBased = 6;
      }
      
      // Check if in dedicated ad sections
      const adSections = ['#tads', '#bottomads', '.commercial-unit-desktop-top', '.ads-ad'];
      for (const section of adSections) {
        if (resultElement.closest(section)) {
          detectionMethods.positionBased = 10;
          break;
        }
      }
      
      // 4. Data Attribute Analysis
      const adDataAttributes = [
        'data-text-ad',
        'data-ad-slot',
        'data-google-av-cxn',
        'data-google-av-cpmav',
        'data-google-av-adk',
        'data-google-query-id'
      ];
      
      let adAttributeCount = 0;
      for (const attr of adDataAttributes) {
        if (resultElement.hasAttribute(attr) || resultElement.querySelector(`[${attr}]`)) {
          adAttributeCount++;
        }
      }
      
      if (adAttributeCount >= 3) {
        detectionMethods.dataAttributes = 10;
      } else if (adAttributeCount >= 1) {
        detectionMethods.dataAttributes = adAttributeCount * 3;
      }
      
      // Check for Google Ads specific data attributes
      const vedAttribute = resultElement.getAttribute('data-ved') || '';
      if (vedAttribute.includes('ad') || vedAttribute.includes('2ahU')) {
        detectionMethods.dataAttributes = Math.max(detectionMethods.dataAttributes, 7);
      }
      
      // 5. URL Pattern Detection
      const links = resultElement.querySelectorAll('a[href]');
      for (const link of links) {
        const href = link.getAttribute('href') || '';
        
        // Google Ads URL patterns
        const adUrlPatterns = [
          /googleadservices\.com/,
          /googleads\.g\.doubleclick\.net/,
          /google\.com\/aclk/,
          /google\.com\/pagead/,
          /google\.com\/url.*adurl=/,
          /ads\.google\.com/,
          /doubleclick\.net/
        ];
        
        for (const pattern of adUrlPatterns) {
          if (pattern.test(href)) {
            detectionMethods.urlPattern = 10;
            break;
          }
        }
        
        if (detectionMethods.urlPattern === 10) break;
      }
      
      // 6. Enhanced CSS Class Detection
      const adCssPatterns = [
        /\bad\b/i,
        /sponsor/i,
        /commercial/i,
        /promoted/i,
        /\btads\b/i,
        /\bpla\b/i, // Product Listing Ads
        /shopping.*ad/i,
        /ads.*visurl/i
      ];
      
      const allClasses = [];
      const elementsWithClasses = resultElement.querySelectorAll('[class]');
      elementsWithClasses.forEach(el => {
        allClasses.push(...(el.className.split(/\s+/) || []));
      });
      
      // Also check the result element itself
      if (resultElement.className) {
        allClasses.push(...resultElement.className.split(/\s+/));
      }
      
      let classMatchCount = 0;
      for (const className of allClasses) {
        for (const pattern of adCssPatterns) {
          if (pattern.test(className)) {
            classMatchCount++;
            break;
          }
        }
      }
      
      if (classMatchCount >= 3) {
        detectionMethods.cssClasses = 10;
      } else if (classMatchCount >= 1) {
        detectionMethods.cssClasses = classMatchCount * 3;
      }
      
      // Calculate total confidence score
      totalConfidence = Object.values(detectionMethods).reduce((sum, score) => sum + score, 0);
      const confidencePercentage = (totalConfidence / maxConfidence) * 100;
      
      // Log detailed detection results for debugging
      if (totalConfidence > 0) {
        console.log('GMB Sponsored Detection:', {
          element: resultElement,
          methods: detectionMethods,
          totalConfidence,
          confidencePercentage: Math.round(confidencePercentage),
          isSponsored: confidencePercentage >= 30 // 30% threshold
        });
      }
      
      // Return true if confidence is above threshold (30%)
      return confidencePercentage >= 30;
    }

        // Function to detect if we're in Maps/Local-only view
    function isLocalOnlyView() {
      const url = window.location.href;
      return url.includes('tbm=lcl') || url.includes('rlfi=') || url.includes('mv:');
    }

    // Function to detect if we're in Maps search view
    function isMapsSearchView() {
      const url = window.location.href;
      return url.includes('google.com/maps/search');
    }

        // Function to count ALL sponsored results in top 4 positions
    function countSponsoredInTop4() {
      let sponsoredCount = 0;
      
      if (isLocalOnlyView()) {
        // Maps/Local-only view: Count .pXf2tf sponsored results in top 4 positions
        console.log('GMB Tracked Domains: Counting sponsored results in top 4 positions for Maps/Local-only view');
        
        // Get all business links in DOM order
        const allBusinessLinks = document.querySelectorAll('a.yYlJEf.Q7PwXb.L48Cpd.brKmxb');
        const top4BusinessLinks = Array.from(allBusinessLinks).slice(0, 4); // Only check first 4
        
        console.log(`GMB Tracked Domains: Checking ${top4BusinessLinks.length} business links in top 4 positions`);
        
        // Check each of the top 4 business links for sponsored markers
        top4BusinessLinks.forEach((link, index) => {
          const href = link.getAttribute('href') || '';
          
          // Check if this is a sponsored ad by URL pattern
          const isSponsoredByUrl = href.includes('googleadservices.com') || 
                                   href.includes('google.com/aclk') || 
                                   href.includes('google.com/pagead');
          
          if (isSponsoredByUrl) {
            sponsoredCount++;
            console.log(`GMB Tracked Domains: Found sponsored result at position ${index + 1} by URL pattern: ${href.substring(0, 60)}...`);
          } else {
            console.log(`GMB Tracked Domains: Position ${index + 1} is organic: ${href.substring(0, 60)}...`);
          }
        });
        
        console.log(`GMB Tracked Domains: Found ${sponsoredCount} sponsored results in top 4 positions`);
      } else {
        // Regular search view: Check 3-pack within search results
        console.log('GMB Tracked Domains: Counting sponsored results in top 4 positions for regular search view');
        
        const threePack3Links = document.querySelectorAll('a.yYlJEf.Q7PwXb.L48Cpd.brKmxb');
        const top4Links = Array.from(threePack3Links).slice(0, 4); // Only check first 4
        
        console.log(`GMB Tracked Domains: Checking ${top4Links.length} 3-pack links in top 4 positions`);
        
        // Check each of the top 4 links for sponsored markers
        top4Links.forEach((link, index) => {
          const container = link.closest('[data-ved]') || link.closest('.g') || link.closest('[data-hveid]');
          
          if (container) {
            // Use TreeWalker to find "Sponsored" text
            const walker = document.createTreeWalker(
              container,
              NodeFilter.SHOW_TEXT,
              null,
              false
            );
            
            let textNode;
            while (textNode = walker.nextNode()) {
              const text = textNode.textContent.trim();
              if (/sponsored/i.test(text)) {
                sponsoredCount++;
                console.log(`GMB Tracked Domains: Found sponsored result at position ${index + 1}: "${text}"`);
                break; // Stop checking this container once we find sponsored text
              }
            }
          }
        });
        
        console.log(`GMB Tracked Domains: Found ${sponsoredCount} sponsored results in top 4 positions`);
      }
      
      return sponsoredCount;
    }

        // Function to highlight tracked domains in Maps search results
    function highlightTrackedMaps() {
      if (!isEnabled || trackedDomains.length === 0) return [];
      
      console.log('GMB Tracked Domains: Starting Maps search highlighting...');
      
      const trackedMapsPositions = [];
      let highlightCount = 0;
      
      // Find Maps business cards
      const businessCards = document.querySelectorAll('.Nv2PK.tH5CWc.THOPZb');
      console.log(`GMB Tracked Domains: Found ${businessCards.length} Maps business cards`);
      
      // Count sponsored results in Maps (look for h1 with aria-label="Sponsored")
      let sponsoredCount = 0;
      for (let i = 0; i < Math.min(4, businessCards.length); i++) {
        const card = businessCards[i];
        const sponsoredHeading = card.querySelector('h1[aria-label="Sponsored"]');
        if (sponsoredHeading) {
          sponsoredCount++;
          console.log(`GMB Tracked Domains: Maps card ${i + 1} is SPONSORED (h1[aria-label="Sponsored"])`);
        }
      }
      console.log(`GMB Tracked Domains: Found ${sponsoredCount} sponsored Maps results in top 4`);
      
      businessCards.forEach((card, index) => {
        try {
          // Skip if already processed
          if (card.classList.contains('gmb-tracked-maps-processed')) {
            return;
          }
          
          // Find website link with "Visit" aria-label
          const websiteLinks = card.querySelectorAll('a[aria-label*="Visit"]');
          let domain = '';
          let websiteUrl = '';
          
          for (const link of websiteLinks) {
            const href = link.getAttribute('href');
            if (href && href.startsWith('http')) {
              websiteUrl = href;
              domain = extractDomainFromUrl(href);
              console.log(`GMB Tracked Domains: Maps card ${index + 1} - Found website: ${domain} (${href.substring(0, 60)}...)`);
              break;
            }
          }
          
          if (domain) {
            console.log(`GMB Tracked Domains: Maps card ${index + 1} - Checking if ${domain} is tracked...`);
            const isTracked = isTrackedDomain(domain);
            console.log(`GMB Tracked Domains: Maps card ${index + 1} - Result: ${isTracked}`);
            
            if (isTracked) {
              // Mark card as processed
              card.classList.add('gmb-tracked-maps-processed');
              
              console.log(`GMB Tracked Domains: Maps card ${index + 1} - Domain ${domain} is TRACKED - proceeding with highlighting`);
            
              // Find the business name element to highlight
              let businessNameElement = card.querySelector('.qBF1Pd.fontHeadlineSmall.kiIehc.Hi2drd');
              console.log(`GMB Tracked Domains: Maps card ${index + 1} - Primary business name element found:`, businessNameElement);
              
              // If primary selector fails, try alternative selectors
              if (!businessNameElement) {
                console.log(`GMB Tracked Domains: Maps card ${index + 1} - Primary selector failed, trying alternatives for ${domain}`);
                
                const alternativeSelectors = [
                  '.fontHeadlineSmall',
                  '[role="heading"]',
                  'h1, h2, h3, h4',
                  '.qBF1Pd',
                  '[data-value="Title"]',
                  'span[aria-label]',
                  '.fontBodyMedium'
                ];
                
                for (const selector of alternativeSelectors) {
                  const altElement = card.querySelector(selector);
                  if (altElement && altElement.textContent.trim()) {
                    console.log(`GMB Tracked Domains: Maps card ${index + 1} - Found alternative business name element with selector '${selector}':`, altElement, 'Text:', altElement.textContent.trim());
                    businessNameElement = altElement;
                    break;
                  }
                }
              }
            
              if (businessNameElement && !businessNameElement.classList.contains('gmb-tracked-domain-title')) {
                businessNameElement.classList.add('gmb-tracked-domain-title');
                businessNameElement.id = `gmb-tracked-maps-${highlightCount}`;
                
                // Calculate position with sponsored ads adjustment
                const actualPosition = index + 1;
                const adjustedPosition = Math.max(1, actualPosition - sponsoredCount);
                
                trackedMapsPositions.push({
                  position: `M${adjustedPosition}`, // M for Maps
                  actualPosition: `M${actualPosition}`,
                  elementId: `gmb-tracked-maps-${highlightCount}`,
                  domain: domain,
                  type: 'maps'
                });
                
                highlightCount++;
                console.log(`GMB Tracked Domains: Successfully highlighted Maps business ${domain} at actual position M${actualPosition}, adjusted to M${adjustedPosition} (${sponsoredCount} sponsored in top 4)`);
              } else if (!businessNameElement) {
                console.log(`GMB Tracked Domains: Maps card ${index + 1} - No business name element found with any selector for ${domain}`);
                
                // Log the card structure for debugging
                console.log(`GMB Tracked Domains: Maps card ${index + 1} - Card HTML structure:`, card.outerHTML.substring(0, 500) + '...');
              } else {
                console.log(`GMB Tracked Domains: Maps card ${index + 1} - Business name element already highlighted for ${domain}`);
              }
            } else {
              console.log(`GMB Tracked Domains: Maps card ${index + 1} - Domain ${domain} is not tracked`);
            }
          }
        } catch (error) {
          console.error('GMB Tracked Domains: Error processing Maps business card:', error);
        }
      });
      
      console.log(`GMB Tracked Domains: Maps highlighting completed - found ${trackedMapsPositions.length} tracked domains`);
      return trackedMapsPositions;
    }

    // Function to create jump navigation for Maps search
    function createMapsJumpNavigation(trackedPositions) {
      // Remove existing jump navigation
      const existingNav = document.getElementById('gmb-jump-navigation-maps');
      if (existingNav) {
        existingNav.remove();
      }

      // Create navigation container
      const jumpNav = document.createElement('div');
      jumpNav.id = 'gmb-jump-navigation-maps';
      jumpNav.style.cssText = `
        background: #1a1a1a !important;
        border: 1px solid #333333 !important;
        border-radius: 8px !important;
        padding: 12px 16px !important;
        margin: 16px 0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        font-family: Google Sans,Roboto,Arial,sans-serif !important;
        color: #9aa0a6 !important;
        font-size: 14px !important;
        position: sticky !important;
        top: 20px !important;
        z-index: 1000 !important;
      `;

      // Add Maps label
      const label = document.createElement('span');
      label.textContent = 'Maps:';
      label.style.cssText = `
        margin-right: 8px !important;
        color: #9aa0a6 !important;
        font-weight: 500 !important;
        min-width: 50px !important;
      `;
      jumpNav.appendChild(label);

      // Create jump buttons for each tracked position
      trackedPositions.forEach((tracked) => {
        // Create container for button + URL
        const itemContainer = document.createElement('div');
        itemContainer.style.cssText = `
          display: flex !important;
          align-items: center !important;
          gap: 8px !important;
        `;

        // Create jump button
        const jumpButton = document.createElement('button');
        jumpButton.textContent = tracked.position;
        jumpButton.style.cssText = `
          background: ${highlightColor} !important;
          color: white !important;
          border: none !important;
          border-radius: 4px !important;
          padding: 4px 8px !important;
          font-size: 12px !important;
          font-weight: 600 !important;
          cursor: pointer !important;
          transition: all 0.2s ease !important;
          min-width: 24px !important;
          text-align: center !important;
          flex-shrink: 0 !important;
        `;

        // Create domain URL text
        const domainText = document.createElement('span');
        domainText.textContent = tracked.domain;
        domainText.style.cssText = `
          color: #9aa0a6 !important;
          font-size: 12px !important;
          font-weight: 400 !important;
          cursor: pointer !important;
          transition: color 0.2s ease !important;
        `;

        // Add hover effects
        jumpButton.addEventListener('mouseenter', () => {
          jumpButton.style.filter = 'brightness(1.2)';
          jumpButton.style.transform = 'scale(1.05)';
        });

        jumpButton.addEventListener('mouseleave', () => {
          jumpButton.style.filter = 'brightness(1)';
          jumpButton.style.transform = 'scale(1)';
        });

        domainText.addEventListener('mouseenter', () => {
          domainText.style.color = '#ffffff';
        });

        domainText.addEventListener('mouseleave', () => {
          domainText.style.color = '#9aa0a6';
        });

        // Add click functionality
        const clickHandler = (e) => {
          e.preventDefault();
          e.stopPropagation();
          
          const targetElement = document.getElementById(tracked.elementId);
          if (targetElement) {
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            });
            
            // Add brief highlight effect
            const originalFilter = targetElement.style.filter;
            targetElement.style.filter = 'brightness(1.3)';
            setTimeout(() => {
              targetElement.style.filter = originalFilter;
            }, 1000);
          }
        };

        jumpButton.addEventListener('click', clickHandler);
        domainText.addEventListener('click', clickHandler);

        itemContainer.appendChild(jumpButton);
        itemContainer.appendChild(domainText);
        jumpNav.appendChild(itemContainer);
      });

      // Insert after the searchbox element within the omnibox
      const searchboxElement = document.querySelector('#searchbox');
      if (searchboxElement && searchboxElement.parentNode) {
        if (searchboxElement.nextSibling) {
          searchboxElement.parentNode.insertBefore(jumpNav, searchboxElement.nextSibling);
        } else {
          searchboxElement.parentNode.appendChild(jumpNav);
        }
        console.log('GMB Tracked Domains: Maps jump navigation inserted after searchbox element');
      } else {
        // Fallback: Insert after omnibox container
        const omniboxContainer = document.querySelector('#omnibox-container');
        if (omniboxContainer && omniboxContainer.parentNode) {
          if (omniboxContainer.nextSibling) {
            omniboxContainer.parentNode.insertBefore(jumpNav, omniboxContainer.nextSibling);
          } else {
            omniboxContainer.parentNode.appendChild(jumpNav);
          }
          console.log('GMB Tracked Domains: Maps jump navigation inserted after omnibox container (fallback)');
        } else {
          // Final fallback: Insert at top of page
          document.body.insertBefore(jumpNav, document.body.firstChild);
          console.log('GMB Tracked Domains: Maps jump navigation inserted at top of page (final fallback)');
        }
      }
    }

        // Function to highlight tracked domains in 3-pack (local business results)
    function highlightTracked3Pack() {
      if (!isEnabled || trackedDomains.length === 0) return [];
      
      console.log('GMB Tracked Domains: Starting 3-pack highlighting...');
      
      const tracked3PackPositions = [];
      let highlightCount = 0;
      
      // Count ALL sponsored results in top 4 positions (applies to all tracked domains)
      const sponsoredInTop4 = countSponsoredInTop4();
      
      // Find local business links based on the view type
      let localBusinessLinks;
      
      if (isLocalOnlyView()) {
        // Maps/Local-only view: Find the same clickable links as 3-pack view
        // These are the website/business links that contain the domain we want to track
        localBusinessLinks = document.querySelectorAll('a.yYlJEf.Q7PwXb.L48Cpd.brKmxb');
        console.log(`GMB Tracked Domains: Found ${localBusinessLinks.length} business website links in Maps/Local-only view`);
      } else {
        // Regular search view: Find 3-pack links with the specific class
        localBusinessLinks = document.querySelectorAll('a.yYlJEf.Q7PwXb.L48Cpd.brKmxb');
        console.log(`GMB Tracked Domains: Found ${localBusinessLinks.length} 3-pack links in regular search view`);
      }
      
      localBusinessLinks.forEach((link, index) => {
        try {
          // Skip if already processed (same logic for both views now)
          if (link.classList.contains('gmb-tracked-3pack-processed')) {
            return;
          }
          
          const href = link.getAttribute('href');
          if (!href) return;
          
          let domain = '';
          if (href.startsWith('http')) {
            domain = extractDomainFromUrl(href);
          } else if (href.startsWith('/url?q=')) {
            // Handle Google redirect URLs
            const urlParam = new URLSearchParams(href.substring(5));
            const actualUrl = urlParam.get('q');
            if (actualUrl) {
              domain = extractDomainFromUrl(actualUrl);
            }
          }
          
          if (domain && isTrackedDomain(domain)) {
            // Mark link as processed (same for both views)
            link.classList.add('gmb-tracked-3pack-processed');
            link.classList.add('gmb-tracked-domain');
            
            // Find the business name element to highlight (same logic for both views)
            let businessNameElement = null;
            
            // First try to find it in the same parent container
            const parentContainer = link.closest('[data-ved]') || link.closest('[data-cid]');
            if (parentContainer) {
              businessNameElement = parentContainer.querySelector('span.OSrXXb');
            }
            
            // If not found, try searching in nearby elements
            if (!businessNameElement) {
              // Look for OSrXXb span in the broader area
              const container = link.closest('.g') || link.closest('[data-hveid]') || link.closest('[data-cid]');
              if (container) {
                businessNameElement = container.querySelector('span.OSrXXb');
              }
            }
            
            if (businessNameElement && !businessNameElement.classList.contains('gmb-tracked-domain-title')) {
              businessNameElement.classList.add('gmb-tracked-domain-title');
              businessNameElement.id = `gmb-tracked-3pack-${highlightCount}`;
            }
            
            if (businessNameElement) {
              // Calculate adjusted position: actual position minus ALL sponsored results in top 4
              const actualPosition = index + 1; // 1-based position in DOM
              const adjustedPosition = Math.max(1, actualPosition - sponsoredInTop4);
              
              tracked3PackPositions.push({
                position: `L${adjustedPosition}`, // Adjusted position for display
                actualPosition: `L${actualPosition}`, // Actual DOM position for reference
                elementId: `gmb-tracked-3pack-${highlightCount}`,
                domain: domain,
                type: 'local'
              });
              
              highlightCount++;
              console.log(`GMB Tracked Domains: Highlighted business name for ${domain} at actual position L${actualPosition}, adjusted to L${adjustedPosition} (${sponsoredInTop4} total sponsored in top 4)`, businessNameElement);
            } else {
              console.log(`GMB Tracked Domains: Could not find business name span (.OSrXXb) for ${domain}`);
            }
          }
        } catch (error) {
          console.error('GMB Tracked Domains: Error processing 3-pack link:', error);
        }
      });
      
      console.log(`GMB Tracked Domains: 3-pack highlighting completed - found ${tracked3PackPositions.length} tracked domains`);
      return tracked3PackPositions;
    }

    // Function to highlight tracked domain links and create jump navigation
    function highlightTrackedDomains() {
      console.log('GMB Tracked Domains: highlightTrackedDomains called - enabled:', isEnabled, 'domains:', trackedDomains.length);
      
      if (!isEnabled) {
        console.log('GMB Tracked Domains: Feature is disabled, skipping highlighting');
        return;
      }
      
      if (trackedDomains.length === 0) {
        console.log('GMB Tracked Domains: No tracked domains configured, skipping highlighting');
        return;
      }
      
      console.log('GMB Tracked Domains: Starting highlighting with domains:', trackedDomains);
      
      // Check what type of page we're on and highlight accordingly
      let trackedPositions = [];
      
      if (isMapsSearchView()) {
        // Maps search view
        console.log('GMB Tracked Domains: Detected Maps search view');
        const trackedMapsPositions = highlightTrackedMaps();
        trackedPositions = [...trackedMapsPositions];
        
        // Create jump navigation for Maps
        if (trackedPositions.length > 0) {
          createMapsJumpNavigation(trackedPositions);
        }
        
        console.log(`GMB Tracked Domains: Maps highlighting completed - found ${trackedPositions.length} tracked domains`);
        return trackedPositions.length;
      } else {
        // Regular search or local-only view - highlight 3-pack results
        const tracked3PackPositions = highlightTracked3Pack();
        
        // Find all links in search results
      const searchResultsContainer = document.querySelector('#search, #rso, .g');
      if (!searchResultsContainer) {
        console.log('GMB Tracked Domains: No search results container found');
        return;
      }

      // Get all search result containers (each individual result) - be more specific
      // First try to get actual search results with h3 elements
      let searchResults = Array.from(document.querySelectorAll('#rso div')).filter(div => {
        const h3 = div.querySelector('h3');
        const link = div.querySelector('a[href]');
        // Exclude "People also ask" and other non-result sections
        const isExcluded = div.querySelector('.related-question-pair') || 
                          div.querySelector('[data-initq]') ||
                          div.classList.contains('related-question-pair') ||
                          div.closest('.related-question-pair');
        return h3 && link && !isExcluded;
      });
      
      // Fallback to broader selection if needed
      if (searchResults.length === 0) {
        searchResults = document.querySelectorAll('.g, [data-hveid]');
      }
      
      let highlightCount = tracked3PackPositions.length; // Continue counting from 3-pack results
      const trackedOrganicPositions = [];
      const processedPositions = new Set(); // Track processed positions to avoid duplicates

      searchResults.forEach((resultContainer, index) => {
        try {
          // Skip if already processed
          if (resultContainer.classList.contains('gmb-tracked-result-processed')) {
            return;
          }

          // Find links within this result container
          const links = resultContainer.querySelectorAll('a[href]');
          let foundTrackedDomain = false;

          links.forEach(link => {
            if (foundTrackedDomain) return; // Only process first tracked domain per result

            const href = link.getAttribute('href');
            if (!href) return;

            // Extract domain from the href
            let domain = '';
            if (href.startsWith('http')) {
              domain = extractDomainFromUrl(href);
            } else if (href.startsWith('/url?q=')) {
              // Handle Google redirect URLs
              const urlParam = new URLSearchParams(href.substring(5));
              const actualUrl = urlParam.get('q');
              if (actualUrl) {
                domain = extractDomainFromUrl(actualUrl);
              }
            }

            if (domain && isTrackedDomain(domain)) {
              // Check if this is a sponsored result before processing
              if (isSponsoredResult(resultContainer)) {
                console.log(`GMB Tracked Domains: Skipping sponsored organic result for ${domain}`);
                return;
              }
              
              // Mark the link as processed
              link.classList.add('gmb-tracked-domain');
              resultContainer.classList.add('gmb-tracked-result-processed');
              
              // Find and style the h3 element inside the link
              const h3Element = link.querySelector('h3');
              if (h3Element && !h3Element.classList.contains('gmb-tracked-domain-title')) {
                
                // Find the position number from SERP numbering (it's inside the h3 element)
                const serpNumber = h3Element.querySelector('.serp-number-indicator');
                let position = null;
                
                if (serpNumber) {
                  position = serpNumber.textContent.replace('.', '').trim();
                  console.log(`GMB Tracked Domains: Found SERP number ${position} for ${domain}`);
                } else {
                  // Fallback: calculate position using EXACT same logic as serp-numbering.js
                  // Use the same TARGET_SELECTOR and validation logic
                  const TARGET_SELECTOR = 'h3.LC20lb.MBeuO.DKV0Md';
                  const allValidHeadings = document.querySelectorAll(TARGET_SELECTOR);
                  
                  let calculatedPosition = 0;
                  let counter = 1;
                  
                  for (const heading of allValidHeadings) {
                    // Apply same validation as serp-numbering.js
                    
                    // Skip if this heading is not visible
                    const rect = heading.getBoundingClientRect();
                    if (rect.width === 0 || rect.height === 0) {
                      continue;
                    }
                    
                    // Check if this is not an ad using comprehensive detection
                    const searchResult = heading.closest('[data-ved]');
                    if (!searchResult) {
                      continue;
                    }
                    
                    // Skip if this is a sponsored result
                    if (isSponsoredResult(searchResult)) {
                      continue;
                    }
                    
                    // Check if this is our heading
                    if (heading === h3Element) {
                      calculatedPosition = counter;
                      break;
                    }
                    
                    counter++;
                  }
                  
                  position = calculatedPosition > 0 ? calculatedPosition.toString() : null;
                  console.log(`GMB Tracked Domains: Calculated position ${position} for ${domain} using serp-numbering.js logic (checked ${allValidHeadings.length} headings, ${counter-1} valid)`);
                }
                
                // Only process if we have a position and it hasn't been processed yet
                if (position && !processedPositions.has(position)) {
                  processedPositions.add(position);
                  
                  h3Element.classList.add('gmb-tracked-domain-title');
                  h3Element.id = `gmb-tracked-result-${highlightCount}`;
                  
                  trackedOrganicPositions.push({
                    position: position,
                    elementId: `gmb-tracked-result-${highlightCount}`,
                    domain: domain,
                    type: 'organic'
                  });
                  
                  highlightCount++;
                  foundTrackedDomain = true;
                  console.log(`GMB Tracked Domains: Highlighted h3 for ${domain} at position ${position} (occurrence ${highlightCount})`);
                } else if (position) {
                  console.log(`GMB Tracked Domains: Skipping duplicate position ${position} for ${domain}`);
                }
              }
            }
          });
        } catch (error) {
          console.error('GMB Tracked Domains: Error processing result container:', error);
        }
      });

      // Combine 3-pack and organic results - 3-pack first, then organic
      const allTrackedPositions = [...tracked3PackPositions, ...trackedOrganicPositions];

      // Create jump navigation if we found tracked domains
      if (allTrackedPositions.length > 0) {
        createJumpNavigation(allTrackedPositions);
      }

      console.log(`GMB Tracked Domains: Completed - highlighted ${allTrackedPositions.length} total instances (${tracked3PackPositions.length} 3-pack, ${trackedOrganicPositions.length} organic)`);
      return allTrackedPositions.length;
      }
    }

    // Function to create jump navigation section after search results
    function createJumpNavigation(trackedPositions) {
      // Remove existing jump navigation
      const existingNav = document.getElementById('gmb-jump-navigation');
      if (existingNav) {
        existingNav.remove();
      }

      // Separate local and organic results
      const localResults = trackedPositions.filter(pos => pos.type === 'local');
      const organicResults = trackedPositions.filter(pos => pos.type === 'organic');

      // Create navigation container
      const jumpNav = document.createElement('div');
      jumpNav.id = 'gmb-jump-navigation';
      jumpNav.style.cssText = `
        background: #1a1a1a !important;
        border: 1px solid #333333 !important;
        border-radius: 8px !important;
        padding: 12px 16px !important;
        margin: 16px 0 !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 8px !important;
        font-family: Google Sans,Roboto,Arial,sans-serif !important;
        color: #9aa0a6 !important;
        font-size: 14px !important;
      `;

      // Helper function to create section
      function createSection(sectionTitle, results) {
        if (results.length === 0) return null;
        
        const sectionContainer = document.createElement('div');
        sectionContainer.style.cssText = `
          display: flex !important;
          align-items: center !important;
          gap: 8px !important;
        `;

                 // Add section label
         const label = document.createElement('span');
         label.textContent = sectionTitle;
         label.style.cssText = `
           margin-right: 8px !important;
           color: #9aa0a6 !important;
           font-weight: 500 !important;
           min-width: 50px !important;
         `;
        sectionContainer.appendChild(label);

        // Create jump buttons for each tracked position in this section
        results.forEach((tracked, index) => {
          // Create container for button + URL
          const itemContainer = document.createElement('div');
          itemContainer.style.cssText = `
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
          `;

          // Create jump button
          const jumpButton = document.createElement('button');
          jumpButton.textContent = tracked.position;
          jumpButton.className = 'gmb-jump-button';
          jumpButton.style.cssText = `
            background: ${highlightColor} !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            padding: 4px 8px !important;
            font-size: 12px !important;
            font-weight: 600 !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            min-width: 24px !important;
            text-align: center !important;
            flex-shrink: 0 !important;
          `;

          // Create domain URL text
          const domainText = document.createElement('span');
          domainText.textContent = tracked.domain;
          domainText.style.cssText = `
            color: #9aa0a6 !important;
            font-size: 12px !important;
            font-weight: 400 !important;
            cursor: pointer !important;
            transition: color 0.2s ease !important;
          `;

          // Add hover effects to button
          jumpButton.addEventListener('mouseenter', () => {
            jumpButton.style.filter = 'brightness(1.2)';
            jumpButton.style.transform = 'scale(1.05)';
          });

          jumpButton.addEventListener('mouseleave', () => {
            jumpButton.style.filter = 'brightness(1)';
            jumpButton.style.transform = 'scale(1)';
          });

          // Add hover effects to domain text
          domainText.addEventListener('mouseenter', () => {
            domainText.style.color = '#ffffff';
          });

          domainText.addEventListener('mouseleave', () => {
            domainText.style.color = '#9aa0a6';
          });

          // Add click functionality to both button and domain text
          const clickHandler = (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const targetElement = document.getElementById(tracked.elementId);
            if (targetElement) {
              targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
              });
              
              // Add a brief highlight effect
              const originalFilter = targetElement.style.filter;
              targetElement.style.filter = 'brightness(1.3)';
              setTimeout(() => {
                targetElement.style.filter = originalFilter;
              }, 1000);
            }
          };

          jumpButton.addEventListener('click', clickHandler);
          domainText.addEventListener('click', clickHandler);

          // Add button and domain to container
          itemContainer.appendChild(jumpButton);
          itemContainer.appendChild(domainText);
          
          sectionContainer.appendChild(itemContainer);
        });

        return sectionContainer;
      }

      // Create local section
      const localSection = createSection('Local:', localResults);
      if (localSection) {
        jumpNav.appendChild(localSection);
      }

      // Create organic section
      const organicSection = createSection('Organic:', organicResults);
      if (organicSection) {
        jumpNav.appendChild(organicSection);
      }

      // Only add to DOM if we have at least one section
      if (localSection || organicSection) {
        // Insert at the top, similar to where result stats appear
        const resultsSection = document.querySelector('.O4T6Pe.TPKH4e') || 
                               document.querySelector('#rso') ||
                               document.querySelector('#search > div:first-child');
        
        if (resultsSection && resultsSection.parentNode) {
          // Insert before the results section to place at top
          resultsSection.parentNode.insertBefore(jumpNav, resultsSection);
        } else {
          // Fallback: insert at beginning of search container
          const searchContainer = document.querySelector('#search');
          if (searchContainer && searchContainer.firstChild) {
            searchContainer.insertBefore(jumpNav, searchContainer.firstChild);
          }
        }
      }
    }

    // Initialize
    function init() {
      // Check if we're on a Google domain first
      const hostname = window.location.hostname;
      const isGoogleDomain = /^(www\.)?google\.(com|co\.uk|ca|com\.au|de|fr|es|it|nl|be|se|dk|no|fi|pl|cz|sk|hu|ro|bg|hr|si|ee|lv|lt|gr|pt|ie|lu|mt|cy)$/.test(hostname);
      
      if (!isGoogleDomain) {
        console.log('GMB Tracked Domains: Not on a Google domain, skipping');
        return;
      }
      
      // Check for Google search pages or Maps
      const url = window.location.href;
      const isSearchPage = url.includes('/search') || url.includes('#q=') || url.includes('&q=') || url.includes('?q=');
      const isMapsPage = isMapsSearchView();
      
      if (!isSearchPage && !isMapsPage) {
        console.log('GMB Tracked Domains: Not a Google search page or Maps, skipping. URL:', url);
        return;
      }
      
      console.log('GMB Tracked Domains: Initializing on valid Google page:', url);

      // Inject styles first - will use current settings
      injectStyles();
      
      // Run highlighting when DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          console.log('GMB Tracked Domains: DOM loaded, starting highlighting...');
          highlightTrackedDomains();
        });
      } else {
        // DOM already ready - wait for SERP numbering to complete
        console.log('GMB Tracked Domains: DOM already ready, scheduling highlighting...');
        setTimeout(() => {
          console.log('GMB Tracked Domains: Running delayed highlighting...');
          highlightTrackedDomains();
        }, 1500);
      }
      
      // Also run on window load as backup
      window.addEventListener('load', () => {
        console.log('GMB Tracked Domains: Window loaded, running backup highlighting...');
        setTimeout(highlightTrackedDomains, 2000);
      });

      // Observer for dynamic content changes (search result updates)
      const observer = new MutationObserver((mutations) => {
        let shouldHighlight = false;
        mutations.forEach((mutation) => {
          if (mutation.addedNodes.length > 0) {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === 1 && (node.tagName === 'A' || node.querySelector && node.querySelector('a'))) {
                shouldHighlight = true;
              }
            });
          }
        });
        
        if (shouldHighlight) {
          setTimeout(highlightTrackedDomains, 100);
        }
      });

      // Start observing
      const targetNode = document.querySelector('#search, #rso') || document.body;
      observer.observe(targetNode, {
        childList: true,
        subtree: true
      });

      console.log('GMB Tracked Domains: Initialized observer for dynamic content');
    }

    // Export for manual testing
    window.highlightTrackedDomains = highlightTrackedDomains;
    
    console.log('GMB Tracked Domains: Initialized and ready');
  }
  
  // Listen for settings updates from the settings page
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'updateSettings') {
        isEnabled = message.settings.trackedDomainsEnabled !== false;
        trackedDomains = message.settings.trackedDomainsList || [];
        highlightColor = message.settings.trackedDomainsColor || '#7c3aed';
        
        if (!isEnabled || trackedDomains.length === 0) {
          // Remove existing highlights and jump links if disabled or no domains
          const highlightedLinks = document.querySelectorAll('.gmb-tracked-domain');
          highlightedLinks.forEach(link => {
            link.classList.remove('gmb-tracked-domain');
            // Also remove highlighting from h3 elements
            const h3Element = link.querySelector('h3.gmb-tracked-domain-title');
            if (h3Element) {
              h3Element.classList.remove('gmb-tracked-domain-title');
              h3Element.removeAttribute('id');
            }
          });
          
          // Remove 3-pack highlights
          const highlighted3PackElements = document.querySelectorAll('.gmb-tracked-domain-title[id^="gmb-tracked-3pack-"]');
          highlighted3PackElements.forEach(element => {
            element.classList.remove('gmb-tracked-domain-title');
            element.removeAttribute('id');
          });
          
          // Remove jump navigation
          const jumpNavigation = document.getElementById('gmb-jump-navigation');
          if (jumpNavigation) {
            jumpNavigation.remove();
          }
          
          // Remove Maps jump navigation
          const mapsJumpNavigation = document.getElementById('gmb-jump-navigation-maps');
          if (mapsJumpNavigation) {
            mapsJumpNavigation.remove();
          }
          
          // Remove processed markers
          const processedResults = document.querySelectorAll('.gmb-tracked-result-processed');
          processedResults.forEach(result => {
            result.classList.remove('gmb-tracked-result-processed');
          });
          
          // Remove 3-pack processed markers
          const processed3PackLinks = document.querySelectorAll('.gmb-tracked-3pack-processed');
          processed3PackLinks.forEach(link => {
            link.classList.remove('gmb-tracked-3pack-processed');
          });
          
          console.log('GMB Tracked Domains: Disabled via settings update - removed highlights and jump links');
        } else {
                      // Re-enable and highlight if enabled
          console.log('GMB Tracked Domains: Enabled via settings update');
          if (!window.GMBTrackedDomainsLoaded) {
            initializeTrackedDomains();
          } else {
            // Re-inject styles with new color and re-highlight
            if (window.location.href.includes('/search')) {
              // Remove existing highlights and jump links first
              const highlightedElements = document.querySelectorAll('.gmb-tracked-domain-title');
              highlightedElements.forEach(el => {
                el.classList.remove('gmb-tracked-domain-title');
                el.removeAttribute('id');
              });
              
              const jumpNavigation = document.getElementById('gmb-jump-navigation');
              if (jumpNavigation) {
                jumpNavigation.remove();
              }
              
              const mapsJumpNavigation = document.getElementById('gmb-jump-navigation-maps');
              if (mapsJumpNavigation) {
                mapsJumpNavigation.remove();
              }
              
              const processedResults = document.querySelectorAll('.gmb-tracked-result-processed');
              processedResults.forEach(result => {
                result.classList.remove('gmb-tracked-result-processed');
              });
              
              // Remove 3-pack processed markers
              const processed3PackLinks = document.querySelectorAll('.gmb-tracked-3pack-processed');
              processed3PackLinks.forEach(link => {
                link.classList.remove('gmb-tracked-3pack-processed');
              });
              
              const trackedLinks = document.querySelectorAll('.gmb-tracked-domain');
              trackedLinks.forEach(link => {
                link.classList.remove('gmb-tracked-domain');
              });
              
              // Re-inject styles and re-highlight with new settings
              injectStyles();
              setTimeout(highlightTrackedDomains, 100);
            }
          }
        }
        sendResponse({received: true});
      }
    });
  }
  
  // Listen for storage changes to catch settings updates
  if (typeof chrome !== 'undefined' && chrome.storage) {
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'local' && changes.gmbExtractorSettings) {
        console.log('GMB Tracked Domains: Storage change detected, updating settings...');
        const newSettings = changes.gmbExtractorSettings.newValue || {};
        const oldEnabled = isEnabled;
        const oldDomains = [...trackedDomains];
        const oldColor = highlightColor;
        
        isEnabled = newSettings.trackedDomainsEnabled !== false;
        trackedDomains = newSettings.trackedDomainsList || [];
        highlightColor = newSettings.trackedDomainsColor || '#7c3aed';
        
        console.log('GMB Tracked Domains: Settings updated via storage change - enabled:', isEnabled, 'domains:', trackedDomains.length, 'color:', highlightColor);
        
        // Check if we need to update the highlighting
        const enabledChanged = oldEnabled !== isEnabled;
        const domainsChanged = JSON.stringify(oldDomains) !== JSON.stringify(trackedDomains);
        const colorChanged = oldColor !== highlightColor;
        
        if (enabledChanged || domainsChanged || colorChanged) {
          console.log('GMB Tracked Domains: Significant changes detected, re-initializing...');
          
          // Remove existing highlights first
          const highlightedElements = document.querySelectorAll('.gmb-tracked-domain-title');
          highlightedElements.forEach(el => {
            el.classList.remove('gmb-tracked-domain-title');
            el.removeAttribute('id');
          });
          
          const jumpNavigation = document.getElementById('gmb-jump-navigation');
          if (jumpNavigation) {
            jumpNavigation.remove();
          }
          
          const mapsJumpNavigation = document.getElementById('gmb-jump-navigation-maps');
          if (mapsJumpNavigation) {
            mapsJumpNavigation.remove();
          }
          
          // Clear processed markers
          const processedResults = document.querySelectorAll('.gmb-tracked-result-processed, .gmb-tracked-3pack-processed, .gmb-tracked-maps-processed');
          processedResults.forEach(result => {
            result.classList.remove('gmb-tracked-result-processed', 'gmb-tracked-3pack-processed', 'gmb-tracked-maps-processed');
          });
          
          const trackedLinks = document.querySelectorAll('.gmb-tracked-domain');
          trackedLinks.forEach(link => {
            link.classList.remove('gmb-tracked-domain');
          });
          
          // Re-inject styles and re-highlight with new settings
          if (isEnabled && trackedDomains.length > 0) {
            injectStyles();
            setTimeout(highlightTrackedDomains, 100);
          }
        }
      }
    });
  }
  
  // Initialize immediately with debug logging
  console.log('GMB Tracked Domains: Script starting initialization...');
  try {
    initializeTrackedDomains();
  } catch (error) {
    console.error('GMB Tracked Domains: Error during initialization:', error);
  }
})(); 