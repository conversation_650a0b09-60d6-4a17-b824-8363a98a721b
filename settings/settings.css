/* Settings Specific Styles */

.settings-tabs {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    margin-bottom: 20px;
    background: #1a1a1a;
    border-radius: 8px;
    padding: 4px;
    border: 1px solid #2a2a2a;
    gap: 2px;
    width: 100%;
    box-sizing: border-box;
}

.settings-tab {
    flex: 1 1 auto;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: #9ca3af;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
    text-align: center;
    white-space: nowrap;
    min-width: 140px;
    max-width: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-tab:hover {
    background: #262626;
    color: #e5e5e5;
}

.settings-tab--active {
    background: #0a0a0a!important;
    color: #ffffff;
    font-weight: 600;
    border-bottom: 2px solid #7c3aed;
}

.settings-tab--active:hover {
    background: #8b5cf6;
}

.settings-content {
    background: #1a1a1a;
    border-radius: 12px;
    padding: 28px;
    border: 1px solid #2a2a2a;
    margin-bottom: 20px;
    min-height: 400px;
}

.settings-panel {
    display: none;
}

.settings-panel--active {
    display: block;
}

.settings-section {
    margin-bottom: 32px;
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section__title {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #333333;
}

.settings-group {
    margin-bottom: 20px;
}

.settings-group__title {
    color: #e5e5e5;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 16px;
}

.settings-group__description {
    color: #9ca3af;
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 16px;
}

.settings-item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 16px 20px;
    background: #262626;
    border-radius: 10px;
    border: 1px solid #333333;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.settings-item:hover {
    background: #2a2a2a;
    border-color: #404040;
}

.settings-item:last-child {
    margin-bottom: 0;
}

.settings-item__content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    width: 100%;
    gap: 20px;
}

.settings-item__info {
    flex: 1;
    min-width: 0;
}

.settings-item__title {
    color: #e5e5e5;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 6px;
    line-height: 1.3;
}

.settings-item__label {
    color: #e5e5e5;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 6px;
    line-height: 1.3;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Chronometer info icon styling for settings */
.settings-item__label .chronometer-info-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 6px;
    transition: all 0.2s ease;
}

.settings-item__label .chronometer-info-icon::before {
    content: "●";
    color: #7C3AED;
    font-size: 12px;
    font-weight: bold;
}

.settings-item__label .chronometer-info-icon:hover::before {
    color: #ffffff;
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.3));
    transform: scale(1.1);
}

.settings-item__description {
    color: #9ca3af;
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
}

.settings-item__control {
    margin-left: 0;
    flex-shrink: 0;
    display: flex;
    align-items: center;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    width: 48px;
    height: 26px;
    background: #404040;
    border-radius: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #505050;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch:hover {
    background: #4a4a4a;
    border-color: #606060;
}

.toggle-switch::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #fff;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch--active {
    background: #7c3aed;
    border-color: #8b5cf6;
    box-shadow: inset 0 2px 4px rgba(124, 58, 237, 0.3);
}

.toggle-switch--active:hover {
    background: #8b5cf6;
    border-color: #a855f7;
}

.toggle-switch--active::before {
    transform: translateX(22px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Input Controls */
.settings-input {
    background: #333333;
    border: 1px solid #404040;
    border-radius: 6px;
    padding: 8px 12px;
    color: #e5e5e5;
    font-size: 13px;
    width: 120px;
    transition: all 0.2s ease;
}

.settings-input:focus {
    outline: none;
    border-color: #7c3aed;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.settings-select {
    background: #333333;
    border: 1px solid #404040;
    border-radius: 6px;
    padding: 8px 12px;
    color: #e5e5e5;
    font-size: 13px;
    min-width: 120px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.settings-select:focus {
    outline: none;
    border-color: #7c3aed;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.settings-select option {
    background: #333333;
    color: #e5e5e5;
}

/* Number input specific styling */
.settings-input[type="number"] {
    width: 80px;
    text-align: center;
}

/* Button in settings */
.settings-button {
    background: #333333;
    border: 1px solid #404040;
    border-radius: 6px;
    padding: 8px 16px;
    color: #e5e5e5;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 150px;
}

.settings-button:hover {
    background: #404040;
    border-color: #505050;
}

.settings-button--danger {
    background: #0a0a0a!important;
    border-color: #ef4444;
}

.settings-button--danger:hover {
    background: #ef4444;
    border-color: #f87171;
}

.settings-button--warning {
    background: #0a0a0a!important;
    border-color: #f59e0b;
}

.settings-button--warning:hover {
    background: #0a0a0a;
    border-color: #fbbf24;
}

/* Import/Export Section Button Styling */
.settings-section:has(.settings-section__title:contains("Import/Export")) .settings-button,
div[class*="Import/Export"] .settings-button,
/* Target Import/Export section buttons specifically */
#exportSettingsBtn,
#importSettingsBtn,
#resetSettingsBtn,
#clearDataBtn {
    background: #1a1a1a !important;
    border: 2px solid #404040 !important;
    color: #e5e5e5 !important;
    min-width: 150px !important;
    width: 150px !important;
    text-align: center !important;
    font-weight: 500 !important;
    padding: 10px 16px !important;
}

/* Orange border for Reset button */
#resetSettingsBtn {
    border-color: #f59e0b !important;
}

#resetSettingsBtn:hover {
    background: #262626 !important;
    border-color: #fbbf24 !important;
}

/* Red border for Clear All Data button */
#clearDataBtn {
    border-color: #ef4444 !important;
}

#clearDataBtn:hover {
    background: #262626 !important;
    border-color: #f87171 !important;
}

/* Default dark buttons hover */
#exportSettingsBtn:hover,
#importSettingsBtn:hover {
    background: #262626 !important;
    border-color: #505050 !important;
}

/* Tracked Domains Accordion */
.tracked-domains-accordion {
    margin-bottom: 8px;
    background: #1a1a1a;
    border-radius: 8px;
    border: 1px solid #2a2a2a;
    transition: all 0.2s ease;
}

.tracked-domains-header {
    padding: 12px;
    cursor: pointer;
    background: #1a1a1a;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
    transition: all 0.2s ease;
}

.tracked-domains-header:hover {
    background: #262626;
}

.tracked-domains-header.expanded {
    border-radius: 8px 8px 0 0;
    background: #262626;
}

.tracked-domains-title {
    font-weight: 500;
    font-size: 13px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #e5e5e5;
}

.tracked-domains-icon {
    transition: transform 0.2s ease;
    color: #9ca3af;
    font-size: 12px;
    font-weight: bold;
}

.tracked-domains-icon.expanded {
    transform: rotate(180deg);
}

.tracked-domains-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #1a1a1a;
}

.tracked-domains-content.expanded {
    max-height: 300px;
}

.tracked-domains-body {
    padding: 12px;
    border-top: 1px solid #2a2a2a;
}

.tracked-domains-input-group {
    margin-bottom: 0;
}

.tracked-domains-input-label {
    display: block;
    font-size: 11px;
    font-weight: 600;
    color: #9ca3af;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tracked-domains-textarea-wrapper {
    position: relative;
}

.tracked-domains-textarea {
    width: 100%;
    min-height: 120px;
    padding: 10px 12px;
    background: #262626;
    border: 1px solid #333333;
    border-radius: 6px;
    font-size: 13px;
    color: #e5e5e5;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.4;
    resize: vertical;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.tracked-domains-color-picker {
    position: absolute;
    bottom: 8px;
    right: 8px;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 6px;
    background: #1a1a1a;
    border: 1px solid #333333;
    border-radius: 6px;
    padding: 4px;
}

.tracked-domains-color-swatch {
    width: 20px;
    height: 20px;
    border: 1px solid #404040;
    border-radius: 4px;
    background: transparent;
    cursor: pointer;
    padding: 0;
    transition: all 0.2s ease;
}

.tracked-domains-color-swatch::-webkit-color-swatch-wrapper {
    padding: 0;
    border-radius: 3px;
}

.tracked-domains-color-swatch::-webkit-color-swatch {
    border: none;
    border-radius: 3px;
}

.tracked-domains-color-swatch:hover {
    border-color: #7c3aed;
    transform: scale(1.05);
}

.tracked-domains-color-hex {
    width: 65px;
    height: 20px;
    background: #262626;
    border: 1px solid #404040;
    border-radius: 4px;
    color: #e5e5e5;
    font-size: 10px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    text-align: center;
    padding: 0 4px;
    transition: all 0.2s ease;
}

.tracked-domains-color-hex:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 1px rgba(124, 58, 237, 0.2);
}

.tracked-domains-color-hex::placeholder {
    color: #6b7280;
    font-size: 9px;
}

.tracked-domains-textarea:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.tracked-domains-textarea::placeholder {
    color: #6b7280;
    font-style: italic;
}

.tracked-domains-help {
    margin-top: 8px;
    font-size: 11px;
    color: #6b7280;
    line-height: 1.4;
}

/* UTM Cleaner Accordion */
.utm-cleaner-accordion {
    margin-bottom: 8px;
    background: #1a1a1a;
    border-radius: 8px;
    border: 1px solid #2a2a2a;
    transition: all 0.2s ease;
}

.utm-cleaner-header {
    padding: 12px;
    cursor: pointer;
    background: #1a1a1a;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
    transition: all 0.2s ease;
}

.utm-cleaner-header:hover {
    background: #262626;
}

.utm-cleaner-header.expanded {
    border-radius: 8px 8px 0 0;
    background: #262626;
}

.utm-cleaner-title {
    font-weight: 500;
    font-size: 13px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #e5e5e5;
}

.utm-cleaner-icon {
    transition: transform 0.2s ease;
    color: #9ca3af;
    font-size: 12px;
    font-weight: bold;
}

.utm-cleaner-icon.expanded {
    transform: rotate(180deg);
}

.utm-cleaner-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #1a1a1a;
}

.utm-cleaner-content.expanded {
    max-height: 300px;
}

.utm-cleaner-body {
    padding: 12px;
    border-top: 1px solid #2a2a2a;
}

.utm-cleaner-input-group {
    margin-bottom: 0;
}

.utm-cleaner-input-label {
    display: block;
    font-size: 11px;
    font-weight: 600;
    color: #9ca3af;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.utm-cleaner-textarea-wrapper {
    position: relative;
}

.utm-cleaner-textarea {
    width: 100%;
    min-height: 120px;
    padding: 10px 12px;
    background: #262626;
    border: 1px solid #333333;
    border-radius: 6px;
    font-size: 13px;
    color: #e5e5e5;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.4;
    resize: vertical;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.utm-cleaner-textarea:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.utm-cleaner-textarea::placeholder {
    color: #6b7280;
    font-style: italic;
}

.utm-cleaner-help {
    margin-top: 8px;
    font-size: 11px;
    color: #6b7280;
    line-height: 1.4;
}

/* Minimal Reader Accordion Styles */
.minimal-reader-accordion {
    margin-bottom: 8px;
    background: #1a1a1a;
    border-radius: 8px;
    border: 1px solid #2a2a2a;
    transition: all 0.2s ease;
}

.minimal-reader-header {
    padding: 12px;
    cursor: pointer;
    background: #1a1a1a;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
    transition: all 0.2s ease;
}

.minimal-reader-header:hover {
    background: #262626;
}

.minimal-reader-header.expanded {
    border-radius: 8px 8px 0 0;
    background: #262626;
}

.minimal-reader-title {
    font-weight: 500;
    font-size: 13px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.minimal-reader-icon {
    font-size: 12px;
    color: #7C3AED;
    transition: transform 0.2s ease;
}

.minimal-reader-icon.expanded {
    transform: rotate(180deg);
}

.minimal-reader-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #1a1a1a;
}

.minimal-reader-content.expanded {
    max-height: 300px;
}

.minimal-reader-body {
    padding: 12px;
    border-top: 1px solid #2a2a2a;
}

.minimal-reader-input-group {
    margin-bottom: 0;
}

.minimal-reader-input-label {
    display: block;
    font-weight: 500;
    font-size: 12px;
    color: #d1d5db;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.minimal-reader-textarea-wrapper {
    position: relative;
    margin-bottom: 8px;
}

.minimal-reader-textarea {
    width: 100%;
    min-height: 100px;
    padding: 8px 12px;
    background: #262626;
    border: 1px solid #374151;
    border-radius: 6px;
    color: #d1d5db;
    font-size: 13px;
    font-family: 'Monaco', 'Menlo', monospace;
    line-height: 1.4;
    resize: vertical;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.minimal-reader-textarea:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

.minimal-reader-textarea::placeholder {
    color: #6b7280;
}

.minimal-reader-help {
    font-size: 12px;
    color: #9ca3af;
    margin-top: 8px;
    line-height: 1.4;
}

/* Drag Select Links Accordion */
.drag-select-accordion {
    margin-bottom: 8px;
    background: #1a1a1a;
    border-radius: 8px;
    border: 1px solid #2a2a2a;
    transition: all 0.2s ease;
}

.drag-select-header {
    padding: 12px;
    cursor: pointer;
    background: #1a1a1a;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
    transition: all 0.2s ease;
}

.drag-select-header:hover {
    background: #262626;
}

.drag-select-header.expanded {
    border-radius: 8px 8px 0 0;
    background: #262626;
}

.drag-select-title {
    font-weight: 500;
    font-size: 13px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #e5e5e5;
}

.drag-select-icon {
    transition: transform 0.2s ease;
    color: #9ca3af;
    font-size: 12px;
    font-weight: bold;
}

.drag-select-icon.expanded {
    transform: rotate(180deg);
}

.drag-select-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #1a1a1a;
}

.drag-select-content.expanded {
    max-height: 100%;
}

.drag-select-body {
    padding: 12px;
    border-top: 1px solid #2a2a2a;
}

.drag-select-input-group {
    margin-bottom: 12px;
}

.drag-select-input-group:last-child {
    margin-bottom: 0;
}

.drag-select-input-label {
    display: block;
    font-size: 11px;
    font-weight: 600;
    color: #9ca3af;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.drag-select-select {
    width: 100%;
    padding: 8px 12px;
    background: #262626;
    border: 1px solid #333333;
    border-radius: 6px;
    font-size: 13px;
    color: #e5e5e5;
    transition: all 0.2s ease;
    cursor: pointer;
}

.drag-select-select:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.drag-select-select option {
    background: #262626;
    color: #e5e5e5;
}

.drag-select-input {
    width: 100%;
    padding: 8px 12px;
    background: #262626;
    border: 1px solid #333333;
    border-radius: 6px;
    font-size: 13px;
    color: #e5e5e5;
    transition: all 0.2s ease;
}

.drag-select-input:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.drag-select-input::placeholder {
    color: #6b7280;
    font-style: italic;
}

.drag-select-color-picker {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 6px;
}

.drag-select-color-swatch {
    width: 24px;
    height: 24px;
    border: 1px solid #404040;
    border-radius: 4px;
    background: transparent;
    cursor: pointer;
    padding: 0;
    transition: all 0.2s ease;
}

.drag-select-color-swatch::-webkit-color-swatch-wrapper {
    padding: 0;
    border-radius: 3px;
}

.drag-select-color-swatch::-webkit-color-swatch {
    border: none;
    border-radius: 3px;
}

.drag-select-color-swatch:hover {
    border-color: #7c3aed;
    transform: scale(1.05);
}

.drag-select-color-hex {
    flex: 1;
    height: 24px;
    background: #262626;
    border: 1px solid #404040;
    border-radius: 4px;
    color: #e5e5e5;
    font-size: 11px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    text-align: center;
    padding: 0 8px;
    transition: all 0.2s ease;
}

.drag-select-color-hex:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 1px rgba(124, 58, 237, 0.2);
}

.drag-select-color-hex::placeholder {
    color: #6b7280;
    font-size: 10px;
}

.drag-select-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.drag-select-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #e5e5e5;
    font-size: 13px;
}

.drag-select-checkbox-label input[type="checkbox"] {
    appearance: none;
    width: 16px;
    height: 16px;
    border: 1px solid #404040;
    border-radius: 3px;
    background: #262626;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
}

.drag-select-checkbox-label input[type="checkbox"]:checked {
    background: #7c3aed;
    border-color: #7c3aed;
}

.drag-select-checkbox-label input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.drag-select-checkbox-label input[type="checkbox"]:hover {
    border-color: #7c3aed;
}

.drag-select-checkbox-text {
    user-select: none;
}

.drag-select-help {
    margin-top: 6px;
    font-size: 11px;
    color: #6b7280;
    line-height: 1.4;
}

/* Inline Color Picker Controls */
.settings-item__inline-controls {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    margin-left: 12px;
    vertical-align: middle;
}

.inline-color-swatch {
    width: 20px;
    height: 20px;
    border: 1px solid #404040;
    border-radius: 3px;
    background: transparent;
    cursor: pointer;
    padding: 0;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.inline-color-swatch::-webkit-color-swatch-wrapper {
    padding: 0;
    border-radius: 2px;
}

.inline-color-swatch::-webkit-color-swatch {
    border: none;
    border-radius: 2px;
}

.inline-color-swatch:hover {
    border-color: #7c3aed;
    transform: scale(1.05);
    box-shadow: 0 0 0 1px rgba(124, 58, 237, 0.3);
}

.inline-color-hex {
    width: 65px;
    height: 20px;
    background: #262626;
    border: 1px solid #404040;
    border-radius: 3px;
    color: #e5e5e5;
    font-size: 10px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    text-align: center;
    padding: 0 4px;
    transition: all 0.2s ease;
}

.inline-color-hex:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 1px rgba(124, 58, 237, 0.2);
}

.inline-color-hex::placeholder {
    color: #6b7280;
    font-size: 9px;
}

.inline-select {
    height: 32px;
    background: #262626;
    border: 1px solid #404040;
    border-radius: 3px;
    color: #e5e5e5;
    font-size: 14px;
    padding: 4px 8px;
    transition: all 0.2s ease;
    min-width: 140px;
}

.inline-select:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 1px rgba(124, 58, 237, 0.2);
}

.inline-select:hover {
    border-color: #7c3aed;
}

/* Custom Shortcut Controls */
.custom-shortcut-container {
    margin-top: 12px;
    margin-left: 12px;
    padding: 12px;
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.custom-shortcut-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    font-weight: 600;
    color: #9ca3af;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.shortcut-icon {
    font-size: 14px;
}

.custom-shortcut-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

.custom-shortcut-input {
    flex: 1;
    padding: 8px 12px;
    background: #262626;
    border: 1px solid #333333;
    border-radius: 6px;
    font-size: 13px;
    color: #e5e5e5;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    transition: all 0.2s ease;
    min-width: 200px;
}

.custom-shortcut-input:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.custom-shortcut-input::placeholder {
    color: #6b7280;
    font-style: italic;
}

.shortcut-help {
    position: relative;
    display: flex;
    align-items: center;
    cursor: help;
}

.help-icon {
    font-size: 14px;
    color: #6b7280;
    transition: color 0.2s ease;
}

.shortcut-help:hover .help-icon {
    color: #9ca3af;
}

.help-tooltip {
    position: absolute;
    top: -160px;
    right: 0;
    background: #0a0a0a;
    color: #d1d5db;
    border: 1px solid #374151;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 11px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
    box-shadow: 0 4px 16px rgba(0,0,0,0.4);
    max-width: 250px;
    line-height: 1.4;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.2s ease;
    pointer-events: none;
    white-space: nowrap;
}

.shortcut-help:hover .help-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.help-tooltip strong {
    color: #10b981;
    font-weight: 600;
}

/* Text Transformers tooltip positioning override */
.copy-replace-content .custom-shortcut-container .help-tooltip {
    top: -40px;
}

.shortcut-status {
    margin-top: 8px;
    font-size: 11px;
    line-height: 1.4;
    min-height: 15px;
}

.shortcut-status.success {
    color: #10b981;
}

.shortcut-status.error {
    color: #ef4444;
}

.shortcut-status.info {
    color: #3b82f6;
}

/* Format Options Styling */
.drag-select-format-section {
    background: #1a1a1a;
    border-radius: 8px;
    border: 1px solid #333333;
    padding: 16px;
    margin-top: 12px;
}

.drag-select-format-group {
    margin-bottom: 20px;
}

.drag-select-format-group:last-child {
    margin-bottom: 0;
}

.drag-select-format-title {
    color: #e5e5e5;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #2a2a2a;
}

.drag-select-radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.drag-select-radio-label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    background: #262626;
    border-radius: 6px;
    border: 1px solid #333333;
    cursor: pointer;
    transition: all 0.2s ease;
}

.drag-select-radio-label:hover {
    background: #2a2a2a;
    border-color: #404040;
}

.drag-select-radio-label input[type="radio"] {
    width: 16px;
    height: 16px;
    margin: 0;
    accent-color: #7c3aed;
    cursor: pointer;
    flex-shrink: 0;
    margin-top: 2px;
}

.drag-select-radio-label > div {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.drag-select-radio-text {
    color: #e5e5e5;
    font-size: 13px;
    font-weight: 500;
    display: block;
}

.drag-select-example {
    color: #9ca3af;
    font-size: 12px;
    line-height: 1.4;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #1a1a1a;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #2a2a2a;
    display: block;
    margin-top: 4px;
    word-break: break-all;
}

.drag-select-custom-group {
    background: #262626;
    border-radius: 6px;
    border: 1px solid #333333;
    padding: 12px;
}

.drag-select-custom-input {
    width: 100%;
    background: #1a1a1a;
    border: 1px solid #333333;
    border-radius: 6px;
    padding: 8px 12px;
    color: #e5e5e5;
    font-size: 13px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    resize: vertical;
    min-height: 60px;
    margin: 8px 0;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.drag-select-custom-input:focus {
    outline: none;
    border-color: #7c3aed;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.drag-select-custom-input::placeholder {
    color: #6b7280;
}

/* Copy Replace Accordion Styles */
.copy-replace-accordion {
    margin-bottom: 20px;
    background: #1a1a1a;
    border-radius: 8px;
    border: 1px solid #2a2a2a;
    transition: all 0.2s ease;
}

.copy-replace-header {
    padding: 12px;
    cursor: pointer;
    background: #1a1a1a;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
    transition: all 0.2s ease;
}

.copy-replace-header:hover {
    background: #262626;
}

.copy-replace-header.expanded {
    border-radius: 8px 8px 0 0;
    background: #262626;
}

.copy-replace-title {
    font-weight: 500;
    font-size: 13px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #e5e5e5;
}

.copy-replace-icon {
    transition: transform 0.2s ease;
    color: #9ca3af;
    font-size: 12px;
    font-weight: bold;
}

.copy-replace-icon.expanded {
    transform: rotate(180deg);
}

.copy-replace-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #1a1a1a;
}

.copy-replace-content.expanded {
    max-height: 100%;
}

.copy-replace-body {
    padding: 12px;
    border-top: 1px solid #2a2a2a;
}

.copy-replace-input-group {
    margin-bottom: 12px;
}

.copy-replace-input-group:last-child {
    margin-bottom: 0;
}

.copy-replace-input-label {
    display: block;
    font-size: 11px;
    font-weight: 600;
    color: #9ca3af;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.copy-replace-input {
    width: 100%;
    padding: 8px 12px;
    background: #262626;
    border: 1px solid #333333;
    border-radius: 6px;
    font-size: 13px;
    color: #e5e5e5;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.copy-replace-input:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.copy-replace-input::placeholder {
    color: #6b7280;
    font-style: italic;
}

.copy-replace-help {
    margin-top: 4px;
    font-size: 11px;
    color: #6b7280;
    line-height: 1.4;
}

.copy-replace-info {
    margin-top: 16px;
    padding: 12px;
    background: #262626;
    border-radius: 6px;
    border: 1px solid #333333;
}

.copy-replace-example {
    font-size: 12px;
    color: #d1d5db;
    line-height: 1.5;
}

.copy-replace-example strong {
    color: #e5e5e5;
    font-weight: 600;
}

.copy-replace-example code {
    background: #1a1a1a;
    color: #7c3aed;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 11px;
    border: 1px solid #2a2a2a;
}

/* Settings Accordion Styles */
.settings-accordion {
    margin-bottom: 16px;
    background: #1a1a1a;
    border-radius: 8px;
    border: 1px solid #2a2a2a;
    transition: all 0.2s ease;
}

.settings-accordion-header {
    padding: 16px;
    cursor: pointer;
    background: #1a1a1a;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
    transition: all 0.2s ease;
}

.settings-accordion-header:hover {
    background: #262626;
}

.settings-accordion-header.expanded {
    border-radius: 8px 8px 0 0;
    background: #262626;
}

.settings-accordion-title {
    font-weight: 600;
    font-size: 15px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #e5e5e5;
}

.settings-accordion-icon {
    transition: transform 0.2s ease;
    color: #9ca3af;
    font-size: 14px;
    font-weight: bold;
}

.settings-accordion-icon.expanded {
    transform: rotate(180deg);
}

.settings-accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease;
    background: #1a1a1a;
}

.settings-accordion-content.expanded {
    max-height: 2000px;
}

.settings-accordion-body {
    padding: 16px;
    border-top: 1px solid #2a2a2a;
}

.settings-accordion-body .settings-group {
    margin: 0;
}

.settings-accordion-body .settings-item {
    margin-bottom: 16px;
}

.settings-accordion-body .settings-item:last-child {
    margin-bottom: 0;
}

/* Advanced Features Search Bar */
.advanced-features-search-container {
    position: relative;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.advanced-features-search {
    width: 100%;
    padding: 12px 16px;
    padding-right: 40px; /* Space for clear button */
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 6px;
    color: #d1d5db;
    font-size: 14px;
    font-family: inherit;
    transition: all 0.2s ease;
}

.advanced-features-search:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.advanced-features-search::placeholder {
    color: #6b7280;
    font-size: 13px;
}

.advanced-features-search-clear {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: #9ca3af;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0;
    pointer-events: none;
}

.advanced-features-search-container:hover .advanced-features-search-clear,
.advanced-features-search:focus + .advanced-features-search-clear,
.advanced-features-search-clear.visible {
    opacity: 1;
    pointer-events: all;
}

.advanced-features-search-clear:hover {
    background: rgba(124, 58, 237, 0.2);
    color: #7C3AED;
    transform: translateY(-50%) scale(1.1);
}

/* Search highlighting for accordion titles */
.settings-accordion.search-highlight .settings-accordion-title {
    color: #7C3AED !important;
}

.settings-accordion.search-dimmed {
    opacity: 0.3;
    transition: opacity 0.2s ease;
}

.settings-accordion.search-dimmed:not(.search-highlight) {
    opacity: 0.3;
}

/* Individual setting highlighting within expanded accordions */
.settings-item.search-highlight-setting {
    background: rgba(124, 58, 237, 0.1) !important;
    border: 1px solid rgba(124, 58, 237, 0.3) !important;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.settings-item.search-highlight-setting .settings-item__label {
    color: #7C3AED !important;
    font-weight: 600;
}

/* Quick Actions Search Bar */
.quick-actions-search-container {
    position: relative;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.quick-actions-search {
    width: 100%;
    padding: 12px 16px;
    padding-right: 40px; /* Space for clear button */
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 6px;
    color: #d1d5db;
    font-size: 14px;
    font-family: inherit;
    transition: all 0.2s ease;
}

.quick-actions-search:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.quick-actions-search::placeholder {
    color: #6b7280;
    font-size: 13px;
}

.quick-actions-search-clear {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: #9ca3af;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0;
    pointer-events: none;
}

.quick-actions-search-container:hover .quick-actions-search-clear,
.quick-actions-search:focus + .quick-actions-search-clear,
.quick-actions-search-clear.visible {
    opacity: 1;
    pointer-events: all;
}

.quick-actions-search-clear:hover {
    background: rgba(124, 58, 237, 0.2);
    color: #7C3AED;
    transform: translateY(-50%) scale(1.1);
}

/* Quick Actions search highlighting */
.settings-item.quick-actions-search-highlight {
    background: rgba(124, 58, 237, 0.1) !important;
    border: 1px solid rgba(124, 58, 237, 0.3) !important;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.settings-item.quick-actions-search-highlight .settings-item__title {
    color: #7C3AED !important;
    font-weight: 600;
}

.settings-item.quick-actions-search-dimmed {
    opacity: 0.3;
    transition: opacity 0.2s ease;
}

/* Select All Toggle Styling */
.settings-item--select-all {
    background: #2a2a2a !important;
    border: 1px solid #7C3AED !important;
    position: relative;
    overflow: hidden;
}

.settings-item--select-all::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #7C3AED, #8b5cf6);
}

.settings-item--select-all:hover {
    background: #333333 !important;
    border-color: #8b5cf6 !important;
}

.settings-item--select-all .settings-item__label {
    color: #8b5cf6 !important;
    font-weight: 700 !important;
}

.settings-item--select-all .settings-item__description {
    color: #a855f7 !important;
} 

/* Profiles Section */
.profiles-container {
    padding: 10px;
}

.profile-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #1a1a1a;
    border-radius: 8px;
    margin-bottom: 10px;
    border: 1px solid #2a2a2a;
}

.profile-item:hover {
    background: #262626;
    border-color: #333333;
}

.profile-name-input {
    flex-grow: 1;
    background: #262626;
    border: 1px solid #333333;
    color: #e5e5e5;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    transition: all 0.2s ease;
}

.profile-name-input:focus {
    outline: none;
    border-color: #7c3aed;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.profile-item .settings-button {
    min-width: 80px;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    height: auto;
}

.profile-save-btn {
    background: #1a1a1a !important;
    color: #e5e5e5 !important;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.profile-save-btn:hover {
    background: #262626 !important;
    border-color: #10b981 !important;
}

.profile-load-btn {
    background: #1a1a1a !important;
    color: #e5e5e5 !important;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.profile-load-btn:hover {
    background: #262626 !important;
    border-color: #4f46e5 !important;
}

.profile-delete-btn {
    background: #1a1a1a !important;
    color: #e5e5e5 !important;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.profile-delete-btn:hover {
    background: #262626 !important;
    border-color: #dc2626 !important;
}

#add-profile-btn {
    width: 100%;
    padding: 10px;
    background: #1a1a1a;
    color: #7c3aed;
    border: 2px solid #7c3aed;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 10px;
    font-weight: 600;
    transition: all 0.2s ease;
}

#add-profile-btn:hover {
    background: #262626;
    border-color: #8b5cf6;
    color: #8b5cf6;
}

/* Video Speed Controller Accordion */
.video-speed-accordion {
    margin-bottom: 8px;
    background: #1a1a1a;
    border-radius: 8px;
    border: 1px solid #2a2a2a;
    transition: all 0.2s ease;
}

.video-speed-header {
    padding: 12px;
    cursor: pointer;
    background: #1a1a1a;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
    transition: all 0.2s ease;
}

.video-speed-header:hover {
    background: #262626;
}

.video-speed-header.expanded {
    border-radius: 8px 8px 0 0;
    background: #262626;
}

.video-speed-title {
    font-size: 14px;
    font-weight: 500;
    color: #e5e5e5;
    display: flex;
    align-items: center;
    gap: 8px;
}

.video-speed-icon {
    color: #9ca3af;
    transition: transform 0.2s ease;
    font-size: 12px;
}

.video-speed-icon.expanded {
    transform: rotate(180deg);
}

.video-speed-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.video-speed-content.expanded {
    max-height: 1000px;
}

.video-speed-body {
    padding: 16px;
    background: #0a0a0a;
    border-radius: 0 0 8px 8px;
}

.video-speed-input-group {
    margin-bottom: 16px;
}

.video-speed-input-group:last-child {
    margin-bottom: 0;
}

.video-speed-input-label {
    display: block;
    margin-bottom: 8px;
    color: #d1d5db;
    font-size: 13px;
    font-weight: 500;
}

.video-speed-checkbox-label {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    cursor: pointer;
    color: #9ca3af;
    font-size: 13px;
}

.video-speed-checkbox-label:hover {
    color: #d1d5db;
}

.video-speed-checkbox-label input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.video-speed-checkbox-text {
    user-select: none;
}

.video-speed-range {
    width: 200px;
    height: 6px;
    background: #2a2a2a;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    margin-right: 12px;
    vertical-align: middle;
}

.video-speed-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: #7c3aed;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.video-speed-range::-webkit-slider-thumb:hover {
    background: #8b5cf6;
    transform: scale(1.1);
}

.video-speed-value {
    color: #7c3aed;
    font-weight: 600;
    font-size: 14px;
}

.video-speed-shortcuts {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 8px;
}

.video-speed-shortcut {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 6px;
}

.shortcut-label {
    color: #9ca3af;
    font-size: 12px;
}

.shortcut-key {
    background: #7c3aed;
    color: #fff;
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 12px;
    font-family: monospace;
}

.video-speed-textarea {
    width: 100%;
    min-height: 80px;
    padding: 10px;
    background: #1a1a1a;
    color: #e5e5e5;
    border: 1px solid #2a2a2a;
    border-radius: 6px;
    font-family: monospace;
    font-size: 12px;
    resize: vertical;
    transition: all 0.2s ease;
}

.video-speed-textarea:focus {
    outline: none;
    border-color: #7c3aed;
    background: #262626;
}

.video-speed-textarea::placeholder {
    color: #6b7280;
    opacity: 0.8;
}

.video-speed-help {
    margin-top: 6px;
    font-size: 11px;
    color: #6b7280;
    line-height: 1.4;
}

/* Screenshot Tool Accordion */
.screenshot-tool-accordion {
    margin-bottom: 8px;
    background: #1a1a1a;
    border-radius: 8px;
    border: 1px solid #2a2a2a;
    transition: all 0.2s ease;
}

.screenshot-tool-header {
    padding: 12px;
    cursor: pointer;
    background: #1a1a1a;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
    transition: all 0.2s ease;
}

.screenshot-tool-header:hover {
    background: #262626;
}

.screenshot-tool-header.expanded {
    border-radius: 8px 8px 0 0;
    background: #262626;
}

.screenshot-tool-title {
    font-weight: 500;
    font-size: 13px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #e5e5e5;
}

.screenshot-tool-icon {
    transition: transform 0.2s ease;
    color: #9ca3af;
    font-size: 12px;
    font-weight: bold;
}

.screenshot-tool-icon.expanded {
    transform: rotate(180deg);
}

.screenshot-tool-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #1a1a1a;
}

.screenshot-tool-content.expanded {
    max-height: 400px;
}

.screenshot-tool-body {
    padding: 12px;
    border-top: 1px solid #2a2a2a;
}

.screenshot-tool-input-group {
    margin-bottom: 16px;
}

.screenshot-tool-input-group:last-child {
    margin-bottom: 0;
}

.screenshot-tool-input-label {
    display: block;
    font-size: 11px;
    font-weight: 600;
    color: #9ca3af;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.screenshot-tool-select {
    width: 100%;
    padding: 8px 12px;
    background: #262626;
    border: 1px solid #333333;
    border-radius: 6px;
    color: #e5e5e5;
    font-size: 13px;
    transition: all 0.2s ease;
}

.screenshot-tool-select:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.screenshot-tool-color-picker {
    display: flex;
    gap: 8px;
    align-items: center;
}

.screenshot-tool-color-swatch {
    width: 32px;
    height: 32px;
    border: 1px solid #333333;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.screenshot-tool-color-swatch:hover {
    border-color: #7c3aed;
    transform: scale(1.05);
}

.screenshot-tool-color-hex {
    flex: 1;
    padding: 8px 12px;
    background: #262626;
    border: 1px solid #333333;
    border-radius: 6px;
    color: #e5e5e5;
    font-size: 13px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    transition: all 0.2s ease;
}

.screenshot-tool-color-hex:focus {
    outline: none;
    border-color: #7c3aed;
    background: #2a2a2a;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.screenshot-tool-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.screenshot-tool-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #e5e5e5;
    font-size: 13px;
}

.screenshot-tool-checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #7c3aed;
    cursor: pointer;
}

.screenshot-tool-checkbox-text {
    user-select: none;
}

/* Onboarding Section Styling */
.onboarding-section {
    margin: 20px 0;
    padding: 16px;
    background: rgba(124, 58, 237, 0.05);
    border: 1px solid rgba(124, 58, 237, 0.2);
    border-radius: 8px;
}

.settings-button--onboarding {
    background: #7C3AED !important;
    border: 1px solid #7C3AED !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    width: auto !important;
    min-width: 180px !important;
    padding: 10px 20px !important;
}

.settings-button--onboarding:hover {
    background: #6d28d9 !important;
    border-color: #6d28d9 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
}

.settings-button--onboarding:active {
    transform: translateY(0);
}

.settings-item__description--small {
    font-size: 12px !important;
    margin-top: 8px !important;
    color: #9ca3af !important;
} 