<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Screenshot YouTube Options</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
            background: #0a0a0a;
            color: #d1d5db;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .options-container {
            max-width: 500px;
            margin: 0 auto;
        }
        
        h1 {
            color: #7C3AED;
            margin: 0 0 20px 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .option-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
        }
        
        .option-section h2 {
            color: #7C3AED;
            margin: 0 0 15px 0;
            font-size: 18px;
            font-weight: 500;
        }
        
        .option-group {
            margin-bottom: 15px;
        }
        
        .option-group:last-child {
            margin-bottom: 0;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #d1d5db;
            font-weight: 500;
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .checkbox-label:hover {
            background: #2a2a2a;
        }
        
        .checkbox-label input[type="checkbox"] {
            margin-right: 10px;
            width: 16px;
            height: 16px;
            accent-color: #7C3AED;
        }
        
        .radio-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
            margin-bottom: 5px;
        }
        
        .radio-label:hover {
            background: #2a2a2a;
        }
        
        .radio-label input[type="radio"] {
            margin-right: 10px;
            width: 16px;
            height: 16px;
            accent-color: #7C3AED;
        }
        
        select {
            width: 100%;
            padding: 8px 12px;
            background: #2a2a2a;
            border: 1px solid #555;
            border-radius: 4px;
            color: #d1d5db;
            font-size: 14px;
        }
        
        select:focus {
            outline: none;
            border-color: #7C3AED;
            box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
        }
        
        .keyboard-shortcuts {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 6px;
            margin-top: 10px;
        }
        
        .keyboard-shortcuts h3 {
            color: #7C3AED;
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        
        .shortcut-list {
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 8px;
            align-items: center;
        }
        
        .shortcut-key {
            background: #333;
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            text-align: center;
            min-width: 24px;
        }
        
        .shortcut-desc {
            color: #d1d5db;
            font-size: 13px;
        }
        
        .buttons-container {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #333;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #7C3AED;
            color: white;
        }
        
        .btn-primary:hover {
            background: #6D28D9;
        }
        
        .btn-secondary {
            background: #4B5563;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #374151;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="options-container">
        <h1>Screenshot YouTube Options</h1>
        
        <div class="option-section">
            <h2>Screenshot Settings</h2>
            
            <div class="option-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="screenshotKeyEnabled" data-setting="screenshotKeyEnabled">
                    <span>Enable 'P' keyboard shortcut to take screenshot</span>
                </label>
            </div>
            
            
            <div class="option-group">
                <label>Save to file or clipboard:</label>
                <div class="radio-group">
                    <label class="radio-label">
                        <input type="radio" name="screenshotFunctionality" value="0" data-setting="screenshotFunctionality">
                        <span>Save to file</span>
                    </label>
                    <label class="radio-label">
                        <input type="radio" name="screenshotFunctionality" value="1" data-setting="screenshotFunctionality">
                        <span>Copy to clipboard</span>
                    </label>
                    <label class="radio-label">
                        <input type="radio" name="screenshotFunctionality" value="2" data-setting="screenshotFunctionality">
                        <span>Both</span>
                    </label>
                </div>
            </div>
        </div>
        
        <div class="buttons-container">
            <button type="button" class="btn-secondary" id="cancelBtn">Cancel</button>
            <button type="button" class="btn-primary" id="saveBtn">Save</button>
        </div>
    </div>
    
    <script src="screenshot-youtube-options.js"></script>
</body>
</html>