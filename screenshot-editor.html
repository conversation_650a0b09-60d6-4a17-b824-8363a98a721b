<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>STM Screenshot Editor</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #0a0a0a;
            color: #d1d5db;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto';
            overflow: hidden;
        }
        
        .editor-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        
        .toolbar {
            background: #1a1a1a;
            border-bottom: 2px solid #7C3AED;
            padding: 12px;
            display: flex;
            gap: 12px;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .tool-group {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .tool-btn {
            width: 40px;
            height: 40px;
            background: #2a2a2a;
            border: 1px solid #7C3AED;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e5e5e5;
            transition: all 0.2s;
        }
        
        .tool-btn:hover,
        .tool-btn.active {
            background: #7C3AED;
            box-shadow: 0 0 8px rgba(124, 58, 237, 0.5);
        }
        
        .color-input {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .stroke-input {
            width: 80px;
            background: #2a2a2a;
            border: 1px solid #7C3AED;
            border-radius: 4px;
            padding: 4px 8px;
            color: #e5e5e5;
        }
        
        .width-select {
            width: 80px;
            background: #2a2a2a;
            border: 1px solid #7C3AED;
            border-radius: 4px;
            padding: 4px 8px;
            color: #e5e5e5;
            cursor: pointer;
        }
        
        .quality-input {
            width: 80px;
            background: #2a2a2a;
            border: 1px solid #7C3AED;
            border-radius: 4px;
            padding: 4px 8px;
            color: #e5e5e5;
        }
        
        .setting-label {
            font-size: 12px;
            color: #a0a0a0;
            margin-bottom: 2px;
        }
        
        .setting-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
        }
        
        .action-btn {
            padding: 8px 16px;
            background: #2a2a2a;
            border: 1px solid #7C3AED;
            border-radius: 6px;
            color: #e5e5e5;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .action-btn:hover {
            background: #7C3AED;
        }
        
        .canvas-container {
            flex: 1;
            position: relative;
            overflow: auto;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #screenshot-canvas {
            display: block;
            cursor: crosshair;
            max-width: 100%;
            max-height: 100%;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 18px;
        }
        
        .separator {
            width: 1px;
            height: 30px;
            background: #7C3AED;
            margin: 0 8px;
        }
        
        .stroke-controls {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-left: 8px;
        }
        
        .stroke-btn {
            width: 24px;
            height: 24px;
            background: #2a2a2a;
            border: 1px solid #7C3AED;
            border-radius: 4px;
            color: #e5e5e5;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.2s;
        }
        
        .stroke-btn:hover {
            background: #7C3AED;
            box-shadow: 0 0 4px rgba(124, 58, 237, 0.5);
        }
        
        .stroke-value {
            min-width: 24px;
            text-align: center;
            font-size: 12px;
            color: #a0a0a0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <div class="toolbar">
            <div class="tool-group">
                <div class="tool-btn active" data-tool="arrow" title="Arrow Tool (A)">↗</div>
                <div class="tool-btn" data-tool="line" title="Line Tool (L)">—</div>
                <div class="tool-btn" data-tool="text" title="Text Tool (T)">T</div>
                <div class="tool-btn" data-tool="box" title="Rectangle Tool (B)">□</div>
                <div class="tool-btn" data-tool="blur" title="Blur Tool (R)">⊙</div>
                <div class="tool-btn" data-tool="pixelate" title="Pixelate Tool (P)">▦</div>
            </div>
            
            <div class="separator"></div>
            
            <div class="tool-group">
                <input type="color" class="color-input" id="color-picker" value="#dc2626" title="Color">
                <input type="range" class="stroke-input" id="stroke-width" min="1" max="200" value="8" title="Stroke Width">
                <div class="stroke-controls">
                    <button class="stroke-btn" id="decrease-stroke" title="Decrease Stroke/Blur (-)" type="button">−</button>
                    <span class="stroke-value" id="stroke-value">8</span>
                    <button class="stroke-btn" id="increase-stroke" title="Increase Stroke/Blur (+)" type="button">+</button>
                </div>
            </div>
            
            <div class="separator"></div>
            
            <div class="tool-group">
                <div class="setting-group">
                    <div class="setting-label">Max Width</div>
                    <select class="width-select" id="width-select" title="Maximum Canvas Width">
                        <option value="480">480px</option>
                        <option value="720">720px</option>
                        <option value="1080">1080px</option>
                        <option value="1440">1440px</option>
                        <option value="1920" selected>1920px</option>
                        <option value="2160">2160px</option>
                    </select>
                </div>
            </div>
            
            <div class="separator"></div>
            
            <div class="tool-group">
                <div class="action-btn" id="undo-btn" title="Undo (Ctrl+Z)">Undo</div>
                <div class="action-btn" id="copy-btn" title="Copy to Clipboard (Ctrl+C)">Copy</div>
                <div class="action-btn" id="save-btn" title="Save Screenshot (Ctrl+S)">Save</div>
                <div class="action-btn" id="close-btn" title="Close Editor (Esc)">Close</div>
            </div>
        </div>
        
        <div class="canvas-container">
            <div class="loading" id="loading">Loading screenshot...</div>
            <canvas id="screenshot-canvas" style="display: none;"></canvas>
        </div>
    </div>
    
    <script src="js/screenshot-editor.js"></script>
</body>
</html>