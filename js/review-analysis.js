// Review Analysis Module for GMB Data Extractor
// This module handles recursive extraction of review data from Google Maps business listings

class ReviewAnalyzer {
    constructor() {
        this.isRunning = false;
        this.currentIndex = 0;
        this.reviewData = [];
        this.totalListings = 0;
        this.delay = 1500; // 1.5 seconds delay between clicks
        this.maxRetries = 3;
        this.retryCount = 0;
    }

    // Enhanced sponsored detection using tracked domains system approach
    isSponsoredResult(element) {
        if (!element) return false;
        
        // Method 1: Check for sponsored text in aria-label
        const ariaLabel = element.getAttribute('aria-label') || '';
        if (/sponsored|ad|advertisement/i.test(ariaLabel)) {
            return true;
        }
        
        // Method 2: Check for sponsored URLs
        const links = element.querySelectorAll('a[href]');
        for (const link of links) {
            const href = link.getAttribute('href') || '';
            if (href.includes('googleadservices.com') || 
                href.includes('google.com/aclk') || 
                href.includes('google.com/pagead')) {
                return true;
            }
        }
        
        // Method 3: Check for sponsored text in element content
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        let textNode;
        while (textNode = walker.nextNode()) {
            const text = textNode.textContent.trim();
            if (/\b(sponsored|ad|advertisement)\b/i.test(text)) {
                return true;
            }
        }
        
        return false;
    }

    // Count sponsored results in first 4 positions for accurate organic positioning
    countSponsoredInTopFour(elements) {
        let sponsoredCount = 0;
        const top4Elements = elements.slice(0, 4);
        
        for (const element of top4Elements) {
            if (this.isSponsoredResult(element)) {
                sponsoredCount++;
            }
        }
        
        console.log(`📊 REVIEW ANALYSIS: Found ${sponsoredCount} sponsored results in top 4 positions`);
        return sponsoredCount;
    }

    // Main function to start review analysis
    async startAnalysis() {
        console.log('🔍 REVIEW ANALYSIS: Starting analysis...');
        
        if (this.isRunning) {
            console.log('⚠️ REVIEW ANALYSIS: Analysis is already running');
            return;
        }

        this.isRunning = true;
        this.currentIndex = 0;
        this.reviewData = [];
        this.retryCount = 0;

        console.log('📊 REVIEW ANALYSIS: Initialized state', {
            isRunning: this.isRunning,
            currentIndex: this.currentIndex,
            reviewDataLength: this.reviewData.length,
            delay: this.delay
        });

        try {
            // Find all business listings on the page
            console.log('🔎 REVIEW ANALYSIS: Looking for business listings...');
            const listings = this.findBusinessListings();
            
            if (listings.length === 0) {
                console.error('❌ REVIEW ANALYSIS: No business listings found');
                throw new Error('No business listings found on the page');
            }

            this.totalListings = listings.length;
            console.log(`✅ REVIEW ANALYSIS: Found ${this.totalListings} business listings to analyze`);

            // Log details about found listings
            listings.forEach((listing, index) => {
                const link = listing.querySelector('a.hfpxzc');
                const ariaLabel = link ? link.getAttribute('aria-label') : 'No link found';
                console.log(`📋 REVIEW ANALYSIS: Listing ${index + 1}: ${ariaLabel}`);
            });

            // Send initial status to popup
            console.log('📤 REVIEW ANALYSIS: Sending initial status to popup');
            this.sendStatusUpdate('started', {
                total: this.totalListings,
                current: 0,
                data: []
            });

            // Extract all review data directly from search results page
            console.log('🔄 REVIEW ANALYSIS: Extracting all review data from search results page...');
            await this.extractAllReviewDataFromPage(listings);
            
            // Create aggregate summary
            const aggregateData = this.createAggregateData();
            console.log('📊 REVIEW ANALYSIS: Aggregate data:', aggregateData);

            // Analysis complete
            console.log('🎉 REVIEW ANALYSIS: Analysis completed successfully!');
            this.sendStatusUpdate('completed', {
                total: this.totalListings,
                current: this.totalListings,
                data: this.reviewData
            });

        } catch (error) {
            console.error('💥 REVIEW ANALYSIS: Error during analysis:', error);
            console.error('💥 REVIEW ANALYSIS: Error stack:', error.stack);
            this.sendStatusUpdate('error', {
                error: error.message,
                data: this.reviewData
            });
        } finally {
            console.log('🏁 REVIEW ANALYSIS: Analysis finished, setting isRunning to false');
            this.isRunning = false;
        }
    }

    // Stop the analysis
    stopAnalysis() {
        this.isRunning = false;
        this.sendStatusUpdate('stopped', {
            total: this.totalListings,
            current: this.currentIndex,
            data: this.reviewData
        });
    }

    // Find all business listings on the page
    findBusinessListings() {
        console.log('🔍 REVIEW ANALYSIS: Looking for business listings...');
        console.log('🌐 REVIEW ANALYSIS: Current URL:', window.location.href);
        
        // Look for rating elements directly - this is where the review data actually is
        const ratingElements = document.querySelectorAll('[aria-label*=" stars "]');
        console.log(`⭐ REVIEW ANALYSIS: Found ${ratingElements.length} rating elements`);
        
        // Also look for business name links
        const businessLinks = document.querySelectorAll('a.hfpxzc');
        console.log(`🔗 REVIEW ANALYSIS: Found ${businessLinks.length} business links`);
        
        // Filter business links to only include those with business names (contain "·" in aria-label)
        const businessLinksWithDot = Array.from(businessLinks).filter(link => {
            const ariaLabel = link.getAttribute('aria-label');
            return ariaLabel && ariaLabel.includes('·');
        });
        
        console.log(`✅ REVIEW ANALYSIS: Found ${businessLinksWithDot.length} business links with aria-labels containing "·"`);
        
        // If we have rating elements, use those as the primary indicator
        if (ratingElements.length > 0) {
            console.log(`📊 REVIEW ANALYSIS: Using ${ratingElements.length} rating elements as primary data source`);
            
            // Log sample rating elements for debugging
            const ratingsToLog = Array.from(ratingElements).slice(0, 5);
            ratingsToLog.forEach((element, index) => {
                const ariaLabel = element.getAttribute('aria-label');
                console.log(`⭐ REVIEW ANALYSIS: Rating ${index + 1}: ${ariaLabel}`);
            });
            
            // Return rating elements as our "listings" since that's where the data is
            return Array.from(ratingElements);
        }
        
        // Fallback to business links if we have them
        if (businessLinksWithDot.length > 0) {
            console.log(`📊 REVIEW ANALYSIS: Using ${businessLinksWithDot.length} business links as fallback`);
            return businessLinksWithDot;
        }
        
        // If no specific business links found, try all business links
        if (businessLinks.length > 0) {
            console.log(`📊 REVIEW ANALYSIS: Using all ${businessLinks.length} business links as last resort`);
            
            // Log sample links for debugging
            const linksToLog = Array.from(businessLinks).slice(0, 5);
            console.log(`🔍 REVIEW ANALYSIS: Sample a.hfpxzc links found:`, 
                linksToLog.map(link => ({
                    ariaLabel: link.getAttribute('aria-label'),
                    href: link.href?.substring(0, 100),
                    textContent: link.textContent?.substring(0, 50)
                }))
            );
            
            return Array.from(businessLinks);
        }
        
        console.log('❌ REVIEW ANALYSIS: No business listings or rating elements found');
        return [];
    }

    // Process a single business listing
    async processListing(link, index) {
        console.log(`🎯 REVIEW ANALYSIS: Starting to process listing ${index + 1}`);
        console.log(`📋 REVIEW ANALYSIS: Link element:`, {
            tagName: link.tagName,
            className: link.className,
            href: link.href
        });
        
        try {
            const ariaLabel = link.getAttribute('aria-label');
            const businessName = this.extractBusinessName(ariaLabel);
            
            console.log(`📝 REVIEW ANALYSIS: Processing listing ${index + 1}/${this.totalListings}: ${businessName}`);
            console.log(`🔗 REVIEW ANALYSIS: Link details:`, {
                href: link.href,
                ariaLabel: ariaLabel,
                className: link.className,
                textContent: link.textContent?.substring(0, 100)
            });

            // Click the listing to open the business details
            console.log(`👆 REVIEW ANALYSIS: Clicking link for ${businessName}`);
            
            // Scroll the element into view first
            link.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await this.wait(500); // Wait for scroll to complete
            
            // Try different click methods
            try {
                link.click();
                console.log(`✅ REVIEW ANALYSIS: Successfully clicked link for ${businessName}`);
            } catch (clickError) {
                console.warn(`⚠️ REVIEW ANALYSIS: Regular click failed, trying alternative methods:`, clickError);
                
                // Try dispatching a click event
                const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                link.dispatchEvent(clickEvent);
                console.log(`✅ REVIEW ANALYSIS: Dispatched click event for ${businessName}`);
            }

            // Wait for the details panel to load
            console.log(`⏱️ REVIEW ANALYSIS: Waiting ${this.delay}ms for panel to load...`);
            await this.wait(this.delay);

            // Extract review data from the opened panel
            console.log(`📊 REVIEW ANALYSIS: Extracting review data for ${businessName}`);
            const reviewInfo = await this.extractReviewData();

            // Store the data
            const listingData = {
                index: index + 1,
                businessName: businessName,
                ...reviewInfo,
                timestamp: new Date().toISOString()
            };

            this.reviewData.push(listingData);
            console.log(`✅ REVIEW ANALYSIS: Extracted data for ${businessName}:`, listingData);

        } catch (error) {
            console.error(`💥 REVIEW ANALYSIS: Error processing listing ${index + 1}:`, error);
            console.error(`💥 REVIEW ANALYSIS: Error stack:`, error.stack);
            
            // Retry logic
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                console.log(`🔄 REVIEW ANALYSIS: Retrying listing ${index + 1} (attempt ${this.retryCount}/${this.maxRetries})`);
                await this.wait(1000); // Wait 1 second before retry
                return this.processListing(link, index);
            } else {
                console.error(`❌ REVIEW ANALYSIS: Max retries reached for listing ${index + 1}`);
                // Add error entry to data
                this.reviewData.push({
                    index: index + 1,
                    businessName: 'Error extracting name',
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                this.retryCount = 0; // Reset retry count for next listing
            }
        }
    }

    // Extract business name from aria-label
    extractBusinessName(ariaLabel) {
        if (!ariaLabel) return 'Unknown Business';
        
        // Remove "·Visited link" or similar suffixes
        const cleanName = ariaLabel.replace(/·.*$/, '').trim();
        return cleanName || 'Unknown Business';
    }

    // Extract all review data from the current page without clicking
    async extractAllReviewDataFromPage(listings) {
        console.log('📊 REVIEW ANALYSIS: Extracting all review data from current page...');
        
        // Check if listings are rating elements or business links
        const firstListing = listings[0];
        const isRatingElement = firstListing && firstListing.getAttribute('aria-label') && firstListing.getAttribute('aria-label').includes('stars');
        
        console.log(`🔍 REVIEW ANALYSIS: Listings type: ${isRatingElement ? 'Rating Elements' : 'Business Links'}`);
        
        let ratingElements;
        let businessLinks = [];
        
        if (isRatingElement) {
            // We already have rating elements
            ratingElements = listings;
            console.log(`⭐ REVIEW ANALYSIS: Using ${ratingElements.length} rating elements directly`);
            
            // Also get business links for names
            businessLinks = Array.from(document.querySelectorAll('a.hfpxzc'));
            console.log(`🔗 REVIEW ANALYSIS: Found ${businessLinks.length} business links for names`);
        } else {
            // We have business links, need to find rating elements
            businessLinks = listings;
            ratingElements = document.querySelectorAll('[aria-label*=" stars "]');
            console.log(`⭐ REVIEW ANALYSIS: Found ${ratingElements.length} rating elements`);
        }
        
        // Get all business containers for sponsored detection
        const businessContainers = Array.from(document.querySelectorAll('.g, [data-ved], .VkpGBb, .commercial-unit-desktop-top'));
        console.log(`🔍 REVIEW ANALYSIS: Found ${businessContainers.length} business containers for sponsored detection`);
        
        // Count sponsored results in first 4 positions for position adjustment
        const sponsoredCount = this.countSponsoredInTopFour(businessContainers);
        
        // Extract data from rating elements
        const extractedData = [];
        
        Array.from(ratingElements).forEach((element, index) => {
            const ariaLabel = element.getAttribute('aria-label');
            console.log(`🔍 REVIEW ANALYSIS: Processing rating element ${index + 1}: "${ariaLabel}"`);
            
            // Match patterns like "4.4 stars 1,267 Reviews" or "4.4 stars 1267 Reviews"
            const match = ariaLabel.match(/^([\d.]+)\s+stars?\s+([\d,]+)\s+Reviews?$/i);
            
            if (match) {
                const rating = parseFloat(match[1]);
                const reviewCount = parseInt(match[2].replace(/,/g, ''));
                
                // Try to find the associated business name and container
                let businessName = `Business ${extractedData.length + 1}`;
                let businessContainer = null;
                let isSponsored = false;
                
                // Look for business name from business links
                if (extractedData.length < businessLinks.length) {
                    const businessLink = businessLinks[extractedData.length];
                    const linkAriaLabel = businessLink.getAttribute('aria-label');
                    businessName = this.extractBusinessName(linkAriaLabel);
                    
                    // Find the business container that contains this link
                    businessContainer = businessLink.closest('.g') || 
                                      businessLink.closest('[data-ved]') || 
                                      businessLink.closest('.VkpGBb') || 
                                      businessLink.closest('.commercial-unit-desktop-top');
                }
                
                // If no container found via link, try to find container by index
                if (!businessContainer && index < businessContainers.length) {
                    businessContainer = businessContainers[index];
                }
                
                // Check if this business is sponsored
                if (businessContainer) {
                    isSponsored = this.isSponsoredResult(businessContainer);
                    console.log(`🔍 REVIEW ANALYSIS: Business "${businessName}" sponsored status: ${isSponsored}`);
                }
                
                const businessData = {
                    index: extractedData.length + 1,
                    businessName: businessName,
                    rating: rating,
                    reviewCount: reviewCount,
                    found: true,
                    isSponsored: isSponsored,
                    timestamp: new Date().toISOString()
                };
                
                extractedData.push(businessData);
                console.log(`✅ REVIEW ANALYSIS: Extracted data for ${businessName}: Rating ${rating}, Reviews ${reviewCount}, Sponsored: ${isSponsored}`);
                
                // Send progress update
                this.reviewData = extractedData;
                this.sendStatusUpdate('progress', {
                    total: Math.max(ratingElements.length, businessLinks.length),
                    current: extractedData.length,
                    data: this.reviewData
                });
            }
        });
        
        console.log(`📊 REVIEW ANALYSIS: Successfully extracted data for ${extractedData.length} businesses (${sponsoredCount} sponsored excluded from top calculations)`);
        this.reviewData = extractedData;
    }

    // Create aggregate data summary
    createAggregateData() {
        if (this.reviewData.length === 0) {
            return null;
        }
        
        // Filter out sponsored results for accurate organic analysis
        const organicData = this.reviewData.filter(item => !item.isSponsored);
        const validData = organicData.filter(item => item.rating && item.reviewCount);
        
        if (validData.length === 0) {
            return null;
        }
        
        console.log(`📊 REVIEW ANALYSIS: Organic data: ${organicData.length} businesses (${this.reviewData.length - organicData.length} sponsored excluded)`);
        
        const totalReviews = validData.reduce((sum, item) => sum + item.reviewCount, 0);
        const averageRating = validData.reduce((sum, item) => sum + item.rating, 0) / validData.length;
        
        // Only consider top 3 ORGANIC businesses for highest rated (by position after sponsored removal)
        const top3OrganicBusinesses = validData.slice(0, 3);
        const highestRated = top3OrganicBusinesses.reduce((max, item) => {
            // If ratings are equal, choose the one with higher position (earlier in the array)
            if (item.rating > max.rating) {
                return item;
            } else if (item.rating === max.rating) {
                // If tie, choose the one that appears first (already in order)
                return max;
            }
            return max;
        }, top3OrganicBusinesses[0]);
        
        const mostReviews = validData.reduce((max, item) => item.reviewCount > max.reviewCount ? item : max);
        
        // Calculate average review count of top 3 ORGANIC listings (by position, not review count)
        const top3OrganicListings = validData.slice(0, 3); // Take first 3 organic businesses in order
        const averageReviewCountTop3 = top3OrganicListings.length > 0 
            ? Math.round(top3OrganicListings.reduce((sum, item) => sum + item.reviewCount, 0) / top3OrganicListings.length)
            : 0;
        
        console.log('📊 REVIEW ANALYSIS: Top 3 ORGANIC listings by position:', top3OrganicListings.map(c => ({
            name: c.businessName,
            reviewCount: c.reviewCount,
            index: c.index,
            isSponsored: c.isSponsored
        })));
        console.log('📊 REVIEW ANALYSIS: Average review count of top 3 organic:', averageReviewCountTop3);
        
        // Create rating distribution using ORGANIC data only
        const ratingDistribution = {};
        organicData.forEach(business => {
            let ratingRange;
            
            if (!business.rating || business.rating === 0) {
                ratingRange = '0'; // No rating
            } else if (business.rating >= 5.0) {
                ratingRange = '5'; // Exactly 5.0 stars
            } else if (business.rating >= 4.0) {
                ratingRange = '4'; // 4.0-4.9 stars
            } else if (business.rating >= 3.0) {
                ratingRange = '3'; // 3.0-3.9 stars
            } else if (business.rating >= 2.0) {
                ratingRange = '2'; // 2.0-2.9 stars
            } else if (business.rating >= 1.0) {
                ratingRange = '1'; // 1.0-1.9 stars
            } else {
                ratingRange = '0'; // No rating or below 1.0
            }
            
            ratingDistribution[ratingRange] = (ratingDistribution[ratingRange] || 0) + 1;
        });
        
        // Category breakdown (if available from business data) - ORGANIC only
        const categoryBreakdown = {};
        organicData.forEach(business => {
            if (business.category && business.category.toLowerCase() !== 'favourites') {
                categoryBreakdown[business.category] = (categoryBreakdown[business.category] || 0) + 1;
            }
        });
        
        const aggregate = {
            totalBusinesses: organicData.length, // Organic businesses only
            totalReviews: totalReviews,
            averageRating: Math.round(averageRating * 10) / 10,
            averageReviewCountTop3: averageReviewCountTop3,
            top3Listings: top3OrganicListings.map(c => ({
                name: c.businessName,
                reviewCount: c.reviewCount,
                index: c.index
            })),
            highestRated: {
                name: highestRated.businessName,
                rating: highestRated.rating,
                reviewCount: highestRated.reviewCount || 0
            },
            mostReviews: {
                name: mostReviews.businessName,
                count: mostReviews.reviewCount
            },
            ratingDistribution: ratingDistribution,
            categoryBreakdown: categoryBreakdown,
            sponsoredExcluded: this.reviewData.length - organicData.length // Track how many sponsored were excluded
        };
        
        // Add aggregate to review data for export
        this.reviewData.unshift({
            index: 0,
            businessName: '📊 AGGREGATE SUMMARY (ORGANIC ONLY)',
            rating: aggregate.averageRating,
            reviewCount: aggregate.totalReviews,
            found: true,
            timestamp: new Date().toISOString(),
            isAggregate: true,
            isSponsored: false,
            aggregateData: aggregate
        });
        
        return aggregate;
    }

    // Extract review data from the currently opened business panel
    async extractReviewData() {
        const maxWaitTime = 5000; // 5 seconds max wait
        const checkInterval = 100; // Check every 100ms
        let waitTime = 0;

        // Wait for review elements to load
        while (waitTime < maxWaitTime) {
            const reviewData = this.tryExtractReviewData();
            if (reviewData.found) {
                return reviewData;
            }
            await this.wait(checkInterval);
            waitTime += checkInterval;
        }

        // If we couldn't find review data, return default values
        console.warn('Could not find review data elements');
        return {
            rating: null,
            reviewCount: null,
            found: false,
            error: 'Review elements not found'
        };
    }

    // Try to extract review data from current page
    tryExtractReviewData() {
        console.log('🔍 REVIEW ANALYSIS: Attempting to extract review data...');
        
        try {
            // Look for rating element with aria-label="N stars"
            console.log('⭐ REVIEW ANALYSIS: Looking for rating elements...');
            const ratingElements = document.querySelectorAll('[aria-label*=" stars"]');
            console.log(`⭐ REVIEW ANALYSIS: Found ${ratingElements.length} elements with "stars" in aria-label`);
            
            let rating = null;
            
            for (const element of ratingElements) {
                const ariaLabel = element.getAttribute('aria-label');
                console.log(`⭐ REVIEW ANALYSIS: Checking rating element with aria-label: "${ariaLabel}"`);
                const match = ariaLabel.match(/^([\d.]+)\s+stars?$/);
                if (match) {
                    rating = parseFloat(match[1]);
                    console.log(`✅ REVIEW ANALYSIS: Found rating: ${rating}`);
                    break;
                }
            }

            if (rating === null) {
                console.log('⚠️ REVIEW ANALYSIS: No rating found with exact pattern, logging all star elements:');
                ratingElements.forEach((el, index) => {
                    console.log(`⭐ REVIEW ANALYSIS: Star element ${index + 1}:`, {
                        ariaLabel: el.getAttribute('aria-label'),
                        textContent: el.textContent,
                        className: el.className
                    });
                });
            }

            // Look for review count element with aria-label="N reviews"
            console.log('📝 REVIEW ANALYSIS: Looking for review count elements...');
            const reviewElements = document.querySelectorAll('[aria-label*=" reviews"]');
            console.log(`📝 REVIEW ANALYSIS: Found ${reviewElements.length} elements with "reviews" in aria-label`);
            
            let reviewCount = null;
            
            for (const element of reviewElements) {
                const ariaLabel = element.getAttribute('aria-label');
                console.log(`📝 REVIEW ANALYSIS: Checking review element with aria-label: "${ariaLabel}"`);
                const match = ariaLabel.match(/^([\d,]+)\s+reviews?$/);
                if (match) {
                    // Remove commas and convert to number
                    reviewCount = parseInt(match[1].replace(/,/g, ''));
                    console.log(`✅ REVIEW ANALYSIS: Found review count: ${reviewCount}`);
                    break;
                }
            }

            if (reviewCount === null) {
                console.log('⚠️ REVIEW ANALYSIS: No review count found with exact pattern, logging all review elements:');
                reviewElements.forEach((el, index) => {
                    console.log(`📝 REVIEW ANALYSIS: Review element ${index + 1}:`, {
                        ariaLabel: el.getAttribute('aria-label'),
                        textContent: el.textContent,
                        className: el.className
                    });
                });
            }

            // Also try to get review count from span text content like "(1,267)"
            if (reviewCount === null) {
                console.log('🔍 REVIEW ANALYSIS: Looking for review count in span text content...');
                const reviewSpans = document.querySelectorAll('span');
                console.log(`🔍 REVIEW ANALYSIS: Found ${reviewSpans.length} span elements to check`);
                
                for (const span of reviewSpans) {
                    const text = span.textContent.trim();
                    const match = text.match(/^\(([\d,]+)\)$/);
                    if (match) {
                        reviewCount = parseInt(match[1].replace(/,/g, ''));
                        console.log(`✅ REVIEW ANALYSIS: Found review count in span: ${reviewCount} (text: "${text}")`);
                        break;
                    }
                }
                
                if (reviewCount === null) {
                    console.log('⚠️ REVIEW ANALYSIS: No review count found in spans, logging sample spans:');
                    Array.from(reviewSpans).slice(0, 10).forEach((span, index) => {
                        const text = span.textContent.trim();
                        if (text.includes('(') || text.includes(')') || /\d/.test(text)) {
                            console.log(`📝 REVIEW ANALYSIS: Sample span ${index + 1}: "${text}"`);
                        }
                    });
                }
            }

            const found = rating !== null || reviewCount !== null;
            
            const result = {
                rating: rating,
                reviewCount: reviewCount,
                found: found
            };
            
            console.log('📊 REVIEW ANALYSIS: Final extraction result:', result);
            return result;

        } catch (error) {
            console.error('💥 REVIEW ANALYSIS: Error extracting review data:', error);
            console.error('💥 REVIEW ANALYSIS: Error stack:', error.stack);
            return {
                rating: null,
                reviewCount: null,
                found: false,
                error: error.message
            };
        }
    }

    // Utility function to wait for a specified time
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Send status updates to the popup
    sendStatusUpdate(status, data) {
        // Send message to popup via chrome runtime
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.sendMessage({
                type: 'reviewAnalysisUpdate',
                status: status,
                data: data
            }).catch(error => {
                console.log('Could not send message to popup:', error);
            });
        }

        // Also dispatch custom event for content script communication
        window.dispatchEvent(new CustomEvent('reviewAnalysisUpdate', {
            detail: { status, data }
        }));
    }

    // Export data as CSV
    exportToCSV() {
        if (this.reviewData.length === 0) {
            console.warn('No review data to export');
            return null;
        }

        const headers = ['Index', 'Business Name', 'Rating', 'Review Count', 'Timestamp', 'Error'];
        const csvContent = [
            headers.join(','),
            ...this.reviewData.map(item => [
                item.index,
                `"${item.businessName.replace(/"/g, '""')}"`, // Escape quotes in business name
                item.rating || '',
                item.reviewCount || '',
                item.timestamp,
                item.error ? `"${item.error.replace(/"/g, '""')}"` : ''
            ].join(','))
        ].join('\n');

        return csvContent;
    }

    // Get current analysis data
    getCurrentData() {
        return {
            isRunning: this.isRunning,
            currentIndex: this.currentIndex,
            totalListings: this.totalListings,
            reviewData: this.reviewData
        };
    }
}

// Create global instance
window.reviewAnalyzer = new ReviewAnalyzer();

// Listen for messages from popup
if (typeof chrome !== 'undefined' && chrome.runtime) {
    console.log('🔧 REVIEW ANALYSIS: Setting up message listener...');
    
    chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
        console.log('📨 REVIEW ANALYSIS: Received message:', request);
        
        // Handle both request.type and request.action for compatibility
        const messageType = request.type || request.action;
        console.log('📨 REVIEW ANALYSIS: Processing message type:', messageType);
        
        if (messageType === 'startReviewAnalysis') {
            console.log('🚀 REVIEW ANALYSIS: Starting analysis from popup request');
            window.reviewAnalyzer.startAnalysis();
            sendResponse({ success: true });
        } else if (messageType === 'stopReviewAnalysis') {
            console.log('⏹️ REVIEW ANALYSIS: Stopping analysis from popup request');
            window.reviewAnalyzer.stopAnalysis();
            sendResponse({ success: true });
        } else if (messageType === 'getReviewAnalysisData') {
            console.log('📊 REVIEW ANALYSIS: Getting current data from popup request');
            const data = window.reviewAnalyzer.getCurrentData();
            console.log('📊 REVIEW ANALYSIS: Sending data:', data);
            sendResponse(data);
        } else if (messageType === 'exportReviewData') {
            console.log('📤 REVIEW ANALYSIS: Exporting data from popup request');
            const csvData = window.reviewAnalyzer.exportToCSV();
            console.log('📤 REVIEW ANALYSIS: CSV data length:', csvData ? csvData.length : 'null');
            sendResponse({ csvData });
        } else {
            console.log('❓ REVIEW ANALYSIS: Unknown message type:', messageType);
        }
        
        return true; // Keep the message channel open for async responses
    });
    
    console.log('✅ REVIEW ANALYSIS: Message listener set up successfully');
} else {
    console.error('❌ REVIEW ANALYSIS: Chrome runtime not available');
}

console.log('🎉 REVIEW ANALYSIS: Module loaded successfully'); 