// Screenshot Selector - Handy Screenshot Approach (Selection UI Only)
class ScreenshotSelector {
    constructor() {
        this.isSelecting = false;
        this.startPos = null;
        this.currentPos = null;
        this.overlay = null;
        this.selectionBox = null;
        this.instructionElement = null;
        this.cleanupTimeout = null;
        
        this.init();
        
        // Failsafe cleanup after 30 seconds regardless of what happens
        this.cleanupTimeout = setTimeout(() => {
            console.log('Screenshot Selector: Failsafe cleanup triggered');
            this.cleanup();
        }, 30000);
    }
    
    init() {
        this.createOverlay();
        this.setupEventListeners();
    }
    
    createOverlay() {
        // Create full-screen overlay
        this.overlay = document.createElement('div');
        this.overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: transparent;
            z-index: 999999;
            cursor: crosshair;
            user-select: none;
        `;
        
        // Create selection box
        this.selectionBox = document.createElement('div');
        this.selectionBox.style.cssText = `
            position: absolute;
            border: 2px solid #7C3AED;
            background: transparent;
            box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.4);
            pointer-events: none;
            display: none;
        `;
        
        this.overlay.appendChild(this.selectionBox);
        document.body.appendChild(this.overlay);
        
        // Show instruction
        this.showInstruction();
    }
    
    showInstruction() {
        this.instructionElement = document.createElement('div');
        this.instructionElement.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #1a1a1a;
            color: #e5e5e5;
            padding: 12px 20px;
            border-radius: 8px;
            border: 2px solid #7C3AED;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto';
            font-size: 14px;
            z-index: 1000000;
        `;
        this.instructionElement.textContent = 'Drag to select area for screenshot (ESC to cancel)';
        
        document.body.appendChild(this.instructionElement);
    }
    
    setupEventListeners() {
        // Mouse events
        this.overlay.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.overlay.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.overlay.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        
        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.cancel();
            } else if (e.key === 'Enter' && this.isSelecting && this.startPos && this.currentPos) {
                e.preventDefault();
                e.stopPropagation();
                this.finalizeSelection();
            }
        });
        
        // Cleanup on tab visibility change or page unload
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                console.log('Screenshot Selector: Tab hidden, cleaning up');
                this.cleanup();
            }
        });
        
        window.addEventListener('beforeunload', () => {
            console.log('Screenshot Selector: Page unloading, cleaning up');
            this.cleanup();
        });
        
        // Note: 'unload' event listener removed due to browser permissions policy violations
    }
    
    handleMouseDown(e) {
        this.isSelecting = true;
        // Use pageX/pageY to get page-absolute coordinates (includes scroll)
        this.startPos = {
            x: e.pageX,
            y: e.pageY
        };
        this.currentPos = { ...this.startPos };
        
        this.selectionBox.style.display = 'block';
        this.updateSelectionBox();
    }
    
    handleMouseMove(e) {
        if (!this.isSelecting) return;
        
        // Use pageX/pageY to get page-absolute coordinates (includes scroll)
        this.currentPos = {
            x: e.pageX,
            y: e.pageY
        };
        
        this.updateSelectionBox();
    }
    
    handleMouseUp(e) {
        if (!this.isSelecting) return;
        this.finalizeSelection();
    }
    
    finalizeSelection() {
        if (!this.startPos || !this.currentPos) {
            console.log('Screenshot Selector: Invalid selection state, cancelling');
            this.cancel();
            return;
        }
        
        this.isSelecting = false;
        
        // Calculate selection area
        const selection = this.getSelectionArea();
        
        // Minimum selection size check
        if (selection.width < 10 || selection.height < 10) {
            this.cancel();
            return;
        }
        
        // HANDY SCREENSHOT APPROACH: Send to background script
        this.sendSelectionToBackground(selection);
    }
    
    updateSelectionBox() {
        if (!this.startPos || !this.currentPos) return;
        
        // Convert page coordinates to viewport coordinates for display
        const scrollX = window.scrollX || window.pageXOffset || 0;
        const scrollY = window.scrollY || window.pageYOffset || 0;
        
        const viewportStartX = this.startPos.x - scrollX;
        const viewportStartY = this.startPos.y - scrollY;
        const viewportCurrentX = this.currentPos.x - scrollX;
        const viewportCurrentY = this.currentPos.y - scrollY;
        
        const left = Math.min(viewportStartX, viewportCurrentX);
        const top = Math.min(viewportStartY, viewportCurrentY);
        const width = Math.abs(viewportCurrentX - viewportStartX);
        const height = Math.abs(viewportCurrentY - viewportStartY);
        
        this.selectionBox.style.left = left + 'px';
        this.selectionBox.style.top = top + 'px';
        this.selectionBox.style.width = width + 'px';
        this.selectionBox.style.height = height + 'px';
    }
    
    getSelectionArea() {
        const left = Math.min(this.startPos.x, this.currentPos.x);
        const top = Math.min(this.startPos.y, this.currentPos.y);
        const width = Math.abs(this.currentPos.x - this.startPos.x);
        const height = Math.abs(this.currentPos.y - this.startPos.y);
        
        return { left, top, width, height };
    }
    
    // HANDY SCREENSHOT APPROACH: Send coordinates to background script
    sendSelectionToBackground(selection) {
        try {
            console.log('Screenshot Selector: Sending selection to background script...');
            
            // Convert page coordinates to viewport coordinates for captureVisibleTab()
            const scrollX = window.scrollX || window.pageXOffset || 0;
            const scrollY = window.scrollY || window.pageYOffset || 0;
            
            const viewportCoords = {
                left: selection.left - scrollX,
                top: selection.top - scrollY,
                width: selection.width,
                height: selection.height
            };
            
            // Validate coordinates are within viewport bounds
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            
            if (viewportCoords.left < 0 || viewportCoords.top < 0 || 
                viewportCoords.left + viewportCoords.width > viewportWidth ||
                viewportCoords.top + viewportCoords.height > viewportHeight) {
                console.warn('Screenshot Selector: Selection extends outside viewport bounds');
            }
            
            // Debug logging for coordinate tracking
            console.log('Screenshot Selector: Coordinate transformation:', {
                pageCoords: selection,
                viewportCoords: viewportCoords,
                scrollPosition: { x: scrollX, y: scrollY },
                viewportSize: { width: viewportWidth, height: viewportHeight }
            });
            
            
            // Hide overlay immediately for clean capture
            this.overlay.style.display = 'none';
            
            // Cleanup immediately after hiding overlay - don't wait for background response
            this.cleanup();
            
            // Add delay to ensure browser processes DOM changes before screenshot capture
            setTimeout(() => {
                // CRITICAL: Check if chrome.runtime is still available before messaging
                if (!chrome || !chrome.runtime || !chrome.runtime.sendMessage) {
                    console.warn('Screenshot Selector: Extension context invalidated during screenshot capture');
                    this.showError('Extension context lost. Please reload the page and try again.');
                    return;
                }
                
                // Additional check: Test if extension context is valid
                try {
                    chrome.runtime.getURL(''); // This will throw if context is invalid
                } catch (contextError) {
                    console.warn('Screenshot Selector: Extension context validation failed:', contextError.message);
                    this.showError('Extension context lost. Please reload the page and try again.');
                    return;
                }
                
                // Send viewport coordinates to background script with retry logic
                this.sendMessageWithRetry({
                    action: "CAPTURE_SELECTION",
                    rect: viewportCoords,
                    devicePixelRatio: window.devicePixelRatio
                }, (success, result) => {
                    if (!success) {
                        this.showError(result || 'Screenshot capture failed');
                    }
                    // Success case is handled automatically by background script
                    // Cleanup already done above - no need to call again
                });
            }, 150); // 150ms delay to ensure DOM changes are processed
            
        } catch (error) {
            // Handle extension context invalidation (common during dev/updates)
            if (error.message.includes('Extension context invalidated')) {
                console.log('Screenshot Selector: Extension context invalidated - ignoring (feature works)');
                // Cleanup already done above
                return;
            }
            
            console.error('Screenshot Selector: Error sending to background:', error);
            this.showError('Error communicating with background script: ' + error.message);
            // Cleanup already done above - no need to call again
        }
    }
    
    // ROBUST MESSAGING: Send message with retry logic for service worker reliability
    sendMessageWithRetry(message, callback, maxRetries = 3, retryDelay = 100) {
        let attempts = 0;
        
        const attemptSend = () => {
            attempts++;
            
            // Check context validity before each attempt
            if (!chrome || !chrome.runtime || !chrome.runtime.sendMessage) {
                console.warn(`Screenshot Selector: Extension context invalid on attempt ${attempts}`);
                callback(false, 'Extension context lost. Please reload the page and try again.');
                return;
            }
            
            try {
                chrome.runtime.getURL(''); // Context validation test
            } catch (contextError) {
                console.warn(`Screenshot Selector: Context validation failed on attempt ${attempts}:`, contextError.message);
                callback(false, 'Extension context lost. Please reload the page and try again.');
                return;
            }
            
            // Send message with timeout
            const timeoutId = setTimeout(() => {
                console.warn(`Screenshot Selector: Message timeout on attempt ${attempts}`);
                if (attempts < maxRetries) {
                    console.log(`Screenshot Selector: Retrying... (${attempts + 1}/${maxRetries})`);
                    setTimeout(attemptSend, retryDelay * attempts); // Exponential backoff
                } else {
                    callback(false, 'Background script not responding. Please reload the extension.');
                }
            }, 5000); // 5 second timeout per attempt
            
            chrome.runtime.sendMessage(message, (response) => {
                clearTimeout(timeoutId);
                
                if (chrome.runtime.lastError) {
                    console.warn(`Screenshot Selector: Runtime error on attempt ${attempts}:`, chrome.runtime.lastError.message);
                    
                    // Check if it's a recoverable error
                    const errorMsg = chrome.runtime.lastError.message;
                    if (errorMsg.includes('Extension context invalidated') || errorMsg.includes('message port closed')) {
                        callback(false, 'Extension context lost. Please reload the page and try again.');
                        return;
                    }
                    
                    // Retry for other errors
                    if (attempts < maxRetries) {
                        console.log(`Screenshot Selector: Retrying after error... (${attempts + 1}/${maxRetries})`);
                        setTimeout(attemptSend, retryDelay * attempts);
                    } else {
                        callback(false, 'Failed to communicate with background script. Please reload the extension.');
                    }
                } else if (!response || !response.success) {
                    // Background script responded but with failure
                    callback(false, response?.error || 'Screenshot capture failed');
                } else {
                    // Success!
                    console.log('Screenshot Selector: Message sent successfully on attempt', attempts);
                    callback(true, response);
                }
            });
        };
        
        attemptSend();
    }

    showError(message, duration = 5000) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc2626;
            color: white;
            padding: 16px 24px;
            border-radius: 8px;
            border: 2px solid #b91c1c;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto';
            font-size: 14px;
            font-weight: 500;
            z-index: 1000000;
            max-width: 400px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        `;
        errorDiv.textContent = message;
        
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            errorDiv.remove();
        }, duration);
    }
    
    cancel() {
        this.cleanup();
    }
    
    cleanup() {
        // Clear the failsafe timeout if cleanup is happening normally
        if (this.cleanupTimeout) {
            clearTimeout(this.cleanupTimeout);
            this.cleanupTimeout = null;
        }
        
        if (this.overlay) {
            this.overlay.remove();
            this.overlay = null;
        }
        
        // Clean up instruction element
        if (this.instructionElement) {
            this.instructionElement.remove();
            this.instructionElement = null;
        }
        
        this.selectionBox = null;
        this.isSelecting = false;
        this.startPos = null;
        this.currentPos = null;
        
        // Clear global flag
        window.STMScreenshotSelectorActive = false;
    }
}

// Global function to start screenshot selection (replaces startAreaSelection)
window.startScreenshotSelection = function() {
    console.log('📸 ScreenshotSelector: startScreenshotSelection() called');
    
    // Prevent multiple instances
    if (window.STMScreenshotSelectorActive) {
        console.log('📸 ScreenshotSelector: Already active, ignoring duplicate call');
        return;
    }
    
    console.log('📸 ScreenshotSelector: Creating new ScreenshotSelector instance');
    window.STMScreenshotSelectorActive = true;
    new ScreenshotSelector();
};

// Backward compatibility alias
window.startAreaSelection = window.startScreenshotSelection;