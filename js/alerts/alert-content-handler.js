/**
 * Alert Content Handler - Manages DOM-based alert popups in content scripts
 */

(function() {
    'use strict';
    
    // Only initialize once per page
    if (window.alertPopupHandler) {
        return;
    }
    
    // Create single instance of AlertPopup
    let alertPopupInstance = null;
    
    // Message listener for alert popup commands
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'alertPopupPing') {
            // Respond to ping to indicate script is loaded
            sendResponse({ loaded: true });
            return true;
        }
        
        if (message.action === 'showAlertPopup') {
            // Create or reuse alert popup instance
            if (!alertPopupInstance) {
                // Check if AlertPopup is available
                if (typeof window.AlertPopup !== 'undefined') {
                    alertPopupInstance = new window.AlertPopup();
                    alertPopupInstance.show(message.alertData);
                } else {
                    console.error('AlertPopup class not available. Background should inject alert-popup.js first.');
                    sendResponse({ success: false, error: 'AlertPopup not loaded' });
                    return true;
                }
            } else {
                alertPopupInstance.show(message.alertData);
            }
            
            sendResponse({ success: true });
            return true;
        }
        
        return false;
    });
    
    // Mark as loaded
    window.alertPopupHandler = true;
})();