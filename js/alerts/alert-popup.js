/**
 * Alert Popup Module - DOM-based alert notifications
 * Follows popup design standards with advanced audio integration
 */

class AlertPopup {
    constructor() {
        this.popup = null;
        this.managedTimeouts = new Set();
        this.managedIntervals = new Set();
        this.alertData = null;
        this.isDragging = false;
        this.dragStartX = 0;
        this.dragStartY = 0;
        this.popupStartX = 0;
        this.popupStartY = 0;
    }

    // Create managed timeout that tracks itself
    createManagedTimeout(callback, delay) {
        const timeoutId = setTimeout(() => {
            this.managedTimeouts.delete(timeoutId);
            callback();
        }, delay);
        this.managedTimeouts.add(timeoutId);
        return timeoutId;
    }

    // Create managed interval that tracks itself
    createManagedInterval(callback, delay) {
        const intervalId = setInterval(callback, delay);
        this.managedIntervals.add(intervalId);
        return intervalId;
    }

    // Show alert popup
    async show(alertData) {
        // Remove any existing popup DOM element only
        if (this.popup && this.popup.parentNode) {
            this.popup.remove();
        }
        
        // Clear any existing timers
        this.managedTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
        this.managedIntervals.forEach(intervalId => clearInterval(intervalId));
        this.managedTimeouts.clear();
        this.managedIntervals.clear();
        
        // Set new alert data
        this.alertData = alertData;
        
        // Create popup structure
        this.createPopup();
        
        // Play notification sound
        await this.playNotificationSound();
        
        // Auto-dismiss removed - notifications persist until manually dismissed or snoozed
        // This ensures notifications remain visible even if user is away from computer
        // this.createManagedTimeout(() => {
        //     this.dismiss();
        // }, 5 * 60 * 1000);
    }

    // Create DOM popup following design standards
    createPopup() {
        // Safety check
        if (!this.alertData) {
            console.error('AlertPopup: No alert data provided');
            return;
        }
        
        // Main container
        this.popup = document.createElement('div');
        this.popup.id = 'gmb-alert-popup';
        this.popup.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #0a0a0a;
            border: 2px solid #7C3AED;
            border-radius: 12px;
            padding: 0;
            width: 550px;
            height: 340px;
            z-index: 9999999;
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            color: #d1d5db;
            overflow: hidden;
        `;

        // Header
        const header = document.createElement('div');
        header.style.cssText = `
            background: #1a1a1a;
            color: white;
            padding: 12px 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
            user-select: none;
            font-size: 16px;
            font-weight: 700;
        `;

        const title = document.createElement('span');
        title.textContent = 'Alert Reminder';
        header.appendChild(title);

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '✕';
        closeButton.style.cssText = `
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        `;
        closeButton.onmouseover = () => closeButton.style.background = 'rgba(255,255,255,0.2)';
        closeButton.onmouseout = () => closeButton.style.background = 'none';
        closeButton.onclick = () => this.dismiss();
        
        // Prevent dragging when clicking close button
        closeButton.onmousedown = (e) => {
            e.stopPropagation();
        };
        
        header.appendChild(closeButton);

        // Content area
        const content = document.createElement('div');
        content.style.cssText = `
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        `;

        // Alert icon
        const icon = document.createElement('div');
        icon.style.cssText = `
            width: 60px;
            height: 60px;
            background: #7C3AED;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 28px;
        `;
        icon.innerHTML = '🔔';
        content.appendChild(icon);

        // Alert message
        const message = document.createElement('div');
        message.style.cssText = `
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #ffffff;
        `;
        message.textContent = this.alertData.title || "Time's Up!";
        content.appendChild(message);

        // Time display
        const timeDisplay = document.createElement('div');
        timeDisplay.style.cssText = `
            font-size: 14px;
            color: #9ca3af;
            margin-bottom: 20px;
        `;
        const now = new Date();
        timeDisplay.textContent = now.toLocaleTimeString();
        content.appendChild(timeDisplay);

        // Timer display
        const timerDisplay = document.createElement('div');
        timerDisplay.id = 'alert-timer-display';
        timerDisplay.style.cssText = `
            font-size: 24px;
            font-weight: 700;
            color: #7C3AED;
            margin-bottom: 20px;
        `;
        timerDisplay.textContent = 'Alert!';
        content.appendChild(timerDisplay);

        // Button container
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            display: flex;
            gap: 10px;
            margin-top: auto;
        `;

        // Dismiss button
        const dismissBtn = document.createElement('button');
        dismissBtn.style.cssText = `
            background: #7C3AED;
            border: 1px solid #7C3AED;
            color: white;
            padding: 8px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s ease;
        `;
        dismissBtn.textContent = 'Dismiss';
        dismissBtn.onmouseover = () => {
            dismissBtn.style.background = '#6D28D9';
            dismissBtn.style.transform = 'translateY(-1px)';
        };
        dismissBtn.onmouseout = () => {
            dismissBtn.style.background = '#7C3AED';
            dismissBtn.style.transform = 'translateY(0)';
        };
        dismissBtn.onclick = () => this.dismiss();
        buttonContainer.appendChild(dismissBtn);

        // Snooze button
        const snoozeBtn = document.createElement('button');
        snoozeBtn.style.cssText = `
            background: transparent;
            border: 1px solid #6b7280;
            color: #6b7280;
            padding: 8px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        `;
        snoozeBtn.textContent = 'Snooze';
        snoozeBtn.onmouseover = () => {
            snoozeBtn.style.background = '#6b7280';
            snoozeBtn.style.color = 'white';
        };
        snoozeBtn.onmouseout = () => {
            snoozeBtn.style.background = 'transparent';
            snoozeBtn.style.color = '#6b7280';
        };
        snoozeBtn.onclick = () => this.showSnoozeOptions();
        buttonContainer.appendChild(snoozeBtn);

        content.appendChild(buttonContainer);

        // Assemble popup
        this.popup.appendChild(header);
        this.popup.appendChild(content);
        document.body.appendChild(this.popup);

        // Load saved position
        this.loadPosition();

        // Make draggable
        this.makeDraggable(header);

        // Setup ESC key handler
        this.setupEscapeHandler();

        // Countdown timer removed - notifications persist until manually dismissed
        // this.startCountdownTimer();
    }

    // Load saved position from localStorage
    loadPosition() {
        try {
            const saved = localStorage.getItem('gmb-alert-popup-position');
            if (saved) {
                const position = JSON.parse(saved);
                
                // Validate position is within screen bounds
                const maxLeft = window.innerWidth - this.popup.offsetWidth;
                const maxTop = window.innerHeight - this.popup.offsetHeight;
                
                const left = Math.max(0, Math.min(position.left, maxLeft));
                const top = Math.max(0, Math.min(position.top, maxTop));
                
                // Apply saved position
                this.popup.style.left = `${left}px`;
                this.popup.style.top = `${top}px`;
                this.popup.style.transform = 'none';
            }
        } catch (error) {
            console.log('Could not load saved position:', error);
        }
    }

    // Save position to localStorage
    savePosition() {
        try {
            const rect = this.popup.getBoundingClientRect();
            const position = {
                left: rect.left,
                top: rect.top,
                lastSaved: Date.now()
            };
            localStorage.setItem('gmb-alert-popup-position', JSON.stringify(position));
        } catch (error) {
            console.log('Could not save position:', error);
        }
    }

    // Make popup draggable
    makeDraggable(handle) {
        handle.onmousedown = (e) => {
            e.preventDefault();
            
            // Don't start drag if clicking close button
            if (e.target.tagName === 'BUTTON') return;
            
            this.isDragging = true;
            this.dragStartX = e.clientX;
            this.dragStartY = e.clientY;
            
            const rect = this.popup.getBoundingClientRect();
            this.popupStartX = rect.left;
            this.popupStartY = rect.top;
            
            // On first drag, convert from centered transform to absolute positioning
            if (this.popup.style.transform !== 'none') {
                this.popup.style.left = `${rect.left}px`;
                this.popup.style.top = `${rect.top}px`;
                this.popup.style.transform = 'none';
            }
            
            document.onmousemove = (e) => this.handleDrag(e);
            document.onmouseup = () => this.stopDrag();
        };
    }

    // Handle drag movement
    handleDrag(e) {
        if (!this.isDragging) return;
        
        e.preventDefault();
        
        const deltaX = e.clientX - this.dragStartX;
        const deltaY = e.clientY - this.dragStartY;
        
        const newLeft = this.popupStartX + deltaX;
        const newTop = this.popupStartY + deltaY;
        
        // Keep within screen bounds
        const maxLeft = window.innerWidth - this.popup.offsetWidth;
        const maxTop = window.innerHeight - this.popup.offsetHeight;
        
        this.popup.style.left = `${Math.max(0, Math.min(newLeft, maxLeft))}px`;
        this.popup.style.top = `${Math.max(0, Math.min(newTop, maxTop))}px`;
    }

    // Stop dragging
    stopDrag() {
        if (this.isDragging) {
            this.isDragging = false;
            document.onmousemove = null;
            document.onmouseup = null;
            
            // Save position after drag
            this.savePosition();
        }
    }

    // Setup ESC key handler
    setupEscapeHandler() {
        this.escapeHandler = (e) => {
            if (e.key === 'Escape') {
                this.dismiss();
            }
        };
        document.addEventListener('keydown', this.escapeHandler);
        
        // Store handler reference for cleanup
        this.popup._escapeHandler = this.escapeHandler;
    }

    // Countdown timer disabled - notifications persist until manually dismissed
    // startCountdownTimer() {
    //     let seconds = 60; // 1 minute countdown
    //     
    //     const updateTimer = () => {
    //         const timerDisplay = document.getElementById('alert-timer-display');
    //         if (timerDisplay) {
    //             if (seconds > 0) {
    //                 timerDisplay.textContent = `Dismissing in ${seconds}s`;
    //                 seconds--;
    //             } else {
    //                 timerDisplay.textContent = 'Alert!';
    //             }
    //         }
    //     };
    //     
    //     // Update immediately
    //     updateTimer();
    //     
    //     // Update every second
    //     this.createManagedInterval(updateTimer, 1000);
    // }

    // Play notification sound using advanced audio system
    async playNotificationSound() {
        try {
            // Get audio settings from local storage
            const result = await chrome.storage.local.get(['pomodoroAudioSettings', 'gmbExtractorSettings']);
            const audioSettings = result.pomodoroAudioSettings || {};
            const generalSettings = result.gmbExtractorSettings || {};
            
            // Check if alert sound is enabled
            if (generalSettings.alertSound === false) {
                return;
            }
            
            // Use Work Completed Sound from pomodoro settings (same as timer completion)
            const soundName = audioSettings.workCompletedSound || 'Bell Meditation';
            const volume = (audioSettings.notificationVolume || 70) / 100;
            
            // Send message to play sound via audio system
            chrome.runtime.sendMessage({
                action: "playSound",
                selectedSound: this.getAudioUrl(soundName),
                soundVolume: volume,
                isSoundEnabled: true
            }, (response) => {
                if (chrome.runtime.lastError) {
                    console.warn('Error playing alert sound:', chrome.runtime.lastError.message);
                }
            });
        } catch (error) {
            console.error('Error playing notification sound:', error);
        }
    }

    // Get audio URL for sound name
    getAudioUrl(soundName) {
        const SOUND_URLS = {
            'Bell Meditation': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Bell+Meditation.mp3',
            'Celestial Gong': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Celestial+Gong.mp3',
            'Deep Meditation Bell Crown Chakra': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Deep+Meditation+Bell+Crown+Chakra.mp3',
            'Deep Meditation Bell Heart Chakra': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Deep+Meditation+Bell+Heart+Chakra.mp3',
            'Funky': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Funky.mp3',
            'Mechanical': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Mechanical.wav',
            'Notification 1': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Notification+1.mp3',
            'Notification 2': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Notification+2.mp3',
            'Notification 3': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Notification+3.mp3',
            'Notification 4': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Notification+4.mp3',
            'Old Church Bell 2': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Old+Church+Bell+2.mp3',
            'Old Church Bell 3': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Old+Church+Bell+3.mp3',
            'Clock': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/clock.mp3',
            'Clock 2': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/clock2.mp3',
            'Clock 3': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/clock3.mp3',
            'Butterfly': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/butterfly.mp3',
            'GTA': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/gta.mp3',
            'iPhone': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/iphone.mp3',
            'Radar': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/radar.mp3',
            'Tada': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/tada.mp3'
        };
        
        return SOUND_URLS[soundName] || SOUND_URLS['Bell Meditation'];
    }

    // Show snooze options
    showSnoozeOptions() {
        // Create snooze options container
        const snoozeContainer = document.createElement('div');
        snoozeContainer.style.cssText = `
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: #1a1a1a;
            border: 1px solid #374151;
            border-radius: 8px;
            padding: 10px;
            display: flex;
            gap: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.4);
        `;

        const snoozeOptions = [1, 2, 5, 10, 15, 30];
        
        snoozeOptions.forEach(minutes => {
            const btn = document.createElement('button');
            btn.style.cssText = `
                background: #2a2a2a;
                border: 1px solid #374151;
                color: #d1d5db;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
            `;
            btn.textContent = `${minutes}m`;
            btn.onmouseover = () => {
                btn.style.background = '#374151';
                btn.style.borderColor = '#6b7280';
            };
            btn.onmouseout = () => {
                btn.style.background = '#2a2a2a';
                btn.style.borderColor = '#374151';
            };
            btn.onclick = () => this.snooze(minutes);
            snoozeContainer.appendChild(btn);
        });


        this.popup.appendChild(snoozeContainer);

        // Remove after 5 seconds
        this.createManagedTimeout(() => {
            if (snoozeContainer.parentNode) {
                snoozeContainer.remove();
            }
        }, 5000);
    }

    // Snooze alert
    async snooze(minutes) {
        if (this.alertData && this.alertData.id) {
            const snoozeTime = Date.now() + (minutes * 60 * 1000);
            console.log('AlertPopup: Snoozing alert for', minutes, 'minutes. Snooze time:', new Date(snoozeTime).toLocaleString());
            
            // Show snoozing feedback
            const timerDisplay = document.getElementById('alert-timer-display');
            if (timerDisplay) {
                timerDisplay.textContent = 'Snoozing...';
                timerDisplay.style.color = '#10b981'; // Green color for success feedback
            }
            
            // Send snooze message to background with timeout handling
            let responseReceived = false;
            
            // Set up a timeout to handle cases where response doesn't come back
            const responseTimeout = this.createManagedTimeout(() => {
                if (!responseReceived) {
                    console.log('AlertPopup: Snooze response timeout - assuming success since snooze functionality works');
                    // Since snooze functionality works, assume success on timeout
                    if (timerDisplay) {
                        timerDisplay.textContent = 'Snoozed successfully';
                        timerDisplay.style.color = '#10b981'; // Green for success
                    }
                    this.createManagedTimeout(() => this.dismiss(), 1000);
                }
            }, 3000); // 3 second timeout
            
            chrome.runtime.sendMessage({
                action: 'snoozeAlert',
                alertId: this.alertData.id,
                snoozeTime: snoozeTime,
                snoozeMinutes: minutes
            }, (response) => {
                responseReceived = true;
                clearTimeout(responseTimeout);
                
                if (chrome.runtime.lastError) {
                    // Check if it's just a message port timing issue
                    const isPortClosed = chrome.runtime.lastError.message?.includes('message port closed') ||
                                       chrome.runtime.lastError.message?.includes('receiving end does not exist');
                    
                    if (isPortClosed) {
                        console.log('AlertPopup: Message port closed during snooze, but operation likely succeeded');
                        if (timerDisplay) {
                            timerDisplay.textContent = 'Snoozed successfully';
                            timerDisplay.style.color = '#10b981'; // Green for success
                        }
                        this.createManagedTimeout(() => this.dismiss(), 1000);
                    } else {
                        console.error('AlertPopup: Runtime error during snooze:', chrome.runtime.lastError);
                        if (timerDisplay) {
                            timerDisplay.textContent = 'Snooze failed!';
                            timerDisplay.style.color = '#ef4444'; // Red for error
                        }
                        this.createManagedTimeout(() => this.dismiss(), 2000);
                    }
                } else if (response && response.success) {
                    console.log('AlertPopup: Snooze successful. Alert scheduled for:', new Date(response.scheduledTime).toLocaleString());
                    if (response.alarmCreated !== undefined) {
                        console.log('AlertPopup: Alarm created:', response.alarmCreated);
                    }
                    if (timerDisplay) {
                        timerDisplay.textContent = 'Snoozed successfully';
                        timerDisplay.style.color = '#10b981';
                    }
                    this.createManagedTimeout(() => this.dismiss(), 1000);
                } else if (response === undefined) {
                    // Undefined response often means port closed but operation succeeded
                    console.log('AlertPopup: Undefined response received, but snooze likely succeeded');
                    if (timerDisplay) {
                        timerDisplay.textContent = 'Snoozed successfully';
                        timerDisplay.style.color = '#10b981';
                    }
                    this.createManagedTimeout(() => this.dismiss(), 1000);
                } else {
                    console.error('AlertPopup: Snooze failed:', response?.error || 'Unknown error');
                    if (timerDisplay) {
                        timerDisplay.textContent = response?.error || 'Snooze failed!';
                        timerDisplay.style.color = '#ef4444';
                    }
                    this.createManagedTimeout(() => this.dismiss(), 2000);
                }
            });
        } else {
            console.error('AlertPopup: No alert data for snooze');
            this.dismiss();
        }
    }

    // Dismiss alert and cleanup
    async dismiss() {
        // Clear all managed timers
        this.managedTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
        this.managedIntervals.forEach(intervalId => clearInterval(intervalId));
        this.managedTimeouts.clear();
        this.managedIntervals.clear();

        // Remove ESC handler
        if (this.escapeHandler) {
            document.removeEventListener('keydown', this.escapeHandler);
        }

        // Remove popup
        if (this.popup && this.popup.parentNode) {
            this.popup.remove();
        }

        // Notify background of dismissal
        if (this.alertData && this.alertData.id) {
            chrome.runtime.sendMessage({
                action: 'alertDismissed',
                alertId: this.alertData.id
            }).catch(() => {
                console.log('Could not notify background of dismissal');
            });
        }

        // Reset state
        this.popup = null;
        this.alertData = null;
    }
}

// Export for use in background script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AlertPopup;
}

// Make available globally for content script
if (typeof window !== 'undefined') {
    window.AlertPopup = AlertPopup;
}