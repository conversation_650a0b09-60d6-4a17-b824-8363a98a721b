// Extension Context Manager - Universal context validation and error handling
(function() {
    'use strict';
    
    // Prevent multiple loads
    if (window.GMBContextManager) return;
    
    class GMBContextManager {
        constructor() {
            this.isValid = true;
            this.lastCheck = 0;
            this.checkCount = 0;
            this.invalidated = false;
            this.debugMode = false;
            this.listeners = new Set();
            
            // Cache validation results for 100ms to prevent excessive checks
            this.cacheTimeout = 100;
            
            // Initialize debug mode from settings
            this.loadDebugMode();
        }
        
        // Load debug mode setting
        async loadDebugMode() {
            try {
                if (this.isContextValid(true)) {
                    const result = await chrome.storage.local.get('gmbExtractorSettings');
                    const settings = result.gmbExtractorSettings || {};
                    this.debugMode = settings.debugMode || false;
                }
            } catch (error) {
                // Silent fallback - debug mode stays false
                this.debugMode = false;
            }
        }
        
        // Main context validation function
        isContextValid(forceCheck = false) {
            // If already marked as invalidated, return false immediately
            if (this.invalidated) {
                return false;
            }
            
            // Use cached result for frequent calls (within cacheTimeout)
            if (!forceCheck && Date.now() - this.lastCheck < this.cacheTimeout) {
                return this.isValid;
            }
            
            try {
                // Comprehensive context validation
                const valid = (
                    typeof chrome !== 'undefined' &&
                    chrome.storage &&
                    chrome.storage.local &&
                    chrome.runtime &&
                    chrome.runtime.id &&
                    chrome.runtime.getManifest &&
                    typeof chrome.runtime.sendMessage === 'function'
                );
                
                if (!valid) {
                    this.markInvalidated('Extension context validation failed');
                    return false;
                }
                
                // Test actual API access with a non-intrusive call
                chrome.runtime.getManifest();
                
                // Update cache
                this.isValid = valid;
                this.lastCheck = Date.now();
                this.checkCount++;
                
                return true;
                
            } catch (error) {
                this.markInvalidated('Extension context test failed: ' + error.message);
                return false;
            }
        }
        
        // Mark context as invalidated
        markInvalidated(reason) {
            if (!this.invalidated) {
                this.invalidated = true;
                this.isValid = false;
                
                if (this.debugMode) {
                    console.log(`GMB Context Manager: Context invalidated - ${reason}`);
                }
                
                // Notify all listeners
                this.notifyListeners('invalidated', reason);
            }
        }
        
        // Add listener for context changes
        addListener(callback) {
            if (typeof callback === 'function') {
                this.listeners.add(callback);
            }
        }
        
        // Remove listener
        removeListener(callback) {
            this.listeners.delete(callback);
        }
        
        // Notify all listeners of context events
        notifyListeners(event, data) {
            this.listeners.forEach(callback => {
                try {
                    callback(event, data);
                } catch (error) {
                    // Silent error handling for listeners
                    if (this.debugMode) {
                        console.error('GMB Context Manager: Listener error:', error);
                    }
                }
            });
        }
        
        // Safe Chrome storage wrapper
        async safeStorageGet(keys) {
            if (!this.isContextValid()) {
                return {}; // Return empty object instead of throwing
            }
            
            try {
                return await chrome.storage.local.get(keys);
            } catch (error) {
                this.markInvalidated('Storage get failed: ' + error.message);
                return {};
            }
        }
        
        // Safe Chrome storage set wrapper
        async safeStorageSet(items) {
            if (!this.isContextValid()) {
                return false;
            }
            
            try {
                await chrome.storage.local.set(items);
                return true;
            } catch (error) {
                this.markInvalidated('Storage set failed: ' + error.message);
                return false;
            }
        }
        
        // Safe message sending wrapper
        async safeMessageSend(tabId, message) {
            if (!this.isContextValid()) {
                return null;
            }
            
            try {
                return await chrome.tabs.sendMessage(tabId, message);
            } catch (error) {
                this.markInvalidated('Message send failed: ' + error.message);
                return null;
            }
        }
        
        // Execute function only if context is valid
        executeIfValid(fn, fallback = null) {
            if (!this.isContextValid()) {
                return fallback;
            }
            
            try {
                return fn();
            } catch (error) {
                if (error.message.includes('Extension context invalidated')) {
                    this.markInvalidated('Function execution failed: ' + error.message);
                } else if (this.debugMode) {
                    console.error('GMB Context Manager: Function execution error:', error);
                }
                return fallback;
            }
        }
        
        // Execute async function only if context is valid
        async executeIfValidAsync(fn, fallback = null) {
            if (!this.isContextValid()) {
                return fallback;
            }
            
            try {
                return await fn();
            } catch (error) {
                if (error.message.includes('Extension context invalidated')) {
                    this.markInvalidated('Async function execution failed: ' + error.message);
                } else if (this.debugMode) {
                    console.error('GMB Context Manager: Async function execution error:', error);
                }
                return fallback;
            }
        }
        
        // Get context status information
        getStatus() {
            return {
                isValid: this.isValid,
                invalidated: this.invalidated,
                lastCheck: this.lastCheck,
                checkCount: this.checkCount,
                debugMode: this.debugMode,
                listenersCount: this.listeners.size
            };
        }
        
        // Reset context manager (for testing/debugging)
        reset() {
            this.isValid = true;
            this.lastCheck = 0;
            this.checkCount = 0;
            this.invalidated = false;
            this.listeners.clear();
            
            if (this.debugMode) {
                console.log('GMB Context Manager: Reset completed');
            }
        }
    }
    
    // Create global instance
    window.GMBContextManager = new GMBContextManager();
    
    // Expose convenient global functions for backward compatibility
    window.isExtensionContextValid = (forceCheck = false) => {
        return window.GMBContextManager.isContextValid(forceCheck);
    };
    
    window.safeStorageGet = (keys) => {
        return window.GMBContextManager.safeStorageGet(keys);
    };
    
    window.safeStorageSet = (items) => {
        return window.GMBContextManager.safeStorageSet(items);
    };
    
    if (window.GMBContextManager.debugMode) {
        console.log('GMB Context Manager: Initialized successfully');
    }
    
})();