// Content script for Google Search pages - <PERSON><PERSON> Button Injection
// Specifically targets the business popup window in search results

// Namespace check to prevent conflicts with other extensions
if (window.SearchNAPInjectorLoaded) {
  console.log('Search NAP Injector: Already loaded, skipping...');
} else {
  window.SearchNAPInjectorLoaded = true;
  
  // Additional check to prevent conflicts with other STM extensions
  if (window.location.href.includes('google.com/maps') || window.location.href.includes('google.com/search') || window.location.href.includes('google.com/localservices/prolist')) {
    console.log('Search NAP Injector: Content script loaded for Google Maps, Search, or Pro List pages');
  } else {
    console.log('Search NAP Injector: Not a Google maps, search or Pro List page, script loaded but inactive');
  }

  // Settings management
  let settings = { searchNAPInjector: true }; // Default enabled
  
  // Load settings from storage
  const loadSettings = async () => {
    try {
      const result = await chrome.storage.local.get('gmbExtractorSettings');
      if (result.gmbExtractorSettings) {
        settings = { ...settings, ...result.gmbExtractorSettings };
      }
      console.log('Search NAP Injector: Loaded settings:', settings);
    } catch (error) {
      console.error('Search NAP Injector: Error loading settings:', error);
    }
  };
  
  // Listen for settings updates
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'updateSettings') {
      settings = { ...settings, ...message.settings };
      console.log('Search NAP Injector: Settings updated:', settings);
      
      // Re-check if we should show/hide buttons based on new settings
      if (!settings.searchNAPInjector) {
        // Remove any existing buttons if Search NAP Injector is disabled
        const existingButton = document.getElementById('gmb-search-popup-nap-btn');
        if (existingButton) {
          existingButton.remove();
          console.log('Search NAP Injector: Button removed due to settings change');
        }
      } else {
        // Re-initialize if Search NAP Injector is enabled
        setTimeout(() => {
          checkAndInjectSearchNAPButtons();
        }, 500);
      }
    }
  });

  // Track if button has been injected to prevent duplicates
  let buttonInjected = false;
  let lastHeadingElement = null;
  let intervalId = null;
  let isPopupOpen = false;
  let debounceTimeout = null;
  let stylesInjected = false;

  // Function to ensure proper styling for business names and buttons
  function ensureProperStyling() {
    if (stylesInjected) {
      return; // Styles already injected
    }
    
    try {
      // Create and inject CSS styles
      const styleElement = document.createElement('style');
      styleElement.id = 'search-nap-injector-styles';
      styleElement.textContent = `
        /* Ensure lfPIob class (business name headings) always have 100% width */
        .lfPIob {
          width: 100% !important;
          max-width: 100% !important;
          min-width: 100% !important;
          display: block !important;
          box-sizing: border-box !important;
        }
        
        /* Ensure DUwDvf lfPIob headings look proper */
        .DUwDvf.lfPIob {
          width: 100% !important;
          max-width: 100% !important;
          min-width: 100% !important;
          display: block !important;
          box-sizing: border-box !important;
          text-align: left !important;
        }
        
        /* Ensure spans within business name headings don't break layout */
        .lfPIob span {
          display: inline !important;
          vertical-align: baseline !important;
        }
        
        /* Make sure buttons in Google business listings look neat */
        .lfPIob ~ * button,
        .DUwDvf.lfPIob ~ * button,
        .qrShPb ~ * button {
          border-radius: 3px !important;
          font-size: 11px !important;
          font-weight: 500 !important;
          padding: 2px 6px !important;
          min-height: 28px !important;
          box-sizing: border-box !important;
          vertical-align: middle !important;
        }
        
        /* Ensure Copy NAP buttons specifically look neat */
        #gmb-search-popup-nap-btn {
          background: #ff8c00 !important;
          height: 28px !important;
          width: 100px !important;
          min-width: 100px !important;
          max-width: 100px !important;
          color: white !important;
          border: none !important;
          border-radius: 3px !important;
          padding: 2px 6px !important;
          font-size: 11px !important;
          font-weight: 500 !important;
          cursor: pointer !important;
          transition: all 0.2s ease !important;
          box-shadow: 0 1px 2px rgba(0,0,0,0.15) !important;
          opacity: 0.9 !important;
          vertical-align: middle !important;
          display: inline-block !important;
          flex-shrink: 0 !important;
          flex-grow: 0 !important;
          margin: 0 !important;
          z-index: 1000 !important;
          position: relative !important;
          box-sizing: border-box !important;
        }
        
        /* Prevent short business names from looking ugly by ensuring proper spacing */
        .lfPIob:empty::before {
          content: " ";
          display: inline-block;
          width: 1px;
        }
      `;
      
      // Insert the styles into the document head
      document.head.appendChild(styleElement);
      stylesInjected = true;
      console.log('Search NAP Injector: Proper styling CSS injected successfully');
      
    } catch (error) {
      console.error('Search NAP Injector: Error injecting styling CSS:', error);
    }
  }

  // Initialize NAP button injection for all supported pages
  function initializeSearchNAPInjection() {
    console.log('Search NAP Injector: Initializing NAP button injection for Maps, Search and Pro List pages...');
    
    // Check if Search NAP Injector is enabled in settings
    if (!settings.searchNAPInjector) {
      console.log('Search NAP Injector: Feature disabled in settings, skipping...');
      return;
    }
    
    // Run on Google Maps, Google search pages and Pro List pages - more flexible URL matching
    if (!window.location.href.includes('google.com/maps') && !window.location.href.includes('google.com/search') && !window.location.href.includes('google.com/localservices/prolist')) {
      console.log('Search NAP Injector: Not a Google maps, search or Pro List page, skipping...');
      return;
    }
    
    // Ensure proper styling is applied first
    ensureProperStyling();
    
    // Check and inject NAP buttons immediately
    checkAndInjectSearchNAPButtons();
    
    // Set up observer to watch for dynamic content changes
    setupSearchNAPButtonObserver();
  }

  function checkAndInjectSearchNAPButtons() {
    try {
      // Ensure proper styling is applied whenever we check for buttons
      ensureProperStyling();
      
      // Skip injection on single business pages (maps/place URLs)
      // The Copy NAP button on single business pages is handled by the main content script
      if (window.location.href.includes('google.com/maps/place/')) {
        return; // Don't inject the additional Copy NAP button on single business pages
      }
      
      // Look for business headings in Google Maps, Google Search and Pro List
      let businessHeading = null;
      
      // Google Maps business heading - primary selector
      businessHeading = document.querySelector('h1[data-attrid="title"]');
      
      // If no Google Maps heading found, try Google Search popup window heading
      if (!businessHeading) {
        businessHeading = document.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-dtype="d3ifr"][data-attrid="title"]') ||
                         document.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-attrid="title"]') ||
                         document.querySelector('h2.qrShPb[data-attrid="title"]');
      }
      
      // If no Google Search heading found, look for open cards in different modes
      if (!businessHeading) {
        // ProList open card heading
        businessHeading = document.querySelector('.rgnuSb.tZPcob');
        
        // If no ProList open card, look for Multiple mode open card
        if (!businessHeading) {
          businessHeading = document.querySelector('.DUwDvf.lfPIob');
        }
      }
      
      if (businessHeading) {
        if (!isPopupOpen) {
          console.log('Search NAP Injector: Business popup/card detected');
          isPopupOpen = true;
        }
        
        // Check if this is the same heading we already processed
        if (lastHeadingElement === businessHeading && buttonInjected) {
          return; // Silent return, no need to log repeatedly
        }
        
        // Check if NAP button already exists anywhere in the document
        const existingButton = document.getElementById('gmb-search-popup-nap-btn');
        if (existingButton) {
          return; // Silent return, button already exists
        }
        
        // Check if button exists in the heading's parent containers
        const headingContainer = businessHeading.closest('div');
        if (headingContainer && headingContainer.querySelector('#gmb-search-popup-nap-btn')) {
          return; // Silent return, button already exists in container
        }
        
        console.log('Search NAP Injector: Injecting NAP button');
        injectPopupNAPButton(businessHeading);
        
        // Track that we've injected the button for this heading
        buttonInjected = true;
        lastHeadingElement = businessHeading;
        
        // Start monitoring for popup closure
        startPopupMonitoring();
        
      } else {
        // Popup is closed
        if (isPopupOpen) {
          console.log('Search NAP Injector: Business popup/card closed');
          isPopupOpen = false;
          // Reset tracking when no popup is found
          buttonInjected = false;
          lastHeadingElement = null;
          // Stop monitoring
          stopPopupMonitoring();
        }
      }
      
    } catch (error) {
      console.error('Search NAP Injector: Error checking for NAP button injection:', error);
    }
  }

  function startPopupMonitoring() {
    // Only start monitoring if not already running
    if (!intervalId) {
      intervalId = setInterval(() => {
        // Check if popup still exists (all types) - using same flexible selectors
        let businessHeading = document.querySelector('h1[data-attrid="title"]') ||
                             document.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-dtype="d3ifr"][data-attrid="title"]') ||
                             document.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-attrid="title"]') ||
                             document.querySelector('h2.qrShPb[data-attrid="title"]');
        
        // If no Google Maps or Search heading, check for open cards in different modes
        if (!businessHeading) {
          // ProList open card heading
          businessHeading = document.querySelector('.rgnuSb.tZPcob');
          
          // If no ProList open card, look for Multiple mode open card
          if (!businessHeading) {
            businessHeading = document.querySelector('.DUwDvf.lfPIob');
          }
        }
        
        if (!businessHeading) {
          // Popup closed, reset everything
          isPopupOpen = false;
          buttonInjected = false;
          lastHeadingElement = null;
          stopPopupMonitoring();
        }
      }, 2000); // Check every 2 seconds only when popup is open
    }
  }

  function stopPopupMonitoring() {
    if (intervalId) {
      clearInterval(intervalId);
      intervalId = null;
    }
  }

  function injectPopupNAPButton(headingElement) {
    try {
      // Double-check that button doesn't exist before creating
      if (document.getElementById('gmb-search-popup-nap-btn')) {
        console.log('Search NAP Injector: Button already exists, aborting injection');
        return;
      }
      
      // Create the NAP button
      const napButton = document.createElement('button');
      napButton.id = 'gmb-search-popup-nap-btn';
      napButton.textContent = 'Copy NAP';
      napButton.style.cssText = `
        background: #ff8c00;
        height: 28px;
        width: 100px;
        min-width: 100px;
        max-width: 100px;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 2px 6px;
        font-size: 11px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 1px 2px rgba(0,0,0,0.15);
        opacity: 0.9;
        vertical-align: middle;
        display: inline-block;
        flex-shrink: 0;
        flex-grow: 0;
        margin: 0;
        z-index: 1000;
        position: relative;
        box-sizing: border-box;
      `;

      // Add hover effects
      napButton.addEventListener('mouseenter', () => {
        napButton.style.background = '#e67e00';
        napButton.style.opacity = '1';
        napButton.style.transform = 'translateY(-0.5px)';
        napButton.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
      });

      napButton.addEventListener('mouseleave', () => {
        napButton.style.background = '#ff8c00';
        napButton.style.opacity = '0.9';
        napButton.style.transform = 'translateY(0)';
        napButton.style.boxShadow = '0 1px 2px rgba(0,0,0,0.15)';
      });

      // Add click handler
      napButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        copyPopupNAPData(headingElement);
      });

      // Insert the button below the heading element as requested
      // Find the parent container of the heading
      const parentContainer = headingElement.parentElement;
      if (parentContainer) {
        // Check if wrapper already exists
        let wrapperDiv = parentContainer.querySelector('.gmb-nap-wrapper');
        
        if (!wrapperDiv) {
          // Create a wrapper div to hold the heading and button container
          wrapperDiv = document.createElement('div');
          wrapperDiv.className = 'gmb-nap-wrapper';
          wrapperDiv.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 8px;
            width: auto;
            max-width: 100%;
          `;
          
          // Insert wrapper before the heading
          parentContainer.insertBefore(wrapperDiv, headingElement);
          
          // Move heading into wrapper
          wrapperDiv.appendChild(headingElement);
          
          // Create a button container for horizontal layout
          const buttonContainer = document.createElement('div');
          buttonContainer.className = 'gmb-button-container';
          buttonContainer.style.cssText = `
            display: flex;
            align-items: center;
            gap: 6px;
            flex-wrap: nowrap;
          `;
          
          wrapperDiv.appendChild(buttonContainer);
        }
        
        // Add button to the button container
        const buttonContainer = wrapperDiv.querySelector('.gmb-button-container');
        if (buttonContainer) {
          buttonContainer.appendChild(napButton);
        } else {
          // Fallback - add directly to wrapper
          wrapperDiv.appendChild(napButton);
        }
        
        console.log('Search NAP Injector: NAP button injected successfully in popup window');
      }
    } catch (error) {
      console.error('Search NAP Injector: Error injecting NAP button in popup:', error);
    }
  }

  function copyPopupNAPData(headingElement) {
    try {
      console.log('Search NAP Injector: Button clicked - waiting for popup content to load...');
      
      // Add a delay to ensure popup content is fully loaded
      setTimeout(() => {
        try {
          console.log('Search NAP Injector: Extracting NAP data from popup window...');
          
          let businessName = '';
          
          // Extract business name based on the heading element type
          if (headingElement.tagName === 'H1') {
            // Google Maps business heading - extract from span inside h1
            const businessNameSpan = headingElement.querySelector('span');
            businessName = businessNameSpan ? businessNameSpan.textContent.trim() : headingElement.textContent.trim();
          } else if (headingElement.tagName === 'H2') {
            // Google Search popup window - extract from span inside h2
            const businessNameSpan = headingElement.querySelector('span');
            businessName = businessNameSpan ? businessNameSpan.textContent.trim() : '';
          } else if (headingElement.classList.contains('rgnuSb') && headingElement.classList.contains('tZPcob')) {
            // ProList open card - business name is directly in the element
            businessName = headingElement.textContent.trim();
          } else if (headingElement.classList.contains('DUwDvf') && headingElement.classList.contains('lfPIob')) {
            // Multiple mode open card - business name is directly in the element
            businessName = headingElement.textContent.trim();
          } else {
            // Fallback - try to get text content directly
            businessName = headingElement.textContent.trim();
          }
          
          console.log('Search NAP Injector: Extracted business name:', businessName);
          
          let address = '';
          let phone = '';
          
          console.log('Search NAP Injector: Searching for address and phone...');
          
          // Extract address using multiple approaches
          // First try the LrzXr class (common in Google Search popups)
          const addressElement = document.querySelector('span.LrzXr');
          if (addressElement) {
            address = addressElement.textContent.trim();
            console.log('Search NAP Injector: Found address with LrzXr:', address);
          } else {
            console.log('Search NAP Injector: Address element with class LrzXr not found, trying fallbacks...');
            
            // Enhanced fallback address selectors for different modes
            const fallbackAddressSelectors = [
              // Google Search selectors
              '[data-attrid="kc:/collection/knowledge_panels/local_business:address"] span',
              '.rogA2c',
              'button[data-item-id="address"] .fontBodyMedium',
              '[data-attrid*="address"] span',
              // ProList and Multiple mode selectors
              '.fccl3c', // ProList address selector
              '.rgnuSb.xYjf2e + div span', // Address often appears in div after business name
              '.DUwDvf + div span', // Address in Multiple mode
              'span[style*="color"]:not([aria-label])', // Address spans often have color styling
              // General selectors
              'div[data-attrid*="address"]',
              'span[data-attrid*="address"]'
            ];
            
            for (const selector of fallbackAddressSelectors) {
              const fallbackAddressElement = document.querySelector(selector);
              if (fallbackAddressElement && fallbackAddressElement.textContent.trim()) {
                const addressText = fallbackAddressElement.textContent.trim();
                // Validate that this looks like an address (contains street/suburb info or Australian state)
                if (addressText.length > 10 && (
                    addressText.includes('St ') || addressText.includes('Ave ') || addressText.includes('Rd ') || 
                    addressText.includes('Street') || addressText.includes('Avenue') || addressText.includes('Road') ||
                    addressText.includes(' NSW') || addressText.includes(' QLD') || addressText.includes(' VIC') || 
                    addressText.includes(' SA') || addressText.includes(' WA') || addressText.includes(' TAS') || 
                    addressText.includes(' NT') || addressText.includes(' ACT') ||
                    addressText.match(/\d{4}$/) // Ends with 4-digit postcode
                )) {
                  address = addressText;
                  console.log(`Search NAP Injector: Found address with fallback selector ${selector}:`, address);
                  break;
                }
              }
            }
          }
          
          // Extract phone using multiple approaches
          // First try the aria-label approach (common in Google Search)
          const phoneElement = document.querySelector('span[aria-label*="Call phone"]');
          if (phoneElement) {
            phone = phoneElement.textContent.trim();
            console.log('Search NAP Injector: Found phone with aria-label:', phone);
          } else {
            console.log('Search NAP Injector: Phone element with aria-label "Call phone" not found, trying fallbacks...');
            
            // Enhanced fallback phone selectors for different modes
            const fallbackPhoneSelectors = [
              // Google Search selectors
              'span[aria-label*="Phone"]',
              'button[aria-label^="Phone:"]',
              '[data-attrid="kc:/collection/knowledge_panels/local_business:phone"] span',
              'button[data-item-id="phone"] .fontBodyMedium',
              '.rogA2c[data-item-id="phone"]',
              'a[href^="tel:"]',
              // ProList and Multiple mode selectors
              'span[jsaction*="phone"]',
              'div[data-attrid*="phone"] span',
              'button[data-attrid*="phone"]'
            ];
            
            for (const selector of fallbackPhoneSelectors) {
              const fallbackPhoneElement = document.querySelector(selector);
              if (fallbackPhoneElement) {
                let phoneText = '';
                
                if (selector.includes('aria-label')) {
                  // Extract from aria-label
                  const ariaLabel = fallbackPhoneElement.getAttribute('aria-label');
                  if (ariaLabel && ariaLabel.includes('Call phone')) {
                    phoneText = fallbackPhoneElement.textContent.trim();
                  } else if (ariaLabel && ariaLabel.includes('Phone:')) {
                    const phoneMatch = ariaLabel.match(/Phone:\s*(.+)/);
                    if (phoneMatch) {
                      phoneText = phoneMatch[1].trim();
                    }
                  }
                } else if (selector.includes('tel:')) {
                  // Extract from tel: link
                  const href = fallbackPhoneElement.getAttribute('href');
                  phoneText = href.replace('tel:', '').trim();
                } else {
                  phoneText = fallbackPhoneElement.textContent.trim();
                }
                
                // Validate that this looks like a phone number (Australian format)
                const phonePattern = /[\(\d\)\s\-\+]{8,}/;
                if (phoneText && phonePattern.test(phoneText)) {
                  phone = phoneText;
                  console.log(`Search NAP Injector: Found phone with fallback selector ${selector}:`, phone);
                  break;
                }
              }
            }
          }
          
          // If still no phone found, try pattern matching in all text elements
          if (!phone) {
            console.log('Search NAP Injector: Trying pattern matching for phone...');
            const phonePatterns = [
              /\(\d{2}\)\s*\d{4}\s*\d{4}/, // (08) 1234 5678
              /\d{2}\s*\d{4}\s*\d{4}/, // 08 1234 5678
              /\+61\s*\d{1}\s*\d{4}\s*\d{4}/, // +61 8 1234 5678
              /1300\s*\d{3}\s*\d{3}/, // 1300 123 456
              /1800\s*\d{3}\s*\d{3}/, // 1800 123 456
              /04\d{2}\s*\d{3}\s*\d{3}/ // 0412 345 678 (mobile)
            ];
            
            const allSpans = document.querySelectorAll('span, div, button');
            for (const element of allSpans) {
              const text = element.textContent.trim();
              for (const pattern of phonePatterns) {
                const match = text.match(pattern);
                if (match) {
                  phone = match[0];
                  console.log('Search NAP Injector: Found phone via pattern matching:', phone);
                  break;
                }
              }
              if (phone) break;
            }
          }
          
          // Log what we found
          console.log('Search NAP Injector: Extraction results:', {
            businessName: businessName || 'NOT FOUND',
            address: address || 'NOT FOUND', 
            phone: phone || 'NOT FOUND'
          });
          
          // Format exactly like Multiple mode: Name\nAddress\nPhone
          const napText = `${businessName}\n${address}\n${phone}`;
          
          console.log('Search NAP Injector: Final NAP text to copy:', napText);
          
          // Copy to clipboard
          navigator.clipboard.writeText(napText).then(() => {
            showPopupNAPFeedback('Copied!', '#22c55e');
            console.log('Search NAP Injector: NAP data copied successfully');
          }).catch(err => {
            console.error('Search NAP Injector: Failed to copy NAP data:', err);
            showPopupNAPFeedback('Failed', '#ef4444');
          });

        } catch (error) {
          console.error('Search NAP Injector: Error extracting NAP data from popup:', error);
          showPopupNAPFeedback('Error', '#ef4444');
        }
      }, 1000); // Wait 1 second for popup content to fully load

    } catch (error) {
      console.error('Search NAP Injector: Error in copyPopupNAPData:', error);
      showPopupNAPFeedback('Error', '#ef4444');
    }
  }

  function showPopupNAPFeedback(message, color) {
    const button = document.getElementById('gmb-search-popup-nap-btn');
    if (button) {
      const originalText = button.textContent;
      const originalColor = button.style.background;
      
      button.textContent = message;
      button.style.background = color;
      
      setTimeout(() => {
        button.textContent = originalText;
        button.style.background = originalColor;
      }, 1500);
    }
  }

  function setupSearchNAPButtonObserver() {
    // Watch for dynamic content changes in search results
    let currentUrl = window.location.href;
    
    const observer = new MutationObserver((mutations) => {
      let shouldCheck = false;
      
      // Check if URL changed
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        shouldCheck = true;
        // Reset tracking when URL changes
        buttonInjected = false;
        lastHeadingElement = null;
        isPopupOpen = false;
        stopPopupMonitoring();
        console.log('Search NAP Injector: URL changed, resetting state');
      }
      
      // Only check for mutations if we're on a search page - more flexible URL matching
      if (!window.location.href.includes('google.com/search') && !window.location.href.includes('google.com/localservices/prolist')) {
        return;
      }
      
      // Check if new content was added that might contain a business popup
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Filter out mutations from other extensions or irrelevant content
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Skip nodes that are clearly from other extensions
              if (node.id && (node.id.includes('STM') || node.id.includes('STM'))) {
                return;
              }
              if (node.className && typeof node.className === 'string' && 
                  (node.className.includes('STM') || node.className.includes('STM'))) {
                return;
              }
              
              // Only check for business popup elements specifically (Google Search or Pro List)
              const hasBusinessPopup = node.querySelector && 
                (node.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-dtype="d3ifr"][data-attrid="title"]') ||
                 node.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-attrid="title"]') ||
                 node.querySelector('h2.qrShPb[data-attrid="title"]') ||
                 node.querySelector('.rgnuSb.tZPcob, .rgnuSb.xYjf2e') ||
                 node.matches && (node.matches('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-dtype="d3ifr"][data-attrid="title"]') ||
                                  node.matches('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-attrid="title"]') ||
                                  node.matches('h2.qrShPb[data-attrid="title"]') ||
                                  node.matches('.rgnuSb.tZPcob, .rgnuSb.xYjf2e')));
              
              if (hasBusinessPopup) {
                shouldCheck = true;
                console.log('Search NAP Injector: Business popup/card detected in new content');
              }
            }
          });
        }
      });
      
      if (shouldCheck) {
        // Use debouncing to prevent excessive checks from other extensions
        if (debounceTimeout) {
          clearTimeout(debounceTimeout);
        }
        
        debounceTimeout = setTimeout(() => {
          checkAndInjectSearchNAPButtons();
          debounceTimeout = null;
        }, 800); // Increased delay to reduce conflicts with other extensions
      }
    });

    // Start observing with more specific configuration
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      // Only observe specific attributes that might indicate popup changes
      attributeFilter: ['data-ved', 'data-attrid', 'class']
    });
    
    console.log('Search NAP Injector: Mutation observer setup complete');
  }

  // Initialize when the script loads with settings check
  // Wait a bit for the page to load
  setTimeout(async () => {
    await loadSettings();
    if (settings.searchNAPInjector) {
      initializeSearchNAPInjection();
    }
  }, 1500);

  // Also initialize on DOM content loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', async () => {
      setTimeout(async () => {
        await loadSettings();
        if (settings.searchNAPInjector) {
          initializeSearchNAPInjection();
        }
      }, 1000);
    });
  }

  // Initialize on window load as well
  window.addEventListener('load', async () => {
    setTimeout(async () => {
      await loadSettings();
      if (settings.searchNAPInjector) {
        initializeSearchNAPInjection();
      }
    }, 1000);
  });
} 