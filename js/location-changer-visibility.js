// Location Changer Visibility Manager
// This file handles showing/hiding the location changer based on user settings

class LocationChangerVisibility {
    constructor() {
        this.init();
    }

    async init() {
        await this.checkVisibility();
        this.setupStorageListener();
    }

    async checkVisibility() {
        try {
            const result = await chrome.storage.local.get('gmbExtractorSettings');
            const settings = result.gmbExtractorSettings || {};
            
            // Default to true if setting doesn't exist (backwards compatibility)
            const isEnabled = settings.locationChangerEnabled !== false;
            
            this.toggleLocationChanger(isEnabled);
        } catch (error) {
            console.error('Error checking location changer visibility:', error);
            // Default to showing it if there's an error
            this.toggleLocationChanger(true);
        }
    }

    toggleLocationChanger(show) {
        const locationChangerElement = document.querySelector('.location-changer-accordion');
        
        if (locationChangerElement) {
            if (show) {
                // Show with smooth transition
                locationChangerElement.style.display = '';
                // Small delay to ensure display is applied before transition
                setTimeout(() => {
                    locationChangerElement.classList.remove('location-changer-hidden');
                }, 10);
            } else {
                // Hide with smooth transition
                locationChangerElement.classList.add('location-changer-hidden');
                // Hide completely after transition completes
                setTimeout(() => {
                    if (locationChangerElement.classList.contains('location-changer-hidden')) {
                        locationChangerElement.style.display = 'none';
                    }
                }, 300); // Match the CSS transition duration
            }
        }
    }

    setupStorageListener() {
        // Listen for settings changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'sync' && changes.gmbExtractorSettings) {
                const newSettings = changes.gmbExtractorSettings.newValue || {};
                const isEnabled = newSettings.locationChangerEnabled !== false;
                this.toggleLocationChanger(isEnabled);
            }
        });
    }

    // Integration function for current location display on SERP pages
    static getCurrentLocationForDisplay() {
        return new Promise((resolve) => {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.get(['gmbExtractorSettings', 'settings'], function(result) {
                    const gmbSettings = result.gmbExtractorSettings || {};
                    const locationSettings = result.settings || {};
                    
                    const isDisplayEnabled = gmbSettings.currentLocationDisplayEnabled !== false;
                    const isLocationEnabled = locationSettings.enabled || false;
                    const location = locationSettings.location || '';
                    
                    resolve({
                        isDisplayEnabled,
                        isLocationEnabled,
                        location,
                        locationData: locationSettings
                    });
                });
            } else {
                resolve({
                    isDisplayEnabled: false,
                    isLocationEnabled: false,
                    location: '',
                    locationData: null
                });
            }
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LocationChangerVisibility();
});

// Export for use in other scripts
window.LocationChangerVisibility = LocationChangerVisibility; 