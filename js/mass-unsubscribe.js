(async () => {
  // Prevent multiple executions
  if (window.MassUnsubscribeLoaded) {
    return;
  }
  window.MassUnsubscribeLoaded = true;

  let isProcessing = false;
  let unsubscribeOpportunities = [];
  let isEnabled = true;
  let processedSenders = new Set(); // Track processed senders for this session only (resets on page reload)
  let whitelistedSenders = new Set(); // Track whitelisted senders

  // --- Storage Functions ---

  async function loadWhitelist() {
    try {
      const data = await chrome.storage.local.get('massUnsubscribeWhitelist');
      const whitelist = data.massUnsubscribeWhitelist || [];
      whitelistedSenders = new Set(whitelist);
      console.log('Loaded whitelist:', Array.from(whitelistedSenders));
    } catch (e) {
      console.error('Error loading whitelist:', e);
      whitelistedSenders = new Set();
    }
  }

  async function saveWhitelist() {
    try {
      await chrome.storage.local.set({
        massUnsubscribeWhitelist: Array.from(whitelistedSenders)
      });
      console.log('Saved whitelist:', Array.from(whitelistedSenders));
    } catch (e) {
      console.error('Error saving whitelist:', e);
    }
  }

  function addToWhitelist(senderInfo) {
    whitelistedSenders.add(senderInfo);
    saveWhitelist();
  }

  function removeFromWhitelist(senderInfo) {
    whitelistedSenders.delete(senderInfo);
    saveWhitelist();
  }

  function isWhitelisted(senderInfo) {
    return whitelistedSenders.has(senderInfo);
  }

  // --- Scanning Functions ---

  function findUnsubscribeButtons() {
    const opportunities = [];
    const seenSenders = new Set(); // Track duplicates
    
    // Detect if we're on subscription management page
    const isSubscriptionPage = window.location.href.includes('#sub');
    
    // Method 1: Find subscription management page buttons (if on subscription page)
    if (isSubscriptionPage) {
      console.log('Detected subscription management page, scanning for subscription buttons...');
      
      // Find subscription page unsubscribe buttons - both icon and text variants
      const subscriptionButtons = document.querySelectorAll([
        'button.pYTkkf-JX-I[aria-label="Unsubscribe"]', // Icon button variant
        'button.mUIrbf-I:has(.mUIrbf-anl)', // Text button variant - look for buttons containing text span
        'button:has(span.mUIrbf-anl)' // Fallback text button selector
      ].join(', '));
      
      console.log(`Found ${subscriptionButtons.length} subscription page buttons`);
      
      subscriptionButtons.forEach((button, index) => {
        // Verify it's actually an unsubscribe button
        const isUnsubscribeButton = 
          button.getAttribute('aria-label') === 'Unsubscribe' ||
          button.textContent.trim().toLowerCase().includes('unsubscribe');
        
        if (!isUnsubscribeButton) return;
        
        // Find sender information from parent div with data-email
        const parentDiv = button.closest('div[jscontroller="BRXymc"]') || 
                         button.closest('div[data-email]') || 
                         button.closest('td').querySelector('div[data-email]');
        
        let senderInfo = 'Unknown Sender';
        let senderEmail = '';
        let senderName = '';
        
        if (parentDiv && parentDiv.hasAttribute('data-email')) {
          senderEmail = parentDiv.getAttribute('data-email') || '';
          
          // Try to find sender name from the row content
          const tableRow = button.closest('tr');
          if (tableRow) {
            // Look for sender name in various possible locations
            const nameElements = [
              tableRow.querySelector('.zA'),
              tableRow.querySelector('.yW'),
              tableRow.querySelector('span[title]'),
              ...Array.from(tableRow.querySelectorAll('span')).filter(span => 
                span.textContent.trim() && 
                !span.textContent.toLowerCase().includes('email') &&
                span.textContent.length > 2 &&
                span.textContent.length < 100
              )
            ].filter(Boolean);
            
            // Use first meaningful name found
            if (nameElements.length > 0) {
              senderName = nameElements[0].getAttribute('title') || nameElements[0].textContent.trim();
            }
          }
          
          // Format the sender info: "Name - <EMAIL>" or just email
          if (senderName && senderEmail && senderName !== senderEmail) {
            senderInfo = `${senderName} - ${senderEmail}`;
          } else if (senderEmail) {
            senderInfo = senderEmail;
          } else if (senderName) {
            senderInfo = senderName;
          }
        }
        
        // Check for duplicates, processed senders, and whitelisted senders
        if (!seenSenders.has(senderInfo) && !processedSenders.has(senderInfo) && !isWhitelisted(senderInfo) && senderInfo !== 'Unknown Sender') {
          seenSenders.add(senderInfo);
          opportunities.push({
            id: `sub-${index}`,
            button: button,
            span: button, // For subscription page, button is the clickable element
            sender: senderInfo,
            email: senderEmail,
            name: senderName,
            processed: false,
            subscriptionPage: true // Flag to indicate this is from subscription page
          });
        }
      });
      
      console.log(`Found ${opportunities.length} subscription page opportunities`);
    }
    
    // Method 2: Find Gmail inbox unsubscribe buttons using span.aJ6 (existing logic for inbox)
    if (!isSubscriptionPage) {
      const unsubButtons = document.querySelectorAll('span.aJ6');
      
      unsubButtons.forEach((span, index) => {
        if (span.textContent.trim() === 'Unsubscribe') {
          // Get the actual clickable button element (parent div with role="button")
          const clickableButton = span.closest('div[role="button"]') || span.closest('.T-I') || span.parentElement;
          
          if (!clickableButton) {
            console.warn('Could not find clickable button for unsubscribe span:', span);
            return;
          }
          
          // Try to find the sender email or domain
          const emailRow = span.closest('tr');
          let senderInfo = 'Unknown Sender';
          let senderEmail = '';
          let senderName = '';
          
          if (emailRow) {
            // Look for sender info in the .zF span first (preferred method)
            const zfElement = emailRow.querySelector('span.zF[email][name]');
            if (zfElement) {
              senderEmail = zfElement.getAttribute('email') || '';
              senderName = zfElement.getAttribute('name') || zfElement.textContent.trim() || '';
            } else {
              // Fallback: look for email element
              const emailElement = emailRow.querySelector('span[email]');
              if (emailElement) {
                senderEmail = emailElement.getAttribute('email') || '';
                senderName = emailElement.getAttribute('name') || '';
              } else {
                // Final fallback: look for sender name in the row
                const senderElement = emailRow.querySelector('.yW span[name]');
                if (senderElement) {
                  senderName = senderElement.getAttribute('name') || senderElement.textContent.trim() || '';
                }
              }
            }
            
            // Format the sender info: "Name - <EMAIL>"
            if (senderName && senderEmail) {
              senderInfo = `${senderName} - ${senderEmail}`;
            } else if (senderName) {
              senderInfo = senderName;
            } else if (senderEmail) {
              senderInfo = senderEmail;
            }
          }
          
          // Check for duplicates, processed senders, and whitelisted senders
          if (!seenSenders.has(senderInfo) && !processedSenders.has(senderInfo) && !isWhitelisted(senderInfo)) {
            seenSenders.add(senderInfo);
            opportunities.push({
              id: index,
              button: clickableButton, // Use clickable element instead of span
              span: span, // Keep reference to original span for debugging
              sender: senderInfo,
              email: senderEmail,
              name: senderName,
              processed: false
            });
          }
        }
      });
    }
    
    // Method 3: Text-based backup method (works for both inbox and subscription pages)
    const allElements = Array.from(document.querySelectorAll('button, span, a, div[role="button"], [tabindex]'));
    const textBasedButtons = allElements.filter(el => {
      const text = el.textContent.trim().toLowerCase();
      const isUnsubscribeText = text === 'unsubscribe';
      
      // Skip if we already found this element in previous methods
      const alreadyFound = opportunities.some(opp => opp.button === el || opp.span === el);
      if (alreadyFound) return false;
      
      // Exclude Gmail navigation elements and interface components (for inbox)
      if (!isSubscriptionPage) {
        const isNavigation = el.closest('.J-Ke') || // Gmail sidebar navigation
                            el.closest('[aria-label*="label"]') || // Label navigation
                            el.hasAttribute('aria-label') && el.getAttribute('aria-label').includes('label') ||
                            el.closest('.nZ') || // Gmail left sidebar
                            el.closest('.aeN') || // Gmail menu items
                            el.closest('.TK') || // Gmail toolbar
                            el.closest('.ar') || // Gmail header area
                            el.closest('.Cr') || // Gmail navigation
                            el.closest('.gb_') || // Google bar
                            el.closest('.z0') || // Gmail compose area
                            el.closest('.inboxsdk') || // InboxSDK elements
                            el.href && el.href.includes('#label/') || // Direct label links
                            el.href && el.href.includes('mail.google.com/mail') || // Gmail navigation links
                            !el.closest('tr') || // Must be within an email row
                            el.closest('tr').querySelector('td.apU') === null; // Must be in email content area
        
        if (isNavigation) return false;
      }
      
      return isUnsubscribeText;
    });
    
    console.log(`Found ${textBasedButtons.length} text-based elements (backup method)`);
    
    // Process text-based buttons
    textBasedButtons.forEach((element, index) => {
      let senderInfo = 'Unknown Sender (Text-based)';
      let senderEmail = '';
      let senderName = '';
      
      if (isSubscriptionPage) {
        // For subscription page, look for data-email in parent
        const parentDiv = element.closest('div[data-email]');
        if (parentDiv) {
          senderEmail = parentDiv.getAttribute('data-email') || '';
          senderInfo = senderEmail || 'Unknown Sender (Text-based)';
        }
      } else {
        // For inbox, use existing logic
        const emailRow = element.closest('tr');
        if (emailRow) {
          const zfElement = emailRow.querySelector('span.zF[email][name]');
          if (zfElement) {
            senderEmail = zfElement.getAttribute('email') || '';
            senderName = zfElement.getAttribute('name') || zfElement.textContent.trim() || '';
            senderInfo = senderName && senderEmail ? `${senderName} - ${senderEmail}` : (senderName || senderEmail);
          }
        }
      }
      
      // Only add if we can identify a real sender (not "Unknown Sender")
      const hasValidSender = senderInfo !== 'Unknown Sender (Text-based)' && 
                            senderInfo !== 'Unknown Sender' && 
                            (senderEmail || senderName);
      
      if (hasValidSender && !seenSenders.has(senderInfo) && !processedSenders.has(senderInfo) && !isWhitelisted(senderInfo)) {
        seenSenders.add(senderInfo);
        opportunities.push({
          id: `text-${index}`,
          button: element, // Use the element directly
          span: element, // Same as button for text-based
          sender: senderInfo,
          email: senderEmail,
          name: senderName,
          processed: false,
          textBased: true, // Flag to indicate this is from text-based detection
          subscriptionPage: isSubscriptionPage // Flag to indicate page type
        });
      }
    });
    
    // Count how many were filtered out due to being already processed or whitelisted
    const allPossibleSenders = new Set();
    const allButtons = isSubscriptionPage ? 
      [...document.querySelectorAll('button[aria-label="Unsubscribe"], button:has(.mUIrbf-anl)'), ...textBasedButtons] :
      [...document.querySelectorAll('span.aJ6'), ...textBasedButtons];
      
    allButtons.forEach(element => {
      if (element.textContent.trim().toLowerCase().includes('unsubscribe') || element.getAttribute('aria-label') === 'Unsubscribe') {
        let senderInfo = '';
        
        if (isSubscriptionPage) {
          const parentDiv = element.closest('div[data-email]');
          if (parentDiv) {
            senderInfo = parentDiv.getAttribute('data-email') || '';
          }
        } else {
          const emailRow = element.closest('tr');
          if (emailRow) {
            const zfElement = emailRow.querySelector('span.zF[email][name]');
            if (zfElement) {
              const email = zfElement.getAttribute('email') || '';
              const name = zfElement.getAttribute('name') || '';
              senderInfo = name && email ? `${name} - ${email}` : (name || email);
            }
          }
        }
        
        if (senderInfo && senderInfo !== 'Unknown Sender') {
          allPossibleSenders.add(senderInfo);
        }
      }
    });
    
    const alreadyProcessedCount = Array.from(allPossibleSenders).filter(sender => processedSenders.has(sender)).length;
    const whitelistedCount = Array.from(allPossibleSenders).filter(sender => isWhitelisted(sender)).length;
    
    console.log(`Found ${opportunities.length} unsubscribe opportunities (${alreadyProcessedCount} already processed, ${whitelistedCount} whitelisted):`, opportunities);
    if (alreadyProcessedCount > 0) {
      console.log(`Already processed senders:`, Array.from(processedSenders));
    }
    if (whitelistedCount > 0) {
      console.log(`Whitelisted senders:`, Array.from(whitelistedSenders));
    }
    
    return opportunities;
  }

  // --- UI Functions ---

  function createUnsubscribePopup(opportunities) {
    // Remove existing popup if any
    const existingPopup = document.getElementById('mass-unsubscribe-popup');
    if (existingPopup) {
      existingPopup.remove();
    }

    const popup = document.createElement('div');
    popup.id = 'mass-unsubscribe-popup';
    popup.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #0a0a0a;
      border: 2px solid #7C3AED;
      border-radius: 12px;
      padding: 0;
      width: 600px;
      height: 90%;
      z-index: 9999999;
      box-shadow: 0 8px 32px rgba(0,0,0,0.6);
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
      color: #d1d5db;
      resize: both;
      overflow: hidden;
      min-width: 400px;
      min-height: 300px;
    `;

    // Create draggable header
    const header = document.createElement('div');
    header.style.cssText = `
      background: #1a1a1a;
      color: white;
      padding: 12px 20px;
      border-radius: 10px 10px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: move;
      user-select: none;
      font-size: 16px;
      font-weight: 700;
    `;

    const titleText = document.createElement('span');
    const processedCount = processedSenders.size;
    const whitelistedCount = whitelistedSenders.size;
    let titleSuffix = '';
    if (processedCount > 0 || whitelistedCount > 0) {
      const parts = [];
      if (processedCount > 0) parts.push(`${processedCount} processed`);
      if (whitelistedCount > 0) parts.push(`${whitelistedCount} whitelisted`);
      titleSuffix = ` - ${parts.join(', ')}`;
    }
    titleText.textContent = `Mass Unsubscribe (${opportunities.length} found${titleSuffix})`;

    const closeButton = document.createElement('button');
    closeButton.innerHTML = '✕';
    closeButton.style.cssText = `
      background: none;
      border: none;
      color: white;
      font-size: 18px;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      transition: background-color 0.2s ease;
    `;
    closeButton.onmouseover = () => closeButton.style.background = '#2a2a2a';
    closeButton.onmouseout = () => closeButton.style.background = 'none';
    closeButton.onclick = () => {
      if (popup._escapeHandler) {
        document.removeEventListener('keydown', popup._escapeHandler);
      }
      popup.remove();
    };
    
    // Prevent dragging when clicking the close button
    closeButton.onmousedown = (e) => {
      e.stopPropagation();
    };

    header.appendChild(titleText);
    header.appendChild(closeButton);

    // Create content area
    const content = document.createElement('div');
    content.style.cssText = `
      padding: 20px;
      height: calc(100% - 88px);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    `;

    // Create select all/deselect all controls
    const selectControls = document.createElement('div');
    selectControls.style.cssText = `
      display: flex;
      gap: 12px;
      margin-bottom: 16px;
      padding: 12px;
      background: #111111;
      border-radius: 8px;
      border: 1px solid #2a2a2a;
    `;

    const selectAllBtn = document.createElement('button');
    selectAllBtn.textContent = 'Select All';
    selectAllBtn.style.cssText = `
      background: transparent;
      border: 1px solid #ffffff;
      color: #ffffff;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
    `;
    selectAllBtn.onmouseover = () => {
      selectAllBtn.style.background = '#010101';
      selectAllBtn.style.color = 'white';
    };
    selectAllBtn.onmouseout = () => {
      selectAllBtn.style.background = 'transparent';
      selectAllBtn.style.color = '#ffffff';
    };

    const deselectAllBtn = document.createElement('button');
    deselectAllBtn.textContent = 'Deselect All';
    deselectAllBtn.style.cssText = `
      background: transparent;
      border: 1px solid #ffffff;
      color: #ffffff;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
    `;
    deselectAllBtn.onmouseover = () => {
      deselectAllBtn.style.background = '#6b7280';
      deselectAllBtn.style.color = 'white';
    };
    deselectAllBtn.onmouseout = () => {
      deselectAllBtn.style.background = 'transparent';
      deselectAllBtn.style.color = '#6b7280';
    };

    selectControls.appendChild(selectAllBtn);
    selectControls.appendChild(deselectAllBtn);

    // Create scrollable list
    const list = document.createElement('div');
    list.style.cssText = `
      flex: 1;
      overflow-y: auto;
      margin-bottom: 16px;
    `;
    
    // Add dark theme scrollbar styling
    const style = document.createElement('style');
    style.textContent = `
      #mass-unsubscribe-popup .scrollable-list::-webkit-scrollbar {
        width: 8px;
      }
      #mass-unsubscribe-popup .scrollable-list::-webkit-scrollbar-track {
        background: #1a1a1a;
        border-radius: 4px;
      }
      #mass-unsubscribe-popup .scrollable-list::-webkit-scrollbar-thumb {
        background: #374151;
        border-radius: 4px;
      }
      #mass-unsubscribe-popup .scrollable-list::-webkit-scrollbar-thumb:hover {
        background: #4b5563;
      }
    `;
    document.head.appendChild(style);
    
    list.className = 'scrollable-list';

    opportunities.forEach((opp, index) => {
      const item = document.createElement('div');
      item.style.cssText = `
        display: flex; 
        align-items: flex-start; 
        margin: 8px 0; 
        padding: 12px; 
        border: 1px solid #374151; 
        border-radius: 6px; 
        background: #111827;
        transition: background-color 0.2s ease;
      `;

      // Add hover effect
      item.onmouseover = () => item.style.background = '#1f2937';
      item.onmouseout = () => item.style.background = '#111827';

      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.checked = true;
      checkbox.id = `unsub-${index}`;
      checkbox.className = 'unsub-checkbox';
      checkbox.style.cssText = `
        margin-right: 12px; 
        margin-top: 2px;
        transform: scale(1.2);
        accent-color: #7C3AED;
        cursor: pointer;
      `;

      const label = document.createElement('label');
      label.setAttribute('for', `unsub-${index}`);
      label.textContent = opp.sender;
      label.style.cssText = `
        flex: 1; 
        cursor: pointer; 
        font-size: 14px; 
        line-height: 1.4;
        color: #d1d5db;
        word-break: break-word;
      `;

      item.appendChild(checkbox);
      item.appendChild(label);
      list.appendChild(item);
    });

    // Create button container
    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = 'display: flex; gap: 12px; justify-content: flex-end;';

    const whitelistButton = document.createElement('button');
    whitelistButton.textContent = 'Add to Whitelist';
    whitelistButton.style.cssText = `
      background: transparent;
      border: 1px solid #10b981;
      color: #10b981;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.2s ease;
    `;
    whitelistButton.onmouseover = () => {
      whitelistButton.style.background = '#10b981';
      whitelistButton.style.color = 'white';
      whitelistButton.style.transform = 'translateY(-1px)';
    };
    whitelistButton.onmouseout = () => {
      whitelistButton.style.background = 'transparent';
      whitelistButton.style.color = '#10b981';
      whitelistButton.style.transform = 'translateY(0)';
    };
    whitelistButton.onclick = () => addSelectedToWhitelist(popup);

    const unsubscribeButton = document.createElement('button');
    unsubscribeButton.textContent = 'Unsubscribe All';
    unsubscribeButton.style.cssText = `
      background: #7C3AED;
      border: 1px solid #7C3AED;
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.2s ease;
    `;
    unsubscribeButton.onmouseover = () => {
      unsubscribeButton.style.background = '#6D28D9';
      unsubscribeButton.style.transform = 'translateY(-1px)';
    };
    unsubscribeButton.onmouseout = () => {
      unsubscribeButton.style.background = '#010101';
      unsubscribeButton.style.transform = 'translateY(0)';
    };
    unsubscribeButton.onclick = () => processSelectedUnsubscribes(popup);

    buttonContainer.appendChild(whitelistButton);
    buttonContainer.appendChild(unsubscribeButton);

    // Add select/deselect all functionality
    selectAllBtn.onclick = () => {
      const checkboxes = popup.querySelectorAll('.unsub-checkbox');
      checkboxes.forEach(cb => cb.checked = true);
    };

    deselectAllBtn.onclick = () => {
      const checkboxes = popup.querySelectorAll('.unsub-checkbox');
      checkboxes.forEach(cb => cb.checked = false);
    };

    // Assemble popup
    content.appendChild(selectControls);
    content.appendChild(list);
    content.appendChild(buttonContainer);
    popup.appendChild(header);
    popup.appendChild(content);

    document.body.appendChild(popup);

    // Make popup draggable
    makeDraggable(popup, header);

    // Handle ESC key
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        popup.remove();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);

    // Store escape handler for cleanup
    popup._escapeHandler = handleEscape;
  }

  // --- Whitelist UI Functions ---

  function createWhitelistPopup() {
    // Remove existing popup if any
    const existingPopup = document.getElementById('whitelist-popup');
    if (existingPopup) {
      existingPopup.remove();
    }

    const popup = document.createElement('div');
    popup.id = 'whitelist-popup';
    popup.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #0a0a0a;
      border: 2px solid #7C3AED;
      border-radius: 12px;
      padding: 0;
      width: 600px;
      height: 500px;
      z-index: 9999999;
      box-shadow: 0 8px 32px rgba(0,0,0,0.6);
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
      color: #d1d5db;
      resize: both;
      overflow: hidden;
      min-width: 400px;
      min-height: 300px;
    `;

    // Create draggable header
    const header = document.createElement('div');
    header.style.cssText = `
      background: #1a1a1a;
      color: white;
      padding: 12px 20px;
      border-radius: 10px 10px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: move;
      user-select: none;
      font-size: 16px;
      font-weight: 700;
    `;

    const titleText = document.createElement('span');
    titleText.innerHTML = `<span style="color: #7C3AED; font-size: 14px;">●</span> Whitelist Management (${whitelistedSenders.size} entries)`;

    const closeButton = document.createElement('button');
    closeButton.innerHTML = '✕';
    closeButton.style.cssText = `
      background: none;
      border: none;
      color: white;
      font-size: 18px;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      transition: background-color 0.2s ease;
    `;
    closeButton.onmouseover = () => closeButton.style.background = '#2a2a2a';
    closeButton.onmouseout = () => closeButton.style.background = 'none';
    closeButton.onclick = () => {
      if (popup._escapeHandler) {
        document.removeEventListener('keydown', popup._escapeHandler);
      }
      popup.remove();
    };
    
    // Prevent dragging when clicking the close button
    closeButton.onmousedown = (e) => {
      e.stopPropagation();
    };

    header.appendChild(titleText);
    header.appendChild(closeButton);

    // Create content area
    const content = document.createElement('div');
    content.style.cssText = `
      padding: 20px;
      height: calc(100% - 88px);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    `;

    if (whitelistedSenders.size === 0) {
      // Show empty state
      const emptyState = document.createElement('div');
      emptyState.style.cssText = `
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #6b7280;
        text-align: center;
      `;
      
      const emptyIcon = document.createElement('div');
      emptyIcon.style.cssText = `
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      `;
      emptyIcon.textContent = '✉';
      
      const emptyText = document.createElement('div');
      emptyText.style.cssText = `
        font-size: 16px;
        margin-bottom: 8px;
        font-weight: 600;
      `;
      emptyText.textContent = 'No Whitelisted Senders';
      
      const emptyDescription = document.createElement('div');
      emptyDescription.style.cssText = `
        font-size: 14px;
        line-height: 1.5;
        max-width: 300px;
      `;
      emptyDescription.textContent = 'When you choose not to unsubscribe from a sender, they will appear here for future management.';
      
      emptyState.appendChild(emptyIcon);
      emptyState.appendChild(emptyText);
      emptyState.appendChild(emptyDescription);
      content.appendChild(emptyState);
    } else {
      // Create select all/deselect all controls
      const selectControls = document.createElement('div');
      selectControls.style.cssText = `
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
        padding: 12px;
        background: #111111;
        border-radius: 8px;
        border: 1px solid #2a2a2a;
      `;

      const selectAllBtn = document.createElement('button');
      selectAllBtn.textContent = 'Select All';
      selectAllBtn.style.cssText = `
        background: transparent;
        border: 1px solid #ffffff;
        color: #ffffff;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
      `;
      selectAllBtn.onmouseover = () => {
        selectAllBtn.style.background = '#7C3AED';
        selectAllBtn.style.color = 'white';
      };
      selectAllBtn.onmouseout = () => {
        selectAllBtn.style.background = 'transparent';
        selectAllBtn.style.color = '#7C3AED';
      };

      const deselectAllBtn = document.createElement('button');
      deselectAllBtn.textContent = 'Deselect All';
      deselectAllBtn.style.cssText = `
        background: transparent;
        border: 1px solid #ffffff;
        color: #ffffff;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
      `;
      deselectAllBtn.onmouseover = () => {
        deselectAllBtn.style.background = '#6b7280';
        deselectAllBtn.style.color = 'white';
      };
      deselectAllBtn.onmouseout = () => {
        deselectAllBtn.style.background = 'transparent';
        deselectAllBtn.style.color = '#6b7280';
      };

      selectControls.appendChild(selectAllBtn);
      selectControls.appendChild(deselectAllBtn);

      // Create scrollable list
      const list = document.createElement('div');
      list.style.cssText = `
        flex: 1;
        overflow-y: auto;
        margin-bottom: 16px;
      `;
      
      // Add dark theme scrollbar styling
      const style = document.createElement('style');
      style.textContent = `
        #whitelist-popup .scrollable-list::-webkit-scrollbar {
          width: 8px;
        }
        #whitelist-popup .scrollable-list::-webkit-scrollbar-track {
          background: #1a1a1a;
          border-radius: 4px;
        }
        #whitelist-popup .scrollable-list::-webkit-scrollbar-thumb {
          background: #374151;
          border-radius: 4px;
        }
        #whitelist-popup .scrollable-list::-webkit-scrollbar-thumb:hover {
          background: #4b5563;
        }
      `;
      document.head.appendChild(style);
      
      list.className = 'scrollable-list';

      const whitelistArray = Array.from(whitelistedSenders);
      whitelistArray.forEach((sender, index) => {
        const item = document.createElement('div');
        item.style.cssText = `
          display: flex; 
          align-items: flex-start; 
          margin: 8px 0; 
          padding: 12px; 
          border: 1px solid #374151; 
          border-radius: 6px; 
          background: #111827;
          transition: background-color 0.2s ease;
        `;

        // Add hover effect
        item.onmouseover = () => item.style.background = '#1f2937';
        item.onmouseout = () => item.style.background = '#111827';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = false;
        checkbox.id = `whitelist-${index}`;
        checkbox.className = 'whitelist-checkbox';
        checkbox.style.cssText = `
          margin-right: 12px; 
          margin-top: 2px;
          transform: scale(1.2);
          accent-color: #dc2626;
          cursor: pointer;
        `;

        const label = document.createElement('label');
        label.setAttribute('for', `whitelist-${index}`);
        label.textContent = sender;
        label.style.cssText = `
          flex: 1; 
          cursor: pointer; 
          font-size: 14px; 
          line-height: 1.4;
          color: #d1d5db;
          word-break: break-word;
        `;

        item.appendChild(checkbox);
        item.appendChild(label);
        list.appendChild(item);
      });

      // Create button container
      const buttonContainer = document.createElement('div');
      buttonContainer.style.cssText = 'display: flex; gap: 12px; justify-content: flex-end;';

      const removeButton = document.createElement('button');
      removeButton.textContent = 'Remove Selected';
      removeButton.style.cssText = `
        background: #dc2626;
        border: 1px solid #dc2626;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.2s ease;
      `;
      removeButton.onmouseover = () => {
        removeButton.style.background = '#b91c1c';
        removeButton.style.transform = 'translateY(-1px)';
      };
      removeButton.onmouseout = () => {
        removeButton.style.background = '#dc2626';
        removeButton.style.transform = 'translateY(0)';
      };
      removeButton.onclick = () => removeSelectedFromWhitelist(popup);

      buttonContainer.appendChild(removeButton);

      // Add select/deselect all functionality
      selectAllBtn.onclick = () => {
        const checkboxes = popup.querySelectorAll('.whitelist-checkbox');
        checkboxes.forEach(cb => cb.checked = true);
      };

      deselectAllBtn.onclick = () => {
        const checkboxes = popup.querySelectorAll('.whitelist-checkbox');
        checkboxes.forEach(cb => cb.checked = false);
      };

      // Assemble popup
      content.appendChild(selectControls);
      content.appendChild(list);
      content.appendChild(buttonContainer);
    }

    popup.appendChild(header);
    popup.appendChild(content);

    document.body.appendChild(popup);

    // Make popup draggable
    makeDraggable(popup, header);

    // Handle ESC key
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        popup.remove();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);

    // Store escape handler for cleanup
    popup._escapeHandler = handleEscape;
  }

  function removeSelectedFromWhitelist(popup) {
    const checkboxes = popup.querySelectorAll('input[type="checkbox"]:checked');
    const selectedIndexes = Array.from(checkboxes).map(cb => parseInt(cb.id.replace('whitelist-', '')));
    
    if (selectedIndexes.length === 0) {
      alert('No items selected for removal.');
      return;
    }

    const whitelistArray = Array.from(whitelistedSenders);
    const toRemove = selectedIndexes.map(index => whitelistArray[index]);
    
    const confirmMessage = `Remove ${toRemove.length} sender(s) from whitelist?\n\n${toRemove.join('\n')}`;
    if (confirm(confirmMessage)) {
      toRemove.forEach(sender => removeFromWhitelist(sender));
      
      // Clean up escape handler
      if (popup._escapeHandler) {
        document.removeEventListener('keydown', popup._escapeHandler);
      }
      popup.remove();
      
      // Show updated whitelist
      setTimeout(() => createWhitelistPopup(), 100);
    }
  }

  function addSelectedToWhitelist(popup) {
    const checkboxes = popup.querySelectorAll('input[type="checkbox"]:checked');
    const selectedIndexes = Array.from(checkboxes).map(cb => parseInt(cb.id.replace('unsub-', '')));
    
    if (selectedIndexes.length === 0) {
      return; // Silently return if nothing selected
    }

    const toWhitelist = selectedIndexes.map(index => unsubscribeOpportunities[index].sender);
    
    // Add to whitelist silently
    toWhitelist.forEach(sender => addToWhitelist(sender));
    
    // Remove the whitelisted items from current popup display
    selectedIndexes.reverse().forEach(index => {
      const item = popup.querySelector(`#unsub-${index}`)?.closest('div[style*="display: flex"]');
      if (item) {
        item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        item.style.opacity = '0';
        item.style.transform = 'translateX(100px)';
        setTimeout(() => {
          item.remove();
          updatePopupTitle(popup);
        }, 300);
      }
    });
    
    // Remove them from the opportunities array to keep indexing consistent
    selectedIndexes.sort((a, b) => b - a).forEach(index => {
      unsubscribeOpportunities.splice(index, 1);
    });
    
    // Re-index remaining checkboxes to maintain consistency
    setTimeout(() => {
      const remainingCheckboxes = popup.querySelectorAll('.unsub-checkbox');
      remainingCheckboxes.forEach((checkbox, newIndex) => {
        const oldId = checkbox.id;
        const newId = `unsub-${newIndex}`;
        checkbox.id = newId;
        const label = popup.querySelector(`label[for="${oldId}"]`);
        if (label) {
          label.setAttribute('for', newId);
        }
      });
    }, 350);
  }

  function updatePopupTitle(popup) {
    const titleText = popup.querySelector('span');
    if (titleText) {
      const remainingCount = popup.querySelectorAll('.unsub-checkbox').length;
      const processedCount = processedSenders.size;
      const whitelistedCount = whitelistedSenders.size;
      let titleSuffix = '';
      if (processedCount > 0 || whitelistedCount > 0) {
        const parts = [];
        if (processedCount > 0) parts.push(`${processedCount} processed`);
        if (whitelistedCount > 0) parts.push(`${whitelistedCount} whitelisted`);
        titleSuffix = ` - ${parts.join(', ')}`;
      }
      titleText.textContent = `Mass Unsubscribe (${remainingCount} found${titleSuffix})`;
    }
  }

  // Make element draggable
  function makeDraggable(element, handle) {
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
    let isDragging = false;
    
    handle.onmousedown = dragMouseDown;
    
    function dragMouseDown(e) {
      e = e || window.event;
      e.preventDefault();
      pos3 = e.clientX;
      pos4 = e.clientY;
      document.onmouseup = closeDragElement;
      document.onmousemove = elementDrag;
      isDragging = false;
    }
    
    function elementDrag(e) {
      e = e || window.event;
      e.preventDefault();
      
      // On first drag, convert from centered transform to absolute positioning
      if (!isDragging) {
        isDragging = true;
        const rect = element.getBoundingClientRect();
        element.style.top = rect.top + 'px';
        element.style.left = rect.left + 'px';
        element.style.transform = 'none';
      }
      
      pos1 = pos3 - e.clientX;
      pos2 = pos4 - e.clientY;
      pos3 = e.clientX;
      pos4 = e.clientY;
      
      const newTop = element.offsetTop - pos2;
      const newLeft = element.offsetLeft - pos1;
      
      // Keep popup within viewport bounds
      const maxTop = window.innerHeight - element.offsetHeight;
      const maxLeft = window.innerWidth - element.offsetWidth;
      
      element.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
      element.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
    }
    
    function closeDragElement() {
      document.onmouseup = null;
      document.onmousemove = null;
    }
  }

  // --- Processing Functions ---

  async function processSelectedUnsubscribes(popup) {
    if (isProcessing) return;
    
    isProcessing = true;
    
    // Get selected opportunities
    const checkboxes = popup.querySelectorAll('input[type="checkbox"]:checked');
    const selectedIndexes = Array.from(checkboxes).map(cb => parseInt(cb.id.replace('unsub-', '')));
    
    if (selectedIndexes.length === 0) {
      alert('No items selected for unsubscribe.');
      isProcessing = false;
      return;
    }

    // Clean up escape handler
    if (popup._escapeHandler) {
      document.removeEventListener('keydown', popup._escapeHandler);
    }
    popup.remove();
    
    // Add progress indicator to button instead of popup
    addProgressIndicatorToButton();
    
    let processed = 0;
    let successful = 0;
    
    for (const index of selectedIndexes) {
      const opportunity = unsubscribeOpportunities[index];
      if (!opportunity) continue;
      
      // Update button progress
      updateButtonProgress(processed + 1, selectedIndexes.length, 'Processing');
      
      try {
        console.log(`Processing unsubscribe ${processed + 1}/${selectedIndexes.length}:`, opportunity.sender);
        
        const success = await processUnsubscribe(opportunity.button);
        if (success) {
          successful++;
          opportunity.processed = true;
          
          // Add to processed senders for this session
          processedSenders.add(opportunity.sender);
          console.log(`Successfully unsubscribed from: ${opportunity.sender}`);
          console.log(`Added to session memory: ${opportunity.sender} (Total processed: ${processedSenders.size})`);
        } else {
          console.log(`Failed to unsubscribe from: ${opportunity.sender}`);
        }
      } catch (error) {
        console.error('Error processing unsubscribe:', error);
      }
      
      processed++;
      
      // Wait 1.5 seconds before next (as requested)
      if (processed < selectedIndexes.length) {
        await new Promise(resolve => setTimeout(resolve, 1500));
      }
    }
    
    // Show completion
    updateButtonProgress(processed, selectedIndexes.length, 'Complete');
    
    setTimeout(() => {
      resetButtonProgress();
      isProcessing = false;
      
      // Show summary
      alert(`Mass Unsubscribe Complete!\n${successful}/${processed} successful unsubscribes.`);
    }, 2000);
  }

  async function processUnsubscribe(unsubButton) {
    return new Promise((resolve) => {
      try {
        console.log('Processing unsubscribe button:', unsubButton);
        
        // Detect if we're on subscription management page
        const isSubscriptionPage = window.location.href.includes('#sub');
        
        let clickableElement = null;
        
        if (isSubscriptionPage) {
          // For subscription page buttons, they are already clickable elements
          // Check if it's a subscription page button by class
          const isSubButton = unsubButton.classList.contains('pYTkkf-JX-I') || 
                             unsubButton.classList.contains('mUIrbf-I') ||
                             unsubButton.getAttribute('aria-label') === 'Unsubscribe';
          
          if (isSubButton) {
            clickableElement = unsubButton;
            console.log('Using subscription page button directly:', clickableElement);
          } else {
            // Fallback for text-based elements on subscription page
            clickableElement = unsubButton.closest('button') || unsubButton;
          }
        } else {
          // Inbox button handling (existing logic)
          // If we received the span.aJ6, find its clickable parent
          if (unsubButton.classList && unsubButton.classList.contains('aJ6')) {
            clickableElement = unsubButton.closest('div[role="button"]') || 
                             unsubButton.closest('.T-I') || 
                             unsubButton.parentElement;
          } else {
            // If we already have the clickable element
            clickableElement = unsubButton;
          }
        }
        
        if (!clickableElement) {
          console.error('Could not find clickable element for unsubscribe button');
          resolve(false);
          return;
        }
        
        console.log('Found clickable element:', clickableElement.className, clickableElement);
        
        // Scroll into view first
        clickableElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Wait for scroll to complete
        setTimeout(() => {
          // For subscription page, use direct click; for inbox, use hover + mousedown/mouseup
          if (isSubscriptionPage) {
            console.log('Using direct click method for subscription page button');
            
            // For subscription page buttons, try direct click first
            clickableElement.click();
            
            // Wait for potential confirmation dialog
            setTimeout(() => {
              // Look for confirmation dialog
              let confirmButton = null;
              
              // Try various selectors for confirmation dialogs
              const selectors = [
                'button[data-mdc-dialog-action="ok"]',
                'button[data-mdc-dialog-action="confirm"]', 
                'button[data-mdc-dialog-action="unsubscribe"]',
                'div[role="dialog"] button:last-child',
                'div[role="alertdialog"] button:last-child'
              ];
              
              for (const selector of selectors) {
                confirmButton = document.querySelector(selector);
                if (confirmButton) {
                  console.log(`Found confirmation button with selector: ${selector}`, confirmButton);
                  break;
                }
              }
              
              // If no specific button found, look for any button with confirmation text
              if (!confirmButton) {
                const allButtons = document.querySelectorAll('button, [role="button"]');
                for (const btn of allButtons) {
                  const text = btn.textContent.trim().toLowerCase();
                  if (text.includes('unsubscribe') || text.includes('confirm') || text.includes('ok')) {
                    // Make sure it's visible and not the original button
                    if (btn !== unsubButton && btn.offsetParent !== null) {
                      confirmButton = btn;
                      console.log('Found confirmation button by text:', confirmButton);
                      break;
                    }
                  }
                }
              }
              
              if (confirmButton) {
                console.log('Clicking confirmation button:', confirmButton);
                confirmButton.click();
                
                // Wait for action to complete
                setTimeout(() => {
                  resolve(true);
                }, 800);
              } else {
                console.log('No confirmation dialog found, assuming direct unsubscribe worked');
                resolve(true);
              }
            }, 1000); // Wait for potential popup
            
          } else {
            // Inbox button handling (existing hover + mousedown/mouseup method)
            console.log('Using hover + mousedown/mouseup method for inbox button');
            
            // First simulate hover to activate the button (Gmail requires this)
            const hoverEvent = new MouseEvent('mouseover', {
              view: window,
              bubbles: true,
              cancelable: true
            });
            clickableElement.dispatchEvent(hoverEvent);
            
            // Small delay for hover to take effect
            setTimeout(() => {
              // Use the proven working method: MouseDown + MouseUp
              console.log('Using MouseDown + MouseUp method for unsubscribe button');
              
              // Method that works: MouseDown + MouseUp sequence
              clickableElement.dispatchEvent(new MouseEvent('mousedown', { 
                view: window,
                bubbles: true,
                cancelable: true
              }));
              
              // Small delay between mousedown and mouseup
              setTimeout(() => {
                clickableElement.dispatchEvent(new MouseEvent('mouseup', { 
                  view: window,
                  bubbles: true,
                  cancelable: true
                }));
              }, 50);
              
              // Wait for confirmation popup to appear
              setTimeout(() => {
              // Look for confirmation dialog with multiple selectors
              let confirmButton = null;
              
              // Try various selectors for Gmail confirmation dialogs
              const selectors = [
                'button[data-mdc-dialog-action="ok"]',
                'button[data-mdc-dialog-action="confirm"]', 
                'button[data-mdc-dialog-action="unsubscribe"]',
                'div[role="dialog"] button:last-child',
                'div[role="alertdialog"] button:last-child'
              ];
              
              for (const selector of selectors) {
                confirmButton = document.querySelector(selector);
                if (confirmButton) {
                  console.log(`Found confirmation button with selector: ${selector}`, confirmButton);
                  break;
                }
              }
              
              // If no specific button found, look for any button with "unsubscribe" text
              if (!confirmButton) {
                const allButtons = document.querySelectorAll('button, [role="button"]');
                for (const btn of allButtons) {
                  const text = btn.textContent.trim().toLowerCase();
                  if (text.includes('unsubscribe') || text.includes('confirm') || text.includes('ok')) {
                    // Make sure it's visible and not the original button
                    if (btn !== unsubButton && btn.offsetParent !== null) {
                      confirmButton = btn;
                      console.log('Found confirmation button by text:', confirmButton);
                      break;
                    }
                  }
                }
              }
              
              if (confirmButton) {
                console.log('Clicking confirmation button:', confirmButton);
                
                // Simulate hover and click on confirmation button too
                const confirmHover = new MouseEvent('mouseover', {
                  view: window,
                  bubbles: true,
                  cancelable: true
                });
                confirmButton.dispatchEvent(confirmHover);
                
                setTimeout(() => {
                  confirmButton.click();
                  
                  // Wait for action to complete
                  setTimeout(() => {
                    resolve(true);
                  }, 800);
                }, 200);
                
              } else {
                console.error('Could not find confirmation unsubscribe button');
                // Log all available buttons for debugging
                const allButtons = document.querySelectorAll('button, [role="button"]');
                console.log('Available buttons:', Array.from(allButtons).map(b => ({
                  text: b.textContent.trim(),
                  visible: b.offsetParent !== null,
                  element: b
                })));
                resolve(false);
              }
            }, 1200); // Wait longer for popup to appear
            
            }, 300); // Wait for hover effect
          }
          
        }, 500); // Wait for scroll to complete
        
      } catch (error) {
        console.error('Error in processUnsubscribe:', error);
        resolve(false);
      }
    });
  }

  function addProgressIndicatorToButton() {
    const button = document.getElementById('mass-unsubscribe-btn');
    if (!button) return;

    // Add progress indicator inside the button
    const progressIndicator = document.createElement('div');
    progressIndicator.id = 'mass-unsubscribe-progress-indicator';
    progressIndicator.style.cssText = `
      position: absolute;
      bottom: 0;
      left: 0;
      height: 3px;
      background: #FCD34D;
      width: 0%;
      transition: width 0.3s ease;
      border-radius: 0 0 25px 25px;
    `;

    // Keep button fixed positioned, just add indicator
    button.style.position = 'fixed';
    button.appendChild(progressIndicator);

    return progressIndicator;
  }

  function updateButtonProgress(current, total, status) {
    const button = document.getElementById('mass-unsubscribe-btn');
    const indicator = document.getElementById('mass-unsubscribe-progress-indicator');
    
    if (button && indicator) {
      const percentage = (current / total) * 100;
      indicator.style.width = `${percentage}%`;
      
      // Update button text to show progress
      button.textContent = `${status} (${current}/${total})`;
    }
  }

  function resetButtonProgress() {
    const button = document.getElementById('mass-unsubscribe-btn');
    const indicator = document.getElementById('mass-unsubscribe-progress-indicator');
    
    if (button) {
      button.textContent = '⚡ Mass Unsubscribe';
    }
    
    if (indicator) {
      indicator.remove();
    }
  }

  // --- URL Detection Functions ---

  function isValidGmailView() {
    const currentUrl = window.location.href;
    
    // Check if we're in Gmail
    if (!currentUrl.includes('mail.google.com')) {
      return false;
    }
    
    // Pattern for valid Gmail views: inbox and subscription management
    // Should match: 
    // - /mail/u/0/#inbox
    // - /mail/u/1/#inbox  
    // - /mail/u/2/#inbox
    // - /mail/#inbox (basic inbox without account number)
    // - /mail/u/0/#sub (subscription management)
    // - /mail/u/1/#sub (subscription management)
    // - /mail/#sub (basic subscription without account number)
    // Should NOT match individual emails like:
    // - /mail/u/0/#inbox/WhctKLbfLsNbDXnlrwlLTmgrVwxHKvxFzkPgkPFSFLRCWQbHVbnZjNVkDwsHPMrqvjjQVnV
    // - /mail/u/1/#inbox/********************************
    
    // Check for valid views with user number: /mail/u/{number}/#(inbox|sub)
    const viewWithUserPattern = /\/mail\/u\/\d+\/#(inbox|sub)$/;
    
    // Check for basic views without account number: /mail/#(inbox|sub)
    const basicViewPattern = /\/mail\/#(inbox|sub)$/;
    
    const isValidView = viewWithUserPattern.test(currentUrl) || basicViewPattern.test(currentUrl);
    
    return isValidView;
  }

  // --- Main Function ---

  function scanAndShowUnsubscribes() {
    if (isProcessing) {
      alert('Already processing unsubscribes. Please wait...');
      return;
    }

    unsubscribeOpportunities = findUnsubscribeButtons();
    
    if (unsubscribeOpportunities.length === 0) {
      const totalProcessedThisSession = processedSenders.size;
      if (totalProcessedThisSession > 0) {
        alert(`No new unsubscribe opportunities found on this page.\n\nAlready processed ${totalProcessedThisSession} sender(s) this session:\n${Array.from(processedSenders).join('\n')}\n\nRefresh the page to reset session memory.`);
      } else {
        alert('No unsubscribe opportunities found on this page.');
      }
      return;
    }

    createUnsubscribePopup(unsubscribeOpportunities);
  }

  // --- Add button to Gmail interface ---

  function addMassUnsubscribeButton() {
    // Check if we're on Gmail
    if (!window.location.hostname.includes('mail.google.com')) {
      return;
    }

    // Only show in main inbox view, not individual email views
    if (!isValidGmailView()) {
      return;
    }

    // Remove existing buttons if any
    const existingButton = document.getElementById('mass-unsubscribe-btn');
    const existingSettings = document.getElementById('mass-unsubscribe-settings-btn');
    if (existingButton) {
      existingButton.remove();
    }
    if (existingSettings) {
      existingSettings.remove();
    }

    // Only add button if enabled
    if (!isEnabled) {
      return;
    }

    // Create container for buttons
    const buttonContainer = document.createElement('div');
    buttonContainer.id = 'mass-unsubscribe-container';
    buttonContainer.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      display: flex;
      gap: 8px;
      z-index: 9999;
    `;

    // Create main floating button
    const button = document.createElement('button');
    button.id = 'mass-unsubscribe-btn';
    button.innerHTML = '<span style="color: #7C3AED; font-size: 14px;">●</span> Mass Unsubscribe';
    button.style.cssText = `
      background: #384151;
      color: white;
      border: none;
      border-radius: 25px;
      padding: 12px 20px;
      font-weight: bold;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
      font-size: 14px;
      transition: all 0.3s ease;
      overflow: hidden;
      white-space: nowrap;
      min-width: 180px;
    `;

    button.onmouseover = () => {
      button.style.background = '#6D28D9';
      button.style.transform = 'scale(1.05)';
    };

    button.onmouseout = () => {
      button.style.background = '#7C3AED';
      button.style.transform = 'scale(1)';
    };

    button.onclick = scanAndShowUnsubscribes;

    // Create settings cog button
    const settingsButton = document.createElement('button');
    settingsButton.id = 'mass-unsubscribe-settings-btn';
    settingsButton.innerHTML = '⚙';
    settingsButton.title = 'Manage Whitelist';
    settingsButton.style.cssText = `
      background: #374151;
      color: white;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      font-size: 16px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    settingsButton.onmouseover = () => {
      settingsButton.style.background = '#4b5563';
      settingsButton.style.transform = 'scale(1.1)';
    };

    settingsButton.onmouseout = () => {
      settingsButton.style.background = '#374151';
      settingsButton.style.transform = 'scale(1)';
    };

    settingsButton.onclick = createWhitelistPopup;

    // Add buttons to container
    buttonContainer.appendChild(button);
    buttonContainer.appendChild(settingsButton);

    document.body.appendChild(buttonContainer);
  }

  function removeButton() {
    const existingContainer = document.getElementById('mass-unsubscribe-container');
    const existingButton = document.getElementById('mass-unsubscribe-btn');
    const existingSettings = document.getElementById('mass-unsubscribe-settings-btn');
    
    if (existingContainer) {
      existingContainer.remove();
    } else {
      // Fallback for individual button removal
      if (existingButton) {
        existingButton.remove();
      }
      if (existingSettings) {
        existingSettings.remove();
      }
    }
  }

  // --- Settings Management ---

  async function loadSettings() {
    try {
      const data = await chrome.storage.local.get('gmbExtractorSettings');
      const settings = data.gmbExtractorSettings || {};
      isEnabled = settings.massUnsubscribeEnabled !== false; // Default to true
    } catch (e) {
      isEnabled = true; // Default to enabled if settings fail
      console.error('Error loading Mass Unsubscribe settings:', e);
    }
  }

  // Listen for settings updates
  chrome.runtime.onMessage.addListener((request) => {
    if (request.action === 'updateSettings' && request.settings) {
      const newSetting = request.settings.massUnsubscribeEnabled !== false;
      if (newSetting !== isEnabled) {
        isEnabled = newSetting;
        if (isEnabled && isValidGmailView()) {
          addMassUnsubscribeButton();
        } else {
          removeButton();
        }
      }
    }
  });

  // --- Initialize ---

  if (window.location.hostname === 'mail.google.com') {
    // Load settings and whitelist first
    Promise.all([loadSettings(), loadWhitelist()]).then(() => {
      // Add button when page loads (only if enabled)
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addMassUnsubscribeButton);
      } else {
        addMassUnsubscribeButton();
      }
    });
    
    // Handle Gmail navigation changes (SPA behavior)
    let lastUrl = window.location.href;
    
    const observer = new MutationObserver(() => {
      const currentUrl = window.location.href;
      
      // Check if URL changed (Gmail navigation)
      if (currentUrl !== lastUrl) {
        lastUrl = currentUrl;
        
        // Remove button when navigating away from inbox
        if (!isValidGmailView()) {
          removeButton();
          return;
        }
      }
      
      // Add button if we're in inbox view and button doesn't exist
      if (isEnabled && isValidGmailView() && !document.getElementById('mass-unsubscribe-container') && !document.getElementById('mass-unsubscribe-btn')) {
        setTimeout(addMassUnsubscribeButton, 1000);
      }
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Also listen for hash changes (Gmail navigation)
    window.addEventListener('hashchange', () => {
      setTimeout(() => {
        if (isValidGmailView()) {
          if (isEnabled && !document.getElementById('mass-unsubscribe-container') && !document.getElementById('mass-unsubscribe-btn')) {
            addMassUnsubscribeButton();
          }
        } else {
          removeButton();
        }
      }, 500); // Small delay to let Gmail load
    });
  }

})(); 