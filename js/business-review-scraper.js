// Business Review Scraper Module
// This module handles review extraction from individual Google Maps business pages

class BusinessReviewScraper {
    constructor() {
        this.reviewData = [];
        this.isRunning = false;
        this.isBusinessPage = false;
        
        this.init();
    }

    init() {
        // Check if we're on a business page with reviews
        this.isBusinessPage = this.detectBusinessPage();
        
        if (this.isBusinessPage) {
            console.log('🏢 BUSINESS SCRAPER: Business page detected');
            this.setupMessageListener();
        }
    }

    detectBusinessPage() {
        const url = window.location.href;
        // Check for Google Maps business URLs
        return url.includes('google.com/maps') && 
               (url.includes('/place/') || url.includes('/@')) &&
               !url.includes('/search/') &&
               !url.includes('/localservices/prolist');
    }

    setupMessageListener() {
        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            console.log('🔔 BUSINESS SCRAPER: Received message:', request);
            
            const messageType = request.action || request.type;
            console.log('🎯 BUSINESS SCRAPER: Message type:', messageType);
            
            switch (messageType) {
                case 'detectBusinessPage':
                    sendResponse({
                        isBusinessPage: this.isBusinessPage,
                        url: window.location.href
                    });
                    break;
                    
                case 'executeBusinessReviewExtraction':
                    console.log('🚀 BUSINESS SCRAPER: Received executeBusinessReviewExtraction message');
                    this.executeExtraction()
                        .then(result => {
                            console.log('✅ BUSINESS SCRAPER: Review extraction completed:', result);
                            sendResponse(result);
                        })
                        .catch(error => {
                            console.error('❌ BUSINESS SCRAPER: Review extraction failed:', error);
                            sendResponse({ error: error.message });
                        });
                    return true;
                    
                case 'stopBusinessReviewExtraction':
                    this.stopExtraction();
                    sendResponse({ success: true });
                    break;
                    
                case 'exportBusinessReviewCSV':
                    try {
                        const csvContent = this.exportToCSV();
                        sendResponse({ 
                            success: true,
                            csvContent: csvContent 
                        });
                    } catch (error) {
                        sendResponse({ error: error.message });
                    }
                    break;
                    
                default:
                    sendResponse({ error: 'Unknown action' });
            }
        });
    }

    // Execute the read more script and extract reviews
    async executeExtraction() {
        console.log('🔍 BUSINESS SCRAPER: Starting review extraction...');
        
        if (this.isRunning) {
            console.log('⚠️ BUSINESS SCRAPER: Extraction already in progress');
            return { error: 'Extraction already in progress' };
        }

        this.isRunning = true;
        
        try {
            // Step 1: Navigate to reviews if not already there
            console.log('📍 BUSINESS SCRAPER: Step 1 - Ensuring we\'re on the reviews section...');
            await this.navigateToReviews();

            // Step 2: Run the read more script to expand all reviews
            console.log('🔽 BUSINESS SCRAPER: Step 2 - Expanding all "More" buttons...');
            await this.runReadMoreScript();

            // Wait for expansions to complete
            await this.wait(3000);

            // Step 3: Extract all review data
            console.log('📊 BUSINESS SCRAPER: Step 3 - Extracting review data...');
            await this.extractAllReviews();

            console.log(`🎉 BUSINESS SCRAPER: Extraction completed! Found ${this.reviewData.length} reviews`);
            
            this.isRunning = false;
            return {
                success: true,
                reviewCount: this.reviewData.length,
                reviews: this.reviewData
            };

        } catch (error) {
            console.error('💥 BUSINESS SCRAPER: Error during extraction:', error);
            this.isRunning = false;
            return {
                error: error.message,
                reviewCount: this.reviewData.length,
                reviews: this.reviewData
            };
        }
    }

    // Navigate to reviews section if not already there
    async navigateToReviews() {
        console.log('📍 BUSINESS SCRAPER: Checking if we need to navigate to reviews...');
        
        // Try multiple selectors for the Reviews tab on Google Maps business pages
        const reviewsTabSelectors = [
            'button[data-value="Reviews"]',                    // Most common selector
            'button[aria-label*="Reviews"]',                   // Aria-label based
            'button[aria-label*="reviews"]',                   // Lowercase variant
            '[role="tab"][aria-label*="Reviews"]',             // Tab role with Reviews
            '[role="tab"][aria-label*="reviews"]',             // Tab role lowercase
            'button:contains("Reviews")',                      // Text content (needs custom handling)
            'button[data-tab-index="2"]',                      // Reviews tab is often index 2
            'div[role="tab"]:contains("Reviews")',             // Div tab elements
            '[jsaction*="reviews"]',                           // JavaScript action based
            '.tab-reviews',                                    // Class-based selector
            'button.review-tab'                                // Button with review class
        ];
        
        let reviewsTab = null;
        let selectorUsed = '';
        
        for (const selector of reviewsTabSelectors) {
            if (selector.includes(':contains')) {
                // Handle :contains manually since it's not supported in querySelectorAll
                const elements = document.querySelectorAll(selector.replace(':contains("Reviews")', ''));
                for (const element of elements) {
                    if (element.textContent && element.textContent.toLowerCase().includes('reviews')) {
                        reviewsTab = element;
                        selectorUsed = selector;
                        break;
                    }
                }
            } else {
                reviewsTab = document.querySelector(selector);
                if (reviewsTab) {
                    selectorUsed = selector;
                    break;
                }
            }
            
            if (reviewsTab) break;
        }
        
        if (reviewsTab) {
            console.log(`📍 BUSINESS SCRAPER: Found Reviews tab using selector: ${selectorUsed}`);
            
            // Check if already selected
            const isSelected = reviewsTab.getAttribute('aria-selected') === 'true' ||
                              reviewsTab.classList.contains('selected') ||
                              reviewsTab.classList.contains('active');
                              
            if (isSelected) {
                console.log('📍 BUSINESS SCRAPER: Reviews tab already selected');
            } else {
                console.log('📍 BUSINESS SCRAPER: Clicking Reviews tab...');
                reviewsTab.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await this.wait(500); // Brief pause before clicking
                reviewsTab.click();
                await this.wait(3000); // Wait for reviews to load
                console.log('📍 BUSINESS SCRAPER: Reviews tab clicked, waiting for content...');
            }
        } else {
            console.log('📍 BUSINESS SCRAPER: Reviews tab not found. Checking current page state...');
            
            // Check if we're already on reviews by looking for review containers
            const reviewContainers = document.querySelectorAll('div.jftiEf, div.MyEned, [data-review-id]');
            if (reviewContainers.length > 0) {
                console.log(`📍 BUSINESS SCRAPER: Found ${reviewContainers.length} review containers - assuming already on reviews`);
            } else {
                console.warn('📍 BUSINESS SCRAPER: Could not find Reviews tab or existing reviews. User may need to manually navigate to Reviews.');
            }
        }
    }

    // Run the read more script for business pages
    async runReadMoreScript() {
        console.log('🔽 BUSINESS SCRAPER: Running read more script...');
        
        const clickElements = () => {
            // Comprehensive "More" button selectors for Google Maps review pages
            const moreButtonSelectors = [
                'button.w8nwRe.kyuRq',        // Classic selector
                'a.MtCSLb',                   // Classic link selector
                'a.review-more-link',         // Classic review more link
                'button[aria-label*="more"]', // Aria-label based (lowercase)
                'button[aria-label*="More"]', // Aria-label based (uppercase)
                'button.w8nwRe',              // Simplified button selector
                '[jsaction*="more"]',         // JavaScript action based
                'button:contains("more")',    // Text content based (needs custom handling)
                'button:contains("More")',    // Text content based capitalized
                'span.w8nwRe',                // Span version of more button
                '.review-more-link',          // Any element with review-more-link class
                '[data-value="more"]'         // Data attribute based
            ];

            let totalClicked = 0;
            
            for (const selector of moreButtonSelectors) {
                let elements;
                
                if (selector.includes(':contains')) {
                    // Handle :contains manually
                    const baseSelector = selector.replace(':contains("more")', '').replace(':contains("More")', '');
                    const allElements = document.querySelectorAll(baseSelector);
                    elements = Array.from(allElements).filter(el => 
                        el.textContent && (
                            el.textContent.toLowerCase().includes('more') ||
                            el.textContent.toLowerCase().includes('see more') ||
                            el.textContent.toLowerCase().includes('show more')
                        )
                    );
                } else {
                    elements = document.querySelectorAll(selector);
                }

                console.log(`🔽 BUSINESS SCRAPER: Found ${elements.length} elements with selector '${selector}'`);

                elements.forEach(function(element) {
                    try {
                        // Only click if element is visible and not already clicked
                        if (element.offsetParent !== null && !element.classList.contains('clicked-by-scraper')) {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            setTimeout(() => {
                                element.click();
                                element.classList.add('clicked-by-scraper'); // Mark as clicked to avoid duplicates
                                totalClicked++;
                            }, 100);
                        }
                    } catch (error) {
                        console.warn(`🔽 BUSINESS SCRAPER: Error clicking element with selector '${selector}':`, error);
                    }
                });
            }

            console.log(`👆 BUSINESS SCRAPER: Clicked ${totalClicked} "More" buttons/links`);
            return totalClicked;
        };

        // Execute the click function with a delay
        return new Promise((resolve) => {
            setTimeout(() => {
                const clickedCount = clickElements();
                resolve(clickedCount);
            }, 2000); // 2 second delay
        });
    }

    // Extract all review data from the page
    async extractAllReviews() {
        console.log('📊 BUSINESS SCRAPER: Extracting all reviews...');
        this.reviewData = [];

        // Try multiple selectors for different Google Maps page layouts
        let allContainers = [];
        let selectorUsed = '';
        
        // Primary selectors for Google Maps single business pages
        const selectorOptions = [
            'div.jftiEf',         // Modern Google Maps review containers
            'div.MyEned',         // Previous Google Maps review containers
            'div.OA1nbd',         // ProList review containers (fallback)
            '[jsaction*="review"]', // Any element with review actions
            '[data-review-id]',    // Elements with review ID attribute
            'div[role="listitem"]' // Review list items
        ];
        
        for (const selector of selectorOptions) {
            allContainers = document.querySelectorAll(selector);
            console.log(`📊 BUSINESS SCRAPER: Trying selector '${selector}' - found ${allContainers.length} containers`);
            
            if (allContainers.length > 0) {
                selectorUsed = selector;
                break;
            }
        }

        console.log(`📊 BUSINESS SCRAPER: Using selector '${selectorUsed}' with ${allContainers.length} potential review containers`);

        if (allContainers.length === 0) {
            throw new Error('No review containers found on the page. Make sure you are on the Reviews tab and reviews have loaded.');
        }

        // Filter out owner responses
        const reviewContainers = Array.from(allContainers).filter((container, index) => {
            console.log(`📊 BUSINESS SCRAPER: Checking container ${index + 1} for owner response...`);
            
            // Check if this container is part of an owner response
            const ownerResponseElement = this.findOwnerResponseIndicator(container);
            
            if (ownerResponseElement) {
                console.log('📊 BUSINESS SCRAPER: ❌ This container is an owner response (not a review)');
                return false; // Exclude owner responses
            }
            
            console.log(`📊 BUSINESS SCRAPER: ✅ Container ${index + 1} is a valid review`);
            return true; // Include actual reviews
        });

        console.log(`📊 BUSINESS SCRAPER: After filtering out owner responses: ${reviewContainers.length} reviews with text`);

        if (reviewContainers.length === 0) {
            console.warn('📊 BUSINESS SCRAPER: No reviews found after filtering. This might indicate:');
            console.warn('  1. All content was filtered as owner responses');
            console.warn('  2. Reviews haven\'t loaded yet');
            console.warn('  3. Page structure has changed');
            
            // Return the total containers count to help with debugging
            console.log(`📊 BUSINESS SCRAPER: Original containers found: ${allContainers.length}`);
            
            // Extract from all containers as fallback (without filtering)
            console.log('📊 BUSINESS SCRAPER: Attempting extraction from all containers as fallback...');
            return this.extractReviewsFromContainers(Array.from(allContainers));
        }

        return this.extractReviewsFromContainers(reviewContainers);
    }

    // Find owner response indicator for a given container (same logic as single-review-scraper)
    findOwnerResponseIndicator(container) {
        // Check for owner response indicators using the same logic as single-review-scraper
        
        // Method 1: Check if this container itself contains the owner response indicator
        const ownerResponseSpan = container.querySelector('span.nM6d2c');
        if (ownerResponseSpan) {
            const spanText = ownerResponseSpan.textContent.toLowerCase();
            if (spanText.includes('response from the owner') || 
                spanText.includes('response from owner') ||
                spanText.includes('owner response')) {
                console.log('📊 BUSINESS SCRAPER: Found owner response indicator (nM6d2c) in container');
                return ownerResponseSpan;
            }
        }
        
        // Method 2: Check parent elements
        const parentElement = container.parentElement;
        if (parentElement) {
            const parentOwnerSpan = parentElement.querySelector('span.nM6d2c');
            if (parentOwnerSpan) {
                const parentSpanText = parentOwnerSpan.textContent.toLowerCase();
                if (parentSpanText.includes('response from the owner') || 
                    parentSpanText.includes('response from owner') ||
                    parentSpanText.includes('owner response')) {
                    console.log('📊 BUSINESS SCRAPER: Found owner response indicator (nM6d2c) in parent');
                    return parentOwnerSpan;
                }
            }
        }
        
        // Method 3: Check previous siblings
        let currentElement = container.previousElementSibling;
        let siblingCheckCount = 0;
        while (currentElement && siblingCheckCount < 3) {
            const siblingOwnerSpan = currentElement.querySelector('span.nM6d2c');
            if (siblingOwnerSpan) {
                const siblingSpanText = siblingOwnerSpan.textContent.toLowerCase();
                if (siblingSpanText.includes('response from the owner') || 
                    siblingSpanText.includes('response from owner') ||
                    siblingSpanText.includes('owner response')) {
                    console.log('📊 BUSINESS SCRAPER: Found owner response indicator (nM6d2c) in sibling');
                    return siblingOwnerSpan;
                }
            }
            currentElement = currentElement.previousElementSibling;
            siblingCheckCount++;
        }
        
        // Method 4: Check container text content
        const containerText = container.textContent.toLowerCase();
        if (containerText.includes('response from the owner') || 
            containerText.includes('response from owner')) {
            console.log('📊 BUSINESS SCRAPER: Found owner response text in container content');
            return container;
        }
        
        return null; // No owner response indicator found
    }

    // Extract reviews from the found containers
    extractReviewsFromContainers(containers) {
        console.log(`📊 BUSINESS SCRAPER: Processing ${containers.length} review containers...`);

        containers.forEach((container, index) => {
            try {
                const reviewData = this.extractSingleReview(container, index + 1);
                if (reviewData) {
                    this.reviewData.push(reviewData);
                }
            } catch (error) {
                console.error(`❌ BUSINESS SCRAPER: Error extracting review ${index + 1}:`, error);
            }
        });

        console.log(`✅ BUSINESS SCRAPER: Successfully extracted ${this.reviewData.length} reviews with text`);
        return this.reviewData.length;
    }

    // Extract data from a single review container (same logic as single-review-scraper)
    extractSingleReview(container, index) {
        const reviewData = {
            id: index,
            reviewText: '',
            reviewerName: '',
            rating: null,
            date: '',
            helpfulCount: null,
            response: '',
            photoCount: 0,
            extractedAt: new Date().toISOString()
        };

        try {
            // Extract review text from the MyEned div (business page specific)
            const reviewTextElement = container.querySelector('span.wiI7pd');
            if (reviewTextElement) {
                reviewData.reviewText = reviewTextElement.textContent.trim();
            } else {
                // Fallback: get text from the container itself
                reviewData.reviewText = container.textContent.trim();
            }

            // Extract reviewer name
            const reviewerNameElement = container.closest('[data-review-id]')?.querySelector('div.d4r55') || 
                                      this.findReviewerName(container);
            if (reviewerNameElement) {
                reviewData.reviewerName = reviewerNameElement.textContent.trim();
            }

            // Extract rating
            reviewData.rating = this.extractRating(container);

            // Extract date
            reviewData.date = this.extractDate(container);

            // Extract helpful count
            reviewData.helpfulCount = this.extractHelpfulCount(container);

            // Extract business response
            reviewData.response = this.extractBusinessResponse(container);

            // Count photos
            reviewData.photoCount = this.countReviewPhotos(container);

            console.log(`📝 BUSINESS SCRAPER: Extracted review ${index}:`, {
                reviewer: reviewData.reviewerName,
                rating: reviewData.rating,
                textLength: reviewData.reviewText.length,
                hasResponse: !!reviewData.response
            });

            return reviewData;

        } catch (error) {
            console.error(`❌ BUSINESS SCRAPER: Error extracting review ${index}:`, error);
            return null;
        }
    }

    // Find reviewer name in the business page structure
    findReviewerName(container) {
        const possibleSelectors = [
            '.d4r55',
            '.reviewer-name',
            '[data-reviewer]',
            '.review-author',
            '.author-name'
        ];

        // Check the container and its parent elements
        let currentElement = container;
        let checkCount = 0;
        
        while (currentElement && checkCount < 5) {
            for (const selector of possibleSelectors) {
                const nameElement = currentElement.querySelector(selector);
                if (nameElement) {
                    return nameElement;
                }
            }
            
            currentElement = currentElement.parentElement;
            checkCount++;
        }

        return null;
    }

    // Extract rating from various possible selectors
    extractRating(container) {
        const ratingSelectors = [
            '[aria-label*="star"]',
            '[aria-label*="Star"]',
            '.review-rating',
            '[data-rating]',
            'span[aria-label*="star"]'
        ];

        // Check container and parent elements
        let currentElement = container;
        let checkCount = 0;
        
        while (currentElement && checkCount < 5) {
            for (const selector of ratingSelectors) {
                const ratingElement = currentElement.querySelector(selector);
                
                if (ratingElement) {
                    const ariaLabel = ratingElement.getAttribute('aria-label');
                    if (ariaLabel) {
                        const ratingMatch = ariaLabel.match(/(\d+)\s*star/i);
                        if (ratingMatch) {
                            return parseInt(ratingMatch[1]);
                        }
                    }

                    const dataRating = ratingElement.getAttribute('data-rating');
                    if (dataRating) {
                        return parseInt(dataRating);
                    }
                }
            }
            
            currentElement = currentElement.parentElement;
            checkCount++;
        }

        return null;
    }

    // Extract date from the review
    extractDate(container) {
        const dateSelectors = [
            '[data-date]',
            '.review-date',
            'span[aria-label*="ago"]',
            'time',
            '[datetime]'
        ];

        // Check container and parent elements
        let currentElement = container;
        let checkCount = 0;
        
        while (currentElement && checkCount < 5) {
            for (const selector of dateSelectors) {
                const dateElement = currentElement.querySelector(selector);
                
                if (dateElement) {
                    return dateElement.textContent.trim() || 
                           dateElement.getAttribute('datetime') || 
                           dateElement.getAttribute('data-date') || '';
                }
            }
            
            // Also look for spans with date-like text
            const spans = currentElement.querySelectorAll('span');
            for (const span of spans) {
                const text = span.textContent.trim();
                if (text.includes('ago') || text.includes('year') || text.includes('month') || text.includes('day')) {
                    return text;
                }
            }
            
            currentElement = currentElement.parentElement;
            checkCount++;
        }

        return '';
    }

    // Extract helpful count
    extractHelpfulCount(container) {
        const helpfulSelectors = [
            '[aria-label*="helpful"]',
            '.helpful-count',
            '[data-helpful]'
        ];

        // Check container and parent elements
        let currentElement = container;
        let checkCount = 0;
        
        while (currentElement && checkCount < 5) {
            for (const selector of helpfulSelectors) {
                const helpfulElement = currentElement.querySelector(selector);
                
                if (helpfulElement) {
                    const text = helpfulElement.textContent;
                    const match = text.match(/(\d+)/);
                    if (match) {
                        return parseInt(match[1]);
                    }
                }
            }
            
            currentElement = currentElement.parentElement;
            checkCount++;
        }

        return null;
    }

    // Extract business response
    extractBusinessResponse(container) {
        const responseSelectors = [
            '.business-response',
            '[data-response]',
            '.owner-response',
            '.response-text'
        ];

        // Check container and parent elements
        let currentElement = container;
        let checkCount = 0;
        
        while (currentElement && checkCount < 5) {
            for (const selector of responseSelectors) {
                const responseElement = currentElement.querySelector(selector);
                
                if (responseElement) {
                    return responseElement.textContent.trim();
                }
            }
            
            currentElement = currentElement.parentElement;
            checkCount++;
        }

        return '';
    }

    // Count photos in the review
    countReviewPhotos(container) {
        const photoSelectors = [
            'img[src*="review"]',
            '.review-photo',
            '[data-photo]',
            'img[alt*="review"]',
            'img'
        ];

        let photoCount = 0;
        let currentElement = container;
        let checkCount = 0;
        
        while (currentElement && checkCount < 3) {
            for (const selector of photoSelectors) {
                const photos = currentElement.querySelectorAll(selector);
                // Filter out profile pictures and other non-review images
                const reviewPhotos = Array.from(photos).filter(img => {
                    const src = img.src || '';
                    const alt = img.alt || '';
                    return !src.includes('default-user') && !alt.includes('profile');
                });
                photoCount += reviewPhotos.length;
            }
            
            currentElement = currentElement.parentElement;
            checkCount++;
        }

        return photoCount;
    }

    // Export reviews to CSV format
    exportToCSV() {
        if (this.reviewData.length === 0) {
            return '';
        }

        const headers = [
            'ID',
            'Reviewer Name',
            'Rating',
            'Date',
            'Review Text',
            'Helpful Count',
            'Business Response',
            'Photo Count',
            'Extracted At'
        ];

        const csvRows = [headers.join(',')];

        this.reviewData.forEach(review => {
            const row = [
                review.id,
                this.escapeCsvValue(review.reviewerName),
                review.rating || '',
                this.escapeCsvValue(review.date),
                this.escapeCsvValue(review.reviewText),
                review.helpfulCount || '',
                this.escapeCsvValue(review.response),
                review.photoCount,
                this.escapeCsvValue(review.extractedAt)
            ];
            csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
    }

    // Escape CSV values
    escapeCsvValue(value) {
        if (value === null || value === undefined) {
            return '';
        }
        
        const stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
    }

    // Utility function to wait
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get current extracted data
    getCurrentData() {
        return {
            reviewCount: this.reviewData.length,
            reviews: this.reviewData,
            isRunning: this.isRunning
        };
    }

    // Stop extraction
    stopExtraction() {
        this.isRunning = false;
        console.log('⏹️ BUSINESS SCRAPER: Extraction stopped');
    }
}

// Initialize the Business Review scraper when the script loads
const businessReviewScraper = new BusinessReviewScraper();

// Make it available globally for debugging
window.businessReviewScraper = businessReviewScraper;

console.log('🏢 BUSINESS SCRAPER: Content script loaded and ready'); 