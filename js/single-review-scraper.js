// Enhanced Single GMB Review Scraper Module with Automated Loading
// This module handles automated scrolling, review loading, expansion, and extraction
// VERSION: 2.1 - Automated Review Loading with Range Support

class SimplifiedReviewScraper {
    constructor() {
        this.reviewData = [];
        this.totalReviewCount = 0;
        this.loadedReviewCount = 0;
        this.version = "2.1-AutomatedLoadingWithRange";
        this.lastScrollPosition = -1; // Track scroll position to detect when we've reached bottom
        this.scrollPositionStableCount = 0; // Count how many times we've seen the same position
        
        // Initialize range utility
        this.rangeUtility = new ReviewRangeUtility();
        
        console.log(`🔍 ENHANCED SCRAPER: Initializing version ${this.version}`);
    }

    // Debug method to check version
    getVersion() {
        console.log(`🔍 ENHANCED SCRAPER: Running version ${this.version}`);
        return this.version;
    }

    // Set the maximum number of reviews to extract
    setMaxReviews(maxReviews) {
        this.rangeUtility.setMaxReviews(maxReviews);
        console.log(`🔍 ENHANCED SCRAPER: ${this.rangeUtility.getStatusMessage()}`);
    }

    // Execute the complete automated extraction process
    async executeExtraction(maxReviews = null) {
        console.log('🔍 ENHANCED SCRAPER: Starting automated review extraction...');
        
        // Set the range if provided
        if (maxReviews !== null) {
            this.setMaxReviews(maxReviews);
        }
        
        try {
            // Step 0: Extract business name first
            console.log('📋 ENHANCED SCRAPER: Step 0 - Extracting business name...');
            this.businessName = await this.extractBusinessName();
            
            // Step 1: Ensure we're on the Reviews tab
            console.log('📋 ENHANCED SCRAPER: Step 1 - Ensuring Reviews tab is active...');
            await this.ensureReviewsTabActive();
            
            // Step 2: Get total review count
            console.log('📊 ENHANCED SCRAPER: Step 2 - Getting total review count...');
            this.totalReviewCount = await this.getTotalReviewCount();
            
            if (this.totalReviewCount === 0) {
                console.log('📊 ENHANCED SCRAPER: No reviews found to extract');
                return {
                    success: true,
                    reviewCount: 0,
                    reviews: [],
                    businessName: this.businessName,
                    message: 'No reviews found on this page',
                    rangeApplied: this.rangeUtility.hasLimit(),
                    maxReviews: this.rangeUtility.getMaxReviews()
                };
            }
            
            // Calculate effective target based on range settings
            const effectiveTarget = this.rangeUtility.getEffectiveTarget(this.totalReviewCount);
            console.log(`📊 ENHANCED SCRAPER: Found ${this.totalReviewCount} total reviews, targeting ${effectiveTarget} reviews`);
            console.log(`📊 ENHANCED SCRAPER: Range settings - ${this.rangeUtility.getStatusMessage()}`);
            
            // Step 3: Automatically load reviews by scrolling (up to the target)
            console.log('🔄 ENHANCED SCRAPER: Step 3 - Loading reviews through automated scrolling...');
            await this.loadAllReviewsThroughScrolling();
            
            // Step 4: Expand all "More" buttons
            console.log('🔽 ENHANCED SCRAPER: Step 4 - Expanding all "More" buttons...');
            await this.runReadMoreScript();

            // Wait for expansions to complete
            await this.wait(3000);

            // Step 5: Extract all review data
            console.log('📊 ENHANCED SCRAPER: Step 5 - Extracting review data...');
            await this.extractAllReviews();

            // Step 6: Apply range limit to results
            let finalReviews = this.reviewData;
            if (this.rangeUtility.hasLimit()) {
                finalReviews = this.rangeUtility.truncateResults(this.reviewData);
                console.log(`📊 ENHANCED SCRAPER: Applied range limit - extracted ${this.reviewData.length}, returning ${finalReviews.length}`);
            }

            console.log(`🎉 ENHANCED SCRAPER: Extraction completed! Found ${finalReviews.length} reviews (${this.rangeUtility.hasLimit() ? 'with limit' : 'no limit'})`);
            
            return {
                success: true,
                reviewCount: finalReviews.length,
                reviews: finalReviews,
                businessName: this.businessName,
                totalExpected: this.totalReviewCount,
                rangeApplied: this.rangeUtility.hasLimit(),
                maxReviews: this.rangeUtility.getMaxReviews(),
                totalAvailable: this.reviewData.length // Total found before range limit
            };

        } catch (error) {
            console.error('💥 ENHANCED SCRAPER: Error during extraction:', error);
            
            // Apply range limit to any data we did collect
            let finalReviews = this.reviewData;
            if (this.rangeUtility.hasLimit()) {
                finalReviews = this.rangeUtility.truncateResults(this.reviewData);
            }
            
            return {
                error: error.message,
                reviewCount: finalReviews.length,
                reviews: finalReviews,
                businessName: this.businessName,
                totalExpected: this.totalReviewCount,
                rangeApplied: this.rangeUtility.hasLimit(),
                maxReviews: this.rangeUtility.getMaxReviews(),
                totalAvailable: this.reviewData.length
            };
        }
    }

    // Extract business name from the page
    async extractBusinessName() {
        console.log('📋 ENHANCED SCRAPER: Starting business name extraction...');
        
        try {
            // Method 1: Try the main business title in the Google Maps business panel
            const businessTitleSelectors = [
                'h1[data-attrid="title"]', // Primary business title
                'h1.DUwDvf.lfPIob', // Alternative title selector
                '[data-attrid="title"] h1', // Title within data attribute
                'h1.x3AX1-LfntMc-header-title-title', // Header title
                '.x3AX1-LfntMc-header-title-title', // Title without h1
                'h1.qrShPb', // Another title selector
                '.qrShPb h1', // Title within qrShPb
                'h1.DUwDvf', // DUwDvf class
                '.DUwDvf h1', // h1 within DUwDvf
                'h1[role="heading"]', // Heading role
                '.SPZz6b h1' // SPZz6b container
            ];
            
            for (const selector of businessTitleSelectors) {
                const titleElement = document.querySelector(selector);
                if (titleElement) {
                    let businessName = titleElement.textContent.trim();
                    businessName = businessName.replace(/\s+/g, ' ').trim();
                    
                    console.log(`📋 ENHANCED SCRAPER: Found element with selector "${selector}": "${businessName}"`);
                    
                    if (businessName && 
                        businessName.length > 2 && 
                        businessName.length < 200 && 
                        businessName !== 'Google Maps' &&
                        businessName !== 'Reviews' &&
                        businessName !== 'Results' &&
                        !businessName.toLowerCase().includes('google maps') &&
                        !businessName.toLowerCase().includes('search') &&
                        !businessName.toLowerCase().includes('menu') &&
                        !businessName.toLowerCase().includes('filter')) {
                        console.log('📋 ENHANCED SCRAPER: ✅ Valid business name extracted:', businessName);
                        return businessName;
                    } else {
                        console.log('📋 ENHANCED SCRAPER: ❌ Invalid business name, continuing search...');
                    }
                }
            }
            
            // Method 2: Try meta content approach
            const metaName = document.querySelector('meta[itemprop="name"]');
            if (metaName) {
                const content = metaName.getAttribute('content');
                console.log('📋 ENHANCED SCRAPER: Found meta itemprop="name":', content);
                
                if (content && content.includes('·')) {
                    // Split by · to separate business name and address
                    const parts = content.split('·');
                    const businessName = parts[0].trim();
                    if (businessName && businessName !== 'Google Maps' && businessName !== 'Results') {
                        console.log('📋 ENHANCED SCRAPER: ✅ Extracted business name from meta content (with ·):', businessName);
                        return businessName;
                    }
                } else if (content && content !== 'Google Maps' && content !== 'Results') {
                    console.log('📋 ENHANCED SCRAPER: ✅ Extracted business name from meta content:', content);
                    return content;
                }
            }
            
            // Method 3: Try title tag
            const titleElement = document.querySelector('title');
            if (titleElement) {
                const titleText = titleElement.textContent.trim();
                console.log('📋 ENHANCED SCRAPER: Found title tag:', titleText);
                
                // Extract business name from title like "Business Name - Google Maps"
                if (titleText.includes(' - Google Maps')) {
                    const businessName = titleText.replace(' - Google Maps', '').trim();
                    if (businessName && businessName !== 'Results' && businessName.length > 2) {
                        console.log('📋 ENHANCED SCRAPER: ✅ Extracted business name from title tag:', businessName);
                        return businessName;
                    }
                }
            }
            
            // Method 4: Try any h1 element with substantial text
            const allH1s = document.querySelectorAll('h1');
            console.log(`📋 ENHANCED SCRAPER: Found ${allH1s.length} h1 elements, checking each...`);
            
            for (const h1 of allH1s) {
                const businessName = h1.textContent?.trim();
                console.log(`📋 ENHANCED SCRAPER: Checking h1 element: "${businessName}"`);
                
                if (businessName && 
                    businessName.length > 2 && 
                    businessName.length < 200 && 
                    businessName !== 'Google Maps' &&
                    businessName !== 'Reviews' &&
                    businessName !== 'Results' &&
                    !businessName.toLowerCase().includes('google maps') &&
                    !businessName.toLowerCase().includes('search') &&
                    !businessName.toLowerCase().includes('menu') &&
                    !businessName.toLowerCase().includes('filter') &&
                    !businessName.toLowerCase().includes('overview')) {
                    console.log('📋 ENHANCED SCRAPER: ✅ Valid business name from h1 fallback:', businessName);
                    return businessName;
                }
            }
            
            // Method 5: Try to find the business name in the URL
            const currentUrl = window.location.href;
            console.log('📋 ENHANCED SCRAPER: Checking URL for business name:', currentUrl);
            
            // Google Maps URLs often contain the business name
            const urlMatch = currentUrl.match(/\/maps\/place\/([^\/\?]+)/);
            if (urlMatch) {
                const urlBusinessName = decodeURIComponent(urlMatch[1]).replace(/\+/g, ' ').trim();
                console.log('📋 ENHANCED SCRAPER: Found business name in URL:', urlBusinessName);
                
                if (urlBusinessName && 
                    urlBusinessName.length > 2 && 
                    urlBusinessName !== 'Results' &&
                    !urlBusinessName.toLowerCase().includes('search')) {
                    console.log('📋 ENHANCED SCRAPER: ✅ Valid business name from URL:', urlBusinessName);
                    return urlBusinessName;
                }
            }
            
            // Method 6: Search in page text for potential business names
            console.log('📋 ENHANCED SCRAPER: Trying to find business name in page content...');
            
            // Look for strong/bold text that might be the business name
            const strongElements = document.querySelectorAll('strong, b, [style*="font-weight"]');
            for (const strong of strongElements) {
                const text = strong.textContent?.trim();
                if (text && 
                    text.length > 2 && 
                    text.length < 100 &&
                    text !== 'Results' &&
                    text !== 'Reviews' &&
                    !text.toLowerCase().includes('google') &&
                    !text.toLowerCase().includes('search') &&
                    !text.match(/^\d+/) && // Not starting with numbers
                    !text.match(/\d+\s*(reviews?|stars?)/i)) { // Not review/rating text
                    console.log('📋 ENHANCED SCRAPER: Potential business name from strong element:', text);
                    return text;
                }
            }
            
            console.log('📋 ENHANCED SCRAPER: ❌ Could not extract business name, using default');
            return 'Unknown Business';
            
        } catch (error) {
            console.error('📋 ENHANCED SCRAPER: Error extracting business name:', error);
            return 'Unknown Business';
        }
    }

    // Ensure the Reviews tab is active and properly loaded
    async ensureReviewsTabActive() {
        console.log('📋 ENHANCED SCRAPER: Ensuring Reviews tab is active...');
        
        try {
            // Look for the Reviews tab using multiple selectors
            const reviewsTabSelectors = [
                'button[data-tab-index="1"]', // Most common
                'button[aria-selected="true"]', // If already active
                'button:contains("Reviews")', // Text-based
                '.jfXkJe', // Alternative Reviews tab class
                '[role="tab"]:contains("Reviews")', // ARIA role
                'button[data-value="1"]' // Data value approach
            ];
            
            let reviewsTab = null;
            for (const selector of reviewsTabSelectors) {
                reviewsTab = document.querySelector(selector);
                if (reviewsTab && reviewsTab.textContent.toLowerCase().includes('review')) {
                    console.log(`📋 ENHANCED SCRAPER: Found Reviews tab with selector: ${selector}`);
                    break;
                }
                reviewsTab = null;
            }
            
            if (!reviewsTab) {
                // Fallback: find by text content
                const allButtons = document.querySelectorAll('button');
                for (const button of allButtons) {
                    if (button.textContent.toLowerCase().includes('review')) {
                        reviewsTab = button;
                        console.log('📋 ENHANCED SCRAPER: Found Reviews tab by text content');
                        break;
                    }
                }
            }
            
            if (reviewsTab) {
                console.log('📋 ENHANCED SCRAPER: Clicking Reviews tab to activate...');
                reviewsTab.click();
                
                // Wait for tab to activate and content to load
                await this.wait(2000);
                
                console.log('📋 ENHANCED SCRAPER: Reviews tab activated');
            } else {
                console.log('📋 ENHANCED SCRAPER: Reviews tab not found, assuming already active');
            }
            
        } catch (error) {
            console.error('📋 ENHANCED SCRAPER: ❌ Error activating Reviews tab:', error);
        }
    }

    // Get the total number of reviews from the specified selector
    async getTotalReviewCount() {
        console.log('📊 ENHANCED SCRAPER: Detecting total review count...');
        
        // Primary selector - the one specified by the user
        const reviewCountElement = document.querySelector('div.fontBodySmall[jslog*="25991"]');
        
        if (reviewCountElement) {
            const reviewText = reviewCountElement.textContent.trim();
            console.log(`📊 ENHANCED SCRAPER: Found review count text: "${reviewText}"`);
            
            // Extract number from text like "60 reviews" or "1 review"
            const match = reviewText.match(/(\d+)\s+reviews?/i);
            if (match) {
                const count = parseInt(match[1], 10);
                console.log(`📊 ENHANCED SCRAPER: Parsed review count: ${count}`);
                return count;
            }
        }
        
        // Fallback selectors if the primary one doesn't work
        const fallbackSelectors = [
            'div.fontBodySmall:contains("review")',
            '[data-value]:contains("review")',
            '.review-count',
            '.reviews-count'
        ];
        
        for (const selector of fallbackSelectors) {
            try {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    const text = element.textContent.trim();
                    const match = text.match(/(\d+)\s+reviews?/i);
                    if (match) {
                        const count = parseInt(match[1], 10);
                        console.log(`📊 ENHANCED SCRAPER: Found review count using fallback: ${count}`);
                        return count;
                    }
                }
            } catch (e) {
                // Continue with next selector
            }
        }
        
        console.log('📊 ENHANCED SCRAPER: Could not determine total review count, proceeding with available reviews');
        return 0;
    }

    // Load all reviews through automated scrolling
    async loadAllReviewsThroughScrolling() {
        console.log('🔄 ENHANCED SCRAPER: Starting automated review loading...');
        console.log(`🔄 ENHANCED SCRAPER: Total reviews expected: ${this.totalReviewCount}`);
        
        // Calculate the effective target based on range settings
        const effectiveTarget = this.rangeUtility.getEffectiveTarget(this.totalReviewCount);
        console.log(`🔄 ENHANCED SCRAPER: Effective target: ${effectiveTarget} reviews`);
        
        // DEBUG: Force execution regardless of count
        console.log('🔄 ENHANCED SCRAPER: DEBUG - Forcing execution to test scrolling methods...');
        
        // First, let's check if the reviews container exists
        const reviewsContainer = document.querySelector('div.m6QErb');
        if (!reviewsContainer) {
            console.error('🔄 ENHANCED SCRAPER: ❌ Reviews container (div.m6QErb) not found! Trying alternative containers...');
            
            // Try alternative containers
            const alternatives = [
                'div.m6QErb.XiKgde',
                '.review-dialog-list',
                '[role="tabpanel"]',
                '.scrollable-container',
                document.body
            ];
            
            for (const selector of alternatives) {
                const alt = typeof selector === 'string' ? document.querySelector(selector) : selector;
                if (alt) {
                    console.log(`🔄 ENHANCED SCRAPER: Found alternative container: ${selector}`);
                    await this.tryAllScrollingMethods(alt);
                    return;
                }
            }
            return;
        }
        
        console.log('🔄 ENHANCED SCRAPER: ✅ Found reviews container (div.m6QErb)');
        console.log(`🔄 ENHANCED SCRAPER: Container dimensions - scrollTop: ${reviewsContainer.scrollTop}, scrollHeight: ${reviewsContainer.scrollHeight}, clientHeight: ${reviewsContainer.clientHeight}`);
        
        // CRITICAL: Activate the reviews container by clicking in a safe empty area
        console.log('🔄 ENHANCED SCRAPER: Step 1 - Activating reviews container...');
        await this.activateReviewsContainer();
        
        // Try all scrolling methods
        await this.tryAllScrollingMethods(reviewsContainer);
        
        let previousCount = 0;
        let stableCount = 0;
        let maxAttempts = Math.ceil(effectiveTarget / 10) + 5; // Use effective target for attempt calculation
        let attempts = 0;
        
        console.log(`🔄 ENHANCED SCRAPER: Will attempt up to ${maxAttempts} scrolling attempts (targeting ${effectiveTarget} reviews)`);
        
        while (attempts < maxAttempts) {
            attempts++;
            console.log(`🔄 ENHANCED SCRAPER: === Scroll Attempt ${attempts}/${maxAttempts} ===`);
            
            // Check current scroll position to detect if we've reached bottom
            const currentScrollPosition = this.getCurrentScrollPosition();
            console.log(`🔄 ENHANCED SCRAPER: Current scroll position: ${currentScrollPosition}px`);
            
            // Count current visible reviews (including empty ones)
            const currentReviews = this.countCurrentVisibleReviews();
            console.log(`🔄 ENHANCED SCRAPER: Attempt ${attempts} - Found ${currentReviews} visible reviews (target: ${effectiveTarget})`);
            
            // Update global progress if available
            if (window.updateGlobalProgress && effectiveTarget > 0) {
                window.updateGlobalProgress(currentReviews, effectiveTarget, 
                    `Loading reviews: ${currentReviews}/${effectiveTarget}`);
                console.log(`🔄 ENHANCED SCRAPER: Updated global progress: ${currentReviews}/${effectiveTarget}`);
            }
            
            // Check if we've reached our target (either range limit or total available)
            if (this.rangeUtility.shouldStopScraping(currentReviews) || currentReviews >= effectiveTarget) {
                console.log(`🎉 ENHANCED SCRAPER: Reached target! (${currentReviews}/${effectiveTarget})`);
                break;
            }
            
            // Check if scroll position hasn't changed (we've reached the bottom)
            if (currentScrollPosition === this.lastScrollPosition) {
                this.scrollPositionStableCount++;
                console.log(`🔄 ENHANCED SCRAPER: Scroll position unchanged (${currentScrollPosition}px), stability count: ${this.scrollPositionStableCount}/2`);
                if (this.scrollPositionStableCount >= 2) {
                    console.log(`🔄 ENHANCED SCRAPER: Scroll position stable at ${currentScrollPosition}px for 2 attempts, reached bottom - stopping`);
                    break;
                }
            } else {
                this.scrollPositionStableCount = 0;
                this.lastScrollPosition = currentScrollPosition;
                console.log(`🔄 ENHANCED SCRAPER: Scroll position changed to ${currentScrollPosition}px, continuing...`);
            }
            
            // Check if count hasn't changed (might be stuck)
            if (currentReviews === previousCount) {
                stableCount++;
                console.log(`🔄 ENHANCED SCRAPER: Count unchanged (${currentReviews}), stability count: ${stableCount}/3`);
                if (stableCount >= 3) {
                    console.log(`🔄 ENHANCED SCRAPER: Review count stable at ${currentReviews} for 3 attempts, assuming all loaded`);
                    break;
                }
            } else {
                stableCount = 0;
                previousCount = currentReviews;
                console.log(`🔄 ENHANCED SCRAPER: Count increased from ${previousCount} to ${currentReviews}, continuing...`);
            }
            
            // Try all scrolling methods for this attempt
            console.log(`🔄 ENHANCED SCRAPER: About to scroll (attempt ${attempts})...`);
            try {
                await this.tryAllScrollingMethods(reviewsContainer);
                console.log(`🔄 ENHANCED SCRAPER: All scrolling methods completed, waiting 1.8 seconds...`);
            } catch (scrollError) {
                console.error(`🔄 ENHANCED SCRAPER: ❌ Scroll error on attempt ${attempts}:`, scrollError);
            }
            
            // Wait for new reviews to load (1.5-2 seconds as specified)
            await this.wait(1800);
            console.log(`🔄 ENHANCED SCRAPER: Wait completed, checking for new reviews...`);
        }
        
        const finalCount = this.countCurrentVisibleReviews();
        console.log(`🔄 ENHANCED SCRAPER: === Scrolling Phase Complete ===`);
        console.log(`🔄 ENHANCED SCRAPER: Final count: ${finalCount} reviews (target: ${effectiveTarget})`);
        console.log(`🔄 ENHANCED SCRAPER: Attempts used: ${attempts}/${maxAttempts}`);
        console.log(`🔄 ENHANCED SCRAPER: Range settings: ${this.rangeUtility.getStatusMessage()}`);
        this.loadedReviewCount = finalCount;
        
        // Update progress to complete
        if (window.setGlobalProgressComplete) {
            const message = this.rangeUtility.hasLimit() 
                ? `Loaded ${Math.min(finalCount, this.rangeUtility.getMaxReviews())} reviews (limited)`
                : `All ${finalCount} reviews loaded`;
            window.setGlobalProgressComplete(message);
        }
    }

    // Try the one working scrolling method
    async tryAllScrollingMethods(container) {
        console.log('📜 ENHANCED SCRAPER: Using SMART Review Parent Scrolling (the only working method)...');
        
        // Method 0: SMART SCROLL - Find actual review parents and scroll from there (WORKING!)
        await this.scrollMethodSmartReviewParent();
        
        console.log('📜 ENHANCED SCRAPER: Scrolling method completed');
    }

    // Method 0: SMART SCROLL - Find actual review parents and scroll from there (WORKING!)
    async scrollMethodSmartReviewParent() {
        console.log('📜 ENHANCED SCRAPER: Method 0 - SMART Review Parent Scrolling...');
        
        try {
            // Find the existing reviews (we know these exist)
            const existingReviews = document.querySelectorAll('div.MyEned');
            console.log(`📜 ENHANCED SCRAPER: Found ${existingReviews.length} existing reviews to analyze`);
            
            if (existingReviews.length === 0) {
                console.log('📜 ENHANCED SCRAPER: No existing reviews found, skipping smart scroll');
                return;
            }
            
            // Get the last review - this is our reference point
            const lastReview = existingReviews[existingReviews.length - 1];
            console.log('📜 ENHANCED SCRAPER: Using last review as scroll target...');
            
            // Get the position of the last review
            const lastReviewRect = lastReview.getBoundingClientRect();
            console.log(`📜 ENHANCED SCRAPER: Last review position: top=${lastReviewRect.top}, bottom=${lastReviewRect.bottom}`);
            
            // Method 1: Scroll to the last review first (basic positioning)
            console.log('📜 ENHANCED SCRAPER: Step 1 - Scrolling last review into view...');
            lastReview.scrollIntoView({ behavior: 'smooth', block: 'start' });
            await this.wait(500);
            
            // Method 2: AGGRESSIVE SCROLLING - Go way beyond the last review
            console.log('📜 ENHANCED SCRAPER: Step 2 - AGGRESSIVE scrolling beyond current content...');
            
            // Calculate how far beyond we need to scroll to trigger lazy loading
            const viewportHeight = window.innerHeight;
            const aggressiveDistance = viewportHeight * 2; // Scroll 2 viewport heights beyond
            
            console.log(`📜 ENHANCED SCRAPER: Viewport height: ${viewportHeight}px, aggressive distance: ${aggressiveDistance}px`);
            
            // Create multiple temporary scroll targets at increasing distances
            const scrollTargets = [];
            const distances = [500, 1000, 1500, 2000, aggressiveDistance]; // Multiple distances to try
            
            for (let i = 0; i < distances.length; i++) {
                const tempTarget = document.createElement('div');
                tempTarget.style.height = '1px';
                tempTarget.style.visibility = 'hidden';
                tempTarget.style.position = 'relative';
                tempTarget.style.top = `${distances[i]}px`; // Position it far below
                tempTarget.setAttribute('data-scroll-target', i);
                
                // Insert after the last review
                lastReview.parentElement.appendChild(tempTarget);
                scrollTargets.push(tempTarget);
                
                console.log(`📜 ENHANCED SCRAPER: Created scroll target ${i} at distance ${distances[i]}px`);
            }
            
            // Scroll to each target progressively to force maximum content loading
            for (let i = 0; i < scrollTargets.length; i++) {
                console.log(`📜 ENHANCED SCRAPER: Scrolling to aggressive target ${i} (${distances[i]}px beyond)...`);
                
                // Use multiple scrolling techniques for maximum effectiveness
                scrollTargets[i].scrollIntoView({ behavior: 'smooth', block: 'center' });
                await this.wait(300);
                
                // Also try direct window scrolling to the calculated position
                const targetY = window.pageYOffset + distances[i];
                window.scrollTo({ top: targetY, behavior: 'smooth' });
                await this.wait(300);
                
                // Trigger scroll events to ensure lazy loading activates
                window.dispatchEvent(new Event('scroll'));
                document.dispatchEvent(new Event('scroll'));
                await this.wait(200);
            }
            
            // Clean up temporary elements
            scrollTargets.forEach(target => target.remove());
            console.log('📜 ENHANCED SCRAPER: Cleaned up temporary scroll targets');
            
            // Method 3: WINDOW SCROLL TO MAXIMUM - Scroll to absolute bottom of current content
            console.log('📜 ENHANCED SCRAPER: Step 3 - Window scroll to maximum content bottom...');
            
            // Get the total document height and scroll near the bottom
            const documentHeight = Math.max(
                document.body.scrollHeight,
                document.body.offsetHeight,
                document.documentElement.clientHeight,
                document.documentElement.scrollHeight,
                document.documentElement.offsetHeight
            );
            
            console.log(`📜 ENHANCED SCRAPER: Document height: ${documentHeight}px`);
            
            // Scroll to 90% of document height first, then 100%
            const ninetyPercent = documentHeight * 0.9;
            console.log(`📜 ENHANCED SCRAPER: Scrolling to 90% of document (${ninetyPercent}px)...`);
            window.scrollTo({ top: ninetyPercent, behavior: 'smooth' });
            await this.wait(800);
            
            console.log(`📜 ENHANCED SCRAPER: Scrolling to 100% of document (${documentHeight}px)...`);
            window.scrollTo({ top: documentHeight, behavior: 'smooth' });
            await this.wait(800);
            
            // Method 4: SAFE RIGHT-SIDE clicking at the very bottom
            console.log('📜 ENHANCED SCRAPER: Step 4 - SAFE RIGHT-SIDE clicking at document bottom...');
            
            // Calculate SAFE right-side position at the bottom of the document
            const safeRightX = window.innerWidth * 0.85; // 85% from left = far right side
            const bottomY = window.innerHeight - 100; // Near bottom of viewport
            
            console.log(`📜 ENHANCED SCRAPER: SAFE RIGHT-SIDE clicking at (${safeRightX}, ${bottomY})`);
            
            // Check if the position is safe (no links/buttons)
            const elementAtPosition = document.elementFromPoint(safeRightX, bottomY);
            if (elementAtPosition) {
                // Check if this element or its parents are unsafe (links, buttons with hrefs, etc.)
                const isUnsafeElement = elementAtPosition.closest('a, button[href], [onclick], [data-href], [role="link"], [role="button"][onclick]');
                
                if (!isUnsafeElement) {
                    console.log(`📜 ENHANCED SCRAPER: SAFE RIGHT-SIDE element found: ${elementAtPosition.tagName}.${elementAtPosition.className}`);
                    
                    // Create safe click event
                    const safeClickEvent = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: safeRightX,
                        clientY: bottomY
                    });
                    
                    elementAtPosition.dispatchEvent(safeClickEvent);
                    await this.wait(300);
                } else {
                    console.log('📜 ENHANCED SCRAPER: Unsafe element detected at bottom right position, skipping click');
                    console.log(`📜 ENHANCED SCRAPER: Unsafe element: ${isUnsafeElement.tagName}.${isUnsafeElement.className}`);
                }
            } else {
                console.log('📜 ENHANCED SCRAPER: No element found at safe bottom right position');
            }
            
            // Method 5: Final aggressive scroll events and page end detection
            console.log('📜 ENHANCED SCRAPER: Step 5 - Final scroll events and page end detection...');
            
            // Try scrolling to an impossibly large position to ensure we hit the absolute bottom
            window.scrollTo({ top: 999999, behavior: 'smooth' });
            await this.wait(500);
            
            // Trigger multiple scroll events to ensure lazy loading is fully activated
            for (let i = 0; i < 3; i++) {
                window.dispatchEvent(new Event('scroll'));
                document.dispatchEvent(new Event('scroll'));
                await this.wait(200);
            }
            
            console.log('📜 ENHANCED SCRAPER: ✅ Aggressive smart review scrolling completed successfully');
            
        } catch (error) {
            console.log('📜 ENHANCED SCRAPER: Aggressive smart review scrolling failed:', error);
        }
    }

    // Activate the reviews container by clicking in a safe empty area
    async activateReviewsContainer() {
        console.log('👆 ENHANCED SCRAPER: Activating reviews container...');
        
        try {
            // Find the Reviews tab to get our reference point
            const reviewsTab = document.querySelector('button[data-tab-index="1"]') || 
                              document.querySelector('button:contains("Reviews")') ||
                              document.querySelector('[aria-selected="true"]') ||
                              document.querySelector('.jfXkJe'); // Alternative Reviews tab selector
            
            if (reviewsTab) {
                console.log('👆 ENHANCED SCRAPER: Found Reviews tab, calculating SAFE RIGHT-SIDE click position...');
                const tabRect = reviewsTab.getBoundingClientRect();
                
                // SAFE CLICKING: Click on the RIGHT side of the viewport, avoiding any links
                // Use 80% of the viewport width to ensure we're on the right side
                const safeRightX = window.innerWidth * 0.8; // 80% from left = right side
                const clickY = tabRect.bottom + 40; // 40px below Reviews tab
                
                console.log(`👆 ENHANCED SCRAPER: SAFE RIGHT-SIDE clicking at (${safeRightX}, ${clickY}) - right side of viewport`);
                
                // Create a click event at the safe right-side position
                const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: safeRightX,
                    clientY: clickY
                });
                
                // Find the element at that safe position and check if it's safe to click
                const elementAtPosition = document.elementFromPoint(safeRightX, clickY);
                if (elementAtPosition) {
                    // Check if this element or its parents are links/buttons that could open tabs
                    const isUnsafeElement = elementAtPosition.closest('a, button[href], [onclick], [data-href]');
                    
                    if (!isUnsafeElement) {
                        console.log(`👆 ENHANCED SCRAPER: SAFE element found: ${elementAtPosition.tagName}.${elementAtPosition.className}`);
                        elementAtPosition.dispatchEvent(clickEvent);
                        elementAtPosition.click(); // Backup click
                    } else {
                        console.log('👆 ENHANCED SCRAPER: Unsafe element detected (link/button), trying container focus instead...');
                        // Fallback: just focus the reviews container without clicking
                        const reviewsContainer = document.querySelector('div.m6QErb');
                        if (reviewsContainer) {
                            reviewsContainer.focus();
                            reviewsContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }
                } else {
                    console.log('👆 ENHANCED SCRAPER: No element found at safe position, trying container focus...');
                    // Fallback: just focus the reviews container
                    const reviewsContainer = document.querySelector('div.m6QErb');
                    if (reviewsContainer) {
                        reviewsContainer.focus();
                        reviewsContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }
            } else {
                console.log('👆 ENHANCED SCRAPER: Reviews tab not found, trying direct container activation...');
                // Fallback: focus the reviews container directly without risky clicking
                const reviewsContainer = document.querySelector('div.m6QErb');
                if (reviewsContainer) {
                    console.log('👆 ENHANCED SCRAPER: Focusing reviews container directly...');
                    reviewsContainer.focus();
                    reviewsContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
            
            // Wait for activation to take effect
            await this.wait(500);
            console.log('👆 ENHANCED SCRAPER: Container activation completed');
            
        } catch (error) {
            console.error('👆 ENHANCED SCRAPER: ❌ Error during container activation:', error);
        }
    }

    // Count currently visible reviews on the page
    countCurrentVisibleReviews() {
        console.log('📜 ENHANCED SCRAPER: Counting reviews - using GLOBAL search first (like extraction)...');
        
        // FIXED: Use the same approach as extraction - search globally first
        // Try MyEned containers first (what actually works during extraction)
        let reviewContainers = document.querySelectorAll('div.MyEned');
        console.log(`📜 ENHANCED SCRAPER: Global search found ${reviewContainers.length} MyEned containers`);
        
        // If no MyEned containers, try other formats
        if (reviewContainers.length === 0) {
            reviewContainers = document.querySelectorAll('div.jftiEf');
            console.log(`📜 ENHANCED SCRAPER: Global search found ${reviewContainers.length} jftiEf containers`);
        }
        
        if (reviewContainers.length === 0) {
            reviewContainers = document.querySelectorAll('div.OA1nbd');
            console.log(`📜 ENHANCED SCRAPER: Global search found ${reviewContainers.length} OA1nbd containers`);
        }
        
        // Filter out owner responses but INCLUDE reviews with no text content
        const actualReviews = Array.from(reviewContainers).filter(container => {
            // Check for owner response indicators
            const ownerResponseSpan = container.querySelector('span.nM6d2c');
            if (ownerResponseSpan) {
                const spanText = ownerResponseSpan.textContent.toLowerCase();
                if (spanText.includes('response from the owner') || 
                    spanText.includes('response from owner') ||
                    spanText.includes('owner response')) {
                    return false; // Exclude owner responses
                }
            }
            
            // Check container text for owner response indicators
            const containerText = container.textContent.toLowerCase();
            if (containerText.includes('response from the owner') || 
                containerText.includes('response from owner')) {
                return false;
            }
            
            // IMPORTANT: Include ALL review containers, even if they have no text content
            // This ensures our count matches what will be extracted
            return true;
        });
        
        console.log(`📜 ENHANCED SCRAPER: Global search after filtering: ${actualReviews.length} actual reviews (including empty ones)`);
        
        // ALSO check inside the container (as backup)
        const reviewsContainer = document.querySelector('div.m6QErb');
        if (reviewsContainer) {
            console.log('📜 ENHANCED SCRAPER: Also checking inside m6QErb container...');
            
            const containerReviews = reviewsContainer.querySelectorAll('div.MyEned, div.jftiEf, div.OA1nbd');
            console.log(`📜 ENHANCED SCRAPER: Found ${containerReviews.length} review containers inside m6QErb`);
            
            // Use the higher count (global vs container) - include empty reviews
            const containerCount = Array.from(containerReviews).filter(container => {
                const containerText = container.textContent.toLowerCase();
                // Only exclude owner responses, include everything else (including empty reviews)
                return !containerText.includes('response from the owner');
            }).length;
            
            const finalCount = Math.max(actualReviews.length, containerCount);
            console.log(`📜 ENHANCED SCRAPER: Using max count: global(${actualReviews.length}) vs container(${containerCount}) = ${finalCount}`);
            
            return finalCount;
        }
        
        return actualReviews.length;
    }

    // Get current scroll position for bottom detection
    getCurrentScrollPosition() {
        // Get the most accurate scroll position possible
        const windowScrollY = window.scrollY || window.pageYOffset;
        const documentScrollTop = document.documentElement.scrollTop;
        const bodyScrollTop = document.body.scrollTop;
        
        // Use the maximum of all scroll position methods
        const scrollPosition = Math.max(windowScrollY, documentScrollTop, bodyScrollTop);
        
        // Also check the reviews container scroll position
        const reviewsContainer = document.querySelector('div.m6QErb');
        if (reviewsContainer) {
            const containerScrollTop = reviewsContainer.scrollTop;
            return Math.max(scrollPosition, containerScrollTop);
        }
        
        return scrollPosition;
    }

    // Run the read more script (from the user's bookmarklet)
    async runReadMoreScript() {
        console.log('🔽 ENHANCED SCRAPER: Running read more script...');
        
        // This is the exact script from the user's bookmarklet
        const clickElements = () => {
            var elements1 = document.querySelectorAll('button.w8nwRe.kyuRq');
            var elements2 = document.querySelectorAll('a.MtCSLb');
            var elements3 = document.querySelectorAll('a.review-more-link');

            console.log(`🔽 ENHANCED SCRAPER: Found ${elements1.length} w8nwRe.kyuRq buttons`);
            console.log(`🔽 ENHANCED SCRAPER: Found ${elements2.length} MtCSLb links`);
            console.log(`🔽 ENHANCED SCRAPER: Found ${elements3.length} review-more-link links`);

            elements1.forEach(function(element) {
                element.click();
            });

            elements2.forEach(function(element) {
                element.click();
            });

            elements3.forEach(function(element) {
                element.click();
            });

            const totalClicked = elements1.length + elements2.length + elements3.length;
            console.log(`👆 ENHANCED SCRAPER: Clicked ${totalClicked} "More" buttons/links`);
            return totalClicked;
        };

        // Execute the click function with a delay (as in the original bookmarklet)
        return new Promise((resolve) => {
            setTimeout(() => {
                const clickedCount = clickElements();
                resolve(clickedCount);
            }, 2000); // 2 second delay as in the original bookmarklet
        });
    }

    // Extract all review data from the page
    async extractAllReviews() {
        console.log('📊 ENHANCED SCRAPER: Extracting all reviews...');
        this.reviewData = [];

        // Look for review containers using the correct class for Pro List pages
        const allContainers = document.querySelectorAll('div.OA1nbd');
        console.log(`📊 ENHANCED SCRAPER: Found ${allContainers.length} potential review containers with class 'OA1nbd'`);

        // Filter out owner responses - they are NOT reviews
        const reviewContainers = Array.from(allContainers).filter((container, index) => {
            console.log(`📊 ENHANCED SCRAPER: Checking container ${index + 1} for owner response...`);
            
            // First, check if this container itself contains the owner response indicator
            const ownerResponseSpan = container.querySelector('span.nM6d2c');
            if (ownerResponseSpan) {
                const spanText = ownerResponseSpan.textContent.toLowerCase();
                console.log(`📊 ENHANCED SCRAPER: Found span.nM6d2c with text: "${spanText}"`);
                if (spanText.includes('response from the owner') || 
                    spanText.includes('response from owner') ||
                    spanText.includes('owner response')) {
                    console.log('📊 ENHANCED SCRAPER: ❌ This container IS an owner response (not a review)');
                    return false; // Exclude owner responses
                }
            }
            
            // Check if this container is part of an owner response structure
            // Look for nM6d2c in parent or sibling elements
            const parentElement = container.parentElement;
            if (parentElement) {
                const parentOwnerSpan = parentElement.querySelector('span.nM6d2c');
                if (parentOwnerSpan) {
                    const parentSpanText = parentOwnerSpan.textContent.toLowerCase();
                    console.log(`📊 ENHANCED SCRAPER: Found span.nM6d2c in parent with text: "${parentSpanText}"`);
                    if (parentSpanText.includes('response from the owner') || 
                        parentSpanText.includes('response from owner') ||
                        parentSpanText.includes('owner response')) {
                        console.log('📊 ENHANCED SCRAPER: ❌ This container is part of owner response structure');
                        return false; // Exclude owner responses
                    }
                }
            }
            
            // Check previous siblings for owner response indicators
            let currentElement = container.previousElementSibling;
            let siblingCheckCount = 0;
            while (currentElement && siblingCheckCount < 3) { // Check up to 3 previous siblings
                const siblingOwnerSpan = currentElement.querySelector('span.nM6d2c');
                if (siblingOwnerSpan) {
                    const siblingSpanText = siblingOwnerSpan.textContent.toLowerCase();
                    console.log(`📊 ENHANCED SCRAPER: Found span.nM6d2c in sibling with text: "${siblingSpanText}"`);
                    if (siblingSpanText.includes('response from the owner') || 
                        siblingSpanText.includes('response from owner') ||
                        siblingSpanText.includes('owner response')) {
                        console.log('📊 ENHANCED SCRAPER: ❌ This container follows an owner response indicator');
                        return false; // Exclude owner responses
                    }
                }
                currentElement = currentElement.previousElementSibling;
                siblingCheckCount++;
            }
            
            // Also check the entire container text for owner response indicators as fallback
            const containerText = container.textContent.toLowerCase();
            if (containerText.includes('response from the owner') || 
                containerText.includes('response from owner')) {
                console.log('📊 ENHANCED SCRAPER: ❌ Filtering out owner response found in container text');
                return false; // Exclude owner responses
            }
            
            console.log(`📊 ENHANCED SCRAPER: ✅ Container ${index + 1} is a valid review`);
            return true; // Include actual reviews
        });

        console.log(`📊 ENHANCED SCRAPER: After filtering out owner responses: ${reviewContainers.length} actual review containers`);

        if (reviewContainers.length === 0) {
            // Fallback: try other common review container selectors
            const fallbackSelectors = [
                'div.MyEned', // Original Google Maps reviews
                '[data-review-id]',
                '.review-item',
                '.review-content',
                '[jsaction*="review"]',
                'div[data-review-id]'
            ];

            for (const selector of fallbackSelectors) {
                const containers = document.querySelectorAll(selector);
                if (containers.length > 0) {
                    console.log(`📊 ENHANCED SCRAPER: Found ${containers.length} reviews using fallback selector: ${selector}`);
                    return this.extractReviewsFromContainers(containers);
                }
            }

            throw new Error('No review containers found on the page');
        }

        return this.extractReviewsFromContainers(reviewContainers);
    }

    // Extract reviews from the found containers
    extractReviewsFromContainers(containers) {
        console.log(`📊 ENHANCED SCRAPER: Processing ${containers.length} review containers...`);

        containers.forEach((container, index) => {
            try {
                const reviewData = this.extractSingleReview(container, index + 1);
                if (reviewData) {
                    this.reviewData.push(reviewData);
                }
            } catch (error) {
                console.error(`❌ ENHANCED SCRAPER: Error extracting review ${index + 1}:`, error);
            }
        });

        console.log(`✅ ENHANCED SCRAPER: Successfully extracted ${this.reviewData.length} reviews`);
        return this.reviewData.length;
    }

    // Extract data from a single review container
    extractSingleReview(container, index) {
        const reviewData = {
            id: index,
            businessName: this.businessName || 'Unknown Business',
            reviewText: '',
            reviewerName: '',
            rating: null,
            date: '',
            helpfulCount: null,
            response: '',
            photoCount: 0,
            extractedAt: new Date().toISOString(),
            isEmpty: false // Track if this is an empty review
        };

        try {
            // Extract review text - handle different page structures
            let reviewText = '';
            
            // For Pro List pages (OA1nbd containers)
            if (container.classList.contains('OA1nbd')) {
                const excludeElement = container.querySelector('div.zMjRQd');
                if (excludeElement) {
                    // Clone the container and remove the zMjRQd section to get clean review text
                    const clonedContainer = container.cloneNode(true);
                    const clonedExcludeElement = clonedContainer.querySelector('div.zMjRQd');
                    if (clonedExcludeElement) {
                        clonedExcludeElement.remove();
                    }
                    reviewText = clonedContainer.textContent.trim();
                } else {
                    // No zMjRQd section, use all text
                    reviewText = container.textContent.trim();
                }
            }
            // For regular Google Maps pages (MyEned containers)
            else if (container.classList.contains('MyEned')) {
                const reviewTextElement = container.querySelector('span.wiI7pd');
                if (reviewTextElement) {
                    reviewText = reviewTextElement.textContent.trim();
                } else {
                    reviewText = container.textContent.trim();
                }
            }
            // Fallback for other structures
            else {
                reviewText = container.textContent.trim();
            }
            
            // Check if review is empty and mark it
            if (!reviewText || reviewText.length === 0) {
                reviewData.reviewText = 'Review with no text';
                reviewData.isEmpty = true;
                console.log(`📝 ENHANCED SCRAPER: Review ${index} has no text content - marking as empty`);
            } else {
                reviewData.reviewText = reviewText;
            }

            // Extract reviewer name - try multiple selectors for different page types
            const reviewerNameSelectors = [
                'div.Vpc5Fe', // Pro List pages
                'div.d4r55', // Original Google Maps
                '.reviewer-name',
                '[data-reviewer-name]'
            ];
            
            let reviewerNameElement = null;
            for (const selector of reviewerNameSelectors) {
                // Look in the parent container structure for reviewer name
                reviewerNameElement = container.parentElement?.parentElement?.querySelector(selector) ||
                                    container.parentElement?.querySelector(selector) ||
                                    container.closest('.bwb7ce')?.querySelector(selector) ||
                                    container.closest('[data-review-id]')?.querySelector(selector) ||
                                    container.querySelector(selector);
                if (reviewerNameElement) {
                    console.log(`📝 ENHANCED SCRAPER: Found reviewer name using selector: ${selector}`);
                    break;
                }
            }
            
            if (reviewerNameElement) {
                reviewData.reviewerName = reviewerNameElement.textContent.trim();
                console.log(`📝 ENHANCED SCRAPER: Extracted reviewer name: "${reviewData.reviewerName}"`);
            } else {
                console.log('📝 ENHANCED SCRAPER: No reviewer name found');
                if (reviewData.isEmpty) {
                    reviewData.reviewerName = 'Unknown Reviewer (Empty Review)';
                }
            }

            // Extract rating
            reviewData.rating = this.extractRating(container);

            // Extract date
            reviewData.date = this.extractDate(container);

            // Extract helpful count
            reviewData.helpfulCount = this.extractHelpfulCount(container);

            // Extract business response
            reviewData.response = this.extractBusinessResponse(container);

            // Count photos
            reviewData.photoCount = this.countReviewPhotos(container);

            console.log(`📝 ENHANCED SCRAPER: Extracted review ${index}:`, {
                reviewer: reviewData.reviewerName,
                rating: reviewData.rating,
                textLength: reviewData.reviewText.length,
                textPreview: reviewData.reviewText.substring(0, 100) + (reviewData.reviewText.length > 100 ? '...' : ''),
                hasResponse: !!reviewData.response,
                isEmpty: reviewData.isEmpty
            });

            return reviewData;

        } catch (error) {
            console.error(`❌ ENHANCED SCRAPER: Error extracting review ${index}:`, error);
            return null;
        }
    }

    // Extract rating from various possible selectors
    extractRating(container) {
        const ratingSelectors = [
            '[aria-label*="Rated"]', // Pro List pages: "Rated 5.0 out of 5"
            '[aria-label*="star"]',
            '[aria-label*="Star"]',
            '.review-rating',
            '[data-rating]',
            'span[aria-label*="star"]',
            '.dHX2k' // Pro List rating container
        ];

        // Look in parent containers for rating
        const searchContainers = [
            container,
            container.parentElement,
            container.parentElement?.parentElement,
            container.closest('.bwb7ce'),
            container.closest('[data-review-id]')
        ].filter(Boolean);

        for (const searchContainer of searchContainers) {
            for (const selector of ratingSelectors) {
                const ratingElement = searchContainer.querySelector(selector);
                
                if (ratingElement) {
                    const ariaLabel = ratingElement.getAttribute('aria-label');
                    if (ariaLabel) {
                        // Try "Rated X.X out of 5" pattern first (Pro List)
                        let ratingMatch = ariaLabel.match(/Rated\s+(\d+(?:\.\d+)?)\s+out\s+of\s+5/i);
                        if (ratingMatch) {
                            console.log(`📝 ENHANCED SCRAPER: Found rating from "Rated X out of 5": ${ratingMatch[1]}`);
                            return parseFloat(ratingMatch[1]);
                        }
                        
                        // Try "X star" pattern (regular Google Maps)
                        ratingMatch = ariaLabel.match(/(\d+)\s*star/i);
                        if (ratingMatch) {
                            console.log(`📝 ENHANCED SCRAPER: Found rating from "X star": ${ratingMatch[1]}`);
                            return parseInt(ratingMatch[1]);
                        }
                    }

                    const dataRating = ratingElement.getAttribute('data-rating');
                    if (dataRating) {
                        console.log(`📝 ENHANCED SCRAPER: Found rating from data-rating: ${dataRating}`);
                        return parseInt(dataRating);
                    }
                }
            }
        }

        console.log('📝 ENHANCED SCRAPER: No rating found');
        return null;
    }

    // Extract date from the review
    extractDate(container) {
        const dateSelectors = [
            'span.y3Ibjb', // Pro List pages
            '[data-date]',
            '.review-date',
            'span[aria-label*="ago"]',
            'time',
            '[datetime]'
        ];

        // Look in parent containers for date
        const searchContainers = [
            container,
            container.parentElement,
            container.parentElement?.parentElement,
            container.closest('.bwb7ce'),
            container.closest('[data-review-id]')
        ].filter(Boolean);

        for (const searchContainer of searchContainers) {
            for (const selector of dateSelectors) {
                const dateElement = searchContainer.querySelector(selector);
                
                if (dateElement) {
                    const dateText = dateElement.textContent.trim() || 
                                   dateElement.getAttribute('datetime') || 
                                   dateElement.getAttribute('data-date') || '';
                    
                    if (dateText) {
                        console.log(`📝 ENHANCED SCRAPER: Found date using selector ${selector}: "${dateText}"`);
                        return dateText;
                    }
                }
            }
        }

        console.log('📝 ENHANCED SCRAPER: No date found');
        return '';
    }

    // Extract helpful count
    extractHelpfulCount(container) {
        const helpfulSelectors = [
            '[aria-label*="helpful"]',
            '.helpful-count',
            '[data-helpful]'
        ];

        for (const selector of helpfulSelectors) {
            const helpfulElement = container.querySelector(selector) || 
                                 container.closest('[data-review-id]')?.querySelector(selector);
            
            if (helpfulElement) {
                const text = helpfulElement.textContent;
                const match = text.match(/(\d+)/);
                if (match) {
                    return parseInt(match[1]);
                }
            }
        }

        return null;
    }

    // Extract business response
    extractBusinessResponse(container) {
        const responseSelectors = [
            '.business-response',
            '[data-response]',
            '.owner-response',
            '.response-text'
        ];

        for (const selector of responseSelectors) {
            const responseElement = container.querySelector(selector) || 
                                  container.closest('[data-review-id]')?.querySelector(selector);
            
            if (responseElement) {
                return responseElement.textContent.trim();
            }
        }

        return '';
    }

    // Count photos in the review
    countReviewPhotos(container) {
        const photoSelectors = [
            'img[src*="review"]',
            '.review-photo',
            '[data-photo]',
            'img[alt*="review"]'
        ];

        let photoCount = 0;
        for (const selector of photoSelectors) {
            const photos = container.querySelectorAll(selector);
            photoCount += photos.length;
        }

        return photoCount;
    }

    // Export reviews to CSV format
    exportToCSV() {
        if (this.reviewData.length === 0) {
            return 'No review data available';
        }

        // Create CSV with summary section first
        let csvContent = 'Single Business Reviews\n\n';
        
        // Summary section
        csvContent += 'SUMMARY\n';
        csvContent += `Total Reviews,${this.reviewData.length}\n`;
        if (this.businessName && this.businessName !== 'Unknown Business') {
            csvContent += `Business Name,${this.businessName.replace(/"/g, '""')}\n`;
        }
        csvContent += `Extracted At,${new Date().toISOString()}\n`;
        csvContent += '\n';
        
        // Headers for detailed review data
        csvContent += 'DETAILED REVIEWS\n';
        const headers = [
            'ID',
            'Business Name',
            'Reviewer Name',
            'Rating',
            'Date',
            'Review Text',
            'Is Empty Review',
            'Helpful Count',
            'Business Response',
            'Photo Count',
            'Extracted At'
        ];
        csvContent += headers.join(',') + '\n';

        // Individual reviews
        this.reviewData.forEach(review => {
            const row = [
                review.id,
                this.escapeCsvValue(review.businessName || this.businessName || 'Unknown Business'),
                this.escapeCsvValue(review.reviewerName),
                review.rating || '',
                this.escapeCsvValue(review.date),
                this.escapeCsvValue(review.reviewText),
                review.isEmpty ? 'YES' : 'NO',
                review.helpfulCount || '',
                this.escapeCsvValue(review.response),
                review.photoCount,
                this.escapeCsvValue(review.extractedAt)
            ];
            csvContent += row.join(',') + '\n';
        });

        return csvContent;
    }

    // Escape CSV values
    escapeCsvValue(value) {
        if (value === null || value === undefined) {
            return '';
        }
        
        const stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
    }

    // Utility function to wait
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get current extracted data
    getCurrentData() {
        return {
            reviewCount: this.reviewData.length,
            reviews: this.reviewData
        };
    }

    // Helper method to format business name for filename
    formatBusinessNameForFilename(businessName) {
        if (!businessName) return 'unknown-business';
        
        return businessName
            .toLowerCase()                    // Convert to lowercase
            .replace(/[^a-z0-9\s]/g, '')     // Remove special characters except spaces
            .replace(/\s+/g, '-')            // Replace spaces with dashes
            .replace(/-+/g, '-')             // Replace multiple consecutive dashes with single dash
            .replace(/^-|-$/g, '');          // Remove leading/trailing dashes
    }
}

// Create global instance
const simplifiedReviewScraper = new SimplifiedReviewScraper();
window.simplifiedReviewScraper = simplifiedReviewScraper;

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('🔍 ENHANCED SCRAPER: Received message:', request.action);

    if (request.action === 'executeReviewExtraction') {
        console.log('🚀 ENHANCED SCRAPER: Starting review extraction...');
        console.log('🚀 ENHANCED SCRAPER: Max reviews parameter:', request.maxReviews);
        
        simplifiedReviewScraper.executeExtraction(request.maxReviews)
            .then(result => {
                console.log('✅ ENHANCED SCRAPER: Extraction completed:', result);
                sendResponse(result);
            })
            .catch(error => {
                console.error('❌ ENHANCED SCRAPER: Extraction failed:', error);
                sendResponse({ 
                    error: error.message,
                    reviewCount: 0,
                    reviews: [],
                    rangeApplied: false,
                    maxReviews: null
                });
            });
        
        return true; // Keep message channel open for async response
    }

    if (request.action === 'exportSingleReviewCSV') {
        console.log('📊 ENHANCED SCRAPER: Exporting CSV...');
        
        try {
            const csvContent = simplifiedReviewScraper.exportToCSV();
            sendResponse({ 
                success: true,
                csvContent: csvContent,
                businessName: simplifiedReviewScraper.businessName || 'Unknown Business'
            });
        } catch (error) {
            console.error('❌ ENHANCED SCRAPER: CSV export failed:', error);
            sendResponse({ 
                error: error.message 
            });
        }
        
        return true;
    }
});

console.log('🔍 ENHANCED SCRAPER: Content script loaded and ready'); 