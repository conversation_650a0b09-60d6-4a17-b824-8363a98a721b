// Top URLs Copier for Google Search Results
// Creates a button to copy the top 5 search result URLs to clipboard

(function() {
    'use strict';
    
    // Prevent multiple loads
    if (window.GMBTopUrlsCopierLoaded) return;
    window.GMBTopUrlsCopierLoaded = true;
    
    let isEnabled = true; // Default to enabled
    
    // Check if we're on a Google search results page
    function isGoogleSearchPage() {
        return window.location.hostname.includes('google.') && 
               window.location.pathname === '/search' &&
               document.querySelector('#search');
    }
    
    // Extract clean URL from Google search result link
    function extractCleanUrl(linkElement) {
        const href = linkElement.getAttribute('href');
        if (!href) return null;
        
        // Handle different URL formats
        if (href.startsWith('http')) {
            // Direct URL
            return href;
        } else if (href.startsWith('/url?q=')) {
            // Google redirect URL - extract the actual URL
            try {
                const urlParams = new URLSearchParams(href.substring(5));
                const actualUrl = urlParams.get('q');
                return actualUrl;
            } catch (error) {
                console.error('Top URLs Copier: Error extracting URL from redirect:', error);
                return null;
            }
        }
        
        return null;
    }
    
    // Get top 5 search result URLs
    function getTop5Urls() {
        const searchResultLinks = document.querySelectorAll('a.zReHs');
        const urls = [];
        
        console.log(`Top URLs Copier: Found ${searchResultLinks.length} search result links`);
        
        // Extract URLs from the first 5 results
        for (let i = 0; i < Math.min(5, searchResultLinks.length); i++) {
            const link = searchResultLinks[i];
            const cleanUrl = extractCleanUrl(link);
            
            if (cleanUrl) {
                urls.push(cleanUrl);
                console.log(`Top URLs Copier: Extracted URL ${i + 1}: ${cleanUrl}`);
            }
        }
        
        return urls;
    }
    
    // Copy URLs to clipboard
    async function copyUrlsToClipboard() {
        const urls = getTop5Urls();
        
        if (urls.length === 0) {
            console.log('Top URLs Copier: No URLs found to copy');
            showNotification('No search result URLs found', 'error');
            return;
        }
        
        // Join URLs with newlines for one-per-line format
        const urlsText = urls.join('\n');
        
        try {
            await navigator.clipboard.writeText(urlsText);
            console.log('Top URLs Copier: Successfully copied URLs to clipboard');
            showNotification(`Copied ${urls.length} URLs to clipboard`, 'success');
        } catch (error) {
            console.error('Top URLs Copier: Error copying to clipboard:', error);
            
            // Fallback: create a textarea and copy using execCommand
            try {
                const textarea = document.createElement('textarea');
                textarea.value = urlsText;
                textarea.style.position = 'fixed';
                textarea.style.opacity = '0';
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                
                console.log('Top URLs Copier: Successfully copied URLs using fallback method');
                showNotification(`Copied ${urls.length} URLs to clipboard`, 'success');
            } catch (fallbackError) {
                console.error('Top URLs Copier: Fallback copy method also failed:', fallbackError);
                showNotification('Failed to copy URLs to clipboard', 'error');
            }
        }
    }
    
    // Show notification to user
    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            background: rgba(0, 0, 0, 0.9) !important;
            color: white !important;
            padding: 12px 16px !important;
            border-radius: 6px !important;
            font-family: Google Sans,Roboto,Arial,sans-serif !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            z-index: 10000 !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
            transition: all 0.3s ease !important;
            border: 1px solid #7C3AED !important;
        `;
        
        // Add purple dot on black background for small notifications
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="color: #7C3AED; font-size: 16px; background: #000; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">●</span>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    // Create the copy button
    function createCopyButton() {
        // Check if button already exists
        const existingButton = document.getElementById('gmb-top-urls-copy-button');
        if (existingButton && existingButton.parentNode) {
            return existingButton;
        }
        
        const button = document.createElement('button');
        button.id = 'gmb-top-urls-copy-button';
        button.textContent = 'Copy Top 5 URLs';
        button.style.cssText = `
            background: #1a1a1a !important;
            color: #9aa0a6 !important;
            border: 1px solid #7c3aed !important;
            border-radius: 4px !important;
            padding: 4px 8px !important;
            font-family: Google Sans,Roboto,Arial,sans-serif !important;
            font-size: 12px !important;
            font-weight: 500 !important;
            cursor: pointer !important;
            margin-left: 8px !important;
            transition: all 0.2s ease !important;
            display: inline-block !important;
            vertical-align: middle !important;
        `;
        
        // Add hover effects
        button.addEventListener('mouseenter', () => {
            button.style.background = '#7c3aed';
            button.style.color = 'white';
            button.style.borderColor = '#8b5cf6';
            button.style.transform = 'translateY(-1px)';
            button.style.boxShadow = '0 2px 4px rgba(124, 58, 237, 0.3)';
        });
        
        button.addEventListener('mouseleave', () => {
            button.style.background = '#1a1a1a';
            button.style.color = '#9aa0a6';
            button.style.borderColor = '#7c3aed';
            button.style.transform = 'translateY(0)';
            button.style.boxShadow = 'none';
        });
        
        // Add click handler
        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            // Visual feedback
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = 'translateY(0)';
            }, 100);
            
            // Copy URLs without removing button
            copyUrlsToClipboard();
            
            // Ensure button stays visible after click
            setTimeout(() => {
                if (!document.getElementById('gmb-top-urls-copy-button')) {
                    console.log('Top URLs Copier: Button disappeared after click, repositioning...');
                    positionCopyButton();
                }
            }, 200);
        });
        
        return button;
    }
    
    // Position the button near the result stats
    function positionCopyButton() {
        if (!isGoogleSearchPage() || !isEnabled) return;
        
        // Check if button already exists and is properly positioned
        const existingButton = document.getElementById('gmb-top-urls-copy-button');
        const existingContainer = document.getElementById('gmb-top-urls-button-container');
        if (existingButton && existingButton.parentNode && 
            (existingButton.parentNode.id === 'gmb-result-stats-clone' || existingContainer)) {
            console.log('Top URLs Copier: Button already properly positioned, skipping');
            return; // Button exists and is properly positioned
        }
        
        // Multiple positioning strategies - independent of other extension features
        
        // Strategy 1: Try to attach to result stats clone if available (optional enhancement)
        let targetElement = document.getElementById('gmb-result-stats-clone');
        if (targetElement) {
            const button = createCopyButton();
            if (button && !button.parentNode) {
                targetElement.appendChild(button);
                console.log('Top URLs Copier: Button positioned with result stats');
                return;
            }
        }
        
        // Strategy 2: Check if our container already exists
        if (document.getElementById('gmb-top-urls-button-container')) {
            console.log('Top URLs Copier: Button container already exists');
            return;
        }
        
        // Strategy 3: Create our own independent container
        const searchContainer = document.querySelector('#search');
        if (!searchContainer) {
            console.log('Top URLs Copier: No search container found');
            return;
        }
        
        // Find the best insertion point (independent of other features)
        let insertionPoint = null;
        let insertionMethod = 'before'; // 'before' or 'after' or 'append'
        
        // Try multiple anchor points in order of preference
        const anchorPoints = [
            // Google's result filters section
            { selector: '.O4T6Pe.TPKH4e', method: 'after', name: 'result filters' },
            // Google's original result stats (hidden)
            { selector: '#result-stats', method: 'after', name: 'original result stats' },
            // Google's search header/app bar
            { selector: '#slim_appbar', method: 'after', name: 'search app bar' },
            // Google's search results container
            { selector: '#rso', method: 'before', name: 'search results container' },
            // First div inside search container
            { selector: '#search > div:first-child', method: 'before', name: 'first search child' }
        ];
        
        for (const anchor of anchorPoints) {
            const element = document.querySelector(anchor.selector);
            if (element) {
                insertionPoint = element;
                insertionMethod = anchor.method;
                console.log(`Top URLs Copier: Found anchor point: ${anchor.name}`);
                break;
            }
        }
        
        // Create our independent container
        const buttonContainer = document.createElement('div');
        buttonContainer.id = 'gmb-top-urls-button-container';
        buttonContainer.style.cssText = `
            color: #9aa0a6 !important;
            font-family: Google Sans,Roboto,Arial,sans-serif !important;
            font-size: 14px !important;
            line-height: 20px !important;
            padding-left: 0px !important;
            margin: 8px 0 12px 0 !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            border-bottom: 1px solid #333333 !important;
            padding-bottom: 8px !important;
        `;
        
        // Add label and button
        const label = document.createElement('span');
        label.textContent = 'Search Tools:';
        label.style.cssText = `
            color: #9aa0a6 !important;
            font-size: 13px !important;
            font-weight: 500 !important;
        `;
        
        const button = createCopyButton();
        if (button) {
            buttonContainer.appendChild(label);
            buttonContainer.appendChild(button);
            
            // Insert using the determined strategy
            if (insertionPoint) {
                if (insertionMethod === 'after') {
                    insertionPoint.parentNode.insertBefore(buttonContainer, insertionPoint.nextSibling);
                } else if (insertionMethod === 'before') {
                    insertionPoint.parentNode.insertBefore(buttonContainer, insertionPoint);
                }
                console.log(`Top URLs Copier: Button positioned ${insertionMethod} anchor point`);
            } else {
                // Final fallback - append to search container
                searchContainer.appendChild(buttonContainer);
                console.log('Top URLs Copier: Button positioned using final fallback');
            }
        }
    }
    
    // Initialize the copy button
    function init() {
        if (!isGoogleSearchPage()) {
            console.log('Top URLs Copier: Not a Google search page, skipping');
            return;
        }
        
        console.log('Top URLs Copier: Initializing on Google search results page');
        
        // Load settings first
        if (typeof chrome !== 'undefined' && chrome.storage) {
            chrome.storage.local.get(['gmbExtractorSettings'], function(result) {
                const settings = result.gmbExtractorSettings || {};
                isEnabled = settings.topUrlsCopierEnabled !== false; // Default to true
                console.log('Top URLs Copier: Settings loaded, enabled:', isEnabled);
                
                if (isEnabled) {
                    // Initial button positioning with multiple attempts
                    setTimeout(positionCopyButton, 500);
                    setTimeout(positionCopyButton, 1000);
                    setTimeout(positionCopyButton, 2000);
                }
            });
        } else {
            // Fallback - just run
            setTimeout(positionCopyButton, 500);
            setTimeout(positionCopyButton, 1000);
            setTimeout(positionCopyButton, 2000);
        }
        
        // Watch for changes in case the page updates
        const observer = new MutationObserver((mutations) => {
            // Skip if disabled
            if (!isEnabled) return;
            
            let shouldReposition = false;
            let buttonWasRemoved = false;
            
            // Check if our button was removed
            const buttonExists = document.getElementById('gmb-top-urls-copy-button');
            const containerExists = document.getElementById('gmb-top-urls-button-container');
            const resultStatsExists = document.getElementById('gmb-result-stats-clone');
            
            // Only reposition if button completely missing and we're enabled
            if (!buttonExists && isEnabled) {
                // Check if it was actively removed vs just not positioned yet
                mutations.forEach((mutation) => {
                    if (mutation.removedNodes.length > 0) {
                        mutation.removedNodes.forEach((node) => {
                            if (node.nodeType === 1) {
                                if (node.id === 'gmb-top-urls-copy-button' || 
                                    node.id === 'gmb-top-urls-button-container' ||
                                    (node.querySelector && (
                                        node.querySelector('#gmb-top-urls-copy-button') || 
                                        node.querySelector('#gmb-top-urls-button-container')
                                    ))) {
                                    buttonWasRemoved = true;
                                    console.log('Top URLs Copier: Button was actively removed by page update');
                                }
                            }
                        });
                    }
                });
                
                if (buttonWasRemoved) {
                    shouldReposition = true;
                }
            }
            
            // Check if page structure changed in a way that affects our positioning
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) {
                            // Check for result stats clone (optional enhancement)
                            if (node.id === 'gmb-result-stats-clone' || 
                                (node.querySelector && node.querySelector('#gmb-result-stats-clone'))) {
                                // Only reposition if we don't have a button positioned there
                                const currentButton = document.getElementById('gmb-top-urls-copy-button');
                                if (currentButton && currentButton.parentNode && 
                                    currentButton.parentNode.id !== 'gmb-result-stats-clone') {
                                    shouldReposition = true;
                                    console.log('Top URLs Copier: Result stats added, repositioning for better placement');
                                }
                            }
                            
                            // Check for core Google search elements that might indicate page reload
                            if (node.id === 'rso' || node.id === 'search' ||
                                (node.querySelector && (node.querySelector('#rso') || node.querySelector('#search')))) {
                                if (!buttonExists) {
                                    shouldReposition = true;
                                    console.log('Top URLs Copier: Core search structure changed, repositioning');
                                }
                            }
                        }
                    });
                }
            });
            
            if (shouldReposition) {
                // Debounce repositioning to prevent excessive calls
                clearTimeout(window.topUrlsCopierRepositionTimeout);
                window.topUrlsCopierRepositionTimeout = setTimeout(() => {
                    positionCopyButton();
                }, 100);
            }
        });
        
        // Start observing
        const targetNode = document.querySelector('#search') || document.body;
        observer.observe(targetNode, {
            childList: true,
            subtree: true
        });
        
        console.log('Top URLs Copier: Initialized successfully');
    }
    
    // Function to remove the button when disabled
    function removeCopyButton() {
        const button = document.getElementById('gmb-top-urls-copy-button');
        const container = document.getElementById('gmb-top-urls-button-container');
        
        if (button) {
            button.remove();
            console.log('Top URLs Copier: Button removed');
        }
        
        if (container) {
            container.remove();
            console.log('Top URLs Copier: Button container removed');
        }
    }
    
    // Start when ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Also initialize on window load as backup
    window.addEventListener('load', () => {
        setTimeout(init, 1000);
    });
    
    // Listen for settings updates from the settings page
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'updateSettings') {
                isEnabled = message.settings.topUrlsCopierEnabled !== false;
                
                if (!isEnabled) {
                    // Remove existing button if disabled
                    removeCopyButton();
                    console.log('Top URLs Copier: Disabled via settings update');
                } else {
                    // Re-enable and position button if enabled
                    console.log('Top URLs Copier: Enabled via settings update');
                    if (isGoogleSearchPage()) {
                        setTimeout(positionCopyButton, 500);
                    }
                }
                sendResponse({received: true});
            }
        });
    }
    
    console.log('Top URLs Copier: Script loaded');
})(); 