// Location Changer Integration for SEO Time Machines
// This file handles the accordion functionality and UI integration

document.addEventListener('DOMContentLoaded', function() {
    initializeLocationChangerAccordion();
    updateLocationStatus();
});

function initializeLocationChangerAccordion() {
    const header = document.getElementById('locationChangerHeader');
    const content = document.getElementById('locationChangerContent');
    const icon = document.getElementById('locationChangerIcon');
    const accordion = document.querySelector('.location-changer-accordion');

    if (!header || !content || !icon) {
        console.warn('Location changer elements not found');
        return;
    }

    // Accordion toggle functionality
    header.addEventListener('click', function() {
        const isExpanded = content.classList.contains('expanded');
        
        if (isExpanded) {
            // Collapse
            content.classList.remove('expanded');
            header.classList.remove('expanded');
            icon.classList.remove('expanded');
            icon.textContent = '▼';
        } else {
            // Expand
            content.classList.add('expanded');
            header.classList.add('expanded');
            icon.classList.add('expanded');
            icon.textContent = '▲';
        }
    });

    // Function to update active state based on location override status
    function updateActiveState() {
        if (accordion) {
            // Check if location override is enabled
            chrome.storage.local.get(['settings'], function(result) {
                if (result.settings && result.settings.enabled) {
                    accordion.classList.add('active');
                } else {
                    accordion.classList.remove('active');
                }
            });
        }
    }

    // Initial state check
    updateActiveState();

    // Listen for storage changes to update the active state
    chrome.storage.onChanged.addListener(function(changes, namespace) {
        if (namespace === 'local' && changes.settings) {
            updateActiveState();
        }
    });

    // Also listen for changes to the enable checkbox
    const enabledCheckbox = document.getElementById('enabled');
    if (enabledCheckbox) {
        enabledCheckbox.addEventListener('change', function() {
            setTimeout(updateActiveState, 50); // Small delay to ensure storage is updated
        });
    }
}

function updateLocationStatus() {
    const statusIndicator = document.getElementById('locationStatusIndicator');
    
    if (!statusIndicator) return;

    // Get current location settings from storage
    chrome.storage.local.get(['settings'], (data) => {
        const isEnabled = data.settings && data.settings.enabled;
        
        if (isEnabled) {
            statusIndicator.classList.remove('disabled');
            statusIndicator.style.backgroundColor = '#28a745'; // Green for enabled
        } else {
            statusIndicator.classList.add('disabled');
            statusIndicator.style.backgroundColor = '#6c757d'; // Gray for disabled
        }
    });
}

// Handle keyboard shortcuts for accordion
document.addEventListener('keydown', function(event) {
    if (event.ctrlKey && event.key === 'l') {
        event.preventDefault();
        const header = document.getElementById('locationChangerHeader');
        if (header) {
            header.click();
        }
    }
});

// Export functions for use in other scripts
window.LocationChangerIntegration = {
    updateLocationStatus,
    initializeLocationChangerAccordion
}; 