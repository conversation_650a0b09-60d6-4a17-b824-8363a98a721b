// Universal Tooltip System for Info Icons
// Extends existing tooltip patterns with info icon-specific features

class UniversalTooltipSystem {
    static tooltips = new Map();
    static stylesAdded = false;
    static activeTooltip = null;
    static activeLevelTwoTooltip = null;

    // Initialize the tooltip system
    static init() {
        this.addTooltipStyles();
        this.attachGlobalListeners();
    }

    // Create an info icon with tooltip
    static createInfoIcon(options = {}) {
        const {
            tooltip = 'Information',
            iconSize = 'default', // 'small', 'default', 'medium', 'large'
            tooltipSize = 'default', // 'small', 'default', 'medium', 'large', 'xl'
            className = '',
            position = 'auto',
            id = null
        } = options;

        // Map icon size to CSS class
        const iconSizeClass = iconSize === 'default' ? '' : `info-icon-${iconSize}`;
        
        const infoContainer = document.createElement('div');
        infoContainer.className = `info-icon-container ${iconSizeClass} ${className}`.trim();
        if (id) infoContainer.id = id;

        // Create the info icon as a purple dot
        const infoIcon = document.createElement('div');
        infoIcon.className = 'info-icon';
        infoIcon.innerHTML = '●';

        infoContainer.appendChild(infoIcon);

        // Store tooltip data
        const tooltipData = {
            text: tooltip,
            position: position,
            tooltipSize: tooltipSize,
            element: infoContainer
        };

        this.tooltips.set(infoContainer, tooltipData);

        // Add event listeners
        this.attachTooltipListeners(infoContainer, tooltipData);

        return infoContainer;
    }

    // Attach event listeners to an info icon
    static attachTooltipListeners(element, tooltipData) {
        let showTimeout;
        let hideTimeout;

        element.addEventListener('mouseenter', (e) => {
            clearTimeout(hideTimeout);
            showTimeout = setTimeout(() => {
                this.showTooltip(element, tooltipData);
            }, 200); // Small delay for better UX
        });

        element.addEventListener('mouseleave', (e) => {
            clearTimeout(showTimeout);
            hideTimeout = setTimeout(() => {
                this.hideTooltip();
            }, 100);
        });

        // Touch support for mobile
        element.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.showTooltip(element, tooltipData);
            
            // Auto-hide after 3 seconds on touch
            setTimeout(() => {
                this.hideTooltip();
            }, 3000);
        });
    }

    // Show tooltip with smart positioning
    static showTooltip(triggerElement, tooltipData) {
        this.hideTooltip(); // Hide any existing tooltip

        // Apply size class if specified
        const sizeClass = tooltipData.tooltipSize && tooltipData.tooltipSize !== 'default' 
            ? `tooltip-${tooltipData.tooltipSize}` 
            : '';

        const tooltip = document.createElement('div');
        tooltip.className = `universal-tooltip ${sizeClass}`.trim();
        tooltip.innerHTML = `
            <div class="tooltip-content">
                ${tooltipData.text}
            </div>
            <div class="tooltip-arrow"></div>
        `;

        document.body.appendChild(tooltip);
        this.activeTooltip = tooltip;

        // Position the tooltip using smart positioning logic
        this.positionTooltip(triggerElement, tooltip, tooltipData.position);

        // Show with animation
        requestAnimationFrame(() => {
            tooltip.classList.add('tooltip-visible');
        });
    }

    // Show level-two tooltip for second popup contexts (settings, etc.)
    static showLevelTwoTooltip(triggerElement, tooltipData) {
        this.hideLevelTwoTooltip(); // Hide any existing tooltip

        const tooltip = document.createElement('div');
        tooltip.style.cssText = `
            position: fixed;
            z-index: 999999999;
            background: #0a0a0a;
            color: #d1d5db;
            border: 2px solid #7C3AED;
            border-radius: 8px;
            padding: 10px 14px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.6), 0 0 10px rgba(124, 58, 237, 0.2);
            pointer-events: none;
            word-wrap: break-word;
            white-space: normal;
            max-width: 300px;
            opacity: 0;
            transform: translateY(-5px);
            transition: all 0.2s ease;
        `;

        tooltip.innerHTML = `<div>${tooltipData.text}</div>`;
        document.body.appendChild(tooltip);
        this.activeLevelTwoTooltip = tooltip;

        // Position the tooltip
        const triggerRect = triggerElement.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Calculate position (bottom-right of trigger by default)
        let left = triggerRect.right + 10;
        let top = triggerRect.bottom + 5;

        // Keep within viewport
        if (left + tooltipRect.width > viewportWidth - 10) {
            left = triggerRect.left - tooltipRect.width - 10;
        }
        if (top + tooltipRect.height > viewportHeight - 10) {
            top = triggerRect.top - tooltipRect.height - 10;
        }

        // Ensure minimum margins
        left = Math.max(10, left);
        top = Math.max(10, top);

        tooltip.style.left = `${left}px`;
        tooltip.style.top = `${top}px`;

        // Show with animation
        requestAnimationFrame(() => {
            tooltip.style.opacity = '1';
            tooltip.style.transform = 'translateY(0)';
        });
    }

    // Hide level-two tooltip
    static hideLevelTwoTooltip() {
        if (this.activeLevelTwoTooltip) {
            this.activeLevelTwoTooltip.style.opacity = '0';
            this.activeLevelTwoTooltip.style.transform = 'translateY(-5px)';
            setTimeout(() => {
                if (this.activeLevelTwoTooltip && this.activeLevelTwoTooltip.parentNode) {
                    this.activeLevelTwoTooltip.parentNode.removeChild(this.activeLevelTwoTooltip);
                }
                this.activeLevelTwoTooltip = null;
            }, 200);
        }
    }

    // Hide active tooltip
    static hideTooltip() {
        if (this.activeTooltip) {
            this.activeTooltip.classList.remove('tooltip-visible');
            setTimeout(() => {
                if (this.activeTooltip && this.activeTooltip.parentNode) {
                    this.activeTooltip.parentNode.removeChild(this.activeTooltip);
                }
                this.activeTooltip = null;
            }, 200);
        }
    }

    // Smart positioning logic (adapted from semantic tooltips)
    static positionTooltip(triggerElement, tooltip, preferredPosition = 'auto') {
        const triggerRect = triggerElement.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const tooltipWidth = tooltipRect.width || 200;
        const tooltipHeight = tooltipRect.height || 40;
        const arrow = tooltip.querySelector('.tooltip-arrow');
        const offset = 10;

        // Calculate available space
        const spaceAbove = triggerRect.top;
        const spaceBelow = viewportHeight - triggerRect.bottom;
        const spaceLeft = triggerRect.left;
        const spaceRight = viewportWidth - triggerRect.right;

        let position = preferredPosition;
        let left, top;

        // Auto-determine position if not specified
        if (position === 'auto') {
            if (spaceAbove >= tooltipHeight + offset && spaceAbove > spaceBelow) {
                position = 'top';
            } else if (spaceBelow >= tooltipHeight + offset) {
                position = 'bottom';
            } else if (spaceRight >= tooltipWidth + offset) {
                position = 'right';
            } else if (spaceLeft >= tooltipWidth + offset) {
                position = 'left';
            } else {
                position = 'bottom'; // Fallback
            }
        }

        // Calculate position based on determined placement
        switch (position) {
            case 'top':
                top = triggerRect.top - tooltipHeight - offset;
                left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2);
                this.setArrowPosition(arrow, 'top', triggerRect, tooltip, left);
                break;

            case 'bottom':
                top = triggerRect.bottom + offset;
                left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2);
                this.setArrowPosition(arrow, 'bottom', triggerRect, tooltip, left);
                break;

            case 'left':
                left = triggerRect.left - tooltipWidth - offset;
                top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2);
                this.setArrowPosition(arrow, 'left', triggerRect, tooltip, top);
                break;

            case 'right':
                left = triggerRect.right + offset;
                top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2);
                this.setArrowPosition(arrow, 'right', triggerRect, tooltip, top);
                break;
        }

        // Ensure tooltip stays within viewport
        left = Math.max(10, Math.min(viewportWidth - tooltipWidth - 10, left));
        top = Math.max(10, Math.min(viewportHeight - tooltipHeight - 10, top));

        // Apply positioning
        tooltip.style.left = `${left}px`;
        tooltip.style.top = `${top}px`;
        tooltip.dataset.position = position;
    }

    // Set arrow position and styling
    static setArrowPosition(arrow, position, triggerRect, tooltip, calculatedOffset) {
        if (!arrow) return;

        // Reset arrow styles
        arrow.style.top = '';
        arrow.style.bottom = '';
        arrow.style.left = '';
        arrow.style.right = '';
        arrow.style.transform = '';
        arrow.className = `tooltip-arrow tooltip-arrow-${position}`;

        const arrowSize = 6;
        
        switch (position) {
            case 'top':
                arrow.style.top = '100%';
                arrow.style.left = '50%';
                arrow.style.transform = 'translateX(-50%)';
                break;

            case 'bottom':
                arrow.style.bottom = '100%';
                arrow.style.left = '50%';
                arrow.style.transform = 'translateX(-50%)';
                break;

            case 'left':
                arrow.style.left = '100%';
                arrow.style.top = '50%';
                arrow.style.transform = 'translateY(-50%)';
                break;

            case 'right':
                arrow.style.right = '100%';
                arrow.style.top = '50%';
                arrow.style.transform = 'translateY(-50%)';
                break;
        }
    }

    // Add tooltip styles to the page
    static addTooltipStyles() {
        if (this.stylesAdded) return;

        const style = document.createElement('style');
        style.id = 'universal-tooltip-styles';
        style.textContent = `
            /* Info Icon Container */
            .info-icon-container {
                position: relative;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                margin-left: 6px;
                transition: all 0.2s ease;
            }

            /* Info Icon Sizing Classes */
            .info-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
                border-radius: 50%;
                color: #7C3AED;
                cursor: pointer;
                font-weight: bold;
            }

            /* Small Info Icons */
            .info-icon-small .info-icon {
                font-size: 10px;
            }

            /* Default Info Icons - Default size */
            .info-icon,
            .info-icon-default .info-icon {
                font-size: 10px;
            }

            /* Medium Info Icons */
            .info-icon-medium .info-icon {
                font-size: 20px;
            }

            /* Large Info Icons */
            .info-icon-large .info-icon {
                font-size: 22px;
            }

            .info-icon-container:hover .info-icon {
                color: #ffffff !important;
                filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.3));
                transform: scale(1.1);
            }


            /* Universal Tooltip Base Styles */
            .universal-tooltip {
                position: fixed;
                background: #0a0a0a;
                color: #d1d5db;
                border: 2px solid #7C3AED;
                border-radius: 8px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
                line-height: 1.4;
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.6), 0 0 10px rgba(124, 58, 237, 0.2);
                z-index: 99999999;
                opacity: 0;
                visibility: hidden;
                transform: translateY(-5px);
                transition: all 0.2s ease;
                pointer-events: none;
                word-wrap: break-word;
                white-space: normal;
            }

            /* Tooltip Size Classes */
            
            /* Small Tooltips - For brief hints */
            .universal-tooltip.tooltip-small {
                padding: 6px 10px;
                font-size: 10px;
                max-width: 200px;
                border-radius: 6px;
            }

            /* Default Tooltips - Standard size */
            .universal-tooltip,
            .universal-tooltip.tooltip-default {
                padding: 10px 14px;
                font-size: 12px;
                max-width: 300px;
                border-radius: 8px;
            }

            /* Medium Tooltips - For more detailed info */
            .universal-tooltip.tooltip-medium {
                padding: 12px 16px;
                font-size: 13px;
                max-width: 350px;
                border-radius: 8px;
            }

            /* Large Tooltips - For comprehensive info */
            .universal-tooltip.tooltip-large {
                padding: 14px 18px;
                font-size: 14px;
                max-width: 400px;
                border-radius: 10px;
                line-height: 1.5;
            }

            /* Extra Large Tooltips - For detailed explanations */
            .universal-tooltip.tooltip-xl {
                padding: 16px 20px;
                font-size: 14px;
                max-width: 500px;
                border-radius: 10px;
                line-height: 1.6;
            }

            .universal-tooltip.tooltip-visible {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }

            .tooltip-content {
                position: relative;
                z-index: 2;
            }

            /* Tooltip Arrow */
            .tooltip-arrow {
                position: absolute;
                width: 0;
                height: 0;
                z-index: 1;
            }

            .tooltip-arrow-top {
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #7C3AED;
            }

            .tooltip-arrow-bottom {
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-bottom: 6px solid #7C3AED;
            }

            .tooltip-arrow-left {
                border-top: 6px solid transparent;
                border-bottom: 6px solid transparent;
                border-left: 6px solid #7C3AED;
            }

            .tooltip-arrow-right {
                border-top: 6px solid transparent;
                border-bottom: 6px solid transparent;
                border-right: 6px solid #7C3AED;
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .universal-tooltip {
                    font-size: 11px;
                    padding: 8px 12px;
                    max-width: 250px;
                }

                .info-icon-container {
                    margin-left: 4px;
                }
            }

            @media (max-width: 480px) {
                .universal-tooltip {
                    font-size: 10px;
                    padding: 6px 10px;
                    max-width: 200px;
                }
            }

            /* Touch device optimizations */
            @media (hover: none) and (pointer: coarse) {
                .info-icon-container:active .info-icon svg {
                    fill: #ffffff !important;
                    transform: scale(1.1);
                }
            }
        `;

        document.head.appendChild(style);
        this.stylesAdded = true;
    }

    // Attach global event listeners
    static attachGlobalListeners() {
        // Hide tooltip on ESC key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideTooltip();
                this.hideLevelTwoTooltip();
            }
        });

        // Hide tooltip on scroll
        document.addEventListener('scroll', () => {
            this.hideTooltip();
            this.hideLevelTwoTooltip();
        }, true);

        // Hide tooltip on window resize
        window.addEventListener('resize', () => {
            this.hideTooltip();
            this.hideLevelTwoTooltip();
        });

        // Hide tooltip on click outside
        document.addEventListener('click', (e) => {
            if (this.activeTooltip && !e.target.closest('.info-icon-container')) {
                this.hideTooltip();
            }
            if (this.activeLevelTwoTooltip && !e.target.closest('.level-two-tooltip-icon')) {
                this.hideLevelTwoTooltip();
            }
        });
    }

    // Add tooltip to existing element
    static addTooltip(element, tooltipText, options = {}) {
        const tooltipData = {
            text: tooltipText,
            position: options.position || 'auto',
            element: element
        };

        this.tooltips.set(element, tooltipData);
        this.attachTooltipListeners(element, tooltipData);

        // Add info icon class for consistent styling
        element.classList.add('has-tooltip');
        
        return element;
    }

    // Remove tooltip from element
    static removeTooltip(element) {
        if (this.tooltips.has(element)) {
            this.tooltips.delete(element);
            element.classList.remove('has-tooltip');
            
            // Clean up event listeners would be handled by cloning, but for simplicity:
            // Just remove the visual indicators
            element.style.cursor = '';
        }
    }

    // Cleanup function for DOM restoration
    static cleanup() {
        this.hideTooltip();
        this.tooltips.clear();
        
        // Remove styles
        const existingStyle = document.getElementById('universal-tooltip-styles');
        if (existingStyle) {
            existingStyle.remove();
            this.stylesAdded = false;
        }

        // Remove all tooltip indicators
        document.querySelectorAll('.info-icon-container, .has-tooltip').forEach(element => {
            element.classList.remove('has-tooltip');
        });

        document.querySelectorAll('.info-icon-container').forEach(element => {
            element.remove();
        });
    }

    // Utility: Create multiple info icons from config
    static createInfoIconsFromConfig(config) {
        const icons = [];
        
        config.forEach(item => {
            const icon = this.createInfoIcon({
                tooltip: item.tooltip,
                iconSize: item.iconSize || 'default',
                tooltipSize: item.tooltipSize || 'default',
                className: item.className || '',
                position: item.position || 'auto',
                id: item.id || null
            });
            
            icons.push({
                element: icon,
                target: item.target, // CSS selector where to append
                insertMethod: item.insertMethod || 'append' // 'append', 'prepend', 'after', 'before'
            });
        });
        
        return icons;
    }
}

// Initialize on DOM ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        UniversalTooltipSystem.init();
    });
} else {
    UniversalTooltipSystem.init();
}

// Export for global use
if (typeof window !== 'undefined') {
    window.UniversalTooltipSystem = UniversalTooltipSystem;
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UniversalTooltipSystem;
}