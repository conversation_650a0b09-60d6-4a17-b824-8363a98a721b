class ProfilesShortcuts {
    constructor() {
        this.shortcuts = new Map();
        this.settingsManager = this.createSettingsManager();
        this.init();
    }

    async init() {
        await this.loadAndAssignShortcuts();
        document.addEventListener('keydown', (event) => this.handleKeyDown(event));
    }

    createSettingsManager() {
        return {
            async get(key) {
                return new Promise((resolve) => {
                    chrome.storage.local.get(key, (result) => {
                        resolve(result[key]);
                    });
                });
            },
            async set(key, value) {
                return new Promise((resolve) => {
                    chrome.storage.local.set({ [key]: value }, () => {
                        resolve();
                    });
                });
            },
            async getAll() {
                return new Promise((resolve) => {
                    chrome.storage.local.get(null, (items) => {
                        resolve(items);
                    });
                });
            },
            async setAll(settings) {
                return new Promise((resolve) => {
                    chrome.storage.local.set(settings, () => {
                        resolve();
                    });
                });
            },
            
            // PROFILE-SAFE METHODS - These methods protect profiles and system data
            async getSettingsOnly() {
                return new Promise((resolve) => {
                    chrome.storage.local.get(null, (items) => {
                        // Create a copy excluding profile-related and system keys
                        const settingsOnly = { ...items };
                        
                        // Remove profile-related data to prevent recursive saving
                        delete settingsOnly.profiles;
                        
                        // Remove system flags that shouldn't be included in profiles
                        delete settingsOnly.onboardingCompleted;
                        delete settingsOnly.showOnboardingAfterReload;
                        
                        // Remove temporary/session data
                        delete settingsOnly.extensionVersion;
                        delete settingsOnly.lastUpdateCheck;
                        
                        console.log('🛡️ Profile Protection: Excluded profiles and system flags from settings');
                        resolve(settingsOnly);
                    });
                });
            },
            
            async setSettingsOnly(settings) {
                return new Promise((resolve) => {
                    // First get current profiles and system data to preserve them
                    chrome.storage.local.get(['profiles', 'onboardingCompleted', 'showOnboardingAfterReload', 'gmbExtractorSettings'], (systemData) => {
                        
                        // Create safe settings object (exclude any profile/system keys from input)
                        const safeSettings = { ...settings };
                        delete safeSettings.profiles;
                        delete safeSettings.onboardingCompleted;
                        delete safeSettings.showOnboardingAfterReload;
                        delete safeSettings.gmbExtractorSettings; // Remove if it exists to avoid nesting
                        
                        // Extract the gmbExtractorSettings from the profile if it exists, otherwise use the settings directly
                        const profileSettings = settings.gmbExtractorSettings || safeSettings;
                        
                        // Prepare final storage structure
                        const finalStorageData = {
                            // Preserve system data
                            profiles: systemData.profiles,
                            onboardingCompleted: systemData.onboardingCompleted,
                            showOnboardingAfterReload: systemData.showOnboardingAfterReload,
                            // Save the settings under the gmbExtractorSettings key where popup expects them
                            gmbExtractorSettings: profileSettings
                        };
                        
                        console.log('🛡️ Profile Protection: Saving settings under gmbExtractorSettings key for popup compatibility');
                        console.log('📊 Settings Structure: gmbExtractorSettings contains', Object.keys(profileSettings).length, 'settings');
                        
                        chrome.storage.local.set(finalStorageData, () => {
                            resolve();
                        });
                    });
                });
            }
        };
    }

    async loadAndAssignShortcuts() {
        const profiles = await this.getProfiles();
        const enabledProfiles = profiles.filter(p => p.enabled);

        this.shortcuts.clear();

        enabledProfiles.forEach((profile, index) => {
            const shortcutNumber = index + 1;
            this.shortcuts.set(shortcutNumber, {
                name: profile.name,
                action: () => this.loadProfile(profile.name)
            });
        });
    }

    async getProfiles() {
        return new Promise((resolve) => {
            chrome.storage.local.get('profiles', (result) => {
                resolve(result.profiles || []);
            });
        });
    }

    async loadProfile(profileName) {
        const profiles = await this.getProfiles();
        const profile = profiles.find(p => p.name === profileName);
        
        if (profile) {
            try {
                // 🛡️ PROFILE PROTECTION: Use setSettingsOnly() to preserve current profiles
                // This prevents overwriting profiles when loading a profile
                await this.settingsManager.setSettingsOnly(profile.settings);
                
                console.log(`🛡️ Profile Protection: Loaded profile '${profileName}' while preserving current profiles`);
                
                // Show success notification before reloading
                this.showProfileNotification(`Profile '${profileName}' loaded successfully. Reloading extension...`, 'success');
                
                // Trigger extension reload to apply all settings
                setTimeout(() => {
                    if (window.GMBExtensionReload && window.GMBExtensionReload.reload) {
                        console.log('🔄 Profile Loading: Triggering extension reload to apply settings');
                        window.GMBExtensionReload.reload();
                    } else {
                        console.warn('🔄 Profile Loading: Extension reload utility not available, using runtime.reload directly');
                        chrome.runtime.reload();
                    }
                }, 1000); // Brief delay to show notification
                
            } catch (error) {
                console.error('❌ Error loading profile:', error);
                this.showProfileNotification(`Error loading profile '${profileName}': ${error.message}`, 'error');
            }
        } else {
            this.showProfileNotification(`Profile '${profileName}' not found.`, 'error');
        }
    }

    showProfileNotification(message, type) {
        // Remove any existing notifications
        document.querySelectorAll('.profile-notification').forEach(n => n.remove());

        const notification = document.createElement('div');
        notification.textContent = message;
        notification.className = 'profile-notification';

        let backgroundColor = 'rgba(0, 0, 0, 0.9)';
        let borderColor = 'rgba(124, 58, 237, 0.3)';
        let textColor = '#fff';

        if (type === 'success') {
            backgroundColor = 'linear-gradient(135deg, #10b981, #34d399)';
            borderColor = '#059669';
        } else if (type === 'error') {
            backgroundColor = 'linear-gradient(135deg, #ef4444, #f87171)';
            borderColor = '#dc2626';
        }

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${backgroundColor};
            color: ${textColor};
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 600;
            z-index: 10000;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
            border: 1px solid ${borderColor};
            animation: profileNotificationFadeIn 0.2s ease-out forwards;
            white-space: nowrap;
        `;

        // Add animation keyframes if not already added
        if (!document.getElementById('profile-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'profile-notification-styles';
            style.textContent = `
                @keyframes profileNotificationFadeIn {
                    from { opacity: 0; transform: translateX(100px) scale(0.8); }
                    to { opacity: 1; transform: translateX(0) scale(1); }
                }
                @keyframes profileNotificationFadeOut {
                    from { opacity: 1; transform: translateX(0) scale(1); }
                    to { opacity: 0; transform: translateX(100px) scale(0.8); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'profileNotificationFadeOut 0.2s ease-in forwards';
                setTimeout(() => {
                    notification.remove();
                }, 200);
            }
        }, 3000);
    }

    handleKeyDown(event) {
        if (event.ctrlKey && event.metaKey && event.shiftKey) {
            const keyNumber = parseInt(event.key);
            if (!isNaN(keyNumber) && keyNumber >= 1 && keyNumber <= 9) {
                const shortcut = this.shortcuts.get(keyNumber);
                if (shortcut) {
                    event.preventDefault();
                    shortcut.action();
                }
            }
        }
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new ProfilesShortcuts();
});
