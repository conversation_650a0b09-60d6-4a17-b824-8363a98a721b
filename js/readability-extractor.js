// Readability Content Extractor - Extract clean article content from any webpage
window.GmbReadabilityExtractor = (function() {
    'use strict';
    
    // Element Classifications for sophisticated HTML handling
    const ELEMENT_CLASSIFICATIONS = {
        // Self-closing (void) elements in HTML5
        SELF_CLOSING: new Set([
            'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input',
            'link', 'meta', 'param', 'source', 'track', 'wbr'
        ]),
        
        // Block-level elements
        BLOCK_ELEMENTS: new Set([
            'address', 'article', 'aside', 'blockquote', 'details', 'dialog', 'dd', 'div',
            'dl', 'dt', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2',
            'h3', 'h4', 'h5', 'h6', 'header', 'hgroup', 'hr', 'li', 'main', 'nav', 'ol',
            'p', 'pre', 'section', 'table', 'ul'
        ]),
        
        // Inline elements
        INLINE_ELEMENTS: new Set([
            'a', 'abbr', 'acronym', 'b', 'bdo', 'big', 'br', 'button', 'cite', 'code',
            'dfn', 'em', 'i', 'img', 'input', 'kbd', 'label', 'map', 'object', 'output',
            'q', 'samp', 'script', 'select', 'small', 'span', 'strong', 'sub', 'sup',
            'textarea', 'time', 'tt', 'var'
        ]),
        
        // Elements likely to contain main content
        CONTENT_ELEMENTS: new Set([
            'article', 'main', 'section', 'div', 'p', 'td', 'pre', 'blockquote'
        ]),
        
        // Elements that should be removed completely
        NEGATIVE_ELEMENTS: new Set([
            'script', 'style', 'noscript', 'iframe', 'object', 'embed', 'applet',
            'link', 'meta'
        ]),
        
        // Elements that affect content structure
        STRUCTURAL_ELEMENTS: new Set([
            'article', 'section', 'nav', 'aside', 'header', 'footer', 'main'
        ]),
        
        // Heading elements
        HEADING_ELEMENTS: new Set(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']),
        
        // List elements
        LIST_ELEMENTS: new Set(['ul', 'ol', 'dl']),
        
        // Table elements
        TABLE_ELEMENTS: new Set(['table', 'thead', 'tbody', 'tfoot', 'tr', 'td', 'th']),
        
        // Form elements
        FORM_ELEMENTS: new Set([
            'form', 'input', 'textarea', 'select', 'option', 'optgroup', 'button',
            'label', 'fieldset', 'legend'
        ]),
        
        // Media elements
        MEDIA_ELEMENTS: new Set([
            'img', 'video', 'audio', 'source', 'track', 'picture', 'canvas', 'svg'
        ]),
        
        // Interactive elements
        INTERACTIVE_ELEMENTS: new Set([
            'a', 'button', 'details', 'embed', 'iframe', 'label', 'select', 'textarea'
        ]),
        
        // Elements that can contain raw text
        RAW_TEXT_ELEMENTS: new Set(['script', 'style']),
        
        // Elements that preserve whitespace
        PRESERVE_WHITESPACE: new Set(['pre', 'code', 'textarea']),
        
        // Elements that should preserve all attributes
        PRESERVE_ATTRIBUTES: new Set(['img', 'video', 'audio', 'source', 'track'])
    };
    
    // Attribute classifications
    const ATTRIBUTE_CLASSIFICATIONS = {
        // Attributes that contain URLs
        URL_ATTRIBUTES: new Set([
            'href', 'src', 'action', 'background', 'cite', 'classid', 'codebase',
            'data', 'formaction', 'icon', 'longdesc', 'manifest', 'poster', 'profile',
            'srcset', 'usemap'
        ]),
        
        // Attributes that should be preserved
        SAFE_ATTRIBUTES: new Set([
            'alt', 'title', 'width', 'height', 'colspan', 'rowspan', 'scope',
            'datetime', 'dir', 'lang', 'start', 'type', 'value', 'cite'
        ]),
        
        // Event handler attributes (should be removed)
        EVENT_ATTRIBUTES: /^on[a-z]+$/i,
        
        // Data attributes
        DATA_ATTRIBUTES: /^data-[a-z][a-z0-9-]*$/i
    };
    
    // Stop words for fading feature
    const STOP_WORDS = new Set([
        'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
        'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
        'to', 'was', 'will', 'with', 'or', 'but', 'if', 'this', 'then',
        'they', 'have', 'had', 'what', 'when', 'where', 'who', 'which',
        'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most',
        'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same',
        'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just',
        'don', 'should', 'now'
    ]);
    
    // Content scoring weights
    const POSITIVE_INDICATORS = [
        'article', 'body', 'content', 'entry', 'hentry', 'main', 'page',
        'pagination', 'post', 'text', 'blog', 'story', 'paragraph'
    ];
    
    const NEGATIVE_INDICATORS = [
        'combx', 'comment', 'com-', 'contact', 'foot', 'footer', 'footnote',
        'masthead', 'media', 'meta', 'outbrain', 'promo', 'related', 'scroll',
        'shoutbox', 'sidebar', 'sponsor', 'shopping', 'tags', 'tool', 'widget',
        'nav', 'navigation', 'menu', 'header', 'banner', 'ad', 'advertisement',
        'social', 'share', 'popup', 'modal'
    ];
    
    // Configuration object
    const CONFIG = {
        extractionStrategy: 'hybrid', // 'scoring', 'semantic', 'density', 'visual', 'hybrid'
        cleaningOptions: {
            removeHidden: true,
            removeSocial: true,
            removeAds: true,
            cleanAttributes: true,
            preserveImages: true,
            preserveVideos: true,  // Changed to true to be less aggressive
            preserveIframes: false,
            preserveForms: false,
            cleanConditionally: true,
            removeEmpty: true,
            removeTracking: true,
            showSiteElements: false,     // New option to show site structural elements (hidden by default)
            minTextLength: 25,
            minWordCount: 3,
            maxLinkDensity: 0.5  // Increased from 0.33 to be less aggressive
        },
        minTextLength: 140,
        minScore: 20,
        debug: false
    };
    
    // Module instances (lazy loading)
    let tokenizer = null;
    let domBuilder = null;
    let linkProcessor = null;
    let contentScorer = null;
    let entityProcessor = null;
    let contentExtractor = null;
    let htmlSerializer = null;
    let advancedCleaner = null;
    
    // Initialize modules on demand
    function initModules() {
        if (!tokenizer && window.GmbHTMLTokenizer) {
            tokenizer = window.GmbHTMLTokenizer;
        }
        if (!domBuilder && window.GmbDOMBuilder) {
            domBuilder = window.GmbDOMBuilder;
        }
        if (!linkProcessor && window.GmbLinkProcessor) {
            linkProcessor = window.GmbLinkProcessor.create();
        }
        if (!contentScorer && window.GmbContentScorer) {
            contentScorer = window.GmbContentScorer.create();
        }
        if (!entityProcessor && window.GmbHTMLEntities) {
            entityProcessor = window.GmbHTMLEntities.create();
        }
        if (!contentExtractor && window.GmbContentExtractor) {
            contentExtractor = window.GmbContentExtractor.create({
                strategy: CONFIG.extractionStrategy
            });
        }
        if (!htmlSerializer && window.GmbHTMLSerializer) {
            htmlSerializer = window.GmbHTMLSerializer.create({
                prettyPrint: false,
                removeComments: true
            });
        }
        if (!advancedCleaner && window.GmbAdvancedCleaner) {
            advancedCleaner = window.GmbAdvancedCleaner.create({
                ...CONFIG.cleaningOptions,
                scorer: contentScorer
            });
        }
    }
    
    function extractContent(options = {}) {
        try {
            const config = Object.assign({}, CONFIG, options);
            
            // Initialize modules
            initModules();
            
            // Try advanced extraction first
            if (tokenizer && domBuilder && contentExtractor) {
                return extractContentAdvanced(config);
            } else {
                console.warn('Advanced modules not available, using fallback extraction');
                return extractContentSimple(config);
            }
            
        } catch (error) {
            console.error('Readability Extractor: Error extracting content, trying fallback:', error);
            try {
                return extractContentSimple(options);
            } catch (fallbackError) {
                console.error('Fallback extraction also failed:', fallbackError);
                return {
                    title: document.title || 'Untitled',
                    content: '<p>Unable to extract content from this page.</p>',
                    author: null,
                    publishedDate: null,
                    url: window.location.href,
                    domain: window.location.hostname,
                    error: error.message
                };
            }
        }
    }
    
    function extractContentAdvanced(config) {
        // Check if modules are available
        if (!tokenizer || !domBuilder || !contentExtractor) {
            console.error('Critical readability modules not loaded');
            throw new Error('Required readability modules (tokenizer, domBuilder, contentExtractor) are not available');
        }
        
        console.log('Using advanced extraction pipeline with enhanced link processing');
        
        // Phase 1: Parse HTML into DOM tree
        const html = document.documentElement.outerHTML;
        const tokens = tokenizer.tokenize(html);
        const { document: domTree, errors } = domBuilder.build(tokens);
        
        if (config.debug && errors.length > 0) {
            console.warn('Parsing errors:', errors);
        }
        
        // Phase 2: Process links (if available)
        if (linkProcessor) {
            linkProcessor.processLinks(domTree);
        }
        
        // Phase 3: Clean the DOM (if available)
        if (advancedCleaner) {
            advancedCleaner.clean(domTree, config.cleaningOptions);
        }
        
        // Phase 4: Extract content
        let extractionResult = null;
        try {
            extractionResult = contentExtractor.extract(domTree, {
                strategy: config.extractionStrategy
            });
        } catch (extractError) {
            console.error('Content extraction error:', extractError);
        }
        
        if (!extractionResult || !extractionResult.content) {
            console.error('Content extraction failed - no extractable content found');
            throw new Error('Content extraction failed - unable to extract meaningful content from page');
        }
        
        // Phase 5: Serialize back to HTML
        let contentHtml;
        try {
            contentHtml = htmlSerializer ? 
                htmlSerializer.serialize(extractionResult.content) :
                extractionResult.content.innerHTML || '<p>Content extracted but serialization failed</p>';
            
            console.log('HTML serialization completed. Sample output:', 
                contentHtml.substring(0, 200) + '...');
        } catch (serializationError) {
            console.error('HTML serialization error:', serializationError);
            throw new Error('HTML serialization failed: ' + serializationError.message);
        }
        
        // Phase 6: Extract metadata
        const metadata = extractionResult.metadata || {};
        
        return {
            title: metadata.title || getTitle(),
            content: contentHtml,
            author: metadata.author || getAuthor(),
            publishedDate: metadata.publishDate || getPublishedDate(),
            url: window.location.href,
            domain: window.location.hostname,
            wordCount: metadata.wordCount || 0,
            readingTime: metadata.readingTime || 0,
            contentType: extractionResult.type
        };
    }
    
    function extractContentSimple(config) {
        console.log('Using simple extraction fallback (no external modules required)');
        
        // Simple content extraction using basic DOM selectors
        const contentSelectors = [
            'article',
            'main',
            '[role="main"]',
            '.content',
            '.post-content',
            '.entry-content',
            '.article-content',
            '#content',
            '#main-content',
            '.story-body',
            '.article-body'
        ];
        
        let contentElement = null;
        
        // Try to find main content container
        for (const selector of contentSelectors) {
            const element = document.querySelector(selector);
            if (element && element.innerText && element.innerText.trim().length > 100) {
                contentElement = element;
                break;
            }
        }
        
        // Fallback to body if no content found
        if (!contentElement) {
            contentElement = document.body || document.documentElement;
        }
        
        // Basic cleaning
        const cleanedElement = contentElement.cloneNode(true);
        
        // Remove unwanted elements
        const unwantedSelectors = [
            'script', 'style', 'nav', 'header', 'footer', 'aside',
            '.sidebar', '.navigation', '.menu', '.header', '.footer',
            '.ad', '.advertisement', '.social', '.share', '.comments'
        ];
        
        for (const selector of unwantedSelectors) {
            const elements = cleanedElement.querySelectorAll(selector);
            elements.forEach(el => el.remove());
        }
        
        // Get HTML content
        let contentHtml = cleanedElement.innerHTML || '<p>No content could be extracted.</p>';
        
        // Basic text metrics
        const textContent = cleanedElement.textContent || '';
        const wordCount = textContent.split(/\s+/).filter(w => w.length > 0).length;
        const readingTime = Math.ceil(wordCount / 200); // 200 words per minute
        
        return {
            title: getTitle(),
            content: contentHtml,
            author: getAuthor(),
            publishedDate: getPublishedDate(),
            url: window.location.href,
            domain: window.location.hostname,
            wordCount: wordCount,
            readingTime: readingTime,
            contentType: 'simple-extraction'
        };
    }
    
    
    function getTitle() {
        // Try various title sources
        const candidates = [
            document.querySelector('h1'),
            document.querySelector('[property="og:title"]'),
            document.querySelector('meta[name="title"]'),
            document.querySelector('title')
        ];
        
        for (const candidate of candidates) {
            if (candidate) {
                const title = candidate.getAttribute && candidate.getAttribute('content') 
                    ? candidate.getAttribute('content')
                    : candidate.textContent;
                
                if (title && title.trim().length > 0) {
                    return title.trim();
                }
            }
        }
        
        return document.title || 'Untitled';
    }
    
    
    
    function getAuthor() {
        const authorSelectors = [
            '[property="article:author"]',
            '[name="author"]',
            '.author',
            '.byline',
            '.by-author',
            '[rel="author"]'
        ];
        
        for (const selector of authorSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const author = element.getAttribute('content') || element.textContent;
                if (author && author.trim()) {
                    return author.trim();
                }
            }
        }
        
        return null;
    }
    
    function getPublishedDate() {
        const dateSelectors = [
            '[property="article:published_time"]',
            '[property="article:published"]',
            '[name="date"]',
            '[name="publish_date"]',
            '.published',
            '.date',
            'time[datetime]'
        ];
        
        for (const selector of dateSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const date = element.getAttribute('content') 
                    || element.getAttribute('datetime') 
                    || element.textContent;
                
                if (date && date.trim()) {
                    const parsedDate = new Date(date.trim());
                    if (!isNaN(parsedDate)) {
                        return parsedDate.toISOString();
                    }
                }
            }
        }
        
        return null;
    }
    
    function processStopWords(content) {
        console.warn('DEPRECATED: processStopWords should not be called on HTML content. Use DOM-aware text processing instead.');
        if (!content) return content;
        
        // LEGACY FUNCTION - DO NOT USE ON HTML CONTENT
        // This function is dangerous when used on HTML as it can break tag structure
        // Use processStopWordsInDOM() instead for safe text-only processing
        
        // Wrap stop words in spans for fading
        const words = content.split(/(\s+)/);
        
        return words.map(word => {
            const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
            if (STOP_WORDS.has(cleanWord) && cleanWord.length > 0) {
                return `<span class="gmb-reader-fade">${word}</span>`;
            }
            return word;
        }).join('');
    }
    
    // NEW: HTML-safe stop words processing that only affects text nodes
    function processStopWordsInDOM(containerElement) {
        if (!containerElement) return;
        
        // Walk through all text nodes in the DOM
        const walker = document.createTreeWalker(
            containerElement,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    // Skip text nodes inside certain elements
                    const parentTag = node.parentElement?.tagName?.toLowerCase();
                    if (['script', 'style', 'code', 'pre', 'a', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(parentTag)) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );
        
        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }
        
        // Process each text node safely
        textNodes.forEach(textNode => {
            const originalText = textNode.textContent;
            if (!originalText || originalText.trim().length === 0) return;
            
            // Split by word boundaries while preserving spaces
            const words = originalText.split(/(\s+)/);
            
            const processedWords = words.map(word => {
                const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
                if (STOP_WORDS.has(cleanWord) && cleanWord.length > 0) {
                    return `<span class="gmb-reader-fade">${word}</span>`;
                }
                return word;
            });
            
            // Only replace if we actually have spans to add
            const processedHTML = processedWords.join('');
            if (processedHTML !== originalText && processedHTML.includes('<span class="gmb-reader-fade">')) {
                try {
                    const newElement = document.createElement('span');
                    newElement.innerHTML = processedHTML;
                    
                    // Ensure parent node exists before replacement
                    if (textNode.parentNode) {
                        textNode.parentNode.replaceChild(newElement, textNode);
                    }
                } catch (error) {
                    console.warn('Stop words processing error for text node:', originalText, error);
                    // Leave original text node unchanged if replacement fails
                }
            }
        });
    }
    
    // Public API
    return {
        // Main extraction method
        extract: extractContent,
        
        // Stop words processing methods
        processStopWords: processStopWords, // DEPRECATED - Use processStopWordsInDOM instead
        processStopWordsInDOM: processStopWordsInDOM, // NEW: Safe HTML-aware processing
        
        // Configuration
        configure: function(options) {
            Object.assign(CONFIG, options);
        },
        
        getConfig: function() {
            return Object.assign({}, CONFIG);
        },
        
        // Module access (for advanced usage)
        getModules: function() {
            initModules();
            return {
                tokenizer: tokenizer,
                domBuilder: domBuilder,
                linkProcessor: linkProcessor,
                contentScorer: contentScorer,
                entityProcessor: entityProcessor,
                contentExtractor: contentExtractor,
                htmlSerializer: htmlSerializer,
                advancedCleaner: advancedCleaner
            };
        },
        
        // Constants
        STOP_WORDS: STOP_WORDS,
        ELEMENT_CLASSIFICATIONS: ELEMENT_CLASSIFICATIONS,
        ATTRIBUTE_CLASSIFICATIONS: ATTRIBUTE_CLASSIFICATIONS,
        POSITIVE_INDICATORS: POSITIVE_INDICATORS,
        NEGATIVE_INDICATORS: NEGATIVE_INDICATORS,
        
        // Utility methods
        parseHTML: function(html) {
            initModules();
            const tokens = tokenizer.tokenize(html);
            return domBuilder.build(tokens);
        },
        
        cleanHTML: function(html, options) {
            initModules();
            const tokens = tokenizer.tokenize(html);
            const { document: domTree } = domBuilder.build(tokens);
            const { node: cleaned } = advancedCleaner.clean(domTree, options);
            return htmlSerializer.serialize(cleaned);
        },
        
        // Performance utilities
        benchmark: function(url) {
            const start = performance.now();
            const result = extractContent({ debug: true });
            const end = performance.now();
            
            return {
                time: end - start,
                result: result,
                metrics: {
                    wordCount: result.wordCount,
                    readingTime: result.readingTime,
                    method: result.extractionMethod
                }
            };
        }
    };
})();