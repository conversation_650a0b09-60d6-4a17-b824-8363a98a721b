// Popup Header Button Shortcuts
// Ultra-safe keyboard shortcuts for header buttons - only works when popup is focused and no UI elements are active

class PopupShortcutsManager {
    constructor() {
        console.log('🟡 PopupShortcuts: Constructor called on:', window.location.href);
        this.shortcuts = new Map();
        this.shortcutsEnabled = true;
        this.debug = true; // Always debug for now to track issues
        this.isPopupContext = false;
        this.popupReady = false;
        this.isInitialized = false; // Track initialization state
        this.keydownHandler = null; // Store handler reference for proper removal
        
        // Define shortcut mappings
        this.shortcutMappings = [
            { key: 'r', buttonId: 'reloadBtn', name: 'Reload Extension', type: 'button' },
            { key: 's', buttonId: 'settingsBtn', name: 'Settings', type: 'button' },
            { key: 't', buttonId: 'pomodoroToggleBtn', name: 'Pomodoro Timer', type: 'button' },
            { key: 'a', buttonId: 'alertToggleBtn', name: '<PERSON><PERSON><PERSON>', type: 'button' },
            { key: 'e', buttonId: 'emailPinnerBtn', name: 'Em<PERSON>', type: 'button' },
            { key: 'q', actionId: 'quickTimer', name: 'Quick Timer', type: 'accordion' },
            { key: 'd', actionId: 'tasksToggle', name: 'Todo Tasks', type: 'accordion' },
            { key: 'l', actionId: 'locationChanger', name: 'Location Changer', type: 'accordion' }
        ];
        
        // Detect context and initialize accordingly
        this.detectContext();
        console.log('🟡 PopupShortcuts: Context detection result:', {
            isPopupContext: this.isPopupContext,
            willInitialize: this.isPopupContext
        });
        if (this.isPopupContext) {
            this.initAsync();
        } else {
            console.log('🟡 PopupShortcuts: Skipping initialization - not in popup context');
        }
    }

    detectContext() {
        console.log('🟡 PopupShortcuts: Detecting context with details:', {
            protocol: window.location.protocol,
            pathname: window.location.pathname,
            href: window.location.href,
            hasDocumentElement: !!document.documentElement,
            documentElementClasses: document.documentElement ? Array.from(document.documentElement.classList) : [],
            hasBody: !!document.body,
            bodyClasses: document.body ? Array.from(document.body.classList) : [],
            isInFrame: window.parent !== window,
            hasFrameElement: !!window.frameElement,
            documentTitle: document.title
        });
        
        // STRICT popup context detection - prevent running on content pages
        const protocolCheck = window.location.protocol === 'chrome-extension:';
        const pathCheck = window.location.pathname.includes('popup.html');
        const httpCheck = !window.location.href.includes('http');
        const contentCheck = !document.documentElement.classList.contains('chrome-extension-content');
        const bodyCheck = document.body && document.body.classList.contains('popup');
        
        console.log('🟡 PopupShortcuts: Context validation checks:', {
            protocolCheck: protocolCheck,
            pathCheck: pathCheck,
            httpCheck: httpCheck,
            contentCheck: contentCheck,
            bodyCheck: bodyCheck
        });
        
        this.isPopupContext = protocolCheck && pathCheck && httpCheck && contentCheck && bodyCheck;
        
        // Additional safety check: ensure we're not in an iframe or content script context
        if (window.parent !== window || window.frameElement) {
            console.log('🟡 PopupShortcuts: Failed frame check - in iframe or content script');
            this.isPopupContext = false;
        }
        
        console.log('🟡 PopupShortcuts: Final context detection result:', {
            isPopupContext: this.isPopupContext,
            reason: this.isPopupContext ? 'All checks passed' : 'One or more checks failed'
        });
        
        if (!this.isPopupContext) {
            console.log('🟡 PopupShortcuts: Not in popup context, skipping initialization');
            return;
        }
    }

    async initAsync() {
        console.log('🟡 PopupShortcuts: Starting async initialization...');
        try {
            await this.loadSettings();
            this.init();
            console.log('🟡 PopupShortcuts: Async initialization completed successfully');
        } catch (error) {
            console.error('🔴 PopupShortcuts: Error in async initialization:', error);
        }
    }

    async loadSettings() {
        console.log('🟡 PopupShortcuts: Loading settings from storage...');
        try {
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            this.shortcutsEnabled = settings.popupShortcutsEnabled !== false; // Default to true
            this.debug = settings.debugMode === true;
            
            console.log('🟡 PopupShortcuts: Settings loaded:', {
                rawResult: result,
                settings: settings,
                shortcutsEnabled: this.shortcutsEnabled,
                debugMode: this.debug
            });
        } catch (error) {
            console.error('🔴 PopupShortcuts: Error loading settings:', error);
            this.shortcutsEnabled = true;
        }
    }

    init() {
        console.log('🟡 PopupShortcuts: Starting initialization...');
        if (!this.isPopupContext) {
            console.log('🟡 PopupShortcuts: Init called but not in popup context, returning');
            return;
        }

        // DUPLICATE PREVENTION: Check if this manager is already initialized
        if (this.isInitialized) {
            console.log('🟡 PopupShortcuts: Manager already initialized, skipping duplicate init');
            return;
        }

        // Setup shortcuts map
        this.shortcutMappings.forEach(mapping => {
            this.shortcuts.set(mapping.key, {
                buttonId: mapping.buttonId,
                actionId: mapping.actionId,
                name: mapping.name,
                type: mapping.type,
                execute: () => {
                    if (mapping.type === 'accordion') {
                        this.executeAccordionAction(mapping.actionId);
                    } else {
                        this.executeButtonAction(mapping.buttonId);
                    }
                }
            });
        });

        // DUPLICATE PREVENTION: Remove any existing keydown listeners before adding new one
        if (this.keydownHandler) {
            console.log('🟡 PopupShortcuts: Removing existing keydown handler');
            // BUG FIX: Match the capture parameter used when adding the listener
            document.removeEventListener('keydown', this.keydownHandler, false);
        }
        
        // Create bound handler reference for proper removal
        this.keydownHandler = (event) => this.handleKeyDown(event);
        
        console.log('🟡 PopupShortcuts: Adding keydown event listener');
        // Add keyboard event listener WITHOUT capture to avoid intercepting global shortcuts
        document.addEventListener('keydown', this.keydownHandler, false);
        
        // Add focus/blur detection for popup window
        window.addEventListener('focus', () => {
            this.popupReady = true;
        });
        
        window.addEventListener('blur', () => {
            this.popupReady = false;
        });
        
        // Mark as ready immediately if window already has focus
        if (document.hasFocus()) {
            this.popupReady = true;
        }
        
        // Mark as initialized
        this.isInitialized = true;
        console.log('🟢 PopupShortcuts: Manager initialized successfully with', {
            shortcutCount: this.shortcuts.size,
            shortcutsEnabled: this.shortcutsEnabled,
            popupReady: this.popupReady,
            availableShortcuts: Array.from(this.shortcuts.keys())
        });
    }

    handleKeyDown(event) {
        console.log('🟡 PopupShortcuts: Key down event:', {
            key: event.key,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            altKey: event.altKey,
            metaKey: event.metaKey,
            target: event.target.tagName
        });
        
        // GLOBAL SHORTCUT EXCLUSION: Skip if this looks like a global shortcut
        if (this.isGlobalShortcut(event)) {
            console.log('🟡 PopupShortcuts: Skipping - detected global shortcut');
            return; // Let global shortcuts handle this
        }

        // Only handle single key presses (no modifiers)
        if (event.ctrlKey || event.shiftKey || event.altKey || event.metaKey) {
            console.log('🟡 PopupShortcuts: Skipping - has modifier keys');
            return;
        }

        // Check if shortcuts are enabled
        if (!this.shortcutsEnabled) {
            console.log('🟡 PopupShortcuts: Skipping - shortcuts disabled');
            return;
        }

        // Ultra-safe check - must pass ALL safety conditions
        const safetyResult = this.isSafeForShortcuts();
        if (!safetyResult) {
            console.log('🟡 PopupShortcuts: Skipping - safety check failed:', this.getSafetyCheckDetails());
            return;
        }

        const key = event.key.toLowerCase();
        const shortcut = this.shortcuts.get(key);
        
        console.log('🟡 PopupShortcuts: Processing key:', {
            key: key,
            hasShortcut: !!shortcut,
            shortcutName: shortcut?.name
        });

        if (shortcut) {
            // KEYBOARD-LEVEL DEBOUNCING: Prevent rapid key press handling
            const now = Date.now();
            if (!this.lastKeyTime) this.lastKeyTime = {};
            
            if (this.lastKeyTime[key] && now - this.lastKeyTime[key] < 300) {
                console.log(`🔒 PopupShortcuts: Key '${key}' pressed too soon after last press, ignoring (debounced)`);
                return;
            }
            
            this.lastKeyTime[key] = now;

            // ONLY prevent default and stop propagation for keys we actually handle
            event.preventDefault();
            event.stopPropagation();

            try {
                shortcut.execute();
                
                // Close popup after executing action (except for settings, pomodoro, and accordion actions)
                if (shortcut.buttonId !== 'settingsBtn' && 
                    shortcut.buttonId !== 'pomodoroToggleBtn' && 
                    shortcut.type !== 'accordion') {
                    setTimeout(() => {
                        window.close();
                    }, 100);
                }
            } catch (error) {
                console.error(`PopupShortcuts: Error executing ${key}:`, error);
            }
        }
    }

    // Global shortcut detection - skip processing if this is a global shortcut
    isGlobalShortcut(event) {
        // Screenshot shortcuts: Cmd+Shift+C, Ctrl+Shift+C
        if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key.toLowerCase() === 'c') {
            return true;
        }
        
        // Text transformer shortcuts and other common global shortcuts
        if (event.ctrlKey || event.metaKey) {
            const key = event.key.toLowerCase();
            // Common global shortcuts that shouldn't be intercepted
            const globalKeys = ['a', 'c', 'v', 'x', 'z', 'y', 'f', 'g', 'h', 'r', 't', 'w', 'n'];
            if (globalKeys.includes(key) || event.shiftKey || event.altKey) {
                return true;
            }
        }
        
        return false;
    }

    // Get detailed safety check information for debugging
    getSafetyCheckDetails() {
        return {
            isPopupContext: this.isPopupContext,
            popupReady: this.popupReady,
            isPopupFocused: this.isPopupFocused(),
            hasAnyActiveInput: this.hasAnyActiveInput(),
            hasAnyModalOpen: this.hasAnyModalOpen(),
            isAnyFormElementFocused: this.isAnyFormElementFocused(),
            isTodoManagerEditing: this.isTodoManagerEditing(),
            activeElement: document.activeElement?.tagName,
            activeElementId: document.activeElement?.id
        };
    }

    // ULTRA-SAFE ACTIVATION CHECK FOR POPUP CONTEXT
    isSafeForShortcuts() {
        if (!this.isPopupContext) {
            return false;
        }
        
        if (!this.popupReady) {
            return false;
        }
        
        // For popup context, allow shortcuts even when accordions are open
        // (users often have expanded sections for configuration)
        return this.isPopupFocused() && 
               !this.hasAnyActiveInput() &&
               !this.hasAnyModalOpen() &&
               !this.isAnyFormElementFocused() &&
               !this.isTodoManagerEditing();
    }

    isPopupFocused() {
        return document.hasFocus();
    }

    hasAnyAccordionOpen() {
        const accordions = [
            'locationChangerContent',
            'quickTimerContent', 
            'tasksContent',
            'pomodoroTimerContent',
            'pomodoroConfigContent',
            'pomodoroAudioContent',
            'pomodoroBlockingContent'
        ];

        return accordions.some(id => {
            const element = document.getElementById(id);
            if (!element) return false;
            
            // Check for expanded state
            return element.classList.contains('expanded') ||
                   element.style.display !== 'none' ||
                   (element.style.maxHeight && element.style.maxHeight !== '0px');
        });
    }

    hasAnyActiveInput() {
        const activeElement = document.activeElement;
        if (!activeElement) return false;

        const inputTypes = ['INPUT', 'TEXTAREA', 'SELECT'];
        return inputTypes.includes(activeElement.tagName) ||
               activeElement.contentEditable === 'true' ||
               activeElement.contentEditable === '';
    }

    hasAnyModalOpen() {
        // Check for any modal or popup windows that might be open
        // This includes settings windows, favorites, etc.
        // EXCLUDE the popup container itself (body.popup or .popup__container)
        const modals = document.querySelectorAll('.modal, .overlay, [role="dialog"]');
        return Array.from(modals).some(modal => {
            // Skip the popup body and popup container elements
            if (modal.classList.contains('popup') || modal.classList.contains('popup__container')) {
                return false;
            }
            
            const style = window.getComputedStyle(modal);
            return style.display !== 'none' && 
                   style.visibility !== 'hidden' &&
                   style.opacity !== '0';
        });
    }

    isAnyFormElementFocused() {
        const activeElement = document.activeElement;
        if (!activeElement) return false;

        // Check if any form element has focus
        return activeElement.matches('input, textarea, select, button[type="submit"], [contenteditable="true"]');
    }

    isTodoManagerEditing() {
        // Check if todo manager is in editing mode
        const todoInput = document.getElementById('tasksNewTaskInput');
        if (todoInput && document.activeElement === todoInput) {
            return true;
        }

        // Check for any todo editing inputs that might be dynamically created
        const editingInputs = document.querySelectorAll('.tasks-edit-input, .todo-edit-input, [data-editing="true"]');
        return editingInputs.length > 0;
    }

    executeButtonAction(buttonId) {
        // EXECUTION GUARD: Prevent rapid sequential button.click() calls
        const now = Date.now();
        if (!this.lastButtonClick) this.lastButtonClick = {};
        
        if (this.lastButtonClick[buttonId] && now - this.lastButtonClick[buttonId] < 400) {
            console.log(`🔒 PopupShortcuts: Button '${buttonId}' clicked too soon after last click, ignoring (debounced)`);
            return;
        }
        
        this.lastButtonClick[buttonId] = now;

        const button = document.getElementById(buttonId);
        if (button && !button.disabled) {
            console.log(`🔗 PopupShortcuts: Executing button action for '${buttonId}'`);
            // Trigger the button click
            button.click();
        }
    }

    executeAccordionAction(actionId) {
        switch (actionId) {
            case 'quickTimer':
                this.toggleQuickTimer();
                break;
            case 'tasksToggle':
                this.toggleTasksCompact();
                break;
            case 'locationChanger':
                this.toggleLocationChanger();
                break;
        }
    }

    toggleQuickTimer() {
        const quickTimerContent = document.getElementById('quickTimerContent');
        const isCurrentlyExpanded = quickTimerContent && quickTimerContent.classList.contains('expanded');
        
        // Use the existing toggleAccordion function from quick-timer.js
        if (typeof window.toggleAccordion === 'function') {
            window.toggleAccordion();
        } else {
            // Fallback to direct DOM manipulation if function not available
            const quickTimerIcon = document.getElementById('quickTimerIcon');
            const quickTimerHeader = document.getElementById('quickTimerHeader');

            if (quickTimerContent && quickTimerIcon && quickTimerHeader) {
                if (isCurrentlyExpanded) {
                    quickTimerContent.classList.remove('expanded');
                    quickTimerIcon.classList.remove('expanded');
                    quickTimerHeader.classList.remove('expanded');
                } else {
                    quickTimerContent.classList.add('expanded');
                    quickTimerIcon.classList.add('expanded');
                    quickTimerHeader.classList.add('expanded');
                }
            }
        }
        
        // Auto-focus first timer button when opening (not closing)
        if (!isCurrentlyExpanded) {
            setTimeout(() => {
                this.focusFirstTimerButton();
                this.setupTimerKeyboardNavigation();
            }, 100); // Small delay to ensure accordion animation completes
        } else {
            // Clean up keyboard navigation when closing
            this.cleanupTimerKeyboardNavigation();
        }
    }

    toggleTasksCompact() {
        const tasksContent = document.getElementById('tasksContent');
        
        // Check if tasks section is currently expanded before toggling
        const isCurrentlyExpanded = tasksContent && (
            tasksContent.classList.contains('expanded') ||
            tasksContent.style.display !== 'none'
        );
        
        // Try to use the existing pomodoro popup method first
        if (window.pomodoroPopup && typeof window.pomodoroPopup.toggleTasksContent === 'function') {
            window.pomodoroPopup.toggleTasksContent();
        } else {
            // Fallback to direct DOM manipulation
            const tasksCompactIcon = document.getElementById('tasksCompactIcon');

            if (tasksContent && tasksCompactIcon) {
                const isExpanded = tasksContent.classList.contains('expanded');
                
                if (isExpanded) {
                    tasksContent.classList.remove('expanded');
                    tasksContent.style.display = 'none';
                    tasksCompactIcon.classList.remove('expanded');
                } else {
                    tasksContent.classList.add('expanded');
                    tasksContent.style.display = 'block';
                    tasksCompactIcon.classList.add('expanded');
                    
                    // Load todos when expanding (like the original function does)
                    if (window.pomodoroPopup && typeof window.pomodoroPopup.updateTodoList === 'function') {
                        window.pomodoroPopup.updateTodoList();
                    }
                }
            }
        }
        
        // Auto-focus tasks input when opening (not closing)
        if (!isCurrentlyExpanded) {
            setTimeout(() => {
                this.focusTasksInput();
            }, 100); // Small delay to ensure accordion animation completes
        }
    }

    toggleLocationChanger() {
        const header = document.getElementById('locationChangerHeader');
        const content = document.getElementById('locationChangerContent');
        
        if (header && content) {
            // Check if accordion is currently expanded
            const computedStyle = window.getComputedStyle(content);
            const isCurrentlyExpanded = content.classList.contains('expanded') || 
                                       (computedStyle.height !== '0px' && computedStyle.height !== 'auto');
            
            header.click(); // This triggers the existing click handler that handles expand/collapse
            
            // Auto-focus location input when opening (not closing)
            if (!isCurrentlyExpanded) {
                setTimeout(() => {
                    this.focusLocationInput();
                }, 200); // Longer delay to ensure accordion animation and typeahead initialization completes
            }
        }
    }

    // Location Input Focus Method
    focusLocationInput() {
        // Target the specific typeahead input field
        const locationInput = document.querySelector('#place.tt-input');
        
        if (locationInput) {
            try {
                // Step 1: Simulate a proper click event to activate typeahead
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                locationInput.dispatchEvent(clickEvent);
                
                // Step 2: Focus the input field
                locationInput.focus();
                
                // Step 3: Select all text for immediate editing
                locationInput.select();
            } catch (error) {
                // Fallback: try with basic #place selector
                const fallbackInput = document.querySelector('#place');
                if (fallbackInput) {
                    fallbackInput.click();
                    fallbackInput.focus();
                    fallbackInput.select();
                }
            }
        }
    }

    // Tasks Input Focus Method
    focusTasksInput() {
        // Target the tasks new task input field
        const tasksInput = document.getElementById('tasksNewTaskInput');
        
        if (tasksInput) {
            try {
                // Step 1: Focus the input field
                tasksInput.focus();
                
                // Step 2: Select all text for immediate editing (if any exists)
                tasksInput.select();
            } catch (error) {
                console.error('PopupShortcuts: Error focusing tasks input:', error);
            }
        }
    }

    // Timer Keyboard Navigation Methods
    focusFirstTimerButton() {
        const firstButton = document.querySelector('.timer-preset');
        if (firstButton) {
            firstButton.focus();
        }
    }

    setupTimerKeyboardNavigation() {
        // Remove existing listener if it exists
        this.cleanupTimerKeyboardNavigation();
        
        // Add timer-specific keyboard handler WITHOUT capture
        this.timerKeyHandler = (event) => this.handleTimerKeyNavigation(event);
        document.addEventListener('keydown', this.timerKeyHandler, false);
    }

    cleanupTimerKeyboardNavigation() {
        if (this.timerKeyHandler) {
            // BUG FIX: Match the capture parameter used when adding the listener
            document.removeEventListener('keydown', this.timerKeyHandler, false);
            this.timerKeyHandler = null;
        }
    }

    handleTimerKeyNavigation(event) {
        // Only handle when timer is expanded and a timer button is focused
        const quickTimerContent = document.getElementById('quickTimerContent');
        const isTimerExpanded = quickTimerContent && quickTimerContent.classList.contains('expanded');
        const activeElement = document.activeElement;
        const isTimerButtonFocused = activeElement && activeElement.classList.contains('timer-preset');
        
        if (!isTimerExpanded || !isTimerButtonFocused) {
            return;
        }

        // Handle arrow keys and Enter
        switch (event.key) {
            case 'ArrowLeft':
            case 'ArrowRight':
            case 'ArrowUp':
            case 'ArrowDown':
                event.preventDefault();
                event.stopPropagation();
                this.navigateTimerGrid(event.key, activeElement);
                break;
            case 'Enter':
                event.preventDefault();
                event.stopPropagation();
                
                // Check if timer is currently running
                const isTimerRunning = window.isQuickTimerActive && window.isQuickTimerActive();
                
                if (isTimerRunning) {
                    // Timer is running - add time and keep accordion open
                    if (activeElement.dataset.custom) {
                        // Handle CUSTOM button when timer is running - show input for additional time
                        activeElement.click();
                    } else {
                        // Handle preset buttons - add time to running timer
                        const minutes = parseFloat(activeElement.dataset.minutes);
                        if (window.addTimeToQuickTimer && !isNaN(minutes)) {
                            window.addTimeToQuickTimer(minutes);
                        }
                    }
                    // Don't close accordion - user may want to add more time
                } else {
                    // Timer not running - start new timer and close accordion  
                    activeElement.click();
                }
                break;
        }
    }

    navigateTimerGrid(direction, currentButton) {
        const timerButtons = Array.from(document.querySelectorAll('.timer-preset'));
        const currentIndex = timerButtons.indexOf(currentButton);
        
        if (currentIndex === -1) return;
        
        let newIndex;
        const gridWidth = 4; // 4 buttons per row
        const gridHeight = 2; // 2 rows
        
        const currentRow = Math.floor(currentIndex / gridWidth);
        const currentCol = currentIndex % gridWidth;
        
        switch (direction) {
            case 'ArrowLeft':
                newIndex = currentIndex > 0 ? currentIndex - 1 : timerButtons.length - 1;
                break;
            case 'ArrowRight':
                newIndex = currentIndex < timerButtons.length - 1 ? currentIndex + 1 : 0;
                break;
            case 'ArrowUp':
                if (currentRow === 0) {
                    // Wrap to bottom row, same column
                    newIndex = Math.min((gridHeight - 1) * gridWidth + currentCol, timerButtons.length - 1);
                } else {
                    // Move to row above
                    newIndex = (currentRow - 1) * gridWidth + currentCol;
                }
                break;
            case 'ArrowDown':
                if (currentRow === gridHeight - 1 || currentIndex + gridWidth >= timerButtons.length) {
                    // Wrap to top row, same column
                    newIndex = currentCol;
                } else {
                    // Move to row below
                    newIndex = Math.min(currentIndex + gridWidth, timerButtons.length - 1);
                }
                break;
        }
        
        if (newIndex !== undefined && timerButtons[newIndex]) {
            timerButtons[newIndex].focus();
        }
    }

    showShortcutFeedback(key, actionName) {
        const feedback = document.createElement('div');
        feedback.textContent = `⚡ ${actionName} (${key})`;
        feedback.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 600;
            z-index: 10000;
            box-shadow: 0 8px 24px rgba(139, 92, 246, 0.3);
            border: 1px solid #8b5cf6;
            animation: popupShortcutFeedback 2s ease-out forwards;
        `;

        // Add animation keyframes
        if (!document.getElementById('popup-shortcut-feedback-styles')) {
            const style = document.createElement('style');
            style.id = 'popup-shortcut-feedback-styles';
            style.textContent = `
                @keyframes popupShortcutFeedback {
                    0% {
                        opacity: 0;
                        transform: translateX(100px) scale(0.8);
                    }
                    10% {
                        opacity: 1;
                        transform: translateX(0) scale(1.05);
                    }
                    20% {
                        transform: translateX(0) scale(1);
                    }
                    80% {
                        opacity: 1;
                        transform: translateX(0) scale(1);
                    }
                    100% {
                        opacity: 0;
                        transform: translateX(100px) scale(0.8);
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(feedback);

        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 2000);
    }

    // Settings update method
    updateSettings(enabled) {
        this.shortcutsEnabled = enabled;
    }

    // Debug method for testing
    debugCurrentState() {
        console.log('=== POPUP SHORTCUTS DEBUG ===');
        console.log('Popup focused:', this.isPopupFocused());
        console.log('Any accordion open (ignored in popup):', this.hasAnyAccordionOpen());
        console.log('Any active input:', this.hasAnyActiveInput());
        console.log('Any modal open:', this.hasAnyModalOpen());
        console.log('Any form element focused:', this.isAnyFormElementFocused());
        console.log('Todo manager editing:', this.isTodoManagerEditing());
        console.log('Safe for shortcuts:', this.isSafeForShortcuts());
        console.log('Shortcuts enabled:', this.shortcutsEnabled);
        console.log('Active element:', document.activeElement);
        console.log('Available shortcuts:', Array.from(this.shortcuts.keys()));
        console.log('=============================');
    }
}

// Initialize when DOM is ready - only in popup context
function initializePopupShortcuts() {
    console.log('🟡 PopupShortcuts: initializePopupShortcuts() called on:', window.location.href);
    try {
        // Quick context check before initializing
        const protocolCheck = window.location.protocol === 'chrome-extension:';
        const pathCheck = window.location.pathname.includes('popup.html');
        const titleCheck = document.title.includes('SEO Time Machines');
        const isPopup = protocolCheck && (pathCheck || titleCheck);
        
        console.log('🟡 PopupShortcuts: Initialization context check:', {
            protocolCheck: protocolCheck,
            pathCheck: pathCheck, 
            titleCheck: titleCheck,
            isPopup: isPopup,
            currentTitle: document.title
        });
        
        if (!isPopup) {
            console.log('🟡 PopupShortcuts: Not initializing - failed context check');
            return;
        }
        
        if (!window.popupShortcutsManager) {
            console.log('🟡 PopupShortcuts: Creating new PopupShortcutsManager instance');
            window.popupShortcutsManager = new PopupShortcutsManager();
        } else {
            console.log('🟡 PopupShortcuts: PopupShortcutsManager already exists');
        }
    } catch (error) {
        console.error('🔴 PopupShortcuts: Error initializing:', error);
    }
}

// Debug functions for console
window.debugPopupShortcuts = function() {
    if (window.popupShortcutsManager) {
        window.popupShortcutsManager.debugCurrentState();
    } else {
        console.log('Popup shortcuts manager not initialized');
    }
};

window.testPopupShortcut = function(key) {
    console.log(`Testing popup shortcut: ${key}`);
    const event = new KeyboardEvent('keydown', { key: key.toLowerCase() });
    document.dispatchEvent(event);
};

// Initialize on DOM ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePopupShortcuts);
} else {
    initializePopupShortcuts();
}

// Also try to initialize when the window loads
window.addEventListener('load', initializePopupShortcuts);

// Try to initialize after a delay to ensure popup.js has finished
setTimeout(initializePopupShortcuts, 1000);

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PopupShortcutsManager;
} else {
    window.PopupShortcutsManager = PopupShortcutsManager;
}