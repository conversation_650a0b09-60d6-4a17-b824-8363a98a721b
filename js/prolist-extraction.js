// Pro List Extraction Module
// Handles extraction and analysis of Google Local Services Pro List pages

class ProListExtractor {
    constructor() {
        this.isProListPage = false;
        this.businesses = [];
        this.analysisData = null;
        this.isAnalyzing = false;
        
        // Add storage for extracted data types
        this.lastExtractedServices = null;
        this.lastExtractedAttributes = null;
        this.lastExtractedReviews = null;
        
        this.init();
    }

    init() {
        // Check if we're on a Pro List page
        this.isProListPage = this.detectProListPage();
        
        if (this.isProListPage) {
            console.log('ProListExtractor: Pro List page detected');
            this.setupMessageListener();
            this.initializeTrackedDomains();
        }
    }

    detectProListPage() {
        const url = window.location.href;
        return url.includes('google.com/localservices/prolist');
    }

    initializeTrackedDomains() {
        // Initialize tracked domains for ProList pages
        console.log('ProListExtractor: Initializing tracked domains for ProList...');
        
        // Load tracked domains settings
        if (typeof chrome !== 'undefined' && chrome.storage) {
            chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
                const settings = result.gmbExtractorSettings || {};
                this.trackedDomainsEnabled = settings.trackedDomainsEnabled !== false;
                this.trackedDomainsList = settings.trackedDomainsList || [];
                this.trackedDomainsColor = settings.trackedDomainsColor || '#7c3aed';
                
                console.log('ProListExtractor: Tracked domains settings:', {
                    enabled: this.trackedDomainsEnabled,
                    domains: this.trackedDomainsList,
                    color: this.trackedDomainsColor
                });
                
                if (this.trackedDomainsEnabled && this.trackedDomainsList.length > 0) {
                    // Inject CSS styles first
                    this.injectTrackedDomainsStyles();
                    
                    // Wait for page to load then highlight
                    setTimeout(() => this.highlightTrackedDomains(), 1000);
                    
                    // Set up observer for dynamic content
                    this.setupTrackedDomainsObserver();
                }
            });
        }
    }

    // Function to normalize domain for comparison
    normalizeDomain(domain) {
        return domain.toLowerCase().replace(/^www\./, '');
    }

    // Function to extract domain from URL
    extractDomainFromUrl(url) {
        try {
            const urlObj = new URL(url);
            return this.normalizeDomain(urlObj.hostname);
        } catch (error) {
            // If URL parsing fails, try to extract domain from href text
            const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^\/\s]+)/);
            return match ? this.normalizeDomain(match[1]) : '';
        }
    }

    // Function to check if a domain matches any tracked domain
    isTrackedDomain(domain) {
        const normalizedDomain = this.normalizeDomain(domain);
        console.log(`ProListExtractor: Checking if "${domain}" (normalized: "${normalizedDomain}") is tracked`);
        
        const isTracked = this.trackedDomainsList.some(trackedDomain => {
            const normalizedTracked = this.normalizeDomain(trackedDomain);
            const exactMatch = normalizedDomain === normalizedTracked;
            const subdomainMatch = normalizedDomain.endsWith('.' + normalizedTracked);
            
            console.log(`ProListExtractor: Comparing with "${trackedDomain}" (normalized: "${normalizedTracked}") - exact: ${exactMatch}, subdomain: ${subdomainMatch}`);
            
            return exactMatch || subdomainMatch;
        });
        
        console.log(`ProListExtractor: Final result for "${domain}": ${isTracked}`);
        return isTracked;
    }

    highlightTrackedDomains() {
        if (!this.trackedDomainsEnabled || !this.trackedDomainsList.length) return;
        
        console.log('ProListExtractor: Starting tracked domain highlighting for ProList...');
        
        // Find all business elements
        const businessElements = this.findBusinessElements();
        let trackedDomainPositions = [];
        let sponsoredCount = 0;
        
        // Count sponsored results in top 4 positions
        for (let i = 0; i < Math.min(4, businessElements.length); i++) {
            const element = businessElements[i];
            
            // Check if sponsored using the same logic as extraction
            const isSponsored = element.getAttribute('data-test-id') === 'paid-list-card' ||
                               element.querySelector('.uDmIZc')?.textContent?.trim()?.toLowerCase() === 'sponsored' ||
                               element.querySelector('a[href*="aclk"]') !== null;
            
            if (isSponsored) {
                sponsoredCount++;
                console.log(`ProListExtractor: Found sponsored business at position ${i + 1}`);
            }
        }
        
        console.log(`ProListExtractor: Found ${sponsoredCount} sponsored businesses in top 4 positions`);
        
        // Process each business element
        businessElements.forEach((element, index) => {
            try {
                // Get business name for debugging
                const businessNameElement = element.querySelector('.I9iumb .rgnuSb.xYjf2e');
                const businessName = businessNameElement?.textContent?.trim() || `Business ${index + 1}`;
                
                // Find website links - look in multiple containers
                const allLinks = element.querySelectorAll('a[href]');
                let domain = '';
                let websiteUrl = '';
                let foundLinks = [];
                
                // Debug all links found
                for (const link of allLinks) {
                    const href = link.getAttribute('href') || '';
                    foundLinks.push(href.substring(0, 80) + (href.length > 80 ? '...' : ''));
                }
                
                console.log(`ProListExtractor: Business ${index + 1} "${businessName}" - Found ${allLinks.length} links:`, foundLinks);
                
                // Look for website links in order of preference
                for (const link of allLinks) {
                    const href = link.getAttribute('href') || '';
                    const ariaLabel = link.getAttribute('aria-label') || '';
                    
                    // Method 1: Website links with aria-label="Website" (ProList specific)
                    if (ariaLabel.toLowerCase() === 'website' && href.startsWith('http') && !href.includes('/aclk?') && !href.includes('googleadservices')) {
                        websiteUrl = href;
                        domain = this.extractDomainFromUrl(href);
                        console.log(`ProListExtractor: Business ${index + 1} - Found aria-label="Website": ${domain} (${href})`);
                        console.log(`ProListExtractor: Business ${index + 1} - Extracted domain: "${domain}"`);
                        console.log(`ProListExtractor: Business ${index + 1} - Tracked domains list:`, this.trackedDomainsList);
                        console.log(`ProListExtractor: Business ${index + 1} - Is tracked domain check:`, this.isTrackedDomain(domain));
                        break;
                    }
                    
                    // Method 2: Check data-website-url attribute inside the link
                    const dataWebsiteDiv = link.querySelector('[data-website-url]');
                    if (dataWebsiteDiv) {
                        const dataWebsiteUrl = dataWebsiteDiv.getAttribute('data-website-url');
                        if (dataWebsiteUrl && dataWebsiteUrl.startsWith('http')) {
                            websiteUrl = dataWebsiteUrl;
                            domain = this.extractDomainFromUrl(dataWebsiteUrl);
                            console.log(`ProListExtractor: Business ${index + 1} - Found data-website-url: ${domain} (${dataWebsiteUrl.substring(0, 60)}...)`);
                            break;
                        }
                    }
                    
                    // Method 3: Direct website links (starts with http, not ad URLs)
                    if (href.startsWith('http') && !href.includes('/aclk?') && !href.includes('googleadservices') && !href.includes('google.com/pagead')) {
                        websiteUrl = href;
                        domain = this.extractDomainFromUrl(href);
                        console.log(`ProListExtractor: Business ${index + 1} - Found direct website: ${domain} (${href.substring(0, 60)}...)`);
                        break;
                    }
                    
                    // Method 4: Google redirect URLs
                    if (href.includes('/url?') && href.includes('url=')) {
                        try {
                            const urlParams = new URLSearchParams(href.split('?')[1]);
                            const actualUrl = urlParams.get('url');
                            if (actualUrl && actualUrl.startsWith('http')) {
                                websiteUrl = actualUrl;
                                domain = this.extractDomainFromUrl(actualUrl);
                                console.log(`ProListExtractor: Business ${index + 1} - Found redirect website: ${domain} (${actualUrl.substring(0, 60)}...)`);
                                break;
                            }
                        } catch (e) {
                            console.log(`ProListExtractor: Business ${index + 1} - Error parsing redirect URL:`, e);
                        }
                    }
                }
                
                if (!domain) {
                    console.log(`ProListExtractor: Business ${index + 1} "${businessName}" - No website URL found`);
                } else if (this.isTrackedDomain(domain)) {
                    console.log(`ProListExtractor: Business ${index + 1} "${businessName}" - TRACKED DOMAIN FOUND: ${domain}`);
                    
                    if (businessNameElement && !businessNameElement.classList.contains('gmb-tracked-domain-title')) {
                        // Apply highlighting
                        businessNameElement.classList.add('gmb-tracked-domain-title');
                        businessNameElement.id = `gmb-tracked-prolist-${index}`;
                        
                        // Calculate positions
                        const actualPosition = index + 1;
                        const adjustedPosition = Math.max(1, actualPosition - sponsoredCount);
                        
                        trackedDomainPositions.push({
                            position: `P${adjustedPosition}`, // P for ProList
                            actualPosition: `P${actualPosition}`,
                            elementId: `gmb-tracked-prolist-${index}`,
                            domain: domain,
                            type: 'prolist'
                        });
                        
                        console.log(`ProListExtractor: Highlighted ${domain} at actual position P${actualPosition}, adjusted to P${adjustedPosition} (${sponsoredCount} sponsored in top 4)`);
                    }
                } else {
                    console.log(`ProListExtractor: Business ${index + 1} "${businessName}" - Domain ${domain} not in tracked list`);
                }
            } catch (error) {
                console.error('ProListExtractor: Error processing tracked domain for business', index, error);
            }
        });
        
        // Create jump navigation if we found tracked domains
        if (trackedDomainPositions.length > 0) {
            this.createJumpNavigation(trackedDomainPositions);
        }
        
        console.log(`ProListExtractor: Tracked domain highlighting complete - found ${trackedDomainPositions.length} tracked domains`);
    }

    createJumpNavigation(trackedPositions) {
        // Remove existing jump navigation
        const existingNav = document.getElementById('gmb-jump-navigation-prolist');
        if (existingNav) {
            existingNav.remove();
        }

        // Create navigation container
        const jumpNav = document.createElement('div');
        jumpNav.id = 'gmb-jump-navigation-prolist';
        jumpNav.style.cssText = `
            background: #1a1a1a !important;
            border: 1px solid #333333 !important;
            border-radius: 8px !important;
            padding: 12px 16px !important;
            margin: 16px 0 !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            font-family: Google Sans,Roboto,Arial,sans-serif !important;
            color: #9aa0a6 !important;
            font-size: 14px !important;
            position: sticky !important;
            top: 20px !important;
            z-index: 1000 !important;
        `;

        // Add ProList label
        const label = document.createElement('span');
        label.textContent = 'ProList:';
        label.style.cssText = `
            margin-right: 8px !important;
            color: #9aa0a6 !important;
            font-weight: 500 !important;
            min-width: 50px !important;
        `;
        jumpNav.appendChild(label);

        // Create jump buttons for each tracked position
        trackedPositions.forEach((tracked) => {
            // Create container for button + URL
            const itemContainer = document.createElement('div');
            itemContainer.style.cssText = `
                display: flex !important;
                align-items: center !important;
                gap: 8px !important;
            `;

            // Create jump button
            const jumpButton = document.createElement('button');
            jumpButton.textContent = tracked.position;
            jumpButton.style.cssText = `
                background: ${this.trackedDomainsColor} !important;
                color: white !important;
                border: none !important;
                border-radius: 4px !important;
                padding: 4px 8px !important;
                font-size: 12px !important;
                font-weight: 600 !important;
                cursor: pointer !important;
                transition: all 0.2s ease !important;
                min-width: 24px !important;
                text-align: center !important;
                flex-shrink: 0 !important;
            `;

            // Create domain URL text
            const domainText = document.createElement('span');
            domainText.textContent = tracked.domain;
            domainText.style.cssText = `
                color: #9aa0a6 !important;
                font-size: 12px !important;
                font-weight: 400 !important;
                cursor: pointer !important;
                transition: color 0.2s ease !important;
            `;

            // Add hover effects
            jumpButton.addEventListener('mouseenter', () => {
                jumpButton.style.filter = 'brightness(1.2)';
                jumpButton.style.transform = 'scale(1.05)';
            });

            jumpButton.addEventListener('mouseleave', () => {
                jumpButton.style.filter = 'brightness(1)';
                jumpButton.style.transform = 'scale(1)';
            });

            domainText.addEventListener('mouseenter', () => {
                domainText.style.color = '#ffffff';
            });

            domainText.addEventListener('mouseleave', () => {
                domainText.style.color = '#9aa0a6';
            });

            // Add click functionality
            const clickHandler = (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                const targetElement = document.getElementById(tracked.elementId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                    
                    // Add brief highlight effect
                    const originalFilter = targetElement.style.filter;
                    targetElement.style.filter = 'brightness(1.3)';
                    setTimeout(() => {
                        targetElement.style.filter = originalFilter;
                    }, 1000);
                }
            };

            jumpButton.addEventListener('click', clickHandler);
            domainText.addEventListener('click', clickHandler);

            itemContainer.appendChild(jumpButton);
            itemContainer.appendChild(domainText);
            jumpNav.appendChild(itemContainer);
        });

        // Insert after the search box container (WJOnGe)
        const searchBoxContainer = document.querySelector('.WJOnGe');
        if (searchBoxContainer && searchBoxContainer.parentNode) {
            // Insert after the search box container
            if (searchBoxContainer.nextSibling) {
                searchBoxContainer.parentNode.insertBefore(jumpNav, searchBoxContainer.nextSibling);
            } else {
                searchBoxContainer.parentNode.appendChild(jumpNav);
            }
            console.log('ProListExtractor: Jump navigation inserted after .WJOnGe search box');
        } else {
            // Fallback: Try .tBsrAe element
            const tBsrAeElement = document.querySelector('.tBsrAe');
            if (tBsrAeElement && tBsrAeElement.parentNode) {
                if (tBsrAeElement.nextSibling) {
                    tBsrAeElement.parentNode.insertBefore(jumpNav, tBsrAeElement.nextSibling);
                } else {
                    tBsrAeElement.parentNode.appendChild(jumpNav);
                }
                console.log('ProListExtractor: Jump navigation inserted after .tBsrAe element (fallback)');
            } else {
                // Final fallback: Insert before the main list container
                const container = document.querySelector('.ykYNg[role="list"]') || document.body;
                if (container && container.parentNode) {
                    container.parentNode.insertBefore(jumpNav, container);
                    console.log('ProListExtractor: Jump navigation inserted before main list (final fallback)');
                }
            }
        }
    }

    setupTrackedDomainsObserver() {
        // Observer for dynamic content changes
        const observer = new MutationObserver((mutations) => {
            let shouldHighlight = false;
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1 && (node.classList.contains('NwqBmc') || node.querySelector && node.querySelector('.NwqBmc'))) {
                            shouldHighlight = true;
                        }
                    });
                }
            });
            
            if (shouldHighlight) {
                setTimeout(() => this.highlightTrackedDomains(), 100);
            }
        });

        // Start observing
        const targetNode = document.querySelector('.ykYNg[role="list"]') || document.body;
        observer.observe(targetNode, {
            childList: true,
            subtree: true
        });

        console.log('ProListExtractor: Tracked domains observer initialized');
    }

    injectTrackedDomainsStyles() {
        // Remove existing styles if they exist
        const existingStyle = document.getElementById('gmb-tracked-domains-prolist-styles');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        const style = document.createElement('style');
        style.id = 'gmb-tracked-domains-prolist-styles';
        
        // Convert hex color to rgb for background opacity
        const hexToRgb = (hex) => {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        };
        
        const rgb = hexToRgb(this.trackedDomainsColor);
        const backgroundColorLight = rgb ? `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.05)` : 'rgba(124, 58, 237, 0.05)';
        const backgroundColorHover = rgb ? `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)` : 'rgba(139, 92, 246, 0.1)';
        
        style.textContent = `
            .gmb-tracked-domain-title {
                border: 2px dashed ${this.trackedDomainsColor} !important;
                padding: 4px 8px !important;
                border-radius: 4px !important;
                display: inline-block !important;
                background-color: ${backgroundColorLight} !important;
            }
            
            .gmb-tracked-domain-title:hover {
                border-color: ${this.trackedDomainsColor} !important;
                background-color: ${backgroundColorHover} !important;
                filter: brightness(1.1) !important;
            }
        `;
        
        document.head.appendChild(style);
        console.log('ProListExtractor: Tracked domains CSS styles injected');
    }

    setupMessageListener() {
        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            console.log('🔔 ProListExtractor: Received message:', request);
            
            // Handle both 'action' and 'type' properties for compatibility
            const messageType = request.action || request.type;
            console.log('🎯 ProListExtractor: Message type:', messageType);
            
            switch (messageType) {
                case 'detectProList':
                    sendResponse({
                        isProList: this.isProListPage,
                        url: window.location.href
                    });
                    break;
                    
                case 'analyzeProList':
                    this.analyzeProListBusinesses()
                        .then(result => sendResponse(result))
                        .catch(error => sendResponse({ error: error.message }));
                    return true; // Keep message channel open for async response
                    
                case 'startProListReviewAnalysis':
                    console.log('🚀 ProListExtractor: Received startProListReviewAnalysis message');
                    const maxReviews = request.maxReviews || null;
                    console.log(`🔢 ProListExtractor: Range parameter received: ${maxReviews ? maxReviews + ' reviews' : 'no limit'}`);
                    this.startProListReviewAnalysis(maxReviews)
                        .then(result => {
                            console.log('✅ ProListExtractor: Review analysis completed:', result);
                            sendResponse(result);
                        })
                        .catch(error => {
                            console.error('❌ ProListExtractor: Review analysis failed:', error);
                            sendResponse({ error: error.message });
                        });
                    return true;
                    
                case 'stopProListReviewAnalysis':
                    this.stopReviewAnalysis();
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ error: 'Unknown action' });
            }
        });
    }

    async analyzeProListBusinesses() {
        try {
            console.log('ProListExtractor: Starting Pro List analysis...');
            
            // Extract search term from URL
            const searchTerm = this.extractSearchTerm();
            
            // Find all business listings
            const businessElements = this.findBusinessElements();
            console.log(`ProListExtractor: Found ${businessElements.length} business elements`);
            
            // Extract basic business data
            this.businesses = this.extractBusinessData(businessElements);
            
            // Perform category analysis (similar to multiple listings)
            const categoryAnalysis = this.analyzeBusinessCategories();
            
            // Extract review snippets
            const reviewData = this.extractReviewsFromBusinessElements();
            
            // Create analysis result
            this.analysisData = {
                type: 'prolist_analysis',
                searchTerm: searchTerm,
                timestamp: new Date().toISOString(),
                totalBusinesses: this.businesses.length,
                businesses: this.businesses,
                reviewSnippets: reviewData,
                analysis: {
                    categories: categoryAnalysis
                }
            };
            
            console.log(`ProListExtractor: Total businesses found: ${this.businesses.length}`);
            
            console.log('ProListExtractor: Analysis complete:', this.analysisData);
            
            return {
                success: true,
                isProList: true,
                data: this.analysisData
            };
            
        } catch (error) {
            console.error('ProListExtractor: Analysis error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    extractSearchTerm() {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            const query = urlParams.get('q');
            return query ? decodeURIComponent(query) : 'Unknown Search';
        } catch (error) {
            console.error('ProListExtractor: Error extracting search term:', error);
            return 'Unknown Search';
        }
    }

    findBusinessElements() {
        // Look for the main list container with class ykYNg and role="list"
        const mainListContainer = document.querySelector('.ykYNg[role="list"]');
        
        if (!mainListContainer) {
            console.log('ProListExtractor: Main list container (.ykYNg[role="list"]) not found');
            return [];
        }
        
        // Find all business listing containers - each business is in a div with the controller
        // The full business card includes both the info section (.NwqBmc) and actions section (.DyM7H)
        const businessCards = mainListContainer.querySelectorAll('[jscontroller="xkZ6Lb"]');
        
        console.log(`ProListExtractor: Found ${businessCards.length} business cards in main container`);
        
        // Debug: Log structure of first element if found
        if (businessCards.length > 0) {
            const firstCard = businessCards[0];
            const infoSection = firstCard.querySelector('.NwqBmc');
            const actionsSection = firstCard.querySelector('.DyM7H');
            
            console.log('ProListExtractor: First card structure:', {
                hasInfoSection: !!infoSection,
                hasActionsSection: !!actionsSection,
                hasBusinessName: !!firstCard.querySelector('.rgnuSb.xYjf2e'),
                hasRating: !!firstCard.querySelector('.rGaJuf'),
                hasReviewCount: !!firstCard.querySelector('.leIgTe'),
                hasWebsiteLink: !!firstCard.querySelector('a[aria-label="Website"]'),
                businessNameText: firstCard.querySelector('.rgnuSb.xYjf2e')?.textContent?.trim(),
                ratingText: firstCard.querySelector('.rGaJuf')?.textContent?.trim(),
                reviewCountText: firstCard.querySelector('.leIgTe')?.textContent?.trim(),
                websiteHref: firstCard.querySelector('a[aria-label="Website"]')?.getAttribute('href')
            });
        }
        
        // Filter out elements that don't look like business listings
        const filteredElements = Array.from(businessCards).filter(element => {
            const hasBusinessName = element.querySelector('.rgnuSb.xYjf2e');
            const hasRating = element.querySelector('.rGaJuf');
            const hasReviewCount = element.querySelector('.leIgTe');
            
            return hasBusinessName || hasRating || hasReviewCount;
        });
        
        console.log(`ProListExtractor: Filtered to ${filteredElements.length} business cards`);
        return filteredElements;
    }

    extractBusinessData(businessElements) {
        const businesses = [];
        
        businessElements.forEach((element, index) => {
            try {
                const business = this.extractSingleBusinessData(element, index + 1);
                if (business.businessName) {
                    businesses.push(business);
                }
            } catch (error) {
                console.error(`ProListExtractor: Error extracting business ${index + 1}:`, error);
                businesses.push({
                    index: index + 1,
                    businessName: `Business ${index + 1}`,
                    error: error.message
                });
            }
        });
        
        return businesses;
    }

    extractSingleBusinessData(element, index) {
        const business = {
            index: index,
            businessName: '',
            rating: null,
            reviewCount: null,
            categories: [],
            address: '',
            phone: '',
            website: '',
            sponsored: false,
            timestamp: new Date().toISOString()
        };

        // Extract business name from the first I9iumb div with rgnuSb xYjf2e classes
        const nameElement = element.querySelector('.I9iumb .rgnuSb.xYjf2e');
        if (nameElement && nameElement.textContent.trim()) {
            business.businessName = nameElement.textContent.trim();
        }

        // Check if this is a sponsored listing - ProList specific detection
        business.sponsored = false;
        
        // Method 1: Check for data-test-id="paid-list-card"
        if (element.getAttribute('data-test-id') === 'paid-list-card') {
            business.sponsored = true;
            console.log(`ProListExtractor: Business ${index} "${business.businessName}" is SPONSORED (paid-list-card)`);
        }
        
        // Method 2: Check for "Sponsored" text
        if (!business.sponsored) {
            const sponsoredText = element.querySelector('.uDmIZc');
            if (sponsoredText && sponsoredText.textContent.trim().toLowerCase() === 'sponsored') {
                business.sponsored = true;
                console.log(`ProListExtractor: Business ${index} "${business.businessName}" is SPONSORED (sponsored text)`);
            }
        }
        
        // Method 3: Check website link for ad URLs
        if (!business.sponsored) {
            const websiteLink = element.querySelector('a[href*="aclk"]') || element.querySelector('a[href*="googleadservices"]');
            if (websiteLink) {
                const href = websiteLink.getAttribute('href') || '';
                business.sponsored = true;
                console.log(`ProListExtractor: Business ${index} "${business.businessName}" is SPONSORED (ad URL: ${href.substring(0, 60)}...)`);
            }
        }

        // Extract rating using the specific class mentioned: rGaJuf
        const ratingElement = element.querySelector('.rGaJuf');
        if (ratingElement) {
            const ratingText = ratingElement.textContent.trim();
            const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
            if (ratingMatch) {
                business.rating = parseFloat(ratingMatch[1]);
            }
        }

        // Extract review count using the specific class mentioned: leIgTe
        const reviewElement = element.querySelector('.leIgTe');
        if (reviewElement) {
            const reviewText = reviewElement.textContent.trim();
            // Extract number from text like "(54)" or "54 reviews"
            const reviewMatch = reviewText.match(/\((\d+)\)|(\d+)\s*reviews?/i);
            if (reviewMatch) {
                business.reviewCount = parseInt(reviewMatch[1] || reviewMatch[2]);
            }
        }

        // Extract categories/business type from spans with hGz87c class
        // IMPORTANT: Only select spans with EXACTLY the hGz87c class and no other classes
        // Real categories: <span class="hGz87c">Custom home builder</span> (one class only)
        // Non-categories: <span class="FjZRNe hGz87c">10+ years in business</span> (multiple classes)
        const allSpans = element.querySelectorAll('span.hGz87c');
        allSpans.forEach(catElement => {
            // Only process elements that have exactly ONE class: "hGz87c"
            if (catElement.classList.length === 1 && catElement.classList.contains('hGz87c')) {
                const categoryText = catElement.textContent.trim();
                
                // Check if it's hours information (contains time/day keywords)
                const isHoursInfo = categoryText.includes('AM') || 
                                   categoryText.includes('PM') || 
                                   categoryText.includes('Closed') || 
                                   categoryText.includes('Opens') || 
                                   categoryText.includes('Closes') ||
                                   categoryText.includes('Open 24 hours') ||
                                   categoryText.includes('Mon') || 
                                   categoryText.includes('Tue') || 
                                   categoryText.includes('Wed') || 
                                   categoryText.includes('Thu') || 
                                   categoryText.includes('Fri') || 
                                   categoryText.includes('Sat') || 
                                   categoryText.includes('Sun');
                
                // Check if it's location information (Australian states)
                const isLocationInfo = categoryText.includes(' SA') || 
                                      categoryText.includes(' NSW') || 
                                      categoryText.includes(' VIC') || 
                                      categoryText.includes(' QLD') || 
                                      categoryText.includes(' WA') || 
                                      categoryText.includes(' TAS') || 
                                      categoryText.includes(' NT') || 
                                      categoryText.includes(' ACT') ||
                                      categoryText.endsWith(' SA') || 
                                      categoryText.endsWith(' NSW') || 
                                      categoryText.endsWith(' VIC') || 
                                      categoryText.endsWith(' QLD') || 
                                      categoryText.endsWith(' WA') || 
                                      categoryText.endsWith(' TAS') || 
                                      categoryText.endsWith(' NT') || 
                                      categoryText.endsWith(' ACT');
                
                // Check if it's a phone number (contains digits, spaces, parentheses, dashes)
                const isPhoneNumber = /^[\d\s\(\)\-\+\.]+$/.test(categoryText) || // Only contains phone number characters
                                     /^\(\d{2}\)\s?\d/.test(categoryText) || // Pattern like (08) 8351
                                     /^1[38]00\s?\d/.test(categoryText) || // 1300/1800 numbers
                                     /^04\d{2}\s?\d/.test(categoryText) || // Mobile numbers 04xx
                                     /^0\d{1}\s?\d/.test(categoryText) || // Other landline formats
                                     /^\+61/.test(categoryText); // International format
                
                if (isHoursInfo) {
                    console.log(`ProListExtractor: Skipping hours info: "${categoryText}"`);
                } else if (isLocationInfo) {
                    console.log(`ProListExtractor: Skipping location info: "${categoryText}"`);
                } else if (isPhoneNumber) {
                    console.log(`ProListExtractor: Skipping phone number: "${categoryText}"`);
                } else if (categoryText && 
                           categoryText.length > 2 && 
                           categoryText.length < 80 && 
                           categoryText.toLowerCase() !== 'favourites' && // Filter out "Favourites"
                           !business.categories.includes(categoryText)) {
                    business.categories.push(categoryText);
                    console.log(`ProListExtractor: Added category (single class only): "${categoryText}"`);
                } else if (categoryText && business.categories.includes(categoryText)) {
                    console.log(`ProListExtractor: Skipped duplicate category: "${categoryText}"`);
                }
            } else {
                console.log(`ProListExtractor: Skipping element with multiple classes (${catElement.classList.length} classes): "${catElement.textContent.trim()}" - Classes: ${Array.from(catElement.classList).join(', ')}`);
            }
        });

        // Extract address from spans with hGz87c class that contain Australian state abbreviations
        // Include FjZRNe spans as they might contain location data
        const addressElements = element.querySelectorAll('span.hGz87c');
        addressElements.forEach(addressElement => {
            const addressText = addressElement.textContent.trim();
            if ((addressText.includes('NSW') || 
                 addressText.includes('VIC') || 
                 addressText.includes('QLD') || 
                 addressText.includes('SA') || 
                 addressText.includes('WA') || 
                 addressText.includes('TAS') || 
                 addressText.includes('NT') || 
                 addressText.includes('ACT')) && !business.address) {
                business.address = addressText;
            }
        });

        // Extract phone from spans with hGz87c class that contain phone numbers
        // Include FjZRNe spans as they might contain contact data
        const phoneElements = element.querySelectorAll('span.hGz87c');
        phoneElements.forEach(phoneElement => {
            const phoneText = phoneElement.textContent.trim();
            // Look for phone number patterns (Australian formats)
            const phoneMatch = phoneText.match(/^(\(\d{2}\)\s?\d{4}\s?\d{4}|\d{4}\s?\d{3}\s?\d{3}|1300\s?\d{3}\s?\d{3}|1800\s?\d{3}\s?\d{3}|\+61\s?\d\s?\d{4}\s?\d{4})$/);
            if (phoneMatch && !business.phone) {
                business.phone = phoneText;
            }
        });

        // Add category field (singular) for compatibility with generateReviewAnalysisHTML
        business.category = business.categories.length > 0 ? business.categories[0] : null;

        // Extract website URL for tracked domain detection
        business.website = '';
        const websiteLinks = element.querySelectorAll('a[href]');
        for (const link of websiteLinks) {
            const href = link.getAttribute('href') || '';
            
            // Skip ad URLs and find actual website links
            if (!href.includes('/aclk?') && !href.includes('googleadservices') && href.startsWith('http')) {
                business.website = href;
                break;
            }
        }

        // Try to extract a sample review snippet for display
        const reviewContainer = element.querySelector('.dLfU4d');
        if (reviewContainer) {
            let reviewText = '';
            
            // Try multiple selectors for review snippets
            const reviewSnippet1 = reviewContainer.querySelector('.ZZz89b .A5yTVb');
            if (reviewSnippet1) {
                reviewText = reviewSnippet1.textContent.trim();
            }
            
            if (!reviewText) {
                const reviewSnippet2 = reviewContainer.querySelector('.ZZz89b');
                if (reviewSnippet2) {
                    reviewText = reviewSnippet2.textContent.trim();
                }
            }
            
            if (!reviewText) {
                const spans = reviewContainer.querySelectorAll('span');
                for (const span of spans) {
                    const text = span.textContent.trim();
                    if (text.length > 20 && (text.includes('"') || text.includes('.'))) {
                        reviewText = text;
                        break;
                    }
                }
            }
            
            if (reviewText && reviewText.length > 10) {
                business.sampleReview = reviewText;
            }
        }

        console.log(`ProListExtractor: Extracted business ${index}:`, business);
        return business;
    }

    extractReviewsFromBusinessElements() {
        // Extract review snippets from the same business elements
        const businessElements = this.findBusinessElements();
        const reviewData = [];
        
        console.log(`🔍 ProListExtractor: Starting review extraction from ${businessElements.length} business elements`);
        
        businessElements.forEach((element, index) => {
            try {
                const businessName = element.querySelector('.I9iumb .rgnuSb.xYjf2e')?.textContent?.trim() || `Business ${index + 1}`;
                console.log(`📝 ProListExtractor: Processing business ${index + 1}: ${businessName}`);
                
                // Look for review snippets in the dLfU4d container
                const reviewContainer = element.querySelector('.dLfU4d');
                console.log(`📦 ProListExtractor: Review container found for ${businessName}:`, !!reviewContainer);
                
                if (reviewContainer) {
                    // Try multiple selectors for review snippets
                    let reviewSnippet = null;
                    let reviewText = '';
                    
                    // First try: .ZZz89b .A5yTVb (original selector)
                    reviewSnippet = reviewContainer.querySelector('.ZZz89b .A5yTVb');
                    if (reviewSnippet) {
                        reviewText = reviewSnippet.textContent.trim();
                        console.log(`💬 ProListExtractor: Found review with .ZZz89b .A5yTVb for ${businessName}:`, reviewText);
                    }
                    
                    // Second try: .ZZz89b directly
                    if (!reviewText) {
                        reviewSnippet = reviewContainer.querySelector('.ZZz89b');
                        if (reviewSnippet) {
                            reviewText = reviewSnippet.textContent.trim();
                            console.log(`💬 ProListExtractor: Found review with .ZZz89b for ${businessName}:`, reviewText);
                        }
                    }
                    
                    // Third try: any span with review-like text
                    if (!reviewText) {
                        const spans = reviewContainer.querySelectorAll('span');
                        for (const span of spans) {
                            const text = span.textContent.trim();
                            if (text.length > 20 && (text.includes('"') || text.includes('.'))) {
                                reviewText = text;
                                console.log(`💬 ProListExtractor: Found review in span for ${businessName}:`, reviewText);
                                break;
                            }
                        }
                    }
                    
                    if (reviewText && reviewText.length > 10) { // Filter out very short snippets
                        const reviewItem = {
                            businessName: businessName,
                            reviewSnippet: reviewText,
                            businessIndex: index + 1,
                            timestamp: new Date().toISOString()
                        };
                        
                        // Also extract rating and review count
                        const rating = element.querySelector('.rGaJuf')?.textContent?.trim();
                        const reviewCount = element.querySelector('.leIgTe')?.textContent?.trim();
                        
                        if (rating) reviewItem.rating = rating;
                        if (reviewCount) reviewItem.reviewCount = reviewCount;
                        
                        reviewData.push(reviewItem);
                        console.log(`✅ ProListExtractor: Added review data for ${businessName}`);
                    } else {
                        console.log(`❌ ProListExtractor: No valid review text found for ${businessName}`);
                    }
                } else {
                    console.log(`❌ ProListExtractor: No review container found for ${businessName}`);
                }
                
            } catch (error) {
                console.error(`❌ ProListExtractor: Error extracting review for business ${index + 1}:`, error);
            }
        });
        
        console.log(`🎯 ProListExtractor: Extracted ${reviewData.length} review snippets total`);
        return reviewData;
    }

    analyzeBusinessCategories() {
        try {
            const categoryCount = {};
            let totalCategories = 0;
            let totalListings = this.businesses.length;
            
            // Count categories
            this.businesses.forEach(business => {
                if (business.categories && business.categories.length > 0) {
                    business.categories.forEach(category => {
                        // Filter out "Favourites" from category analysis
                        if (category.toLowerCase() !== 'favourites') {
                            categoryCount[category] = (categoryCount[category] || 0) + 1;
                            totalCategories++;
                        }
                    });
                }
            });
            
            // Sort categories by count
            const sortedCategories = Object.entries(categoryCount)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10); // Top 10 categories
            
            const topCategories = sortedCategories.map(([category, count]) => ({
                category,
                count,
                percentage: ((count / totalListings) * 100).toFixed(1)
            }));
            
            // Calculate statistics
            const categoriesPerListing = this.businesses.map(b => b.categories ? b.categories.length : 0);
            const averageCategoriesPerListing = categoriesPerListing.length > 0 
                ? (categoriesPerListing.reduce((a, b) => a + b, 0) / categoriesPerListing.length).toFixed(1)
                : 0;
            const maxCategoriesPerListing = Math.max(...categoriesPerListing, 0);
            
            return {
                success: true,
                data: {
                    totalCategories: Object.keys(categoryCount).length,
                    totalListings,
                    averageCategoriesPerListing: parseFloat(averageCategoriesPerListing),
                    maxCategoriesPerListing,
                    topCategories
                }
            };
            
        } catch (error) {
            console.error('ProListExtractor: Category analysis error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async startProListReviewAnalysis(maxReviews = null) {
        try {
            console.log('🚀 ProListExtractor: Starting ProList review analysis...');
            
            // Check if ProListReviewScraper is available
            if (typeof window.proListReviewScraper === 'undefined') {
                console.error('❌ ProListExtractor: ProListReviewScraper not available');
                return {
                    success: false,
                    error: 'ProList Review Scraper not loaded. Please refresh the page.'
                };
            }
            
            // Use the working ProListReviewScraper class
            const result = await window.proListReviewScraper.executeExtraction(maxReviews);
            
            if (result.success) {
                console.log(`✅ ProListExtractor: Review extraction completed! Found ${result.reviewCount} reviews`);
                
                // Store the result for export
                this.lastExtractedReviews = result;
                window.lastExtractedReviews = result;
                
                // Update UI stats if elements exist
                try {
                    const exportBtn = document.getElementById('gmbExportBtn');
                    if (exportBtn) {
                        exportBtn.disabled = false;
                        console.log('📤 ProListExtractor: Enabled export button');
                    }
                    
                    const statsReviews = document.getElementById('gmbStatsReviews');
                    if (statsReviews && result.reviewCount) {
                        statsReviews.textContent = result.reviewCount;
                        console.log('📤 ProListExtractor: Updated reviews count:', result.reviewCount);
                    }
                    
                } catch (uiError) {
                    console.log('📤 ProListExtractor: UI update error (popup may not be open):', uiError.message);
                }
                
                return {
                    success: true,
                    reviewCount: result.reviewCount,
                    reviews: result.reviews
                };
            } else {
                console.error('❌ ProListExtractor: Review extraction failed:', result.error);
                return {
                    success: false,
                    error: result.error || 'Unknown error during review extraction'
                };
            }
            
        } catch (error) {
            console.error('❌ ProListExtractor: Error in startProListReviewAnalysis:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Send review update to popup via storage or messaging
    sendReviewUpdate(status, data) {
        console.log(`📡 ProListExtractor: Sending review update - Status: ${status}`, data);
        
        // Try to send message to popup if available
        try {
            chrome.runtime.sendMessage({
                type: 'reviewAnalysisUpdate',
                status: status,
                data: data
            }).catch(err => {
                console.log('📡 ProListExtractor: Message send failed (popup may be closed):', err.message);
            });
        } catch (error) {
            console.log('📡 ProListExtractor: Message send error:', error.message);
        }
        
        // Also store in local storage as fallback
        try {
            localStorage.setItem('proListReviewUpdate', JSON.stringify({
                status: status,
                data: data,
                timestamp: Date.now()
            }));
        } catch (error) {
            console.log('📡 ProListExtractor: Storage error:', error.message);
        }
    }

    // Calculate aggregate data for review analysis
    calculateAggregateData(businesses) {
        console.log('📊 ProListExtractor: Calculating aggregate data for', businesses.length, 'businesses');
        
        if (!businesses || businesses.length === 0) {
            return {
                totalBusinesses: 0,
                totalReviews: 0,
                averageRating: 0,
                averageReviewCountTop3: 0,
                highestRated: { name: 'N/A', rating: 0, reviewCount: 0 },
                mostReviews: { name: 'N/A', count: 0 },
                ratingDistribution: { '5': 0, '4': 0, '3': 0, '2': 0, '1': 0, '0': 0 },
                top3Listings: []
            };
        }

        // Calculate position adjustments for sponsored listings
        console.log('📊 ProListExtractor: Calculating position adjustments for sponsored listings...');
        let sponsoredCount = 0;
        
        // Count sponsored results in first 4 positions
        for (let i = 0; i < Math.min(4, businesses.length); i++) {
            if (businesses[i].sponsored) {
                sponsoredCount++;
                console.log(`📊 ProListExtractor: Found sponsored business at position ${i + 1}: ${businesses[i].businessName}`);
            }
        }
        
        console.log(`📊 ProListExtractor: Found ${sponsoredCount} sponsored businesses in top 4 positions`);
        
        // Add adjusted positions to businesses
        businesses.forEach((business, index) => {
            business.actualPosition = index + 1; // DOM position
            business.adjustedPosition = Math.max(1, (index + 1) - sponsoredCount); // Adjusted for sponsored
            
            if (business.sponsored) {
                console.log(`📊 ProListExtractor: Business "${business.businessName}" - SPONSORED at position ${business.actualPosition}`);
            } else {
                console.log(`📊 ProListExtractor: Business "${business.businessName}" - Position ${business.actualPosition} adjusted to ${business.adjustedPosition} (${sponsoredCount} sponsored in top 4)`);
            }
        });
        
        let totalReviews = 0;
        let totalRating = 0;
        let businessesWithRating = 0;
        let highestRated = { name: 'N/A', rating: 0, reviewCount: 0 };
        let mostReviews = { name: 'N/A', count: 0 };
        
        // Rating distribution calculation
        const ratingDistribution = { '5': 0, '4': 0, '3': 0, '2': 0, '1': 0, '0': 0 };
        
        // Process each ORGANIC business only
        organicBusinesses.forEach(business => {
            if (business.reviewCount && !isNaN(business.reviewCount)) {
                totalReviews += parseInt(business.reviewCount);
                
                if (business.reviewCount > mostReviews.count) {
                    mostReviews = {
                        name: business.businessName || 'Unknown',
                        count: business.reviewCount
                    };
                }
            }
            
            if (business.rating && !isNaN(business.rating)) {
                totalRating += parseFloat(business.rating);
                businessesWithRating++;
                
                if (business.rating > highestRated.rating) {
                    highestRated = {
                        name: business.businessName || 'Unknown',
                        rating: business.rating,
                        reviewCount: business.reviewCount || 0
                    };
                }
                
                // Rating distribution categorization using ORGANIC data only
                const rating = parseFloat(business.rating);
                let ratingRange;
                
                if (rating >= 5.0) {
                    ratingRange = '5'; // Exactly 5.0 stars
                } else if (rating >= 4.0) {
                    ratingRange = '4'; // 4.0-4.9 stars
                } else if (rating >= 3.0) {
                    ratingRange = '3'; // 3.0-3.9 stars
                } else if (rating >= 2.0) {
                    ratingRange = '2'; // 2.0-2.9 stars
                } else if (rating >= 1.0) {
                    ratingRange = '1'; // 1.0-1.9 stars
                } else {
                    ratingRange = '0'; // No rating or below 1.0
                }
                
                ratingDistribution[ratingRange] = (ratingDistribution[ratingRange] || 0) + 1;
                console.log(`📊 ProListExtractor: ORGANIC Business "${business.businessName}" rating ${rating} categorized as "${ratingRange}"`);
            } else {
                // Business with no rating
                ratingDistribution['0'] = (ratingDistribution['0'] || 0) + 1;
                console.log(`📊 ProListExtractor: ORGANIC Business "${business.businessName}" has no rating, categorized as "0"`);
            }
        });
        
        // Filter out sponsored businesses for accurate organic analysis
        const organicBusinesses = businesses.filter(business => !business.sponsored);
        console.log(`📊 ProListExtractor: Organic businesses: ${organicBusinesses.length} (${businesses.length - organicBusinesses.length} sponsored excluded)`);
        
        // Get top 3 ORGANIC listings by position
        const top3OrganicListings = organicBusinesses.slice(0, 3).map((business, index) => ({
            name: business.businessName || `Business ${index + 1}`,
            reviewCount: business.reviewCount || 0,
            rating: business.rating || 0
        }));
        
        // Calculate top 3 organic average review count
        const top3OrganicReviewCounts = top3OrganicListings.map(b => b.reviewCount || 0);
        const averageReviewCountTop3 = top3OrganicReviewCounts.length > 0 ? 
            (top3OrganicReviewCounts.reduce((sum, count) => sum + count, 0) / top3OrganicReviewCounts.length).toFixed(1) : 0;
        
        const averageRating = businessesWithRating > 0 ? 
            (totalRating / businessesWithRating).toFixed(1) : 0;
        
        console.log('📊 ProListExtractor: Rating distribution calculated:', ratingDistribution);
        console.log('📊 ProListExtractor: Businesses with ratings:', businessesWithRating, 'Total businesses:', businesses.length);
        
        const aggregateData = {
            totalBusinesses: organicBusinesses.length, // Organic businesses only
            totalReviews: totalReviews,
            averageRating: parseFloat(averageRating),
            averageReviewCountTop3: parseFloat(averageReviewCountTop3),
            highestRated: highestRated,
            mostReviews: mostReviews,
            ratingDistribution: ratingDistribution,
            top3Listings: top3OrganicListings, // Use organic top 3
            sponsoredExcluded: businesses.length - organicBusinesses.length // Track excluded sponsored
        };
        
        console.log('📊 ProListExtractor: Aggregate data calculated:', aggregateData);
        return aggregateData;
    }

    // Stop review analysis
    stopReviewAnalysis() {
        console.log('⏹️ ProListExtractor: Stopping review analysis');
        this.isAnalyzing = false;
    }

    // Add the Pro List service extraction functionality
    async extractServicesFromProList(businessRange = null) {
        try {
            console.log('GMB Service Scraper: Starting service extraction from Pro List...');
            
            // Find all business cards in the Pro List
            const businessCards = document.querySelectorAll('.DVBRsc[jsaction*="click:xxAyzd"]');
            console.log(`GMB Service Scraper: Found ${businessCards.length} business cards`);
            
            if (businessCards.length === 0) {
                return {
                    success: false,
                    error: 'No business cards found. Make sure you are on a Pro List page with businesses loaded.'
                };
            }

            // Apply business range if specified
            let businessesToProcess = Array.from(businessCards);
            let totalAvailable = businessCards.length;
            
            if (businessRange) {
                const startIndex = Math.max(0, (businessRange.start || 1) - 1); // Convert to 0-based index
                const endIndex = Math.min(businessCards.length, businessRange.end || businessCards.length);
                
                if (startIndex >= businessCards.length) {
                    return {
                        success: false,
                        error: `Start index ${businessRange.start} is beyond available businesses (${businessCards.length})`
                    };
                }
                
                businessesToProcess = Array.from(businessCards).slice(startIndex, endIndex);
                console.log(`GMB Service Scraper: Processing businesses ${businessRange.start}-${Math.min(businessRange.end, totalAvailable)} (${businessesToProcess.length} businesses)`);
            }

            const allServices = [];
            const businessCount = businessesToProcess.length;
            let processedCount = 0;

            // Send initial progress update
            this.sendServicesUpdate('started', {
                total: businessCount,
                current: 0,
                data: []
            });

            // Process each business card in the specified range
            for (let i = 0; i < businessesToProcess.length; i++) {
                const businessCard = businessesToProcess[i];
                processedCount = i + 1;
                
                // Calculate the actual business number for display
                const actualBusinessNumber = businessRange ? 
                    (businessRange.start - 1 + processedCount) : 
                    processedCount;
                
                console.log(`GMB Service Scraper: Processing business ${actualBusinessNumber} (${processedCount}/${businessCount})`);

                try {
                    // Extract business name first
                    const businessName = this.extractBusinessNameFromCard(businessCard);
                    console.log(`GMB Service Scraper: Business name: ${businessName}`);

                    // Click the business card to open details
                    businessCard.click();
                    
                    // Wait for the business details to load
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    // Try to find and click the Services tab
                    const servicesTab = document.querySelector('button[role="tab"]#jobtypes, button[role="tab"][aria-controls="jobtypes-panel"]');
                    
                    if (servicesTab) {
                        console.log(`GMB Service Scraper: Found Services tab for ${businessName}`);
                        servicesTab.click();
                        
                        // Wait for services to load
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        
                        // Extract services
                        const businessServices = await this.extractServicesFromCurrentBusiness(businessName);
                        if (businessServices.length > 0) {
                            // Additional deduplication at business level to ensure no cross-category duplicates
                            const uniqueBusinessServices = [];
                            const businessSeenServices = new Set();
                            
                            businessServices.forEach(serviceItem => {
                                const serviceKey = this.createNormalizedKey(serviceItem.service);
                                if (!businessSeenServices.has(serviceKey)) {
                                    businessSeenServices.add(serviceKey);
                                    uniqueBusinessServices.push(serviceItem);
                                } else {
                                    console.log(`GMB Service Scraper: Removed cross-category duplicate: ${serviceItem.service}`);
                                }
                            });
                            
                            allServices.push({
                                businessName: businessName,
                                businessNumber: actualBusinessNumber,
                                services: uniqueBusinessServices,
                                extractedAt: new Date().toISOString()
                            });
                            
                            console.log(`GMB Service Scraper: Added ${uniqueBusinessServices.length} unique services for ${businessName}`);
                        } else {
                            // Still add the business even if no services found
                            allServices.push({
                                businessName: businessName,
                                businessNumber: actualBusinessNumber,
                                services: [],
                                error: 'No services found',
                                extractedAt: new Date().toISOString()
                            });
                            console.log(`GMB Service Scraper: No services found for ${businessName}`);
                        }
                    } else {
                        // Still add the business even if no Services tab found
                        allServices.push({
                            businessName: businessName,
                            businessNumber: actualBusinessNumber,
                            services: [],
                            error: 'No Services tab found',
                            extractedAt: new Date().toISOString()
                        });
                        console.log(`GMB Service Scraper: No Services tab found for ${businessName}`);
                    }

                    // Send progress update
                    this.sendServicesUpdate('progress', {
                        total: businessCount,
                        current: processedCount,
                        data: allServices
                    });

                } catch (businessError) {
                    console.error(`GMB Service Scraper: Error processing business ${actualBusinessNumber}:`, businessError);
                    
                    // Add error entry
                    allServices.push({
                        businessName: `Business ${actualBusinessNumber}`,
                        businessNumber: actualBusinessNumber,
                        services: [],
                        error: businessError.message,
                        extractedAt: new Date().toISOString()
                    });
                }
                
                // Small delay between businesses
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Generate analysis
            const analysis = this.generateServiceAnalysis(allServices);

            const finalResult = {
                success: true,
                services: allServices,
                analysis: analysis,
                businessCount: businessCount,
                totalAvailable: totalAvailable,
                range: businessRange ? `${businessRange.start}-${Math.min(businessRange.end, totalAvailable)}` : 'All'
            };

            // Send completion update
            this.sendServicesUpdate('completed', {
                total: businessCount,
                current: businessCount,
                data: finalResult
            });

            // Store the result for export - convert to flat format for CSV
            const flatServices = [];
            finalResult.services.forEach(business => {
                business.services.forEach(service => {
                    flatServices.push({
                        businessIndex: business.businessNumber,
                        businessName: business.businessName,
                        service: service,
                        extractedAt: business.extractedAt
                    });
                });
            });
            
            // Store in the format expected by export functions
            this.lastExtractedServices = {
                ...finalResult,
                services: flatServices
            };
            
            // Also store in global window for content script access
            window.lastExtractedServices = this.lastExtractedServices;

            return finalResult;

        } catch (error) {
            console.error('GMB Service Scraper: Error during service extraction:', error);
            
            // Send error update
            this.sendServicesUpdate('error', {
                error: error.message,
                data: { success: false, error: error.message }
            });
            
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Add progress update sender for services extraction
    sendServicesUpdate(status, data) {
        console.log('📤 ProListExtractor: Services extraction update:', status, 'with data:', data);
        
        // Use global progress functions if available
        if (status === 'started' && window.showGlobalProgress && window.resetGlobalProgress) {
            window.resetGlobalProgress();
            window.showGlobalProgress();
            if (window.updateGlobalProgress && data.total) {
                window.updateGlobalProgress(0, data.total, 'Starting services extraction...');
            }
        } else if (status === 'progress' && window.updateGlobalProgress && data.current && data.total) {
            window.updateGlobalProgress(data.current, data.total, `Processing business ${data.current} of ${data.total}...`);
        } else if (status === 'completed' && window.setGlobalProgressComplete) {
            // Calculate actual services found for accurate message
            let totalServicesFound = 0;
            if (data.data && data.data.services && Array.isArray(data.data.services)) {
                totalServicesFound = data.data.services.length;
            }
            
            const message = totalServicesFound > 0 
                ? `Services extraction completed! Found ${totalServicesFound} services from ${data.data?.businessCount || 0} businesses`
                : `Services extraction completed! No services found in ${data.data?.businessCount || 0} businesses`;
            
            window.setGlobalProgressComplete(message);
        } else if (status === 'error' && window.setGlobalProgressError) {
            window.setGlobalProgressError('Services extraction failed');
        }
        
        // Store data globally like the Search extractor does
        if (status === 'completed' && data && data.data) {
            // Store in multiple formats for compatibility
            window.lastExtractedServices = data.data;
            window.proListServicesData = data.data;
            console.log('📤 ProListExtractor: Stored services data globally:', window.lastExtractedServices);
            
            // Manually update the popup UI elements if they exist
            try {
                const exportBtn = document.getElementById('gmbExportBtn');
                if (exportBtn) {
                    exportBtn.disabled = false;
                    console.log('📤 ProListExtractor: Enabled export button');
                }
                
                // Update stats if elements exist
                const statsBusinesses = document.getElementById('gmbStatsBusinesses');
                const statsServices = document.getElementById('gmbStatsServices');
                
                if (statsBusinesses && data.data.businessCount) {
                    statsBusinesses.textContent = data.data.businessCount;
                    console.log('📤 ProListExtractor: Updated businesses count:', data.data.businessCount);
                }
                
                if (statsServices && data.data.services) {
                    const serviceCount = Array.isArray(data.data.services) ? data.data.services.length : 
                                       (data.data.services.length || Object.keys(data.data.services).length || 0);
                    statsServices.textContent = serviceCount;
                    console.log('📤 ProListExtractor: Updated services count:', serviceCount);
                }
                
            } catch (uiError) {
                console.log('📤 ProListExtractor: UI update error (popup may not be open):', uiError.message);
            }
        }
    }

    async extractServicesFromCurrentBusiness(businessName) {
        try {
            console.log(`GMB Service Scraper: Extracting services for ${businessName}`);
            
            const services = [];
            const seenServices = new Set(); // Track duplicates using normalized keys
            
            // Look for service sections with headers and lists
            const serviceSections = document.querySelectorAll('.bg3Wkc');
            console.log(`GMB Service Scraper: Found ${serviceSections.length} service sections`);
            
            for (const section of serviceSections) {
                const rawSectionTitle = section.textContent.trim();
                const normalizedSectionTitle = this.normalizeAndCapitalize(rawSectionTitle);
                console.log(`GMB Service Scraper: Processing section: ${normalizedSectionTitle}`);
                
                // Find the corresponding service list
                // Look for the next sibling or nearby element with class VralU
                let serviceList = section.nextElementSibling;
                while (serviceList && !serviceList.classList.contains('VralU')) {
                    serviceList = serviceList.nextElementSibling;
                }
                
                // Also try looking for VralU in the same parent
                if (!serviceList) {
                    const parent = section.parentElement;
                    if (parent) {
                        serviceList = parent.querySelector('.VralU');
                    }
                }
                
                if (serviceList) {
                    console.log(`GMB Service Scraper: Found service list for section ${normalizedSectionTitle}`);
                    
                    // Extract individual services
                    const serviceItems = serviceList.querySelectorAll('.KNUbhf');
                    console.log(`GMB Service Scraper: Found ${serviceItems.length} service items in ${normalizedSectionTitle}`);
                    
                    for (const item of serviceItems) {
                        const rawServiceText = item.textContent.trim();
                        if (rawServiceText && rawServiceText.length > 0) {
                            const normalizedServiceText = this.normalizeAndCapitalize(rawServiceText);
                            const serviceKey = this.createNormalizedKey(normalizedServiceText);
                            
                            // Check for duplicates
                            if (!seenServices.has(serviceKey)) {
                                seenServices.add(serviceKey);
                                services.push({
                                    category: normalizedSectionTitle,
                                    service: normalizedServiceText
                                });
                                console.log(`GMB Service Scraper: Added service: ${normalizedServiceText} (${normalizedSectionTitle})`);
                            } else {
                                console.log(`GMB Service Scraper: Skipped duplicate service: ${normalizedServiceText}`);
                            }
                        }
                    }
                } else {
                    console.log(`GMB Service Scraper: No service list found for section ${normalizedSectionTitle}`);
                }
            }
            
            // If no sections found, try a more general approach
            if (services.length === 0) {
                console.log(`GMB Service Scraper: No services found with section approach, trying general approach`);
                
                // Look for any service lists without specific sections
                const allServiceLists = document.querySelectorAll('.VralU');
                console.log(`GMB Service Scraper: Found ${allServiceLists.length} general service lists`);
                
                for (const list of allServiceLists) {
                    const serviceItems = list.querySelectorAll('.KNUbhf');
                    for (const item of serviceItems) {
                        const rawServiceText = item.textContent.trim();
                        if (rawServiceText && rawServiceText.length > 0) {
                            const normalizedServiceText = this.normalizeAndCapitalize(rawServiceText);
                            const serviceKey = this.createNormalizedKey(normalizedServiceText);
                            
                            // Check for duplicates
                            if (!seenServices.has(serviceKey)) {
                                seenServices.add(serviceKey);
                                services.push({
                                    category: 'General Services',
                                    service: normalizedServiceText
                                });
                                console.log(`GMB Service Scraper: Added general service: ${normalizedServiceText}`);
                            } else {
                                console.log(`GMB Service Scraper: Skipped duplicate general service: ${normalizedServiceText}`);
                            }
                        }
                    }
                }
            }
            
            console.log(`GMB Service Scraper: Total unique services extracted for ${businessName}: ${services.length}`);
            return services;
            
        } catch (error) {
            console.error(`GMB Service Scraper: Error extracting services for ${businessName}:`, error);
            return [];
        }
    }

    extractBusinessNameFromCard(businessCard) {
        try {
            // Try different selectors to find the business name
            const nameSelectors = [
                '.rGhpQd',
                '.uMdZh.tIxNaf',
                '.qLhwHc',
                '.B0eCL',
                'h3',
                '[role="heading"]'
            ];
            
            for (const selector of nameSelectors) {
                const nameElement = businessCard.querySelector(selector);
                if (nameElement && nameElement.textContent.trim()) {
                    return nameElement.textContent.trim();
                }
            }
            
            // Fallback to any text content
            return businessCard.textContent.trim().split('\n')[0] || 'Unknown Business';
        } catch (error) {
            console.error('GMB Service Scraper: Error extracting business name:', error);
            return 'Unknown Business';
        }
    }

    normalizeAndCapitalize(text) {
        if (!text) return '';
        
        return text
            .trim()
            .toLowerCase()
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    createNormalizedKey(text) {
        if (!text) return '';
        
        return text
            .toLowerCase()
            .replace(/[^\w\s]/g, '') // Remove punctuation
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim();
    }

    generateServiceAnalysis(allServices) {
        try {
            const analysis = {
                totalBusinesses: allServices.length,
                totalServices: 0,
                uniqueServices: new Set(),
                servicesByCategory: {},
                serviceFrequency: {},
                topServices: [],
                topCategories: []
            };

            // Process each business's services
            allServices.forEach(business => {
                business.services.forEach(serviceItem => {
                    analysis.totalServices++;
                    analysis.uniqueServices.add(serviceItem.service);
                    
                    // Count by category
                    if (!analysis.servicesByCategory[serviceItem.category]) {
                        analysis.servicesByCategory[serviceItem.category] = 0;
                    }
                    analysis.servicesByCategory[serviceItem.category]++;
                    
                    // Count service frequency
                    if (!analysis.serviceFrequency[serviceItem.service]) {
                        analysis.serviceFrequency[serviceItem.service] = 0;
                    }
                    analysis.serviceFrequency[serviceItem.service]++;
                });
            });

            // Generate top services
            analysis.topServices = Object.entries(analysis.serviceFrequency)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 20)
                .map(([service, count]) => ({
                    service,
                    count,
                    percentage: ((count / analysis.totalBusinesses) * 100).toFixed(1)
                }));

            // Generate top categories
            analysis.topCategories = Object.entries(analysis.servicesByCategory)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10)
                .map(([category, count]) => ({
                    category,
                    count,
                    percentage: ((count / analysis.totalServices) * 100).toFixed(1)
                }));

            analysis.uniqueServiceCount = analysis.uniqueServices.size;
            
            return analysis;
        } catch (error) {
            console.error('GMB Service Scraper: Error generating analysis:', error);
            return {};
        }
    }

    // Add the Pro List attributes extraction functionality
    async extractAttributesFromProList(businessRange = null) {
        try {
            console.log('ProListExtractor: Starting attributes extraction from Pro List...');
            
            // Find all business cards in the Pro List using the same selector as services extraction
            const businessCards = document.querySelectorAll('.DVBRsc[jsaction*="click:xxAyzd"]');
            console.log(`ProListExtractor: Found ${businessCards.length} business cards`);
            
            if (businessCards.length === 0) {
                return {
                    success: false,
                    error: 'No business listings found. Make sure you are on a Pro List page with businesses loaded.'
                };
            }

            // Apply business range if specified
            let businessesToProcess = Array.from(businessCards);
            let totalAvailable = businessCards.length;
            
            if (businessRange) {
                const startIndex = Math.max(0, (businessRange.start || 1) - 1); // Convert to 0-based index
                const endIndex = Math.min(businessCards.length, businessRange.end || businessCards.length);
                
                if (startIndex >= businessCards.length) {
                    return {
                        success: false,
                        error: `Start index ${businessRange.start} is beyond available businesses (${businessCards.length})`
                    };
                }
                
                businessesToProcess = Array.from(businessCards).slice(startIndex, endIndex);
                console.log(`ProListExtractor: Processing businesses ${businessRange.start}-${Math.min(businessRange.end, totalAvailable)} (${businessesToProcess.length} businesses)`);
            }

            const allAttributes = [];
            const businessCount = businessesToProcess.length;
            let processedCount = 0;

            // Send initial progress update
            this.sendAttributesUpdate('started', {
                total: businessCount,
                current: 0,
                data: []
            });

            // Process each business card in the specified range
            for (let i = 0; i < businessesToProcess.length; i++) {
                const businessCard = businessesToProcess[i];
                processedCount = i + 1;
                
                // Calculate the actual business number for display
                const actualBusinessNumber = businessRange ? 
                    (businessRange.start - 1 + processedCount) : 
                    processedCount;
                
                console.log(`ProListExtractor: Processing business ${actualBusinessNumber} (${processedCount}/${businessCount}) for attributes`);

                try {
                    // Extract business name first from the card
                    const businessName = this.extractBusinessNameFromCard(businessCard);
                    console.log(`ProListExtractor: Extracting attributes for: ${businessName}`);

                    // Click the business card to open details
                    businessCard.click();
                    
                    // Wait for the business details to load
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // Extract attributes from the opened business page
                    const businessAttributes = await this.extractAttributesFromOpenBusiness(businessName);
                    
                    if (businessAttributes && Object.keys(businessAttributes).length > 0) {
                        allAttributes.push({
                            businessName: businessName,
                            businessNumber: actualBusinessNumber,
                            businessIndex: processedCount,
                            attributes: businessAttributes,
                            extractedAt: new Date().toISOString()
                        });
                        
                        console.log(`ProListExtractor: Added attributes for ${businessName}: ${Object.keys(businessAttributes).length} sections`);
                    } else {
                        // Still add the business even if no attributes found
                        allAttributes.push({
                            businessName: businessName,
                            businessNumber: actualBusinessNumber,
                            businessIndex: processedCount,
                            attributes: {},
                            error: 'No attributes found',
                            extractedAt: new Date().toISOString()
                        });
                        console.log(`ProListExtractor: No attributes found for ${businessName}`);
                    }

                    // Send progress update
                    this.sendAttributesUpdate('progress', {
                        total: businessCount,
                        current: processedCount,
                        data: allAttributes
                    });

                } catch (businessError) {
                    console.error(`ProListExtractor: Error processing business ${actualBusinessNumber}:`, businessError);
                    
                    // Add error entry
                    allAttributes.push({
                        businessName: `Business ${actualBusinessNumber}`,
                        businessNumber: actualBusinessNumber,
                        businessIndex: processedCount,
                        attributes: {},
                        error: businessError.message,
                        extractedAt: new Date().toISOString()
                    });
                }
                
                // Small delay between businesses
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Generate analysis
            const analysis = this.generateAttributesAnalysis(allAttributes);

            const finalResult = {
                success: true,
                attributes: allAttributes,
                analysis: analysis,
                businessCount: businessCount,
                totalAvailable: totalAvailable,
                range: businessRange ? `${businessRange.start}-${Math.min(businessRange.end, totalAvailable)}` : 'All'
            };

            // Send completion update
            this.sendAttributesUpdate('completed', {
                total: businessCount,
                current: businessCount,
                data: finalResult
            });

            // Store the result for export
            this.lastExtractedAttributes = finalResult;
            
            // Also store in global window for content script access
            window.lastExtractedAttributes = this.lastExtractedAttributes;

            return finalResult;

        } catch (error) {
            console.error('ProListExtractor: Error during attributes extraction:', error);
            
            // Send error update
            this.sendAttributesUpdate('error', {
                error: error.message,
                data: { success: false, error: error.message }
            });
            
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Add progress update sender for attributes extraction
    sendAttributesUpdate(status, data) {
        console.log('📤 ProListExtractor: Attributes extraction update:', status, 'with data:', data);
        
        // Use global progress functions if available
        if (status === 'started' && window.showGlobalProgress && window.resetGlobalProgress) {
            window.resetGlobalProgress();
            window.showGlobalProgress();
            if (window.updateGlobalProgress && data.total) {
                window.updateGlobalProgress(0, data.total, 'Starting attributes extraction...');
            }
        } else if (status === 'progress' && window.updateGlobalProgress && data.current && data.total) {
            window.updateGlobalProgress(data.current, data.total, `Processing business ${data.current} of ${data.total}...`);
        } else if (status === 'completed' && window.setGlobalProgressComplete) {
            // Calculate actual attributes found for accurate message
            let totalAttributesFound = 0;
            if (data.data && data.data.attributes && Array.isArray(data.data.attributes)) {
                data.data.attributes.forEach(business => {
                    if (business.attributes && typeof business.attributes === 'object') {
                        totalAttributesFound += Object.values(business.attributes).reduce((sum, attrs) => {
                            return sum + (Array.isArray(attrs) ? attrs.length : 0);
                        }, 0);
                    }
                });
            }
            
            const message = totalAttributesFound > 0 
                ? `Attributes extraction completed! Found ${totalAttributesFound} attributes from ${data.data?.businessCount || 0} businesses`
                : `Attributes extraction completed! No attributes found in ${data.data?.businessCount || 0} businesses`;
            
            window.setGlobalProgressComplete(message);
        } else if (status === 'error' && window.setGlobalProgressError) {
            window.setGlobalProgressError('Attributes extraction failed');
        }
        
        // Store data globally like the Search extractor does
        if (status === 'completed' && data && data.data) {
            // Store in multiple formats for compatibility
            window.lastExtractedAttributes = data.data;
            window.proListAttributesData = data.data;
            console.log('📤 ProListExtractor: Stored attributes data globally:', window.lastExtractedAttributes);
            
            // Manually update the popup UI elements if they exist
            try {
                const exportBtn = document.getElementById('gmbExportBtn');
                if (exportBtn) {
                    exportBtn.disabled = false;
                    console.log('📤 ProListExtractor: Enabled export button');
                }
                
                // Update stats if elements exist
                const statsBusinesses = document.getElementById('gmbStatsBusinesses');
                const statsAttributes = document.getElementById('gmbStatsAttributes');
                
                if (statsBusinesses && data.data.businessCount) {
                    statsBusinesses.textContent = data.data.businessCount;
                    console.log('📤 ProListExtractor: Updated businesses count:', data.data.businessCount);
                }
                
                if (statsAttributes && data.data.attributes) {
                    let attributeCount = 0;
                    if (Array.isArray(data.data.attributes)) {
                        data.data.attributes.forEach((business, index) => {
                            if (business.attributes && typeof business.attributes === 'object') {
                                const businessAttrCount = Object.values(business.attributes).reduce((sum, attrs) => {
                                    return sum + (Array.isArray(attrs) ? attrs.length : 0);
                                }, 0);
                                attributeCount += businessAttrCount;
                                console.log(`📤 ProListExtractor: Business ${index + 1} (${business.businessName}) has ${businessAttrCount} attributes`);
                            } else {
                                console.log(`📤 ProListExtractor: Business ${index + 1} (${business.businessName}) has no attributes object`);
                            }
                        });
                    }
                    statsAttributes.textContent = attributeCount;
                    console.log('📤 ProListExtractor: Updated attributes count:', attributeCount);
                } else {
                    console.log('📤 ProListExtractor: statsAttributes element or data.data.attributes not found');
                }
                
            } catch (uiError) {
                console.log('📤 ProListExtractor: UI update error (popup may not be open):', uiError.message);
            }
        }
    }

    async extractAttributesFromOpenBusiness(businessName) {
        try {
            console.log(`ProListExtractor: Extracting attributes from opened business: ${businessName}`);
            
            const attributes = {};
            const seenAttributes = new Set(); // Track duplicates using normalized keys
            
            // First, try to click "View more" button with multiple possible selectors (from working version)
            let viewMoreClicked = false;
            const viewMoreSelectors = [
                'button[jsname="HVQwsb"]',
                'button:contains("View")',
                'button:contains("more")',
                '[jsname="HVQwsb"]',
                '.VfPpkd-LgbsSe[jsname="HVQwsb"]'
            ];
            
            for (const selector of viewMoreSelectors) {
                try {
                    let viewMoreButton;
                    if (selector.includes(':contains')) {
                        // Handle contains selector manually
                        const buttons = document.querySelectorAll('button');
                        for (const button of buttons) {
                            const text = button.textContent.toLowerCase();
                            if (text.includes('view') && text.includes('more')) {
                                viewMoreButton = button;
                                break;
                            }
                        }
                    } else {
                        viewMoreButton = document.querySelector(selector);
                    }
                    
                    if (viewMoreButton) {
                        console.log(`ProListExtractor: Found "View more" button with selector: ${selector}`);
                        console.log(`ProListExtractor: Button text: "${viewMoreButton.textContent.trim()}"`);
                        
                        // Scroll the button into view first
                        viewMoreButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        await new Promise(resolve => setTimeout(resolve, 500));
                        
                        // Click the button
                        viewMoreButton.click();
                        viewMoreClicked = true;
                        console.log(`ProListExtractor: Successfully clicked "View more" button`);
                        
                        // Wait longer for content to load after clicking
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        break;
                    }
                } catch (error) {
                    console.log(`ProListExtractor: Error with selector ${selector}:`, error);
                }
            }
            
            if (!viewMoreClicked) {
                console.log(`ProListExtractor: No "View more" button found, proceeding with visible attributes`);
            }
            
            // DEBUG: Check what selectors are available on the page
            console.log(`ProListExtractor: DEBUG - Testing available selectors on page:`);
            const testSelectors = [
                '.A0QKLe', '.Hm3kOd', '.F5xrb', '.ceNzKf', '.HHrUdb.fontTitleSmall',
                '.Io6YTe', '.Z1hOCe', '.eigqqc.wcaQd', '.t39EBf.GUrTXd', '.y0skZc',
                '.OqCZI .fontBodyMedium', '.YhemCb', '.AeaXub', '.hpLkke .fontBodyMedium',
                '[data-attrid]', '[aria-label*="hours"]', '[aria-label*="phone"]', 
                '.gm2-body-2', '.section-info', '.section-editorial-quote'
            ];
            
            testSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    console.log(`ProListExtractor: DEBUG - Found ${elements.length} elements with selector: ${selector}`);
                    // Log first element's text content for inspection
                    if (elements[0] && elements[0].textContent) {
                        const sampleText = elements[0].textContent.trim().substring(0, 100);
                        console.log(`ProListExtractor: DEBUG - Sample text: "${sampleText}"`);
                    }
                } else {
                    console.log(`ProListExtractor: DEBUG - No elements found with selector: ${selector}`);
                }
            });
            
            // DEBUG: Look for any element containing text that looks like attributes
            console.log(`ProListExtractor: DEBUG - Searching for potential attribute text patterns:`);
            const allElements = document.querySelectorAll('*');
            let potentialAttributeElements = [];
            
            allElements.forEach(element => {
                const text = element.textContent?.trim();
                if (text && text.length > 5 && text.length < 100) {
                    // Look for common attribute patterns
                    const attributePatterns = [
                        /wheelchair/i, /accessible/i, /parking/i, /wifi/i, /delivery/i,
                        /takeout/i, /dine.?in/i, /outdoor/i, /credit.*card/i, /cash/i,
                        /reservation/i, /appointment/i, /consultation/i, /estimate/i,
                        /licensed/i, /insured/i, /bonded/i, /guarantee/i, /warranty/i
                    ];
                    
                    const isAttributeLike = attributePatterns.some(pattern => pattern.test(text));
                    if (isAttributeLike) {
                        potentialAttributeElements.push({
                            element: element,
                            text: text,
                            className: element.className,
                            tagName: element.tagName
                        });
                    }
                }
            });
            
            if (potentialAttributeElements.length > 0) {
                console.log(`ProListExtractor: DEBUG - Found ${potentialAttributeElements.length} potential attribute elements:`);
                potentialAttributeElements.slice(0, 10).forEach((item, index) => {
                    console.log(`ProListExtractor: DEBUG - ${index + 1}. ${item.tagName}.${item.className}: "${item.text}"`);
                });
            } else {
                console.log(`ProListExtractor: DEBUG - No potential attribute elements found`);
            }
            
            // Look for attribute sections with headers (class A0QKLe) and items (class Hm3kOd) - FROM WORKING VERSION
            const attributeHeadings = document.querySelectorAll('.A0QKLe');
            console.log(`ProListExtractor: Found ${attributeHeadings.length} attribute sections`);
            
            for (const heading of attributeHeadings) {
                const rawSectionTitle = heading.textContent.trim();
                const normalizedSectionTitle = this.normalizeAndCapitalize(rawSectionTitle);
                console.log(`ProListExtractor: Processing section: ${normalizedSectionTitle}`);
                
                // Find the corresponding attribute items
                // Look for the next sibling or nearby element with class Hm3kOd
                let attributeContainer = heading.parentElement;
                let attributeItems = [];
                
                // Try to find Hm3kOd elements in the same container or nearby
                if (attributeContainer) {
                    attributeItems = attributeContainer.querySelectorAll('.Hm3kOd');
                }
                
                // If not found, try looking in the next sibling
                if (attributeItems.length === 0) {
                    let nextElement = heading.nextElementSibling;
                    while (nextElement && attributeItems.length === 0) {
                        attributeItems = nextElement.querySelectorAll('.Hm3kOd');
                        nextElement = nextElement.nextElementSibling;
                    }
                }
                
                // If still not found, try looking in parent's siblings
                if (attributeItems.length === 0 && heading.parentElement) {
                    let parentSibling = heading.parentElement.nextElementSibling;
                    while (parentSibling && attributeItems.length === 0) {
                        attributeItems = parentSibling.querySelectorAll('.Hm3kOd');
                        parentSibling = parentSibling.nextElementSibling;
                    }
                }
                
                if (attributeItems.length > 0) {
                    console.log(`ProListExtractor: Found ${attributeItems.length} attribute items in ${normalizedSectionTitle}`);
                    
                    // Initialize the category array if not exists
                    if (!attributes[normalizedSectionTitle]) {
                        attributes[normalizedSectionTitle] = [];
                    }
                    
                    // Extract individual attributes
                    for (const item of attributeItems) {
                        let attributeText = '';
                        
                        // The structure is: <div class="uXOFJb"><span class="Hm3kOd">...</span><span>Attribute Text</span></div>
                        // So we need to look at the parent container and find the text span
                        const parentContainer = item.parentElement;
                        if (parentContainer) {
                            // Look for a span sibling that contains the text
                            const textSpan = parentContainer.querySelector('span:not(.Hm3kOd):not(.SXZKyc)');
                            if (textSpan) {
                                attributeText = textSpan.textContent.trim();
                            }
                        }
                        
                        // Fallback: Try multiple approaches to get the text content
                        if (!attributeText) {
                            // 1. Direct text content of the item
                            attributeText = item.textContent.trim();
                            
                            // 2. If no direct text, look for spans
                            if (!attributeText) {
                                const spans = item.querySelectorAll('span');
                                for (const span of spans) {
                                    const spanText = span.textContent.trim();
                                    if (spanText && spanText.length > 0) {
                                        attributeText = spanText;
                                        break;
                                    }
                                }
                            }
                            
                            // 3. If still no text, look for any text-containing child
                            if (!attributeText) {
                                const allChildren = item.querySelectorAll('*');
                                for (const child of allChildren) {
                                    const childText = child.textContent.trim();
                                    if (childText && childText.length > 0 && !childText.includes('✓') && !childText.includes('✗')) {
                                        attributeText = childText;
                                        break;
                                    }
                                }
                            }
                        }
                        
                        if (attributeText && attributeText.length > 0) {
                            const normalizedAttributeText = this.normalizeAndCapitalize(attributeText);
                            const attributeKey = this.createNormalizedKey(normalizedAttributeText);
                            
                            // Check for duplicates
                            if (!seenAttributes.has(attributeKey)) {
                                seenAttributes.add(attributeKey);
                                attributes[normalizedSectionTitle].push(normalizedAttributeText);
                                console.log(`ProListExtractor: Added attribute: ${normalizedAttributeText} (${normalizedSectionTitle})`);
                            } else {
                                console.log(`ProListExtractor: Skipped duplicate attribute: ${normalizedAttributeText}`);
                            }
                        } else {
                            console.log(`ProListExtractor: No text found in attribute item for ${normalizedSectionTitle}`);
                        }
                    }
                } else {
                    console.log(`ProListExtractor: No attribute items found for section ${normalizedSectionTitle}`);
                }
            }
            
            // If no sections found, try a more general approach
            if (Object.keys(attributes).length === 0) {
                console.log(`ProListExtractor: No attributes found with section approach, trying general approach`);
                
                // Look for any Hm3kOd elements without specific sections
                const allAttributeItems = document.querySelectorAll('.Hm3kOd');
                console.log(`ProListExtractor: Found ${allAttributeItems.length} general attribute items`);
                
                if (allAttributeItems.length > 0) {
                    attributes['General Details'] = [];
                }
                
                for (const item of allAttributeItems) {
                    let attributeText = '';
                    
                    // The structure is: <div class="uXOFJb"><span class="Hm3kOd">...</span><span>Attribute Text</span></div>
                    // So we need to look at the parent container and find the text span
                    const parentContainer = item.parentElement;
                    if (parentContainer) {
                        // Look for a span sibling that contains the text
                        const textSpan = parentContainer.querySelector('span:not(.Hm3kOd):not(.SXZKyc)');
                        if (textSpan) {
                            attributeText = textSpan.textContent.trim();
                        }
                    }
                    
                    // Fallback: Try multiple approaches to get the text content
                    if (!attributeText) {
                        // 1. Direct text content of the item
                        attributeText = item.textContent.trim();
                        
                        // 2. If no direct text, look for spans
                        if (!attributeText) {
                            const spans = item.querySelectorAll('span');
                            for (const span of spans) {
                                const spanText = span.textContent.trim();
                                if (spanText && spanText.length > 0) {
                                    attributeText = spanText;
                                    break;
                                }
                            }
                        }
                        
                        // 3. If still no text, look for any text-containing child
                        if (!attributeText) {
                            const allChildren = item.querySelectorAll('*');
                            for (const child of allChildren) {
                                const childText = child.textContent.trim();
                                if (childText && childText.length > 0 && !childText.includes('✓') && !childText.includes('✗')) {
                                    attributeText = childText;
                                    break;
                                }
                            }
                        }
                    }
                    
                    if (attributeText && attributeText.length > 0) {
                        const normalizedAttributeText = this.normalizeAndCapitalize(attributeText);
                        const attributeKey = this.createNormalizedKey(normalizedAttributeText);
                        
                        // Check for duplicates
                        if (!seenAttributes.has(attributeKey)) {
                            seenAttributes.add(attributeKey);
                            attributes['General Details'].push(normalizedAttributeText);
                            console.log(`ProListExtractor: Added general attribute: ${normalizedAttributeText}`);
                        } else {
                            console.log(`ProListExtractor: Skipped duplicate general attribute: ${normalizedAttributeText}`);
                        }
                    } else {
                        console.log(`ProListExtractor: No text found in general attribute item`);
                    }
                }
            }
            
            console.log(`ProListExtractor: Extracted ${Object.keys(attributes).length} attribute sections for ${businessName}`);
            return attributes;
            
        } catch (error) {
            console.error(`ProListExtractor: Error extracting attributes from opened business ${businessName}:`, error);
            return {};
        }
    }

    generateAttributesAnalysis(allAttributes) {
        try {
            const analysis = {
                totalBusinesses: allAttributes.length,
                totalAttributeSections: 0,
                attributesBySection: {},
                commonAttributes: {},
                businessesWithCategories: 0,
                businessesWithContact: 0,
                businessesWithHours: 0
            };

            // Process each business's attributes
            allAttributes.forEach(business => {
                Object.keys(business.attributes).forEach(sectionName => {
                    analysis.totalAttributeSections++;
                    
                    if (!analysis.attributesBySection[sectionName]) {
                        analysis.attributesBySection[sectionName] = 0;
                    }
                    analysis.attributesBySection[sectionName]++;
                    
                    // Track specific attribute types
                    if (sectionName.toLowerCase().includes('categor')) {
                        analysis.businessesWithCategories++;
                    }
                    if (sectionName.toLowerCase().includes('contact')) {
                        analysis.businessesWithContact++;
                    }
                    if (sectionName.toLowerCase().includes('hours')) {
                        analysis.businessesWithHours++;
                    }
                    
                    // Track common individual attributes
                    business.attributes[sectionName].forEach(attr => {
                        if (!analysis.commonAttributes[attr]) {
                            analysis.commonAttributes[attr] = 0;
                        }
                        analysis.commonAttributes[attr]++;
                    });
                });
            });

            // Get top common attributes
            analysis.topCommonAttributes = Object.entries(analysis.commonAttributes)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10)
                .map(([attr, count]) => ({
                    attribute: attr,
                    count,
                    percentage: ((count / analysis.totalBusinesses) * 100).toFixed(1)
                }));

            return analysis;
        } catch (error) {
            console.error('ProListExtractor: Error generating attributes analysis:', error);
            return {};
        }
    }

    // Add method to get extraction status for popup display
    getExtractionStatus() {
        const status = {
            hasServices: !!window.lastExtractedServices,
            hasAttributes: !!window.lastExtractedAttributes,
            hasReviews: !!window.lastExtractedReviews,
            servicesCount: 0,
            attributesCount: 0,
            reviewsCount: 0,
            businessCount: 0
        };
        
        if (window.lastExtractedServices) {
            status.businessCount = window.lastExtractedServices.businessCount || 0;
            status.servicesCount = window.lastExtractedServices.services ? window.lastExtractedServices.services.length : 0;
        }
        
        if (window.lastExtractedAttributes) {
            status.businessCount = Math.max(status.businessCount, window.lastExtractedAttributes.businessCount || 0);
            if (window.lastExtractedAttributes.attributes) {
                status.attributesCount = window.lastExtractedAttributes.attributes.reduce((total, business) => {
                    if (business.attributes) {
                        return total + Object.values(business.attributes).reduce((sum, attrs) => sum + attrs.length, 0);
                    }
                    return total;
                }, 0);
            }
        }
        
        if (window.lastExtractedReviews) {
            status.reviewsCount = window.lastExtractedReviews.reviewCount || (window.lastExtractedReviews.reviews ? window.lastExtractedReviews.reviews.length : 0);
        }
        
        console.log('ProListExtractor: Current extraction status:', status);
        return status;
    }

    // Add method to export CSV directly like the Search extractor
    exportToCSV() {
        console.log('ProListExtractor: Export requested');
        
        let exportedCount = 0;
        const availableData = [];
        
        try {
            // Export services if available
            if (window.lastExtractedServices && window.lastExtractedServices.services) {
                const servicesCsvContent = this.convertServicesToCSV(window.lastExtractedServices);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                this.downloadCSV(servicesCsvContent, `prolist-services-${timestamp}.csv`);
                availableData.push('Services');
                exportedCount++;
                console.log('ProListExtractor: Exported services data');
            }
            
            // Export attributes if available
            if (window.lastExtractedAttributes && window.lastExtractedAttributes.attributes && window.lastExtractedAttributes.attributes.length > 0) {
                // Check if there are actually any attributes to export
                let hasAttributes = false;
                for (const business of window.lastExtractedAttributes.attributes) {
                    if (business.attributes && Object.keys(business.attributes).length > 0) {
                        hasAttributes = true;
                        break;
                    }
                }
                
                if (hasAttributes) {
                    const attributesCsvContent = this.convertAttributesToCSV(window.lastExtractedAttributes);
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    this.downloadCSV(attributesCsvContent, `prolist-attributes-${timestamp}.csv`);
                    availableData.push('Attributes');
                    exportedCount++;
                    console.log('ProListExtractor: Exported attributes data');
                } else {
                    console.log('ProListExtractor: Attributes data exists but contains no actual attributes to export');
                }
            } else {
                console.log('ProListExtractor: No attributes data available for export');
            }
            
            // Export reviews if available
            if (window.lastExtractedReviews && window.lastExtractedReviews.reviews && window.lastExtractedReviews.reviews.length > 0) {
                // Use the ProListReviewScraper's export function which includes business name
                if (window.proListReviewScraper && typeof window.proListReviewScraper.exportToCSV === 'function') {
                    console.log('ProListExtractor: Using ProListReviewScraper export (includes business name)');
                    const reviewsCsvContent = window.proListReviewScraper.exportToCSV();
                    
                    // Extract business name from the review data for filename
                    let businessName = 'unknown-business';
                    if (window.lastExtractedReviews.reviews && window.lastExtractedReviews.reviews.length > 0) {
                        const firstReview = window.lastExtractedReviews.reviews[0];
                        if (firstReview && firstReview.businessName) {
                            businessName = this.formatBusinessNameForFilename(firstReview.businessName);
                        }
                    }
                    
                    const timestamp = new Date().toISOString().split('T')[0]; // Get YYYY-MM-DD format
                    const filename = `${businessName}-reviews-${timestamp}.csv`;
                    this.downloadCSV(reviewsCsvContent, filename);
                } else {
                    // Fallback to ProListExtractor method if ProListReviewScraper not available
                    console.log('ProListExtractor: ProListReviewScraper not available, using fallback export');
                    
                    // Extract business name for filename
                    let businessName = 'unknown-business';
                    if (window.lastExtractedReviews.reviews && window.lastExtractedReviews.reviews.length > 0) {
                        const firstReview = window.lastExtractedReviews.reviews[0];
                        if (firstReview && firstReview.businessName) {
                            businessName = this.formatBusinessNameForFilename(firstReview.businessName);
                        }
                    }
                    
                    const reviewsCsvContent = this.convertReviewsToCSV(window.lastExtractedReviews);
                    const timestamp = new Date().toISOString().split('T')[0]; // Get YYYY-MM-DD format
                    const filename = `${businessName}-reviews-${timestamp}.csv`;
                    this.downloadCSV(reviewsCsvContent, filename);
                }
                availableData.push('Reviews');
                exportedCount++;
                console.log('ProListExtractor: Exported reviews data');
            } else {
                console.log('ProListExtractor: No reviews data available for export');
            }
            
            // Show success message
            if (exportedCount > 0) {
                console.log(`ProListExtractor: Successfully exported ${exportedCount} data type(s): ${availableData.join(', ')}`);
                // Show browser notification if available
                GMBNotifications.showSuccess('ProList Export Complete!', 
                    `Exported ${availableData.join(' and ')} data`);
            } else {
                console.log('ProListExtractor: No data available for export');
                alert('No ProList data available for export. Please run some extractions first.');
            }
            
        } catch (error) {
            console.error('ProListExtractor: Error during export:', error);
            alert(`Export failed: ${error.message}`);
        }
    }

    // CSV conversion methods
    convertServicesToCSV(servicesData) {
        if (!servicesData || !servicesData.services || servicesData.services.length === 0) {
            return 'No Pro List services data available';
        }
        
        let csvContent = 'Pro List Business Services\n\n';
        csvContent += 'SUMMARY\n';
        csvContent += `Total Businesses,${servicesData.businessCount || 'N/A'}\n`;
        csvContent += `Total Services,${servicesData.services.length}\n`;
        csvContent += '\n';
        
        csvContent += 'DETAILED SERVICES\n';
        csvContent += 'Business Index,Business Name,Service Category,Service Name,Extracted At\n';
        
        servicesData.services.forEach(item => {
            const serviceText = typeof item.service === 'object' ? 
                `${item.service.category || ''},${item.service.service || ''}` :
                `,${item.service || ''}`;
            csvContent += `${item.businessIndex || 'N/A'},"${(item.businessName || '').replace(/"/g, '""')}",${serviceText},"${item.extractedAt || ''}"\n`;
        });
        
        return csvContent;
    }

    convertAttributesToCSV(attributesData) {
        if (!attributesData || !attributesData.attributes || attributesData.attributes.length === 0) {
            return 'No Pro List attributes data available\n\nNo businesses were processed for attributes extraction.';
        }
        
        let csvContent = 'Pro List Business Attributes\n\n';
        csvContent += 'SUMMARY\n';
        csvContent += `Total Businesses,${attributesData.businessCount}\n`;
        csvContent += `Businesses with Attributes,${attributesData.attributes.length}\n`;
        
        // Count businesses with actual attributes
        let businessesWithData = 0;
        let totalAttributes = 0;
        attributesData.attributes.forEach(business => {
            if (business.attributes && Object.keys(business.attributes).length > 0) {
                businessesWithData++;
                Object.values(business.attributes).forEach(attrs => {
                    totalAttributes += attrs.length;
                });
            }
        });
        
        csvContent += `Businesses with Actual Attributes,${businessesWithData}\n`;
        csvContent += `Total Attributes Found,${totalAttributes}\n`;
        csvContent += '\n';
        
        if (totalAttributes === 0) {
            csvContent += 'RESULT\n';
            csvContent += 'No attributes were found for any of the processed businesses.\n';
            csvContent += 'This may indicate that the businesses do not have detailed attribute information available,\n';
            csvContent += 'or the attribute extraction selectors may need to be updated.\n\n';
            
            csvContent += 'PROCESSED BUSINESSES\n';
            csvContent += 'Business Index,Business Name,Status,Extracted At\n';
            attributesData.attributes.forEach(business => {
                const status = (business.attributes && Object.keys(business.attributes).length > 0) ? 'Has Attributes' : 'No Attributes Found';
                csvContent += `${business.businessIndex},"${business.businessName.replace(/"/g, '""')}","${status}","${business.extractedAt}"\n`;
            });
        } else {
            csvContent += 'DETAILED BUSINESS ATTRIBUTES\n';
            csvContent += 'Business Index,Business Name,Section,Attribute,Extracted At\n';
            
            attributesData.attributes.forEach(business => {
                if (business.attributes && Object.keys(business.attributes).length > 0) {
                    Object.keys(business.attributes).forEach(sectionName => {
                        business.attributes[sectionName].forEach(attr => {
                            csvContent += `${business.businessIndex},"${business.businessName.replace(/"/g, '""')}","${sectionName}","${attr.replace(/"/g, '""')}","${business.extractedAt}"\n`;
                        });
                    });
                }
            });
        }
        
        return csvContent;
    }

    convertReviewsToCSV(reviewsData) {
        if (!reviewsData || !reviewsData.reviews || reviewsData.reviews.length === 0) {
            return 'No Pro List reviews data available';
        }
        
        let csvContent = 'Pro List Business Reviews\n\n';
        csvContent += 'SUMMARY\n';
        csvContent += `Total Reviews,${reviewsData.reviewCount || reviewsData.reviews.length}\n`;
        csvContent += '\n';
        
        csvContent += 'DETAILED REVIEWS\n';
        csvContent += 'Review ID,Reviewer Name,Rating,Date,Review Text,Helpful Count,Response,Photo Count,Extracted At\n';
        
        reviewsData.reviews.forEach(review => {
            csvContent += `${review.id || ''},"${(review.reviewerName || '').replace(/"/g, '""')}",${review.rating || ''},"${review.date || ''}","${(review.reviewText || '').replace(/"/g, '""')}",${review.helpfulCount || ''},"${(review.response || '').replace(/"/g, '""')}",${review.photoCount || 0},"${review.extractedAt || ''}"\n`;
        });
        
        return csvContent;
    }

    // Helper method to download CSV
    downloadCSV(csvContent, filename) {
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    // Helper method to format business name for filename
    formatBusinessNameForFilename(businessName) {
        if (!businessName) return 'unknown-business';
        
        return businessName
            .toLowerCase()                    // Convert to lowercase
            .replace(/[^a-z0-9\s]/g, '')     // Remove special characters except spaces
            .replace(/\s+/g, '-')            // Replace spaces with dashes
            .replace(/-+/g, '-')             // Replace multiple consecutive dashes with single dash
            .replace(/^-|-$/g, '');          // Remove leading/trailing dashes
    }
}

// Initialize the Pro List extractor when the script loads
const proListExtractor = new ProListExtractor();

// Make it available globally for debugging
window.proListExtractor = proListExtractor;

// Add a global test function for console debugging
window.testProListSelectors = function() {
    if (window.proListExtractor) {
        return window.proListExtractor.testSelectors();
    } else {
        console.log('❌ ProListExtractor not available');
        return null;
    }
}; 