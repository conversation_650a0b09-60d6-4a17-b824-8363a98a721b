/**
 * SEO Time Machines - Onboarding Content Script
 * Creates full-screen onboarding overlay in the active tab's page context
 */

// Namespace protection
if (window.GMBOnboardingInjected) {
    console.log('Onboarding content script already injected, skipping...');
} else {
    window.GMBOnboardingInjected = true;

    class OnboardingContentScript {
        constructor() {
            this.selectedToolsets = new Set();
            this.isActive = false;
            
            // Settings manager - copied from profiles.js for direct storage access
            this.settingsManager = {
                async get(key) {
                    return new Promise((resolve) => {
                        chrome.storage.local.get(key, (result) => {
                            resolve(result[key]);
                        });
                    });
                },
                async set(key, value) {
                    return new Promise((resolve) => {
                        chrome.storage.local.set({ [key]: value }, () => {
                            resolve();
                        });
                    });
                },
                async getAll() {
                    return new Promise((resolve) => {
                        chrome.storage.local.get(null, (items) => {
                            resolve(items);
                        });
                    });
                },
                async setAll(settings) {
                    return new Promise((resolve) => {
                        chrome.storage.local.set(settings, () => {
                            resolve();
                        });
                    });
                }
            };
            
            // Tool category mappings - each category maps to specific settings keys
            this.toolsetMappings = {
                'local-seo': [
                    'locationChangerEnabled',
                    'citationHunter',
                    'schemaEnabled',
                    'showHiddenEnabled',
                    'searchNAPInjector',
                    'currentLocationDisplayEnabled',
                    'trackedDomainsEnabled',
                    'openSingleListing',
                    'linksExtractorEnabled',
                    'pageStructureEnabled',
                    'seoTestsEnabled',
                    'utmBuilderEnabled',
                    'dragSelectLinksEnabled',
                    'boldFromSerpEnabled',
                    'clickToCopyEnabled',
                    'metadataEnabled',
                    'keywordEnabled',
                    'copyReplaceEnabled',
                    'autoCleanAndTitleEnabled',
                    'imagesEnabled',
                    'htagsEnabled',
                    'youtubeEmbedScraperEnabled',
                    'textTransformersEnabled',
                    'youtubeGifEnabled',
                    'youtubeFramesEnabled',
                    'youtubeThumbnailViewerEnabled',
                    // Universal shortcuts
                    'globalShortcutsEnabled',
                    'keyboardShortcutsEnabled'
                ],
                'web-dev': [
                    'showHiddenEnabled',
                    'pageStructureEnabled',
                    'responsiveEnabled',
                    'copyElementEnabled',
                    // Universal shortcuts
                    'globalShortcutsEnabled',
                    'keyboardShortcutsEnabled'
                ],
                'digital-marketing': [
                    'utmBuilderEnabled',
                    'utmTrackingCleanerEnabled',
                    'trackerDetectionEnabled',
                    'utmCopyCleanEnabled',
                    'utmCleanAndGoEnabled',
                    'clickToCopyEnabled',
                    'autoCleanAndTitleEnabled',
                    // Universal shortcuts
                    'globalShortcutsEnabled',
                    'keyboardShortcutsEnabled'
                ],
                'content-writer': [
                    'minimalReaderEnabled',
                    'minimalReaderSpeedReadingEnabled',
                    'copyElementEnabled',
                    'showLinksEnabled',
                    'metadataEnabled',
                    'keywordEnabled',
                    'copyReplaceEnabled',
                    'autoCleanAndTitleEnabled',
                    // Universal shortcuts
                    'globalShortcutsEnabled',
                    'keyboardShortcutsEnabled'
                ],
                'designer': [
                    'screenshotToolEnabled',
                    'imagesEnabled',
                    'htagsEnabled',
                    'headingStructureEnabled',
                    // Universal shortcuts
                    'globalShortcutsEnabled',
                    'keyboardShortcutsEnabled'
                ],
                'email-tools': [
                    'reverseGmailOrderEnabled',
                    'showGmailTimeEnabled',
                    'showGmailIconsEnabled',
                    'massUnsubscribeEnabled',
                    'gmailEnhancedTimestampsEnabled',
                    'emailPinnerEnabled',
                    'gmailThreadExpanderEnabled',
                    // Universal shortcuts
                    'globalShortcutsEnabled',
                    'keyboardShortcutsEnabled'
                ],
                'youtube-tools': [
                    'screenshotYouTubeEnabled',
                    'youtubeGifEnabled',
                    'youtubeFramesEnabled',
                    'youtubeThumbnailViewerEnabled',
                    'youtubeAdsSkipperEnabled',
                    'youtubeEmbedScraperEnabled',
                    'screenshotKeyEnabled',
                    'videoSpeedControllerEnabled',
                    // Universal shortcuts
                    'globalShortcutsEnabled',
                    'keyboardShortcutsEnabled'
                ],
                'productivity-tools': [
                    'pomodoroEnabled',
                    'pomodoroBlockingEnabled',
                    'quickTimerEnabled',
                    'tasksEnabled',
                    'alertsEnabled',
                    'videoSpeedControllerEnabled',
                    'notificationUtilityEnabled',
                    // Universal shortcuts
                    'globalShortcutsEnabled',
                    'keyboardShortcutsEnabled'
                ],
                'seo-ninja': 'ALL_TOOLS' // Special case - enables everything
            };

            this.toolsets = [
                {
                    id: 'local-seo',
                    title: 'Local SEO',
                    firstWord: 'Local',
                    icon: 'MapPin',
                    color: 'border-green-400',
                    underlineColor: 'decoration-green-400',
                    iconColor: 'text-green-400',
                    features: [
                        'Location Changer (geolocation spoofing)',
                        'Citation Hunter (finds business citations)',
                        'Search NAP Injector (injects NAP data)',
                        'Current Location Display (shows location)',
                        'Tracked Domains (highlights domains)'
                    ]
                },
                {
                    id: 'web-dev',
                    title: 'Web Developer',
                    firstWord: 'Web',
                    icon: 'Code',
                    color: 'border-cyan-400',
                    underlineColor: 'decoration-cyan-400',
                    iconColor: 'text-cyan-400',
                    features: [
                        'Schema Markup Extractor (extracts JSON-LD)',
                        'CSS Class Inspector (inspects element CSS)',
                        'Show Hidden Elements (reveals hidden elements)',
                        'Responsive Design Simulator (tests layouts)',
                        'Links Extractor (interactive link extraction)'
                    ]
                },
                {
                    id: 'digital-marketing',
                    title: 'Digital Marketing',
                    firstWord: 'Digital',
                    icon: 'TrendingUp',
                    color: 'border-purple-400',
                    underlineColor: 'decoration-purple-400',
                    iconColor: 'text-purple-400',
                    features: [
                        'UTM Builder (creates UTM tracking URLs)',
                        'UTM Tracking Cleaner (removes parameters)',
                        'Tracker Detection (detects tracking scripts)',
                        'Top URLs Copier (copies top search URLs)',
                        'Drag Select Links (batch link selection)'
                    ]
                },
                {
                    id: 'content-writer',
                    title: 'Content Writer',
                    firstWord: 'Content',
                    icon: 'PenTool',
                    color: 'border-orange-400',
                    underlineColor: 'decoration-orange-400',
                    iconColor: 'text-orange-400',
                    features: [
                        'Word Counter (counts words and characters)',
                        'Keyword Density Calculator (calculates density)',
                        'Clean Selected Content (cleans HTML)',
                        'Quick Edit (quick page editing)',
                        'Minimal Reader (clean reading mode)'
                    ]
                },
                {
                    id: 'designer',
                    title: 'Designer',
                    firstWord: 'Designer',
                    icon: 'Palette',
                    color: 'border-pink-400',
                    underlineColor: 'decoration-pink-400',
                    iconColor: 'text-pink-400',
                    features: [
                        'Color Picker (EyeDropper API color picker)',
                        'Color Palette Extractor (extracts page colors)',
                        'Font Inspector (analyzes page fonts)',
                        'Images Analyzer (analyzes page images)',
                        'Screenshot Tool (full-featured screenshots)'
                    ]
                },
                {
                    id: 'email-tools',
                    title: 'Email Tools',
                    firstWord: 'Email',
                    icon: 'Mail',
                    color: 'border-blue-400',
                    underlineColor: 'decoration-blue-400',
                    iconColor: 'text-blue-400',
                    features: [
                        'Reverse Gmail Order (reverses email order)',
                        'Gmail Time Formatter (custom time formats)',
                        'Gmail Sender Icons (shows sender icons)',
                        'Mass Unsubscribe (bulk unsubscribe tool)',
                        'Gmail Enhanced Timestamps (better timestamps)'
                    ]
                },
                {
                    id: 'youtube-tools',
                    title: 'YouTube Tools',
                    firstWord: 'YouTube',
                    icon: 'Play',
                    color: 'border-red-400',
                    underlineColor: 'decoration-red-400',
                    iconColor: 'text-red-400',
                    features: [
                        'YouTube Screenshot Tool (screenshots with editing)',
                        'YouTube GIF Creator (creates GIFs from videos)',
                        'YouTube Frames Tool (frame-by-frame navigation)',
                        'YouTube Thumbnail Viewer (views thumbnails)',
                        'YouTube Ads Skipper (skips advertisements)'
                    ]
                },
                {
                    id: 'productivity-tools',
                    title: 'Productivity Tools',
                    firstWord: 'Productivity',
                    icon: 'Clock',
                    color: 'border-yellow-400',
                    underlineColor: 'decoration-yellow-400',
                    iconColor: 'text-yellow-400',
                    features: [
                        'Pomodoro Timer (productivity timer with blocking)',
                        'Quick Timer (simple countdown timer)',
                        'Tasks System (task management)',
                        'Alerts & Reminders (notification system)',
                        'Video Speed Controller (controls playback speed)'
                    ]
                },
                {
                    id: 'seo-ninja',
                    title: 'Maximum Power',
                    firstWord: 'SEO',
                    icon: 'Search',
                    color: 'border-violet-400',
                    underlineColor: 'decoration-violet-400',
                    iconColor: 'text-violet-400',
                    features: [
                        'All Quick Actions (complete toolkit)',
                        'All Extras Tools (every single feature)',
                        'Advanced Data Extractors (full power)',
                        'Complete SEO Analysis Suite (everything)',
                        'Ultimate Power User Experience (maximum)'
                    ]
                }
            ];

            this.init();
        }

        init() {
            // Listen for messages from popup
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                if (message.action === 'showOnboarding') {
                    this.showOnboarding();
                    sendResponse({ success: true });
                } else if (message.action === 'hideOnboarding') {
                    this.hideOnboarding();
                    sendResponse({ success: true });
                }
            });

            // Check for auto-show flag (after extension reload)
            this.checkAutoShowOnboarding();

            console.log('Onboarding content script initialized');
        }

        async checkAutoShowOnboarding() {
            try {
                // Check if we should auto-show onboarding after reload
                const autoShowFlag = await this.settingsManager.get('showOnboardingAfterReload');
                
                if (autoShowFlag) {
                    console.log('🔄 Auto-show onboarding flag detected - showing onboarding after extension reload');
                    
                    // Clear the flag immediately to prevent repeated showing
                    await chrome.storage.local.remove(['showOnboardingAfterReload']);
                    
                    // Small delay to ensure page is fully loaded
                    setTimeout(() => {
                        this.showOnboarding();
                    }, 500);
                }
            } catch (error) {
                console.error('❌ Error checking auto-show onboarding flag:', error);
            }
        }

        async resetAllToggleSettings() {
            console.log('🔄 Starting comprehensive toggle settings reset...');
            
            // Get all available toggle settings (same comprehensive list used by seo-ninja mode)
            const allSettings = await this.getAllAvailableSettings();
            const resetSettings = {};
            
            // Set all toggle settings to false (preserving user data like domains, audio settings, etc.)
            Object.keys(allSettings).forEach(key => {
                resetSettings[key] = false;
            });
            
            console.log(`📊 Resetting ${Object.keys(resetSettings).length} toggle settings to false`);
            console.log('🔧 Settings being reset:', Object.keys(resetSettings).join(', '));
            
            // Get current settings to preserve non-toggle data
            const currentSettingsResult = await chrome.storage.local.get('gmbExtractorSettings');
            const existingSettings = currentSettingsResult.gmbExtractorSettings || {};
            
            // Merge - this will turn off all toggles but keep other user data
            const updatedSettings = { ...existingSettings, ...resetSettings };
            
            // Save the reset settings
            await chrome.storage.local.set({ 
                gmbExtractorSettings: updatedSettings
            });
            
            // CRITICAL: Immediately propagate settings changes to main extension
            // Same pattern used in applyToolsetSettings() method
            if (window.settingsManager) {
                await window.settingsManager.loadSettings();
                window.settingsManager.updateUI();
                console.log('✅ Settings reset propagated to main extension');
            } else {
                console.log('⚠️ SettingsManager not available - settings reset saved but needs manual reload');
            }
            
            console.log('✅ All toggle settings reset to false, user data preserved, changes propagated');
        }

        async showOnboarding() {
            if (this.isActive) return;

            // If this is a rerun (onboarding was already completed), reset all toggle settings first
            const onboardingStatus = await this.settingsManager.get('onboardingCompleted');
            if (onboardingStatus) {
                console.log('🔄 Onboarding rerun detected - resetting all toggle settings');
                await this.resetAllToggleSettings();
            }

            this.isActive = true;
            this.createOnboardingOverlay();
        }

        hideOnboarding() {
            if (!this.isActive) return;

            this.removeOnboardingOverlay();
            this.isActive = false;
        }

        createOnboardingOverlay() {
            // Remove any existing overlay
            this.removeOnboardingOverlay();

            // Inject CSS if not already present
            this.injectCSS();

            // Create main overlay container
            const overlay = document.createElement('div');
            overlay.id = 'gmbOnboardingOverlay';
            overlay.className = 'gmb-onboarding-overlay';
            
            // Create backdrop
            const backdrop = document.createElement('div');
            backdrop.className = 'gmb-onboarding-backdrop';

            // Create content container
            const content = document.createElement('div');
            content.className = 'gmb-onboarding-content';
            content.innerHTML = this.generateOverlayHTML();

            overlay.appendChild(backdrop);
            overlay.appendChild(content);
            document.body.appendChild(overlay);

            // Add event listeners
            this.attachEventListeners();

            // Focus management
            content.focus();

            // ESC key support
            document.addEventListener('keydown', this.handleKeydown.bind(this));

            console.log('Onboarding overlay created in page context');
        }

        injectCSS() {
            if (document.getElementById('gmbOnboardingCSS')) return;

            const style = document.createElement('style');
            style.id = 'gmbOnboardingCSS';
            style.textContent = `
                /* GMB Onboarding Overlay Styles */
                .gmb-onboarding-overlay {
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100vw !important;
                    height: 100vh !important;
                    z-index: 2147483647 !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
                    pointer-events: all !important;
                }

                .gmb-onboarding-backdrop {
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100vw !important;
                    height: 100vh !important;
                    background: rgba(0, 0, 0, 0.85) !important;
                    backdrop-filter: blur(8px) !important;
                    z-index: 2147483646 !important;
                }

                .gmb-onboarding-content {
                    position: relative !important;
                    background: #111827 !important;
                    border: 2px solid #7C3AED !important;
                    border-radius: 12px !important;
                    width: 1200px !important;
                    max-width: 95vw !important;
                    max-height: 95vh !important;
                    overflow-y: auto !important;
                    box-shadow: 0 25px 50px -12px rgba(124, 58, 237, 0.25) !important;
                    z-index: 2147483647 !important;
                    outline: none !important;
                }

                .gmb-onboarding-container {
                    padding: 32px !important;
                }

                .gmb-onboarding-header {
                    text-align: center !important;
                    margin-bottom: 32px !important;
                }

                .gmb-onboarding-title {
                    font-size: 28px !important;
                    font-weight: 700 !important;
                    color: #ffffff !important;
                    margin: 0 0 8px 0 !important;
                    line-height: 1.2 !important;
                    transition: all 0.3s ease !important;
                }

                .gmb-onboarding-subtitle {
                    font-size: 18px !important;
                    color: #9ca3af !important;
                    margin: 0 !important;
                    font-weight: 400 !important;
                }

                .gmb-onboarding-grid {
                    display: grid !important;
                    grid-template-columns: repeat(3, 1fr) !important;
                    gap: 20px !important;
                    margin-bottom: 32px !important;
                }

                .gmb-toolset-card {
                    position: relative !important;
                    background: #1f2937 !important;
                    border: 2px solid #374151 !important;
                    border-radius: 12px !important;
                    padding: 20px !important;
                    cursor: pointer !important;
                    transition: all 0.3s ease !important;
                    min-height: 200px !important;
                    display: flex !important;
                    flex-direction: column !important;
                }

                .gmb-toolset-card:hover {
                    border-color: #6b7280 !important;
                    background: #374151 !important;
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
                    transform: translateY(-2px) !important;
                }

                .gmb-toolset-card.selected {
                    background: #1f2937 !important;
                    box-shadow: 0 8px 32px rgba(124, 58, 237, 0.25) !important;
                    transform: translateY(-2px) !important;
                }

                .gmb-toolset-card.selected[data-color="border-green-400"] {
                    border-color: #4ade80 !important;
                    box-shadow: 0 8px 32px rgba(74, 222, 128, 0.2) !important;
                }

                .gmb-toolset-card.selected[data-color="border-cyan-400"] {
                    border-color: #22d3ee !important;
                    box-shadow: 0 8px 32px rgba(34, 211, 238, 0.2) !important;
                }

                .gmb-toolset-card.selected[data-color="border-purple-400"] {
                    border-color: #a855f7 !important;
                    box-shadow: 0 8px 32px rgba(168, 85, 247, 0.2) !important;
                }

                .gmb-toolset-card.selected[data-color="border-orange-400"] {
                    border-color: #fb923c !important;
                    box-shadow: 0 8px 32px rgba(251, 146, 60, 0.2) !important;
                }

                .gmb-toolset-card.selected[data-color="border-pink-400"] {
                    border-color: #f472b6 !important;
                    box-shadow: 0 8px 32px rgba(244, 114, 182, 0.2) !important;
                }

                .gmb-toolset-card.selected[data-color="border-blue-400"] {
                    border-color: #60a5fa !important;
                    box-shadow: 0 8px 32px rgba(96, 165, 250, 0.2) !important;
                }

                .gmb-toolset-card.selected[data-color="border-red-400"] {
                    border-color: #f87171 !important;
                    box-shadow: 0 8px 32px rgba(248, 113, 113, 0.2) !important;
                }

                .gmb-toolset-card.selected[data-color="border-yellow-400"] {
                    border-color: #facc15 !important;
                    box-shadow: 0 8px 32px rgba(250, 204, 21, 0.2) !important;
                }

                .gmb-toolset-card.selected[data-color="border-violet-400"] {
                    border-color: #a78bfa !important;
                    box-shadow: 0 8px 32px rgba(167, 139, 250, 0.2) !important;
                }

                .gmb-toolset-header {
                    display: flex !important;
                    align-items: center !important;
                    margin-bottom: 12px !important;
                    position: relative !important;
                }

                .gmb-toolset-icon {
                    width: 40px !important;
                    height: 40px !important;
                    border-radius: 8px !important;
                    background: #374151 !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    margin-right: 12px !important;
                    transition: all 0.3s ease !important;
                    color: #9ca3af !important;
                }

                .gmb-toolset-card.selected .gmb-toolset-icon {
                    background: #374151 !important;
                }

                .gmb-toolset-card.selected[data-icon-color="text-green-400"] .gmb-toolset-icon {
                    color: #4ade80 !important;
                }

                .gmb-toolset-card.selected[data-icon-color="text-cyan-400"] .gmb-toolset-icon {
                    color: #22d3ee !important;
                }

                .gmb-toolset-card.selected[data-icon-color="text-purple-400"] .gmb-toolset-icon {
                    color: #a855f7 !important;
                }

                .gmb-toolset-card.selected[data-icon-color="text-orange-400"] .gmb-toolset-icon {
                    color: #fb923c !important;
                }

                .gmb-toolset-card.selected[data-icon-color="text-pink-400"] .gmb-toolset-icon {
                    color: #f472b6 !important;
                }

                .gmb-toolset-card.selected[data-icon-color="text-blue-400"] .gmb-toolset-icon {
                    color: #60a5fa !important;
                }

                .gmb-toolset-card.selected[data-icon-color="text-red-400"] .gmb-toolset-icon {
                    color: #f87171 !important;
                }

                .gmb-toolset-card.selected[data-icon-color="text-yellow-400"] .gmb-toolset-icon {
                    color: #facc15 !important;
                }

                .gmb-toolset-card.selected[data-icon-color="text-violet-400"] .gmb-toolset-icon {
                    color: #a78bfa !important;
                }

                .gmb-toolset-title {
                    font-size: 16px !important;
                    font-weight: 600 !important;
                    color: #ffffff !important;
                    margin: 0 !important;
                    flex: 1 !important;
                }

                .gmb-toolset-checkbox {
                    width: 20px !important;
                    height: 20px !important;
                    border-radius: 50% !important;
                    background: #374151 !important;
                    border: 2px solid #6b7280 !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    transition: all 0.3s ease !important;
                    position: absolute !important;
                    top: 0 !important;
                    right: 0 !important;
                }

                .gmb-toolset-card.selected .gmb-toolset-checkbox {
                    background: #7C3AED !important;
                    border-color: #7C3AED !important;
                }

                .gmb-toolset-card.selected .gmb-toolset-checkbox::after {
                    content: '✓' !important;
                    color: #ffffff !important;
                    font-size: 12px !important;
                    font-weight: bold !important;
                }

                .gmb-toolset-features {
                    flex: 1 !important;
                    display: flex !important;
                    flex-direction: column !important;
                }

                .gmb-features-title {
                    font-size: 12px !important;
                    font-weight: 600 !important;
                    color: #9ca3af !important;
                    margin: 0 0 8px 0 !important;
                    text-transform: uppercase !important;
                    letter-spacing: 0.5px !important;
                }

                .gmb-features-list {
                    list-style: none !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    flex: 1 !important;
                }

                .gmb-feature-item {
                    display: flex !important;
                    align-items: flex-start !important;
                    margin-bottom: 4px !important;
                    font-size: 12px !important;
                    color: #9ca3af !important;
                    line-height: 1.4 !important;
                }

                .gmb-feature-bullet {
                    width: 4px !important;
                    height: 4px !important;
                    background: #6b7280 !important;
                    border-radius: 50% !important;
                    margin-right: 8px !important;
                    margin-top: 6px !important;
                    flex-shrink: 0 !important;
                }

                .gmb-onboarding-footer {
                    text-align: center !important;
                }

                .gmb-continue-btn {
                    background: #6b7280 !important;
                    color: #9ca3af !important;
                    border: none !important;
                    border-radius: 8px !important;
                    padding: 12px 24px !important;
                    font-size: 16px !important;
                    font-weight: 600 !important;
                    cursor: not-allowed !important;
                    transition: all 0.2s ease !important;
                    display: inline-flex !important;
                    align-items: center !important;
                    gap: 8px !important;
                    margin-bottom: 16px !important;
                }

                .gmb-continue-btn:not(.disabled) {
                    background: #7C3AED !important;
                    color: #ffffff !important;
                    cursor: pointer !important;
                }

                .gmb-continue-btn:not(.disabled):hover {
                    background: #6d28d9 !important;
                    transform: translateY(-1px) !important;
                    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3) !important;
                }

                .gmb-onboarding-note {
                    font-size: 12px !important;
                    color: #6b7280 !important;
                    margin: 0 !important;
                }

                /* Dynamic title underlines */
                .gmb-dynamic-underline {
                    text-decoration: underline !important;
                    text-underline-offset: 4px !important;
                    text-decoration-thickness: 2px !important;
                    transition: all 0.3s ease !important;
                }

                .gmb-dynamic-underline.decoration-green-400 {
                    text-decoration-color: #4ade80 !important;
                }

                .gmb-dynamic-underline.decoration-cyan-400 {
                    text-decoration-color: #22d3ee !important;
                }

                .gmb-dynamic-underline.decoration-purple-400 {
                    text-decoration-color: #a855f7 !important;
                }

                .gmb-dynamic-underline.decoration-orange-400 {
                    text-decoration-color: #fb923c !important;
                }

                .gmb-dynamic-underline.decoration-pink-400 {
                    text-decoration-color: #f472b6 !important;
                }

                .gmb-dynamic-underline.decoration-blue-400 {
                    text-decoration-color: #60a5fa !important;
                }

                .gmb-dynamic-underline.decoration-red-400 {
                    text-decoration-color: #f87171 !important;
                }

                .gmb-dynamic-underline.decoration-yellow-400 {
                    text-decoration-color: #facc15 !important;
                }

                .gmb-dynamic-underline.decoration-violet-400 {
                    text-decoration-color: #a78bfa !important;
                }

                /* Responsive Design */
                @media (max-width: 1024px) {
                    .gmb-onboarding-grid {
                        grid-template-columns: repeat(2, 1fr) !important;
                    }
                }

                @media (max-width: 768px) {
                    .gmb-onboarding-content {
                        width: 95vw !important;
                        max-height: 95vh !important;
                    }
                    
                    .gmb-onboarding-container {
                        padding: 16px !important;
                    }
                    
                    .gmb-onboarding-grid {
                        grid-template-columns: 1fr !important;
                        gap: 12px !important;
                    }
                    
                    .gmb-toolset-card {
                        min-height: 160px !important;
                        padding: 12px !important;
                    }
                    
                    .gmb-onboarding-title {
                        font-size: 24px !important;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        generateOverlayHTML() {
            return `
                <div class="gmb-onboarding-container">
                    <div class="gmb-onboarding-header">
                        <h1 class="gmb-onboarding-title" id="gmbOnboardingTitle">
                            ${this.getDynamicTitle()}
                        </h1>
                        <p class="gmb-onboarding-subtitle">Which toolsets would you like to turn on? You can select more than one.</p>
                    </div>

                    <div class="gmb-onboarding-grid" id="gmbOnboardingGrid">
                        ${this.toolsets.map(toolset => this.generateToolsetCard(toolset)).join('')}
                    </div>

                    <div class="gmb-onboarding-footer">
                        <button 
                            id="gmbContinueBtn" 
                            class="gmb-continue-btn disabled"
                        >
                            Continue with Selected Toolsets
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"></polyline>
                            </svg>
                        </button>
                        
                        <p class="gmb-onboarding-note">
                            You can change these settings anytime in the extension settings
                        </p>
                    </div>
                </div>
            `;
        }

        generateToolsetCard(toolset) {
            return `
                <div 
                    class="gmb-toolset-card" 
                    data-toolset="${toolset.id}"
                    data-color="${toolset.color}"
                    data-icon-color="${toolset.iconColor}"
                    data-underline-color="${toolset.underlineColor}"
                >
                    <div class="gmb-toolset-header">
                        <div class="gmb-toolset-icon">
                            ${this.getIconSVG(toolset.icon)}
                        </div>
                        <h3 class="gmb-toolset-title">${toolset.title}</h3>
                        <div class="gmb-toolset-checkbox"></div>
                    </div>

                    <div class="gmb-toolset-features">
                        <h4 class="gmb-features-title">Top Features:</h4>
                        <ul class="gmb-features-list">
                            ${toolset.features.map(feature => `
                                <li class="gmb-feature-item">
                                    <span class="gmb-feature-bullet"></span>
                                    ${feature}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }

        getIconSVG(iconName) {
            const icons = {
                MapPin: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>`,
                Code: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="16,18 22,12 16,6"></polyline><polyline points="8,6 2,12 8,18"></polyline></svg>`,
                TrendingUp: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="23,6 13.5,15.5 8.5,10.5 1,18"></polyline><polyline points="17,6 23,6 23,12"></polyline></svg>`,
                PenTool: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m12 19 7-7 3 3-7 7-3-3z"></path><path d="m18 13-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path><path d="m2 2 7.586 7.586"></path><circle cx="11" cy="11" r="2"></circle></svg>`,
                Palette: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="13.5" cy="6.5" r=".5"></circle><circle cx="17.5" cy="10.5" r=".5"></circle><circle cx="8.5" cy="7.5" r=".5"></circle><circle cx="6.5" cy="12.5" r=".5"></circle><path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"></path></svg>`,
                Mail: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></svg>`,
                Play: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><polygon points="10,8 16,12 10,16 10,8"></polygon></svg>`,
                Clock: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><polyline points="12,6 12,12 16,14"></polyline></svg>`,
                Search: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.35-4.35"></path></svg>`
            };
            return icons[iconName] || icons.Search;
        }

        getDynamicTitle() {
            if (this.selectedToolsets.size === 0) {
                return "SEO Time Machines - Choose Your Tools";
            }

            const selectedArray = Array.from(this.selectedToolsets);
            if (selectedArray.length === 1) {
                const toolset = this.toolsets.find(t => t.id === selectedArray[0]);
                return `<span><span class="gmb-dynamic-underline ${toolset.underlineColor}">${toolset.firstWord}</span> Time Machine - Do More</span>`;
            } else {
                return `<span><span class="gmb-dynamic-underline decoration-purple-400">Multi-Tool</span> Time Machine - Do More</span>`;
            }
        }

        attachEventListeners() {
            // Toolset card clicks
            document.querySelectorAll('.gmb-toolset-card').forEach(card => {
                card.addEventListener('click', (e) => this.handleToolsetClick(e));
            });

            // Continue button
            const continueBtn = document.getElementById('gmbContinueBtn');
            if (continueBtn) {
                continueBtn.addEventListener('click', () => this.handleContinue());
            }
        }

        handleToolsetClick(event) {
            const card = event.currentTarget;
            const toolsetId = card.dataset.toolset;
            
            if (this.selectedToolsets.has(toolsetId)) {
                this.selectedToolsets.delete(toolsetId);
                card.classList.remove('selected');
            } else {
                this.selectedToolsets.add(toolsetId);
                card.classList.add('selected');
            }

            this.updateUI();
        }

        updateUI() {
            // Update title
            const titleElement = document.getElementById('gmbOnboardingTitle');
            if (titleElement) {
                titleElement.innerHTML = this.getDynamicTitle();
            }

            // Update continue button
            const continueBtn = document.getElementById('gmbContinueBtn');
            if (continueBtn) {
                if (this.selectedToolsets.size > 0) {
                    continueBtn.classList.remove('disabled');
                    
                    const selectedArray = Array.from(this.selectedToolsets);
                    if (selectedArray.length === 1) {
                        const toolset = this.toolsets.find(t => t.id === selectedArray[0]);
                        continueBtn.innerHTML = `Continue with ${toolset.title} <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="9,18 15,12 9,6"></polyline></svg>`;
                    } else {
                        continueBtn.innerHTML = `Continue with ${selectedArray.length} Toolsets <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="9,18 15,12 9,6"></polyline></svg>`;
                    }
                } else {
                    continueBtn.classList.add('disabled');
                    continueBtn.innerHTML = `Continue with Selected Toolsets <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="9,18 15,12 9,6"></polyline></svg>`;
                }
            }
        }

        async handleContinue() {
            if (this.selectedToolsets.size === 0) return;

            try {
                // Show loading state
                const continueBtn = document.getElementById('gmbContinueBtn');
                if (continueBtn) {
                    continueBtn.innerHTML = 'Applying Settings...';
                    continueBtn.classList.add('disabled');
                }

                // Apply selected toolset settings
                await this.applyToolsetSettings();

                // Mark onboarding as completed
                await chrome.storage.local.set({ onboardingCompleted: true });

                // Show completion state
                this.showCompletionState();

                // Hide onboarding after a brief delay (no reload needed)
                setTimeout(() => {
                    this.hideOnboarding();
                }, 2000);

            } catch (error) {
                console.error('Error during onboarding completion:', error);
                this.showErrorState(error.message);
            }
        }

        async applyToolsetSettings() {
            // Use Set to collect unique setting keys (deduplication)
            const uniqueSettingKeys = new Set();

            // ADDITIVE behavior: collect settings from ALL selected toolsets
            for (const toolsetId of this.selectedToolsets) {
                if (toolsetId === 'seo-ninja') {
                    // SEO Ninja enables ALL tools
                    const allSettings = await this.getAllAvailableSettings();
                    Object.keys(allSettings).forEach(key => uniqueSettingKeys.add(key));
                } else {
                    // Regular toolset - add specific tools to the collection
                    const toolSettings = this.toolsetMappings[toolsetId];
                    if (toolSettings) {
                        toolSettings.forEach(settingKey => uniqueSettingKeys.add(settingKey));
                    }
                }
            }

            // Convert deduplicated settings to object
            const settingsToUpdate = {};
            uniqueSettingKeys.forEach(key => {
                settingsToUpdate[key] = true;
            });

            console.log(`📊 Additive selection: ${this.selectedToolsets.size} toolsets selected`);
            console.log(`🔧 Deduplication: ${uniqueSettingKeys.size} unique settings to apply`);

            // CRITICAL FIX: Use the same storage structure as settings.js
            // Get current settings from the gmbExtractorSettings key (where settings.js expects them)
            const currentSettingsResult = await chrome.storage.local.get('gmbExtractorSettings');
            const existingSettings = currentSettingsResult.gmbExtractorSettings || {};
            
            // Merge existing settings with onboarding settings
            const updatedSettings = { ...existingSettings, ...settingsToUpdate };
            
            // Save back to the correct storage structure (same as settings.js saveSettings method)
            await chrome.storage.local.set({ 
                gmbExtractorSettings: updatedSettings
            });
            
            console.log('✅ Onboarding: Settings saved to gmbExtractorSettings key');
            console.log('📋 Settings merged:', Object.keys(settingsToUpdate).length, 'new settings applied');
            
            // Reload settings and update UI (same as main settings system)
            if (window.settingsManager) {
                await window.settingsManager.loadSettings();
                window.settingsManager.updateUI();
                console.log('✅ Settings reloaded and UI updated');
            } else {
                console.log('⚠️ SettingsManager not available - settings saved but UI needs manual reload');
            }

            console.log('Applied settings for toolsets:', Array.from(this.selectedToolsets));
            console.log('Settings updated:', settingsToUpdate);
        }

        async getAllAvailableSettings() {
            // For Maximum Power - return all possible settings enabled
            return {
                // Quick Actions
                htagsEnabled: true,
                headingStructureEnabled: true,
                showLinksEnabled: true,
                showHiddenEnabled: true,
                schemaEnabled: true,
                imagesEnabled: true,
                metadataEnabled: true,
                utmBuilderEnabled: true,
                pageStructureEnabled: true,
                copyElementEnabled: true,
                linksExtractorEnabled: true,
                responsiveEnabled: true,
                seoTestsEnabled: true,
                trackerDetectionEnabled: true,
                keywordEnabled: true,
                boldFromSerpEnabled: true,
                youtubeEmbedScraperEnabled: true,
                keyboardShortcutsEnabled: true,
                
                // Extras Tools
                locationChangerEnabled: true,
                serpNumbering: true,
                sponsoredHighlighter: true,
                searchResultStats: true,
                citationHunter: true,
                searchNAPInjector: true,
                currentLocationDisplayEnabled: true,
                trackedDomainsEnabled: true,
                openSingleListing: true,
                textTransformersEnabled: true,
                
                // Gmail Tools
                reverseGmailOrderEnabled: true,
                showGmailTimeEnabled: true,
                showGmailIconsEnabled: true,
                massUnsubscribeEnabled: true,
                gmailEnhancedTimestampsEnabled: true,
                emailPinnerEnabled: true,
                gmailThreadExpanderEnabled: true,
                
                // YouTube Tools
                screenshotYouTubeEnabled: true,
                youtubeGifEnabled: true,
                youtubeFramesEnabled: true,
                youtubeThumbnailViewerEnabled: true,
                youtubeAdsSkipperEnabled: true,
                screenshotKeyEnabled: true,
                
                // Productivity Tools
                pomodoroEnabled: true,
                quickTimerEnabled: true,
                tasksEnabled: true,
                alertsEnabled: true,
                videoSpeedControllerEnabled: true,
                notificationUtilityEnabled: true,
                
                // UTM & URL Tools
                utmTrackingCleanerEnabled: true,
                utmCopyCleanEnabled: true,
                utmCleanAndGoEnabled: true,
                
                // Browser Tools
                minimalReaderEnabled: true,
                dragSelectLinksEnabled: true,
                screenshotToolEnabled: true,
                
                // Additional Tools
                topUrlsCopierEnabled: true,
                colorPickerEnabled: true,
                
                // Global Settings
                globalShortcutsEnabled: true,
                clickToCopyEnabled: true,
                copyReplaceEnabled: true,
                autoCleanAndTitleEnabled: true,
                showhiddenAutoDetectionEnabled: true,
                
                // Missing Tools from Settings.js
                pomodoroBlockingEnabled: true,
                minimalReaderSpeedReadingEnabled: true
            };
        }

        showCompletionState() {
            const content = document.querySelector('.gmb-onboarding-content');
            if (!content) return;

            content.innerHTML = `
                <div style="text-align: center; padding: 48px 24px;">
                    <div style="margin: 0 auto 24px; width: 64px; height: 64px; color: #22c55e;">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22,4 12,14.01 9,11.01"></polyline>
                        </svg>
                    </div>
                    <h2 style="font-size: 24px; font-weight: 700; color: #ffffff; margin: 0 0 16px 0;">You're All Set!</h2>
                    <p style="font-size: 16px; color: #9ca3af; margin: 0 0 32px 0; line-height: 1.5;">
                        Your selected toolsets have been enabled and are ready to use immediately.
                    </p>
                    <div id="completionState" style="display: flex; align-items: center; justify-content: center; gap: 12px; color: #22c55e; font-weight: 600;">
                        <div style="width: 20px; height: 20px; color: #22c55e;">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                <polyline points="22,4 12,14.01 9,11.01"></polyline>
                            </svg>
                        </div>
                        <span>Settings Applied Successfully!</span>
                    </div>
                    <button id="manualCloseBtn" onclick="window.GMBOnboardingContent.hideOnboarding()" style="margin-top: 20px; background: #7C3AED; color: #ffffff; border: none; border-radius: 8px; padding: 12px 24px; font-size: 16px; font-weight: 600; cursor: pointer;">
                        Start Using Tools
                    </button>
                </div>
                <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                </style>
            `;

            // Show manual close button after 3 seconds if reload doesn't work
            setTimeout(() => {
                const reloadingState = document.getElementById('reloadingState');
                const manualBtn = document.getElementById('manualCloseBtn');
                if (reloadingState) {
                    reloadingState.innerHTML = '<span style="color: #9ca3af;">Extension reload taking longer than expected...</span>';
                }
                if (manualBtn) {
                    manualBtn.style.display = 'inline-block';
                }
            }, 3000);
        }

        showErrorState(errorMessage) {
            const content = document.querySelector('.gmb-onboarding-content');
            if (!content) return;

            content.innerHTML = `
                <div style="text-align: center; padding: 48px 24px;">
                    <div style="margin: 0 auto 24px; width: 64px; height: 64px; color: #ef4444;">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="15" y1="9" x2="9" y2="15"></line>
                            <line x1="9" y1="9" x2="15" y2="15"></line>
                        </svg>
                    </div>
                    <h2 style="font-size: 24px; font-weight: 700; color: #ffffff; margin: 0 0 16px 0;">Setup Error</h2>
                    <p style="font-size: 16px; color: #9ca3af; margin: 0 0 32px 0; line-height: 1.5;">
                        There was an error applying your settings: ${errorMessage}
                    </p>
                    <button onclick="location.reload()" style="background: #7C3AED; color: #ffffff; border: none; border-radius: 8px; padding: 12px 24px; font-size: 16px; font-weight: 600; cursor: pointer;">
                        Try Again
                    </button>
                </div>
            `;
        }

        reloadExtension() {
            try {
                console.log('Requesting extension reload...');
                chrome.runtime.sendMessage({ action: 'reloadExtension' }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error('Error sending reload message:', chrome.runtime.lastError);
                        // Fallback: hide onboarding and let user manually reload
                        this.hideOnboarding();
                    } else {
                        console.log('Extension reload requested successfully');
                    }
                });
            } catch (error) {
                console.error('Error reloading extension:', error);
                // Fallback: hide onboarding and let user manually reload
                this.hideOnboarding();
            }
        }

        handleKeydown(event) {
            if (event.key === 'Escape') {
                // Allow ESC to close onboarding
                this.hideOnboarding();
            }
        }

        removeOnboardingOverlay() {
            const overlay = document.getElementById('gmbOnboardingOverlay');
            if (overlay) {
                overlay.remove();
            }

            // Remove event listeners
            document.removeEventListener('keydown', this.handleKeydown.bind(this));

            // Remove CSS
            const style = document.getElementById('gmbOnboardingCSS');
            if (style) {
                style.remove();
            }
        }
    }

    // Initialize content script
    const onboardingContent = new OnboardingContentScript();
    window.GMBOnboardingContent = onboardingContent;

    console.log('GMB Onboarding content script loaded');
}