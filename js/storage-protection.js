// Storage Protection Utility for SEO Time Machines
// Prevents automatic logout issues by protecting authentication data

(function() {
    'use strict';

    console.log('🛡️ STM Storage Protection: Initializing authentication data protection');

    // Define critical storage keys that should NEVER be cleared automatically
    const PROTECTED_STORAGE_KEYS = [
        // Google authentication and session data
        'google_auth', 'google_session', 'google_cookies', 'auth_tokens',
        'login_state', 'session_data', 'user_credentials', 'oauth_tokens',
        'access_token', 'refresh_token', 'id_token', 'session_token',
        
        // Browser and extension critical data
        'extension_id', 'installation_data', 'browser_session',
        'chrome_session', 'browser_state', 'extension_state',
        
        // User preferences and settings that should persist
        'user_preferences', 'language_settings', 'accessibility_settings',
        'theme_settings', 'display_preferences', 'privacy_settings',
        
        // Timer and chronometer states (active operations)
        'pomodoroTimerState', 'pomodoroAudioSettings', 'chronometerState',
        'timerState', 'alertState', 'activeTimer',
        
        // Settings and configurations
        'settings', 'profileSettings', 'quickActionSettings',
        'generalSettings', 'extrasSettings', 'audioSettings',
        
        // Developer and debugging state
        'developerModeEnabled', 'debugModeState', 'secretModeState',
        'universalDebugger_settings', 'debugState'
    ];

    // Storage protection functions
    const StorageProtection = {
        
        // Check if a key should be protected from clearing
        isProtectedKey(key) {
            if (!key || typeof key !== 'string') return false;
            
            return PROTECTED_STORAGE_KEYS.some(protectedKey => 
                key.includes(protectedKey) || 
                key.startsWith('auth') || 
                key.startsWith('login') ||
                key.startsWith('session') ||
                key.startsWith('google') ||
                key.startsWith('oauth') ||
                key.startsWith('user_') ||
                key.startsWith('browser_') ||
                key.includes('Settings') ||
                key.includes('State') ||
                key.includes('Mode') ||
                key.includes('Token') ||
                key.includes('Credentials')
            );
        },

        // Safe storage clearing that preserves authentication data
        async safeClearStorage(options = {}) {
            try {
                console.log('🛡️ STM Storage Protection: Starting safe storage clearing');
                
                // Get all current storage data
                const allData = await chrome.storage.local.get(null);
                const allKeys = Object.keys(allData);
                
                console.log(`🔍 STM Storage Protection: Found ${allKeys.length} total storage keys`);
                
                // Separate protected keys from clearable keys
                const protectedKeys = allKeys.filter(key => this.isProtectedKey(key));
                const clearableKeys = allKeys.filter(key => !this.isProtectedKey(key));
                
                console.log(`🛡️ STM Storage Protection: Protected ${protectedKeys.length} keys from clearing`);
                console.log(`🧹 STM Storage Protection: ${clearableKeys.length} keys marked for clearing`);
                
                // Log protected keys for debugging (only in developer mode)
                if (protectedKeys.length > 0) {
                    console.log('🛡️ STM Storage Protection: Protected keys:', protectedKeys);
                }
                
                // Clear only the safe-to-clear keys
                if (clearableKeys.length > 0) {
                    await chrome.storage.local.remove(clearableKeys);
                    console.log(`✅ STM Storage Protection: Successfully cleared ${clearableKeys.length} data keys`);
                } else {
                    console.log('🛡️ STM Storage Protection: No clearable keys found - all storage is protected');
                }
                
                return {
                    success: true,
                    clearedCount: clearableKeys.length,
                    protectedCount: protectedKeys.length,
                    protectedKeys: protectedKeys
                };
                
            } catch (error) {
                console.error('❌ STM Storage Protection: Error during safe clearing:', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        },

        // Monitor storage operations for debugging
        monitorStorageOperations() {
            
            // Add storage change listener
            if (chrome.storage && chrome.storage.onChanged) {
                chrome.storage.onChanged.addListener((changes, namespace) => {
                    if (namespace === 'local') {
                        const changedKeys = Object.keys(changes);
                        const protectedChanges = changedKeys.filter(key => this.isProtectedKey(key));
                        
                        if (protectedChanges.length > 0) {
                            // Check for deletions of protected keys
                            protectedChanges.forEach(key => {
                                const change = changes[key];
                                if (change.oldValue && !change.newValue) {
                                    console.error(`⚠️ STM Storage Protection: CRITICAL - Protected key "${key}" was deleted!`);
                                }
                            });
                        }
                    }
                });
            }
        },

        // Initialize protection system
        init() {
            console.log('🛡️ STM Storage Protection: System active - monitoring storage for security threats');
            
            // Start monitoring
            this.monitorStorageOperations();
            
            // Make protection functions globally available
            globalThis.STMStorageProtection = this;
        }
    };

    // Initialize protection system
    StorageProtection.init();

    // Export for use in other parts of the extension
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = StorageProtection;
    }

})();