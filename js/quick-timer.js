// Quick Timer Functionality
(function() {
    'use strict';
    
    // Timer state
    let timerState = {
        active: false,
        startTime: null,
        endTime: null,
        duration: null,
        intervalId: null,
        alarmName: null
    };
    
    // Track last badge text to prevent duplicate updates
    let lastBadgeText = '';
    
    // DOM Elements
    const quickTimerSection = document.getElementById('quickTimerSection');
    const quickTimerHeader = document.getElementById('quickTimerHeader');
    const quickTimerContent = document.getElementById('quickTimerContent');
    const quickTimerIcon = document.getElementById('quickTimerIcon');
    const headerTimerDisplay = document.getElementById('headerTimerDisplay');
    const timerCustomInput = document.getElementById('timerCustomInput');
    const customDurationInput = document.getElementById('customDurationInput');
    const timerCustomSetBtn = document.getElementById('timerCustomSetBtn');
    
    // Header timer elements
    const headerTimerHours = headerTimerDisplay.querySelector('.header-timer-hours');
    const headerTimerMinutes = headerTimerDisplay.querySelector('.header-timer-minutes');
    const headerTimerSeconds = headerTimerDisplay.querySelector('.header-timer-seconds');
    
    // Initialize timer
    async function initTimer() {
        // Check if alerts are enabled
        const result = await chrome.storage.local.get(['gmbExtractorSettings', 'quickTimerState']);
        const settings = result.gmbExtractorSettings || {};
        const savedState = result.quickTimerState;
        
        if (settings.quickTimerEnabled !== false) {
            quickTimerSection.style.display = 'block';
            
            // Restore saved timer state if exists
            if (savedState && savedState.active && savedState.endTime > Date.now()) {
                timerState = savedState;
                startCountdown();
                showHeaderTimer();
            }
        } else {
            // Hide both timer accordion and header timer when alerts disabled
            quickTimerSection.style.display = 'none';
            hideHeaderTimer();
        }
        
        // Setup event listeners
        setupEventListeners();
    }
    
    // Setup event listeners
    function setupEventListeners() {
        // Accordion toggle
        quickTimerHeader.addEventListener('click', toggleAccordion);
        
        // Preset buttons
        document.querySelectorAll('.timer-preset').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // Check if click was on the plus symbol
                if (e.target.classList.contains('timer-preset-plus')) {
                    // Handle plus button click
                    e.stopPropagation();
                    const minutes = parseFloat(e.target.dataset.minutes);
                    addTimeToTimer(minutes);
                } else {
                    // Handle main button click
                    console.log('Quick Timer: Button clicked. Timer active:', timerState.active);
                    if (!timerState.active) {
                        // Timer not running - normal behavior
                        if (btn.dataset.custom) {
                            toggleCustomInput();
                        } else {
                            const minutes = parseFloat(btn.dataset.minutes);
                            console.log('Quick Timer: Setting timer for', minutes, 'minutes');
                            setHeaderTimer(minutes);
                            hideCustomInput();
                            // Auto-start timer and collapse accordion
                            startTimer();
                            collapseAccordion();
                        }
                    } else {
                        // Timer IS running
                        if (btn.dataset.custom) {
                            // Allow CUSTOM button to open input for adding time
                            console.log('Quick Timer: CUSTOM button clicked - opening input to add time');
                            toggleCustomInput();
                        } else {
                            // Preset buttons remain blocked (use + buttons or keyboard)
                            console.log('Quick Timer: Preset button click ignored - timer already active');
                        }
                    }
                }
            });
        });
        
        // Custom duration set button
        timerCustomSetBtn.addEventListener('click', setCustomDuration);
        
        // Custom duration input enter key
        customDurationInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                setCustomDuration();
            }
        });
    }
    
    // Toggle accordion
    function toggleAccordion() {
        const isExpanded = quickTimerContent.classList.contains('expanded');
        
        if (isExpanded) {
            collapseAccordion();
        } else {
            expandAccordion();
        }
    }
    
    // Expand accordion
    function expandAccordion() {
        quickTimerContent.classList.add('expanded');
        quickTimerIcon.classList.add('expanded');
        quickTimerHeader.classList.add('expanded');
    }
    
    // Collapse accordion
    function collapseAccordion() {
        quickTimerContent.classList.remove('expanded');
        quickTimerIcon.classList.remove('expanded');
        quickTimerHeader.classList.remove('expanded');
    }
    
    // Show/hide custom input
    function toggleCustomInput() {
        const isVisible = timerCustomInput.style.display !== 'none';
        timerCustomInput.style.display = isVisible ? 'none' : 'block';
        if (!isVisible) {
            customDurationInput.focus();
        }
    }
    
    function hideCustomInput() {
        timerCustomInput.style.display = 'none';
    }
    
    // Parse custom duration input
    function parseCustomDuration(input) {
        const trimmed = input.trim().toLowerCase();
        let totalMinutes = 0;
        
        // Patterns for different time units
        const patterns = [
            { regex: /(\d+(?:\.\d+)?)\s*h(?:ours?)?/g, multiplier: 60 },
            { regex: /(\d+(?:\.\d+)?)\s*hr(?:s)?/g, multiplier: 60 },
            { regex: /(\d+(?:\.\d+)?)\s*m(?:in(?:utes?)?)?/g, multiplier: 1 },
            { regex: /(\d+(?:\.\d+)?)\s*s(?:ec(?:onds?)?)?/g, multiplier: 1/60 }
        ];
        
        let hasMatch = false;
        
        for (const pattern of patterns) {
            let match;
            while ((match = pattern.regex.exec(trimmed)) !== null) {
                totalMinutes += parseFloat(match[1]) * pattern.multiplier;
                hasMatch = true;
            }
        }
        
        // If no patterns matched, try to parse as plain number (assume minutes)
        if (!hasMatch) {
            const plainNumber = parseFloat(trimmed);
            if (!isNaN(plainNumber) && plainNumber > 0) {
                totalMinutes = plainNumber;
                hasMatch = true;
            }
        }
        
        return hasMatch ? Math.max(0.1, totalMinutes) : null;
    }
    
    // Set custom duration
    function setCustomDuration() {
        const input = customDurationInput.value;
        const minutes = parseCustomDuration(input);
        
        if (minutes === null) {
            // Flash input red for invalid input
            customDurationInput.style.borderColor = '#ef4444';
            setTimeout(() => {
                customDurationInput.style.borderColor = '#444';
            }, 1000);
            return;
        }
        
        // Check if timer is currently running
        if (timerState.active) {
            // Timer is running - add time to existing timer
            addTimeToTimer(minutes);
            hideCustomInput();
            customDurationInput.value = '';
            // Don't close accordion when timer is running - user may want to add more time
        } else {
            // Timer not running - start new timer
            setHeaderTimer(minutes);
            hideCustomInput();
            customDurationInput.value = '';
            // Auto-start timer and collapse accordion
            startTimer();
            collapseAccordion();
        }
    }
    
    // Set timer duration in header
    function setHeaderTimer(minutes) {
        const totalSeconds = Math.floor(minutes * 60);
        const hours = Math.floor(totalSeconds / 3600);
        const mins = Math.floor((totalSeconds % 3600) / 60);
        const secs = totalSeconds % 60;
        
        headerTimerHours.textContent = hours.toString().padStart(2, '0');
        headerTimerMinutes.textContent = mins.toString().padStart(2, '0');
        headerTimerSeconds.textContent = secs.toString().padStart(2, '0');
        
        timerState.duration = totalSeconds * 1000; // Convert to milliseconds
        showHeaderTimer();
    }
    
    // Add time to current timer display (cumulative)
    function addTimeToTimer(minutes) {
        if (timerState.active) {
            // Timer is running - extend the end time
            const additionalMs = minutes * 60 * 1000;
            timerState.endTime += additionalMs;
            timerState.duration += additionalMs;
            
            // Update the alarm
            if (timerState.alarmName) {
                chrome.alarms.clear(timerState.alarmName);
                chrome.runtime.sendMessage({
                    action: 'createQuickTimer',
                    alarmName: timerState.alarmName,
                    endTime: timerState.endTime,
                    duration: timerState.duration
                });
            }
            
            // Save updated state
            saveTimerState();
            
            // Update display immediately
            updateDisplay();
        } else {
            // Timer is not active - add to display
            const currentHours = parseInt(headerTimerHours.textContent) || 0;
            const currentMinutes = parseInt(headerTimerMinutes.textContent) || 0;
            const currentSeconds = parseInt(headerTimerSeconds.textContent) || 0;
            
            // Calculate current total minutes
            const currentTotalMinutes = currentHours * 60 + currentMinutes + currentSeconds / 60;
            
            // Add new minutes
            const newTotalMinutes = currentTotalMinutes + minutes;
            
            // Set the new timer value
            setHeaderTimer(newTotalMinutes);
        }
        
        // Add visual feedback animation
        const headerTimer = document.getElementById('headerTimerDisplay');
        if (headerTimer) {
            headerTimer.classList.remove('time-added');
            // Force reflow to restart animation
            headerTimer.offsetHeight;
            headerTimer.classList.add('time-added');
            
            // Remove animation class after completion
            setTimeout(() => {
                headerTimer.classList.remove('time-added');
            }, 600);
        }
    }
    
    // Show header timer display
    function showHeaderTimer() {
        headerTimerDisplay.style.display = 'block';
        headerTimerDisplay.style.cursor = 'pointer';
        headerTimerDisplay.title = 'Click to stop and clear timer';
        
        // Add click event listener to stop timer
        headerTimerDisplay.addEventListener('click', stopTimer);
    }
    
    // Hide header timer display
    function hideHeaderTimer() {
        headerTimerDisplay.style.display = 'none';
        headerTimerDisplay.style.cursor = 'default';
        headerTimerDisplay.title = '';
        
        // Remove click event listener
        headerTimerDisplay.removeEventListener('click', stopTimer);
    }
    
    
    // Start timer
    function startTimer() {
        console.log('Quick Timer: Starting timer. Current state:', timerState);
        
        const hours = parseInt(headerTimerHours.textContent) || 0;
        const minutes = parseInt(headerTimerMinutes.textContent) || 0;
        const seconds = parseInt(headerTimerSeconds.textContent) || 0;
        
        const totalSeconds = hours * 3600 + minutes * 60 + seconds;
        
        console.log('Quick Timer: Total seconds:', totalSeconds);
        
        if (totalSeconds === 0) {
            // Flash the display to indicate no time set
            headerTimerDisplay.style.color = '#ef4444';
            setTimeout(() => {
                headerTimerDisplay.style.color = '#fbbf24';
            }, 500);
            console.log('Quick Timer: Cannot start - no time set');
            return;
        }
        
        timerState.active = true;
        timerState.startTime = Date.now();
        timerState.duration = totalSeconds * 1000;
        timerState.endTime = timerState.startTime + timerState.duration;
        timerState.alarmName = 'quick-timer-' + Date.now();
        
        // Create alarm
        chrome.runtime.sendMessage({
            action: 'createQuickTimer',
            alarmName: timerState.alarmName,
            endTime: timerState.endTime,
            duration: timerState.duration
        });
        
        // Save state
        saveTimerState();
        
        // Start countdown
        startCountdown();
        
        // Show header timer
        showHeaderTimer();
    }
    
    // Stop timer
    function stopTimer() {
        // Clear interval first to stop countdown
        if (timerState.intervalId) {
            clearInterval(timerState.intervalId);
            timerState.intervalId = null;
        }
        
        // Clear alarm
        if (timerState.alarmName) {
            chrome.alarms.clear(timerState.alarmName);
        }
        
        // Reset state immediately
        timerState.active = false;
        timerState.startTime = null;
        timerState.endTime = null;
        timerState.duration = null;
        timerState.alarmName = null;
        
        // Reset display
        headerTimerHours.textContent = '00';
        headerTimerMinutes.textContent = '00';
        headerTimerSeconds.textContent = '00';
        
        // Hide header timer
        hideHeaderTimer();
        
        // Clear badge
        chrome.runtime.sendMessage({ action: 'clearTimerBadge' });
        
        // Reset badge tracking
        lastBadgeText = '';
        
        // Clear saved state
        chrome.storage.local.remove('quickTimerState');
        
        // Force final state reset to ensure clean state
        timerState = {
            active: false,
            startTime: null,
            endTime: null,
            duration: null,
            intervalId: null,
            alarmName: null
        };
        
        // Log state for debugging
        console.log('Quick Timer: Timer stopped and reset. State:', timerState);
    }
    
    // Start countdown display
    function startCountdown() {
        // Update immediately
        updateDisplay();
        
        // Update every 1000ms (1 second) for countdown - reduces console spam
        timerState.intervalId = setInterval(updateDisplay, 1000);
    }
    
    // Update countdown display
    function updateDisplay() {
        const now = Date.now();
        const remaining = Math.max(0, timerState.endTime - now);
        
        if (remaining === 0) {
            // Timer complete
            stopTimer();
            return;
        }
        
        const totalSeconds = Math.ceil(remaining / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        headerTimerHours.textContent = hours.toString().padStart(2, '0');
        headerTimerMinutes.textContent = minutes.toString().padStart(2, '0');
        headerTimerSeconds.textContent = seconds.toString().padStart(2, '0');
        
        // Update badge
        updateBadge(remaining);
    }
    
    // Update extension badge with remaining time
    function updateBadge(remaining) {
        const totalSeconds = Math.ceil(remaining / 1000);
        let badgeText = '';
        
        if (totalSeconds >= 3600) {
            // Show hours
            const hours = Math.floor(totalSeconds / 3600);
            badgeText = hours + 'h';
        } else if (totalSeconds >= 60) {
            // Show minutes
            const minutes = Math.floor(totalSeconds / 60);
            badgeText = minutes + 'm';
        } else {
            // Show seconds
            badgeText = totalSeconds + 's';
        }
        
        // Only send update if badge text has changed
        if (badgeText !== lastBadgeText) {
            lastBadgeText = badgeText;
            chrome.runtime.sendMessage({
                action: 'updateTimerBadge',
                text: badgeText,
                remaining: remaining
            });
        }
    }
    
    
    // Save timer state
    function saveTimerState() {
        chrome.storage.local.set({ quickTimerState: timerState });
    }
    
    // Listen for timer complete message from background
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'timerComplete' && message.alarmName === timerState.alarmName) {
            stopTimer();
        }
    });
    
    // Listen for settings changes to update timer visibility immediately
    chrome.storage.onChanged.addListener((changes, areaName) => {
        if (areaName === 'local' && changes.gmbExtractorSettings) {
            const newSettings = changes.gmbExtractorSettings.newValue || {};
            
            if (newSettings.quickTimerEnabled !== false) {
                quickTimerSection.style.display = 'block';
            } else {
                quickTimerSection.style.display = 'none';
                hideHeaderTimer();
                // If timer is running, stop it when quick timer is disabled
                if (timerState.active) {
                    stopTimer();
                }
            }
        }
    });
    
    // Expose functions globally for keyboard navigation integration
    window.isQuickTimerActive = function() {
        return timerState.active;
    };
    
    window.addTimeToQuickTimer = function(minutes) {
        addTimeToTimer(minutes);
    };
    
    window.getQuickTimerState = function() {
        return {
            active: timerState.active,
            remainingTime: timerState.active ? Math.max(0, timerState.endTime - Date.now()) : 0
        };
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTimer);
    } else {
        initTimer();
    }
    
})();