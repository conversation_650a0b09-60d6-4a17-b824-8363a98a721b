// Background script starting up
console.log('🚀 STM BACKGROUND SCRIPT LOADING - VERSION 2.0 🚀');
console.log('📅 Background script loaded at:', new Date().toISOString());

// Global URL patterns for context menus
let universalUrlPatterns = [
  "*://*/*",
  "https://*.google.com/*",
  "https://google.com/*", 
  "https://maps.google.com/*",
  "https://maps.google.co.uk/*", 
  "https://maps.google.ca/*", 
  "https://maps.google.com.au/*",
  "https://*.google.co.uk/*",
  "https://*.google.ca/*", 
  "https://*.google.com.au/*"
];

// SERVICE WORKER LIFECYCLE MONITORING SYSTEM
class ServiceWorkerLifecycleManager {
    constructor() {
        this.isActive = true;
        this.lastActivity = Date.now();
        this.activeOperations = new Set();
        this.contextValidationInterval = null;
        this.keepAliveInterval = null;
        
        this.initializeLifecycleMonitoring();
    }
    
    initializeLifecycleMonitoring() {
        // Monitor service worker lifecycle events
        self.addEventListener('activate', () => {
            console.log('🔄 Service Worker: Activated');
            this.isActive = true;
            this.lastActivity = Date.now();
        });
        
        self.addEventListener('install', () => {
            console.log('⚡ Service Worker: Installed');
            self.skipWaiting();
        });
        
        // Context validation check every 30 seconds
        this.contextValidationInterval = setInterval(() => {
            this.validateContext();
        }, 30000);
        
        // Keepalive mechanism - ping every 25 seconds to prevent termination
        this.keepAliveInterval = setInterval(() => {
            if (this.activeOperations.size > 0) {
                this.updateActivity();
                console.log('💓 Service Worker: Keepalive ping (active operations:', this.activeOperations.size + ')');
            }
        }, 25000);
        
        console.log('🔍 Service Worker Lifecycle Manager: Initialized');
    }
    
    validateContext() {
        try {
            // Test basic Chrome API availability
            if (!chrome || !chrome.storage || !chrome.runtime) {
                this.handleContextInvalidation('Basic Chrome APIs unavailable');
                return false;
            }
            
            // Test storage access
            chrome.storage.local.get(['_contextTest'], (result) => {
                if (chrome.runtime.lastError) {
                    this.handleContextInvalidation('Storage API test failed: ' + chrome.runtime.lastError.message);
                    return false;
                }
                
                this.updateActivity();
                return true;
            });
            
        } catch (error) {
            this.handleContextInvalidation('Context validation error: ' + error.message);
            return false;
        }
    }
    
    updateActivity() {
        this.lastActivity = Date.now();
    }
    
    addOperation(operationId) {
        this.activeOperations.add(operationId);
        this.updateActivity();
        
        // Auto-remove operation after 5 minutes to prevent memory leaks
        setTimeout(() => {
            this.activeOperations.delete(operationId);
        }, 300000);
    }
    
    removeOperation(operationId) {
        this.activeOperations.delete(operationId);
    }
    
    handleContextInvalidation(reason) {
        console.warn('⚠️ Service Worker: Context invalidation detected -', reason);
        this.isActive = false;
        
        // Clear intervals to prevent further errors
        if (this.contextValidationInterval) {
            clearInterval(this.contextValidationInterval);
            this.contextValidationInterval = null;
        }
        
        if (this.keepAliveInterval) {
            clearInterval(this.keepAliveInterval);
            this.keepAliveInterval = null;
        }
        
        // Clear active operations
        this.activeOperations.clear();
    }
    
    getStatus() {
        return {
            isActive: this.isActive,
            lastActivity: this.lastActivity,
            activeOperations: this.activeOperations.size,
            uptime: Date.now() - this.lastActivity
        };
    }
}

// Initialize lifecycle manager
const lifecycleManager = new ServiceWorkerLifecycleManager();

// SAFE CHROME API WRAPPERS WITH LIFECYCLE TRACKING
const safeStorage = {
    async get(keys) {
        const operationId = `storage_get_${Date.now()}_${Math.random()}`;
        lifecycleManager.addOperation(operationId);
        
        try {
            if (!lifecycleManager.isActive) {
                throw new Error('Service worker context invalidated');
            }
            
            const result = await chrome.storage.local.get(keys);
            lifecycleManager.updateActivity();
            return result;
        } catch (error) {
            if (error.message.includes('Extension context invalidated')) {
                lifecycleManager.handleContextInvalidation('Storage get operation failed');
            }
            throw error;
        } finally {
            lifecycleManager.removeOperation(operationId);
        }
    },
    
    async set(items) {
        const operationId = `storage_set_${Date.now()}_${Math.random()}`;
        lifecycleManager.addOperation(operationId);
        
        try {
            if (!lifecycleManager.isActive) {
                throw new Error('Service worker context invalidated');
            }
            
            await chrome.storage.local.set(items);
            lifecycleManager.updateActivity();
        } catch (error) {
            if (error.message.includes('Extension context invalidated')) {
                lifecycleManager.handleContextInvalidation('Storage set operation failed');
            }
            throw error;
        } finally {
            lifecycleManager.removeOperation(operationId);
        }
    }
};

const safeMessaging = {
    async sendMessage(tabId, message) {
        const operationId = `message_send_${Date.now()}_${Math.random()}`;
        lifecycleManager.addOperation(operationId);
        
        try {
            if (!lifecycleManager.isActive) {
                return null;
            }
            
            const response = await chrome.tabs.sendMessage(tabId, message);
            lifecycleManager.updateActivity();
            return response;
        } catch (error) {
            if (error.message.includes('Extension context invalidated')) {
                lifecycleManager.handleContextInvalidation('Message send operation failed');
            }
            return null;
        } finally {
            lifecycleManager.removeOperation(operationId);
        }
    }
};

// CRITICAL: Import storage protection FIRST to prevent authentication data loss
importScripts('storage-protection.js');

// Import logout debug monitor to track what's causing automatic logouts
importScripts('logout-debug-monitor.js');

// Import logging utility for debug mode control
importScripts('../settings/logging-utility.js');

// Import centralized badge manager
importScripts('centralized-badge-manager.js');

// Import blocking system functions
let updateBlockingRules, removeAllBlockingRules, BlockingSystem;

// Location Changer Settings
const options = {
  consent: '',
  contextnumber: 3,
  maxplaces: 100
};

const settings = {
  'latitude': 37.422388,
  'longitude': -122.0841883,
  'location': "Google Building 40, Amphitheatre Parkway, Mountain View, CA, USA",
  'name': "Google Building 40",
  'placeId': "ChIJj38IfwK6j4ARNcyPDnEGa9g",
  'enabled': false,
  'hl': 'en',
  'gl': 'US',
  'regions': 'United States - English'
};

// Initialize extension
// Removed validateAndRepairSettings - using simple approach from working commit

const knownPlaces = [settings];

// Function to add a location to knownPlaces with deduplication
// Removed addLocationToKnownPlaces function - replaced with simple Object.assign pattern

// Track which tabs are citation search tabs and their actual URLs
const citationTabs = new Map();


// SETTINGS POPUP LOCKDOWN SYSTEM
// Complete protection for pomodoro timer values when settings popup is active
let settingsPopupActive = false;
let settingsPopupWindowId = null;
let timerValueBackup = null;
let lockdownTimeout = null;
let isRestoringFromBackup = false; // Flag to prevent timer restart during backup restoration
let restorationTimestamp = 0; // Timestamp for double protection against restoration-triggered events

// Helper functions for settings popup lockdown
const activateSettingsLockdown = (windowId = null) => {
    settingsPopupActive = true;
    settingsPopupWindowId = windowId;
    
    // Clear any existing timeout
    if (lockdownTimeout) {
        clearTimeout(lockdownTimeout);
    }
    
    // Auto-deactivate after 30 seconds as safety measure
    lockdownTimeout = setTimeout(() => {
        deactivateSettingsLockdown();
        console.log('⏰ STM: Settings lockdown auto-deactivated after timeout');
    }, 30000);
    
    console.log(`🔒 STM: Settings popup lockdown ACTIVATED - Timer values completely protected`);
    console.log(`🔒 STM: Settings window ID: ${windowId}`);
};

const deactivateSettingsLockdown = () => {
    settingsPopupActive = false;
    settingsPopupWindowId = null;
    
    // Clear timeout
    if (lockdownTimeout) {
        clearTimeout(lockdownTimeout);
        lockdownTimeout = null;
    }
    
    console.log(`🔓 STM: Settings popup lockdown DEACTIVATED - Timer values unprotected`);
};

const isSettingsLockdownActive = () => {
    return settingsPopupActive;
};

const backupTimerValues = async () => {
    try {
        const result = await safeStorage.get(['gmbExtractorSettings']);
        const settings = result.gmbExtractorSettings || {};
        
        timerValueBackup = {
            pomodoroWorkDuration: settings.pomodoroWorkDuration,
            pomodoroShortBreak: settings.pomodoroShortBreak,
            pomodoroLongBreak: settings.pomodoroLongBreak,
            pomodoroNumberOfCycles: settings.pomodoroNumberOfCycles,
            timestamp: Date.now()
        };
        
        console.log('💾 STM: Timer values backed up:', timerValueBackup);
    } catch (error) {
        console.error('❌ STM: Error backing up timer values:', error);
    }
};

const restoreTimerValues = async () => {
    if (!timerValueBackup) {
        console.log('⚠️ STM: No timer backup available for restore');
        return;
    }
    
    try {
        const result = await safeStorage.get(['gmbExtractorSettings']);
        const settings = result.gmbExtractorSettings || {};
        
        // Check if restoration is actually needed - only restore if values actually changed during lockdown
        const timerValuesChanged = 
            settings.pomodoroWorkDuration != timerValueBackup.pomodoroWorkDuration ||
            settings.pomodoroShortBreak != timerValueBackup.pomodoroShortBreak ||
            settings.pomodoroLongBreak != timerValueBackup.pomodoroLongBreak ||
            settings.pomodoroNumberOfCycles != timerValueBackup.pomodoroNumberOfCycles;
            
        if (!timerValuesChanged) {
            console.log('🔄 STM: Timer values unchanged during lockdown - no restoration needed');
            timerValueBackup = null;
            return;
        }
        
        console.log('🔄 STM: Timer values changed during lockdown - restoring from backup');
        console.log('🔄 STM: Current values:', {
            workDuration: settings.pomodoroWorkDuration,
            shortBreak: settings.pomodoroShortBreak,
            longBreak: settings.pomodoroLongBreak,
            cycles: settings.pomodoroNumberOfCycles
        });
        console.log('🔄 STM: Backup values:', timerValueBackup);
        
        // Set flag and timestamp to prevent timer restart during restoration
        isRestoringFromBackup = true;
        restorationTimestamp = Date.now();
        
        try {
            // Restore timer values from backup
            settings.pomodoroWorkDuration = timerValueBackup.pomodoroWorkDuration;
            settings.pomodoroShortBreak = timerValueBackup.pomodoroShortBreak;
            settings.pomodoroLongBreak = timerValueBackup.pomodoroLongBreak;
            settings.pomodoroNumberOfCycles = timerValueBackup.pomodoroNumberOfCycles;
            
            await safeStorage.set({ gmbExtractorSettings: settings });
            
            console.log('🔄 STM: Timer values restored silently from backup:', timerValueBackup);
            console.log('🔄 STM: Restoration timestamp set for double protection:', restorationTimestamp);
            timerValueBackup = null;
            
            // Keep restoration flag active for 1500ms to catch async storage events
            setTimeout(() => {
                isRestoringFromBackup = false;
                console.log('🔄 STM: Restoration protection period ended');
            }, 1500);
            
        } catch (error) {
            // Clear flag immediately on error
            isRestoringFromBackup = false;
            throw error;
        }
    } catch (error) {
        console.error('❌ STM: Error restoring timer values:', error);
        isRestoringFromBackup = false;
    }
};

// Helper function to capture full screenshot
function captureFullScreenshot() {
    console.log('📸 Background: captureFullScreenshot() called, initiating chrome.tabs.captureVisibleTab...');
    return new Promise((resolve) => {
        chrome.tabs.captureVisibleTab(null, { format: 'png' }, (dataUrl) => {
            if (chrome.runtime.lastError) {
                console.error('📸 Background: chrome.tabs.captureVisibleTab failed:', {
                    error: chrome.runtime.lastError.message,
                    timestamp: new Date().toISOString()
                });
                resolve(null);
            } else {
                console.log('📸 Background: chrome.tabs.captureVisibleTab succeeded:', {
                    dataUrlLength: dataUrl ? dataUrl.length : 0,
                    dataUrlPrefix: dataUrl ? dataUrl.substring(0, 50) + '...' : 'null',
                    timestamp: new Date().toISOString()
                });
                resolve(dataUrl);
            }
        });
    });
}

// Helper function to crop image to selected area
function cropImageToArea(imageDataUrl, selection) {
    return new Promise((resolve, reject) => {
        console.log('Background: Cropping image to area:', selection);
        
        const img = new Image();
        img.onload = () => {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // Set canvas size to selected area
                canvas.width = selection.width;
                canvas.height = selection.height;
                
                // Draw the cropped portion
                ctx.drawImage(
                    img,
                    selection.left,    // Source x
                    selection.top,     // Source y
                    selection.width,   // Source width
                    selection.height,  // Source height
                    0,                 // Destination x
                    0,                 // Destination y
                    selection.width,   // Destination width
                    selection.height   // Destination height
                );
                
                const croppedDataUrl = canvas.toDataURL('image/png');
                console.log('Background: Image cropped successfully');
                resolve(croppedDataUrl);
                
            } catch (error) {
                console.error('Background: Error cropping image:', error);
                reject(error);
            }
        };
        
        img.onerror = () => {
            console.error('Background: Failed to load image for cropping');
            reject(new Error('Failed to load image'));
        };
        
        img.src = imageDataUrl;
    });
}

// HANDY SCREENSHOT APPROACH: Store full screenshot + selection data (let editor handle cropping)
// This avoids DOM API issues in service worker environment



// HANDY SCREENSHOT APPROACH: Handle area selection capture
async function handleCaptureSelection(message) {
    console.log('📸 Background: handleCaptureSelection() started with message:', {
        rect: message.rect,
        devicePixelRatio: message.devicePixelRatio,
        timestamp: new Date().toISOString()
    });
    
    try {
        // Additional delay to ensure DOM changes are fully processed
        // This serves as backup protection for overlay cleanup timing
        console.log('📸 Background: Applying 50ms delay for DOM cleanup...');
        await new Promise(resolve => setTimeout(resolve, 50));
        console.log('📸 Background: Delay complete, proceeding with screenshot capture');
        
        // Capture full screenshot
        console.log('📸 Background: Calling captureFullScreenshot()...');
        const fullScreenshotData = await captureFullScreenshot();
        
        if (!fullScreenshotData) {
            console.error('📸 Background: Screenshot capture returned null/empty data');
            return { success: false, error: 'Failed to capture screenshot' };
        }
        
        console.log('📸 Background: Screenshot captured successfully, data length:', fullScreenshotData.length);
        
        // Store full screenshot + selection data for editor to handle cropping
        console.log('📸 Background: Storing screenshot data and selection rect to chrome.storage.local...');
        const storageData = {
            screenshotData: fullScreenshotData,
            selectionRect: message.rect,
            devicePixelRatio: message.devicePixelRatio,
            screenshotTimestamp: Date.now()
        };
        
        // Use safe storage wrapper for context stability
        if (typeof safeStorageSet === 'function') {
            const success = await safeStorageSet(storageData);
            if (!success) {
                console.error('📸 Background: Safe storage operation failed - extension context may be invalidated');
                return { success: false, error: 'Storage operation failed - extension context invalidated' };
            }
            console.log('📸 Background: Safe storage operation completed successfully');
        } else {
            // Fallback to direct API with context validation
            try {
                await chrome.storage.local.set(storageData);
                console.log('📸 Background: Direct storage operation completed successfully');
            } catch (error) {
                if (error.message && error.message.includes('Extension context invalidated')) {
                    console.error('📸 Background: Extension context invalidated during storage operation');
                    return { success: false, error: 'Extension context invalidated during storage operation' };
                }
                throw error; // Re-throw other errors
            }
        }
        
        // Open screenshot editor with context validation
        console.log('📸 Background: Creating new tab for screenshot-editor.html...');
        const editorUrl = chrome.runtime.getURL('screenshot-editor.html');
        console.log('📸 Background: Editor URL:', editorUrl);
        
        // Validate context before tab creation
        try {
            // Test if chrome.tabs API is available
            if (!chrome || !chrome.tabs || !chrome.tabs.create) {
                console.error('📸 Background: Chrome tabs API not available - extension context may be invalidated');
                return { success: false, error: 'Chrome tabs API not available - extension context invalidated' };
            }
            
            // Additional context validation
            chrome.runtime.getManifest(); // This will throw if context is invalid
            
            const editorTab = await chrome.tabs.create({
                url: editorUrl,
                active: true
            });
            
            console.log('📸 Background: Screenshot editor tab created successfully:', {
                tabId: editorTab.id,
                url: editorTab.url,
                active: editorTab.active
            });
            
            // Validate the created tab
            if (!editorTab || !editorTab.id) {
                console.error('📸 Background: Tab creation succeeded but returned invalid tab object');
                return { success: false, error: 'Invalid tab object returned from chrome.tabs.create' };
            }
        } catch (error) {
            if (error.message && error.message.includes('Extension context invalidated')) {
                console.error('📸 Background: Extension context invalidated during tab creation');
                return { success: false, error: 'Extension context invalidated during tab creation' };
            }
            console.error('📸 Background: Unexpected error during tab creation:', error);
            return { success: false, error: 'Tab creation failed: ' + error.message };
        }
        
        const successResult = { success: true, tabId: editorTab.id };
        console.log('📸 Background: handleCaptureSelection() completed successfully:', successResult);
        return successResult;
        
    } catch (error) {
        console.error('📸 Background: Screenshot capture error in handleCaptureSelection():', error);
        console.error('📸 Background: Error stack:', error.stack);
        const errorResult = { success: false, error: error.message };
        console.log('📸 Background: handleCaptureSelection() returning error result:', errorResult);
        return errorResult;
    }
}

// Initialize extension
function initializeExtension() {
  console.log('Initializing extension...');
  chrome.storage.local.get(null, (data) => {
    console.log('Storage data loaded:', data);
    if (data.settings) {
      Object.assign(settings, data.settings);
      checkEnabled();
    }
    if (data.options) {
      Object.assign(options, data.options);
    }
    if (data.knownPlaces) {
      Object.assign(knownPlaces, data.knownPlaces);
    }
    // Always setup context menu once after loading all data
    setupContextMenu(knownPlaces);
  });
}

// Restore Pomodoro timer state from storage on extension restart
async function restoreTimerFromStorage() {
  try {
    console.log('🔄 POMODORO: Attempting to restore timer state from storage...');
    
    const result = await chrome.storage.local.get(['pomodoroTimerState']);
    const savedState = result.pomodoroTimerState;
    
    if (!savedState || savedState.currentState === 'idle') {
      console.log('🔄 POMODORO: No active timer to restore');
      return false;
    }
    
    // Check if the saved state is recent (within last 2 minutes)
    const now = Date.now();
    const timeSinceLastUpdate = now - (savedState.lastUpdate || 0);
    const maxRestoreTime = 2 * 60 * 1000; // 2 minutes
    
    if (timeSinceLastUpdate > maxRestoreTime) {
      console.log('🔄 POMODORO: Saved state too old, not restoring');
      // Clear stale state
      await chrome.storage.local.set({
        pomodoroTimerState: {
          currentState: 'idle',
          timeRemaining: 0,
          isPaused: false,
          totalCycles: savedState.totalCycles || 'unlimited',
          currentCycle: 0,
          currentMode: savedState.currentMode || 'standard',
          lastUpdate: now
        }
      });
      return false;
    }
    
    // Calculate actual time remaining based on elapsed time
    const elapsedSeconds = Math.floor(timeSinceLastUpdate / 1000);
    let adjustedTimeRemaining = savedState.timeRemaining;
    
    if (!savedState.isPaused) {
      adjustedTimeRemaining = Math.max(0, savedState.timeRemaining - elapsedSeconds);
    }
    
    if (adjustedTimeRemaining <= 0) {
      console.log('🔄 POMODORO: Timer would have completed, not restoring');
      return false;
    }
    
    console.log('🔄 POMODORO: Restoring timer state:', {
      state: savedState.currentState,
      originalTime: savedState.timeRemaining,
      adjustedTime: adjustedTimeRemaining,
      elapsedSeconds: elapsedSeconds,
      isPaused: savedState.isPaused
    });
    
    // Wait for pomodoroTimer to be initialized
    if (!pomodoroTimer) {
      console.log('🔄 POMODORO: Waiting for timer initialization...');
      return false;
    }
    
    // Restore timer state
    pomodoroTimer.currentState = savedState.currentState;
    pomodoroTimer.timeRemaining = adjustedTimeRemaining;
    pomodoroTimer.isPaused = savedState.isPaused;
    pomodoroTimer.totalCycles = savedState.totalCycles || 'unlimited';
    pomodoroTimer.currentCycle = savedState.currentCycle || 0;
    pomodoroTimer.currentMode = savedState.currentMode || 'standard';
    pomodoroTimer.sessionCount = savedState.sessionCount || 0;
    
    // Clear any existing interval first
    if (pomodoroTimer.interval) {
      clearInterval(pomodoroTimer.interval);
      pomodoroTimer.interval = null;
    }
    
    // Only restart countdown if not paused
    if (!savedState.isPaused) {
      console.log('🔄 POMODORO: Restarting countdown from', adjustedTimeRemaining, 'seconds');
      
      // Use centralized countdown method
      pomodoroTimer.startCountdown();
      
      // Restore chronometer if it was active
      const isBreakPeriod = savedState.currentState === 'short_break' || savedState.currentState === 'long_break';
      await pomodoroTimer.startChronometer(isBreakPeriod, 'state_restore');
      
      // Restore website blocking if in work session
      if (savedState.currentState === 'work' && typeof updateBlockingRules === 'function') {
        updateBlockingRules().catch(err => console.error('Failed to restore blocking:', err));
      }
    }
    
    // Update badge to show current state
    if (pomodoroBadgeManager) {
      pomodoroBadgeManager.updateBadge(adjustedTimeRemaining, savedState.currentState, savedState.isPaused);
    }
    
    console.log('✅ POMODORO: Timer state successfully restored');
    return true;
    
  } catch (error) {
    console.error('❌ POMODORO: Error restoring timer state:', error);
    return false;
  }
}

// Initialize location changer on startup and install
chrome.runtime.onStartup.addListener(() => {
  initializeExtension();
  
  // CRITICAL FIX: Add startup storage monitoring for authentication protection
  console.log('🛡️ STM: Startup initiated - monitoring storage for authentication protection');
  
  // Also initialize Pomodoro to restore timer state
  initializePomodoroComponents().then(async () => {
    // Attempt to restore timer state
    await restoreTimerFromStorage();
    
    // Clear any leftover blocking rules from previous session only if no timer was restored
    const result = await chrome.storage.local.get(['pomodoroTimerState']);
    const currentState = result.pomodoroTimerState;
    
    if (!currentState || currentState.currentState === 'idle') {
      if (typeof removeAllBlockingRules === 'function') {
        removeAllBlockingRules().catch(err => console.log('No blocking rules to clear on startup'));
      }
    }
    
    // STM: SAFE chronometer state reset - only update specific chronometer data
    if (!currentState || currentState.currentState === 'idle') {
      try {
        console.log('🧹 STM: Safely resetting chronometer state on startup (preserving all other data)');
        
        // CRITICAL: Only update chronometer-specific state, never clear other storage
        await chrome.storage.local.set({
          chronometerState: {
            isActive: false,
            ownerId: null,
            lastHeartbeat: 0,
            frequency: 2,
            isBreakPeriod: false
          }
        });
        
        console.log('✅ STM: Chronometer state safely reset without affecting authentication data');
      } catch (error) {
        console.error('❌ STM: Error resetting chronometer state on startup:', error);
      }
    }
    
    // Add storage monitoring for debugging logout issues
    console.log('🔍 STM: Startup complete - storage operations monitored for authentication protection');
  });
});
chrome.runtime.onInstalled.addListener((details) => {
  console.log('Extension installed/updated, reason:', details.reason);
  initializeExtension();
  // Initialize Pomodoro on install/update
  initializePomodoroComponents().then(async () => {
    // Only attempt restoration on updates, not fresh installs
    if (details.reason === 'update') {
      await restoreTimerFromStorage();
    }
  });
  
  // Initialize default site blocking lists like Pomodoro Grande
  chrome.storage.local.get(['blockedSites', 'allowedUrls'], (result) => {
    if (!result.blockedSites) {
      const defaultBlockedSites = ['facebook.com', 'twitter.com', 'instagram.com', 'youtube.com', 'reddit.com', 'tiktok.com'];
      chrome.storage.local.set({ blockedSites: defaultBlockedSites });
      console.log('Pomodoro: Initialized default blocked sites:', defaultBlockedSites);
    }
    
    if (!result.allowedUrls) {
      const defaultAllowedUrls = ['google.com', 'maps.google.com', 'github.com'];
      chrome.storage.local.set({ allowedUrls: defaultAllowedUrls });
      console.log('Pomodoro: Initialized default allowed URLs:', defaultAllowedUrls);
    }
  });
});

// SETTINGS POPUP WINDOW TRACKING
// Track when settings popup opens and closes to activate/deactivate lockdown
chrome.windows.onCreated.addListener(async (window) => {
    try {
        // Check if this is the settings popup window
        const tabs = await chrome.tabs.query({ windowId: window.id });
        if (tabs.length > 0) {
            const tab = tabs[0];
            const settingsUrl = chrome.runtime.getURL('settings/settings.html');
            
            if (tab.url === settingsUrl) {
                console.log('🔒 STM: Settings popup window detected, activating lockdown');
                
                // Backup current timer values before activating lockdown
                await backupTimerValues();
                
                // Activate lockdown
                activateSettingsLockdown(window.id);
                
                // Send message to any listening content scripts
                try {
                    await chrome.runtime.sendMessage({
                        action: 'settingsLockdownActivated',
                        windowId: window.id
                    });
                } catch (error) {
                    // Message sending may fail if no listeners, which is okay
                }
            }
        }
    } catch (error) {
        console.error('❌ STM: Error in window creation handler:', error);
    }
});

chrome.windows.onRemoved.addListener(async (windowId) => {
    try {
        // Check if this was the settings popup window
        if (settingsPopupWindowId === windowId) {
            console.log('🔓 STM: Settings popup window closed, deactivating lockdown');
            
            // Deactivate lockdown
            deactivateSettingsLockdown();
            
            // Restore timer values if backup exists
            await restoreTimerValues();
            
            // Send message to any listening content scripts
            try {
                await chrome.runtime.sendMessage({
                    action: 'settingsLockdownDeactivated',
                    windowId: windowId
                });
            } catch (error) {
                // Message sending may fail if no listeners, which is okay
            }
        }
    } catch (error) {
        console.error('❌ STM: Error in window removal handler:', error);
    }
});

// Pomodoro Timer System
let pomodoroTimer = null;
let pomodoroBlockingSystem = null;
let pomodoroBadgeManager = null;

// No longer needed - using DOM popups instead of chrome windows

// STM Audio System - Simple and Direct
let pomodoroAudioSettings = {
    notificationVolume: 0.7,
    chronometerVolume: 0.3,
    workCompletedSound: 'Bell Meditation',
    endBreakSound: 'Celestial Gong', 
    tickingSound: 'Clock Ticking 1',
    chronometerEnabled: true,
    chronometerOnBreak: false
};

// STM: Simple audio URL mapping like sound-preview.js
const getSTMAudioUrl = (soundName, isTickingSound = false) => {
    const soundUrls = {
        // Notification sounds
        'Bell Meditation': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Bell+Meditation.mp3',
        'Celestial Gong': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Celestial+Gong.mp3',
        'Deep Meditation Bell Crown Chakra': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Deep+Meditation+Bell+Crown+Chakra.mp3',
        'Deep Meditation Bell Heart Chakra': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Deep+Meditation+Bell+Heart+Chakra.mp3',
        'Funky': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Funky.mp3',
        'Mechanical': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Mechanical.wav',
        'Clock': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/clock.mp3',
        'Clock 2': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/clock2.mp3',
        'Clock 3': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/clock3.mp3',
        
        // Ticking sounds (local files)
        'Clock Ticking 1': chrome.runtime.getURL('sounds/clock-ticking-1.wav'),
        'Clock Ticking 2': chrome.runtime.getURL('sounds/clock-ticking-2.mp3'),
        'Clock Ticking 3': chrome.runtime.getURL('sounds/clock-ticking-3.mp3')
    };
    
    return soundUrls[soundName] || null;
};

// STM: Notification sound durations in milliseconds (exact timing)
const NOTIFICATION_SOUND_DURATIONS = {
    'Bell Meditation': 17000,
    'Celestial Gong': 5000,
    'Deep Meditation Bell Crown Chakra': 16000,
    'Deep Meditation Bell Heart Chakra': 16000,
    'Funky': 7000,
    'Mechanical': 2000,
    'Notification 1': 3000,
    'Notification 2': 2000,
    'Notification 3': 2000,
    'Notification 4': 2000,
    'Old Church Bell 2': 11000,
    'Old Church Bell 3': 5000
};

// STM: Get exact duration for notification sound
const getNotificationSoundDuration = (soundName) => {
    return NOTIFICATION_SOUND_DURATIONS[soundName] || 5000; // Default fallback
};

// Debounced logging for health check failures
let lastHealthCheckFailureLog = 0;
const HEALTH_CHECK_LOG_INTERVAL = 60000; // Only log health check failures once per minute

// Throttled logging for connection issues
let connectionIssueCount = 0;
let lastConnectionIssueLog = 0;
const CONNECTION_LOG_INTERVAL = 30000; // Only log connection issues summary every 30 seconds

// Smart restart logging
let lastRestartCheckLog = 0;
const RESTART_CHECK_LOG_INTERVAL = 60000; // Only log restart check details every minute

// STM: Enhanced offscreen document management with connection health checks
const ensureSTMOffscreenDocument = async (forceRecreate = false) => {
    try {
        // Check if offscreen API is supported
        if (!chrome.offscreen || !chrome.offscreen.createDocument) {
            console.warn('STM: Offscreen API not supported');
            return false;
        }

        // Check if offscreen document already exists
        const hasDocument = await chrome.offscreen.hasDocument();
        
        // Force recreation if requested or if document doesn't exist
        if (forceRecreate && hasDocument) {
            console.log('STM: Force recreating offscreen document...');
            try {
                await chrome.offscreen.closeDocument();
                // Wait for cleanup
                await new Promise(resolve => setTimeout(resolve, 200));
            } catch (error) {
                console.warn('STM: Error closing existing offscreen document:', error.message);
            }
        }
        
        // Create document if it doesn't exist or was forced to recreate
        if (!hasDocument || forceRecreate) {
            console.log('STM: Creating offscreen document for audio...');
            await chrome.offscreen.createDocument({
                url: chrome.runtime.getURL('offscreen.html'),
                reasons: ['AUDIO_PLAYBACK'],
                justification: 'Pomodoro timer needs to play notification sounds and chronometer ticking'
            });
            console.log('STM: Offscreen document created successfully');
            
            // Wait for document to fully initialize
            await new Promise(resolve => setTimeout(resolve, 300));
        }
        
        // Health check: Try to send a ping message to verify the connection
        try {
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => reject(new Error('Health check timeout')), 2000);
                
                chrome.runtime.sendMessage({ action: 'ping' }, (response) => {
                    clearTimeout(timeout);
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
            
            console.log('✅ STM: Offscreen document connection verified');
        } catch (healthError) {
            // Debounced logging for health check failures to reduce console spam
            const now = Date.now();
            if (now - lastHealthCheckFailureLog > HEALTH_CHECK_LOG_INTERVAL) {
                console.warn('⚠️ STM: Offscreen document health check failed (will retry silently):', healthError.message);
                lastHealthCheckFailureLog = now;
            }
            // Don't fail completely, allow the chronometer to try anyway
        }
        
        return true;
    } catch (error) {
        console.error('STM: Error ensuring offscreen document:', error);
        return false;
    }
};

// STM: Load audio settings from local storage (STM pattern)
const loadSTMAudioSettings = async () => {
    try {
        console.log('🔧 STM Background: Loading audio settings from local storage...');
        const result = await chrome.storage.local.get(['pomodoroAudioSettings']);
        let audioSettings = result.pomodoroAudioSettings || {};
        
        // Migration: If no local audio settings, try to migrate from sync storage
        if (Object.keys(audioSettings).length === 0) {
            console.log('🔄 STM: Migrating audio settings from sync to local storage...');
            const syncResult = await chrome.storage.local.get(['gmbExtractorSettings']);
            const syncSettings = syncResult.gmbExtractorSettings || {};
            
            if (syncSettings.pomodoroWorkCompletedSound || syncSettings.pomodoroNotificationVolume) {
                audioSettings = {
                    notificationVolume: syncSettings.pomodoroNotificationVolume || 70,
                    chronometerVolume: syncSettings.pomodoroChronometerVolume || 30,
                    workCompletedSound: syncSettings.pomodoroWorkCompletedSound || 'Bell Meditation',
                    endBreakSound: syncSettings.pomodoroEndBreakSound || 'Celestial Gong',
                    tickingSound: syncSettings.pomodoroTickingSound || 'Clock Ticking 1'
                };
                
                // Save migrated settings to local storage
                await chrome.storage.local.set({ pomodoroAudioSettings: audioSettings });
                console.log('✅ STM: Audio settings migrated successfully');
            }
        }
        
        console.log('📋 STM Background: Raw audio settings from storage:', audioSettings);
        
        // Map from local storage format to STM format
        pomodoroAudioSettings.notificationVolume = (audioSettings.notificationVolume || 70) / 100;
        pomodoroAudioSettings.chronometerVolume = (audioSettings.chronometerVolume || 30) / 100;
        pomodoroAudioSettings.workCompletedSound = audioSettings.workCompletedSound || 'Bell Meditation';
        pomodoroAudioSettings.endBreakSound = audioSettings.endBreakSound || 'Celestial Gong';
        pomodoroAudioSettings.tickingSound = audioSettings.tickingSound || 'Clock Ticking 1';
        
        // Get chronometer toggles from local storage (now stored there to prevent conflicts)
        pomodoroAudioSettings.chronometerEnabled = audioSettings.chronometerEnabled !== false;
        pomodoroAudioSettings.chronometerOnBreak = audioSettings.chronometerOnBreak === true;
        
        // Also check sync storage for chronometerOnBreak fallback (for migration)
        try {
            const syncResult = await chrome.storage.local.get(['gmbExtractorSettings']);
            const syncSettings = syncResult.gmbExtractorSettings || {};
            if (audioSettings.chronometerOnBreak === undefined && syncSettings.pomodoroChronometerOnBreak !== undefined) {
                pomodoroAudioSettings.chronometerOnBreak = syncSettings.pomodoroChronometerOnBreak === true;
            }
        } catch (error) {
            console.warn('STM: Could not check sync storage for chronometerOnBreak:', error);
        }
        
        console.log('✅ STM Background: Audio settings processed:', pomodoroAudioSettings);
    } catch (error) {
        console.error('❌ STM Background: Error loading audio settings:', error);
    }
};

// REMOVED: sendToOffscreenDocument - all audio now uses direct STM messaging

// Debounce timer for settings changes
let settingsChangeTimeout = null;

// Helper function to detect if a specific setting actually changed
const hasSettingChanged = (changes, settingName) => {
    const change = changes.gmbExtractorSettings;
    if (!change || !change.oldValue || !change.newValue) {
        console.log(`🔍 STM: No valid change data for setting "${settingName}" - skipping`);
        return false;
    }
    
    const oldValue = change.oldValue[settingName];
    const newValue = change.newValue[settingName];
    
    // Handle undefined/null cases properly to prevent false positives
    if (oldValue === undefined && newValue === undefined) {
        return false; // Both undefined, no change
    }
    
    if (oldValue === undefined && newValue !== undefined) {
        // IMPROVED: More conservative detection for initialization vs real changes
        // Only treat as change if it's not a common default value or reasonable user value
        const isLikelyDefaultValue = (settingName === 'pomodoroWorkDuration' && (newValue === 25 || newValue === 20 || newValue === 30)) ||
                                   (settingName === 'pomodoroShortBreak' && (newValue === 5 || newValue === 3 || newValue === 10)) ||
                                   (settingName === 'pomodoroLongBreak' && (newValue === 15 || newValue === 10 || newValue === 20)) ||
                                   (settingName === 'pomodoroNumberOfCycles' && (newValue === 8 || newValue === 'unlimited' || newValue === 4)) ||
                                   (settingName === 'pomodoroChronometerSound' && (newValue === true || newValue === false)) ||
                                   (settingName === 'pomodoroChronometerFrequency' && (newValue === 2 || newValue === 1 || newValue === 3)) ||
                                   (settingName === 'pomodoroTickingSound' && newValue.includes('Clock Ticking')) ||
                                   (settingName === 'pomodoroChronometerVolume' && (newValue === 30 || newValue === 50 || newValue === 70)) ||
                                   (settingName === 'pomodoroNotificationVolume' && (newValue === 70 || newValue === 50 || newValue === 80));
        
        if (isLikelyDefaultValue) {
            console.log(`🔍 STM: Setting "${settingName}" initialized with likely default value "${newValue}" - not treating as change`);
            return false;
        }
        
        console.log(`🔄 STM: Setting "${settingName}" added with non-default value "${newValue}"`);
        return true;
    }
    
    const changed = oldValue !== newValue;
    
    if (changed) {
        console.log(`🔄 STM: Setting "${settingName}" changed from "${oldValue}" to "${newValue}"`);
    } else {
        console.log(`🔍 STM: Setting "${settingName}" unchanged (value: "${newValue}")`);
    }
    
    return changed;
};

// Helper function to check if chronometer is currently active (using ownership state)
const isChronometerActive = async () => {
    try {
        const result = await chrome.storage.local.get(['chronometerState']);
        const state = result.chronometerState || {};
        return state.isActive === true && state.ownerId !== null;
    } catch (error) {
        console.error('❌ STM: Error checking chronometer state:', error);
        return false;
    }
};

// Migrate Pomodoro settings from sync to local storage
const migratePomodoroSettingsToLocal = async () => {
    try {
        console.log('🔄 STM: Checking for Pomodoro settings migration...');
        
        // Check if already migrated by looking for local Pomodoro settings
        const localResult = await chrome.storage.local.get(['pomodoroSettings']);
        if (localResult.pomodoroSettings && Object.keys(localResult.pomodoroSettings).length > 0) {
            console.log('✅ STM: Pomodoro settings already in local storage');
            return localResult.pomodoroSettings;
        }
        
        // Get sync settings to migrate
        const syncResult = await chrome.storage.local.get(['gmbExtractorSettings']);
        const syncSettings = syncResult.gmbExtractorSettings || {};
        
        // Extract only Pomodoro-related settings
        const pomodoroSettings = {
            pomodoroWorkDuration: syncSettings.pomodoroWorkDuration || 25,
            pomodoroShortBreak: syncSettings.pomodoroShortBreak || 5,
            pomodoroLongBreak: syncSettings.pomodoroLongBreak || 15,
            pomodoroNumberOfCycles: syncSettings.pomodoroNumberOfCycles || 8,
            pomodoroEnabled: syncSettings.pomodoroEnabled !== false,
            pomodoroBlockingEnabled: syncSettings.pomodoroBlockingEnabled !== false,
            pomodoroChronometerSound: syncSettings.pomodoroChronometerSound !== false,
            pomodoroChronometerFrequency: syncSettings.pomodoroChronometerFrequency || 2,
            pomodoroChronometerOnBreak: syncSettings.pomodoroChronometerOnBreak === true
        };
        
        // Save to local storage
        await chrome.storage.local.set({ pomodoroSettings });
        console.log('✅ STM: Migrated Pomodoro settings to local storage:', pomodoroSettings);
        
        return pomodoroSettings;
    } catch (error) {
        console.error('❌ STM: Error migrating Pomodoro settings:', error);
        // Return defaults on error
        return {
            pomodoroWorkDuration: 25,
            pomodoroShortBreak: 5,
            pomodoroLongBreak: 15,
            pomodoroNumberOfCycles: 8,
            pomodoroEnabled: true,
            pomodoroBlockingEnabled: true,
            pomodoroChronometerSound: true,
            pomodoroChronometerFrequency: 2,
            pomodoroChronometerOnBreak: false
        };
    }
};

// Initialize timer settings with proper Pomodoro defaults - enhanced validation for problematic values
const ensureTimerDefaults = async (force = false) => {
    // LOCKDOWN PROTECTION: Prevent timer validation during settings lockdown
    if (isSettingsLockdownActive()) {
        console.log('🔒 STM: Timer validation BLOCKED - Settings lockdown active');
        console.log('🔒 STM: Timer defaults will be preserved during lockdown');
        return false;
    }
    
    try {
        console.log('🔧 STM: Enhanced validation of timer settings and clearing problematic cached values...');
        const result = await chrome.storage.local.get(['gmbExtractorSettings']);
        const settings = result.gmbExtractorSettings || {};
        
        // CRITICAL FIX: Detect both corrupted values AND problematic minimum patterns
        // that are likely artifacts of previous reset cycles
        const hasInvalidValues = 
            (settings.pomodoroWorkDuration !== undefined && 
             (settings.pomodoroWorkDuration <= 0 || !Number.isInteger(Number(settings.pomodoroWorkDuration)))) ||
            
            (settings.pomodoroShortBreak !== undefined && 
             (settings.pomodoroShortBreak <= 0 || !Number.isInteger(Number(settings.pomodoroShortBreak)))) ||
            
            (settings.pomodoroLongBreak !== undefined && 
             (settings.pomodoroLongBreak <= 0 || !Number.isInteger(Number(settings.pomodoroLongBreak))));
        
        // CRITICAL FIX: Detect problematic minimum patterns (1min work, 1min break)
        // These are likely artifacts of previous corruption/reset cycles, not user preferences
        const hasProblematicPattern = 
            (settings.pomodoroWorkDuration === 1 && settings.pomodoroShortBreak === 1) ||
            (settings.pomodoroWorkDuration === 1 && settings.pomodoroLongBreak === 15 && settings.pomodoroNumberOfCycles === 'unlimited');
        
        if (hasProblematicPattern) {
            console.log('🔧 STM: Detected problematic minimum pattern (1,1,15,unlimited) - likely reset artifact, forcing proper defaults');
        }
        
        if (hasInvalidValues || hasProblematicPattern) {
            if (hasInvalidValues) {
                console.log('🧹 STM: Found invalid timer values (non-positive or non-integer), clearing bad cache and forcing proper defaults');
            }
            if (hasProblematicPattern) {
                console.log('🧹 STM: Found problematic minimum pattern, resetting to proper Pomodoro defaults');
            }
            
            console.log('❌ STM: Current problematic values:', {
                work: settings.pomodoroWorkDuration + 'min',
                shortBreak: settings.pomodoroShortBreak + 'min',
                longBreak: settings.pomodoroLongBreak + 'min',
                cycles: settings.pomodoroNumberOfCycles
            });
            
            // Step 1: Clear ALL timer-related keys from storage to remove any corruption
            const cleanedSettings = { ...settings };
            const timerKeys = [
                'pomodoroWorkDuration', 'pomodoroShortBreak', 'pomodoroLongBreak',
                'pomodoroNumberOfCycles'
            ];
            timerKeys.forEach(key => delete cleanedSettings[key]);
            
            // Step 2: Force proper Pomodoro defaults (based on settings.js)
            const properDefaults = {
                pomodoroWorkDuration: 25,    // Standard Pomodoro work duration
                pomodoroShortBreak: 5,       // Standard short break
                pomodoroLongBreak: 15,       // Standard long break
                pomodoroNumberOfCycles: 8,   // Standard cycle count
                // Also ensure chronometer defaults
                pomodoroChronometerSound: settings.pomodoroChronometerSound !== undefined ? settings.pomodoroChronometerSound : true,
                pomodoroChronometerFrequency: settings.pomodoroChronometerFrequency || 2,
                pomodoroTickingSound: settings.pomodoroTickingSound || 'Clock Ticking 1',
                pomodoroChronometerVolume: settings.pomodoroChronometerVolume || 30,
                pomodoroNotificationVolume: settings.pomodoroNotificationVolume || 70
            };
            
            // Step 3: Merge cleaned settings with proper defaults
            const updated = { ...cleanedSettings, ...properDefaults };
            
            // Step 4: Force save to storage
            await chrome.storage.local.set({ gmbExtractorSettings: updated });
            
            console.log('✅ STM: Problematic values cleared! Timer defaults restored to standard values:', {
                work: updated.pomodoroWorkDuration + 'min',
                shortBreak: updated.pomodoroShortBreak + 'min', 
                longBreak: updated.pomodoroLongBreak + 'min',
                cycles: updated.pomodoroNumberOfCycles
            });
            
            return true; // Indicate that defaults were reset
        } else {
            console.log('✅ STM: Timer settings are valid and properly configured:', {
                work: settings.pomodoroWorkDuration + 'min',
                shortBreak: settings.pomodoroShortBreak + 'min',
                longBreak: settings.pomodoroLongBreak + 'min', 
                cycles: settings.pomodoroNumberOfCycles
            });
            
            return false; // Indicate no reset was needed
        }
    } catch (error) {
        console.error('❌ STM: Error ensuring timer defaults:', error);
        return false;
    }
};

// Listen for settings changes and apply them immediately to running timer
chrome.storage.onChanged.addListener((changes, namespace) => {
    // Handle audio settings from local storage (STM pattern)
    if (namespace === 'local' && changes.pomodoroAudioSettings) {
        console.log('STM: Audio settings changed, reloading...');
        loadSTMAudioSettings();
        return;
    }
    
    // Handle Pomodoro settings from local storage like Grande, other settings from sync storage
    if (namespace === 'local' && changes.gmbExtractorSettings && pomodoroTimer) {
        // Clear any existing timeout to debounce rapid changes (like volume slider)
        if (settingsChangeTimeout) {
            clearTimeout(settingsChangeTimeout);
        }
        
        // Debounce settings changes by 250ms
        settingsChangeTimeout = setTimeout(async () => {
            try {
                console.log('🔧 STM: Settings changed, analyzing changes...');
                const oldSettings = changes.gmbExtractorSettings.oldValue || {};
                const newSettings = changes.gmbExtractorSettings.newValue || {};
                
                // Debug: Log what actually changed - compare actual values, not just existence
                const changedKeys = [];
                const allKeys = new Set([...Object.keys(oldSettings), ...Object.keys(newSettings)]);
                
                allKeys.forEach(key => {
                    const oldVal = oldSettings[key];
                    const newVal = newSettings[key];
                    
                    // Only count as changed if values are actually different
                    if (oldVal !== newVal && !(oldVal === undefined && newVal === undefined)) {
                        // FIXED: Improved logic to prevent false positives
                        // Skip if it's just initialization (undefined -> value)
                        if (oldVal === undefined && newVal !== undefined) {
                            // Only count initialization as change if it's NOT a default value
                            const isTimerDefaultValue = (key === 'pomodoroWorkDuration' && newVal === 25) ||
                                                      (key === 'pomodoroShortBreak' && newVal === 5) ||
                                                      (key === 'pomodoroLongBreak' && newVal === 15) ||
                                                      (key === 'pomodoroNumberOfCycles' && (newVal === 8 || newVal === 'unlimited'));
                            
                            if (!isTimerDefaultValue) {
                                changedKeys.push(key);
                                console.log(`🔄 STM: Initialization with non-default value: ${key} = ${newVal}`);
                            } else {
                                console.log(`🔍 STM: Skipping default initialization: ${key} = ${newVal}`);
                            }
                        } else {
                            // Real value change (not initialization)
                            changedKeys.push(key);
                        }
                    }
                });
                
                console.log('📋 STM: Settings keys that actually changed values:', changedKeys);
                
                // BLACKLIST: Remove non-timer settings that should NEVER trigger timer logic
                // Extending the advancedFeaturesSearch exclusion pattern
                const blacklistedKeys = [
                    // Search and UI-related
                    'advancedFeaturesSearch',
                    
                    // Quick Actions and toggle-related
                    'quickActionsEnabled', 'quickActionsOrder', 'htagsEnabled', 'headingStructureEnabled',
                    'showLinksEnabled', 'showHiddenEnabled', 'boldFromSerpEnabled', 'schemaEnabled',
                    'imagesEnabled', 'metadataEnabled', 'utmBuilderEnabled', 'pageStructureEnabled',
                    'linksExtractorEnabled', 'colorPickerEnabled', 'responsiveEnabled', 'seoTestsEnabled',
                    'trackerDetectionEnabled', 'copyElementEnabled', 'textTransformersEnabled',
                    
                    // Location and domain settings
                    'locationChangerEnabled', 'trackedDomainsList', 'utmCleanerWhitelistDomains',
                    
                    // Video and media settings
                    'videoSpeedControllerEnabled', 'videoSpeedControllerKeyBindings', 'videoSpeedControllerSpeeds',
                    'videoSpeedControllerAudioSupport', 'videoSpeedControllerBlacklist', 'videoSpeedControllerOpacity',
                    'videoSpeedControllerRememberSpeed', 'videoSpeedControllerLastSpeed', 'videoSpeedControllerStartHidden',
                    
                    // General extension settings
                    'alertsEnabled', 'autoBackupEnabled', 'autoClean', 'autoCleanAndTitleEnabled', 'autoOpenFirst',
                    'extractorEnabled', 'notificationsEnabled', 'debugMode', 'developerMode',
                    
                    // Pomodoro audio and non-duration settings (should NOT trigger timer resets)
                    'pomodoroEnabled', 'pomodoroChronometerSound', 'pomodoroChronometerFrequency',
                    'pomodoroChronometerVolume', 'pomodoroNotificationVolume',
                    
                    // Any setting ending with common toggle suffixes
                ];
                
                // Filter out blacklisted keys
                const filteredChangedKeys = changedKeys.filter(key => {
                    const isBlacklisted = blacklistedKeys.includes(key) || 
                                        key.endsWith('Enabled') || 
                                        key.endsWith('Order') || 
                                        key.includes('Search') ||
                                        key.includes('Toggle') ||
                                        key.includes('Whitelist') ||
                                        key.includes('Blacklist');
                    
                    if (isBlacklisted) {
                        console.log(`🚫 STM: Blacklisted setting change ignored: ${key}`);
                        return false;
                    }
                    return true;
                });
                
                console.log('📋 STM: Settings keys after blacklist filtering:', filteredChangedKeys);
                
                // EARLY EXIT: Only run timer logic if timer-related keys actually changed
                const timerRelatedKeys = [
                    'pomodoroWorkDuration', 'pomodoroShortBreak', 'pomodoroLongBreak', 
                    'pomodoroNumberOfCycles', 'pomodoroChronometerSound', 'pomodoroChronometerFrequency',
                    'pomodoroEnabled', 'pomodoroChronometerVolume', 'pomodoroNotificationVolume'
                ];
                const timerKeysChanged = filteredChangedKeys.some(key => timerRelatedKeys.includes(key));
                
                // SURGICAL LOCKDOWN PROTECTION: Only block timer changes during lockdown, allow other settings
                if (isSettingsLockdownActive() && timerKeysChanged) {
                    console.log('🔒 STM: Settings lockdown active - BLOCKING timer-related changes only');
                    console.log('🔒 STM: Timer keys blocked:', filteredChangedKeys.filter(key => timerRelatedKeys.includes(key)));
                    console.log('🔓 STM: Non-timer settings continue processing normally during lockdown');
                    return; // Block only timer logic, non-timer settings processed normally
                }
                
                // RESTORATION PROTECTION: Skip timer restart if we're restoring from backup
                if (isRestoringFromBackup && timerKeysChanged) {
                    console.log('🔄 STM: Backup restoration in progress - blocking timer restart');
                    console.log('🔄 STM: Non-timer settings will still update normally');
                    return;
                }
                
                // RESTORATION TIMESTAMP PROTECTION: Double protection against restoration-triggered events
                if (timerKeysChanged && Date.now() - restorationTimestamp < 2500) {
                    console.log('🔄 STM: Storage change likely from recent restoration - blocking timer restart');
                    console.log('🔄 STM: Time since restoration:', Date.now() - restorationTimestamp, 'ms');
                    return;
                }
                
                if (!timerKeysChanged) {
                    console.log('🔍 STM: Non-timer settings changed (filtered out by blacklist), skipping ALL timer logic');
                    console.log('🔍 STM: Changed keys were:', filteredChangedKeys, '- none are timer-related');
                    return; // Exit early - no timer logic should run
                }
                
                console.log('⏱️ STM: Timer-related settings changed, proceeding with timer logic');
                console.log('⏱️ STM: Timer keys that changed:', filteredChangedKeys.filter(key => timerRelatedKeys.includes(key)));
        
                // If chronometer setting ACTUALLY changed and timer is running
                if (hasSettingChanged(changes, 'pomodoroChronometerSound')) {
                    const chronometerEnabled = newSettings.pomodoroChronometerSound === true;
                    const isTimerRunning = pomodoroTimer.currentState !== 'idle' && !pomodoroTimer.isPaused;
                    
                    if (isTimerRunning) {
                        if (chronometerEnabled) {
                            console.log('Pomodoro: Chronometer enabled - starting ticking');
                            const isBreakPeriod = pomodoroTimer.currentState === 'short_break' || pomodoroTimer.currentState === 'long_break';
                            if (typeof pomodoroTimer.startChronometer === 'function') {
                                await pomodoroTimer.startChronometer(isBreakPeriod, 'settings_change');
                            } else {
                                console.error('Pomodoro: startChronometer method not available');
                            }
                        } else {
                            console.log('Pomodoro: Chronometer disabled - stopping ticking');
                            if (typeof pomodoroTimer.stopChronometer === 'function') {
                                await pomodoroTimer.stopChronometer();
                            } else {
                                console.error('Pomodoro: stopChronometer method not available');
                            }
                        }
                    }
                }
                
                // If chronometer frequency ACTUALLY changed and chronometer is currently active
                if (hasSettingChanged(changes, 'pomodoroChronometerFrequency') && (await isChronometerActive())) {
                    console.log('Pomodoro: Chronometer frequency changed - restarting with new interval');
                    const isBreakPeriod = pomodoroTimer.currentState === 'short_break' || pomodoroTimer.currentState === 'long_break';
                    if (typeof pomodoroTimer.stopChronometer === 'function' && typeof pomodoroTimer.startChronometer === 'function') {
                        await pomodoroTimer.stopChronometer();
                        await pomodoroTimer.startChronometer(isBreakPeriod, 'settings_change');
                    } else {
                        console.error('Pomodoro: Chronometer methods not available for frequency change');
                    }
                }
                
                // If pomodoroEnabled ACTUALLY changed to false, stop the timer immediately
                if (hasSettingChanged(changes, 'pomodoroEnabled')) {
                    const newSettings = changes.gmbExtractorSettings.newValue || {};
                    if (newSettings.pomodoroEnabled === false) {
                        console.log('🍅 Pomodoro: Settings disabled - stopping timer immediately');
                        if (pomodoroTimer && pomodoroTimer.currentState !== 'idle') {
                            pomodoroTimer.stop();
                            await pomodoroTimer.stopChronometer();
                            if (pomodoroBlockingSystem) {
                                pomodoroBlockingSystem.stopBlocking();
                            }
                            if (pomodoroBadgeManager) {
                                pomodoroBadgeManager.clearBadge();
                            }
                            console.log('🍅 Pomodoro: Timer stopped due to settings change');
                        }
                    }
                }
                
                // If volume ACTUALLY changed, reload STM audio settings immediately
                if (hasSettingChanged(changes, 'pomodoroChronometerVolume') || hasSettingChanged(changes, 'pomodoroNotificationVolume')) {
                    console.log('STM: Volume changed - reloading settings immediately');
                    loadSTMAudioSettings();
                }
                
                // STM: If timer duration settings ACTUALLY changed, reload timer settings immediately
                const timerSettings = ['pomodoroWorkDuration', 'pomodoroShortBreak', 'pomodoroLongBreak', 'pomodoroNumberOfCycles'];
                const timerSettingsChanged = timerSettings.some(setting => hasSettingChanged(changes, setting));
                
                console.log('⏱️ STM: Timer settings change check:', {
                    timerSettingsChanged,
                    checkedSettings: timerSettings,
                    changedTimerSettings: timerSettings.filter(s => hasSettingChanged(changes, s)),
                    timerRunning: pomodoroTimer?.currentState !== 'idle'
                });
                
                // IMPORTANT: Only reload timer if duration settings actually changed
                // This prevents the chronometer from stopping when unrelated settings change
                if (timerSettingsChanged && pomodoroTimer && typeof pomodoroTimer.loadSettings === 'function') {
                    console.log('STM: Timer duration settings changed - validating and reloading timer configuration');
                    
                    // Log which specific timer settings changed
                    const changedTimerSettingsList = timerSettings.filter(s => hasSettingChanged(changes, s));
                    console.log('STM: Specific timer settings that changed:', changedTimerSettingsList);
                    
                    // FIXED: Only validate defaults if actual duration settings changed
                    // This prevents unnecessary resets on non-duration changes
                    const durationSettings = ['pomodoroWorkDuration', 'pomodoroShortBreak', 'pomodoroLongBreak'];
                    const durationSettingsChanged = changedTimerSettingsList.some(s => durationSettings.includes(s));
                    
                    if (durationSettingsChanged) {
                        // LOCKDOWN PROTECTION: Settings popup lockdown system handles timer validation
                        // Automatic validation from storage changes is no longer needed
                        console.log('STM: Duration settings changed, but skipping automatic validation');
                        console.log('🛡️ STM: Timer validation is handled by settings popup lockdown system');
                    } else {
                        console.log('STM: Non-duration timer settings changed, skipping validation');
                    }
                    
                    // If timer is running, we need to restart it with new settings
                    const wasRunning = pomodoroTimer.currentState !== 'idle';
                    const wasPaused = pomodoroTimer.isPaused;
                    const currentMode = pomodoroTimer.currentMode;
                    const wasChronometerActive = await isChronometerActive();
                    
                    if (wasRunning) {
                        console.log('STM: Timer was running - stopping to apply new settings');
                        // Stop everything cleanly
                        pomodoroTimer.stop();
                        // Only stop chronometer if timer durations changed, not for other settings
                        if (changedTimerSettingsList.some(s => ['pomodoroWorkDuration', 'pomodoroShortBreak', 'pomodoroLongBreak'].includes(s))) {
                            console.log('STM: Duration settings changed, stopping chronometer');
                            await pomodoroTimer.stopChronometer();
                        } else {
                            console.log('STM: Non-duration settings changed, keeping chronometer active');
                        }
                    }
                    
                    // Reload settings
                    pomodoroTimer.loadSettings().then(async () => {
                        console.log('STM: Timer configuration reloaded successfully');
                        
                        // If timer was running, restart it with new settings
                        if (wasRunning && !wasPaused) {
                            console.log('STM: Restarting timer with new settings');
                            pomodoroTimer.start(currentMode);
                            
                            // If chronometer was active and we didn't stop it, or if it should be active, restart it
                            if (wasChronometerActive || (pomodoroTimer.settings?.pomodoroChronometerSound && !pomodoroTimer.isPaused)) {
                                console.log('STM: Restarting chronometer after timer restart');
                                const isBreakPeriod = pomodoroTimer.currentState === 'short_break' || pomodoroTimer.currentState === 'long_break';
                                await pomodoroTimer.startChronometer(isBreakPeriod, 'settings_change');
                            }
                        }
                    }).catch(error => {
                        console.error('STM: Error reloading timer configuration:', error);
                    });
                }
            } catch (error) {
                console.error('Pomodoro: Error applying settings changes:', error);
            }
        }, 250); // 250ms debounce
    }
    
    // Handle site blocking changes like Pomodoro Grande
    if ((namespace === 'local') && (changes.blockedSites || changes.allowedUrls) && pomodoroBlockingSystem && pomodoroBlockingSystem.isActive) {
        // Use async IIFE to handle await call
        (async () => {
            try {
                console.log('Pomodoro: Site blocking lists changed - updating rules like Grande');
                // Use cached function instead of dynamic import
                if (updateBlockingRules) {
                    await updateBlockingRules();
                } else {
                    console.warn('Pomodoro: updateBlockingRules function not available');
                }
            } catch (error) {
                console.error('Pomodoro: Error updating blocking rules:', error);
            }
        })();
    }
    
    // Handle other non-Pomodoro settings from sync storage
    if (namespace === 'sync' && changes.gmbExtractorSettings) {
        try {
            // Handle non-Pomodoro settings here if needed
            console.log('Non-Pomodoro settings changed in sync storage');
            
            // Also check pomodoroEnabled in sync storage (extras settings uses sync)
            const newSettings = changes.gmbExtractorSettings.newValue || {};
            if ('pomodoroEnabled' in newSettings && newSettings.pomodoroEnabled === false) {
                console.log('🍅 Pomodoro: Settings disabled in sync storage - stopping timer immediately');
                if (pomodoroTimer && pomodoroTimer.currentState !== 'idle') {
                    pomodoroTimer.stop();
                    pomodoroTimer.stopChronometer().catch(err => console.log('Chronometer stop error:', err));
                    if (pomodoroBlockingSystem) {
                        pomodoroBlockingSystem.stopBlocking();
                    }
                    if (pomodoroBadgeManager) {
                        pomodoroBadgeManager.clearBadge();
                    }
                    console.log('🍅 Pomodoro: Timer stopped due to sync storage settings change');
                }
            }
        } catch (error) {
            console.error('Settings: Error applying sync storage changes:', error);
        }
    }
});

// STM: Direct notification sound - exactly like settings page
const playSTMNotificationSound = async (soundType) => {
    try {
        console.log('STM: Playing notification sound...', soundType);
        await loadSTMAudioSettings();
        
        // Ensure offscreen document exists first
        const offscreenReady = await ensureSTMOffscreenDocument();
        if (!offscreenReady) {
            console.error('STM: Could not create offscreen document for audio');
            return;
        }
        
        const soundName = soundType === 'workComplete' 
            ? pomodoroAudioSettings.workCompletedSound 
            : pomodoroAudioSettings.endBreakSound;
            
        const audioUrl = getSTMAudioUrl(soundName);
        if (!audioUrl) {
            console.error('STM: No audio URL for sound:', soundName);
            return;
        }
        
        console.log('STM: Playing notification:', soundName, audioUrl);
        
        // Use safe messaging to prevent connection errors
        await safeChronometerSendMessage({
            action: 'playSound',
            selectedSound: audioUrl,
            soundVolume: pomodoroAudioSettings.notificationVolume,
            isSoundEnabled: true
        }, 'notification-sound');
    } catch (error) {
        console.error('STM: Error playing notification sound:', error);
    }
};

// STM: Multi-Window-Safe Chronometer System
let grandeChrometerInterval = null;
let chronometerWasPaused = false; // Track if chronometer was paused for preview
let chronometerRestartCount = 0; // Track restart attempts to prevent infinite loops
let chronometerLastRestartTime = 0; // Track when last restart occurred

// Atomic chronometer control - prevents multiple ticking loops
const chronometerLock = {
    isLocked: false,
    ownerId: null,
    lastHeartbeat: 0
};

// Generate unique ID for each window/tab
const generateChromometerId = () => {
    return `chronometer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Initialize chronometer state in local storage
const initializeChronometerState = async () => {
    try {
        const result = await chrome.storage.local.get(['chronometerState']);
        if (!result.chronometerState) {
            await chrome.storage.local.set({
                chronometerState: {
                    isActive: false,
                    ownerId: null,
                    lastHeartbeat: 0,
                    frequency: 2,
                    isBreakPeriod: false
                }
            });
            console.log('🔧 STM: Chronometer state initialized in local storage');
        }
    } catch (error) {
        console.error('❌ STM: Error initializing chronometer state:', error);
    }
};

// Check if chronometer is owned by another window
const isChronometerOwnedByOther = async (myId) => {
    try {
        const result = await chrome.storage.local.get(['chronometerState']);
        const state = result.chronometerState || {};
        
        // If no owner, it's free
        if (!state.ownerId || !state.isActive) {
            return false;
        }
        
        // If we own it, it's not owned by another
        if (state.ownerId === myId) {
            return false;
        }
        
        // Check if other owner is still alive (heartbeat within 10 seconds)
        const now = Date.now();
        const isOwnerAlive = (now - state.lastHeartbeat) < 10000;
        
        if (!isOwnerAlive) {
            console.log('💀 STM: Previous chronometer owner expired, clearing state');
            await chrome.storage.local.set({
                chronometerState: {
                    isActive: false,
                    ownerId: null,
                    lastHeartbeat: 0,
                    frequency: state.frequency || 2,
                    isBreakPeriod: state.isBreakPeriod || false
                }
            });
            return false;
        }
        
        return true;
    } catch (error) {
        console.error('❌ STM: Error checking chronometer ownership:', error);
        return false;
    }
};

// Update chronometer heartbeat
const updateChronometerHeartbeat = async (ownerId) => {
    try {
        const result = await chrome.storage.local.get(['chronometerState']);
        const state = result.chronometerState || {};
        
        // Only update if we own it
        if (state.ownerId === ownerId && state.isActive) {
            state.lastHeartbeat = Date.now();
            await chrome.storage.local.set({ chronometerState: state });
        }
    } catch (error) {
        console.error('❌ STM: Error updating heartbeat:', error);
    }
};

// Acquire chronometer ownership
const acquireChronometerOwnership = async (ownerId, isBreakPeriod = false, isRestart = false, callSource = 'unknown') => {
    try {
        // BLACKLIST: Block settings changes and non-user actions from owning chronometer
        const blockedSources = ['settings_change', 'auto_settings', 'popup_init', 'background_init'];
        if (blockedSources.includes(callSource)) {
            console.log('🚫 STM: Chronometer ownership blocked for source:', callSource);
            console.log('🔒 STM: Only user actions (reset, toggle, duration changes) can own chronometer');
            return false;
        }
        
        console.log('✅ STM: Chronometer ownership allowed for source:', callSource);
        
        // For restart scenarios, be more lenient about existing ownership
        if (!isRestart) {
            // Check if already owned by another window (normal startup)
            if (await isChronometerOwnedByOther(ownerId)) {
                console.log('🔒 STM: Chronometer already owned by another window');
                return false;
            }
        } else {
            // For restart scenarios, clear any stale ownership first
            console.log('🔄 STM: Restart mode - clearing any stale ownership');
            const result = await chrome.storage.local.get(['chronometerState']);
            const state = result.chronometerState || {};
            
            // Clear stale ownership if it exists
            await chrome.storage.local.set({
                chronometerState: {
                    isActive: false,
                    ownerId: null,
                    lastHeartbeat: 0,
                    frequency: state.frequency || 2,
                    isBreakPeriod: isBreakPeriod
                }
            });
        }
        
        // Get frequency from settings
        const settingsResult = await chrome.storage.local.get(['gmbExtractorSettings']);
        const settings = settingsResult.gmbExtractorSettings || {};
        const frequency = settings.pomodoroChronometerFrequency || 2;
        
        // Acquire ownership
        await chrome.storage.local.set({
            chronometerState: {
                isActive: true,
                ownerId: ownerId,
                lastHeartbeat: Date.now(),
                frequency: frequency,
                isBreakPeriod: isBreakPeriod
            }
        });
        
        console.log('🏆 STM: Chronometer ownership acquired by:', ownerId, isRestart ? '(restart)' : '(new)');
        return true;
    } catch (error) {
        console.error('❌ STM: Error acquiring chronometer ownership:', error);
        return false;
    }
};

// Release chronometer ownership
const releaseChronometerOwnership = async (ownerId) => {
    try {
        const result = await chrome.storage.local.get(['chronometerState']);
        const state = result.chronometerState || {};
        
        // Only release if we own it
        if (state.ownerId === ownerId) {
            await chrome.storage.local.set({
                chronometerState: {
                    isActive: false,
                    ownerId: null,
                    lastHeartbeat: 0,
                    frequency: state.frequency || 2,
                    isBreakPeriod: state.isBreakPeriod || false
                }
            });
            console.log('🔓 STM: Chronometer ownership released by:', ownerId);
        }
    } catch (error) {
        console.error('❌ STM: Error releasing chronometer ownership:', error);
    }
};

// Check if chronometer should restart (prevent infinite restart loops)
const shouldAttemptChronometerRestart = () => {
    const now = Date.now();
    const minRestartInterval = 30000; // Increased: Minimum 30 seconds between restart attempts  
    const maxRestartCount = 2; // Reduced: Maximum 2 restart attempts per session
    
    // Reset restart count if enough time has passed
    if (now - chronometerLastRestartTime > 120000) { // Increased: Reset after 2 minutes
        chronometerRestartCount = 0;
    }
    
    // Check if we should attempt restart
    const timeSinceLastRestart = now - chronometerLastRestartTime;
    const canRestart = chronometerRestartCount < maxRestartCount && timeSinceLastRestart > minRestartInterval;
    
    // Smart restart logging - only log details occasionally to reduce spam
    if (now - lastRestartCheckLog > RESTART_CHECK_LOG_INTERVAL || canRestart) {
        console.log(`🔍 STM: Restart check - Count: ${chronometerRestartCount}/${maxRestartCount}, Time since last: ${timeSinceLastRestart}ms, Can restart: ${canRestart}`);
        lastRestartCheckLog = now;
    }
    
    return canRestart;
};

// Restart chronometer after connection failure
const restartSTMChronometer = async () => {
    try {
        if (!shouldAttemptChronometerRestart()) {
            // Only log skipped restart occasionally to reduce console spam
            const now = Date.now();
            if (now - lastRestartCheckLog > RESTART_CHECK_LOG_INTERVAL) {
                console.log('🚫 STM: Chronometer restart skipped - system stable');
                lastRestartCheckLog = now;
            }
            return false;
        }
        
        console.log('🔄 STM: Attempting to restart chronometer after connection failure...');
        chronometerRestartCount++;
        chronometerLastRestartTime = Date.now();
        
        // Load current audio settings to check if chronometer is still enabled
        await loadSTMAudioSettings();
        
        // Don't restart if chronometer has been disabled
        if (!pomodoroAudioSettings.chronometerEnabled) {
            console.log('🚫 STM: Chronometer restart skipped - chronometer disabled in settings');
            return false;
        }
        
        // Get current chronometer state to preserve settings
        const result = await chrome.storage.local.get(['chronometerState']);
        const state = result.chronometerState || {};
        const isBreakPeriod = state.isBreakPeriod || false;
        
        // Don't restart during breaks if chronometerOnBreak is disabled
        if (isBreakPeriod && !pomodoroAudioSettings.chronometerOnBreak) {
            console.log('🚫 STM: Chronometer restart skipped - disabled during breaks');
            return false;
        }
        
        // Stop the failed chronometer (clear interval and ownership)
        await stopSTMChronometer();
        
        // Enhanced stabilization delay for audio synchronization
        console.log('⏳ STM: Waiting for audio system stabilization...');
        await new Promise(resolve => setTimeout(resolve, 800)); // Increased from 500ms to 800ms
        
        // Check if background script is ready with enhanced document management
        try {
            // Force recreate offscreen document on restart to ensure fresh connection
            const documentReady = await ensureSTMOffscreenDocument(true);
            if (!documentReady) {
                console.log('🚫 STM: Background not ready for restart, will retry later');
                return false;
            }
        } catch (error) {
            console.log('🚫 STM: Background not ready for restart, will retry later');
            return false;
        }
        
        // Restart the chronometer with the same settings (mark as restart)
        const restarted = await startSTMChronometer(isBreakPeriod, true);
        
        if (restarted) {
            // Chronometer restarted successfully (reduced logging for less spam)
            // Reset restart count on successful restart
            chronometerRestartCount = 0;
            return true;
        } else {
            console.log('❌ STM: Failed to restart chronometer');
            return false;
        }
        
    } catch (error) {
        console.error('❌ STM: Error during chronometer restart:', error);
        return false;
    }
};

// Safe chrome.runtime.sendMessage wrapper for chronometer
const safeChronometerSendMessage = async (message, context = 'chronometer') => {
    return new Promise((resolve, reject) => {
        try {
            // Check if runtime is available
            if (!chrome.runtime?.id) {
                throw new Error('Chrome runtime not available');
            }
            
            chrome.runtime.sendMessage(message, (response) => {
                // Check for connection errors
                if (chrome.runtime.lastError) {
                    const errorMessage = chrome.runtime.lastError.message;
                    
                    // Suppress specific connection errors that indicate background restart
                    if (errorMessage.includes('Could not establish connection') || 
                        errorMessage.includes('Receiving end does not exist') ||
                        errorMessage.includes('Extension context invalidated') ||
                        errorMessage.includes('message port closed')) {
                        
                        // Throttled logging for connection issues to reduce console spam
                        connectionIssueCount++;
                        const now = Date.now();
                        if (now - lastConnectionIssueLog > CONNECTION_LOG_INTERVAL) {
                            if (connectionIssueCount === 1) {
                                console.log(`🔄 STM: ${context} connection lost, monitoring...`);
                            } else {
                                console.log(`🔄 STM: ${connectionIssueCount} connection issues detected, system stable`);
                            }
                            connectionIssueCount = 0; // Reset counter
                            lastConnectionIssueLog = now;
                        }
                        
                        // Only trigger restart for chronometer ticks, not initial or notification sounds
                        if (context === 'chronometer-tick') {
                            restartSTMChronometer().catch(err => {
                                console.error('❌ STM: Restart failed:', err);
                            });
                        }
                        
                        // Resolve with null to prevent error propagation
                        resolve(null);
                        return;
                    }
                    
                    // For other errors, reject normally
                    reject(new Error(errorMessage));
                    return;
                }
                
                resolve(response);
            });
        } catch (error) {
            // For immediate errors, only restart on tick failures (with throttled logging)
            connectionIssueCount++;
            const now = Date.now();
            
            if (context === 'chronometer-tick') {
                if (now - lastConnectionIssueLog > CONNECTION_LOG_INTERVAL) {
                    console.log(`🔄 STM: Connection issues detected (${connectionIssueCount}), managing automatically`);
                    connectionIssueCount = 0;
                    lastConnectionIssueLog = now;
                }
                restartSTMChronometer().catch(err => {
                    console.error('❌ STM: Restart failed:', err);
                });
            }
            resolve(null);
        }
    });
};

// Broadcast chronometer state to all windows (simplified to avoid connection errors)
const broadcastChronometerState = async () => {
    try {
        const result = await chrome.storage.local.get(['chronometerState']);
        const state = result.chronometerState || {};
        
        // Instead of trying to message all tabs (which can cause connection errors),
        // just update the storage state. Other parts of the system will read from storage.
        // Chronometer state updated in storage (reduced logging for less spam)
        
        // Optional: Only broadcast to active tabs if we really need to
        // For now, let's rely on storage-based state management to avoid connection errors
        
    } catch (error) {
        console.error('❌ STM: Error broadcasting chronometer state:', error);
    }
};

const startSTMChronometer = async (isBreakPeriod = false, isRestart = false, callSource = 'unknown') => {
    try {
        console.log('🔄 STM: Starting chronometer...', { isBreakPeriod, isRestart, callSource });
        await loadSTMAudioSettings();
        
        if (!pomodoroAudioSettings.chronometerEnabled) {
            console.log('⏹️ STM: Chronometer disabled in settings');
            return false;
        }
        
        // FIXED: Check chronometerOnBreak setting properly during break periods
        if (isBreakPeriod && !pomodoroAudioSettings.chronometerOnBreak) {
            console.log('⏸️ STM: Chronometer disabled during breaks (chronometerOnBreak = false)');
            return false;
        }
        
        // Generate unique ID for this chronometer instance
        const myId = generateChromometerId();
        
        // Try to acquire ownership (pass restart flag and call source)
        const ownershipAcquired = await acquireChronometerOwnership(myId, isBreakPeriod, isRestart, callSource);
        if (!ownershipAcquired) {
            console.log('🚫 STM: Cannot start chronometer - failed to acquire ownership');
            return false;
        }
        
        // Stop any existing local chronometer (safety)
        await stopSTMChronometer();
        
        // Ensure offscreen document exists first
        const offscreenReady = await ensureSTMOffscreenDocument();
        if (!offscreenReady) {
            console.error('STM: Could not create offscreen document for chronometer');
            await releaseChronometerOwnership(myId);
            return false;
        }
        
        const audioUrl = getSTMAudioUrl(pomodoroAudioSettings.tickingSound, true);
        if (!audioUrl) {
            console.error('❌ STM: No audio URL for ticking sound:', pomodoroAudioSettings.tickingSound);
            await releaseChronometerOwnership(myId);
            return false;
        }
        
        // Get frequency from local storage
        const result = await chrome.storage.local.get(['chronometerState']);
        const state = result.chronometerState || {};
        const frequency = state.frequency || 2;
        
        console.log('⏰ STM: Starting chronometer with frequency:', frequency, 'seconds');
        console.log('🎵 STM: Using ticking sound:', pomodoroAudioSettings.tickingSound, audioUrl);
        console.log('🏆 STM: Chronometer owned by:', myId);
        
        // Audio state tracking to prevent overlaps
        let isPlayingTick = false;
        
        // Play first tick immediately with state tracking
        const playInitialTick = async () => {
            if (isPlayingTick) return; // Prevent overlap
            isPlayingTick = true;
            
            try {
                await safeChronometerSendMessage({
                    action: 'playSound',
                    selectedSound: audioUrl,
                    soundVolume: pomodoroAudioSettings.chronometerVolume,
                    isSoundEnabled: true
                }, 'chronometer-initial');
            } catch (err) {
                console.log('🔄 STM: Initial tick failed, will continue with interval');
            } finally {
                isPlayingTick = false;
            }
        };
        
        playInitialTick();
        
        // Set up interval for subsequent ticks with audio state tracking
        grandeChrometerInterval = setInterval(async () => {
            try {
                // Update heartbeat to maintain ownership
                await updateChronometerHeartbeat(myId);
                
                // Skip tick if audio is already playing (prevent overlap)
                if (isPlayingTick) {
                    console.log('🔇 STM: Skipping tick - audio still playing');
                    return;
                }
                
                isPlayingTick = true;
                
                // Play tick sound with connection error handling
                try {
                    await safeChronometerSendMessage({
                        action: 'playSound',
                        selectedSound: audioUrl,
                        soundVolume: pomodoroAudioSettings.chronometerVolume,
                        isSoundEnabled: true
                    }, 'chronometer-tick');
                } finally {
                    isPlayingTick = false;
                }
                
            } catch (error) {
                isPlayingTick = false; // Reset state on error
                // Only log non-connection errors
                if (!error.message.includes('connection') && !error.message.includes('runtime')) {
                    console.error('STM: Error in chronometer interval:', error);
                }
            }
        }, frequency * 1000);
        
        // Store ownership reference
        chronometerLock.isLocked = true;
        chronometerLock.ownerId = myId;
        chronometerLock.lastHeartbeat = Date.now();
        
        // Broadcast state to all windows (non-blocking)
        broadcastChronometerState().catch(err => {
            console.log('🔄 STM: Broadcast failed, continuing anyway');
        });
        
        // Chronometer started successfully (reduced logging for less spam)
        return true;
    } catch (error) {
        console.error('❌ STM: Error starting chronometer:', error);
        return false;
    }
};

const stopSTMChronometer = async (skipAudioDebounce = false) => {
    try {
        console.log('⏹️ STM: Stopping chronometer...');
        
        // Clear the interval
        if (grandeChrometerInterval) {
            clearInterval(grandeChrometerInterval);
            grandeChrometerInterval = null;
            console.log('✅ STM: Chronometer interval cleared');
        }
        
        // Release ownership if we own it
        if (chronometerLock.ownerId) {
            await releaseChronometerOwnership(chronometerLock.ownerId);
            chronometerLock.isLocked = false;
            chronometerLock.ownerId = null;
            chronometerLock.lastHeartbeat = 0;
        }
        
        // Audio debouncing: Add delay to prevent audio overlap during restarts
        if (!skipAudioDebounce) {
            console.log('🔇 STM: Audio debounce delay to prevent overlap...');
            await new Promise(resolve => setTimeout(resolve, 300)); // 300ms delay
        }
        
        // Broadcast state to all windows (non-blocking)
        broadcastChronometerState().catch(err => {
            console.log('🔄 STM: Stop broadcast failed, continuing anyway');
        });
        
        console.log('✅ STM: Chronometer stopped successfully');
    } catch (error) {
        console.error('❌ STM: Error stopping chronometer:', error);
    }
};

// Initialize Pomodoro components
async function initializePomodoroComponents() {
    // Initialize chronometer state in local storage
    await initializeChronometerState();
    
    // Ensure timer settings have proper defaults (force during initialization)
    await ensureTimerDefaults(true);
    
    // Create offscreen document immediately on initialization
    try {
        await ensureSTMOffscreenDocument();
        console.log('STM: Offscreen document ready for Pomodoro initialization');
    } catch (error) {
        console.error('STM: Error creating offscreen document during initialization:', error);
    }
    
    if (!pomodoroTimer) {
        try {
            // Use chrome.scripting to inject and execute Pomodoro classes in background context
            const scripts = [
                'js/pomodoro/blocking-system.js',
                'js/pomodoro/badge-manager.js'
            ];
            
            // For background script, we need to manually inject the classes
            // This is a simplified initialization
            console.log('Initializing Pomodoro in background script...');
            
            // Check for saved timer state first
            const savedState = await chrome.storage.local.get(['pomodoroTimerState']);
            const timerState = savedState.pomodoroTimerState;
            
            // STM-style timer variables (in milliseconds like original)
            let l = 1000 * 60 * 25; // Work time (25 minutes default)
            let S = 1000 * 60 * 5;  // Short break (5 minutes default)
            let E = 1000 * 60 * 15; // Long break (15 minutes default)
            let t = 0; // Current timer countdown (in seconds)
            
            // Load timer settings from storage like Grande
            const loadTimerSettings = async () => {
                try {
                    // Get Pomodoro settings from local storage 
                    const result = await chrome.storage.local.get(['gmbExtractorSettings']);
                    const settings = result.gmbExtractorSettings || {};
                    
                    // Load user settings or use defaults
                    const workDuration = settings.pomodoroWorkDuration || 25;
                    const shortBreak = settings.pomodoroShortBreak || 5;
                    const longBreak = settings.pomodoroLongBreak || 15;
                    
                    // Update Grande-style variables (convert minutes to milliseconds)
                    l = 1000 * 60 * workDuration;
                    S = 1000 * 60 * shortBreak;
                    E = 1000 * 60 * longBreak;
                    
                    console.log('STM: Timer settings loaded:', {
                        work: workDuration + 'min',
                        shortBreak: shortBreak + 'min', 
                        longBreak: longBreak + 'min',
                    });
                } catch (error) {
                    console.error('STM: Error loading timer settings:', error);
                }
            };
            
            // Load initial settings
            await loadTimerSettings();
            
            // Create simplified instances for background script
            pomodoroTimer = {
                currentState: 'idle',
                timeRemaining: 0,
                isPaused: false,
                sessionCount: 0, // Track completed work sessions for break logic
                interval: null,
                loadSettings: loadTimerSettings, // Expose settings loading function
                
                // Cycle tracking
                totalCycles: 'unlimited',
                currentCycle: 0,
                currentMode: 'standard', // Track current mode
                
                // CENTRALIZED countdown method - single source of truth for all timer operations
                startCountdown() {
                    // Clear any existing interval first to prevent multiple timers
                    this.clearCountdown();
                    
                    console.log('🔄 POMODORO: Starting centralized countdown:', {
                        state: this.currentState,
                        timeRemaining: this.timeRemaining,
                        isPaused: this.isPaused
                    });
                    
                    this.interval = setInterval(() => {
                        this.timeRemaining--;
                        if (this.timeRemaining <= 0) {
                            this.complete();
                        }
                        // Update badge
                        if (pomodoroBadgeManager) {
                            pomodoroBadgeManager.updateBadge(this.timeRemaining, this.currentState, this.isPaused);
                        }
                        
                        // Store state in chrome.storage for popup to read
                        chrome.storage.local.set({
                            pomodoroTimerState: {
                                currentState: this.currentState,
                                timeRemaining: this.timeRemaining,
                                isPaused: this.isPaused,
                                totalCycles: this.totalCycles,
                                currentCycle: this.currentCycle,
                                currentMode: this.currentMode,
                                sessionCount: this.sessionCount,
                                lastUpdate: Date.now()
                            }
                        }).catch(() => {});
                    }, 1000);
                },
                
                // CENTRALIZED cleanup method
                clearCountdown() {
                    if (this.interval) {
                        clearInterval(this.interval);
                        this.interval = null;
                        console.log('🧹 POMODORO: Cleared countdown interval');
                    }
                },
                
                async start() {
                    console.log('Pomodoro: Starting timer with current time preset');
                    
                    // Prevent multiple timer instances - use centralized cleanup
                    if (this.currentState !== 'idle' || this.interval) {
                        console.warn('Pomodoro: Timer already running, stopping first');
                        await this.stop();
                    }
                    
                    // Load current settings before starting like Grande
                    await loadTimerSettings();
                    
                    // Load cycle settings
                    const result = await chrome.storage.local.get(['gmbExtractorSettings', 'pomodoroTimerState']);
                    const settings = result.gmbExtractorSettings || {};
                    const timerState = result.pomodoroTimerState || {};
                    
                    this.totalCycles = settings.pomodoroNumberOfCycles || 'unlimited';
                    this.currentCycle = this.totalCycles === 'unlimited' ? 0 : 1; // Start on cycle 1
                    this.sessionCount = 0;
                    
                    this.currentState = 'work';
                    // Use current time remaining from timer state if available, otherwise default to 25 minutes
                    this.timeRemaining = timerState.timeRemaining || (l / 1000); // Use preset time or default
                    this.isPaused = false;
                    
                    console.log('Pomodoro: Starting with time remaining:', this.timeRemaining / 60, 'minutes');
                    
                    // Use centralized countdown method
                    this.startCountdown();
                    
                    // Start website blocking for work sessions
                    if (typeof updateBlockingRules === 'function') {
                        updateBlockingRules().catch(err => console.error('Failed to start blocking:', err));
                    }
                    
                    // Clear any existing chronometer ownership and start fresh
                    try {
                        console.log('🧹 STM: Clearing chronometer ownership for fresh start');
                        await chrome.storage.local.set({
                            chronometerState: {
                                isActive: false,
                                ownerId: null,
                                lastHeartbeat: 0,
                                frequency: 2,
                                isBreakPeriod: false
                            }
                        });
                        
                        // Small delay to ensure storage is written
                        await new Promise(resolve => setTimeout(resolve, 100));
                    } catch (error) {
                        console.error('❌ STM: Error clearing chronometer state:', error);
                    }
                    
                    // Start STM chronometer immediately
                    const isBreakPeriod = this.currentState === 'short_break' || this.currentState === 'long_break';
                    await this.startChronometer(isBreakPeriod, 'user_action');
                    
                    return true;
                },
                
                async pause() {
                    console.log('Pomodoro: Pausing timer');
                    this.isPaused = true;
                    // Use centralized cleanup method
                    this.clearCountdown();
                    
                    // Update storage to reflect paused state
                    chrome.storage.local.set({
                        pomodoroTimerState: {
                            currentState: this.currentState,
                            timeRemaining: this.timeRemaining,
                            isPaused: this.isPaused,
                            totalCycles: this.totalCycles,
                            currentCycle: this.currentCycle,
                            currentMode: this.currentMode,
                            sessionCount: this.sessionCount,
                            lastUpdate: Date.now()
                        }
                    }).catch(() => {});
                    
                    return true;
                },
                
                async resume() {
                    console.log('Pomodoro: Resuming timer');
                    if (this.isPaused && this.currentState !== 'idle') {
                        this.isPaused = false;
                        
                        // Use centralized countdown method
                        this.startCountdown();
                        
                        // Restart STM chronometer when resuming
                        const isBreakPeriod = this.currentState === 'short_break' || this.currentState === 'long_break';
                        await this.startChronometer(isBreakPeriod, 'user_action');
                        
                        return true;
                    }
                    return false;
                },
                
                async stop() {
                    console.log('Pomodoro: Stopping timer');
                    this.currentState = 'idle';
                    this.timeRemaining = 0;
                    this.isPaused = false;
                    this.currentCycle = 0;
                    this.sessionCount = 0;
                    
                    // Use centralized cleanup method
                    this.clearCountdown();
                    
                    // Stop chronometer using the global function
                    await stopSTMChronometer();
                    
                    // Clear badge
                    if (pomodoroBadgeManager) {
                        pomodoroBadgeManager.clearBadge();
                    }
                    
                    // Clear state from storage
                    chrome.storage.local.set({
                        pomodoroTimerState: {
                            currentState: 'idle',
                            timeRemaining: 0,
                            isPaused: false,
                            totalCycles: this.totalCycles,
                            currentCycle: 0,
                            currentMode: this.currentMode,
                            sessionCount: 0,
                            lastUpdate: Date.now()
                        }
                    }).catch(() => {});
                    
                    // Stop website blocking when timer stops
                    if (typeof removeAllBlockingRules === 'function') {
                        removeAllBlockingRules().catch(err => console.error('Failed to stop blocking:', err));
                    }
                    
                    console.log('Pomodoro: Timer fully stopped and cleaned up');
                    return true;
                },
                
                complete() {
                    console.log('Pomodoro: Timer completed, current state:', this.currentState);
                    const sessionType = this.currentState;
                    
                    // Use centralized cleanup method
                    this.clearCountdown();
                    
                    // Stop chronometer for transition
                    this.stopChronometer();
                    
                    // Flash badge and play sound
                    if (pomodoroBadgeManager) {
                        pomodoroBadgeManager.flashBadge();
                    }
                    
                    if (sessionType === 'work') {
                        // Work session completed
                        console.log('Pomodoro: Work session completed, mode:', this.currentMode);
                        playSTMNotificationSound('workComplete');
                        
                        // Stop website blocking during break
                        if (typeof removeAllBlockingRules === 'function') {
                            removeAllBlockingRules().catch(err => console.error('Failed to stop blocking:', err));
                        }
                        
                        
                        // Determine break type (short or long) for standard mode
                        this.sessionCount = (this.sessionCount || 0) + 1;
                        const isLongBreak = this.currentMode === 'standard' && this.sessionCount % 4 === 0;
                        
                        // Start break session
                        this.currentState = isLongBreak ? 'long_break' : 'short_break';
                        this.timeRemaining = isLongBreak ? (E / 1000) : (S / 1000); // Use loaded break durations
                        this.isPaused = false;
                        
                        console.log(`Pomodoro: Starting ${isLongBreak ? 'long' : 'short'} break (${this.timeRemaining}s)`);
                        
                        // Show break notification
                        const breakType = isLongBreak ? 'Long Break' : 'Quick Break';
                        const breakMessage = isLongBreak 
                            ? 'Fantastic work session! Time for a proper recharge!' 
                            : 'Great job! Take a short break and stretch.';
                        this.showPomodoroNotification(`${breakType} Time!`, breakMessage);
                        
                        // Use centralized countdown method for break
                        this.startCountdown();
                        
                        // Start chronometer for break period (will check chronometerOnBreak setting)
                        this.startChronometer(true, 'timer_action');
                        
                    } else {
                        // Break completed - check if cycle is finished
                        console.log('Pomodoro: Break completed');
                        playSTMNotificationSound('breakEnd');
                        
                        // Check for cycle completion
                        const isLongBreak = this.currentState === 'long_break';
                        
                        // Cycle completion logic:
                        // Increment cycle after every break completion (not just long breaks)
                        if (this.totalCycles !== 'unlimited') {
                            // Increment cycle after every break
                            this.currentCycle++;
                            console.log(`Pomodoro: Completed break, advancing to cycle ${this.currentCycle}/${this.totalCycles}`);
                            
                            // Check if this was the last cycle
                            if (this.currentCycle > parseInt(this.totalCycles)) {
                                // All cycles completed - go to idle
                                console.log('Pomodoro: All cycles completed!');
                                this.currentState = 'idle';
                                this.timeRemaining = 0;
                                this.isPaused = false;
                                this.currentCycle = 0;
                                
                                // Clear badge
                                if (pomodoroBadgeManager) {
                                    pomodoroBadgeManager.clearBadge();
                                }
                                
                                // Show completion notification
                                chrome.notifications.create({
                                    type: 'basic',
                                    iconUrl: chrome.runtime.getURL('images/icon128.png'),
                                    title: 'All Cycles Complete!',
                                    message: `Congratulations! You completed all ${this.totalCycles} cycles.`,
                                    priority: 2,
                                    requireInteraction: true
                                });
                                return;
                            }
                        }
                        
                        // Continue with next work session (either new cycle or continuing)
                        this.currentState = 'work';
                        this.timeRemaining = l / 1000; // Use work duration
                        this.isPaused = false;
                        
                        // Show work session notification
                        this.showPomodoroNotification('Focus Time!', 'Break is over. Let\'s get back to focused work!');
                        
                        // Use centralized countdown method for work session
                        this.startCountdown();
                        
                        // Start chronometer for work period after delay to allow End of Break sound to complete
                        const endBreakSoundDuration = getNotificationSoundDuration(pomodoroAudioSettings.endBreakSound);
                        console.log(`STM: Waiting ${endBreakSoundDuration}ms for End of Break sound "${pomodoroAudioSettings.endBreakSound}" to complete`);
                        setTimeout(() => {
                            this.startChronometer(false, 'timer_action');
                        }, endBreakSoundDuration); // Exact duration for End of Break sound
                        
                        // Start website blocking for work sessions
                        if (typeof updateBlockingRules === 'function') {
                            updateBlockingRules().catch(err => console.error('Failed to start blocking:', err));
                        }
                    }
                    
                    // Store final state with complete session tracking
                    chrome.storage.local.set({
                        pomodoroTimerState: {
                            currentState: this.currentState,
                            timeRemaining: this.timeRemaining,
                            isPaused: this.isPaused,
                            totalCycles: this.totalCycles,
                            currentCycle: this.currentCycle,
                            currentMode: this.currentMode,
                            sessionCount: this.sessionCount,
                            lastUpdate: Date.now()
                        }
                    }).catch(() => {});
                },
                
                // Simple browser notification system
                async showPomodoroNotification(title, message) {
                    try {
                        const result = await chrome.storage.local.get(['gmbExtractorSettings']);
                        const settings = result.gmbExtractorSettings || {};
                        
                        // Check if notifications are enabled
                        if (settings.pomodoroCompletionNotifications !== false) {
                            const notificationId = `pomodoro-notif-${Date.now()}`;
                            chrome.notifications.create(notificationId, {
                                type: 'basic',
                                iconUrl: chrome.runtime.getURL('images/icon128.png'),
                                title: title,
                                message: message,
                                priority: 1,
                                requireInteraction: false
                            });
                            
                            // Auto-clear notification after 10 seconds
                            setTimeout(() => {
                                chrome.notifications.clear(notificationId);
                            }, 10000);
                        }
                    } catch (error) {
                        console.error('Error showing Pomodoro notification:', error);
                    }
                },
                
                // Fallback notification method using Chrome notifications API
                async showFallbackNotification(title, message) {
                    try {
                        if (chrome.notifications) {
                            const notificationId = `pomodoro-${Date.now()}`;
                            chrome.notifications.create(notificationId, {
                                type: 'basic',
                                iconUrl: chrome.runtime.getURL('images/icon128.png'),
                                title: title,
                                message: message,
                                priority: 1,
                                requireInteraction: false
                            });
                            
                            setTimeout(() => {
                                chrome.notifications.clear(notificationId);
                            }, 10000);
                        }
                    } catch (error) {
                        console.error('Error showing fallback notification:', error);
                    }
                },
                
                getState() {
                    const state = {
                        currentState: this.currentState,
                        timeRemaining: this.timeRemaining,
                        isPaused: this.isPaused,
                        totalCycles: this.totalCycles,
                        currentCycle: this.currentCycle,
                        currentMode: this.currentMode,
                        sessionCount: this.sessionCount
                    };
                    // Removed console.log to reduce spam - state logging available in debug mode
                    return state;
                },
                
                // STM chronometer methods - simple wrappers (no local interval management)
                async startChronometer(isBreakPeriod = false, callSource = 'timer_action') {
                    console.log('🎯 STM Timer: Starting chronometer, isBreakPeriod:', isBreakPeriod, 'callSource:', callSource);
                    return await startSTMChronometer(isBreakPeriod, false, callSource);
                },
                
                async stopChronometer() {
                    console.log('🛑 STM Timer: Stopping chronometer');
                    return await stopSTMChronometer();
                }
            };
            
            // Initialize STM audio system
            try {
                console.log('STM: Initializing audio system...');
                await loadSTMAudioSettings();
                console.log('STM: Audio system ready');
            } catch (error) {
                console.error('STM: Error initializing audio system:', error);
            }
            
            // Initialize blocking system directly (no imports in service workers)
            try {
                // Define blocking functions directly in background script (Grande pattern)
                const getBlockedSites = async () => {
                    const { blockedSites: e } = await chrome.storage.local.get("blockedSites");
                    return e;
                };
                
                const getAllowedUrls = async () => {
                    const { allowedUrls: e } = await chrome.storage.local.get("allowedUrls");
                    return e;
                };
                
                const getCurrentRuleIds = async () => {
                    try {
                        return (await chrome.declarativeNetRequest.getDynamicRules()).map((s) => s.id);
                    } catch (e) {
                        return console.error("Error getting existing rules:", e), [];
                    }
                };
                
                updateBlockingRules = async () => {
                    let e = 1;
                    const l = ((await getAllowedUrls()) || []).map((t) => ({ id: e++, priority: 2, action: { type: "allow" }, condition: { urlFilter: t, resourceTypes: ["main_frame"] } })),
                        r = ((await getBlockedSites()) || []).map((t) => ({ id: e++, priority: 1, action: { type: "redirect", redirect: { url: chrome.runtime.getURL("blocked.html") } }, condition: { urlFilter: `||${t}/`, resourceTypes: ["main_frame"] } })),
                        o = [...l, ...r];
                    try {
                        const t = await getCurrentRuleIds();
                        await chrome.declarativeNetRequest.updateDynamicRules({ removeRuleIds: t, addRules: o });
                    } catch (t) {
                        console.error("Error updating rules:", t);
                    }
                };
                
                removeAllBlockingRules = async () => {
                    const e = await getCurrentRuleIds();
                    await chrome.declarativeNetRequest.updateDynamicRules({ removeRuleIds: e });
                };
                
                // Create simple blocking system
                pomodoroBlockingSystem = {
                    isActive: false,
                    
                    async startBlocking(mode = 'standard') {
                        try {
                            console.log('Pomodoro: Starting website blocking in', mode, 'mode using Grande pattern');
                            await updateBlockingRules();
                            this.isActive = true;
                            return true;
                        } catch (error) {
                            console.error('Pomodoro: Error starting blocking:', error);
                            return false;
                        }
                    },
                    
                    async stopBlocking() {
                        try {
                            console.log('Pomodoro: Stopping website blocking using Grande pattern');
                            await removeAllBlockingRules();
                            this.isActive = false;
                            return true;
                        } catch (error) {
                            console.error('Pomodoro: Error stopping blocking:', error);
                            return false;
                        }
                    }
                };
                
                console.log('Pomodoro: Blocking system initialized directly in background');
            } catch (error) {
                console.error('Pomodoro: Error initializing blocking system:', error);
                // Fallback to placeholder
                pomodoroBlockingSystem = {
                    isActive: false,
                    async startBlocking() { return false; },
                    async stopBlocking() { return false; }
                };
            }
            
            pomodoroBadgeManager = {
                heartbeatCounter: 0, // For visual heartbeat indicator
                
                updateBadge(timeRemaining, currentState, isPaused = false) {
                    if (currentState === 'idle') {
                        this.clearBadge();
                        return;
                    }
                    
                    // Enhanced time formatting inspired by Timer app
                    let badgeText = '';
                    const days = Math.floor(timeRemaining / (24 * 60 * 60));
                    const hours = Math.floor((timeRemaining % (24 * 60 * 60)) / (60 * 60));
                    const minutes = Math.floor((timeRemaining % (60 * 60)) / 60);
                    const seconds = timeRemaining % 60;
                    
                    // Smart formatting based on time remaining
                    if (days > 0) {
                        badgeText = `${days}d${this.getHeartbeatIndicator()}`;
                    } else if (hours > 0) {
                        badgeText = `${hours}h${this.getHeartbeatIndicator()}`;
                    } else if (minutes > 0) {
                        // For times under 1 hour, show minutes with heartbeat
                        badgeText = `${minutes}m${this.getHeartbeatIndicator()}`;
                    } else {
                        // Last minute: show seconds
                        badgeText = `${seconds}s`;
                    }
                    
                    chrome.action.setBadgeText({ text: badgeText });
                    
                    // Color based on state - same as before
                    let color = '#7C3AED'; // Purple for work
                    if (isPaused) {
                        color = '#ef4444'; // Red for paused
                    } else if (currentState === 'short_break' || currentState === 'long_break') {
                        color = '#22c55e'; // Green for breaks
                    }
                    
                    chrome.action.setBadgeBackgroundColor({ color });
                    
                    // Increment heartbeat counter
                    this.heartbeatCounter++;
                },
                
                getHeartbeatIndicator() {
                    // Alternate between : and . for visual heartbeat
                    return (this.heartbeatCounter % 2 === 0) ? ':' : '.';
                },
                
                flashBadge() {
                    console.log('Pomodoro: Flashing badge');
                    chrome.action.setBadgeText({ text: '✓' });
                    chrome.action.setBadgeBackgroundColor({ color: '#22c55e' });
                    
                    setTimeout(() => {
                        this.clearBadge();
                    }, 3000);
                },
                
                clearBadge() {
                    chrome.action.setBadgeText({ text: '' });
                    this.heartbeatCounter = 0; // Reset heartbeat counter
                }
            };
            
            console.log('Pomodoro components initialized successfully');
            
            // Use centralized timer restoration function instead of duplicate logic
            await restoreTimerFromStorage();
            
            return true;
        } catch (error) {
            console.error('Failed to initialize Pomodoro components:', error);
            return false;
        }
    }
    return true;
}

// Handle runtime messages for global shortcuts
console.log('📋 Background: Setting up message listener...');
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // Only log non-frequent messages to reduce console spam
    const frequentActions = ['pomodoroGetState', 'chronometerHeartbeat'];
    if (!frequentActions.includes(message.action)) {
        console.log('🔔 Background: Message received:', message.action, 'from', sender.tab ? 'tab' : 'popup');
    }
    
    // Handle ping messages for health checks
    if (message.action === 'ping') {
        console.log('🏓 Background: Responding to ping');
        sendResponse({ success: true, ready: true });
        return true;
    }
    
    // EMERGENCY RESET SYSTEM: Handle emergency reset requests
    if (message.action === 'emergencyReset') {
        console.log('🚨 Background: Emergency reset requested');
        
        (async () => {
            try {
                // TARGETED RESET: Only clear the specific corrupted location keys
                console.log('🚨 Background: Performing surgical reset of corrupted location data...');
                
                // First check if settings object itself is corrupted
                const currentData = await chrome.storage.local.get(['settings']);
                if (currentData.settings) {
                    console.log('🔍 Background: Found existing settings, checking for corruption...');
                    
                    // Check if settings has corrupted location data (like hardcoded fallbacks)
                    const corruptedSettings = { ...currentData.settings };
                    
                    // Remove only the corrupted location properties from settings
                    if (corruptedSettings.latitude || corruptedSettings.longitude) {
                        console.log('🚨 Background: Removing corrupted coordinates from settings');
                        delete corruptedSettings.latitude;
                        delete corruptedSettings.longitude;
                        delete corruptedSettings.location;
                        delete corruptedSettings.name;
                        delete corruptedSettings.placeId;
                        // Keep enabled, hl, gl, regions if they exist
                        
                        // Save cleaned settings back
                        await chrome.storage.local.set({ settings: corruptedSettings });
                        console.log('🚨 Background: Cleaned settings saved, coordinates removed');
                    }
                } else {
                    console.log('🔍 Background: No settings object found, will remain empty for user control');
                }
                
                // Define other corrupted location-related keys to clear
                const corruptedKeys = [
                    'locationChangerFavorites',
                    'locationChangerSettings', 
                    'locationData',
                    'spoofingSettings',
                    'geolocationSettings',
                    'backgroundSettings',
                    'locationSpooferState',
                    'locationFavorites',
                    'location_favorites',
                    'location_settings'
                ];
                
                console.log('🚨 Background: Clearing corrupted location keys:', corruptedKeys);
                
                // Remove only the corrupted keys from both storage types
                await chrome.storage.local.remove(corruptedKeys);
                await chrome.storage.sync.remove(corruptedKeys);
                
                // PRESERVE EVERYTHING ELSE - Get current data to verify preservation
                const allData = await chrome.storage.local.get(null);
                const preservedKeys = Object.keys(allData);
                console.log('🛡️ Background: Preserved', preservedKeys.length, 'storage keys including:');
                console.log('   ✅ pomodoroTodos, pomodoroAudioSettings, chronometerState');
                console.log('   ✅ settings, gmbExtractorSettings, quickActionSettings');
                console.log('   ✅ debugMode, bulkLinkOpenSettings, whitelists');
                console.log('   ✅ All other extension data');
                
                // Reset declarativeNetRequest rules (location spoofing rules only)
                console.log('🚨 Background: Resetting location spoofing network rules...');
                try {
                    await chrome.declarativeNetRequest.updateSessionRules({
                        removeRuleIds: [1, 2, 3] // These are typically location spoofing rule IDs
                    });
                } catch (netRequestError) {
                    console.warn('🚨 Background: Network rules may not exist (this is fine):', netRequestError);
                }
                
                // Clear and rebuild context menus (this will remove location duplicates)
                console.log('🚨 Background: Rebuilding context menus to remove duplicates...');
                await chrome.contextMenus.removeAll();
                
                // Reset location favorites manager specifically
                if (typeof locationFavoritesManager !== 'undefined' && locationFavoritesManager) {
                    console.log('🚨 Background: Resetting location favorites manager...');
                    locationFavoritesManager.clearFavoriteMenus();
                    locationFavoritesManager.favorites = [];
                    locationFavoritesManager.contextMenuIds.clear();
                }
                
                // Add storage inspection to identify what's corrupted
                console.log('🚨 Background: Inspecting current storage for corruption...');
                const currentStorage = await chrome.storage.local.get(null);
                console.log('🔍 Background: Current storage keys:', Object.keys(currentStorage));
                
                if (currentStorage.settings) {
                    console.log('🔍 Background: Current settings object:', currentStorage.settings);
                } else {
                    console.log('🔍 Background: No settings object found in storage');
                }
                
                console.log('✅ Background: Surgical reset completed - corruption cleared, ready for user location setup');
                
                // Get final storage state for reporting
                const finalStorage = await chrome.storage.local.get(null);
                const finalKeys = Object.keys(finalStorage);
                
                sendResponse({ 
                    success: true, 
                    message: 'STEP 1 COMPLETE: Location corruption cleared. STEP 2: RESTART YOUR BROWSER to complete reset, then set location via popup.',
                    preservedKeys: finalKeys.length,
                    instruction: 'CRITICAL: Restart Chrome/browser completely, then click extension popup to set your location'
                });
                
                // Reload extension after short delay to ensure response is sent
                setTimeout(() => {
                    chrome.runtime.reload();
                }, 500);
                
            } catch (error) {
                console.error('🚨 Background: Emergency reset failed:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    // SETTINGS LOCKDOWN SYSTEM: Handle manual lockdown control
    if (message.action === 'activateSettingsLockdown') {
        console.log('🔒 Background: Manual settings lockdown activation requested');
        
        (async () => {
            try {
                await backupTimerValues();
                activateSettingsLockdown(message.windowId);
                sendResponse({ success: true, lockdownActive: true });
            } catch (error) {
                console.error('❌ Background: Error activating settings lockdown:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'deactivateSettingsLockdown') {
        console.log('🔓 Background: Manual settings lockdown deactivation requested');
        
        (async () => {
            try {
                deactivateSettingsLockdown();
                await restoreTimerValues();
                sendResponse({ success: true, lockdownActive: false });
            } catch (error) {
                console.error('❌ Background: Error deactivating settings lockdown:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'checkSettingsLockdown') {
        sendResponse({ 
            success: true, 
            lockdownActive: isSettingsLockdownActive(),
            windowId: settingsPopupWindowId 
        });
        return true;
    }
    
    // Handle new simple screenshot capture and editor opening
    if (message.action === 'openScreenshotEditor') {
        console.log('Background: Starting screenshot capture for editor...');
        
        try {
            chrome.tabs.captureVisibleTab(null, { format: 'png' }, async (dataUrl) => {
                if (chrome.runtime.lastError) {
                    console.error('Background: Screenshot capture failed:', chrome.runtime.lastError.message);
                    sendResponse({ success: false, error: chrome.runtime.lastError.message });
                } else {
                    console.log('Background: Screenshot captured, opening editor...');
                    
                    try {
                        // Store screenshot data temporarily
                        await chrome.storage.local.set({ screenshotData: dataUrl });
                        
                        // Open screenshot editor in new tab
                        const editorTab = await chrome.tabs.create({
                            url: chrome.runtime.getURL('screenshot-editor.html'),
                            active: true
                        });
                        
                        console.log('Background: Screenshot editor opened in tab:', editorTab.id);
                        sendResponse({ success: true, tabId: editorTab.id });
                    } catch (error) {
                        console.error('Background: Error opening editor:', error);
                        sendResponse({ success: false, error: 'Failed to open editor: ' + error.message });
                    }
                }
            });
        } catch (error) {
            console.error('Background: Exception in screenshot capture:', error);
            sendResponse({ success: false, error: 'Screenshot capture failed: ' + error.message });
        }
        
        return true; // Keep message channel open for async response
    }
    
    // Handle area selection and cropping with fixed async handling
    if (message.action === 'captureSelectedArea') {
        console.log('Background: Processing selected area:', message.selection);
        
        // Use simpler async pattern to avoid message port issues
        (async () => {
            try {
                // Capture full screenshot first
                const fullScreenshotData = await captureFullScreenshot();
                
                if (fullScreenshotData) {
                    // Crop to selected area
                    const croppedData = await cropImageToArea(fullScreenshotData, message.selection);
                    
                    // Store for editor
                    await chrome.storage.local.set({ screenshotData: croppedData });
                    
                    // Open editor
                    const editorTab = await chrome.tabs.create({
                        url: chrome.runtime.getURL('screenshot-editor.html'),
                        active: true
                    });
                    
                    console.log('Background: Success! Editor opened:', editorTab.id);
                    sendResponse({ success: true, tabId: editorTab.id });
                } else {
                    console.error('Background: Failed to capture screenshot');
                    sendResponse({ success: false, error: 'Failed to capture screenshot' });
                }
                
            } catch (error) {
                console.error('Background: Error in area processing:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        
        return true; // Keep message channel open for async response
    }
    
    // HANDY SCREENSHOT APPROACH: Handle area selection capture
    if (message.action === 'CAPTURE_SELECTION') {
        console.log('Background: CAPTURE_SELECTION message received!', {
            rect: message.rect,
            devicePixelRatio: message.devicePixelRatio,
            sender: sender.tab ? `tab ${sender.tab.id}` : 'popup'
        });
        
        // FIXED: Use proper Manifest V3 async message handling
        (async () => {
            try {
                const result = await handleCaptureSelection(message);
                console.log('Background: Sending response:', result);
                sendResponse(result);
            } catch (error) {
                console.error('Background: Error in handleCaptureSelection:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        
        return true; // Keep message channel open for async response
    }
    
    // Handle legacy screenshot capture (for fallback)
    if (message.action === 'captureVisibleTab') {
        return new Promise((resolve) => {
            // Add timeout to prevent hanging
            const timeoutId = setTimeout(() => {
                console.error('Background: Screenshot capture timeout after 10 seconds');
                resolve({ error: 'Screenshot capture timeout - operation took too long' });
            }, 10000); // 10 second timeout
            
            try {
                chrome.tabs.captureVisibleTab(null, { format: 'png' }, (dataUrl) => {
                    clearTimeout(timeoutId);
                    
                    if (chrome.runtime.lastError) {
                        const errorMsg = chrome.runtime.lastError.message;
                        console.error('Background: Screenshot capture failed:', errorMsg);
                        
                        // Provide more specific error messages
                        let userFriendlyError = errorMsg;
                        if (errorMsg.includes('permission')) {
                            userFriendlyError = 'Screenshot permission denied - please reload the extension';
                        } else if (errorMsg.includes('tab')) {
                            userFriendlyError = 'Cannot capture tab - please try again';
                        }
                        
                        resolve({ error: userFriendlyError, originalError: errorMsg });
                    } else {
                        console.log('Background: Screenshot captured successfully');
                        resolve({ dataUrl: dataUrl });
                    }
                });
            } catch (error) {
                clearTimeout(timeoutId);
                console.error('Background: Screenshot capture exception:', error);
                resolve({ error: 'Screenshot capture failed: ' + error.message });
            }
        });
    }
    
    // Handle chronometer preview controls
    if (message.action === 'pauseChronometer') {
        console.log('STM: Pausing chronometer for preview');
        if (grandeChrometerInterval) {
            clearInterval(grandeChrometerInterval);
            chronometerWasPaused = true; // Track if we paused it
        }
        sendResponse({ success: true });
        return true;
    }
    
    if (message.action === 'resumeChronometer') {
        console.log('STM: Resuming chronometer after preview');
        if (chronometerWasPaused && pomodoroTimer && pomodoroTimer.currentState !== 'idle' && !pomodoroTimer.isPaused) {
            // Only resume if timer is still running
            const isBreakPeriod = pomodoroTimer.currentState === 'short_break' || pomodoroTimer.currentState === 'long_break';
            startSTMChronometer(isBreakPeriod);
            chronometerWasPaused = false;
        }
        sendResponse({ success: true });
        return true;
    }
    
    // Handle Pomodoro messages - SIMPLE PATTERN FROM WORKING COMMIT
    if (message.action === 'pomodoroStart') {
        (async () => {
            try {
                console.log('🚀 Background: Starting pomodoroStart handler');
                console.log('🎯 Background: Starting timer with current time preset');
                
                // Initialize if needed
                if (!pomodoroTimer || !pomodoroBadgeManager) {
                    const initialized = await initializePomodoroComponents();
                    if (!initialized) {
                        sendResponse({ success: false, error: 'Failed to initialize' });
                        return;
                    }
                }
                
                const started = await pomodoroTimer.start();
                console.log('⏱️ Background: Timer start result:', started);
                
                if (started) {
                    // Start chronometer
                    const timerState = pomodoroTimer.getState();
                    const isBreakPeriod = timerState.currentState === 'short_break' || timerState.currentState === 'long_break';
                    await pomodoroTimer.startChronometer(isBreakPeriod, 'user_action');
                    
                    // Start blocking if enabled
                    const settings = await chrome.storage.local.get(['gmbExtractorSettings']);
                    const pomodoroSettings = settings.gmbExtractorSettings || {};
                    if (pomodoroSettings.pomodoroBlockingEnabled !== false) {
                        await pomodoroBlockingSystem.startBlocking('standard');
                    }
                }
                
                sendResponse({ success: started });
            } catch (error) {
                console.error('❌ Background: Error in pomodoroStart:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'pomodoroStop') {
        (async () => {
            try {
                console.log('🛑 Background: Starting pomodoroStop handler');
                
                // Initialize if needed
                if (!pomodoroTimer || !pomodoroBadgeManager) {
                    const initialized = await initializePomodoroComponents();
                    if (!initialized) {
                        sendResponse({ success: false, error: 'Failed to initialize' });
                        return;
                    }
                }
                
                const stopped = await pomodoroTimer.stop();
                console.log('⏹️ Background: Timer stop result:', stopped);
                
                if (stopped) {
                    // Stop chronometer
                    await pomodoroTimer.stopChronometer();
                    // Stop blocking
                    await pomodoroBlockingSystem.stopBlocking();
                    // Clear badge
                    pomodoroBadgeManager.clearBadge();
                }
                
                sendResponse({ success: stopped });
            } catch (error) {
                console.error('❌ Background: Error in pomodoroStop:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    // EMERGENCY FORCE STOP: Complete timer shutdown and state reset
    if (message.action === 'pomodoroForceStop') {
        (async () => {
            try {
                console.log('🚨 Background: FORCE STOP - Emergency timer shutdown requested');
                
                // Force stop timer even if not initialized
                if (pomodoroTimer) {
                    try {
                        await pomodoroTimer.stop();
                        console.log('🚨 Background: Timer force stopped');
                    } catch (error) {
                        console.log('🚨 Background: Timer force stop failed (already stopped?):', error.message);
                    }
                }
                
                // Force stop chronometer
                try {
                    await pomodoroTimer?.stopChronometer();
                    console.log('🚨 Background: Chronometer force stopped');
                } catch (error) {
                    console.log('🚨 Background: Chronometer force stop failed:', error.message);
                }
                
                // Force stop blocking
                try {
                    await pomodoroBlockingSystem?.stopBlocking();
                    console.log('🚨 Background: Blocking force stopped');
                } catch (error) {
                    console.log('🚨 Background: Blocking force stop failed:', error.message);
                }
                
                // Force clear badge
                try {
                    pomodoroBadgeManager?.clearBadge();
                    console.log('🚨 Background: Badge force cleared');
                } catch (error) {
                    console.log('🚨 Background: Badge force clear failed:', error.message);
                }
                
                // Force reset storage state to idle
                try {
                    await chrome.storage.local.set({
                        pomodoroTimerState: {
                            currentState: 'idle',
                            isPaused: false,
                            timeRemaining: 0,
                            totalTime: 0,
                            mode: 'standard',
                            currentCycle: 1,
                            isBreakPeriod: false,
                            startTime: null,
                            endTime: null
                        }
                    });
                    console.log('🚨 Background: Storage state force reset to idle');
                } catch (error) {
                    console.log('🚨 Background: Storage force reset failed:', error.message);
                }
                
                sendResponse({ success: true, message: 'Force stop completed' });
            } catch (error) {
                console.error('❌ Background: Error in pomodoroForceStop:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    // REMOVED: pomodoroToggle handler to prevent conflicts with unified timer control
    // Toggle functionality now handled by pomodoroStart/pomodoroStop in popup.js
    
    if (message.action === 'pomodoroGetState') {
        (async () => {
            try {
                // Initialize if needed
                if (!pomodoroTimer) {
                    const initialized = await initializePomodoroComponents();
                    if (!initialized) {
                        sendResponse({ success: false, error: 'Failed to initialize' });
                        return;
                    }
                }
                
                const state = pomodoroTimer.getState();
                sendResponse({ success: true, state: state });
            } catch (error) {
                console.error('❌ Background: Error in pomodoroGetState:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'pomodoroPause') {
        (async () => {
            try {
                // Initialize if needed
                if (!pomodoroTimer) {
                    const initialized = await initializePomodoroComponents();
                    if (!initialized) {
                        sendResponse({ success: false, error: 'Failed to initialize' });
                        return;
                    }
                }
                
                const paused = await pomodoroTimer.pause();
                if (paused) {
                    await pomodoroTimer.stopChronometer();
                    
                    // Stop blocking when timer is paused to allow access to blacklisted sites
                    if (pomodoroBlockingSystem && pomodoroBlockingSystem.isActive) {
                        console.log('🍅 Pomodoro: Timer paused - temporarily stopping website blocking');
                        await pomodoroBlockingSystem.stopBlocking();
                    }
                }
                sendResponse({ success: paused });
            } catch (error) {
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'pomodoroResume') {
        (async () => {
            try {
                // Initialize if needed
                if (!pomodoroTimer) {
                    const initialized = await initializePomodoroComponents();
                    if (!initialized) {
                        sendResponse({ success: false, error: 'Failed to initialize' });
                        return;
                    }
                }
                
                const resumed = await pomodoroTimer.resume();
                if (resumed) {
                    const timerState = pomodoroTimer.getState();
                    const isBreakPeriod = timerState.currentState === 'short_break' || timerState.currentState === 'long_break';
                    await pomodoroTimer.startChronometer(isBreakPeriod, 'user_action');
                    
                    // Restart blocking when timer is resumed, but only if blocking is enabled in settings
                    const settings = await chrome.storage.local.get(['gmbExtractorSettings']);
                    const pomodoroSettings = settings.gmbExtractorSettings || {};
                    if (pomodoroSettings.pomodoroBlockingEnabled !== false && pomodoroBlockingSystem) {
                        console.log('🍅 Pomodoro: Timer resumed - restarting website blocking');
                        await pomodoroBlockingSystem.startBlocking('standard');
                    }
                }
                sendResponse({ success: resumed });
            } catch (error) {
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'pomodoroSnooze') {
        (async () => {
            try {
                console.log('Pomodoro: Snooze requested for', message.minutes, 'minutes');
                
                // Initialize if needed
                if (!pomodoroTimer) {
                    const initialized = await initializePomodoroComponents();
                    if (!initialized) {
                        sendResponse({ success: false, error: 'Failed to initialize' });
                        return;
                    }
                }
                
                // Stop current timer if running
                if (pomodoroTimer.currentState !== 'idle') {
                    await pomodoroTimer.stop();
                }
                
                // Set a timeout to restart after snooze period
                const snoozeMs = message.minutes * 60 * 1000;
                setTimeout(async () => {
                    // Check if timer is still idle (user didn't manually start)
                    if (pomodoroTimer.currentState === 'idle') {
                        console.log('Pomodoro: Snooze complete, restarting timer');
                        
                        // Determine next session type
                        const nextSessionType = message.sessionType === 'work' ? 'break' : 'work';
                        
                        if (nextSessionType === 'work') {
                            await pomodoroTimer.start(pomodoroTimer.currentMode || 'standard');
                        } else {
                            // For break, manually set up break state
                            pomodoroTimer.currentState = 'short_break';
                            pomodoroTimer.timeRemaining = 5 * 60; // 5 minute break default
                            pomodoroTimer.isPaused = false;
                            
                            // Start countdown
                            pomodoroTimer.interval = setInterval(() => {
                                pomodoroTimer.timeRemaining--;
                                if (pomodoroTimer.timeRemaining <= 0) {
                                    pomodoroTimer.complete();
                                }
                                if (pomodoroBadgeManager) {
                                    pomodoroBadgeManager.updateBadge(pomodoroTimer.timeRemaining, pomodoroTimer.currentState, pomodoroTimer.isPaused);
                                }
                            }, 1000);
                            
                            // Start chronometer for break
                            await pomodoroTimer.startChronometer(true, 'user_action');
                        }
                    }
                }, snoozeMs);
                
                sendResponse({ success: true });
            } catch (error) {
                console.error('Error handling Pomodoro snooze:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'getPomodoroState') {
        (async () => {
            try {
                // Initialize if needed to get state
                if (!pomodoroTimer) {
                    const initialized = await initializePomodoroComponents();
                    if (!initialized) {
                        sendResponse({ success: false, error: 'Failed to initialize' });
                        return;
                    }
                }
                
                const state = pomodoroTimer.getState();
                sendResponse({
                    success: true,
                    state: state.currentState,
                    isPaused: state.isPaused,
                    timeRemaining: state.timeRemaining
                });
            } catch (error) {
                console.error('Pomodoro: Error getting state:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'pomodoroPlayNotification') {
        (async () => {
            try {
                await playSTMNotificationSound(message.soundType);
                sendResponse({ success: true });
            } catch (error) {
                console.error('STM: Error playing notification sound:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'pomodoroNotificationTest') {
        (async () => {
            try {
                await playSTMNotificationSound(message.type);
                
                // Also show browser notification
                const title = message.type === 'workComplete' ? 'Work Complete!' : 'Break Over!';
                const messageText = message.type === 'workComplete' ? 'Time for a break!' : 'Back to work!';
                
                chrome.notifications.create({
                    type: 'basic',
                    iconUrl: chrome.runtime.getURL('images/icon128.png'),
                    title,
                    message: messageText,
                    priority: 2
                });
                
                sendResponse({ success: true });
            } catch (error) {
                console.error('Error in notification test:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'adjustPomodoroVolume') {
        try {
            console.log('Background: Adjusting Pomodoro volume immediately:', message);
            
            // Send separate messages for each volume type
            if (message.notificationVolume !== undefined) {
                chrome.runtime.sendMessage({
                    action: 'adjust-notification-volume',
                    notificationVolume: message.notificationVolume
                });
            }
            
            if (message.chronometerVolume !== undefined) {
                chrome.runtime.sendMessage({
                    action: 'adjust-volume',
                    musicVolume: message.chronometerVolume
                });
            }
            
            sendResponse({ success: true });
        } catch (error) {
            console.error('Background: Error adjusting volume:', error);
            sendResponse({ success: false, error: error.message });
        }
        return true;
    }
    
    if (message.action === 'playPomodoroPreview') {
        (async () => {
            try {
                console.log('Background: Playing Pomodoro preview:', message);
                
                // Ensure offscreen document exists
                const offscreenReady = await ensureSTMOffscreenDocument();
                if (!offscreenReady) {
                    sendResponse({ success: false, error: 'Could not create offscreen document' });
                    return;
                }
                
                // Get audio URL
                const audioUrl = getSTMAudioUrl(message.soundName, message.isTickingSound);
                if (!audioUrl) {
                    sendResponse({ success: false, error: 'Sound not found' });
                    return;
                }
                
                // Use direct STM messaging
                chrome.runtime.sendMessage({
                    action: 'playSound',
                    selectedSound: audioUrl,
                    soundVolume: message.volume || 0.7,
                    isSoundEnabled: true
                });
                
                sendResponse({ success: true });
            } catch (error) {
                console.error('Background: Error playing preview:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'stopPreview') {
        (async () => {
            try {
                console.log('Background: Stopping preview');
                
                // Ensure offscreen document exists
                const offscreenReady = await ensureSTMOffscreenDocument();
                if (!offscreenReady) {
                    sendResponse({ success: false, error: 'Could not create offscreen document' });
                    return;
                }
                
                // Send stop message to offscreen document
                chrome.runtime.sendMessage({
                    action: 'stopMusic'
                });
                
                sendResponse({ success: true });
            } catch (error) {
                console.error('Background: Error stopping preview:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'pomodoroUpdateCycles') {
        (async () => {
            try {
                if (pomodoroTimer) {
                    console.log('Background: Updating cycles immediately:', message.totalCycles);
                    pomodoroTimer.totalCycles = message.totalCycles;
                    
                    // Update storage
                    const currentState = pomodoroTimer.getState();
                    await chrome.storage.local.set({ 
                        pomodoroTimerState: {
                            ...currentState,
                            totalCycles: message.totalCycles,
                            lastUpdate: Date.now()
                        }
                    });
                    
                    sendResponse({ success: true });
                } else {
                    sendResponse({ success: false, error: 'No active timer' });
                }
            } catch (error) {
                console.error('Background: Error updating cycles:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'positionNotificationWindow') {
        try {
            if (sender.tab && sender.tab.windowId) {
                const { position, screen, window } = message;
                let updateInfo = {};
                
                // Use fallback screen dimensions
                const fallbackScreenWidth = 1920;
                const fallbackScreenHeight = 1080;
                
                if (position === 'center') {
                    updateInfo.left = Math.round((fallbackScreenWidth - window.width) / 2);
                    updateInfo.top = Math.round((fallbackScreenHeight - window.height) / 2);
                } else if (position === 'br') {
                    updateInfo.left = fallbackScreenWidth - window.width - 20;
                    updateInfo.top = fallbackScreenHeight - window.height - 60;
                } else if (position === 'tr') {
                    updateInfo.left = fallbackScreenWidth - window.width - 20;
                    updateInfo.top = 20;
                }
                
                chrome.windows.update(sender.tab.windowId, updateInfo);
            }
            sendResponse({ success: true });
        } catch (error) {
            console.error('Error positioning notification window:', error);
            sendResponse({ success: false, error: error.message });
        }
        return true;
    }
    
    if (message.action === 'bringPomodoroNotificationToFront') {
        try {
            if (sender.tab && sender.tab.windowId) {
                chrome.windows.update(sender.tab.windowId, {
                    focused: true
                });
                chrome.tabs.update(sender.tab.id, {
                    highlighted: true
                });
            }
            sendResponse({ success: true });
        } catch (error) {
            console.error('Error bringing notification to front:', error);
            sendResponse({ success: false, error: error.message });
        }
        return true;
    }
    
    
    if (message.action === 'pomodoroForceReloadSettings') {
        (async () => {
            try {
                console.log('🔄 Background: Force reloading Pomodoro settings...');
                
                // Ensure defaults (force during settings reload)
                await ensureTimerDefaults(true);
                
                // Reload timer settings
                if (pomodoroTimer && typeof pomodoroTimer.loadSettings === 'function') {
                    await pomodoroTimer.loadSettings();
                }
                
                // Reload badge manager settings
                if (pomodoroBadgeManager && typeof pomodoroBadgeManager.loadSettings === 'function') {
                    await pomodoroBadgeManager.loadSettings();
                }
                
                // Reload blocking system settings
                if (pomodoroBlockingSystem && typeof pomodoroBlockingSystem.loadSettings === 'function') {
                    await pomodoroBlockingSystem.loadSettings();
                }
                
                console.log('✅ Background: Pomodoro settings force reloaded');
                sendResponse({ success: true });
            } catch (error) {
                console.error('❌ Background: Error force reloading settings:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'pomodoroForceTimerRestart') {
        (async () => {
            try {
                console.log('🔄 Background: Forced timer restart requested:', message.reason);
                
                // Initialize if needed
                if (!pomodoroTimer) {
                    const initialized = await initializePomodoroComponents();
                    if (!initialized) {
                        sendResponse({ success: false, error: 'Failed to initialize timer' });
                        return;
                    }
                }
                
                // Use exact same logic as manual typing (lines 1210-1246) to restart running timer
                if (pomodoroTimer && typeof pomodoroTimer.loadSettings === 'function') {
                    // Capture current timer state before stopping (same as manual typing)
                    const wasRunning = pomodoroTimer.currentState !== 'idle';
                    const wasPaused = pomodoroTimer.isPaused;
                    const currentMode = pomodoroTimer.currentMode;
                    const wasChronometerActive = await isChronometerActive();
                    
                    if (wasRunning) {
                        console.log('🔄 STM: Timer was running - stopping to apply reset defaults');
                        // Stop everything cleanly (same as manual typing)
                        pomodoroTimer.stop();
                        console.log('🔄 STM: Duration settings reset, stopping chronometer');
                        await pomodoroTimer.stopChronometer();
                    }
                    
                    // Reload settings with new defaults (same as manual typing)
                    await pomodoroTimer.loadSettings();
                    console.log('🔄 STM: Timer configuration reloaded with reset defaults');
                    
                    // If timer was running, restart it with new settings (same as manual typing)
                    if (wasRunning && !wasPaused) {
                        console.log('🔄 STM: Restarting timer with reset defaults');
                        pomodoroTimer.start(currentMode);
                        
                        // Restart chronometer if needed (same as manual typing)
                        if (wasChronometerActive || (pomodoroTimer.settings?.pomodoroChronometerSound && !pomodoroTimer.isPaused)) {
                            console.log('🔄 STM: Restarting chronometer after reset');
                            const isBreakPeriod = pomodoroTimer.currentState === 'short_break' || pomodoroTimer.currentState === 'long_break';
                            await pomodoroTimer.startChronometer(isBreakPeriod, 'reset_defaults');
                        }
                    }
                } else {
                    // Fallback to validation if timer not available
                    await ensureTimerDefaults();
                }
                
                console.log('✅ Background: Timer restart completed for reason:', message.reason);
                sendResponse({ success: true });
            } catch (error) {
                console.error('❌ Background: Error in forced timer restart:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    if (message.action === 'pomodoroForceExtensionReload') {
        (async () => {
            try {
                console.log('🔄 Background: Force reloading extension...');
                
                // Save reload flag
                await chrome.storage.local.set({ 
                    pomodoroExtensionReloading: true,
                    pomodoroReloadTimestamp: Date.now()
                });
                
                // Small delay then reload
                setTimeout(() => {
                    chrome.runtime.reload();
                }, 100);
                
                sendResponse({ success: true });
            } catch (error) {
                console.error('❌ Background: Error reloading extension:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    }
    
    // DELETE ALL THE COMPLEX WRAPPER CODE BELOW
    
    // Handle notification requests
    if (message.action === 'showNotification') {
        console.log('Background: Received notification request:', message);
        
        // Check if notifications are enabled in settings
        chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
            const settings = result.gmbExtractorSettings || {};
            const notificationsEnabled = settings.notifications !== false; // Default to true
            
            if (notificationsEnabled && chrome.notifications) {
                // Create notification using Chrome extension API
                const notificationOptions = {
                    type: 'basic',
                    iconUrl: chrome.runtime.getURL('images/icon48.png'),
                    title: message.title || 'SEO Time Machines',
                    message: message.message || message.body || '',
                    priority: message.priority || 1
                };
                
                // Generate unique notification ID
                const notificationId = 'gmb-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                
                chrome.notifications.create(notificationId, notificationOptions, (createdId) => {
                    if (chrome.runtime.lastError) {
                        console.error('Background: Notification creation failed:', chrome.runtime.lastError);
                        sendResponse({ success: false, error: chrome.runtime.lastError.message });
                    } else {
                        console.log('Background: Notification created successfully:', createdId);
                        sendResponse({ success: true, notificationId: createdId });
                    }
                });
            } else {
                const reason = !notificationsEnabled ? 'disabled in settings' : 'API not available';
                console.log('Background: Notifications not shown -', reason);
                sendResponse({ success: false, error: `Notifications ${reason}` });
            }
        });
        
        return true; // Keep message channel open for async response
    }
    
    // Handle alert badge update requests
    if (message.action === 'updateAlertBadge') {
        chrome.storage.local.get(['activeAlerts'], (result) => {
            const activeAlerts = result.activeAlerts || [];
            const activeCount = activeAlerts.filter(alert => alert.enabled).length;
            
            // Update badge on extension icon
            if (activeCount > 0) {
                chrome.action.setBadgeText({ text: activeCount.toString() });
                chrome.action.setBadgeBackgroundColor({ color: '#ef4444' });
            } else {
                chrome.action.setBadgeText({ text: '' });
            }
            
            sendResponse({ success: true, count: activeCount });
        });
        return true;
    }
    
    
    if (message.action === 'snoozeAlert') {
        const { alertId, snoozeTime, snoozeMinutes } = message;
        
        console.log('Background: Snooze alert requested:', {
            alertId,
            snoozeTime: new Date(snoozeTime).toLocaleString(),
            snoozeMinutes
        });
        
        // Check if this is a quick timer (different storage pattern)
        if (alertId.startsWith('quick-timer-')) {
            console.log('Background: Handling quick timer snooze');
            
            (async () => {
                try {
                    // Get timer info from storage
                    const timerKey = `timer-${alertId}`;
                    console.log('Background: Looking for timer with key:', timerKey);
                    const result = await chrome.storage.local.get([timerKey]);
                const timerInfo = result[timerKey];
                console.log('Background: Timer lookup result:', { timerKey, timerInfo, allKeys: Object.keys(result) });
                
                if (timerInfo) {
                    console.log('Background: Found quick timer to reschedule:', timerInfo);
                    
                    // Clear the existing alarm
                    const wasCleared = await new Promise((resolve) => {
                        chrome.alarms.clear(alertId, (wasCleared) => {
                            console.log('Background: Cleared existing quick timer alarm:', wasCleared);
                            resolve(wasCleared);
                        });
                    });
                    
                    // Create new alarm with snoozed time
                    await new Promise((resolve) => {
                        chrome.alarms.create(alertId, {
                            when: snoozeTime
                        }, () => {
                            console.log('Background: Created new quick timer alarm for:', new Date(snoozeTime).toLocaleString());
                            resolve();
                        });
                    });
                    
                    // Update timer info with new end time
                    const updatedTimerInfo = {
                        ...timerInfo,
                        endTime: snoozeTime,
                        snoozedMinutes: snoozeMinutes,
                        lastSnoozed: Date.now()
                    };
                    
                    await chrome.storage.local.set({
                        [timerKey]: updatedTimerInfo
                    });
                    
                    console.log('Background: Successfully snoozed quick timer');
                    sendResponse({ 
                        success: true, 
                        scheduledTime: snoozeTime,
                        isQuickTimer: true 
                    });
                } else {
                    console.error('Background: Quick timer not found:', alertId);
                    sendResponse({ 
                        success: false, 
                        error: 'Quick timer not found' 
                    });
                }
                } catch (error) {
                    console.error('Background: Error processing quick timer snooze:', error);
                    sendResponse({ 
                        success: false, 
                        error: error.message 
                    });
                }
            })();
            
            return true; // Keep message channel open for async response
        }
        
        // Handle regular alerts - Get active alerts and reschedule the original alert
        (async () => {
            try {
                const result = await chrome.storage.local.get(['activeAlerts']);
            const activeAlerts = result.activeAlerts || [];
            const alertIndex = activeAlerts.findIndex(a => a.id === alertId);
            
            if (alertIndex !== -1) {
                const alert = activeAlerts[alertIndex];
                console.log('Background: Found original alert to reschedule:', alert);
                
                // Clear the existing alarm
                const wasCleared = await new Promise((resolve) => {
                    chrome.alarms.clear(alertId, (wasCleared) => {
                        console.log('Background: Cleared existing alarm:', wasCleared);
                        resolve(wasCleared);
                    });
                });
                
                // Create new alarm with extended time
                await new Promise((resolve) => {
                    chrome.alarms.create(alertId, {
                        when: snoozeTime
                    }, () => {
                        console.log('Background: Created new alarm for:', new Date(snoozeTime).toLocaleString());
                        resolve();
                    });
                });
                
                // Verify the alarm was created
                const verifyAlarm = await new Promise((resolve) => {
                    chrome.alarms.get(alertId, (alarm) => {
                        if (alarm) {
                            console.log('Background: Verified alarm creation:', alarm);
                        } else {
                            console.error('Background: Failed to verify alarm creation');
                        }
                        resolve(alarm);
                    });
                });
                
                // Update the alert's next scheduled time in storage (for display purposes)
                alert.nextScheduledTime = snoozeTime;
                alert.snoozedMinutes = snoozeMinutes;
                alert.lastSnoozed = Date.now();
                
                // Save updated alerts
                await new Promise((resolve, reject) => {
                    chrome.storage.local.set({ activeAlerts }, () => {
                        if (chrome.runtime.lastError) {
                            console.error('Background: Error saving snoozed alert:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            console.log('Background: Successfully updated alert storage after snooze');
                            resolve();
                        }
                    });
                });
                
                // Send success response after all operations complete
                sendResponse({ 
                    success: true, 
                    scheduledTime: snoozeTime,
                    alarmCreated: !!verifyAlarm 
                });
                
            } else {
                console.error('Background: Alert not found for snooze:', alertId);
                sendResponse({ 
                    success: false, 
                    error: 'Alert not found' 
                });
            }
            } catch (error) {
                console.error('Background: Error processing snooze:', error);
                sendResponse({ 
                    success: false, 
                    error: error.message 
                });
            }
        })();
        
        return true; // Keep message channel open for async response
    }
    
    if (message.action === 'createQuickTimer') {
        const { alarmName, endTime, duration } = message;
        
        // Create alarm for timer
        chrome.alarms.create(alarmName, {
            when: endTime
        });
        
        // Store timer info
        chrome.storage.local.set({
            [`timer-${alarmName}`]: {
                duration: duration,
                endTime: endTime
            }
        });
        
        sendResponse({ success: true });
        return true;
    }
    
    if (message.action === 'updateTimerBadge') {
        // Route through centralized badge manager for Quick Timer
        if (self.centralizedBadgeManager) {
            self.centralizedBadgeManager.updateQuickTimer(message.remaining);
        } else {
            // Fallback to direct update if manager not available
            chrome.action.setBadgeText({ text: message.text });
            chrome.action.setBadgeBackgroundColor({ color: '#f97316' });
        }
        sendResponse({ success: true });
        return true;
    }
    
    if (message.action === 'clearTimerBadge') {
        // Route through centralized badge manager for Quick Timer
        if (self.centralizedBadgeManager) {
            self.centralizedBadgeManager.clearBadge('quick-timer');
        } else {
            // Fallback to direct clear if manager not available
            chrome.action.setBadgeText({ text: '' });
        }
        sendResponse({ success: true });
        return true;
    }
    
    if (message.action === 'updatePomodoroBadge') {
        // Route through centralized badge manager for Pomodoro Timer
        if (self.centralizedBadgeManager) {
            self.centralizedBadgeManager.updatePomodoro(message.timeRemaining, message.state, message.isPaused);
        } else {
            // Fallback to direct update if manager not available
            console.warn('CentralizedBadgeManager not available for Pomodoro badge update');
        }
        sendResponse({ success: true });
        return true;
    }
    
    if (message.action === 'updateYouTubeAdsCount') {
        console.log('Background: YouTube ads count update received:', message.count);
        
        // Forward count update to all settings pages
        chrome.tabs.query({url: chrome.runtime.getURL('settings/settings.html')}, (tabs) => {
            tabs.forEach(tab => {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'youtubeAdsCountUpdated',
                    count: message.count
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        // Silent ignore - settings page might not be ready
                    }
                });
            });
        });
        
        sendResponse({ success: true });
        return true;
    }
    
    if (message.action === 'preloadCopyElement') {
        console.log('Background: Received Copy Element preload request from:', sender.tab?.url);
        
        // Use chrome.scripting to preload Copy Element script silently
        if (sender.tab && sender.tab.id) {
            // Check if this is a restricted page
            const isRestrictedPage = sender.tab.url && (
                sender.tab.url.includes('chrome://') ||
                sender.tab.url.includes('chrome-extension://') ||
                sender.tab.url.includes('moz-extension://') ||
                sender.tab.url.includes('about:') ||
                sender.tab.url.includes('edge://') ||
                sender.tab.url.includes('opera://')
            );
            
            if (isRestrictedPage) {
                console.log('Background: Skipping Copy Element preload on restricted page:', sender.tab.url);
                sendResponse({ success: false, error: 'Cannot preload on restricted page' });
                return;
            }
            
            chrome.scripting.executeScript({
                target: { tabId: sender.tab.id, allFrames: false },
                files: ['settings/quick-actions/copyelement.js']
            }).then(() => {
                console.log('Background: Copy Element script preloaded successfully for:', sender.tab.url);
                sendResponse({ success: true });
            }).catch((error) => {
                console.log('Background: Copy Element preload failed (this is normal for some pages):', error.message);
                sendResponse({ success: false, error: error.message });
            });
        } else {
            sendResponse({ success: false, error: 'No valid tab ID' });
        }
        return true; // Keep message channel open for async response
    }
    
    if (message.action === 'injectCopyElement') {
        console.log('Background: Received Copy Element injection request from:', sender.tab?.url);
        
        // Use chrome.scripting to inject Copy Element script
        if (sender.tab && sender.tab.id) {
            // Check if this is a restricted page
            const isRestrictedPage = sender.tab.url && (
                sender.tab.url.includes('chrome://') ||
                sender.tab.url.includes('chrome-extension://') ||
                sender.tab.url.includes('moz-extension://') ||
                sender.tab.url.includes('about:') ||
                sender.tab.url.includes('edge://') ||
                sender.tab.url.includes('opera://')
            );
            
            if (isRestrictedPage) {
                console.warn('Background: Cannot inject Copy Element on restricted page:', sender.tab.url);
                sendResponse({ success: false, error: 'Cannot inject on restricted page' });
                return;
            }
            
            chrome.scripting.executeScript({
                target: { tabId: sender.tab.id, allFrames: false },
                files: ['settings/quick-actions/copyelement.js']
            }).then(() => {
                console.log('Background: Copy Element script injected successfully for:', sender.tab.url);
                sendResponse({ success: true });
            }).catch((error) => {
                console.error('Background: Error injecting Copy Element script:', error);
                
                // For Google pages, the error might be due to CSP restrictions
                // Try alternative injection method
                if (sender.tab.url && sender.tab.url.includes('google.')) {
                    console.log('Background: Attempting alternative injection for Google page...');
                    
                    // Try injecting the code directly as a function
                    chrome.scripting.executeScript({
                        target: { tabId: sender.tab.id, allFrames: false },
                        func: function() {
                            // Signal that Copy Element should be loaded via alternate method
                            window.postMessage({ type: 'LOAD_COPY_ELEMENT_ALTERNATE' }, '*');
                        }
                    }).then(() => {
                        sendResponse({ success: true, method: 'alternative' });
                    }).catch((altError) => {
                        console.error('Background: Alternative injection also failed:', altError);
                        sendResponse({ success: false, error: altError.message });
                    });
                } else {
                    sendResponse({ success: false, error: error.message });
                }
            });
        } else {
            sendResponse({ success: false, error: 'No valid tab ID' });
        }
        return true; // Keep message channel open for async response
    }
    
    if (message.action === 'injectAndExecuteCopyElement') {
        console.log('Background: Received Copy Element inject and execute request from:', sender.tab?.url);
        
        // Use chrome.scripting to inject and execute Copy Element script
        if (sender.tab && sender.tab.id) {
            // Check if this is a restricted page
            const isRestrictedPage = sender.tab.url && (
                sender.tab.url.includes('chrome://') ||
                sender.tab.url.includes('chrome-extension://') ||
                sender.tab.url.includes('moz-extension://') ||
                sender.tab.url.includes('about:') ||
                sender.tab.url.includes('edge://') ||
                sender.tab.url.includes('opera://')
            );
            
            if (isRestrictedPage) {
                console.warn('Background: Cannot inject Copy Element on restricted page:', sender.tab.url);
                sendResponse({ success: false, error: 'Cannot inject on restricted page' });
                return;
            }
            
            // First inject the script
            chrome.scripting.executeScript({
                target: { tabId: sender.tab.id, allFrames: false },
                files: ['settings/quick-actions/copyelement.js']
            }).then(() => {
                console.log('Background: Copy Element script injected successfully, executing...');
                
                // Then execute it
                return chrome.scripting.executeScript({
                    target: { tabId: sender.tab.id, allFrames: false },
                    func: function() {
                        if (typeof window.CopyElementAction !== 'undefined') {
                            console.log('Background: Executing Copy Element in page context...');
                            window.CopyElementAction.execute();
                            return { success: true };
                        } else {
                            console.error('Background: CopyElementAction not available after injection');
                            return { success: false, error: 'CopyElementAction not available' };
                        }
                    }
                });
            }).then((results) => {
                if (results && results[0] && results[0].result) {
                    const result = results[0].result;
                    if (result.success) {
                        console.log('Background: Copy Element executed successfully');
                        sendResponse({ success: true });
                    } else {
                        console.error('Background: Copy Element execution failed:', result.error);
                        sendResponse({ success: false, error: result.error });
                    }
                } else {
                    console.log('Background: Copy Element injected and executed');
                    sendResponse({ success: true });
                }
            }).catch((error) => {
                console.error('Background: Error with Copy Element inject and execute:', error);
                sendResponse({ success: false, error: error.message });
            });
        } else {
            sendResponse({ success: false, error: 'No valid tab ID' });
        }
        return true; // Keep message channel open for async response
    }
    
    if (message.action === 'openLinksLazy') {
        console.log('Background: Received lazy link opening request for', message.urls.length, 'URLs');
        
        if (message.urls && Array.isArray(message.urls)) {
            // Open tabs with lazy loading using Chrome tabs API
            let openedCount = 0;
            const totalUrls = message.urls.length;
            
            const openNextBatch = (startIndex) => {
                const batchSize = 3; // Small batches to prevent overwhelming
                const endIndex = Math.min(startIndex + batchSize, totalUrls);
                
                for (let i = startIndex; i < endIndex; i++) {
                    const url = message.urls[i];
                    
                    setTimeout(() => {
                        chrome.tabs.create({
                            url: url,
                            active: false, // Don't activate the tab
                            discarded: true // Tab won't load until visited (Chrome 54+)
                        }, (tab) => {
                            openedCount++;
                            console.log(`Background: Opened lazy tab ${openedCount}/${totalUrls}:`, url);
                        });
                    }, (i - startIndex) * 100); // Small delay between individual tabs
                }
                
                // Schedule next batch
                if (endIndex < totalUrls) {
                    setTimeout(() => openNextBatch(endIndex), 800);
                }
            };
            
            openNextBatch(0);
            sendResponse({ success: true, count: totalUrls });
        } else {
            sendResponse({ success: false, error: 'Invalid URLs array' });
        }
        return true; // Keep message channel open for async response
    }
    
    // Handle alert dismissal cleanup
    if (message.action === 'alertDismissed') {
        (async () => {
            console.log('Background: Alert dismissed, performing cleanup for:', message.alertId);
            
            // Check if this is a quick timer (different cleanup process)
            if (message.alertId.startsWith('quick-timer-')) {
                console.log('Background: Handling quick timer dismissal cleanup');
                
                // Check if timer was snoozed (has a future alarm scheduled)
                const alarm = await new Promise(resolve => {
                    chrome.alarms.get(message.alertId, alarm => resolve(alarm));
                });
            
                const wasSnoozed = alarm && alarm.scheduledTime > Date.now();
                
                if (!wasSnoozed) {
                    // Timer was dismissed without snoozing, clean up timer data
                    const timerKey = `timer-${message.alertId}`;
                    chrome.storage.local.remove(timerKey);
                    console.log('Background: Cleaned up quick timer data:', timerKey);
                } else {
                    console.log('Background: Quick timer was snoozed, keeping data. Next alarm at:', new Date(alarm.scheduledTime).toLocaleString());
                }
                
                sendResponse({ success: true });
            } else {
                // Handle regular alerts - Get alert details to perform proper cleanup
                const result = await chrome.storage.local.get(['activeAlerts']);
                const activeAlerts = result.activeAlerts || [];
                const alertIndex = activeAlerts.findIndex(a => a.id === message.alertId);
                
                if (alertIndex !== -1) {
                    const alert = activeAlerts[alertIndex];
                    
                    // Check if alert was snoozed (has a future alarm scheduled)
                    const alarm = await new Promise(resolve => {
                        chrome.alarms.get(message.alertId, alarm => resolve(alarm));
                    });
                    
                    const wasSnoozed = alarm && alarm.scheduledTime > Date.now();
                    
                    if (!wasSnoozed) {
                        // Alert was dismissed without snoozing, perform cleanup
                        console.log('Background: Alert dismissed without snooze, performing cleanup');
                        
                        // Check cleanup instructions from the alert data
                        if (alert._cleanupInstructions) {
                            const { shouldRemoveIfOnce, shouldClearSnoozeData, shouldScheduleNextWeekday } = alert._cleanupInstructions;
                            
                            // If it's a one-time alert, remove it completely
                            if (shouldRemoveIfOnce) {
                                console.log(`Background: Removing completed one-time alert: "${alert.title}"`);
                                const alertIndex = activeAlerts.findIndex(a => a.id === message.alertId);
                                if (alertIndex !== -1) {
                                    activeAlerts.splice(alertIndex, 1);
                                    console.log(`Background: One-time alert "${alert.title}" removed from storage`);
                                }
                            }
                            
                            // Clear any snooze tracking info since alert has fired and was dismissed
                            if (shouldClearSnoozeData) {
                                delete alert.snoozedMinutes;
                                delete alert.lastSnoozed;
                                delete alert.nextScheduledTime;
                                console.log('Background: Cleared snooze tracking data');
                            }
                            
                            
                            // Clean up the instructions
                            delete alert._cleanupInstructions;
                        }
                        
                        // Save updated alerts
                        await chrome.storage.local.set({ activeAlerts });
                        
                        // Clear the alarm only if not snoozed
                        chrome.alarms.clear(message.alertId).catch(err => {
                            console.log('Background: No alarm to clear for:', message.alertId);
                        });
                    } else {
                        console.log('Background: Alert was snoozed, skipping cleanup. Next alarm at:', new Date(alarm.scheduledTime).toLocaleString());
                    }
                }
                
                // Update badge
                chrome.runtime.sendMessage({ action: 'updateAlertBadge' }).catch(() => {
                    // This might fail if popup is closed, which is fine
                });
                
                sendResponse({ success: true });
            }
        })();
        
        return true;
    }
    
    // Handle tab creation requests from Drag Select Links
    if (message.action === 'createDragSelectTabs') {
        console.log('Background: Creating drag select tabs for URLs:', message.urls);
        
        const createTabsSequentially = async () => {
            let successCount = 0;
            
            for (let i = 0; i < message.urls.length; i++) {
                try {
                    const url = message.urls[i];
                    
                    // Create placeholder HTML content showing the URL
                    const placeholderContent = `
                        <!DOCTYPE html>
                        <html>
                        <head>
                          <title>Loading: ${url.substring(0, 50)}...</title>
                          <style>
                            body { 
                              font-family: Arial, sans-serif; 
                              padding: 40px; 
                              background: #f5f5f5; 
                              color: #333;
                              text-align: center;
                            }
                            .container {
                              background: white;
                              padding: 30px;
                              border-radius: 8px;
                              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                              max-width: 600px;
                              margin: 0 auto;
                            }
                            .url { 
                              background: #f0f0f0; 
                              padding: 15px; 
                              border-radius: 4px; 
                              font-family: monospace;
                              margin: 20px 0;
                              word-break: break-all;
                            }
                            .loading {
                              color: #666;
                              font-style: italic;
                            }
                          </style>
                        </head>
                        <body>
                          <div class="container">
                            <h2><span style="color: #7C3AED; font-size: 18px;">●</span> Link Ready to Load</h2>
                            <p>This tab will navigate to:</p>
                            <div class="url">${url}</div>
                            <p class="loading">Click this tab to load the website...</p>
                          </div>
                        </body>
                        </html>
                      `;
                    
                    const dataUrl = `data:text/html;charset=utf-8,${encodeURIComponent(placeholderContent)}`;
                    
                    // Create tab with placeholder content
                    const newTab = await chrome.tabs.create({
                        url: dataUrl,
                        active: false, // Keep in background
                        pinned: false
                    });
                    
                    // Store the actual URL to load when tab is activated
                    citationTabs.set(newTab.id, url);
                    
                    console.log(`Background: Created drag select placeholder tab ${i + 1}/${message.urls.length} for URL: ${url}`);
                    successCount++;
                    
                    // Small delay between tab creation
                    if (i < message.urls.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                    
                } catch (error) {
                    console.error(`Background: Error creating drag select tab for URL "${message.urls[i]}":`, error);
                }
            }
            
            // Send response back to content script
            try {
                sendResponse({ 
                    success: successCount > 0, 
                    successCount: successCount,
                    totalCount: message.urls.length
                });
            } catch (error) {
                console.error('Background: Error sending response:', error);
            }
        };
        
        createTabsSequentially().catch(error => {
            console.error('Background: Error in createTabsSequentially:', error);
            try {
                sendResponse({ 
                    success: false, 
                    error: error.message
                });
            } catch (responseError) {
                console.error('Background: Error sending error response:', responseError);
            }
        });
        
        return true; // Indicates we will send a response asynchronously
    }
    
    // Handle tab creation requests from Text Time Machine Launcher
    if (message.action === 'createTextTimeMachineLauncherTab') {
        console.log('Background: Creating Text Time Machine tab for URL:', message.url);
        
        try {
            chrome.tabs.create({ 
                url: message.url,
                active: true // Open in foreground
            }, (tab) => {
                if (chrome.runtime.lastError) {
                    console.error('Background: Error creating Text Time Machine tab:', chrome.runtime.lastError);
                    sendResponse({ 
                        success: false, 
                        error: chrome.runtime.lastError.message 
                    });
                } else {
                    console.log('Background: Text Time Machine tab created successfully:', tab.id);
                    sendResponse({ 
                        success: true,
                        tabId: tab.id,
                        url: message.url
                    });
                }
            });
        } catch (error) {
            console.error('Background: Error in Text Time Machine tab creation:', error);
            sendResponse({ 
                success: false, 
                error: error.message
            });
        }
        
        return true; // Indicates we will send a response asynchronously
    }
    
    // Handle tab creation requests from New Tab Redirect
    if (message.action === 'createNewTabRedirectTab') {
        console.log('🟢 DEBUG: Background script received createNewTabRedirectTab message');
        console.log('🟢 DEBUG: Message details:', message);
        console.log('🟢 DEBUG: URL to open:', message.url);
        console.log('🟢 DEBUG: Sender details:', sender);
        
        try {
            console.log('🟢 DEBUG: Attempting to create new tab...');
            const tabConfig = { 
                url: message.url,
                active: true // Open in foreground
            };
            console.log('🟢 DEBUG: Tab configuration:', tabConfig);
            
            chrome.tabs.create(tabConfig, (tab) => {
                console.log('🟢 DEBUG: chrome.tabs.create callback executed');
                console.log('🟢 DEBUG: chrome.runtime.lastError:', chrome.runtime.lastError);
                console.log('🟢 DEBUG: Created tab object:', tab);
                
                if (chrome.runtime.lastError) {
                    console.error('🟢 DEBUG: Tab creation failed!');
                    console.error('Background: Error creating New Tab Redirect tab:', chrome.runtime.lastError);
                    const errorResponse = { 
                        success: false, 
                        error: chrome.runtime.lastError.message 
                    };
                    console.log('🟢 DEBUG: Sending error response:', errorResponse);
                    sendResponse(errorResponse);
                } else {
                    console.log('🟢 DEBUG: Tab creation succeeded!');
                    console.log('Background: New Tab Redirect tab created successfully:', tab.id);
                    const successResponse = { 
                        success: true,
                        tabId: tab.id,
                        url: message.url
                    };
                    console.log('🟢 DEBUG: Sending success response:', successResponse);
                    sendResponse(successResponse);
                }
            });
        } catch (error) {
            console.error('🟢 DEBUG: Exception caught in background script!');
            console.error('🟢 DEBUG: Exception details:', error);
            console.error('Background: Error in New Tab Redirect tab creation:', error);
            const errorResponse = { 
                success: false, 
                error: error.message
            };
            console.log('🟢 DEBUG: Sending exception error response:', errorResponse);
            sendResponse(errorResponse);
        }
        
        console.log('🟢 DEBUG: Returning true for async response');
        return true; // Indicates we will send a response asynchronously
    }
    
    // Handle tab querying requests from Bulk Link Open
    if (message.action === 'getCurrentTabs') {
        console.log('Background: Getting currently open tabs');
        
        chrome.tabs.query({}, (tabs) => {
            if (chrome.runtime.lastError) {
                console.error('Background: Error querying tabs:', chrome.runtime.lastError);
                sendResponse({ 
                    success: false, 
                    error: chrome.runtime.lastError.message 
                });
                return;
            }
            
            // Filter out system/invalid URLs
            const validUrls = tabs
                .map(tab => tab.url)
                .filter(url => {
                    if (!url) return false;
                    const isValid = !url.startsWith('chrome://') && 
                                   !url.startsWith('chrome-extension://') && 
                                   !url.startsWith('moz-extension://') && 
                                   !url.startsWith('about:') &&
                                   !url.startsWith('edge://') &&
                                   !url.startsWith('opera://') &&
                                   !url.startsWith('brave://');
                    return isValid;
                });
            
            console.log(`Background: Found ${validUrls.length} valid tabs out of ${tabs.length} total tabs`);
            sendResponse({ 
                success: true, 
                urls: validUrls 
            });
        });
        
        return true; // Indicates we will send a response asynchronously
    }
    
    // Handle tab creation requests from Find Citations
    if (message.action === 'createCitationTabs') {
        console.log('Background: Creating citation tabs for queries:', message.queries);
        
        const createTabsSequentially = async () => {
            let successCount = 0;
            
            for (let i = 0; i < message.queries.length; i++) {
                try {
                    const query = message.queries[i];
                    const encodedQuery = encodeURIComponent(query);
                    const actualSearchUrl = `https://www.google.com/search?q=${encodedQuery}`;
                    
                    // Create placeholder HTML content showing the search query
                    const placeholderContent = `
                        <!DOCTYPE html>
                        <html>
                        <head>
                          <title>Citation Search: ${query.replace(/"/g, '').substring(0, 50)}...</title>
                          <style>
                            body { 
                              font-family: Arial, sans-serif; 
                              padding: 40px; 
                              background: #f5f5f5; 
                              color: #333;
                              text-align: center;
                            }
                            .container {
                              background: white;
                              padding: 30px;
                              border-radius: 8px;
                              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                              max-width: 600px;
                              margin: 0 auto;
                            }
                            .query { 
                              background: #f0f0f0; 
                              padding: 15px; 
                              border-radius: 4px; 
                              font-family: monospace;
                              margin: 20px 0;
                              word-break: break-all;
                            }
                            .loading {
                              color: #666;
                              font-style: italic;
                            }
                          </style>
                        </head>
                        <body>
                          <div class="container">
                            <h2><span style="color: #7C3AED; font-size: 18px;">●</span> Citation Search Ready</h2>
                            <p>This tab will search for:</p>
                            <div class="query">${query}</div>
                            <p class="loading">Click this tab to load the Google search...</p>
                          </div>
                        </body>
                        </html>
                      `;
                    
                    const dataUrl = `data:text/html;charset=utf-8,${encodeURIComponent(placeholderContent)}`;
                    
                    // Create tab with placeholder content
                    const newTab = await chrome.tabs.create({
                        url: dataUrl,
                        active: false, // Keep in background
                        pinned: false
                    });
                    
                    // Store the actual search URL to load when tab is activated
                    citationTabs.set(newTab.id, actualSearchUrl);
                    
                    console.log(`Background: Created citation placeholder tab ${i + 1}/${message.queries.length} for query: ${query}`);
                    successCount++;
                    
                    // Small delay between tab creation
                    if (i < message.queries.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                    
                } catch (error) {
                    console.error(`Background: Error creating citation tab for query "${message.queries[i]}":`, error);
                }
            }
            
            // Send response back to content script
            try {
                sendResponse({ 
                    success: successCount > 0, 
                    successCount: successCount,
                    totalCount: message.queries.length
                });
            } catch (error) {
                console.error('Background: Error sending response:', error);
            }
        };
        
        createTabsSequentially().catch(error => {
            console.error('Background: Error in createTabsSequentially:', error);
            try {
                sendResponse({ 
                    success: false, 
                    error: error.message
                });
            } catch (responseError) {
                console.error('Background: Error sending error response:', responseError);
            }
        });
        
        return true; // Indicates we will send a response asynchronously
    }
    
    // Handle Email Pinner messages
    if (message.action === 'emailPinnerUnpin') {
        (async () => {
            try {
                const emailId = message.emailId;
                
                // Get current pinned emails
                const result = await chrome.storage.local.get('gmailPinnedEmails');
                let pinnedEmails = result.gmailPinnedEmails || [];
                
                // Remove the email with matching ID
                pinnedEmails = pinnedEmails.filter(email => email.id !== emailId);
                
                // Save updated list
                await chrome.storage.local.set({ gmailPinnedEmails: pinnedEmails });
                
                console.log('📌 Background: Email unpinned, remaining:', pinnedEmails.length);
                
                // Notify content script to update pin icons if on Gmail
                if (message.tabId) {
                    chrome.tabs.sendMessage(message.tabId, {
                        action: 'unpinEmail',
                        emailId: emailId
                    }).catch(() => {
                        // Content script might not be ready, ignore error
                        console.log('📌 Background: Could not notify content script (expected if not on Gmail)');
                    });
                }
                
                // Notify popup to update counter
                chrome.runtime.sendMessage({
                    action: 'updateEmailPinnerCounter',
                    count: pinnedEmails.length
                }).catch(() => {
                    // Popup might not be open, ignore error
                    console.log('📌 Background: Could not notify popup (expected if popup is closed)');
                });
                
                sendResponse({ success: true, count: pinnedEmails.length });
            } catch (error) {
                console.error('📌 Background: Error unpinning email:', error);
                sendResponse({ success: false, error: error.message });
            }
        })();
        
        return true; // Indicates we will send a response asynchronously
    }
    
    // For all other messages in this listener, return false to indicate not handled
    return false;
});

// Location Changer Functions
function genUULE() {
  var lat = Math.floor(settings.latitude*1e7) || 525109360;
  var lng = Math.floor(settings.longitude*1e7) || 134104990;
  var decodedXgeo = 'role: CURRENT_LOCATION\nproducer: DEVICE_LOCATION\nradius: 65000\nlatlng <\n  latitude_e7: '+lat+'\n  longitude_e7: '+lng+'\n>';
  var encodedXgeo = 'a '+btoa(decodedXgeo);
  return encodedXgeo
}

function genAcceptLanguage() {
  return `${settings.hl}-${settings.gl}`;
}

function checkEnabled() {
  if (settings.enabled) {
    chrome.action.setIcon({path:"/images/icon48.png"});
    chrome.declarativeNetRequest.updateSessionRules(
      {
        removeRuleIds: [
          1,2,3
        ],
        addRules: [
          {
            "id": 1,
            "priority": 1,
            "action": {
              "type": "modifyHeaders",
              "requestHeaders": [
                {
                  "header": "x-geo",
                  "operation": "set",
                  "value": genUULE()
                },
                {
                  "header": "accept-language",
                  "operation": "set",
                  "value": genAcceptLanguage()
                }
              ]
            },
            "condition": {
              "urlFilter": "google.com/",
              "resourceTypes": [
                "main_frame",
                "sub_frame",
                "image",
                "xmlhttprequest",
                "ping"
              ]
            }
          }
        ]},() => {
          if (chrome.runtime.lastError) {
            console.log(chrome.runtime.lastError.message);
          }
        }
    );
  } else {
    chrome.action.setIcon({path:"/images/icon48.png"});
    chrome.declarativeNetRequest.updateSessionRules(
      {
        removeRuleIds: [1]
      },
      () => {
        if (chrome.runtime.lastError) {
          console.log(chrome.runtime.lastError.message);
        }
      }
    );
  }
}

// Debounce mechanism for context menu updates
let contextMenuUpdateTimeout;

// Listen for storage changes
chrome.storage.onChanged.addListener((changes, area) => {
  if (area !== 'local') {
    return;
  }
  
  let needsContextMenuUpdate = false;
  
  if (changes.settings) {
    Object.assign(settings, changes.settings.newValue);
    checkEnabled();
    needsContextMenuUpdate = true;
  }
  if (changes.options) {
    Object.assign(options, changes.options.newValue);
  }
  if (changes.knownPlaces) {
    Object.assign(knownPlaces, changes.knownPlaces.newValue);
    needsContextMenuUpdate = true;
  }
  
  // Debounce context menu updates to prevent duplicate menus
  if (needsContextMenuUpdate) {
    clearTimeout(contextMenuUpdateTimeout);
    contextMenuUpdateTimeout = setTimeout(() => {
      setupContextMenu(knownPlaces);
    }, 100);
  }
});

// Context Menu Setup - All menus are created in setupContextMenu function
var parent;

function compareTimestamp(a, b) {
  if (!a.timestamp || !b.timestamp) {
    return 0;
  } else {
    return a.timestamp - b.timestamp;
  }
}

// Track if we've already added the click listener
let contextMenuClickListenerAdded = false;

function setupContextMenu(allPlaces) {
  console.log('Setting up context menu...');
  var contextPlaces = allPlaces.sort(compareTimestamp).slice(Math.max(allPlaces.length - options.contextnumber, 0));
  
  // Define comprehensive URL patterns for ALL supported domains including Google
  const universalUrlPatterns = [
    "*://*/*",
    "https://*.google.com/*",
    "https://google.com/*", 
    "https://maps.google.com/*",
    "https://maps.google.co.uk/*", 
    "https://maps.google.ca/*", 
    "https://maps.google.com.au/*",
    "https://*.google.co.uk/*",
    "https://*.google.ca/*", 
    "https://*.google.com.au/*"
  ];
  
  // Ensure we remove all existing menus and reset state
  chrome.contextMenus.removeAll(function() {
    if (chrome.runtime.lastError) {
      console.error('Error removing context menus:', chrome.runtime.lastError);
      return;
    }
    console.log('All existing context menus removed, creating new ones...');
    // Create selection-specific context menu items at top level (NO PARENT)
    chrome.contextMenus.create({
      "title": "📊 Count Words & Characters",
      "id": "word-counter",
      "contexts": ["selection"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating word counter context menu:', chrome.runtime.lastError);
      } else {
        console.log('Word counter context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "🧹 Clean Content",
      "id": "clean-selected-content", 
      "contexts": ["selection"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating clean selected content context menu:', chrome.runtime.lastError);
      } else {
        console.log('Clean selected content context menu created successfully');
      }
    });
    
    // Create main parent menu with universal URL patterns for all pages including Google domains
    parent = chrome.contextMenus.create({
      "title": "⚡ SEO Time Machines", 
      "id": "window",
      "documentUrlPatterns": universalUrlPatterns
    }, () => chrome.runtime.lastError);
    
    // Create Location Changer submenu
    const locationParent = chrome.contextMenus.create({
      "title": "🌍 Location Changer", 
      "id": "location-changer",
      "parentId": parent,
      "documentUrlPatterns": universalUrlPatterns
    }, () => chrome.runtime.lastError);
    
    // Add location-related items under Location Changer submenu
    chrome.contextMenus.create({
      "title": "🎯 Check Location", 
      "id": "check-location", 
      "parentId": locationParent,
      "documentUrlPatterns": universalUrlPatterns
    }, () => chrome.runtime.lastError);
    chrome.contextMenus.create({
      "type": "separator", 
      "id": "s0", 
      "parentId": locationParent,
      "documentUrlPatterns": universalUrlPatterns
    }, () => chrome.runtime.lastError);
    chrome.contextMenus.create({
      "title": "🚫 disable fake location", 
      "id": "disable",
      "parentId": locationParent,
      "documentUrlPatterns": universalUrlPatterns
    }, () => chrome.runtime.lastError);
    chrome.contextMenus.create({
      "type": "separator", 
      "id": "s1", 
      "parentId": locationParent,
      "documentUrlPatterns": universalUrlPatterns
    }, () => chrome.runtime.lastError);
    if (settings.enabled && settings.location) {
      chrome.contextMenus.create({
        "title": "📍"+settings.location, 
        "id": settings.placeId, 
        "parentId": locationParent,
        "documentUrlPatterns": universalUrlPatterns
      }, () => chrome.runtime.lastError);
      chrome.contextMenus.create({
        "type": "separator", 
        "id": "s2", 
        "parentId": locationParent,
        "documentUrlPatterns": universalUrlPatterns
      }, () => chrome.runtime.lastError);
    } else {
      if (settings.location) {
        chrome.contextMenus.create({
          "title": settings.location, 
          "id": settings.placeId, 
          "parentId": locationParent,
          "documentUrlPatterns": universalUrlPatterns
        }, () => chrome.runtime.lastError);
      }
    }
    contextPlaces.forEach(function (item) {
      if (!item.placeId) {
        return;
      }
      if (!item.location) {
        return;
      }
      // Skip current settings location to prevent duplication
      if (item.placeId === settings.placeId) {
        console.log('🌍 Location Changer: Skipping current settings location to prevent duplicate:', item.location);
        return;
      }
      chrome.contextMenus.create({
        "title": item.location, 
        "id": item.placeId, 
        "parentId": locationParent,
        "documentUrlPatterns": universalUrlPatterns
      }, () => chrome.runtime.lastError);
    });
    
    // Create SEO Tools submenu
    const seoToolsParent = chrome.contextMenus.create({
      "title": "⚙️ SEO Tools", 
      "id": "seo-tools",
      "parentId": parent,
      "documentUrlPatterns": universalUrlPatterns
    }, () => chrome.runtime.lastError);
    
    // Add SEO tools under SEO Tools submenu (reordered)
    chrome.contextMenus.create({
      "title": "🤖 Robots.txt Analyzer",
      "id": "robots-txt",
      "parentId": seoToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating robots.txt context menu:', chrome.runtime.lastError);
      } else {
        console.log('Robots.txt context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "🕸️ Check XML Sitemap",
      "id": "xml-sitemap-checker",
      "parentId": seoToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating XML sitemap checker context menu:', chrome.runtime.lastError);
      } else {
        console.log('XML sitemap checker context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "📊 Page Keyword Density Checker",
      "id": "page-keyword-density",
      "parentId": seoToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating page keyword density context menu:', chrome.runtime.lastError);
      } else {
        console.log('Page keyword density context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "❌ 404 Checker",
      "id": "link-checker",
      "parentId": seoToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating Link Checker context menu:', chrome.runtime.lastError);
      } else {
        console.log('Link Checker context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "📈 MOZ DA Checker",
      "id": "moz-da-checker",
      "parentId": seoToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating MOZ DA checker context menu:', chrome.runtime.lastError);
      } else {
        console.log('MOZ DA checker context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "⏰ Wayback Machine",
      "id": "wayback-machine",
      "parentId": seoToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating Wayback Machine context menu:', chrome.runtime.lastError);
      } else {
        console.log('Wayback Machine context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "📧 Email Generator",
      "id": "email-generator",
      "parentId": seoToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating Email Generator context menu:', chrome.runtime.lastError);
      } else {
        console.log('Email Generator context menu created successfully');
      }
    });
    
    // Create DEV Tools submenu
    const devToolsParent = chrome.contextMenus.create({
      "title": "🧰 DEV Tools", 
      "id": "dev-tools",
      "parentId": parent,
      "documentUrlPatterns": universalUrlPatterns
    }, () => chrome.runtime.lastError);
    
    // Add DEV tools under DEV Tools submenu (reordered)
    chrome.contextMenus.create({
      "title": "📃 Toggle Page Structure X-ray",
      "id": "page-structure-xray",
      "parentId": devToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating page structure context menu:', chrome.runtime.lastError);
      } else {
        console.log('Page structure context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "🎨 Color Palette Extractor",
      "id": "color-palette-extractor",
      "parentId": devToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating color palette extractor context menu:', chrome.runtime.lastError);
      } else {
        console.log('Color palette extractor context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "🎯 Color Picker",
      "id": "color-picker",
      "parentId": devToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating color picker context menu:', chrome.runtime.lastError);
      } else {
        console.log('Color picker context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": " 🔤 Font Inspector",
      "id": "font-inspector",
      "parentId": devToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating font inspector context menu:', chrome.runtime.lastError);
      } else {
        console.log('Font inspector context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "🖥️ Responsive Font Analysis",
      "id": "font-styles",
      "parentId": devToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating font styles context menu:', chrome.runtime.lastError);
      } else {
        console.log('Font styles context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "📄 CSS Class Inspector",
      "id": "css-class-inspector",
      "parentId": devToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating CSS class inspector context menu:', chrome.runtime.lastError);
      } else {
        console.log('CSS class inspector context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "👓 Screen Reader Simulation",
      "id": "screen-reader-simulation",
      "parentId": devToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating screen reader simulation context menu:', chrome.runtime.lastError);
      } else {
        console.log('Screen reader simulation context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "🏷️ Highlight Semantic Elements",
      "id": "semantic-elements",
      "parentId": devToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating semantic elements context menu:', chrome.runtime.lastError);
      } else {
        console.log('Semantic elements context menu created successfully');
      }
    });
    
    chrome.contextMenus.create({
      "title": "🪄 Copy Element",
      "id": "copy-element",
      "parentId": devToolsParent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating copy element context menu:', chrome.runtime.lastError);
      } else {
        console.log('Copy element context menu created successfully');
      }
    });
    
    // Add other tools directly under main menu
    chrome.contextMenus.create({
      "title": "🔤 Calculate Keyword Density",
      "id": "keyword-density",
      "parentId": parent,
      "contexts": ["selection"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating keyword density context menu:', chrome.runtime.lastError);
      } else {
        console.log('Keyword density context menu created successfully');
      }
    });
    
    
    // Add Quick Edit directly under main menu
    chrome.contextMenus.create({
      "title": "✏️ Quick Edit",
      "id": "quick-edit",
      "parentId": parent,
      "contexts": ["all"],
      "documentUrlPatterns": universalUrlPatterns
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Error creating quick edit context menu:', chrome.runtime.lastError);
      } else {
        console.log('Quick edit context menu created successfully');
      }
    });
    
    // Only add the click listener once
    if (!contextMenuClickListenerAdded) {
      chrome.contextMenus.onClicked.addListener(genericOnClick);
      contextMenuClickListenerAdded = true;
      console.log('Context menu click listener added');
    }
    
    // Build location favorites context menu items
    if (locationFavoritesManager) {
      locationFavoritesManager.buildContextMenus();
    }
    
    console.log('Context menu setup completed successfully');
  });
}

function deleteUULE() {
  chrome.cookies.getAll({'name':'UULE'}, function(cookies) {
    for (c in cookies) {
      var cookie = cookies[c];
      var url = 'https://'+cookie.domain+cookie.path;
      chrome.cookies.remove({'name':'UULE', 'url': url}, function(details) {
        console.log(details);
      });
    }
  });
}

function executeKeywordDensity(info, tab) {
  console.log('executeKeywordDensity called with:', { info, tab });
  console.log('Selected text:', info.selectionText);
  
  const selectedText = info.selectionText;
  
  if (!selectedText || selectedText.trim().length === 0) {
    // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
    executeContextMenuInterferenceCleanup(tab, 'KeywordDensity').then(() => {
      // First clear any potential global conflicts
      cleanupGlobalScope(tab, 'KeywordDensityAction').then(() => {
      // First inject the KeywordDensityAction class, then show error
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        files: ['settings/quick-actions/keyworddensity.js']
      }).then(() => {
        chrome.scripting.executeScript({
          target: {tabId: tab.id},
          func: function() {
            if (typeof KeywordDensityAction !== 'undefined') {
              KeywordDensityAction.showSelectionError();
            } else {
              alert('Please select some text first, then right-click to calculate keyword density.');
            }
          }
        });
      }).catch((error) => {
        console.error('Error injecting KeywordDensityAction script:', error);
        chrome.scripting.executeScript({
          target: {tabId: tab.id},
          func: function() {
            alert('Please select some text first, then right-click to calculate keyword density.');
          }
        });
      });
      }).catch((error) => {
        console.error('Error cleaning up before Keyword Density injection:', error);
      });
    }).catch((error) => {
      console.error('Error cleaning up Extras features interference before KeywordDensity:', error);
    });
    return;
  }
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  executeContextMenuInterferenceCleanup(tab, 'KeywordDensity').then(() => {
    // First clear any potential global conflicts
    cleanupGlobalScope(tab, 'KeywordDensityAction').then(() => {
    // Now inject the KeywordDensityAction class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/keyworddensity.js']
    }).then(() => {
      // Now execute the action
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function(selectedText) {
          if (typeof KeywordDensityAction !== 'undefined') {
            KeywordDensityAction.execute(selectedText);
            return { success: true, message: 'Keyword Density analysis activated' };
          } else {
            return { error: 'KeywordDensityAction not found' };
          }
        },
        args: [selectedText]
      }).then((result) => {
        console.log('Keyword density execution completed:', result);
      }).catch((error) => {
        console.error('Error executing Keyword Density:', error);
      });
    }).catch((error) => {
      console.error('Error injecting KeywordDensityAction script:', error);
    });
    }).catch((error) => {
      console.error('Error cleaning up before Keyword Density injection:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up Extras features interference before KeywordDensity:', error);
  });
}

function executePageStructureXray(info, tab) {
  console.log('executePageStructureXray called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  chrome.scripting.executeScript({
    target: {tabId: tab.id},
    func: function() {
      // Force clear Drag Select Links state that blocks context menu
      if (window.dragSelectLinksCleanup) {
        window.dragSelectLinksCleanup();
      }
      // Clear any remaining interference variables
      window.isKeyPressed = false;
      window.isDragging = false;
      
      // Remove both possible context menu prevention listeners
      if (typeof preventContextMenu === 'function') {
        window.removeEventListener("contextmenu", preventContextMenu, true);
        document.removeEventListener("contextmenu", preventContextMenu, true);
      }
      if (window.preventContextMenu) {
        window.removeEventListener("contextmenu", window.preventContextMenu, true);
        document.removeEventListener("contextmenu", window.preventContextMenu, true);
      }
      
      // Force cleanup Images action interference  
      if (window.imagesActionEscapeListener) {
        document.removeEventListener('keydown', window.imagesActionEscapeListener);
        delete window.imagesActionEscapeListener;
      }
      // Remove any Images action global listeners that might interfere
      const imagePanels = document.querySelectorAll('.images-audit-panel');
      imagePanels.forEach(panel => panel.remove());
      
      console.log('[Background] Force cleaned ALL Extras features before Page Structure X-ray context menu');
    }
  }).then(() => {
    // First clear any potential global conflicts from popup execution
    cleanupGlobalScope(tab, 'PageStructureAction').then(() => {
    // Now inject the PageStructureAction class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/pagestructure.js']
    }).then(() => {
      // Now execute the action
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          if (typeof PageStructureAction !== 'undefined') {
            return PageStructureAction.execute();
          } else {
            return { error: 'PageStructureAction not found' };
          }
        }
      }).then((result) => {
        console.log('Page Structure X-ray execution completed:', result);
      }).catch((error) => {
        console.error('Error executing Page Structure X-ray:', error);
      });
    }).catch((error) => {
      console.error('Error injecting PageStructureAction script:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up before Page Structure X-ray injection:', error);
  });
  }).catch((error) => {
    console.error('Error cleaning up Drag Select Links interference before Page Structure X-ray:', error);
  });
}

// Standardized forced cleanup utility for context menu interference
async function executeContextMenuInterferenceCleanup(tab, actionName) {
  return await chrome.scripting.executeScript({
    target: {tabId: tab.id},
    func: function(actionName) {
      console.log(`[Background] Force cleaning ALL Extras features before ${actionName} context menu`);
      
      // Force clear Drag Select Links state that blocks context menu
      if (window.dragSelectLinksCleanup) {
        window.dragSelectLinksCleanup();
      }
      
      // Clear any remaining interference variables
      window.isKeyPressed = false;
      window.isDragging = false;
      
      // Remove both possible context menu prevention listeners
      if (typeof preventContextMenu === 'function') {
        window.removeEventListener("contextmenu", preventContextMenu, true);
        document.removeEventListener("contextmenu", preventContextMenu, true);
      }
      if (window.preventContextMenu) {
        window.removeEventListener("contextmenu", window.preventContextMenu, true);
        document.removeEventListener("contextmenu", window.preventContextMenu, true);
      }
      
      // Force cleanup Images action interference  
      if (window.imagesActionEscapeListener) {
        document.removeEventListener('keydown', window.imagesActionEscapeListener);
        delete window.imagesActionEscapeListener;
      }
      
      // Remove any Images action global listeners that might interfere
      const imagePanels = document.querySelectorAll('.images-audit-panel');
      imagePanels.forEach(panel => panel.remove());
      
      // Force cleanup Minimal Reader interference ONLY if it's potentially blocking context menu
      if (window.MinimalReaderLoaded || window.minimalReaderObserver || document.getElementById('minimal-reader-btn')) {
        console.log('[Background] Minimal Reader detected - performing selective cleanup for context menu reliability');
        
        if (window.minimalReaderCleanup && typeof window.minimalReaderCleanup === 'function') {
          try {
            window.minimalReaderCleanup();
            console.log('[Background] Minimal Reader cleanup executed (MutationObserver disconnected, high z-index button removed)');
          } catch (error) {
            console.error('[Background] Minimal Reader cleanup failed:', error);
          }
        }
        
        // Remove Minimal Reader global variables that might interfere
        window.MinimalReaderLoaded = false;
        if (window.minimalReaderObserver) {
          try {
            window.minimalReaderObserver.disconnect();
            window.minimalReaderObserver = null;
          } catch (error) {
            console.error('[Background] Error disconnecting Minimal Reader observer:', error);
          }
        }
      }
      
      // Clear any additional interfering global listeners
      const interferingListeners = [
        'linksExtractorEscapeListener',
        'responsiveEscapeListener',
        'copyElementEscapeListener',
        'cleanSelectedContentEscapeListener'
      ];
      
      interferingListeners.forEach(listenerName => {
        if (window[listenerName]) {
          document.removeEventListener('keydown', window[listenerName]);
          delete window[listenerName];
        }
      });
      
      console.log(`[Background] Force cleaned ALL Extras features before ${actionName} context menu - COMPLETE`);
    },
    args: [actionName]
  });
}

// Verify that required utilities are loaded and ready for execution
async function verifyUtilitiesLoaded(tab, timeout = 10000) {
  console.log('[Background] Verifying utilities are loaded and ready...');
  
  try {
    const result = await chrome.scripting.executeScript({
      target: {tabId: tab.id},
      func: function(timeoutMs) {
        return new Promise((resolve) => {
          const startTime = Date.now();
          let sharedConfigReady = false;
          let domUtilityReady = false;
          
          function checkUtilities() {
            // Check SharedCleanupConfig
            if (window.SharedCleanupConfig && typeof window.SharedCleanupConfig.getGlobalListeners === 'function') {
              sharedConfigReady = true;
              console.log('[Background] SharedCleanupConfig is ready');
            }
            
            // Check DOM Snapshot Utility
            if (window.domSnapshotUtility && typeof window.domSnapshotUtility.isSnapshotReady === 'function') {
              domUtilityReady = true;
              console.log('[Background] DOM Snapshot Utility is loaded');
            }
            
            // Check if both are ready or timeout reached
            if (sharedConfigReady && domUtilityReady) {
              console.log('[Background] All utilities verified and ready');
              resolve({ success: true, sharedConfig: true, domUtility: true });
            } else if (Date.now() - startTime > timeoutMs) {
              console.warn('[Background] Utilities verification timeout reached', {
                sharedConfig: sharedConfigReady,
                domUtility: domUtilityReady,
                timeElapsed: Date.now() - startTime
              });
              resolve({ success: false, sharedConfig: sharedConfigReady, domUtility: domUtilityReady });
            } else {
              // Continue checking
              setTimeout(checkUtilities, 100);
            }
          }
          
          checkUtilities();
        });
      },
      args: [timeout]
    });
    
    const verification = result[0].result;
    console.log('[Background] Utilities verification result:', verification);
    return verification.success;
    
  } catch (error) {
    console.error('[Background] Error verifying utilities:', error);
    return false;
  }
}

// Enhanced cleanup function using async/await pattern with proper dependency verification
async function cleanupGlobalScope(tab, actionClassName) {
  console.log(`[Background] Starting cleanupGlobalScope for ${actionClassName}`);
  
  try {
    // First inject the shared cleanup config and DOM Snapshot Utility if not already present
    await chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/shared-cleanup-config.js', 'settings/dom-snapshot-utility.js']
    });
    
    console.log('[Background] DOM Snapshot Utility injected, verifying initialization...');
    
    // Verify utilities are loaded and ready
    const utilitiesReady = await verifyUtilitiesLoaded(tab);
    if (!utilitiesReady) {
      console.warn('[Background] Utilities verification failed, proceeding with manual cleanup only');
    }
    
    // Execute cleanup with proper dependency verification
    const result = await chrome.scripting.executeScript({
      target: {tabId: tab.id},
      func: function(className) {
        console.log('[Background] Starting DOM cleanup for:', className);
        
        // Function to wait for DOM snapshot utility to be ready
        function waitForSnapshotReady(timeout = 5000) {
          return new Promise((resolve) => {
            const startTime = Date.now();
            
            function checkReady() {
              if (window.domSnapshotUtility && window.domSnapshotUtility.isSnapshotReady()) {
                console.log('[Background] DOM snapshot utility is ready');
                resolve(true);
              } else if (Date.now() - startTime > timeout) {
                console.log('[Background] Timeout waiting for DOM snapshot utility, proceeding with manual cleanup');
                resolve(false);
              } else {
                console.log('[Background] DOM snapshot utility not ready yet, waiting...');
                setTimeout(checkReady, 100); // Check every 100ms
              }
            }
            
            checkReady();
          });
        }
        
        // Wait for snapshot to be ready, then proceed
        return waitForSnapshotReady().then(isReady => {
          if (isReady) {
            console.log('[Background] Using DOM snapshot restoration');
            return window.domSnapshotUtility.restoreToInitialState().then(success => {
              if (success) {
                console.log('[Background] DOM snapshot restoration successful');
                return { cleaned: true, method: 'snapshot', className: className };
              } else {
                console.log('[Background] DOM snapshot restoration failed, falling back to manual cleanup');
                return fallbackCleanup(className);
              }
            }).catch(error => {
              console.log('[Background] DOM snapshot restoration error, falling back to manual cleanup:', error);
              return fallbackCleanup(className);
            });
          } else {
            console.log('[Background] DOM snapshot not ready within timeout, using manual cleanup');
            return fallbackCleanup(className);
          }
        });
        
        function fallbackCleanup(className) {
          // Clear any existing action class from previous injections
          if (window[className]) {
            delete window[className];
          }
          
          // Use shared cleanup configuration
          const commonListeners = window.SharedCleanupConfig ? 
            window.SharedCleanupConfig.getGlobalListeners() : 
            [
              // Fallback list if shared config not available
              'quickEditEscapeListener', 'quickEditListeners', 'colorPaletteEscapeListener', 
              'colorPickerEscapeListener', 'fontInspectorEscapeListener', 'fontStylesEscapeListener',
              'cssClassInspectorEscapeListener', 'pageStructureEscapeListener', 'robotsTxtEscapeListener',
              'mozdaCheckerEscapeListener', 'keywordDensityEscapeListener', 'xmlSitemapEscapeListener',
              'screenReaderEscapeListener', 'waybackMachineEscapeListener', 'linkCheckerEscapeListener'
            ];
          
          commonListeners.forEach(listenerName => {
            if (window[listenerName]) {
              if (listenerName === 'quickEditListeners') {
                // Special handling for quickEditListeners object
                const listeners = window[listenerName];
                if (listeners) {
                  if (listeners.keydown) document.removeEventListener('keydown', listeners.keydown);
                  if (listeners.keyup) document.removeEventListener('keyup', listeners.keyup);
                  if (listeners.mousedown) document.removeEventListener('mousedown', listeners.mousedown, { capture: true });
                  if (listeners.mousemove) document.removeEventListener('mousemove', listeners.mousemove, { capture: true });
                  if (listeners.mouseup) document.removeEventListener('mouseup', listeners.mouseup, { capture: true });
                }
              } else {
                document.removeEventListener('keyup', window[listenerName]);
              }
              delete window[listenerName];
            }
          });
          
          // Use shared cleanup configuration for panel selectors
          const commonPanelSelectors = window.SharedCleanupConfig ? 
            window.SharedCleanupConfig.getPanelSelectors() : 
            [
              // Fallback selectors if shared config not available
              '#quick-edit-notice', '.color-palette-extractor-panel', '.font-inspector-panel',
              '.robots-txt-panel', '.htags-panel', '.schema-panel', '.images-panel',
              '.metadata-panel', '.page-structure-panel', '[data-quick-action]'
            ];
          
          commonPanelSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => el.remove());
          });

          // Clear QuickEdit selected elements
          document.querySelectorAll('.quick-edit-selected').forEach(el => {
            el.classList.remove('quick-edit-selected');
          });

          // Reset body cursor
          if (document.body) {
            document.body.style.cursor = '';
          }
          
          // Reset document.designMode if it was modified
          if (document.designMode === 'on') {
            document.designMode = 'off';
          }
          
          return { cleaned: true, method: 'manual', className: className };
        }
      },
      args: [actionClassName]
    });
    
    console.log('[Background] Cleanup completed:', result[0].result);
    return result[0].result;
    
  } catch (error) {
    console.error('[Background] Error during cleanupGlobalScope:', error);
    throw error;
  }
}

async function executeQuickEdit(info, tab) {
  console.log('executeQuickEdit called');
  
  try {
    // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
    await executeContextMenuInterferenceCleanup(tab, 'QuickEdit');
    
    // Clear any potential global conflicts from popup execution
    await cleanupGlobalScope(tab, 'QuickEditAction');
    
    // Now inject the QuickEditAction class
    await chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/quickedit.js']
    });
    
    // Now execute the action
    const result = await chrome.scripting.executeScript({
      target: {tabId: tab.id},
      func: function() {
        if (typeof QuickEditAction !== 'undefined') {
          const result = QuickEditAction.execute();
          return { success: true, result: result };
        } else {
          return { error: 'QuickEditAction not found' };
        }
      }
    });
    
    console.log('Quick Edit execution completed:', result);
    
  } catch (error) {
    console.error('Error during Quick Edit execution:', error);
  }
}

async function executeRobotsTxt(info, tab) {
  console.log('executeRobotsTxt called');
  
  try {
    // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
    await executeContextMenuInterferenceCleanup(tab, 'RobotsTxt');
    
    // Clear any potential global conflicts
    await cleanupGlobalScope(tab, 'RobotsTxtAction');
    
    // Now inject the RobotsTxtAction class
    await chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/robotstxt.js']
    });
    
    // Now execute the action
    const result = await chrome.scripting.executeScript({
      target: {tabId: tab.id},
      func: function() {
        if (typeof RobotsTxtAction !== 'undefined') {
          RobotsTxtAction.execute();
          return { success: true, message: 'Robots.txt analyzer activated' };
        } else {
          return { error: 'RobotsTxtAction not found' };
        }
      }
    });
    
    console.log('Robots.txt analyzer execution completed:', result);
    
  } catch (error) {
    console.error('Error during Robots.txt execution:', error);
  }
}

function executePageKeywordDensity(info, tab) {
  console.log('executePageKeywordDensity called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  executeContextMenuInterferenceCleanup(tab, 'PageKeywordDensity').then(() => {
    // First clear any potential global conflicts
    cleanupGlobalScope(tab, 'PageKeywordDensityAction').then(() => {
    // Now inject the PageKeywordDensityAction class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/pagekeyworddensity.js']
    }).then(() => {
      // Now execute the action
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          if (typeof PageKeywordDensityAction !== 'undefined') {
            PageKeywordDensityAction.execute();
            return { success: true, message: 'Page Keyword Density analyzer activated' };
          } else {
            return { error: 'PageKeywordDensityAction not found' };
          }
        }
      }).then((result) => {
        console.log('Page Keyword Density analyzer execution completed:', result);
      }).catch((error) => {
        console.error('Error executing Page Keyword Density analyzer:', error);
      });
    }).catch((error) => {
      console.error('Error injecting PageKeywordDensityAction script:', error);
    });
    }).catch((error) => {
      console.error('Error cleaning up before Page Keyword Density injection:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up Extras features interference before PageKeywordDensity:', error);
  });
}

function executeMozDAChecker(info, tab) {
  console.log('executeMozDAChecker called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  executeContextMenuInterferenceCleanup(tab, 'MozDAChecker').then(() => {
    // First clear any potential global conflicts
    cleanupGlobalScope(tab, 'MOZDACheckerAction').then(() => {
    // Now inject the MOZDACheckerAction class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/mozdachecker.js']
    }).then(() => {
      // Now execute the action
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          if (typeof MOZDACheckerAction !== 'undefined') {
            return MOZDACheckerAction.execute();
          } else {
            return { error: 'MOZDACheckerAction not found' };
          }
        }
      }).then((result) => {
        console.log('MOZ DA checker execution completed:', result);
      }).catch((error) => {
        console.error('Error executing MOZ DA checker:', error);
      });
    }).catch((error) => {
      console.error('Error injecting MOZDACheckerAction script:', error);
    });
    }).catch((error) => {
      console.error('Error cleaning up before MOZ DA checker injection:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up Extras features interference before MozDAChecker:', error);
  });
}

function executeXMLSitemapChecker(info, tab) {
  console.log('executeXMLSitemapChecker called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  executeContextMenuInterferenceCleanup(tab, 'XMLSitemapChecker').then(() => {
    // First clear any potential global conflicts
    cleanupGlobalScope(tab, 'XMLSitemapCheckerAction').then(() => {
    // Now inject the XMLSitemapCheckerAction class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/xmlsitemapchecker.js']
    }).then(() => {
      // Now execute the action
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          if (typeof XMLSitemapCheckerAction !== 'undefined') {
            XMLSitemapCheckerAction.execute();
            return { success: true, message: 'XML Sitemap Checker activated' };
          } else {
            return { error: 'XMLSitemapCheckerAction not found' };
          }
        }
      }).then((result) => {
        console.log('XML Sitemap checker execution completed:', result);
      }).catch((error) => {
        console.error('Error executing XML Sitemap checker:', error);
      });
    }).catch((error) => {
      console.error('Error injecting XMLSitemapCheckerAction script:', error);
    });
    }).catch((error) => {
      console.error('Error cleaning up before XML Sitemap checker injection:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up Extras features interference before XMLSitemapChecker:', error);
  });
}

function executeFontInspector(info, tab) {
  console.log('executeFontInspector called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  executeContextMenuInterferenceCleanup(tab, 'FontInspector').then(() => {
    // First clear any potential global conflicts
    cleanupGlobalScope(tab, 'FontInspectorAction').then(() => {
    // Now inject the FontInspectorAction class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/fontinspector.js']
    }).then(() => {
      // Now execute the action
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          if (typeof FontInspectorAction !== 'undefined') {
            FontInspectorAction.execute();
            return { success: true, message: 'Font Inspector activated' };
          } else {
            return { error: 'FontInspectorAction not found' };
          }
        }
      }).then((result) => {
        console.log('Font Inspector execution completed:', result);
      }).catch((error) => {
        console.error('Error executing Font Inspector:', error);
      });
    }).catch((error) => {
      console.error('Error injecting FontInspectorAction script:', error);
    });
    }).catch((error) => {
      console.error('Error cleaning up before Font Inspector injection:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up Extras features interference before FontInspector:', error);
  });
}

function executeFontStyles(info, tab) {
  console.log('executeFontStyles called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  chrome.scripting.executeScript({
    target: {tabId: tab.id},
    func: function() {
      // Force clear Drag Select Links state that blocks context menu
      if (window.dragSelectLinksCleanup) {
        window.dragSelectLinksCleanup();
      }
      // Clear any remaining interference variables
      window.isKeyPressed = false;
      window.isDragging = false;
      
      // Remove both possible context menu prevention listeners
      if (typeof preventContextMenu === 'function') {
        window.removeEventListener("contextmenu", preventContextMenu, true);
        document.removeEventListener("contextmenu", preventContextMenu, true);
      }
      if (window.preventContextMenu) {
        window.removeEventListener("contextmenu", window.preventContextMenu, true);
        document.removeEventListener("contextmenu", window.preventContextMenu, true);
      }
      
      // Force cleanup Images action interference  
      if (window.imagesActionEscapeListener) {
        document.removeEventListener('keydown', window.imagesActionEscapeListener);
        delete window.imagesActionEscapeListener;
      }
      // Remove any Images action global listeners that might interfere
      const imagePanels = document.querySelectorAll('.images-audit-panel');
      imagePanels.forEach(panel => panel.remove());
      
      console.log('[Background] Force cleaned ALL Extras features before Font Styles context menu');
    }
  }).then(() => {
    // First clear any potential global conflicts
    cleanupGlobalScope(tab, 'FontStylesAction').then(() => {
    // Now inject the FontStylesAction class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/fontstyles.js']
    }).then(() => {
      // Now execute the action
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          if (typeof FontStylesAction !== 'undefined') {
            FontStylesAction.execute();
            return { success: true, message: 'Font Styles activated' };
          } else {
            return { error: 'FontStylesAction not found' };
          }
        }
      }).then((result) => {
        console.log('Font Styles execution completed:', result);
      }).catch((error) => {
        console.error('Error executing Font Styles:', error);
      });
    }).catch((error) => {
      console.error('Error injecting FontStylesAction script:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up before Font Styles injection:', error);
  });
  }).catch((error) => {
    console.error('Error cleaning up Drag Select Links interference before Font Styles:', error);
  });
}

function executeCSSClassInspector(info, tab) {
  console.log('executeCSSClassInspector called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  executeContextMenuInterferenceCleanup(tab, 'CSSClassInspector').then(() => {
    // First clear any potential global conflicts
    cleanupGlobalScope(tab, 'CSSClassInspectorAction').then(() => {
    // Now inject the CSSClassInspectorAction class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/cssclassinspector.js']
    }).then(() => {
      // Now execute the action
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          if (typeof CSSClassInspectorAction !== 'undefined') {
            CSSClassInspectorAction.execute();
            return { success: true, message: 'CSS Class Inspector activated' };
          } else {
            return { error: 'CSSClassInspectorAction not found' };
          }
        }
      }).then((result) => {
        console.log('CSS Class Inspector execution completed:', result);
      }).catch((error) => {
        console.error('Error executing CSS Class Inspector:', error);
      });
    }).catch((error) => {
      console.error('Error injecting CSSClassInspectorAction script:', error);
    });
    }).catch((error) => {
      console.error('Error cleaning up before CSS Class Inspector injection:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up Extras features interference before CSSClassInspector:', error);
  });
}

function executeColorPaletteExtractor(info, tab) {
  console.log('executeColorPaletteExtractor called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  executeContextMenuInterferenceCleanup(tab, 'ColorPaletteExtractor').then(() => {
    // First clear any potential global conflicts from popup execution
    cleanupGlobalScope(tab, 'ColorPaletteExtractorAction').then(() => {
    // Now inject the ColorPaletteExtractorAction class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/colorpaletteextractor.js']
    }).then(() => {
      // Now execute the action
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          if (typeof ColorPaletteExtractorAction !== 'undefined') {
            ColorPaletteExtractorAction.execute();
            return { success: true, message: 'Color Palette Extractor activated' };
          } else {
            return { error: 'ColorPaletteExtractorAction not found' };
          }
        }
      }).then((result) => {
        console.log('Color Palette Extractor execution completed:', result);
      }).catch((error) => {
        console.error('Error executing Color Palette Extractor:', error);
      });
    }).catch((error) => {
      console.error('Error injecting ColorPaletteExtractorAction script:', error);
    });
    }).catch((error) => {
      console.error('Error cleaning up before Color Palette Extractor injection:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up Extras features interference before ColorPaletteExtractor:', error);
  });
}

async function executeColorPicker(info, tab) {
  console.log('executeColorPicker called');
  
  try {
    // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
    await chrome.scripting.executeScript({
      target: {tabId: tab.id},
      func: function() {
        // Force clear Drag Select Links state that blocks context menu
        if (window.dragSelectLinksCleanup) {
          window.dragSelectLinksCleanup();
        }
        // Clear any remaining interference variables
        window.isKeyPressed = false;
        window.isDragging = false;
        
        // Remove both possible context menu prevention listeners
        if (typeof preventContextMenu === 'function') {
          window.removeEventListener("contextmenu", preventContextMenu, true);
          document.removeEventListener("contextmenu", preventContextMenu, true);
        }
        if (window.preventContextMenu) {
          window.removeEventListener("contextmenu", window.preventContextMenu, true);
          document.removeEventListener("contextmenu", window.preventContextMenu, true);
        }
        
        // Force cleanup Images action interference  
        if (window.imagesActionEscapeListener) {
          document.removeEventListener('keydown', window.imagesActionEscapeListener);
          delete window.imagesActionEscapeListener;
        }
        // Remove any Images action global listeners that might interfere
        const imagePanels = document.querySelectorAll('.images-audit-panel');
        imagePanels.forEach(panel => panel.remove());
        
        console.log('[Background] Force cleaned ALL Extras features before Color Picker context menu');
      }
    });
    
    // CRITICAL: MUST clear conflicts and restore DOM before execution
    await cleanupGlobalScope(tab, 'ColorPickerAction');
    
    // Now inject the action class
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['settings/quick-actions/colorpicker.js']
    });
    
    // Execute the action DIRECTLY (no wrapper function)
    const result = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: function() {
        if (typeof ColorPickerAction !== 'undefined') {
          ColorPickerAction.execute(); // Direct execution
          return { success: true, message: 'Color Picker activated' };
        } else {
          return { error: 'ColorPickerAction not found' };
        }
      }
    });
    
    console.log('Color Picker execution completed:', result);
    
  } catch (error) {
    console.error('Error during Color Picker execution:', error);
  }
}

function executeScreenReaderSimulation(info, tab) {
  console.log('executeScreenReaderSimulation called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  chrome.scripting.executeScript({
    target: {tabId: tab.id},
    func: function() {
      // Force clear Drag Select Links state that blocks context menu
      if (window.dragSelectLinksCleanup) {
        window.dragSelectLinksCleanup();
      }
      // Clear any remaining interference variables
      window.isKeyPressed = false;
      window.isDragging = false;
      
      // Remove both possible context menu prevention listeners
      if (typeof preventContextMenu === 'function') {
        window.removeEventListener("contextmenu", preventContextMenu, true);
        document.removeEventListener("contextmenu", preventContextMenu, true);
      }
      if (window.preventContextMenu) {
        window.removeEventListener("contextmenu", window.preventContextMenu, true);
        document.removeEventListener("contextmenu", window.preventContextMenu, true);
      }
      
      // Force cleanup Images action interference  
      if (window.imagesActionEscapeListener) {
        document.removeEventListener('keydown', window.imagesActionEscapeListener);
        delete window.imagesActionEscapeListener;
      }
      // Remove any Images action global listeners that might interfere
      const imagePanels = document.querySelectorAll('.images-audit-panel');
      imagePanels.forEach(panel => panel.remove());
      
      console.log('[Background] Force cleaned ALL Extras features before Screen Reader Simulation context menu');
    }
  }).then(() => {
    // First clear any potential global conflicts
    cleanupGlobalScope(tab, 'ScreenReaderSimulationAction').then(() => {
    // Now inject the ScreenReaderSimulationAction class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/screenreadersimulation.js']
    }).then(() => {
      // Now execute the action
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          if (typeof ScreenReaderSimulationAction !== 'undefined') {
            return ScreenReaderSimulationAction.execute();
          } else {
            return { error: 'ScreenReaderSimulationAction not found' };
          }
        }
      }).then((result) => {
        console.log('Screen Reader Simulation execution completed:', result);
      }).catch((error) => {
        console.error('Error executing Screen Reader Simulation:', error);
      });
    }).catch((error) => {
      console.error('Error injecting ScreenReaderSimulationAction script:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up before Screen Reader Simulation injection:', error);
  });
  }).catch((error) => {
    console.error('Error cleaning up Drag Select Links interference before Screen Reader Simulation:', error);
  });
}

function executeSemanticElements(info, tab) {
  console.log('executeSemanticElements called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  executeContextMenuInterferenceCleanup(tab, 'SemanticElements').then(() => {
    // First clear any potential global conflicts
    cleanupGlobalScope(tab, 'SemanticElementsAction').then(() => {
    // First inject the SemanticTooltips class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/semantic-tooltips.js']
    }).then(() => {
      // Then inject the SemanticElementsAction class
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        files: ['settings/quick-actions/semanticelements.js']
      }).then(() => {
        // Now execute the action
        chrome.scripting.executeScript({
          target: {tabId: tab.id},
          func: function() {
            if (typeof SemanticElementsAction !== 'undefined') {
              return SemanticElementsAction.execute();
            } else {
              return { error: 'SemanticElementsAction not found' };
            }
          }
        }).then((result) => {
          console.log('Semantic Elements execution completed:', result);
        }).catch((error) => {
          console.error('Error executing Semantic Elements:', error);
        });
      }).catch((error) => {
        console.error('Error injecting SemanticElementsAction script:', error);
      });
    }).catch((error) => {
      console.error('Error injecting SemanticTooltips script:', error);
    });
    }).catch((error) => {
      console.error('Error cleaning up before Semantic Elements injection:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up Extras features interference before SemanticElements:', error);
  });
}

function executeWaybackMachine(info, tab) {
  console.log('executeWaybackMachine called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  chrome.scripting.executeScript({
    target: {tabId: tab.id},
    func: function() {
      // Force clear Drag Select Links state that blocks context menu
      if (window.dragSelectLinksCleanup) {
        window.dragSelectLinksCleanup();
      }
      // Clear any remaining interference variables
      window.isKeyPressed = false;
      window.isDragging = false;
      
      // Remove both possible context menu prevention listeners
      if (typeof preventContextMenu === 'function') {
        window.removeEventListener("contextmenu", preventContextMenu, true);
        document.removeEventListener("contextmenu", preventContextMenu, true);
      }
      if (window.preventContextMenu) {
        window.removeEventListener("contextmenu", window.preventContextMenu, true);
        document.removeEventListener("contextmenu", window.preventContextMenu, true);
      }
      
      // Force cleanup Images action interference  
      if (window.imagesActionEscapeListener) {
        document.removeEventListener('keydown', window.imagesActionEscapeListener);
        delete window.imagesActionEscapeListener;
      }
      // Remove any Images action global listeners that might interfere
      const imagePanels = document.querySelectorAll('.images-audit-panel');
      imagePanels.forEach(panel => panel.remove());
      
      console.log('[Background] Force cleaned ALL Extras features before Wayback Machine context menu');
    }
  }).then(() => {
    // First clear any potential global conflicts
    cleanupGlobalScope(tab, 'WaybackMachineAction').then(() => {
    // Now inject the WaybackMachineAction class
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/waybackmachine.js']
    }).then(() => {
      // Now execute the action
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          if (typeof WaybackMachineAction !== 'undefined') {
            return WaybackMachineAction.execute();
          } else {
            return { error: 'WaybackMachineAction not found' };
          }
        }
      }).then((result) => {
        console.log('Wayback Machine execution completed:', result);
      }).catch((error) => {
        console.error('Error executing Wayback Machine:', error);
      });
    }).catch((error) => {
      console.error('Error injecting WaybackMachineAction script:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up before Wayback Machine injection:', error);
  });
  }).catch((error) => {
    console.error('Error cleaning up Drag Select Links interference before Wayback Machine:', error);
  });
}

function executeEmailGenerator(info, tab) {
  console.log('executeEmailGenerator called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  chrome.scripting.executeScript({
    target: {tabId: tab.id},
    func: function() {
      // Force clear Drag Select Links state that blocks context menu
      if (window.dragSelectLinksCleanup) {
        window.dragSelectLinksCleanup();
      }
      // Clear any remaining interference variables
      window.isKeyPressed = false;
      window.isDragging = false;
      
      // Remove both possible context menu prevention listeners
      if (typeof preventContextMenu === 'function') {
        window.removeEventListener("contextmenu", preventContextMenu, true);
        document.removeEventListener("contextmenu", preventContextMenu, true);
      }
      if (window.preventContextMenu) {
        window.removeEventListener("contextmenu", window.preventContextMenu, true);
        document.removeEventListener("contextmenu", window.preventContextMenu, true);
      }
      
      // Force cleanup Images action interference  
      if (window.imagesActionEscapeListener) {
        document.removeEventListener('keydown', window.imagesActionEscapeListener);
        delete window.imagesActionEscapeListener;
      }
      // Remove any Images action global listeners that might interfere
      const imagePanels = document.querySelectorAll('.images-audit-panel');
      imagePanels.forEach(panel => panel.remove());
      
      console.log('[Background] Force cleaned ALL Extras features before Email Generator context menu');
    }
  }).then(() => {
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/email-generator.js']
    }).then(() => {
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          if (typeof EmailGeneratorAction !== 'undefined') {
            return EmailGeneratorAction.execute();
          } else {
            return { error: 'EmailGeneratorAction not found' };
          }
        }
      }).then((result) => {
        console.log('Email Generator execution completed:', result);
      }).catch((error) => {
        console.error('Error executing Email Generator:', error);
      });
    }).catch((error) => {
      console.error('Error injecting EmailGeneratorAction script:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up before Email Generator injection:', error);
  });
}

function executeLinkChecker(info, tab) {
  console.log('executeLinkChecker called');
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  chrome.scripting.executeScript({
    target: {tabId: tab.id},
    func: function() {
      // Force clear Drag Select Links state that blocks context menu
      if (window.dragSelectLinksCleanup) {
        window.dragSelectLinksCleanup();
      }
      // Clear any remaining interference variables
      window.isKeyPressed = false;
      window.isDragging = false;
      
      // Remove both possible context menu prevention listeners
      if (typeof preventContextMenu === 'function') {
        window.removeEventListener("contextmenu", preventContextMenu, true);
        document.removeEventListener("contextmenu", preventContextMenu, true);
      }
      if (window.preventContextMenu) {
        window.removeEventListener("contextmenu", window.preventContextMenu, true);
        document.removeEventListener("contextmenu", window.preventContextMenu, true);
      }
      
      // Force cleanup Images action interference  
      if (window.imagesActionEscapeListener) {
        document.removeEventListener('keydown', window.imagesActionEscapeListener);
        delete window.imagesActionEscapeListener;
      }
      // Remove any Images action global listeners that might interfere
      const imagePanels = document.querySelectorAll('.images-audit-panel');
      imagePanels.forEach(panel => panel.remove());
      
      console.log('[Background] Force cleaned ALL Extras features before Link Checker context menu');
    }
  }).then(() => {
    // CRITICAL: Clear conflicts and restore DOM before execution
    cleanupGlobalScope(tab, 'LinkCheckerAction').then(() => {
    // Now inject the action class
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['settings/quick-actions/linkchecker.js']
    }).then(() => {
      // Execute the action
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: function() {
          if (typeof LinkCheckerAction !== 'undefined') {
            return LinkCheckerAction.execute();
          } else {
            return { error: 'LinkCheckerAction not found' };
          }
        }
      }).then((result) => {
        console.log('Link Checker execution completed:', result);
      }).catch((error) => {
        console.error('Error executing Link Checker:', error);
      });
    }).catch((error) => {
      console.error('Error injecting LinkCheckerAction script:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up before Link Checker injection:', error);
  });
  }).catch((error) => {
    console.error('Error cleaning up Drag Select Links interference before Link Checker:', error);
  });
}

async function executeWordCounter(info, tab) {
  console.log('executeWordCounter called with:', { info, tab });
  console.log('Selected text:', info.selectionText);
  console.log('Menu item ID:', info.menuItemId);
  
  const selectedText = info.selectionText;
  
  // Require selected text for word counter
  if (!selectedText || selectedText.trim().length === 0) {
    try {
      await chrome.scripting.executeScript({
        target: {tabId: tab.id},
        func: function() {
          alert('Please select some text first to count words and characters.');
        }
      });
    } catch (error) {
      console.error('Error showing selection alert:', error);
    }
    return;
  }
  
  try {
    // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
    await chrome.scripting.executeScript({
      target: {tabId: tab.id},
      func: function() {
        // Force clear Drag Select Links state that blocks context menu
        if (window.dragSelectLinksCleanup) {
          window.dragSelectLinksCleanup();
        }
        // Clear any remaining interference variables
        window.isKeyPressed = false;
        window.isDragging = false;
        
        // 🚨 SELECTION-SPECIFIC FIX: Remove both possible context menu prevention listeners
        // The Drag Select Links uses a local preventContextMenu function (not window.preventContextMenu)
        if (typeof preventContextMenu === 'function') {
          window.removeEventListener("contextmenu", preventContextMenu, true);
          document.removeEventListener("contextmenu", preventContextMenu, true);
        }
        // Also try the window version for safety
        if (window.preventContextMenu) {
          window.removeEventListener("contextmenu", window.preventContextMenu, true);
          document.removeEventListener("contextmenu", window.preventContextMenu, true);
        }
        
        // 🚨 NEW: Force cleanup Images action interference  
        if (window.imagesActionEscapeListener) {
          document.removeEventListener('keydown', window.imagesActionEscapeListener);
          delete window.imagesActionEscapeListener;
        }
        // Remove any Images action global listeners that might interfere
        const imagePanels = document.querySelectorAll('.images-audit-panel');
        imagePanels.forEach(panel => panel.remove());
        
        console.log('[Background] Force cleaned ALL Extras features before Word Counter - SELECTION-SPECIFIC');
      }
    });
    
    // Clear any potential global conflicts from popup execution
    await cleanupGlobalScope(tab, 'WordCounterAction');
    
    // Now inject the WordCounterAction class
    await chrome.scripting.executeScript({
      target: {tabId: tab.id},
      files: ['settings/quick-actions/wordcounter.js']
    });
    
    // Now execute the action
    const result = await chrome.scripting.executeScript({
      target: {tabId: tab.id},
      func: function(selectedText) {
        if (typeof WordCounterAction !== 'undefined') {
          return WordCounterAction.execute(selectedText);
        } else {
          return { error: 'WordCounterAction not found' };
        }
      },
      args: [selectedText]
    });
    
    console.log('Word Counter execution completed:', result);
    
  } catch (error) {
    console.error('Error during Word Counter execution:', error);
  }
}

function executeCopyElement(info, tab) {
  console.log('executeCopyElement called for tab:', tab.url);
  
  // Check if we can access this tab
  if (!tab || !tab.id) {
    console.error('No valid tab for Copy Element execution');
    return;
  }
  
  // Special handling for restricted Google pages
  const isRestrictedPage = tab.url && (
    tab.url.includes('chrome://') ||
    tab.url.includes('chrome-extension://') ||
    tab.url.includes('moz-extension://') ||
    tab.url.includes('about:') ||
    tab.url.includes('edge://') ||
    tab.url.includes('opera://')
  );
  
  if (isRestrictedPage) {
    console.warn('Cannot execute Copy Element on restricted page:', tab.url);
    // Try to notify user if possible
    try {
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: function() {
          alert('Copy Element cannot be used on browser internal pages. Please navigate to a regular website.');
        }
      }).catch(() => {
        console.log('Could not show notification on restricted page');
      });
    } catch (error) {
      console.log('Could not access restricted page for notification');
    }
    return;
  }
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  chrome.scripting.executeScript({
    target: {tabId: tab.id},
    func: function() {
      // Force clear Drag Select Links state that blocks context menu
      if (window.dragSelectLinksCleanup) {
        window.dragSelectLinksCleanup();
      }
      // Clear any remaining interference variables
      window.isKeyPressed = false;
      window.isDragging = false;
      // Remove any context menu prevention listeners
      window.removeEventListener("contextmenu", window.preventContextMenu, true);
      
      // 🚨 NEW: Force cleanup Images action interference  
      if (window.imagesActionEscapeListener) {
        document.removeEventListener('keydown', window.imagesActionEscapeListener);
        delete window.imagesActionEscapeListener;
      }
      // Remove any Images action global listeners that might interfere
      const imagePanels = document.querySelectorAll('.images-audit-panel');
      imagePanels.forEach(panel => panel.remove());
      
      console.log('[Background] Force cleaned Drag Select Links and Images action before context menu');
    }
  }).then(() => {
    // 🚨 CRITICAL: MUST clear conflicts and restore DOM before execution
    // This ensures clean state regardless of what was previously active
    cleanupGlobalScope(tab, 'CopyElementAction').then(() => {
    // Now inject the action class with enhanced error handling
    chrome.scripting.executeScript({
      target: { tabId: tab.id, allFrames: false },
      files: ['settings/quick-actions/copyelement.js']
    }).then(() => {
      console.log('Copy Element script injected successfully');
      // Execute the action
      chrome.scripting.executeScript({
        target: { tabId: tab.id, allFrames: false },
        func: function() {
          try {
            if (typeof CopyElementAction !== 'undefined') {
              console.log('CopyElementAction found, executing...');
              return CopyElementAction.execute();
            } else {
              console.error('CopyElementAction class not found after injection');
              return { error: 'CopyElementAction not found' };
            }
          } catch (error) {
            console.error('Error in Copy Element execution:', error);
            return { error: error.message };
          }
        }
      }).then((result) => {
        console.log('Copy Element execution completed:', result);
        if (result && result[0] && result[0].result && result[0].result.error) {
          console.error('Copy Element execution error:', result[0].result.error);
        }
      }).catch((error) => {
        console.error('Error executing Copy Element script:', error);
        // Try fallback notification
        chrome.scripting.executeScript({
          target: { tabId: tab.id },
          func: function() {
            console.error('Copy Element failed to execute. Please try again.');
          }
        }).catch(() => {});
      });
    }).catch((error) => {
      console.error('Error injecting CopyElementAction script:', error);
      
      // Enhanced error handling for specific Google page restrictions
      if (error.message && error.message.includes('Cannot access')) {
        console.log('Attempting alternative injection method for restricted page...');
        
        // Try alternative method using chrome.tabs.sendMessage
        chrome.tabs.sendMessage(tab.id, {
          action: 'injectCopyElement'
        }).catch((messageError) => {
          console.error('Alternative injection method also failed:', messageError);
        });
      }
    });
  }).catch((error) => {
    console.error('Error cleaning up before Copy Element injection:', error);
  });
  }).catch((error) => {
    console.error('Error cleaning up Drag Select Links interference:', error);
  });
}

function executeCleanSelectedContent(info, tab) {
  console.log('executeCleanSelectedContent called with:', { info, tab });
  console.log('Selected text:', info.selectionText);
  
  const selectedText = info.selectionText;
  
  // Require selected text for clean content
  if (!selectedText || selectedText.trim().length === 0) {
    chrome.scripting.executeScript({
      target: {tabId: tab.id},
      func: function() {
        alert('Please select some text first to clean content.');
      }
    });
    return;
  }
  
  // 🚨 CRITICAL: Force cleanup of ALL Extras features interference before context menu execution
  chrome.scripting.executeScript({
    target: {tabId: tab.id},
    func: function() {
      // Force clear Drag Select Links state that blocks context menu
      if (window.dragSelectLinksCleanup) {
        window.dragSelectLinksCleanup();
      }
      // Clear any remaining interference variables
      window.isKeyPressed = false;
      window.isDragging = false;
      
      // 🚨 SELECTION-SPECIFIC FIX: Remove both possible context menu prevention listeners
      // The Drag Select Links uses a local preventContextMenu function (not window.preventContextMenu)
      if (typeof preventContextMenu === 'function') {
        window.removeEventListener("contextmenu", preventContextMenu, true);
        document.removeEventListener("contextmenu", preventContextMenu, true);
      }
      // Also try the window version for safety
      if (window.preventContextMenu) {
        window.removeEventListener("contextmenu", window.preventContextMenu, true);
        document.removeEventListener("contextmenu", window.preventContextMenu, true);
      }
      
      // 🚨 NEW: Force cleanup Images action interference  
      if (window.imagesActionEscapeListener) {
        document.removeEventListener('keydown', window.imagesActionEscapeListener);
        delete window.imagesActionEscapeListener;
      }
      // Remove any Images action global listeners that might interfere
      const imagePanels = document.querySelectorAll('.images-audit-panel');
      imagePanels.forEach(panel => panel.remove());
      
      console.log('[Background] Force cleaned ALL Extras features before Clean Selected Content - SELECTION-SPECIFIC');
    }
  }).then(() => {
    // 🚨 CRITICAL: MUST clear conflicts and restore DOM before execution
    cleanupGlobalScope(tab, 'CleanSelectedContentAction').then(() => {
    // Now inject the action class
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['settings/quick-actions/cleanselectedcontent.js']
    }).then(() => {
      // Execute the action with selected text as parameter
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: function(selectedText) {
          if (typeof CleanSelectedContentAction !== 'undefined') {
            return CleanSelectedContentAction.execute(selectedText); // Pass selected text
          } else {
            return { error: 'CleanSelectedContentAction not found' };
          }
        },
        args: [selectedText]
      }).then((result) => {
        console.log('Clean Selected Content execution completed:', result);
      }).catch((error) => {
        console.error('Error executing Clean Selected Content:', error);
      });
    }).catch((error) => {
      console.error('Error injecting CleanSelectedContentAction script:', error);
    });
  }).catch((error) => {
    console.error('Error cleaning up before Clean Selected Content injection:', error);
  });
  }).catch((error) => {
    console.error('Error cleaning up Drag Select Links interference:', error);
  });
}

function genericOnClick(info, tab) {
  console.log('Context menu clicked:', { menuItemId: info.menuItemId, selectionText: info.selectionText, tab: tab });
  
  if (info.menuItemId == 'disable') {
    console.log('Disabling fake location');
    settings.enabled = false;
    deleteUULE();
    settings.timestamp = new Date().getTime();
    chrome.storage.local.set({settings: settings});
  } else if (info.menuItemId == 'keyword-density') {
    console.log('Keyword density menu item clicked');
    // Handle keyword density calculation
    executeKeywordDensity(info, tab);
  } else if (info.menuItemId == 'page-structure-xray') {
    console.log('Page structure X-ray menu item clicked');
    // Handle page structure X-ray toggle
    executePageStructureXray(info, tab);
  } else if (info.menuItemId == 'quick-edit') {
    console.log('Quick edit menu item clicked');
    // Handle quick edit toggle
    executeQuickEdit(info, tab);
  } else if (info.menuItemId == 'robots-txt') {
    console.log('Robots.txt analyzer menu item clicked');
    // Handle robots.txt analyzer
    executeRobotsTxt(info, tab);
  } else if (info.menuItemId == 'page-keyword-density') {
    console.log('Page keyword density menu item clicked');
    // Handle page keyword density analyzer
    executePageKeywordDensity(info, tab);
  } else if (info.menuItemId == 'moz-da-checker') {
    console.log('MOZ DA checker menu item clicked');
    // Handle MOZ DA checker
    executeMozDAChecker(info, tab);
  } else if (info.menuItemId == 'xml-sitemap-checker') {
    console.log('XML sitemap checker menu item clicked');
    // Handle XML sitemap checker
    executeXMLSitemapChecker(info, tab);
  } else if (info.menuItemId == 'font-inspector') {
    console.log('Font inspector menu item clicked');
    // Handle font inspector
    executeFontInspector(info, tab);
  } else if (info.menuItemId == 'font-styles') {
    console.log('Font styles menu item clicked');
    // Handle font styles
    executeFontStyles(info, tab);
  } else if (info.menuItemId == 'css-class-inspector') {
    console.log('CSS class inspector menu item clicked');
    // Handle CSS class inspector
    executeCSSClassInspector(info, tab);
  } else if (info.menuItemId == 'color-palette-extractor') {
    console.log('Color palette extractor menu item clicked');
    // Handle color palette extractor
    executeColorPaletteExtractor(info, tab);
  } else if (info.menuItemId == 'color-picker') {
    console.log('Color picker menu item clicked');
    // Handle color picker
    executeColorPicker(info, tab);
  } else if (info.menuItemId == 'screen-reader-simulation') {
    console.log('Screen reader simulation menu item clicked');
    // Handle screen reader simulation
    executeScreenReaderSimulation(info, tab);
  } else if (info.menuItemId == 'semantic-elements') {
    console.log('Semantic elements menu item clicked');
    // Handle semantic elements highlighting
    executeSemanticElements(info, tab);
  } else if (info.menuItemId == 'wayback-machine') {
    console.log('Wayback Machine menu item clicked');
    // Handle Wayback Machine
    executeWaybackMachine(info, tab);
  } else if (info.menuItemId == 'email-generator') {
    console.log('Email Generator menu item clicked');
    // Handle Email Generator
    executeEmailGenerator(info, tab);
  } else if (info.menuItemId == 'link-checker') {
    console.log('Link Checker menu item clicked');
    // Handle Link Checker
    executeLinkChecker(info, tab);
  } else if (info.menuItemId == 'word-counter') {
    console.log('Word counter menu item clicked');
    // Handle word counter
    executeWordCounter(info, tab);
  } else if (info.menuItemId == 'clean-selected-content') {
    console.log('Clean selected content menu item clicked');
    // Handle clean selected content
    executeCleanSelectedContent(info, tab);
  } else if (info.menuItemId == 'copy-element') {
    console.log('Copy element menu item clicked');
    // Handle copy element
    executeCopyElement(info, tab);
  } else if (info.menuItemId == 'check-location') {
    console.log('Check location menu item clicked');
    // Open the "where am i" Google search in a new tab
    chrome.tabs.create({
      url: 'https://www.google.com/search?q=where+am+i'
    });
  } else if (info.menuItemId.startsWith('location-favorite-') || info.menuItemId === 'location-favorites-more') {
    console.log('Location favorite menu item clicked:', info.menuItemId);
    // Handle favorite clicks using LocationFavoritesManager
    if (locationFavoritesManager) {
      locationFavoritesManager.handleFavoriteClick(info.menuItemId, tab);
    } else {
      console.error('LocationFavoritesManager not initialized');
    }
  } else {
    console.log('🌍 Location menu item clicked:', info.menuItemId);
    var data = knownPlaces.find(place => place.placeId == info.menuItemId);
    if (data && data.placeId) {
      console.log('🌍 Location Changer: Found location data:', {
        name: data.name,
        location: data.location,
        latitude: data.latitude,
        longitude: data.longitude,
        placeId: data.placeId
      });
      
      // CRITICAL FIX: Only update location-specific properties, preserve all other settings
      settings.latitude = data.latitude;
      settings.longitude = data.longitude;
      settings.location = data.location;
      settings.name = data.name;
      settings.placeId = data.placeId;
      settings.enabled = true;
      settings.timestamp = new Date().getTime();
      
      console.log('🌍 Location Changer: Settings updated safely, preserving existing config');
      chrome.storage.local.set({settings: settings}, () => {
        if (chrome.runtime.lastError) {
          console.error('🌍 Location Changer: ERROR saving settings:', chrome.runtime.lastError.message);
        } else {
          console.log('🌍 Location Changer: Settings saved successfully to storage');
        }
      });
    } else {
      console.error('🌍 Location Changer: ERROR - Location data not found for menuItemId:', info.menuItemId);
    }
  }
}

// Handle tab activation - load Google search when user clicks on citation tab
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  const tabId = activeInfo.tabId;
  
  if (citationTabs.has(tabId)) {
    const actualUrl = citationTabs.get(tabId);
    console.log(`Background: Loading Google search for activated citation tab: ${actualUrl}`);
    
    try {
      await chrome.tabs.update(tabId, { url: actualUrl });
      // Remove from tracking once loaded
      citationTabs.delete(tabId);
    } catch (error) {
      console.error('Background: Error loading citation search:', error);
    }
  }
});

// Clean up tracking when tabs are closed
chrome.tabs.onRemoved.addListener(async (tabId) => {
  if (citationTabs.has(tabId)) {
    citationTabs.delete(tabId);
  }
  
  // STM: Check if this tab owned the chronometer and clean up if needed
  try {
    const result = await chrome.storage.local.get(['chronometerState']);
    const state = result.chronometerState || {};
    
    if (state.isActive && state.ownerId) {
      // Check if owner is still alive after tab close
      const tabs = await chrome.tabs.query({});
      const isOwnerStillAlive = tabs.some(tab => 
        state.ownerId.includes(`${tab.id}`) || 
        (Date.now() - state.lastHeartbeat) < 5000
      );
      
      if (!isOwnerStillAlive) {
        console.log('🧹 STM: Chronometer owner tab closed, clearing state');
        await chrome.storage.local.set({
          chronometerState: {
            isActive: false,
            ownerId: null,
            lastHeartbeat: 0,
            frequency: state.frequency || 2,
            isBreakPeriod: state.isBreakPeriod || false
          }
        });
        
        // Also clear local interval if it exists
        if (grandeChrometerInterval) {
          clearInterval(grandeChrometerInterval);
          grandeChrometerInterval = null;
          console.log('🧹 STM: Local chronometer interval cleared due to tab close');
        }
      }
    }
  } catch (error) {
    console.error('❌ STM: Error cleaning up chronometer on tab close:', error);
  }
});

// Background service worker for SEO Time Machines
// Handles keepalive connections and message passing

console.log('SEO Time Machines: Background service worker started');

// Handle connections from popup for keepalive
chrome.runtime.onConnect.addListener((port) => {
  if (port.name === 'popup-keepalive') {
    console.log('Background: Popup keepalive connection established');
    
    // Keep the port alive
    port.onDisconnect.addListener(() => {
      console.log('Background: Popup keepalive connection disconnected');
    });
    
    // Ping periodically to keep connection alive
    const keepAlivePing = setInterval(() => {
      try {
        port.postMessage({ type: 'ping' });
      } catch (error) {
        console.log('Background: Keepalive ping failed, clearing interval');
        clearInterval(keepAlivePing);
      }
    }, 5000); // Ping every 5 seconds
    
    port.onDisconnect.addListener(() => {
      clearInterval(keepAlivePing);
    });
  }
});



// Alert System - Chrome Alarms Listener
chrome.alarms.onAlarm.addListener(async (alarm) => {
    console.log('Alert alarm triggered:', alarm.name);
    
    // Check if this is an alert alarm (starts with 'alert-')
    if (alarm.name.startsWith('alert-')) {
        // Get alert details from storage
        const result = await chrome.storage.local.get(['activeAlerts', 'gmbExtractorSettings']);
        const activeAlerts = result.activeAlerts || [];
        const settings = result.gmbExtractorSettings || {};
        
        // Check if alerts are enabled (setting that popup UI controls)
        if (settings.alertsEnabled === false) {
            console.log('Alert skipped - alerts disabled');
            return;
        }
        
        // Find the alert
        const alertIndex = activeAlerts.findIndex(a => a.id === alarm.name && a.enabled);
        if (alertIndex !== -1) {
            const alert = activeAlerts[alertIndex];
            
            // Show alert popup - could be original alert or rescheduled (snoozed) alert
            const alertTitle = alert.snoozedMinutes ? `${alert.title} (Snoozed)` : alert.title;
            
            // Store cleanup instructions in the alert data
            // This prevents race conditions with snooze operations
            alert._cleanupInstructions = {
                shouldRemoveIfOnce: alert.repeat === 'once',
                shouldClearSnoozeData: !!alert.snoozedMinutes,
            };
            
            // Save the alert with cleanup instructions
            await chrome.storage.local.set({ activeAlerts });
            
            // Show the alert popup
            await showAlertPopup({
                ...alert,
                title: alertTitle
            });
            
            // NOTE: Cleanup operations moved to alertDismissed handler
            // to prevent race conditions with snooze functionality
        }
        return;
    }
    
    // Check if this is a quick timer alarm
    if (alarm.name.startsWith('quick-timer-')) {
        // Show timer complete notification
        const timerKey = `timer-${alarm.name}`;
        const result = await chrome.storage.local.get([timerKey, 'gmbExtractorSettings']);
        const timerInfo = result[timerKey];
        const settings = result.gmbExtractorSettings || {};
        
        if (timerInfo) {
            // Show browser notification if enabled
            if (settings.quickTimerNotifications !== false && chrome.notifications) {
                const notificationId = `quick-timer-notif-${Date.now()}`;
                chrome.notifications.create(notificationId, {
                    type: 'basic',
                    iconUrl: chrome.runtime.getURL('images/icon48.png'),
                    title: 'Timer Complete!',
                    message: 'Your quick timer has finished.',
                    priority: 1,
                    requireInteraction: false
                });
                
                // Auto-clear notification after 8 seconds
                setTimeout(() => {
                    chrome.notifications.clear(notificationId);
                }, 8000);
            }
            
            // Create notification window
            const notificationUrl = chrome.runtime.getURL('alerts/alert-notification.html') + 
                '?alertId=' + encodeURIComponent(alarm.name) +
                '&title=' + encodeURIComponent('Timer Complete!') +
                '&time=' + encodeURIComponent('Timer finished');
            
            // Use DOM popup for timer alerts too
            await showAlertPopup({
                id: alarm.name,
                title: 'Timer Complete!',
                time: 'Timer finished',
                // Store cleanup instructions to prevent race condition with snooze
                _cleanupInstructions: {
                    shouldRemoveTimerData: true,
                    timerKey: timerKey
                }
            });
            
            // NOTE: Timer cleanup moved to alertDismissed handler to prevent race condition
        }
        
        // Notify popup that timer is complete
        chrome.runtime.sendMessage({
            action: 'timerComplete',
            alarmName: alarm.name
        }).catch(() => {
            // Popup might be closed, ignore error
        });
        
        return;
    }
    
    // Check if this is an alert alarm (starts with 'alert-')
    if (alarm.name.startsWith('alert-')) {
        // Get alert details from storage
        const result = await chrome.storage.local.get(['activeAlerts', 'gmbExtractorSettings']);
        const activeAlerts = result.activeAlerts || [];
        const settings = result.gmbExtractorSettings || {};
        
        // Check if alerts are enabled (setting that popup UI controls)
        if (settings.alertsEnabled === false) {
            console.log('Alert skipped - alerts disabled');
            return;
        }
        
        // Find the alert
        const alert = activeAlerts.find(a => a.id === alarm.name && a.enabled);
        if (alert) {
            // Show alert popup - DOM popups handle their own state
            await showAlertPopup(alert);
            
            // If it's a one-time alert, remove it completely
            if (alert.repeat === 'once') {
                console.log(`Removing completed one-time alert: "${alert.title}"`);
                const alertIndex = activeAlerts.findIndex(a => a.id === alarm.name);
                if (alertIndex !== -1) {
                    activeAlerts.splice(alertIndex, 1);
                    await chrome.storage.local.set({ activeAlerts });
                    console.log(`One-time alert "${alert.title}" removed from storage`);
                }
                
                // Update badge
                chrome.runtime.sendMessage({ action: 'updateAlertBadge' });
            }
        }
    }


});

// Helper function to play alert notification sound
async function playAlertSound() {
    try {
        // Get audio settings from local storage
        const result = await chrome.storage.local.get(['pomodoroAudioSettings', 'gmbExtractorSettings']);
        const audioSettings = result.pomodoroAudioSettings || {};
        const generalSettings = result.gmbExtractorSettings || {};
        
        // Check if alert sound is enabled
        if (generalSettings.alertSound === false) {
            return;
        }
        
        // Load STM audio settings if needed
        await loadSTMAudioSettings();
        
        // Use Work Completed Sound from pomodoro settings (same as timer completion)
        const soundName = audioSettings.workCompletedSound || 'Bell Meditation';
        const soundUrl = getSTMAudioUrl(soundName);
        const volume = (audioSettings.notificationVolume || 70) / 100;
        
        if (soundUrl) {
            // Ensure offscreen document exists
            const offscreenReady = await ensureSTMOffscreenDocument();
            if (offscreenReady) {
                // Play sound via STM pattern
                chrome.runtime.sendMessage({
                    action: "playSound",
                    selectedSound: soundUrl,
                    soundVolume: volume,
                    isSoundEnabled: true
                });
                console.log('Alert sound played:', soundName);
            }
        }
    } catch (error) {
        console.error('Error playing alert sound from background:', error);
    }
}

// Helper function to show alert using DOM popup
async function showAlertPopup(alert) {
    try {
        // Always play sound first (even on restricted tabs)
        await playAlertSound();
        
        // Get the active tab first
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        let targetTab = tabs[0];
        
        // If active tab is restricted, find any other open tab
        if (!targetTab || !targetTab.id || 
            targetTab.url?.startsWith('chrome://') || 
            targetTab.url?.startsWith('chrome-extension://') ||
            targetTab.url?.startsWith('edge://') ||
            targetTab.url?.startsWith('about:') ||
            targetTab.url?.startsWith('chrome-search://')) {
            
            // Find any non-restricted tab
            const allTabs = await chrome.tabs.query({});
            targetTab = allTabs.find(tab => 
                tab.url && 
                !tab.url.startsWith('chrome://') && 
                !tab.url.startsWith('chrome-extension://') &&
                !tab.url.startsWith('edge://') &&
                !tab.url.startsWith('about:') &&
                !tab.url.startsWith('chrome-search://')
            );
            
            if (!targetTab) {
                console.log('No suitable tab found for alert popup. Sound was played.');
                return; // Just play sound, no visual
            }
        }
        
        // Inject AlertPopup script and show popup directly
        try {
            // First inject the AlertPopup class
            await chrome.scripting.executeScript({
                target: { tabId: targetTab.id },
                files: ['js/alerts/alert-popup.js']
            });
            
            // Then execute the popup creation directly
            const results = await chrome.scripting.executeScript({
                target: { tabId: targetTab.id },
                func: (alertData) => {
                    try {
                        // Ensure AlertPopup is available
                        if (typeof window.AlertPopup === 'undefined') {
                            console.error('AlertPopup class not found after injection');
                            return { success: false, error: 'AlertPopup not found' };
                        }
                        
                        // Create and show popup
                        if (!window.activeAlertPopup) {
                            window.activeAlertPopup = new window.AlertPopup();
                        }
                        window.activeAlertPopup.show(alertData);
                        
                        return { success: true };
                    } catch (error) {
                        console.error('Error creating alert popup:', error);
                        return { success: false, error: error.message };
                    }
                },
                args: [alert]
            });
            
            if (results && results[0] && results[0].result && results[0].result.success) {
                console.log('Alert popup shown for:', alert.title);
            } else {
                console.error('Failed to show alert popup:', results?.[0]?.result);
            }
        } catch (scriptError) {
            console.error('Script injection failed:', scriptError);
        }
    } catch (error) {
        console.error('Error showing alert popup:', error);
        console.log('Alert sound was played, but popup could not be shown');
    }
}


// Initialize alert badge on startup
chrome.storage.local.get(['activeAlerts'], (result) => {
    const activeAlerts = result.activeAlerts || [];
    const activeCount = activeAlerts.filter(alert => alert.enabled).length;
    
    if (activeCount > 0) {
        chrome.action.setBadgeText({ text: activeCount.toString() });
        chrome.action.setBadgeBackgroundColor({ color: '#ef4444' });
    }
});

// Location Favorites Manager for Background Script
// Manages favorites data and context menu integration
class LocationFavoritesManager {
    constructor() {
        this.storageKey = 'locationChangerFavorites';
        this.favorites = [];
        this.contextMenuIds = new Set();
        this.init();
    }

    async init() {
        await this.loadFavorites();
        this.setupStorageListener();
        // Note: buildContextMenus() is called by setupContextMenu in background.js
    }

    async loadFavorites() {
        try {
            const result = await chrome.storage.local.get([this.storageKey]);
            let loadedFavorites = result[this.storageKey];
            
            // If no favorites in chrome.storage.local, try to migrate from localStorage
            if (!loadedFavorites || !Array.isArray(loadedFavorites) || loadedFavorites.length === 0) {
                console.log('LocationFavoritesManager: No favorites in chrome.storage.local, checking localStorage for migration...');
                loadedFavorites = await this.migrateFromLocalStorage();
            }
            
            // Validate the loaded data
            if (Array.isArray(loadedFavorites) && loadedFavorites.length > 0) {
                // Filter out any corrupted favorites
                this.favorites = loadedFavorites.filter(fav => {
                    return fav && 
                           typeof fav.name === 'string' && 
                           fav.data && 
                           typeof fav.data === 'object';
                });
                console.log('LocationFavoritesManager: Loaded', this.favorites.length, 'valid favorites');
            } else {
                this.favorites = [];
                console.log('LocationFavoritesManager: No favorites found, starting with empty array');
            }
        } catch (error) {
            console.error('LocationFavoritesManager: Error loading favorites:', error);
            this.favorites = [];
        }
    }

    async migrateFromLocalStorage() {
        try {
            // Inject a script to read from localStorage since service workers can't access it directly
            const tabs = await chrome.tabs.query({});
            if (tabs.length === 0) {
                console.log('LocationFavoritesManager: No tabs available for localStorage migration');
                return [];
            }

            // Try to read from localStorage via content script injection
            const result = await chrome.scripting.executeScript({
                target: { tabId: tabs[0].id },
                func: function(storageKey) {
                    try {
                        const stored = localStorage.getItem(storageKey);
                        return stored ? JSON.parse(stored) : [];
                    } catch (error) {
                        console.error('Migration script error:', error);
                        return [];
                    }
                },
                args: [this.storageKey]
            });

            if (result && result[0] && result[0].result && Array.isArray(result[0].result)) {
                const migratedFavorites = result[0].result;
                if (migratedFavorites.length > 0) {
                    console.log('LocationFavoritesManager: Migrating', migratedFavorites.length, 'favorites from localStorage');
                    // Save to chrome.storage.local
                    await chrome.storage.local.set({ [this.storageKey]: migratedFavorites });
                    return migratedFavorites;
                }
            }
        } catch (error) {
            console.error('LocationFavoritesManager: Error during localStorage migration:', error);
        }
        
        return [];
    }

    setupStorageListener() {
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'local' && changes[this.storageKey]) {
                console.log('LocationFavoritesManager: Favorites changed, rebuilding menus');
                this.favorites = changes[this.storageKey].newValue || [];
                this.buildContextMenus();
            }
        });
    }

    buildContextMenus() {
        // Clear existing favorite menu items
        this.clearFavoriteMenus();

        if (this.favorites.length === 0) {
            console.log('LocationFavoritesManager: No favorites to display');
            return;
        }

        // Sort favorites by recent usage (lastUsed), then savedAt, then by name
        const sortedFavorites = [...this.favorites].sort((a, b) => {
            // First sort by lastUsed (most recent first)
            const lastUsedA = new Date(a.lastUsed || 0);
            const lastUsedB = new Date(b.lastUsed || 0);
            const lastUsedDiff = lastUsedB - lastUsedA;
            
            // If significant difference in lastUsed, use that
            if (Math.abs(lastUsedDiff) > 1000) {
                return lastUsedDiff;
            }
            
            // Otherwise, sort by savedAt (creation time)
            const dateA = new Date(a.savedAt || 0);
            const dateB = new Date(b.savedAt || 0);
            const dateDiff = dateB - dateA;
            
            // If dates are the same (or both missing), sort by name
            if (Math.abs(dateDiff) < 1000) { // Within 1 second, consider same
                return a.name.localeCompare(b.name);
            }
            return dateDiff;
        });
        
        // Limit to 8 favorites to prevent menu overflow and leave room for separator and "More"
        const displayFavorites = sortedFavorites.slice(0, 8);

        // Add separator before favorites if not already added
        const separatorId = 'location-favorites-separator';
        if (!this.contextMenuIds.has(separatorId)) {
            chrome.contextMenus.create({
                "type": "separator",
                "id": separatorId,
                "parentId": "location-changer",
                "documentUrlPatterns": universalUrlPatterns
            }, () => {
                if (chrome.runtime.lastError) {
                    console.error('Error creating favorites separator:', chrome.runtime.lastError);
                } else {
                    this.contextMenuIds.add(separatorId);
                }
            });
        }

        // Add each favorite as a menu item
        displayFavorites.forEach((favorite, index) => {
            const menuId = `location-favorite-${index}`;
            chrome.contextMenus.create({
                "title": `📍 ${favorite.name}`,
                "id": menuId,
                "parentId": "location-changer",
                "documentUrlPatterns": universalUrlPatterns
            }, () => {
                if (chrome.runtime.lastError) {
                    console.error(`Error creating favorite menu "${favorite.name}":`, chrome.runtime.lastError);
                } else {
                    this.contextMenuIds.add(menuId);
                }
            });
        });

        // Add "More..." option if there are more than 8 favorites
        if (sortedFavorites.length > 8) {
            const moreId = 'location-favorites-more';
            chrome.contextMenus.create({
                "title": `📋 More... (${sortedFavorites.length - 8} more)`,
                "id": moreId,
                "parentId": "location-changer", 
                "documentUrlPatterns": universalUrlPatterns
            }, () => {
                if (chrome.runtime.lastError) {
                    console.error('Error creating more favorites menu:', chrome.runtime.lastError);
                } else {
                    this.contextMenuIds.add(moreId);
                }
            });
        }

        console.log('LocationFavoritesManager: Built context menus for', displayFavorites.length, 'favorites');
    }

    clearFavoriteMenus() {
        // Remove all favorite-related context menu items
        this.contextMenuIds.forEach(id => {
            chrome.contextMenus.remove(id, () => {
                if (chrome.runtime.lastError) {
                    // Ignore errors for items that don't exist
                }
            });
        });
        this.contextMenuIds.clear();
    }

    handleFavoriteClick(menuItemId, tab) {
        try {
            // Extract index from menu item ID
            const match = menuItemId.match(/location-favorite-(\d+)/);
            if (!match) {
                if (menuItemId === 'location-favorites-more') {
                    this.handleMoreFavoritesClick(tab);
                }
                return;
            }

            const index = parseInt(match[1]);
            if (isNaN(index) || index < 0) {
                console.error('LocationFavoritesManager: Invalid favorite index:', index);
                return;
            }

            // Use the same sorting algorithm as buildContextMenus
            const sortedFavorites = [...this.favorites].sort((a, b) => {
                const lastUsedA = new Date(a.lastUsed || 0);
                const lastUsedB = new Date(b.lastUsed || 0);
                const lastUsedDiff = lastUsedB - lastUsedA;
                
                if (Math.abs(lastUsedDiff) > 1000) {
                    return lastUsedDiff;
                }
                
                const dateA = new Date(a.savedAt || 0);
                const dateB = new Date(b.savedAt || 0);
                const dateDiff = dateB - dateA;
                
                if (Math.abs(dateDiff) < 1000) {
                    return a.name.localeCompare(b.name);
                }
                return dateDiff;
            });

            const favorite = sortedFavorites[index];

            if (!favorite) {
                console.error('LocationFavoritesManager: Favorite not found for index', index);
                chrome.notifications.create({
                    type: 'basic',
                    iconUrl: 'images/icon48.png',
                    title: 'Location Error',
                    message: 'Selected location is no longer available.'
                });
                return;
            }

            console.log('LocationFavoritesManager: Setting location to', favorite.name);
            this.setLocationFromFavorite(favorite, tab);
            
        } catch (error) {
            console.error('LocationFavoritesManager: Error handling favorite click:', error);
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'images/icon48.png',
                title: 'Location Error', 
                message: 'Failed to set location. Please try again.'
            });
        }
    }

    async setLocationFromFavorite(favorite, tab) {
        try {
            // Validate inputs
            if (!favorite || !favorite.name || !favorite.data) {
                throw new Error('Invalid favorite data');
            }
            
            if (!tab || !tab.id) {
                throw new Error('Invalid tab information');
            }

            // Update usage timestamp for better sorting
            await this.updateFavoriteUsage(favorite.name);
            
            // First, clean up any interfering features
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: function() {
                    try {
                        // Force clear any interference
                        window.isKeyPressed = false;
                        window.isDragging = false;
                        
                        // Remove context menu prevention listeners
                        if (typeof preventContextMenu === 'function') {
                            window.removeEventListener("contextmenu", preventContextMenu, true);
                            document.removeEventListener("contextmenu", preventContextMenu, true);
                        }
                        if (window.preventContextMenu) {
                            window.removeEventListener("contextmenu", window.preventContextMenu, true);
                            document.removeEventListener("contextmenu", window.preventContextMenu, true);
                        }
                        return { success: true };
                    } catch (error) {
                        return { error: error.message };
                    }
                }
            });

            // Inject the location setter script
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['settings/quick-actions/location-quick-set.js']
            });

            // Execute the location setting with the favorite data
            const result = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: function(favoriteData) {
                    try {
                        if (typeof LocationQuickSet !== 'undefined') {
                            return LocationQuickSet.setLocation(favoriteData);
                        } else {
                            return { success: false, error: 'LocationQuickSet not found' };
                        }
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                },
                args: [favorite]
            });

            // Check the result
            if (result && result[0] && result[0].result) {
                const executionResult = result[0].result;
                if (!executionResult.success) {
                    throw new Error(executionResult.error || 'Location setting failed');
                }
            }

            console.log('LocationFavoritesManager: Successfully set location to', favorite.name);

        } catch (error) {
            console.error('LocationFavoritesManager: Error setting location:', error);
            
            // Show user-friendly error notification
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'images/icon48.png',
                title: 'Location Error',
                message: `Failed to set location to "${favorite.name}". ${error.message || 'Please try again.'}`
            });
        }
    }

    async updateFavoriteUsage(favoriteName) {
        try {
            // Find and update the favorite's usage timestamp
            const favoriteIndex = this.favorites.findIndex(fav => fav.name === favoriteName);
            if (favoriteIndex >= 0) {
                this.favorites[favoriteIndex].lastUsed = new Date().toISOString();
                
                // Save back to storage
                await chrome.storage.local.set({ [this.storageKey]: this.favorites });
                console.log('LocationFavoritesManager: Updated usage for', favoriteName);
            }
        } catch (error) {
            console.error('LocationFavoritesManager: Error updating favorite usage:', error);
        }
    }

    handleMoreFavoritesClick(tab) {
        // Show helpful notification with additional context
        const totalFavorites = this.favorites.length;
        const hiddenCount = totalFavorites - 8;
        
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'images/icon48.png',
            title: 'Location Favorites',
            message: `You have ${hiddenCount} more saved locations. Click the extension icon to access all ${totalFavorites} favorites.`
        });
    }

    // Public method to get favorites for other parts of the extension
    getFavorites() {
        return [...this.favorites];
    }

    // Check if a specific favorite exists
    hasFavorite(name) {
        return this.favorites.some(fav => fav.name === name);
    }
}

// Initialize the LocationFavoritesManager
let locationFavoritesManager;

// Background script fully loaded
console.log('✅ Background script completely loaded - message listener should be active');
console.log('📋 Extension is ready to receive messages');

// Initialize LocationFavoritesManager after background script loads
locationFavoritesManager = new LocationFavoritesManager();