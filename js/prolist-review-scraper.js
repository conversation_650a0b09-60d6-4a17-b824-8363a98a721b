// Pro List Review Scraper Module
// This module handles the scrolling, "read more" expansion and extraction for Google Local Services Pro List pages

class ProListReviewScraper {
    constructor() {
        this.reviewData = [];
        this.isRunning = false;
        this.rangeUtility = new ReviewRangeUtility();
        this.totalReviewCount = 0;
        this.version = "1.1-ProListWithScrolling";
        console.log(`🔍 PRO LIST SCRAPER: Initializing version ${this.version} with range utility and scrolling`);
    }

    // Execute the review extraction with proper sequence: scroll, then more buttons, then extract
    async executeExtraction(maxReviews = null) {
        console.log('🔍 PRO LIST SCRAPER: Starting review extraction...');
        
        try {
            // Initialize range utility with the provided limit
            this.rangeUtility.setMaxReviews(maxReviews);
            console.log(`📊 PRO LIST SCRAPER: Range settings - ${this.rangeUtility.getStatusMessage()}`);

            // Step 1: Count current reviews and check if we need to scroll
            console.log('📊 PRO LIST SCRAPER: Step 1 - Checking current review count...');
            await this.wait(1000);
            const initialCount = this.countCurrentVisibleReviews();
            console.log(`📊 PRO LIST SCRAPER: Initial visible reviews: ${initialCount}`);

            // Step 2: Load reviews through scrolling FIRST (like single-review-scraper)
            console.log('🔄 PRO LIST SCRAPER: Step 2 - Loading reviews through automated scrolling...');
            await this.loadAllReviewsThroughScrolling();

            // Step 3: Expand all "More" buttons (using same selectors as working commit)
            console.log('🔽 PRO LIST SCRAPER: Step 3 - Expanding all "More" buttons...');
            await this.runReadMoreScript();

            // Wait for expansions to complete
            await this.wait(3000);

            // Step 4: Extract all review data
            console.log('📊 PRO LIST SCRAPER: Step 4 - Extracting review data...');
            await this.extractAllReviews();

            // Note: Range limit is now applied during extraction, no need for final truncation
            let finalReviews = this.reviewData;
            console.log(`🎉 PRO LIST SCRAPER: Extraction completed! Found ${finalReviews.length} reviews (${this.rangeUtility.hasLimit() ? 'with limit' : 'no limit'})`);
            
            return {
                success: true,
                reviewCount: finalReviews.length,
                reviews: finalReviews,
                totalExpected: this.totalReviewCount,
                rangeApplied: this.rangeUtility.hasLimit(),
                maxReviews: this.rangeUtility.getMaxReviews(),
                totalAvailable: this.reviewData.length
            };

        } catch (error) {
            console.error('❌ PRO LIST SCRAPER: Error during extraction:', error);
            
            // Note: Range limit is applied during extraction, so this.reviewData is already limited
            let finalReviews = this.reviewData;
            
            return {
                error: error.message,
                reviewCount: finalReviews.length,
                reviews: finalReviews,
                totalExpected: this.totalReviewCount,
                rangeApplied: this.rangeUtility.hasLimit(),
                maxReviews: this.rangeUtility.getMaxReviews(),
                totalAvailable: this.reviewData.length
            };
        }
    }

    // Load all reviews through scrolling (EXACT same approach as single-review-scraper)
    async loadAllReviewsThroughScrolling() {
        console.log('🔄 PRO LIST SCRAPER: Starting automated review loading...');
        
        // Calculate the effective target based on range settings
        const totalExpected = 50; // Assume a reasonable number for ProList
        const effectiveTarget = this.rangeUtility.getEffectiveTarget(totalExpected);
        console.log(`🔄 PRO LIST SCRAPER: Effective target: ${effectiveTarget} reviews`);
        
        // For ProList, we don't have div.m6QErb, so we'll use the body or main content container
        let reviewsContainer = document.querySelector('div.m6QErb'); // Try first
        if (!reviewsContainer) {
            console.log('🔄 PRO LIST SCRAPER: No m6QErb container found, using body as container for ProList');
            reviewsContainer = document.body;
        }
        
        console.log('🔄 PRO LIST SCRAPER: ✅ Using container for scrolling');
        
        // CRITICAL: Activate the reviews container (adapted for ProList)
        console.log('🔄 PRO LIST SCRAPER: Step 1 - Activating reviews container...');
        await this.activateReviewsContainer();
        
        // Try all scrolling methods
        await this.tryAllScrollingMethods(reviewsContainer);
        
        let previousCount = 0;
        let stableCount = 0;
        let maxAttempts = Math.ceil(effectiveTarget / 10) + 5;
        let attempts = 0;
        
        // Initialize scroll tracking
        this.lastScrollPosition = 0;
        this.scrollPositionStableCount = 0;
        
        console.log(`🔄 PRO LIST SCRAPER: Will attempt up to ${maxAttempts} scrolling attempts (targeting ${effectiveTarget} reviews)`);
        
        while (attempts < maxAttempts) {
            attempts++;
            console.log(`🔄 PRO LIST SCRAPER: === Scroll Attempt ${attempts}/${maxAttempts} ===`);
            
            // Check current scroll position to detect if we've reached bottom
            const currentScrollPosition = this.getCurrentScrollPosition();
            console.log(`🔄 PRO LIST SCRAPER: Current scroll position: ${currentScrollPosition}px`);
            
            // Count current visible reviews (including empty ones)
            const currentReviews = this.countCurrentVisibleReviews();
            console.log(`🔄 PRO LIST SCRAPER: Attempt ${attempts} - Found ${currentReviews} visible reviews (target: ${effectiveTarget})`);
            
            // CHECK RANGE LIMIT FIRST - Stop scrolling if we've reached the desired number
            if (this.rangeUtility.hasLimit() && currentReviews >= this.rangeUtility.getMaxReviews()) {
                console.log(`🎯 PRO LIST SCRAPER: RANGE LIMIT REACHED! Found ${currentReviews} reviews, limit is ${this.rangeUtility.getMaxReviews()}`);
                console.log(`🎯 PRO LIST SCRAPER: Stopping scrolling early to respect range limit`);
                break;
            }
            
            // Check if we've reached our target (either range limit or total available)
            if (this.rangeUtility.shouldStopScraping(currentReviews) || currentReviews >= effectiveTarget) {
                console.log(`🎉 PRO LIST SCRAPER: Reached target! (${currentReviews}/${effectiveTarget})`);
                break;
            }
            
            // Check if scroll position hasn't changed (we've reached the bottom)
            if (currentScrollPosition === this.lastScrollPosition) {
                this.scrollPositionStableCount++;
                console.log(`🔄 PRO LIST SCRAPER: Scroll position unchanged (${currentScrollPosition}px), stability count: ${this.scrollPositionStableCount}/2`);
                if (this.scrollPositionStableCount >= 2) {
                    console.log(`🔄 PRO LIST SCRAPER: Scroll position stable at ${currentScrollPosition}px for 2 attempts, reached bottom - stopping`);
                    break;
                }
            } else {
                this.scrollPositionStableCount = 0;
                this.lastScrollPosition = currentScrollPosition;
                console.log(`🔄 PRO LIST SCRAPER: Scroll position changed to ${currentScrollPosition}px, continuing...`);
            }
            
            // Check if count hasn't changed (might be stuck)
            if (currentReviews === previousCount) {
                stableCount++;
                console.log(`🔄 PRO LIST SCRAPER: Count unchanged (${currentReviews}), stability count: ${stableCount}/3`);
                if (stableCount >= 3) {
                    console.log(`🔄 PRO LIST SCRAPER: Review count stable at ${currentReviews} for 3 attempts, assuming all loaded`);
                    break;
                }
            } else {
                stableCount = 0;
                previousCount = currentReviews;
                console.log(`🔄 PRO LIST SCRAPER: Count increased from ${previousCount} to ${currentReviews}, continuing...`);
            }
            
            // Try all scrolling methods for this attempt
            console.log(`🔄 PRO LIST SCRAPER: About to scroll (attempt ${attempts})...`);
            try {
                await this.tryAllScrollingMethods(reviewsContainer);
                console.log(`🔄 PRO LIST SCRAPER: All scrolling methods completed, waiting 1.8 seconds...`);
            } catch (scrollError) {
                console.error(`🔄 PRO LIST SCRAPER: ❌ Scroll error on attempt ${attempts}:`, scrollError);
            }
            
            // Wait for new reviews to load (1.5-2 seconds as specified)
            await this.wait(1800);
            console.log(`🔄 PRO LIST SCRAPER: Wait completed, checking for new reviews...`);
        }
        
        const finalCount = this.countCurrentVisibleReviews();
        console.log(`🔄 PRO LIST SCRAPER: === Scrolling Phase Complete ===`);
        console.log(`🔄 PRO LIST SCRAPER: Final count: ${finalCount} reviews (target: ${effectiveTarget})`);
        console.log(`🔄 PRO LIST SCRAPER: Attempts used: ${attempts}/${maxAttempts}`);
        console.log(`🔄 PRO LIST SCRAPER: Range settings: ${this.rangeUtility.getStatusMessage()}`);
        
        return finalCount;
    }

    // Try all scrolling methods (EXACT same as single-review-scraper)
    async tryAllScrollingMethods(container) {
        console.log('📜 PRO LIST SCRAPER: Using SMART Review Parent Scrolling (the only working method)...');
        
        // Method 0: SMART SCROLL - Find actual review parents and scroll from there (WORKING!)
        await this.scrollMethodSmartReviewParent();
        
        console.log('📜 PRO LIST SCRAPER: Scrolling method completed');
    }

    // EXACT same smart scrolling method as single-review-scraper
    async scrollMethodSmartReviewParent() {
        console.log('📜 PRO LIST SCRAPER: Method 0 - SMART Review Parent Scrolling...');
        
        try {
            // Find the existing reviews (ProList uses OA1nbd)
            const existingReviews = document.querySelectorAll('div.OA1nbd');
            console.log(`📜 PRO LIST SCRAPER: Found ${existingReviews.length} existing reviews to analyze`);
            
            if (existingReviews.length === 0) {
                console.log('📜 PRO LIST SCRAPER: No existing reviews found, trying basic scroll');
                // Just scroll down if no reviews found yet
                window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                await this.wait(1000);
                return;
            }
            
            // Get the last review - this is our reference point
            const lastReview = existingReviews[existingReviews.length - 1];
            console.log('📜 PRO LIST SCRAPER: Using last review as scroll target...');
            
            // Get the position of the last review
            const lastReviewRect = lastReview.getBoundingClientRect();
            console.log(`📜 PRO LIST SCRAPER: Last review position: top=${lastReviewRect.top}, bottom=${lastReviewRect.bottom}`);
            
            // Step 1: Scroll to the last review first (basic positioning)
            console.log('📜 PRO LIST SCRAPER: Step 1 - Scrolling last review into view...');
            lastReview.scrollIntoView({ behavior: 'smooth', block: 'start' });
            await this.wait(500);
            
            // Step 2: AGGRESSIVE SCROLLING - Go way beyond the last review (THE KEY METHOD)
            console.log('📜 PRO LIST SCRAPER: Step 2 - AGGRESSIVE scrolling beyond current content...');
            
            // Calculate how far beyond we need to scroll to trigger lazy loading
            const viewportHeight = window.innerHeight;
            const aggressiveDistance = viewportHeight * 2; // Scroll 2 viewport heights beyond
            
            console.log(`📜 PRO LIST SCRAPER: Viewport height: ${viewportHeight}px, aggressive distance: ${aggressiveDistance}px`);
            
            // Create temporary scroll targets at increasing distances
            const distances = [500, 1000, 1500, 2000, aggressiveDistance]; // Multiple distances to try
            
            for (let i = 0; i < distances.length; i++) {
                const tempTarget = document.createElement('div');
                tempTarget.style.height = '1px';
                tempTarget.style.visibility = 'hidden';
                tempTarget.style.position = 'relative';
                tempTarget.style.top = `${distances[i]}px`; // Position it far below
                tempTarget.setAttribute('data-scroll-target', i);
                
                // Insert after the last review
                lastReview.parentElement.appendChild(tempTarget);
                
                console.log(`📜 PRO LIST SCRAPER: Scrolling to aggressive target ${i} (${distances[i]}px beyond)...`);
                
                // Use multiple scrolling techniques for maximum effectiveness
                tempTarget.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await this.wait(200);
                
                // Also try direct window scrolling to the calculated position
                const targetY = window.pageYOffset + distances[i];
                window.scrollTo({ top: targetY, behavior: 'smooth' });
                await this.wait(200);
                
                // Clean up immediately
                tempTarget.remove();
            }
            
            // Step 3: Final window scroll to maximum height to ensure we hit the bottom
            console.log('📜 PRO LIST SCRAPER: Step 3 - Final scroll to document maximum...');
            window.scrollTo({ top: 999999, behavior: 'smooth' });
            await this.wait(500);
            
            // Trigger scroll events to ensure lazy loading is activated
            window.dispatchEvent(new Event('scroll'));
            document.dispatchEvent(new Event('scroll'));
            await this.wait(200);
            
            console.log('📜 PRO LIST SCRAPER: ✅ Efficient smart review scrolling completed');
            
        } catch (error) {
            console.log('📜 PRO LIST SCRAPER: Smart review scrolling failed:', error);
        }
    }

    // Activate the reviews container (adapted for ProList)
    async activateReviewsContainer() {
        console.log('👆 PRO LIST SCRAPER: Activating reviews container for ProList...');
        
        try {
            // For ProList, we don't have the same tab structure, so we'll focus on the main content area
            console.log('👆 PRO LIST SCRAPER: ProList page - focusing on main content area...');
            
            // Try to find the main ProList content area
            const mainContent = document.querySelector('main') || 
                               document.querySelector('[role="main"]') || 
                               document.querySelector('.main-content') ||
                               document.body;
            
            if (mainContent) {
                console.log('👆 PRO LIST SCRAPER: Found main content area, activating...');
                
                // Calculate SAFE right-side position for ProList
                const safeRightX = window.innerWidth * 0.8; // 80% from left = right side
                const clickY = 200; // 200px from top
                
                console.log(`👆 PRO LIST SCRAPER: SAFE RIGHT-SIDE clicking at (${safeRightX}, ${clickY}) - right side of viewport`);
                
                // Find the element at that safe position and check if it's safe to click
                const elementAtPosition = document.elementFromPoint(safeRightX, clickY);
                if (elementAtPosition) {
                    // Check if this element or its parents are links/buttons that could open tabs
                    const isUnsafeElement = elementAtPosition.closest('a, button[href], [onclick], [data-href]');
                    
                    if (!isUnsafeElement) {
                        console.log(`👆 PRO LIST SCRAPER: SAFE element found: ${elementAtPosition.tagName}.${elementAtPosition.className}`);
                        const clickEvent = new MouseEvent('click', {
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            clientX: safeRightX,
                            clientY: clickY
                        });
                        elementAtPosition.dispatchEvent(clickEvent);
                    } else {
                        console.log('👆 PRO LIST SCRAPER: Unsafe element detected, trying main content focus instead...');
                        mainContent.focus();
                        mainContent.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                } else {
                    console.log('👆 PRO LIST SCRAPER: No element found at safe position, focusing main content...');
                    mainContent.focus();
                    mainContent.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
            
            // Wait for activation to take effect
            await this.wait(500);
            console.log('👆 PRO LIST SCRAPER: Container activation completed');
            
        } catch (error) {
            console.error('👆 PRO LIST SCRAPER: ❌ Error during container activation:', error);
        }
    }

    // Get current scroll position
    getCurrentScrollPosition() {
        return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
    }

    // Count currently visible reviews (EXACT same approach as single-review-scraper)
    countCurrentVisibleReviews() {
        console.log('📜 PRO LIST SCRAPER: Counting reviews - using GLOBAL search first (like extraction)...');
        
        // FIXED: Use the same approach as extraction - search globally first
        // For ProList we primarily use OA1nbd containers, but also try others
        let reviewContainers = document.querySelectorAll('div.OA1nbd');
        console.log(`📜 PRO LIST SCRAPER: Global search found ${reviewContainers.length} OA1nbd containers`);
        
        // If no OA1nbd containers, try other formats (matching single-review-scraper)
        if (reviewContainers.length === 0) {
            reviewContainers = document.querySelectorAll('div.MyEned');
            console.log(`📜 PRO LIST SCRAPER: Global search found ${reviewContainers.length} MyEned containers`);
        }
        
        if (reviewContainers.length === 0) {
            reviewContainers = document.querySelectorAll('div.jftiEf');
            console.log(`📜 PRO LIST SCRAPER: Global search found ${reviewContainers.length} jftiEf containers`);
        }
        
        // Filter out owner responses but INCLUDE reviews with no text content (like single mode)
        const actualReviews = Array.from(reviewContainers).filter(container => {
            // Check for owner response indicators
            const containerText = container.textContent.toLowerCase();
            if (containerText.includes('response from the owner') || 
                containerText.includes('response from owner')) {
                return false; // Exclude owner responses
            }
            
            // Check for owner response span
            const ownerResponseSpan = container.querySelector('span.nM6d2c');
            if (ownerResponseSpan) {
                const spanText = ownerResponseSpan.textContent.toLowerCase();
                if (spanText.includes('response from the owner') || 
                    spanText.includes('response from owner') ||
                    spanText.includes('owner response')) {
                    return false; // Exclude owner responses
                }
            }
            
            // IMPORTANT: Include ALL review containers, even if they have no text content
            // This ensures our count matches what will be extracted (like single mode)
            const notHidden = !container.hasAttribute('hidden') && 
                           container.style.display !== 'none' &&
                           container.style.visibility !== 'hidden';
            
            return notHidden; // Include all visible containers, even empty ones
        });
        
        console.log(`📜 PRO LIST SCRAPER: Found ${actualReviews.length} actual reviews after filtering (including empty ones)`);
        
        return actualReviews.length;
    }

    // Run the read more script (using same selectors as working commit)
    async runReadMoreScript() {
        console.log('🔽 PRO LIST SCRAPER: Running read more script...');
        
        const clickElements = () => {
            // Use the same working selectors as single-review-scraper.js
            var elements1 = document.querySelectorAll('button.w8nwRe.kyuRq');
            var elements2 = document.querySelectorAll('a.MtCSLb');
            var elements3 = document.querySelectorAll('a.review-more-link');
            // Keep the original Pro List specific selector as well
            var elements4 = document.querySelectorAll('a.MtCSLb[role="button"]');
            
            console.log(`🔽 PRO LIST SCRAPER: Found ${elements1.length} w8nwRe.kyuRq buttons`);
            console.log(`🔽 PRO LIST SCRAPER: Found ${elements2.length} MtCSLb links`);
            console.log(`🔽 PRO LIST SCRAPER: Found ${elements3.length} review-more-link links`);
            console.log(`🔽 PRO LIST SCRAPER: Found ${elements4.length} MtCSLb role="button" links`);

            let clickedCount = 0;
            
            // Click all button types
            [elements1, elements2, elements3, elements4].forEach((elementList, listIndex) => {
                elementList.forEach(function(button, index) {
                    try {
                        // Scroll into view first
                        button.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        
                        // Click immediately instead of using setTimeout
                        button.click();
                        clickedCount++;
                        console.log(`👆 PRO LIST SCRAPER: Clicked "More" button ${index + 1} from selector group ${listIndex + 1}`);
                    } catch (error) {
                        console.warn('🔽 PRO LIST SCRAPER: Error clicking button:', error);
                    }
                });
            });

            console.log(`👆 PRO LIST SCRAPER: Clicked ${clickedCount} "More" buttons total`);
            return clickedCount;
        };

        return new Promise((resolve) => {
            setTimeout(() => {
                const result = clickElements();
                resolve(result);
            }, 1000);
        });
    }

    // Extract business name using the same method as working commit
    extractBusinessNameFromNAP() {
        console.log('📊 PRO LIST SCRAPER: Extracting business name using NAP method...');
        
        // Method 1: Try the h1 element used by NAP button
        const headingElement = document.querySelector('h1.DUwDvf.lfPIob');
        if (headingElement) {
            const businessName = headingElement.textContent.trim();
            if (businessName && businessName !== 'Google Maps' && businessName.length > 3) {
                console.log(`📊 PRO LIST SCRAPER: Business name from h1.DUwDvf.lfPIob: "${businessName}"`);
                return businessName;
            }
        }
        
        // Method 2: Try the gmb-nap-wrapper approach as fallback
        let businessNameElement = document.querySelector('.gmb-nap-wrapper .rgnuSb.tZPcob');
        if (businessNameElement) {
            const businessName = businessNameElement.textContent.trim();
            console.log(`📊 PRO LIST SCRAPER: Business name from gmb-nap-wrapper: "${businessName}"`);
            return businessName;
        }
        
        // Method 3: Try just the rgnuSb tZPcob classes
        businessNameElement = document.querySelector('.rgnuSb.tZPcob');
        if (businessNameElement) {
            const businessName = businessNameElement.textContent.trim();
            console.log(`📊 PRO LIST SCRAPER: Business name from rgnuSb.tZPcob: "${businessName}"`);
            return businessName;
        }
        
        // Method 4: Try meta tags
        const metaName = document.querySelector('meta[itemprop="name"]');
        if (metaName) {
            const content = metaName.getAttribute('content');
            if (content && content.includes('·')) {
                const businessName = content.split('·')[0].trim();
                console.log(`📊 PRO LIST SCRAPER: Business name from meta: "${businessName}"`);
                return businessName;
            } else if (content && content !== 'Google Maps') {
                console.log(`📊 PRO LIST SCRAPER: Business name from meta (no separator): "${content}"`);
                return content;
            }
        }
        
        // Method 5: Final fallback - try any h1 element
        const allH1s = document.querySelectorAll('h1');
        for (const h1 of allH1s) {
            const businessName = h1.textContent?.trim();
            if (businessName && 
                businessName.length > 3 && 
                businessName.length < 200 && 
                businessName !== 'Google Maps' &&
                !businessName.toLowerCase().includes('google maps') &&
                !businessName.toLowerCase().includes('search') &&
                !businessName.toLowerCase().includes('menu')) {
                console.log(`📊 PRO LIST SCRAPER: Business name from fallback h1: "${businessName}"`);
                return businessName;
            }
        }
        
        console.log('📊 PRO LIST SCRAPER: ❌ Could not extract business name using any method');
        return 'Unknown Business';
    }

    // Extract all review data from the page
    async extractAllReviews() {
        console.log('📊 PRO LIST SCRAPER: Extracting all reviews...');
        this.reviewData = [];

        // Extract business name using NAP method BEFORE starting review extraction
        const businessName = this.extractBusinessNameFromNAP();
        console.log(`📊 PRO LIST SCRAPER: Final business name: "${businessName}"`);

        // Look for review containers using Pro List specific class
        const allContainers = document.querySelectorAll('div.OA1nbd');
        console.log(`📊 PRO LIST SCRAPER: Found ${allContainers.length} potential review containers with class 'OA1nbd'`);

        // Filter out containers that might be owner responses
        const reviewContainers = [];
        
        allContainers.forEach((container, index) => {
            console.log(`📊 PRO LIST SCRAPER: Checking container ${index + 1} for owner response...`);
            
            // Check for owner response indicators
            const hasOwnerResponse = this.isOwnerResponseContainer(container);
            
            if (!hasOwnerResponse) {
                reviewContainers.push(container);
                console.log(`📊 PRO LIST SCRAPER: ✅ Container ${index + 1} is a valid review`);
            } else {
                console.log(`📊 PRO LIST SCRAPER: ⏭️ Container ${index + 1} appears to be an owner response, skipping`);
            }
        });

        console.log(`📊 PRO LIST SCRAPER: After filtering out owner responses: ${reviewContainers.length} actual review containers`);

        if (reviewContainers.length === 0) {
            console.log('📊 PRO LIST SCRAPER: ❌ No review containers found on the page');
            throw new Error('No review containers found on the page');
        }

        return this.extractReviewsFromContainers(reviewContainers, businessName);
    }

    // Find owner response indicator for a given container
    isOwnerResponseContainer(container) {
        // Check for specific owner response indicators in Pro List structure
        const ownerResponseIndicators = [
            'Response from the owner',
            'Response from owner',
            'Owner response',
            'Business response',
            'Response from business'
        ];

        const containerText = container.textContent.toLowerCase();
        
        for (const indicator of ownerResponseIndicators) {
            if (containerText.includes(indicator.toLowerCase())) {
                console.log(`📊 PRO LIST SCRAPER: Found owner response indicator: "${indicator}"`);
                return true;
            }
        }

        // Check for specific classes that might indicate owner responses
        const ownerResponseClasses = [
            '.owner-response',
            '.business-response',
            '[data-response-type="owner"]'
        ];

        for (const selector of ownerResponseClasses) {
            if (container.querySelector(selector)) {
                console.log(`📊 PRO LIST SCRAPER: Found owner response class: ${selector}`);
                return true;
            }
        }

        return false;
    }

    // Extract reviews from the found containers
    extractReviewsFromContainers(containers, businessName) {
        console.log(`📊 PRO LIST SCRAPER: Processing ${containers.length} review containers...`);
        
        // Apply range limit during extraction (not just at the end)
        let containersToProcess = containers;
        if (this.rangeUtility.hasLimit()) {
            const maxReviews = this.rangeUtility.getMaxReviews();
            containersToProcess = containers.slice(0, maxReviews);
            console.log(`📊 PRO LIST SCRAPER: 🎯 RANGE LIMIT APPLIED: Processing only first ${containersToProcess.length} containers (limit: ${maxReviews})`);
        }

        containersToProcess.forEach((container, index) => {
            try {
                const reviewData = this.extractSingleReview(container, index + 1, businessName);
                if (reviewData) {
                    this.reviewData.push(reviewData);
                    
                    // Double-check range limit during extraction
                    if (this.rangeUtility.hasLimit() && this.reviewData.length >= this.rangeUtility.getMaxReviews()) {
                        console.log(`📊 PRO LIST SCRAPER: 🎯 RANGE LIMIT REACHED during extraction: ${this.reviewData.length}/${this.rangeUtility.getMaxReviews()}`);
                        return; // Stop extracting more reviews
                    }
                }
            } catch (error) {
                console.error(`📊 PRO LIST SCRAPER: Error extracting review ${index + 1}:`, error);
            }
        });

        console.log(`✅ PRO LIST SCRAPER: Successfully extracted ${this.reviewData.length} reviews`);
        return this.reviewData;
    }

    // Extract data from a single review container
    extractSingleReview(container, index, businessName) {
        const reviewData = {
            id: index,
            reviewText: '',
            reviewerName: '',
            rating: null,
            date: null,
            helpfulCount: null,
            response: '',
            photoCount: 0,
            extractedAt: new Date().toISOString(),
            businessName: businessName,
            isEmpty: false // Track if this is an empty review like single mode
        };

        try {
            // Extract reviewer name
            const reviewerElement = this.findReviewerName(container);
            if (reviewerElement) {
                reviewData.reviewerName = reviewerElement.textContent.trim();
            }

            // Extract review text from the OA1nbd div (Pro List specific) - same as working commit
            let reviewText = container.textContent.trim();
            
            // Check if review is empty and mark it (like single mode)
            if (!reviewText || reviewText.length === 0) {
                reviewData.reviewText = 'Review with no text';
                reviewData.isEmpty = true;
                console.log(`📝 PRO LIST SCRAPER: Review ${index} has no text content - marking as empty but valid`);
                
                // If no reviewer name found for empty review, provide default
                if (!reviewData.reviewerName) {
                    reviewData.reviewerName = 'Unknown Reviewer (Empty Review)';
                }
            } else {
                reviewData.reviewText = reviewText;
            }

            // Extract rating
            reviewData.rating = this.extractRating(container);

            // Extract date
            reviewData.date = this.extractDate(container);

            // Extract helpful count
            reviewData.helpfulCount = this.extractHelpfulCount(container);

            // Extract business response
            reviewData.response = this.extractBusinessResponse(container);

            // Count photos
            reviewData.photoCount = this.countReviewPhotos(container);

            // Log differently for empty vs non-empty reviews
            if (reviewData.isEmpty) {
                console.log(`📝 PRO LIST SCRAPER: Extracted EMPTY review ${index}: {reviewer: '${reviewData.reviewerName}', rating: ${reviewData.rating}, textLength: 0 (empty), hasResponse: ${!!reviewData.response}}`);
            } else {
                console.log(`📝 PRO LIST SCRAPER: Extracted review ${index}: {reviewer: '${reviewData.reviewerName}', rating: ${reviewData.rating}, textLength: ${reviewData.reviewText.length}, hasResponse: ${!!reviewData.response}}`);
            }

            return reviewData;

        } catch (error) {
            console.error(`📊 PRO LIST SCRAPER: Error processing review ${index}:`, error);
            return null;
        }
    }

    // Find reviewer name in the Pro List structure (same as working commit)
    findReviewerName(container) {
        // First try: Look for the specific Pro List reviewer name class
        const reviewerNameElement = container.querySelector('div.Vpc5Fe');
        if (reviewerNameElement) {
            console.log(`📊 PRO LIST SCRAPER: Found reviewer name with Vpc5Fe class: ${reviewerNameElement.textContent.trim()}`);
            return reviewerNameElement;
        }

        // Second try: Look in parent/ancestor elements for Vpc5Fe
        let currentElement = container;
        let checkCount = 0;
        
        while (currentElement && checkCount < 5) {
            const nameElement = currentElement.querySelector('div.Vpc5Fe');
            if (nameElement) {
                console.log(`📊 PRO LIST SCRAPER: Found reviewer name in ancestor with Vpc5Fe class: ${nameElement.textContent.trim()}`);
                return nameElement;
            }
            currentElement = currentElement.parentElement;
            checkCount++;
        }

        // Fallback: Look for reviewer name in various possible locations in the Pro List structure
        const possibleSelectors = [
            '.reviewer-name',
            '[data-reviewer]',
            '.name',
            '.author'
        ];

        // Check the container and its parent elements
        currentElement = container;
        checkCount = 0;
        
        while (currentElement && checkCount < 5) {
            for (const selector of possibleSelectors) {
                const element = currentElement.querySelector(selector);
                if (element && element.textContent.trim()) {
                    return element;
                }
            }
            
            // Also check for common patterns in Pro List as final fallback
            const spans = currentElement.querySelectorAll('span');
            for (const span of spans) {
                const text = span.textContent.trim();
                if (text.length > 1 && text.length < 100 &&
                    !text.includes('star') && 
                    !text.includes('ago') && 
                    !text.includes('star') && 
                    !text.includes('helpful') &&
                    !text.toLowerCase().includes('response from') &&
                    !text.toLowerCase().includes('report')) {
                    console.log(`📊 PRO LIST SCRAPER: Found reviewer name via fallback pattern: ${text}`);
                    return span;
                }
            }
            
            currentElement = currentElement.parentElement;
            checkCount++;
        }

        console.log(`📊 PRO LIST SCRAPER: No reviewer name found for review`);
        return null;
    }

    // Extract rating from container
    extractRating(container) {
        // Look for rating indicators
        const ratingSelectors = [
            '[aria-label*="star"]',
            '[title*="star"]',
            '.rating',
            '[data-rating]'
        ];

        for (const selector of ratingSelectors) {
            const element = container.querySelector(selector);
            if (element) {
                const ariaLabel = element.getAttribute('aria-label') || element.getAttribute('title') || '';
                const ratingMatch = ariaLabel.match(/(\d+)\s*star/);
                if (ratingMatch) {
                    return parseInt(ratingMatch[1], 10);
                }
            }
        }

        return null;
    }

    // Extract date from container
    extractDate(container) {
        // Look for date patterns
        const allText = container.textContent;
        const datePatterns = [
            /(\d{1,2}\/\d{1,2}\/\d{4})/,
            /(\d{1,2}-\d{1,2}-\d{4})/,
            /(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2},?\s+\d{4}/i,
            /(\d+)\s+(day|week|month|year)s?\s+ago/i
        ];

        for (const pattern of datePatterns) {
            const match = allText.match(pattern);
            if (match) {
                return match[0];
            }
        }

        return null;
    }

    // Extract helpful count from container
    extractHelpfulCount(container) {
        const allText = container.textContent;
        const helpfulMatch = allText.match(/(\d+)\s+helpful/i);
        return helpfulMatch ? parseInt(helpfulMatch[1], 10) : null;
    }

    // Extract business response from container
    extractBusinessResponse(container) {
        // Look for response indicators
        const responseText = container.textContent.toLowerCase();
        if (responseText.includes('response from') || responseText.includes('owner response')) {
            // Find the response text
            const responseElement = container.querySelector('.owner-response, .business-response');
            if (responseElement) {
                return responseElement.textContent.trim();
            }
        }
        return '';
    }

    // Count photos in review
    countReviewPhotos(container) {
        const photos = container.querySelectorAll('img[src*="photo"], img[src*="image"], .review-photo');
        return photos.length;
    }

    // Wait utility
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Export reviews to CSV
    exportToCSV() {
        if (!this.reviewData || this.reviewData.length === 0) {
            console.log('📊 PRO LIST SCRAPER: No review data to export');
            return '';
        }

        console.log(`📊 PRO LIST SCRAPER: Exporting ${this.reviewData.length} reviews to CSV`);
        
        // Debug: Log sample review data to see what's in business name field
        if (this.reviewData.length > 0) {
            console.log('📊 PRO LIST SCRAPER: Sample review data (first review):', {
                id: this.reviewData[0].id,
                reviewerName: this.reviewData[0].reviewerName,
                businessName: this.reviewData[0].businessName,
                hasBusinessName: !!this.reviewData[0].businessName
            });
        }

        const headers = [
            'ID',
            'Reviewer Name',
            'Rating',
            'Review Text',
            'Date',
            'Helpful Count',
            'Business Response',
            'Photo Count',
            'Extracted At',
            'Business Name'
        ];

        const csvRows = [headers.join(',')];

        this.reviewData.forEach((review, index) => {
            const row = [
                review.id,
                this.escapeCsvValue(review.reviewerName),
                review.rating || '',
                this.escapeCsvValue(review.reviewText),
                this.escapeCsvValue(review.date),
                review.helpfulCount || '',
                this.escapeCsvValue(review.response),
                review.photoCount,
                this.escapeCsvValue(review.extractedAt),
                this.escapeCsvValue(review.businessName)
            ];
            
            // Debug: Log business name for first few rows
            if (index < 3) {
                console.log(`📊 PRO LIST SCRAPER: Row ${index + 1} business name: "${review.businessName}"`);
            }
            
            csvRows.push(row.join(','));
        });

        const csvContent = csvRows.join('\n');
        console.log(`📊 PRO LIST SCRAPER: CSV export completed with ${csvRows.length - 1} data rows`);
        
        return csvContent;
    }

    // Escape CSV values
    escapeCsvValue(value) {
        if (value === null || value === undefined) {
            return '';
        }
        
        const stringValue = String(value);
        
        // If the value contains comma, newline, or quotes, wrap in quotes and escape internal quotes
        if (stringValue.includes(',') || stringValue.includes('\n') || stringValue.includes('"')) {
            return '"' + stringValue.replace(/"/g, '""') + '"';
        }
        
        return stringValue;
    }

    // Get current extraction data
    getCurrentData() {
        return {
            reviewCount: this.reviewData.length,
            reviews: this.reviewData,
            isRunning: this.isRunning
        };
    }
}

// Initialize the ProList scraper
let proListReviewScraper;

// Make it globally accessible for prolist-extraction.js
if (typeof window !== 'undefined') {
    window.proListReviewScraper = null;
}

// Listen for messages
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('🔍 PRO LIST SCRAPER: Received message:', request.action || request.type || request);
    
    if (request.action === 'executeProListReviewExtraction' || request.type === 'executeProListReviewExtraction') {
        if (!proListReviewScraper) {
            proListReviewScraper = new ProListReviewScraper();
            // Make it globally accessible
            if (typeof window !== 'undefined') {
                window.proListReviewScraper = proListReviewScraper;
            }
        }
        
        const maxReviews = request.maxReviews || null;
        console.log(`🔍 PRO LIST SCRAPER: Starting extraction with max reviews: ${maxReviews || 'no limit'}`);
        
        proListReviewScraper.executeExtraction(maxReviews)
            .then(result => {
                console.log('✅ PRO LIST SCRAPER: Extraction completed:', result);
                sendResponse(result);
            })
            .catch(error => {
                console.error('❌ PRO LIST SCRAPER: Extraction failed:', error);
                sendResponse({ error: error.message, reviewCount: 0, reviews: [] });
            });
        
        return true; // Keep message channel open for async response
    }
    
    if (request.action === 'getProListData' || request.type === 'getProListData') {
        if (proListReviewScraper) {
            const data = proListReviewScraper.getCurrentData();
            console.log(`🔍 PRO LIST SCRAPER: Returning current data: ${data.reviewCount} reviews`);
            sendResponse(data);
        } else {
            console.log('🔍 PRO LIST SCRAPER: No scraper instance, returning empty data');
            sendResponse({ reviewCount: 0, reviews: [], isRunning: false });
        }
        
        return true;
    }

    if (request.type === 'startProListReviewAnalysis') {
        console.log('🚀 PRO LIST SCRAPER: Starting review extraction...');
        console.log('🚀 PRO LIST SCRAPER: Max reviews parameter:', request.maxReviews);
        
        // Ensure instance exists
        if (!proListReviewScraper) {
            proListReviewScraper = new ProListReviewScraper();
            if (typeof window !== 'undefined') {
                window.proListReviewScraper = proListReviewScraper;
            }
        }
        
        proListReviewScraper.executeExtraction(request.maxReviews)
            .then(result => {
                console.log('✅ PRO LIST SCRAPER: Extraction completed:', result);
                sendResponse(result);
            })
            .catch(error => {
                console.error('❌ PRO LIST SCRAPER: Extraction failed:', error);
                sendResponse({ 
                    error: error.message,
                    reviewCount: 0,
                    reviews: [],
                    rangeApplied: false,
                    maxReviews: null
                });
            });
        
        return true; // Keep message channel open for async response
    }

    if (request.type === 'exportProListReviewCSV') {
        console.log('📊 PRO LIST SCRAPER: Exporting CSV...');
        
        try {
            const csvContent = window.proListReviewScraper.exportToCSV();
            sendResponse({ 
                success: true,
                csvContent: csvContent,
                businessName: window.proListReviewScraper.businessName || 'Pro List Reviews'
            });
        } catch (error) {
            console.error('❌ PRO LIST SCRAPER: CSV export failed:', error);
            sendResponse({ 
                error: error.message 
            });
        }
        
        return true;
    }
});

// Initialize the global instance immediately
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 PRO LIST SCRAPER: DOM loaded, initializing global instance...');
    if (!proListReviewScraper) {
        proListReviewScraper = new ProListReviewScraper();
        if (typeof window !== 'undefined') {
            window.proListReviewScraper = proListReviewScraper;
            console.log('🔍 PRO LIST SCRAPER: Global instance created and available at window.proListReviewScraper');
        }
    }
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM still loading, wait for DOMContentLoaded
    console.log('🔍 PRO LIST SCRAPER: DOM still loading, waiting for DOMContentLoaded...');
} else {
    // DOM already loaded
    console.log('🔍 PRO LIST SCRAPER: DOM already loaded, initializing global instance immediately...');
    if (!proListReviewScraper) {
        proListReviewScraper = new ProListReviewScraper();
        if (typeof window !== 'undefined') {
            window.proListReviewScraper = proListReviewScraper;
            console.log('🔍 PRO LIST SCRAPER: Global instance created and available at window.proListReviewScraper');
        }
    }
}

console.log('🔍 PRO LIST SCRAPER: Script loaded and ready for ProList review extraction with scrolling and range support'); 