// Global variables to store extracted data
let extractedData = null;
let popupPort = null;

// DOM elements
const statusIndicator = document.getElementById('statusIndicator');
const statusText = document.getElementById('statusText');
const statusWrapper = document.querySelector('.header__status-wrapper');
const versionWrapper = document.getElementById('versionWrapper');
const versionText = document.getElementById('versionText');
const extractAttributesBtn = document.getElementById('extractAttributesBtn');
const claimStatus = document.getElementById('claimStatus');
const exportBtn = document.getElementById('exportBtn');
const copyTextBtn = document.getElementById('copyTextBtn');
const loading = document.getElementById('loading');
const dataContainer = document.getElementById('dataContainer');

// Review analysis DOM elements
const stopReviewAnalysisBtn = document.getElementById('stopReviewAnalysisBtn');
const exportReviewBtn = document.getElementById('exportReviewBtn');
const reviewAnalysisContainer = document.getElementById('reviewAnalysisContainer');
const reviewStatus = document.getElementById('reviewStatus');
const reviewProgressBar = document.getElementById('reviewProgressBar');
const reviewProgressText = document.getElementById('reviewProgressText');
const reviewProgressFill = document.getElementById('reviewProgressFill');
const reviewResults = document.getElementById('reviewResults');
const reviewResultsList = document.getElementById('reviewResultsList');

// Single review scraper DOM elements
const singleReviewScraperBtn = document.getElementById('singleReviewScraperBtn');
const exportSingleReviewBtn = document.getElementById('exportSingleReviewBtn');
const singleReviewContainer = document.getElementById('singleReviewContainer');
const singleReviewResults = document.getElementById('singleReviewResults');
const singleReviewResultsList = document.getElementById('singleReviewResultsList');
const singleReviewProgress = document.getElementById('singleReviewProgress');
const singleReviewProgressText = document.getElementById('singleReviewProgressText');




// Pro List review scraper button
const proListReviewScraperBtn = document.getElementById('proListReviewScraperBtn');

// Services extraction DOM elements
const servicesExtractionControls = document.getElementById('servicesExtractionControls');
const servicesExtractionBtn = document.getElementById('servicesExtractionBtn');
const exportServicesBtn = document.getElementById('exportServicesBtn');
const servicesExtractionContainer = document.getElementById('servicesExtractionContainer');
const servicesStatus = document.getElementById('servicesStatus');
const servicesProgressBar = document.getElementById('servicesProgressBar');
const servicesProgressText = document.getElementById('servicesProgressText');
const servicesProgressFill = document.getElementById('servicesProgressFill');
const servicesResults = document.getElementById('servicesResults');
const servicesResultsList = document.getElementById('servicesResultsList');

// Settings DOM elements
const settingsBtn = document.getElementById('settingsBtn');
const utmWhitelistBtn = document.getElementById('utmWhitelistBtn');
const alertToggleBtn = document.getElementById('alertToggleBtn');
const alertBadge = document.getElementById('alertBadge');
const emailPinnerBtn = document.getElementById('emailPinnerBtn');
const emailPinnerBadge = document.getElementById('emailPinnerBadge');

// Secret debug toggle
const secretDebugToggle = document.getElementById('secretDebugToggle');

// Quick Actions DOM elements
const quickActionsControls = document.getElementById('quickActionsControls');
const htagsBtn = document.getElementById('htagsBtn');
const headingStructureBtn = document.getElementById('headingStructureBtn');
const showLinksBtn = document.getElementById('showLinksBtn');
const showHiddenBtn = document.getElementById('showHiddenBtn');
// const keywordBtn = document.getElementById('keywordBtn'); // Removed - now in context menu
const boldFromSerpBtn = document.getElementById('boldFromSerpBtn');
const schemaBtn = document.getElementById('schemaBtn');
const imagesBtn = document.getElementById('imagesBtn');
const metadataBtn = document.getElementById('metadataBtn');
const utmBuilderBtn = document.getElementById('utmBuilderBtn');
const pageStructureBtn = document.getElementById('pageStructureBtn');
const copyElementBtn = document.getElementById('copyElementBtn');
const linksExtractorBtn = document.getElementById('linksExtractorBtn');
const bulkLinkOpenBtn = document.getElementById('bulkLinkOpenBtn');
const youtubeScraperContainer = document.getElementById('youtubeScraperContainer');
const youtubeScraperIcon = document.getElementById('youtubeScraperIcon');

const colorPickerBtn = document.getElementById('colorPickerBtn');
const responsiveBtn = document.getElementById('responsiveBtn');
const seoTestsBtn = document.getElementById('seoTestsBtn');
const trackerDetectionBtn = document.getElementById('trackerDetectionBtn');

// Initialize popup with connection keepalive
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🚀 Popup: DOM loaded - hiding content and checking onboarding status...');
  
  // CRITICAL: Hide popup content immediately to prevent flash/overlap
  document.body.style.visibility = 'hidden';
  
  // Check onboarding status FIRST before any other initialization
  const onboardingTriggered = await initializeOnboarding();
  
  if (onboardingTriggered) {
    console.log('✨ Onboarding triggered - closing popup window');
    // Keep content hidden and close popup window after brief delay
    setTimeout(() => {
      window.close();
    }, 100);
    return;
  }
  
  console.log('🔄 No onboarding needed - showing popup and proceeding with normal initialization');
  // Show popup content since no onboarding is needed
  document.body.style.visibility = 'visible';
  
  // Test basic ping communication immediately
  try {
    const pingWorking = await isPopupBackgroundReady();
    console.log('🏓 Popup: Background ping test result:', pingWorking);
  } catch (error) {
    console.error('❌ Popup: Background ping test failed:', error);
  }
  
  
  
  // Clear caches on popup open
  currentDomainCache = null;
  utmSettingsCache = null;
  
  // Create a long-lived connection to keep popup alive during operations
  try {
    popupPort = chrome.runtime.connect({ name: 'popup-keepalive' });
    
    popupPort.onMessage.addListener((message) => {
      if (message.type === 'ping') {
        // Respond to keepalive pings
        try {
          popupPort.postMessage({ type: 'pong' });
        } catch (error) {
          console.log('Popup: Failed to respond to ping:', error);
        }
      }
    });
    
    popupPort.onDisconnect.addListener(() => {
      console.log('Popup: Port disconnected');
      popupPort = null;
    });
  } catch (error) {
    console.warn('Popup: Could not create keepalive port:', error);
  }
  
  checkCurrentTabAndExtract();
  
  
  setupQuickActions();
  setupSecretDebugToggle();
  
  // Listen for settings changes to update UTM whitelist button
  chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local' && changes.gmbExtractorSettings) {
      // Clear cache when settings change
      utmSettingsCache = null;
      checkUTMCleanerSettings();
      checkPomodoroSettings();
      checkAlertSettings();
      checkEmailPinnerSettings();
      checkPopupShortcutsSettings();
    }
  });
});

// Check if current tab is a Google Maps business page or Pro List page and auto-extract
function checkCurrentTabAndExtract() {
  // Initialize version text dynamically from manifest
  const version = chrome.runtime.getManifest().version;
  versionText.textContent = 'SEO Time Machines Version: ' + version;
  
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    const currentTab = tabs[0];
    const isGoogleMaps = currentTab.url.includes('google.com/maps');
    const isProList = currentTab.url.includes('google.com/localservices/prolist');
    // Trigger on Google search pages with sca_esv (local business search indicator)
    const isGoogleSearchWithCoordinates = currentTab.url.includes('google.com/search') && 
                                         (currentTab.url.includes('sca_esv') || currentTab.url.includes('mv:[['));
    const isAnyGooglePage = isGoogleMaps || isProList || isGoogleSearchWithCoordinates;
    
    
    // Always show settings button on all pages
    if (settingsBtn) {
      settingsBtn.style.display = 'inline-block';
    }
    
    // Show buttons immediately - no delay needed
    checkUTMCleanerSettings();
    checkPomodoroSettings();
    checkAlertSettings();
    checkEmailPinnerSettings();
    checkPopupShortcutsSettings();
    
    if (isGoogleSearchWithCoordinates) {
      // Show status wrapper and hide version wrapper on Google search with coordinates (extraction available)
      statusWrapper.style.display = 'flex';
      versionWrapper.style.display = 'none';
      statusIndicator.className = 'status__indicator status__indicator--active';
      statusText.textContent = 'Google search with maps detected';
      
      // Hide Maps search controls on Google Search pages
      
      // Hide Quick Actions on Google pages
      if (quickActionsControls) {
        quickActionsControls.style.display = 'none';
      }
      
      // Show export/copy buttons on Google Search pages
      exportBtn.style.display = 'inline-block';
      copyTextBtn.style.display = 'inline-block';
      
      // Add CSS class to hide specific buttons on Google search pages
      document.body.classList.add('google-search-page');
      
      // Show services extraction controls for Google search
      servicesExtractionControls.style.display = 'flex';
      
      // Check for Google search maps listings and auto-analyze
      setTimeout(() => {
        startGoogleSearchReviewAnalysis();
      }, 500);
    } else if (currentTab.url.includes('google.com/search') && currentTab.url.includes('sca_esv')) {
      // Regular Google search page without coordinates - show Quick Actions only
      // Hide status wrapper and show version wrapper since no extraction functionality is available
      statusWrapper.style.display = 'none';
      versionWrapper.style.display = 'flex';
      
      // Hide Maps search controls on Google Search pages
      
      // Show Quick Actions on regular Google search pages
      loadAndShowQuickActions();
      
      // Hide export/copy buttons when showing Quick Actions
      exportBtn.disabled = true;
      copyTextBtn.disabled = true;
      exportBtn.style.display = 'none';
      copyTextBtn.style.display = 'none';
      
      showEmptyState();
    } else if (isProList) {
      // Show status wrapper and hide version wrapper on Pro List pages (extraction available)
      statusWrapper.style.display = 'flex';
      versionWrapper.style.display = 'none';
      statusIndicator.className = 'status__indicator status__indicator--active';
      statusText.textContent = 'Pro List page detected';
      
      // Hide Maps search controls on Pro List pages
      
      // Hide Quick Actions on Google pages
      if (quickActionsControls) {
        quickActionsControls.style.display = 'none';
      }
      
      // Show export/copy buttons on Pro List pages
      exportBtn.style.display = 'inline-block';
      copyTextBtn.style.display = 'inline-block';
      
      // Check for Pro List and analyze
      setTimeout(() => {
        checkForProList();
      }, 500);
    } else if (isGoogleMaps) {
      // Show status wrapper and hide version wrapper on Google Maps pages (extraction available)
      statusWrapper.style.display = 'flex';
      versionWrapper.style.display = 'none';
      statusIndicator.className = 'status__indicator status__indicator--active';
      statusText.textContent = 'Google Maps page detected';
      
      // Hide Maps search controls on Google Maps pages
      
      // Hide Quick Actions on Google pages
      if (quickActionsControls) {
        quickActionsControls.style.display = 'none';
      }
      
      // Show export/copy buttons on Google Maps pages
      exportBtn.style.display = 'inline-block';
      copyTextBtn.style.display = 'inline-block';
      
      // First check if this is a multiple listings page
      setTimeout(() => {
        checkForMultipleListings();
      }, 500);
    } else {
      // Hide status wrapper and show version wrapper on non-Google pages
      statusWrapper.style.display = 'none';
      versionWrapper.style.display = 'flex';
      
      
      // Show Maps search controls when NOT on Google Maps/Pro List pages
      
      // Show Quick Actions on non-Google pages
      loadAndShowQuickActions();
      
      // Hide export/copy buttons when showing Maps search controls
      exportBtn.disabled = true;
      copyTextBtn.disabled = true;
      exportBtn.style.display = 'none';
      copyTextBtn.style.display = 'none';
      
      showEmptyState();
    }
  });
}

// Check for Pro List page and handle accordingly
function checkForProList() {
  chrome.tabs.query({active: true, currentWindow: true}, tabs => {
    // Add timeout to prevent hanging connections
    const timeoutId = setTimeout(() => {
      console.warn('Popup: Message timeout for detectProList');
      showErrorState('Request timed out. Please refresh the page and try again.');
    }, 10000); // 10 second timeout

    chrome.tabs.sendMessage(tabs[0].id, {action: "detectProList"}, (response) => {
      // Clear timeout since we got a response
      clearTimeout(timeoutId);
      
      if (chrome.runtime.lastError) {
        statusText.textContent = 'Content script not loaded - please refresh the page';
        statusIndicator.className = 'status__indicator';
        showErrorState('Content script not loaded. Please refresh the Pro List page and try again.');
        return;
      }
      
      if (response && response.isProList) {
        console.log('Popup: Pro List page confirmed');
        statusText.textContent = 'Pro List page confirmed - analyzing...';
        analyzeProList();
      } else {
        console.log('Popup: Not a Pro List page');
        statusText.textContent = 'Not a Pro List page';
        showErrorState('This does not appear to be a Pro List page.');
      }
    });
  });
}

// Check for multiple listings and handle accordingly
function checkForMultipleListings() {
  chrome.tabs.query({active: true, currentWindow: true}, tabs => {
    // Add timeout to prevent hanging connections
    const timeoutId = setTimeout(() => {
      console.warn('Popup: Message timeout for detectMultipleListings');
      showErrorState('Request timed out. Please refresh the page and try again.');
    }, 10000); // 10 second timeout

    chrome.tabs.sendMessage(tabs[0].id, {action: "detectMultipleListings"}, (response) => {
      // Clear timeout since we got a response
      clearTimeout(timeoutId);
      
      if (chrome.runtime.lastError) {
        statusText.textContent = 'Content script not loaded - please refresh the page';
        statusIndicator.className = 'status__indicator';
        showErrorState('Content script not loaded. Please refresh the Google Maps page and try again.');
        return;
      }
      
      if (response && response.isMultipleListings) {
        console.log('Popup: Multiple listings detected');
        statusText.textContent = 'Multiple listings detected - analyzing...';
        analyzeMultipleListings();
      } else {
        console.log('Popup: Single business page detected');
        statusText.textContent = 'Single business page detected';
        extractData();
      }
    });
  });
}

// Analyze Pro List
function analyzeProList() {
  showLoading(true);
  statusText.textContent = 'Analyzing Pro List...';
  
  chrome.tabs.query({active: true, currentWindow: true}, tabs => {
    // Add timeout to prevent hanging connections
    const timeoutId = setTimeout(() => {
      console.warn('Popup: Message timeout for analyzeProList');
      showLoading(false);
      showErrorState('Analysis timed out. Please refresh the page and try again.');
    }, 30000); // 30 second timeout for analysis

    chrome.tabs.sendMessage(tabs[0].id, {action: "analyzeProList"}, (response) => {
      // Clear timeout since we got a response
      clearTimeout(timeoutId);
      
      showLoading(false);
      
      if (chrome.runtime.lastError) {
        statusText.textContent = 'Connection error during analysis';
        statusIndicator.className = 'status__indicator';
        showErrorState('Failed to connect to content script. Please refresh the page and try again.');
        return;
      }
      
      if (response && response.success && response.data) {
        displayProListAnalysis(response.data);
        exportBtn.disabled = false;
        copyTextBtn.disabled = false;
        statusText.textContent = 'Pro List analysis complete';
        statusIndicator.className = 'status__indicator status__indicator--active';
        
        // REMOVED: Automatic review analysis trigger 
        // User should manually click "Start Extraction" button instead
        console.log('✅ POPUP: ProList analysis complete. User can now manually start review extraction via the Start Extraction button.');
        
      } else {
        statusText.textContent = 'Failed to analyze Pro List';
        statusIndicator.className = 'status__indicator';
        showErrorState(response?.error || 'Unknown error analyzing Pro List');
        console.error('Pro List analysis error:', response);
      }
    });
  });
}

// Analyze multiple listings
function analyzeMultipleListings() {
  showLoading(true);
  statusText.textContent = 'Analyzing multiple listings...';
  
  chrome.tabs.query({active: true, currentWindow: true}, tabs => {
    // Add timeout to prevent hanging connections
    const timeoutId = setTimeout(() => {
      console.warn('Popup: Message timeout for analyzeMultipleListings');
      showLoading(false);
      showErrorState('Analysis timed out. Please refresh the page and try again.');
    }, 30000); // 30 second timeout for analysis

    chrome.tabs.sendMessage(tabs[0].id, {action: "analyzeMultipleListings"}, (response) => {
      // Clear timeout since we got a response
      clearTimeout(timeoutId);
      
      showLoading(false);
      
      if (chrome.runtime.lastError) {
        statusText.textContent = 'Connection error during analysis';
        statusIndicator.className = 'status__indicator';
        showErrorState('Failed to connect to content script. Please refresh the page and try again.');
        return;
      }
      
      if (response && response.isMultipleListings && response.analysis) {
        displayMultipleListingsAnalysis(response);
        exportBtn.disabled = false;
        copyTextBtn.disabled = false;
        statusText.textContent = 'Multiple listings analysis complete';
        statusIndicator.className = 'status__indicator status__indicator--active';
      } else {
        statusText.textContent = 'Failed to analyze multiple listings';
        statusIndicator.className = 'status__indicator';
        showErrorState(response?.error || 'Unknown error analyzing multiple listings');
        console.error('Multiple listings analysis error:', response);
      }
    });
  });
}

// Display Pro List analysis results
function displayProListAnalysis(data) {
  if (!data || !data.analysis) {
    showErrorState('Error displaying Pro List analysis');
    return;
  }
  
  let html = '';
  
  // Search Information Section
  html += '<div class="data-section">';
  html += '<div class="data-section__title">Search Information</div>';
  html += createDataItemWithLabel('Search Term', data.searchTerm);
  html += createDataItemWithLabel('Analysis Type', 'Pro List Analysis');
  html += createDataItemWithLabel('Total Businesses', data.totalBusinesses);
  html += createDataItemWithLabel('Timestamp', new Date(data.timestamp).toLocaleString());
  html += '</div>';
  
  // Category Analysis Section
  const categoryData = data.analysis.categories;
  if (categoryData && categoryData.success && categoryData.data) {
    const stats = categoryData.data;
    
    html += '<div class="data-section">';
    html += '<div class="data-section__title">Category Analysis</div>';
    html += createDataItemWithLabel('Total Categories', stats.totalCategories);
    html += createDataItemWithLabel('Total Listings', stats.totalListings);
    html += '</div>';
    
    // Top Categories Section
    if (stats.topCategories && stats.topCategories.length > 0) {
      html += '<div class="data-section">';
      html += '<div class="data-section__title">Top Categories</div>';
      
      stats.topCategories.forEach((cat, index) => {
        const label = `${index + 1}. ${cat.category}`;
        const value = `${cat.count} listings (${cat.percentage}%)`;
        html += createDataItemWithLabel(label, value);
      });
      
      html += '</div>';
    }
  }
  
  // AUTOMATIC REVIEW ANALYSIS SECTION - Added to match Multiple Listings behavior
  if (data.businesses && data.businesses.length > 0) {
    console.log('🚀 ProList: Starting automatic review analysis...');
    
    // Calculate aggregate data for review analysis (same logic as proListExtractor.calculateAggregateData)
    const businesses = data.businesses;
    let totalReviews = 0;
    let totalRating = 0;
    let businessesWithRating = 0;
    let highestRated = { name: 'N/A', rating: 0 };
    let mostReviews = { name: 'N/A', count: 0 };
    
    // Rating distribution calculation
    const ratingDistribution = { '5': 0, '4': 0, '3': 0, '2': 0, '1': 0, '0': 0 };
    
    // Category breakdown
    const categoryBreakdown = {};
    
    // Process each business
    // Process ORGANIC businesses only for accurate analysis
    organicBusinesses.forEach(business => {
      if (business.reviewCount && !isNaN(business.reviewCount)) {
        totalReviews += parseInt(business.reviewCount);
        
        if (business.reviewCount > mostReviews.count) {
          mostReviews = {
            name: business.businessName || 'Unknown',
            count: business.reviewCount
          };
        }
      }
      
      if (business.rating && !isNaN(business.rating)) {
        totalRating += parseFloat(business.rating);
        businessesWithRating++;
        
        if (business.rating > highestRated.rating) {
          highestRated = {
            name: business.businessName || 'Unknown',
            rating: business.rating,
            reviewCount: business.reviewCount || 0
          };
        }
        
        // Rating distribution categorization using ORGANIC data only
        const rating = parseFloat(business.rating);
        let ratingRange;
        
        if (rating >= 5.0) {
          ratingRange = '5'; // Exactly 5.0 stars
        } else if (rating >= 4.0) {
          ratingRange = '4'; // 4.0-4.9 stars
        } else if (rating >= 3.0) {
          ratingRange = '3'; // 3.0-3.9 stars
        } else if (rating >= 2.0) {
          ratingRange = '2'; // 2.0-2.9 stars
        } else if (rating >= 1.0) {
          ratingRange = '1'; // 1.0-1.9 stars
        } else {
          ratingRange = '0'; // No rating or below 1.0
        }
        
        ratingDistribution[ratingRange] = (ratingDistribution[ratingRange] || 0) + 1;
      } else {
        // Business with no rating
        ratingDistribution['0'] = (ratingDistribution['0'] || 0) + 1;
      }
      
      // Category breakdown using ORGANIC data only
      if (business.categories && Array.isArray(business.categories)) {
        business.categories.forEach(category => {
          if (category && category.trim() && category.toLowerCase() !== 'favourites') {
            categoryBreakdown[category] = (categoryBreakdown[category] || 0) + 1;
          }
        });
      }
    });
    
      // Filter out sponsored businesses for accurate organic analysis
  const organicBusinesses = businesses.filter(business => !business.sponsored && !business.isSponsored);
  console.log(`📊 Popup: Organic businesses: ${organicBusinesses.length} (${businesses.length - organicBusinesses.length} sponsored excluded)`);
  
  // Get top 3 ORGANIC listings by position
  const top3OrganicListings = organicBusinesses.slice(0, 3).map((business, index) => ({
    name: business.businessName || `Business ${index + 1}`,
    reviewCount: business.reviewCount || 0,
    rating: business.rating || 0
  }));
  
  // Calculate top 3 organic average review count
  const top3OrganicReviewCounts = top3OrganicListings.map(b => b.reviewCount || 0);
  const averageReviewCountTop3 = top3OrganicReviewCounts.length > 0 ? 
    (top3OrganicReviewCounts.reduce((sum, count) => sum + count, 0) / top3OrganicReviewCounts.length).toFixed(1) : 0;
    
    const averageRating = businessesWithRating > 0 ? 
      (totalRating / businessesWithRating) : 0;
    
    // Generate and add the review analysis HTML
    if (totalReviews > 0 || businessesWithRating > 0) {
      console.log('📊 ProList: Generating automatic review analysis with ORGANIC data:', {
        totalBusinesses: organicBusinesses.length,
        totalReviews,
        averageRating,
        averageReviewCountTop3,
        ratingDistribution,
        highestRated,
        mostReviews,
        sponsoredExcluded: businesses.length - organicBusinesses.length
      });
      
      const reviewAnalysisHTML = generateReviewAnalysisHTML({
        reviewData: organicBusinesses, // Use organic businesses only
        totalBusinesses: organicBusinesses.length, // Organic count
        totalReviews: totalReviews,
        averageRating: averageRating,
        averageReviewCountTop3: parseFloat(averageReviewCountTop3),
        ratingDistribution: ratingDistribution,
        categoryBreakdown: categoryBreakdown,
        highestRated: highestRated,
        mostReviews: mostReviews,
        sponsoredExcluded: businesses.length - organicBusinesses.length
      }, 'PROLIST REVIEW ANALYSIS (ORGANIC)');
      
      // Append the review analysis HTML to the existing content
      html += '<div class="data-section">';
      html += '<div class="data-section__title">Review Analysis Results</div>';
      html += reviewAnalysisHTML;
      html += '</div>';
      
      console.log('✅ ProList: Review analysis table generated and added to display');
    } else {
      console.log('⚠️ ProList: No review data found for automatic analysis');
    }
  }
  
  dataContainer.innerHTML = html;
  addCopyEventListeners();
  
  
  // Hide Extract Attributes button for Pro List mode (not applicable)
  extractAttributesBtn.style.display = 'none';
  
  // Hide review analysis buttons for Pro List mode (analysis happens automatically)
  exportReviewBtn.style.display = 'inline-block';
  
  // Hide single review scraper buttons for Pro List mode
  singleReviewScraperBtn.style.display = 'none';
  exportSingleReviewBtn.style.display = 'none';
  
  // Show Pro List review scraper button for Pro List mode
  proListReviewScraperBtn.style.display = 'inline-block';
  
  // Store the analysis data for export
  extractedData = {
    type: 'prolist_analysis',
    searchTerm: data.searchTerm,
    timestamp: data.timestamp,
    totalBusinesses: data.totalBusinesses,
    businesses: data.businesses,
    analysis: data.analysis
  };
  
  // Add review analysis data to extractedData if we generated it
  if (data.businesses && data.businesses.length > 0) {
    const businesses = data.businesses;
    let totalReviews = 0;
    let totalRating = 0;
    let businessesWithRating = 0;
    
    businesses.forEach(business => {
      if (business.reviewCount && !isNaN(business.reviewCount)) {
        totalReviews += parseInt(business.reviewCount);
      }
      if (business.rating && !isNaN(business.rating)) {
        totalRating += parseFloat(business.rating);
        businessesWithRating++;
      }
    });
    
    if (totalReviews > 0 || businessesWithRating > 0) {
      const averageRating = businessesWithRating > 0 ? (totalRating / businessesWithRating) : 0;
      
      // Filter out sponsored businesses for accurate top 3 calculation
      const organicBusinessesForExport = businesses.filter(business => !business.sponsored && !business.isSponsored);
      const top3OrganicReviewCounts = organicBusinessesForExport.slice(0, 3).map(b => b.reviewCount || 0);
      const averageReviewCountTop3 = top3OrganicReviewCounts.length > 0 ? 
        (top3OrganicReviewCounts.reduce((sum, count) => sum + count, 0) / top3OrganicReviewCounts.length) : 0;
      
      extractedData.reviewAnalysis = {
        type: 'prolist_review_analysis',
        timestamp: data.timestamp,
        totalBusinesses: organicBusinessesForExport.length, // Use organic count
        totalReviews: totalReviews,
        averageRating: averageRating,
        averageReviewCountTop3: averageReviewCountTop3,
        reviewData: organicBusinessesForExport, // Use organic businesses
        sponsoredExcluded: businesses.length - organicBusinessesForExport.length
      };
      
      console.log('💾 ProList: Review analysis data stored for export');
    }
  }
}

// Display multiple listings analysis results
function displayMultipleListingsAnalysis(data) {
  if (!data || !data.analysis) {
    showErrorState('Error displaying multiple listings analysis');
    return;
  }
  
  let html = '';
  
  // Search Information Section
  html += '<div class="data-section">';
  html += '<div class="data-section__title">Search Information</div>';
  html += createDataItemWithLabel('Search Term', data.searchTerm);
  html += createDataItemWithLabel('Analysis Type', 'Multiple Listings');
  html += createDataItemWithLabel('Timestamp', new Date(data.timestamp).toLocaleString());
  html += '</div>';
  
  // Category Analysis Section
  const categoryData = data.analysis?.categories;
  if (categoryData && categoryData.success && categoryData.data) {
    const stats = categoryData.data;
    
    html += '<div class="data-section">';
    html += '<div class="data-section__title">Category Analysis</div>';
    html += createDataItemWithLabel('Total Categories', stats.totalCategories);
    html += createDataItemWithLabel('Total Listings', stats.totalListings);
    html += createDataItemWithLabel('Average Categories per Listing', stats.averageCategoriesPerListing);
    html += createDataItemWithLabel('Max Categories per Listing', stats.maxCategoriesPerListing);
    html += '</div>';
    
    // Top Categories Section
    if (stats.topCategories && stats.topCategories.length > 0) {
      html += '<div class="data-section">';
      html += '<div class="data-section__title">Top Categories</div>';
      
      stats.topCategories.forEach((cat, index) => {
        const label = `${index + 1}. ${cat.category}`;
        const value = `${cat.count} listings (${cat.percentage}%)`;
        html += createDataItemWithLabel(label, value);
      });
      
      html += '</div>';
    }
  }
  
  // Review Analysis Section - Use the same logic as ProList mode
  const reviewData = data.analysis?.reviews;
  if (reviewData && reviewData.success && reviewData.data && reviewData.data.length > 0) {
    // Check if we have aggregate data (like ProList) or raw data (like regular search)
    const aggregateItem = reviewData.data.find(item => item.isAggregate);
    const businessData = reviewData.data.filter(item => !item.isAggregate);
    
    if (aggregateItem && aggregateItem.aggregateData) {
      // ProList-style aggregate data available - use the same display function as ProList
      const agg = aggregateItem.aggregateData;
      
      console.log('🔍 MULTIPLE LISTINGS REVIEW ANALYSIS DETAILS:');
      console.log('Total businesses:', agg.totalBusinesses);
      console.log('Total reviews:', agg.totalReviews);
      console.log('Average rating:', agg.averageRating);
      console.log('Top 3 average reviews:', agg.averageReviewCountTop3);
      console.log('Highest rated:', agg.highestRated);
      console.log('Most reviews:', agg.mostReviews);
      console.log('Rating distribution:', agg.ratingDistribution);
      console.log('Category breakdown:', agg.categoryBreakdown);
      
      // Use the same HTML generation function as ProList
      const reviewAnalysisHTML = generateReviewAnalysisHTML({
        reviewData: businessData,
        totalBusinesses: agg.totalBusinesses,
        totalReviews: agg.totalReviews,
        averageRating: agg.averageRating,
        averageReviewCountTop3: agg.averageReviewCountTop3,
        ratingDistribution: agg.ratingDistribution,
        categoryBreakdown: agg.categoryBreakdown,
        highestRated: agg.highestRated,
        mostReviews: agg.mostReviews
      }, 'MULTIPLE LISTINGS REVIEW ANALYSIS');
      
      console.log('🔍 MULTIPLE LISTINGS: Rating distribution being passed to HTML generator:', agg.ratingDistribution);
      console.log('🔍 MULTIPLE LISTINGS: Sample business data:', businessData.slice(0, 3));
      
      // Append the review analysis HTML to the existing content
      html += '<div class="data-section">';
      html += '<div class="data-section__title">Review Analysis Results</div>';
      html += reviewAnalysisHTML;
      html += '</div>';
      
    } else {
      // Fallback to basic review summary if no aggregate data
      html += '<div class="data-section">';
      html += '<div class="data-section__title">Review Analysis</div>';
      html += createDataItemWithLabel('Businesses with Review Data', reviewData.reviewCount);
      html += createDataItemWithLabel('Total Businesses Analyzed', reviewData.totalListings);
      html += createDataItemWithLabel('Success Rate', `${((reviewData.reviewCount / reviewData.totalListings) * 100).toFixed(1)}%`);
      html += createDataItemWithLabel('Status', reviewData.message);
      
      // Review Summary Statistics if data is available
      if (reviewData.data && reviewData.data.length > 0) {
        const reviewStats = calculateReviewStats(reviewData.data);
        
        html += '<div class="data-section">';
        html += '<div class="data-section__title">Review Statistics</div>';
        html += createDataItemWithLabel('Average Rating', reviewStats.averageRating ? reviewStats.averageRating.toFixed(1) : 'N/A');
        html += createDataItemWithLabel('Total Reviews', reviewStats.totalReviews || 'N/A');
        html += createDataItemWithLabel('Businesses with Ratings', reviewStats.businessesWithRatings);
        html += createDataItemWithLabel('Businesses with Review Counts', reviewStats.businessesWithReviewCounts);
        html += '</div>';
      }
      html += '</div>';
    }
  } else if (reviewData) {
    // Show review analysis error
    html += '<div class="data-section">';
    html += '<div class="data-section__title">Review Analysis</div>';
    if (reviewData.success === false) {
      html += createDataItemWithLabel('Review Analysis', 'Failed');
      html += createDataItemWithLabel('Error', reviewData.error || 'Unknown error');
    } else {
      html += createDataItemWithLabel('Review Analysis', 'No review data found');
      html += createDataItemWithLabel('Status', reviewData.message || 'No reviews available');
    }
    html += '</div>';
  }
  
  dataContainer.innerHTML = html;
  addCopyEventListeners();
  
  // Update the Review Gaps report asynchronously after rendering (if review data available)
  if (reviewData && reviewData.success && reviewData.data) {
    const aggregateItem = reviewData.data.find(item => item.isAggregate);
    const businessData = reviewData.data.filter(item => !item.isAggregate);
    if (aggregateItem && aggregateItem.aggregateData && businessData.length > 0) {
      setTimeout(() => updateReviewGapsReport(businessData), 100);
    }
  }
  
  // Hide Extract Attributes button for multiple listings mode (not applicable)
  extractAttributesBtn.style.display = 'none';
  
  // Hide review analysis buttons for multiple listings mode (analysis already done automatically)
  exportReviewBtn.style.display = 'inline-block'; // Keep export button
  
  // Show DATA SCRAPER buttons for multiple listings mode (user specifically requested this)
  singleReviewScraperBtn.style.display = 'inline-block';
  exportSingleReviewBtn.style.display = 'none'; // Keep export hidden initially
  
  // Hide Pro List review scraper button for multiple listings mode
  proListReviewScraperBtn.style.display = 'none';
  
  // Hide the review analysis container to prevent duplicate displays
  reviewAnalysisContainer.style.display = 'none';
  
  // Store the analysis data for export - Include review data if available
  extractedData = {
    type: 'multiple_listings',
    searchTerm: data.searchTerm,
    timestamp: data.timestamp,
    analysis: data.analysis
  };
  
  // If we have review analysis data with aggregate info, add it to extractedData for export
  if (reviewData && reviewData.success && reviewData.data) {
    const aggregateItem = reviewData.data.find(item => item.isAggregate);
    if (aggregateItem && aggregateItem.aggregateData) {
      const agg = aggregateItem.aggregateData;
      extractedData.reviewAnalysis = {
        type: 'multiple_listings_review_analysis',
        timestamp: data.timestamp,
        totalBusinesses: agg.totalBusinesses,
        totalReviews: agg.totalReviews,
        averageRating: agg.averageRating,
        averageReviewCountTop3: agg.averageReviewCountTop3,
        reviewData: reviewData.data.filter(item => !item.isAggregate),
        ratingDistribution: agg.ratingDistribution,
        categoryBreakdown: agg.categoryBreakdown
      };
    }
  }
}

// Helper function to calculate review statistics
function calculateReviewStats(reviewData) {
  let totalRatings = 0;
  let ratingCount = 0;
  let totalReviews = 0;
  let businessesWithRatings = 0;
  let businessesWithReviewCounts = 0;
  
  reviewData.forEach(business => {
    if (business.rating !== null && business.rating !== undefined && !isNaN(business.rating)) {
      totalRatings += parseFloat(business.rating);
      ratingCount++;
      businessesWithRatings++;
    }
    
    if (business.reviewCount !== null && business.reviewCount !== undefined && !isNaN(business.reviewCount)) {
      totalReviews += parseInt(business.reviewCount);
      businessesWithReviewCounts++;
    }
  });
  
  return {
    averageRating: ratingCount > 0 ? totalRatings / ratingCount : null,
    totalReviews: totalReviews,
    businessesWithRatings: businessesWithRatings,
    businessesWithReviewCounts: businessesWithReviewCounts
  };
}


// Start Google search review analysis automatically with improved error handling
function startGoogleSearchReviewAnalysis() {
  console.log('Popup: Starting Google search review analysis...');
  
  // Show the review analysis container
  reviewAnalysisContainer.style.display = 'block';
  reviewStatus.textContent = 'Starting Google search review analysis...';
  reviewProgressBar.style.display = 'block';
  reviewProgressFill.style.width = '0%';
  reviewProgressText.textContent = 'Initializing...';
  
  // Show progress section for new analysis
  showProgressSection();
  
  // Hide other UI elements that aren't relevant for Google search
  extractAttributesBtn.style.display = 'none';
  singleReviewScraperBtn.style.display = 'none';
  exportSingleReviewBtn.style.display = 'none';
  proListReviewScraperBtn.style.display = 'none';
  
  // Hide redundant buttons for Google search mode (analysis is automatic)
  exportReviewBtn.style.display = 'inline-block';
  
  // Start the analysis with improved error handling
  chrome.tabs.query({active: true, currentWindow: true}, tabs => {
    // Add timeout to prevent hanging connections
    const timeoutId = setTimeout(() => {
      console.warn('Popup: Message timeout for startGoogleSearchReviewAnalysis');
      reviewStatus.textContent = 'Request timed out';
      showReviewError('Analysis request timed out. Please refresh the page and try again.');
    }, 15000); // 15 second timeout for analysis start

    // Set up listener BEFORE sending the message to avoid race conditions
    setupGoogleSearchReviewAnalysisListener();

    chrome.tabs.sendMessage(tabs[0].id, {action: "startGoogleSearchReviewAnalysis"}, (response) => {
      // Clear timeout since we got a response
      clearTimeout(timeoutId);
      
      // Check for Chrome runtime errors
      if (chrome.runtime.lastError) {
        reviewStatus.textContent = 'Connection error';
        showReviewError('Failed to connect to content script. Please refresh the page and try again.');
        return;
      }
      
      if (response && response.success) {
        console.log('Popup: Google search review analysis started successfully');
        reviewStatus.textContent = 'Google search review analysis in progress...';
      } else {
        console.error('Popup: Failed to start Google search review analysis:', response);
        reviewStatus.textContent = 'Failed to start analysis';
        showReviewError(response?.error || 'Unknown error starting Google search review analysis');
      }
    });
  });
}

// Set up listener for Google search review analysis updates with better cleanup
function setupGoogleSearchReviewAnalysisListener() {
  // Remove any existing listeners first to prevent duplicates
  if (window.googleSearchReviewListener) {
    chrome.runtime.onMessage.removeListener(window.googleSearchReviewListener);
  }

  // Create and store the listener function
  window.googleSearchReviewListener = function(message, sender, sendResponse) {
    if (message.type === 'reviewAnalysisHalt') {
      console.log('Popup: Review analysis halted:', message.reason);
      reviewStatus.textContent = 'No business listings detected';
      showReviewError(message.message || 'This search page does not contain business listings with reviews.');
      
      // Hide the progress section
      hideProgressSection();
      
      // Remove this listener
      chrome.runtime.onMessage.removeListener(window.googleSearchReviewListener);
      window.googleSearchReviewListener = null;
      return;
    }
    
    if (message.action === 'googleSearchReviewAnalysisUpdate') {
      console.log('Popup: Received Google search review analysis update:', message.status);
      
      try {
        switch (message.status) {
          case 'started':
            reviewStatus.textContent = `Analyzing ${message.data.total} businesses...`;
            reviewProgressText.textContent = `0 / ${message.data.total} businesses analyzed`;
            break;
            
          case 'progress':
            const progressPercent = (message.data.current / message.data.total) * 100;
            reviewProgressFill.style.width = `${progressPercent}%`;
            reviewProgressText.textContent = `${message.data.current} / ${message.data.total} businesses analyzed`;
            break;
            
          case 'completed':
            reviewStatus.textContent = 'Google search review analysis completed!';
            reviewProgressFill.style.width = '100%';
            reviewProgressText.textContent = `${message.data.total} / ${message.data.total} businesses analyzed`;
            displayGoogleSearchReviewResults(message.data);
            
            // Hide the progress section after completion for minimalistic view
            hideProgressSection();
            
            // Remove this listener
            chrome.runtime.onMessage.removeListener(window.googleSearchReviewListener);
            window.googleSearchReviewListener = null;
            break;
            
          case 'error':
            reviewStatus.textContent = 'Analysis failed';
            showReviewError(message.data.error || 'Unknown error during analysis');
            
            // Hide the progress section on error as well for minimalistic view
            hideProgressSection();
            
            // Remove this listener
            chrome.runtime.onMessage.removeListener(window.googleSearchReviewListener);
            window.googleSearchReviewListener = null;
            break;
            
          case 'stopped':
            reviewStatus.textContent = 'Analysis stopped by user';
            displayGoogleSearchReviewResults(message.data);
            
            // Hide the progress section when stopped for minimalistic view
            hideProgressSection();
            
            // Remove this listener
            chrome.runtime.onMessage.removeListener(window.googleSearchReviewListener);
            window.googleSearchReviewListener = null;
            break;
        }
      } catch (error) {
        console.error('Popup: Error handling analysis update:', error);
        reviewStatus.textContent = 'Error processing update';
        showReviewError('Error processing analysis update');
        
        // Remove this listener on error
        chrome.runtime.onMessage.removeListener(window.googleSearchReviewListener);
        window.googleSearchReviewListener = null;
      }
    }
  };

  // Add the listener
  chrome.runtime.onMessage.addListener(window.googleSearchReviewListener);
  
  // Set up cleanup when popup closes/unloads
  window.addEventListener('beforeunload', () => {
    if (window.googleSearchReviewListener) {
      chrome.runtime.onMessage.removeListener(window.googleSearchReviewListener);
      window.googleSearchReviewListener = null;
    }
  });
}

// Helper function to generate review analysis HTML (shared between different analysis types)
function generateReviewAnalysisHTML(data, title = 'REVIEW ANALYSIS') {
  const { reviewData, totalBusinesses, totalReviews, averageRating, averageReviewCountTop3, ratingDistribution, categoryBreakdown, highestRated, mostReviews } = data;
  
  // Find highest rated business NOT in top 3
  const businessesNotInTop3 = reviewData.slice(3); // Skip first 3 businesses
  const highestRatedNotInTop3 = businessesNotInTop3.length > 0 ? 
    businessesNotInTop3.reduce((max, business) => {
      if (business.rating > max.rating) {
        return business;
      } else if (business.rating === max.rating && business.reviewCount > max.reviewCount) {
        return business; // If ratings are equal, prefer one with more reviews
      }
      return max;
    }, businessesNotInTop3[0]) : null;
  
  let html = `
    <div class="review-analysis">
      <div class="review-analysis__header">
      ${title}
      </div>
      
      <!-- Main Stats Grid -->
      <div class="review-analysis__stats-grid">
        <div class="review-analysis__stats-card">
          <div class="review-analysis__stats-label">TOTAL BUSINESSES</div>
        <div class="review-analysis__stats-value">${totalBusinesses}</div>
        </div>
        
        <div class="review-analysis__stats-card">
          <div class="review-analysis__stats-label">TOTAL REVIEWS</div>
        <div class="review-analysis__stats-value">${totalReviews.toLocaleString()}</div>
        </div>
        
        <div class="review-analysis__stats-card">
          <div class="review-analysis__stats-label">AVERAGE RATING</div>
        <div class="review-analysis__stats-value">${averageRating.toFixed(2)}</div>
        </div>
        
        <div class="review-analysis__stats-card">
          <div class="review-analysis__stats-label">AVG TOP 3 REVIEWS</div>
        <div class="review-analysis__stats-value">${averageReviewCountTop3}</div>
        </div>
      </div>
      
      <!-- Top Performers -->
      <div class="review-analysis__top-performers">
        <div class="review-analysis__performer-card">
          <div class="review-analysis__performer-label">HIGHEST RATED in top 3</div>
          <div class="review-analysis__performer-name-link" onclick="openBusinessWebsite('${encodeURIComponent(highestRated.businessName || highestRated.name)}')" title="Click to visit website: ${highestRated.businessName || highestRated.name}">
            ${highestRated.businessName || highestRated.name}
          </div>
        <div class="review-analysis__performer-rating">${generateFlatStars(highestRated.rating)}</div>
          <div class="review-analysis__performer-reviews">${(highestRated.reviewCount || highestRated.count || 0).toLocaleString()} reviews</div>
        </div>
        
        <div class="review-analysis__performer-card">
          <div class="review-analysis__performer-label">HIGHEST RATED (NOT) in top 3</div>
          ${highestRatedNotInTop3 ? `
            <div class="review-analysis__performer-name-link" onclick="openBusinessWebsite('${encodeURIComponent(highestRatedNotInTop3.businessName || highestRatedNotInTop3.name)}')" title="Click to visit website: ${highestRatedNotInTop3.businessName || highestRatedNotInTop3.name}">
              ${highestRatedNotInTop3.businessName || highestRatedNotInTop3.name}
            </div>
            <div class="review-analysis__performer-rating">${generateFlatStars(highestRatedNotInTop3.rating)}</div>
            <div class="review-analysis__performer-reviews">${(highestRatedNotInTop3.reviewCount || 0).toLocaleString()} reviews</div>
          ` : `
            <div class="review-analysis__performer-name">No businesses found</div>
            <div class="review-analysis__performer-rating">N/A</div>
            <div class="review-analysis__performer-reviews">0 reviews</div>
          `}
        </div>
      </div>
      
      <!-- SEO Metrics -->
      <div class="review-analysis__seo-metrics">
        <div class="review-analysis__seo-card">
          <div class="review-analysis__seo-label">TOP 3 REVIEW SHARE</div>
          <div class="review-analysis__seo-value">${calculateTop3ReviewShare(reviewData, totalReviews)}%</div>
          <div class="review-analysis__seo-subtitle">of all reviews</div>
        </div>
        
        <div class="review-analysis__seo-card">
          <div class="review-analysis__seo-label">MARKET QUALITY</div>
          <div class="review-analysis__seo-value">${calculateMarketQuality(reviewData)}%</div>
          <div class="review-analysis__seo-subtitle">above 4.0 stars</div>
        </div>
      </div>
      
      <!-- Rating Distribution -->
      <div class="review-analysis__section">
        <div class="review-analysis__data-section__title">
        ★ RATING DISTRIBUTION
        </div>
        
        <div class="review-analysis__chart-container">
          <div class="review-analysis__chart-y-axis">
            <div class="review-analysis__y-label" data-value="100">100%</div>
            <div class="review-analysis__y-label" data-value="75">75%</div>
            <div class="review-analysis__y-label" data-value="50">50%</div>
            <div class="review-analysis__y-label" data-value="25">25%</div>
            <div class="review-analysis__y-label" data-value="0">0%</div>
          </div>
          
          <div class="review-analysis__chart-area">
            <div class="review-analysis__chart-grid">
              <div class="review-analysis__grid-line" data-value="100"></div>
              <div class="review-analysis__grid-line" data-value="75"></div>
              <div class="review-analysis__grid-line" data-value="50"></div>
              <div class="review-analysis__grid-line" data-value="25"></div>
              <div class="review-analysis__grid-line" data-value="0"></div>
            </div>
            
            <div class="review-analysis__bars">`;

  // Calculate maximum count for bar height scaling
  const maxCount = Math.max(...Object.values(ratingDistribution));
  
  // Add rating distribution bars in order: 5, 4, 3, 2, 1, No rating
  const ratingOrder = [
    { range: '5', label: '★ 5.0', modifier: 'five-star' },
    { range: '4', label: '★ 4.0-4.9', modifier: 'four-star' },
    { range: '3', label: '★ 3.0-3.9', modifier: 'three-star' },
    { range: '2', label: '★ 2.0-2.9', modifier: 'two-star' },
    { range: '1', label: '★ 1.0-1.9', modifier: 'one-star' },
    { range: '0', label: '☆ None', modifier: 'no-rating' }
  ];

  ratingOrder.forEach(({ range, label, modifier }) => {
    const count = ratingDistribution[range] || 0;
    const percentage = totalBusinesses > 0 ? ((count / totalBusinesses) * 100).toFixed(1) : '0.0';
    const barHeight = maxCount > 0 ? (count / maxCount) * 100 : 0;
    
    html += `
      <div class="review-analysis__bar-column">
        <div class="review-analysis__bar-wrapper">
          <div class="review-analysis__bar review-analysis__bar--${modifier}" 
               style="height: ${barHeight}%;"
               data-count="${count}"
               data-percentage="${percentage}%">
          </div>
          <div class="review-analysis__bar-value">${count}</div>
        </div>
        <div class="review-analysis__bar-label">${label}</div>
          </div>
  `;
  });

  html += `
          </div>
    </div>
  </div>
      </div>
      
      <!-- Competitive Intelligence Report -->
      <div class="review-analysis__section">
        <div class="review-analysis__data-section__title">
          COMPETITIVE INTELLIGENCE REPORT
        </div>
        
        <div class="intelligence-item" style="background: rgba(139, 92, 246, 0.1); border: 1px solid #7C3AED; margin-bottom: 20px;">
          <div class="intelligence-label">Important Note</div>
          <div class="intelligence-value">Reviews are correlation factors - most reviews don't always get the highest position</div>
          <div class="intelligence-desc">Google rankings depend on multiple factors including relevance, proximity, local SEO signals, business authority, and service quality beyond just review count</div>
        </div>
        
        <div class="review-analysis__intelligence-list">
          ${generateCompetitiveIntelligence(reviewData, totalReviews)}
        </div>
      </div>
    </div>`;
          
  return html;
}

// Calculate top 3 review share percentage using ORGANIC businesses only
function calculateTop3ReviewShare(reviewData, totalReviews) {
  if (!reviewData || reviewData.length < 3 || totalReviews === 0) return 0;
  
  // Filter out sponsored businesses for accurate organic top 3 calculation
  const organicBusinesses = reviewData.filter(business => !business.sponsored && !business.isSponsored);
  
  if (organicBusinesses.length < 3) return 0;
  
  const top3OrganicReviews = organicBusinesses.slice(0, 3).reduce((sum, business) => {
    return sum + (business.reviewCount || 0);
  }, 0);
  
  return Math.round((top3OrganicReviews / totalReviews) * 100);
}

// Calculate market quality percentage (businesses above 4.0 stars)
function calculateMarketQuality(reviewData) {
  if (!reviewData || reviewData.length === 0) return 0;
  
  const businessesAbove4 = reviewData.filter(business => {
    return business.rating && business.rating >= 4.0;
  }).length;
  
  return Math.round((businessesAbove4 / reviewData.length) * 100);
}

// Generate comprehensive competitive intelligence report using ORGANIC businesses only
function generateCompetitiveIntelligence(reviewData, totalReviews) {
  if (!reviewData || reviewData.length === 0) return '<div class="intelligence-item">No data available for analysis</div>';
  
  let html = '';
  
  // Filter out sponsored businesses for accurate organic analysis
  const organicBusinesses = reviewData.filter(business => !business.sponsored && !business.isSponsored);
  
  if (organicBusinesses.length === 0) return '<div class="intelligence-item">No organic businesses available for analysis</div>';

  // Add Review Gaps to Advance report for tracked domains at the top
  html += generateReviewGapsReport(organicBusinesses);
  
  // Sort by review count to identify high-volume businesses (organic only)
  const sortedByReviews = [...organicBusinesses].sort((a, b) => (b.reviewCount || 0) - (a.reviewCount || 0));
  const top3ByPosition = organicBusinesses.slice(0, 3);
  
  // 1. Position vs Review Volume Disconnect (using organic businesses only)
  const highReviewLowPosition = organicBusinesses.slice(3).filter(b => {
    const top3MinReviews = Math.min(...top3ByPosition.map(t => t.reviewCount || 0));
    return (b.reviewCount || 0) > top3MinReviews;
  });
  
  const top3MinReviews = Math.min(...top3ByPosition.map(t => t.reviewCount || 0));
  
  let disconnectedList = 'None found';
  if (highReviewLowPosition.length > 0) {
    const disconnectedBusinesses = highReviewLowPosition.map(b => {
      const position = organicBusinesses.findIndex(rb => rb.businessName === b.businessName) + 1;
      return `<br>pos#${position}. ${b.businessName} (${b.reviewCount} reviews)`;
    });
    disconnectedList = disconnectedBusinesses.join('');
  }
  
  html += `<div class="intelligence-item">
    <div class="intelligence-label">Position vs Review Volume Disconnect</div>
    <div class="intelligence-value">${highReviewLowPosition.length} businesses with >${top3MinReviews} reviews not in top 3:<br>${disconnectedList}</div>
    <div class="intelligence-desc">These businesses have more reviews than at least one top 3 competitor but rank lower, indicating potential ranking opportunities or SEO issues that could be addressed</div>
  </div>`;
  
  // 2. Review Efficiency Score (using organic positions)
  const pos1Reviews = organicBusinesses[0]?.reviewCount || 0;
  const pos2Reviews = organicBusinesses[1]?.reviewCount || 0;
  const pos3Reviews = organicBusinesses[2]?.reviewCount || 0;
  
  const pos1Business = organicBusinesses[0]?.businessName || 'N/A';
  const pos2Business = organicBusinesses[1]?.businessName || 'N/A';
  const pos3Business = organicBusinesses[2]?.businessName || 'N/A';
  
  html += `<div class="intelligence-item">
    <div class="intelligence-label">Review Efficiency by Position</div>
    <div class="intelligence-value"><br>Position #1: ${pos1Business} (${pos1Reviews} reviews)<br>Position #2: ${pos2Business} (${pos2Reviews} reviews)<br>Position #3: ${pos3Business} (${pos3Reviews} reviews)</div>
    <div class="intelligence-desc">Shows how many reviews it takes to rank in each top position - remember that review count alone doesn't determine rankings</div>
  </div>`;
  
  // 3. Vulnerability Index
  const minTop3Reviews = Math.min(...top3ByPosition.map(b => b.reviewCount || 0));
  const vulnerablePosition = top3ByPosition.findIndex(b => (b.reviewCount || 0) === minTop3Reviews) + 1;
  const vulnerableBusiness = top3ByPosition.find(b => (b.reviewCount || 0) === minTop3Reviews);
  
  html += `<div class="intelligence-item">
    <div class="intelligence-label">Most Vulnerable Top 3 Position</div>
    <div class="intelligence-value">Position #${vulnerablePosition}: ${vulnerableBusiness?.businessName || 'N/A'} (${minTop3Reviews} reviews)</div>
    <div class="intelligence-desc">Identifies ranking positions that could be overtaken with review growth</div>
  </div>`;
  
  // 5. Hidden Gems Identifier (using organic businesses only)
  const hiddenGem = sortedByReviews.find(b => {
    const position = organicBusinesses.findIndex(rb => rb.businessName === b.businessName) + 1;
    return position > 3;
  });
  
  if (hiddenGem) {
    const hiddenGemPosition = organicBusinesses.findIndex(rb => rb.businessName === hiddenGem.businessName) + 1;
    html += `<div class="intelligence-item">
      <div class="intelligence-label">Hidden Gem (Underranking High-Performer)</div>
      <div class="intelligence-value">${hiddenGem.businessName} (${hiddenGem.reviewCount} reviews, position #${hiddenGemPosition})</div>
      <div class="intelligence-desc">This business has many reviews but ranks lower than expected. Remember that Google rankings depend on multiple factors beyond review count - including relevance, proximity, prominence, SEO quality, and local authority. High review volume indicates customer satisfaction but doesn't guarantee top rankings due to these complex algorithmic factors.</div>
    </div>`;
  }
  
  // 5.5 Rating vs Position Correlation (using organic businesses only) - moved here after Hidden Gem
  const perfectRatingBusinesses = organicBusinesses.filter(b => b.rating === 5.0);
  const perfectRatingPositions = perfectRatingBusinesses.map(b => organicBusinesses.findIndex(rb => rb.businessName === b.businessName) + 1);
  const topPositionRating = organicBusinesses[0]?.rating || 0;
  
  html += `<div class="intelligence-item">
    <div class="intelligence-label">Perfect 5.0 Star Businesses</div>
    <div class="intelligence-value">${perfectRatingBusinesses.length} businesses at positions: ${perfectRatingPositions.join(', ')}</div>
    <div class="intelligence-desc">Top position has ${topPositionRating} stars - this demonstrates that perfect ratings don't guarantee top rankings. Correlation is not causation: while ratings matter, Google's algorithm weighs multiple ranking factors including relevance, proximity, local SEO signals, and business authority beyond just star ratings.</div>
  </div>`;
  
  // 6. Market Maturity Indicator (using organic businesses only)
  const maxReviews = Math.max(...organicBusinesses.map(b => b.reviewCount || 0));
  const minReviews = Math.min(...organicBusinesses.map(b => b.reviewCount || 0));
  const reviewSpread = maxReviews - minReviews;
  
  // Color coding based on market difficulty (70+ min reviews = hard market)
  const marketDifficulty = minReviews >= 70 ? 'hard' : 'opportunity';
  const marketColor = marketDifficulty === 'hard' ? '#dc3545' : '#28a745'; // Red for hard, green for opportunity
  const marketDesc = marketDifficulty === 'hard' ? 'Mature, competitive market - high barrier to entry' : 'Immature market with growth opportunities';
  
  html += `<div class="intelligence-item">
    <div class="intelligence-label">Market Review Spread</div>
    <div class="intelligence-value" style="color: ${marketColor}; font-weight: bold;">${reviewSpread} review difference (${maxReviews} - ${minReviews})</div>
    <div class="intelligence-desc" style="color: ${marketColor};">${marketDesc}</div>
  </div>`;
  
  // 7. Review Velocity Opportunities (using organic businesses only)
  const highRatingLowReview = organicBusinesses.filter(b => b.rating >= 4.8 && (b.reviewCount || 0) < 50);
  
  if (highRatingLowReview.length > 0) {
    html += `<div class="intelligence-item">
      <div class="intelligence-label">High-Rating, Low-Review Opportunities</div>
      <div class="intelligence-value">${highRatingLowReview.length} businesses with 4.8+ stars and <50 reviews</div>
      <div class="intelligence-desc">Businesses that could surge with review acquisition</div>
    </div>`;
  }
  
  // 8. Market Entry Barrier (using organic businesses only)
  const minViableReviews = Math.min(...organicBusinesses.map(b => b.reviewCount || 0));
  const recommendedTop3Reviews = Math.min(...top3ByPosition.map(b => b.reviewCount || 0));
  
  html += `<div class="intelligence-item">
    <div class="intelligence-label">Market Entry Requirements</div>
    <div class="intelligence-value">Min for top 10: ${minViableReviews} reviews | Min for top 3: ${recommendedTop3Reviews}+ reviews</div>
    <div class="intelligence-desc">Minimum review thresholds to compete in this market</div>
  </div>`;
  
  // 9. Review Concentration Score (using organic businesses only)
  const top30PercentCount = Math.ceil(organicBusinesses.length * 0.3);
  const organicSortedByReviews = [...organicBusinesses].sort((a, b) => (b.reviewCount || 0) - (a.reviewCount || 0));
  const top30PercentReviews = organicSortedByReviews.slice(0, top30PercentCount).reduce((sum, b) => sum + (b.reviewCount || 0), 0);
  const organicTotalReviews = organicBusinesses.reduce((sum, b) => sum + (b.reviewCount || 0), 0);
  const concentrationPercent = Math.round((top30PercentReviews / organicTotalReviews) * 100);
  
  html += `<div class="intelligence-item">
    <div class="intelligence-label">Review Market Concentration (Organic)</div>
    <div class="intelligence-value">Top ${top30PercentCount} businesses (30%) control ${concentrationPercent}% of reviews</div>
    <div class="intelligence-desc">Shows market concentration and competitive intensity among organic results only</div>
  </div>`;
  
  // 10. Position Efficiency Ranking (using organic businesses only)
  const efficiencyScores = organicBusinesses.map((b, index) => ({
    name: b.businessName,
    position: index + 1,
    reviews: b.reviewCount || 0,
    efficiency: (b.reviewCount || 0) / (index + 1) // Reviews per position
  })).sort((a, b) => b.efficiency - a.efficiency);
  
  const mostEfficient = efficiencyScores[0];
  const leastEfficient = efficiencyScores[efficiencyScores.length - 1];
  
  html += `<div class="intelligence-item">
    <div class="intelligence-label">Position Efficiency Leaders</div>
    <div class="intelligence-value">
      • Most efficient: ${mostEfficient.name} - Position #${mostEfficient.position} (${mostEfficient.efficiency.toFixed(1)} reviews/position)
      <br>• Least efficient: ${leastEfficient.name} - Position #${leastEfficient.position} (${leastEfficient.efficiency.toFixed(1)} reviews/position)
    </div>
    <div class="intelligence-desc">Efficiency metric = Reviews ÷ Position rank. Higher numbers indicate businesses getting more reviews relative to their ranking position. This shows ranking performance efficiency, not absolute review volume.</div>
  </div>`;
  
  // 11. Easiest Competitor to Overtake (using organic top 3 only)
  const easiestTarget = top3ByPosition.reduce((min, current) => {
    return (current.reviewCount || 0) < (min.reviewCount || 0) ? current : min;
  });
  const targetPosition = organicBusinesses.findIndex(b => b.businessName === easiestTarget.businessName) + 1;
  
  html += `<div class="intelligence-item">
    <div class="intelligence-label">Easiest Top 3 Target</div>
    <div class="intelligence-value">${easiestTarget.businessName} (Position #${targetPosition}, ${easiestTarget.reviewCount} reviews)</div>
    <div class="intelligence-desc">Softest target in top 3 - beat with ${(easiestTarget.reviewCount || 0) + 1}+ reviews</div>
  </div>`;
  
  // 12. Review ROI Predictor (using organic businesses only)
  const avgReviewsPerPosition = organicBusinesses.reduce((sum, b, index) => sum + ((b.reviewCount || 0) / (index + 1)), 0) / organicBusinesses.length;
  const expectedPositionGainPer50Reviews = Math.round((50 / avgReviewsPerPosition) * 10) / 10;
  
  html += `<div class="intelligence-item">
    <div class="intelligence-label">Review ROI Predictor</div>
    <div class="intelligence-value">Expected position gain per 50 reviews: +${expectedPositionGainPer50Reviews} positions</div>
    <div class="intelligence-desc">Predicted ranking improvement based on current market distribution</div>
  </div>`;
  
  // 13. Market Leader Threat Analysis (using organic businesses only)
  const leader = organicBusinesses[0];
  const perfectRatingCompetitors2 = organicBusinesses.filter(b => b.rating === 5.0).length;
  const leaderRating = leader?.rating || 0;
  const leaderReviews = leader?.reviewCount || 0;
  
  // Find perfect-rated competitors and their positions
  const perfectCompetitors = organicBusinesses
    .map((b, index) => ({ ...b, position: index + 1 }))
    .filter(b => b.rating === 5.0 && b.position > 1);
  
  // Calculate review gap to perfect competitors
  const closestPerfectCompetitor = perfectCompetitors.length > 0 ? perfectCompetitors[0] : null;
  const reviewGap = closestPerfectCompetitor ? leaderReviews - closestPerfectCompetitor.reviewCount : 0;
  
  // Threat assessment with actionable metrics
  let threatAnalysis = '';
  let actionableAdvice = '';
  
  if (leaderRating === 5.0) {
    threatAnalysis = `Secure (perfect rating, ${reviewGap > 0 ? `+${reviewGap} review advantage` : 'maintaining lead'})`;
    actionableAdvice = 'Maintain service quality and continue review acquisition to stay ahead';
  } else if (perfectRatingCompetitors2 === 0) {
    threatAnalysis = `Moderate risk (${leaderRating} stars, no perfect competitors yet)`;
    actionableAdvice = 'Focus on improving service quality to reach 5.0 stars before competitors do';
  } else {
    const immediateThreats = perfectCompetitors.filter(c => Math.abs(c.reviewCount - leaderReviews) <= 20);
    if (immediateThreats.length > 0) {
      threatAnalysis = `High vulnerability (${immediateThreats.length} perfect competitors within 20 reviews)`;
      actionableAdvice = `Urgent: Improve to 5.0 stars AND gain ${Math.abs(reviewGap) + 25}+ reviews to create buffer`;
    } else {
      threatAnalysis = `Medium risk (${reviewGap > 0 ? `${reviewGap} review buffer` : `${Math.abs(reviewGap)} reviews behind`})`;
      actionableAdvice = reviewGap > 0 ? 'Improve rating to 5.0 while maintaining review lead' : 'Focus on both rating improvement and review acquisition';
    }
  }
  
  html += `<div class="intelligence-item">
    <div class="intelligence-label">Market Leader Vulnerability Analysis</div>
    <div class="intelligence-value">${threatAnalysis} | ${perfectRatingCompetitors2} perfect competitors in market</div>
    <div class="intelligence-desc">Actionable strategy: ${actionableAdvice}</div>
  </div>`;
          
  return html;
}

// Function to get tracked domains from Chrome storage
async function getTrackedDomains() {
  return new Promise((resolve) => {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
        const settings = result.gmbExtractorSettings || {};
        const trackedDomains = settings.trackedDomainsList || [];
        const isEnabled = settings.trackedDomainsEnabled !== false;
        
        console.log('🎯 REVIEW GAPS: Retrieved tracked domains:', {
          enabled: isEnabled,
          domains: trackedDomains,
          count: trackedDomains.length
        });
        
        resolve(isEnabled ? trackedDomains : []);
      });
    } else {
      console.log('🎯 REVIEW GAPS: Chrome storage not available');
      resolve([]);
    }
  });
}

// Function to normalize domain for comparison (matches tracked-domains.js logic)
function normalizeDomainForComparison(domain) {
  return domain.toLowerCase().replace(/^www\./, '');
}

// Function to extract domain from business website URL or name
function extractDomainFromBusiness(business) {
  // First try to get domain from website URL
  const websiteUrl = business.website || business.websiteUrl;
  if (websiteUrl) {
    try {
      const url = new URL(websiteUrl.startsWith('http') ? websiteUrl : 'https://' + websiteUrl);
      return normalizeDomainForComparison(url.hostname);
    } catch (error) {
      console.log(`🎯 REVIEW GAPS: Failed to parse website URL "${websiteUrl}":`, error);
    }
  }
  
  // Fallback: Try to extract domain from business name if it looks like a URL
  const businessName = business.businessName || business.name || '';
  
  // Check if business name contains a domain-like pattern
  const domainMatch = businessName.match(/([a-zA-Z0-9-]+\.(?:com|org|net|edu|gov|mil|int|co\.uk|co\.nz|com\.au|ca|de|fr|it|es|nl|br|in|cn|jp|kr|ru|pl|se|no|dk|fi|pt|gr|cz|sk|hu|ro|bg|hr|si|ee|lv|lt|mt|cy|lu|be|at|ch|li|ie|is|fo|gl|ad|mc|sm|va|gg|je|im|gi|mk|al|me|rs|ba|xk|md|ua|by|am|az|ge|kz|kg|uz|tm|tj|af|ir|iq|tr|sy|lb|jo|il|ps|sa|ye|om|ae|qa|bh|kw|pk|bd|bt|np|lk|mv|mm|th|la|kh|vn|my|sg|bn|id|tl|ph|tw|hk|mo|mn|kp|jp|kr|au|nz|fj|pg|sb|vu|nc|pf|ck|nu|to|ws|ki|tv|nf|fm|mh|pw|gu|mp|as|vi|pr|us))/i);
  
  if (domainMatch) {
    return normalizeDomainForComparison(domainMatch[1]);
  }
  
  // If no domain found, return null
  return null;
}

// Function to check if a domain matches any tracked domain
function isBusinessTrackedDomain(businessDomain, trackedDomains) {
  if (!businessDomain) return false;
  
  const normalizedBusinessDomain = normalizeDomainForComparison(businessDomain);
  
  return trackedDomains.some(trackedDomain => {
    const normalizedTracked = normalizeDomainForComparison(trackedDomain);
    return normalizedBusinessDomain === normalizedTracked || 
           normalizedBusinessDomain.endsWith('.' + normalizedTracked);
  });
}

// Function to generate Review Gaps to Advance report for tracked domains
function generateReviewGapsReport(organicBusinesses) {
  // This function will be called asynchronously, so we need to handle it properly
  // For now, return a placeholder and update it later
  return `<div id="review-gaps-placeholder" class="intelligence-item">
    <div class="intelligence-label">Review Gaps to Advance (Loading...)</div>
    <div class="intelligence-value">Analyzing tracked domains...</div>
    <div class="intelligence-desc">Calculating review requirements for tracked domains to advance positions</div>
  </div>`;
}

// Async function to generate and update the Review Gaps report
async function updateReviewGapsReport(organicBusinesses) {
  const trackedDomains = await getTrackedDomains();
  
  if (!trackedDomains || trackedDomains.length === 0) {
    // Remove the placeholder if no tracked domains
    const placeholder = document.getElementById('review-gaps-placeholder');
    if (placeholder) {
      placeholder.remove();
    }
    return;
  }
  
  console.log('🎯 REVIEW GAPS: Starting analysis with tracked domains:', trackedDomains);
  
  // Find tracked domains in the business list
  const trackedBusinesses = organicBusinesses.map((business, index) => {
    const businessDomain = extractDomainFromBusiness(business);
    
    console.log(`🎯 REVIEW GAPS: Business ${index + 1} - "${business.businessName}" -> domain: "${businessDomain}"`);
    
    if (businessDomain && isBusinessTrackedDomain(businessDomain, trackedDomains)) {
      console.log(`🎯 REVIEW GAPS: ✅ TRACKED DOMAIN FOUND: ${business.businessName} (${businessDomain}) at position ${index + 1}`);
      
      return {
        ...business,
        position: index + 1,
        domain: businessDomain,
        isTracked: true
      };
    }
    
    return null;
  }).filter(business => business !== null);
  
  console.log('🎯 REVIEW GAPS: Found tracked businesses:', trackedBusinesses);
  
  if (trackedBusinesses.length === 0) {
    // Update placeholder to show no tracked domains found
    const placeholder = document.getElementById('review-gaps-placeholder');
    if (placeholder) {
      placeholder.innerHTML = `
        <div class="intelligence-label">Review Gaps to Advance</div>
        <div class="intelligence-value">No tracked domains found in current results</div>
        <div class="intelligence-desc">Add domains to the tracked domains list in settings to see advancement opportunities</div>
      `;
    }
    return;
  }
  
  // Generate the Review Gaps analysis
  let html = '';
  
  trackedBusinesses.forEach(trackedBusiness => {
    const currentPosition = trackedBusiness.position;
    const currentReviews = trackedBusiness.reviewCount || 0;
    
    console.log(`🎯 REVIEW GAPS: Analyzing ${trackedBusiness.businessName} at position ${currentPosition} with ${currentReviews} reviews`);
    
    // Find positions above this tracked domain
    const positionsAbove = organicBusinesses.slice(0, currentPosition - 1);
    
    if (positionsAbove.length === 0) {
      // Already in position 1
      html += `<div class="intelligence-item" style="border-left: 4px solid #7C3AED;">
        <div class="intelligence-label">🎯 ${trackedBusiness.businessName} (${trackedBusiness.domain})</div>
        <div class="intelligence-value" style="color: #28a745; font-weight: bold;">Already in Position #1! 🏆</div>
        <div class="intelligence-desc">This tracked domain is currently leading the market. Focus on maintaining position and service quality.</div>
      </div>`;
    } else {
      // Calculate reviews needed for each position above
      const reviewTargets = positionsAbove.map((competitor, index) => {
        const targetPosition = index + 1;
        const competitorReviews = competitor.reviewCount || 0;
        const reviewsNeeded = Math.max(1, competitorReviews - currentReviews + 1);
        
        return {
          position: targetPosition,
          competitorName: competitor.businessName,
          competitorReviews: competitorReviews,
          reviewsNeeded: reviewsNeeded
        };
      }).reverse(); // Show highest position first
      
      const reviewTargetList = reviewTargets.map(target => 
        `<br>• Beat position #${target.position} (${target.competitorName}): +${target.reviewsNeeded} review${target.reviewsNeeded !== 1 ? 's' : ''} (${target.competitorReviews} → ${currentReviews + target.reviewsNeeded})`
      ).join('');
      
      html += `<div class="intelligence-item" style="border-left: 4px solid #7C3AED;">
        <div class="intelligence-label">🎯 ${trackedBusiness.businessName} (${trackedBusiness.domain})</div>
        <div class="intelligence-value">Currently Position #${currentPosition} (${currentReviews} reviews)${reviewTargetList}</div>
        <div class="intelligence-desc">Direct actionable targets for advancement. Note: Review count is a correlation factor - high-rating domains don't always rank in top positions due to other Google ranking factors including relevance, proximity, and local SEO signals.</div>
      </div>`;
    }
  });
  
  // Update the placeholder with the generated content
  const placeholder = document.getElementById('review-gaps-placeholder');
  if (placeholder && html) {
    placeholder.outerHTML = html;
  }
}

// Display Google search review analysis results (refactored to use shared function)
function displayGoogleSearchReviewResults(data) {
  if (!data || !data.data || data.data.length === 0) {
    showReviewError('No review data found');
      return;
    }
    
  const reviewData = data.data;
  const totalBusinesses = reviewData.length;
  
  // Calculate aggregate statistics
  const totalReviews = reviewData.reduce((sum, business) => sum + business.reviewCount, 0);
  const averageRating = reviewData.reduce((sum, business) => sum + business.rating, 0) / totalBusinesses;
  
  // Filter out sponsored businesses for accurate organic analysis
  const organicBusinesses = reviewData.filter(business => !business.isSponsored);
  console.log(`🔍 GOOGLE SEARCH: Organic businesses: ${organicBusinesses.length} (${reviewData.length - organicBusinesses.length} sponsored excluded)`);
  
  // Calculate average reviews for top 3 ORGANIC businesses by position (first 3 in organic list)
  const top3OrganicByPosition = organicBusinesses.slice(0, 3);
  console.log('Top 3 ORGANIC businesses by position:', top3OrganicByPosition.map(b => ({ 
    name: b.businessName, 
    reviews: b.reviewCount,
    originalReviewText: b.originalReviewText || 'N/A',
    isSponsored: b.isSponsored
  })));
  
  const averageReviewCountTop3 = top3OrganicByPosition.length > 0 
    ? Math.round(top3OrganicByPosition.reduce((sum, business) => sum + business.reviewCount, 0) / top3OrganicByPosition.length)
    : 0;
    
  console.log('🧮 TOP 3 ORGANIC CALCULATION DETAILS:');
  console.log('Organic Business 1:', top3OrganicByPosition[0] ? {
    name: top3OrganicByPosition[0].businessName,
    reviews: top3OrganicByPosition[0].reviewCount,
    originalText: top3OrganicByPosition[0].originalReviewText,
    isSponsored: top3OrganicByPosition[0].isSponsored
  } : 'N/A');
  console.log('Organic Business 2:', top3OrganicByPosition[1] ? {
    name: top3OrganicByPosition[1].businessName,
    reviews: top3OrganicByPosition[1].reviewCount,
    originalText: top3OrganicByPosition[1].originalReviewText,
    isSponsored: top3OrganicByPosition[1].isSponsored
  } : 'N/A');
  console.log('Organic Business 3:', top3OrganicByPosition[2] ? {
    name: top3OrganicByPosition[2].businessName,
    reviews: top3OrganicByPosition[2].reviewCount,
    originalText: top3OrganicByPosition[2].originalReviewText,
    isSponsored: top3OrganicByPosition[2].isSponsored
  } : 'N/A');
  
  // Rating distribution using ORGANIC data only
  const ratingDistribution = {};
  organicBusinesses.forEach(business => {
    let ratingRange;
    
    if (!business.rating || business.rating === 0) {
      ratingRange = '0'; // No rating
    } else if (business.rating >= 5.0) {
      ratingRange = '5'; // Exactly 5.0 stars
    } else if (business.rating >= 4.0) {
      ratingRange = '4'; // 4.0-4.9 stars
    } else if (business.rating >= 3.0) {
      ratingRange = '3'; // 3.0-3.9 stars
    } else if (business.rating >= 2.0) {
      ratingRange = '2'; // 2.0-2.9 stars
    } else if (business.rating >= 1.0) {
      ratingRange = '1'; // 1.0-1.9 stars
    } else {
      ratingRange = '0'; // No rating or below 1.0
    }
    
    ratingDistribution[ratingRange] = (ratingDistribution[ratingRange] || 0) + 1;
  });
  
  // Category breakdown using ORGANIC data only
  const categoryBreakdown = {};
  organicBusinesses.forEach(business => {
    if (business.category && business.category.toLowerCase() !== 'favourites') {
      categoryBreakdown[business.category] = (categoryBreakdown[business.category] || 0) + 1;
    }
  });
  
  // Find highest rated and most reviewed businesses (using ORGANIC businesses only)
  // Only consider top 3 ORGANIC businesses for highest rated (by position)
  const highestRated = top3OrganicByPosition.reduce((max, business) => {
    // If ratings are equal, choose the one with higher position (earlier in the array)
    if (business.rating > max.rating) {
      return business;
    } else if (business.rating === max.rating) {
      // If tie, choose the one that appears first (already in order)
      return max;
    }
    return max;
  }, top3OrganicByPosition[0]);
  
  const mostReviews = organicBusinesses.reduce((max, business) => 
    business.reviewCount > max.reviewCount ? business : max, organicBusinesses[0]);
    
  console.log('🔍 DETAILED DEBUGGING:');
  console.log('Most reviewed business:', { 
    name: mostReviews.businessName, 
    reviews: mostReviews.reviewCount,
    originalText: mostReviews.originalReviewText
  });
  console.log('All businesses review counts (first 10):', reviewData.slice(0, 10).map(b => ({ 
    name: b.businessName, 
    reviews: b.reviewCount,
    originalText: b.originalReviewText
  })));
  
  // Use shared HTML generation function with organic data
  const html = generateReviewAnalysisHTML({
    reviewData: organicBusinesses, // Use organic businesses only
    totalBusinesses: organicBusinesses.length, // Organic count
    totalReviews,
    averageRating,
    averageReviewCountTop3,
    ratingDistribution,
    categoryBreakdown,
    highestRated,
    mostReviews,
    sponsoredExcluded: reviewData.length - organicBusinesses.length
  }, 'GOOGLE SEARCH REVIEW ANALYSIS (ORGANIC)');
  
  reviewResultsList.innerHTML = html;
  reviewResults.style.display = 'block';
  
  // Update the Review Gaps report asynchronously after rendering
  setTimeout(() => updateReviewGapsReport(organicBusinesses), 100);
  
  // Store data for export using organic data
  extractedData = {
    type: 'google_search_review_analysis',
    timestamp: new Date().toISOString(),
    totalBusinesses: organicBusinesses.length, // Organic count
    totalReviews: totalReviews,
    averageRating: parseFloat(averageRating.toFixed(2)),
    averageReviewCountTop3: averageReviewCountTop3,
    reviewData: organicBusinesses, // Use organic businesses only
    ratingDistribution: ratingDistribution,
    categoryBreakdown: categoryBreakdown,
    sponsoredExcluded: reviewData.length - organicBusinesses.length
  };
  
  // Enable export button
  exportReviewBtn.disabled = false;
}

// Display Pro List review analysis results (using same format as Google Search)
function displayProListReviewResults(data) {
  if (!data || data.length === 0) {
    showReviewError('No review data found');
    return;
  }
  
  // Find aggregate data
  const aggregateItem = data.find(item => item.isAggregate);
  const businessData = data.filter(item => !item.isAggregate);
  
  if (!aggregateItem || !aggregateItem.aggregateData) {
    showReviewError('No aggregate data available');
    return;
  }
  
  const agg = aggregateItem.aggregateData;
  const reviewData = businessData;
  const totalBusinesses = agg.totalBusinesses;
  const totalReviews = agg.totalReviews;
  const averageRating = agg.averageRating;
  const averageReviewCountTop3 = agg.averageReviewCountTop3;
  const ratingDistribution = agg.ratingDistribution;
  const categoryBreakdown = agg.categoryBreakdown;
  const highestRated = agg.highestRated;
  const mostReviews = agg.mostReviews;
  
  console.log('🔍 PRO LIST REVIEW ANALYSIS DETAILS:');
  console.log('Total businesses:', totalBusinesses);
  console.log('Total reviews:', totalReviews);
  console.log('Average rating:', averageRating);
  console.log('Top 3 average reviews:', averageReviewCountTop3);
  console.log('Highest rated:', highestRated);
  console.log('Most reviews:', mostReviews);
  console.log('Rating distribution:', ratingDistribution);
  console.log('Category breakdown:', categoryBreakdown);
  
  // Use the same HTML generation function as regular search
  const html = generateReviewAnalysisHTML({
    reviewData,
    totalBusinesses,
    totalReviews,
    averageRating,
    averageReviewCountTop3,
    ratingDistribution,
    categoryBreakdown,
    highestRated,
    mostReviews
  }, 'PRO LIST REVIEW ANALYSIS');
  
  reviewResultsList.innerHTML = html;
  reviewResults.style.display = 'block';
  
  // Update the Review Gaps report asynchronously after rendering
  setTimeout(() => updateReviewGapsReport(reviewData), 100);
  
  // Store data for export using the same format as regular search
  extractedData = {
    type: 'prolist_review_analysis',
    timestamp: new Date().toISOString(),
    totalBusinesses: totalBusinesses,
    totalReviews: totalReviews,
    averageRating: parseFloat(averageRating.toFixed(2)),
    averageReviewCountTop3: averageReviewCountTop3,
    reviewData: reviewData,
    ratingDistribution: ratingDistribution,
    categoryBreakdown: categoryBreakdown
  };
  
  // Enable export button
  exportReviewBtn.disabled = false;
}

// Extract data automatically
function extractData() {
  showLoading(true);
  statusText.textContent = 'Extracting data...';
  
  chrome.tabs.query({active: true, currentWindow: true}, tabs => {
    // Add timeout to prevent hanging connections
    const timeoutId = setTimeout(() => {
      console.warn('Popup: Message timeout for getData');
      showLoading(false);
      statusText.textContent = 'Extraction timed out';
      showErrorState('Data extraction timed out. Please refresh the page and try again.');
    }, 15000); // 15 second timeout for data extraction

    chrome.tabs.sendMessage(tabs[0].id, {action: "getData"}, (response) => {
      // Clear timeout since we got a response
      clearTimeout(timeoutId);
      
      showLoading(false);
      
      // Check for Chrome runtime errors first
      if (chrome.runtime.lastError) {
        statusText.textContent = 'Connection error during extraction';
        statusIndicator.className = 'status__indicator';
        showErrorState('Failed to connect to content script. Please refresh the page and try again.');
        return;
      }
      
      if (response && !response.error) {
        extractedData = response;
        displayExtractedData(extractedData);
        updateClaimStatus(extractedData.businessStatus);
        exportBtn.disabled = false;
        copyTextBtn.disabled = false;
        
        // Show Extract Attributes button for single business mode (functionality still available in persistent popup)
        extractAttributesBtn.style.display = 'none';
        
        // Hide review analysis buttons for single business mode (not applicable)
        exportReviewBtn.style.display = 'none';
        
        // Show single review scraper buttons for single business mode
        singleReviewScraperBtn.style.display = 'inline-block';
        exportSingleReviewBtn.style.display = 'none'; // Hide Export Reviews CSV button
        

        
        // Hide Pro List review scraper button for single business mode
        proListReviewScraperBtn.style.display = 'none';
        
        statusText.textContent = 'Data extracted successfully';
        statusIndicator.className = 'status__indicator status__indicator--active';
      } else {
        statusText.textContent = 'Failed to extract data';
        statusIndicator.className = 'status__indicator';
        showErrorState(response?.error || 'Unknown error');
        console.error('Extraction error:', response);
      }
    });
  });
}

// Extract attributes separately - this function runs independently from the main GMB extraction
function extractAttributes() {
  // Check if we're on a Google Maps page
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    const currentTab = tabs[0];
    const isGoogleMaps = currentTab.url.includes('google.com/maps');
    
    if (!isGoogleMaps) {
      statusText.textContent = 'Navigate to Google Maps for attribute extraction';
      statusIndicator.className = 'status__indicator';
      showErrorState('Please navigate to a Google Maps business listing first.');
      return;
    }
    
    // Show loading state
    showLoading(true);
    statusText.textContent = 'Extracting attributes...';
    
    // Add timeout to prevent hanging connections
    const timeoutId = setTimeout(() => {
      console.warn('Popup: Message timeout for getAttributes');
      showLoading(false);
      statusText.textContent = 'Attributes extraction timed out';
      showErrorState('Attributes extraction timed out. Please refresh the page and try again.');
    }, 15000); // 15 second timeout for attributes extraction
    
    // Send message to content script to extract attributes only
    chrome.tabs.sendMessage(tabs[0].id, {action: "getAttributes"}, (response) => {
      // Clear timeout since we got a response
      clearTimeout(timeoutId);
      
      showLoading(false);
      
      // Check for Chrome runtime errors (content script not loaded)
      if (chrome.runtime.lastError) {
        statusText.textContent = 'Content script not loaded - please refresh the page';
        statusIndicator.className = 'status__indicator';
        showErrorState('Content script not loaded. Please refresh the Google Maps page and try again.');
        return;
      }
      
      if (response && !response.error) {
        // Add attributes to existing data or create new data object
        if (!extractedData) {
          extractedData = { attributes: response.attributes };
        } else {
          extractedData.attributes = response.attributes;
        }
        
        // Add the attributes section to the existing display
        addAttributesToDisplay(response.attributes);
        
        statusText.textContent = 'Attributes extracted successfully';
        statusIndicator.className = 'status__indicator status__indicator--active';
      } else {
        statusText.textContent = 'Failed to extract attributes';
        statusIndicator.className = 'status__indicator';
        showErrorState(response?.error || 'Unknown error extracting attributes');
        console.error('Attributes extraction error:', response);
      }
    });
  });
}

// Add attributes section to the existing display
function addAttributesToDisplay(attributes) {
  if (!attributes || Object.keys(attributes).length === 0) {
    console.log('No attributes to display');
    return;
  }
  
  // Check if attributes section already exists and remove it
  const existingAttributesSection = dataContainer.querySelector('.attributes-section');
  if (existingAttributesSection) {
    existingAttributesSection.remove();
  }
  
  // Create attributes HTML
  let attributesHtml = '<div class="data-section attributes-section">';
  attributesHtml += '<div class="data-section__title">Business Attributes</div>';
  
  Object.keys(attributes).forEach(sectionTitle => {
    const sectionAttributes = attributes[sectionTitle];
    if (Array.isArray(sectionAttributes) && sectionAttributes.length > 0) {
      // Create one data item per section with all attributes combined
      const attributesText = sectionAttributes.map(attr => `• ${attr}`).join('\n');
      const itemId = 'attr_' + Math.random().toString(36).substr(2, 9);
      
      attributesHtml += '<div class="data-item">';
      attributesHtml += '<div class="data-item__value">';
      attributesHtml += `<span class="data-item__label">${sectionTitle}:</span>`;
      attributesHtml += `<span class="data-raw-value" id="${itemId}" style="white-space: pre-line;">${attributesText}</span>`;
      attributesHtml += '</div>';
      attributesHtml += `<button class="copy-btn" data-copy-target="${itemId}">Copy</button>`;
      attributesHtml += '</div>';
    }
  });
  
  attributesHtml += '</div>';
  
  // Add the attributes section to the end of the data container
  dataContainer.insertAdjacentHTML('beforeend', attributesHtml);
  
  // Add event listeners for the new copy buttons
  addCopyEventListeners();
  
  console.log('Attributes section added to display');
}

// Display extracted data with labels and copy buttons for raw data
function displayExtractedData(data) {
  // Reset search when loading new data
  resetDataSearch();
  
  if (!data || data.error) {
    showErrorState('Error extracting data');
    return;
  }
  
  let html = '';
  
  // Add search bar OUTSIDE of any data-section to prevent it from being hidden
  html += `
    <div class="data-search-container-wrapper">
      <div class="data-search-container">
        <input type="text" id="dataSearch" class="data-search" placeholder="Search data... (3+ letters, press space to filter)" autocomplete="off">
        <button type="button" id="clearDataSearch" class="data-search-clear" title="Clear search">×</button>
      </div>
    </div>
  `;
  
  // Basic Information Section
  html += '<div class="data-section">';
  html += '<div class="data-section__title">Basic Information</div>';
  
  // Business Name with inline claim status
  if (data.businessName) {
    const itemId = 'item_' + Math.random().toString(36).substr(2, 9);
    let businessNameHtml = `
      <div class="data-item">
        <div class="data-item__value">
          <span class="data-item__label">Business Name:</span>
          <span class="data-raw-value" id="${itemId}">${data.businessName}</span>`;
    
    // Add styled claim status if available
    if (data.businessStatus) {
      const status = data.businessStatus.toLowerCase();
      let statusHtml = '';
      
      if (status.includes('claimed') && !status.includes('unclaimed') && !status.includes('un-claimed')) {
        statusHtml = `<span style="margin-left: 8px; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600; background: rgba(34, 197, 94, 0.1); color: #22c55e; border: 1px solid rgba(34, 197, 94, 0.3); min-width: 73px !important;">✅ Claimed</span>`;
      } else if (status.includes('unclaimed') || status.includes('un-claimed') || status === 'unclaimed' || status === 'un-claimed') {
        statusHtml = `<span style="margin-left: 8px; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600; background: rgba(239, 68, 68, 0.1); color: #ef4444; border: 1px solid rgba(239, 68, 68, 0.3);">❌ Un-Claimed</span>`;
      } else {
        statusHtml = `<span style="margin-left: 8px; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600; background: rgba(209, 213, 219, 0.1); color: #d1d5db; border: 1px solid rgba(209, 213, 219, 0.3);">📍 ${data.businessStatus}</span>`;
      }
      
      businessNameHtml += statusHtml;
    }
    
    businessNameHtml += `
        </div>
        <button class="copy-btn" data-copy-target="${itemId}">Copy</button>
      </div>`;
    
    html += businessNameHtml;
  }
  
  const basicFields = [
    { key: 'address', label: 'Address', value: data.address },
    { key: 'phone', label: 'Phone', value: data.phone },
    { key: 'bookingLink', label: 'Booking Link', value: data.bookingLink },
    { key: 'website', label: 'Website', value: data.website }
  ];

  basicFields.forEach(field => {
    if (field.value) {
      html += createDataItemWithLabel(field.label, field.value);
    }
  });
  html += '</div>';

  // Reviews and Rating Section (moved to appear right after Basic Information)
  const reviewFields = [
    { key: 'averageRating', label: 'Average Rating', value: data.averageRating },
    { key: 'rating', label: 'Rating', value: data.rating },
    { key: 'reviewCount', label: 'Review Count', value: data.reviewCount }
  ];

  const validReviews = reviewFields.filter(field => field.value);
  if (validReviews.length > 0) {
    html += '<div class="data-section">';
    html += '<div class="data-section__title">Reviews & Rating</div>';
    
    validReviews.forEach(field => {
      html += createDataItemWithLabel(field.label, field.value);
    });
    html += '</div>';
  }

  // Categories Section
  if (data.categories && Array.isArray(data.categories) && data.categories.length > 0) {
    html += '<div class="data-section">';
    html += '<div class="data-section__title">Categories</div>';
    
    // Main Category with its own copy button
    if (data.mainCategory) {
      html += createDataItemWithLabel('Main Category', data.mainCategory);
    }
    
    // Additional Categories as a single copyable item
    if (data.additionalCategories && data.additionalCategories.length > 0) {
      const additionalCategoriesText = data.additionalCategories.join(', ');
      html += createDataItemWithLabel('Additional Categories', additionalCategoriesText);
    } else if (data.categories.length > 1) {
      // Fallback: if additionalCategories is not set but we have multiple categories
      const additionalCats = data.categories.slice(1);
      if (additionalCats.length > 0) {
        const additionalCategoriesText = additionalCats.join(', ');
        html += createDataItemWithLabel('Additional Categories', additionalCategoriesText);
      }
    } else {
      // Show that there are no additional categories
      html += '<div class="data-item">';
      html += '<div class="data-item__value">';
      html += '<span class="data-item__label">Additional Categories:</span>';
      html += '<span class="data-raw-value">No Additional Categories Detected</span>';
      html += '</div>';
      html += '</div>';
    }
    
    // Debug info
    console.log('Categories display debug:', {
      allCategories: data.categories,
      mainCategory: data.mainCategory,
      additionalCategories: data.additionalCategories,
      categoriesLength: data.categories.length
    });
    
    html += '</div>';
  }

  // Location Information Section
  if (data.coordinates) {
    html += '<div class="data-section">';
    html += '<div class="data-section__title">Location</div>';
    html += createDataItemWithLabel('Latitude', data.coordinates.latitude);
    html += createDataItemWithLabel('Longitude', data.coordinates.longitude);
    html += createDataItemWithLabel('Coordinates', `${data.coordinates.latitude},${data.coordinates.longitude}`);
    html += '</div>';
  }

  // Technical Information Section
  html += '<div class="data-section">';
  html += '<div class="data-section__title">Technical IDs</div>';
  
  const technicalFields = [
    { key: 'placeId', label: 'Place ID', value: data.placeId },
    { key: 'knowledgePanelId', label: 'Knowledge Panel ID', value: data.knowledgePanelId },
    { key: 'cidNumber', label: 'CID Number', value: data.cidNumber }
  ];

  technicalFields.forEach(field => {
    if (field.value) {
      html += createDataItemWithLabel(field.label, field.value);
    }
  });
  html += '</div>';

  // GMB Links Section
  const linkFields = [
    { key: 'reviewListDisplayLink', label: 'Review List Link', value: data.reviewListDisplayLink },
    { key: 'reviewRequestLink', label: 'Review Request Link', value: data.reviewRequestLink },
    { key: 'knowledgePanelPageLink', label: 'Knowledge Panel Link', value: data.knowledgePanelPageLink },
    { key: 'gmbPostUrl', label: 'GMB Post URL', value: data.gmbPostUrl },
    { key: 'askQuestionRequestUrl', label: 'Ask Question URL', value: data.askQuestionRequestUrl },
    { key: 'questionsAndAnswersUrl', label: 'Q&A URL', value: data.questionsAndAnswersUrl },
    { key: 'products', label: 'Products URL', value: data.products },
    { key: 'services', label: 'Services URL', value: data.services },
    { key: 'otherGmbsAtSameAddress', label: 'Same Address GMBs', value: data.otherGmbsAtSameAddress },
    { key: 'gmbsWithSameWebsiteDomain', label: 'Same Domain GMBs', value: data.gmbsWithSameWebsiteDomain },
    { key: 'gmbLinkWithPlaceId', label: 'GMB Link (Place ID)', value: data.gmbLinkWithPlaceId },
    { key: 'gmbLinkWithCid', label: 'GMB Link (CID)', value: data.gmbLinkWithCid }
  ];

  const validLinks = linkFields.filter(field => field.value);
  if (validLinks.length > 0) {
    html += '<div class="data-section">';
    html += '<div class="data-section__title">GMB Links</div>';
    
    validLinks.forEach(field => {
      html += createDataItemWithLabel(field.label, field.value);
    });
    html += '</div>';
  }

  // Business Hours Section
  if (data.hours && Object.keys(data.hours).length > 0) {
    html += '<div class="data-section">';
    html += '<div class="data-section__title">Business Hours</div>';
    
    Object.entries(data.hours).forEach(([day, time]) => {
      html += createDataItemWithLabel(day, time);
    });
    html += '</div>';
  }

  // Current URL
  if (data.url) {
    html += '<div class="data-section">';
    html += '<div class="data-section__title">Current URL</div>';
    html += createDataItemWithLabel('URL', data.url);
    html += '</div>';
  }

  dataContainer.innerHTML = html;
  
  // Add event listeners for copy buttons
  addCopyEventListeners();
  
  // Setup search functionality for data items
  setupDataSearch();
}

// Create a data item with label and copy button for raw value only
function createDataItemWithLabel(label, value) {
  const itemId = 'item_' + Math.random().toString(36).substr(2, 9);
  return `
    <div class="data-item">
      <div class="data-item__value">
        <span class="data-item__label">${label}:</span>
        <span class="data-raw-value" id="${itemId}">${value}</span>
      </div>
      <button class="copy-btn" data-copy-target="${itemId}">Copy</button>
    </div>
  `;
}

// Add event listeners for copy buttons
function addCopyEventListeners() {
  const copyButtons = document.querySelectorAll('.copy-btn');
  copyButtons.forEach(button => {
    button.addEventListener('click', () => {
      const targetId = button.getAttribute('data-copy-target');
      const targetElement = document.getElementById(targetId);
      const textToCopy = targetElement.textContent;
      
      navigator.clipboard.writeText(textToCopy).then(() => {
        // Visual feedback
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.classList.add('btn--copied');
        
        setTimeout(() => {
          button.textContent = originalText;
          button.classList.remove('btn--copied');
        }, 1500);
      }).catch(err => {
        console.error('Failed to copy text: ', err);
        button.textContent = 'Failed';
        setTimeout(() => {
          button.textContent = 'Copy';
        }, 1500);
      });
    });
  });
}

// Reset data search
function resetDataSearch() {
  const searchInput = document.getElementById('dataSearch');
  const clearButton = document.getElementById('clearDataSearch');
  
  if (searchInput) {
    searchInput.value = '';
  }
  
  if (clearButton) {
    clearButton.classList.remove('visible');
  }
  
  // Reset all data items visibility
  const dataItems = document.querySelectorAll('.data-item');
  dataItems.forEach(item => {
    item.classList.remove('search-dimmed', 'search-highlight');
    item.style.display = '';
  });
  
  // Reset all sections visibility
  const sections = document.querySelectorAll('.data-section');
  sections.forEach(section => {
    section.style.display = '';
  });
  
  // CRITICAL: Always ensure search controls remain visible
  const searchWrapper = document.querySelector('.data-search-container-wrapper');
  const searchContainer = document.querySelector('.data-search-container');
  if (searchWrapper) {
    searchWrapper.style.display = '';
    searchWrapper.style.visibility = 'visible';
  }
  if (searchContainer) {
    searchContainer.style.display = '';
    searchContainer.style.visibility = 'visible';
  }
}

// Setup data search functionality
function setupDataSearch() {
  const searchInput = document.getElementById('dataSearch');
  const clearButton = document.getElementById('clearDataSearch');
  
  if (!searchInput || !clearButton) {
    console.log('Data search elements not found');
    return;
  }

  // Get all data items across all sections
  const dataItems = document.querySelectorAll('.data-item');
  
  // Build search index
  const searchIndex = [];
  dataItems.forEach(item => {
    const labelElement = item.querySelector('.data-item__label');
    const valueElement = item.querySelector('.data-raw-value');
    
    if (labelElement) {
      const label = labelElement.textContent.replace(':', '').trim();
      const value = valueElement ? valueElement.textContent.trim() : '';
      
      searchIndex.push({
        element: item,
        label: label,
        value: value,
        searchText: label.toLowerCase()
      });
    }
  });

  let lastSearchTerm = '';

  // Search functionality
  const performSearch = (searchTerm, shouldFilter = false) => {
    if (searchTerm.length < 3) {
      // Reset all items to visible
      dataItems.forEach(item => {
        item.classList.remove('search-dimmed', 'search-highlight');
        item.style.display = '';
      });
      clearButton.classList.remove('visible');
      return;
    }

    clearButton.classList.add('visible');
    const term = searchTerm.toLowerCase();
    let hasMatches = false;

    // Search through all items
    searchIndex.forEach(item => {
      const labelWords = item.label.toLowerCase().split(/\s+/);
      
      // Check for matches
      const hasExactMatch = labelWords.some(word => word === term);
      const hasPartialMatch = item.searchText.includes(term);
      
      if (hasExactMatch || hasPartialMatch) {
        hasMatches = true;
        item.element.classList.remove('search-dimmed');
        item.element.classList.add('search-highlight');
        item.element.style.display = '';
      } else {
        if (shouldFilter) {
          item.element.style.display = 'none';
        } else {
          item.element.classList.add('search-dimmed');
          item.element.classList.remove('search-highlight');
          item.element.style.display = '';
        }
      }
    });

    // Hide empty sections when filtering, but NEVER hide search controls
    if (shouldFilter) {
      const sections = document.querySelectorAll('.data-section');
      sections.forEach(section => {
        const visibleItems = section.querySelectorAll('.data-item:not([style*="display: none"])');
        if (visibleItems.length === 0) {
          section.style.display = 'none';
        } else {
          section.style.display = '';
        }
      });
    } else {
      // Show all sections when not filtering
      const sections = document.querySelectorAll('.data-section');
      sections.forEach(section => {
        section.style.display = '';
      });
    }
    
    // CRITICAL: Always ensure search controls remain visible
    const searchWrapper = document.querySelector('.data-search-container-wrapper');
    const searchContainer = document.querySelector('.data-search-container');
    if (searchWrapper) {
      searchWrapper.style.display = '';
      searchWrapper.style.visibility = 'visible';
    }
    if (searchContainer) {
      searchContainer.style.display = '';
      searchContainer.style.visibility = 'visible';
    }
  };

  // Enhanced input event listener with visual feedback
  searchInput.addEventListener('input', (e) => {
    const searchTerm = e.target.value.trim();
    performSearch(searchTerm, false);
    lastSearchTerm = searchTerm;
    
    // Add visual feedback for active search
    const searchContainer = document.querySelector('.data-search-container');
    if (searchTerm.length >= 3) {
      searchInput.classList.add('search-active');
      if (searchContainer) {
        searchContainer.classList.add('search-active');
      }
    } else {
      searchInput.classList.remove('search-active');
      if (searchContainer) {
        searchContainer.classList.remove('search-active');
      }
    }
  });

  // Enhanced keydown event listener for spacebar detection and Escape
  searchInput.addEventListener('keydown', (e) => {
    if (e.code === 'Space' && lastSearchTerm.length >= 3) {
      e.preventDefault(); // Prevent space from being added
      const searchTerm = searchInput.value.trim();
      performSearch(searchTerm, true);
      
      // Add visual feedback for filtered state
      searchInput.classList.add('search-active', 'search-filtered');
      const searchContainer = document.querySelector('.data-search-container');
      if (searchContainer) {
        searchContainer.classList.add('search-active', 'search-filtered');
      }
      
      console.log(`Search filtered with term: "${searchTerm}"`);
    } else if (e.code === 'Escape') {
      // Enhanced clear search on escape
      searchInput.value = '';
      lastSearchTerm = '';
      performSearch('', false);
      
      // Remove all visual feedback
      searchInput.classList.remove('search-active', 'search-filtered');
      const searchContainer = document.querySelector('.data-search-container');
      if (searchContainer) {
        searchContainer.classList.remove('search-active', 'search-filtered');
      }
      
      // Hide clear button
      clearButton.classList.remove('visible');
      
      searchInput.blur();
      console.log('Search cleared via Escape key');
    }
  });

  // Enhanced clear button functionality
  clearButton.addEventListener('click', () => {
    searchInput.value = '';
    lastSearchTerm = '';
    performSearch('', false);
    
    // Reset all sections visibility (redundant but safe)
    const sections = document.querySelectorAll('.data-section');
    sections.forEach(section => {
      section.style.display = '';
    });
    
    // Remove all search classes
    const dataItems = document.querySelectorAll('.data-item');
    dataItems.forEach(item => {
      item.classList.remove('search-dimmed', 'search-highlight');
      item.style.display = '';
    });
    
    // Hide clear button and remove active search visual feedback
    clearButton.classList.remove('visible');
    searchInput.classList.remove('search-active');
    const searchContainer = document.querySelector('.data-search-container');
    if (searchContainer) {
      searchContainer.classList.remove('search-active');
    }
    
    // Ensure search controls remain visible and focus input
    const searchWrapper = document.querySelector('.data-search-container-wrapper');
    if (searchWrapper) {
      searchWrapper.style.display = '';
      searchWrapper.style.visibility = 'visible';
    }
    
    searchInput.focus();
    console.log('Search cleared and reset to original state');
  });

  console.log('Data search initialized with', searchIndex.length, 'items');
}

// Show empty state
function showEmptyState() {
  claimStatus.style.display = 'none';
  
  // Hide Extract Attributes button when not on a business page
  extractAttributesBtn.style.display = 'none';
  
  // Hide review analysis buttons when not on a business page
  exportReviewBtn.style.display = 'none';
  
  // Hide single review scraper buttons when not on a business page
  singleReviewScraperBtn.style.display = 'none';
  exportSingleReviewBtn.style.display = 'none';
  
  // Hide Pro List review scraper button when not on a business page
  proListReviewScraperBtn.style.display = 'none';
  
  dataContainer.innerHTML = `
    <div class="empty-state">
      <div style="margin-top: 16px;">
        <a href="https://www.google.com/maps/place/" target="_blank" class="maps-search-link">
          🔍 Search on Google Maps
        </a>
      </div>
    </div>
  `;
}

// Show error state
function showErrorState(error) {
  claimStatus.style.display = 'none';
  
  // Hide Extract Attributes button on error
  extractAttributesBtn.style.display = 'none';
  
  // Hide review analysis buttons on error
  exportReviewBtn.style.display = 'none';
  
  // Hide single review scraper buttons on error
  singleReviewScraperBtn.style.display = 'none';
  exportSingleReviewBtn.style.display = 'none';
  
  // Hide Pro List review scraper button on error
  proListReviewScraperBtn.style.display = 'none';
  
  dataContainer.innerHTML = `
    <div class="data-item data-item--error">
      <div class="data-item__value">
        <div style="margin-bottom: 12px;">Error: ${error}</div>
        <div style="margin-bottom: 8px;">Unable to extract data from this page.</div>
        <div>
          <a href="https://www.google.com/maps/place/" target="_blank" class="maps-search-link">
            🔍 Search on Google Maps
          </a>
        </div>
      </div>
    </div>
  `;
}

// Export data to CSV
document.getElementById('exportBtn').addEventListener('click', () => {
  if (!extractedData) return;
  
  const csvContent = convertToCSV(extractedData);
  downloadCSV(csvContent, `gmb-data-${Date.now()}.csv`);
});

// Copy text data for Google Sheets
document.getElementById('copyTextBtn').addEventListener('click', () => {
  if (!extractedData) return;
  
  copyTextData(extractedData);
});


// Extract Attributes button - runs separately from main extraction
document.getElementById('extractAttributesBtn').addEventListener('click', () => {
  extractAttributes();
});

// Review Analysis event listeners
document.getElementById('stopReviewAnalysisBtn').addEventListener('click', () => {
  stopReviewAnalysis();
});

document.getElementById('exportReviewBtn').addEventListener('click', () => {
  exportReviewData();
});

// Single Review Scraper event listeners
document.getElementById('singleReviewScraperBtn').addEventListener('click', () => {
  handleReviewScrapingStep();
});

document.getElementById('exportSingleReviewBtn').addEventListener('click', () => {
  exportSingleReviewData();
});

// Pro List Review Scraper event listener
document.getElementById('proListReviewScraperBtn').addEventListener('click', () => {
  handleProListReviewScraping();
});

// Services extraction event listeners
document.getElementById('servicesExtractionBtn').addEventListener('click', () => {
  startServicesExtraction();
});

document.getElementById('exportServicesBtn').addEventListener('click', () => {
  exportServicesData();
});


// Copy text data formatted for Google Sheets
function copyTextData(data) {
  let fields = [];
  
  // Handle Pro List data
  if (data.type === 'prolist_analysis') {
    const categoryData = data.analysis?.categories;
    const stats = categoryData?.data;
    
    fields = [
      ['Search Term', data.searchTerm || ''],
      ['Analysis Type', 'Pro List Analysis'],
      ['Total Businesses', data.totalBusinesses || ''],
      ['Timestamp', data.timestamp || ''],
      ['Total Categories', stats?.totalCategories || ''],
      ['Total Listings', stats?.totalListings || ''],
      ['Average Categories per Listing', stats?.averageCategoriesPerListing || ''],
      ['Max Categories per Listing', stats?.maxCategoriesPerListing || '']
    ];
    
    // Add top categories
    if (stats?.topCategories && stats.topCategories.length > 0) {
      fields.push(['', '']); // Empty row for separation
      fields.push(['Top Categories', '']);
      stats.topCategories.forEach((cat, index) => {
        fields.push([`${index + 1}. ${cat.category}`, `${cat.count} listings (${cat.percentage}%)`]);
      });
    }
  }
  // Handle multiple listings data
  else if (data.type === 'multiple_listings') {
    const categoryData = data.analysis?.categories;
    const stats = categoryData?.data;
    
    fields = [
      ['Search Term', data.searchTerm || ''],
      ['Analysis Type', 'Multiple Listings'],
      ['Timestamp', data.timestamp || ''],
      ['Total Categories', stats?.totalCategories || ''],
      ['Total Listings', stats?.totalListings || ''],
      ['Average Categories per Listing', stats?.averageCategoriesPerListing || ''],
      ['Max Categories per Listing', stats?.maxCategoriesPerListing || '']
    ];
    
    // Add top categories
    if (stats?.topCategories && stats.topCategories.length > 0) {
      fields.push(['', '']); // Empty row for separation
      fields.push(['Top Categories', '']);
      stats.topCategories.forEach((cat, index) => {
        fields.push([`${index + 1}. ${cat.category}`, `${cat.count} listings (${cat.percentage}%)`]);
      });
    }
  }
  // Handle Google search review analysis data
  else if (data.type === 'google_search_review_analysis' || data.type === 'prolist_review_analysis') {
    fields = [
      ['Analysis Type', data.type === 'prolist_review_analysis' ? 'Pro List Review Analysis' : 'Google Search Review Analysis'],
      ['Total Businesses', data.totalBusinesses || ''],
      ['Total Reviews', data.totalReviews || ''],
      ['Average Rating', data.averageRating !== undefined ? data.averageRating.toFixed(2) : ''],
      ['Average Top 3 Review Count', data.averageReviewCountTop3 || ''],
      ['Timestamp', data.timestamp || '']
    ];
    
    // Add rating distribution
    if (data.ratingDistribution && Object.keys(data.ratingDistribution).length > 0) {
      fields.push(['', '']); // Empty row for separation
      fields.push(['Rating Distribution', '']);
      
      const ratingLabels = {
        '5': '5.0 Stars',
        '4': '4.0-4.9 Stars', 
        '3': '3.0-3.9 Stars',
        '2': '2.0-2.9 Stars',
        '1': '1.0-1.9 Stars',
        '0': 'None'
      };
      
      ['5', '4', '3', '2', '1', '0'].forEach(rating => {
        const count = data.ratingDistribution[rating] || 0;
        const total = Object.values(data.ratingDistribution).reduce((sum, c) => sum + c, 0);
        const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : '0.0';
        fields.push([ratingLabels[rating], `${count} (${percentage}%)`]);
      });
    }
    
    // Add category breakdown
    if (data.categoryBreakdown && Object.keys(data.categoryBreakdown).length > 0) {
      fields.push(['', '']); // Empty row for separation
      fields.push(['Category Breakdown', '']);
      
      Object.entries(data.categoryBreakdown)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10) // Top 10 categories
        .forEach(([category, count]) => {
          const percentage = data.totalBusinesses > 0 ? ((count / data.totalBusinesses) * 100).toFixed(1) : '0.0';
          fields.push([category, `${count} (${percentage}%)`]);
        });
    }
    
    // Add individual business data (first 20 businesses)
    if (data.reviewData && data.reviewData.length > 0) {
      fields.push(['', '']); // Empty row for separation
      fields.push(['Individual Business Data', '']);
      fields.push(['Business Name | Rating | Review Count | Category', '']);
      
      data.reviewData.slice(0, 20).forEach((business, index) => {
        const businessInfo = [
          business.businessName || `Business ${index + 1}`,
          business.rating || 'N/A',
          business.reviewCount || 'N/A',
          business.category || 'N/A'
        ].join(' | ');
        fields.push([`${index + 1}`, businessInfo]);
      });
      
      if (data.reviewData.length > 20) {
        fields.push(['...', `and ${data.reviewData.length - 20} more businesses`]);
      }
    }
  } else {
    // Original single business format
    fields = [
      ['Business Name', data.businessName || ''],
      ['Address', data.address || ''],
      ['Phone', data.phone || ''],
      ['Booking Link', data.bookingLink || ''],
      ['Website', data.website || ''],
      ['Business Status', data.businessStatus || ''],
      ['Main Category', data.mainCategory || ''],
      ['Additional Categories', Array.isArray(data.additionalCategories) ? data.additionalCategories.join('; ') : ''],
      ['All Categories', Array.isArray(data.categories) ? data.categories.join('; ') : (data.categories || '')],
      ['Average Rating', data.averageRating !== undefined && data.averageRating !== null ? data.averageRating : ''],
      ['Rating', data.rating !== undefined && data.rating !== null ? data.rating : ''],
      ['Review Count', data.reviewCount !== undefined && data.reviewCount !== null ? data.reviewCount : ''],
      ['Latitude', data.coordinates?.latitude !== undefined && data.coordinates?.latitude !== null ? data.coordinates.latitude : ''],
      ['Longitude', data.coordinates?.longitude !== undefined && data.coordinates?.longitude !== null ? data.coordinates.longitude : ''],
      ['Coordinates', data.coordinates ? `${data.coordinates.latitude},${data.coordinates.longitude}` : ''],
      ['Place ID', data.placeId || ''],
      ['Knowledge Panel ID', data.knowledgePanelId || ''],
      ['CID Number', data.cidNumber || ''],
      ['Review List Display Link', data.reviewListDisplayLink || ''],
      ['Review Request Link', data.reviewRequestLink || ''],
      ['Knowledge Panel Page Link', data.knowledgePanelPageLink || ''],
      ['GMB Post URL', data.gmbPostUrl || ''],
      ['Ask Question Request URL', data.askQuestionRequestUrl || ''],
      ['Questions and Answers URL', data.questionsAndAnswersUrl || ''],
      ['Products', data.products || ''],
      ['Services', data.services || ''],
      ['Other GMBs at Same Address', data.otherGmbsAtSameAddress || ''],
      ['GMBs with Same Website Domain', data.gmbsWithSameWebsiteDomain || ''],
      ['GMB Link with Place ID', data.gmbLinkWithPlaceId || ''],
      ['GMB Link with CID', data.gmbLinkWithCid || ''],
      ['URL', data.url || ''],
      ['Extracted At', data.extractedAt || '']
    ];
    
    // Add attributes if they exist - each section as one field
    if (data.attributes && Object.keys(data.attributes).length > 0) {
      Object.keys(data.attributes).forEach(sectionTitle => {
        const sectionAttributes = data.attributes[sectionTitle];
        if (Array.isArray(sectionAttributes) && sectionAttributes.length > 0) {
          // Join attributes with bullet points for better readability
          const attributesText = sectionAttributes.map(attr => `• ${attr}`).join('\n');
          fields.push([sectionTitle, attributesText]);
        }
      });
    }
  }
  
  // Format as tab-separated values with each value wrapped in quotes
  // This format works well for pasting into Google Sheets
  const filteredFields = fields.filter(([name, value]) => value !== '' && value !== null && value !== undefined);
  
  const textData = filteredFields
    .map(([name, value]) => `"${name}"\t"${value}"`)
    .join('\n');
  
  navigator.clipboard.writeText(textData).then(() => {
    // Visual feedback
    const originalText = copyTextBtn.textContent;
    copyTextBtn.textContent = 'Copied!';
    copyTextBtn.classList.add('btn--copied');
    
    setTimeout(() => {
      copyTextBtn.textContent = originalText;
      copyTextBtn.classList.remove('btn--copied');
    }, 1500);
  }).catch(err => {
    console.error('Failed to copy text data: ', err);
    copyTextBtn.textContent = 'Failed';
    setTimeout(() => {
      copyTextBtn.textContent = 'Copy Text';
    }, 1500);
  });
}

// Convert data to CSV format
function convertToCSV(data) {
  // Handle Pro List data
  if (data.type === 'prolist_analysis') {
    return convertProListToCSV(data);
  }
  // Handle Pro List review analysis data - use same comprehensive format as Search
  else if (data.type === 'prolist_review_analysis') {
    return convertGoogleSearchReviewDataToCSV(data);
  }
  // Handle multiple listings data
  else if (data.type === 'multiple_listings') {
    return convertMultipleListingsToCSV(data);
  }
  // Handle multiple listings review analysis data (Maps) - use same comprehensive format as Search
  else if (data.type === 'multiple_listings_review_analysis') {
    return convertGoogleSearchReviewDataToCSV(data);
  }
  // Handle Google search review analysis data
  else if (data.type === 'google_search_review_analysis') {
    return convertGoogleSearchReviewDataToCSV(data);
  }
  
  // Original single business CSV format
  const headers = [
    'Business Name', 'Current Address', 'Phone', 'Booking Link', 'Website', 'Business Status', 'Main Category', 'Additional Categories', 'All Categories',
    'Average Rating', 'Rating', 'Review Count', 'Latitude', 'Longitude', 'Coordinates', 'Place ID', 'Knowledge Panel ID (KG ID)',
    'CID Number', 'Review List Display Link', 'Review Request Link',
    'Knowledge Panel Page Link', 'GMB Post URL', 'Ask Question Request URL', 'Questions and Answers URL',
    'Products', 'Services', 'Other GMBs at Same Address', 'GMBs with Same Website Domain',
    'GMB Link with Place ID', 'GMB Link with CID', 'URL', 'Extracted At'
  ];
  
  const row = [
    data.businessName || '',
    data.address || '',
    data.phone || '',
    data.bookingLink || '',
    data.website || '',
    data.businessStatus || '',
    data.mainCategory || '',
    Array.isArray(data.additionalCategories) ? data.additionalCategories.join('; ') : '',
    Array.isArray(data.categories) ? data.categories.join('; ') : (data.categories || ''),
    data.averageRating || '',
    data.rating || '',
    data.reviewCount || '',
    data.coordinates?.latitude || '',
    data.coordinates?.longitude || '',
    data.coordinates ? `${data.coordinates.latitude},${data.coordinates.longitude}` : '',
    data.placeId || '',
    data.knowledgePanelId || '',
    data.cidNumber || '',
    data.reviewListDisplayLink || '',
    data.reviewRequestLink || '',
    data.knowledgePanelPageLink || '',
    data.gmbPostUrl || '',
    data.askQuestionRequestUrl || '',
    data.questionsAndAnswersUrl || '',
    data.products || '',
    data.services || '',
    data.otherGmbsAtSameAddress || '',
    data.gmbsWithSameWebsiteDomain || '',
    data.gmbLinkWithPlaceId || '',
    data.gmbLinkWithCid || '',
    data.url || '',
    data.extractedAt || ''
  ];

  // Add attribute headers and data if they exist - each section as one column
  if (data.attributes && Object.keys(data.attributes).length > 0) {
    Object.keys(data.attributes).forEach(sectionTitle => {
      const sectionAttributes = data.attributes[sectionTitle];
      if (Array.isArray(sectionAttributes) && sectionAttributes.length > 0) {
        headers.push(sectionTitle);
        // Join attributes with bullet points and line breaks for CSV
        const attributesText = sectionAttributes.map(attr => `• ${attr}`).join('\n');
        row.push(attributesText);
      }
    });
  }
  
  return [headers.join(','), row.map(field => `"${field}"`).join(',')].join('\n');
}

// Convert Pro List analysis to CSV format
function convertProListToCSV(data) {
  const headers = [
    'Search Term', 'Analysis Type', 'Total Businesses', 'Timestamp', 'Total Categories', 'Total Listings', 
    'Average Categories per Listing', 'Max Categories per Listing'
  ];
  
  const categoryData = data.analysis?.categories;
  const stats = categoryData?.data;
  
  const row = [
    data.searchTerm || '',
    'Pro List Analysis',
    data.totalBusinesses || '',
    data.timestamp || '',
    stats?.totalCategories || '',
    stats?.totalListings || '',
    stats?.averageCategoriesPerListing || '',
    stats?.maxCategoriesPerListing || ''
  ];
  
  let csvContent = [headers.join(','), row.map(field => `"${field}"`).join(',')].join('\n');
  
  // Add category breakdown
  if (stats?.topCategories && stats.topCategories.length > 0) {
    csvContent += '\n\nCategory Breakdown:\n';
    csvContent += 'Rank,Category,Count,Percentage\n';
    
    stats.topCategories.forEach((cat, index) => {
      const categoryRow = [
        index + 1,
        cat.category,
        cat.count,
        cat.percentage + '%'
      ];
      csvContent += categoryRow.map(field => `"${field}"`).join(',') + '\n';
    });
  }
  
  // Add individual business data if available
  if (data.businesses && data.businesses.length > 0) {
    csvContent += '\n\nIndividual Business Data:\n';
    csvContent += 'Index,Business Name,Rating,Review Count,Categories,Address,Phone,Booking Link,Website,Timestamp,Error\n';
    
    data.businesses.forEach(business => {
      const businessRow = [
        business.index || '',
        business.businessName || '',
        business.rating || '',
        business.reviewCount || '',
        (business.categories || []).join('; '),
        business.address || '',
        business.phone || '',
        business.bookingLink || '',
        business.website || '',
        business.timestamp || '',
        business.error || ''
      ];
      csvContent += businessRow.map(field => `"${field}"`).join(',') + '\n';
    });
  }
  
  return csvContent;
}

// Convert multiple listings analysis to CSV format
function convertMultipleListingsToCSV(data) {
  const headers = [
    'Search Term', 'Analysis Type', 'Timestamp', 'Total Categories', 'Total Listings', 
    'Average Categories per Listing', 'Max Categories per Listing'
  ];
  
  const categoryData = data.analysis?.categories;
  const stats = categoryData?.data;
  
  const row = [
    data.searchTerm || '',
    'Multiple Listings Analysis',
    data.timestamp || '',
    stats?.totalCategories || '',
    stats?.totalListings || '',
    stats?.averageCategoriesPerListing || '',
    stats?.maxCategoriesPerListing || ''
  ];
  
  let csvContent = [headers.join(','), row.map(field => `"${field}"`).join(',')].join('\n');
  
  // Add category breakdown
  if (stats?.topCategories && stats.topCategories.length > 0) {
    csvContent += '\n\nCategory Breakdown:\n';
    csvContent += 'Rank,Category,Count,Percentage\n';
    
    stats.topCategories.forEach((cat, index) => {
      const categoryRow = [
        index + 1,
        cat.category,
        cat.count,
        cat.percentage + '%'
      ];
      csvContent += categoryRow.map(field => `"${field}"`).join(',') + '\n';
    });
  }
  
  return csvContent;
}

// Convert Google search review analysis to CSV format
function convertGoogleSearchReviewDataToCSV(data) {
  if (!data || !data.reviewData) return '';
  
  // Summary data first
  let csvContent = 'Google Search Review Analysis Summary\n';
  csvContent += `Total Businesses,${data.totalBusinesses}\n`;
  csvContent += `Total Reviews,${data.totalReviews}\n`;
  csvContent += `Average Rating,${data.averageRating}\n`;
  csvContent += `Average Top 3 Reviews,${data.averageReviewCountTop3}\n`;
  csvContent += `Analysis Date,${data.timestamp}\n\n`;
  
  // Rating distribution
  csvContent += 'Rating Distribution\n';
  csvContent += 'Rating Range,Count,Percentage\n';
  Object.entries(data.ratingDistribution).forEach(([rating, count]) => {
    const percentage = ((count / data.totalBusinesses) * 100).toFixed(1);
    csvContent += `${rating} stars,${count},${percentage}%\n`;
  });
  csvContent += '\n';
  
  // Category breakdown
  if (data.categoryBreakdown && Object.keys(data.categoryBreakdown).length > 0) {
    csvContent += 'Category Breakdown\n';
    csvContent += 'Category,Count,Percentage\n';
    Object.entries(data.categoryBreakdown)
      .sort(([,a], [,b]) => b - a)
      .forEach(([category, count]) => {
        const percentage = ((count / data.totalBusinesses) * 100).toFixed(1);
        csvContent += `"${category.replace(/"/g, '""')}",${count},${percentage}%\n`;
      });
    csvContent += '\n';
  }
  
  // Competitive Intelligence Report
  csvContent += 'Competitive Intelligence Report\n';
  csvContent += 'Metric,Value,Description\n';
  
  // Calculate competitive intelligence metrics
  const reviewData = data.reviewData;
  const totalReviews = data.totalReviews;
  
  // Top 3 Review Share
  const top3ReviewShare = calculateTop3ReviewShare(reviewData, totalReviews);
  csvContent += `"Top 3 Review Share","${top3ReviewShare}%","Percentage of market reviews controlled by top 3 businesses"\n`;
  
  // Market Quality
  const marketQuality = calculateMarketQuality(reviewData);
  csvContent += `"Market Quality","${marketQuality}%","Percentage of businesses with 4.0+ star ratings"\n`;
  
  // Position vs Review Disconnect
  const top3ByPosition = reviewData.slice(0, 3);
  const top3MinReviews = Math.min(...top3ByPosition.map(t => t.reviewCount || 0));
  const highReviewLowPosition = reviewData.slice(3).filter(b => {
    return (b.reviewCount || 0) > top3MinReviews;
  });
  
  const disconnectedBusinessesCSV = highReviewLowPosition.map(b => {
    const position = reviewData.findIndex(rb => rb.businessName === b.businessName) + 1;
    return `${position}. ${b.businessName} (${b.reviewCount} reviews)`;
  });
  
  const disconnectedListCSV = disconnectedBusinessesCSV.length > 0 ? disconnectedBusinessesCSV.join('; ') : 'None found';
  csvContent += `"Position vs Review Volume Disconnect","${highReviewLowPosition.length} businesses with >${top3MinReviews} reviews not in top 3: ${disconnectedListCSV}","These businesses have more reviews than at least one top 3 competitor but rank lower indicating potential ranking opportunities or SEO issues"\n`;
  
  // Review Efficiency by Position
  const pos1Reviews = reviewData[0]?.reviewCount || 0;
  const pos2Reviews = reviewData[1]?.reviewCount || 0;
  const pos3Reviews = reviewData[2]?.reviewCount || 0;
  csvContent += `"Position #1 Review Count","${pos1Reviews} reviews","Reviews needed to rank #1"\n`;
  csvContent += `"Position #2 Review Count","${pos2Reviews} reviews","Reviews needed to rank #2"\n`;
  csvContent += `"Position #3 Review Count","${pos3Reviews} reviews","Reviews needed to rank #3"\n`;
  
  // Market Entry Requirements
  const minViableReviews = Math.min(...reviewData.map(b => b.reviewCount || 0));
  const recommendedTop3Reviews = Math.min(...top3ByPosition.map(b => b.reviewCount || 0));
  csvContent += `"Min Reviews for Top 10","${minViableReviews} reviews","Minimum reviews to appear in top 10"\n`;
  csvContent += `"Min Reviews for Top 3","${recommendedTop3Reviews}+ reviews","Minimum reviews to compete for top 3"\n`;
  
  // Review Market Concentration
  const sortedByReviews = [...reviewData].sort((a, b) => (b.reviewCount || 0) - (a.reviewCount || 0));
  const top30PercentCount = Math.ceil(reviewData.length * 0.3);
  const top30PercentReviews = sortedByReviews.slice(0, top30PercentCount).reduce((sum, b) => sum + (b.reviewCount || 0), 0);
  const concentrationPercent = Math.round((top30PercentReviews / totalReviews) * 100);
  csvContent += `"Review Market Concentration","${concentrationPercent}%","Top ${top30PercentCount} businesses (30%) control this % of reviews"\n`;
  
  // Perfect Rating Analysis
  const perfectRatingBusinesses = reviewData.filter(b => b.rating === 5.0);
  const perfectRatingPositions = perfectRatingBusinesses.map(b => reviewData.findIndex(rb => rb.businessName === b.businessName) + 1);
  csvContent += `"Perfect 5.0 Star Businesses","${perfectRatingBusinesses.length} businesses","Businesses with perfect ratings at positions: ${perfectRatingPositions.join(', ')}"\n`;
  
  // Market Review Spread
  const maxReviews = Math.max(...reviewData.map(b => b.reviewCount || 0));
  const minReviews = Math.min(...reviewData.map(b => b.reviewCount || 0));
  const reviewSpread = maxReviews - minReviews;
  csvContent += `"Market Review Spread","${reviewSpread} reviews","Difference between highest (${maxReviews}) and lowest (${minReviews}) review counts"\n`;
  
  // High-Rating Low-Review Opportunities
  const highRatingLowReview = reviewData.filter(b => b.rating >= 4.8 && (b.reviewCount || 0) < 50);
  if (highRatingLowReview.length > 0) {
    csvContent += `"High-Rating Low-Review Opportunities","${highRatingLowReview.length} businesses","Businesses with 4.8+ stars and <50 reviews"\n`;
  }
  
  // Easiest Top 3 Target
  const easiestTarget = top3ByPosition.reduce((min, current) => {
    return (current.reviewCount || 0) < (min.reviewCount || 0) ? current : min;
  });
  const targetPosition = reviewData.findIndex(b => b.businessName === easiestTarget.businessName) + 1;
  csvContent += `"Easiest Top 3 Target","${easiestTarget.businessName} (Position #${targetPosition})","Softest target with ${easiestTarget.reviewCount} reviews"\n`;
  
  // Review ROI Predictor
  const avgReviewsPerPosition = reviewData.reduce((sum, b, index) => sum + ((b.reviewCount || 0) / (index + 1)), 0) / reviewData.length;
  const expectedPositionGainPer50Reviews = Math.round((50 / avgReviewsPerPosition) * 10) / 10;
  csvContent += `"Review ROI Predictor","+${expectedPositionGainPer50Reviews} positions per 50 reviews","Expected ranking improvement based on market distribution"\n`;
  
  // Market Leader Vulnerability
  const leader = reviewData[0];
  const perfectRatingCompetitors = reviewData.filter(b => b.rating === 5.0).length;
  const leaderRating = leader?.rating || 0;
  const threatLevel = leaderRating === 5.0 ? 'Low' : perfectRatingCompetitors > 0 ? 'High' : 'Medium';
  csvContent += `"Market Leader Vulnerability","${threatLevel} threat level","Leader has ${leaderRating} rating vs ${perfectRatingCompetitors} perfect competitors"\n`;
  
  csvContent += '\n';
  
  // Individual business data
  csvContent += 'Individual Business Data\n';
  csvContent += 'Business Name,Rating,Review Count,Category,Sample Review,Extracted At\n';
  
  data.reviewData.forEach(business => {
    const row = [
      `"${business.businessName.replace(/"/g, '""')}"`,
      business.rating,
      business.reviewCount,
      business.category ? `"${business.category.replace(/"/g, '""')}"` : '',
      business.sampleReview ? `"${business.sampleReview.replace(/"/g, '""')}"` : '',
      business.extractedAt || ''
    ];
    csvContent += row.join(',') + '\n';
  });
  
  return csvContent;
}

// Download CSV file
function downloadCSV(csvContent, filename) {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

// Show/hide loading state
function showLoading(show) {
  if (show) {
    loading.classList.add('loading--show');
    dataContainer.style.display = 'none';
  } else {
    loading.classList.remove('loading--show');
    dataContainer.style.display = 'block';
  }
}

// Update claim status indicator
function updateClaimStatus(businessStatus) {
  // Hide the header claim status since we now show it inline with business name
  claimStatus.style.display = 'none';
}

// Function to copy individual text to clipboard
function copyToClipboard(text, buttonElement) {
  navigator.clipboard.writeText(text).then(() => {
    // Show feedback on the specific button if provided
    if (buttonElement) {
      const originalText = buttonElement.textContent;
      buttonElement.textContent = 'Copied!';
      buttonElement.classList.add('copy-btn--copied');
      setTimeout(() => {
        buttonElement.textContent = originalText;
        buttonElement.classList.remove('copy-btn--copied');
      }, 1000);
    }
  }).catch(err => {
    console.error('Failed to copy text: ', err);
    if (buttonElement) {
      buttonElement.textContent = 'Failed';
      setTimeout(() => {
        buttonElement.textContent = 'Copy';
      }, 1000);
    }
  });
}

// Make copyToClipboard available globally for onclick handlers
window.copyToClipboard = copyToClipboard;

// Review Analysis Functions
let reviewAnalysisData = [];

// Start review analysis
function startReviewAnalysis() {
  console.log('🚀 POPUP: Starting review analysis...');
  
  // Prevent manual review analysis for multiple listings mode (analysis already happens automatically)
  if (extractedData && extractedData.type === 'multiple_listings') {
    console.log('⚠️ POPUP: Skipping manual review analysis - multiple listings mode already includes automatic analysis');
    return;
  }
  
  // Check if we're on a Google Maps page or Pro List page
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    const currentTab = tabs[0];
    console.log('🌐 POPUP: Current tab URL:', currentTab.url);
    
    const isGoogleMaps = currentTab.url.includes('google.com/maps');
    const isProList = currentTab.url.includes('google.com/localservices/prolist');
    console.log('🗺️ POPUP: Is Google Maps page:', isGoogleMaps);
    console.log('📋 POPUP: Is Pro List page:', isProList);
    
    if (!isGoogleMaps && !isProList) {
      console.error('❌ POPUP: Not on Google Maps or Pro List page');
      showErrorState('Please navigate to a Google Maps search results page or Pro List page with multiple business listings.');
      return;
    }
    
    console.log('✅ POPUP: On Google Maps page, proceeding with analysis');
    
    // Show review analysis container
    reviewAnalysisContainer.style.display = 'block';
    console.log('📱 POPUP: Showing review analysis container');
    
    // Show progress section for new analysis
    showProgressSection();
    
    // Update UI state
    stopReviewAnalysisBtn.disabled = false;
    stopReviewAnalysisBtn.style.display = 'inline-block';
    exportReviewBtn.disabled = true;
    console.log('🎛️ POPUP: Updated UI state');
    
    // Reset data
    reviewAnalysisData = [];
    reviewResults.style.display = 'none';
    reviewProgressBar.style.display = 'none';
    console.log('🔄 POPUP: Reset data and UI elements');
    
    // Update status in main status bar and review section
    statusText.textContent = 'Starting review analysis...';
    statusIndicator.className = 'status__indicator status__indicator--active';
    reviewStatus.textContent = 'Starting analysis...';
    console.log('📝 POPUP: Updated status text');
    
    // Send message to content script to start analysis
    console.log('📨 POPUP: Sending message to content script...');
    
    // Choose the appropriate message type based on page type
    const messageType = isProList ? 'startProListReviewAnalysis' : 'startReviewAnalysis';
    console.log('📨 POPUP: Using message type:', messageType);
    
    // Send message to content script to start analysis
    let message = { type: messageType };
    
    chrome.tabs.sendMessage(tabs[0].id, message, (response) => {
      console.log('📨 POPUP: Received response from content script:', response);
      
      if (chrome.runtime.lastError) {
        console.error('💥 POPUP: Chrome runtime error:', chrome.runtime.lastError);
        statusText.textContent = 'Review Analysis Error: Content script not loaded';
        statusIndicator.className = 'status__indicator';
        showReviewError('Content script not loaded. Please refresh the page and try again.');
        resetReviewAnalysisUI();
        return;
      }
      
      if (response && response.success) {
        console.log('✅ POPUP: Analysis started successfully');
        statusText.textContent = 'Review analysis started...';
        statusIndicator.className = 'status__indicator status__indicator--active';
        reviewStatus.textContent = 'Analysis started...';
      } else {
        console.error('❌ POPUP: Failed to start analysis, response:', response);
        statusText.textContent = 'Review Analysis Error: Failed to start';
        statusIndicator.className = 'status__indicator';
        showReviewError('Failed to start review analysis');
        resetReviewAnalysisUI();
      }
    });
  });
}

// Stop review analysis
function stopReviewAnalysis() {
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    const currentTab = tabs[0];
    const isProList = currentTab.url.includes('google.com/localservices/prolist');
    
    // Choose the appropriate message type based on page type
    const messageType = isProList ? 'stopProListReviewAnalysis' : 'stopReviewAnalysis';
    
    chrome.tabs.sendMessage(tabs[0].id, {type: messageType}, (response) => {
      console.log('Stop analysis response:', response);
    });
  });
  
  resetReviewAnalysisUI();
  reviewStatus.textContent = 'Analysis stopped by user';
}

// Reset review analysis UI
function resetReviewAnalysisUI() {
  stopReviewAnalysisBtn.disabled = true;
  stopReviewAnalysisBtn.style.display = 'none';
  
  if (reviewAnalysisData.length > 0) {
    exportReviewBtn.disabled = false;
  }
}

// Export review data
function exportReviewData() {
  console.log('🔄 Export Review Data: Starting export...');
  
  // Check if we have review analysis data - use extractedData instead of reviewAnalysisData
  if (!extractedData || (!extractedData.reviewData || extractedData.reviewData.length === 0)) {
    console.warn('❌ Export Review Data: No review data to export');
    alert('No review data available to export. Please run a review analysis first.');
    return;
  }
  
  console.log(`📊 Export Review Data: Found data to export:`, extractedData);
  
  try {
    let csvContent = '';
    
    // Handle different data types appropriately
    if (extractedData.type === 'google_search_review_analysis' || extractedData.type === 'prolist_review_analysis') {
      // Create CSV for Google Search or Pro List review analysis using comprehensive converter
      csvContent = convertToCSV(extractedData);
    } else if (extractedData.reviewData && Array.isArray(extractedData.reviewData)) {
      // Fallback: try to create CSV from review data array
      csvContent = convertReviewDataToCSV(extractedData.reviewData);
    } else {
      throw new Error('Unsupported data format for export');
    }
    
    if (csvContent && csvContent.length > 0) {
      // Generate filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const dataType = extractedData.type === 'prolist_review_analysis' ? 'prolist' : 'google-search';
      const filename = `${dataType}-review-analysis-${timestamp}.csv`;
      
      console.log(`✅ Export Review Data: Downloading file: ${filename}`);
      downloadCSV(csvContent, filename);
      
      // Show success feedback
      const originalText = exportReviewBtn.textContent;
      exportReviewBtn.textContent = '✅ Exported!';
      exportReviewBtn.style.backgroundColor = '#22c55e';
      
      setTimeout(() => {
        exportReviewBtn.textContent = originalText;
        exportReviewBtn.style.backgroundColor = '';
      }, 2000);
      
    } else {
      throw new Error('Failed to generate CSV content');
    }
    
  } catch (error) {
    console.error('💥 Export Review Data: Error during export:', error);
    alert(`Export failed: ${error.message}`);
    
    // Show error feedback
    const originalText = exportReviewBtn.textContent;
    exportReviewBtn.textContent = '❌ Failed';
    exportReviewBtn.style.backgroundColor = '#ef4444';
    
    setTimeout(() => {
      exportReviewBtn.textContent = originalText;
      exportReviewBtn.style.backgroundColor = '';
    }, 2000);
  }
}

// Convert review data to CSV
function convertReviewDataToCSV(data) {
  if (!data || data.length === 0) return '';
  
  // Find aggregate data
  const aggregateItem = data.find(item => item.isAggregate);
  const businessData = data.filter(item => !item.isAggregate);
  
  let csvContent = '';
  
  // Add aggregate summary section if available
  if (aggregateItem && aggregateItem.aggregateData) {
    const agg = aggregateItem.aggregateData;
    csvContent += 'AGGREGATE SUMMARY\n';
    csvContent += 'Metric,Value\n';
    csvContent += `Total Businesses,${agg.totalBusinesses}\n`;
    csvContent += `Total Reviews,${agg.totalReviews}\n`;
    csvContent += `Average Rating,${agg.averageRating}\n`;
    csvContent += `Average Top 3 Review Count,${agg.averageReviewCountTop3 || 0}\n`;
    csvContent += `Highest Rated Business (top 3),"${agg.highestRated.name.replace(/"/g, '""')}"\n`;
    csvContent += `Highest Rating,${agg.highestRated.rating}\n`;
    csvContent += `Most Reviewed Business,"${agg.mostReviews.name.replace(/"/g, '""')}"\n`;
    csvContent += `Most Reviews Count,${agg.mostReviews.count}\n`;
    
    // Add top 3 listings breakdown (support both old and new format)
    const top3Data = agg.top3Listings || agg.top3Competitors;
    if (top3Data && top3Data.length > 0) {
      csvContent += '\nTOP 3 LISTINGS BY POSITION\n';
      csvContent += 'Rank,Business Name,Review Count\n';
      top3Data.forEach((listing, index) => {
        csvContent += `${index + 1},"${listing.name.replace(/"/g, '""')}",${listing.reviewCount}\n`;
      });
    }
    
    csvContent += '\n';
  }
  
  // Add individual business data
  const headers = ['Index', 'Business Name', 'Rating', 'Review Count', 'Timestamp', 'Error'];
  csvContent += 'INDIVIDUAL BUSINESS DATA\n';
  csvContent += headers.join(',') + '\n';
  
  businessData.forEach(item => {
    const row = [
      item.index || '',
      `"${(item.businessName || '').replace(/"/g, '""')}"`,
      item.rating || '',
      item.reviewCount || '',
      item.timestamp || '',
      item.error ? `"${item.error.replace(/"/g, '""')}"` : ''
    ];
    csvContent += row.join(',') + '\n';
  });
  
  return csvContent;
}

// Display review analysis results (refactored to use shared function)
function displayReviewResults(data) {
  if (!data || data.length === 0) {
    reviewResultsList.innerHTML = '<div class="empty-state"><p>No review data extracted</p></div>';
    return;
  }
  
  // Find aggregate data
  const aggregateItem = data.find(item => item.isAggregate);
  const businessData = data.filter(item => !item.isAggregate);
  
  if (aggregateItem && aggregateItem.aggregateData) {
    const agg = aggregateItem.aggregateData;
    
    // Prepare data for shared HTML generation function
    const analysisData = {
      reviewData: businessData,
      totalBusinesses: agg.totalBusinesses,
      totalReviews: agg.totalReviews,
      averageRating: agg.averageRating,
      averageReviewCountTop3: agg.averageReviewCountTop3,
      ratingDistribution: agg.ratingDistribution || {},
      categoryBreakdown: agg.categoryBreakdown || {},
      highestRated: agg.highestRated,
      mostReviews: agg.mostReviews
    };
    
    // Use shared HTML generation function
    const html = generateReviewAnalysisHTML(analysisData, 'GOOGLE SEARCH REVIEW ANALYSIS');
    
    reviewResultsList.innerHTML = html;
    reviewResults.style.display = 'block';
    
    // Store data for export
    extractedData = {
      type: 'google_search_review_analysis',
      timestamp: new Date().toISOString(),
      totalBusinesses: agg.totalBusinesses,
      totalReviews: agg.totalReviews,
      averageRating: agg.averageRating,
      averageReviewCountTop3: agg.averageReviewCountTop3,
      reviewData: businessData,
      ratingDistribution: agg.ratingDistribution,
      categoryBreakdown: agg.categoryBreakdown
    };
    
    // Enable export button
    exportReviewBtn.disabled = false;
  } else {
    reviewResultsList.innerHTML = '<div class="empty-state"><p>No aggregate data available</p></div>';
  }
}

// Show review analysis error
function showReviewError(message) {
  reviewStatus.textContent = `Error: ${message}`;
  reviewStatus.style.color = '#fca5a5';
}

// Update review progress bar
function updateReviewProgress(current, total) {
  const percentage = total > 0 ? (current / total) * 100 : 0;
  reviewProgressFill.style.width = `${percentage}%`;
  reviewProgressText.textContent = `${current}/${total}`;
}

// Hide the progress section for minimalistic view after completion
function hideProgressSection() {
  // Hide the progress section title and progress elements
  const progressSectionTitle = reviewAnalysisContainer.querySelector('.data-section__title');
  const reviewProgress = document.getElementById('reviewProgress');
  const reviewProgressBar = document.getElementById('reviewProgressBar');
  
  if (progressSectionTitle) {
    progressSectionTitle.style.display = 'none';
  }
  if (reviewProgress) {
    reviewProgress.style.display = 'none';
  }
  if (reviewProgressBar) {
    reviewProgressBar.style.display = 'none';
  }
  
  console.log('Progress section hidden for minimalistic view');
}

// Show the progress section (for when starting new analysis)
function showProgressSection() {
  // Show the progress section title and progress elements
  const progressSectionTitle = reviewAnalysisContainer.querySelector('.data-section__title');
  const reviewProgress = document.getElementById('reviewProgress');
  const reviewProgressBar = document.getElementById('reviewProgressBar');
  
  if (progressSectionTitle) {
    progressSectionTitle.style.display = 'block';
  }
  if (reviewProgress) {
    reviewProgress.style.display = 'flex';
  }
  if (reviewProgressBar) {
    reviewProgressBar.style.display = 'flex';
  }
  
  console.log('Progress section shown for new analysis');
}

// Listen for updates from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // Listen for Pro List review analysis updates
  if (request.type === 'proListReviewUpdate') {
    const { status, data } = request;
    
    switch (status) {
      case 'started':
        // Update main status bar
        statusText.textContent = `Pro List Review Analysis: Found ${data.total} listings to analyze`;
        statusIndicator.className = 'status__indicator status__indicator--active';
        
        // Also update review status for consistency
        reviewStatus.textContent = `Found ${data.total} listings to analyze`;
        reviewStatus.style.color = '#d1d5db';
        reviewProgressBar.style.display = 'block';
        updateReviewProgress(data.current, data.total);
        break;
        
      case 'progress':
        // Update main status bar
        statusText.textContent = `Pro List Review Analysis: Processing listing ${data.current}/${data.total}`;
        statusIndicator.className = 'status__indicator status__indicator--active';
        
        // Also update review status
        reviewStatus.textContent = `Analyzing listing ${data.current}/${data.total}`;
        updateReviewProgress(data.current, data.total);
        reviewAnalysisData = data.data;
        if (data.data.length > 0) {
          // Format data like regular search and use generateReviewAnalysisHTML directly
          displayProListReviewResults(data.data);
        }
        break;
        
      case 'completed':
        // Update main status bar
        statusText.textContent = `Pro List Review Analysis Complete! Processed ${data.total} listings`;
        statusIndicator.className = 'status__indicator status__indicator--active';
        
        // Also update review status
        reviewStatus.textContent = `Analysis complete! Processed ${data.total} listings`;
        reviewStatus.style.color = '#86efac';
        updateReviewProgress(data.total, data.total);
        reviewAnalysisData = data.data;
        // Format data like regular search and use generateReviewAnalysisHTML directly
        displayProListReviewResults(data.data);
        resetReviewAnalysisUI();
        
        // Hide the progress section after completion for minimalistic view
        hideProgressSection();
        break;
        
      case 'error':
        // Update main status bar
        statusText.textContent = `Pro List Review Analysis Error: ${data.error}`;
        statusIndicator.className = 'status__indicator';
        
        // Also update review status
        showReviewError(data.error);
        reviewAnalysisData = data.data || [];
        if (reviewAnalysisData.length > 0) {
          // Format data like regular search and use generateReviewAnalysisHTML directly
          displayProListReviewResults(reviewAnalysisData);
        }
        resetReviewAnalysisUI();
        
        // Hide the progress section on error as well for minimalistic view
        hideProgressSection();
        break;
        
      case 'stopped':
        // Update main status bar
        statusText.textContent = 'Pro List Review Analysis Stopped';
        statusIndicator.className = 'status__indicator';
        
        // Also update review status
        reviewStatus.textContent = 'Analysis stopped';
        reviewStatus.style.color = '#fbbf24';
        reviewAnalysisData = data.data || [];
        if (reviewAnalysisData.length > 0) {
          // Format data like regular search and use generateReviewAnalysisHTML directly
          displayProListReviewResults(reviewAnalysisData);
        }
        resetReviewAnalysisUI();
        
        // Hide the progress section when stopped for minimalistic view
        hideProgressSection();
        break;
    }
    
    sendResponse({received: true});
  }
  
  // Listen for review analysis updates
  if (request.type === 'reviewAnalysisUpdate') {
    const { status, data } = request;
    
    switch (status) {
      case 'started':
        // Update main status bar
        statusText.textContent = `Review Analysis: Found ${data.total} listings to analyze`;
        statusIndicator.className = 'status__indicator status__indicator--active';
        
        // Also update review status for consistency
        reviewStatus.textContent = `Found ${data.total} listings to analyze`;
        reviewStatus.style.color = '#d1d5db';
        reviewProgressBar.style.display = 'block';
        updateReviewProgress(data.current, data.total);
        break;
        
      case 'progress':
        // Update main status bar
        statusText.textContent = `Review Analysis: Processing listing ${data.current}/${data.total}`;
        statusIndicator.className = 'status__indicator status__indicator--active';
        
        // Also update review status
        reviewStatus.textContent = `Analyzing listing ${data.current}/${data.total}`;
        updateReviewProgress(data.current, data.total);
        reviewAnalysisData = data.data;
        if (data.data.length > 0) {
          displayReviewResults(data.data);
        }
        break;
        
      case 'completed':
        // Update main status bar
        statusText.textContent = `Review Analysis Complete! Processed ${data.total} listings`;
        statusIndicator.className = 'status__indicator status__indicator--active';
        
        // Also update review status
        reviewStatus.textContent = `Analysis complete! Processed ${data.total} listings`;
        reviewStatus.style.color = '#86efac';
        updateReviewProgress(data.total, data.total);
        reviewAnalysisData = data.data;
        displayReviewResults(data.data);
        resetReviewAnalysisUI();
        
        // Hide the progress section after completion for minimalistic view
        hideProgressSection();
        break;
        
      case 'error':
        // Update main status bar
        statusText.textContent = `Review Analysis Error: ${data.error}`;
        statusIndicator.className = 'status__indicator';
        
        // Also update review status
        showReviewError(data.error);
        reviewAnalysisData = data.data || [];
        if (reviewAnalysisData.length > 0) {
          displayReviewResults(reviewAnalysisData);
        }
        resetReviewAnalysisUI();
        
        // Hide the progress section on error as well for minimalistic view
        hideProgressSection();
        break;
        
      case 'stopped':
        // Update main status bar
        statusText.textContent = 'Review Analysis Stopped';
        statusIndicator.className = 'status__indicator';
        
        // Also update review status
        reviewStatus.textContent = 'Analysis stopped';
        reviewStatus.style.color = '#fbbf24';
        reviewAnalysisData = data.data || [];
        if (reviewAnalysisData.length > 0) {
          displayReviewResults(reviewAnalysisData);
        }
        resetReviewAnalysisUI();
        
        // Hide the progress section when stopped for minimalistic view
        hideProgressSection();
        break;
    }
    
    sendResponse({received: true});
  }
  
  // Listen for single review scraper updates
  if (request.action === 'singleReviewProgress') {
    const { status, data } = request;
    
    switch (status) {
      case 'scrolling':
        singleReviewProgressText.textContent = `Scrolling... Attempt ${data.scrollAttempt}/${data.maxScrollAttempts} - Found ${data.reviewCount} reviews`;
        break;
        
      default:
        singleReviewProgressText.textContent = `Status: ${status}`;
        break;
    }
    
    sendResponse({received: true});
  }
});

// Global variables for review scraping
let singleReviewData = [];
let reviewScrapingStep = 1; // 1 = instructions, 2 = ready to scrape

// Handle review scraping step
function handleReviewScrapingStep() {
  console.log('Popup: Handling review scraping step:', reviewScrapingStep);
  
  // Check if we're on a Google Maps page
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    const currentTab = tabs[0];
    const isGoogleMaps = currentTab.url.includes('google.com/maps');
    
    if (!isGoogleMaps) {
      statusText.textContent = 'Navigate to Google Maps for review scraping';
        statusIndicator.className = 'status__indicator';
      showErrorState('Please navigate to a Google Maps business listing first.');
        return;
      }
      
    if (reviewScrapingStep === 1) {
      // Step 1: Show instructions and open persistent popup
      showReviewInstructions();
      // Also open the persistent popup automatically
      openPersistentPopup();
    } else if (reviewScrapingStep === 2) {
      // Step 2: Run the scraping in persistent popup
      openPersistentPopup();
    }
  });
}

// Show instructions for manual review preparation
function showReviewInstructions() {
  // Update UI to show instructions
  singleReviewContainer.style.display = 'block';
  singleReviewProgress.style.display = 'block';
  
  // Clear previous results
  singleReviewResultsList.innerHTML = '';
  singleReviewData = [];
  
  // Show instructions
  singleReviewProgressText.innerHTML = `
    <div style="color: #fbbf24; font-weight: bold; margin-bottom: 12px;">🚀 AUTOMATED EXTRACTION</div>
    <div style="color: #d1d5db; line-height: 1.5; margin-bottom: 12px;">
      The scraper will automatically handle everything:
    </div>
    <div style="color: #d1d5db; line-height: 1.6; font-size: 12px;">
      1. Switch to the <strong style="color: #60a5fa;">"Reviews"</strong> tab<br>
      2. Set a limit if you want to extract a specific number of reviews<br>
      <div style="color: #9ca3af; font-style: italic; margin-top: 8px;">Just click "Start Extraction" when ready!</div>
    </div>
  `;
  
  // Update button text and step
  singleReviewScraperBtn.textContent = 'Open Persistent Popup';
  reviewScrapingStep = 2;
  
  // Update status
  statusText.textContent = 'Follow the instructions to prepare reviews';
  statusIndicator.className = 'status__indicator status__indicator--active';
}

// Reset review scraping UI
function resetReviewScrapingUI() {
  singleReviewScraperBtn.disabled = false;
  singleReviewScraperBtn.textContent = 'Data Extractor';
  reviewScrapingStep = 1;
  singleReviewProgress.style.display = 'none';
}

// Display single review results
function displaySingleReviewResults(reviewData) {
  if (!reviewData || reviewData.length === 0) {
    singleReviewResultsList.innerHTML = '<div class="data-item data-item--error"><div class="data-item__value">No reviews found</div></div>';
    return;
  }
  
  console.log('Popup: Displaying single review results:', reviewData.length, 'reviews');
  
  let html = '';
  
  // Summary section
  html += '<div class="data-section">';
  html += '<div class="data-section__title">Review Summary</div>';
  html += createDataItemWithLabel('Total Reviews Found', reviewData.length);
  
  // Calculate average rating
  const ratingsWithValues = reviewData.filter(review => review.rating && review.rating > 0);
  if (ratingsWithValues.length > 0) {
    const averageRating = ratingsWithValues.reduce((sum, review) => sum + review.rating, 0) / ratingsWithValues.length;
    html += createDataItemWithLabel('Average Rating', generateFlatStars(averageRating.toFixed(1)));
  }
  
  // Count reviews with text
  const reviewsWithText = reviewData.filter(review => review.reviewText && review.reviewText.trim().length > 0);
  html += createDataItemWithLabel('Reviews with Text', reviewsWithText.length);
  
  // Count reviews with responses
  const reviewsWithResponses = reviewData.filter(review => review.response && review.response.trim().length > 0);
  html += createDataItemWithLabel('Reviews with Business Response', reviewsWithResponses.length);
  
  html += '</div>';
  
  // Individual reviews section (show first 10)
  html += '<div class="data-section">';
  html += '<div class="data-section__title">Recent Reviews (First 10)</div>';
  
  const reviewsToShow = reviewData.slice(0, 10);
  reviewsToShow.forEach((review, index) => {
    html += '<div class="data-item">';
    html += `<div class="data-item__label">Review ${review.id || index + 1}</div>`;
    html += '<div class="data-item__value">';
    
    if (review.reviewerName) {
      html += `<strong>${review.reviewerName}</strong><br>`;
    }
    
    if (review.rating) {
      html += `Rating: ${generateFlatStars(review.rating)}<br>`;
    }
    
    if (review.date) {
      html += `Date: ${review.date}<br>`;
    }
    
    if (review.reviewText) {
      const truncatedText = review.reviewText.length > 200 
        ? review.reviewText.substring(0, 200) + '...' 
        : review.reviewText;
      html += `<em>"${truncatedText}"</em>`;
    }
    
    html += '</div>';
    html += '</div>';
  });
  
  if (reviewData.length > 10) {
    html += '<div class="data-item">';
    html += '<div class="data-item__value" style="text-align: center; font-style: italic; color: #9ca3af;">';
    html += `... and ${reviewData.length - 10} more reviews (export CSV to see all)`;
    html += '</div>';
    html += '</div>';
  }
  
  html += '</div>';
  
  singleReviewResultsList.innerHTML = html;
  singleReviewContainer.style.display = 'block';
}

// Export single review data
function exportSingleReviewData() {
  if (!singleReviewData || singleReviewData.length === 0) {
    console.warn('Popup: No single review data to export');
    return;
  }
  
  console.log('Popup: Exporting single review data...');
  
  chrome.tabs.query({active: true, currentWindow: true}, tabs => {
    chrome.tabs.sendMessage(tabs[0].id, {action: "exportSingleReviewCSV"}, (response) => {
      if (response && response.csvContent) {
        // Extract business name for filename
        let businessName = 'unknown-business';
        if (response.businessName) {
          businessName = formatBusinessNameForFilename(response.businessName);
        } else if (singleReviewData && singleReviewData.length > 0 && singleReviewData[0].businessName) {
          businessName = formatBusinessNameForFilename(singleReviewData[0].businessName);
        }
        
        const timestamp = new Date().toISOString().split('T')[0]; // Get YYYY-MM-DD format
        const filename = `${businessName}-reviews-${timestamp}.csv`;
        downloadCSV(response.csvContent, filename);
        
        // Visual feedback
        const originalText = exportSingleReviewBtn.textContent;
        exportSingleReviewBtn.textContent = 'Exported!';
        exportSingleReviewBtn.classList.add('btn--copied');
        
        setTimeout(() => {
          exportSingleReviewBtn.textContent = originalText;
          exportSingleReviewBtn.classList.remove('btn--copied');
        }, 1500);
      } else {
        console.error('Popup: Failed to export single review data');
        exportSingleReviewBtn.textContent = 'Export Failed';
        setTimeout(() => {
          exportSingleReviewBtn.textContent = 'Export Single Reviews CSV';
        }, 1500);
      }
    });
  });
}

// Open persistent popup
function openPersistentPopup() {
  console.log('Popup: Opening persistent popup...');
  
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    const currentTab = tabs[0];
    const isGoogleMaps = currentTab.url.includes('google.com/maps');
    
    if (!isGoogleMaps) {
      statusText.textContent = 'Navigate to Google Maps page first';
        statusIndicator.className = 'status__indicator';
      return;
    }
    
    chrome.tabs.sendMessage(tabs[0].id, {action: "openPersistentPopup"}, (response) => {
      if (chrome.runtime.lastError) {
        statusText.textContent = 'Content script not loaded - please refresh the page';
        statusIndicator.className = 'status__indicator';
        return;
      }
      
      if (response && response.success) {
        console.log('Popup: Persistent popup opened successfully');
        statusText.textContent = 'Persistent popup opened - follow instructions';
        statusIndicator.className = 'status__indicator status__indicator--active';
        // Close the extension popup since the persistent one is now open
        window.close();
      } else {
        console.error('Popup: Failed to open persistent popup');
        statusText.textContent = 'Failed to open persistent popup';
        statusIndicator.className = 'status__indicator';
      }
    });
  });
}

// Handle Pro List Review Scraping
function handleProListReviewScraping() {
  console.log('Popup: Handling Pro List review scraping...');
  
  // Check if we're on a Pro List page
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    const currentTab = tabs[0];
    const isProList = currentTab.url.includes('google.com/localservices/prolist');
    
    if (!isProList) {
      statusText.textContent = 'Navigate to Pro List page first';
      statusIndicator.className = 'status__indicator';
      showErrorState('Please navigate to a Google Local Services Pro List page first.');
      return;
    }
    
    // Open the persistent popup with Pro List specific instructions
    chrome.tabs.sendMessage(tabs[0].id, {action: "openPersistentPopup"}, (response) => {
      if (chrome.runtime.lastError) {
        statusText.textContent = 'Content script not loaded - please refresh the page';
        statusIndicator.className = 'status__indicator';
        return;
      }
      
      if (response && response.success) {
        console.log('Popup: Persistent popup opened for Pro List review scraping');
        statusText.textContent = 'Data extractor opened - follow instructions';
        statusIndicator.className = 'status__indicator status__indicator--active';
        // Close the extension popup since the persistent one is now open
        window.close();
      } else {
        console.error('Popup: Failed to open persistent popup for Pro List');
        statusText.textContent = 'Failed to open data extractor';
        statusIndicator.className = 'status__indicator';
      }
    });
  });
}


// Start services extraction
function startServicesExtraction() {
  console.log('Popup: Creating services extraction docked interface...');
  
  // Send message to content script to create services extraction docked interface
  chrome.tabs.query({active: true, currentWindow: true}, tabs => {
    chrome.tabs.sendMessage(tabs[0].id, {action: "startGoogleSearchServicesExtraction"}, (response) => {
      if (chrome.runtime.lastError) {
        console.error('Popup: Error creating services extraction interface:', chrome.runtime.lastError);
        alert('Failed to create services extraction interface. Please refresh the page and try again.');
        return;
      }
      
      if (response && response.success) {
        console.log('Popup: Services extraction docked interface created successfully');
        // Close the popup since we're using the docked interface
        window.close();
      } else {
        console.error('Popup: Services extraction interface creation failed:', response);
        alert(response?.error || 'Failed to create services extraction interface');
      }
    });
  });
}

function displayServicesResults(servicesData) {
  // This function is no longer needed since we're using the docked interface
  console.log('Popup: Services results will be displayed in docked interface');
}

function updateServicesProgress(current, total) {
  // This function is no longer needed since we're using the docked interface
  console.log('Popup: Services progress will be shown in docked interface');
}

function showServicesError(message) {
  // This function is no longer needed since we're using the docked interface
  console.log('Popup: Services error will be shown in docked interface:', message);
}

function exportServicesData() {
  // This function is no longer needed since we're using the docked interface
  console.log('Popup: Services export will be handled by docked interface');
}

function convertServicesDataToCSV(data) {
  // This function is no longer needed since we're using the docked interface
  console.log('Popup: CSV conversion will be handled by docked interface');
  return '';
}

function downloadCSV(csvContent, filename) {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

// Helper function to generate Google-style flat star ratings
function generateFlatStars(rating) {
  if (!rating) return '';
  
  const numericRating = parseFloat(rating);
  if (isNaN(numericRating) || numericRating < 0 || numericRating > 5) return '';
  
  const fullStars = Math.floor(numericRating);
  const hasHalfStar = numericRating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  return `
    <span class="gmb-star-rating" style="display: inline-flex; align-items: center; gap: 1px; font-size: 14px; color: #fbbf24;">
      ${'★'.repeat(fullStars)}${hasHalfStar ? '☆' : ''}${'☆'.repeat(emptyStars)}
      <span style="margin-left: 4px; font-size: 12px; color: #9ca3af;">${rating}</span>
    </span>
  `;
}

// Function to open business website
function openBusinessWebsite(businessName) {
  if (!businessName) return;
  
  const decodedName = decodeURIComponent(businessName);
  const searchQuery = encodeURIComponent(`${decodedName} website`);
  const googleSearchUrl = `https://www.google.com/search?q=${searchQuery}`;
  
  // Open in new tab
  window.open(googleSearchUrl, '_blank');
}

// Helper method to format business name for filename
function formatBusinessNameForFilename(businessName) {
  if (!businessName) return 'unknown-business';
  
  return businessName
      .toLowerCase()                    // Convert to lowercase
      .replace(/[^a-z0-9\s]/g, '')     // Remove special characters except spaces
      .replace(/\s+/g, '-')            // Replace spaces with dashes
      .replace(/-+/g, '-')             // Replace multiple consecutive dashes with single dash
      .replace(/^-|-$/g, '');          // Remove leading/trailing dashes
}

// Helper function to create CSV for review analysis data
function createReviewAnalysisCSV(data) {
    // Similar to regular CSV but with additional review analysis fields
    const items = data.items || [];
    
    if (items.length === 0) {
        return 'No data available';
    }

    // Get all possible fields from all items
    const allFields = new Set();
    items.forEach(item => {
        Object.keys(item).forEach(key => allFields.add(key));
    });

    const headers = Array.from(allFields);
    const csvContent = [
        headers.join(','),
        ...items.map(item => 
            headers.map(header => {
                const value = item[header];
                if (value === null || value === undefined) return '';
                if (typeof value === 'string' && value.includes(',')) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return value;
            }).join(',')
        )
    ].join('\n');

    return csvContent;
}

// Quick Actions functionality
function setupQuickActions() {
    // Add event listener for Htags button
    if (htagsBtn) {
        htagsBtn.addEventListener('click', () => {
            executeHtagsScript();
        });
    }
    
    // Add event listener for Heading Structure button
    if (headingStructureBtn) {
        headingStructureBtn.addEventListener('click', () => {
            executeHeadingStructureScript();
        });
    }
    
    // Add event listener for Show Links button
    if (showLinksBtn) {
        showLinksBtn.addEventListener('click', () => {
            executeShowLinksScript();
        });
    }
    
    // Add event listener for Show Hidden button
    if (showHiddenBtn) {
        showHiddenBtn.addEventListener('click', () => {
            executeShowHiddenScript();
        });
    }
    
    // Add event listener for Bold From SERP button
    if (boldFromSerpBtn) {
        boldFromSerpBtn.addEventListener('click', () => {
            executeBoldFromSerpScript();
        });
    }
    
    // Add event listener for Schema button
    if (schemaBtn) {
        schemaBtn.addEventListener('click', () => {
            executeSchemaScript();
        });
    }
    
    // Add event listener for Images button
    if (imagesBtn) {
        imagesBtn.addEventListener('click', () => {
            executeImagesScript();
        });
    }
    
    // Add event listener for Metadata button
    if (metadataBtn) {
        metadataBtn.addEventListener('click', () => {
            executeMetadataScript();
        });
    }

    // Add event listener for UTM Builder button
    if (utmBuilderBtn) {
        utmBuilderBtn.addEventListener('click', () => {
            executeUTMBuilderScript();
        });
    }
    
    // Add event listener for Page Structure button
    if (pageStructureBtn) {
        pageStructureBtn.addEventListener('click', () => {
            executePageStructureScript();
        });
    }
    
    // Add event listener for Copy Element button
    if (copyElementBtn) {
        copyElementBtn.addEventListener('click', () => {
            executeCopyElementScript();
        });
    }

    // Add event listener for Links Extractor button
    if (linksExtractorBtn) {
        linksExtractorBtn.addEventListener('click', () => {
            executeLinksExtractorScript();
        });
    }
    
    // Add event listener for Bulk Link Open button
    if (bulkLinkOpenBtn) {
        bulkLinkOpenBtn.addEventListener('click', () => {
            executeBulkLinkOpenScript();
        });
    }
    
    // Add event listener for Color Picker button
    if (colorPickerBtn) {
        colorPickerBtn.addEventListener('click', async () => {
            try {
                // MANDATORY: Restore DOM before execution following golden rules
                await restoreBeforeQuickAction('Color Picker');
                
                // Get current tab to inject scripts
                const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
                if (!tabs[0]) {
                    throw new Error('No active tab found');
                }
                
                const currentTab = tabs[0];
                
                // Inject the ColorPickerAction
                console.log('Injecting ColorPickerAction...');
                await chrome.scripting.executeScript({
                    target: { tabId: currentTab.id },
                    files: ['settings/quick-actions/colorpicker.js']
                });
                console.log('ColorPickerAction injected successfully');
                
                // Execute the ColorPickerAction
                console.log('Executing ColorPickerAction...');
                const result = await chrome.scripting.executeScript({
                    target: { tabId: currentTab.id },
                    func: function() {
                        if (typeof ColorPickerAction !== 'undefined') {
                            return ColorPickerAction.execute();
                        } else {
                            throw new Error('ColorPickerAction not found');
                        }
                    }
                });
                console.log('ColorPickerAction execution result:', result);
                
                            console.log('Color Picker executed successfully');
            showNotification('Color Picker activated! Click to pick colors from the page.', 'success');
            // Close popup after successful execution
            setTimeout(() => window.close(), 100);
            
        } catch (error) {
            console.error('Error executing Color Picker:', error);
            showNotification('Could not execute Color Picker on this page. Make sure you\'re on a regular webpage.', 'error');
        }
    });
}

async function executeResponsiveScript() {
    try {
        console.log('Starting Responsive Simulator execution...');
        
        // 🚨 CRITICAL: MUST restore DOM before ANY action execution
        await restoreBeforeQuickAction('Responsive Simulator');
        
        console.log('DOM restored, creating ResponsiveAction...');
        
        // Execute the responsive action directly (it doesn't return a Promise)
        ResponsiveAction.execute();
        
        console.log('Responsive Simulator executed successfully');
        showNotification('Responsive Device Simulator launched!', 'success');
        
        // Close popup after successful execution
        setTimeout(() => window.close(), 100);
        
    } catch (error) {
        console.error('Error executing Responsive Simulator:', error);
        showNotification('Could not launch Responsive Simulator on this page. Make sure you\'re on a regular webpage.', 'error');
    }
}
    
    // Add event listener for Responsive button
    if (responsiveBtn) {
        responsiveBtn.addEventListener('click', () => {
            executeResponsiveScript();
        });
    }

    // Add event listener for SEO Tests button
    if (seoTestsBtn) {
        seoTestsBtn.addEventListener('click', () => {
            executeSeoTestsScript();
        });
    }

    // Add event listener for Tracker Detection button
    if (trackerDetectionBtn) {
        trackerDetectionBtn.addEventListener('click', () => {
            executeTrackerDetectionScript();
        });
    }

    // Add event listener for YouTube Embed Scraper icon
    if (youtubeScraperIcon) {
        youtubeScraperIcon.addEventListener('click', () => {
            executeYouTubeEmbedScraperScript();
        });
    }
    

}

async function loadAndShowQuickActions() {
    try {
        // Load settings and custom order
        const result = await chrome.storage.local.get(['gmbExtractorSettings', 'quickActionsOrder']);
        const settings = result.gmbExtractorSettings || {};
        const customOrder = result.quickActionsOrder || [];
        
        // Define default order if no custom order exists
        const defaultOrder = [
            { id: 'htagsBtn', setting: 'htagsEnabled' },
            { id: 'headingStructureBtn', setting: 'headingStructureEnabled' },
            { id: 'showLinksBtn', setting: 'showLinksEnabled' },
            { id: 'showHiddenBtn', setting: 'showHiddenEnabled' },
            { id: 'boldFromSerpBtn', setting: 'boldFromSerpEnabled' },
            { id: 'schemaBtn', setting: 'schemaEnabled' },
            { id: 'imagesBtn', setting: 'imagesEnabled' },
            { id: 'metadataBtn', setting: 'metadataEnabled' },
            { id: 'utmBuilderBtn', setting: 'utmBuilderEnabled' },
            { id: 'pageStructureBtn', setting: 'pageStructureEnabled' },
            { id: 'linksExtractorBtn', setting: 'linksExtractorEnabled' },
            { id: 'bulkLinkOpenBtn', setting: 'bulkLinkOpenEnabled' },
            { id: 'responsiveBtn', setting: 'responsiveEnabled' },
            { id: 'seoTestsBtn', setting: 'seoTestsEnabled' },
            { id: 'trackerDetectionBtn', setting: 'trackerDetectionEnabled' },
            { id: 'copyElementBtn', setting: 'copyElementEnabled' },
            { id: 'colorPickerBtn', setting: 'colorPickerEnabled' },
            { id: 'youtubeEmbedScraperBtn', setting: 'youtubeEmbedScraperEnabled' }
        ];
        
        // Use custom order if available, otherwise use default
        let actionOrder = customOrder.length > 0 ? customOrder : defaultOrder;
        
        // FORCE-FIX: YouTube Embed Scraper must ALWAYS be last in popup display
        const youtubeIndex = actionOrder.findIndex(item => item.id === 'youtubeEmbedScraperBtn');
        if (youtubeIndex !== -1 && youtubeIndex !== actionOrder.length - 1) {
            const youtubeItem = actionOrder.splice(youtubeIndex, 1)[0];
            actionOrder.push(youtubeItem);
        }
        
        console.log('Loading quick actions with order:', actionOrder);
        
        // First, hide all buttons
        const allButtons = [
            { id: 'htagsBtn', element: htagsBtn },
            { id: 'headingStructureBtn', element: headingStructureBtn },
            { id: 'showLinksBtn', element: showLinksBtn },
            { id: 'showHiddenBtn', element: showHiddenBtn },
            { id: 'boldFromSerpBtn', element: boldFromSerpBtn },
            { id: 'schemaBtn', element: schemaBtn },
            { id: 'imagesBtn', element: imagesBtn },
            { id: 'metadataBtn', element: metadataBtn },
            { id: 'pageStructureBtn', element: pageStructureBtn },
            { id: 'copyElementBtn', element: copyElementBtn },
            { id: 'linksExtractorBtn', element: linksExtractorBtn },
            { id: 'bulkLinkOpenBtn', element: bulkLinkOpenBtn },
            { id: 'colorPickerBtn', element: colorPickerBtn },
            { id: 'responsiveBtn', element: responsiveBtn },
            { id: 'seoTestsBtn', element: seoTestsBtn },
            { id: 'trackerDetectionBtn', element: trackerDetectionBtn }
        ];
        
        allButtons.forEach(button => {
            if (button.element) {
                button.element.style.display = 'none';
            }
            if (button.container) {
                button.container.style.display = 'none';
            }
        });
        
        // Reorder and show buttons based on custom order and settings
        let hasVisibleActions = false;
        const buttonsContainer = document.querySelector('.quick-actions-buttons');
        
        if (buttonsContainer) {
            // Store all action buttons first, before removing them
            const buttonElements = {};
            actionOrder.forEach(action => {
                const element = document.getElementById(action.id);
                if (element) {
                    buttonElements[action.id] = element;
                }
            });
            
            // Remove all existing action buttons
            const allActionButtons = buttonsContainer.querySelectorAll('button');
            allActionButtons.forEach(btn => btn.remove());
            
            // Add buttons back in the correct order
            actionOrder.forEach(action => {
                const setting = action.setting || action.id.replace('Btn', 'Enabled'); // fallback
                const isEnabled = settings[setting] !== false; // Default to true if not set
                
                const element = buttonElements[action.id];
                if (element) {
                    if (isEnabled) {
                        hasVisibleActions = true;
                        element.style.display = 'inline-block';
                        console.log(`Showing button: ${action.id} (${action.name || action.id})`);
                    } else {
                        element.style.display = 'none';
                    }
                    
                    // Append element to container
                    buttonsContainer.appendChild(element);
                }
            });
            

        }
        
        // Special handling for YouTube scraper (not in main order)
        const youtubeEmbedScraperEnabled = settings.youtubeEmbedScraperEnabled !== false;
        if (youtubeEmbedScraperEnabled) {
            hasVisibleActions = true;
            if (youtubeScraperContainer) {
                youtubeScraperContainer.style.display = 'block';
            }
        }
        
        // Show/hide the entire quick actions container
        if (hasVisibleActions) {
            if (quickActionsControls) {
                quickActionsControls.style.display = 'block';
            }
        } else {
            if (quickActionsControls) {
                quickActionsControls.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('Error loading quick actions settings:', error);
        // Default to showing all quick actions if there's an error (using default order)
        if (quickActionsControls) {
            quickActionsControls.style.display = 'block';
        }
        
        const defaultOrder = [
            { id: 'htagsBtn' },
            { id: 'headingStructureBtn' },
            { id: 'showLinksBtn' },
            { id: 'showHiddenBtn' },
            { id: 'boldFromSerpBtn' },
            { id: 'schemaBtn' },
            { id: 'imagesBtn' },
            { id: 'metadataBtn' },
            { id: 'pageStructureBtn' },
            { id: 'linksExtractorBtn' },
            { id: 'bulkLinkOpenBtn' },
            { id: 'responsiveBtn' },
            { id: 'seoTestsBtn' },
            { id: 'trackerDetectionBtn' },
            { id: 'copyElementBtn' },
            { id: 'colorPickerBtn' },
            { id: 'youtubeEmbedScraperBtn' }
        ];
        
        defaultOrder.forEach(action => {
            const element = document.getElementById(action.id);
            if (element) {
                element.style.display = 'inline-block';
            }
        });
        
        if (youtubeScraperContainer) {
            youtubeScraperContainer.style.display = 'block';
        }
        
        // Force refresh shortcuts after reordering quick actions
        if (window.quickActionsShortcuts) {
            console.log('Force refreshing shortcuts after Quick Actions reorder');
            setTimeout(() => {
                window.quickActionsShortcuts.forceRefresh();
            }, 100);
        }
    }
    
    // Listen for storage changes to update order in real-time
    if (chrome && chrome.storage && chrome.storage.onChanged) {
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'local' && changes.quickActionsOrder) {
                console.log('Quick actions order changed in popup, refreshing...');
                loadAndShowQuickActions().then(() => {
                    // Additional refresh for shortcuts system after DOM reorder
                    if (window.quickActionsShortcuts) {
                        setTimeout(() => {
                            console.log('Additional shortcuts refresh after storage change');
                            window.quickActionsShortcuts.forceRefresh();
                        }, 150);
                    }
                });
            }
        });
    }
}

async function executeHtagsScript() {
    try {
        // Restore DOM before execution
        await restoreBeforeQuickAction('Htags');
        
        // Use the new HtagsAction class
        const htagsAction = new HtagsAction();
        
        htagsAction.execute()
            .then(() => {
                console.log('Htags script executed successfully');
                showNotification('Htags highlighting applied!', 'success');
                // Close the popup after successful execution
                setTimeout(() => window.close(), 100);
            })
            .catch((error) => {
                console.error('Error executing Htags script:', error);
                showNotification('Could not execute Htags on this page. Make sure you\'re on a regular webpage.', 'error');
            });
    } catch (error) {
        console.error('Error setting up Htags script execution:', error);
        showNotification('Error applying Htags highlighting', 'error');
    }
}

function resetHtagsScript() {
    try {
        // Use the new HtagsAction class
        const htagsAction = new HtagsAction();
        
        htagsAction.reset()
            .then(() => {
                console.log('Htags reset successfully');
                showNotification('Htags highlighting removed!', 'success');
            })
            .catch((error) => {
                console.error('Error resetting Htags:', error);
                showNotification('Could not reset Htags on this page.', 'error');
            });
    } catch (error) {
        console.error('Error setting up Htags reset:', error);
        showNotification('Error resetting Htags highlighting', 'error');
    }
}

async function executeHeadingStructureScript() {
    try {
        // Restore DOM before execution
        await restoreBeforeQuickAction('Heading Structure');
        
        // Use the new HeadingStructureAction class
        const headingStructureAction = new HeadingStructureAction();
        
        headingStructureAction.execute()
            .then(() => {
                console.log('Heading Structure script executed successfully');
                showNotification('Heading Structure analysis opened in new window!', 'success');
            })
            .catch((error) => {
                console.error('Error executing Heading Structure script:', error);
                showNotification('Could not execute Heading Structure on this page. Make sure you\'re on a regular webpage.', 'error');
            });
    } catch (error) {
        console.error('Error setting up Heading Structure script execution:', error);
        showNotification('Error launching Heading Structure analysis', 'error');
    }
}

async function executeShowLinksScript() {
    try {
        // Restore DOM before execution
        await restoreBeforeQuickAction('Show Links');
        
        // Use the new ShowLinksAction class
        const showLinksAction = new ShowLinksAction();
        
        showLinksAction.execute()
            .then(() => {
                console.log('Show Links script executed successfully');
                showNotification('Show Links highlighting applied!', 'success');
                // Close the popup after successful execution
                setTimeout(() => window.close(), 100);
            })
            .catch((error) => {
                console.error('Error executing Show Links script:', error);
                showNotification('Could not execute Show Links on this page. Make sure you\'re on a regular webpage.', 'error');
            });
    } catch (error) {
        console.error('Error setting up Show Links script execution:', error);
        showNotification('Error applying Show Links highlighting', 'error');
    }
}

function resetShowLinksScript() {
    try {
        // Use the new ShowLinksAction class
        const showLinksAction = new ShowLinksAction();
        
        showLinksAction.reset()
            .then(() => {
                console.log('Show Links reset successfully');
                showNotification('Show Links highlighting removed!', 'success');
            })
            .catch((error) => {
                console.error('Error resetting Show Links:', error);
                showNotification('Could not reset Show Links on this page.', 'error');
            });
    } catch (error) {
        console.error('Error setting up Show Links reset:', error);
        showNotification('Error resetting Show Links highlighting', 'error');
    }
}

function executeShowHiddenScript() {
    try {
        // Use the new ShowHiddenAction class
        const showHiddenAction = new ShowHiddenAction();
        
        showHiddenAction.execute()
            .then(() => {
                console.log('Show Hidden script executed successfully');
                showNotification('Show Hidden toggled!', 'success');
                // Close the popup after successful execution
                setTimeout(() => window.close(), 100);
            })
            .catch((error) => {
                console.error('Error executing Show Hidden script:', error);
                showNotification('Could not execute Show Hidden on this page. Make sure you\'re on a regular webpage.', 'error');
            });
    } catch (error) {
        console.error('Error setting up Show Hidden script execution:', error);
        showNotification('Error applying Show Hidden functionality', 'error');
    }
}

function executeKeywordScript() {
    try {
        // Use the new KeywordAction class
        const keywordAction = new KeywordAction();
        
        keywordAction.execute()
            .then(() => {
                console.log('Keyword script executed successfully');
                showNotification('Keyword highlighting activated!', 'success');
                // Close the popup after successful execution
                setTimeout(() => window.close(), 100);
            })
            .catch((error) => {
                console.error('Error executing Keyword script:', error);
                showNotification('Could not execute Keyword highlighting on this page. Make sure you\'re on a regular webpage.', 'error');
            });
    } catch (error) {
        console.error('Error setting up Keyword script execution:', error);
        showNotification('Error applying Keyword highlighting', 'error');
    }
}

function executeBoldFromSerpScript() {
    try {
        // Use the new BoldFromSerpAction class
        if (typeof BoldFromSerpAction !== 'undefined') {
            BoldFromSerpAction.execute();
            showNotification('Bold From SERP executed!', 'success');
            // Close the popup after successful execution
            setTimeout(() => window.close(), 100);
        } else {
            throw new Error('BoldFromSerpAction class not found');
        }
    } catch (error) {
        console.error('Error executing Bold From SERP script:', error);
        showNotification('Error extracting bold text from SERP. Make sure you\'re on a page with <em> tags.', 'error');
    }
}

async function executeCopyElementScript() {
    try {
        // 🚨 CRITICAL: MUST restore DOM before ANY action execution
        await restoreBeforeQuickAction('Copy Element');
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (!tabs[0]) throw new Error('No active tab found');
        const tabId = tabs[0].id;

        // Inject the action script
        await chrome.scripting.executeScript({
            target: { tabId },
            files: ['settings/quick-actions/copyelement.js']
        });

        // Now call the static execute method in the page context
        await chrome.scripting.executeScript({
            target: { tabId },
            func: () => {
                if (typeof CopyElementAction !== 'undefined') {
                    CopyElementAction.execute();
                } else {
                    throw new Error('CopyElementAction not found');
                }
            }
        });

        showNotification('Copy Element activated! Hover and click elements to copy.', 'success');
        setTimeout(() => window.close(), 100);
    } catch (error) {
        console.error('Error executing Copy Element:', error);
        showNotification('Could not execute Copy Element on this page.', 'error');
    }
}

async function executeLinksExtractorScript() {
    try {
        // 🚨 CRITICAL: MUST restore DOM before ANY action execution
        await restoreBeforeQuickAction('Links Extractor');
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (!tabs[0]) throw new Error('No active tab found');
        const tabId = tabs[0].id;

        // Inject the action script
        await chrome.scripting.executeScript({
            target: { tabId },
            files: ['settings/quick-actions/linksextractor.js']
        });

        // Now call the static execute method in the page context
        await chrome.scripting.executeScript({
            target: { tabId },
            func: () => {
                if (typeof LinksExtractorAction !== 'undefined') {
                    LinksExtractorAction.execute();
                } else {
                    throw new Error('LinksExtractorAction not found');
                }
            }
        });

        showNotification('Links Extractor activated! Hover and click elements to extract links.', 'success');
        setTimeout(() => window.close(), 100);
    } catch (error) {
        console.error('Error executing Links Extractor:', error);
        showNotification('Could not execute Links Extractor on this page.', 'error');
    }
}

async function executeBulkLinkOpenScript() {
    try {
        // 🚨 CRITICAL: MUST restore DOM before ANY action execution
        await restoreBeforeQuickAction('Bulk Link Open');
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (!tabs[0]) throw new Error('No active tab found');
        const tabId = tabs[0].id;

        // Inject the action script
        await chrome.scripting.executeScript({
            target: { tabId },
            files: ['settings/quick-actions/bulklinkopen.js']
        });

        // Now call the static execute method in the page context
        await chrome.scripting.executeScript({
            target: { tabId },
            func: () => {
                if (typeof BulkLinkOpenAction !== 'undefined') {
                    BulkLinkOpenAction.execute();
                } else {
                    throw new Error('BulkLinkOpenAction not found');
                }
            }
        });

        showNotification('Bulk Link Open launched!', 'success');
        setTimeout(() => window.close(), 100);
    } catch (error) {
        console.error('Error executing Bulk Link Open:', error);
        showNotification('Could not launch Bulk Link Open on this page.', 'error');
    }
}

function executeSchemaScript() {
    try {
        // Use the new SchemaAction class
        if (typeof SchemaAction !== 'undefined') {
            SchemaAction.execute();
            showNotification('Schema Extractor activated! Looking for structured data on this page.', 'success');
            // Close the popup after successful execution
            setTimeout(() => window.close(), 100);
        } else {
            throw new Error('SchemaAction class not found');
        }
    } catch (error) {
        console.error('Error executing Schema script:', error);
        showNotification('Error launching Schema Extractor. Please try again.', 'error');
    }
}

function executeImagesScript() {
    try {
        // Use the new ImagesAction class
        if (typeof ImagesAction !== 'undefined') {
            ImagesAction.execute();
            showNotification('Images SEO Audit activated! Analyzing all images on this page.', 'success');
            // Close the popup after launching the script
            window.close();
        } else {
            throw new Error('ImagesAction class not found');
        }
    } catch (error) {
        console.error('Error executing Images script:', error);
        showNotification('Error launching Images SEO Audit. Please try again.', 'error');
    }
}

function executeMetadataScript() {
    try {
        // Use the new MetadataAction class
        if (typeof MetadataAction !== 'undefined') {
            MetadataAction.execute();
            showNotification('Metadata Analyzer activated! Analyzing page metadata and social tags.', 'success');
            // Close the popup after launching the script
            window.close();
        } else {
            throw new Error('MetadataAction class not found');
        }
    } catch (error) {
        console.error('Error executing Metadata script:', error);
        showNotification('Error launching Metadata Analyzer. Please try again.', 'error');
    }
}

function executeUTMBuilderScript() {
    try {
        // Use the new UTMBuilderAction class - no DOM restoration needed for overlay panels
        if (typeof UTMBuilderAction !== 'undefined') {
            UTMBuilderAction.execute();
            showNotification('UTM Builder activated! Build trackable URLs with templates.', 'success');
            // Close the popup after launching the script
            window.close();
        } else {
            throw new Error('UTMBuilderAction class not found');
        }
    } catch (error) {
        console.error('Error executing UTM Builder script:', error);
        showNotification('Error launching UTM Builder. Please try again.', 'error');
    }
}

async function executePageStructureScript() {
    try {
        // Restore DOM before execution
        await restoreBeforeQuickAction('Page Structure');
        
        // Use the new PageStructureAction class
        if (typeof PageStructureAction !== 'undefined') {
            const pageStructureAction = new PageStructureAction();
            pageStructureAction.execute()
                .then(() => {
                    console.log('Page Structure script executed successfully');
                    showNotification('Page Structure X-ray toggled!', 'success');
                    // Close the popup after successful execution
                    setTimeout(() => window.close(), 100);
                })
                .catch((error) => {
                    console.error('Error executing Page Structure script:', error);
                    showNotification('Could not execute Page Structure on this page. Make sure you\'re on a regular webpage.', 'error');
                });
        } else {
            throw new Error('PageStructureAction class not found');
        }
    } catch (error) {
        console.error('Error executing Page Structure script:', error);
        showNotification('Error launching Page Structure X-ray. Please try again.', 'error');
    }
}

function executeYouTubeEmbedScraperScript() {
    try {
        // Use the new YouTubeEmbedScraperAction class
        if (typeof YouTubeEmbedScraperAction !== 'undefined') {
            YouTubeEmbedScraperAction.execute();
            showNotification('YouTube Embed Scraper activated! Navigate to a YouTube channel videos page to begin.', 'success');
            
            // Close the popup window after launching the scraper
            window.close();
        } else {
            throw new Error('YouTubeEmbedScraperAction class not found');
        }
    } catch (error) {
        console.error('Error executing YouTube Embed Scraper script:', error);
        showNotification('Error launching YouTube Embed Scraper. Please try again.', 'error');
    }
}

async function executeSemanticElementsScript() {
    try {
        // MANDATORY: Restore DOM before execution
        await restoreBeforeQuickAction('Semantic Elements');
        
        // Your action logic here
        const semanticElementsAction = new SemanticElementsAction();
        
        semanticElementsAction.execute()
            .then(() => {
                console.log('Semantic Elements executed successfully');
                showNotification('Semantic elements highlighted!', 'success');
                // Close popup after successful execution
                setTimeout(() => window.close(), 100);
            })
            .catch((error) => {
                console.error('Error executing Semantic Elements:', error);
                showNotification('Could not execute Semantic Elements on this page.', 'error');
            });
    } catch (error) {
        console.error('Error setting up Semantic Elements execution:', error);
        showNotification('Error launching Semantic Elements', 'error');
    }
}

function executeSeoTestsScript() {
    try {
        // Use the new SeoTestsAction class - no DOM restoration needed for overlay panels
        if (typeof SeoTestsAction !== 'undefined') {
            SeoTestsAction.execute();
            showNotification('SEO Tests panel opened! Run tests for the current URL.', 'success');
            // Close the popup after launching the script
            window.close();
        } else {
            throw new Error('SeoTestsAction class not found');
        }
    } catch (error) {
        console.error('Error executing SEO Tests script:', error);
        showNotification('Error launching SEO Tests. Please try again.', 'error');
    }
}

function executeTrackerDetectionScript() {
    try {
        // Use the new TrackerDetectionAction class - no DOM restoration needed for overlay panels
        if (typeof TrackerDetectionAction !== 'undefined') {
            TrackerDetectionAction.execute();
            showNotification('Tracker Detection activated! Analyzing page for tracking scripts.', 'success');
            // Close the popup after launching the script
            window.close();
        } else {
            throw new Error('TrackerDetectionAction class not found');
        }
    } catch (error) {
        console.error('Error executing Tracker Detection script:', error);
        showNotification('Error launching Tracker Detection. Please try again.', 'error');
    }
}

async function resetAllQuickActions() {
    try {
        console.log('[Reset] Starting comprehensive Quick Actions reset...');
        
        // Get current tab for DOM restoration
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (!tabs[0]) {
            console.error('[Reset] No active tab found');
            showNotification('Error: No active tab found', 'error');
            return;
        }
        
        const currentTab = tabs[0];
        
        // First, inject and use DOM snapshot restoration
        try {
            console.log('[Reset] Attempting DOM snapshot restoration...');
            
            // Inject DOM Snapshot Utility
            await chrome.scripting.executeScript({
                target: { tabId: currentTab.id },
                files: ['settings/dom-snapshot-utility.js']
            });
            
            // Attempt DOM restoration
            const restorationResult = await chrome.scripting.executeScript({
                target: { tabId: currentTab.id },
                func: function() {
                    console.log('[Content] Starting DOM restoration from popup reset...');
                    
                    if (window.domSnapshotUtility && window.domSnapshotUtility.isSnapshotReady()) {
                        console.log('[Content] DOM snapshot available, restoring...');
                        return window.domSnapshotUtility.restoreToInitialState();
                    } else {
                        console.log('[Content] DOM snapshot not ready, skipping restoration');
                        return Promise.resolve(false);
                    }
                }
            });
            
            if (restorationResult && restorationResult[0] && restorationResult[0].result) {
                console.log('[Reset] DOM snapshot restoration successful');
                showNotification('All quick actions reset! (DOM restored)', 'success');
                return;
            } else {
                console.log('[Reset] DOM snapshot restoration failed or not available, falling back to manual reset...');
            }
            
        } catch (snapshotError) {
            console.warn('[Reset] DOM snapshot restoration error, falling back to manual reset:', snapshotError);
        }
        
        // Fallback to manual reset of individual actions
        console.log('[Reset] Using manual reset fallback...');
        
        // Create instances of all quick action classes
        const htagsAction = new HtagsAction();
        const showLinksAction = new ShowLinksAction();
        
        // Create an array of reset promises
        const resetPromises = [];
        
        // Add Htags reset if it exists
        if (typeof HtagsAction !== 'undefined') {
            resetPromises.push(
                htagsAction.reset().catch((error) => {
                    console.warn('Htags reset failed:', error);
                    return { action: 'Htags', success: false, error };
                })
            );
        }
        
        // Add Heading Structure reset if it exists
        if (typeof HeadingStructureAction !== 'undefined') {
            const headingStructureAction = new HeadingStructureAction();
            resetPromises.push(
                headingStructureAction.reset().catch((error) => {
                    console.warn('Heading Structure reset failed:', error);
                    return { action: 'Heading Structure', success: false, error };
                })
            );
        }
        
        // Add Show Links reset if it exists
        if (typeof ShowLinksAction !== 'undefined') {
            resetPromises.push(
                showLinksAction.reset().catch((error) => {
                    console.warn('Show Links reset failed:', error);
                    return { action: 'Show Links', success: false, error };
                })
            );
        }
        
        // Add Show Hidden reset if it exists
        if (typeof ShowHiddenAction !== 'undefined') {
            const showHiddenAction = new ShowHiddenAction();
            resetPromises.push(
                showHiddenAction.reset().catch((error) => {
                    console.warn('Show Hidden reset failed:', error);
                    return { action: 'Show Hidden', success: false, error };
                })
            );
        }
        
        // Add Keyword reset if it exists
        if (typeof KeywordAction !== 'undefined') {
            const keywordAction = new KeywordAction();
            resetPromises.push(
                keywordAction.reset().catch((error) => {
                    console.warn('Keyword reset failed:', error);
                    return { action: 'Keyword', success: false, error };
                })
            );
        }
        
        // Add Bold From SERP reset if it exists
        if (typeof BoldFromSerpAction !== 'undefined') {
            resetPromises.push(
                BoldFromSerpAction.reset().catch((error) => {
                    console.warn('Bold From SERP reset failed:', error);
                    return { action: 'Bold From SERP', success: false, error };
                })
            );
        }
        
        // Add Schema reset if it exists
        if (typeof SchemaAction !== 'undefined') {
            resetPromises.push(
                SchemaAction.reset().catch((error) => {
                    console.warn('Schema reset failed:', error);
                    return { action: 'Schema', success: false, error };
                })
            );
        }
        
        // Add Images reset if it exists
        if (typeof ImagesAction !== 'undefined') {
            resetPromises.push(
                ImagesAction.reset().catch((error) => {
                    console.warn('Images reset failed:', error);
                    return { action: 'Images', success: false, error };
                })
            );
        }
        
        // Add Metadata reset if it exists
        if (typeof MetadataAction !== 'undefined') {
            resetPromises.push(
                MetadataAction.reset().catch((error) => {
                    console.warn('Metadata reset failed:', error);
                    return { action: 'Metadata', success: false, error };
                })
            );
        }

        // Add UTM Builder reset if it exists
        if (typeof UTMBuilderAction !== 'undefined') {
            resetPromises.push(
                UTMBuilderAction.reset().catch((error) => {
                    console.warn('UTM Builder reset failed:', error);
                    return { action: 'UTM Builder', success: false, error };
                })
            );
        }
        
        // Add Page Structure reset if it exists
        if (typeof PageStructureAction !== 'undefined') {
            const pageStructureAction = new PageStructureAction();
            resetPromises.push(
                pageStructureAction.reset().catch((error) => {
                    console.warn('Page Structure reset failed:', error);
                    return { action: 'Page Structure', success: false, error };
                })
            );
        }
        
        // Add YouTube Embed Scraper reset if it exists
        if (typeof YouTubeEmbedScraperAction !== 'undefined') {
            resetPromises.push(
                YouTubeEmbedScraperAction.reset().catch((error) => {
                    console.warn('YouTube Embed Scraper reset failed:', error);
                    return { action: 'YouTube Embed Scraper', success: false, error };
                })
            );
        }
        
        // Add Semantic Elements reset if it exists
        if (typeof SemanticElementsAction !== 'undefined') {
            resetPromises.push(
                SemanticElementsAction.reset().catch((error) => {
                    console.warn('Semantic Elements reset failed:', error);
                    return { action: 'Semantic Elements', success: false, error };
                })
            );
        }
        
        // Add Word Counter reset if it exists
        if (typeof WordCounterAction !== 'undefined') {
            resetPromises.push(
                WordCounterAction.reset().catch((error) => {
                    console.warn('Word Counter reset failed:', error);
                    return { action: 'Word Counter', success: false, error };
                })
            );
        }
        
        // Add Link Checker reset if it exists
        if (typeof LinkCheckerAction !== 'undefined') {
            resetPromises.push(
                LinkCheckerAction.reset().catch((error) => {
                    console.warn('Link Checker reset failed:', error);
                    return { action: 'Link Checker', success: false, error };
                })
            );
        }
        
        // Add Copy Element reset if it exists
        if (typeof CopyElementAction !== 'undefined') {
            resetPromises.push(
                CopyElementAction.reset().catch((error) => {
                    console.warn('Copy Element reset failed:', error);
                    return { action: 'Copy Element', success: false, error };
                })
            );
        }

        // Add Bulk Link Open reset if it exists
        if (typeof BulkLinkOpenAction !== 'undefined') {
            resetPromises.push(
                BulkLinkOpenAction.reset().catch((error) => {
                    console.warn('Bulk Link Open reset failed:', error);
                    return { action: 'Bulk Link Open', success: false, error };
                })
            );
        }

        // Add Responsive Simulator reset if it exists
        if (typeof ResponsiveAction !== 'undefined') {
            resetPromises.push(
                ResponsiveAction.reset().catch((error) => {
                    console.warn('Responsive Simulator reset failed:', error);
                    return { action: 'Responsive Simulator', success: false, error };
                })
            );
        }
        
        // Execute all resets
        const results = await Promise.all(resetPromises);
        const failures = results.filter(result => result && result.success === false);
        
        if (failures.length === 0) {
            console.log('[Reset] All quick actions reset successfully');
            showNotification('All quick actions reset!', 'success');
        } else {
            console.warn('[Reset] Some quick actions failed to reset:', failures);
            showNotification('Quick actions reset (some may have failed)', 'info');
        }
            
    } catch (error) {
        console.error('[Reset] Error setting up quick actions reset:', error);
        showNotification('Error resetting quick actions', 'error');
    }
}

// Helper function to load DOM restore utility
async function loadDOMRestoreUtility() {
    // Check if DOMSnapshotUtility is already defined in the page context
    const isAlreadyDefined = await chrome.scripting.executeScript({
        target: {tabId: (await chrome.tabs.query({active: true, currentWindow: true}))[0].id},
        func: function() {
            return typeof window.DOMSnapshotUtility !== 'undefined';
        }
    });
    if (isAlreadyDefined && isAlreadyDefined[0] && isAlreadyDefined[0].result) {
        console.log('[Popup] DOMSnapshotUtility already present, skipping injection');
        return;
    }
    if (!window.quickActionsDOMRestore) {
        try {
            // Load the DOM restore utility script
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('settings/quick-actions-dom-restore.js');
            document.head.appendChild(script);
            // Wait for it to load
            await new Promise((resolve, reject) => {
                script.onload = resolve;
                script.onerror = reject;
            });
            console.log('[Popup] DOM restore utility loaded successfully');
        } catch (error) {
            console.error('[Popup] Failed to load DOM restore utility:', error);
        }
    }
}

// Helper function to restore DOM before Quick Action execution
async function restoreBeforeQuickAction(actionName) {
    try {
        await loadDOMRestoreUtility();
        
        if (window.quickActionsDOMRestore) {
            console.log(`[Popup] Restoring DOM before ${actionName}...`);
            const success = await window.quickActionsDOMRestore.restoreBeforeAction(actionName);
            if (success) {
                console.log(`[Popup] DOM restoration successful for ${actionName}`);
            } else {
                console.warn(`[Popup] DOM restoration failed for ${actionName}, continuing anyway...`);
            }
            return success;
        } else {
            console.warn(`[Popup] DOM restore utility not available for ${actionName}`);
            return false;
        }
    } catch (error) {
        console.error(`[Popup] Error during DOM restoration for ${actionName}:`, error);
        return false;
    }
}

function showNotification(message, type = 'info') {
    // Create notification with black background and colored dots matching clicktocopy.js style
    const notification = document.createElement('div');
    
    // Add colored dot and border based on notification type
    let dotColor = '#7C3AED'; // Default purple for info
    let borderColor = 'rgba(124, 58, 237, 0.3)'; // Default purple border
    
    if (type === 'success') {
        dotColor = '#22c55e'; // Green
        borderColor = 'rgba(34, 197, 94, 0.5)'; // Green border
    } else if (type === 'error') {
        dotColor = '#ef4444'; // Red
        borderColor = 'rgba(239, 68, 68, 0.5)'; // Red border
    } else if (type === 'disable') {
        dotColor = '#ef4444'; // Red dot for disable
        borderColor = 'rgba(239, 68, 68, 0.5)'; // Red border for disable
    }
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.9);
        color: #fff;
        padding: 8px 12px;
        border-radius: 6px;
        z-index: 10000000;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        font-size: 13px;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(5px);
        border: 1px solid ${borderColor};
        opacity: 0;
        transform: translateX(20px);
        transition: all 0.3s ease-out;
        max-width: 300px;
        pointer-events: none;
        white-space: nowrap;
    `;
    
    notification.innerHTML = `<span style="color: ${dotColor}; font-size: 16px;">●</span> ${message}`;
    
    document.body.appendChild(notification);
    
    // Animate in
    requestAnimationFrame(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    });
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(20px)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Global variables for UTM whitelist functionality
let currentDomainCache = null;
let utmSettingsCache = null;

// UTM Whitelist functionality
async function checkUTMCleanerSettings() {
  try {
    // Wait a bit to ensure UTM cleaner has loaded
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const result = await chrome.storage.local.get(['gmbExtractorSettings']);
    const settings = result.gmbExtractorSettings || {};
    utmSettingsCache = settings; // Cache the settings
    
    console.log('UTM Whitelist Button: Raw settings loaded:', settings);
    
    // Show whitelist button only if UTM cleaner is enabled (default is true)
    const isUTMCleanerEnabled = settings.utmTrackingCleanerEnabled !== false;
    
    if (utmWhitelistBtn) {
      if (isUTMCleanerEnabled) {
        utmWhitelistBtn.style.display = 'inline-block';
        
        // Check if UTM cleaner is temporarily disabled
        const isTemporarilyDisabled = settings.utmCleanerTemporarilyDisabled || false;
        
        if (isTemporarilyDisabled) {
          // Show red button if temporarily disabled
          updateWhitelistButtonForTemporaryDisable(true);
        } else {
          // Check if current domain is already whitelisted
          const currentDomain = await getCurrentDomain();
          let whitelistDomains = settings.utmCleanerWhitelistDomains || [];
          
          console.log('UTM Whitelist Button: Original whitelist domains:', whitelistDomains);
          
          // Clean up any duplicates and corrupted entries in the whitelist
          const cleanedDomains = cleanupWhitelistDomains(whitelistDomains);
          if (JSON.stringify(cleanedDomains) !== JSON.stringify(whitelistDomains)) {
            // Update settings if cleanup was needed
            settings.utmCleanerWhitelistDomains = cleanedDomains;
            await chrome.storage.local.set({ gmbExtractorSettings: settings });
            whitelistDomains = cleanedDomains;
            console.log('UTM Whitelist Button: Cleaned up whitelist domains on load');
            console.log('UTM Whitelist Button: Cleaned domains:', cleanedDomains);
          }
          
          const isWhitelisted = isDomainWhitelisted(currentDomain, whitelistDomains);
          
          console.log('UTM Whitelist Button: Domain check', {
            currentDomain,
            whitelistDomains,
            isWhitelisted
          });
          
          // Update button appearance based on whitelist status
          updateWhitelistButtonState(isWhitelisted);
        }
      } else {
        utmWhitelistBtn.style.display = 'none';
      }
    }
  } catch (error) {
    console.error('Error checking UTM cleaner settings:', error);
    if (utmWhitelistBtn) {
      utmWhitelistBtn.style.display = 'none';
    }
  }
}

async function getCurrentDomain() {
  // Return cached domain if available
  if (currentDomainCache) {
    return currentDomainCache;
  }
  
  try {
    const tabs = await chrome.tabs.query({active: true, currentWindow: true});
    if (tabs[0] && tabs[0].url) {
      const url = new URL(tabs[0].url);
      // Use the same normalization function for consistency
      currentDomainCache = normalizeDomain(url.hostname);
      console.log('UTM Whitelist Button: Current domain detected:', currentDomainCache);
      return currentDomainCache;
    }
  } catch (error) {
    console.error('Error getting current domain:', error);
  }
  return '';
}

// Normalize domain for consistent comparison
function normalizeDomain(domain) {
  if (!domain) return '';
  return domain.toLowerCase().replace(/^www\./, '').trim();
}

// Remove duplicate domains from whitelist array
function removeDuplicateDomains(domains) {
  if (!Array.isArray(domains)) return [];
  
  const normalizedDomains = new Set();
  const uniqueDomains = [];
  
  domains.forEach(domain => {
    const normalized = normalizeDomain(domain);
    if (normalized && !normalizedDomains.has(normalized)) {
      normalizedDomains.add(normalized);
      uniqueDomains.push(normalized);
    }
  });
  
  console.log('UTM Whitelist Button: Removed duplicates', {
    original: domains,
    cleaned: uniqueDomains
  });
  
  return uniqueDomains;
}

// Clean up whitelist domains (remove duplicates, corrupted entries, and normalize)
function cleanupWhitelistDomains(domains) {
  if (!Array.isArray(domains)) return [];
  
  const normalizedDomains = new Set();
  const cleanDomains = [];
  
  domains.forEach(domain => {
    if (!domain || typeof domain !== 'string') {
      console.log('UTM Whitelist Button: Removing invalid domain entry:', domain);
      return;
    }
    
    // Check for obvious typos or corrupted entries
    const originalDomain = domain.trim();
    let cleanedDomain = normalizeDomain(originalDomain);
    
    // Fix common typos (e.g., 'Flexlxip.com' -> 'flexclip.com')
    if (cleanedDomain.includes('flexlxip')) {
      cleanedDomain = cleanedDomain.replace('flexlxip', 'flexclip');
      console.log('UTM Whitelist Button: Fixed typo in domain:', originalDomain, '->', cleanedDomain);
    }
    
    // Validate domain format (basic check)
    if (cleanedDomain && cleanedDomain.includes('.') && !cleanedDomain.includes(' ')) {
      if (!normalizedDomains.has(cleanedDomain)) {
        normalizedDomains.add(cleanedDomain);
        cleanDomains.push(cleanedDomain);
      } else {
        console.log('UTM Whitelist Button: Removing duplicate domain:', cleanedDomain);
      }
    } else {
      console.log('UTM Whitelist Button: Removing invalid domain format:', originalDomain);
    }
  });
  
  console.log('UTM Whitelist Button: Cleaned up whitelist', {
    original: domains,
    cleaned: cleanDomains
  });
  
  return cleanDomains;
}

function isDomainWhitelisted(domain, whitelistDomains) {
  if (!domain || !whitelistDomains || whitelistDomains.length === 0) return false;
  
  // Normalize the current domain
  const normalizedDomain = normalizeDomain(domain);
  
  const isWhitelisted = whitelistDomains.some(whitelistDomain => {
    const normalizedWhitelist = normalizeDomain(whitelistDomain);
    // Exact match: flexclip.com matches flexclip.com
    // Subdomain match: flexclip.com matches www.flexclip.com, app.flexclip.com, etc.
    const isMatch = normalizedDomain === normalizedWhitelist || normalizedDomain.endsWith('.' + normalizedWhitelist);
    
    console.log('UTM Whitelist Button: Domain comparison', {
      normalizedDomain,
      normalizedWhitelist,
      isMatch
    });
    
    return isMatch;
  });
  
  console.log('UTM Whitelist Button: Final whitelist check result:', {
    domain: normalizedDomain,
    whitelistDomains,
    isWhitelisted
  });
  
  return isWhitelisted;
}

function updateWhitelistButtonState(isWhitelisted) {
  if (!utmWhitelistBtn) return;
  
  if (isWhitelisted) {
    utmWhitelistBtn.classList.add('added');
    utmWhitelistBtn.title = 'Current site is whitelisted (click to remove)';
  } else {
    utmWhitelistBtn.classList.remove('added');
    utmWhitelistBtn.title = 'Add current site to UTM cleaner whitelist';
  }
}

// Update button appearance for temporary disable state
function updateWhitelistButtonForTemporaryDisable(isDisabled) {
  if (!utmWhitelistBtn) return;
  
  if (isDisabled) {
    // Make button red when temporarily disabled and disable normal click
    utmWhitelistBtn.classList.add('temporarily-disabled');
    utmWhitelistBtn.style.setProperty('background', 'rgba(239, 68, 68, 0.15)', 'important');
    utmWhitelistBtn.style.setProperty('border-color', 'rgba(239, 68, 68, 0.4)', 'important');
    utmWhitelistBtn.style.setProperty('color', '#ef4444', 'important');
    utmWhitelistBtn.style.setProperty('opacity', '0.7', 'important');
    utmWhitelistBtn.style.setProperty('cursor', 'not-allowed', 'important');
    utmWhitelistBtn.title = 'UTM Cleaner temporarily disabled (Shift+click to re-enable)';
    
    // Make button appear greyed out and unclickable for normal clicks
    utmWhitelistBtn.disabled = false; // Keep enabled for shift+click to work
  } else {
    // Reset to normal appearance
    utmWhitelistBtn.classList.remove('temporarily-disabled');
    utmWhitelistBtn.style.removeProperty('background');
    utmWhitelistBtn.style.removeProperty('border-color');
    utmWhitelistBtn.style.removeProperty('color');
    utmWhitelistBtn.style.removeProperty('opacity');
    utmWhitelistBtn.style.removeProperty('cursor');
    utmWhitelistBtn.disabled = false;
    utmWhitelistBtn.title = 'Add current site to UTM cleaner whitelist';
  }
}

// Handle shift+click for temporary disable functionality
async function handleShiftClickTemporaryDisable(event) {
  try {
    // Prevent default click behavior
    event.preventDefault();
    event.stopPropagation();
    
    console.log('UTM Popup: Shift+click detected, toggling temporary disable...');
    
    // Get current settings to check current state
    const result = await chrome.storage.local.get(['gmbExtractorSettings']);
    const settings = result.gmbExtractorSettings || {};
    const currentlyDisabled = settings.utmCleanerTemporarilyDisabled || false;
    
    console.log('UTM Popup: Current temporary disable state:', currentlyDisabled);
    
    // Toggle the state
    const newState = !currentlyDisabled;
    settings.utmCleanerTemporarilyDisabled = newState;
    
    // Save to storage
    await chrome.storage.local.set({ gmbExtractorSettings: settings });
    
    console.log('UTM Popup: New temporary disable state saved:', newState);
    
    // Update button appearance immediately
    updateWhitelistButtonForTemporaryDisable(newState);
    
    // Show notification
    if (newState) {
      showNotification('UTM Cleaner now disabled<br>(Shift+click again to re-enable)', 'info');
    } else {
      showNotification('UTM Cleaner re-enabled', 'success');
    }
    
    // Try to notify content script if available (but don't fail if not)
    try {
      const tabs = await chrome.tabs.query({active: true, currentWindow: true});
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, {
          type: 'UTM_TOGGLE_TEMPORARY_DISABLE'
        }, (response) => {
          // Ignore response - we've already updated the state
          if (chrome.runtime.lastError) {
            console.log('UTM Popup: Content script not available, but state saved to storage');
          }
        });
      }
    } catch (error) {
      console.log('UTM Popup: Could not notify content script, but state saved:', error);
    }
    
  } catch (error) {
    console.error('UTM Popup: Error handling shift+click temporary disable:', error);
    showNotification('Error toggling UTM cleaner', 'error');
  }
}

async function toggleDomainWhitelist() {
  try {
    const currentDomain = await getCurrentDomain();
    if (!currentDomain) {
      showNotification('Unable to get current domain', 'error');
      return;
    }
    
    console.log('UTM Whitelist Button: Toggling whitelist for domain:', currentDomain);
    
    const result = await chrome.storage.local.get(['gmbExtractorSettings']);
    const settings = result.gmbExtractorSettings || {};
    let whitelistDomains = settings.utmCleanerWhitelistDomains || [];
    
    // Clean up any existing duplicates and corrupted entries first
    whitelistDomains = cleanupWhitelistDomains(whitelistDomains);
    
    console.log('UTM Whitelist Button: Current whitelist before toggle:', whitelistDomains);
    
    const normalizedCurrentDomain = normalizeDomain(currentDomain);
    const isCurrentlyWhitelisted = isDomainWhitelisted(currentDomain, whitelistDomains);
    
    if (isCurrentlyWhitelisted) {
      // Remove domain from whitelist - use normalized comparison
      whitelistDomains = whitelistDomains.filter(domain => {
        const normalizedStoredDomain = normalizeDomain(domain);
        return normalizedStoredDomain !== normalizedCurrentDomain;
      });
      showNotification(`Removed ${normalizedCurrentDomain} from UTM cleaner whitelist`, 'success');
    } else {
      // Add domain to whitelist - ensure it's normalized and not a duplicate
      if (!whitelistDomains.some(domain => normalizeDomain(domain) === normalizedCurrentDomain)) {
        whitelistDomains.push(normalizedCurrentDomain);
        showNotification(`Added ${normalizedCurrentDomain} to UTM cleaner whitelist`, 'success');
      } else {
        console.log('UTM Whitelist Button: Domain already exists (duplicate detected)');
        showNotification(`${normalizedCurrentDomain} is already in the whitelist`, 'info');
        return;
      }
    }
    
    // Final cleanup to ensure no duplicates or corrupted entries
    whitelistDomains = cleanupWhitelistDomains(whitelistDomains);
    
    console.log('UTM Whitelist Button: Updated whitelist after toggle:', whitelistDomains);
    
    // Save updated settings
    settings.utmCleanerWhitelistDomains = whitelistDomains;
    await chrome.storage.local.set({ gmbExtractorSettings: settings });
    
    // Update cached settings
    utmSettingsCache = settings;
    
    // Update button state immediately
    updateWhitelistButtonState(!isCurrentlyWhitelisted);
    
    // Notify content scripts about the change
    try {
      const tabs = await chrome.tabs.query({active: true, currentWindow: true});
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, {
          type: 'UTM_SETTINGS_UPDATED',
          settings: settings
        });
      }
    } catch (error) {
      // Ignore errors if content script is not available
      console.log('Could not notify content script about whitelist change:', error);
    }
    
  } catch (error) {
    console.error('Error toggling domain whitelist:', error);
    showNotification('Error updating whitelist', 'error');
  }
}

// Manual cleanup function for debugging (can be called from console)
window.cleanupUTMWhitelist = async function() {
  try {
    const result = await chrome.storage.local.get(['gmbExtractorSettings']);
    const settings = result.gmbExtractorSettings || {};
    let whitelistDomains = settings.utmCleanerWhitelistDomains || [];
    
    console.log('Manual cleanup: Original whitelist:', whitelistDomains);
    
    const cleanedDomains = cleanupWhitelistDomains(whitelistDomains);
    settings.utmCleanerWhitelistDomains = cleanedDomains;
    await chrome.storage.local.set({ gmbExtractorSettings: settings });
    
    console.log('Manual cleanup: Cleaned whitelist:', cleanedDomains);
    
    // Refresh the button state
    checkUTMCleanerSettings();
    checkPomodoroSettings();
    
    return { original: whitelistDomains, cleaned: cleanedDomains };
  } catch (error) {
    console.error('Manual cleanup failed:', error);
    return { error: error.message };
  }
};

// UTM Whitelist button event listener
if (utmWhitelistBtn) {
  utmWhitelistBtn.addEventListener('click', (event) => {
    // Check if shift key is held down
    if (event.shiftKey) {
      // Handle shift+click for temporary disable
      handleShiftClickTemporaryDisable(event);
    } else {
      // Check if UTM cleaner is temporarily disabled
      if (utmWhitelistBtn.classList.contains('temporarily-disabled')) {
        // Prevent normal click when temporarily disabled
        event.preventDefault();
        event.stopPropagation();
        showNotification('UTM Cleaner is temporarily disabled. Use Shift+click to re-enable.', 'info');
        return;
      }
      
      // Normal click - toggle whitelist
      toggleDomainWhitelist();
    }
  });
}

// Check Pomodoro settings and show/hide button
async function checkPomodoroSettings() {
  try {
    const result = await chrome.storage.local.get(['gmbExtractorSettings']);
    const settings = result.gmbExtractorSettings || {};
    
    // Show Pomodoro button only if Pomodoro is enabled (default is true)
    const isPomodoroEnabled = settings.pomodoroEnabled !== false;
    
    if (pomodoroToggleBtn) {
      if (isPomodoroEnabled) {
        pomodoroToggleBtn.style.display = 'inline-block';
        
        // Check current timer state from storage
        const timerResult = await chrome.storage.local.get(['pomodoroTimerState']);
        if (timerResult.pomodoroTimerState) {
          const timerState = timerResult.pomodoroTimerState;
          if (timerState.currentState === 'idle') {
            updatePomodoroButtonState('idle', false);
          } else {
            updatePomodoroButtonState(timerState.currentState, timerState.isPaused || false);
          }
        }
      } else {
        pomodoroToggleBtn.style.display = 'none';
      }
    }
  } catch (error) {
    console.error('Error checking Pomodoro settings:', error);
    if (pomodoroToggleBtn) {
      pomodoroToggleBtn.style.display = 'none';
    }
  }
}

// Check Alert settings and update button visibility
async function checkAlertSettings() {
  try {
    const result = await chrome.storage.local.get(['gmbExtractorSettings', 'activeAlerts']);
    const settings = result.gmbExtractorSettings || {};
    const activeAlerts = result.activeAlerts || [];
    
    // Show Alert button only if alerts are enabled (default is true)
    const isAlertsEnabled = settings.alertsEnabled !== false;
    
    if (alertToggleBtn) {
      if (isAlertsEnabled) {
        alertToggleBtn.style.display = 'inline-block';
        
        // Count active alerts
        const activeCount = activeAlerts.filter(alert => alert.enabled).length;
        
        if (activeCount > 0) {
          // Show badge with count
          alertToggleBtn.classList.add('has-alerts');
          alertBadge.textContent = activeCount.toString();
          alertBadge.style.display = 'block';
        } else {
          // Hide badge
          alertToggleBtn.classList.remove('has-alerts');
          alertBadge.style.display = 'none';
        }
        
        // Update title to show active alerts count
        alertToggleBtn.title = activeCount > 0 
          ? `Manage Alerts & Reminders (${activeCount} active)`
          : 'Manage Alerts & Reminders';
      } else {
        alertToggleBtn.style.display = 'none';
      }
    }
  } catch (error) {
    console.error('Error checking alert settings:', error);
    if (alertToggleBtn) {
      alertToggleBtn.style.display = 'none';
    }
  }
}

// Check Email Pinner settings and update button visibility
async function checkEmailPinnerSettings() {
  try {
    const result = await chrome.storage.local.get(['gmbExtractorSettings', 'gmailPinnedEmails']);
    const settings = result.gmbExtractorSettings || {};
    const pinnedEmails = result.gmailPinnedEmails || [];
    
    // Show Email Pinner button only if enabled (default is true)
    const isEmailPinnerEnabled = settings.emailPinnerEnabled !== false;
    
    if (emailPinnerBtn) {
      if (isEmailPinnerEnabled) {
        emailPinnerBtn.style.display = 'inline-block';
        
        // Count pinned emails
        const pinnedCount = pinnedEmails.length;
        
        if (pinnedCount > 0) {
          // Show badge with count
          emailPinnerBtn.classList.add('has-emails');
          emailPinnerBadge.textContent = pinnedCount.toString();
          emailPinnerBadge.style.display = 'block';
        } else {
          // Hide badge
          emailPinnerBtn.classList.remove('has-emails');
          emailPinnerBadge.style.display = 'none';
        }
        
        // Update title to show pinned emails count
        emailPinnerBtn.title = pinnedCount > 0 
          ? `Manage Pinned Emails (${pinnedCount} pinned)`
          : 'Manage Pinned Emails';
      } else {
        emailPinnerBtn.style.display = 'none';
      }
    }
  } catch (error) {
    console.error('Error checking email pinner settings:', error);
    if (emailPinnerBtn) {
      emailPinnerBtn.style.display = 'none';
    }
  }
}

// Check Popup Shortcuts settings and update tooltip visibility
async function checkPopupShortcutsSettings() {
  try {
    const result = await chrome.storage.local.get(['gmbExtractorSettings']);
    const settings = result.gmbExtractorSettings || {};
    
    // Default to enabled (true) to match popup-shortcuts.js default
    const isPopupShortcutsEnabled = settings.popupShortcutsEnabled !== false;
    
    // Add/remove class to control tooltip visibility
    if (isPopupShortcutsEnabled) {
      document.body.classList.remove('popup-shortcuts-disabled');
    } else {
      document.body.classList.add('popup-shortcuts-disabled');
    }
    
    console.log('Popup Shortcuts tooltips:', isPopupShortcutsEnabled ? 'enabled' : 'disabled');
  } catch (error) {
    console.error('Error checking popup shortcuts settings:', error);
    // Default to enabled on error
    document.body.classList.remove('popup-shortcuts-disabled');
  }
}

// Pomodoro Toggle Button functionality
const pomodoroToggleBtn = document.getElementById('pomodoroToggleBtn');
let pomodoroCurrentState = 'idle'; // idle, running, paused

if (pomodoroToggleBtn) {
  // ENHANCED EVENT LISTENER: Add debouncing and prevent multiple attachments
  if (!pomodoroToggleBtn.hasAttribute('data-listener-attached')) {
    pomodoroToggleBtn.addEventListener('click', () => {
      togglePomodoroTimer();
    });
    
    // Mark as having listener attached to prevent duplicates
    pomodoroToggleBtn.setAttribute('data-listener-attached', 'true');
    console.log('🔗 Popup: Pomodoro toggle button event listener attached with duplicate protection');
  } else {
    console.log('🔗 Popup: Pomodoro toggle button listener already attached, skipping');
  }
}

// Alert Toggle Button functionality
if (alertToggleBtn) {
  alertToggleBtn.addEventListener('click', async () => {
    // Open alert manager window
    let windowOptions = {
      url: chrome.runtime.getURL('alerts/alert-manager.html'),
      type: 'popup',
      width: 550,
      height: 650,
      focused: true
    };
    
    try {
      // Try to get saved window settings from localStorage
      const savedSettings = localStorage.getItem('gmb-alert-manager-window-settings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        
        // Validate saved position is still within screen bounds
        const maxX = screen.width - settings.width;
        const maxY = screen.height - settings.height;
        
        const validX = Math.max(0, Math.min(settings.x, maxX));
        const validY = Math.max(0, Math.min(settings.y, maxY));
        
        // Use saved settings if valid
        windowOptions.width = settings.width;
        windowOptions.height = settings.height;
        windowOptions.left = validX;
        windowOptions.top = validY;
      } else {
        // Calculate center position
        const left = Math.round((screen.width - windowOptions.width) / 2);
        const top = Math.round((screen.height - windowOptions.height) / 2);
        
        windowOptions.left = left;
        windowOptions.top = top;
      }
    } catch (error) {
      console.error('Error loading alert window settings:', error);
    }
    
    // Create the alert manager window
    chrome.windows.create(windowOptions);
  });
}

// Email Pinner Button functionality
if (emailPinnerBtn) {
  emailPinnerBtn.addEventListener('click', async () => {
    showEmailPinnerPopup();
  });
}

// Show Email Pinner popup as separate window
async function showEmailPinnerPopup() {
  try {
    // Window options similar to Alert Manager
    let windowOptions = {
      url: chrome.runtime.getURL('alerts/email-pinner.html'),
      type: 'popup',
      width: 550,
      height: 650,
      focused: true
    };
    
    try {
      // Try to get saved window settings from localStorage
      const savedSettings = localStorage.getItem('gmb-email-pinner-window-settings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        
        // Validate saved position is still within screen bounds
        const maxX = screen.width - settings.width;
        const maxY = screen.height - settings.height;
        
        const validX = Math.max(0, Math.min(settings.x, maxX));
        const validY = Math.max(0, Math.min(settings.y, maxY));
        
        // Use saved settings if valid
        windowOptions.width = settings.width;
        windowOptions.height = settings.height;
        windowOptions.left = validX;
        windowOptions.top = validY;
      } else {
        // Calculate center position
        const left = Math.round((screen.width - windowOptions.width) / 2);
        const top = Math.round((screen.height - windowOptions.height) / 2);
        
        windowOptions.left = left;
        windowOptions.top = top;
      }
    } catch (error) {
      console.error('Error loading email pinner window settings:', error);
    }
    
    // Create the email pinner window
    chrome.windows.create(windowOptions);
    
  } catch (error) {
    console.error('Error showing email pinner popup:', error);
  }
}


// Helper function to send messages with proper error handling
function sendMessageWithErrorHandling(message, timeoutMs = 15000) {
  return new Promise((resolve, reject) => {
    console.log('📤 Popup: Sending message to background:', message.action);
    
    // Set up timeout for async operations
    const timeoutId = setTimeout(() => {
      reject(new Error(`Message timeout after ${timeoutMs}ms for action: ${message.action}`));
    }, timeoutMs);
    
    try {
      // Check if background script context exists
      if (!chrome.runtime?.id) {
        clearTimeout(timeoutId);
        reject(new Error('Extension context invalidated'));
        return;
      }
      
      chrome.runtime.sendMessage(message, (response) => {
        console.log('📦 Popup: Raw response received:', response, 'lastError:', chrome.runtime.lastError);
        
        // Clear timeout since we got a response (or error)
        clearTimeout(timeoutId);
        
        // Check for Chrome runtime errors
        if (chrome.runtime.lastError) {
          console.error('❌ Popup: Chrome runtime error:', chrome.runtime.lastError.message);
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        
        // Check if we got a response
        if (response === undefined || response === null) {
          console.error('❌ Popup: No response received from background script for action:', message.action);
          reject(new Error('No response received from background script'));
          return;
        }
        
        console.log('📥 Popup: Received response from background:', response);
        
        // Check if the response indicates an error
        if (response.success === false) {
          const errorMsg = response.error || 'Unknown error from background script';
          console.error('❌ Popup: Background script returned error:', errorMsg);
          reject(new Error(errorMsg));
          return;
        }
        
        resolve(response);
      });
    } catch (error) {
      clearTimeout(timeoutId);
      console.error('❌ Popup: Error sending message:', error);
      reject(new Error('Failed to send message: ' + error.message));
    }
  });
}

// Helper function to check if background script is ready
async function isPopupBackgroundReady() {
  try {
    console.log('🔍 Popup: Testing background script connection...');
    if (!chrome.runtime?.id) {
      console.error('❌ Popup: Chrome runtime ID not available');
      return false;
    }
    
    const response = await sendMessageWithErrorHandling({
      action: 'ping'
    });
    
    console.log('🏓 Popup: Ping response received:', response);
    return response && response.success;
  } catch (error) {
    console.log('Popup: Background not ready:', error.message);
    return false;
  }
}

// Helper function to wait for background script readiness
async function waitForPopupBackgroundReady(maxAttempts = 3, delayMs = 300) {
  for (let i = 0; i < maxAttempts; i++) {
    if (await isPopupBackgroundReady()) {
      return true;
    }
    
    console.log(`Popup: Background not ready, attempt ${i + 1}/${maxAttempts}`);
    await new Promise(resolve => setTimeout(resolve, delayMs));
  }
  
  return false;
}



// Pomodoro toggle function - true on/off toggle behavior (start/stop)
async function togglePomodoroTimer() {
  // EXECUTION GUARD: Prevent concurrent executions
  if (window.pomodoroToggleExecuting) {
    console.log('🔒 Popup: Toggle already executing, ignoring duplicate call');
    return;
  }
  
  // DEBOUNCING: Prevent rapid-fire clicks
  const now = Date.now();
  if (window.pomodoroLastToggle && now - window.pomodoroLastToggle < 500) {
    console.log('🕒 Popup: Toggle too soon after last call, ignoring (debounced)');
    return;
  }
  
  try {
    // Set execution guards
    window.pomodoroToggleExecuting = true;
    window.pomodoroLastToggle = now;
    
    console.log('🔗 Popup: Toggle button clicked - using start/stop toggle logic');
    
    // STATE VERIFICATION: Get fresh state from storage to ensure accuracy
    const result = await chrome.storage.local.get(['pomodoroTimerState']);
    const timerState = result.pomodoroTimerState;
    const currentState = timerState?.currentState || 'idle';
    const isPaused = timerState?.isPaused || false;
    
    console.log('🔗 Popup: Current timer state:', { currentState, isPaused });
    
    if (currentState === 'idle') {
      // Start timer with current mode (only when truly idle)
      const currentMode = getCurrentSelectedMode();
      console.log('🔗 Popup: Starting timer in mode:', currentMode);
      
      chrome.runtime.sendMessage({ 
        action: 'pomodoroStart',
        mode: currentMode 
      });
    } else {
      // Timer is active (running or paused) - stop it completely to make it a true toggle
      console.log('🔗 Popup: Stopping timer (true toggle behavior)');
      chrome.runtime.sendMessage({ action: 'pomodoroStop' });
    }
    
    console.log('🔗 Popup: Timer control message sent, state will update via storage sync');
  } catch (error) {
    console.error('🔗 Popup: Error in toggle:', error);
    showNotification('Error with Pomodoro timer', 'error');
  } finally {
    // Clear execution guard after a delay to allow state updates
    setTimeout(() => {
      window.pomodoroToggleExecuting = false;
    }, 1000);
  }
}

// EMERGENCY FORCE STOP: Available for debugging and if button gets stuck
async function forcePomodoroStop() {
  try {
    console.log('🚨 Popup: Force stop requested - emergency timer shutdown');
    
    const response = await chrome.runtime.sendMessage({ 
      action: 'pomodoroForceStop' 
    });
    
    if (response && response.success) {
      console.log('🚨 Popup: Force stop completed successfully');
      
      // Force update UI to idle state
      if (pomodoroToggleBtn) {
        updatePomodoroButtonState('idle', false);
      }
      
      // Clear execution guards
      window.pomodoroToggleExecuting = false;
      window.pomodoroLastToggle = 0;
      
      showNotification('Timer force stopped', 'success');
    } else {
      console.error('❌ Popup: Force stop failed:', response?.error);
      showNotification('Force stop failed', 'error');
    }
  } catch (error) {
    console.error('❌ Popup: Error in force stop:', error);
    showNotification('Force stop error: ' + error.message, 'error');
  }
}

// Make force stop available globally for debugging
window.forcePomodoroStop = forcePomodoroStop;

// Get currently selected mode from shared interface or fallback to buttons
function getCurrentSelectedMode() {
  try {
    // First try to get mode from shared pomodoro interface
    if (window.pomodoroPopup && window.pomodoroPopup.timerState) {
      const mode = window.pomodoroPopup.timerState.currentMode;
      if (mode) {
        console.log('🔗 Popup: Got mode from shared interface:', mode);
        return mode;
      }
    }
    
    // Fallback to checking button states
    const ultraBtn = document.getElementById('pomodoroUltraMode');
    
    if (ultraBtn && ultraBtn.classList.contains('pomodoro-mode-btn--active')) {
      return 'ultra';
    } else {
      return 'standard'; // Default to standard
    }
  } catch (error) {
    console.log('Could not determine selected mode, defaulting to standard');
    return 'standard';
  }
}

// Update button state with proper pause/resume indicators
function updatePomodoroButtonState(state, isPaused = false) {
  pomodoroCurrentState = state;
  
  if (state === 'idle') {
    // Timer is idle - ready to start
    pomodoroToggleBtn.classList.remove('active', 'paused');
    pomodoroToggleBtn.title = 'Start Pomodoro Timer';
  } else if (isPaused) {
    // Timer is paused - ready to resume
    pomodoroToggleBtn.classList.add('active', 'paused');
    pomodoroToggleBtn.title = 'Resume Pomodoro Timer';
  } else {
    // Timer is running - ready to pause
    pomodoroToggleBtn.classList.add('active');
    pomodoroToggleBtn.classList.remove('paused');
    pomodoroToggleBtn.title = 'Pause Pomodoro Timer';
  }
}

// Listen for timer state updates from background
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'pomodoroStateUpdate') {
    const state = message.state;
    if (state.currentState === 'idle') {
      updatePomodoroButtonState('idle', false);
    } else {
      updatePomodoroButtonState(state.currentState, state.isPaused || false);
    }
  }
  
  // Handle Email Pinner counter updates from content script
  if (message.action === 'updateEmailPinnerCounter') {
    checkEmailPinnerSettings();
  }
});

// Listen for timer state changes from the Pomodoro popup (mode buttons)
document.addEventListener('pomodoroStateChange', (event) => {
  const state = event.detail.state;
  console.log('Toggle button: Received state change from Pomodoro popup:', state);
  // For mode button changes, determine state based on whether timer is running
  if (state === 'running') {
    updatePomodoroButtonState('work', false); // Assume work state when running
  } else {
    updatePomodoroButtonState(state, false);
  }
});

// Listen for timer state changes in storage (CROSS-TAB SYNC)
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local' && changes.pomodoroTimerState) {
    const newState = changes.pomodoroTimerState.newValue;
    if (newState && pomodoroToggleBtn) {
      // Timer state changed in storage, updating toggle button
      if (newState.currentState === 'idle') {
        updatePomodoroButtonState('idle', false);
      } else {
        updatePomodoroButtonState(newState.currentState, newState.isPaused || false);
      }
      // Toggle button cross-tab sync completed
    }
  }
});

// Multi-monitor detection utility
async function getActiveMonitorInfo() {
  try {
    const displays = await chrome.system.display.getInfo();
    
    // Get current window position (this is the popup window position)
    const currentWindow = await chrome.windows.getCurrent();
    const windowCenterX = currentWindow.left + (currentWindow.width / 2);
    const windowCenterY = currentWindow.top + (currentWindow.height / 2);
    
    // Find which display contains the center of the current window
    let activeDisplay = displays.find(display => {
      const bounds = display.bounds;
      return windowCenterX >= bounds.left && 
             windowCenterX < bounds.left + bounds.width &&
             windowCenterY >= bounds.top && 
             windowCenterY < bounds.top + bounds.height;
    });
    
    // Fallback to primary display if not found
    if (!activeDisplay) {
      activeDisplay = displays.find(display => display.isPrimary) || displays[0];
    }
    
    return {
      display: activeDisplay,
      allDisplays: displays,
      currentWindowCenter: { x: windowCenterX, y: windowCenterY }
    };
  } catch (error) {
    console.log('Multi-monitor detection failed, using primary display:', error);
    // Fallback to screen object for single monitor scenarios
    return {
      display: {
        bounds: { left: 0, top: 0, width: screen.width, height: screen.height },
        isPrimary: true
      },
      allDisplays: [{ bounds: { left: 0, top: 0, width: screen.width, height: screen.height }, isPrimary: true }],
      currentWindowCenter: { x: screen.width / 2, y: screen.height / 2 }
    };
  }
}

// Settings functionality
if (settingsBtn) {
  settingsBtn.addEventListener('click', async () => {
    // Get active monitor information
    const monitorInfo = await getActiveMonitorInfo();
    const activeDisplay = monitorInfo.display;
    const displayBounds = activeDisplay.bounds;
    
    // Get saved window settings to avoid jump effect
    let windowOptions = {
      url: chrome.runtime.getURL('settings/settings.html'),
      type: 'popup',
      width: 700,
      height: 600,
      focused: true
    };
    
    try {
      // Try to get saved window settings from localStorage
      const savedSettings = localStorage.getItem('gmb-settings-window-settings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        
        // Validate saved position is within active display bounds
        const maxX = displayBounds.left + displayBounds.width - settings.width;
        const maxY = displayBounds.top + displayBounds.height - settings.height;
        const minX = displayBounds.left;
        const minY = displayBounds.top;
        
        const validX = Math.max(minX, Math.min(settings.x, maxX));
        const validY = Math.max(minY, Math.min(settings.y, maxY));
        
        // Use saved settings if valid, otherwise center on active display
        windowOptions.width = settings.width;
        windowOptions.height = settings.height;
        windowOptions.left = validX;
        windowOptions.top = validY;
      } else {
        // Center window on active display if no saved settings
        windowOptions.left = Math.max(displayBounds.left, displayBounds.left + (displayBounds.width - windowOptions.width) / 2);
        windowOptions.top = Math.max(displayBounds.top, displayBounds.top + (displayBounds.height - windowOptions.height) / 2);
      }
    } catch (e) {
      console.log('Could not read saved window settings, using defaults:', e);
      // Center window on active display as fallback
      windowOptions.left = Math.max(displayBounds.left, displayBounds.left + (displayBounds.width - windowOptions.width) / 2);
      windowOptions.top = Math.max(displayBounds.top, displayBounds.top + (displayBounds.height - windowOptions.height) / 2);
    }
    
    // Open settings page in a new popup window at the correct position
    chrome.windows.create(windowOptions, (window) => {
      // Activate settings lockdown to protect timer values
      chrome.runtime.sendMessage({
        action: 'activateSettingsLockdown',
        windowId: window.id
      }, (response) => {
        if (response && response.success) {
          console.log('🔒 Popup: Settings lockdown activated for window', window.id);
        } else {
          console.error('❌ Popup: Failed to activate settings lockdown:', response);
        }
      });
    });
  });
}

// Reload functionality
const reloadBtn = document.getElementById('reloadBtn');
if (reloadBtn) {
  reloadBtn.addEventListener('click', () => {
    if (window.GMBExtensionReload && window.GMBExtensionReload.reload) {
      window.GMBExtensionReload.reload();
    } else {
      console.error('Popup: Extension reload utility not available');
      alert('Extension reload feature not available. Please reload manually from the extensions page.');
    }
  });
}

// Check Location functionality
const checkLocationBtn = document.getElementById('checkLocationBtn');
if (checkLocationBtn) {
  checkLocationBtn.addEventListener('click', () => {
    // Open the "where am i" Google search in a new tab
    chrome.tabs.create({
      url: 'https://www.google.com/search?q=where+am+i'
    });
  });
}

// Secret debug toggle functionality
async function setupSecretDebugToggle() {
  if (!secretDebugToggle) return;
  
  // Load current debug state
  const result = await chrome.storage.local.get(['secretDebugEnabled']);
  const isEnabled = result.secretDebugEnabled || false;
  
  // Update button appearance based on state
  updateDebugToggleAppearance(isEnabled);
  
  // Add click listener
  secretDebugToggle.addEventListener('click', async () => {
    const currentState = await chrome.storage.local.get(['secretDebugEnabled']);
    const newState = !currentState.secretDebugEnabled;
    
    // Save new state
    await chrome.storage.local.set({ secretDebugEnabled: newState });
    
    // Update appearance
    updateDebugToggleAppearance(newState);
    
    // Send message to all tabs to toggle debugger
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach(tab => {
        chrome.tabs.sendMessage(tab.id, {
          type: 'toggleUniversalDebugger',
          enabled: newState
        }).catch(() => {}); // Ignore errors for tabs without content script
      });
    });
    
    // Show subtle feedback
    secretDebugToggle.style.transform = 'scale(1.2)';
    setTimeout(() => {
      secretDebugToggle.style.transform = 'scale(1)';
    }, 150);
  });
}

function updateDebugToggleAppearance(isEnabled) {
  if (!secretDebugToggle) return;
  
  if (isEnabled) {
    secretDebugToggle.style.color = '#7C3AED';
    secretDebugToggle.textContent = '.';
    secretDebugToggle.title = '';
  } else {
    secretDebugToggle.style.color = '#888';
    secretDebugToggle.textContent = '.';
    secretDebugToggle.title = '';
  }
}

// Onboarding system initialization
async function initializeOnboarding() {
  try {
    // Check if this is the first time opening the popup
    const result = await chrome.storage.local.get(['onboardingCompleted']);
    const isFirstTime = !result.onboardingCompleted;
    
    if (isFirstTime) {
      console.log('🎉 First-time user detected - triggering onboarding via content script');
      
      // Get current active tab (using Promise instead of callback)
      return new Promise((resolve) => {
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
          const currentTab = tabs[0];
          if (!currentTab || !currentTab.url) {
            console.warn('⚠️ No active tab found for onboarding');
            resolve(false); // Could not trigger onboarding
            return;
          }

          // Check if tab supports content script injection
          if (currentTab.url.startsWith('chrome://') || 
              currentTab.url.startsWith('chrome-extension://') || 
              currentTab.url.startsWith('edge://') || 
              currentTab.url.startsWith('moz-extension://')) {
            console.warn('⚠️ Cannot inject onboarding into restricted page:', currentTab.url);
            resolve(false); // Could not trigger onboarding
            return;
          }

          console.log('📝 Injecting onboarding script into tab:', currentTab.url);

          // Inject onboarding content script
          chrome.scripting.executeScript({
            target: { tabId: currentTab.id },
            files: ['js/onboarding-content.js']
          }, (injectionResults) => {
            if (chrome.runtime.lastError) {
              console.error('❌ Error injecting onboarding script:', chrome.runtime.lastError);
              resolve(false); // Could not trigger onboarding
              return;
            }

            console.log('✅ Onboarding script injected successfully');

            // Wait a moment for script to initialize, then trigger onboarding
            setTimeout(() => {
              chrome.tabs.sendMessage(currentTab.id, { 
                action: 'showOnboarding' 
              }, (response) => {
                if (chrome.runtime.lastError) {
                  console.warn('⚠️ Could not send onboarding message:', chrome.runtime.lastError);
                  resolve(false); // Could not trigger onboarding
                } else if (response && response.success) {
                  console.log('✨ Onboarding triggered successfully in content script');
                  resolve(true); // Onboarding was successfully triggered
                } else {
                  console.warn('⚠️ Onboarding response:', response);
                  resolve(false); // Could not trigger onboarding
                }
              });
            }, 300); // Increased timeout to ensure script loads
          });
        });
      });
    } else {
      console.log('🔄 Returning user - skipping onboarding');
      return false; // No onboarding needed
    }
  } catch (error) {
    console.error('❌ Error checking onboarding status:', error);
    return false; // Error occurred, no onboarding triggered
  }
}

// Helper function to reset onboarding for testing (available in console)
window.resetOnboarding = async function() {
  try {
    await chrome.storage.local.remove(['onboardingCompleted']);
    console.log('🔄 Onboarding reset - will show on next popup reload');
    if (window.OnboardingSystem && window.OnboardingSystem.resetOnboarding) {
      await window.OnboardingSystem.resetOnboarding();
    }
  } catch (error) {
    console.error('❌ Error resetting onboarding:', error);
  }
};
