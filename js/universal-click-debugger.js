// Universal Click Debugger - Find working click methods for any element
// Usage: Paste target selector and HTML structure to systematically test all click methods

(function() {
  'use strict';

  // Global state for testing control and result tracking
  let testingPaused = false;
  let testingResults = [];
  let successfulMethods = new Set(); // Track successful element+method combinations
  let globalDebugMode = false; // Track global debug mode state
  
  // State change listening variables
  let isListening = false;
  let initialDOMState = null;
  let stateChangeObserver = null;
  let clickListener = null;

  let debugSession = {
    targetSelector: '',
    htmlStructure: '',
    results: [],
    successfulMethods: []
  };

  // Function to check if global debug mode is enabled
  function checkGlobalDebugMode() {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.get(['gmbExtractorSettings'], function(result) {
        const settings = result.gmbExtractorSettings || {};
        const debugModeSection = document.getElementById('debugModeSection');
        const isDeveloperModeActive = debugModeSection && debugModeSection.hasAttribute('data-developer-mode-active');
        globalDebugMode = settings.debugMode === true && isDeveloperModeActive;
      });
    }
  }

  // Enhanced console logging - Universal Click Debugger always logs when active
  function debugLog(message, style = '') {
    // Always log if Universal Click Debugger is active (button or panel exists)
    const universalDebuggerActive = document.getElementById('universal-click-debugger') || document.getElementById('universal-debugger-btn');
    
    // Also log if global debug mode is enabled (for when Universal Debugger is OFF but debug mode is ON)
    if (universalDebuggerActive || globalDebugMode) {
      if (style) {
        console.log(message, style);
      } else {
        console.log(message);
      }
    }
  }

  // Check global debug mode on script load
  checkGlobalDebugMode();

  // Listen for settings changes to update debug mode
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'updateSettings') {
        checkGlobalDebugMode();
      }
    });
    
    // Also listen for Chrome storage changes
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'sync' && changes.gmbExtractorSettings) {
        checkGlobalDebugMode();
      }
    });
  }

  // Create debug panel
  // Position memory functions following popup design standards
  function savePopupPosition(popup) {
    try {
      const position = {
        top: popup.style.top,
        left: popup.style.left,
        timestamp: Date.now()
      };
      localStorage.setItem('universalDebugger_position', JSON.stringify(position));
      debugLog('📍 Popup position saved:', position);
    } catch (e) {
      debugLog('⚠️ Could not save popup position:', e.message);
    }
  }

  function loadPopupPosition() {
    try {
      const saved = localStorage.getItem('universalDebugger_position');
      if (saved) {
        const position = JSON.parse(saved);
        // Only use saved position if it's not too old (24 hours)
        const isRecent = Date.now() - (position.timestamp || 0) < 24 * 60 * 60 * 1000;
        if (isRecent && position.top && position.left) {
          return {
            top: position.top,
            left: position.left
          };
        }
      }
    } catch (e) {
      debugLog('⚠️ Could not load popup position:', e.message);
    }
    return {}; // Return empty object for default positioning
  }

  function createUniversalDebugPanel() {
    // SUPER VERBOSE LOGGING for debugging
    debugLog('%c🔍 UNIVERSAL DEBUGGER: Creating debug panel...', 'color: #7C3AED; font-size: 16px; font-weight: bold; background: rgba(124, 58, 237, 0.1); padding: 4px;');
    debugLog('🔧 Debug panel creation started at:' + new Date().toLocaleTimeString());
    
    // Remove existing popup
    const existing = document.getElementById('universal-click-debugger');
    if (existing) {
      debugLog('🗑️ Removing existing Universal Debugger panel');
      // Clean up escape handler before removing
      if (existing._escapeHandler) {
        document.removeEventListener('keydown', existing._escapeHandler);
      }
      existing.remove();
    } else {
      debugLog('✅ No existing Universal Debugger panel found - proceeding with creation');
    }

    // Load saved position or use default
    const savedPosition = loadPopupPosition();
    debugLog('📍 Loaded saved position:', savedPosition);

    // Main container following popup design standards
    const panel = document.createElement('div');
    panel.id = 'universal-click-debugger';
    debugLog('🎨 Creating main panel container with ID: ' + panel.id);
    
    panel.style.cssText = `
      position: fixed;
      top: ${savedPosition.top || '50%'};
      left: ${savedPosition.left || '50%'};
      ${savedPosition.top && savedPosition.left ? '' : 'transform: translate(-50%, -50%);'}
      background: #0a0a0a;
      border: 2px solid #7C3AED;
      border-radius: 12px;
      padding: 0;
      width: 600px;
      height: 90%;
      z-index: 9999999;
      box-shadow: 0 8px 32px rgba(0,0,0,0.6);
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
      color: #d1d5db;
      resize: both;
      overflow: hidden;
      min-width: 400px;
      min-height: 300px;
    `;
    debugLog('🎨 Panel styling applied - dimensions: 600x90%, position memory enabled');

      // Header following standards (NO EMOJIS)
      const header = document.createElement('div');
      debugLog('📋 Creating header element');
    
    header.style.cssText = `
      background: #1a1a1a;
      color: white;
      padding: 12px 20px;
      border-radius: 10px 10px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: move;
      user-select: none;
      font-size: 16px;
      font-weight: 700;
    `;

          const titleContainer = document.createElement('div');
      const titleText = document.createElement('span');
      titleText.textContent = 'Universal Click Debugger';
      titleText.style.cssText = 'display: block; margin-bottom: 2px;';
      
      const statusMessage = document.createElement('div');
      statusMessage.id = 'debugger-status-message';
      statusMessage.textContent = 'Ready to test element interactions';
      statusMessage.style.cssText = 'font-size: 11px; color: #6b7280; font-weight: 400;';
      
      titleContainer.appendChild(titleText);
      titleContainer.appendChild(statusMessage);
      debugLog('📝 Header title and status set');

      const closeButton = document.createElement('button');
      closeButton.innerHTML = '✕';
      debugLog('❌ Close button created');
    
    closeButton.style.cssText = `
      background: none;
      border: none;
      color: white;
      font-size: 18px;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      transition: background-color 0.2s ease;
    `;
    closeButton.onmouseover = () => closeButton.style.background = 'rgba(255,255,255,0.2)';
    closeButton.onmouseout = () => closeButton.style.background = 'none';
    closeButton.onclick = () => {
      debugLog('❌ Close button clicked - removing Universal Debugger panel');
      // Save current position before closing
      savePopupPosition(panel);
      // Clean up escape handler before removing popup - CRITICAL
      if (panel._escapeHandler) {
        debugLog('🗑️ Removing escape key handler');
        document.removeEventListener('keydown', panel._escapeHandler);
      }
      panel.remove();
      debugLog('✅ Universal Debugger panel removed successfully');
    };

    // Prevent dragging when clicking the close button - REQUIRED
    closeButton.onmousedown = (e) => {
      e.stopPropagation();
    };

          header.appendChild(titleContainer);
      header.appendChild(closeButton);
      debugLog('📋 Header assembly complete');

      // Content area following standards
      const content = document.createElement('div');
      debugLog('📄 Creating content area');
    
    content.style.cssText = `
      padding: 20px;
      height: calc(100% - 52px);
      display: flex;
      flex-direction: column;
      overflow-y: auto;
    `;

          // Form fields
      debugLog('📝 Creating form HTML with ALL BUTTONS VISIBLE INCLUDING PAUSE BUTTON');
    const formHTML = `
      <!-- Collapsible Input Section -->
      <div style="margin-bottom: 15px;">
        <button id="input-accordion-toggle" style="width: 100%; background: #374151; border: 1px solid #4b5563; color: #d1d5db; padding: 12px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 600; transition: all 0.2s ease; display: flex; justify-content: space-between; align-items: center;">
          <span>Input Settings</span>
          <span id="accordion-icon" style="transition: transform 0.2s ease;">▼</span>
        </button>
        
        <div id="input-accordion-content" style="margin-top: 8px; transition: all 0.3s ease;">
          <div style="padding: 16px; background: #1a1a1a; border: 1px solid #374151; border-radius: 6px;">
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; color: #d1d5db; font-size: 14px; font-weight: 500;">HTML Structure (surrounding your target):</label>
              <textarea id="html-structure" placeholder="<div>...paste HTML here...</div>" style="width: 100%; height: 80px; padding: 8px; background: #111111; border: 1px solid #374151; border-radius: 6px; color: #d1d5db; font-family: monospace; resize: vertical; font-size: 12px; box-sizing: border-box;"></textarea>
            </div>
            
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; color: #d1d5db; font-size: 14px; font-weight: 500;">Target Selector (e.g., span.aJ6, .button-class):</label>
              <input type="text" id="target-selector" placeholder="span.aJ6" style="width: 100%; padding: 8px; background: #111111; border: 1px solid #374151; border-radius: 6px; color: #d1d5db; font-size: 14px; box-sizing: border-box;">
            </div>
            
            <div style="margin-bottom: 0;">
              <label style="display: block; margin-bottom: 5px; color: #d1d5db; font-size: 14px; font-weight: 500;">Success Indicators (use shortcodes or selectors):</label>
              <input type="text" id="success-indicators" placeholder="NP, MD, NT" style="width: 100%; padding: 8px; background: #111111; border: 1px solid #374151; border-radius: 6px; color: #d1d5db; font-size: 14px; box-sizing: border-box;">
              
              <div style="margin-top: 8px; padding: 12px; background: #0a0a0a; border-radius: 6px; border: 1px solid #2a2a2a;">
                <div style="color: #f59e0b; font-weight: 600; margin-bottom: 8px; font-size: 13px; display: flex; justify-content: space-between; align-items: center;">
                  <span>Quick Select Shortcodes (click to toggle)</span>
                  <button id="toggle-all-shortcodes" style="background: transparent; border: 1px solid #f59e0b; color: #f59e0b; padding: 2px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500; transition: all 0.2s ease;">
                    Select All
                  </button>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 6px; margin-bottom: 10px;">
                  <button class="shortcode-btn" data-shortcode="NP" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    NP - New Page
                  </button>
                  <button class="shortcode-btn" data-shortcode="MD" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    MD - Modal
                  </button>
                  <button class="shortcode-btn" data-shortcode="NT" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    NT - Notification
                  </button>
                  <button class="shortcode-btn" data-shortcode="PP" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    PP - Popup
                  </button>
                  <button class="shortcode-btn" data-shortcode="FM" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    FM - Form Submit
                  </button>
                  <button class="shortcode-btn" data-shortcode="DD" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    DD - Dropdown
                  </button>
                  <button class="shortcode-btn" data-shortcode="TB" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    TB - Tab Change
                  </button>
                  <button class="shortcode-btn" data-shortcode="AC" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    AC - Accordion
                  </button>
                  <button class="shortcode-btn" data-shortcode="LD" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    LD - Loading
                  </button>
                  <button class="shortcode-btn" data-shortcode="CT" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    CT - Content
                  </button>
                  <button class="shortcode-btn" data-shortcode="ER" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    ER - Error
                  </button>
                  <button class="shortcode-btn" data-shortcode="SU" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; transition: all 0.2s ease; font-weight: 500;">
                    SU - Success
                  </button>
                </div>
                <div style="color: #6b7280; font-size: 10px; border-top: 1px solid #2a2a2a; padding-top: 6px;">
                  Click shortcodes above or type CSS selectors (e.g., "[role=dialog], .popup")
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Listen for State Change - Outside accordion -->
      <div style="margin-bottom: 15px;">
        <div id="listening-status" style="display: none; margin-bottom: 8px; padding: 12px; background: rgba(5, 150, 105, 0.1); border: 1px solid #059669; border-radius: 6px; color: #059669; font-size: 16px; font-weight: bold; text-align: center;">
          🎧 Listening for DOM changes... Click your target element now!
        </div>
        <button id="listen-state-change" style="background: #059669; border: 1px solid #059669; color: white; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 600; transition: all 0.2s ease; width: 100%;">
          Listen for State Change
        </button>
      </div>
      
      <div style="margin-bottom: 15px;">
        <label for="expected-payload" style="display: block; margin-bottom: 5px; color: #d1d5db; font-size: 14px; font-weight: 500;">Expected Payload (clipboard content) - Optional:</label>
        <textarea id="expected-payload" placeholder="Optional: Paste expected clipboard content here (e.g., https://youtu.be/8Oc6mHMvWxM). Leave empty if testing page navigation only." style="width: 100%; height: 60px; padding: 8px; background: #1a1a1a; border: 1px solid #374151; border-radius: 6px; color: #d1d5db; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-size: 12px; resize: vertical; box-sizing: border-box;"></textarea>
        <div style="font-size: 11px; color: #6b7280; margin-top: 4px;">
          If specified, success will only be confirmed when this content is found in the clipboard. UTM parameters are automatically ignored.
        </div>
      </div>
      
      <div style="margin-bottom: 15px;">
        <div style="margin-top: 8px; padding: 12px; background: #111111; border-radius: 6px; border: 1px solid #2a2a2a;">
          <div style="color: #f59e0b; font-weight: 600; margin-bottom: 8px; font-size: 13px; display: flex; justify-content: space-between; align-items: center;">
            <span>Concurrent Testing Settings</span>
            <div id="concurrent-status" style="font-size: 10px; color: #6b7280; font-weight: 400;">
              Sequential Mode
            </div>
          </div>
          <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
            <label style="color: #d1d5db; font-size: 12px; font-weight: 500; min-width: 80px;">Thread Count:</label>
            <div style="display: flex; gap: 4px; flex-wrap: wrap;">
              <button class="thread-btn" data-threads="1" style="background: #7C3AED; border: 1px solid #7C3AED; color: white; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 24px;">
                1
              </button>
              <button class="thread-btn" data-threads="2" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 24px;">
                2
              </button>
              <button class="thread-btn" data-threads="3" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 24px;">
                3
              </button>
              <button class="thread-btn" data-threads="4" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 24px;">
                4
              </button>
              <button class="thread-btn" data-threads="5" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 24px;">
                5
              </button>
              <button class="thread-btn" data-threads="6" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 24px;">
                6
              </button>
              <button class="thread-btn" data-threads="7" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 24px;">
                7
              </button>
              <button class="thread-btn" data-threads="8" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 24px;">
                8
              </button>
              <button class="thread-btn" data-threads="9" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 24px;">
                9
              </button>
              <button class="thread-btn" data-threads="10" style="background: transparent; border: 1px solid #374151; color: #6b7280; padding: 4px 6px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 26px;">
                10
              </button>
            </div>
          </div>
          <div style="color: #6b7280; font-size: 10px; border-top: 1px solid #2a2a2a; padding-top: 6px;">
            Higher thread counts (up to 10) test multiple elements simultaneously for faster results
          </div>
        </div>
      </div>
      
      <div style="margin-bottom: 15px; display: flex; gap: 10px; flex-wrap: wrap;">
        <button id="analyze-structure" style="background: #7C3AED; border: 1px solid #7C3AED; color: white; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 600; transition: all 0.2s ease;">
          Analyze Structure
        </button>
        <button id="test-all-variations" style="background: #010101; border: 1px solid #059669; color: white; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 600; transition: all 0.2s ease;">
          Test All Variations
        </button>
        <button id="pause-testing" style="background: #dc2626; border: 1px solid #dc2626; color: white; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 600; transition: all 0.2s ease;">
          Pause Testing
        </button>
        <button id="test-first-element" style="background: transparent; border: 1px solid #6b7280; color: #6b7280; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s ease;">
          Test First Element
        </button>
        <button id="test-all-elements" style="background: transparent; border: 1px solid #6b7280; color: #6b7280; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s ease;">
          Test All Elements
        </button>
        <button id="export-results" style="background: transparent; border: 1px solid #6b7280; color: #6b7280; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s ease;">
          Export Code
        </button>
        <button id="copy-summary" style="background: transparent; border: 1px solid #059669; color: #059669; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s ease;">
          Copy Summary
        </button>
        <button id="copy-log" style="background: transparent; border: 1px solid #7C3AED; color: #7C3AED; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s ease;">
          Copy Log
        </button>
        <button id="clear-log" style="background: transparent; border: 1px solid #6b7280; color: #6b7280; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s ease;">
          Clear
        </button>
        <button id="clean-dom" style="background: transparent; border: 1px solid #f59e0b; color: #f59e0b; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; font-weight: 600; transition: all 0.2s ease;">
          Clean DOM
        </button>
      </div>
      
      <div id="debug-output" style="background: #111111; padding: 15px; border-radius: 8px; flex: 1; overflow-y: auto; font-size: 12px; line-height: 1.4; border: 1px solid #2a2a2a;">
        <div style="color: #6b7280;">Ready to debug. Enter HTML structure and target selector, then click "Analyze Structure" to begin.</div>
        <div style="color: #7C3AED; margin-top: 10px; padding: 8px; background: rgba(124, 58, 237, 0.1); border-radius: 4px;">Tip: Open Developer Console (F12 or Cmd+Shift+C) for detailed debug logs!</div>
      </div>
    `;

          content.innerHTML = formHTML;
      debugLog('📝 Form HTML injected - ALL BUTTONS INCLUDING PAUSE BUTTON ARE VISIBLE');

      // Assembly
      panel.appendChild(header);
      panel.appendChild(content);
      document.body.appendChild(panel);
      debugLog('🏗️ Panel assembly complete - added to document body');

      // Make draggable following standards
      debugLog('🖱️ Setting up draggable functionality');
      makeDraggable(panel, header);

      // ESC handler following standards
    debugLog('⌨️ Setting up ESC key handler');
    // ESC key handler disabled - only close button should close panel
    // This prevents accidental closing when ESC is needed for testing
    debugLog('⌨️ ESC key closing disabled - use close button only');

    // Setup button hover effects following standards
    debugLog('🎨 Setting up button hover effects');
    setupButtonEffects();
    
          debugLog('🔗 Setting up event listeners for all buttons');
    setupEventListeners();
    
    debugLog('🎛️ Setting up accordion functionality');
    setupAccordion();
    
    debugLog('🎨 Setting up panel border color functionality');
    window._debuggerPanel = panel; // Store panel reference for border color changes
    
    // Simple notification that debugger is ready
    debugLog('%c🔍 Universal Click Debugger Active!', 'color: #7C3AED; font-size: 16px; font-weight: bold; background: rgba(124, 58, 237, 0.1); padding: 4px;');
    debugLog('✅ Universal Debugger panel creation COMPLETE at: ' + new Date().toLocaleTimeString());
    
    return panel;
  }





  // Improved draggable functionality that prevents jumping
  function makeDraggable(element, handle) {
    let isDragging = false;
    let startX = 0, startY = 0;
    let initialLeft = 0, initialTop = 0;
    
    handle.onmousedown = dragMouseDown;
    
    function dragMouseDown(e) {
      e = e || window.event;
      e.preventDefault();
      
      isDragging = true;
      startX = e.clientX;
      startY = e.clientY;
      
      // Get current position, accounting for transform
      const rect = element.getBoundingClientRect();
      initialLeft = rect.left;
      initialTop = rect.top;
      
      // Remove transform and set absolute positioning
      element.style.transform = 'none';
      element.style.left = initialLeft + 'px';
      element.style.top = initialTop + 'px';
      
      document.onmouseup = closeDragElement;
      document.onmousemove = elementDrag;
      
      // Add dragging class for visual feedback
      handle.style.cursor = 'grabbing';
    }
    
    function elementDrag(e) {
      if (!isDragging) return;
      
      e = e || window.event;
      e.preventDefault();
      
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;
      
      const newLeft = initialLeft + deltaX;
      const newTop = initialTop + deltaY;
      
      // Constrain to viewport
      const maxTop = window.innerHeight - element.offsetHeight;
      const maxLeft = window.innerWidth - element.offsetWidth;
      
      const constrainedLeft = Math.max(0, Math.min(newLeft, maxLeft));
      const constrainedTop = Math.max(0, Math.min(newTop, maxTop));
      
      element.style.left = constrainedLeft + 'px';
      element.style.top = constrainedTop + 'px';
    }
    
    function closeDragElement() {
      document.onmouseup = null;
      document.onmousemove = null;
      
      // Save position after dragging
      if (isDragging) {
        savePopupPosition(element);
        debugLog('📍 Position saved after drag');
      }
      
      isDragging = false;
    }
  }

  // Setup accordion functionality for input settings with state persistence
  function setupAccordion() {
    const toggleBtn = document.getElementById('input-accordion-toggle');
    const content = document.getElementById('input-accordion-content');
    const icon = document.getElementById('accordion-icon');
    
    // Load saved accordion state
    let isCollapsed = localStorage.getItem('universalDebugger_accordionCollapsed') === 'true';
    
    if (toggleBtn && content && icon) {
      // Apply saved state on load
      if (isCollapsed) {
        content.style.maxHeight = '0';
        content.style.opacity = '0';
        content.style.marginTop = '0';
        content.style.overflow = 'hidden';
        icon.style.transform = 'rotate(-90deg)';
        toggleBtn.style.background = '#2a2a2a';
        debugLog('🔽 Input accordion loaded in collapsed state');
      } else {
        content.style.maxHeight = 'none';
        content.style.opacity = '1';
        content.style.marginTop = '8px';
        content.style.overflow = 'visible';
        icon.style.transform = 'rotate(0deg)';
        toggleBtn.style.background = '#374151';
        debugLog('🔼 Input accordion loaded in expanded state');
      }
      
      toggleBtn.addEventListener('click', () => {
        isCollapsed = !isCollapsed;
        
        // Save state to localStorage
        localStorage.setItem('universalDebugger_accordionCollapsed', isCollapsed.toString());
        
        if (isCollapsed) {
          content.style.maxHeight = '0';
          content.style.opacity = '0';
          content.style.marginTop = '0';
          content.style.overflow = 'hidden';
          icon.style.transform = 'rotate(-90deg)';
          toggleBtn.style.background = '#2a2a2a';
          debugLog('🔽 Input accordion collapsed and saved');
        } else {
          content.style.maxHeight = 'none';
          content.style.opacity = '1';
          content.style.marginTop = '8px';
          content.style.overflow = 'visible';
          icon.style.transform = 'rotate(0deg)';
          toggleBtn.style.background = '#374151';
          debugLog('🔼 Input accordion expanded and saved');
        }
      });
      
      debugLog('✅ Accordion functionality initialized with state persistence');
    }
  }
  
  // Update debugger panel border color and status message
  function updatePanelStatus(status, message) {
    const panel = window._debuggerPanel;
    const statusElement = document.getElementById('debugger-status-message');
    
    if (panel) {
      switch (status) {
        case 'success':
          panel.style.border = '2px solid #10b981';
          panel.style.boxShadow = '0 0 20px rgba(16, 185, 129, 0.3)';
          if (statusElement) {
            statusElement.textContent = message || 'Success! Working method found';
            statusElement.style.color = '#10b981';
          }
          debugLog('🟢 Panel status updated: SUCCESS');
          break;
          
        case 'error':
          panel.style.border = '2px solid #ef4444';
          panel.style.boxShadow = '0 0 20px rgba(239, 68, 68, 0.3)';
          if (statusElement) {
            statusElement.textContent = message || 'Error: No working methods found';
            statusElement.style.color = '#ef4444';
          }
          debugLog('🔴 Panel status updated: ERROR');
          break;
          
        case 'testing':
          panel.style.border = '2px solid #f59e0b';
          panel.style.boxShadow = '0 0 20px rgba(245, 158, 11, 0.3)';
          if (statusElement) {
            statusElement.textContent = message || 'Testing in progress...';
            statusElement.style.color = '#f59e0b';
          }
          debugLog('🟡 Panel status updated: TESTING');
          break;
          
        case 'ready':
        default:
          panel.style.border = '1px solid #374151';
          panel.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
          if (statusElement) {
            statusElement.textContent = message || 'Ready to test element interactions';
            statusElement.style.color = '#6b7280';
          }
          debugLog('⚪ Panel status updated: READY');
          break;
      }
    }
  }

  // Setup button hover effects following standards
  function setupButtonEffects() {
    // Primary button (Analyze Structure)
    const primaryBtn = document.getElementById('analyze-structure');
    if (primaryBtn) {
      primaryBtn.onmouseover = () => {
        primaryBtn.style.background = '#6D28D9';
        primaryBtn.style.transform = 'translateY(-1px)';
      };
      primaryBtn.onmouseout = () => {
        primaryBtn.style.background = '#7C3AED';
        primaryBtn.style.transform = 'translateY(0)';
      };
    }

    // Test All Variations button (Green primary)
    const testVariationsBtn = document.getElementById('test-all-variations');
    if (testVariationsBtn) {
      testVariationsBtn.onmouseover = () => {
        testVariationsBtn.style.background = '#047857';
        testVariationsBtn.style.transform = 'translateY(-1px)';
      };
      testVariationsBtn.onmouseout = () => {
        testVariationsBtn.style.background = '#059669';
        testVariationsBtn.style.transform = 'translateY(0)';
      };
    }

    // Pause button (Red primary)
    const pauseBtn = document.getElementById('pause-testing');
    if (pauseBtn) {
      pauseBtn.onmouseover = () => {
        pauseBtn.style.background = '#b91c1c';
        pauseBtn.style.transform = 'translateY(-1px)';
      };
      pauseBtn.onmouseout = () => {
        pauseBtn.style.background = '#dc2626';
        pauseBtn.style.transform = 'translateY(0)';
      };
    }

    // Secondary buttons
    const secondaryBtns = ['test-first-element', 'test-all-elements', 'export-results', 'clear-log'];
    secondaryBtns.forEach(btnId => {
      const btn = document.getElementById(btnId);
      if (btn) {
        btn.onmouseover = () => {
          btn.style.background = '#6b7280';
          btn.style.color = 'white';
        };
        btn.onmouseout = () => {
          btn.style.background = 'transparent';
          btn.style.color = '#6b7280';
        };
      }
    });

    // Listen for State Change button (Green primary)
    const listenBtn = document.getElementById('listen-state-change');
    if (listenBtn) {
      listenBtn.onmouseover = () => {
        listenBtn.style.background = '#047857';
        listenBtn.style.transform = 'translateY(-1px)';
      };
      listenBtn.onmouseout = () => {
        listenBtn.style.background = '#059669';
        listenBtn.style.transform = 'translateY(0)';
      };
    }

    // Copy Summary button (Special green secondary)
    const copySummaryBtn = document.getElementById('copy-summary');
    if (copySummaryBtn) {
      copySummaryBtn.onmouseover = () => {
        copySummaryBtn.style.background = '#059669';
        copySummaryBtn.style.color = 'white';
      };
      copySummaryBtn.onmouseout = () => {
        copySummaryBtn.style.background = 'transparent';
        copySummaryBtn.style.color = '#059669';
      };
    }

    // Copy Log button (Special purple secondary)
    const copyLogBtn = document.getElementById('copy-log');
    if (copyLogBtn) {
      copyLogBtn.onmouseover = () => {
        copyLogBtn.style.background = '#7C3AED';
        copyLogBtn.style.color = 'white';
      };
      copyLogBtn.onmouseout = () => {
        copyLogBtn.style.background = 'transparent';
        copyLogBtn.style.color = '#7C3AED';
      };
    }

    // Clean DOM button (Special orange secondary)
    const cleanDOMBtn = document.getElementById('clean-dom');
    if (cleanDOMBtn) {
      cleanDOMBtn.onmouseover = () => {
        cleanDOMBtn.style.background = '#f59e0b';
        cleanDOMBtn.style.color = 'white';
        cleanDOMBtn.style.transform = 'translateY(-1px)';
      };
      cleanDOMBtn.onmouseout = () => {
        cleanDOMBtn.style.background = 'transparent';
        cleanDOMBtn.style.color = '#f59e0b';
        cleanDOMBtn.style.transform = 'translateY(0)';
      };
    }
  }

  function setupEventListeners() {
    document.getElementById('analyze-structure').onclick = analyzeStructure;
    document.getElementById('test-all-variations').onclick = testAllSelectorVariations;
    document.getElementById('pause-testing').onclick = pauseTesting;
    document.getElementById('test-first-element').onclick = testFirstElement;
    document.getElementById('test-all-elements').onclick = testAllElements;
    document.getElementById('export-results').onclick = exportResults;
    document.getElementById('copy-summary').onclick = copySummary;
    document.getElementById('copy-log').onclick = copyFullLog;
    document.getElementById('clear-log').onclick = clearLogOnly;
    document.getElementById('clean-dom').onclick = cleanDOMAndPause;
    document.getElementById('listen-state-change').onclick = startListeningForStateChange;
    
    // Setup toggle all shortcodes button
    document.getElementById('toggle-all-shortcodes').onclick = toggleAllShortcodes;
    
    // Setup thread count buttons
    setupThreadButtons();
    
    // Setup shortcode button listeners
    setupShortcodeButtons();
    
    // Setup settings auto-save
    setupSettingsAutoSave();
    
    // Load saved settings after a small delay to ensure DOM is ready
    setTimeout(() => {
      loadSavedSettings();
    }, 100);
    
    // Initialize clean detection state
    window._debuggerDetectedUrls = [];
  }



  function generateOptimalSelector(element) {
    // Try different selector strategies in order of preference
    
    // 1. ID selector (most specific)
    if (element.id) {
      return `#${element.id}`;
    }

    // 2. Class selector with tag
    if (element.className && typeof element.className === 'string') {
      const classes = element.className.trim().split(/\s+/).filter(c => c);
      if (classes.length > 0) {
        // Try just the first class with tag
        const selector = `${element.tagName.toLowerCase()}.${classes[0]}`;
        if (document.querySelectorAll(selector).length === 1) {
          return selector;
        }
        
        // Try with multiple classes
        const multiClassSelector = `${element.tagName.toLowerCase()}.${classes.join('.')}`;
        if (document.querySelectorAll(multiClassSelector).length <= 5) {
          return multiClassSelector;
        }
      }
    }

    // 3. Attribute-based selectors
    const attributes = ['role', 'type', 'name', 'data-testid', 'aria-label'];
    for (const attr of attributes) {
      const value = element.getAttribute(attr);
      if (value) {
        const selector = `${element.tagName.toLowerCase()}[${attr}="${value}"]`;
        if (document.querySelectorAll(selector).length <= 3) {
          return selector;
        }
      }
    }

    // 4. Text-based selector for buttons/links
    if (['BUTTON', 'A', 'SPAN'].includes(element.tagName)) {
      const text = element.textContent.trim();
      if (text && text.length < 50) {
        // This would be used in a text-based search, but return a class-based fallback
        if (element.className) {
          const classes = element.className.trim().split(/\s+/).filter(c => c);
          if (classes.length > 0) {
            return `.${classes[0]}`;
          }
        }
      }
    }

    // 5. Fallback to nth-child selector
    const parent = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children).filter(child => child.tagName === element.tagName);
      const index = siblings.indexOf(element) + 1;
      return `${parent.tagName.toLowerCase()} > ${element.tagName.toLowerCase()}:nth-child(${index})`;
    }

    // 6. Final fallback
    return element.tagName.toLowerCase();
  }

  // Enhanced function to capture comprehensive element data
  function captureElementData(element) {
    const data = {
      tagName: element.tagName,
      id: element.id || null,
      className: element.className || null,
      classes: element.className ? element.className.trim().split(/\s+/).filter(c => c) : [],
      textContent: element.textContent.trim(),
      attributes: {},
      computedStyle: {},
      position: {
        offsetTop: element.offsetTop,
        offsetLeft: element.offsetLeft,
        offsetWidth: element.offsetWidth,
        offsetHeight: element.offsetHeight
      }
    };

    // Capture all attributes
    for (const attr of element.attributes) {
      data.attributes[attr.name] = attr.value;
    }

    // Capture relevant computed styles
    const style = window.getComputedStyle(element);
    const styleProps = ['display', 'position', 'visibility', 'cursor', 'background-color', 'color'];
    styleProps.forEach(prop => {
      data.computedStyle[prop] = style[prop];
    });

    return data;
  }

  // Generate multiple selector variations from captured element data
  function generateSelectorVariations(elementData) {
    const variations = [];
    const tag = elementData.tagName.toLowerCase();
    const classes = elementData.classes;
    
    // 1. ID selector (if available)
    if (elementData.id) {
      variations.push({
        selector: `#${elementData.id}`,
        specificity: 100,
        description: 'ID selector (most specific)'
      });
      
      variations.push({
        selector: `${tag}#${elementData.id}`,
        specificity: 101,
        description: 'Tag + ID selector'
      });
    }

    // 2. Single class selectors
    classes.forEach((cls, index) => {
      variations.push({
        selector: `.${cls}`,
        specificity: 10 + index,
        description: `Single class selector (${cls})`
      });
      
      variations.push({
        selector: `${tag}.${cls}`,
        specificity: 11 + index,
        description: `Tag + single class (${cls})`
      });
    });

    // 3. Multiple class combinations
    if (classes.length > 1) {
      // All classes
      variations.push({
        selector: `.${classes.join('.')}`,
        specificity: 20,
        description: 'All classes combined'
      });
      
      variations.push({
        selector: `${tag}.${classes.join('.')}`,
        specificity: 21,
        description: 'Tag + all classes'
      });

      // First 2 classes
      if (classes.length >= 2) {
        variations.push({
          selector: `.${classes.slice(0, 2).join('.')}`,
          specificity: 15,
          description: 'First 2 classes'
        });
        
        variations.push({
          selector: `${tag}.${classes.slice(0, 2).join('.')}`,
          specificity: 16,
          description: 'Tag + first 2 classes'
        });
      }

      // First 3 classes
      if (classes.length >= 3) {
        variations.push({
          selector: `.${classes.slice(0, 3).join('.')}`,
          specificity: 17,
          description: 'First 3 classes'
        });
        
        variations.push({
          selector: `${tag}.${classes.slice(0, 3).join('.')}`,
          specificity: 18,
          description: 'Tag + first 3 classes'
        });
      }
    }

    // 4. Attribute-based selectors
    const importantAttrs = ['role', 'type', 'name', 'data-testid', 'aria-label', 'title', 'href'];
    importantAttrs.forEach(attrName => {
      if (elementData.attributes[attrName]) {
        const value = elementData.attributes[attrName];
        variations.push({
          selector: `[${attrName}="${value}"]`,
          specificity: 30,
          description: `Attribute selector (${attrName})`
        });
        
        variations.push({
          selector: `${tag}[${attrName}="${value}"]`,
          specificity: 31,
          description: `Tag + attribute (${attrName})`
        });

        // Combine with first class if available
        if (classes.length > 0) {
          variations.push({
            selector: `.${classes[0]}[${attrName}="${value}"]`,
            specificity: 32,
            description: `Class + attribute (${attrName})`
          });
        }
      }
    });

    // 5. Text-based selectors (for buttons/links with short text)
    if (['BUTTON', 'A', 'SPAN'].includes(elementData.tagName) && 
        elementData.textContent && 
        elementData.textContent.length < 50 && 
        elementData.textContent.length > 0) {
      
      variations.push({
        selector: `${tag}:contains("${elementData.textContent}")`,
        specificity: 25,
        description: 'Text content selector (jQuery-style, may need custom implementation)',
        note: 'Requires custom :contains implementation or text-based matching'
      });
    }

    // 6. Combined selectors with parent context
    if (classes.length > 0) {
      // Add some parent context variations
      variations.push({
        selector: `div .${classes[0]}`,
        specificity: 5,
        description: 'Parent div + first class'
      });
      
      variations.push({
        selector: `td .${classes[0]}`,
        specificity: 6,
        description: 'Parent td + first class'
      });
      
      variations.push({
        selector: `tr .${classes[0]}`,
        specificity: 7,
        description: 'Parent tr + first class'
      });
    }

    // Sort by specificity (higher is more specific)
    variations.sort((a, b) => b.specificity - a.specificity);
    
    return variations;
  }

  function generateSuccessIndicators(element) {
    // Generate appropriate success indicators based on the element type and context
    const indicators = [];
    
    // Common success indicators
    indicators.push('[role="dialog"]', '[role="alertdialog"]');
    
    // If it's an unsubscribe button, look for confirmation dialogs
    const text = element.textContent.toLowerCase();
    if (text.includes('unsubscribe')) {
      indicators.push(
        '.popup', '.modal', '.confirmation',
        '[class*="confirm"]', '[class*="dialog"]', '[class*="modal"]',
        '[aria-label*="confirm"]', '[aria-label*="unsubscribe"]'
      );
    }
    
    // If it's a form button, look for form submission indicators
    if (element.tagName === 'BUTTON' || element.type === 'submit') {
      indicators.push(
        '.success', '.error', '.message', '.notification',
        '[class*="success"]', '[class*="error"]', '[class*="message"]'
      );
    }
    
    // Look for elements that might appear in the same container
    const container = element.closest('div, section, article, main');
    if (container) {
      // Add container-specific selectors
      const containerClass = container.className;
      if (containerClass) {
        const firstClass = containerClass.split(' ')[0];
        if (firstClass) {
          indicators.push(`.${firstClass} .popup`, `.${firstClass} .modal`);
        }
      }
    }
    
    // Remove duplicates and join
    return [...new Set(indicators)].join(', ');
  }

  function log(message, color = '#d1d5db', highlight = false) {
    const timestamp = new Date().toLocaleTimeString();
    const output = document.getElementById('debug-output');
    
    if (output) {
      const div = document.createElement('div');
      div.style.cssText = `
        color: ${color}; 
        margin: 2px 0; 
        ${highlight ? 'background: rgba(124, 58, 237, 0.2); padding: 4px 8px; border-radius: 4px; font-weight: bold;' : ''}
      `;
      div.innerHTML = `<span style="color: #6b7280;">[${timestamp}]</span> ${message}`;
      output.appendChild(div);
      output.scrollTop = output.scrollHeight;
    }
    
    console.log(`[Universal Debugger] ${message}`);
  }

  function clearLogOnly() {
    const output = document.getElementById('debug-output');
    if (output) {
      output.innerHTML = '<div style="color: #6b7280;">Log cleared. Settings preserved.</div>';
    }
    debugSession.results = [];
    debugSession.successfulMethods = [];
    // Reset tracking variables
    testingResults = [];
    successfulMethods.clear();
  }

  function clearAll() {
    // Clear log
    clearLogOnly();
    
    // Clear all form fields
    document.getElementById('html-structure').value = '';
    document.getElementById('target-selector').value = '';
    document.getElementById('success-indicators').value = '';
    
    // Reset all shortcode buttons
    const shortcodeBtns = document.querySelectorAll('.shortcode-btn');
    shortcodeBtns.forEach(btn => {
      btn.style.background = 'transparent';
      btn.style.color = '#6b7280';
      btn.style.borderColor = '#374151';
    });
    
    // Clear saved settings
    try {
      localStorage.removeItem('universalDebugger_settings');
      log('🗑️ All settings and data cleared!', '#f59e0b', true);
    } catch (e) {
      log('Settings cleared from session', '#f59e0b');
    }
    
    // Reset global state
    window._debuggerInitialUrl = undefined;
    window._debuggerInitialTitle = undefined;
    window._debuggerInitialContentLength = undefined;
  }

  function cleanDOMAndPause() {
    log('🧹 CLEAN DOM TRIGGERED - Pausing all testing immediately!', '#f59e0b', true);
    
    // IMMEDIATELY pause all testing
    testingPaused = true;
    
    // Update pause button state
    const pauseBtn = document.getElementById('pause-testing');
    if (pauseBtn) {
      pauseBtn.textContent = 'Resume Testing';
      pauseBtn.style.background = '#059669';
      pauseBtn.style.borderColor = '#059669';
    }
    
    // Update panel status
    updatePanelStatus('paused', 'Testing paused - DOM cleaning in progress...');
    
    log('⏸️ Testing PAUSED immediately', '#f59e0b', true);
    log('🧹 Starting comprehensive DOM cleanup...', '#f59e0b');
    
    // Start DOM cleanup
    performComprehensiveDOMCleanup();
  }

  function performComprehensiveDOMCleanup() {
    let cleanupCount = 0;
    
    try {
      // 1. Remove all modal/dialog overlays
      const modals = document.querySelectorAll('[role="dialog"], [role="alertdialog"], .modal, [class*="modal"], [id*="modal"]');
      modals.forEach(modal => {
        modal.remove();
        cleanupCount++;
      });
      if (modals.length > 0) {
        log(`🗑️ Removed ${modals.length} modal/dialog elements`, '#6366f1');
      }
      
      // 2. Remove all popup/overlay elements
      const popups = document.querySelectorAll('.popup, [class*="popup"], .overlay, [class*="overlay"], .dropdown, [class*="dropdown"]');
      popups.forEach(popup => {
        if (!popup.closest('#universal-click-debugger')) { // Don't remove debugger elements
          popup.remove();
          cleanupCount++;
        }
      });
      if (popups.length > 0) {
        log(`🗑️ Removed ${popups.length} popup/overlay elements`, '#6366f1');
      }
      
      // 3. Remove notification/alert elements
      const notifications = document.querySelectorAll('.notification, .toast, .alert, [role="alert"], [class*="notification"], [class*="toast"], [class*="alert"]');
      notifications.forEach(notif => {
        if (!notif.closest('#universal-click-debugger')) {
          notif.remove();
          cleanupCount++;
        }
      });
      if (notifications.length > 0) {
        log(`🗑️ Removed ${notifications.length} notification/alert elements`, '#6366f1');
      }
      
      // 4. Remove loading/spinner elements
      const loaders = document.querySelectorAll('.loading, .spinner, [class*="loading"], [class*="spinner"], [class*="loader"]');
      loaders.forEach(loader => {
        if (!loader.closest('#universal-click-debugger')) {
          loader.remove();
          cleanupCount++;
        }
      });
      if (loaders.length > 0) {
        log(`🗑️ Removed ${loaders.length} loading/spinner elements`, '#6366f1');
      }
      
      // 5. Reset all form elements to initial state
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        if (!form.closest('#universal-click-debugger')) {
          form.reset();
        }
      });
      if (forms.length > 0) {
        log(`🔄 Reset ${forms.length} forms to initial state`, '#6366f1');
      }
      
      // 6. Clear all text inputs and textareas
      const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="search"], textarea');
      let inputsCleared = 0;
      inputs.forEach(input => {
        if (!input.closest('#universal-click-debugger') && input.value.trim()) {
          input.value = '';
          inputsCleared++;
        }
      });
      if (inputsCleared > 0) {
        log(`🧹 Cleared ${inputsCleared} text inputs`, '#6366f1');
      }
      
      // 7. Reset aria-expanded states
      const expandedElements = document.querySelectorAll('[aria-expanded="true"]');
      expandedElements.forEach(el => {
        if (!el.closest('#universal-click-debugger')) {
          el.setAttribute('aria-expanded', 'false');
        }
      });
      if (expandedElements.length > 0) {
        log(`🔄 Reset ${expandedElements.length} aria-expanded states`, '#6366f1');
      }
      
      // 8. Remove focus from all elements except debugger
      const focusedElement = document.activeElement;
      if (focusedElement && !focusedElement.closest('#universal-click-debugger')) {
        focusedElement.blur();
        log(`👁️ Removed focus from active element`, '#6366f1');
      }
      
      // 9. Reset scroll positions to top
      window.scrollTo(0, 0);
      const scrollableElements = document.querySelectorAll('[class*="scroll"], .overflow-auto, .overflow-y-auto');
      scrollableElements.forEach(el => {
        if (!el.closest('#universal-click-debugger')) {
          el.scrollTop = 0;
          el.scrollLeft = 0;
        }
      });
      log(`⬆️ Reset scroll positions to top`, '#6366f1');
      
      // 10. Clear any temporary highlight classes
      const highlighted = document.querySelectorAll('[class*="highlight"], [class*="selected"], [class*="active"]');
      highlighted.forEach(el => {
        if (!el.closest('#universal-click-debugger')) {
          el.classList.remove(...Array.from(el.classList).filter(cls => 
            cls.includes('highlight') || cls.includes('selected') || cls.includes('active')
          ));
          cleanupCount++;
        }
      });
      
      // 11. Reset debugger tracking variables
      window._debuggerNewTabActivity = 0;
      window._debuggerDetectedUrls = [];
      successfulMethods.clear();
      testingResults = [];
      
      log(`✅ DOM cleanup complete! Removed/reset ${cleanupCount} elements`, '#10b981', true);
      log(`🔄 Reset all tracking variables`, '#10b981');
      log(`⏸️ Testing remains PAUSED - click Resume to continue`, '#f59e0b', true);
      
      // Update panel status
      updatePanelStatus('ready', 'DOM cleaned - ready for fresh testing');
      
    } catch (error) {
      log(`❌ DOM cleanup error: ${error.message}`, '#ef4444');
      log(`⚠️ Partial cleanup may have occurred`, '#f59e0b');
    }
  }

  function setupShortcodeButtons() {
    const shortcodeBtns = document.querySelectorAll('.shortcode-btn');
    
    shortcodeBtns.forEach(btn => {
      btn.onclick = function() {
        const shortcode = this.dataset.shortcode;
        toggleShortcode(shortcode, this);
      };
      
      // Add hover effects
      btn.onmouseover = function() {
        if (this.style.background === 'transparent' || !this.style.background) {
          this.style.background = '#374151';
          this.style.color = '#d1d5db';
        }
      };
      
      btn.onmouseout = function() {
        if (this.style.background === 'rgb(55, 65, 81)') { // Not selected
          this.style.background = 'transparent';
          this.style.color = '#6b7280';
        }
      };
    });
  }

  function toggleShortcode(shortcode, buttonElement) {
    const input = document.getElementById('success-indicators');
    const currentValue = input.value.trim();
    
    // Parse current shortcodes
    const currentShortcodes = currentValue ? currentValue.split(',').map(s => s.trim()).filter(s => s) : [];
    
    if (currentShortcodes.includes(shortcode)) {
      // Remove shortcode
      const newShortcodes = currentShortcodes.filter(s => s !== shortcode);
      input.value = newShortcodes.join(', ');
      
      // Update button style (deselected)
      buttonElement.style.background = 'transparent';
      buttonElement.style.color = '#6b7280';
      buttonElement.style.borderColor = '#374151';
      
      log(`➖ Removed ${shortcode} from success indicators`, '#f59e0b');
    } else {
      // Add shortcode
      const newShortcodes = [...currentShortcodes, shortcode];
      input.value = newShortcodes.join(', ');
      
      // Update button style (selected)
      buttonElement.style.background = '#7C3AED';
      buttonElement.style.color = 'white';
      buttonElement.style.borderColor = '#7C3AED';
      
      log(`➕ Added ${shortcode} to success indicators`, '#10b981');
    }
    
    // Save settings and update toggle button text
    saveSettings();
    updateToggleAllButtonText();
  }

  function setupSettingsAutoSave() {
    // Save settings when fields change
    const htmlStructure = document.getElementById('html-structure');
    const targetSelector = document.getElementById('target-selector');
    const successIndicators = document.getElementById('success-indicators');
    const expectedPayload = document.getElementById('expected-payload');
    
    [htmlStructure, targetSelector, expectedPayload].forEach(field => {
      if (field) {
        field.addEventListener('input', saveSettings);
        field.addEventListener('change', saveSettings);
      }
    });
    
    // Special handling for success indicators to also update toggle button and shortcode states
    if (successIndicators) {
      successIndicators.addEventListener('input', () => {
        saveSettings();
        updateShortcodeButtonStates();
      });
      successIndicators.addEventListener('change', () => {
        saveSettings();
        updateShortcodeButtonStates();
      });
    }
  }

  function saveSettings() {
    try {
      const htmlStructure = document.getElementById('html-structure');
      const targetSelector = document.getElementById('target-selector');
      const successIndicators = document.getElementById('success-indicators');
      const expectedPayload = document.getElementById('expected-payload');
      
      // Only save if core elements exist (expectedPayload is optional)
      if (htmlStructure && targetSelector && successIndicators) {
        const settings = {
          htmlStructure: htmlStructure.value,
          targetSelector: targetSelector.value,
          successIndicators: successIndicators.value,
          expectedPayload: expectedPayload ? expectedPayload.value : '',
          timestamp: Date.now()
        };
        
        localStorage.setItem('universalDebugger_settings', JSON.stringify(settings));
      }
    } catch (e) {
      // Silently fail if localStorage is not available
      console.log('Universal Debugger: Could not save settings:', e);
    }
  }

  function loadSavedSettings() {
    try {
      const saved = localStorage.getItem('universalDebugger_settings');
      if (saved) {
        const settings = JSON.parse(saved);
        
        // Ensure elements exist before trying to set values
        const htmlStructure = document.getElementById('html-structure');
        const targetSelector = document.getElementById('target-selector');
        const successIndicators = document.getElementById('success-indicators');
        const expectedPayload = document.getElementById('expected-payload');
        
        if (htmlStructure && targetSelector && successIndicators) {
          // Load form values
          htmlStructure.value = settings.htmlStructure || '';
          targetSelector.value = settings.targetSelector || '';
          successIndicators.value = settings.successIndicators || '';
          if (expectedPayload) {
            expectedPayload.value = settings.expectedPayload || '';
          }
          
          // Update shortcode button states
          updateShortcodeButtonStates();
          
          // Show restore message if any settings were actually restored
          if (settings.targetSelector || settings.successIndicators || settings.htmlStructure || settings.expectedPayload) {
            const timeDiff = Date.now() - (settings.timestamp || 0);
            const timeAgo = timeDiff < 60000 ? 'just now' : 
                           timeDiff < 3600000 ? Math.floor(timeDiff / 60000) + ' min ago' :
                           Math.floor(timeDiff / 3600000) + ' hr ago';
            
            log(`🔄 Settings restored from ${timeAgo}`, '#10b981', true);
          }
        }
      }
    } catch (e) {
      // Silently fail if localStorage is not available or data is corrupted
      console.log('Universal Debugger: Could not load saved settings:', e);
    }
  }

  function updateShortcodeButtonStates() {
    const successIndicators = document.getElementById('success-indicators').value.trim();
    const shortcodes = successIndicators ? successIndicators.split(',').map(s => s.trim().toUpperCase()) : [];
    
    const shortcodeBtns = document.querySelectorAll('.shortcode-btn');
    shortcodeBtns.forEach(btn => {
      const shortcode = btn.dataset.shortcode;
      
      if (shortcodes.includes(shortcode)) {
        // Selected state
        btn.style.background = '#7C3AED';
        btn.style.color = 'white';
        btn.style.borderColor = '#7C3AED';
      } else {
        // Unselected state
        btn.style.background = 'transparent';
        btn.style.color = '#6b7280';
        btn.style.borderColor = '#374151';
      }
    });
    
    // Update toggle all button text
    updateToggleAllButtonText();
  }

  function toggleAllShortcodes() {
    const input = document.getElementById('success-indicators');
    const toggleBtn = document.getElementById('toggle-all-shortcodes');
    const shortcodeBtns = document.querySelectorAll('.shortcode-btn');
    
    // Get all available shortcodes
    const allShortcodes = Array.from(shortcodeBtns).map(btn => btn.dataset.shortcode);
    
    // Check current state - if any are selected, we'll deselect all, otherwise select all
    const currentValue = input.value.trim();
    const currentShortcodes = currentValue ? currentValue.split(',').map(s => s.trim().toUpperCase()).filter(s => s) : [];
    const hasAnySelected = allShortcodes.some(code => currentShortcodes.includes(code));
    
    if (hasAnySelected) {
      // Deselect all - remove all shortcodes from the input
      const nonShortcodes = currentShortcodes.filter(code => !allShortcodes.includes(code));
      input.value = nonShortcodes.join(', ');
      
      // Update all button styles to deselected
      shortcodeBtns.forEach(btn => {
        btn.style.background = 'transparent';
        btn.style.color = '#6b7280';
        btn.style.borderColor = '#374151';
      });
      
      log('🔄 Deselected all shortcodes', '#f59e0b');
    } else {
      // Select all - add all shortcodes to the input
      const existingNonShortcodes = currentShortcodes.filter(code => !allShortcodes.includes(code));
      const newShortcodes = [...existingNonShortcodes, ...allShortcodes];
      input.value = newShortcodes.join(', ');
      
      // Update all button styles to selected
      shortcodeBtns.forEach(btn => {
        btn.style.background = '#7C3AED';
        btn.style.color = 'white';
        btn.style.borderColor = '#7C3AED';
      });
      
      log('✅ Selected all shortcodes', '#10b981');
    }
    
    // Save settings and update button text
    saveSettings();
    updateToggleAllButtonText();
  }

  function updateToggleAllButtonText() {
    const toggleBtn = document.getElementById('toggle-all-shortcodes');
    const shortcodeBtns = document.querySelectorAll('.shortcode-btn');
    const allShortcodes = Array.from(shortcodeBtns).map(btn => btn.dataset.shortcode);
    
    const input = document.getElementById('success-indicators');
    const currentValue = input.value.trim();
    const currentShortcodes = currentValue ? currentValue.split(',').map(s => s.trim().toUpperCase()).filter(s => s) : [];
    const hasAnySelected = allShortcodes.some(code => currentShortcodes.includes(code));
    
    if (hasAnySelected) {
      toggleBtn.textContent = 'Deselect All';
      toggleBtn.style.background = '#f59e0b';
      toggleBtn.style.borderColor = '#f59e0b';
      toggleBtn.style.color = 'white';
    } else {
      toggleBtn.textContent = 'Select All';
      toggleBtn.style.background = 'transparent';
      toggleBtn.style.borderColor = '#f59e0b';
      toggleBtn.style.color = '#f59e0b';
    }
  }

  // Thread count management for concurrent testing
  let currentThreadCount = 1; // Default to sequential (1 thread)
  
  function setupThreadButtons() {
    const threadBtns = document.querySelectorAll('.thread-btn');
    
    threadBtns.forEach(btn => {
      btn.onclick = function() {
        const threadCount = parseInt(this.dataset.threads);
        setThreadCount(threadCount);
      };
      
      // Add hover effects
      btn.onmouseover = function() {
        if (this.style.background === 'transparent' || !this.style.background.includes('#7C3AED')) {
          this.style.background = '#374151';
          this.style.color = '#d1d5db';
        }
      };
      
      btn.onmouseout = function() {
        if (this.style.background === 'rgb(55, 65, 81)') { // Not selected
          this.style.background = 'transparent';
          this.style.color = '#6b7280';
        }
      };
    });
    
    // Load saved thread count
    loadThreadCount();
  }
  
  function setThreadCount(count) {
    currentThreadCount = count;
    
    // Update button styles
    const threadBtns = document.querySelectorAll('.thread-btn');
    threadBtns.forEach(btn => {
      const btnCount = parseInt(btn.dataset.threads);
      if (btnCount === count) {
        btn.style.background = '#7C3AED';
        btn.style.color = 'white';
        btn.style.borderColor = '#7C3AED';
      } else {
        btn.style.background = 'transparent';
        btn.style.color = '#6b7280';
        btn.style.borderColor = '#374151';
      }
    });
    
    // Update status display
    const statusElement = document.getElementById('concurrent-status');
    if (statusElement) {
      if (count === 1) {
        statusElement.textContent = 'Sequential Mode';
        statusElement.style.color = '#6b7280';
      } else {
        statusElement.textContent = `Concurrent Mode (${count} threads)`;
        statusElement.style.color = '#10b981';
      }
    }
    
    // Save thread count
    saveThreadCount(count);
    
    log(`🔧 Thread count set to ${count} (${count === 1 ? 'Sequential' : 'Concurrent'} mode)`, '#f59e0b');
  }
  
  function saveThreadCount(count) {
    try {
      localStorage.setItem('universalDebugger_threadCount', count.toString());
    } catch (e) {
      debugLog('⚠️ Could not save thread count:', e.message);
    }
  }
  
  function loadThreadCount() {
    try {
      const saved = localStorage.getItem('universalDebugger_threadCount');
      if (saved) {
        const count = parseInt(saved);
        if (count >= 1 && count <= 10) {
          setThreadCount(count);
          return;
        }
      }
    } catch (e) {
      debugLog('⚠️ Could not load thread count:', e.message);
    }
    // Default to 1 thread (sequential)
    setThreadCount(1);
  }

  // Enhanced new tab/window detection system
  function setupNewTabDetection() {
    // Store original window.open to detect programmatic new tabs
    const originalWindowOpen = window.open;
    let newTabDetected = false;
    let lastClickTime = 0;
    
    // Override window.open to detect when new tabs are opened
    window.open = function(...args) {
      console.log('🔍 New tab/window opened via window.open()');
      window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + 1;
      newTabDetected = true;
      return originalWindowOpen.apply(this, args);
    };
    
    // Enhanced click detection - catch ANY click that might open new tab
    document.addEventListener('click', function(event) {
      lastClickTime = Date.now();
      const target = event.target;
      const closestLink = target.closest('a');
      
      // Log all clicks for debugging
      console.log('🔍 Click detected on:', target.tagName, target.className, target);
      
      if (closestLink) {
        const href = closestLink.getAttribute('href');
        const targetAttr = closestLink.getAttribute('target');
        
        console.log('🔍 Link found:', href, 'target:', targetAttr);
        
        // Check if link will open in new tab/window
        if (targetAttr === '_blank' || 
            targetAttr === '_new' ||
            event.ctrlKey || 
            event.metaKey || 
            event.shiftKey ||
            event.button === 1) { // Middle click
          
          console.log('🔍 Link click detected that may open new tab:', href);
          window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + 1;
          newTabDetected = true;
        }
      }
      
             // IMMEDIATE: Set new tab activity for ANY click during testing
       if (window._debuggerTesting) {
         console.log('🔍 Click during testing - assuming new tab activity');
         window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + 1;
         newTabDetected = true;
         
                 // Also trigger a delayed cross-page check and shared counter check
        setTimeout(() => {
          const crossPageActivity = checkCrossPageActivity();
          if (crossPageActivity > 0) {
            console.log('🌐 Additional cross-page activity detected after click!');
            window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + crossPageActivity;
          }
          
          // Check shared localStorage counter for detailed detection info
          const crossPageSession = window._debuggerCrossPageSession;
          if (crossPageSession) {
            const sharedCounter = 'debugger_tab_activity_' + crossPageSession;
            const sharedDetections = JSON.parse(localStorage.getItem(sharedCounter) || '[]');
            if (sharedDetections.length > 0) {
              console.log(`🔍 Found ${sharedDetections.length} shared detections:`, sharedDetections);
              sharedDetections.forEach(detection => {
                log(`🌐 ${detection.method} opened: ${detection.newUrl}`, '#10b981', true);
              });
              window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + sharedDetections.length;
              // Clear the shared detections after reading
              localStorage.removeItem(sharedCounter);
            }
          }
        }, 5000); // Check 5 seconds after click to allow page loading
       }
      
    }, true);
    
    // Detect focus changes that might indicate tab switching
    let wasHidden = false;
    document.addEventListener('visibilitychange', function() {
      console.log('🔍 Visibility changed:', document.visibilityState, 'newTabDetected:', newTabDetected);
      
      if (document.hidden) {
        wasHidden = true;
        // If page becomes hidden soon after a click, likely opened new tab
        if (newTabDetected || (Date.now() - lastClickTime < 3000)) {
          console.log('🔍 Page hidden after recent click - likely new tab opened');
          window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + 1;
        }
      } else if (wasHidden) {
        // Page became visible again - might indicate returning from new tab
        if (newTabDetected || (Date.now() - lastClickTime < 10000)) {
          console.log('🔍 Visibility change detected - possible return from new tab');
          window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + 1;
          newTabDetected = false; // Reset flag
        }
        wasHidden = false;
      }
    });
    
    // Listen for window blur/focus events
    let windowWasBlurred = false;
    window.addEventListener('blur', function() {
      windowWasBlurred = true;
    });
    
    window.addEventListener('focus', function() {
      if (windowWasBlurred && newTabDetected) {
        console.log('🔍 Window focus returned - possible new tab activity');
        window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + 1;
        windowWasBlurred = false;
        newTabDetected = false;
      }
    });
    
    // Enhanced detection using Page Visibility API changes
    let lastVisibilityState = document.visibilityState;
    let lastUrl = window.location.href;
    
    setInterval(() => {
      // Check for URL changes (navigation in same tab)
      if (window.location.href !== lastUrl) {
        console.log('🔍 URL change detected:', lastUrl, '->', window.location.href);
        window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + 1;
        lastUrl = window.location.href;
      }
      
      // Check for visibility changes
      if (document.visibilityState !== lastVisibilityState) {
        if (lastVisibilityState === 'hidden' && document.visibilityState === 'visible') {
          // Page became visible - user might have returned from another tab
          if (newTabDetected || (Date.now() - lastClickTime < 10000)) {
            console.log('🔍 Page visibility change suggests new tab interaction');
            window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + 1;
          }
        }
        lastVisibilityState = document.visibilityState;
      }
    }, 100);
    
    // Detect potential form submissions to new tabs
    document.addEventListener('submit', function(event) {
      const form = event.target;
      if (form.target === '_blank' || form.target === '_new') {
        console.log('🔍 Form submission to new tab detected');
        window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + 1;
        newTabDetected = true;
      }
    }, true);
    
    console.log('🔍 New tab detection system initialized');
    
    // Listen for messages from new tabs/windows
    window.addEventListener('message', function(event) {
      if (event.data.type === 'debugger_new_tab_detected') {
        console.log('🔍 Received cross-page detection message:', event.data);
        window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + event.data.detectionCount;
        
        log(`🌐 Cross-page detection: ${event.data.method} opened new page: ${event.data.newUrl}`, '#10b981', true);
      } else if (event.data.type === 'debugger_detection_stopped') {
        console.log('🔍 Received detection stopped message:', event.data);
        
        if (event.data.reason === 'duplicate_url') {
          log(`🛑 Cross-page monitoring stopped: Same URL detected ${event.data.count} times`, '#f59e0b', true);
          log(`🔄 URL: ${event.data.url}`, '#9ca3af');
        } else if (event.data.reason === 'unknown_page') {
          log(`🛑 Cross-page monitoring stopped: "Unknown" page detected`, '#f59e0b', true);
          log(`🔄 URL: ${event.data.url}`, '#9ca3af');
        }
        
        // Stop local monitoring as well
        if (window._debuggerCrossPageInterval) {
          clearInterval(window._debuggerCrossPageInterval);
          window._debuggerCrossPageInterval = null;
          log('⛔ Local cross-page monitoring STOPPED', '#f59e0b');
        }
      }
    });
    
    return true;
  }

  // Cross-page detection system - like a "pixel tracker"
  function setupCrossPageDetection() {
    // Create a unique session ID for this testing session
    const sessionId = 'debugger_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    // Store session data in localStorage with timestamp
    const sessionData = {
      sessionId: sessionId,
      startUrl: window.location.href,
      startTime: Date.now(),
      debuggerActive: true,
      popupOpen: document.getElementById('universal-click-debugger') !== null,
      targetSelector: debugSession.targetSelector || '',
      currentMethod: window._debuggerCurrentMethod || 'unknown',
      utm_source: 'universal_debugger',
      utm_medium: 'click_testing',
      utm_campaign: 'element_detection'
    };
    
    try {
      localStorage.setItem('universalDebugger_crossPage', JSON.stringify(sessionData));
      window._debuggerCrossPageSession = sessionId;
      console.log('🔍 Cross-page detection pixel set:', sessionId);
    } catch (e) {
      console.log('🔍 Cross-page detection: localStorage not available');
    }
    
    // Update pixel every few seconds
    const updateInterval = setInterval(() => {
      try {
        const currentData = JSON.parse(localStorage.getItem('universalDebugger_crossPage') || '{}');
        if (currentData.sessionId === sessionId) {
          currentData.popupOpen = document.getElementById('universal-click-debugger') !== null;
          currentData.lastUpdate = Date.now();
          localStorage.setItem('universalDebugger_crossPage', JSON.stringify(currentData));
        } else {
          clearInterval(updateInterval);
        }
      } catch (e) {
        clearInterval(updateInterval);
      }
    }, 1000);
    
    return sessionId;
  }

  // Persistent cross-page monitoring system
  function startCrossPageMonitoring() {
    // Don't start monitoring if testing is paused
    if (testingPaused) {
      console.log('🔍 Cross-page monitoring not started - testing is paused');
      return null;
    }
    
    // Clear any existing interval
    if (window._debuggerCrossPageInterval) {
      clearInterval(window._debuggerCrossPageInterval);
    }
    
    // Check every 2 seconds for cross-page activity
    window._debuggerCrossPageInterval = setInterval(() => {
      // Don't check if testing is paused
      if (testingPaused) {
        return;
      }
      
      const activity = checkCrossPageActivity();
      if (activity > 0) {
        console.log('🌐 Cross-page new tab detected during monitoring!');
        window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + activity;
      }
    }, 2000);

    // Stop monitoring after 60 seconds to prevent memory leaks
    setTimeout(() => {
      if (window._debuggerCrossPageInterval) {
        clearInterval(window._debuggerCrossPageInterval);
        window._debuggerCrossPageInterval = null;
        console.log('🔍 Cross-page monitoring stopped after 60 seconds');
      }
    }, 60000);

    console.log('🔍 Started persistent cross-page monitoring for 60 seconds');
    return window._debuggerCrossPageInterval;
  }

  function checkCrossPageActivity() {
    try {
      // Check if testing is paused - if so, don't detect anything
      if (testingPaused) {
        return 0;
      }
      
      const crossPageData = localStorage.getItem('universalDebugger_crossPage');
      if (!crossPageData) return 0;
      
      const data = JSON.parse(crossPageData);
      
      // Initialize or retrieve detected URLs array
      if (!window._debuggerDetectedUrls) {
        window._debuggerDetectedUrls = [];
      }
      
             // Wait for page to fully load before checking for debugger button
       // Some pages like Google Maps take time to render all elements
       const checkForDebuggerBtn = () => {
         return document.getElementById('universal-debugger-btn') || 
                document.querySelector('[id*="debugger"]') || 
                document.querySelector('button[id$="debugger-btn"]');
       };
       
       // Additional heuristic: if we're on a Google Maps page and there's an active session
       // we can assume this is likely from the debugger testing
       const isGoogleMapsPage = window.location.href.includes('google.com/maps') && 
                                document.querySelector('[data-value="Directions"], [data-value="directions"], .widget-pane') !== null;
      
      // Try multiple times to find the debugger button as it may load later
      let debuggerBtn = checkForDebuggerBtn();
      
      // If no button found immediately, wait a bit for page to load
      if (!debuggerBtn && document.readyState !== 'complete') {
        // Don't wait synchronously, just return 0 for now and check again later
        return 0;
      }
      
      const debuggerPopup = document.getElementById('universal-click-debugger');
      
             // Check if we're on a page with the debugger button (or it's a Google Maps page) but no popup
       // AND there's an active cross-page session
       if ((debuggerBtn || isGoogleMapsPage) && !debuggerPopup && data.debuggerActive) {
        const currentUrl = window.location.href;
        const timeSinceStart = Date.now() - data.startTime;
        
        // Clean URL for duplicate detection (remove utm parameters)
        const cleanUrl = currentUrl.split('?')[0] + (currentUrl.includes('?') ? '?' + 
          currentUrl.split('?')[1].split('&').filter(param => 
            !param.startsWith('utm_')).join('&') : '');
        
        // Check for duplicate URL detection
        const urlCount = window._debuggerDetectedUrls.filter(url => url === cleanUrl).length;
        
                 // Stop detection if we've seen this URL twice or if it contains "unknown"
         if (urlCount >= 2) {
           console.log('🛑 Cross-page detection stopped: Same URL detected twice:', cleanUrl);
           
           // Send message to original session about stopping
           if (window.opener || window.parent !== window) {
             try {
               const message = {
                 type: 'debugger_detection_stopped',
                 reason: 'duplicate_url',
                 url: cleanUrl,
                 count: urlCount
               };
               
               if (window.opener) {
                 window.opener.postMessage(message, '*');
               }
               if (window.parent !== window) {
                 window.parent.postMessage(message, '*');
               }
             } catch (e) {}
           }
           
           // Permanently disable cross-page detection for this session
           data.debuggerActive = false;
           localStorage.setItem('universalDebugger_crossPage', JSON.stringify(data));
           // Clear the interval to stop monitoring
           if (window._debuggerCrossPageInterval) {
             clearInterval(window._debuggerCrossPageInterval);
             window._debuggerCrossPageInterval = null;
           }
           return 0;
         }
         
         if (currentUrl.toLowerCase().includes('unknown')) {
           console.log('🛑 Cross-page detection stopped: "Unknown" page detected:', currentUrl);
           
           // Send message to original session about stopping
           if (window.opener || window.parent !== window) {
             try {
               const message = {
                 type: 'debugger_detection_stopped',
                 reason: 'unknown_page',
                 url: currentUrl
               };
               
               if (window.opener) {
                 window.opener.postMessage(message, '*');
               }
               if (window.parent !== window) {
                 window.parent.postMessage(message, '*');
               }
             } catch (e) {}
           }
           
           // Permanently disable cross-page detection for this session
           data.debuggerActive = false;
           localStorage.setItem('universalDebugger_crossPage', JSON.stringify(data));
           // Clear the interval to stop monitoring
           if (window._debuggerCrossPageInterval) {
             clearInterval(window._debuggerCrossPageInterval);
             window._debuggerCrossPageInterval = null;
           }
           return 0;
         }
        
        // Extended time window for slow-loading pages like Google Maps
        // If we're on a different URL than the start URL, and it's been less than 45 seconds
        // this indicates a new page was opened from the testing
        if (currentUrl !== data.startUrl && timeSinceStart < 45000) {
          
              // Add this URL to detected URLs list
    window._debuggerDetectedUrls.push(cleanUrl);
                   console.log('🔍 Cross-page activity detected!');
     console.log('  🎯 UTM Tracking Details:');
     console.log('    utm_source:', data.utm_source);
     console.log('    utm_medium:', data.utm_medium);
     console.log('    utm_campaign:', data.utm_campaign);
     console.log('    utm_content (method):', data.utm_content);
     console.log('    utm_term (selector):', data.utm_term);
     console.log('  Start URL:', data.startUrl);
     console.log('  Current URL:', currentUrl);
     
     // Log the captured URL prominently in the main debugger log
     log(`🎯 TARGET URL CAPTURED: ${currentUrl}`, '#10b981', true);
     log(`🕐 Captured at: ${new Date().toLocaleTimeString()}`, '#6366f1');
     
     // Also check for URLs in clipboard and page content
     setTimeout(async () => {
       await checkForYouTubeUrls();
     }, 1000);
           console.log('  Time elapsed:', timeSinceStart + 'ms');
           console.log('  Debugger button found:', !!debuggerBtn);
           console.log('  Is Google Maps page:', isGoogleMapsPage);
           console.log('  Popup open:', !!debuggerPopup);
           console.log('  Page ready state:', document.readyState);
          
                     // Don't clear the session immediately - let it detect multiple times
           // Mark detection but keep session active for continued monitoring
           data.detected = true;
           data.detectionTime = Date.now();
           data.detectionCount = (data.detectionCount || 0) + 1;
           
           // Add UTM parameters to track the testing method and source
           const sessionData = {
             ...data,
             utm_source: 'universal_debugger',
             utm_medium: 'click_testing',
             utm_campaign: 'element_detection',
             utm_content: data.currentMethod || 'unknown_method',
             utm_term: data.targetSelector || 'unknown_selector'
           };
           
           localStorage.setItem('universalDebugger_crossPage', JSON.stringify(sessionData));
           
           // Add UTM parameters to the current URL after a delay to allow page to stabilize
           setTimeout(() => {
             try {
               debugLog('%c🔍 UTM PARAMETERS: Starting URL modification process', 'color: #6366f1; font-size: 13px; font-weight: bold;');
               debugLog('🌐 Current URL before UTM addition: ' + window.location.href);
               
               const currentUrl = new URL(window.location.href);
               let urlChanged = false;
               
               debugLog('🔧 Checking existing UTM parameters...');
               
               // Add UTM parameters if they don't exist
               if (!currentUrl.searchParams.has('utm_source')) {
                 currentUrl.searchParams.set('utm_source', 'universal_debugger');
                 urlChanged = true;
                 debugLog('✅ Added utm_source: universal_debugger');
               } else {
                 debugLog('🔄 utm_source already exists: ' + currentUrl.searchParams.get('utm_source'));
               }
               
               if (!currentUrl.searchParams.has('utm_medium')) {
                 currentUrl.searchParams.set('utm_medium', 'click_testing');
                 urlChanged = true;
                 debugLog('✅ Added utm_medium: click_testing');
               } else {
                 debugLog('🔄 utm_medium already exists: ' + currentUrl.searchParams.get('utm_medium'));
               }
               
               if (!currentUrl.searchParams.has('utm_campaign')) {
                 currentUrl.searchParams.set('utm_campaign', 'element_detection');
                 urlChanged = true;
                 debugLog('✅ Added utm_campaign: element_detection');
               } else {
                 debugLog('🔄 utm_campaign already exists: ' + currentUrl.searchParams.get('utm_campaign'));
               }
               
               if (!currentUrl.searchParams.has('utm_content') && data.currentMethod) {
                 currentUrl.searchParams.set('utm_content', data.currentMethod);
                 urlChanged = true;
                 debugLog('✅ Added utm_content: ' + data.currentMethod);
               } else {
                 debugLog('🔄 utm_content check - method: ' + data.currentMethod + ', existing: ' + currentUrl.searchParams.get('utm_content'));
               }
               
               if (!currentUrl.searchParams.has('utm_term') && data.targetSelector) {
                 currentUrl.searchParams.set('utm_term', data.targetSelector);
                 urlChanged = true;
                 debugLog('✅ Added utm_term: ' + data.targetSelector);
               } else {
                 debugLog('🔄 utm_term check - selector: ' + data.targetSelector + ', existing: ' + currentUrl.searchParams.get('utm_term'));
               }
               
               // Update URL without causing a page reload using pushState
               if (urlChanged) {
                 debugLog('%c🔍 UTM PARAMETERS: Updating URL with new parameters', 'color: #6366f1; font-size: 13px; font-weight: bold;');
                 debugLog('🌐 New URL with UTM parameters: ' + currentUrl.toString());
                 
                 history.pushState(null, '', currentUrl.toString());
                 debugLog('🔍 UTM parameters added to URL: ' + currentUrl.toString());
                 debugLog('✅ URL successfully updated with pushState');
               } else {
                 debugLog('📄 No URL changes needed - all UTM parameters already present');
               }
             } catch (e) {
               debugLog('❌ UTM PARAMETERS ERROR: ' + e.message);
               debugLog('🔍 Could not add UTM parameters to URL: ' + e.message);
             }
           }, 3000); // Wait 3 seconds for Google Maps to stabilize
           
           // Feed back detection to original session
           if (window.opener || window.parent !== window) {
             // Try to communicate back to parent/opener window
             try {
               const message = {
                 type: 'debugger_new_tab_detected',
                 detectionCount: data.detectionCount,
                 method: data.currentMethod,
                 selector: data.targetSelector,
                 newUrl: currentUrl
               };
               
               if (window.opener) {
                 window.opener.postMessage(message, '*');
               }
               if (window.parent !== window) {
                 window.parent.postMessage(message, '*');
               }
               
               console.log('🔍 Sent cross-page detection message back to parent:', message);
             } catch (e) {
               console.log('🔍 Could not send message to parent window:', e.message);
             }
           }
           
                       // Also use a shared localStorage counter that the original session can read
            const sharedCounter = 'debugger_tab_activity_' + data.sessionId;
            const sharedDetections = JSON.parse(localStorage.getItem(sharedCounter) || '[]');
            sharedDetections.push({
              timestamp: Date.now(),
              method: data.currentMethod || data.utm_content,
              selector: data.targetSelector || data.utm_term,
              newUrl: currentUrl,
              detectionId: Date.now() + '_' + Math.random().toString(36).substr(2, 5)
            });
            localStorage.setItem(sharedCounter, JSON.stringify(sharedDetections));
           
           return 1;
        }
      }
      
      // Clean up old sessions (older than 75 seconds for slow-loading pages)
      if (data.startTime && (Date.now() - data.startTime) > 75000) {
        console.log('🔍 Cross-page session expired after 75 seconds');
        localStorage.removeItem('universalDebugger_crossPage');
      }
      
    } catch (e) {
      console.log('🔍 Cross-page detection error:', e);
    }
    
    return 0;
  }

  function logSuccessDetails(beforeCounts, afterCounts) {
    // Provide detailed feedback about what type of success was detected
    const changes = [];
    
    Object.keys(afterCounts).forEach(key => {
      const before = beforeCounts[key] || 0;
      const after = afterCounts[key] || 0;
      
      if (after > before) {
        const increase = after - before;
        switch (key.toUpperCase()) {
          case 'NP':
            changes.push(`🌐 New Page/Navigation activity detected (${increase})`);
            break;
          case 'MD':
            changes.push(`📱 Modal/Dialog appeared (${increase})`);
            break;
          case 'NT':
            changes.push(`🔔 Notification/Toast shown (${increase})`);
            break;
          case 'PP':
            changes.push(`🪟 Popup window opened (${increase})`);
            break;
          case 'FM':
            changes.push(`📝 Form submission detected (${increase})`);
            break;
          case 'DD':
            changes.push(`📋 Dropdown menu opened (${increase})`);
            break;
          case 'TB':
            changes.push(`📂 Tab change detected (${increase})`);
            break;
          case 'AC':
            changes.push(`📄 Accordion expanded (${increase})`);
            break;
          case 'LD':
            changes.push(`⏳ Loading indicator appeared (${increase})`);
            break;
          case 'CT':
            changes.push(`📄 Content change detected (${increase})`);
            break;
          case 'ER':
            changes.push(`❌ Error message shown (${increase})`);
            break;
          case 'SU':
            changes.push(`✅ Success message shown (${increase})`);
            break;
          default:
            if (!key.startsWith('_')) {
              changes.push(`🔍 ${key}: +${increase} elements`);
            }
        }
      }
    });
    
    if (changes.length > 0) {
      changes.forEach(change => log(`    ${change}`, '#10b981'));
    }
  }

  function pauseTesting() {
    debugLog('%c⏸️ PAUSE TESTING FUNCTION CALLED', 'color: #f59e0b; font-size: 14px; font-weight: bold; background: rgba(245, 158, 11, 0.1); padding: 4px;');
    debugLog('🔧 Current testing state before toggle: ' + (testingPaused ? 'PAUSED' : 'RUNNING'));
    debugLog('🕐 Pause toggle triggered at: ' + new Date().toLocaleTimeString());
    
    testingPaused = !testingPaused;
    const pauseBtn = document.getElementById('pause-testing');
    
    if (!pauseBtn) {
      debugLog('❌ ERROR: Pause button not found in DOM!');
      return;
    } else {
      debugLog('✅ Pause button found successfully: ' + pauseBtn.id);
    }
    
    if (testingPaused) {
      debugLog('%c⏸️ SWITCHING TO PAUSED STATE', 'color: #f59e0b; font-size: 13px; font-weight: bold;');
      pauseBtn.textContent = 'Resume Testing';
      pauseBtn.style.background = '#059669';
      pauseBtn.style.borderColor = '#059669';
      debugLog('🎨 Pause button styling updated - background: green, text: "Resume Testing"');
      
      log('⏸️ Testing PAUSED', '#f59e0b', true);
      
      // Stop cross-page monitoring when paused
      if (window._debuggerCrossPageInterval) {
        debugLog('🛑 Stopping cross-page monitoring interval (ID: ' + window._debuggerCrossPageInterval + ')');
        clearInterval(window._debuggerCrossPageInterval);
        window._debuggerCrossPageInterval = null;
        log('⛔ Cross-page monitoring STOPPED', '#f59e0b');
        debugLog('✅ Cross-page monitoring interval cleared successfully');
      } else {
        debugLog('📄 No active cross-page monitoring interval to stop');
      }
    } else {
      debugLog('%c▶️ SWITCHING TO RUNNING STATE', 'color: #10b981; font-size: 13px; font-weight: bold;');
      pauseBtn.textContent = 'Pause Testing';
      pauseBtn.style.background = '#dc2626';
      pauseBtn.style.borderColor = '#dc2626';
      debugLog('🎨 Pause button styling updated - background: red, text: "Pause Testing"');
      
      log('▶️ Testing RESUMED', '#10b981', true);
      
      // Restart cross-page monitoring when resumed
      debugLog('🔄 Restarting cross-page monitoring...');
      startCrossPageMonitoring();
      
      // Reset duplicate detection when resuming
      debugLog('🔄 Resetting duplicate URL detection array');
      window._debuggerDetectedUrls = [];
      
      log('▶️ Cross-page monitoring RESTARTED', '#10b981');
      log('🔄 Duplicate URL detection RESET', '#6366f1');
      debugLog('✅ Cross-page monitoring and duplicate detection reset complete');
    }
    
    debugLog('🔧 Final testing state after toggle: ' + (testingPaused ? 'PAUSED' : 'RUNNING'));
    debugLog('✅ Pause testing function execution COMPLETE at: ' + new Date().toLocaleTimeString());
  }

  function copySummary() {
    const summary = generateLLMFriendlySummary();
    
    navigator.clipboard.writeText(summary).then(() => {
      log('📋 LLM-friendly summary copied to clipboard!', '#10b981', true);
      
      // Temporarily change button text
      const btn = document.getElementById('copy-summary');
      const originalText = btn.textContent;
      btn.textContent = 'Copied!';
      btn.style.background = '#10b981';
      btn.style.color = 'white';
      
      setTimeout(() => {
        btn.textContent = originalText;
        btn.style.background = 'transparent';
        btn.style.color = '#059669';
      }, 1500);
    }).catch(err => {
      log('❌ Failed to copy summary: ' + err, '#ef4444');
    });
  }

  function copyFullLog() {
    const output = document.getElementById('debug-output');
    if (!output) {
      log('❌ No log content to copy', '#ef4444');
      return;
    }
    
    // Extract all text content from the log, preserving structure
    let logText = '';
    const logEntries = output.children;
    
    for (let i = 0; i < logEntries.length; i++) {
      const entry = logEntries[i];
      
      // Handle pre-formatted code blocks
      if (entry.querySelector('pre')) {
        const preElement = entry.querySelector('pre');
        logText += preElement.textContent + '\n\n';
      } else {
        // Regular log entry - extract text content
        let entryText = entry.textContent || entry.innerText || '';
        
        // Clean up the text - remove excessive whitespace
        entryText = entryText.replace(/\s+/g, ' ').trim();
        
        if (entryText) {
          logText += entryText + '\n';
        }
      }
    }
    
    // Add header information
    const selector = document.getElementById('target-selector').value.trim();
    const successIndicators = document.getElementById('success-indicators').value.trim();
    const timestamp = new Date().toLocaleString();
    
    const fullLog = `# Universal Click Debugger - Full Log Export
Generated: ${timestamp}

## Configuration
- Target Selector: ${selector}
- Success Indicators: ${successIndicators}

## Debug Log
${logText}

---
End of log export`;
    
    navigator.clipboard.writeText(fullLog).then(() => {
      log('📋 Full log copied to clipboard!', '#7C3AED', true);
      
      // Temporarily change button text
      const btn = document.getElementById('copy-log');
      const originalText = btn.textContent;
      btn.textContent = 'Copied!';
      btn.style.background = '#7C3AED';
      btn.style.color = 'white';
      
      setTimeout(() => {
        btn.textContent = originalText;
        btn.style.background = 'transparent';
        btn.style.color = '#7C3AED';
      }, 1500);
    }).catch(err => {
      log('❌ Failed to copy full log: ' + err, '#ef4444');
    });
  }

  function generateLLMFriendlySummary() {
    const selector = document.getElementById('target-selector').value.trim();
    const successIndicators = document.getElementById('success-indicators').value.trim();
    
    // Extract working methods from debugSession
    const workingMethods = debugSession.successfulMethods || [];
    const uniqueMethods = [...new Set(workingMethods.map(r => r.method))];
    
    // Check for successful variations from selector variations testing
    const hasVariations = debugSession.successfulVariations && debugSession.successfulVariations.length > 0;
    const variationsCount = hasVariations ? debugSession.successfulVariations.length : 0;
    
    // Get captured URLs from localStorage or session data
    const capturedUrls = window._debuggerDetectedUrls || [];
    const currentUrl = window.location.href;
    
    return `# Element Interaction Testing Results

## Target Configuration
- **Selector Tested**: \`${selector}\`
- **Success Indicators**: \`${successIndicators}\`
- **Elements Found**: ${workingMethods.length > 0 ? workingMethods[0].elementsFound || 'N/A' : 'N/A'}
- **Current Page URL**: \`${currentUrl}\`
${capturedUrls.length > 0 ? `- **Captured Target URLs**: ${capturedUrls.length} URL(s) detected\n${capturedUrls.map((url, i) => `  ${i + 1}. \`${url}\``).join('\n')}` : '- **Captured Target URLs**: None detected'}
${variationsCount > 0 ? `- **Working Selector Variations**: ${variationsCount} variations successfully tested` : ''}

## Working Methods Discovered
${uniqueMethods.length > 0 ? uniqueMethods.map(method => `- **${method}**: Successfully triggered target behavior`).join('\n') : '- No working methods found yet'}

${hasVariations ? `## Selector Variations Tested
${debugSession.successfulVariations.map((variation, index) => 
`${index + 1}. **${variation.selector}** (${variation.description})
   - Method: ${variation.workingMethod}
   - Elements found: ${variation.elementsFound}
   - Specificity: ${variation.specificity}`
).join('\n')}` : ''}

## Most Reliable Approach
${workingMethods.length > 0 ? `
**Recommended Method**: ${workingMethods[0].method}
**Selector**: \`${workingMethods[0].selector}\`
**Code Implementation**:
\`\`\`javascript
${generateCodeSnippet(workingMethods[0])}
\`\`\`
` : 'No working methods discovered yet. Continue testing or try different selectors.'}

## Testing Summary
- **Total Attempts**: ${debugSession.results.length || 0}
- **Successful Methods**: ${workingMethods.length}
${variationsCount > 0 ? `- **Working Variations**: ${variationsCount}` : ''}
- **Success Rate**: ${debugSession.results.length > 0 ? Math.round((workingMethods.length / debugSession.results.length) * 100) : 0}%

## Next Steps
${workingMethods.length > 0 ? 
'✅ Working solution found! Use the recommended method above in your automation script.' : 
'❌ No working solution yet. Try:\n- Different selectors (check element structure)\n- Alternative success indicators\n- Manual verification that target element is actually clickable'
}

---
*Generated by Universal Click Debugger - LLM Analysis Ready*`;
  }

  function analyzeStructure() {
    const selector = document.getElementById('target-selector').value.trim();
    const htmlStructure = document.getElementById('html-structure').value.trim();
    
    if (!selector) {
      log('❌ Please enter a target selector', '#ef4444');
      updatePanelStatus('error', 'Please enter a target selector');
      return;
    }
    
    updatePanelStatus('testing', 'Analyzing structure...');
    
    // Validate and clean selector
    const cleanedSelector = validateAndCleanSelector(selector);
    if (!cleanedSelector) {
      return; // Error already logged by validateAndCleanSelector
    }
    
    debugSession.targetSelector = cleanedSelector;
    debugSession.htmlStructure = htmlStructure;
    
    log(`🔍 Analyzing structure for selector: "${cleanedSelector}"`, '#7C3AED', true);
    
    // Find elements matching the selector
    let elements;
    try {
      elements = document.querySelectorAll(cleanedSelector);
    } catch (error) {
      log(`❌ Invalid selector: ${error.message}`, '#ef4444');
      return;
    }
    
    log(`Found ${elements.length} elements matching "${cleanedSelector}"`, '#10b981');
    
    if (elements.length === 0) {
      log('❌ No elements found. Check your selector.', '#ef4444');
      updatePanelStatus('error', 'No elements found with selector');
      return;
    }
    
    // Analyze each element
    elements.forEach((element, index) => {
      log(`\n--- Element ${index + 1} Analysis ---`, '#f59e0b');
      log(`Tag: ${element.tagName}`, '#d1d5db');
      log(`Classes: ${element.className || 'none'}`, '#d1d5db');
      log(`ID: ${element.id || 'none'}`, '#d1d5db');
      log(`Text: "${element.textContent.trim().substring(0, 50)}${element.textContent.trim().length > 50 ? '...' : ''}"`, '#d1d5db');
      log(`Attributes: ${Array.from(element.attributes).map(attr => `${attr.name}="${attr.value}"`).join(', ')}`, '#9ca3af');
      
      // Analyze ancestor tree
      const ancestors = [];
      let current = element;
      let level = 0;
      while (current && level < 10) {
        current = current.parentElement;
        if (current) {
          ancestors.push({
            level: level + 1,
            tag: current.tagName,
            classes: current.className,
            role: current.getAttribute('role'),
            tabindex: current.getAttribute('tabindex'),
            clickable: current.onclick !== null || current.getAttribute('onclick') !== null
          });
        }
        level++;
      }
      
      log(`Ancestor tree (${ancestors.length} levels):`, '#6366f1');
      ancestors.forEach(ancestor => {
        const desc = `  L${ancestor.level}: ${ancestor.tag}${ancestor.classes ? '.' + ancestor.classes.split(' ').slice(0, 2).join('.') : ''}${ancestor.role ? '[role="' + ancestor.role + '"]' : ''}${ancestor.tabindex ? '[tabindex="' + ancestor.tabindex + '"]' : ''}${ancestor.clickable ? ' [onclick]' : ''}`;
        log(desc, '#9ca3af');
      });
    });
    
    // Analyze HTML structure if provided
    if (htmlStructure) {
      log('\n--- HTML Structure Analysis ---', '#f59e0b');
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlStructure;
      
      const clickableElements = tempDiv.querySelectorAll('button, [role="button"], [onclick], [tabindex], a');
      log(`Found ${clickableElements.length} potentially clickable elements in HTML:`, '#6366f1');
      
      clickableElements.forEach((el, index) => {
        log(`  ${index + 1}: ${el.tagName}.${el.className} ${el.getAttribute('role') ? '[role="' + el.getAttribute('role') + '"]' : ''}`, '#9ca3af');
      });
    }
    
    log('\n✅ Structure analysis complete!', '#10b981', true);
    updatePanelStatus('ready', 'Structure analysis complete - ready to test');
  }

  async function testAllSelectorVariations() {
    const selector = document.getElementById('target-selector').value.trim();
    if (!selector) {
      log('❌ Please enter a target selector first.', '#ef4444');
      return;
    }

    // Show pause button and reset pause state
    testingPaused = false;
    const pauseBtn = document.getElementById('pause-testing');
    pauseBtn.style.display = 'inline-block';
    pauseBtn.textContent = 'Pause Testing';
    pauseBtn.style.background = '#dc2626';
    pauseBtn.style.borderColor = '#dc2626';

    // Validate and clean selector
    const cleanedSelector = validateAndCleanSelector(selector);
    if (!cleanedSelector) {
      pauseBtn.style.display = 'none';
      return; // Error already logged by validateAndCleanSelector
    }

    // Find elements matching the selector
    let elements;
    try {
      elements = document.querySelectorAll(cleanedSelector);
    } catch (error) {
      log(`❌ Invalid selector: ${error.message}`, '#ef4444');
      pauseBtn.style.display = 'none';
      return;
    }
    
    if (elements.length === 0) {
      log('❌ No elements found. Check your selector.', '#ef4444');
      pauseBtn.style.display = 'none';
      return;
    }

    // Capture element data from the first matching element
    const elementData = captureElementData(elements[0]);
    const variations = generateSelectorVariations(elementData);
    
    log(`🧪 Testing ${variations.length} selector variations for captured element...`, '#f59e0b', true);
    log(`Element: ${elementData.tagName}.${elementData.classes.join('.')} "${elementData.textContent.substring(0, 30)}${elementData.textContent.length > 30 ? '...' : ''}"`, '#d1d5db');
    
    updatePanelStatus('testing', `Testing ${variations.length} variations...`);
    
    // Check UTM Cleaner settings and provide guidance
    const utmStatus = await checkUTMCleanerSettings();
    
    const successfulVariations = [];
    const workingMethods = {};
    
    // Test each variation
    for (let i = 0; i < variations.length; i++) {
      // Check if testing is paused before each variation
      if (testingPaused) {
        log('⏸️ Testing paused. Click Resume to continue...', '#f59e0b');
        while (testingPaused) {
          await delay(1000); // Wait while paused
        }
        log('▶️ Testing resumed...', '#10b981');
      }

      const variation = variations[i];
      
      log(`\n--- Testing Variation ${i + 1}/${variations.length} ---`, '#f59e0b');
      log(`Selector: ${variation.selector}`, '#7C3AED');
      log(`Description: ${variation.description}`, '#9ca3af');
      
      // Check if selector is valid and finds elements
      let elements;
      try {
        elements = document.querySelectorAll(variation.selector);
      } catch (error) {
        log(`  ❌ Invalid selector: ${error.message}`, '#ef4444');
        continue;
      }
      
      if (elements.length === 0) {
        log(`  ❌ No elements found`, '#ef4444');
        continue;
      }
      
      log(`  Found ${elements.length} element(s)`, '#6366f1');
      
      // Test the first element with our interaction methods (with pause support)
      const testResult = await testElementWithAllMethodsPausable(elements[0], `Variation ${i + 1}`);
      
      if (testResult && debugSession.successfulMethods.length > 0) {
        const lastSuccess = debugSession.successfulMethods[debugSession.successfulMethods.length - 1];
        successfulVariations.push({
          ...variation,
          workingMethod: lastSuccess.method,
          elementsFound: elements.length
        });
        
        // Track which methods work for this selector
        if (!workingMethods[variation.selector]) {
          workingMethods[variation.selector] = [];
        }
        workingMethods[variation.selector].push(lastSuccess.method);
        
        log(`  ✅ SUCCESS with ${lastSuccess.method}!`, '#10b981', true);
      } else {
        log(`  ❌ No working methods found`, '#ef4444');
      }
      
      // Check pause before delay
      if (!testingPaused) {
        await delay(300);
      }
    }
    
    // Summary
    log(`\n🎯 SELECTOR VARIATIONS SUMMARY:`, '#10b981', true);
    log(`Total tested: ${variations.length}`, '#d1d5db');
    log(`Successful: ${successfulVariations.length}`, '#10b981');
    
    if (successfulVariations.length > 0) {
      log(`\n🏆 WORKING SELECTORS (ranked by specificity):`, '#f59e0b', true);
      
      // Sort successful variations by specificity (highest first)
      successfulVariations.sort((a, b) => b.specificity - a.specificity);
      
      successfulVariations.forEach((variation, index) => {
        log(`\n${index + 1}. ${variation.selector}`, '#7C3AED', true);
        log(`   Description: ${variation.description}`, '#9ca3af');
        log(`   Elements found: ${variation.elementsFound}`, '#6366f1');
        log(`   Working method: ${variation.workingMethod}`, '#10b981');
        log(`   Specificity score: ${variation.specificity}`, '#9ca3af');
      });
      
      // Store successful variations for export
      debugSession.successfulVariations = successfulVariations;
      
      log(`\n💡 RECOMMENDATIONS:`, '#f59e0b', true);
      
      // Find most specific working selector
      const mostSpecific = successfulVariations[0];
      log(`🎯 Most specific: ${mostSpecific.selector}`, '#10b981');
      
      // Find least specific (most general) working selector
      const leastSpecific = successfulVariations[successfulVariations.length - 1];
      log(`🌐 Most general: ${leastSpecific.selector}`, '#6366f1');
      
      // Find selectors that match exactly one element
      const uniqueSelectors = successfulVariations.filter(v => v.elementsFound === 1);
      if (uniqueSelectors.length > 0) {
        log(`🎯 Unique element selectors: ${uniqueSelectors.length} found`, '#10b981');
        uniqueSelectors.forEach(v => {
          log(`   • ${v.selector}`, '#10b981');
        });
      }
      
    } else {
      log(`❌ No working selectors found. The element might not be interactive or require special handling.`, '#ef4444');
    }
    
    // Hide pause button when testing completes
    const pauseButton = document.getElementById('pause-testing');
    pauseButton.style.display = 'none';
    
    log(`\n🎬 Testing completed! Use "Copy Summary" for LLM-friendly results.`, '#7C3AED', true);
  }

  function testFirstElement() {
    const selector = document.getElementById('target-selector').value.trim();
    if (!selector) {
      log('❌ Please enter a target selector first', '#ef4444');
      return;
    }
    
    const cleanedSelector = validateAndCleanSelector(selector);
    if (!cleanedSelector) return;
    
    let elements;
    try {
      elements = document.querySelectorAll(cleanedSelector);
    } catch (error) {
      log(`❌ Invalid selector: ${error.message}`, '#ef4444');
      return;
    }
    
    if (elements.length === 0) {
      log('❌ No elements found for testing', '#ef4444');
      return;
    }
    
    log(`🧪 Testing first element with all interaction methods...`, '#f59e0b', true);
    testElementWithAllMethods(elements[0], 1);
  }

  async function testAllElements() {
    const selector = document.getElementById('target-selector').value.trim();
    if (!selector) {
      log('❌ Please enter a target selector first', '#ef4444');
      updatePanelStatus('error', 'Please enter a target selector');
      return;
    }
    
    updatePanelStatus('testing', 'Testing all elements...');
    
    const cleanedSelector = validateAndCleanSelector(selector);
    if (!cleanedSelector) return;
    
    let elements;
    try {
      elements = document.querySelectorAll(cleanedSelector);
    } catch (error) {
      log(`❌ Invalid selector: ${error.message}`, '#ef4444');
      return;
    }
    
    if (elements.length === 0) {
      log('❌ No elements found for testing', '#ef4444');
      return;
    }
    
    // Check if concurrent testing is enabled
    if (currentThreadCount > 1) {
      return await testAllElementsConcurrent(elements);
    } else {
      return await testAllElementsSequential(elements);
    }
  }
  
  async function testAllElementsSequential(elements) {
    log(`🧪 Smart testing ${elements.length} elements sequentially...`, '#f59e0b', true);
    
    // Test first element thoroughly
    log(`\n--- Testing Element 1 (Full Analysis) ---`, '#f59e0b');
    const firstResult = await testElementWithAllMethods(elements[0], 1);
    
    if (firstResult && debugSession.successfulMethods.length > 0) {
      const workingMethod = debugSession.successfulMethods[debugSession.successfulMethods.length - 1];
      log(`\n🎯 Found working method: ${workingMethod.method}`, '#10b981', true);
      log(`🚀 Testing this method on remaining ${elements.length - 1} elements...`, '#6366f1');
      
      // Test the working method on remaining elements (up to 10 more)
      const testLimit = Math.min(elements.length, 11); // Total of 11 elements max
      let successCount = 1; // First element already successful
      
      for (let i = 1; i < testLimit; i++) {
        log(`\n--- Testing Element ${i + 1} (Quick Test) ---`, '#f59e0b');
        const quickSuccess = await testSingleMethod(elements[i], workingMethod.method, i + 1);
        if (quickSuccess) successCount++;
        await delay(300); // Shorter delay for quick tests
      }
      
      log(`\n✅ Summary: ${successCount}/${testLimit} elements respond to ${workingMethod.method}`, '#10b981', true);
      
      if (elements.length > testLimit) {
        log(`⚠️ Limited testing to first ${testLimit} elements to prevent spam`, '#f59e0b');
        log(`💡 Tip: The working method likely applies to all ${elements.length} elements`, '#6366f1');
      }
      
    } else {
      // If first element fails, test a few more with full analysis
      log(`\n❌ Element 1 failed, trying ${Math.min(3, elements.length)} more elements...`, '#ef4444');
      
      for (let i = 1; i < Math.min(elements.length, 4); i++) {
        log(`\n--- Testing Element ${i + 1} (Full Analysis) ---`, '#f59e0b');
        const result = await testElementWithAllMethods(elements[i], i + 1);
        if (result) {
          log(`🎉 Found working method on element ${i + 1}!`, '#10b981', true);
          break;
        }
        await delay(500);
      }
    }
  }
  
  async function testAllElementsConcurrent(elements) {
    log(`🧪 Testing ${elements.length} elements with ${currentThreadCount} concurrent threads...`, '#f59e0b', true);
    log(`⚡ Concurrent mode: Testing multiple elements simultaneously for faster results`, '#10b981');
    
    // Chunk elements into batches for concurrent processing
    const chunks = chunkArray(Array.from(elements), currentThreadCount);
    let totalSuccessCount = 0;
    let workingMethods = new Set();
    
    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
      const chunk = chunks[chunkIndex];
      log(`\n🔄 Processing batch ${chunkIndex + 1}/${chunks.length} (${chunk.length} elements)`, '#6366f1', true);
      
      // Test all elements in this chunk concurrently
      const promises = chunk.map(async (element, index) => {
        const elementNumber = chunkIndex * currentThreadCount + index + 1;
        
        // Add visual highlight to show which elements are being tested
        const originalStyle = element.style.cssText;
        element.style.cssText += '; outline: 2px solid #f59e0b !important; outline-offset: 1px !important; box-shadow: 0 0 10px rgba(249, 115, 22, 0.3) !important;';
        
        try {
          // Get descriptive element name for logging
          const elementDesc = getElementDescription(element, `Element ${elementNumber}`);
          log(`🧪 Thread ${index + 1}: Testing ${elementDesc}`, '#6366f1');
          
          const result = await testElementWithAllMethods(element, elementNumber);
          
          // Update visual feedback based on result
          if (result) {
            element.style.cssText = originalStyle + '; outline: 3px solid #10b981 !important; outline-offset: 2px !important; background: rgba(16, 185, 129, 0.1) !important;';
            // Keep success highlight for longer
            setTimeout(() => {
              element.style.cssText = originalStyle;
            }, 5000);
            
            // Track working method
            if (debugSession.successfulMethods.length > 0) {
              const lastSuccessful = debugSession.successfulMethods[debugSession.successfulMethods.length - 1];
              workingMethods.add(lastSuccessful.method);
            }
            
            return { success: true, elementNumber, element, elementDesc };
          } else {
            // Failed - remove highlight after short delay
            setTimeout(() => {
              element.style.cssText = originalStyle;
            }, 2000);
            return { success: false, elementNumber, element, elementDesc };
          }
        } catch (error) {
          const elementDesc = getElementDescription(element, `Element ${elementNumber}`);
          log(`❌ Thread ${index + 1}: Error testing ${elementDesc}: ${error.message}`, '#ef4444');
          element.style.cssText = originalStyle;
          return { success: false, elementNumber, element, elementDesc, error };
        }
      });
      
      // Wait for all threads in this batch to complete
      const results = await Promise.allSettled(promises);
      
      // Process results
      let batchSuccessCount = 0;
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          batchSuccessCount++;
          totalSuccessCount++;
          const desc = result.value.elementDesc || `Element ${result.value.elementNumber}`;
          log(`✅ Thread ${index + 1}: ${desc} - SUCCESS`, '#10b981');
        } else if (result.status === 'fulfilled') {
          const desc = result.value.elementDesc || `Element ${result.value.elementNumber}`;
          log(`❌ Thread ${index + 1}: ${desc} - FAILED`, '#6b7280');
        } else {
          log(`💥 Thread ${index + 1}: Promise rejected - ${result.reason}`, '#ef4444');
        }
      });
      
      log(`📊 Batch ${chunkIndex + 1} complete: ${batchSuccessCount}/${chunk.length} successful`, batchSuccessCount > 0 ? '#10b981' : '#6b7280');
      
      // Small delay between batches to prevent overwhelming the page
      if (chunkIndex < chunks.length - 1) {
        await delay(500);
      }
    }
    
    // Final summary
    log(`\n🎯 CONCURRENT TESTING COMPLETE`, '#7C3AED', true);
    log(`📊 Overall Results: ${totalSuccessCount}/${elements.length} elements successful`, totalSuccessCount > 0 ? '#10b981' : '#ef4444');
    
    if (workingMethods.size > 0) {
      log(`🛠️ Working methods found: ${Array.from(workingMethods).join(', ')}`, '#10b981');
      updatePanelStatus('success', `Testing complete - ${totalSuccessCount} elements successful`);
    } else {
      updatePanelStatus('error', 'No working methods found');
    }
    
    if (totalSuccessCount > 0) {
      log(`⚡ Concurrent testing completed ${Math.round((elements.length / currentThreadCount) * 100) / 100}x faster than sequential`, '#6366f1');
    }
  }
  
  // Utility function to chunk array into smaller arrays
  function chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  async function testElementWithAllMethods(element, elementNumber) {
    log(`Testing ${getTargetElementCount(element)} target elements...`, '#6366f1');
    
    // Reset new tab activity for this element test
    window._debuggerNewTabActivity = 0;
    
    // Get target elements to test (element + ancestors)
    const testTargets = getTestTargets(element);
    
    // Test each target
    for (const target of testTargets) {
      const success = await testInteractionMethods(target.element, target.name);
      if (success) {
        log(`🎉 FOUND WORKING METHOD for Element ${elementNumber}!`, '#10b981', true);
        updatePanelStatus('success', `Working method found on element ${elementNumber}`);
        return true;
      }
      await delay(200);
    }
    
    return false;
  }

  // Pausable version of testElementWithAllMethods for use in testAllSelectorVariations
  async function testElementWithAllMethodsPausable(element, elementNumber) {
    log(`Testing ${getTargetElementCount(element)} target elements...`, '#6366f1');
    
    // Reset new tab activity for this element test
    window._debuggerNewTabActivity = 0;
    
    // Add visual highlight to the element being tested
    const originalStyle = element.style.cssText;
    element.style.cssText += '; outline: 3px solid #7C3AED !important; outline-offset: 2px !important; background: rgba(124, 58, 237, 0.1) !important;';
    
    // Get target elements to test (element + ancestors)
    const testTargets = getTestTargets(element);
    
    // Test each target
    for (const target of testTargets) {
      // Check pause before testing each target
      while (testingPaused) {
        await delay(1000);
      }
      
      // Highlight current target
      const targetOriginalStyle = target.element.style.cssText;
      target.element.style.cssText += '; outline: 2px solid #f59e0b !important; outline-offset: 1px !important;';
      
      const success = await testInteractionMethodsPausable(target.element, target.name);
      
      // Restore target styling
      target.element.style.cssText = targetOriginalStyle;
      
      if (success) {
        // Keep success highlight longer
        target.element.style.cssText += '; outline: 3px solid #10b981 !important; outline-offset: 2px !important; background: rgba(16, 185, 129, 0.2) !important;';
        setTimeout(() => {
          target.element.style.cssText = targetOriginalStyle;
        }, 3000);
        
        log(`🎉 FOUND WORKING METHOD for Element ${elementNumber}!`, '#10b981', true);
        updatePanelStatus('success', `Working method found on element ${elementNumber}`);
        return true;
      }
      
      // Check pause before delay
      if (!testingPaused) {
        await delay(200);
      }
    }
    
    // Restore original styling
    element.style.cssText = originalStyle;
    
    return false;
  }

  // Quick test of a single method on an element
  async function testSingleMethod(element, methodName, elementNumber) {
    const methods = getInteractionMethods();
    const method = methods.find(m => m.name === methodName);
    
    if (!method) {
      log(`❌ Method "${methodName}" not found`, '#ef4444');
      return false;
    }

    const successIndicators = document.getElementById('success-indicators').value.trim();
    
    try {
      // Count indicators before action
      const beforeCounts = getSuccessIndicatorCounts(successIndicators);
      
      // Scroll element into view
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      await delay(200);
      
              // Set testing flag to catch clicks and track current method
        window._debuggerTesting = true;
        window._debuggerCurrentMethod = method.name;
        
        // Update session data with current method for UTM tracking
        const crossPageSession = window._debuggerCrossPageSession;
        if (crossPageSession) {
          try {
            const sessionData = JSON.parse(localStorage.getItem('universalDebugger_crossPage') || '{}');
            sessionData.currentMethod = method.name;
            sessionData.utm_content = method.name;
            sessionData.utm_term = debugSession.targetSelector;
            localStorage.setItem('universalDebugger_crossPage', JSON.stringify(sessionData));
          } catch (e) {
            console.log('🔍 Could not update session UTM data:', e.message);
          }
        }
        
        // Execute the method with comprehensive error handling
        try {
          await method.action(element);
        } catch (methodError) {
          // Log method-specific errors but don't fail the entire test
          console.log(`🔍 Method "${method.name}" error (non-fatal):`, methodError.message);
          
          // For Polymer gesture errors, try a safer fallback
          if (methodError.message && methodError.message.includes('identifier')) {
            try {
              // Simple fallback click
              element.click();
            } catch (fallbackError) {
              console.log('🔍 Even fallback click failed:', fallbackError.message);
            }
          }
        }
        
        // Clear testing flag after a delay
        setTimeout(() => {
          window._debuggerTesting = false;
        }, 3000);
      
      // Wait for changes
      await delay(800);
      
      // Check for changes
      const afterCounts = getSuccessIndicatorCounts(successIndicators);
      const hasChanges = Object.keys(beforeCounts).some(key => afterCounts[key] > beforeCounts[key]);
      
      if (hasChanges) {
        log(`    ✅ SUCCESS! ${methodName} on Element ${elementNumber}`, '#10b981');
        
        // Try to close any dialogs that appeared
        try {
          const dialogs = document.querySelectorAll('[role="dialog"], [role="alertdialog"]');
          if (dialogs.length > 0) {
            const closeBtn = dialogs[dialogs.length - 1].querySelector('[data-mdc-dialog-action="cancel"], [aria-label*="Close"], [aria-label*="close"]');
            if (closeBtn) {
              closeBtn.click();
              await delay(300);
            }
          }
        } catch (e) {}
        
        return true;
      } else {
        log(`    ❌ ${methodName} - No changes detected`, '#6b7280');
        return false;
      }
      
    } catch (error) {
      log(`    ❌ ${methodName} - Error: ${error.message}`, '#ef4444');
      return false;
    }
  }

  // Helper function to get test targets
  function getTestTargets(element) {
    // Create better description for original element
    const originalDesc = getElementDescription(element, 'Original');
    const testTargets = [{ element: element, name: originalDesc }];
    
    // Add ancestors as test targets
    let current = element;
    let level = 0;
    while (current && level < 8) {
      current = current.parentElement;
      if (current) {
        const name = getElementDescription(current, `Parent L${level + 1}`);
        testTargets.push({ element: current, name: name });
      }
      level++;
    }
    
    return testTargets;
  }
  
  // Helper function to create descriptive element names
  function getElementDescription(element, prefix) {
    const tag = element.tagName.toLowerCase();
    const id = element.id ? `#${element.id}` : '';
    const classes = element.className ? `.${element.className.split(' ').slice(0, 2).join('.')}` : '';
    
    // Get text content (truncated)
    const text = element.textContent?.trim() || '';
    const textPreview = text.length > 20 ? `"${text.substring(0, 20)}..."` : text ? `"${text}"` : '';
    
    // Get useful attributes
    const role = element.getAttribute('role') ? `[role="${element.getAttribute('role')}"]` : '';
    const type = element.getAttribute('type') ? `[type="${element.getAttribute('type')}"]` : '';
    const ariaLabel = element.getAttribute('aria-label') ? `[aria-label="${element.getAttribute('aria-label').substring(0, 15)}..."]` : '';
    
    // Build description
    let description = `${prefix}: ${tag}${id}${classes}${role}${type}${ariaLabel}`;
    if (textPreview) {
      description += ` ${textPreview}`;
    }
    
    return description;
  }

  // Helper function to get target element count
  function getTargetElementCount(element) {
    return getTestTargets(element).length;
  }

  // Safe method execution wrapper
  async function safeExecuteMethod(method, element) {
    try {
      await method.action(element);
    } catch (methodError) {
      // Log method-specific errors but don't fail the entire test
      console.log(`🔍 Method "${method.name}" error (non-fatal):`, methodError.message);
      
      // For Polymer gesture errors or other common errors, try safer fallbacks
      if (methodError.message && (
          methodError.message.includes('identifier') || 
          methodError.message.includes('polymer') ||
          methodError.message.includes('gesture')
        )) {
        try {
          // Simple fallback click
          element.click();
        } catch (fallbackError) {
          console.log('🔍 Even fallback click failed:', fallbackError.message);
        }
      }
    }
  }

  // Get all interaction methods
  function getInteractionMethods() {
    return [
      {
        name: 'Direct Click',
        action: async (el) => el.click()
      },
      {
        name: 'Hover + Click',
        action: async (el) => {
          el.dispatchEvent(new MouseEvent('mouseover', { bubbles: true }));
          await delay(300);
          el.click();
        }
      },
      {
        name: 'Click Event Dispatch',
        action: async (el) => {
          el.dispatchEvent(new MouseEvent('click', {
            view: window, bubbles: true, cancelable: true
          }));
        }
      },
      {
        name: 'MouseDown + MouseUp',
        action: async (el) => {
          el.dispatchEvent(new MouseEvent('mousedown', {
            view: window, bubbles: true, cancelable: true
          }));
          await delay(50);
          el.dispatchEvent(new MouseEvent('mouseup', {
            view: window, bubbles: true, cancelable: true
          }));
        }
      },
      {
        name: 'Focus + Enter',
        action: async (el) => {
          el.focus();
          await delay(100);
          el.dispatchEvent(new KeyboardEvent('keydown', {
            key: 'Enter', code: 'Enter', bubbles: true
          }));
        }
      },
      {
        name: 'Focus + Space',
        action: async (el) => {
          el.focus();
          await delay(100);
          el.dispatchEvent(new KeyboardEvent('keydown', {
            key: ' ', code: 'Space', bubbles: true
          }));
        }
      },
      {
        name: 'Double Click',
        action: async (el) => {
          el.dispatchEvent(new MouseEvent('dblclick', {
            view: window, bubbles: true, cancelable: true
          }));
        }
      },
      {
        name: 'Pointer Events',
        action: async (el) => {
          el.dispatchEvent(new PointerEvent('pointerdown', { bubbles: true }));
          await delay(50);
          el.dispatchEvent(new PointerEvent('pointerup', { bubbles: true }));
        }
      },
      {
        name: 'Touch Events',
        action: async (el) => {
          el.dispatchEvent(new TouchEvent('touchstart', { bubbles: true }));
          await delay(50);
          el.dispatchEvent(new TouchEvent('touchend', { bubbles: true }));
        }
      },
      {
        name: 'Context Menu',
        action: async (el) => {
          el.dispatchEvent(new MouseEvent('contextmenu', {
            view: window, bubbles: true, cancelable: true
          }));
        }
      },
      {
        name: 'Forced Click (JS)',
        action: async (el) => {
          // Try to trigger click handlers directly
          if (el.onclick) {
            el.onclick.call(el);
          }
          // Also try manual click with coordinates
          const rect = el.getBoundingClientRect();
          el.dispatchEvent(new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: rect.left + rect.width / 2,
            clientY: rect.top + rect.height / 2
          }));
        }
      },
      {
        name: 'Synthetic Mouse Sequence',
        action: async (el) => {
          const rect = el.getBoundingClientRect();
          const x = rect.left + rect.width / 2;
          const y = rect.top + rect.height / 2;
          
          // Full mouse event sequence with coordinates
          const eventOptions = {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: x,
            clientY: y,
            button: 0,
            buttons: 1
          };
          
          el.dispatchEvent(new MouseEvent('mouseenter', eventOptions));
          await delay(50);
          el.dispatchEvent(new MouseEvent('mouseover', eventOptions));
          await delay(50);
          el.dispatchEvent(new MouseEvent('mousedown', eventOptions));
          await delay(50);
          el.dispatchEvent(new MouseEvent('mouseup', eventOptions));
          await delay(50);
          el.dispatchEvent(new MouseEvent('click', eventOptions));
        }
      },
      {
        name: 'Native Event Trigger',
        action: async (el) => {
          // Try to find and trigger any event listeners
          const events = ['click', 'mousedown', 'mouseup', 'pointerdown', 'pointerup'];
          for (const eventType of events) {
            try {
              // Create native event
              const event = new Event(eventType, { bubbles: true, cancelable: true });
              el.dispatchEvent(event);
              await delay(100);
            } catch (e) {
              // Continue if event creation fails
            }
          }
        }
      },
      {
        name: 'Element.click() Override',
        action: async (el) => {
          // Store original click method
          const originalClick = el.click;
          
          // Try calling click directly on the element
          try {
            HTMLElement.prototype.click.call(el);
          } catch (e) {
            // Fallback to original click
            if (originalClick) {
              originalClick.call(el);
            }
          }
        }
      },
      {
        name: 'jQuery Trigger (if available)',
        action: async (el) => {
          // Check if jQuery is available
          if (window.jQuery || window.$) {
            const $ = window.jQuery || window.$;
            try {
              $(el).trigger('click');
              await delay(100);
              $(el).click();
            } catch (e) {
              // jQuery not available or failed
              el.click();
            }
          } else {
            // Fallback to regular click
            el.click();
          }
        }
      },
      {
        name: 'Polymer/Lit Element Click',
        action: async (el) => {
          // For Polymer/Lit elements (which YouTube uses)
          if (el._handleClick) {
            el._handleClick();
          } else if (el.click) {
            // Try to bypass any preventDefault
            const event = new MouseEvent('click', {
              view: window,
              bubbles: true,
              cancelable: false  // Non-cancelable
            });
            el.dispatchEvent(event);
          }
        }
      },
      {
        name: 'Shadow DOM Click',
        action: async (el) => {
          // If element has shadow root, try clicking inside
          if (el.shadowRoot) {
            const clickableInShadow = el.shadowRoot.querySelector('button, [role="button"], [tabindex]');
            if (clickableInShadow) {
              clickableInShadow.click();
            }
          }
          
          // Also try the main element
          el.click();
        }
      },
      {
        name: 'Accessibility Click',
        action: async (el) => {
          // Try ARIA/accessibility methods
          if (el.ariaPressed !== undefined) {
            el.ariaPressed = el.ariaPressed === 'true' ? 'false' : 'true';
          }
          
          // Simulate screen reader activation
          el.dispatchEvent(new KeyboardEvent('keydown', {
            key: 'Enter',
            code: 'Enter',
            bubbles: true,
            cancelable: true
          }));
          
          await delay(50);
          
          el.dispatchEvent(new KeyboardEvent('keyup', {
            key: 'Enter',
            code: 'Enter',
            bubbles: true,
            cancelable: true
          }));
        }
      },
      {
        name: 'YouTube Trusted Event',
        action: async (el) => {
          // Create a more trusted-looking event with all properties YouTube expects
          const rect = el.getBoundingClientRect();
          const x = rect.left + rect.width / 2;
          const y = rect.top + rect.height / 2;
          
          const trustedEvent = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: x,
            clientY: y,
            screenX: x + window.screenX,
            screenY: y + window.screenY,
            button: 0,
            buttons: 1,
            detail: 1,
            composed: true,
            which: 1
          });
          
          // Try to make it look more legitimate (this won't actually set isTrusted but shows intent)
          try {
            Object.defineProperty(trustedEvent, 'isTrusted', {
              value: true,
              writable: false
            });
          } catch (e) {
            // Property is read-only, but worth attempting
          }
          
          el.dispatchEvent(trustedEvent);
        }
      },
      {
        name: 'YouTube Pointer Sequence',
        action: async (el) => {
          // Use pointer events which YouTube may trust more than regular mouse events
          const rect = el.getBoundingClientRect();
          const x = rect.left + rect.width / 2;
          const y = rect.top + rect.height / 2;
          
          const pointerOptions = {
            pointerId: 1,
            bubbles: true,
            cancelable: true,
            clientX: x,
            clientY: y,
            button: 0,
            buttons: 1,
            isPrimary: true,
            pointerType: 'mouse',
            pressure: 0.5,
            tiltX: 0,
            tiltY: 0
          };
          
          // Full pointer event sequence
          el.dispatchEvent(new PointerEvent('pointerenter', pointerOptions));
          await delay(50);
          el.dispatchEvent(new PointerEvent('pointerover', pointerOptions));
          await delay(50);
          el.dispatchEvent(new PointerEvent('pointerdown', pointerOptions));
          await delay(50);
          el.dispatchEvent(new PointerEvent('pointerup', pointerOptions));
          await delay(50);
          
          // Follow with mouse click
          el.dispatchEvent(new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: x,
            clientY: y,
            button: 0,
            buttons: 1,
            detail: 1
          }));
        }
      },
      {
        name: 'YouTube Event Bypass',
        action: async (el) => {
          // Try multiple methods to bypass YouTube's event detection
          try {
            // Method 1: Direct onclick handler
            if (el.onclick) {
              el.onclick.call(el, { 
                type: 'click', 
                target: el, 
                currentTarget: el,
                preventDefault: () => {},
                stopPropagation: () => {},
                isTrusted: true,
                bubbles: true
              });
            }
            
            // Method 2: Look for React event handlers
            const keys = Object.keys(el);
            for (const key of keys) {
              if (key.startsWith('__reactEvent') || key.startsWith('_vei') || key.includes('onClick')) {
                const handler = el[key];
                if (typeof handler === 'function') {
                  handler.call(el, {
                    type: 'click',
                    target: el,
                    currentTarget: el,
                    preventDefault: () => {},
                    stopPropagation: () => {}
                  });
                }
              }
            }
            
            // Method 3: Try accessing event listeners directly
            if (el._listeners && el._listeners.click) {
              el._listeners.click.forEach(listener => {
                if (typeof listener === 'function') {
                  listener.call(el);
                }
              });
            }
            
            // Method 4: Non-cancelable event
            const event = new MouseEvent('click', {
              view: window,
              bubbles: true,
              cancelable: false, // Make it non-cancelable
              composed: true
            });
            el.dispatchEvent(event);
            
          } catch (e) {
            // Final fallback
            el.click();
          }
        }
      },
      {
        name: 'YouTube Focus Navigation',
        action: async (el) => {
          // YouTube-specific focus and keyboard navigation sequence
          try {
            // Ensure element is visible
            el.scrollIntoView({ behavior: 'instant', block: 'center' });
            await delay(100);
            
            // Set focus using multiple methods
            el.focus();
            if (el.tabIndex === -1) {
              el.tabIndex = 0;
              el.focus();
            }
            await delay(200);
            
            // Simulate tab navigation (YouTube respects keyboard navigation)
            el.dispatchEvent(new KeyboardEvent('keydown', {
              key: 'Tab',
              code: 'Tab',
              keyCode: 9,
              bubbles: true,
              cancelable: true
            }));
            await delay(100);
            
            // Try multiple keyboard activation methods
            const activationKeys = [
              { key: 'Enter', code: 'Enter', keyCode: 13 },
              { key: ' ', code: 'Space', keyCode: 32 }
            ];
            
            for (const keyInfo of activationKeys) {
              el.dispatchEvent(new KeyboardEvent('keydown', {
                ...keyInfo,
                bubbles: true,
                cancelable: true
              }));
              await delay(50);
              
              el.dispatchEvent(new KeyboardEvent('keyup', {
                ...keyInfo,
                bubbles: true,
                cancelable: true
              }));
              await delay(50);
            }
            
            // Follow up with a click
            el.click();
            
          } catch (e) {
            el.click();
          }
        }
      },
      {
        name: 'YouTube Touch Events',
        action: async (el) => {
          // Simulate mobile touch events for YouTube mobile detection bypass
          const rect = el.getBoundingClientRect();
          const x = rect.left + rect.width / 2;
          const y = rect.top + rect.height / 2;
          
          try {
            // Simple touch events (broad browser support)
            const touchOptions = {
              bubbles: true,
              cancelable: true,
              view: window,
              clientX: x,
              clientY: y,
              touches: [],
              targetTouches: [],
              changedTouches: []
            };
            
            // Touch sequence
            el.dispatchEvent(new TouchEvent('touchstart', touchOptions));
            await delay(100);
            
            el.dispatchEvent(new TouchEvent('touchend', touchOptions));
            await delay(50);
            
            // Follow with click events
            el.dispatchEvent(new MouseEvent('mousedown', {
              view: window,
              bubbles: true,
              cancelable: true,
              clientX: x,
              clientY: y,
              button: 0
            }));
            await delay(50);
            
            el.dispatchEvent(new MouseEvent('mouseup', {
              view: window,
              bubbles: true,
              cancelable: true,
              clientX: x,
              clientY: y,
              button: 0
            }));
            await delay(50);
            
            el.click();
            
          } catch (e) {
            // Fallback for browsers that don't support TouchEvent constructor
            el.dispatchEvent(new TouchEvent('touchstart', { bubbles: true }));
            await delay(100);
            el.dispatchEvent(new TouchEvent('touchend', { bubbles: true }));
            await delay(50);
            el.click();
          }
        }
      },
      {
        name: 'YouTube Script Injection',
        action: async (el) => {
          // Try to execute click in a different execution context
          try {
            // Method 1: setTimeout to break out of current execution context
            setTimeout(() => {
              el.click();
            }, 0);
            await delay(100);
            
            // Method 2: Use requestAnimationFrame
            requestAnimationFrame(() => {
              el.click();
            });
            await delay(100);
            
            // Method 3: Create and execute script (with CSP protection)
            try {
              const script = document.createElement('script');
              script.textContent = `
                (function() {
                  const el = arguments[0];
                  if (el && typeof el.click === 'function') {
                    el.click();
                  }
                })(document.querySelector('${el.tagName.toLowerCase()}[class*="${el.className.split(' ')[0]}"]'));
              `;
              document.head.appendChild(script);
              await delay(100);
              document.head.removeChild(script);
            } catch (cspError) {
              // CSP blocked script injection, fallback to direct click
              console.log('🔍 CSP blocked script injection, using fallback');
              el.click();
            }
            
            // Method 4: Direct DOM manipulation click
            el.dispatchEvent(new Event('click', { bubbles: true }));
            
          } catch (e) {
            el.click();
          }
        }
      },
      {
        name: 'Safe Polymer Click',
        action: async (el) => {
          try {
            // Safer approach for Polymer elements
            const rect = el.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            // Create safer click events that don't trigger Polymer gesture errors
            const clickEvent = new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
              view: window,
              clientX: centerX,
              clientY: centerY,
              button: 0
            });
            
            el.dispatchEvent(clickEvent);
          } catch (e) {
            // Fallback to basic click
            el.click();
          }
        }
      },
      {
        name: 'YouTube Share Helper',
        action: async (el) => {
          // Specifically designed for YouTube share workflows
          try {
            // First try normal click
            el.click();
            await delay(300);
            
            // Look for share menu
            const shareMenu = document.querySelector('[role="dialog"] [aria-label*="Share"], .yt-simple-menu-service-host');
            if (shareMenu) {
              // Look for copy link button
              const copyBtn = shareMenu.querySelector('[aria-label*="Copy"], [title*="Copy"], button:contains("Copy")');
              if (copyBtn) {
                copyBtn.click();
                await delay(200);
              }
            }
          } catch (e) {
            el.click();
          }
        }
      },
      {
        name: 'Error-Safe Click',
        action: async (el) => {
          // Ultra-safe click method with comprehensive error handling
          try {
            // Ensure element is valid
            if (!el || typeof el.click !== 'function') {
              throw new Error('Invalid element');
            }
            
            // Try different click approaches with error isolation
            const methods = [
              () => el.click(),
              () => el.dispatchEvent(new Event('click', { bubbles: true })),
              () => {
                const event = new MouseEvent('click', { bubbles: true, cancelable: true });
                el.dispatchEvent(event);
              },
              () => {
                // Last resort: trigger via focus and space/enter
                el.focus();
                el.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
              }
            ];
            
            for (const method of methods) {
              try {
                method();
                break; // If any method succeeds, stop trying others
              } catch (methodError) {
                console.log(`🔍 Click method failed (trying next):`, methodError.message);
              }
            }
          } catch (e) {
            console.log('🔍 Error-Safe Click: All methods failed:', e.message);
          }
        }
      }
    ];
  }

  async function testInteractionMethods(element, elementName) {
    const successIndicators = document.getElementById('success-indicators').value.trim();
    const methods = getInteractionMethods();
    
    for (const method of methods) {
      try {
        // Count indicators before action (don't reset activity here - let it accumulate)
        const beforeCounts = getSuccessIndicatorCounts(successIndicators);
        
        // Log debug info for NP detection
        if (successIndicators.toUpperCase().includes('NP')) {
          console.log(`🔍 Before ${method.name}: URL=${window.location.href}, TabActivity=${window._debuggerNewTabActivity || 0}`);
        }
        
        // Scroll element into view
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await delay(200);
        
        // Set testing flag to catch clicks and track current method
        window._debuggerTesting = true;
        window._debuggerCurrentMethod = method.name;
        
        // Update session data with current method for UTM tracking
        const crossPageSession = window._debuggerCrossPageSession;
        if (crossPageSession) {
          try {
            const sessionData = JSON.parse(localStorage.getItem('universalDebugger_crossPage') || '{}');
            sessionData.currentMethod = method.name;
            sessionData.utm_content = method.name;
            sessionData.utm_term = debugSession.targetSelector;
            localStorage.setItem('universalDebugger_crossPage', JSON.stringify(sessionData));
          } catch (e) {
            console.log('🔍 Could not update session UTM data:', e.message);
          }
        }
        
        // Execute the method
        await method.action(element);
        
        // Clear testing flag after a delay
        setTimeout(() => {
          window._debuggerTesting = false;
        }, 3000);
        
        // Wait for changes - increased delay for new tab detection
        await delay(1500);
        
        // Additional check specifically for new page detection
        if (successIndicators.toUpperCase().includes('NP')) {
          // Give more time for tab switching and focus changes
          await delay(1000);
          console.log(`🔍 After ${method.name}: URL=${window.location.href}, TabActivity=${window._debuggerNewTabActivity || 0}`);
        }
        
        // Check for changes
        const afterCounts = getSuccessIndicatorCounts(successIndicators);
        const hasChanges = Object.keys(beforeCounts).some(key => afterCounts[key] > beforeCounts[key]);
        
        if (hasChanges) {
          const result = {
            element: elementName,
            method: method.name,
            selector: debugSession.targetSelector,
            success: true,
            changes: afterCounts
          };
          
          // Log specific types of changes detected
          logSuccessDetails(beforeCounts, afterCounts);
          
          // Create unique identifier for this element+method combination
          const methodKey = `${elementName}+${method.name}`;
          
          // Check if we've seen this combination before
          if (successfulMethods.has(methodKey)) {
            log(`    ✅ SUCCESS! ${method.name} on ${elementName}`, '#10b981', true);
            log(`    Changes detected: ${JSON.stringify(afterCounts)}`, '#9ca3af');
            log(`    🔄 DUPLICATE method detected - continuing without auto-pause`, '#f59e0b', true);
            
            debugSession.results.push(result);
            debugSession.successfulMethods.push(result);
            return true;
          }
          
          // Add to successful methods tracking
          successfulMethods.add(methodKey);
          
          debugSession.results.push(result);
          debugSession.successfulMethods.push(result);
          
          log(`    ✅ SUCCESS! ${method.name} on ${elementName}`, '#10b981', true);
          log(`    Changes detected: ${JSON.stringify(afterCounts)}`, '#9ca3af');
          
          // Try to close any dialogs that appeared
          try {
            const dialogs = document.querySelectorAll('[role="dialog"], [role="alertdialog"]');
            if (dialogs.length > 0) {
              const closeBtn = dialogs[dialogs.length - 1].querySelector('[data-mdc-dialog-action="cancel"], [aria-label*="Close"], [aria-label*="close"]');
              if (closeBtn) {
                closeBtn.click();
                await delay(300);
              }
            }
          } catch (e) {}
          
          return true;
          
        } else {
          log(`    ❌ ${method.name} on ${elementName} - No changes detected`, '#6b7280');
          debugSession.results.push({
            element: elementName,
            method: method.name,
            selector: debugSession.targetSelector,
            success: false
          });
        }
        
      } catch (error) {
        log(`    ❌ ${method.name} on ${elementName} - Error: ${error.message}`, '#ef4444');
      }
      
      await delay(150);
    }
    
    return false;
  }

  // Pausable version of testInteractionMethods for use in testAllSelectorVariations
  async function testInteractionMethodsPausable(element, elementName) {
    const successIndicators = document.getElementById('success-indicators').value.trim();
    const methods = getInteractionMethods();
    
    for (const method of methods) {
      // Check pause before each method
      if (testingPaused) {
        log('⏸️ Testing paused. Click Resume to continue...', '#f59e0b');
        while (testingPaused) {
          await delay(1000);
        }
        log('▶️ Testing resumed...', '#10b981');
      }
      
      try {
        // Count indicators before action (don't reset activity here - let it accumulate)
        const beforeCounts = getSuccessIndicatorCounts(successIndicators);
        
        // Log debug info for NP detection
        if (successIndicators.toUpperCase().includes('NP')) {
          console.log(`🔍 Before ${method.name}: URL=${window.location.href}, TabActivity=${window._debuggerNewTabActivity || 0}`);
        }
        
        // Scroll element into view
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await delay(200);
        
        // Set testing flag to catch clicks and track current method
        window._debuggerTesting = true;
        window._debuggerCurrentMethod = method.name;
        
        // Update session data with current method for UTM tracking
        const crossPageSession = window._debuggerCrossPageSession;
        if (crossPageSession) {
          try {
            const sessionData = JSON.parse(localStorage.getItem('universalDebugger_crossPage') || '{}');
            sessionData.currentMethod = method.name;
            sessionData.utm_content = method.name;
            sessionData.utm_term = debugSession.targetSelector;
            localStorage.setItem('universalDebugger_crossPage', JSON.stringify(sessionData));
          } catch (e) {
            console.log('🔍 Could not update session UTM data:', e.message);
          }
        }
        
        // Execute the method
        await method.action(element);
        
        // Clear testing flag after a delay
        setTimeout(() => {
          window._debuggerTesting = false;
        }, 3000);
        
        // Wait for changes - increased delay for new tab detection
        await delay(1500);
        
        // Additional check specifically for new page detection
        if (successIndicators.toUpperCase().includes('NP')) {
          // Give more time for tab switching and focus changes
          await delay(1000);
          console.log(`🔍 After ${method.name}: URL=${window.location.href}, TabActivity=${window._debuggerNewTabActivity || 0}`);
        }
        
        // Check for changes
        const afterCounts = getSuccessIndicatorCounts(successIndicators);
        const hasChanges = Object.keys(beforeCounts).some(key => afterCounts[key] > beforeCounts[key]);
        
        if (hasChanges) {
          const result = {
            element: elementName,
            method: method.name,
            selector: debugSession.targetSelector,
            success: true,
            changes: afterCounts
          };
          
          // Log specific types of changes detected
          logSuccessDetails(beforeCounts, afterCounts);
          
          // Create unique identifier for this element+method combination
          const methodKey = `${elementName}+${method.name}`;
          
          // Check if we've seen this combination before
          if (successfulMethods.has(methodKey)) {
            log(`    ✅ SUCCESS! ${method.name} on ${elementName}`, '#10b981', true);
            log(`    Changes detected: ${JSON.stringify(afterCounts)}`, '#9ca3af');
            log(`    🔄 DUPLICATE method detected - continuing without auto-pause`, '#f59e0b', true);
            
            // Show visual success confirmation (even for duplicates)
            showSuccessOverlay(method.name, elementName, afterCounts);
            
            debugSession.results.push(result);
            debugSession.successfulMethods.push(result);
            return true;
          }
          
          // Add to successful methods tracking
          successfulMethods.add(methodKey);
          
          debugSession.results.push(result);
          debugSession.successfulMethods.push(result);
          
          log(`    ✅ SUCCESS! ${method.name} on ${elementName}`, '#10b981', true);
          log(`    Changes detected: ${JSON.stringify(afterCounts)}`, '#9ca3af');
          
          // Show visual success confirmation
          showSuccessOverlay(method.name, elementName, afterCounts);
          
          // Try to close any dialogs that appeared
          try {
            const dialogs = document.querySelectorAll('[role="dialog"], [role="alertdialog"]');
            if (dialogs.length > 0) {
              const closeBtn = dialogs[dialogs.length - 1].querySelector('[data-mdc-dialog-action="cancel"], [aria-label*="Close"], [aria-label*="close"]');
              if (closeBtn) {
                closeBtn.click();
                await delay(300);
              }
            }
          } catch (e) {}
          
          return true;
          
        } else {
          log(`    ❌ ${method.name} on ${elementName} - No changes detected`, '#6b7280');
          debugSession.results.push({
            element: elementName,
            method: method.name,
            selector: debugSession.targetSelector,
            success: false
          });
        }
        
      } catch (error) {
        log(`    ❌ ${method.name} on ${elementName} - Error: ${error.message}`, '#ef4444');
      }
      
      // Check pause before delay
      if (!testingPaused) {
        await delay(150);
      }
    }
    
    return false;
  }

  function getSuccessIndicatorCounts(indicators) {
    const items = indicators.split(',').map(s => s.trim()).filter(s => s.length > 0);
    const counts = {};
    
    // Shortcode mapping
    const shortcodeMap = {
             'NP': {
         description: 'New Page/Navigation',
         check: () => {
           const currentUrl = window.location.href;
           const currentTitle = document.title;
           
           // Store initial values if not set
           if (!window._debuggerInitialUrl) {
             window._debuggerInitialUrl = currentUrl;
             window._debuggerInitialTitle = currentTitle;
             window._debuggerInitialTabCount = 1;
             
             // Set up new tab/window detection
             if (!window._debuggerNewTabDetector) {
               window._debuggerNewTabDetector = setupNewTabDetection();
             }
             
             // Set up cross-page detection "pixel" and start monitoring
             setupCrossPageDetection();
             startCrossPageMonitoring();
           }
           
           // Check for navigation changes in current tab
           const urlChanged = currentUrl !== window._debuggerInitialUrl;
           const titleChanged = currentTitle !== window._debuggerInitialTitle;
           
           // Check for new tab/window activity
           const newTabActivity = window._debuggerNewTabActivity || 0;
           
           // Check for cross-page detection
           const crossPageActivity = checkCrossPageActivity();
           
           // Reset the activity flag after checking
           if (newTabActivity > 0) {
             window._debuggerNewTabActivity = 0;
           }
           
           return (urlChanged || titleChanged || newTabActivity > 0 || crossPageActivity > 0) ? 1 : 0;
         }
       },
      'MD': {
        description: 'Modal/Dialog appears',
        selectors: ['[role="dialog"]', '[role="alertdialog"]', '.modal', '.dialog', '[class*="modal"]', '[class*="dialog"]']
      },
      'NT': {
        description: 'Notification/Toast',
        selectors: ['.notification', '.toast', '.alert', '[class*="notification"]', '[class*="toast"]', '[class*="alert"]', '[role="alert"]']
      },
      'PP': {
        description: 'Popup window',
        selectors: ['.popup', '[class*="popup"]', '.overlay', '[class*="overlay"]']
      },
      'FM': {
        description: 'Form submission',
        selectors: ['.success', '.submitted', '[class*="success"]', '.form-success', '.confirmation']
      },
      'DD': {
        description: 'Dropdown menu',
        selectors: ['.dropdown', '[class*="dropdown"]', '.menu', '[class*="menu"]', '[role="menu"]', '[role="listbox"]']
      },
      'TB': {
        description: 'Tab change',
        selectors: ['[role="tab"][aria-selected="true"]', '.tab.active', '.tab-pane.active', '[class*="tab-active"]']
      },
      'AC': {
        description: 'Accordion expand',
        selectors: ['[aria-expanded="true"]', '.accordion.open', '.accordion.expanded', '[class*="accordion-open"]']
      },
      'LD': {
        description: 'Loading indicator',
        selectors: ['.loading', '.spinner', '[class*="loading"]', '[class*="spinner"]', '[aria-busy="true"]']
      },
      'CT': {
        description: 'Content change',
        check: () => {
          const currentContent = document.body.innerHTML.length;
          if (!window._debuggerInitialContentLength) {
            window._debuggerInitialContentLength = currentContent;
          }
          return Math.abs(currentContent - window._debuggerInitialContentLength) > 100 ? 1 : 0;
        }
      },
      'ER': {
        description: 'Error message',
        selectors: ['.error', '.error-message', '[class*="error"]', '[role="alert"][class*="error"]']
      },
      'SU': {
        description: 'Success message',
        selectors: ['.success', '.success-message', '[class*="success"]', '[role="alert"][class*="success"]']
      }
    };
    
    items.forEach(item => {
      const upperItem = item.toUpperCase();
      
      if (shortcodeMap[upperItem]) {
        const shortcode = shortcodeMap[upperItem];
        
        if (shortcode.check) {
          // Custom check function
          try {
            counts[item] = shortcode.check();
          } catch (e) {
            counts[item] = 0;
          }
        } else if (shortcode.selectors) {
          // Count elements from multiple selectors
          let totalCount = 0;
          shortcode.selectors.forEach(selector => {
            try {
              totalCount += document.querySelectorAll(selector).length;
            } catch (e) {
              // Ignore invalid selectors
            }
          });
          counts[item] = totalCount;
        }
      } else {
        // Treat as CSS selector
        try {
          counts[item] = document.querySelectorAll(item).length;
        } catch (e) {
          counts[item] = 0;
        }
      }
    });
    
    // Also check some general indicators
    counts['_dialogs'] = document.querySelectorAll('[role="dialog"], [role="alertdialog"]').length;
    counts['_visible_elements'] = document.querySelectorAll('[style*="display: block"], [style*="visibility: visible"]').length;
    
    return counts;
  }

  function exportResults() {
    const hasRegularResults = debugSession.successfulMethods.length > 0;
    const hasVariationResults = debugSession.successfulVariations && debugSession.successfulVariations.length > 0;
    
    if (!hasRegularResults && !hasVariationResults) {
      log('❌ No successful methods found yet. Run tests first.', '#ef4444');
      return;
    }
    
    let exportCode = '';
    
    // Export regular successful methods
    if (hasRegularResults) {
      log('\n🎯 SUCCESSFUL METHODS FOUND:', '#10b981', true);
      
      debugSession.successfulMethods.forEach((result, index) => {
        log(`\n--- Success ${index + 1} ---`, '#f59e0b');
        log(`Element: ${result.element}`, '#d1d5db');
        log(`Method: ${result.method}`, '#10b981');
        log(`Selector: ${result.selector}`, '#d1d5db');
        
        // Generate code snippet
        const codeSnippet = generateCodeSnippet(result);
        log(`\nCode Snippet:`, '#7C3AED');
        log(`<pre style="background: #1a1a1a; padding: 8px; border-radius: 4px; font-family: monospace; white-space: pre-wrap;">${codeSnippet}</pre>`, '#d1d5db');
      });
      
      exportCode += debugSession.successfulMethods.map(result => generateCodeSnippet(result)).join('\n\n');
    }
    
    // Export selector variations
    if (hasVariationResults) {
      log('\n🎯 SUCCESSFUL SELECTOR VARIATIONS:', '#10b981', true);
      
      const sortedVariations = debugSession.successfulVariations.sort((a, b) => b.specificity - a.specificity);
      
      sortedVariations.forEach((variation, index) => {
        log(`\n--- Variation ${index + 1} ---`, '#f59e0b');
        log(`Selector: ${variation.selector}`, '#7C3AED');
        log(`Description: ${variation.description}`, '#9ca3af');
        log(`Method: ${variation.workingMethod}`, '#10b981');
        log(`Elements found: ${variation.elementsFound}`, '#6366f1');
        log(`Specificity: ${variation.specificity}`, '#9ca3af');
        
        // Generate code snippet for variation
        const codeSnippet = generateVariationCodeSnippet(variation);
        log(`\nCode Snippet:`, '#7C3AED');
        log(`<pre style="background: #1a1a1a; padding: 8px; border-radius: 4px; font-family: monospace; white-space: pre-wrap;">${codeSnippet}</pre>`, '#d1d5db');
      });
      
      if (exportCode) exportCode += '\n\n';
      exportCode += '// === SELECTOR VARIATIONS ===\n\n';
      exportCode += sortedVariations.map(variation => generateVariationCodeSnippet(variation)).join('\n\n');
    }
    
    // Copy to clipboard
    navigator.clipboard.writeText(exportCode).then(() => {
      log('\n📋 All successful code snippets copied to clipboard!', '#10b981', true);
    });
  }

  function generateVariationCodeSnippet(variation) {
    const methodMap = {
      'Direct Click': 'element.click();',
      'Hover + Click': `element.dispatchEvent(new MouseEvent('mouseover', { bubbles: true }));\nawait delay(300);\nelement.click();`,
      'Click Event Dispatch': `element.dispatchEvent(new MouseEvent('click', {\n  view: window, bubbles: true, cancelable: true\n}));`,
      'MouseDown + MouseUp': `element.dispatchEvent(new MouseEvent('mousedown', {\n  view: window, bubbles: true, cancelable: true\n}));\nawait delay(50);\nelement.dispatchEvent(new MouseEvent('mouseup', {\n  view: window, bubbles: true, cancelable: true\n}));`,
      'Focus + Enter': `element.focus();\nawait delay(100);\nelement.dispatchEvent(new KeyboardEvent('keydown', {\n  key: 'Enter', code: 'Enter', bubbles: true\n}));`,
      'Focus + Space': `element.focus();\nawait delay(100);\nelement.dispatchEvent(new KeyboardEvent('keydown', {\n  key: ' ', code: 'Space', bubbles: true\n}));`
    };
    
    const methodCode = methodMap[variation.workingMethod] || variation.workingMethod;
    
    return `// ${variation.description}
// Selector: ${variation.selector} (finds ${variation.elementsFound} elements)
// Method: ${variation.workingMethod}
// Specificity: ${variation.specificity}
const element = document.querySelector('${variation.selector}');
if (element) {
  ${methodCode}
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}`;
  }

  function generateCodeSnippet(result) {
    const methodMap = {
      'Direct Click': 'element.click();',
      'Hover + Click': `element.dispatchEvent(new MouseEvent('mouseover', { bubbles: true }));\nawait delay(300);\nelement.click();`,
      'Click Event Dispatch': `element.dispatchEvent(new MouseEvent('click', {\n  view: window, bubbles: true, cancelable: true\n}));`,
      'MouseDown + MouseUp': `element.dispatchEvent(new MouseEvent('mousedown', {\n  view: window, bubbles: true, cancelable: true\n}));\nawait delay(50);\nelement.dispatchEvent(new MouseEvent('mouseup', {\n  view: window, bubbles: true, cancelable: true\n}));`,
      'Focus + Enter': `element.focus();\nawait delay(100);\nelement.dispatchEvent(new KeyboardEvent('keydown', {\n  key: 'Enter', code: 'Enter', bubbles: true\n}));`,
      'Focus + Space': `element.focus();\nawait delay(100);\nelement.dispatchEvent(new KeyboardEvent('keydown', {\n  key: ' ', code: 'Space', bubbles: true\n}));`
    };
    
    const methodCode = methodMap[result.method] || result.method;
    
    return `// ${result.method} on ${result.element}
// Selector: ${result.selector}
const element = document.querySelector('${result.selector}');
if (element) {
  ${methodCode}
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}`;
  }

  function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Strip UTM and tracking parameters from URLs
  function stripTrackingParameters(url) {
    // Parameters to remove (UTM parameters and common tracking parameters)
    const trackingParams = [
      'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
      'utm_id', 'utm_source_platform', 'utm_creative_format', 'utm_marketing_tactic',
      'si', 'fbclid', 'gclid', 'msclkid', 'twclid', '_ga', 'mc_cid', 'mc_eid',
      'ref', 'referrer', 'source', 'campaign', 'medium', 'term', 'content',
      'hsCtaTracking', 'hsa_acc', 'hsa_ad', 'hsa_cam', 'hsa_grp', 'hsa_kw',
      'hsa_mt', 'hsa_net', 'hsa_src', 'hsa_tgt', 'hsa_ver', '_hsenc', '_hsmi',
      'vero_conv', 'vero_id', 'wickedid', 'yclid', 'affiliate', 'aff_id',
      'partner', 'promo', 'coupon', 'discount', 'tracking', 'track'
    ];
    
    const urlObj = new URL(url);
    
    // Remove tracking parameters
    trackingParams.forEach(param => {
      urlObj.searchParams.delete(param);
    });
    
    // For YouTube, also remove the 'feature' parameter which is tracking
    if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
      urlObj.searchParams.delete('feature');
      urlObj.searchParams.delete('app');
      urlObj.searchParams.delete('desktop');
    }
    
    // Return clean URL string
    let cleanUrl = urlObj.toString();
    
    // Remove trailing ? if no parameters remain
    if (cleanUrl.endsWith('?')) {
      cleanUrl = cleanUrl.slice(0, -1);
    }
    
    return cleanUrl;
  }

  // Check clipboard for expected payload
  async function checkClipboardPayload(expectedPayload) {
    if (!expectedPayload || !expectedPayload.trim()) {
      return { hasPayload: false, message: 'No expected payload specified' };
    }
    
    try {
      // Check if clipboard API is available
      if (!navigator.clipboard || !navigator.clipboard.readText) {
        return { hasPayload: false, message: 'Clipboard API not available' };
      }
      
      // Read clipboard content
      const clipboardText = await navigator.clipboard.readText();
      const trimmedExpected = expectedPayload.trim();
      const trimmedClipboard = clipboardText.trim();
      
      // Check for exact match
      if (trimmedClipboard === trimmedExpected) {
        return { hasPayload: true, message: 'Exact match found in clipboard' };
      }
      
      // Check if expected payload is contained in clipboard
      if (trimmedClipboard.includes(trimmedExpected)) {
        return { hasPayload: true, message: 'Expected payload found in clipboard' };
      }
      
      // Check if it's a URL and compare base URL (ignoring UTM and other parameters)
      if (trimmedExpected.startsWith('http') && trimmedClipboard.startsWith('http')) {
        try {
          const expectedUrl = new URL(trimmedExpected);
          const clipboardUrl = new URL(trimmedClipboard);
          
          // Strip UTM and tracking parameters from both URLs
          const cleanExpectedUrl = stripTrackingParameters(expectedUrl);
          const cleanClipboardUrl = stripTrackingParameters(clipboardUrl);
          
          // Compare cleaned URLs
          if (cleanExpectedUrl === cleanClipboardUrl) {
            return { hasPayload: true, message: 'URL match found (UTM parameters ignored)' };
          }
          
          // Also check if clean expected URL is contained in clean clipboard URL
          if (cleanClipboardUrl.includes(cleanExpectedUrl)) {
            return { hasPayload: true, message: 'Expected URL found in clipboard (UTM parameters ignored)' };
          }
        } catch (e) {
          // URLs are malformed, continue with string comparison
        }
      }
      
      return { 
        hasPayload: false, 
        message: `Clipboard content doesn't match. Found: "${trimmedClipboard.substring(0, 100)}${trimmedClipboard.length > 100 ? '...' : ''}"` 
      };
      
    } catch (error) {
      return { hasPayload: false, message: `Clipboard read failed: ${error.message}` };
    }
  }

  // Check for YouTube URLs in clipboard and page content
  async function checkForYouTubeUrls() {
    const foundUrls = [];
    
    try {
      // Check clipboard for YouTube URLs
      if (navigator.clipboard && navigator.clipboard.readText) {
        try {
          const clipboardText = await navigator.clipboard.readText();
          const youtubeUrlRegex = /(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[^\s]+/gi;
          const clipboardUrls = clipboardText.match(youtubeUrlRegex);
          
          if (clipboardUrls && clipboardUrls.length > 0) {
            clipboardUrls.forEach(url => {
              // Ensure URL has protocol
              const fullUrl = url.startsWith('http') ? url : 'https://' + url;
              foundUrls.push({ source: 'clipboard', url: fullUrl });
              log(`📋 YouTube URL found in clipboard: ${fullUrl}`, '#10b981', true);
            });
          }
        } catch (clipboardError) {
          debugLog('📋 Could not read clipboard: ' + clipboardError.message);
        }
      }
      
      // Check page content for YouTube URLs
      const pageText = document.body.textContent || document.body.innerText || '';
      const youtubeUrlRegex = /(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[^\s\)]+/gi;
      const pageUrls = pageText.match(youtubeUrlRegex);
      
      if (pageUrls && pageUrls.length > 0) {
        // Get unique URLs and limit to recent ones
        const uniquePageUrls = [...new Set(pageUrls)].slice(0, 5);
        uniquePageUrls.forEach(url => {
          // Ensure URL has protocol
          const fullUrl = url.startsWith('http') ? url : 'https://' + url;
          if (!foundUrls.some(found => found.url === fullUrl)) {
            foundUrls.push({ source: 'page_content', url: fullUrl });
            log(`📄 YouTube URL found in page content: ${fullUrl}`, '#6366f1', true);
          }
        });
      }
      
      // Check for share dialog or modal content specifically
      const shareElements = document.querySelectorAll('[aria-label*="share" i], [class*="share" i], [id*="share" i]');
      shareElements.forEach(element => {
        const elementText = element.textContent || element.innerText || '';
        const shareUrls = elementText.match(youtubeUrlRegex);
        if (shareUrls && shareUrls.length > 0) {
          shareUrls.forEach(url => {
            const fullUrl = url.startsWith('http') ? url : 'https://' + url;
            if (!foundUrls.some(found => found.url === fullUrl)) {
              foundUrls.push({ source: 'share_dialog', url: fullUrl });
              log(`🔗 YouTube URL found in share dialog: ${fullUrl}`, '#7C3AED', true);
            }
          });
        }
      });
      
      // Store found URLs globally
      if (foundUrls.length > 0) {
        window._debuggerDetectedUrls = window._debuggerDetectedUrls || [];
        foundUrls.forEach(urlData => {
          if (!window._debuggerDetectedUrls.includes(urlData.url)) {
            window._debuggerDetectedUrls.push(urlData.url);
          }
        });
        
        log(`🎯 TOTAL YOUTUBE URLS DISCOVERED: ${foundUrls.length}`, '#10b981', true);
        foundUrls.forEach((urlData, index) => {
          log(`  ${index + 1}. [${urlData.source}] ${urlData.url}`, '#6366f1');
        });
      } else {
        log(`🔍 No YouTube URLs found in clipboard or page content`, '#6b7280');
      }
      
    } catch (error) {
      log(`❌ Error checking for YouTube URLs: ${error.message}`, '#ef4444');
    }
    
    return foundUrls;
  }

  // Visual success overlay to show when action actually works
  function showSuccessOverlay(methodName, elementName, changes) {
    // Remove any existing overlay
    const existingOverlay = document.getElementById('debug-success-overlay');
    if (existingOverlay) {
      existingOverlay.remove();
    }
    
    // Create the overlay
    const overlay = document.createElement('div');
    overlay.id = 'debug-success-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
      padding: 16px 20px;
      border-radius: 12px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 600;
      box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
      z-index: 999999;
      max-width: 350px;
      transform: translateX(400px);
      transition: transform 0.3s ease-out;
      border: 2px solid rgba(255, 255, 255, 0.2);
    `;
    
    // Count the main changes for display
    const mainChanges = [];
    for (const [key, value] of Object.entries(changes)) {
      if (value > 0 && !key.startsWith('_')) {
        mainChanges.push(`${key}: ${value}`);
      }
    }
    
    overlay.innerHTML = `
      <div style="display: flex; align-items: center; margin-bottom: 8px;">
        <div style="font-size: 20px; margin-right: 8px;">✅</div>
        <div style="font-size: 16px; font-weight: bold;">SUCCESS!</div>
      </div>
      <div style="margin-bottom: 4px; opacity: 0.9;">
        <strong>${methodName}</strong> worked on:<br>
        ${elementName}
      </div>
      <div style="font-size: 12px; opacity: 0.8; margin-top: 8px;">
        Changes: ${mainChanges.slice(0, 3).join(', ')}
        ${mainChanges.length > 3 ? '...' : ''}
      </div>
    `;
    
    document.body.appendChild(overlay);
    
    // Animate in
    setTimeout(() => {
      overlay.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto-remove after 4 seconds
    setTimeout(() => {
      overlay.style.transform = 'translateX(400px)';
      setTimeout(() => {
        if (overlay.parentNode) {
          overlay.remove();
        }
      }, 300);
    }, 4000);
    
    // Make it clickable to dismiss
    overlay.addEventListener('click', () => {
      overlay.style.transform = 'translateX(400px)';
      setTimeout(() => {
        if (overlay.parentNode) {
          overlay.remove();
        }
      }, 300);
    });
  }

  // Check UTM Cleaner settings and recommend whitelisting if needed
  async function checkUTMCleanerSettings() {
    try {
      const result = await chrome.storage.local.get(['gmbExtractorSettings']);
      const settings = result.gmbExtractorSettings || {};
      
      const utmCleanerEnabled = settings.utmTrackingCleanerEnabled !== false;
      const whitelistDomains = settings.utmCleanerWhitelistDomains || [];
      const currentDomain = window.location.hostname;
      
      // Check if current domain is whitelisted
      const isCurrentDomainWhitelisted = whitelistDomains.some(domain => {
        const normalizedDomain = domain.toLowerCase().replace(/^www\./, '');
        const normalizedCurrent = currentDomain.toLowerCase().replace(/^www\./, '');
        return normalizedCurrent === normalizedDomain || normalizedCurrent.endsWith('.' + normalizedDomain);
      });
      
      if (utmCleanerEnabled && !isCurrentDomainWhitelisted) {
        log(`⚠️ UTM Tracking Cleaner is ON but ${currentDomain} is not whitelisted`, '#f59e0b', true);
        log(`💡 Consider whitelisting ${currentDomain} in UTM Cleaner settings to preserve debugging UTM parameters`, '#6366f1');
        log(`🔧 Go to Extension Settings → UTM Tracking Cleaner → Whitelist Domains`, '#9ca3af');
        
        return {
          enabled: true,
          whitelisted: false,
          recommendation: `Add ${currentDomain} to UTM cleaner whitelist`
        };
      } else if (utmCleanerEnabled && isCurrentDomainWhitelisted) {
        log(`✅ UTM Tracking Cleaner: ${currentDomain} is whitelisted - UTM parameters will be preserved`, '#10b981');
        
        return {
          enabled: true,
          whitelisted: true,
          recommendation: null
        };
      } else {
        log(`ℹ️ UTM Tracking Cleaner is disabled - UTM parameters will be preserved`, '#6366f1');
        
        return {
          enabled: false,
          whitelisted: false,
          recommendation: null
        };
      }
    } catch (error) {
      log(`⚠️ Could not check UTM Cleaner settings: ${error.message}`, '#f59e0b');
      return {
        enabled: false,
        whitelisted: false,
        recommendation: 'Check UTM Cleaner settings manually'
      };
    }
  }

  // Validate and clean selector input - handles both HTML and CSS
  function validateAndCleanSelector(input) {
    if (!input || !input.trim()) {
      log('❌ Please enter a target selector', '#ef4444');
      return null;
    }

    let selector = input.trim();
    let convertedFromHTML = false;

    // Handle HTML input - convert to CSS selector
    if (selector.includes('<') || selector.includes('class=') || selector.includes('id=')) {
      const converted = convertHTMLToSelector(selector);
      if (converted) {
        selector = converted;
        convertedFromHTML = true;
        log(`💡 Converted HTML to CSS selector: "${selector}"`, '#10b981');
      }
    }

    // Validate the final selector
    try {
      document.querySelector(selector);
      if (!convertedFromHTML) {
        log(`✅ Valid CSS selector: "${selector}"`, '#10b981');
      }
      return selector;
    } catch (error) {
      log(`❌ Invalid selector: "${selector}"`, '#ef4444');
      log('💡 Examples of valid inputs:', '#f59e0b');
      log('   CSS Selectors:', '#d1d5db');
      log('   • span.aJ6', '#9ca3af');
      log('   • .my-class', '#9ca3af');
      log('   • #my-id', '#9ca3af');
      log('   • button[role="button"]', '#9ca3af');
      log('   HTML Elements:', '#d1d5db');
      log('   • <span class="aJ6">Unsubscribe</span>', '#9ca3af');
      log('   • <button id="submit-btn">', '#9ca3af');
      log('   • class="my-class"', '#9ca3af');
      return null;
    }
  }

  // Convert HTML to CSS selector
  function convertHTMLToSelector(htmlInput) {
    let selector = '';
    
    // Handle full HTML tags like <span class="aJ6">Unsubscribe</span>
    if (htmlInput.includes('<') && htmlInput.includes('>')) {
      const tagMatch = htmlInput.match(/<(\w+)/);
      const tag = tagMatch ? tagMatch[1] : '';
      
      const classMatch = htmlInput.match(/class="([^"]+)"/);
      const idMatch = htmlInput.match(/id="([^"]+)"/);
      const roleMatch = htmlInput.match(/role="([^"]+)"/);
      const typeMatch = htmlInput.match(/type="([^"]+)"/);
      
      selector = tag;
      
      if (classMatch) {
        const classes = classMatch[1].split(' ').filter(c => c.trim()).map(c => '.' + c.trim()).join('');
        selector += classes;
      }
      
      if (idMatch) {
        selector += '#' + idMatch[1];
      }
      
      if (roleMatch) {
        selector += `[role="${roleMatch[1]}"]`;
      }
      
      if (typeMatch) {
        selector += `[type="${typeMatch[1]}"]`;
      }
      
      return selector;
    }
    
    // Handle attribute snippets like class="aJ6" or id="my-id"
    if (htmlInput.includes('class="')) {
      const classMatch = htmlInput.match(/class="([^"]+)"/);
      if (classMatch) {
        const classes = classMatch[1].split(' ').filter(c => c.trim()).map(c => '.' + c.trim()).join('');
        return classes;
      }
    }
    
    if (htmlInput.includes('id="')) {
      const idMatch = htmlInput.match(/id="([^"]+)"/);
      if (idMatch) {
        return '#' + idMatch[1];
      }
    }
    
    if (htmlInput.includes('role="')) {
      const roleMatch = htmlInput.match(/role="([^"]+)"/);
      if (roleMatch) {
        return `[role="${roleMatch[1]}"]`;
      }
    }
    
    return null;
  }

  // Create floating debug button
  function createDebugButton() {
    debugLog('%c🔘 CREATING UNIVERSAL DEBUGGER BUTTON', 'color: #7C3AED; font-size: 14px; font-weight: bold; background: rgba(124, 58, 237, 0.1); padding: 4px;');
    debugLog('🕐 Button creation started at: ' + new Date().toLocaleTimeString());
    
    const button = document.createElement('button');
    button.id = 'universal-debugger-btn';
    button.textContent = 'Universal Debugger';
    debugLog('🔘 Button element created with ID: ' + button.id);
    debugLog('📝 Button text set to: ' + button.textContent);
    
    button.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 20px;
      background: #7C3AED;
      color: white;
      border: none;
      border-radius: 25px;
      padding: 12px 20px;
      font-weight: bold;
      cursor: pointer;
      z-index: 9998;
      box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
      font-size: 13px;
      transition: all 0.3s ease;
    `;
    debugLog('🎨 Button styling applied - position: fixed bottom-left, purple background');

    button.onmouseover = () => {
      button.style.background = '#6d28d9';
      button.style.transform = 'translateY(-2px)';
      debugLog('🖱️ Button hover effect activated');
    };
    button.onmouseout = () => {
      button.style.background = '#7C3AED';
      button.style.transform = 'translateY(0)';
      debugLog('🖱️ Button hover effect deactivated');
    };

    button.onclick = () => {
      debugLog('%c🔘 UNIVERSAL DEBUGGER BUTTON CLICKED!', 'color: #7C3AED; font-size: 14px; font-weight: bold; background: rgba(124, 58, 237, 0.1); padding: 4px;');
      debugLog('🕐 Button clicked at: ' + new Date().toLocaleTimeString());
      
      const existingPanel = document.getElementById('universal-click-debugger');
      if (existingPanel) {
        debugLog('🗑️ Existing Universal Debugger panel found - removing it');
        existingPanel.remove();
        debugLog('✅ Existing panel removed successfully');
      } else {
        debugLog('🆕 No existing panel found - creating new Universal Debugger panel');
        createUniversalDebugPanel();
        debugLog('✅ New Universal Debugger panel created');
      }
    };

    document.body.appendChild(button);
    debugLog('🏗️ Universal Debugger button added to document body');
    debugLog('✅ Universal Debugger button creation COMPLETE at: ' + new Date().toLocaleTimeString());
  }

  // Listen for toggle messages from popup
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === 'toggleUniversalDebugger') {
      if (message.enabled) {
        // AUTOMATICALLY ENABLE DEBUG LOGGING when Universal Debugger is turned ON
        console.log('%c🔧 Universal Debugger ACTIVATED - Enabling debug logging automatically!', 'color: #7C3AED; font-size: 14px; font-weight: bold; background: rgba(124, 58, 237, 0.1); padding: 4px;');
        
        // Save original debug mode state and enable debug logging
        chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
          const settings = result.gmbExtractorSettings || {};
          
          // Remember the original debug mode state BEFORE enabling Universal Debugger
          const originalDebugMode = settings.debugMode === true;
          chrome.storage.local.set({ 
            originalDebugModeState: originalDebugMode 
          });
          console.log('%c💾 Saved original debug mode state:', originalDebugMode ? 'ON' : 'OFF', 'color: #6b7280; font-size: 11px;');
          
          // Enable debug mode
          settings.debugMode = true;
          
          chrome.storage.local.set({ 
            gmbExtractorSettings: settings 
          }).then(() => {
            console.log('%c📊 Debug logging ENABLED automatically by Universal Debugger', 'color: #10b981; font-size: 12px; font-weight: bold;');
            
            // Send message to all tabs to update their debug mode
            chrome.runtime.sendMessage({
              action: 'updateSettings',
              settings: settings
            });
          }).catch(err => {
            console.log('🔧 Could not enable debug logging in settings:', err);
          });
        });
        
        // Check if debugger is already shown
        if (!document.getElementById('universal-debugger-btn')) {
          createDebugButton();
        }
      } else {
        // RESTORE ORIGINAL DEBUG MODE STATE when Universal Debugger is turned OFF
        console.log('%c🔧 Universal Debugger DEACTIVATED - Restoring original debug mode state', 'color: #f59e0b; font-size: 12px;');
        
        chrome.storage.local.get(['originalDebugModeState'], (result) => {
          const originalDebugMode = result.originalDebugModeState === true;
          console.log('%c🔄 Restoring debug mode to original state:', originalDebugMode ? 'ON' : 'OFF', 'color: #6b7280; font-size: 11px;');
          
          // Restore debug mode to original state
          chrome.storage.local.get(['gmbExtractorSettings'], (syncResult) => {
            const settings = syncResult.gmbExtractorSettings || {};
            settings.debugMode = originalDebugMode;
            
            chrome.storage.local.set({ 
              gmbExtractorSettings: settings 
            }).then(() => {
              console.log('%c✅ Debug mode restored to original state:', originalDebugMode ? 'ON' : 'OFF', 'color: #10b981; font-size: 12px;');
              
              // Send message to all tabs to update their debug mode
              chrome.runtime.sendMessage({
                action: 'updateSettings',
                settings: settings
              });
              
              // Clean up the saved state
              chrome.storage.local.remove(['originalDebugModeState']);
            }).catch(err => {
              console.log('🔧 Could not restore debug logging state:', err);
            });
          });
        });
        
        const existingBtn = document.getElementById('universal-debugger-btn');
        const existingPanel = document.getElementById('universal-click-debugger');
        if (existingBtn) existingBtn.remove();
        if (existingPanel) existingPanel.remove();
      }
    }
  });

  // Initialize - check if debugger should be enabled on page load
  chrome.storage.local.get(['secretDebugEnabled']).then(result => {
    if (result.secretDebugEnabled) {
      // AUTOMATICALLY ENABLE DEBUG LOGGING when Universal Debugger is active
      console.log('%c🔧 Universal Debugger initializing - Enabling debug logging automatically!', 'color: #7C3AED; font-size: 14px; font-weight: bold; background: rgba(124, 58, 237, 0.1); padding: 4px;');
      
      // Save original debug mode state and enable debug logging during initialization
      chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
        const settings = result.gmbExtractorSettings || {};
        
        // Check if we already have a saved state (avoid overwriting during page refresh)
        chrome.storage.local.get(['originalDebugModeState'], (localResult) => {
          if (localResult.originalDebugModeState === undefined) {
            // Only save original state if we haven't saved it yet
            const originalDebugMode = settings.debugMode === true;
            chrome.storage.local.set({ 
              originalDebugModeState: originalDebugMode 
            });
            console.log('%c💾 Saved original debug mode state during initialization:', originalDebugMode ? 'ON' : 'OFF', 'color: #6b7280; font-size: 11px;');
          } else {
            console.log('%c📋 Original debug mode state already saved:', localResult.originalDebugModeState ? 'ON' : 'OFF', 'color: #6b7280; font-size: 11px;');
          }
          
          // Enable debug mode
          settings.debugMode = true;
          
          chrome.storage.local.set({ 
            gmbExtractorSettings: settings 
          }).then(() => {
            console.log('%c📊 Debug logging ENABLED automatically during Universal Debugger initialization', 'color: #10b981; font-size: 12px; font-weight: bold;');
            
            // Send message to all tabs to update their debug mode
            chrome.runtime.sendMessage({
              action: 'updateSettings',
              settings: settings
            });
          }).catch(err => {
            console.log('🔧 Could not enable debug logging in settings during initialization:', err);
          });
        });
      });
      
      setTimeout(() => {
        createDebugButton();
        
        // Start monitoring for cross-page activity immediately on page load
        // This catches cases where we land on a new page from debugger testing
        setTimeout(() => {
          const crossPageActivity = checkCrossPageActivity();
          if (crossPageActivity > 0) {
            console.log('🌐 Cross-page new tab detected on page load!');
            window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + crossPageActivity;
          }
          
          // Start persistent monitoring in case page is still loading
          startCrossPageMonitoring();
        }, 2000);
        
        // Additional check after page fully loads (for slow pages like Google Maps)
        window.addEventListener('load', () => {
          setTimeout(() => {
            const lateActivity = checkCrossPageActivity();
            if (lateActivity > 0) {
              console.log('🌐 Cross-page new tab detected after full page load!');
              window._debuggerNewTabActivity = (window._debuggerNewTabActivity || 0) + lateActivity;
            }
          }, 3000);
        });
      }, 1000);
    }
  });

  // ===========================================
  // STATE CHANGE LISTENING FUNCTIONALITY
  // ===========================================

  function startListeningForStateChange() {
    if (isListening) {
      stopListening();
      return;
    }

    log('🎧 Starting DOM state listener with countdown...', '#059669', true);
    log('⏰ You have 5 seconds to reopen any popups that may have closed', '#f59e0b');
    
    // Show listening status with countdown
    const statusDiv = document.getElementById('listening-status');
    statusDiv.style.display = 'block';
         statusDiv.innerHTML = '<div style="font-size: 20px; font-weight: bold;">⏰ 5</div><div style="font-size: 14px; margin-top: 4px;">seconds to reopen popups, then listening starts...</div>';
     statusDiv.style.background = 'rgba(245, 158, 11, 0.1)';
     statusDiv.style.borderColor = '#f59e0b';
     statusDiv.style.color = '#f59e0b';
    
    // Update button
    const listenBtn = document.getElementById('listen-state-change');
    listenBtn.textContent = 'Cancel Countdown';
    listenBtn.style.background = '#dc2626';
    listenBtn.style.borderColor = '#dc2626';
    
    isListening = true;
    
         // Start countdown
     let countdown = 5;
     const countdownInterval = setInterval(() => {
       countdown--;
       if (countdown > 0) {
         statusDiv.innerHTML = `<div style="font-size: 20px; font-weight: bold;">⏰ ${countdown}</div><div style="font-size: 14px; margin-top: 4px;">seconds to reopen popups, then listening starts...</div>`;
         log(`⏰ ${countdown} seconds remaining...`, '#f59e0b');
       } else {
         clearInterval(countdownInterval);
         startActualListening();
       }
     }, 1000);
    
    // Store interval ID so we can cancel it
    window._listenerCountdownInterval = countdownInterval;
  }

  function startActualListening() {
    // Update status to active listening
    const statusDiv = document.getElementById('listening-status');
    statusDiv.innerHTML = '🎧 Listening for DOM changes... Click your target element now!';
    statusDiv.style.background = 'rgba(5, 150, 105, 0.1)';
    statusDiv.style.borderColor = '#059669';
    statusDiv.style.color = '#059669';
    
    // Update button
    const listenBtn = document.getElementById('listen-state-change');
    listenBtn.textContent = 'Stop Listening';
    
    log('📸 Capturing initial DOM state...', '#6366f1');
    
    // Capture initial DOM state
    captureInitialDOMState();
    
    // Set up click listener for the page
    setupClickListener();
    
    // Set up mutation observer for DOM changes
    setupMutationObserver();
    
    log('✅ State listener now active! Click your target element...', '#10b981');
  }

  function stopListening() {
    log('🛑 Stopping DOM state listener...', '#f59e0b');
    
    isListening = false;
    
    // Clear countdown if it's running
    if (window._listenerCountdownInterval) {
      clearInterval(window._listenerCountdownInterval);
      window._listenerCountdownInterval = null;
      log('⏰ Countdown cancelled', '#f59e0b');
    }
    
    // Hide listening status
    const statusDiv = document.getElementById('listening-status');
    statusDiv.style.display = 'none';
    
    // Reset button
    const listenBtn = document.getElementById('listen-state-change');
    listenBtn.textContent = 'Listen for State Change';
    listenBtn.style.background = '#059669';
    listenBtn.style.borderColor = '#059669';
    
    // Clean up listeners
    if (clickListener) {
      document.removeEventListener('click', clickListener, true);
      clickListener = null;
    }
    
    if (stateChangeObserver) {
      stateChangeObserver.disconnect();
      stateChangeObserver = null;
    }
    
    initialDOMState = null;
    
    log('✅ State listener stopped', '#6b7280');
  }

  function captureInitialDOMState() {
    log('📸 Capturing initial DOM state...', '#6366f1');
    
    initialDOMState = {
      // Capture all elements with IDs
      elementIds: Array.from(document.querySelectorAll('[id]')).map(el => el.id),
      
      // Capture all unique class names
      classNames: Array.from(new Set(
        Array.from(document.querySelectorAll('[class]'))
          .flatMap(el => Array.from(el.classList))
      )),
      
      // Capture common state indicators
      visibleElements: document.querySelectorAll('[style*="display"], [style*="visibility"]').length,
      modals: document.querySelectorAll('[role="dialog"], [role="alertdialog"], .modal, [class*="modal"]').length,
      dropdowns: document.querySelectorAll('.dropdown, [class*="dropdown"], [role="menu"]').length,
      notifications: document.querySelectorAll('.notification, .toast, .alert, [role="alert"]').length,
      popups: document.querySelectorAll('.popup, [class*="popup"], .overlay').length,
      accordions: document.querySelectorAll('[aria-expanded]').length,
      tabs: document.querySelectorAll('[role="tab"], .tab').length,
      loadingIndicators: document.querySelectorAll('.loading, .spinner, [class*="loading"]').length,
      
      // Capture aria states
      ariaExpanded: Array.from(document.querySelectorAll('[aria-expanded="true"]')).map(el => generateOptimalSelector(el)),
      ariaSelected: Array.from(document.querySelectorAll('[aria-selected="true"]')).map(el => generateOptimalSelector(el)),
      
      // Capture form states
      checkedInputs: Array.from(document.querySelectorAll('input:checked')).map(el => generateOptimalSelector(el)),
      
      // Document title and URL
      title: document.title,
      url: window.location.href,
      
      // Total DOM size
      domSize: document.documentElement.innerHTML.length
    };
    
    log(`📊 Initial state captured: ${initialDOMState.elementIds.length} IDs, ${initialDOMState.classNames.length} classes`, '#9ca3af');
  }

  function setupClickListener() {
    clickListener = function(event) {
      // Ignore clicks on the Universal Debugger panel itself
      const debuggerPanel = document.getElementById('universal-click-debugger');
      if (debuggerPanel && debuggerPanel.contains(event.target)) {
        log(`🔧 Ignoring click on debugger panel`, '#9ca3af');
        return;
      }
      
      // Ignore clicks on the Universal Debugger button
      const debuggerButton = document.getElementById('universal-debugger-btn');
      if (debuggerButton && debuggerButton.contains(event.target)) {
        log(`🔧 Ignoring click on debugger button`, '#9ca3af');
        return;
      }
      
      log(`🖱️ Click detected on: ${event.target.tagName}.${event.target.className || 'no-class'}`, '#6366f1');
      
      // Wait a moment for changes to occur, then analyze
      setTimeout(() => {
        analyzeStateChanges();
      }, 1500); // Give time for animations, async operations, etc.
      
      // Also check again after a longer delay for slow operations
      setTimeout(() => {
        analyzeStateChanges(true);
      }, 4000);
    };
    
    document.addEventListener('click', clickListener, true);
  }

  function setupMutationObserver() {
    let mutationCount = 0;
    let lastMutationLogTime = 0;
    
    stateChangeObserver = new MutationObserver((mutations) => {
      if (!isListening) return;
      
      mutationCount++;
      const now = Date.now();
      
      // Only log mutations every 2 seconds max, and only if significant
      const significantChanges = mutations.some(mutation => 
        mutation.type === 'childList' && mutation.addedNodes.length > 0 ||
        mutation.type === 'attributes' && ['class', 'style', 'aria-expanded', 'aria-selected'].includes(mutation.attributeName)
      );
      
      if (significantChanges && (now - lastMutationLogTime) > 2000) {
        log(`🔄 DOM mutations detected (${mutationCount} total)`, '#6366f1');
        lastMutationLogTime = now;
        mutationCount = 0; // Reset counter after logging
      }
    });
    
    stateChangeObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style', 'aria-expanded', 'aria-selected', 'aria-hidden', 'role']
    });
  }

  function analyzeStateChanges(isSecondCheck = false) {
    if (!initialDOMState || !isListening) return;
    
    const checkLabel = isSecondCheck ? '(Extended Check)' : '(Initial Check)';
    log(`🔍 Analyzing state changes ${checkLabel}...`, '#f59e0b', true);
    
    const currentState = {
      elementIds: Array.from(document.querySelectorAll('[id]')).map(el => el.id),
      classNames: Array.from(new Set(
        Array.from(document.querySelectorAll('[class]'))
          .flatMap(el => Array.from(el.classList))
      )),
      visibleElements: document.querySelectorAll('[style*="display"], [style*="visibility"]').length,
      modals: document.querySelectorAll('[role="dialog"], [role="alertdialog"], .modal, [class*="modal"]').length,
      dropdowns: document.querySelectorAll('.dropdown, [class*="dropdown"], [role="menu"]').length,
      notifications: document.querySelectorAll('.notification, .toast, .alert, [role="alert"]').length,
      popups: document.querySelectorAll('.popup, [class*="popup"], .overlay').length,
      accordions: document.querySelectorAll('[aria-expanded]').length,
      tabs: document.querySelectorAll('[role="tab"], .tab').length,
      loadingIndicators: document.querySelectorAll('.loading, .spinner, [class*="loading"]').length,
      ariaExpanded: Array.from(document.querySelectorAll('[aria-expanded="true"]')).map(el => generateOptimalSelector(el)),
      ariaSelected: Array.from(document.querySelectorAll('[aria-selected="true"]')).map(el => generateOptimalSelector(el)),
      checkedInputs: Array.from(document.querySelectorAll('input:checked')).map(el => generateOptimalSelector(el)),
      title: document.title,
      url: window.location.href,
      domSize: document.documentElement.innerHTML.length
    };

    const detectedChanges = [];
    const newSuccessIndicators = [];

    // Check for new IDs
    const newIds = currentState.elementIds.filter(id => !initialDOMState.elementIds.includes(id));
    if (newIds.length > 0) {
      detectedChanges.push(`🆔 New IDs: ${newIds.join(', ')}`);
      newIds.forEach(id => newSuccessIndicators.push(`#${id}`));
    }

    // Check for new classes
    const newClasses = currentState.classNames.filter(cls => !initialDOMState.classNames.includes(cls));
    if (newClasses.length > 0) {
      detectedChanges.push(`🎨 New Classes: ${newClasses.join(', ')}`);
      newClasses.forEach(cls => newSuccessIndicators.push(`.${cls}`));
    }

    // Check state changes with shortcode mapping
    const stateChecks = [
      { key: 'modals', shortcode: 'MD', description: 'Modals/Dialogs' },
      { key: 'notifications', shortcode: 'NT', description: 'Notifications' },
      { key: 'popups', shortcode: 'PP', description: 'Popups' },
      { key: 'dropdowns', shortcode: 'DD', description: 'Dropdowns' },
      { key: 'tabs', shortcode: 'TB', description: 'Tabs' },
      { key: 'accordions', shortcode: 'AC', description: 'Accordions' },
      { key: 'loadingIndicators', shortcode: 'LD', description: 'Loading Indicators' }
    ];

    stateChecks.forEach(check => {
      const initial = initialDOMState[check.key];
      const current = currentState[check.key];
      if (current > initial) {
        detectedChanges.push(`${check.description}: +${current - initial}`);
        newSuccessIndicators.push(check.shortcode);
      }
    });

    // Check for URL/title changes
    if (currentState.url !== initialDOMState.url) {
      detectedChanges.push(`🌐 URL Changed: ${currentState.url}`);
      newSuccessIndicators.push('NP');
    }

    if (currentState.title !== initialDOMState.title) {
      detectedChanges.push(`📄 Title Changed: ${currentState.title}`);
      if (!newSuccessIndicators.includes('NP')) {
        newSuccessIndicators.push('NP');
      }
    }

    // Check for significant DOM size changes
    const domSizeChange = Math.abs(currentState.domSize - initialDOMState.domSize);
    if (domSizeChange > 1000) {
      detectedChanges.push(`📄 Content Change: ${domSizeChange > 0 ? '+' : ''}${domSizeChange} chars`);
      newSuccessIndicators.push('CT');
    }

    // Check aria state changes
    const newAriaExpanded = currentState.ariaExpanded.filter(sel => !initialDOMState.ariaExpanded.includes(sel));
    if (newAriaExpanded.length > 0) {
      detectedChanges.push(`🔽 Expanded Elements: ${newAriaExpanded.join(', ')}`);
      newAriaExpanded.forEach(sel => newSuccessIndicators.push(sel));
    }

    const newAriaSelected = currentState.ariaSelected.filter(sel => !initialDOMState.ariaSelected.includes(sel));
    if (newAriaSelected.length > 0) {
      detectedChanges.push(`✅ Selected Elements: ${newAriaSelected.join(', ')}`);
      newAriaSelected.forEach(sel => newSuccessIndicators.push(sel));
    }

    // Report findings
    if (detectedChanges.length > 0) {
      log(`\n🎯 STATE CHANGES DETECTED ${checkLabel}:`, '#10b981', true);
      detectedChanges.forEach(change => {
        log(`   ${change}`, '#d1d5db');
      });

      if (newSuccessIndicators.length > 0) {
        log(`\n📝 Suggested Success Indicators:`, '#7C3AED', true);
        const uniqueIndicators = [...new Set(newSuccessIndicators)];
        log(`${uniqueIndicators.join(', ')}`, '#10b981');

        // Auto-populate the success indicators field
        const successField = document.getElementById('success-indicators');
        const currentIndicators = successField.value.trim();
        const combinedIndicators = currentIndicators ? 
          `${currentIndicators}, ${uniqueIndicators.join(', ')}` : 
          uniqueIndicators.join(', ');
        
        successField.value = combinedIndicators;
        
        // Update shortcode button states
        updateShortcodeButtonStates();
        
        // Save settings
        saveSettings();
        
        log(`✅ Success indicators auto-populated!`, '#10b981', true);
        
        // Check for YouTube URLs after successful state changes
        setTimeout(async () => {
          await checkForYouTubeUrls();
        }, 1000);
        
        // Stop listening after successful detection (on first check only)
        if (!isSecondCheck) {
          setTimeout(() => {
            log(`🎯 State changes detected! Click "Analyze Structure" to continue testing.`, '#7C3AED', true);
          }, 500);
        }
      }
    } else if (!isSecondCheck) {
      log(`❌ No significant state changes detected ${checkLabel}`, '#f59e0b');
      log(`💡 Try clicking a different element or wait for the extended check...`, '#6366f1');
    } else {
      log(`❌ No additional changes found in extended check`, '#f59e0b');
      log(`💡 The element might not trigger visible changes or may need manual indicators`, '#6366f1');
    }
  }

  debugLog('%c🔍 Universal Click Debugger Loaded (Secret Mode)', 'color: #7C3AED; font-size: 16px; font-weight: bold; background: rgba(124, 58, 237, 0.1); padding: 4px;');
  debugLog('%c✅ FEATURES CONFIRMED:', 'color: #10b981; font-size: 14px; font-weight: bold;');
  debugLog('   🔘 Pause Button: ALWAYS VISIBLE (no display:none)');
  debugLog('   🔧 Auto Debug Logging: ENABLED when Universal Debugger activates');
  debugLog('   🔍 UTM Parameters: FUNCTIONAL with super verbose logging');
  debugLog('   📊 Super Verbose Console Output: RESPECTS GLOBAL DEBUG MODE');
  debugLog('   🎧 State Change Listener: ADDED with auto-detection');

})(); 