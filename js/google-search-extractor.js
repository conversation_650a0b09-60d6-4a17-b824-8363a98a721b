// Google Search Results Extractor for GMB Data Extractor
// Handles Google search URLs with &sca_esv parameter containing maps listings
// Namespace check to prevent conflicts with other extensions
if (window.GMBGoogleSearchLoaded) {
  console.log('GMB Google Search: Already loaded, skipping...');
} else {
  window.GMBGoogleSearchLoaded = true;
  console.log('GMB Google Search: Extractor loaded (passive mode - manual trigger only)');

/**
 * Detects if the current page has coordinate data in the hash fragment
 * @returns {boolean} True if coordinates are found in the hash
 */
function hasCoordinatesInHash() {
  console.log('GMB Google Search: Checking for coordinates in hash...');
  
  try {
    const currentHash = window.location.hash;
    console.log('GMB Google Search: Current hash:', currentHash);
    
    // Check for coordinate pattern mv:[[ in hash
    if (currentHash.includes('mv:[[')) {
      console.log('GMB Google Search: Found coordinates pattern mv:[[ in hash');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('GMB Google Search: Error checking hash coordinates:', error);
    return false;
  }
}

/**
 * Detects if the current page is a Google search with maps listings
 * @returns {boolean} True if Google search with sca_esv parameter is detected
 */
function detectGoogleSearchMapsListings() {
  console.log('GMB Google Search: Checking for Google search maps listings...');
  
  try {
    const currentUrl = window.location.href;
    console.log('GMB Google Search: Current URL:', currentUrl);
    
    // Check if URL contains sca_esv parameter
    if (!currentUrl.includes('&sca_esv') && !currentUrl.includes('?sca_esv')) {
      console.log('GMB Google Search: No sca_esv parameter found');
      return false;
    }
    
    // Check if we're on google.com/search
    if (!currentUrl.includes('google.com/search')) {
      console.log('GMB Google Search: Not a Google search URL');
      return false;
    }
    
    // Check for various business listing containers
    const possibleContainers = [
      '.rl_full-list',
      '.commercial-unit-desktop-top',
      '.g',
      '[data-cid]',
      '.uEierd',
      '.VkpGBb'
    ];
    
    for (const selector of possibleContainers) {
      const container = document.querySelector(selector);
      if (container) {
        console.log(`GMB Google Search: Found container with selector: ${selector}`);
        
        // Check if this container has business-like content
        const hasBusinessContent = container.querySelector('[data-cid]') || 
                                  container.querySelector('.yi40Hd') || 
                                  container.querySelector('.OSrXXb') ||
                                  container.textContent.includes('★') ||
                                  container.textContent.includes('reviews');
        
        if (hasBusinessContent) {
          console.log('GMB Google Search: Found business content in container');
          return true;
        }
      }
    }
    
    // Alternative check: look for business cards anywhere on the page
    const businessCards = document.querySelectorAll('[data-cid]');
    if (businessCards && businessCards.length > 0) {
      console.log('GMB Google Search: Found business cards with data-cid');
      return true;
    }
    
    // Check for rating elements (indicates business listings)
    const ratingElements = document.querySelectorAll('.yi40Hd, .Aq14fc');
    if (ratingElements && ratingElements.length > 0) {
      console.log('GMB Google Search: Found rating elements');
      return true;
    }
    
    console.log('GMB Google Search: No maps listings detected');
    return false;
    
  } catch (error) {
    console.error('GMB Google Search: Error detecting Google search maps listings:', error);
    return false;
  }
}

/**
 * Extracts business data from Google search results
 * @returns {Array} Array of business data objects
 */
function extractGoogleSearchBusinessData() {
  console.log('GMB Google Search: Starting business data extraction...');
  
  try {
    // Find all business cards 
    const allCards = document.querySelectorAll('.VkpGBb, .rllt__link, .commercial-unit-desktop-top, .uMdZh');
    console.log(`GMB Google Search: Found ${allCards.length} total cards`);
    
    // Filter out sponsored cards for accurate business analysis
    const businessCards = Array.from(allCards);
    
    console.log(`GMB Google Search: Found ${businessCards.length} business cards (will exclude sponsored from analysis)`);
    
    if (businessCards.length === 0) {
      console.log('GMB Google Search: No business cards found');
      return {
        success: false,
        error: 'No business listings found on this page'
      };
    }
    
    const businesses = [];
    const seenBusinesses = new Set(); // Track seen business names to avoid duplicates
    
    console.log(`GMB Google Search: Processing ${businessCards.length} business cards (including sponsored)`);
    
    businessCards.forEach((card, index) => {
      try {
        const businessData = extractBusinessDataFromCard(card);
        if (businessData && businessData.name) {
          // Check for duplicates using business name
          const businessKey = businessData.name.toLowerCase().trim();
          
          if (seenBusinesses.has(businessKey)) {
            console.log(`GMB Google Search: Skipping duplicate business: ${businessData.name}`);
            return;
          }
          
          seenBusinesses.add(businessKey);
          businesses.push(businessData);
          
          // Log with sponsored status
          const sponsoredStatus = businessData.isSponsored ? '(SPONSORED)' : '(ORGANIC)';
          console.log(`GMB Google Search: Extracted data for business ${businesses.length}: ${businessData.name} ${sponsoredStatus}`);
        }
      } catch (error) {
        console.error(`GMB Google Search: Error extracting data from card ${index + 1}:`, error);
      }
    });
    
    console.log(`GMB Google Search: Successfully extracted data for ${businesses.length} unique businesses (including sponsored)`);
    return businesses;
    
  } catch (error) {
    console.error('GMB Google Search: Error extracting business data:', error);
    return [];
  }
}

/**
 * Enhanced sponsored detection for Google search results
 * @param {Element} element - The business card element to check
 * @returns {boolean} True if the element is sponsored
 */
function isSponsoredGoogleSearchResult(element) {
  if (!element) return false;
  
  // Method 1: Check for sponsored text in aria-label
  const ariaLabel = element.getAttribute('aria-label') || '';
  if (/sponsored|ad|advertisement/i.test(ariaLabel)) {
    return true;
  }
  
  // Method 2: Check for sponsored URLs
  const links = element.querySelectorAll('a[href]');
  for (const link of links) {
    const href = link.getAttribute('href') || '';
    if (href.includes('googleadservices.com') || 
        href.includes('google.com/aclk') || 
        href.includes('google.com/pagead')) {
      return true;
    }
  }
  
  // Method 3: Check for sponsored text in element content
  const walker = document.createTreeWalker(
    element,
    NodeFilter.SHOW_TEXT,
    null,
    false
  );
  
  let textNode;
  while (textNode = walker.nextNode()) {
    const text = textNode.textContent.trim();
    if (/\b(sponsored|ad|advertisement)\b/i.test(text)) {
      return true;
    }
  }
  
  // Method 4: Check for specific ad-related classes
  const adClasses = ['.ads-ad', '.commercial-unit-desktop-top', '[data-is-ad="1"]'];
  for (const adClass of adClasses) {
    if (element.querySelector(adClass)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Extracts business data from a single business card
 * @param {Element} card - The business card element
 * @returns {Object} Business data object
 */
function extractBusinessDataFromCard(card) {
  try {
    const data = {
      name: '',
      rating: '',
      reviewCount: '',
      category: '',
      address: '',
      phone: '',
      hours: '',
      website: '',
      cid: '',
      isAd: false,
      isSponsored: false,
      originalReviewText: ''
    };
    
    // Extract CID
    data.cid = card.getAttribute('data-cid') || '';
    
    // Enhanced sponsored detection using tracked domains system approach
    data.isSponsored = isSponsoredGoogleSearchResult(card);
    
    // Legacy ad detection for backwards compatibility
    data.isAd = card.querySelector('[data-is-ad="1"]') !== null || 
               card.querySelector('.ads-ad') !== null ||
               card.textContent.includes('Ad') ||
               data.isSponsored;
    
    // Extract business name - try multiple selectors
    const nameSelectors = [
      '.OSrXXb.RfnDt.o5Th0.E3DyYd.VbTUod',
      '.vwVdIc.wzN8Ac.rllt__link.a-no-hover-decoration .OSrXXb',
      '.OSrXXb', // Generic business name selector (works with Google Search)
      '.BNeawe.vvjwJb.AP7Wnd',
      '.sVXRqc',
      'h3', // Common heading selector for Google Search
      '.LC20lb', // Google Search title selector
      '.DKV0Md' // Additional Google Search selector
    ];
    
    for (const selector of nameSelectors) {
      const nameElement = card.querySelector(selector);
      if (nameElement && nameElement.textContent.trim()) {
        data.name = nameElement.textContent.trim();
        break;
      }
    }
    
    // Extract rating - try multiple selectors
    const ratingSelectors = [
      '.yi40Hd.YrbPuc',
      '.yi40Hd',
      '.Aq14fc',
      '.BNeawe.UPmit.AP7Wnd'
    ];
    
    for (const selector of ratingSelectors) {
      const ratingElement = card.querySelector(selector);
      if (ratingElement) {
        const ratingText = ratingElement.textContent.trim();
        const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
        if (ratingMatch) {
          data.rating = parseFloat(ratingMatch[1]);
          break;
        }
      }
    }
    
    // Extract review count - try multiple selectors
    const reviewCountSelectors = [
      '.RDApEe.YrbPuc',
      '.RDApEe',
      '.hqzQac'
    ];
    
    let reviewCount = 0;
    let originalReviewText = '';
    for (const selector of reviewCountSelectors) {
      const reviewCountElement = card.querySelector(selector);
      if (reviewCountElement) {
        const reviewText = reviewCountElement.textContent.trim();
        originalReviewText = reviewText; // Store for debugging
        
        // Handle different formats: (123), (1.2K), (2,345), etc.
        let reviewMatch = reviewText.match(/\(([0-9,.K]+)\)/i);
        if (reviewMatch) {
          let countStr = reviewMatch[1];
          console.log(`GMB Google Search: Found review count in parentheses: "${countStr}" from text: "${reviewText}"`);
          
          // Handle K notation (e.g., "2.1K" = 2100)
          if (countStr.toUpperCase().includes('K')) {
            const kValue = parseFloat(countStr.replace(/K/i, ''));
            reviewCount = Math.round(kValue * 1000);
            console.log(`GMB Google Search: Converted ${countStr} to ${reviewCount}`);
          } else {
            // Handle regular numbers with commas
            reviewCount = parseInt(countStr.replace(/,/g, ''));
            console.log(`GMB Google Search: Parsed regular number ${countStr} to ${reviewCount}`);
          }
          break;
        }
        
        // Fallback: try to match any number pattern including K
        reviewMatch = reviewText.match(/(\d+(?:[.,]\d+)*[K]?)/i);
        if (reviewMatch) {
          let countStr = reviewMatch[1];
          console.log(`GMB Google Search: Found review count (fallback): "${countStr}" from text: "${reviewText}"`);
          
          if (countStr.toUpperCase().includes('K')) {
            const kValue = parseFloat(countStr.replace(/K/i, ''));
            reviewCount = Math.round(kValue * 1000);
            console.log(`GMB Google Search: Converted ${countStr} to ${reviewCount}`);
          } else {
            reviewCount = parseInt(countStr.replace(/,/g, ''));
            console.log(`GMB Google Search: Parsed regular number ${countStr} to ${reviewCount}`);
          }
          break;
        }
      }
    }
    
    // Extract category and other details
    const detailSelectors = [
      '.rllt__details > div',
      '.BNeawe.s3v9rd.AP7Wnd',
      '.VuuXrf'
    ];
    
    for (const selector of detailSelectors) {
      const elements = card.querySelectorAll(selector);
      for (const element of elements) {
        const text = element.textContent.trim();
        
        // Skip empty text or rating text
        if (!text || text.includes('★') || text.includes('Rating:')) continue;
        
        // Extract category - look for text after the rating/review info
        if (!data.category) {
          // First try to find category in Y0A0hc element (most reliable)
          const categoryElement = element.querySelector('.Y0A0hc');
          if (categoryElement) {
            const categoryText = categoryElement.textContent.trim();
            // Extract text after the rating and review count
            const categoryMatch = categoryText.match(/\)\s*·\s*(.+)$/);
            if (categoryMatch && categoryMatch[1]) {
              data.category = categoryMatch[1].trim();
              break;
            }
          }
          
          // Fallback: look for category in text with · separator
          if (!data.category && text.includes('·')) {
            const parts = text.split('·');
            for (const part of parts) {
              const trimmedPart = part.trim();
              // Skip parts that look like ratings, review counts, or location info
              if (trimmedPart && 
                  !trimmedPart.match(/^\d+\.?\d*$/) && // Skip pure numbers (ratings)
                  !trimmedPart.match(/^\(\d+[\d,]*\)$/) && // Skip review counts like (123)
                  !trimmedPart.includes('years in business') && 
                  !trimmedPart.includes('Open') && 
                  !trimmedPart.includes('Closed') &&
                  !trimmedPart.includes('VIC') &&
                  !trimmedPart.includes('NSW') &&
                  !trimmedPart.includes('QLD') &&
                  !trimmedPart.includes('SA') &&
                  !trimmedPart.includes('WA') &&
                  !trimmedPart.includes('TAS') &&
                  !trimmedPart.includes('NT') &&
                  !trimmedPart.includes('ACT') &&
                  !trimmedPart.match(/\d{4}/) && // Skip postcodes
                  !trimmedPart.includes('★') && // Skip rating symbols
                  !trimmedPart.match(/^\d+\.?\d*\s*\(/) && // Skip "4.9(123)" format
                  trimmedPart.length > 2) { // Must be more than 2 characters
                data.category = trimmedPart;
                break;
              }
            }
          }
        }
        
        // Extract address (contains state abbreviations or postcodes)
        if (!data.address && (text.includes('VIC') || text.includes('NSW') || text.includes('QLD') || 
                             text.includes('SA') || text.includes('WA') || text.includes('TAS') || 
                             text.includes('NT') || text.includes('ACT') || text.match(/\d{4}/))) {
          if (text.includes('·')) {
            const parts = text.split('·');
            for (const part of parts) {
              const trimmedPart = part.trim();
              if (trimmedPart && (trimmedPart.includes('VIC') || trimmedPart.includes('NSW') || 
                                 trimmedPart.includes('QLD') || trimmedPart.includes('SA') || 
                                 trimmedPart.includes('WA') || trimmedPart.includes('TAS') || 
                                 trimmedPart.includes('NT') || trimmedPart.includes('ACT') ||
                                 trimmedPart.match(/\d{4}/))) {
                data.address = trimmedPart;
                break;
              }
            }
          } else {
            data.address = text;
          }
        }
        
        // Extract phone number
        if (!data.phone) {
          const phoneMatch = text.match(/(\(?\d{2,4}\)?\s?\d{3,4}\s?\d{3,4}|\d{4}\s?\d{3}\s?\d{3})/);
          if (phoneMatch) {
            data.phone = phoneMatch[0];
          }
        }
        
        // Extract hours
        if (!data.hours && (text.includes('Open') || text.includes('Closed') || 
                           text.includes('24 hours') || text.includes('am') || text.includes('pm'))) {
          data.hours = text;
        }
      }
    }
    
    // Extract website
    const websiteLink = card.querySelector('a[href*="http"]');
    if (websiteLink && !websiteLink.href.includes('google.com') && !websiteLink.href.includes('maps.google.com')) {
      data.website = websiteLink.href;
    }
    
    // If no name found, skip this card
    if (!data.name) {
      console.log('GMB Google Search: Skipping card with no business name');
      return null;
    }
    
    return data;
    
  } catch (error) {
    console.error('GMB Google Search: Error extracting business data from card:', error);
    return null;
  }
}

/**
 * Creates a CSV download link with proper visibility handling
 * @param {string} csvContent - The CSV content to download
 * @param {string} filename - The filename for the download
 */
function downloadCSV(csvContent, filename) {
  console.log(`GMB Google Search: Downloading CSV file: ${filename}`);
  
  try {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.classList.add('gmb-search-extractor__hidden');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    console.log('GMB Google Search: CSV file download initiated successfully');
  } catch (error) {
    console.error('GMB Google Search: Error downloading CSV file:', error);
    throw error;
  }
}

/**
 * Review Analysis for Google Search Results
 */
class GoogleSearchReviewAnalyzer {
  constructor() {
    this.isRunning = false;
    this.reviewData = [];
    this.totalListings = 0;
  }

  async startAnalysis() {
    if (this.isRunning) {
      console.log('🔄 GOOGLE SEARCH REVIEW ANALYSIS: Analysis already running');
      return;
    }

    this.isRunning = true;
    this.reviewData = [];
    this.totalListings = 0;

    try {
      console.log('🚀 GOOGLE SEARCH REVIEW ANALYSIS: Starting analysis...');
      
      // First check if we have coordinates (lat/long) which guarantees business listings
      const hasCoordinates = hasCoordinatesInHash();
      if (!hasCoordinates) {
        console.log('❌ GOOGLE SEARCH REVIEW ANALYSIS: No coordinates found - this appears to be a regular search page without business listings');
        
        // Send halt message to popup
        chrome.runtime.sendMessage({
          type: 'reviewAnalysisHalt',
          message: 'No business listings found on this search page',
          reason: 'no_coordinates'
        });
        
        this.isRunning = false;
        return;
      }
      
      console.log('✅ GOOGLE SEARCH REVIEW ANALYSIS: Coordinates found - proceeding with business listings analysis');

      // Find all business cards (including sponsored listings)
      const allCards = document.querySelectorAll('.VkpGBb, .rllt__link, .commercial-unit-desktop-top, .uMdZh, .g');
      console.log(`🔍 GOOGLE SEARCH REVIEW ANALYSIS: Found ${allCards.length} total cards`);
      
      // Include all cards (including sponsored listings)
      const businessCards = Array.from(allCards);
      
      console.log(`✅ GOOGLE SEARCH REVIEW ANALYSIS: Found ${businessCards.length} business cards (including sponsored)`);

      if (businessCards.length === 0) {
        throw new Error('No business cards found on this page');
      }

      this.totalListings = businessCards.length;
      console.log(`✅ GOOGLE SEARCH REVIEW ANALYSIS: Found ${this.totalListings} business listings to analyze (including sponsored)`);

      // Send initial status to popup
      this.sendStatusUpdate('started', {
        total: this.totalListings,
        current: 0,
        data: []
      });

      // Extract review data from all business cards
      await this.extractAllReviewDataFromCards(businessCards);
      
      // Create aggregate summary
      const aggregateData = this.createAggregateData();
      console.log('GOOGLE SEARCH REVIEW ANALYSIS: Aggregate data:', aggregateData);

      // Analysis complete
      console.log('🎉 GOOGLE SEARCH REVIEW ANALYSIS: Analysis completed successfully!');
      this.sendStatusUpdate('completed', {
        total: this.totalListings,
        current: this.totalListings,
        data: this.reviewData
      });

    } catch (error) {
      console.error('💥 GOOGLE SEARCH REVIEW ANALYSIS: Error during analysis:', error);
      this.sendStatusUpdate('error', {
        error: error.message,
        data: this.reviewData
      });
    } finally {
      this.isRunning = false;
    }
  }

  async extractAllReviewDataFromCards(businessCards) {
    console.log('🔄 GOOGLE SEARCH REVIEW ANALYSIS: Extracting review data from all cards...');
    
    const seenBusinesses = new Set(); // Track seen business names to avoid duplicates
    
    businessCards.forEach((card, index) => {
      try {
        const reviewData = this.extractReviewDataFromCard(card, index);
        if (reviewData && reviewData.businessName) {
          // Check for duplicates using business name
          const businessKey = reviewData.businessName.toLowerCase().trim();
          
          if (seenBusinesses.has(businessKey)) {
            console.log(`🔄 GOOGLE SEARCH REVIEW ANALYSIS: Skipping duplicate business: ${reviewData.businessName}`);
            return;
          }
          
          seenBusinesses.add(businessKey);
          this.reviewData.push(reviewData);
          
          const sponsoredStatus = reviewData.isSponsored ? '(SPONSORED)' : '(ORGANIC)';
          console.log(`GOOGLE SEARCH REVIEW ANALYSIS: Extracted review data for ${reviewData.businessName} ${sponsoredStatus}`);
        }
      } catch (error) {
        console.error(`💥 GOOGLE SEARCH REVIEW ANALYSIS: Error extracting review data from card ${index + 1}:`, error);
      }
    });

    const organicCount = this.reviewData.filter(item => !item.isSponsored).length;
    const sponsoredCount = this.reviewData.length - organicCount;
    console.log(`✅ GOOGLE SEARCH REVIEW ANALYSIS: Extracted review data for ${this.reviewData.length} unique businesses (${organicCount} organic, ${sponsoredCount} sponsored)`);
  }

  extractReviewDataFromCard(card, index) {
    try {
      // Check if this business is sponsored
      const isSponsored = isSponsoredGoogleSearchResult(card);
      
      // Extract business name - try multiple selectors
      const nameSelectors = [
        '.OSrXXb',
        '.dbg0pd .OSrXXb',
        'h3',
        '.LC20lb',
        '.DKV0Md',
        '.BNeawe.vvjwJb.AP7Wnd'
      ];
      
      let businessName = `Business ${index + 1}`;
      for (const selector of nameSelectors) {
        const nameElement = card.querySelector(selector);
        if (nameElement && nameElement.textContent.trim()) {
          businessName = nameElement.textContent.trim();
          break;
        }
      }
      
      // Extract rating - try multiple selectors
      const ratingSelectors = [
        '.yi40Hd.YrbPuc',
        '.yi40Hd',
        '.Aq14fc',
        '.BNeawe.UPmit.AP7Wnd'
      ];
      
      let rating = 0;
      for (const selector of ratingSelectors) {
        const ratingElement = card.querySelector(selector);
        if (ratingElement) {
          const ratingText = ratingElement.textContent.trim();
          const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
          if (ratingMatch) {
            rating = parseFloat(ratingMatch[1]);
            break;
          }
        }
      }
      
      // Extract review count - try multiple selectors
      const reviewCountSelectors = [
        '.RDApEe.YrbPuc',
        '.RDApEe',
        '.hqzQac'
      ];
      
      let reviewCount = 0;
      let originalReviewText = '';
      for (const selector of reviewCountSelectors) {
        const reviewCountElement = card.querySelector(selector);
        if (reviewCountElement) {
          const reviewText = reviewCountElement.textContent.trim();
          originalReviewText = reviewText; // Store for debugging
          
          // Handle different formats: (123), (1.2K), (2,345), etc.
          let reviewMatch = reviewText.match(/\(([0-9,.K]+)\)/i);
          if (reviewMatch) {
            let countStr = reviewMatch[1];
            console.log(`GMB Google Search: Found review count in parentheses: "${countStr}" from text: "${reviewText}"`);
            
            // Handle K notation (e.g., "2.1K" = 2100)
            if (countStr.toUpperCase().includes('K')) {
              const kValue = parseFloat(countStr.replace(/K/i, ''));
              reviewCount = Math.round(kValue * 1000);
              console.log(`GMB Google Search: Converted ${countStr} to ${reviewCount}`);
            } else {
              // Handle regular numbers with commas
              reviewCount = parseInt(countStr.replace(/,/g, ''));
              console.log(`GMB Google Search: Parsed regular number ${countStr} to ${reviewCount}`);
            }
            break;
          }
          
          // Fallback: try to match any number pattern including K
          reviewMatch = reviewText.match(/(\d+(?:[.,]\d+)*[K]?)/i);
          if (reviewMatch) {
            let countStr = reviewMatch[1];
            console.log(`GMB Google Search: Found review count (fallback): "${countStr}" from text: "${reviewText}"`);
            
            if (countStr.toUpperCase().includes('K')) {
              const kValue = parseFloat(countStr.replace(/K/i, ''));
              reviewCount = Math.round(kValue * 1000);
              console.log(`GMB Google Search: Converted ${countStr} to ${reviewCount}`);
            } else {
              reviewCount = parseInt(countStr.replace(/,/g, ''));
              console.log(`GMB Google Search: Parsed regular number ${countStr} to ${reviewCount}`);
            }
            break;
          }
        }
      }
      
      // Extract category
      let category = '';
      const detailSelectors = [
        '.rllt__details > div',
        '.BNeawe.s3v9rd.AP7Wnd',
        '.VuuXrf'
      ];
      
      for (const selector of detailSelectors) {
        const elements = card.querySelectorAll(selector);
        
        for (const element of elements) {
          const text = element.textContent.trim();
          
          // Skip empty text or rating text
          if (!text || text.includes('★') || text.includes('Rating:')) continue;
          
          // Extract category - look for text after the rating/review info
          if (!category) {
            // First try to find category in Y0A0hc element (most reliable)
            const categoryElement = element.querySelector('.Y0A0hc');
            if (categoryElement) {
              const categoryText = categoryElement.textContent.trim();
              // Extract text after the rating and review count
              const categoryMatch = categoryText.match(/\)\s*·\s*(.+)$/);
              if (categoryMatch && categoryMatch[1]) {
                category = categoryMatch[1].trim();
                break;
              }
            }
            
            // Fallback: look for category in text with · separator
            if (!category && text.includes('·')) {
              const parts = text.split('·');
              for (const part of parts) {
                const trimmedPart = part.trim();
                // Skip parts that look like ratings, review counts, or location info
                if (trimmedPart && 
                    !trimmedPart.match(/^\d+\.?\d*$/) && // Skip pure numbers (ratings)
                    !trimmedPart.match(/^\(\d+[\d,]*\)$/) && // Skip review counts like (123)
                    !trimmedPart.includes('years in business') && 
                    !trimmedPart.includes('Open') && 
                    !trimmedPart.includes('Closed') &&
                    !trimmedPart.includes('VIC') &&
                    !trimmedPart.includes('NSW') &&
                    !trimmedPart.includes('QLD') &&
                    !trimmedPart.includes('SA') &&
                    !trimmedPart.includes('WA') &&
                    !trimmedPart.includes('TAS') &&
                    !trimmedPart.includes('NT') &&
                    !trimmedPart.includes('ACT') &&
                    !trimmedPart.match(/\d{4}/) && // Skip postcodes
                    !trimmedPart.includes('★') && // Skip rating symbols
                    !trimmedPart.match(/^\d+\.?\d*\s*\(/) && // Skip "4.9(123)" format
                    trimmedPart.length > 2) { // Must be more than 2 characters
                  category = trimmedPart;
                  break;
                }
              }
            }
          }
          
          if (category) break;
        }
        if (category) break;
      }
      
      // Extract sample review if available
      let sampleReview = '';
      const reviewSelectors = [
        '.uDyWh.OSrXXb.btbrud',
        '.BNeawe.s3v9rd.AP7Wnd',
        '.st'
      ];
      
      for (const selector of reviewSelectors) {
        const reviewElement = card.querySelector(selector);
        if (reviewElement) {
          const reviewText = reviewElement.textContent.trim();
          if (reviewText && !reviewText.includes('★') && reviewText.length > 10) {
            sampleReview = reviewText.replace(/"/g, '');
            break;
          }
        }
      }
      
      return {
        businessName: businessName,
        rating: rating,
        reviewCount: reviewCount,
        category: category,
        sampleReview: sampleReview,
        originalReviewText: originalReviewText,
        isSponsored: isSponsored,
        extractedAt: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('GMB Google Search: Error extracting review data from card:', error);
      return null;
    }
  }

  createAggregateData() {
    if (this.reviewData.length === 0) {
      return {
        totalBusinesses: 0,
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: {},
        categoryBreakdown: {},
        sponsoredExcluded: 0
      };
    }

    // Filter out sponsored results for accurate organic analysis
    const organicData = this.reviewData.filter(item => !item.isSponsored);
    const sponsoredCount = this.reviewData.length - organicData.length;
    
    if (organicData.length === 0) {
      return {
        totalBusinesses: 0,
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: {},
        categoryBreakdown: {},
        sponsoredExcluded: sponsoredCount
      };
    }

    const totalBusinesses = organicData.length;
    const totalReviews = organicData.reduce((sum, business) => sum + business.reviewCount, 0);
    const averageRating = organicData.reduce((sum, business) => sum + business.rating, 0) / totalBusinesses;

    // Debug: Log the first 5 organic businesses and their review counts
    console.log('🔍 GOOGLE SEARCH REVIEW ANALYSIS: First 5 organic businesses review counts:');
    organicData.slice(0, 5).forEach((business, index) => {
      console.log(`${index + 1}. ${business.businessName}: ${business.reviewCount} reviews (original text: "${business.originalReviewText}") - ORGANIC`);
    });

    // Rating distribution using ORGANIC data only
    const ratingDistribution = {};
    organicData.forEach(business => {
      let ratingRange;
      
      if (!business.rating || business.rating === 0) {
        ratingRange = '0'; // No rating
      } else if (business.rating >= 5.0) {
        ratingRange = '5'; // Exactly 5.0 stars
      } else if (business.rating >= 4.0) {
        ratingRange = '4'; // 4.0-4.9 stars
      } else if (business.rating >= 3.0) {
        ratingRange = '3'; // 3.0-3.9 stars
      } else if (business.rating >= 2.0) {
        ratingRange = '2'; // 2.0-2.9 stars
      } else if (business.rating >= 1.0) {
        ratingRange = '1'; // 1.0-1.9 stars
      } else {
        ratingRange = '0'; // No rating or below 1.0
      }
      
      ratingDistribution[ratingRange] = (ratingDistribution[ratingRange] || 0) + 1;
    });

    // Category breakdown using ORGANIC data only
    const categoryBreakdown = {};
    organicData.forEach(business => {
      if (business.category && business.category.toLowerCase() !== 'favourites') {
        categoryBreakdown[business.category] = (categoryBreakdown[business.category] || 0) + 1;
      }
    });

    return {
      totalBusinesses,
      averageRating: parseFloat(averageRating.toFixed(2)),
      totalReviews,
      ratingDistribution,
      categoryBreakdown,
      sponsoredExcluded: sponsoredCount,
      extractedAt: new Date().toISOString()
    };
  }

  sendStatusUpdate(status, data) {
    try {
      chrome.runtime.sendMessage({
        action: 'googleSearchReviewAnalysisUpdate',
        status: status,
        data: data
      });
    } catch (error) {
      console.error('GMB Google Search: Error sending status update:', error);
    }
  }

  stopAnalysis() {
    this.isRunning = false;
    this.sendStatusUpdate('stopped', {
      total: this.totalListings,
      current: this.reviewData.length,
      data: this.reviewData
    });
  }

  getCurrentData() {
    return {
      reviewData: this.reviewData,
      aggregateData: this.createAggregateData(),
      isRunning: this.isRunning
    };
  }

  exportToCSV() {
    if (this.reviewData.length === 0) {
      console.log('GMB Google Search: No review data to export');
      return;
    }

    const csvContent = this.convertReviewsToCSV(this.reviewData);
    const filename = `google_search_review_analysis_${new Date().toISOString().split('T')[0]}.csv`;
    downloadCSV(csvContent, filename);
  }

  convertReviewsToCSV(reviews) {
    const headers = ['#', 'Business Name', 'Rating', 'Reviews', 'Category', 'Sample Review'];
    const csvRows = [headers.join(',')];

    reviews.forEach((review, index) => {
      const row = [
        index + 1, // Index number starting from 1
        this.escapeCsvValue(review.businessName),
        review.rating,
        review.reviewCount,
        this.escapeCsvValue(review.category),
        this.escapeCsvValue(review.sampleReview)
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  escapeCsvValue(value) {
    if (value === null || value === undefined) return '';
    const stringValue = String(value);
    if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }
    return stringValue;
  }
}

// Create global instance
window.googleSearchReviewAnalyzer = new GoogleSearchReviewAnalyzer();

/**
 * Google Search Services/Categories Extractor
 * Extracts services and categories from business listings in Google Search
 */
class GoogleSearchServicesExtractor {
  constructor() {
    this.isRunning = false;
    this.servicesData = [];
    this.currentBusinessIndex = 0;
    this.totalBusinesses = 0;
    this.maxBusinessesToProcess = 5; // Default value, will be updated from user input
    this.dockedInterface = null;
    this.primaryBusinessName = null; // Track primary business name for filename
    // New extraction settings
    this.extractionSettings = {
      products: true,
      categories: true,
      qa: true
    };
  }

  async startServicesExtraction() {
    console.log('🚀 GOOGLE SEARCH SERVICES: startServicesExtraction called');
    
    try {
      // If already running, don't start again
      if (this.isRunning) {
        console.log('🔄 GOOGLE SEARCH SERVICES: Extraction already running');
        return;
      }

      // Check if we're on a Google Search page or Pro List page
      const isGoogleSearchPage = window.location.href.includes('google.com/search');
      const isProListPage = window.location.href.includes('google.com/localservices/prolist');
      
      console.log('🔍 GOOGLE SEARCH SERVICES: Page check - Google Search:', isGoogleSearchPage, 'Pro List:', isProListPage);
      
      if (!isGoogleSearchPage && !isProListPage) {
        console.error('🚫 GOOGLE SEARCH SERVICES: Not on a supported page');
        alert('This feature works on Google Search pages with business listings or Google Local Services Pro List pages. Please navigate to an appropriate page.');
        return;
      }

      // Add visual numbering to business cards
      console.log('🔢 GOOGLE SEARCH SERVICES: Adding visual numbering...');
      this.addVisualNumberingToBusinessCards();

      // Create docked interface if it doesn't exist
      if (!this.dockedInterface) {
        console.log('🚀 GOOGLE SEARCH SERVICES: Creating docked interface...');
        try {
          this.createDockedInterface();
          console.log('✅ GOOGLE SEARCH SERVICES: Docked interface created successfully');
        } catch (interfaceError) {
          console.error('💥 GOOGLE SEARCH SERVICES: Error creating docked interface:', interfaceError);
          throw interfaceError;
        }
      } else {
        console.log('🔄 GOOGLE SEARCH SERVICES: Docked interface already exists');
      }
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error in startServicesExtraction:', error);
      alert('Error starting services extraction: ' + error.message);
    }
  }

  addVisualNumberingToBusinessCards() {
    try {
      console.log('🔢 GOOGLE SEARCH SERVICES: Adding visual numbering to business cards...');
      
      // Find business cards with the specific class for clickable links
      const businessCards = document.querySelectorAll('.vwVdIc.wzN8Ac.rllt__link.a-no-hover-decoration');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${businessCards.length} business cards to number`);

      businessCards.forEach((card, index) => {
        try {
          // Create number badge using semantic HTML and BEM classes
          const numberBadge = document.createElement('div');
          numberBadge.className = 'gmb-search-extractor__business-number';
          numberBadge.textContent = (index + 1).toString();
          numberBadge.setAttribute('aria-label', `Business number ${index + 1}`);
          numberBadge.setAttribute('data-business-number', index + 1);

          // Make sure the parent card has relative positioning using CSS class
          const cardParent = card.parentElement || card;
          if (!cardParent.classList.contains('gmb-search-extractor__card-relative')) {
            cardParent.classList.add('gmb-search-extractor__card-relative');
          }

          // Add the badge to the card
          cardParent.appendChild(numberBadge);
          
          console.log(`🔢 Added number ${index + 1} to business card`);
        } catch (error) {
          console.error(`💥 Error adding number to business card ${index + 1}:`, error);
        }
      });

      console.log('✅ GOOGLE SEARCH SERVICES: Visual numbering completed');
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error adding visual numbering:', error);
    }
  }

  async beginExtraction() {
    if (this.isRunning) {
      console.log('🔄 GOOGLE SEARCH SERVICES: Extraction already running');
      return;
    }

    this.isRunning = true;
    this.servicesData = [];
    this.currentBusinessIndex = 0;
    this.totalBusinesses = 0;

    try {
      console.log('🚀 GOOGLE SEARCH SERVICES: Starting services extraction...');

      // Parse range input and get extraction settings
      let businessIndicesToProcess = [];
      let reviewsMaxLimit = null;
      
      if (this.dockedInterface) {
        const rangeInput = this.dockedInterface.querySelector('#gmb-services-business-range');
        if (rangeInput) {
          const rangeValue = rangeInput.value.trim();
          businessIndicesToProcess = this.parseBusinessRange(rangeValue);
          console.log(`🎯 GOOGLE SEARCH SERVICES: Parsed range "${rangeValue}" to indices:`, businessIndicesToProcess);
        }

        // Get extraction settings
        const productsCheckbox = this.dockedInterface.querySelector('#gmb-services-extract-products');
        const categoriesCheckbox = this.dockedInterface.querySelector('#gmb-services-extract-categories');
        const qaCheckbox = this.dockedInterface.querySelector('#gmb-services-extract-qa');
        const reviewsCheckbox = this.dockedInterface.querySelector('#gmb-services-extract-reviews');
        
        this.extractionSettings = {
          products: productsCheckbox ? productsCheckbox.checked : true,
          categories: categoriesCheckbox ? categoriesCheckbox.checked : true,
          qa: qaCheckbox ? qaCheckbox.checked : true,
          reviews: reviewsCheckbox ? reviewsCheckbox.checked : false
        };

        // Get review range limit if reviews extraction is selected
        if (this.extractionSettings.reviews) {
          const reviewRangeInput = this.dockedInterface.querySelector('#gmb-services-review-range');
          if (reviewRangeInput && reviewRangeInput.value.trim()) {
            const reviewLimit = parseInt(reviewRangeInput.value.trim());
            if (!isNaN(reviewLimit) && reviewLimit > 0) {
              reviewsMaxLimit = reviewLimit;
              console.log(`🎯 GOOGLE SEARCH SERVICES: Review limit set to: ${reviewsMaxLimit}`);
            }
          }
        }

        console.log('🎯 GOOGLE SEARCH SERVICES: Extraction settings:', this.extractionSettings);
      }

      // SPECIAL HANDLING FOR REVIEWS EXTRACTION
      if (this.extractionSettings.reviews) {
        console.log('⭐ GOOGLE SEARCH SERVICES: Reviews extraction selected - using Multiple Review Scraper');
        
        // Don't close the interface immediately - keep it open for status updates
        // this.closeDockedInterface();
        
        console.log('⭐ GOOGLE SEARCH SERVICES: Starting multiple review extraction...');
        
        // Process multiple businesses according to the range
        await this.extractReviewsFromMultipleBusinesses(businessIndicesToProcess, reviewsMaxLimit);
        
        // IMPORTANT: Return early to prevent running regular extraction logic
        return;
        
      } else {
        // REGULAR SERVICES/PRODUCTS/QA EXTRACTION (existing logic)
        // Keep the docked interface open - don't close it automatically
        console.log('🔧 GOOGLE SEARCH SERVICES: Keeping interface open for services/products/QA extraction');
      }

      // REGULAR SERVICES/PRODUCTS/QA EXTRACTION (existing logic)
      if (businessIndicesToProcess.length === 0) {
        throw new Error('Invalid range specified. Please enter a valid range (e.g., "1-5", "3-7") or single number (e.g., "5").');
      }

      this.maxBusinessesToProcess = businessIndicesToProcess.length;

      // Get all business cards with the same selector as numbering
      const allBusinessCards = document.querySelectorAll('.vwVdIc.wzN8Ac.rllt__link.a-no-hover-decoration');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${allBusinessCards.length} total business cards`);

      // Include all cards (including sponsored listings)
      const businessCards = Array.from(allBusinessCards);

      console.log(`✅ GOOGLE SEARCH SERVICES: Found ${businessCards.length} business cards (including sponsored)`);

      if (businessCards.length === 0) {
        throw new Error('No business cards found with the required class. Make sure you\'re on a Google Search page with business listings.');
      }

      // Validate that the requested indices exist
      const maxAvailableIndex = businessCards.length;
      const validIndices = businessIndicesToProcess.filter(index => index >= 1 && index <= maxAvailableIndex);
      
      if (validIndices.length === 0) {
        throw new Error(`No valid business indices found. Available businesses: 1-${maxAvailableIndex}, requested: ${businessIndicesToProcess.join(', ')}`);
      }

      if (validIndices.length !== businessIndicesToProcess.length) {
        const invalidIndices = businessIndicesToProcess.filter(index => index < 1 || index > maxAvailableIndex);
        console.warn(`⚠️ GOOGLE SEARCH SERVICES: Some indices are out of range and will be skipped: ${invalidIndices.join(', ')}`);
      }

      this.totalBusinesses = validIndices.length;
      console.log(`✅ GOOGLE SEARCH SERVICES: Will process ${this.totalBusinesses} businesses with indices: ${validIndices.join(', ')}`);

      // Update docked interface
      this.updateDockedInterface('Starting extraction...', {
        total: this.totalBusinesses,
        current: 0,
        data: []
      });

      // Process each selected business card
      for (let i = 0; i < validIndices.length; i++) {
        const businessIndex = validIndices[i] - 1; // Convert from 1-based to 0-based
        this.currentBusinessIndex = i;
        console.log(`🔄 GOOGLE SEARCH SERVICES: Processing business ${validIndices[i]} (${i + 1}/${this.totalBusinesses})`);
        
        try {
          await this.processBusinessCard(businessCards[businessIndex], businessIndex);
        } catch (error) {
          console.error(`💥 GOOGLE SEARCH SERVICES: Error processing business ${validIndices[i]}:`, error);
        }

        // Update progress
        this.updateDockedInterface(`Processed ${i + 1} of ${this.totalBusinesses} businesses...`, {
          total: this.totalBusinesses,
          current: i + 1,
          data: this.servicesData
        });

        // Add delay between businesses to avoid overwhelming the page
        if (i < this.totalBusinesses - 1) {
          await this.delay(4000); // Increased from 2000 to 4000ms for better UI settling
        }
      }

      // Extraction complete
      console.log('🎉 GOOGLE SEARCH SERVICES: Services extraction completed successfully!');
      this.updateDockedInterface('✅ Complete! Click Export CSV or X to close.', {
        total: this.totalBusinesses,
        current: this.totalBusinesses,
        data: this.servicesData
      });

      // Send browser notification for successful completion
      this.sendCompletionNotification('success', this.totalBusinesses, this.servicesData);
      
      console.log('✅ GOOGLE SEARCH SERVICES: Extraction complete - interface stays open until manually closed');
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error during extraction:', error);
      this.updateDockedInterface(`❌ Error: ${error.message}. Click X to close.`, {
        error: error.message,
        data: this.servicesData
      });

      // Send browser notification for error
      this.sendCompletionNotification('error', this.servicesData.length, this.servicesData, error.message);
      
      console.log('❌ GOOGLE SEARCH SERVICES: Error occurred - interface stays open until manually closed');
    } finally {
      this.isRunning = false;
    }
  }

  sendCompletionNotification(status, businessCount, servicesData, errorMessage = null) {
    try {
      if (status === 'success') {
        // Calculate totals for the notification
        let totalProducts = 0;
        let totalCategories = 0;
        let totalServices = 0;
        let totalQa = 0;
        
        servicesData.forEach(business => {
          if (business.products) {
            totalProducts += business.products.length;
          }
          totalCategories += business.categories.length;
          business.categories.forEach(category => {
            totalServices += category.services.length;
          });
          totalQa += business.qa.length;
        });

        const message = `Successfully scraped ${businessCount} businesses!\n` +
                       `Products: ${totalProducts} • Categories: ${totalCategories} • Services: ${totalServices} • Q&A: ${totalQa}`;

        chrome.runtime.sendMessage({
          action: 'showNotification',
          title: '🎉 Google Search Scraping Complete!',
          message: message,
          type: 'basic',
          iconUrl: '/images/icon48.png'
        });

        console.log('🔔 GOOGLE SEARCH SERVICES: Success notification sent');
      } else if (status === 'error') {
        const message = errorMessage ? 
          `Error: ${errorMessage}\nProcessed ${businessCount} businesses before error.` :
          `Extraction failed after processing ${businessCount} businesses.`;

        chrome.runtime.sendMessage({
          action: 'showNotification',
          title: '❌ Google Search Scraping Failed',
          message: message,
          type: 'basic',
          iconUrl: '/images/icon48.png'
        });

        console.log('🔔 GOOGLE SEARCH SERVICES: Error notification sent');
      }
    } catch (notificationError) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error sending notification:', notificationError);
    }
  }

  parseBusinessRange(rangeValue) {
    try {
      console.log(`🔍 GOOGLE SEARCH SERVICES: Parsing range input: "${rangeValue}"`);
      
      if (!rangeValue) {
        console.log('❌ Empty range value, using default 1-5');
        return [1, 2, 3, 4, 5];
      }

      // Check if it's a single number
      if (!rangeValue.includes('-')) {
        const singleNumber = parseInt(rangeValue);
        if (isNaN(singleNumber) || singleNumber < 1) {
          throw new Error('Invalid single number format');
        }
        console.log(`✅ Single number parsed: ${singleNumber}`);
        return [singleNumber];
      }

      // Parse range format (e.g., "3-7")
      const parts = rangeValue.split('-');
      if (parts.length !== 2) {
        throw new Error('Range must contain exactly one dash (e.g., "3-7")');
      }

      const startNum = parseInt(parts[0].trim());
      const endNum = parseInt(parts[1].trim());

      if (isNaN(startNum) || isNaN(endNum)) {
        throw new Error('Range values must be valid numbers');
      }

      if (startNum < 1 || endNum < 1) {
        throw new Error('Range values must be greater than 0');
      }

      if (startNum > endNum) {
        throw new Error('Start number must be less than or equal to end number');
      }

      // Generate array of indices
      const indices = [];
      for (let i = startNum; i <= endNum; i++) {
        indices.push(i);
      }

      console.log(`✅ Range parsed successfully: ${startNum}-${endNum} = [${indices.join(', ')}]`);
      return indices;

    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error parsing range:', error);
      console.log('🔄 Using default range 1-5');
      return [1, 2, 3, 4, 5]; // Default fallback
    }
  }

  createDockedInterface() {
    // Remove existing interface if present
    const existing = document.getElementById('gmb-services-docked-interface');
    if (existing) {
      existing.remove();
    }

    // Create the docked interface using semantic HTML and BEM classes
    const dockedDiv = document.createElement('section');
    dockedDiv.id = 'gmb-services-docked-interface';
    dockedDiv.className = 'gmb-search-extractor__docked-interface';
    dockedDiv.setAttribute('role', 'dialog');
    dockedDiv.setAttribute('aria-labelledby', 'gmb-services-title');
    dockedDiv.setAttribute('aria-describedby', 'gmb-services-description');
    
    dockedDiv.innerHTML = `
      <header class="gmb-search-extractor__header">
        <h1 id="gmb-services-title" class="gmb-search-extractor__title">Maps Search Data Extractor</h1>
        <button 
          class="gmb-search-extractor__close-btn" 
          id="gmb-services-close-btn"
          type="button"
          aria-label="Close extractor interface"
        >×</button>
      </header>
      <main class="gmb-search-extractor__content">
        <section class="gmb-search-extractor__status">
          <div class="gmb-search-extractor__status-indicator" aria-hidden="true"></div>
          <span class="gmb-search-extractor__status-text" id="gmb-services-description">Ready to extract data</span>
        </section>
        <section class="gmb-search-extractor__settings">
          <div class="gmb-search-extractor__setting">
            <label for="gmb-services-business-range" class="gmb-search-extractor__setting-label">Business range to scrape:</label>
            <input 
              type="text" 
              id="gmb-services-business-range" 
              class="gmb-search-extractor__text-input" 
              value="1-5" 
              placeholder="e.g., 1-5 or 3-7 or 5"
              aria-describedby="gmb-services-range-help"
            >
            <div id="gmb-services-range-help" class="gmb-search-extractor__help-text">Enter a range (e.g., "3-7") or single number (e.g., "5")</div>
          </div>
          <fieldset class="gmb-search-extractor__extraction-options">
            <legend class="gmb-search-extractor__options-legend">Extraction Options</legend>
            <label class="gmb-search-extractor__checkbox-setting">
              <input type="checkbox" id="gmb-services-extract-products" checked>
              <span class="gmb-search-extractor__checkbox-label">🛍️ Extract Products</span>
            </label>
            <label class="gmb-search-extractor__checkbox-setting">
              <input type="checkbox" id="gmb-services-extract-categories" checked>
              <span class="gmb-search-extractor__checkbox-label">📂 Extract Categories/Services</span>
            </label>
            <label class="gmb-search-extractor__checkbox-setting">
              <input type="checkbox" id="gmb-services-extract-qa">
              <span class="gmb-search-extractor__checkbox-label">❓ Extract Q&A (separate operation)</span>
            </label>
            <label class="gmb-search-extractor__checkbox-setting">
              <input type="checkbox" id="gmb-services-extract-reviews">
              <span class="gmb-search-extractor__checkbox-label">⭐ Extract Reviews (separate operation)</span>
            </label>
          </fieldset>
          <div class="gmb-search-extractor__review-range" id="gmb-services-review-range-container" style="display: none;">
            <label for="gmb-services-review-range" class="gmb-search-extractor__setting-label">Review limit (leave empty for all):</label>
            <input 
              type="text" 
              id="gmb-services-review-range" 
              class="gmb-search-extractor__text-input" 
              placeholder="e.g., 50 or 100 (leave empty for all)"
              maxlength="5"
              pattern="[0-9]*"
              inputmode="numeric"
            >
            <div class="gmb-search-extractor__help-text">Enter max reviews to scrape (leave empty for all available)</div>
          </div>
        </section>
        <section class="gmb-search-extractor__progress" aria-live="polite">
          <div class="gmb-search-extractor__progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
            <div class="gmb-search-extractor__progress-fill"></div>
          </div>
          <div class="gmb-search-extractor__progress-text">0 / 0 businesses processed</div>
        </section>
        <section class="gmb-search-extractor__controls">
          <button 
            class="gmb-search-extractor__btn gmb-search-extractor__btn--start" 
            id="gmb-services-start-btn"
            type="button"
            aria-describedby="gmb-services-start-help"
          >
            Start Extraction
          </button>
          <button 
            class="gmb-search-extractor__btn gmb-search-extractor__btn--export" 
            id="gmb-services-export-btn" 
            disabled
            type="button"
            aria-describedby="gmb-services-export-help"
          >
            📊 Export CSV
          </button>
        </section>
        <section class="gmb-search-extractor__results" aria-live="polite">
          <div class="gmb-search-extractor__summary" role="status">
            <div class="gmb-search-extractor__stat">
              <span class="gmb-search-extractor__stat-label">Businesses</span>
              <span class="gmb-search-extractor__stat-value" id="gmb-services-businesses-count">0</span>
            </div>
            <div class="gmb-search-extractor__stat">
              <span class="gmb-search-extractor__stat-label">Products</span>
              <span class="gmb-search-extractor__stat-value" id="gmb-services-products-count">0</span>
            </div>
            <div class="gmb-search-extractor__stat">
              <span class="gmb-search-extractor__stat-label">Categories</span>
              <span class="gmb-search-extractor__stat-value" id="gmb-services-categories-count">0</span>
            </div>
            <div class="gmb-search-extractor__stat">
              <span class="gmb-search-extractor__stat-label">Services</span>
              <span class="gmb-search-extractor__stat-value" id="gmb-services-services-count">0</span>
            </div>
            <div class="gmb-search-extractor__stat">
              <span class="gmb-search-extractor__stat-label">Q&A</span>
              <span class="gmb-search-extractor__stat-value" id="gmb-services-qa-count">0</span>
            </div>
            <div class="gmb-search-extractor__stat">
              <span class="gmb-search-extractor__stat-label">Reviews</span>
              <span class="gmb-search-extractor__stat-value" id="gmb-services-reviews-count">0</span>
            </div>
          </div>
          <div class="gmb-search-extractor__list" id="gmb-services-list" role="log"></div>
        </section>
      </main>
    `;

    document.body.appendChild(dockedDiv);
    this.dockedInterface = dockedDiv;

    // Add event listeners for the buttons
    this.setupInterfaceEventListeners();

    console.log('✅ GOOGLE SEARCH SERVICES: Docked interface created with semantic HTML and BEM classes');
  }

  setupInterfaceEventListeners() {
    const closeBtn = this.dockedInterface.querySelector('#gmb-services-close-btn');
    const startBtn = this.dockedInterface.querySelector('#gmb-services-start-btn');
    const exportBtn = this.dockedInterface.querySelector('#gmb-services-export-btn');

    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        console.log('🔒 GOOGLE SEARCH SERVICES: Close button clicked');
        this.closeDockedInterface();
      });
    }

    if (startBtn) {
      startBtn.addEventListener('click', () => {
        console.log('🚀 GOOGLE SEARCH SERVICES: Start button clicked');
        if (!this.isRunning) {
          // Check extraction settings to determine which method to call
          const reviewsCheckbox = this.dockedInterface.querySelector('#gmb-services-extract-reviews');
          
          if (reviewsCheckbox && reviewsCheckbox.checked) {
            // Reviews mode - call the review extraction method
            console.log('🚀 GOOGLE SEARCH SERVICES: Starting REVIEW extraction mode');
            this.beginExtraction(); // This will handle reviews via extractReviewsFromMultipleBusinesses
          } else {
            // Regular services/products/Q&A mode
            console.log('🚀 GOOGLE SEARCH SERVICES: Starting REGULAR extraction mode');
            this.beginExtraction();
          }
        }
      });
    }

    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        console.log('📊 GOOGLE SEARCH SERVICES: Export button clicked');
        this.exportToCSV();
      });
    }

    // Add mutual exclusion logic for checkboxes
    this.setupCheckboxMutualExclusion();
  }

  setupCheckboxMutualExclusion() {
    const productsCheckbox = this.dockedInterface.querySelector('#gmb-services-extract-products');
    const categoriesCheckbox = this.dockedInterface.querySelector('#gmb-services-extract-categories');
    const qaCheckbox = this.dockedInterface.querySelector('#gmb-services-extract-qa');
    const reviewsCheckbox = this.dockedInterface.querySelector('#gmb-services-extract-reviews');

    // Q&A Checkbox - Must be separate from everything else
    if (qaCheckbox) {
      qaCheckbox.addEventListener('change', () => {
        console.log('🔄 GOOGLE SEARCH SERVICES: Q&A checkbox changed:', qaCheckbox.checked);
        if (qaCheckbox.checked) {
          // Q&A was checked - uncheck ALL other options
          productsCheckbox.checked = false;
          categoriesCheckbox.checked = false;
          reviewsCheckbox.checked = false;
          this.hideReviewRangeContainer();
          console.log('🔄 GOOGLE SEARCH SERVICES: Q&A enabled - disabled Products, Categories, and Reviews (Q&A is separate)');
        }
      });
    }

    // Reviews Checkbox - Must be separate from everything else
    if (reviewsCheckbox) {
      reviewsCheckbox.addEventListener('change', () => {
        console.log('🔄 GOOGLE SEARCH SERVICES: Reviews checkbox changed:', reviewsCheckbox.checked);
        if (reviewsCheckbox.checked) {
          // Reviews was checked - uncheck ALL other options
          productsCheckbox.checked = false;
          categoriesCheckbox.checked = false;
          qaCheckbox.checked = false;
          this.showReviewRangeContainer();
          console.log('🔄 GOOGLE SEARCH SERVICES: Reviews enabled - disabled Products, Categories, and Q&A (Reviews is separate)');
        } else {
          // Reviews was unchecked, hide range container
          this.hideReviewRangeContainer();
          console.log('🔄 GOOGLE SEARCH SERVICES: Reviews disabled - hid range container');
        }
      });
    }

    // Products Checkbox - Can be combined with Categories, but not with Q&A or Reviews
    if (productsCheckbox) {
      productsCheckbox.addEventListener('change', () => {
        console.log('🔄 GOOGLE SEARCH SERVICES: Products checkbox changed:', productsCheckbox.checked);
        if (productsCheckbox.checked) {
          // Products was checked - uncheck Q&A and Reviews (but allow Categories)
          qaCheckbox.checked = false;
          reviewsCheckbox.checked = false;
          this.hideReviewRangeContainer();
          console.log('🔄 GOOGLE SEARCH SERVICES: Products enabled - disabled Q&A and Reviews (Products can work with Categories)');
        }
      });
    }

    // Categories Checkbox - Can be combined with Products, but not with Q&A or Reviews
    if (categoriesCheckbox) {
      categoriesCheckbox.addEventListener('change', () => {
        console.log('🔄 GOOGLE SEARCH SERVICES: Categories checkbox changed:', categoriesCheckbox.checked);
        if (categoriesCheckbox.checked) {
          // Categories was checked - uncheck Q&A and Reviews (but allow Products)
          qaCheckbox.checked = false;
          reviewsCheckbox.checked = false;
          this.hideReviewRangeContainer();
          console.log('🔄 GOOGLE SEARCH SERVICES: Categories enabled - disabled Q&A and Reviews (Categories can work with Products)');
        }
      });
    }
  }

  // Show review range container
  showReviewRangeContainer() {
    const rangeContainer = this.dockedInterface.querySelector('#gmb-services-review-range-container');
    if (rangeContainer) {
      rangeContainer.style.display = 'block';
      console.log('🔄 GOOGLE SEARCH SERVICES: Review range container shown');
    }
  }

  // Hide review range container
  hideReviewRangeContainer() {
    const rangeContainer = this.dockedInterface.querySelector('#gmb-services-review-range-container');
    if (rangeContainer) {
      rangeContainer.style.display = 'none';
      console.log('🔄 GOOGLE SEARCH SERVICES: Review range container hidden');
    }
  }

  closeDockedInterface() {
    console.log('🔒 GOOGLE SEARCH SERVICES: Closing docked interface...');
    
    try {
      if (this.dockedInterface) {
        // Add fade out animation using CSS classes
        this.dockedInterface.classList.add('gmb-search-extractor__fade-out');
        
        // Remove after animation
        setTimeout(() => {
          if (this.dockedInterface && this.dockedInterface.parentNode) {
            this.dockedInterface.parentNode.removeChild(this.dockedInterface);
          }
          this.dockedInterface = null;
          console.log('✅ GOOGLE SEARCH SERVICES: Docked interface closed successfully');
        }, 300);
      } else {
        console.log('⚠️ GOOGLE SEARCH SERVICES: No docked interface to close');
      }
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error closing docked interface:', error);
      // Force remove if there's an error
      const existing = document.getElementById('gmb-services-docked-interface');
      if (existing) {
        existing.remove();
      }
      this.dockedInterface = null;
    }
  }

  updateDockedInterface(statusMessage, data) {
    if (!this.dockedInterface) return;

    const statusIndicator = this.dockedInterface.querySelector('.gmb-search-extractor__status-indicator');
    const statusText = this.dockedInterface.querySelector('.gmb-search-extractor__status-text');
    const progressFill = this.dockedInterface.querySelector('.gmb-search-extractor__progress-fill');
    const progressBar = this.dockedInterface.querySelector('.gmb-search-extractor__progress-bar');
    const progressText = this.dockedInterface.querySelector('.gmb-search-extractor__progress-text');
    const startBtn = this.dockedInterface.querySelector('.gmb-search-extractor__btn--start');
    const exportBtn = this.dockedInterface.querySelector('.gmb-search-extractor__btn--export');

    // Update status text
    if (statusText) {
      statusText.textContent = statusMessage;
    }

    // Determine if extraction is in progress based on status message and data
    const isExtractionInProgress = this.isRunning || 
      (typeof statusMessage === 'string' && (
        statusMessage.includes('Processing') ||
        statusMessage.includes('Extracting') ||
        statusMessage.includes('Starting') ||
        statusMessage.includes('Loading') ||
        statusMessage.includes('Finding') ||
        statusMessage.includes('Expanding') ||
        statusMessage.includes('Waiting') ||
        statusMessage.includes('...') && !statusMessage.includes('Complete')
      )) ||
      (data && ((data.current > 0 && data.current < data.total) || 
                (data.currentBusiness > 0 && data.currentBusiness < data.totalBusinesses)));

    // Update status indicator to show pulsing green during extractions
    if (statusIndicator) {
      if (isExtractionInProgress) {
        statusIndicator.classList.add('gmb-search-extractor__status-indicator--active');
        console.log('🟢 GOOGLE SEARCH SERVICES: Status indicator set to pulsing green (extraction in progress)');
      } else {
        statusIndicator.classList.remove('gmb-search-extractor__status-indicator--active');
        console.log('🔴 GOOGLE SEARCH SERVICES: Status indicator set to red (extraction complete/idle)');
      }
    }

    // Check if we're in review extraction mode
    if (this.extractionSettings && this.extractionSettings.reviews && data) {
      // Handle review extraction progress
      const totalBusinesses = data.totalBusinesses || 0;
      const currentBusiness = data.currentBusiness || 0;
      const totalReviews = data.totalReviews || 0;

      // Update progress bar based on businesses processed
      if (totalBusinesses > 0) {
        const progress = (currentBusiness / totalBusinesses) * 100;
        if (progressFill) {
          progressFill.style.setProperty('--progress-width', `${progress}%`);
          progressFill.classList.add('gmb-search-extractor__progress-dynamic');
        }
        if (progressBar) {
          progressBar.setAttribute('aria-valuenow', Math.round(progress));
        }
      }

      // Update progress text
      if (progressText) {
        progressText.textContent = `${currentBusiness} / ${totalBusinesses} businesses processed`;
      }

      // Update results display for review mode
      const resultsData = {
        totalBusinesses: totalBusinesses,
        totalReviews: totalReviews
      };
      this.updateResultsDisplay(resultsData);

      // Update buttons state for review extraction
      if (startBtn) {
        if (currentBusiness === totalBusinesses && totalBusinesses > 0) {
          // Extraction complete
          startBtn.disabled = false;
          startBtn.textContent = '🚀 Start';
          
          if (exportBtn) {
            exportBtn.disabled = totalReviews === 0;
          }
        } else if (currentBusiness > 0) {
          // Extraction in progress
          startBtn.disabled = true;
          startBtn.textContent = '⏳ Running';
          
          if (exportBtn) {
            exportBtn.disabled = true;
          }
        }
      }

    } else {
      // Regular services extraction mode (existing logic)
      if (data && typeof data === 'object' && data.total !== undefined) {
        // Handle the old data format for services extraction
        const progress = data.total > 0 ? (data.current / data.total) * 100 : 0;
        
        if (progressFill) {
          progressFill.style.setProperty('--progress-width', `${progress}%`);
          progressFill.classList.add('gmb-search-extractor__progress-dynamic');
        }
        if (progressBar) {
          progressBar.setAttribute('aria-valuenow', Math.round(progress));
        }
        if (progressText) {
          progressText.textContent = `${data.current || 0} / ${data.total || 0} processed`;
        }
        
        // Update buttons state for regular extraction
        if (startBtn) {
          if (data.current === data.total && data.total > 0) {
            // Extraction complete
            startBtn.disabled = false;
            startBtn.textContent = '🚀 Start';
            
            if (exportBtn) {
              exportBtn.disabled = !data.data || data.data.length === 0;
            }
          } else if (data.current > 0) {
            // Extraction in progress
            startBtn.disabled = true;
            startBtn.textContent = '⏳ Running';
            
            if (exportBtn) {
              exportBtn.disabled = true;
            }
          }
        }
        
        if (data.data) {
          this.updateResultsDisplay(data.data);
        }
      }
    }
  }

  updateResultsDisplay(servicesData) {
    const businessesCount = document.getElementById('gmb-services-businesses-count');
    const productsCount = document.getElementById('gmb-services-products-count');
    const categoriesCount = document.getElementById('gmb-services-categories-count');
    const servicesCount = document.getElementById('gmb-services-services-count');
    const qaCount = document.getElementById('gmb-services-qa-count');
    const reviewsCount = document.getElementById('gmb-services-reviews-count');
    const servicesList = document.getElementById('gmb-services-list');

    if (!businessesCount || !productsCount || !categoriesCount || !servicesCount || !qaCount || !reviewsCount || !servicesList) return;

    // Check if we're in review extraction mode (extractionSettings.reviews is true)
    if (this.extractionSettings && this.extractionSettings.reviews) {
      // For review extraction mode, show review-specific stats
      businessesCount.textContent = servicesData.totalBusinesses || 0;
      productsCount.textContent = 0; // No products in review mode
      categoriesCount.textContent = 0; // No categories in review mode
      servicesCount.textContent = 0; // No services in review mode
      qaCount.textContent = 0; // No Q&A in review mode
      reviewsCount.textContent = servicesData.totalReviews || 0; // Use reviews counter for total reviews
      
      // Clear the services list - no preview functionality
      servicesList.innerHTML = '';
    } else {
      // Regular services/products/Q&A extraction mode
      // Update counts
      businessesCount.textContent = servicesData.length;
      
      let totalProducts = 0;
      let totalCategories = 0;
      let totalServices = 0;
      let totalQa = 0;
      
      servicesData.forEach(business => {
        if (business.products) {
          totalProducts += business.products.length;
        }
        totalCategories += business.categories.length;
        business.categories.forEach(category => {
          totalServices += category.services.length;
        });
        totalQa += business.qa.length;
      });

      productsCount.textContent = totalProducts;
      categoriesCount.textContent = totalCategories;
      servicesCount.textContent = totalServices;
      qaCount.textContent = totalQa;
      reviewsCount.textContent = 0; // No reviews in regular mode

      // Clear the services list - no preview functionality
      servicesList.innerHTML = '';
    }
  }

  // Utility functions for visual marker management
  addVisualMarker(element, type = 'red') {
    if (!element) return;
    
    element.classList.add(`gmb-search-extractor__marker--${type}`);
    element.classList.add('gmb-search-extractor__marker-animated');
    console.log(`🔴 GOOGLE SEARCH SERVICES: Added ${type} visual marker`);
  }

  removeVisualMarker(element) {
    if (!element) return;
    
    element.classList.remove('gmb-search-extractor__marker--red');
    element.classList.remove('gmb-search-extractor__marker--green');
    element.classList.add('gmb-search-extractor__marker--none');
    element.removeAttribute('data-gmb-marked-products');
    element.removeAttribute('data-gmb-marked-qa');
    console.log('🔴 GOOGLE SEARCH SERVICES: Removed visual marker');
  }

  async processBusinessCard(businessCard, index) {
    try {
      // Extract business name first
      const businessName = this.extractBusinessName(businessCard);
      console.log(`📋 GOOGLE SEARCH SERVICES: Processing business: ${businessName}`);

      // Store business name for service extraction
      this.currentBusinessName = businessName;

      // Click the business card to open the popup with improved targeting
      console.log('🖱️ GOOGLE SEARCH SERVICES: Clicking business card...');
      await this.clickBusinessCardWithVerification(businessCard, businessName);

      // Wait for popup to load and verify it opened
      await this.delay(3000); // Wait for popup to load
      
      // Verify popup opened by checking for business profile elements
      const popupOpened = await this.verifyBusinessPopupOpened(businessName);
      if (!popupOpened) {
        console.log('⚠️ GOOGLE SEARCH SERVICES: Business popup did not open, trying alternative click method...');
        await this.tryAlternativeClickMethod(businessCard, businessName);
        await this.delay(3000);
      }

      const businessServices = {
        businessName: businessName,
        businessIndex: index + 1,
        products: [],
        categories: [],
        qa: [],
        extractedAt: new Date().toISOString()
      };

      let hasExtractedData = false;

      // Step 1: Extract Products (if enabled)
      if (this.extractionSettings.products) {
        console.log('🛍️ GOOGLE SEARCH SERVICES: Looking for products section...');
        
        // Update status to show we're extracting products
        this.updateDockedInterface(`Extracting products from ${businessName}...`, {
          total: this.totalBusinesses,
          current: this.currentBusinessIndex,
          data: this.servicesData
        });
        
        const productsViewAllButton = await this.findProductsViewAllButton();
        
        if (productsViewAllButton) {
          console.log('✅ GOOGLE SEARCH SERVICES: Found products "View all" button');
          
          try {
            // Click products view all button
            productsViewAllButton.click();
            await this.delay(3000); // Wait for products popup to load
            
            // Remove the red border marker from the products button using utility function
            this.removeVisualMarker(productsViewAllButton);
            
            // Extract products from popup
            const products = await this.extractProductsFromPopup();
            businessServices.products = products;
            
            console.log(`✅ GOOGLE SEARCH SERVICES: Extracted ${products.length} products`);
            hasExtractedData = true;
            
            // Close products popup
            await this.closePopup();
            await this.delay(2000); // Wait for popup to close

          } catch (error) {
            console.error('💥 GOOGLE SEARCH SERVICES: Error extracting products:', error);
            
            // Ensure red border is removed even on error using utility function
            this.removeVisualMarker(productsViewAllButton);
          }
        } else {
          console.log('❌ GOOGLE SEARCH SERVICES: No products section found');
        }
      }

      // Step 2: Extract Categories/Services (if enabled)
      if (this.extractionSettings.categories) {
        console.log('📂 GOOGLE SEARCH SERVICES: Looking for categories section...');
        
        // Update status to show we're extracting categories/services
        this.updateDockedInterface(`Extracting categories/services from ${businessName}...`, {
          total: this.totalBusinesses,
          current: this.currentBusinessIndex,
          data: this.servicesData
        });
        
        // Look for categories container
        const categoriesContainer = document.querySelector('.rh1NH.xHRvib');
        if (!categoriesContainer) {
          console.log('❌ GOOGLE SEARCH SERVICES: No categories container found');
          
          if (!hasExtractedData) {
            // Add business with note that it has no categories/products
            businessServices.note = 'No categories/services or products found for this business';
          }
        } else {
          console.log('✅ GOOGLE SEARCH SERVICES: Found categories container');

          // Extract categories
          const categoryButtons = categoriesContainer.querySelectorAll('.MaauSd');
          console.log(`📂 GOOGLE SEARCH SERVICES: Found ${categoryButtons.length} categories`);

          // Since all categories open the same popup, just click the first one
          if (categoryButtons.length > 0) {
            const firstCategoryButton = categoryButtons[0];
            const categoryName = firstCategoryButton.textContent.trim();
            
            console.log(`🔄 GOOGLE SEARCH SERVICES: Clicking first category: ${categoryName} (all categories show same services)`);

            try {
              // Click the first category button to open services popup
              firstCategoryButton.click();
              await this.delay(3000); // Increased from 2000 to 3000ms to ensure popup loads properly

              // Extract ALL services from the popup
              const services = await this.extractServicesFromPopup();
              
              // Close the category services popup immediately after extraction
              await this.closePopup();
              
              // Additional wait after closing popup to ensure UI has settled
              console.log('⏳ GOOGLE SEARCH SERVICES: Waiting for UI to settle after popup close...');
              await this.delay(2000); // Wait for UI to settle
              
              // Group services by their categories from the popup
              const categoriesMap = new Map();
              
              // First, check if we have services with category information
              let hasCategoriesWithServices = false;
              services.forEach(service => {
                if (service.category && service.category !== 'Services') {
                  hasCategoriesWithServices = true;
                }
              });
              
              if (hasCategoriesWithServices) {
                // Group services by category
                services.forEach(service => {
                  const categoryName = service.category || 'Uncategorized';
                  if (!categoriesMap.has(categoryName)) {
                    categoriesMap.set(categoryName, {
                      categoryName: categoryName,
                      categoryDescription: '',
                      services: []
                    });
                  }
                  categoriesMap.get(categoryName).services.push(service);
                });
                
                // Convert map to array for businessServices
                businessServices.categories = Array.from(categoriesMap.values());
              } else {
                // If no category information, put all services under a single category
                if (services.length > 0) {
                  businessServices.categories.push({
                    categoryName: 'All Services',
                    categoryDescription: `${services.length} services extracted from ${categoryName} popup`,
                    services: services
                  });
                }
              }

              console.log(`✅ GOOGLE SEARCH SERVICES: Extracted ${services.length} services in ${businessServices.categories.length} categories`);
              hasExtractedData = true;

            } catch (error) {
              console.error(`💥 GOOGLE SEARCH SERVICES: Error processing services popup:`, error);
            }
          } else {
            console.log('❌ GOOGLE SEARCH SERVICES: No category buttons found');
          }
        }
      }

      // Step 3: Extract Q&A (if enabled) - IMPLEMENTING FROM COMMIT
      if (this.extractionSettings.qa) {
        console.log('❓ GOOGLE SEARCH SERVICES: Looking for Q&A section...');
        
        // Update status to show we're extracting Q&A
        this.updateDockedInterface(`Extracting Q&A from ${businessName}...`, {
          total: this.totalBusinesses,
          current: this.currentBusinessIndex,
          data: this.servicesData
        });
        
        const qaButton = await this.findQAButton();
        
        if (qaButton) {
          console.log('✅ GOOGLE SEARCH SERVICES: Found Q&A "See all questions" button');
          
          try {
            // Click Q&A button
            qaButton.click();
            await this.delay(3000); // Wait for Q&A popup to load
            
            // Remove the red border marker from the Q&A button using utility function
            this.removeVisualMarker(qaButton);
            
            // Extract Q&A from popup
            const qaData = await this.extractQAFromPopup();
            businessServices.qa = qaData;
            
            console.log(`✅ GOOGLE SEARCH SERVICES: Extracted ${qaData.length} Q&A pairs`);
            hasExtractedData = true;
            
            // Close Q&A popup
            await this.closeQAPopup();
            await this.delay(2000); // Wait for popup to close
            
          } catch (error) {
            console.error('💥 GOOGLE SEARCH SERVICES: Error extracting Q&A:', error);
            
            // Ensure red border is removed even on error using utility function
            this.removeVisualMarker(qaButton);
          }
        } else {
          console.log('❌ GOOGLE SEARCH SERVICES: No Q&A section found');
        }
      }

      // If no data was extracted and no categories/products/qa were found
      if (!hasExtractedData && businessServices.products.length === 0 && businessServices.categories.length === 0 && businessServices.qa.length === 0) {
        businessServices.note = 'No extractable data found for this business';
      }

      this.servicesData.push(businessServices);
      console.log(`✅ GOOGLE SEARCH SERVICES: Completed processing business: ${businessName}`);
      console.log(`   📊 Results: ${businessServices.products.length} products, ${businessServices.categories.length} categories, ${businessServices.qa.length} Q&A`);

    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error processing business card:', error);
      throw error;
    }
  }

  async findProductsViewAllButton() {
    try {
      console.log('🔍 GOOGLE SEARCH SERVICES: Searching for products "View all" button with visual targeting...');
      
      // Look for the specific "View all" button for products with exact classes
      const viewAllButtons = document.querySelectorAll('a.i2MEmb.laVYkc');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${viewAllButtons.length} "View all" buttons with classes i2MEmb laVYkc`);
      
      if (viewAllButtons.length === 0) {
        console.log('❌ GOOGLE SEARCH SERVICES: No "View all" buttons found with required classes');
        return null;
      }
      
      // Try to identify the products button by checking context
      let productsButton = null;
      
      for (const button of viewAllButtons) {
        try {
          // Check if this is the products view all button
          const buttonText = button.textContent.trim().toLowerCase();
          const jsaction = button.getAttribute('jsaction');
          const dataVed = button.getAttribute('data-ved');
          
          console.log(`🔍 GOOGLE SEARCH SERVICES: Checking View All button:`, {
            text: buttonText,
            jsaction: jsaction,
            dataVed: dataVed
          });
          
          // Look for "Products" heading nearby
          const parentContainer = button.closest('.VjDMZd') || 
                                 button.closest('.commercial-unit-desktop-top') || 
                                 button.closest('.nOqI9b') ||
                                 button.closest('.rl_full-list') ||
                                 button.parentElement?.parentElement ||
                                 button.parentElement;
          
          if (parentContainer) {
            const containerText = parentContainer.textContent;
            console.log(`🔍 GOOGLE SEARCH SERVICES: Container text contains:`, containerText.substring(0, 200) + '...');
            
            // Check for Products-related keywords in the container
            const hasProductsContext = containerText.includes('Products') ||
                                      containerText.includes('Product') ||
                                      containerText.includes('Explore categories') ||
                                      // Check if it's NOT in a services/categories section
                                      (!containerText.includes('Services') && 
                                       !containerText.includes('Categories') &&
                                       buttonText === 'view all');
            
            if (hasProductsContext && buttonText === 'view all') {
              console.log('✅ GOOGLE SEARCH SERVICES: Found potential products "View all" button');
              productsButton = button;
              break;
            }
          }
          
          // Fallback: if it's the first "View all" button and has the right jsaction
          if (!productsButton && buttonText === 'view all' && jsaction && jsaction.includes('trigger.QTy97')) {
            console.log('🔄 GOOGLE SEARCH SERVICES: Using fallback - first View all button with trigger.QTy97');
            productsButton = button;
          }
          
        } catch (error) {
          console.log('⚠️ GOOGLE SEARCH SERVICES: Error checking button:', error);
        }
      }
      
      if (!productsButton) {
        console.log('❌ GOOGLE SEARCH SERVICES: Could not identify products "View all" button');
        return null;
      }
      
      // Add visual red border marker to the identified button using utility function
      console.log('🔴 GOOGLE SEARCH SERVICES: Adding red border marker to products "View all" button...');
      this.addVisualMarker(productsButton, 'red');
      
      // Add a custom data attribute for additional targeting
      productsButton.setAttribute('data-gmb-marked-products', 'true');
      
      console.log('✅ GOOGLE SEARCH SERVICES: Added red border marker to products "View all" button');
      
      // Wait a moment for visual confirmation
      await this.delay(1000);
      
      return productsButton;
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error finding products view all button:', error);
      return null;
    }
  }

  async extractProductsFromPopup() {
    const products = [];
    
    try {
      console.log('🛍️ GOOGLE SEARCH SERVICES: Starting products extraction from popup...');
      
      // Wait for popup to fully load
      await this.delay(3000);
      
      // Look for the iframe that contains the products
      const iframe = document.querySelector('iframe#C6GqMd') || 
                    document.querySelector('iframe[name="lpc"]') || 
                    document.querySelector('iframe.PxHPSd');
      
      if (!iframe) {
        console.log('❌ GOOGLE SEARCH SERVICES: No iframe found for products popup');
        console.log('🔍 GOOGLE SEARCH SERVICES: Looking in main document as fallback...');
      } else {
        console.log('✅ GOOGLE SEARCH SERVICES: Found iframe for products popup');
        
        // Wait a bit more for iframe content to load
        await this.delay(2000);
        
        try {
          // Get the iframe's document
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
          
          if (!iframeDoc) {
            console.log('❌ GOOGLE SEARCH SERVICES: Cannot access iframe document (possibly cross-origin)');
          } else {
            console.log('✅ GOOGLE SEARCH SERVICES: Accessed iframe document successfully');
            
            // Check for "View more" button first and click it if present
            await this.checkAndClickViewMoreButton(iframeDoc);
            
            // Look for product category containers inside the iframe (similar to services)
            const productCategoryContainers = iframeDoc.querySelectorAll('.f8twAd');
            console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${productCategoryContainers.length} product category containers (.f8twAd) in iframe`);
            
            if (productCategoryContainers.length > 0) {
              // Process the product categories from iframe
              return this.extractProductsFromCategories(productCategoryContainers);
            }
            
            // If no category containers, look for product buttons directly in iframe
            const productButtons = iframeDoc.querySelectorAll('a.pooVf.prDW');
            console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${productButtons.length} product buttons in iframe`);
            
            if (productButtons.length > 0) {
              // Extract products directly from iframe
              productButtons.forEach((productButton, index) => {
                try {
                  const productData = this.extractProductDataFromButton(productButton, index, 'Uncategorized');
                  if (productData) {
                    products.push(productData);
                  }
                } catch (error) {
                  console.error(`💥 Error extracting product ${index + 1}:`, error);
                }
              });
              
              return products;
            }
          }
        } catch (error) {
          console.error('💥 GOOGLE SEARCH SERVICES: Error accessing iframe content:', error);
        }
      }
      
      // Fallback: try to find products in the main document
      console.log('🔍 GOOGLE SEARCH SERVICES: Falling back to main document search for products...');
      
      // Check for "View more" button in main document
      await this.checkAndClickViewMoreButton(document);
      
      // Look for product category containers in main document
      const productCategoryContainers = document.querySelectorAll('.f8twAd');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${productCategoryContainers.length} product category containers (.f8twAd) in main document`);
      
      if (productCategoryContainers.length > 0) {
        return this.extractProductsFromCategories(productCategoryContainers);
      }
      
      // Look for product buttons directly in main document
      const productButtons = document.querySelectorAll('a.pooVf.prDW');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${productButtons.length} product buttons in main document`);
      
      if (productButtons.length > 0) {
        productButtons.forEach((productButton, index) => {
          try {
            const productData = this.extractProductDataFromButton(productButton, index, 'Uncategorized');
            if (productData) {
              products.push(productData);
            }
          } catch (error) {
            console.error(`💥 Error extracting product ${index + 1}:`, error);
          }
        });
      }
      
      console.log(`🎯 GOOGLE SEARCH SERVICES: Final result - Extracted ${products.length} products`);
      return products;

    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error in extractProductsFromPopup:', error);
      return products;
    }
  }

  async checkAndClickViewMoreButton(doc) {
    try {
      console.log('🔍 GOOGLE SEARCH SERVICES: Checking for "View more" button in products...');
      
      // Look for the specific "View more" button with the exact classes
      const viewMoreButtons = doc.querySelectorAll('button.VfPpkd-LgbsSe');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${viewMoreButtons.length} potential "View more" buttons`);
      
      let viewMoreButton = null;
      
      // Find the button that contains "View more" text
      for (const button of viewMoreButtons) {
        try {
          const buttonText = button.textContent.trim().toLowerCase();
          const spanElement = button.querySelector('span.VfPpkd-vQzf8d');
          
          if (buttonText.includes('view more') || (spanElement && spanElement.textContent.trim().toLowerCase() === 'view more')) {
            console.log('✅ GOOGLE SEARCH SERVICES: Found "View more" button for products');
            viewMoreButton = button;
            break;
          }
        } catch (error) {
          console.log('⚠️ GOOGLE SEARCH SERVICES: Error checking button text:', error);
        }
      }
      
      if (viewMoreButton) {
        console.log('🖱️ GOOGLE SEARCH SERVICES: Clicking "View more" button...');
        
        // Add visual marker using utility function
        this.addVisualMarker(viewMoreButton, 'green');
        
        // Click the button
        viewMoreButton.click();
        
        // Wait 1 second for products to load as requested
        console.log('⏳ GOOGLE SEARCH SERVICES: Waiting 1 second for more products to load...');
        await this.delay(1000);
        
        // Remove visual marker using utility function
        this.removeVisualMarker(viewMoreButton);
        
        console.log('✅ GOOGLE SEARCH SERVICES: "View more" button clicked and products loaded');
        
      } else {
        console.log('ℹ️ GOOGLE SEARCH SERVICES: No "View more" button found - proceeding with existing products');
      }
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error checking/clicking "View more" button:', error);
    }
  }

  extractProductsFromCategories(categoryContainers) {
    const products = [];
    
    console.log('✅ GOOGLE SEARCH SERVICES: Processing product categories...');
    
    categoryContainers.forEach((categoryContainer, catIndex) => {
      try {
        // Extract category name for products
        const categoryNameEl = categoryContainer.querySelector('.EJHGm');
        const categoryName = categoryNameEl ? categoryNameEl.textContent.trim() : `Product Category ${catIndex + 1}`;
        console.log(`🛍️ GOOGLE SEARCH SERVICES: Processing product category: ${categoryName}`);
        
        // Find all products within this category
        const categoryProducts = categoryContainer.querySelectorAll('a.pooVf.prDW');
        console.log(`   Found ${categoryProducts.length} products in ${categoryName}`);
        
        categoryProducts.forEach((productButton, index) => {
          try {
            const productData = this.extractProductDataFromButton(productButton, index, categoryName);
            if (productData) {
              products.push(productData);
            }
          } catch (error) {
            console.error(`   💥 Error extracting product ${index + 1}:`, error);
          }
        });
        
      } catch (error) {
        console.error(`💥 GOOGLE SEARCH SERVICES: Error processing product category ${catIndex + 1}:`, error);
      }
    });
    
    console.log(`✅ GOOGLE SEARCH SERVICES: Successfully extracted ${products.length} products from categories`);
    return products;
  }

  extractProductDataFromButton(productButton, index, categoryName) {
    try {
      // Extract product information from aria-label
      const ariaLabel = productButton.getAttribute('aria-label') || '';
      
      // Extract product name and price from aria-label
      // Format: "commercial cleaning melbourne $300.00" or "Office Cleaning Melbourne $99.00"
      let productName = '';
      let productPrice = '';
      
      if (ariaLabel) {
        // Try to extract name and price from aria-label
        const priceMatch = ariaLabel.match(/\$[\d,.]+(?: – \$[\d,.]+)?/);
        if (priceMatch) {
          productPrice = priceMatch[0];
          productName = ariaLabel.replace(priceMatch[0], '').trim();
        } else {
          productName = ariaLabel;
        }
      }
      
      // Fallback: extract from DOM elements
      if (!productName) {
        const nameElement = productButton.querySelector('.t3RpAe.prDW.Rgstwe');
        productName = nameElement ? nameElement.textContent.trim() : `Product ${index + 1}`;
      }
      
      if (!productPrice) {
        const priceElement = productButton.querySelector('.A978lb.prDW');
        productPrice = priceElement ? priceElement.textContent.trim() : 'Price not available';
      }
      
      // Get additional data
      const dataHref = productButton.getAttribute('data-href') || '';
      const ludocidMatch = dataHref.match(/ludocid=(\d+)/);
      const entryIdMatch = dataHref.match(/entry_id=([^&]+)/);
      
      // Get data-id from parent container
      const parentContainer = productButton.closest('.J8zyUd');
      const dataId = parentContainer ? parentContainer.getAttribute('data-id') : '';
      
      const product = {
        title: productName,
        price: productPrice,
        description: ariaLabel,
        category: categoryName,
        ludocid: ludocidMatch ? ludocidMatch[1] : '',
        entryId: entryIdMatch ? entryIdMatch[1] : '',
        dataId: dataId,
        dataHref: dataHref,
        extractedAt: new Date().toISOString()
      };
      
      console.log(`🛍️ Product ${index + 1}: ${productName} - ${productPrice} (Category: ${categoryName})`);
      return product;
      
    } catch (error) {
      console.error(`💥 Error extracting product data from button:`, error);
      return null;
    }
  }

  extractBusinessName(businessCard) {
    try {
      // Try multiple selectors for business name
      const nameSelectors = [
        '.OSrXXb.RfnDt.o5Th0.E3DyYd.VbTUod',
        '.vwVdIc.wzN8Ac.rllt__link.a-no-hover-decoration .OSrXXb',
        '.OSrXXb', // Generic business name selector (works with Google Search)
        '.BNeawe.vvjwJb.AP7Wnd',
        '.sVXRqc',
        'h3', // Common heading selector for Google Search
        '.LC20lb', // Google Search title selector
        '.DKV0Md' // Additional Google Search selector
      ];

      for (const selector of nameSelectors) {
        const nameElement = businessCard.querySelector(selector);
        if (nameElement && nameElement.textContent.trim()) {
          return nameElement.textContent.trim();
        }
      }

      return `Business ${this.currentBusinessIndex + 1}`;

    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error extracting business name:', error);
      return `Business ${this.currentBusinessIndex + 1}`;
    }
  }

  async extractServicesFromPopup() {
    const services = [];
    
    try {
      // Wait for popup to fully load
      console.log('⏳ GOOGLE SEARCH SERVICES: Waiting for popup to fully load...');
      await this.delay(3000); // Increased from 2000 to 3000ms to ensure iframe loads properly
      
      console.log('🔍 GOOGLE SEARCH SERVICES: Starting services extraction from popup...');
      
      // Look for the iframe that contains the services
      const iframe = document.querySelector('iframe#C6GqMd') || 
                    document.querySelector('iframe[name="lpc"]') || 
                    document.querySelector('iframe.PxHPSd');
      
      if (!iframe) {
        console.log('❌ GOOGLE SEARCH SERVICES: No iframe found for services popup');
        console.log('🔍 GOOGLE SEARCH SERVICES: Looking in main document as fallback...');
      } else {
        console.log('✅ GOOGLE SEARCH SERVICES: Found iframe for services popup');
        
        // Wait a bit more for iframe content to load
        await this.delay(2000); // Increased from 1000 to 2000ms to ensure iframe content loads
        
        try {
          // Get the iframe's document
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
          
          if (!iframeDoc) {
            console.log('❌ GOOGLE SEARCH SERVICES: Cannot access iframe document (possibly cross-origin)');
          } else {
            console.log('✅ GOOGLE SEARCH SERVICES: Accessed iframe document successfully');
            
            // Check for "View more" button first and click it if present (for services)
            await this.checkAndClickViewMoreButton(iframeDoc);
            
            // Look for category containers inside the iframe
            const categoryContainers = iframeDoc.querySelectorAll('.f8twAd');
            console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${categoryContainers.length} category containers (.f8twAd) in iframe`);
            
            if (categoryContainers.length > 0) {
              // Process the categories from iframe
              return this.extractFromCategories(categoryContainers);
            }
            
            // If no categories, look for service buttons in iframe
            const serviceButtons = iframeDoc.querySelectorAll('a.pooVf.prDW');
            console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${serviceButtons.length} service buttons in iframe`);
            
            if (serviceButtons.length > 0) {
              // Extract services directly from iframe
              serviceButtons.forEach((serviceButton, index) => {
                try {
                  const serviceNameEl = serviceButton.querySelector('.t3RpAe.prDW.Rgstwe');
                  const serviceName = serviceNameEl ? serviceNameEl.textContent.trim() : '';
                  
                  const priceEl = serviceButton.querySelector('.A978lb.prDW');
                  const price = priceEl ? priceEl.textContent.trim() : '';
                  
                  const ariaLabel = serviceButton.getAttribute('aria-label') || '';
                  
                  const serviceContainer = serviceButton.closest('.J8zyUd');
                  const dataId = serviceContainer ? serviceContainer.getAttribute('data-id') : '';
                  
                  if (serviceName) {
                    const service = {
                      title: serviceName,
                      price: price || 'Price not available',
                      description: ariaLabel,
                      dataId: dataId,
                      category: 'Uncategorized',
                      extractedAt: new Date().toISOString()
                    };
                    
                    services.push(service);
                    console.log(`📝 Service ${index + 1}: ${serviceName} - ${price}`);
                  }
                } catch (error) {
                  console.error(`💥 Error extracting service ${index + 1}:`, error);
                }
              });
              
              return services;
            }
          }
        } catch (error) {
          console.error('💥 GOOGLE SEARCH SERVICES: Error accessing iframe content:', error);
          console.log('🔍 GOOGLE SEARCH SERVICES: This might be due to cross-origin restrictions');
        }
      }
      
      // Fallback: try to find elements in the main document
      console.log('🔍 GOOGLE SEARCH SERVICES: Falling back to main document search...');
      
      // Check for "View more" button in main document (for services)
      await this.checkAndClickViewMoreButton(document);
      
      // Look for category containers in main document
      const categoryContainers = document.querySelectorAll('.f8twAd');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${categoryContainers.length} category containers (.f8twAd) in main document`);
      
      if (categoryContainers.length > 0) {
        return this.extractFromCategories(categoryContainers);
      }
      
      // Look for service buttons in main document
      const serviceButtons = document.querySelectorAll('a.pooVf.prDW');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${serviceButtons.length} service buttons in main document`);
      
      if (serviceButtons.length > 0) {
        serviceButtons.forEach((serviceButton, index) => {
          try {
            const serviceNameEl = serviceButton.querySelector('.t3RpAe.prDW.Rgstwe');
            const serviceName = serviceNameEl ? serviceNameEl.textContent.trim() : '';
            
            const priceEl = serviceButton.querySelector('.A978lb.prDW');
            const price = priceEl ? priceEl.textContent.trim() : '';
            
            const ariaLabel = serviceButton.getAttribute('aria-label') || '';
            
            const serviceContainer = serviceButton.closest('.J8zyUd');
            const dataId = serviceContainer ? serviceContainer.getAttribute('data-id') : '';
            
            if (serviceName) {
              const service = {
                title: serviceName,
                price: price || 'Price not available',
                description: ariaLabel,
                dataId: dataId,
                category: 'Uncategorized',
                extractedAt: new Date().toISOString()
              };
              
              services.push(service);
              console.log(`📝 Service ${index + 1}: ${serviceName} - ${price}`);
            }
          } catch (error) {
            console.error(`💥 Error extracting service ${index + 1}:`, error);
          }
        });
      }
      
      console.log(`🎯 GOOGLE SEARCH SERVICES: Final result - Extracted ${services.length} services`);
      return services;

    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error in extractServicesFromPopup:', error);
      return services;
    }
  }

  extractFromCategories(categoryContainers) {
    const services = [];
    
    console.log('✅ GOOGLE SEARCH SERVICES: Processing categories and services...');
    
    categoryContainers.forEach((categoryContainer, catIndex) => {
      try {
        // Extract category name
        const categoryNameEl = categoryContainer.querySelector('.EJHGm');
        const categoryName = categoryNameEl ? categoryNameEl.textContent.trim() : `Category ${catIndex + 1}`;
        console.log(`📂 GOOGLE SEARCH SERVICES: Processing category: ${categoryName}`);
        
        // Find all services within this category
        const categoryServices = categoryContainer.querySelectorAll('a.pooVf.prDW');
        console.log(`   Found ${categoryServices.length} services in ${categoryName}`);
        
        categoryServices.forEach((serviceButton, index) => {
          try {
            // Extract service name
            const serviceNameEl = serviceButton.querySelector('.t3RpAe.prDW.Rgstwe');
            const serviceName = serviceNameEl ? serviceNameEl.textContent.trim() : '';
            
            // Extract price
            const priceEl = serviceButton.querySelector('.A978lb.prDW');
            const price = priceEl ? priceEl.textContent.trim() : '';
            
            // Extract aria-label for additional info
            const ariaLabel = serviceButton.getAttribute('aria-label') || '';
            
            // Extract data-id from the service container
            const serviceContainer = serviceButton.closest('.J8zyUd');
            const dataId = serviceContainer ? serviceContainer.getAttribute('data-id') : '';
            
            if (serviceName) {
              const service = {
                title: serviceName,
                price: price || 'Price not available',
                description: ariaLabel,
                dataId: dataId,
                category: categoryName,
                extractedAt: new Date().toISOString()
              };
              
              services.push(service);
              console.log(`   📝 Service ${index + 1}: ${serviceName} - ${price}`);
            }
          } catch (error) {
            console.error(`   💥 Error extracting service ${index + 1}:`, error);
          }
        });
          } catch (error) {
        console.error(`💥 GOOGLE SEARCH SERVICES: Error processing category ${catIndex + 1}:`, error);
      }
    });
    
    console.log(`✅ GOOGLE SEARCH SERVICES: Successfully extracted ${services.length} services from categories`);
      return services;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  sendStatusUpdate(status, data) {
    try {
      chrome.runtime.sendMessage({
        action: 'googleSearchServicesUpdate',
        status: status,
        data: data
      });
    } catch (error) {
      console.error('GMB Google Search Services: Error sending status update:', error);
    }
  }

  stopExtraction() {
    console.log('🛑 GOOGLE SEARCH SERVICES: Stopping extraction...');
    this.isRunning = false;
  }

  getCurrentData() {
    return {
      servicesData: this.servicesData,
      isRunning: this.isRunning,
      currentBusiness: this.currentBusinessIndex,
      totalBusinesses: this.totalBusinesses
    };
  }

  exportToCSV() {
    console.log('📊 GOOGLE SEARCH SERVICES: Export button clicked - starting export process...');
    
    if (this.servicesData.length === 0) {
      console.log('❌ GMB Google Search Services: No services data to export');
      alert('No services data to export. Please run the extraction first.');
      return;
    }

    console.log(`📊 GMB Google Search Services: Starting CSV export with ${this.servicesData.length} businesses...`);
    
    try {
      const csvContent = this.convertServicesToCSV(this.servicesData);
      console.log('📊 GMB Google Search Services: CSV content generated successfully');
      
      const filename = `google_search_services_${new Date().toISOString().split('T')[0]}.csv`;
      console.log(`📊 GMB Google Search Services: Downloading file: ${filename}`);
      
      this.downloadCSV(csvContent, filename);
      
      console.log('✅ GMB Google Search Services: CSV export completed successfully');
      
      // Show success feedback in the docked interface using CSS classes
      if (this.dockedInterface) {
        const exportBtn = this.dockedInterface.querySelector('#gmb-services-export-btn');
        if (exportBtn) {
          const originalText = exportBtn.textContent;
          
          exportBtn.textContent = '✅ Exported!';
          exportBtn.classList.add('gmb-search-extractor__export-btn--success');
          
          setTimeout(() => {
            exportBtn.textContent = originalText;
            exportBtn.classList.remove('gmb-search-extractor__export-btn--success');
            exportBtn.classList.add('gmb-search-extractor__export-btn--default');
          }, 2000);
        } else {
          console.log('⚠️ GMB Google Search Services: Export button not found for feedback');
        }
      } else {
        console.log('⚠️ GMB Google Search Services: Docked interface not found for feedback');
      }
      
    } catch (error) {
      console.error('💥 GMB Google Search Services: Error during CSV export:', error);
      alert('Error exporting CSV: ' + error.message);
    }
  }

  convertServicesToCSV(servicesData) {
    console.log('GMB Google Search Services: Converting services data to CSV...');
    
    const headers = ['Business Name', 'Business Index', 'Type', 'Category', 'Category Description', 'Item Title', 'Item Price', 'Item Description', 'Data Info', 'Extracted At'];
    const csvRows = [headers.join(',')];

    servicesData.forEach(business => {
      console.log(`GMB Google Search Services: Processing business: ${business.businessName} with ${business.products?.length || 0} products, ${business.categories.length} categories, and ${business.qa?.length || 0} Q&A`);
      
      let hasData = false;
      
      // Add products first
      if (business.products && business.products.length > 0) {
        business.products.forEach(product => {
          const row = [
            this.escapeCsvValue(business.businessName),
            business.businessIndex,
            'Product',
            this.escapeCsvValue(product.category || 'Products'),
            '',
            this.escapeCsvValue(product.title),
            this.escapeCsvValue(product.price || ''),
            this.escapeCsvValue(product.description),
            this.escapeCsvValue(product.ludocid || product.entryId || product.dataId || ''),
            product.extractedAt
          ];
          csvRows.push(row.join(','));
          hasData = true;
        });
      }
      
      // Add categories and services
      if (business.categories.length === 0) {
        if (!hasData) {
          // Add row for business with no categories or products
          const row = [
            this.escapeCsvValue(business.businessName),
            business.businessIndex,
            'No Data',
            'No Categories Found',
            this.escapeCsvValue(business.note || 'No categories/services or products found for this business'),
            '',
            '',
            '',
            '',
            business.extractedAt
          ];
          csvRows.push(row.join(','));
        }
      } else {
        // Process categories normally
        business.categories.forEach(category => {
          console.log(`GMB Google Search Services: Processing category: ${category.categoryName} with ${category.services.length} services`);
          
          if (category.services.length === 0) {
            // Add category even if no services
            const row = [
              this.escapeCsvValue(business.businessName),
              business.businessIndex,
              'Category',
              this.escapeCsvValue(category.categoryName),
              this.escapeCsvValue(category.categoryDescription || ''),
              '',
              '',
              '',
              '',
              business.extractedAt
            ];
            csvRows.push(row.join(','));
          } else {
            category.services.forEach(service => {
              const row = [
                this.escapeCsvValue(business.businessName),
                business.businessIndex,
                'Service',
                this.escapeCsvValue(category.categoryName),
                this.escapeCsvValue(category.categoryDescription || ''),
                this.escapeCsvValue(service.title),
                this.escapeCsvValue(service.price || ''),
                this.escapeCsvValue(service.description),
                this.escapeCsvValue(service.dataId || ''),
                service.extractedAt
              ];
              csvRows.push(row.join(','));
              hasData = true;
            });
          }
        });
      }

      // Add Q&A data
      if (business.qa && business.qa.length > 0) {
        business.qa.forEach(qaItem => {
          const row = [
            this.escapeCsvValue(business.businessName),
            business.businessIndex,
            'Q&A',
            'Questions & Answers',
            '',
            this.escapeCsvValue(qaItem.question),
            '', // No price for Q&A
            this.escapeCsvValue(qaItem.answer),
            '',
            qaItem.extractedAt
          ];
          csvRows.push(row.join(','));
          hasData = true;
        });
      }
    });

    const csvContent = csvRows.join('\n');
    console.log(`GMB Google Search Services: Generated CSV with ${csvRows.length - 1} data rows`);
    return csvContent;
  }

  escapeCsvValue(value) {
    if (value === null || value === undefined) return '';
    const stringValue = String(value);
    if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }
    return stringValue;
  }

  downloadCSV(csvContent, filename) {
    console.log(`GMB Google Search Services: Downloading CSV file: ${filename}`);
    
    try {
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.classList.add('gmb-search-extractor__hidden');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      console.log('GMB Google Search Services: CSV file download initiated successfully');
    } catch (error) {
      console.error('GMB Google Search Services: Error downloading CSV file:', error);
      throw error;
    }
  }

  async closePopup() {
    try {
      console.log('🎯 GOOGLE SEARCH SERVICES: Using visual targeting to find and click close button...');
      
      // Method 1: Visual targeting with red border marker
      const closeButton = await this.findAndMarkCloseButton();
      
      if (closeButton) {
        console.log('✅ GOOGLE SEARCH SERVICES: Found and marked close button with red border');
        
        // Click the visually marked button
        try {
          closeButton.click();
          console.log('🖱️ GOOGLE SEARCH SERVICES: Clicked the red-bordered close button');
          
          // Wait for close animation
          await this.delay(2000);
          
          // Remove the red border marker using utility function
          this.removeVisualMarker(closeButton);
          
          console.log('✅ GOOGLE SEARCH SERVICES: Removed red border marker');
          
          // Check if popup actually closed
          await this.delay(1000);
          const iframe = document.querySelector('iframe#C6GqMd') || 
                         document.querySelector('iframe[name="lpc"]') || 
                         document.querySelector('iframe.PxHPSd');
          
          if (!iframe) {
            console.log('✅ GOOGLE SEARCH SERVICES: Popup successfully closed via visual targeting');
            return true;
          } else {
            console.log('📌 GOOGLE SEARCH SERVICES: Popup still open but button was clicked');
          }
          
        } catch (error) {
          console.error('💥 GOOGLE SEARCH SERVICES: Error clicking marked button:', error);
        }
      }
      
      // Method 2: Fallback - natural dismissal (click away)
      console.log('🔄 GOOGLE SEARCH SERVICES: Fallback - trying natural dismissal...');
      
      try {
        // Click on a safe area to dismiss popup
        const searchResults = document.querySelector('#search') || 
                             document.querySelector('.g') || 
                             document.querySelector('#main') ||
                             document.querySelector('body');
        
        if (searchResults) {
          const clickEvent = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: 100,
            clientY: 300
          });
          searchResults.dispatchEvent(clickEvent);
          
          console.log('🖱️ GOOGLE SEARCH SERVICES: Clicked on search results area as fallback');
          await this.delay(1000);
        }
      } catch (error) {
        console.log('⚠️ GOOGLE SEARCH SERVICES: Error with fallback method:', error);
      }
      
      // Always return true and continue
      console.log('✅ GOOGLE SEARCH SERVICES: Close attempt completed, continuing extraction');
      return true;

    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error in visual targeting closePopup:', error);
      return true; // Continue regardless
    }
  }

  async findAndMarkCloseButton() {
    try {
      console.log('🔍 GOOGLE SEARCH SERVICES: Searching for close button inside iframe...');
      
      // First, find the iframe that contains the services popup
      const iframe = document.querySelector('iframe#C6GqMd') || 
                     document.querySelector('iframe[name="lpc"]') || 
                     document.querySelector('iframe.PxHPSd');
      
      if (!iframe) {
        console.log('❌ GOOGLE SEARCH SERVICES: No iframe found - cannot search for close button');
        return null;
      }
      
      console.log('✅ GOOGLE SEARCH SERVICES: Found iframe, accessing its document...');
      
      // Access the iframe's document
      let iframeDoc;
      try {
        iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        if (!iframeDoc) {
          console.log('❌ GOOGLE SEARCH SERVICES: Cannot access iframe document (cross-origin restrictions)');
          return null;
        }
        console.log('✅ GOOGLE SEARCH SERVICES: Successfully accessed iframe document');
      } catch (error) {
        console.log('❌ GOOGLE SEARCH SERVICES: Error accessing iframe document:', error);
        return null;
      }
      
      // Multiple strategies to find the close button inside the iframe
      const buttonSelectors = [
        // Strategy 1: Exact jsname match
        'button[jsname="FdGojd"]',
        
        // Strategy 2: Aria label with specific classes
        'button[aria-label="Close"].VfPpkd-Bz112c-LgbsSe',
        'button[aria-label="Close"]',
        
        // Strategy 3: Material design close button pattern
        'button.VfPpkd-Bz112c-LgbsSe.yHy1rc.eT1oJ.mN1ivc',
        'button.VfPpkd-Bz112c-LgbsSe',
        
        // Strategy 4: Any button with close-related attributes
        'button[data-disable-idom="true"]',
        
        // Strategy 5: Look for buttons containing close icon
        'button i.material-icons-extended'
      ];
      
      let closeButton = null;
      
      // Try each selector in the iframe document
      for (const selector of buttonSelectors) {
        try {
          if (selector === 'button i.material-icons-extended') {
            // Special handling for icon-based search
            const icons = iframeDoc.querySelectorAll(selector);
            for (const icon of icons) {
              if (icon.textContent.trim() === 'close') {
                closeButton = icon.closest('button');
                if (closeButton) {
                  console.log(`✅ GOOGLE SEARCH SERVICES: Found close button via icon search in iframe`);
                  break;
                }
              }
            }
          } else {
            closeButton = iframeDoc.querySelector(selector);
            if (closeButton) {
              console.log(`✅ GOOGLE SEARCH SERVICES: Found close button via selector in iframe: ${selector}`);
              break;
            }
          }
        } catch (error) {
          console.log(`⚠️ GOOGLE SEARCH SERVICES: Iframe selector failed: ${selector}`, error);
        }
      }
      
      // Additional fallback: search all buttons in iframe
      if (!closeButton) {
        console.log('🔄 GOOGLE SEARCH SERVICES: Trying fallback search in iframe...');
        try {
          const allButtons = iframeDoc.querySelectorAll('button');
          console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${allButtons.length} buttons in iframe`);
          
          for (const btn of allButtons) {
            // Check for close-related attributes or content
            const ariaLabel = btn.getAttribute('aria-label');
            const jsname = btn.getAttribute('jsname');
            const innerText = btn.textContent.trim().toLowerCase();
            
            if ((ariaLabel && ariaLabel.toLowerCase().includes('close')) ||
                (jsname === 'FdGojd') ||
                (innerText.includes('close')) ||
                (btn.querySelector('i') && btn.querySelector('i').textContent.trim() === 'close')) {
              closeButton = btn;
              console.log(`✅ GOOGLE SEARCH SERVICES: Found close button via fallback search in iframe`);
              break;
            }
          }
        } catch (error) {
          console.log('⚠️ GOOGLE SEARCH SERVICES: Error in fallback search:', error);
        }
      }
      
      if (!closeButton) {
        console.log('❌ GOOGLE SEARCH SERVICES: Could not find close button in iframe');
        return null;
      }
      
      // Add distinctive red border marker to the button inside the iframe using utility function
      this.addVisualMarker(closeButton, 'red');
      
      // Add a custom data attribute for additional targeting
      closeButton.setAttribute('data-gmb-marked-close', 'true');
      
      console.log('🔴 GOOGLE SEARCH SERVICES: Added red border marker to close button inside iframe');
      
      // Wait a moment for visual confirmation
      await this.delay(500);
      
      return closeButton;
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error finding and marking close button in iframe:', error);
      return null;
    }
  }

  async findQAButton() {
    try {
      console.log('🔍 GOOGLE SEARCH SERVICES: Searching for Q&A button with visual targeting...');
      
      // Look for the specific "See all questions" button
      const qaButtons = document.querySelectorAll('a[jsaction="zwAFKf"]');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${qaButtons.length} potential Q&A buttons with jsaction="zwAFKf"`);
      
      if (qaButtons.length === 0) {
        console.log('❌ GOOGLE SEARCH SERVICES: No Q&A buttons found');
        return null;
      }
      
      // Find the button that contains "See all questions" text
      let qaButton = null;
      for (const button of qaButtons) {
        try {
          const spanElement = button.querySelector('.QlPmEd');
          if (spanElement && spanElement.textContent.toLowerCase().includes('see all questions')) {
            console.log('✅ GOOGLE SEARCH SERVICES: Found "See all questions" button');
            qaButton = button;
            break;
          }
        } catch (error) {
          console.log('⚠️ GOOGLE SEARCH SERVICES: Error checking button text:', error);
        }
      }
      
      if (!qaButton) {
        console.log('❌ GOOGLE SEARCH SERVICES: No "See all questions" button found');
        return null;
      }
      
      // Add visual red border marker to the Q&A button using utility function
      console.log('🔴 GOOGLE SEARCH SERVICES: Adding red border marker to Q&A button...');
      this.addVisualMarker(qaButton, 'red');
      
      // Add a custom data attribute for additional targeting
      qaButton.setAttribute('data-gmb-marked-qa', 'true');
      
      console.log('✅ GOOGLE SEARCH SERVICES: Added red border marker to Q&A button');
      
      // Wait a moment for visual confirmation
      await this.delay(1000);
      
      return qaButton;
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error finding Q&A button:', error);
      return null;
    }
  }

  async extractQAFromPopup() {
    const qaData = [];
    
    try {
      console.log('❓ GOOGLE SEARCH SERVICES: Starting Q&A extraction from popup...');
      
      // Wait for popup to fully load
      await this.delay(3000);
      
      // Look for the iframe that contains the Q&A
      const iframe = document.querySelector('iframe#C6GqMd') || 
                    document.querySelector('iframe[name="lpc"]') || 
                    document.querySelector('iframe.PxHPSd');
      
      if (!iframe) {
        console.log('❌ GOOGLE SEARCH SERVICES: No iframe found for Q&A popup');
        console.log('🔍 GOOGLE SEARCH SERVICES: Looking in main document as fallback...');
      } else {
        console.log('✅ GOOGLE SEARCH SERVICES: Found iframe for Q&A popup');
        
        // Wait a bit more for iframe content to load
        await this.delay(2000);
        
        try {
          // Get the iframe's document
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
          
          if (!iframeDoc) {
            console.log('❌ GOOGLE SEARCH SERVICES: Cannot access iframe document (possibly cross-origin)');
          } else {
            console.log('✅ GOOGLE SEARCH SERVICES: Accessed iframe document successfully');
            
            // Look for questions in the iframe using the correct selector
            const questionElements = iframeDoc.querySelectorAll('.mq1Pic.dFINPc.aTWSi');
            console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${questionElements.length} questions in iframe`);
            
            // Look for answers in the iframe using the correct selector
            const answerElements = iframeDoc.querySelectorAll('.CKgeL.V8kO1.mq1Pic.q0Zn5b');
            console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${answerElements.length} answers in iframe`);
            
            // Match questions with answers (they should be in the same order)
            const maxPairs = Math.min(questionElements.length, answerElements.length);
            
            for (let i = 0; i < maxPairs; i++) {
              try {
                const question = questionElements[i].textContent.trim();
                const answer = answerElements[i].textContent.trim();
                
                if (question && answer) {
                  qaData.push({
                    question: question,
                    answer: answer,
                    extractedAt: new Date().toISOString()
                  });
                  console.log(`❓ Q&A ${i + 1}: Q: "${question.substring(0, 50)}..." A: "${answer.substring(0, 50)}..."`);
                }
              } catch (error) {
                console.error(`💥 Error extracting Q&A pair ${i + 1}:`, error);
              }
            }
            
            return qaData;
          }
        } catch (error) {
          console.error('💥 GOOGLE SEARCH SERVICES: Error accessing iframe content:', error);
        }
      }
      
      // Fallback: try to find Q&A in the main document
      console.log('🔍 GOOGLE SEARCH SERVICES: Falling back to main document search for Q&A...');
      
      // Look for questions in main document
      const questionElements = document.querySelectorAll('.mq1Pic.dFINPc.aTWSi');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${questionElements.length} questions in main document`);
      
      // Look for answers in main document
      const answerElements = document.querySelectorAll('.CKgeL.V8kO1.mq1Pic.q0Zn5b');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${answerElements.length} answers in main document`);
      
      // Match questions with answers (they should be in the same order)
      const maxPairs = Math.min(questionElements.length, answerElements.length);
      
      for (let i = 0; i < maxPairs; i++) {
        try {
          const question = questionElements[i].textContent.trim();
          const answer = answerElements[i].textContent.trim();
          
          if (question && answer) {
            qaData.push({
              question: question,
              answer: answer,
              extractedAt: new Date().toISOString()
            });
            console.log(`❓ Q&A ${i + 1}: Q: "${question.substring(0, 50)}..." A: "${answer.substring(0, 50)}..."`);
          }
        } catch (error) {
          console.error(`💥 Error extracting Q&A pair ${i + 1}:`, error);
        }
      }
      
      console.log(`🎯 GOOGLE SEARCH SERVICES: Final result - Extracted ${qaData.length} Q&A pairs`);
      return qaData;
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error in extractQAFromPopup:', error);
      return qaData;
    }
  }

  async closeQAPopup() {
    try {
      console.log('🎯 GOOGLE SEARCH SERVICES: Using positional targeting to close Q&A popup...');
      
      // Method 1: Positional clicking approach
      const success = await this.closeQAPopupPositionally();
      if (success) {
        console.log('✅ GOOGLE SEARCH SERVICES: Q&A popup closed via positional targeting');
        return true;
      }
      
      // Method 2: Visual targeting (fallback)
      console.log('🔄 GOOGLE SEARCH SERVICES: Fallback - trying visual targeting for Q&A close button...');
      const closeButton = await this.findAndMarkQACloseButton();
      
      if (closeButton) {
        console.log('✅ GOOGLE SEARCH SERVICES: Found Q&A close button');
        
        // Click the close button
        try {
          closeButton.click();
          console.log('🖱️ GOOGLE SEARCH SERVICES: Clicked the Q&A close button');
          
          // Wait for close animation
          await this.delay(2000);
          
          console.log('✅ GOOGLE SEARCH SERVICES: Q&A close button clicked successfully');
          return true;
          
        } catch (error) {
          console.error('💥 GOOGLE SEARCH SERVICES: Error clicking Q&A close button:', error);
        }
      }
      
      // Method 3: Natural dismissal (final fallback)
      console.log('🔄 GOOGLE SEARCH SERVICES: Final fallback - trying natural dismissal for Q&A...');
      
      try {
        // Click on a safe area to dismiss popup
        const searchResults = document.querySelector('#search') || 
                             document.querySelector('.g') || 
                             document.querySelector('#main') ||
                             document.querySelector('body');
        
        if (searchResults) {
          const clickEvent = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: 100,
            clientY: 300
          });
          searchResults.dispatchEvent(clickEvent);
          
          console.log('🖱️ GOOGLE SEARCH SERVICES: Clicked on search results area as final fallback for Q&A');
          await this.delay(1000);
        }
      } catch (error) {
        console.log('⚠️ GOOGLE SEARCH SERVICES: Error with final fallback method for Q&A:', error);
      }
      
      // Always return true and continue
      console.log('✅ GOOGLE SEARCH SERVICES: Q&A close attempt completed, continuing extraction');
      return true;

    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error in closeQAPopup:', error);
      return true; // Continue regardless
    }
  }

  async closeQAPopupPositionally() {
    try {
      console.log('🎯 GOOGLE SEARCH SERVICES: Starting positional Q&A popup close...');
      
      // First, find the main dialog container
      const dialogSelectors = [
        'div[role="dialog"]',
        '.d1e3vf.VDgVie.ya0wRd.IjAyyc',
        'div[jsname="AHe6Kc"]'
      ];
      
      let dialogContainer = null;
      for (const selector of dialogSelectors) {
        dialogContainer = document.querySelector(selector);
        if (dialogContainer) {
          console.log(`✅ GOOGLE SEARCH SERVICES: Found dialog container with selector: ${selector}`);
          break;
        }
      }
      
      if (!dialogContainer) {
        console.log('❌ GOOGLE SEARCH SERVICES: No dialog container found for positional targeting');
        return false;
      }
      
      // Get the dialog's position and dimensions
      const dialogRect = dialogContainer.getBoundingClientRect();
      console.log('📐 GOOGLE SEARCH SERVICES: Dialog dimensions:', {
        top: dialogRect.top,
        right: dialogRect.right,
        bottom: dialogRect.bottom,
        left: dialogRect.left,
        width: dialogRect.width,
        height: dialogRect.height
      });
      
      // Calculate the target position: 40px from top, 40px from right
      const targetX = dialogRect.right - 40;
      const targetY = dialogRect.top + 40;
      
      console.log(`🎯 GOOGLE SEARCH SERVICES: Target click position: (${targetX}, ${targetY})`);
      
      // Add visual marker at target position for debugging using CSS classes
      const marker = document.createElement('div');
      marker.className = 'gmb-search-extractor__position-marker';
      marker.style.top = `${targetY - 5}px`;
      marker.style.left = `${targetX - 5}px`;
      document.body.appendChild(marker);
      
      console.log('🔴 GOOGLE SEARCH SERVICES: Added red marker at target position');
      
      // Wait a moment for visual confirmation
      await this.delay(1000);
      
      // Try multiple click approaches at the target position
      const clickMethods = [
        // Method 1: elementFromPoint + click
        () => {
          const targetElement = document.elementFromPoint(targetX, targetY);
          if (targetElement) {
            console.log('🖱️ GOOGLE SEARCH SERVICES: Found element at target position:', targetElement.className, targetElement.tagName);
            targetElement.click();
            return true;
          }
          return false;
        },
        
        // Method 2: Mouse event at coordinates
        () => {
          const clickEvent = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: targetX,
            clientY: targetY
          });
          document.elementFromPoint(targetX, targetY)?.dispatchEvent(clickEvent);
          return true;
        },
        
        // Method 3: Focus and Enter key on element at position
        () => {
          const targetElement = document.elementFromPoint(targetX, targetY);
          if (targetElement && targetElement.focus) {
            targetElement.focus();
            const enterEvent = new KeyboardEvent('keydown', {
              key: 'Enter',
              code: 'Enter',
              keyCode: 13,
              bubbles: true
            });
            targetElement.dispatchEvent(enterEvent);
            return true;
          }
          return false;
        }
      ];
      
      // Try each click method
      let success = false;
      for (let i = 0; i < clickMethods.length; i++) {
        try {
          console.log(`🔄 GOOGLE SEARCH SERVICES: Trying click method ${i + 1}...`);
          const result = clickMethods[i]();
          if (result) {
            console.log(`✅ GOOGLE SEARCH SERVICES: Click method ${i + 1} executed successfully`);
            success = true;
            break;
          }
        } catch (error) {
          console.log(`⚠️ GOOGLE SEARCH SERVICES: Click method ${i + 1} failed:`, error);
        }
      }
      
      // Wait for potential close animation
      await this.delay(2000);
      
      // Remove the visual marker
      if (marker && marker.parentNode) {
        marker.parentNode.removeChild(marker);
        console.log('🔴 GOOGLE SEARCH SERVICES: Removed red position marker');
      }
      
      // Check if dialog is still present
      const stillOpen = document.querySelector('div[role="dialog"]') || 
                       document.querySelector('.d1e3vf.VDgVie.ya0wRd.IjAyyc') ||
                       document.querySelector('div[jsname="AHe6Kc"]');
      
      if (!stillOpen) {
        console.log('✅ GOOGLE SEARCH SERVICES: Q&A popup successfully closed via positional targeting!');
        return true;
      } else {
        console.log('📌 GOOGLE SEARCH SERVICES: Q&A popup still open after positional targeting');
        return false;
      }
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error in positional Q&A popup close:', error);
      return false;
    }
  }

  async findAndMarkQACloseButton() {
    try {
      console.log('🔍 GOOGLE SEARCH SERVICES: Searching for Q&A close button with exact class MULd7b...');
      
      // Direct targeting of the exact close button class provided by user
      let closeButton = null;
      
      // Method 1: Target the exact class in main document
      closeButton = document.querySelector('.MULd7b[aria-label="Close"]');
      if (closeButton) {
        console.log('✅ GOOGLE SEARCH SERVICES: Found Q&A close button in main document via .MULd7b[aria-label="Close"]');
        return closeButton;
      }
      
      // Method 2: Target just the class without aria-label requirement
      closeButton = document.querySelector('.MULd7b');
      if (closeButton) {
        console.log('✅ GOOGLE SEARCH SERVICES: Found Q&A close button in main document via .MULd7b');
        return closeButton;
      }
      
      // Method 3: Try in iframe as fallback
      console.log('🔍 GOOGLE SEARCH SERVICES: Close button not found in main document, trying iframe...');
      
      // Find the iframe that contains the Q&A popup
      const iframe = document.querySelector('iframe#C6GqMd') || 
                     document.querySelector('iframe[name="lpc"]') || 
                     document.querySelector('iframe.PxHPSd');
      
      if (!iframe) {
        console.log('❌ GOOGLE SEARCH SERVICES: No iframe found');
        return null;
      }
      
      console.log('✅ GOOGLE SEARCH SERVICES: Found iframe, accessing its document for Q&A close button...');
      
      // Access the iframe's document
      let iframeDoc;
      try {
        iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        if (!iframeDoc) {
          console.log('❌ GOOGLE SEARCH SERVICES: Cannot access iframe document (cross-origin restrictions)');
          return null;
        }
        console.log('✅ GOOGLE SEARCH SERVICES: Successfully accessed iframe document for Q&A');
      } catch (error) {
        console.log('❌ GOOGLE SEARCH SERVICES: Error accessing iframe document for Q&A:', error);
        return null;
      }
      
      // Method 4: Target the exact class in iframe
      closeButton = iframeDoc.querySelector('.MULd7b[aria-label="Close"]');
      if (closeButton) {
        console.log('✅ GOOGLE SEARCH SERVICES: Found Q&A close button in iframe via .MULd7b[aria-label="Close"]');
        return closeButton;
      }
      
      // Method 5: Target just the class in iframe without aria-label requirement
      closeButton = iframeDoc.querySelector('.MULd7b');
      if (closeButton) {
        console.log('✅ GOOGLE SEARCH SERVICES: Found Q&A close button in iframe via .MULd7b');
        return closeButton;
      }
      
      console.log('❌ GOOGLE SEARCH SERVICES: Could not find Q&A close button with class MULd7b anywhere');
      return null;
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error finding Q&A close button:', error);
      return null;
    }
  }

  // Extract reviews from multiple businesses according to the specified range
  async extractReviewsFromMultipleBusinesses(businessIndicesToProcess, reviewsMaxLimit) {
    try {
      console.log(`🔄 GOOGLE SEARCH SERVICES: Starting review extraction for ${businessIndicesToProcess.length} businesses`);
      
      // Reset tracking variables
      this.primaryBusinessName = null;
      let allReviewsData = [];
      let totalExtractedReviews = 0;
      let successfulBusinesses = 0;
      let failedBusinesses = 0;
      
      // DISABLE EXPORT BUTTON DURING EXTRACTION to prevent accidental navigation
      const exportBtn = this.dockedInterface?.querySelector('#gmb-services-export-btn');
      if (exportBtn) {
        exportBtn.disabled = true;
        exportBtn.textContent = '⏳ Extracting...';
        console.log('🔒 GOOGLE SEARCH SERVICES: Disabled Export CSV button during extraction');
      }
      
      // Update interface to show we're starting
      this.updateDockedInterface('Starting review extraction...', {
        totalBusinesses: businessIndicesToProcess.length,
        currentBusiness: 0,
        totalReviews: 0
      });
      
      // Get all business cards
      const allBusinessCards = document.querySelectorAll('.vwVdIc.wzN8Ac.rllt__link.a-no-hover-decoration');
      console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${allBusinessCards.length} total business cards`);
      
      // Process each business in the specified range
      for (let i = 0; i < businessIndicesToProcess.length; i++) {
        const businessIndex = businessIndicesToProcess[i] - 1; // Convert to 0-based index
        const currentBusinessNumber = businessIndex + 1;
        
        console.log(`\n📍 GOOGLE SEARCH SERVICES: === Processing Business ${currentBusinessNumber}/${allBusinessCards.length} ===`);
        
        // Update interface with current business progress
        this.updateDockedInterface(`Processing business ${i + 1} of ${businessIndicesToProcess.length}...`, {
          totalBusinesses: businessIndicesToProcess.length,
          currentBusiness: i + 1,
          totalReviews: totalExtractedReviews
        });
        
        if (businessIndex >= allBusinessCards.length) {
          console.log(`⚠️ GOOGLE SEARCH SERVICES: Business index ${currentBusinessNumber} exceeds available businesses (${allBusinessCards.length})`);
          failedBusinesses++;
          continue;
        }
        
        const businessCard = allBusinessCards[businessIndex];
        const businessName = this.extractBusinessNameFromCard(businessCard);
        
        console.log(`🏢 GOOGLE SEARCH SERVICES: Processing "${businessName}" (index ${currentBusinessNumber})`);
        
        // Store primary business name for single business extractions
        if (businessIndicesToProcess.length === 1) {
          this.primaryBusinessName = businessName;
          console.log(`📁 GOOGLE SEARCH SERVICES: Set primary business name: "${this.primaryBusinessName}"`);
        }
        
        try {
          // Extract reviews for this business using the multiple review scraper
          if (window.googleSearchMultipleReviewScraper) {
            // Set the review limit if specified (this will also reset the reviewData array)
            if (reviewsMaxLimit) {
              window.googleSearchMultipleReviewScraper.setMaxReviews(reviewsMaxLimit);
            }
            
            // Extract reviews for this specific business
            const result = await this.extractReviewsForSingleBusiness(businessCard, reviewsMaxLimit, businessName);
            
            if (result.success && result.reviews && result.reviews.length > 0) {
              allReviewsData = allReviewsData.concat(result.reviews);
              totalExtractedReviews += result.reviews.length;
              successfulBusinesses++;
              
              console.log(`✅ GOOGLE SEARCH SERVICES: Successfully extracted ${result.reviews.length} reviews from "${businessName}"`);
              
              // Update interface after successful extraction
              this.updateDockedInterface(`Extracted ${result.reviews.length} reviews from "${businessName}"...`, {
                totalBusinesses: businessIndicesToProcess.length,
                currentBusiness: i + 1,
                totalReviews: totalExtractedReviews
              });
            } else {
              console.log(`⚠️ GOOGLE SEARCH SERVICES: No reviews found for "${businessName}"`);
              failedBusinesses++;
              
              // Update interface for failed extraction
              this.updateDockedInterface(`No reviews found for "${businessName}"...`, {
                totalBusinesses: businessIndicesToProcess.length,
                currentBusiness: i + 1,
                totalReviews: totalExtractedReviews
              });
            }
          } else {
            throw new Error('Google Search Multiple Review Scraper not available');
          }
          
          // Wait between businesses to avoid rate limiting
          if (i < businessIndicesToProcess.length - 1) {
            console.log('⏳ GOOGLE SEARCH SERVICES: Waiting 2 seconds between businesses...');
            
            // Update interface during wait
            this.updateDockedInterface(`Waiting before next business...`, {
              totalBusinesses: businessIndicesToProcess.length,
              currentBusiness: i + 1,
              totalReviews: totalExtractedReviews
            });
            
            await this.delay(2000);
          }
          
        } catch (businessError) {
          console.error(`💥 GOOGLE SEARCH SERVICES: Error extracting reviews from "${businessName}":`, businessError);
          failedBusinesses++;
          
          // Update interface for error
          this.updateDockedInterface(`Error extracting from "${businessName}"...`, {
            totalBusinesses: businessIndicesToProcess.length,
            currentBusiness: i + 1,
            totalReviews: totalExtractedReviews
          });
        }
      }
      
      // Final results
      console.log(`\n🎉 GOOGLE SEARCH SERVICES: === EXTRACTION COMPLETE ===`);
      console.log(`📊 Total Reviews Extracted: ${totalExtractedReviews}`);
      console.log(`✅ Successful Businesses: ${successfulBusinesses}`);
      console.log(`❌ Failed Businesses: ${failedBusinesses}`);
      
      // Update interface with final results
      this.updateDockedInterface('Extraction complete! Preparing export...', {
        totalBusinesses: businessIndicesToProcess.length,
        currentBusiness: businessIndicesToProcess.length,
        totalReviews: totalExtractedReviews
      });
      
      // Export all reviews data
      if (allReviewsData.length > 0) {
        // Determine business name for filename
        let businessNameForFilename = null;
        
        // If only one business was successfully processed, use its name for the filename
        if (successfulBusinesses === 1 && allReviewsData.length > 0) {
          // Try to get business name from the first review data
          businessNameForFilename = allReviewsData[0].businessName;
          console.log(`📁 GOOGLE SEARCH SERVICES: Using single business name for filename: "${businessNameForFilename}"`);
        } else if (businessIndicesToProcess.length === 1) {
          // If only one business was requested (even if extraction failed), try stored name first
          businessNameForFilename = this.primaryBusinessName;
          console.log(`📁 GOOGLE SEARCH SERVICES: Using stored primary business name for filename: "${businessNameForFilename}"`);
          
          // Fallback: try to get the name from the business card
          if (!businessNameForFilename) {
            const businessIndex = businessIndicesToProcess[0] - 1;
            const allBusinessCards = document.querySelectorAll('.vwVdIc.wzN8Ac.rllt__link.a-no-hover-decoration');
            if (businessIndex < allBusinessCards.length) {
              businessNameForFilename = this.extractBusinessNameFromCard(allBusinessCards[businessIndex]);
              console.log(`📁 GOOGLE SEARCH SERVICES: Using extracted business name for filename: "${businessNameForFilename}"`);
            }
          }
        }
        
        this.exportReviewsToCSV(allReviewsData, businessNameForFilename);
        
        // ENABLE EXPORT BUTTON after successful extraction
        const exportBtn = this.dockedInterface?.querySelector('#gmb-services-export-btn');
        if (exportBtn) {
          exportBtn.disabled = false;
          exportBtn.textContent = '📊 Export CSV';
          console.log('✅ GOOGLE SEARCH SERVICES: Re-enabled Export CSV button after successful extraction');
        }
        
        // Send completion notification
        chrome.runtime.sendMessage({
          action: 'showNotification',
          title: '⭐ Google Search Review Extraction Complete!',
          message: `Successfully extracted ${totalExtractedReviews} reviews from ${successfulBusinesses} businesses!`,
          type: 'basic',
          iconUrl: '/images/icon48.png'
        });
        
        // Update interface with completion status - DO NOT auto-close
        this.updateDockedInterface(`✅ Complete! Extracted ${totalExtractedReviews} reviews. Click X to close.`, {
          totalBusinesses: businessIndicesToProcess.length,
          currentBusiness: businessIndicesToProcess.length,
          totalReviews: totalExtractedReviews
        });
        
        console.log('✅ GOOGLE SEARCH SERVICES: Extraction complete - interface stays open until manually closed');
        
      } else {
        console.error('❌ GOOGLE SEARCH SERVICES: No reviews were extracted from any business');
        
        // ENABLE EXPORT BUTTON even when no reviews found (so user can close interface)
        const exportBtn = this.dockedInterface?.querySelector('#gmb-services-export-btn');
        if (exportBtn) {
          exportBtn.disabled = true; // Keep disabled since no data to export
          exportBtn.textContent = '📊 Export CSV';
          console.log('🔒 GOOGLE SEARCH SERVICES: Export CSV button remains disabled - no data to export');
        }
        
        // Send error notification
        chrome.runtime.sendMessage({
          action: 'showNotification',
          title: '❌ Google Search Review Extraction Failed',
          message: 'No reviews were found in the specified businesses',
          type: 'basic',
          iconUrl: '/images/icon48.png'
        });
        
        // Update interface with error status - DO NOT auto-close
        this.updateDockedInterface(`❌ No reviews found. Click X to close.`, {
          totalBusinesses: businessIndicesToProcess.length,
          currentBusiness: businessIndicesToProcess.length,
          totalReviews: 0
        });
        
        console.log('❌ GOOGLE SEARCH SERVICES: No reviews found - interface stays open until manually closed');
      }
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error during multi-business review extraction:', error);
      
      // ENABLE EXPORT BUTTON after error (restore interface state)
      const exportBtn = this.dockedInterface?.querySelector('#gmb-services-export-btn');
      if (exportBtn) {
        exportBtn.disabled = true; // Keep disabled since extraction failed
        exportBtn.textContent = '📊 Export CSV';
        console.log('🔒 GOOGLE SEARCH SERVICES: Export CSV button remains disabled - extraction failed');
      }
      
      // Send error notification
      chrome.runtime.sendMessage({
        action: 'showNotification',
        title: '❌ Google Search Review Extraction Error',
        message: `Review extraction error: ${error.message}`,
        type: 'basic',
        iconUrl: '/images/icon48.png'
      });
      
      // Update interface with error status - DO NOT auto-close
      this.updateDockedInterface(`❌ Error: ${error.message}. Click X to close.`, {
        totalBusinesses: 0,
        currentBusiness: 0,
        totalReviews: 0
      });
      
      console.log('❌ GOOGLE SEARCH SERVICES: Error occurred - interface stays open until manually closed');
    }
  }

  // Extract reviews for a single business 
  async extractReviewsForSingleBusiness(businessCard, reviewsMaxLimit, businessName) {
    try {
      console.log(`🔍 GOOGLE SEARCH SERVICES: Starting review extraction for "${businessName}"`);
      
      // Click the business card to open popup - use safer clicking method
      console.log('🖱️ GOOGLE SEARCH SERVICES: Clicking business card...');
      
      // Find a safe area to click - prioritize business name or main container
      let clickTarget = businessCard;
      
      // Try to find the business name element first (safest to click)
      const nameSelectors = [
        '.OSrXXb.RfnDt.o5Th0.E3DyYd.VbTUod',
        '.vwVdIc.wzN8Ac.rllt__link.a-no-hover-decoration .OSrXXb',
        '.BNeawe.vvjwJb.AP7Wnd',
        '.sVXRqc'
      ];
      
      for (const selector of nameSelectors) {
        const nameElement = businessCard.querySelector(selector);
        if (nameElement) {
          clickTarget = nameElement;
          console.log('🎯 GOOGLE SEARCH SERVICES: Found business name element to click');
          break;
        }
      }
      
      // If we have the main business card, use programmatic click to avoid hitting wrong elements
      if (clickTarget === businessCard) {
        console.log('🎯 GOOGLE SEARCH SERVICES: Using main business card for click');
        
        // Create a click event and dispatch it to the center-bottom area to avoid share links
        const rect = businessCard.getBoundingClientRect();
        const clickX = rect.left + (rect.width * 0.7); // 70% from left (right side but not extreme right)
        const clickY = rect.top + (rect.height * 0.8); // 80% from top (bottom area)
        
        const clickEvent = new MouseEvent('click', {
          view: window,
          bubbles: true,
          cancelable: true,
          clientX: clickX,
          clientY: clickY
        });
        
        businessCard.dispatchEvent(clickEvent);
        console.log(`🎯 GOOGLE SEARCH SERVICES: Clicked at coordinates (${clickX}, ${clickY}) to avoid share links`);
      } else {
        // Click on the business name element (safest)
        clickTarget.click();
        console.log('🎯 GOOGLE SEARCH SERVICES: Clicked on business name element');
      }
      
      // Wait for popup to load
      await this.delay(3000); // Increase wait time to ensure popup loads fully
      
      // Use the multiple review scraper to extract reviews from the opened popup
      // Don't let it click another business card - work with the current popup
      const result = await this.extractReviewsFromCurrentPopup(reviewsMaxLimit, businessName);
      
      return result;
      
    } catch (error) {
      console.error(`💥 GOOGLE SEARCH SERVICES: Error extracting reviews for "${businessName}":`, error);
      return { success: false, error: error.message };
    }
  }

  // Extract reviews from the currently opened popup (no clicking needed)
  async extractReviewsFromCurrentPopup(reviewsMaxLimit, businessName) {
    try {
      console.log(`🔍 GOOGLE SEARCH SERVICES: Extracting reviews from current popup for "${businessName}"`);
      
      if (!window.googleSearchMultipleReviewScraper) {
        throw new Error('Google Search Multiple Review Scraper not available');
      }
      
      // Set the review limit if specified
      if (reviewsMaxLimit) {
        window.googleSearchMultipleReviewScraper.setMaxReviews(reviewsMaxLimit);
      }
      
      // Navigate to reviews section in the current popup
      console.log('📍 GOOGLE SEARCH SERVICES: Navigating to reviews section...');
      
      // Update progress: Finding reviews
      this.updateDockedInterface(`Finding reviews for "${businessName}"...`, {
        totalBusinesses: this.totalBusinesses || 1,
        currentBusiness: this.currentBusinessIndex + 1 || 1,
        totalReviews: 0
      });
      
      const navResult = await window.googleSearchMultipleReviewScraper.navigateToReviews();
      
      if (!navResult) {
        console.log('⚠️ GOOGLE SEARCH SERVICES: Could not navigate to reviews section');
        return { success: false, error: 'Could not find reviews section' };
      }
      
      // Count current reviews immediately - no wait needed
      const initialCount = window.googleSearchMultipleReviewScraper.countCurrentVisibleReviews();
      console.log(`📊 GOOGLE SEARCH SERVICES: Initial visible reviews: ${initialCount}`);
      
      // Update progress: Reviews found
      this.updateDockedInterface(`Found ${initialCount} reviews, loading more...`, {
        totalBusinesses: this.totalBusinesses || 1,
        currentBusiness: this.currentBusinessIndex + 1 || 1,
        totalReviews: initialCount
      });
      
      // Load more reviews through scrolling
      console.log('🔄 GOOGLE SEARCH SERVICES: Loading more reviews...');
      await window.googleSearchMultipleReviewScraper.loadAllReviewsThroughScrolling();
      
      // Update progress: Expanding reviews
      this.updateDockedInterface(`Expanding review text...`, {
        totalBusinesses: this.totalBusinesses || 1,
        currentBusiness: this.currentBusinessIndex + 1 || 1,
        totalReviews: window.googleSearchMultipleReviewScraper.countCurrentVisibleReviews()
      });
      
      // Expand "More" buttons
      console.log('🔽 GOOGLE SEARCH SERVICES: Expanding review text...');
      await window.googleSearchMultipleReviewScraper.runReadMoreScript();
      
      // Update progress: Extracting data
      this.updateDockedInterface(`Extracting review data...`, {
        totalBusinesses: this.totalBusinesses || 1,
        currentBusiness: this.currentBusinessIndex + 1 || 1,
        totalReviews: window.googleSearchMultipleReviewScraper.countCurrentVisibleReviews()
      });
      
      // Extract all reviews
      console.log('📊 GOOGLE SEARCH SERVICES: Extracting review data...');
      const extractedReviews = await window.googleSearchMultipleReviewScraper.extractAllReviews();
      
      const finalCount = extractedReviews ? extractedReviews.length : 0;
      console.log(`✅ GOOGLE SEARCH SERVICES: Extracted ${finalCount} reviews from "${businessName}"`);
      
      return { 
        success: true, 
        reviews: extractedReviews || [], 
        reviewCount: finalCount,
        businessName: businessName
      };
      
    } catch (error) {
      console.error(`💥 GOOGLE SEARCH SERVICES: Error extracting reviews from popup:`, error);
      return { success: false, error: error.message };
    }
  }

  // Extract business name from card for identification
  extractBusinessNameFromCard(businessCard) {
    try {
      // Try multiple selectors to get business name
      const nameSelectors = [
        '.OSrXXb.RfnDt.o5Th0.E3DyYd.VbTUod',
        '.vwVdIc.wzN8Ac.rllt__link.a-no-hover-decoration .OSrXXb',
        '.OSrXXb', // Generic business name selector (works with Google Search)
        '.BNeawe.vvjwJb.AP7Wnd',
        '.sVXRqc',
        'h3', // Common heading selector for Google Search
        '.LC20lb', // Google Search title selector
        '.DKV0Md' // Additional Google Search selector
      ];
      
      for (const selector of nameSelectors) {
        const nameElement = businessCard.querySelector(selector);
        if (nameElement && nameElement.textContent.trim()) {
          return nameElement.textContent.trim();
        }
      }

      // Fallback: try getting text from the link itself
      const linkText = businessCard.textContent.trim();
      if (linkText) {
        // Get the first meaningful part of the text
        const firstLine = linkText.split('\n')[0].trim();
        if (firstLine && firstLine.length > 2) {
          return firstLine;
        }
      }
      
      return 'Unknown Business';
    } catch (error) {
      console.error('Error extracting business name:', error);
      return 'Unknown Business';
    }
  }

  // Export reviews to CSV
  exportReviewsToCSV(reviewsData, businessName = null) {
    try {
      console.log(`📊 GOOGLE SEARCH SERVICES: Exporting ${reviewsData.length} reviews to CSV...`);
      
      if (!reviewsData || reviewsData.length === 0) {
        console.error('❌ GOOGLE SEARCH SERVICES: No review data to export');
        return;
      }
      
      // Create CSV content
      const csvContent = this.convertReviewsToCSV(reviewsData);
      
      // Generate filename with business name if available
      let filename;
      if (businessName) {
        // Sanitize business name for filename (same pattern as other extractors)
        const sanitizedBusinessName = businessName
          .replace(/[^a-zA-Z0-9\s-]/g, '')  // Remove special characters
          .replace(/\s+/g, '-')             // Replace spaces with hyphens
          .toLowerCase()                    // Convert to lowercase
          .substring(0, 50);                // Limit length
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
        filename = `${sanitizedBusinessName}-reviews-${timestamp}.csv`;
      } else {
        // Fallback to generic filename for multiple businesses
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        filename = `google-search-reviews-${timestamp}.csv`;
      }
      
      // Download the CSV
      this.downloadCSV(csvContent, filename);
      
      console.log(`✅ GOOGLE SEARCH SERVICES: Successfully exported reviews to ${filename}`);
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error exporting reviews:', error);
    }
  }

  // Convert reviews data to CSV format
  convertReviewsToCSV(reviews) {
    if (!reviews || reviews.length === 0) {
      return '';
    }

    // CSV headers
    const headers = [
      'Business Name',
      'Reviewer Name',
      'Rating',
      'Review Text',
      'Review Date',
      'Review URL',
      'Helpful Count',
      'Response Text',
      'Response Date',
      'Extraction Date',
      'Index'
    ];

    // Convert reviews to CSV rows
    const csvRows = [headers.join(',')];
    
    reviews.forEach((review, index) => {
      const row = [
        this.escapeCsvValue(review.businessName || ''),
        this.escapeCsvValue(review.reviewerName || ''),
        this.escapeCsvValue(review.rating || ''),
        this.escapeCsvValue(review.reviewText || ''),
        this.escapeCsvValue(review.reviewDate || ''),
        this.escapeCsvValue(review.reviewUrl || ''),
        this.escapeCsvValue(review.helpfulCount || ''),
        this.escapeCsvValue(review.responseText || ''),
        this.escapeCsvValue(review.responseDate || ''),
        this.escapeCsvValue(review.extractionDate || new Date().toISOString()),
        this.escapeCsvValue((index + 1).toString())
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  // Improved business card clicking with verification
  async clickBusinessCardWithVerification(businessCard, businessName) {
    try {
      console.log(`🎯 GOOGLE SEARCH SERVICES: Attempting to click business card for "${businessName}"`);
      
      // Method 1: Click on the business name element (most reliable)
      const nameElement = this.findBusinessNameElement(businessCard);
      if (nameElement) {
        console.log('🎯 GOOGLE SEARCH SERVICES: Found business name element, clicking it...');
        nameElement.click();
        await this.delay(1000);
        return;
      }
      
      // Method 2: Click on the main business card container
      console.log('🎯 GOOGLE SEARCH SERVICES: Clicking main business card container...');
      businessCard.click();
      await this.delay(1000);
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error in clickBusinessCardWithVerification:', error);
      throw error;
    }
  }

  // Find the business name element within the business card
  findBusinessNameElement(businessCard) {
    const nameSelectors = [
      '.OSrXXb.RfnDt.o5Th0.E3DyYd.VbTUod',
      '.OSrXXb', // Generic business name selector
      '.BNeawe.vvjwJb.AP7Wnd',
      '.sVXRqc',
      'h3', // Common heading selector for Google Search
      '.LC20lb', // Google Search title selector
      '.DKV0Md', // Additional Google Search selector
      '.cXedhc a', // Link within business card
      'a[data-cid]' // Business link with CID
    ];
    
    for (const selector of nameSelectors) {
      const element = businessCard.querySelector(selector);
      if (element) {
        console.log(`🎯 GOOGLE SEARCH SERVICES: Found name element with selector: ${selector}`);
        return element;
      }
    }
    
    console.log('⚠️ GOOGLE SEARCH SERVICES: No specific name element found');
    return null;
  }

  // Verify that the business popup opened by looking for profile elements
  async verifyBusinessPopupOpened(businessName) {
    try {
      console.log(`🔍 GOOGLE SEARCH SERVICES: Verifying popup opened for "${businessName}"`);
      
      // Look for various indicators that a business popup/panel opened
      const popupIndicators = [
        '.rh1NH.xHRvib', // Categories container
        '.VjDMZd', // Business profile container
        '.commercial-unit-desktop-top', // Commercial unit
        'a.i2MEmb.laVYkc', // View all buttons
        '.f7raNc', // Business panel
        '.ecceSd', // Business details panel
        '[data-attrid="title"]', // Business title in panel
        '.kp-header', // Knowledge panel header
        '.Vn6oBd' // Business info panel
      ];
      
      for (const selector of popupIndicators) {
        const element = document.querySelector(selector);
        if (element) {
          console.log(`✅ GOOGLE SEARCH SERVICES: Found popup indicator: ${selector}`);
          return true;
        }
      }
      
      // Alternative check: see if the page content changed significantly
      const possibleBusinessContent = document.querySelectorAll('[data-cid], .g, .VkpGBb');
      if (possibleBusinessContent.length > 0) {
        console.log(`🔍 GOOGLE SEARCH SERVICES: Found ${possibleBusinessContent.length} potential business elements`);
        
        // Check if any of these contain the business name
        for (const element of possibleBusinessContent) {
          if (element.textContent.toLowerCase().includes(businessName.toLowerCase().substring(0, 10))) {
            console.log(`✅ GOOGLE SEARCH SERVICES: Found business content containing "${businessName}"`);
            return true;
          }
        }
      }
      
      console.log('❌ GOOGLE SEARCH SERVICES: No popup indicators found');
      return false;
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error verifying popup:', error);
      return false;
    }
  }

  // Try alternative click methods if initial click failed
  async tryAlternativeClickMethod(businessCard, businessName) {
    try {
      console.log(`🔄 GOOGLE SEARCH SERVICES: Trying alternative click methods for "${businessName}"`);
      
      // Method 1: Double click
      console.log('🖱️ GOOGLE SEARCH SERVICES: Trying double click...');
      businessCard.dispatchEvent(new MouseEvent('dblclick', {
        view: window,
        bubbles: true,
        cancelable: true
      }));
      await this.delay(1000);
      
      // Method 2: Mouse down/up sequence
      console.log('🖱️ GOOGLE SEARCH SERVICES: Trying mouse down/up sequence...');
      businessCard.dispatchEvent(new MouseEvent('mousedown', {
        view: window,
        bubbles: true,
        cancelable: true
      }));
      await this.delay(100);
      businessCard.dispatchEvent(new MouseEvent('mouseup', {
        view: window,
        bubbles: true,
        cancelable: true
      }));
      await this.delay(1000);
      
      // Method 3: Focus and Enter key
      console.log('⌨️ GOOGLE SEARCH SERVICES: Trying focus and Enter key...');
      if (businessCard.focus) {
        businessCard.focus();
        await this.delay(100);
        businessCard.dispatchEvent(new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          bubbles: true
        }));
        await this.delay(1000);
      }
      
      // Method 4: Try clicking any links within the business card
      const links = businessCard.querySelectorAll('a');
      if (links.length > 0) {
        console.log(`🔗 GOOGLE SEARCH SERVICES: Found ${links.length} links, trying first link...`);
        const firstLink = links[0];
        // Avoid clicking social media or phone links
        if (!firstLink.href.includes('facebook') && 
            !firstLink.href.includes('instagram') && 
            !firstLink.href.includes('twitter') && 
            !firstLink.href.includes('tel:') && 
            !firstLink.href.includes('mailto:')) {
          firstLink.click();
          await this.delay(1000);
        }
      }
      
      console.log('🔄 GOOGLE SEARCH SERVICES: Alternative click methods completed');
      
    } catch (error) {
      console.error('💥 GOOGLE SEARCH SERVICES: Error in alternative click methods:', error);
    }
  }
}

// Create global instance
window.googleSearchServicesExtractor = new GoogleSearchServicesExtractor();

// Export functions for use by other modules
window.detectGoogleSearchMapsListings = detectGoogleSearchMapsListings;
window.extractGoogleSearchBusinessData = extractGoogleSearchBusinessData;

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  try {
    console.log('GMB Google Search: Message listener called with:', request);
    
    // Add a check to ensure this is our extension making the request
    if (!request.action) {
      console.log('GMB Google Search: No action specified, ignoring');
      return false; // Not our request, ignore
    }
    
    console.log('GMB Google Search: Received message:', request.action);
    
    if (request.action === "detectGoogleSearchMapsListings") {
      console.log('GMB Google Search: Processing detectGoogleSearchMapsListings');
      const isGoogleSearchMaps = detectGoogleSearchMapsListings();
      console.log('GMB Google Search: Detection result:', isGoogleSearchMaps);
      sendResponse({ isGoogleSearchMaps: isGoogleSearchMaps });
    } else if (request.action === "hasCoordinatesInHash") {
      console.log('GMB Google Search: Processing hasCoordinatesInHash');
      const hasCoordinates = hasCoordinatesInHash();
      console.log('GMB Google Search: Hash coordinates result:', hasCoordinates);
      sendResponse({ hasCoordinates: hasCoordinates });
    } else if (request.action === "startGoogleSearchReviewAnalysis") {
      console.log('GMB Google Search: Processing startGoogleSearchReviewAnalysis');
      // Start the review analysis
      if (window.googleSearchReviewAnalyzer) {
        console.log('GMB Google Search: Starting review analyzer...');
        window.googleSearchReviewAnalyzer.startAnalysis();
        sendResponse({ success: true });
      } else {
        console.error('GMB Google Search: Review analyzer not available');
        sendResponse({ success: false, error: 'Review analyzer not available' });
      }
    } else if (request.action === "startGoogleSearchServicesExtraction") {
      console.log('GMB Google Search: Processing startGoogleSearchServicesExtraction');
      // Start the services extraction
      if (window.googleSearchServicesExtractor) {
        console.log('GMB Google Search: Starting services extractor...');
        window.googleSearchServicesExtractor.startServicesExtraction();
        sendResponse({ success: true });
      } else {
        console.error('GMB Google Search: Services extractor not available');
        sendResponse({ success: false, error: 'Services extractor not available' });
      }
    } else {
      console.log('GMB Google Search: Unknown action:', request.action);
    }
    
    return true; // Keep message channel open for async response
    
  } catch (error) {
    console.error('GMB Google Search: Error in message listener:', error);
    sendResponse({ success: false, error: error.message });
    return true;
  }
});

} // End namespace check

