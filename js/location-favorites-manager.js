// Location Favorites Manager for Background Script
// Manages favorites data and context menu integration

class LocationFavoritesManager {
    constructor() {
        this.storageKey = 'locationChangerFavorites';
        this.favorites = [];
        this.contextMenuIds = new Set();
        this.init();
    }

    async init() {
        await this.loadFavorites();
        this.setupStorageListener();
        // Note: buildContextMenus() is called by setupContextMenu in background.js
    }

    async loadFavorites() {
        try {
            const result = await chrome.storage.local.get([this.storageKey]);
            let loadedFavorites = result[this.storageKey];
            
            // If no favorites in chrome.storage.local, try to migrate from localStorage
            if (!loadedFavorites || !Array.isArray(loadedFavorites) || loadedFavorites.length === 0) {
                console.log('LocationFavoritesManager: No favorites in chrome.storage.local, checking localStorage for migration...');
                loadedFavorites = await this.migrateFromLocalStorage();
            }
            
            // Validate the loaded data
            if (Array.isArray(loadedFavorites) && loadedFavorites.length > 0) {
                // Filter out any corrupted favorites
                this.favorites = loadedFavorites.filter(fav => {
                    return fav && 
                           typeof fav.name === 'string' && 
                           fav.data && 
                           typeof fav.data === 'object';
                });
                console.log('LocationFavoritesManager: Loaded', this.favorites.length, 'valid favorites');
            } else {
                this.favorites = [];
                console.log('LocationFavoritesManager: No favorites found, starting with empty array');
            }
        } catch (error) {
            console.error('LocationFavoritesManager: Error loading favorites:', error);
            this.favorites = [];
        }
    }

    async migrateFromLocalStorage() {
        try {
            // Inject a script to read from localStorage since service workers can't access it directly
            const tabs = await chrome.tabs.query({});
            if (tabs.length === 0) {
                console.log('LocationFavoritesManager: No tabs available for localStorage migration');
                return [];
            }

            // Try to read from localStorage via content script injection
            const result = await chrome.scripting.executeScript({
                target: { tabId: tabs[0].id },
                func: function(storageKey) {
                    try {
                        const stored = localStorage.getItem(storageKey);
                        return stored ? JSON.parse(stored) : [];
                    } catch (error) {
                        console.error('Migration script error:', error);
                        return [];
                    }
                },
                args: [this.storageKey]
            });

            if (result && result[0] && result[0].result && Array.isArray(result[0].result)) {
                const migratedFavorites = result[0].result;
                if (migratedFavorites.length > 0) {
                    console.log('LocationFavoritesManager: Migrating', migratedFavorites.length, 'favorites from localStorage');
                    // Save to chrome.storage.local
                    await chrome.storage.local.set({ [this.storageKey]: migratedFavorites });
                    return migratedFavorites;
                }
            }
        } catch (error) {
            console.error('LocationFavoritesManager: Error during localStorage migration:', error);
        }
        
        return [];
    }

    setupStorageListener() {
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'local' && changes[this.storageKey]) {
                console.log('LocationFavoritesManager: Favorites changed, rebuilding menus');
                this.favorites = changes[this.storageKey].newValue || [];
                this.buildContextMenus();
            }
        });
    }

    buildContextMenus() {
        // Clear existing favorite menu items
        this.clearFavoriteMenus();

        if (this.favorites.length === 0) {
            console.log('LocationFavoritesManager: No favorites to display');
            return;
        }

        // Sort favorites by recent usage (lastUsed), then savedAt, then by name
        const sortedFavorites = [...this.favorites].sort((a, b) => {
            // First sort by lastUsed (most recent first)
            const lastUsedA = new Date(a.lastUsed || 0);
            const lastUsedB = new Date(b.lastUsed || 0);
            const lastUsedDiff = lastUsedB - lastUsedA;
            
            // If significant difference in lastUsed, use that
            if (Math.abs(lastUsedDiff) > 1000) {
                return lastUsedDiff;
            }
            
            // Otherwise, sort by savedAt (creation time)
            const dateA = new Date(a.savedAt || 0);
            const dateB = new Date(b.savedAt || 0);
            const dateDiff = dateB - dateA;
            
            // If dates are the same (or both missing), sort by name
            if (Math.abs(dateDiff) < 1000) { // Within 1 second, consider same
                return a.name.localeCompare(b.name);
            }
            return dateDiff;
        });
        
        // Limit to 8 favorites to prevent menu overflow and leave room for separator and "More"
        const displayFavorites = sortedFavorites.slice(0, 8);

        // Add separator before favorites if not already added
        const separatorId = 'location-favorites-separator';
        if (!this.contextMenuIds.has(separatorId)) {
            chrome.contextMenus.create({
                "type": "separator",
                "id": separatorId,
                "parentId": "location-changer",
                "documentUrlPatterns": universalUrlPatterns
            }, () => {
                if (chrome.runtime.lastError) {
                    console.error('Error creating favorites separator:', chrome.runtime.lastError);
                } else {
                    this.contextMenuIds.add(separatorId);
                }
            });
        }

        // Add each favorite as a menu item
        displayFavorites.forEach((favorite, index) => {
            const menuId = `location-favorite-${index}`;
            chrome.contextMenus.create({
                "title": `📍 ${favorite.name}`,
                "id": menuId,
                "parentId": "location-changer",
                "documentUrlPatterns": universalUrlPatterns
            }, () => {
                if (chrome.runtime.lastError) {
                    console.error(`Error creating favorite menu "${favorite.name}":`, chrome.runtime.lastError);
                } else {
                    this.contextMenuIds.add(menuId);
                }
            });
        });

        // Add "More..." option if there are more than 8 favorites
        if (sortedFavorites.length > 8) {
            const moreId = 'location-favorites-more';
            chrome.contextMenus.create({
                "title": `📋 More... (${sortedFavorites.length - 8} more)`,
                "id": moreId,
                "parentId": "location-changer", 
                "documentUrlPatterns": universalUrlPatterns
            }, () => {
                if (chrome.runtime.lastError) {
                    console.error('Error creating more favorites menu:', chrome.runtime.lastError);
                } else {
                    this.contextMenuIds.add(moreId);
                }
            });
        }

        console.log('LocationFavoritesManager: Built context menus for', displayFavorites.length, 'favorites');
    }

    clearFavoriteMenus() {
        // Remove all favorite-related context menu items
        this.contextMenuIds.forEach(id => {
            chrome.contextMenus.remove(id, () => {
                if (chrome.runtime.lastError) {
                    // Ignore errors for items that don't exist
                }
            });
        });
        this.contextMenuIds.clear();
    }

    handleFavoriteClick(menuItemId, tab) {
        try {
            // Extract index from menu item ID
            const match = menuItemId.match(/location-favorite-(\d+)/);
            if (!match) {
                if (menuItemId === 'location-favorites-more') {
                    this.handleMoreFavoritesClick(tab);
                }
                return;
            }

            const index = parseInt(match[1]);
            if (isNaN(index) || index < 0) {
                console.error('LocationFavoritesManager: Invalid favorite index:', index);
                return;
            }

            // Use the same sorting algorithm as buildContextMenus
            const sortedFavorites = [...this.favorites].sort((a, b) => {
                const lastUsedA = new Date(a.lastUsed || 0);
                const lastUsedB = new Date(b.lastUsed || 0);
                const lastUsedDiff = lastUsedB - lastUsedA;
                
                if (Math.abs(lastUsedDiff) > 1000) {
                    return lastUsedDiff;
                }
                
                const dateA = new Date(a.savedAt || 0);
                const dateB = new Date(b.savedAt || 0);
                const dateDiff = dateB - dateA;
                
                if (Math.abs(dateDiff) < 1000) {
                    return a.name.localeCompare(b.name);
                }
                return dateDiff;
            });

            const favorite = sortedFavorites[index];

            if (!favorite) {
                console.error('LocationFavoritesManager: Favorite not found for index', index);
                chrome.notifications.create({
                    type: 'basic',
                    iconUrl: 'images/icon48.png',
                    title: 'Location Error',
                    message: 'Selected location is no longer available.'
                });
                return;
            }

            console.log('LocationFavoritesManager: Setting location to', favorite.name);
            this.setLocationFromFavorite(favorite, tab);
            
        } catch (error) {
            console.error('LocationFavoritesManager: Error handling favorite click:', error);
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'images/icon48.png',
                title: 'Location Error', 
                message: 'Failed to set location. Please try again.'
            });
        }
    }

    async setLocationFromFavorite(favorite, tab) {
        try {
            // Validate inputs
            if (!favorite || !favorite.name || !favorite.data) {
                throw new Error('Invalid favorite data');
            }
            
            if (!tab || !tab.id) {
                throw new Error('Invalid tab information');
            }

            // Update usage timestamp for better sorting
            await this.updateFavoriteUsage(favorite.name);
            
            // First, clean up any interfering features
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: function() {
                    try {
                        // Force clear any interference
                        window.isKeyPressed = false;
                        window.isDragging = false;
                        
                        // Remove context menu prevention listeners
                        if (typeof preventContextMenu === 'function') {
                            window.removeEventListener("contextmenu", preventContextMenu, true);
                            document.removeEventListener("contextmenu", preventContextMenu, true);
                        }
                        if (window.preventContextMenu) {
                            window.removeEventListener("contextmenu", window.preventContextMenu, true);
                            document.removeEventListener("contextmenu", window.preventContextMenu, true);
                        }
                        return { success: true };
                    } catch (error) {
                        return { error: error.message };
                    }
                }
            });

            // Inject the location setter script
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['settings/quick-actions/location-quick-set.js']
            });

            // Execute the location setting with the favorite data
            const result = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: function(favoriteData) {
                    try {
                        if (typeof LocationQuickSet !== 'undefined') {
                            return LocationQuickSet.setLocation(favoriteData);
                        } else {
                            return { success: false, error: 'LocationQuickSet not found' };
                        }
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                },
                args: [favorite]
            });

            // Check the result
            if (result && result[0] && result[0].result) {
                const executionResult = result[0].result;
                if (!executionResult.success) {
                    throw new Error(executionResult.error || 'Location setting failed');
                }
            }

            console.log('LocationFavoritesManager: Successfully set location to', favorite.name);

        } catch (error) {
            console.error('LocationFavoritesManager: Error setting location:', error);
            
            // Show user-friendly error notification
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'images/icon48.png',
                title: 'Location Error',
                message: `Failed to set location to "${favorite.name}". ${error.message || 'Please try again.'}`
            });
        }
    }

    async updateFavoriteUsage(favoriteName) {
        try {
            // Find and update the favorite's usage timestamp
            const favoriteIndex = this.favorites.findIndex(fav => fav.name === favoriteName);
            if (favoriteIndex >= 0) {
                this.favorites[favoriteIndex].lastUsed = new Date().toISOString();
                
                // Save back to storage
                await chrome.storage.local.set({ [this.storageKey]: this.favorites });
                console.log('LocationFavoritesManager: Updated usage for', favoriteName);
            }
        } catch (error) {
            console.error('LocationFavoritesManager: Error updating favorite usage:', error);
        }
    }

    handleMoreFavoritesClick(tab) {
        // Show helpful notification with additional context
        const totalFavorites = this.favorites.length;
        const hiddenCount = totalFavorites - 8;
        
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'images/icon48.png',
            title: 'Location Favorites',
            message: `You have ${hiddenCount} more saved locations. Click the extension icon to access all ${totalFavorites} favorites.`
        });
    }

    // Public method to get favorites for other parts of the extension
    getFavorites() {
        return [...this.favorites];
    }

    // Check if a specific favorite exists
    hasFavorite(name) {
        return this.favorites.some(fav => fav.name === name);
    }
}

// Initialize the favorites manager
let locationFavoritesManager;

// Initialize when background script loads
if (typeof chrome !== 'undefined' && chrome.runtime) {
    locationFavoritesManager = new LocationFavoritesManager();
}

// Export for use in other parts of background script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LocationFavoritesManager };
}