// Google Search Multiple Review Scraper Module
// This module handles the scrolling, "read more" expansion and extraction for Google Search business listings
// Modeled after the existing multiple mode scrapers (prolist and single review scrapers)

class GoogleSearchMultipleReviewScraper {
    constructor() {
        this.reviewData = [];
        this.isRunning = false;
        this.rangeUtility = new ReviewRangeUtility();
        this.totalReviewCount = 0;
        this.loadedReviewCount = 0;
        this.currentBusinessIndex = 0;
        this.totalBusinesses = 0;
        this.businessName = 'Unknown Business';
        this.version = "1.0-GoogleSearchMultipleReviews";
        this.lastScrollPosition = -1;
        this.scrollPositionStableCount = 0;
        this.dockedInterface = null;
        
        console.log(`🔍 GOOGLE SEARCH MULTIPLE REVIEWS: Initializing version ${this.version} with range utility and scrolling`);
    }

    // Set the maximum number of reviews to extract
    setMaxReviews(maxReviews) {
        console.log(`🔢 GOOGLE SEARCH MULTIPLE REVIEWS: Limit set to ${maxReviews} reviews`);
        this.rangeUtility.setMaxReviews(maxReviews);
        
        // Reset review data for each new business extraction
        this.reviewData = [];
        console.log('🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Reset review data for new business');
    }

    // Execute the review extraction with proper sequence: click business card, navigate to reviews, scroll, expand, extract
    async executeExtraction(maxReviews = null) {
        console.log('🔍 GOOGLE SEARCH MULTIPLE REVIEWS: Starting review extraction...');
        
        try {
            // Initialize range utility with the provided limit
            this.rangeUtility.setMaxReviews(maxReviews);
            console.log(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: Range settings - ${this.rangeUtility.getStatusMessage()}`);

            // Step 1: Find and click on a business card to open the business popup
            console.log('📊 GOOGLE SEARCH MULTIPLE REVIEWS: Step 1 - Finding and clicking business card...');
            const businessCard = await this.findAndClickBusinessCard();
            
            if (!businessCard.success) {
                throw new Error(businessCard.error);
            }

            // Step 2: Wait for business popup to load
            console.log('📊 GOOGLE SEARCH MULTIPLE REVIEWS: Step 2 - Waiting for business popup to load...');
            await this.wait(3000); // Wait for business popup to load

            // Step 3: Navigate to reviews section within the business popup
            console.log('📊 GOOGLE SEARCH MULTIPLE REVIEWS: Step 3 - Navigating to reviews section...');
            await this.navigateToReviews();

            // Step 4: Count current reviews and check if we need to scroll
            console.log('📊 GOOGLE SEARCH MULTIPLE REVIEWS: Step 4 - Checking current review count...');
            await this.wait(1000);
            const initialCount = this.countCurrentVisibleReviews();
            console.log(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: Initial visible reviews: ${initialCount}`);

            // Step 5: Load reviews through scrolling FIRST
            console.log('🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Step 5 - Loading reviews through automated scrolling...');
            await this.loadAllReviewsThroughScrolling();

            // Step 6: Expand all "More" buttons
            console.log('🔽 GOOGLE SEARCH MULTIPLE REVIEWS: Step 6 - Expanding all "More" buttons...');
            await this.runReadMoreScript();

            // Wait for expansions to complete
            await this.wait(3000);

            // Step 7: Extract all review data
            console.log('📊 GOOGLE SEARCH MULTIPLE REVIEWS: Step 7 - Extracting review data...');
            await this.extractAllReviews();

            // Apply range limit to results
            let finalReviews = this.reviewData;
            if (this.rangeUtility.hasLimit()) {
                finalReviews = this.rangeUtility.truncateResults(this.reviewData);
                console.log(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: Applied range limit - extracted ${this.reviewData.length}, returning ${finalReviews.length}`);
            }

            console.log(`🎉 GOOGLE SEARCH MULTIPLE REVIEWS: Extraction completed! Found ${finalReviews.length} reviews (${this.rangeUtility.hasLimit() ? 'with limit' : 'no limit'})`);
            
            return {
                success: true,
                reviewCount: finalReviews.length,
                reviews: finalReviews,
                businessName: this.businessName,
                totalExpected: this.totalReviewCount,
                rangeApplied: this.rangeUtility.hasLimit(),
                maxReviews: this.rangeUtility.getMaxReviews(),
                totalAvailable: this.reviewData.length
            };

        } catch (error) {
            console.error('❌ GOOGLE SEARCH MULTIPLE REVIEWS: Error during extraction:', error);
            
            // Apply range limit to any data we did collect
            let finalReviews = this.reviewData;
            if (this.rangeUtility.hasLimit()) {
                finalReviews = this.rangeUtility.truncateResults(this.reviewData);
            }
            
            return {
                error: error.message,
                reviewCount: finalReviews.length,
                reviews: finalReviews,
                businessName: this.businessName,
                totalExpected: this.totalReviewCount,
                rangeApplied: this.rangeUtility.hasLimit(),
                maxReviews: this.rangeUtility.getMaxReviews(),
                totalAvailable: this.reviewData.length
            };
        }
    }

    // Find and click on a business card to open the business popup (like services extractor does)
    async findAndClickBusinessCard() {
        try {
            console.log('🔍 GOOGLE SEARCH MULTIPLE REVIEWS: Searching for business cards...');
            
            // Find business cards with the same selectors used by services extractor
            // Avoid Google header elements (gb_ classes) and be more specific
            const businessCardSelectors = [
                '.vwVdIc.wzN8Ac.rllt__link.a-no-hover-decoration', // Primary business card selector (services extractor uses this)
                '.rllt__link:not([class*="gb_"])', // Alternative business card selector, exclude Google header
                '.commercial-unit-desktop-top a:not([class*="gb_"])', // Sponsored listings, exclude Google header
                '.uEierd a[data-cid]:not([class*="gb_"])', // Business cards with CID, exclude Google header
                'a[href*="/maps/place/"]:not([class*="gb_"])', // Maps place links, exclude Google header
                'a[data-ved]:not([class*="gb_"]):not(.gb_B):not(.gb_Za)' // Links with data-ved but not Google header
            ];
            
            let businessCards = [];
            let selectorUsed = '';
            
            // Try each selector to find business cards
            for (const selector of businessCardSelectors) {
                businessCards = Array.from(document.querySelectorAll(selector));
                
                // Filter out Google header elements and invalid cards
                businessCards = businessCards.filter(card => {
                    // Skip if it's in Google header (gb_ classes anywhere in parent chain)
                    if (card.closest('[class*="gb_"]') || card.className.includes('gb_')) {
                        return false;
                    }
                    
                    // Skip if it's the profile picture or Google apps
                    if (card.getAttribute('aria-label')?.includes('Google') || 
                        card.getAttribute('aria-label')?.includes('Account') ||
                        card.href?.includes('accounts.google.com') ||
                        card.href?.includes('about/products')) {
                        return false;
                    }
                    
                    // Must be visible and have valid href
                    return card.offsetParent !== null && card.href && card.href.length > 10;
                });
                
                if (businessCards.length > 0) {
                    selectorUsed = selector;
                    console.log(`🔍 GOOGLE SEARCH MULTIPLE REVIEWS: Found ${businessCards.length} business cards using selector: ${selector}`);
                    break;
                }
            }
            
            if (businessCards.length === 0) {
                console.error('❌ GOOGLE SEARCH MULTIPLE REVIEWS: No business cards found');
                return { success: false, error: 'No business cards found on page' };
            }
            
            // Use the first business card (or could be made configurable)
            const selectedCard = businessCards[0];
            const businessName = this.extractBusinessNameFromCard(selectedCard);
            
            console.log(`📋 GOOGLE SEARCH MULTIPLE REVIEWS: Selected business: ${businessName}`);
            console.log(`🔗 GOOGLE SEARCH MULTIPLE REVIEWS: Business card href: ${selectedCard.href}`);
            console.log(`🎯 GOOGLE SEARCH MULTIPLE REVIEWS: Business card classes: ${selectedCard.className}`);
            
            // Click the business card to open popup
            console.log('🖱️ GOOGLE SEARCH MULTIPLE REVIEWS: Clicking business card to open popup...');
            
            // Add a small delay and ensure we're clicking the right element
            await this.wait(500);
            
            // Scroll the card into view first
            selectedCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await this.wait(1000);
            
            // Click the card
            selectedCard.click();
            
            return { success: true, businessName: businessName, card: selectedCard };
            
        } catch (error) {
            console.error('💥 GOOGLE SEARCH MULTIPLE REVIEWS: Error finding/clicking business card:', error);
            return { success: false, error: error.message };
        }
    }

    // Extract business name from business card (before clicking)
    extractBusinessNameFromCard(businessCard) {
        try {
            // Try multiple selectors for business name (same as services extractor)
            const nameSelectors = [
                '.OSrXXb',
                '.dbg0pd .OSrXXb', 
                'h3',
                '.LC20lb',
                '.DKV0Md',
                '.business-name',
                '.name'
            ];

            for (const selector of nameSelectors) {
                const nameElement = businessCard.querySelector(selector);
                if (nameElement && nameElement.textContent.trim()) {
                    return nameElement.textContent.trim();
                }
            }

            // If no name found in the card, try parent containers
            const parentContainer = businessCard.closest('.commercial-unit-desktop-top') || 
                                  businessCard.closest('.uEierd') ||
                                  businessCard.closest('.VkpGBb');
            
            if (parentContainer) {
                for (const selector of nameSelectors) {
                    const nameElement = parentContainer.querySelector(selector);
                    if (nameElement && nameElement.textContent.trim()) {
                        return nameElement.textContent.trim();
                    }
                }
            }

            return 'Unknown Business';

        } catch (error) {
            console.error('💥 GOOGLE SEARCH MULTIPLE REVIEWS: Error extracting business name from card:', error);
            return 'Unknown Business';
        }
    }

    // Navigate to reviews section within the business popup (after clicking business card)
    async navigateToReviews() {
        console.log('📍 GOOGLE SEARCH MULTIPLE REVIEWS: Navigating to reviews within business popup...');
        
        // Wait a moment for popup to fully load
        await this.wait(1000);
        
        // Try to find and click reviews tab within the business popup
        const reviewsTabSelectors = [
            'div.F3Istb.sSWCId', // Google Search Reviews tab div (the actual structure!)
            'button[data-value="Reviews"]', // Standard GMB reviews tab
            'button[aria-label*="Reviews"]', // Aria label containing "Reviews"  
            'button[aria-label*="reviews"]', // Aria label containing "reviews"
            'button.tab-reviews', // Review tab class
            '[role="tab"][aria-selected]', // Tab role elements
            'a[href*="reviews"]', // Links containing reviews
            '.review-tab', // Generic review tab
            'button:contains("Reviews")' // Buttons containing "Reviews" text
        ];
        
        let reviewsTab = null;
        let selectorUsed = '';
        
        for (const selector of reviewsTabSelectors) {
            try {
                if (selector === 'div.F3Istb.sSWCId') {
                    // Special handling for Google Search Reviews div structure
                    const reviewsDivs = document.querySelectorAll(selector);
                    for (const div of reviewsDivs) {
                        const span = div.querySelector('span');
                        if (span && span.textContent.trim().toLowerCase() === 'reviews') {
                            reviewsTab = div;
                            selectorUsed = 'div.F3Istb.sSWCId (with Reviews text)';
                            console.log('📍 GOOGLE SEARCH MULTIPLE REVIEWS: Found Reviews div with correct text');
                            break;
                        }
                    }
                } else if (selector.includes(':contains')) {
                    // Handle pseudo-selector manually for buttons containing "Reviews"
                    const buttons = document.querySelectorAll('button');
                    for (const button of buttons) {
                        if (button.textContent.toLowerCase().includes('reviews')) {
                            reviewsTab = button;
                            selectorUsed = 'button:contains("Reviews")';
                            break;
                        }
                    }
                } else {
                    reviewsTab = document.querySelector(selector);
                }
                
                if (reviewsTab) {
                    selectorUsed = selectorUsed || selector;
                    break;
                }
            } catch (error) {
                console.log(`📍 GOOGLE SEARCH MULTIPLE REVIEWS: Selector failed: ${selector}`);
            }
        }
        
        if (reviewsTab) {
            console.log(`📍 GOOGLE SEARCH MULTIPLE REVIEWS: Found Reviews tab using selector: ${selectorUsed}`);
            
            // Check if already selected
            const isSelected = reviewsTab.getAttribute('aria-selected') === 'true' ||
                              reviewsTab.classList.contains('selected') ||
                              reviewsTab.classList.contains('active');
                              
            if (isSelected) {
                console.log('📍 GOOGLE SEARCH MULTIPLE REVIEWS: Reviews tab already selected');
            } else {
                console.log('📍 GOOGLE SEARCH MULTIPLE REVIEWS: Clicking Reviews tab...');
                
                // Scroll tab into view and click
                reviewsTab.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await this.wait(500);
                reviewsTab.click();
                
                // Reviews tab clicked - ready to start scrolling immediately
                console.log('📍 GOOGLE SEARCH MULTIPLE REVIEWS: Reviews tab clicked, ready to scroll immediately');
                console.log('📍 GOOGLE SEARCH MULTIPLE REVIEWS: Successfully navigated to Reviews tab');
                return true; // IMPORTANT: Return true to indicate success
            }
        } else {
            console.log('📍 GOOGLE SEARCH MULTIPLE REVIEWS: Reviews tab not found in business popup. Checking current page state...');
            
            // Check if we're already viewing reviews by looking for review containers
            const reviewContainers = document.querySelectorAll('div.jftiEf, div.MyEned, [data-review-id], .gws-localreviews__google-review');
            if (reviewContainers.length > 0) {
                console.log(`📍 GOOGLE SEARCH MULTIPLE REVIEWS: Found ${reviewContainers.length} review containers - assuming already on reviews`);
            } else {
                console.warn('📍 GOOGLE SEARCH MULTIPLE REVIEWS: Could not find Reviews tab in business popup. The business may not have reviews or the popup structure is different.');
            }
        }
    }

    // Load all reviews through scrolling (EXACT same approach as single-review-scraper)
    async loadAllReviewsThroughScrolling() {
        console.log('🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Starting automated review loading...');
        
        // Extract the actual total review count from the page
        let actualTotalReviews = 1000; // Default fallback
        try {
            // Look for the review count in the .z5jxId element
            const reviewCountElement = document.querySelector('.z5jxId');
            if (reviewCountElement) {
                const reviewText = reviewCountElement.textContent.trim();
                console.log(`🔍 GOOGLE SEARCH MULTIPLE REVIEWS: Found review count element: "${reviewText}"`);
                
                // Extract number from text like "943 reviews"
                const match = reviewText.match(/(\d+(?:,\d+)*)\s+reviews?/i);
                if (match) {
                    actualTotalReviews = parseInt(match[1].replace(/,/g, ''), 10);
                    console.log(`🎯 GOOGLE SEARCH MULTIPLE REVIEWS: Extracted actual review count: ${actualTotalReviews}`);
                } else {
                    console.log(`⚠️ GOOGLE SEARCH MULTIPLE REVIEWS: Could not parse review count from: "${reviewText}"`);
                }
            } else {
                console.log(`⚠️ GOOGLE SEARCH MULTIPLE REVIEWS: .z5jxId element not found, using fallback target`);
            }
        } catch (error) {
            console.log(`⚠️ GOOGLE SEARCH MULTIPLE REVIEWS: Error extracting review count:`, error);
        }
        
        // Store the total for later use
        this.totalReviewCount = actualTotalReviews;
        
        // Calculate the effective target based on range settings and actual count
        const effectiveTarget = this.rangeUtility.getEffectiveTarget(actualTotalReviews);
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Actual total: ${actualTotalReviews}, Effective target: ${effectiveTarget} reviews`);
        
        // Find reviews container - try Google Search specific containers
        let reviewsContainer = document.querySelector('div.m6QErb'); // Standard GMB container
        if (!reviewsContainer) {
            // Try Google Search specific containers
            const alternativeContainers = [
                '.gws-localreviews__google-review',
                '.commercial-unit-desktop-top',
                '.VjDMZd',
                '.rl_full-list',
                document.body
            ];
            
            for (const selector of alternativeContainers) {
                const container = typeof selector === 'string' ? document.querySelector(selector) : selector;
                if (container) {
                    reviewsContainer = container;
                    console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Using alternative container: ${typeof selector === 'string' ? selector : 'document.body'}`);
                    break;
                }
            }
        }
        
        console.log('🔄 GOOGLE SEARCH MULTIPLE REVIEWS: ✅ Using container for scrolling');
        
        // CRITICAL: Activate the reviews container
        console.log('🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Step 1 - Activating reviews container...');
        const startTime = performance.now();
        await this.activateReviewsContainer();
        const activationTime = (performance.now() - startTime) / 1000;
        console.log(`⏱️ GOOGLE SEARCH MULTIPLE REVIEWS: Container activation took ${activationTime.toFixed(2)} seconds`);
        
        // PHASE 1: PURE SCROLLING - NO BUTTON CLICKING
        console.log('🔄 GOOGLE SEARCH MULTIPLE REVIEWS: === PHASE 1: PURE SCROLLING (NO BUTTON CLICKING) ===');
        const phase1Count = await this.performPureScrollingPhase(effectiveTarget);
        
        // Check if we've already reached our target after Phase 1
        const currentCountAfterPhase1 = this.countCurrentVisibleReviews();
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Phase 1 complete - Found ${currentCountAfterPhase1} reviews (target: ${effectiveTarget})`);
        
        if (currentCountAfterPhase1 >= effectiveTarget) {
            console.log(`🎯 GOOGLE SEARCH MULTIPLE REVIEWS: TARGET REACHED! Found ${currentCountAfterPhase1}/${effectiveTarget} reviews - SKIPPING Phase 2 retry scrolling`);
        } else {
            const shortfall = effectiveTarget - currentCountAfterPhase1;
            console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Target not reached (short by ${shortfall} reviews) - proceeding with Phase 2...`);
            
            // PHASE 2: FINAL RETRY SCROLLING (only if target not reached)
            console.log('🔄 GOOGLE SEARCH MULTIPLE REVIEWS: === PHASE 2: FINAL RETRY SCROLLING ===');
            await this.performFinalRetryScrolling();
        }
        
        const finalCount = this.countCurrentVisibleReviews();
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: === Scrolling Phase Complete ===`);
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Final count: ${finalCount} reviews (target: ${effectiveTarget})`);
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Range settings: ${this.rangeUtility.getStatusMessage()}`);
        
        return finalCount;
    }

    // PHASE 1: Pure scrolling without any button clicking
    async performPureScrollingPhase(effectiveTarget) {
        console.log('📜 GOOGLE SEARCH MULTIPLE REVIEWS: Starting pure scrolling phase (no buttons)...');
        
        const maxScrollAttempts = Math.min(60, Math.ceil(effectiveTarget / 20)); // More attempts for higher targets
        const scrollDistance = 20000; // Increased from 8000px to 20000px as requested
        let previousReviewCount = 0;
        let scrollPosition = 0; // Track WHERE WE ARE, not distance from top
        let stableScrollCount = 0;
        let lastScrollHeight = 0;
        
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Will attempt up to ${maxScrollAttempts} pure scrolling attempts (targeting ${effectiveTarget} reviews)`);
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Using incremental scroll distance: ${scrollDistance}px per attempt`);
        
        for (let attempt = 1; attempt <= maxScrollAttempts; attempt++) {
            console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: === Pure Scroll Attempt ${attempt}/${maxScrollAttempts} ===`);
            
            // Get current scroll position and total scroll height
            const currentScrollTop = this.getCurrentScrollPosition();
            const totalScrollHeight = document.documentElement.scrollHeight;
            const windowHeight = window.innerHeight;
            const maxPossibleScroll = totalScrollHeight - windowHeight;
            
            console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Scroll position: ${currentScrollTop}px / ${maxPossibleScroll}px (${((currentScrollTop/maxPossibleScroll)*100).toFixed(1)}% down)`);
            
            const currentReviewCount = this.countCurrentVisibleReviews();
            console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Attempt ${attempt} - Found ${currentReviewCount} visible reviews (target: ${effectiveTarget})`);
            
            // Update progress to show current review count
            try {
                if (window.googleSearchServicesExtractor && window.googleSearchServicesExtractor.updateDockedInterface) {
                    window.googleSearchServicesExtractor.updateDockedInterface(`Loading reviews... found ${currentReviewCount}`, {
                        totalBusinesses: 1,
                        currentBusiness: 1,
                        totalReviews: currentReviewCount
                    });
                }
            } catch (progressError) {
                // Silently fail if progress update doesn't work
            }
            
            // Check if we've reached our target
            if (currentReviewCount >= effectiveTarget) {
                console.log(`🎯 GOOGLE SEARCH MULTIPLE REVIEWS: Target reached! Found ${currentReviewCount}/${effectiveTarget} reviews`);
                break;
            }
            
            // Check if we're near the bottom using scroll position percentage
            const scrollPercentage = (currentScrollTop / maxPossibleScroll) * 100;
            if (scrollPercentage > 95) { // If we're 95% down the page
                console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Near bottom (${scrollPercentage.toFixed(1)}% down), checking for content stability...`);
                
                // Check if scroll height has changed (new content loaded)
                if (totalScrollHeight === lastScrollHeight) {
                    stableScrollCount++;
                    console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Page height stable (${totalScrollHeight}px), stability count: ${stableScrollCount}/3`);
                    
                    if (stableScrollCount >= 3) {
                        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Page height stable for 3 attempts, likely reached absolute bottom - stopping pure scroll`);
                    break;
                }
            } else {
                    stableScrollCount = 0; // Reset if content changed
                    console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Page height changed from ${lastScrollHeight}px to ${totalScrollHeight}px, continuing...`);
                }
                lastScrollHeight = totalScrollHeight;
            }
            
            // Check for progress
            if (currentReviewCount > previousReviewCount) {
                console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Count increased from ${previousReviewCount} to ${currentReviewCount}, continuing...`);
                previousReviewCount = currentReviewCount;
            }
            
            console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: About to scroll (attempt ${attempt})...`);
            const scrollStartTime = performance.now();
            await this.performIncrementalScroll(scrollPosition, scrollDistance);
            const scrollEndTime = performance.now();
            console.log(`⏱️ GOOGLE SEARCH MULTIPLE REVIEWS: Scroll methods took ${((scrollEndTime - scrollStartTime) / 1000).toFixed(2)} seconds`);
            
            // Update our scroll position for next iteration
            scrollPosition += scrollDistance;
            
            console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: All scrolling methods completed, waiting 0.2 seconds for content to load...`);
            await this.wait(200); // Changed from 500ms to 200ms as requested
            console.log(`⏱️ GOOGLE SEARCH MULTIPLE REVIEWS: Wait completed (0.20s), checking for new reviews...`);
            console.log(`⏱️ GOOGLE SEARCH MULTIPLE REVIEWS: Attempt ${attempt} total time: ${((performance.now() - scrollStartTime) / 1000).toFixed(2)} seconds`);
        }
        
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: === PURE SCROLLING PHASE COMPLETE ===`);
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Used ${Math.min(maxScrollAttempts, previousReviewCount > 0 ? maxScrollAttempts : 1)} pure scroll attempts`);
        
        return this.countCurrentVisibleReviews();
    }

    // PHASE 2: Final retry scrolling with more aggressive approach
    async performFinalRetryScrolling() {
        console.log('🔄 GOOGLE SEARCH MULTIPLE REVIEWS: === FINAL RETRY PHASE: 1 retry at bottom with pause ===');
        
        const finalRetryPhaseStartTime = performance.now(); // Track phase start time
        const preRetryCount = this.countCurrentVisibleReviews();
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Pre-retry count: ${preRetryCount} reviews`);
        
        // Single final retry instead of 3
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: === Final Retry 1/1 ===`);
        
        const retryStartTime = performance.now();
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Final retry - Aggressive scroll to absolute bottom...`);
        
        // Super aggressive final scrolling
        await this.performSuperAggressiveScroll();
        
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Final retry - Extended pause (2.5 seconds)...`);
        await this.wait(2500);
        
        const reviewsAfterRetry = this.countCurrentVisibleReviews();
        const retryTime = (performance.now() - retryStartTime) / 1000;
        console.log(`⏱️ GOOGLE SEARCH MULTIPLE REVIEWS: Final retry completed in ${retryTime.toFixed(2)}s - Reviews: ${reviewsAfterRetry}`);
        
        if (reviewsAfterRetry > preRetryCount) {
            const newReviews = reviewsAfterRetry - preRetryCount;
            console.log(`🎉 GOOGLE SEARCH MULTIPLE REVIEWS: Final retry loaded ${newReviews} additional reviews!`);
        } else {
            console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Final retry - No new reviews loaded`);
        }
        
        const finalRetryCount = this.countCurrentVisibleReviews();
        const finalRetryPhaseTime = (performance.now() - finalRetryPhaseStartTime) / 1000; // Calculate total phase time
        console.log(`⏱️ GOOGLE SEARCH MULTIPLE REVIEWS: Final retry phase took: ${finalRetryPhaseTime.toFixed(2)} seconds`);
        console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Range settings: ${this.rangeUtility.getStatusMessage()}`);
    }

    // Perform incremental scrolling from current position
    async performIncrementalScroll(currentPosition, scrollDistance = 20000) {
        console.log('📜 GOOGLE SEARCH MULTIPLE REVIEWS: Using INCREMENTAL DISTANCE-BASED Scrolling...');
        
        try {
            // Calculate target position: current position + incremental distance
            const targetPosition = currentPosition + scrollDistance;
            console.log(`📜 GOOGLE SEARCH MULTIPLE REVIEWS: Scrolling from ${currentPosition}px to ${targetPosition}px (distance: ${scrollDistance}px)`);
            
            // Method 1: Direct window scroll to target position
            window.scrollTo({ 
                top: targetPosition, 
                behavior: 'smooth' 
            });
            await this.wait(800);
            
            // Method 2: Find and scroll past the last visible review
            await this.scrollPastLastVisibleReview();
            
            // Method 3: Additional aggressive distance scrolling
            const distances = [2000, 5000, 8000, scrollDistance];
            for (const distance of distances) {
                const newTarget = currentPosition + distance;
                window.scrollTo({ top: newTarget, behavior: 'smooth' });
                await this.wait(300);
            }
            
            // Method 4: Scroll to document end as fallback
            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
            await this.wait(500);
            
            // Trigger scroll events to ensure lazy loading
            window.dispatchEvent(new Event('scroll'));
            document.dispatchEvent(new Event('scroll'));
            await this.wait(200);
            
            console.log('📜 GOOGLE SEARCH MULTIPLE REVIEWS: ✅ Incremental distance-based scrolling completed');
            
        } catch (error) {
            console.log('📜 GOOGLE SEARCH MULTIPLE REVIEWS: Incremental scrolling failed:', error);
        }
    }

    // Scroll past the last visible review
    async scrollPastLastVisibleReview() {
        try {
            const reviewSelectors = [
                'div.bwb7ce', // Main selector
                '.EfDVh.wDYxhc.NFQFxe .bwb7ce',
                '.OYzgjc .bwb7ce'
            ];
            
            let existingReviews = [];
            for (const selector of reviewSelectors) {
                existingReviews = document.querySelectorAll(selector);
                if (existingReviews.length > 0) {
                    break;
                }
            }
            
            if (existingReviews.length > 0) {
            const lastReview = existingReviews[existingReviews.length - 1];
                console.log(`📜 GOOGLE SEARCH MULTIPLE REVIEWS: Found ${existingReviews.length} reviews, scrolling past the last one...`);
            
                // Scroll to last review first
            lastReview.scrollIntoView({ behavior: 'smooth', block: 'start' });
            await this.wait(500);
            
                // Then scroll additional distance beyond it
                const additionalScroll = 3000;
                const lastReviewRect = lastReview.getBoundingClientRect();
                const targetY = window.pageYOffset + lastReviewRect.bottom + additionalScroll;
                
                window.scrollTo({ top: targetY, behavior: 'smooth' });
                await this.wait(500);
            }
        } catch (error) {
            console.log('📜 GOOGLE SEARCH MULTIPLE REVIEWS: Error scrolling past last review:', error);
        }
    }

    // Super aggressive scrolling for final retries
    async performSuperAggressiveScroll() {
        try {
            const startPosition = this.getCurrentScrollPosition();
            const documentHeight = document.documentElement.scrollHeight;
            const windowHeight = window.innerHeight;
            const maxScrollPosition = documentHeight - windowHeight;
            
            console.log(`📜 GOOGLE SEARCH MULTIPLE REVIEWS: Super aggressive scroll starting from ${startPosition}px`);
            console.log(`📜 GOOGLE SEARCH MULTIPLE REVIEWS: Document height: ${documentHeight}px, Max scroll: ${maxScrollPosition}px`);
            
            // STAGE 1: Multiple extreme scrolling techniques with longer distances
            const extremeDistances = [8000, 15000, 30000, 50000, 100000, 200000];
            
            for (const distance of extremeDistances) {
                // Method 1: Scroll from current position + distance
                const targetY = startPosition + distance;
                window.scrollTo({ top: targetY, behavior: 'smooth' });
                await this.wait(300);
                
                // Method 2: Scroll to document height + additional distance (beyond bottom)
                window.scrollTo({ top: documentHeight + distance, behavior: 'smooth' });
                await this.wait(300);
                
                // Method 3: Scroll to max possible + distance
                window.scrollTo({ top: maxScrollPosition + distance, behavior: 'smooth' });
                await this.wait(300);
                
                // Trigger scroll events after each major scroll to force lazy loading
                window.dispatchEvent(new Event('scroll'));
                document.dispatchEvent(new Event('scroll'));
                await this.wait(100);
            }
            
            // STAGE 2: Try to scroll to the absolute maximum possible
            const maxValues = [999999999, Number.MAX_SAFE_INTEGER, documentHeight * 10];
            for (const maxVal of maxValues) {
                window.scrollTo({ top: maxVal, behavior: 'smooth' });
                await this.wait(400);
                
                // Force multiple scroll events
                for (let i = 0; i < 5; i++) {
            window.dispatchEvent(new Event('scroll'));
                    await this.wait(50);
                }
            }
            
            // STAGE 3: Alternative scrolling methods
            try {
                // Method: Use scrollBy for additional distance
                window.scrollBy({ top: 50000, behavior: 'smooth' });
                await this.wait(300);
                
                // Method: Direct element scrolling if we can find scroll container
                const scrollContainers = ['.m6QErb', '.OYzgjc', '.EfDVh', 'body'];
                for (const containerSelector of scrollContainers) {
                    const container = document.querySelector(containerSelector);
                    if (container && container.scrollHeight > container.clientHeight) {
                        container.scrollTop = container.scrollHeight + 50000;
            await this.wait(200);
                    }
                }
                
                // Method: Trigger resize to potentially load more content
                window.dispatchEvent(new Event('resize'));
                window.dispatchEvent(new Event('orientationchange'));
                await this.wait(300);
                
            } catch (altError) {
                console.log('📜 GOOGLE SEARCH MULTIPLE REVIEWS: Alternative scroll methods failed:', altError);
            }
            
            // STAGE 4: Final scroll position verification
            const finalPosition = this.getCurrentScrollPosition();
            const finalDocumentHeight = document.documentElement.scrollHeight;
            console.log(`📜 GOOGLE SEARCH MULTIPLE REVIEWS: Super aggressive scroll completed`);
            console.log(`📜 GOOGLE SEARCH MULTIPLE REVIEWS: Position: ${startPosition}px → ${finalPosition}px (moved ${finalPosition - startPosition}px)`);
            console.log(`📜 GOOGLE SEARCH MULTIPLE REVIEWS: Document height: ${documentHeight}px → ${finalDocumentHeight}px (grew ${finalDocumentHeight - documentHeight}px)`);
            
        } catch (error) {
            console.log('📜 GOOGLE SEARCH MULTIPLE REVIEWS: Super aggressive scroll failed:', error);
        }
    }

    // Activate reviews container by clicking in a safe area
    async activateReviewsContainer() {
        try {
            console.log('🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Activating reviews container...');
            
            // Find the reviews container
            const reviewsContainer = document.querySelector('div.m6QErb') || 
                                   document.querySelector('.gws-localreviews__google-review') ||
                                   document.querySelector('.commercial-unit-desktop-top') ||
                                   document.body;
            
            if (reviewsContainer && reviewsContainer !== document.body) {
                const rect = reviewsContainer.getBoundingClientRect();
                console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Container bounds: ${rect.left}, ${rect.top}, ${rect.width}, ${rect.height}`);
                
                // Click in a safe area (center-right of the container)
                const safeX = rect.left + (rect.width * 0.8);
                const safeY = rect.top + (rect.height * 0.3);
                
                console.log(`🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Clicking safe area at (${safeX}, ${safeY})`);
                
                const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: safeX,
                    clientY: safeY
                });
                
                reviewsContainer.dispatchEvent(clickEvent);
                await this.wait(500);
                console.log('🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Container activated');
            }
        } catch (error) {
            console.log('🔄 GOOGLE SEARCH MULTIPLE REVIEWS: Container activation failed:', error);
        }
    }

    // Count current visible reviews
    countCurrentVisibleReviews() {
        try {
            const reviewSelectors = [
                'div.bwb7ce', // Individual review containers (the actual review items!)
                '.EfDVh.wDYxhc.NFQFxe .bwb7ce', // Reviews within the main reviews container
                '.OYzgjc .bwb7ce', // Reviews within the alternative container  
                'div.MyEned', // Standard GMB reviews (fallback)
                'div.jftiEf', // Alternative GMB reviews (fallback)
                '[data-review-id]', // Reviews with data attributes (fallback)
                '.review-item', // Generic review items (fallback)
                '.review' // Generic review class (fallback)
            ];
            
            let maxCount = 0;
            for (const selector of reviewSelectors) {
                const reviews = document.querySelectorAll(selector);
                if (reviews.length > maxCount) {
                    maxCount = reviews.length;
                    console.log(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: Found ${reviews.length} reviews using selector: ${selector}`);
                }
            }
            
            return maxCount;
        } catch (error) {
            console.error('💥 GOOGLE SEARCH MULTIPLE REVIEWS: Error counting reviews:', error);
            return 0;
        }
    }

    // Get current scroll position
    getCurrentScrollPosition() {
        return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
    }

    // Run "Read More" script to expand all reviews (adapted from existing scrapers)
    async runReadMoreScript() {
        try {
            console.log('🔽 GOOGLE SEARCH MULTIPLE REVIEWS: Expanding "More" buttons to reveal full review text...');
            
            // Target ONLY the specific review "More" buttons with class "MtCSLb"
            const moreButtons = document.querySelectorAll('a.MtCSLb[role="button"][aria-label*="Read more"][jsaction="KoToPc"]');
            console.log(`🔍 GOOGLE SEARCH MULTIPLE REVIEWS: Found ${moreButtons.length} review "More" buttons with class MtCSLb`);
            
            if (moreButtons.length === 0) {
                console.log('ℹ️ GOOGLE SEARCH MULTIPLE REVIEWS: No review "More" buttons found to expand');
                return;
            }
            
            let expandedCount = 0;
            
            for (let i = 0; i < moreButtons.length; i++) {
                try {
                    const button = moreButtons[i];
                    
                    // Double-check this is a review "More" button by aria-label
                    const ariaLabel = button.getAttribute('aria-label') || '';
                    if (!ariaLabel.toLowerCase().includes('read more') || !ariaLabel.toLowerCase().includes('review')) {
                        console.log(`⚠️ GOOGLE SEARCH MULTIPLE REVIEWS: Skipping button ${i + 1} - not a review "More" button`);
                        continue;
                    }
                    
                    // Additional safety check: make sure it's not a "More hours" button
                    if (ariaLabel.toLowerCase().includes('more hours') || ariaLabel.toLowerCase().includes('hours')) {
                        console.log(`🚫 GOOGLE SEARCH MULTIPLE REVIEWS: Skipping "More hours" button: ${ariaLabel}`);
                        continue;
                    }
                    
                    console.log(`🔽 GOOGLE SEARCH MULTIPLE REVIEWS: Clicking review "More" button ${i + 1}/${moreButtons.length}: "${ariaLabel}"`);
                    
                    button.click();
                    expandedCount++;
                    
                    // Small delay between clicks to avoid overwhelming the page
                    if (i < moreButtons.length - 1) {
                        await this.wait(50); // Very short delay
                    }
                    
                } catch (error) {
                    console.error(`💥 GOOGLE SEARCH MULTIPLE REVIEWS: Error clicking "More" button ${i + 1}:`, error);
                }
            }
            
            console.log(`✅ GOOGLE SEARCH MULTIPLE REVIEWS: Successfully expanded ${expandedCount} review "More" buttons`);
            
            // Wait for DOM updates to complete
            await this.wait(500);
            
        } catch (error) {
            console.error('💥 GOOGLE SEARCH MULTIPLE REVIEWS: Error in runReadMoreScript:', error);
        }
    }

    // Extract all reviews from the page
    async extractAllReviews() {
        console.log('📊 GOOGLE SEARCH MULTIPLE REVIEWS: Starting review extraction...');
        
        // Reset reviewData at the start of each business extraction to prevent accumulation
        this.reviewData = [];
        
        try {
            // Try multiple selectors for Google Search reviews
            const reviewSelectors = [
                'div.bwb7ce', // Individual review containers (the actual review items!)
                '.EfDVh.wDYxhc.NFQFxe .bwb7ce', // Reviews within the main reviews container
                '.OYzgjc .bwb7ce', // Reviews within the alternative container
                'div.MyEned', // Standard GMB reviews (fallback)
                'div.jftiEf', // Alternative GMB reviews (fallback)
                '[data-review-id]', // Reviews with data attributes (fallback)
                '.review-item', // Generic review items (fallback)
                '.review' // Generic review class (fallback)
            ];
            
            let reviewContainers = [];
            let selectorUsed = '';
            
            for (const selector of reviewSelectors) {
                reviewContainers = document.querySelectorAll(selector);
                if (reviewContainers.length > 0) {
                    selectorUsed = selector;
                    break;
                }
            }
            
            if (reviewContainers.length === 0) {
                console.log('❌ GOOGLE SEARCH MULTIPLE REVIEWS: No review containers found');
                return [];
            }
            
            console.log(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: Found ${reviewContainers.length} review containers using selector: ${selectorUsed}`);
            
            // Extract business name
            this.businessName = this.extractBusinessName();
            
            // Extract reviews from containers with range limit
            const extractedReviews = this.extractReviewsFromContainers(reviewContainers, this.businessName);
            
            return extractedReviews || [];
            
        } catch (error) {
            console.error('💥 GOOGLE SEARCH MULTIPLE REVIEWS: Error in extractAllReviews:', error);
            return [];
        }
    }

    // Extract business name from the page
    extractBusinessName() {
        try {
            const nameSelectors = [
                'h1.x3AX1-LfntMc-header-title-title',
                'h1[data-attrid="title"]',
                '.SPZz6b .qrShPb',
                '.x3AX1-LfntMc-header-title-title',
                'h1',
                '.business-name',
                '.name'
            ];
            
            for (const selector of nameSelectors) {
                const nameElement = document.querySelector(selector);
                if (nameElement && nameElement.textContent.trim()) {
                    const businessName = nameElement.textContent.trim();
                    console.log(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: Extracted business name: ${businessName}`);
                    return businessName;
                }
            }
            
            return 'Unknown Business';
        } catch (error) {
            console.error('💥 GOOGLE SEARCH MULTIPLE REVIEWS: Error extracting business name:', error);
            return 'Unknown Business';
        }
    }

    // Extract reviews from the found containers
    extractReviewsFromContainers(containers, businessName) {
        console.log(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: Processing ${containers.length} review containers...`);
        
        // Apply range limit during extraction
        let containersToProcess = containers;
        if (this.rangeUtility.hasLimit()) {
            const maxReviews = this.rangeUtility.getMaxReviews();
            containersToProcess = Array.from(containers).slice(0, maxReviews);
            console.log(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: 🎯 RANGE LIMIT APPLIED: Processing only first ${containersToProcess.length} containers (limit: ${maxReviews})`);
        }

        containersToProcess.forEach((container, index) => {
            try {
                const reviewData = this.extractSingleReview(container, index + 1, businessName);
                if (reviewData) {
                    this.reviewData.push(reviewData);
                    
                    // Double-check range limit during extraction
                    if (this.rangeUtility.hasLimit() && this.reviewData.length >= this.rangeUtility.getMaxReviews()) {
                        console.log(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: 🎯 RANGE LIMIT REACHED during extraction: ${this.reviewData.length}/${this.rangeUtility.getMaxReviews()}`);
                        return; // Stop extracting more reviews
                    }
                }
            } catch (error) {
                console.error(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: Error extracting review ${index + 1}:`, error);
            }
        });

        console.log(`✅ GOOGLE SEARCH MULTIPLE REVIEWS: Successfully extracted ${this.reviewData.length} reviews`);
        return this.reviewData;
    }

    // Extract single review data (adapted for Google Search)
    extractSingleReview(container, index, businessName) {
        try {
            const review = {
                reviewIndex: index,
                businessName: businessName,
                reviewerName: '',
                rating: 0,
                reviewDate: '',
                reviewText: '',
                reviewerPhotoUrl: '',
                businessResponse: '',
                reviewId: '',
                extractedAt: new Date().toISOString()
            };
            
            // Extract reviewer name
            const nameSelectors = [
                '.Vpc5Fe', // Google Search reviewer name (the actual selector!)
                '.d4r55', // GMB reviewer name (fallback)
                '.TSUbDb', // GMB reviewer name alternative (fallback)
                '.reviewer-name', // Generic reviewer name (fallback)
                '.name' // Generic name (fallback)
            ];
            
            for (const selector of nameSelectors) {
                const nameElement = container.querySelector(selector);
                if (nameElement && nameElement.textContent.trim()) {
                    review.reviewerName = nameElement.textContent.trim();
                    break;
                }
            }
            
            // Extract rating (still look for standard rating patterns)
            const ratingSelectors = [
                '.dHX2k[role="img"]', // Google Search rating container
                '.kvMYJc', // GMB rating (fallback)
                '.Fam1ne.EBe2gf', // GMB rating alternative (fallback)
                '.review-rating', // Generic rating (fallback)
                '[role="img"][aria-label*="star"]', // Star ratings (fallback)
                '.rating' // Generic rating (fallback)
            ];
            
            for (const selector of ratingSelectors) {
                const ratingElement = container.querySelector(selector);
                if (ratingElement) {
                    const ariaLabel = ratingElement.getAttribute('aria-label');
                    if (ariaLabel) {
                        const ratingMatch = ariaLabel.match(/(\d+(?:\.\d+)?)/);
                        if (ratingMatch) {
                            review.rating = parseFloat(ratingMatch[1]);
                            break;
                        }
                    }
                }
            }
            
            // Extract review date (look for date patterns)
            const dateSelectors = [
                '.y3Ibjb', // Google Search review date 
                '.GSM50', // Could also be date in some cases
                '.rsqaWe', // GMB review date (fallback)
                '.review-date', // Generic review date (fallback)
                '.date' // Generic date (fallback)
            ];
            
            for (const selector of dateSelectors) {
                const dateElement = container.querySelector(selector);
                if (dateElement && dateElement.textContent.trim()) {
                    review.reviewDate = dateElement.textContent.trim();
                    break;
                }
            }
            
            // Extract review text
            const textSelectors = [
                '.OA1nbd', // Google Search review text (MAIN SELECTOR - contains full review text!)
                '.lgNNHf', // Alternative Google Search review text container
                'div.OA1nbd[jscontroller="lgNNHf"]', // More specific selector for review text
                '.MyEned .wiI7pd', // GMB review text (fallback)
                '.jftiEf .wiI7pd', // GMB review text alternative (fallback)
                '.review-text', // Generic review text (fallback)
                '.text', // Generic text (fallback)
                '.content' // Generic content (fallback)
            ];
            
            for (const selector of textSelectors) {
                const textElement = container.querySelector(selector);
                if (textElement && textElement.textContent.trim()) {
                    review.reviewText = textElement.textContent.trim();
                    break;
                }
            }
            
            // Extract reviewer photo URL (look in various places)
            const photoSelectors = [
                '.wSokxc', // Google Search reviewer photo container
                '.NBa7we img', // GMB reviewer photo (fallback)
                '.reviewer-photo img', // Generic reviewer photo (fallback)
                '.photo img' // Generic photo (fallback)
            ];
            
            for (const selector of photoSelectors) {
                const photoElement = container.querySelector(selector);
                if (photoElement) {
                    if (photoElement.tagName === 'IMG' && photoElement.src) {
                        review.reviewerPhotoUrl = photoElement.src;
                        break;
                    } else if (photoElement.style && photoElement.style.backgroundImage) {
                        // Extract URL from background-image style
                        const bgImage = photoElement.style.backgroundImage;
                        const urlMatch = bgImage.match(/url\("?([^"]+)"?\)/);
                        if (urlMatch) {
                            review.reviewerPhotoUrl = urlMatch[1];
                            break;
                        }
                    }
                }
            }
            
            // Extract business response
            const responseSelectors = [
                '.KmCjbd', // Google Search business response text (the actual selector!)
                '.dILdNe .wiI7pd', // GMB business response (fallback)
                '.business-response .text', // Generic business response (fallback)
                '.response .content' // Generic response content (fallback)
            ];
            
            for (const selector of responseSelectors) {
                const responseElement = container.querySelector(selector);
                if (responseElement && responseElement.textContent.trim()) {
                    review.businessResponse = responseElement.textContent.trim();
                    break;
                }
            }
            
            // Extract review ID if available
            review.reviewId = container.getAttribute('data-review-id') || 
                            container.getAttribute('data-rid') || 
                            container.id || 
                            `review-${index}`;
            
            // Only return review if we have minimum required data
            if (review.reviewerName || review.reviewText || review.rating > 0) {
                console.log(`📊 Review ${index}: ${review.reviewerName} - ${review.rating} stars - "${review.reviewText.substring(0, 50)}..."`);
                return review;
            } else {
                console.log(`📊 Review ${index}: Insufficient data, skipping`);
                return null;
            }
            
        } catch (error) {
            console.error(`💥 GOOGLE SEARCH MULTIPLE REVIEWS: Error extracting single review ${index}:`, error);
            return null;
        }
    }

    // Utility function to wait
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Stop extraction
    stopExtraction() {
        console.log('🛑 GOOGLE SEARCH MULTIPLE REVIEWS: Stopping extraction...');
        this.isRunning = false;
    }

    // Get current data
    getCurrentData() {
        return {
            reviewData: this.reviewData,
            isRunning: this.isRunning,
            businessName: this.businessName,
            totalReviews: this.reviewData.length,
            rangeSettings: this.rangeUtility.getStatusMessage()
        };
    }

    // Export to CSV
    exportToCSV() {
        if (this.reviewData.length === 0) {
            console.log('❌ GOOGLE SEARCH MULTIPLE REVIEWS: No review data to export');
            return;
        }

        const csvContent = this.convertReviewsToCSV(this.reviewData);
        
        // Create filename with business name (sanitized for file system) - matching other extractors format
        const sanitizedBusinessName = this.businessName 
            ? this.businessName.replace(/[^a-zA-Z0-9\s-]/g, '').replace(/\s+/g, '-').toLowerCase().substring(0, 50)
            : 'unknown-business';
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
        const filename = `${sanitizedBusinessName}-reviews-${timestamp}.csv`;
        
        console.log(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: Exporting ${this.reviewData.length} reviews to: ${filename}`);
        this.downloadCSV(csvContent, filename);
    }

    // Convert reviews to CSV format
    convertReviewsToCSV(reviews) {
        const headers = ['#', 'Business Name', 'Reviewer Name', 'Rating', 'Date', 'Review Text', 'Business Response', 'Reviewer Photo URL', 'Review ID', 'Extracted At'];
        const csvRows = [headers.join(',')];

        reviews.forEach((review, index) => {
            const row = [
                index + 1,
                this.escapeCsvValue(review.businessName),
                this.escapeCsvValue(review.reviewerName),
                review.rating,
                this.escapeCsvValue(review.reviewDate),
                this.escapeCsvValue(review.reviewText),
                this.escapeCsvValue(review.businessResponse),
                this.escapeCsvValue(review.reviewerPhotoUrl),
                this.escapeCsvValue(review.reviewId),
                review.extractedAt
            ];
            csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
    }

    // Escape CSV values
    escapeCsvValue(value) {
        if (value === null || value === undefined) return '';
        const stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
    }

    // Download CSV file
    downloadCSV(csvContent, filename) {
        console.log(`📊 GOOGLE SEARCH MULTIPLE REVIEWS: Downloading CSV file: ${filename}`);
        
        try {
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            
            console.log('✅ GOOGLE SEARCH MULTIPLE REVIEWS: CSV file download initiated successfully');
        } catch (error) {
            console.error('💥 GOOGLE SEARCH MULTIPLE REVIEWS: Error downloading CSV file:', error);
            throw error;
        }
    }
}

// Create global instance
window.googleSearchMultipleReviewScraper = new GoogleSearchMultipleReviewScraper();

// Global flag check to prevent multiple loads
if (!window.googleSearchMultipleReviewScraperLoaded) {
    window.googleSearchMultipleReviewScraperLoaded = true;
    
    // Make globally accessible
    window.GoogleSearchMultipleReviewScraper = GoogleSearchMultipleReviewScraper;

console.log('🔍 GOOGLE SEARCH MULTIPLE REVIEWS: Module loaded and ready'); 
} 
