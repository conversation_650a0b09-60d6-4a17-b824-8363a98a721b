// Review Range Utility Module
// This module provides functionality to limit the number of reviews scraped
// Can be reused across different scrapers (single, prolist, etc.)

class ReviewRangeUtility {
    constructor() {
        this.maxReviews = null; // null means no limit
        this.version = "1.0-RangeUtility";
        console.log(`🔢 RANGE UTILITY: Initializing version ${this.version}`);
    }

    // Set the maximum number of reviews to scrape
    setMaxReviews(maxReviews) {
        if (maxReviews === null || maxReviews === undefined || maxReviews === '') {
            this.maxReviews = null;
            console.log(`🔢 RANGE UTILITY: No limit set - will scrape all reviews`);
        } else {
            const parsedMax = parseInt(maxReviews, 10);
            if (isNaN(parsedMax) || parsedMax < 1) {
                console.warn(`🔢 RANGE UTILITY: Invalid max reviews value: ${maxReviews}, using no limit`);
                this.maxReviews = null;
            } else if (parsedMax > 99999) {
                console.warn(`🔢 RANGE UTILITY: Max reviews value too high: ${parsedMax}, limiting to 99999`);
                this.maxReviews = 99999;
            } else {
                this.maxReviews = parsedMax;
                console.log(`🔢 RANGE UTILITY: Max reviews set to: ${this.maxReviews}`);
            }
        }
        return this.maxReviews;
    }

    // Get the current max reviews setting
    getMaxReviews() {
        return this.maxReviews;
    }

    // Check if we have a limit set
    hasLimit() {
        return this.maxReviews !== null && this.maxReviews > 0;
    }

    // Check if we should stop scraping based on current count
    shouldStopScraping(currentCount) {
        if (!this.hasLimit()) {
            return false; // No limit, don't stop
        }
        
        const shouldStop = currentCount >= this.maxReviews;
        if (shouldStop) {
            console.log(`🔢 RANGE UTILITY: Reached limit (${currentCount}/${this.maxReviews}) - should stop scraping`);
        } else {
            console.log(`🔢 RANGE UTILITY: Within limit (${currentCount}/${this.maxReviews}) - continue scraping`);
        }
        
        return shouldStop;
    }

    // Get the effective target count (what we're actually aiming for)
    getEffectiveTarget(totalAvailable) {
        if (!this.hasLimit()) {
            return totalAvailable; // No limit, return total available
        }
        
        const effectiveTarget = Math.min(this.maxReviews, totalAvailable);
        console.log(`🔢 RANGE UTILITY: Effective target: ${effectiveTarget} (limit: ${this.maxReviews}, available: ${totalAvailable})`);
        return effectiveTarget;
    }

    // Truncate results to the specified limit
    truncateResults(results) {
        if (!this.hasLimit() || !Array.isArray(results)) {
            return results; // No limit or invalid input, return as-is
        }
        
        if (results.length <= this.maxReviews) {
            console.log(`🔢 RANGE UTILITY: Results (${results.length}) within limit (${this.maxReviews}) - no truncation needed`);
            return results;
        }
        
        const truncatedResults = results.slice(0, this.maxReviews);
        console.log(`🔢 RANGE UTILITY: Truncated results from ${results.length} to ${truncatedResults.length} (limit: ${this.maxReviews})`);
        return truncatedResults;
    }

    // Get a status message about the current range setting
    getStatusMessage() {
        if (!this.hasLimit()) {
            return 'No limit set - will extract all available reviews';
        }
        return `Limit set to ${this.maxReviews} reviews`;
    }

    // Validate range input from UI
    static validateRangeInput(input) {
        // Allow empty string (no limit)
        if (input === '' || input === null || input === undefined) {
            return { valid: true, value: null, message: 'No limit (will extract all reviews)' };
        }
        
        // Remove any non-digit characters
        const cleanInput = input.toString().replace(/[^\d]/g, '');
        
        if (cleanInput === '') {
            return { valid: true, value: null, message: 'No limit (will extract all reviews)' };
        }
        
        const numValue = parseInt(cleanInput, 10);
        
        if (isNaN(numValue)) {
            return { valid: false, value: null, message: 'Please enter a valid number' };
        }
        
        if (numValue < 1) {
            return { valid: false, value: null, message: 'Number must be at least 1' };
        }
        
        if (numValue > 99999) {
            return { valid: false, value: null, message: 'Maximum limit is 99,999 reviews' };
        }
        
        return { 
            valid: true, 
            value: numValue, 
            message: `Will extract up to ${numValue.toLocaleString()} reviews` 
        };
    }

    // Format number for display (with commas)
    static formatNumber(num) {
        if (num === null || num === undefined) {
            return 'All';
        }
        return num.toLocaleString();
    }

    // Create UI input element for range setting
    static createRangeInput(id = 'reviewRangeInput', placeholder = 'Enter max reviews (leave empty for all)') {
        const input = document.createElement('input');
        input.type = 'text';
        input.id = id;
        input.className = 'search__input range-input';
        input.placeholder = placeholder;
        input.maxLength = '5'; // 5 digits max (99999)
        input.pattern = '[0-9]*'; // Only numbers
        input.inputMode = 'numeric'; // Mobile numeric keyboard
        
        // Add real-time validation
        input.addEventListener('input', function(e) {
            // Remove non-digits
            let value = e.target.value.replace(/[^\d]/g, '');
            
            // Limit to 5 digits
            if (value.length > 5) {
                value = value.substring(0, 5);
            }
            
            e.target.value = value;
            
            // Show validation message
            const validation = ReviewRangeUtility.validateRangeInput(value);
            ReviewRangeUtility.showValidationMessage(e.target, validation);
        });
        
        return input;
    }

    // Show validation message for input
    static showValidationMessage(inputElement, validation) {
        // Remove existing message
        const existingMessage = inputElement.parentElement.querySelector('.range-validation-message');
        if (existingMessage) {
            existingMessage.remove();
        }
        
        // Create new message
        const messageDiv = document.createElement('div');
        messageDiv.className = `range-validation-message ${validation.valid ? 'valid' : 'invalid'}`;
        messageDiv.textContent = validation.message;
        messageDiv.style.fontSize = '11px';
        messageDiv.style.marginTop = '4px';
        messageDiv.style.color = validation.valid ? '#10b981' : '#ef4444';
        
        // Insert after input
        inputElement.parentElement.appendChild(messageDiv);
    }

    // Create UI label for range input
    static createRangeLabel(text = 'Review Limit:', htmlFor = 'reviewRangeInput') {
        const label = document.createElement('label');
        label.htmlFor = htmlFor;
        label.textContent = text;
        label.style.fontSize = '12px';
        label.style.fontWeight = 'bold';
        label.style.color = '#d1d5db';
        label.style.marginBottom = '4px';
        label.style.display = 'block';
        return label;
    }

    // Get range value from UI input
    static getRangeFromInput(inputId = 'reviewRangeInput') {
        const input = document.getElementById(inputId);
        if (!input) {
            console.warn(`🔢 RANGE UTILITY: Input element ${inputId} not found`);
            return null;
        }
        
        const validation = ReviewRangeUtility.validateRangeInput(input.value);
        if (!validation.valid) {
            console.warn(`🔢 RANGE UTILITY: Invalid range input: ${input.value}`);
            return null;
        }
        
        return validation.value;
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.ReviewRangeUtility = ReviewRangeUtility;
}

console.log('🔢 RANGE UTILITY: Utility module loaded and ready'); 