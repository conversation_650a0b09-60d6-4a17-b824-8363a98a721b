// Location Changer Favorites Manager
class LocationFavorites {
    constructor() {
        this.storageKey = 'locationChangerFavorites';
        this.favorites = [];
        this.init();
    }

    async init() {
        this.createFavoritesSection();
        this.bindEvents();
        this.setupMessageListener();
        await this.loadFavoritesAsync();
        this.updateLoadDropdown();
    }

    setupMessageListener() {
        // Listen for location changes from background script
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                if (message.type === 'locationChanged') {
                    console.log('LocationFavorites: Received location change from background');
                    // Update current location display if it exists
                    this.updateCurrentLocationDisplay(message.data);
                    // Update placeholders
                    this.updatePlaceholders();
                }
            });
        }
    }

    updateCurrentLocationDisplay(data) {
        // Update the current location display in the popup header if it exists
        const currentLocationText = document.getElementById('currentLocationText');
        if (currentLocationText && data.place) {
            currentLocationText.textContent = data.place;
        }
        
        // Update location status indicator if it exists
        const locationStatusIndicator = document.getElementById('locationStatusIndicator');
        if (locationStatusIndicator) {
            locationStatusIndicator.style.background = data.enabled ? '#10b981' : '#6b7280';
        }
    }

    async loadFavoritesAsync() {
        try {
            const result = await chrome.storage.local.get([this.storageKey]);
            this.favorites = result[this.storageKey] || [];
            this.updateLoadDropdown();
            return this.favorites;
        } catch (error) {
            console.error('Error loading favorites:', error);
            this.favorites = [];
            return [];
        }
    }

    async saveFavorites() {
        try {
            await chrome.storage.local.set({ [this.storageKey]: this.favorites });
            // Dispatch custom event to notify background script
            window.dispatchEvent(new CustomEvent('locationFavoritesChanged', { 
                detail: { favorites: this.favorites } 
            }));
        } catch (error) {
            console.error('Error saving favorites:', error);
        }
    }

    getCurrentLocationData() {
        return {
            place: document.getElementById('place')?.value || '',
            hl: document.getElementById('hl')?.value || '',
            gl: document.getElementById('gl')?.value || '',
            latitude: document.getElementById('latitude')?.value || '',
            longitude: document.getElementById('longitude')?.value || '',
            enabled: document.getElementById('enabled')?.checked || false
        };
    }

    setLocationData(data) {
        const fieldMappings = {
            place: 'place',
            hl: 'hl', 
            gl: 'gl',
            latitude: 'latitude',
            longitude: 'longitude',
            enabled: 'enabled'
        };
        
        Object.entries(fieldMappings).forEach(([dataKey, elementId]) => {
            const element = document.getElementById(elementId);
            if (element && data[dataKey] !== undefined) {
                if (element.type === 'checkbox') {
                    element.checked = data[dataKey];
                } else {
                    element.value = data[dataKey];
                }
                // Trigger change event to update any dependencies
                element.dispatchEvent(new Event('change', { bubbles: true }));
                // Also trigger input event for good measure
                element.dispatchEvent(new Event('input', { bubbles: true }));
            }
        });

        // Manually update background settings and save to storage
        // This ensures the location data is actually saved when loading favorites
        if (typeof background !== 'undefined' && background.settings) {
            // Update background settings object
            if (data.latitude) background.settings.latitude = parseFloat(data.latitude);
            if (data.longitude) background.settings.longitude = parseFloat(data.longitude);
            if (data.place) {
                background.settings.location = data.place;
                background.settings.name = data.place;
            }
            if (data.hl) background.settings.hl = data.hl;
            if (data.gl) background.settings.gl = data.gl;
            if (data.enabled !== undefined) background.settings.enabled = data.enabled;
            
            // Add timestamp and save to chrome storage
            background.settings.timestamp = new Date().getTime();
            
            // Save to chrome storage
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
                chrome.storage.local.set({settings: background.settings});
            }

            // Update placeholders to reflect the new values
            this.updatePlaceholders();

            // Trigger the enabler function if it exists to update UI state
            if (typeof enabler === 'function') {
                enabler();
            }
        }
    }

    updatePlaceholders() {
        // Update placeholders using jQuery if available
        if (typeof $ !== 'undefined') {
            const placeElement = $('#place');
            const latElement = $('#latitude');
            const lngElement = $('#longitude');
            const hlElement = $('#hl');
            const glElement = $('#gl');

            if (background && background.settings) {
                if (background.settings.location) placeElement.prop('placeholder', background.settings.location);
                if (background.settings.latitude) latElement.prop('placeholder', background.settings.latitude);
                if (background.settings.longitude) lngElement.prop('placeholder', background.settings.longitude);
                if (background.settings.hl) hlElement.prop('placeholder', background.settings.hl);
                if (background.settings.gl) glElement.prop('placeholder', background.settings.gl);
            }
        }
    }

    createFavoritesSection() {
        const locationBody = document.querySelector('.location-changer-body');
        if (!locationBody) return;

        const favoritesSection = document.createElement('div');
        favoritesSection.className = 'location-favorites-section';
        favoritesSection.innerHTML = `
            <div class="location-favorites-header">
                <span class="location-favorites-title">Favorite Locations</span>
            </div>
            <div class="location-favorites-controls">
                <div class="location-favorites-row">
                    <input type="text" 
                           id="favorite-name-input" 
                           class="location-input location-favorites-input" 
                           placeholder="Location name..."
                           maxlength="30">
                    <button id="save-location-btn" 
                            class="location-favorites-btn location-save-btn" 
                            title="Save Current Location">
                        Save
                    </button>
                </div>
                <div class="location-favorites-row">
                    <select id="load-location-select" 
                            class="location-input location-favorites-select">
                        <option value="">Select a favorite...</option>
                    </select>
                    <button id="load-location-btn" 
                            class="location-favorites-btn location-load-btn" 
                            title="Load Selected Location">
                        Load
                    </button>
                    <button id="delete-location-btn" 
                            class="location-favorites-btn location-delete-btn" 
                            title="Delete Selected Location">
                        ×
                    </button>
                </div>
            </div>
        `;

        locationBody.appendChild(favoritesSection);
    }

    bindEvents() {
        // Save location
        document.getElementById('save-location-btn')?.addEventListener('click', () => {
            this.saveCurrentLocation();
        });

        // Load location
        document.getElementById('load-location-btn')?.addEventListener('click', () => {
            this.loadSelectedLocation();
        });

        // Delete location
        document.getElementById('delete-location-btn')?.addEventListener('click', () => {
            this.deleteSelectedLocation();
        });

        // Enter key on name input should save
        document.getElementById('favorite-name-input')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.saveCurrentLocation();
            }
        });

        // Update delete button state when selection changes
        document.getElementById('load-location-select')?.addEventListener('change', () => {
            this.updateDeleteButtonState();
        });
    }

    async saveCurrentLocation() {
        const nameInput = document.getElementById('favorite-name-input');
        const name = nameInput?.value.trim();

        if (!name) {
            this.showMessage('Please enter a name for this location', 'error');
            return;
        }

        const locationData = this.getCurrentLocationData();
        
        // Check if we have at least latitude/longitude or place
        if (!locationData.latitude && !locationData.longitude && !locationData.place) {
            this.showMessage('Please set a location before saving', 'error');
            return;
        }

        // Check if name already exists
        const existingIndex = this.favorites.findIndex(fav => fav.name === name);
        
        if (existingIndex >= 0) {
            // Update existing
            this.favorites[existingIndex] = {
                name: name,
                data: locationData,
                savedAt: new Date().toISOString()
            };
            this.showMessage(`Updated "${name}"`, 'success');
        } else {
            // Add new
            this.favorites.push({
                name: name,
                data: locationData,
                savedAt: new Date().toISOString()
            });
            this.showMessage(`Saved "${name}"`, 'success');
        }

        await this.saveFavorites();
        this.updateLoadDropdown();
        nameInput.value = '';
    }

    loadSelectedLocation() {
        const selectElement = document.getElementById('load-location-select');
        const selectedName = selectElement?.value;

        if (!selectedName) {
            this.showMessage('Please select a location to load', 'error');
            return;
        }

        const favorite = this.favorites.find(fav => fav.name === selectedName);
        if (favorite) {
            this.setLocationData(favorite.data);
            this.showMessage(`Loaded "${selectedName}"`, 'success');
        } else {
            this.showMessage('Location not found', 'error');
        }
    }

    async deleteSelectedLocation() {
        const selectElement = document.getElementById('load-location-select');
        const selectedName = selectElement?.value;

        if (!selectedName) {
            this.showMessage('Please select a location to delete', 'error');
            return;
        }

        if (confirm(`Are you sure you want to delete "${selectedName}"?`)) {
            this.favorites = this.favorites.filter(fav => fav.name !== selectedName);
            await this.saveFavorites();
            this.updateLoadDropdown();
            this.showMessage(`Deleted "${selectedName}"`, 'success');
        }
    }

    updateLoadDropdown() {
        const selectElement = document.getElementById('load-location-select');
        if (!selectElement) return;

        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="">Select a favorite...</option>';

        // Add favorites sorted by name
        const sortedFavorites = [...this.favorites].sort((a, b) => a.name.localeCompare(b.name));
        
        sortedFavorites.forEach(favorite => {
            const option = document.createElement('option');
            option.value = favorite.name;
            option.textContent = favorite.name;
            selectElement.appendChild(option);
        });

        this.updateDeleteButtonState();
    }

    updateDeleteButtonState() {
        const selectElement = document.getElementById('load-location-select');
        const deleteBtn = document.getElementById('delete-location-btn');
        
        if (deleteBtn) {
            deleteBtn.disabled = !selectElement?.value;
            deleteBtn.style.opacity = selectElement?.value ? '1' : '0.5';
        }
    }

    showMessage(text, type = 'info') {
        // Create or get existing message element
        let messageEl = document.querySelector('.location-favorites-message');
        
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'location-favorites-message';
            const favoritesSection = document.querySelector('.location-favorites-section');
            if (favoritesSection) {
                favoritesSection.appendChild(messageEl);
            }
        }

        messageEl.textContent = text;
        messageEl.className = `location-favorites-message location-favorites-message-${type}`;
        
        // Clear message after 3 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.textContent = '';
                messageEl.className = 'location-favorites-message';
            }
        }, 3000);
    }

    // Export favorites to JSON (for backup)
    exportFavorites() {
        const dataStr = JSON.stringify(this.favorites, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = 'location-favorites.json';
        link.click();
        
        URL.revokeObjectURL(url);
    }

    // Import favorites from JSON
    async importFavorites(jsonData) {
        try {
            const imported = JSON.parse(jsonData);
            if (Array.isArray(imported)) {
                this.favorites = imported;
                await this.saveFavorites();
                this.updateLoadDropdown();
                this.showMessage('Favorites imported successfully', 'success');
            } else {
                throw new Error('Invalid format');
            }
        } catch (error) {
            this.showMessage('Error importing favorites', 'error');
            console.error('Import error:', error);
        }
    }
}

// Initialize when the location changer is ready
function initLocationFavorites() {
    // Wait for the location changer body to be available
    const checkForLocationChanger = () => {
        const locationBody = document.querySelector('.location-changer-body');
        if (locationBody) {
            // Check if favorites haven't been initialized yet
            if (!document.querySelector('.location-favorites-section')) {
                new LocationFavorites();
            }
        } else {
            // Try again in 100ms
            setTimeout(checkForLocationChanger, 100);
        }
    };

    checkForLocationChanger();
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initLocationFavorites);
} else {
    initLocationFavorites();
} 