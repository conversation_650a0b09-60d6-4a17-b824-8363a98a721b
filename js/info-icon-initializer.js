// Info Icon Initializer
// Initializes info icons throughout the extension

class InfoIconInitializer {
    static initialized = false;
    static iconElements = [];

    // Initialize all info icons
    static init() {
        if (this.initialized) return;

        try {
            // Ensure tooltip system is loaded
            if (typeof UniversalTooltipSystem === 'undefined') {
                console.warn('InfoIconInitializer: UniversalTooltipSystem not loaded');
                return;
            }

            // Initialize the tooltip system
            UniversalTooltipSystem.init();

            // Initialize all info icons
            this.initializeInfoIcons();

            // Add fallback processing for settings context
            this.processSettingsFallback();

            this.initialized = true;
            console.log('InfoIconInitializer: Initialization complete');
        } catch (error) {
            console.error('InfoIconInitializer: Error during initialization:', error);
        }
    }

    // Initialize all info icons in the page
    static initializeInfoIcons() {
        // Find all elements with data-tooltip attribute
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            const tooltipText = element.getAttribute('data-tooltip');
            const className = element.className;
            
            if (tooltipText) {
                // Create info icon based on element class
                if (className.includes('chronometer-info-icon')) {
                    this.createChronometerInfoIcon(element, tooltipText);
                } else {
                    // Generic info icon for other elements
                    this.createGenericInfoIcon(element, tooltipText);
                }
            }
        });
    }

    // Fallback processing for settings context
    static processSettingsFallback() {
        // Only run fallback if we're in settings context
        if (!this.detectSettingsContext()) {
            return;
        }

        console.log('InfoIconInitializer: Running settings fallback processing');

        // Find any remaining chronometer-info-icon elements that weren't processed
        const remainingElements = document.querySelectorAll('.chronometer-info-icon[data-tooltip]');
        
        console.log('InfoIconInitializer: Found remaining elements:', remainingElements.length);

        remainingElements.forEach(element => {
            const tooltipText = element.getAttribute('data-tooltip');
            if (tooltipText) {
                console.log('InfoIconInitializer: Processing remaining element with fallback', {
                    element: element,
                    tooltipText: tooltipText.substring(0, 50) + '...'
                });
                
                // Force use of level-two tooltip system
                this.createLevelTwoTooltipIcon(element, tooltipText);
            }
        });

        // Also add a delayed fallback for dynamically loaded content
        setTimeout(() => {
            const delayedElements = document.querySelectorAll('.chronometer-info-icon[data-tooltip]');
            if (delayedElements.length > 0) {
                console.log('InfoIconInitializer: Processing delayed elements:', delayedElements.length);
                delayedElements.forEach(element => {
                    const tooltipText = element.getAttribute('data-tooltip');
                    if (tooltipText) {
                        this.createLevelTwoTooltipIcon(element, tooltipText);
                    }
                });
            }
        }, 1000);
    }

    // Enhanced settings context detection
    static detectSettingsContext() {
        const detectionMethods = {
            url: window.location.href.includes('settings'),
            pathname: window.location.pathname.includes('settings'),
            title: document.title && document.title.toLowerCase().includes('settings'),
            settingsElements: document.querySelector('.settings-tabs, .settings-item, #general-settings-content'),
            settingsClass: document.body.classList.contains('settings-page'),
            settingsId: document.getElementById('settingsContainer')
        };
        
        const isSettingsContext = Object.values(detectionMethods).some(method => method);
        
        console.log('InfoIconInitializer: Settings context detection:', {
            methods: detectionMethods,
            result: isSettingsContext,
            url: window.location.href
        });
        
        return isSettingsContext;
    }

    // Create chronometer-specific info icon
    static createChronometerInfoIcon(element, tooltipText) {
        // Enhanced settings context detection
        const isSettingsContext = this.detectSettingsContext();
        
        console.log('InfoIconInitializer: Creating chronometer info icon', {
            isSettingsContext,
            element: element,
            tooltipText: tooltipText.substring(0, 50) + '...'
        });
        
        if (isSettingsContext) {
            // Use level-two tooltip system for settings context
            console.log('InfoIconInitializer: Using level-two tooltip system');
            this.createLevelTwoTooltipIcon(element, tooltipText);
        } else {
            // Use standard tooltip system
            console.log('InfoIconInitializer: Using standard tooltip system');
            const infoIcon = UniversalTooltipSystem.createInfoIcon({
                tooltip: tooltipText,
                iconSize: 'default',
                tooltipSize: 'medium',
                className: 'chronometer-tooltip-icon',
                position: 'auto',
                id: 'chronometer-info-' + Math.random().toString(36).substr(2, 9)
            });

            // Replace the placeholder element with the actual info icon
            element.parentNode.replaceChild(infoIcon, element);
            
            this.iconElements.push(infoIcon);
            
            console.log('InfoIconInitializer: Chronometer info icon created');
        }
    }

    // Create level-two tooltip icon for settings contexts
    static createLevelTwoTooltipIcon(element, tooltipText) {
        try {
            console.log('InfoIconInitializer: Creating level-two tooltip icon', {
                element: element,
                hasParent: !!element.parentNode,
                tooltipText: tooltipText
            });

            // Create a simple div that acts as the tooltip trigger
            const iconDiv = document.createElement('div');
            iconDiv.className = 'level-two-tooltip-icon';
            iconDiv.style.cssText = `
                display: inline-flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                margin-left: 6px;
                transition: all 0.2s ease;
            `;
            
            // Add the purple dot
            iconDiv.innerHTML = '●';
            iconDiv.style.color = '#7C3AED';
            iconDiv.style.fontSize = '12px';
            iconDiv.style.fontWeight = 'bold';

            // Add hover effects
            iconDiv.addEventListener('mouseenter', () => {
                console.log('InfoIconInitializer: Level-two tooltip mouseenter');
                iconDiv.style.color = '#ffffff';
                iconDiv.style.filter = 'drop-shadow(0 0 3px rgba(255, 255, 255, 0.3))';
                iconDiv.style.transform = 'scale(1.1)';
                
                // Show level-two tooltip
                if (typeof UniversalTooltipSystem !== 'undefined' && UniversalTooltipSystem.showLevelTwoTooltip) {
                    UniversalTooltipSystem.showLevelTwoTooltip(iconDiv, { text: tooltipText });
                } else {
                    console.error('InfoIconInitializer: UniversalTooltipSystem.showLevelTwoTooltip not available');
                }
            });

            iconDiv.addEventListener('mouseleave', () => {
                console.log('InfoIconInitializer: Level-two tooltip mouseleave');
                iconDiv.style.color = '#7C3AED';
                iconDiv.style.filter = 'none';
                iconDiv.style.transform = 'scale(1)';
                
                // Hide level-two tooltip
                if (typeof UniversalTooltipSystem !== 'undefined' && UniversalTooltipSystem.hideLevelTwoTooltip) {
                    UniversalTooltipSystem.hideLevelTwoTooltip();
                } else {
                    console.error('InfoIconInitializer: UniversalTooltipSystem.hideLevelTwoTooltip not available');
                }
            });

            // Replace the placeholder element with the level-two tooltip icon
            if (element.parentNode) {
                element.parentNode.replaceChild(iconDiv, element);
                console.log('InfoIconInitializer: Successfully replaced element with level-two tooltip icon');
            } else {
                console.error('InfoIconInitializer: Element has no parent node, cannot replace');
                return;
            }
            
            this.iconElements.push(iconDiv);
            
            console.log('InfoIconInitializer: Level-two tooltip icon created for settings context');
        } catch (error) {
            console.error('InfoIconInitializer: Error creating level-two tooltip icon:', error);
        }
    }

    // Create generic info icon
    static createGenericInfoIcon(element, tooltipText) {
        const infoIcon = UniversalTooltipSystem.createInfoIcon({
            tooltip: tooltipText,
            iconSize: 'default',
            tooltipSize: 'default',
            className: 'generic-tooltip-icon',
            position: 'auto'
        });

        // Replace the placeholder element with the actual info icon
        element.parentNode.replaceChild(infoIcon, element);
        
        this.iconElements.push(infoIcon);
        
        console.log('InfoIconInitializer: Generic info icon created');
    }

    // Add info icon to any element programmatically
    static addInfoIcon(targetSelector, tooltipText, options = {}) {
        const targetElement = document.querySelector(targetSelector);
        if (!targetElement) {
            console.warn(`InfoIconInitializer: Target element not found: ${targetSelector}`);
            return null;
        }

        const infoIcon = UniversalTooltipSystem.createInfoIcon({
            tooltip: tooltipText,
            iconSize: options.iconSize || 'default',
            tooltipSize: options.tooltipSize || 'default',
            className: options.className || 'dynamic-tooltip-icon',
            position: options.position || 'auto',
            id: options.id || null
        });

        // Insert based on method
        const insertMethod = options.insertMethod || 'after';
        switch (insertMethod) {
            case 'after':
                targetElement.parentNode.insertBefore(infoIcon, targetElement.nextSibling);
                break;
            case 'before':
                targetElement.parentNode.insertBefore(infoIcon, targetElement);
                break;
            case 'append':
                targetElement.appendChild(infoIcon);
                break;
            case 'prepend':
                targetElement.insertBefore(infoIcon, targetElement.firstChild);
                break;
            default:
                targetElement.parentNode.insertBefore(infoIcon, targetElement.nextSibling);
        }

        this.iconElements.push(infoIcon);
        return infoIcon;
    }

    // Reinitialize icons (useful for dynamic content)
    static reinitialize() {
        this.cleanup();
        this.initialized = false;
        this.init();
    }

    // Cleanup function for DOM restoration
    static cleanup() {
        try {
            // Remove all created info icons
            this.iconElements.forEach(icon => {
                if (icon && icon.parentNode) {
                    icon.parentNode.removeChild(icon);
                }
            });
            
            this.iconElements = [];

            // Cleanup tooltip system
            if (typeof UniversalTooltipSystem !== 'undefined') {
                UniversalTooltipSystem.cleanup();
            }

            this.initialized = false;
            console.log('InfoIconInitializer: Cleanup complete');
        } catch (error) {
            console.error('InfoIconInitializer: Error during cleanup:', error);
        }
    }

    // Check if system is initialized
    static isInitialized() {
        return this.initialized;
    }

    // Get all created icon elements
    static getIconElements() {
        return [...this.iconElements];
    }
}

// Auto-initialize when DOM is ready
function initializeInfoIcons() {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            InfoIconInitializer.init();
        });
    } else {
        InfoIconInitializer.init();
    }
}

// Initialize for different contexts
if (typeof window !== 'undefined') {
    window.InfoIconInitializer = InfoIconInitializer;
    
    // Initialize immediately if in popup context
    if (window.location.pathname.includes('popup.html') || 
        window.location.pathname.includes('settings') ||
        document.getElementById('pomodoroChronometerSound')) {
        initializeInfoIcons();
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = InfoIconInitializer;
}