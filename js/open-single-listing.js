// Open Single Listing - Simple Independent Button
// Creates a standalone button to open business share links in new tabs

// Prevent multiple loads
if (window.OpenSingleListingActive) {
  console.log('Open Single Listing: Already active');
} else {
  window.OpenSingleListingActive = true;
  
  // Settings management
  let settings = { openSingleListing: true }; // Default enabled
  
  // Load settings from storage
  const loadSettings = async () => {
    try {
      const result = await chrome.storage.local.get('gmbExtractorSettings');
      if (result.gmbExtractorSettings) {
        settings = { ...settings, ...result.gmbExtractorSettings };
      }
      console.log('Open Single Listing: Loaded settings:', settings);
    } catch (error) {
      console.error('Open Single Listing: Error loading settings:', error);
    }
  };
  
  // Listen for settings updates
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'updateSettings') {
      settings = { ...settings, ...message.settings };
      console.log('Open Single Listing: Settings updated:', settings);
      
      // Re-check if we should show/hide buttons based on new settings
      if (!settings.openSingleListing) {
        // Remove any existing buttons if Open Single Listing is disabled
        const existingButton = document.getElementById('open-single-listing-btn');
        if (existingButton) {
          existingButton.remove();
          console.log('Open Single Listing: Button removed due to settings change');
        }
      } else {
        // Re-initialize if Open Single Listing is enabled
        setTimeout(() => {
          injectButton();
        }, 500);
      }
    }
  });

  console.log('Open Single Listing: Starting simple detection system');
   
  // Use the EXACT same detection logic as Multiple Listings Review Analysis
  function isMultipleListingsMode() {    
    try {
      // Check for the aria-label pattern that indicates search results
      const resultsContainer = document.querySelector('[aria-label*="Results for"]');
      if (resultsContainer) {
        return true;
      }
      
      // Alternative check: Look for multiple business listing containers
      const businessListings = document.querySelectorAll('[data-cid]');
      if (businessListings && businessListings.length > 1) {
        return true;
      }
      
      // Check for search results feed
      const feedElement = document.querySelector('[role="feed"]');
      if (feedElement) {
        return true;
      }
      
      // Check page source for multiple listings indicators
      const pageSource = document.documentElement.outerHTML;
      
      // Look for categorical-search-results-injection pattern
      if (pageSource.includes('categorical-search-results-injection')) {
        return true;
      }
      
      // Look for multiple gcid patterns (business category identifiers)
      const gcidMatches = pageSource.match(/gcid:[a-z_]+/g);
      if (gcidMatches && gcidMatches.length > 3) {
        return true;
      }
      
      return false;
      
    } catch (error) {
      console.error('Open Single Listing: Error detecting multiple listings:', error);
      return false;
    }
  }
   
  // Simple detection function
  function findBusinessHeading() {
    // Try all possible business heading selectors
    const selectors = [
      '.DUwDvf.lfPIob'                            // Multiple listing popup
    ];
    
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element;
      }
    }
    
    return null;
  }
  
  // Create and inject button
  function createOpenButton() {
    const button = document.createElement('button');
    button.id = 'open-single-listing-btn';
    button.textContent = 'Open Single Listing';
    button.style.cssText = `
      background: #4285f4 !important;
      color: white !important;
      border: none !important;
      border-radius: 4px !important;
      padding: 4px 12px !important;
      height: 28px !important;
      margin-bottom: 7px !important;
      margin-right: 8px !important;
      font-size: 12px !important;
      font-weight: 500 !important;
      cursor: pointer !important;
      z-index: 99999 !important;
      position: relative !important;
      display: inline-block !important;
    `;
    
    button.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      openShareLink(button);
    });
    
    return button;
  }
  
  // Main function to handle share link opening
  function openShareLink(button) {
    console.log('Open Single Listing: Button clicked, looking for share button');
    
    // Update button to show loading
    const originalText = button.textContent;
    button.textContent = 'Loading...';
    button.style.background = '#orange';
    
    // Find and click share button
    const shareButton = document.querySelector('button[aria-label="Share"]');
    
    if (!shareButton) {
      console.log('Open Single Listing: Share button not found');
      button.textContent = 'No Share';
      button.style.background = '#red';
      setTimeout(() => {
        button.textContent = originalText;
        button.style.background = '#4285f4';
      }, 2000);
      return;
    }
    
    console.log('Open Single Listing: Clicking share button');
    shareButton.click();
    
    // Wait for share dialog and extract link
    setTimeout(() => {
      const shareInput = document.querySelector('input.vrsrZe[readonly]');
      
      if (shareInput && shareInput.value) {
        const rawShareUrl = shareInput.value;
        
        // Clean the URL by removing Google tracking parameters
        const cleanShareUrl = rawShareUrl.split('?')[0]; // Remove everything after the first ?
        
        console.log('Open Single Listing: Raw URL:', rawShareUrl);
        console.log('Open Single Listing: Clean URL:', cleanShareUrl);
        
        window.open(cleanShareUrl, '_blank');
       
       button.textContent = 'Opened!';
       button.style.background = '#green';
       setTimeout(() => {
         button.textContent = originalText;
         button.style.background = '#4285f4';
       }, 2000);
       
     } else {
       console.log('Open Single Listing: Share input not found');
       button.textContent = 'No Link';
       button.style.background = '#red';
       setTimeout(() => {
         button.textContent = originalText;
         button.style.background = '#4285f4';
       }, 2000);
     }
   }, 1500);
  }
  
  // Cache to reduce repeated checks
  let lastModeCheck = { time: 0, result: null, url: '' };
  let lastHeadingCheck = { time: 0, result: null, url: '' };
  
  // Simple injection function
  function injectButton() {
    // Check if Open Single Listing is enabled in settings
    if (!settings.openSingleListing) {
      return;
    }
    
    // Don't inject if button already exists
    if (document.getElementById('open-single-listing-btn')) {
      return;
    }
    
    // Cache heading checks for 5 seconds
    const currentUrl = window.location.href;
    const now = Date.now();
    
    let heading;
    if (lastHeadingCheck.url === currentUrl && (now - lastHeadingCheck.time) < 5000) {
      heading = lastHeadingCheck.result;
    } else {
      heading = findBusinessHeading();
      lastHeadingCheck = { time: now, result: heading, url: currentUrl };
    }
    
    if (!heading) {
      return;
    }
    
    // Cache mode checks for 10 seconds
    let isMultipleMode;
    if (lastModeCheck.url === currentUrl && (now - lastModeCheck.time) < 10000) {
      isMultipleMode = lastModeCheck.result;
    } else {
      isMultipleMode = isMultipleListingsMode();
      lastModeCheck = { time: now, result: isMultipleMode, url: currentUrl };
    }
    
    // Only inject if we're in multiple listings mode
    if (!isMultipleMode) {
      return; // Remove console.log to reduce spam
    }
    
    console.log('Open Single Listing: Injecting button');
   
   const button = createOpenButton();
   
   // Simple injection - just add after the heading
   const parent = heading.parentElement;
   if (parent) {
     // Try to add to existing button container first
     const existingContainer = parent.querySelector('.gmb-button-container');
     if (existingContainer) {
       existingContainer.appendChild(button);
       console.log('Open Single Listing: Added to existing container');
     } else {
       // Create simple container
       const container = document.createElement('div');
       container.style.cssText = 'margin-top: 8px;';
       container.appendChild(button);
       parent.appendChild(container);
       console.log('Open Single Listing: Created new container');
     }
   }
  }
  
  // Simple observer
  function startWatching() {
    console.log('Open Single Listing: Starting observer');
    
    const observer = new MutationObserver(() => {
      injectButton();
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    // Also check immediately
    injectButton();
    
    // Check periodically
    setInterval(injectButton, 3000);
  }
  
  // Initialize when the script loads
  setTimeout(() => {
    // Load settings first, then start watching
    loadSettings().then(() => {
      console.log('Open Single Listing: Initializing...');
      startWatching();
    });
  }, 2000);
} 