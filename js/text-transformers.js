// Text Transformers - Universal text processing with rich editor support
// Based on proven STM extension architecture with context management

console.log('SEO Time Machines: Text Transformers loading...');

class TextTransformers {
    
    // Context validation using centralized manager
    static isContextValid() {
        return typeof window.isExtensionContextValid === 'function' ? 
               window.isExtensionContextValid() : 
               !!(chrome && chrome.storage && chrome.storage.local);
    }
    
    // Safe storage operations using context manager
    static async safeStorageGet(keys) {
        if (typeof window.safeStorageGet === 'function') {
            return await window.safeStorageGet(keys);
        }
        
        // Fallback if context manager not available
        try {
            if (!this.isContextValid()) return {};
            return await chrome.storage.local.get(keys);
        } catch (error) {
            console.warn('SEO Time Machines: Storage get error:', error);
            return {};
        }
    }
    
    static async safeStorageSet(items) {
        if (typeof window.safeStorageSet === 'function') {
            return await window.safeStorageSet(items);
        }
        
        // Fallback if context manager not available
        try {
            if (!this.isContextValid()) return false;
            await chrome.storage.local.set(items);
            return true;
        } catch (error) {
            console.warn('SEO Time Machines: Storage set error:', error);
            return false;
        }
    }
    
    // Bulletproof text detection: selected text OR clipboard (always attempts clipboard)
    static async detectAndExtractText() {
        let textInfo = {
            text: '',
            source: 'none',
            element: null,
            editorType: 'unknown',
            canPaste: false
        };
        
        // Strategy 1: Check for focused INPUT/TEXTAREA (WordPress priority)
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
            const start = activeElement.selectionStart;
            const end = activeElement.selectionEnd;
            
            // Priority 1: Selected text in focused input/textarea
            if (start !== null && end !== null && start !== end) {
                textInfo.text = activeElement.value.substring(start, end);
                textInfo.source = 'input_selection';
                textInfo.element = activeElement;
                textInfo.editorType = this.detectEditorType(activeElement);
                textInfo.canPaste = true;
                console.log('SEO Time Machines: Found selected text in focused input/textarea:', textInfo.text.substring(0, 50));
                return textInfo;
            }
            
            // Priority 2: ANY content in focused input/textarea (prevents TinyMCE interference)
            if (activeElement.value && activeElement.value.trim()) {
                textInfo.text = activeElement.value;
                textInfo.source = 'focused_input_content';
                textInfo.element = activeElement;
                textInfo.editorType = this.detectEditorType(activeElement);
                textInfo.canPaste = true;
                console.log('SEO Time Machines: Found content in focused input/textarea, preventing TinyMCE extraction:', activeElement.tagName, activeElement.id || activeElement.name);
                return textInfo;
            }
            
            // Priority 3: Empty focused input/textarea (still prevents TinyMCE, allows clipboard fallback)
            console.log('SEO Time Machines: Found focused but empty input/textarea, will skip TinyMCE and try clipboard:', activeElement.tagName, activeElement.id || activeElement.name);
            textInfo.element = activeElement;
            textInfo.editorType = this.detectEditorType(activeElement);
            textInfo.canPaste = true;
            textInfo.source = 'focused_empty_input';
            // Don't return yet - let it try clipboard, but mark that we have a focused input
        }
        
        // Strategy 1.5: WordPress Visual Editor specific detection
        const wpEditor = this.detectWordPressEditor();
        if (wpEditor && wpEditor.element) {
            const start = wpEditor.element.selectionStart;
            const end = wpEditor.element.selectionEnd;
            if (start !== null && end !== null && start !== end) {
                textInfo.text = wpEditor.element.value.substring(start, end);
                textInfo.source = 'wordpress_selection';
                textInfo.element = wpEditor.element;
                textInfo.editorType = 'wordpress_visual';
                textInfo.canPaste = true;
                return textInfo;
            } else if (wpEditor.element.value && wpEditor.element.value.trim()) {
                // If no selection but WordPress editor has content, use all content
                textInfo.text = wpEditor.element.value;
                textInfo.source = 'wordpress_content';
                textInfo.element = wpEditor.element;
                textInfo.editorType = 'wordpress_visual';
                textInfo.canPaste = true;
                return textInfo;
            }
        }
        
        // Strategy 1.75: Enhanced TinyMCE iframe editor detection with timing
        // FOCUS VALIDATION: Only proceed with TinyMCE if no regular input/textarea is focused
        const currentlyFocusedElement = document.activeElement;
        const isFocusedInputOrTextarea = currentlyFocusedElement && 
            (currentlyFocusedElement.tagName === 'INPUT' || currentlyFocusedElement.tagName === 'TEXTAREA') &&
            !currentlyFocusedElement.classList.contains('wp-editor-area'); // Allow WordPress textarea
            
        const hasEarlyFocusDetection = textInfo.source === 'focused_empty_input';
        
        if (isFocusedInputOrTextarea || hasEarlyFocusDetection) {
            console.log('SEO Time Machines: Skipping TinyMCE detection - regular input/textarea is focused:', {
                tagName: currentlyFocusedElement?.tagName,
                id: currentlyFocusedElement?.id,
                name: currentlyFocusedElement?.name,
                className: currentlyFocusedElement?.className,
                hasEarlyDetection: hasEarlyFocusDetection
            });
            // Skip TinyMCE detection and go to Strategy 2 (window selection) or clipboard
        } else {
            let tinyMceEditor = this.detectTinyMCEEditor();
            if (!tinyMceEditor) {
                // Try with retry mechanism for better timing
                tinyMceEditor = await this.detectTinyMCEEditorWithRetry(2, 300);
            }
            
            if (tinyMceEditor && tinyMceEditor.body) {
            console.log('SEO Time Machines: Found TinyMCE editor, checking for selection...', {
                bodyId: tinyMceEditor.bodyId,
                dataId: tinyMceEditor.dataId,
                type: tinyMceEditor.type
            });
            
            // Get the iframe window and selection
            const iframeWindow = tinyMceEditor.iframe.contentWindow;
            const iframeSelection = iframeWindow ? iframeWindow.getSelection() : null;
            
            if (iframeSelection && iframeSelection.toString().trim()) {
                // Selected text in TinyMCE
                textInfo.text = iframeSelection.toString();
                textInfo.source = 'tinymce_selection';
                textInfo.element = tinyMceEditor.body;
                textInfo.editorType = 'tinymce';
                textInfo.canPaste = true;
                textInfo.tinyMceEditor = tinyMceEditor;
                console.log('SEO Time Machines: Found selected text in TinyMCE editor:', textInfo.text.substring(0, 50));
                return textInfo;
            } else if (tinyMceEditor.editor && typeof tinyMceEditor.editor.getContent === 'function') {
                // Use TinyMCE API to get content
                try {
                    const content = tinyMceEditor.editor.getContent({ format: 'text' });
                    if (content && content.trim()) {
                        textInfo.text = content;
                        textInfo.source = 'tinymce_api_content';
                        textInfo.element = tinyMceEditor.body;
                        textInfo.editorType = 'tinymce';
                        textInfo.canPaste = true;
                        textInfo.tinyMceEditor = tinyMceEditor;
                        console.log('SEO Time Machines: Retrieved content via TinyMCE API:', content.substring(0, 50));
                        return textInfo;
                    }
                } catch (error) {
                    console.log('SEO Time Machines: TinyMCE API content retrieval failed:', error);
                }
            }
            
            // Enhanced DOM text extraction for TinyMCE
            if (tinyMceEditor.body) {
                try {
                    console.log('SEO Time Machines: Attempting enhanced TinyMCE DOM extraction...');
                    
                    // Method 1: Direct text content
                    let textContent = tinyMceEditor.body.textContent || tinyMceEditor.body.innerText || '';
                    
                    // Method 2: If no direct text, try extracting from paragraphs
                    if (!textContent.trim()) {
                        const paragraphs = tinyMceEditor.body.querySelectorAll('p');
                        if (paragraphs.length > 0) {
                            textContent = Array.from(paragraphs)
                                .map(p => p.textContent || p.innerText || '')
                                .filter(text => text.trim())
                                .join('\n');
                            console.log('SEO Time Machines: Extracted text from paragraphs:', textContent.substring(0, 50));
                        }
                    }
                    
                    // Method 3: If still no text, try all text nodes
                    if (!textContent.trim()) {
                        const walker = tinyMceEditor.iframe.contentDocument.createTreeWalker(
                            tinyMceEditor.body,
                            NodeFilter.SHOW_TEXT,
                            null,
                            false
                        );
                        const textNodes = [];
                        let node;
                        while (node = walker.nextNode()) {
                            if (node.nodeValue && node.nodeValue.trim()) {
                                textNodes.push(node.nodeValue.trim());
                            }
                        }
                        textContent = textNodes.join(' ');
                        console.log('SEO Time Machines: Extracted text from text nodes:', textContent.substring(0, 50));
                    }
                    
                    if (textContent && textContent.trim()) {
                        textInfo.text = textContent;
                        textInfo.source = 'tinymce_enhanced_dom';
                        textInfo.element = tinyMceEditor.body;
                        textInfo.editorType = 'tinymce';
                        textInfo.canPaste = true;
                        textInfo.tinyMceEditor = tinyMceEditor;
                        console.log('SEO Time Machines: Successfully retrieved TinyMCE content via enhanced DOM extraction');
                        return textInfo;
                    } else {
                        console.log('SEO Time Machines: No text content found in TinyMCE body');
                    }
                } catch (error) {
                    console.log('SEO Time Machines: Enhanced TinyMCE DOM extraction failed:', error);
                }
            }
        }
        } // End of focus validation conditional block
        
        // Strategy 2: Check window selection (works for any text on page)
        const selection = window.getSelection();
        if (selection && selection.toString().trim()) {
            textInfo.text = selection.toString();
            textInfo.source = 'window_selection';
            textInfo.canPaste = this.canPasteToSelection(selection);
            return textInfo;
        }
        
        // Strategy 3: ALWAYS attempt clipboard (bulletproof with multiple methods)
        textInfo = await this.bulletproofClipboardRead(textInfo);
        
        return textInfo;
    }
    
    // Bulletproof clipboard reading with multiple fallback methods
    static async checkClipboardPermissionsPolicy() {
        try {
            // Cache the result to avoid repeated checks
            if (typeof this._clipboardPermissions !== 'undefined') {
                return this._clipboardPermissions;
            }
            
            console.log('SEO Time Machines: Detecting clipboard permissions...');
            
            // Method 1: Check permissions API if available
            if (navigator.permissions) {
                try {
                    const permission = await navigator.permissions.query({name: 'clipboard-read'});
                    if (permission.state === 'denied') {
                        console.log('SEO Time Machines: Clipboard permissions explicitly denied');
                        this._clipboardPermissions = { blocked: true, modern: false, legacy: true, reason: 'permissions_denied' };
                        return this._clipboardPermissions;
                    }
                } catch (permError) {
                    console.log('SEO Time Machines: Permissions API check failed:', permError);
                }
            }
            
            // Method 2: Check for permissions policy blocking clipboard access
            if (document.featurePolicy && document.featurePolicy.features().includes('clipboard-read')) {
                const allowedOrigins = document.featurePolicy.getAllowlistForFeature('clipboard-read');
                if (allowedOrigins.length === 0 || !allowedOrigins.includes('*') && !allowedOrigins.includes(window.location.origin)) {
                    console.log('SEO Time Machines: Clipboard access blocked by permissions policy');
                    this._clipboardPermissions = { blocked: true, modern: false, legacy: true, reason: 'permissions_policy' };
                    return this._clipboardPermissions;
                }
            }
            
            // Method 3: Test clipboard API availability with a quick write attempt
            try {
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    await navigator.clipboard.writeText('');
                    console.log('SEO Time Machines: Modern clipboard API available');
                    this._clipboardPermissions = { blocked: false, modern: true, legacy: true, reason: 'available' };
                    return this._clipboardPermissions;
                }
            } catch (clipboardError) {
                console.log('SEO Time Machines: Modern clipboard API blocked:', clipboardError.message);
                
                // Check for specific permissions policy errors
                if (clipboardError.message.includes('permissions policy') || 
                    clipboardError.message.includes('Permissions policy') ||
                    clipboardError.message.includes('blocked because of a permissions policy')) {
                    console.log('SEO Time Machines: Permissions policy blocks modern clipboard API');
                    this._clipboardPermissions = { blocked: true, modern: false, legacy: true, reason: 'permissions_policy_header' };
                    return this._clipboardPermissions;
                }
                
                // Other clipboard errors (like user activation required)
                this._clipboardPermissions = { blocked: false, modern: false, legacy: true, reason: 'clipboard_error' };
                return this._clipboardPermissions;
            }
            
            // Method 4: Check if navigator.clipboard exists at all
            if (!navigator.clipboard) {
                console.log('SEO Time Machines: Navigator clipboard not available');
                this._clipboardPermissions = { blocked: false, modern: false, legacy: true, reason: 'not_available' };
                return this._clipboardPermissions;
            }
            
            // Default fallback - assume modern is blocked, legacy works
            console.log('SEO Time Machines: Defaulting to legacy clipboard methods');
            this._clipboardPermissions = { blocked: false, modern: false, legacy: true, reason: 'default_fallback' };
            return this._clipboardPermissions;
            
        } catch (error) {
            console.log('SEO Time Machines: Error detecting clipboard permissions:', error);
            // When in doubt, use legacy methods
            this._clipboardPermissions = { blocked: false, modern: false, legacy: true, reason: 'detection_error' };
            return this._clipboardPermissions;
        }
    }

    static async bulletproofClipboardRead(textInfo) {
        console.log('SEO Time Machines: Attempting bulletproof clipboard access...');
        
        // Quick permissions policy check to avoid repeated failures
        const policyCheck = this.checkClipboardPermissionsPolicy();
        if (policyCheck.blocked) {
            console.log(`SEO Time Machines: Skipping Navigator API due to ${policyCheck.reason}`);
            // Skip Navigator API attempts and go straight to execCommand
        } else {
            // Method 1: Standard Navigator Clipboard API (single attempt if policy allows)
            try {
                const clipboardText = await navigator.clipboard.readText();
                if (clipboardText && clipboardText.trim()) {
                    textInfo.text = clipboardText;
                    textInfo.source = 'clipboard_navigator';
                    textInfo.canPaste = true;
                    console.log('SEO Time Machines: Clipboard read successful via Navigator API');
                    return textInfo;
                }
            } catch (clipboardError) {
                console.log('SEO Time Machines: Navigator clipboard failed:', clipboardError);
                // Don't retry if it's a permissions policy error
                if (clipboardError.message && clipboardError.message.includes('permissions policy')) {
                    console.log('SEO Time Machines: Permissions policy detected, skipping Navigator retries');
                }
            }
        }
        
        // Method 2: execCommand clipboard read with optimized strategies
        const execStrategies = [
            { tag: 'textarea', style: 'position: fixed; top: -1000px; left: -1000px; opacity: 0; pointer-events: none;' },
            { tag: 'input', type: 'text', style: 'position: fixed; top: -2000px; left: -2000px; opacity: 0; pointer-events: none; z-index: -1;' }
        ];
        
        for (const strategy of execStrategies) {
            try {
                const tempElement = document.createElement(strategy.tag);
                tempElement.style.cssText = strategy.style;
                
                if (strategy.type) {
                    tempElement.type = strategy.type;
                }
                
                document.body.appendChild(tempElement);
                
                // Focus and attempt paste
                tempElement.focus();
                if (tempElement.select) tempElement.select();
                
                const pasteSuccess = document.execCommand('paste');
                const content = tempElement.value || '';
                
                document.body.removeChild(tempElement);
                
                if (pasteSuccess && content && content.trim()) {
                    textInfo.text = content;
                    textInfo.source = 'clipboard_execcommand';
                    textInfo.canPaste = true;
                    console.log(`SEO Time Machines: Clipboard read successful via execCommand (${strategy.tag})`);
                    return textInfo;
                }
            } catch (strategyError) {
                console.log(`SEO Time Machines: execCommand strategy ${strategy.tag} failed:`, strategyError);
            }
        }
        
        // Method 3: Permission-based clipboard access (only if not blocked by policy)
        if (!policyCheck.blocked) {
            try {
                if (navigator.permissions) {
                    const permission = await navigator.permissions.query({name: 'clipboard-read'});
                    console.log('SEO Time Machines: Clipboard permission state:', permission.state);
                    
                    if (permission.state === 'granted') {
                        const clipboardText = await navigator.clipboard.readText();
                        if (clipboardText && clipboardText.trim()) {
                            textInfo.text = clipboardText;
                            textInfo.source = 'clipboard_permission';
                            textInfo.canPaste = true;
                            console.log('SEO Time Machines: Clipboard read successful after permission check');
                            return textInfo;
                        }
                    }
                }
            } catch (permissionError) {
                console.log('SEO Time Machines: Permission check failed:', permissionError);
            }
        }
        
        // If we reach here, no text was found anywhere
        console.log('SEO Time Machines: All clipboard reading methods exhausted - no text found');
        return textInfo;
    }



    

    
    // Clear cached permissions (useful for testing or context changes)
    static clearClipboardPermissionsCache() {
        delete this._clipboardPermissions;
        console.log('SEO Time Machines: Clipboard permissions cache cleared');
    }

    // Bulletproof clipboard writing with multiple fallback methods
    static async bulletproofClipboardWrite(text) {
        console.log('SEO Time Machines: Attempting bulletproof clipboard write...');
        
        if (!text || typeof text !== 'string') {
            console.warn('SEO Time Machines: Invalid text provided for clipboard write');
            return false;
        }
        
        // STEP 1: Detect clipboard permissions to choose the right strategy
        const permissions = await this.checkClipboardPermissionsPolicy();
        console.log(`SEO Time Machines: Clipboard permissions - Modern: ${permissions.modern}, Legacy: ${permissions.legacy}, Reason: ${permissions.reason}`);
        
        // STEP 2: Choose strategy based on permissions
        if (permissions.blocked || !permissions.modern || permissions.reason === 'permissions_policy' || permissions.reason === 'permissions_policy_header') {
            // Permissions policy blocks modern API - use execCommand FIRST
            console.log('SEO Time Machines: Using legacy-first strategy due to permissions restrictions');
            return await this._legacyClipboardWrite(text);
        } else {
            // Modern API should work - try it first with fallback
            console.log('SEO Time Machines: Using modern-first strategy');
            return await this._modernClipboardWrite(text);
        }
    }
    
    // Modern clipboard API approach (when not blocked by permissions policy)
    static async _modernClipboardWrite(text) {
        // Method 1: Standard Navigator Clipboard API (multiple attempts)
        for (let attempt = 0; attempt < 3; attempt++) {
            try {
                await new Promise(resolve => setTimeout(resolve, 25 * attempt)); // Small delay between attempts
                await navigator.clipboard.writeText(text);
                console.log(`SEO Time Machines: Clipboard write successful via Navigator API (attempt ${attempt + 1})`);
                return true;
            } catch (clipboardError) {
                console.log(`SEO Time Machines: Navigator clipboard write attempt ${attempt + 1} failed:`, clipboardError);
                
                // If we get a permissions policy error, switch to legacy immediately
                if (clipboardError.message.includes('permissions policy')) {
                    console.log('SEO Time Machines: Permissions policy detected, switching to legacy method');
                    return await this._legacyClipboardWrite(text);
                }
            }
        }
        
        // Method 2: Fallback to legacy if modern API failed
        console.log('SEO Time Machines: Modern API failed, falling back to legacy methods');
        return await this._legacyClipboardWrite(text);
    }
    
    // Legacy execCommand approach (works even with permissions policy restrictions)
    static async _legacyClipboardWrite(text) {
        console.log('SEO Time Machines: Using legacy clipboard write methods');
        
        // Method 1: Enhanced execCommand with multiple strategies and better timing
        const execStrategies = [
            { 
                tag: 'textarea', 
                style: 'position: fixed !important; top: -1000px !important; left: -1000px !important; opacity: 0 !important; pointer-events: none !important; z-index: -9999 !important;',
                setup: (elem) => { elem.value = text; }
            },
            { 
                tag: 'input', 
                type: 'text',
                style: 'position: fixed !important; top: -2000px !important; left: -2000px !important; opacity: 0 !important; pointer-events: none !important; z-index: -9999 !important;',
                setup: (elem) => { elem.type = 'text'; elem.value = text; }
            },
            { 
                tag: 'div', 
                contentEditable: true,
                style: 'position: absolute !important; top: -3000px !important; left: -3000px !important; opacity: 0 !important; pointer-events: none !important; z-index: -9999 !important;',
                setup: (elem) => { elem.contentEditable = 'true'; elem.textContent = text; }
            }
        ];
        
        for (const strategy of execStrategies) {
            try {
                console.log(`SEO Time Machines: Trying execCommand strategy: ${strategy.tag}`);
                
                const tempElement = document.createElement(strategy.tag);
                tempElement.style.cssText = strategy.style;
                tempElement.setAttribute('tabindex', '-1');
                tempElement.setAttribute('aria-hidden', 'true');
                
                // Setup element content
                strategy.setup(tempElement);
                
                document.body.appendChild(tempElement);
                
                // Enhanced focus and selection with better timing
                await this._performCopySequence(tempElement, strategy.contentEditable);
                
                document.body.removeChild(tempElement);
                
                console.log(`SEO Time Machines: execCommand strategy ${strategy.tag} completed`);
                return true; // Assume success for execCommand
                
            } catch (strategyError) {
                console.log(`SEO Time Machines: execCommand strategy ${strategy.tag} failed:`, strategyError);
                continue;
            }
        }
        

        
        // Method 3: Aggressive multi-attempt execCommand
        try {
            console.log('SEO Time Machines: Attempting aggressive multi-attempt execCommand...');
            
            for (let attempt = 0; attempt < 3; attempt++) {
                const tempTextarea = document.createElement('textarea');
                tempTextarea.style.cssText = `
                    position: fixed !important;
                    top: ${-5000 - (attempt * 100)}px !important;
                    left: ${-5000 - (attempt * 100)}px !important;
                    width: 1px !important;
                    height: 1px !important;
                    opacity: 0 !important;
                    pointer-events: none !important;
                    z-index: ${-9999 - attempt} !important;
                    border: none !important;
                    outline: none !important;
                    background: transparent !important;
                `;
                tempTextarea.setAttribute('tabindex', '-1');
                tempTextarea.setAttribute('aria-hidden', 'true');
                tempTextarea.value = text;
                
                document.body.appendChild(tempTextarea);
                
                // Wait for element to be ready
                await new Promise(resolve => setTimeout(resolve, 10 + (attempt * 5)));
                
                const copySuccess = await this._performCopySequence(tempTextarea, false);
                document.body.removeChild(tempTextarea);
                
                if (copySuccess) {
                    console.log(`SEO Time Machines: Aggressive execCommand successful on attempt ${attempt + 1}`);
                    return true;
                }
            }
        } catch (aggressiveError) {
            console.log('SEO Time Machines: Aggressive execCommand failed:', aggressiveError);
        }
        
        // Method 4: Keyboard simulation as absolute last resort
        try {
            console.log('SEO Time Machines: Attempting keyboard simulation copy...');
            
            const tempTextarea = document.createElement('textarea');
            tempTextarea.style.cssText = 'position: fixed; top: -1000px; left: -1000px; opacity: 0; z-index: -1;';
            tempTextarea.value = text;
            document.body.appendChild(tempTextarea);
            
            tempTextarea.focus();
            tempTextarea.select();
            
            const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
            const copyEvent = new KeyboardEvent('keydown', {
                key: 'c',
                code: 'KeyC',
                ctrlKey: !isMac,
                metaKey: isMac,
                bubbles: true,
                cancelable: true
            });
            
            tempTextarea.dispatchEvent(copyEvent);
            
            // Also dispatch copy event
            const copyClipboardEvent = new ClipboardEvent('copy', {
                bubbles: true,
                cancelable: true
            });
            tempTextarea.dispatchEvent(copyClipboardEvent);
            
            document.body.removeChild(tempTextarea);
            
            console.log('SEO Time Machines: Keyboard simulation copy attempted');
            return true; // Assume success since we can't reliably detect it
        } catch (keyboardError) {
            console.log('SEO Time Machines: Keyboard simulation copy failed:', keyboardError);
        }
        
        console.log('SEO Time Machines: All legacy clipboard writing methods attempted');
        return false;
    }
    
    // Enhanced copy sequence with better timing and error handling
    static async _performCopySequence(element, isContentEditable) {
        try {
            // Step 1: Focus the element
            element.focus();
            
            // Step 2: Wait for focus to settle
            await new Promise(resolve => setTimeout(resolve, 10));
            
            // Step 3: Select content based on element type
            if (isContentEditable) {
                const range = document.createRange();
                range.selectNodeContents(element);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
            } else if (element.select) {
                element.select();
                element.setSelectionRange(0, element.value.length);
            }
            
            // Step 4: Wait for selection to settle
            await new Promise(resolve => setTimeout(resolve, 5));
            
            // Step 5: Execute copy command
            const copySuccess = document.execCommand('copy');
            
            // Step 6: Additional wait for copy to complete
            await new Promise(resolve => setTimeout(resolve, 5));
            
            return copySuccess;
        } catch (error) {
            console.log('SEO Time Machines: Copy sequence failed:', error);
            return false;
        }
    }
    

    
    
    // Check if we can paste to current selection
    static canPasteToSelection(selection) {
        if (!selection || selection.rangeCount === 0) return false;
        
        const range = selection.getRangeAt(0);
        const container = range.commonAncestorContainer;
        
        // Check if selection is within an editable element
        let element = container.nodeType === Node.TEXT_NODE ? container.parentElement : container;
        
        while (element) {
            if (element.contentEditable === 'true' || 
                element.tagName === 'INPUT' || 
                element.tagName === 'TEXTAREA') {
                return true;
            }
            element = element.parentElement;
        }
        
        return false;
    }
    
    // Execute the specified transformation on detected text (bulletproof mode)
    static async executeTransformation(transformType) {
        try {
            console.log(`SEO Time Machines: Executing ${transformType} transformation`);
            
            // STEP 1: Use universal text detection (selection first, then clipboard fallback)
            const textInfo = await this.detectAndExtractText();
            
            // STEP 2: If no text found anywhere, try one more aggressive clipboard attempt
            if (!textInfo.text || textInfo.text.trim() === '') {
                console.log('SEO Time Machines: No text found automatically, attempting forced clipboard mode...');
                
                const forcedTextInfo = await this.bulletproofClipboardRead({ text: '', source: 'none', element: null, editorType: 'unknown', canPaste: false });
                if (forcedTextInfo.text && forcedTextInfo.text.trim()) {
                    textInfo.text = forcedTextInfo.text;
                    textInfo.source = forcedTextInfo.source;
                    textInfo.canPaste = true; // Assume we can paste since we got clipboard content
                    console.log('SEO Time Machines: Forced clipboard read successful');
                } else {
                    console.log('SEO Time Machines: No text available for transformation');
                    this.showNotification('No Text Found - Copy text first, then try again');
                    return { 
                        success: false, 
                        message: 'No text found. Copy text to clipboard first, then run transformation again.' 
                    };
                }
            }
            
            console.log(`SEO Time Machines: Text found from ${textInfo.source} (${textInfo.editorType}): "${textInfo.text.substring(0, 50)}${textInfo.text.length > 50 ? '...' : ''}"`);
            
            // STEP 3: Transform the text
            const transformedText = this.transform(textInfo.text, transformType);
            
            if (transformedText === textInfo.text) {
                console.log('SEO Time Machines: Text unchanged after transformation');
                this.showNotification('Text Unchanged');
                return { success: true, message: 'Text unchanged' };
            }
            
            // STEP 4: ALWAYS write transformed text to clipboard (bulletproof guarantee)
            console.log('SEO Time Machines: Writing transformed text to clipboard...');
            const clipboardWriteSuccess = await this.bulletproofClipboardWrite(transformedText);
            
            if (!clipboardWriteSuccess) {
                console.warn('SEO Time Machines: Failed to write to clipboard, but transformation succeeded');
                this.showNotification('Transform Complete (Manual Copy Required)');
                return { 
                    success: true, 
                    message: `${transformType} transformation complete. Please copy result manually.`,
                    transformedText: transformedText
                };
            }
            
            console.log('SEO Time Machines: Transformed text successfully written to clipboard');
            
            // STEP 5: OPTIONAL auto-paste (only if we have a pasteable target and setting enabled)
            let pasteAttempted = false;
            let pasteSuccessful = false;
            
            try {
                const settings = await this.safeStorageGet(['gmbExtractorSettings']);
                const mainSettings = settings.gmbExtractorSettings || {};
                const autoPaste = mainSettings.textTransformersAutoPaste !== false; // Default true
                
                // Only attempt paste if we have selected text or a known pasteable element
                if (autoPaste && (textInfo.canPaste || textInfo.element)) {
                    console.log('SEO Time Machines: Attempting auto-paste...');
                    pasteAttempted = true;
                    pasteSuccessful = await this.executeSmartPaste(transformedText, textInfo);
                } else {
                    console.log('SEO Time Machines: Skipping auto-paste - no pasteable target or disabled');
                }
            } catch (settingsError) {
                console.log('SEO Time Machines: Settings error, skipping auto-paste:', settingsError);
            }
            
            // STEP 6: Report success - clipboard is guaranteed to have transformed text
            if (pasteAttempted && pasteSuccessful) {
                console.log(`SEO Time Machines: ${transformType} transformation completed with successful auto-paste`);
                this.showNotification('Text Transformed & Pasted');
                return { success: true, message: `${transformType} transformation applied and pasted` };
            } else if (pasteAttempted && !pasteSuccessful) {
                console.log(`SEO Time Machines: ${transformType} transformation completed - auto-paste failed but text is in clipboard`);
                this.showNotification('Text Transformed (Use Cmd+V to Paste)');
                return { success: true, message: `${transformType} transformation copied to clipboard - use Cmd+V to paste` };
            } else {
                console.log(`SEO Time Machines: ${transformType} transformation completed - text copied to clipboard`);
                this.showNotification('Text Transformed (In Clipboard)');
                return { success: true, message: `${transformType} transformation copied to clipboard` };
            }
            
        } catch (error) {
            console.error(`SEO Time Machines: Error in ${transformType} transformation:`, error);
            return { error: error.message };
        }
    }
    
    // Transform text based on type
    static transform(text, transformType) {
        switch (transformType) {
            case 'capitalCase':
                return this.toCapitalCase(text);
            case 'lowerCase':
                return text.toLowerCase();
            case 'upperCase':
                return text.toUpperCase();
            case 'sentenceCase':
                return this.toSentenceCase(text);
            case 'slugify':
                return this.slugify(text);
            case 'trimToPage':
                return this.trimToPage(text);
            case 'sortAlphabetically':
                return this.sortAlphabetically(text);
            case 'removeEmptyLines':
                return this.removeEmptyLines(text);
            case 'removeDuplicateLines':
                return this.removeDuplicateLines(text);
            default:
                return text;
        }
    }
    
    // Convert to Capital Case (Title Case)
    static toCapitalCase(text) {
        return text.replace(/\w\S*/g, (txt) => {
            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
        });
    }
    
    // Convert to Sentence case
    static toSentenceCase(text) {
        return text.toLowerCase().replace(/(^\s*\w|[\.\!\?]\s*\w)/g, (c) => {
            return c.toUpperCase();
        });
    }
    
    // Convert to URL-friendly slug (toggle function)
    static slugify(text) {
        if (!text || typeof text !== 'string') {
            return text;
        }
        
        // Split by line breaks to preserve them
        const lines = text.split('\n');
        
        // Process each line individually with toggle logic
        const processedLines = lines.map(line => {
            const trimmedLine = line.trim();
            if (!trimmedLine) return trimmedLine;
            
            // Check if line is already slugified
            if (this.isSlugified(trimmedLine)) {
                // Un-slugify: convert dashes to spaces
                return trimmedLine.replace(/-/g, ' ');
            } else {
                // Slugify: convert to URL-friendly format
                return trimmedLine
                    .toLowerCase()
                    .replace(/[^\w\s-]/g, '') // Remove special characters
                    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
                    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
            }
        });
        
        // Join lines back together with preserved line breaks
        return processedLines.join('\n');
    }
    
    // Helper method to detect if text is already slugified
    static isSlugified(text) {
        if (!text || typeof text !== 'string') {
            return false;
        }
        
        const trimmed = text.trim();
        if (!trimmed) return false;
        
        // Check if text looks like a slug:
        // 1. Contains dashes
        // 2. No spaces (or very minimal spaces)
        // 3. Is lowercase or mostly lowercase
        // 4. Only contains word characters, dashes, and minimal special chars
        const hasDashes = trimmed.includes('-');
        const hasSpaces = trimmed.includes(' ');
        const isLowercase = trimmed === trimmed.toLowerCase();
        const onlyValidChars = /^[a-z0-9\-]+$/i.test(trimmed);
        
        // Consider it slugified if:
        // - Has dashes AND no spaces AND is lowercase AND only valid chars
        // - OR has dashes AND minimal spaces (less than 20% of chars) AND is lowercase
        const spaceRatio = hasSpaces ? (trimmed.split(' ').length - 1) / trimmed.length : 0;
        
        return hasDashes && isLowercase && onlyValidChars && !hasSpaces ||
               hasDashes && isLowercase && spaceRatio < 0.2;
    }
    
    // Trim to last page segment (extract final part after last separator)
    static trimToPage(text) {
        if (!text || typeof text !== 'string') {
            return text;
        }
        
        const trimmed = text.trim();
        if (!trimmed) {
            return trimmed;
        }
        
        // Split by forward slash and get the last non-empty segment
        const segments = trimmed.split('/');
        const lastSegment = segments[segments.length - 1];
        
        return lastSegment || trimmed;
    }
    
    // Sort lines alphabetically
    static sortAlphabetically(text) {
        if (!text || typeof text !== 'string') {
            return text;
        }
        
        const trimmed = text.trim();
        if (!trimmed) {
            return trimmed;
        }
        
        // Split text into lines
        const lines = text.split('\n');
        
        // Sort lines alphabetically (case-insensitive)
        const sortedLines = lines.sort((a, b) => {
            return a.localeCompare(b, undefined, { sensitivity: 'base' });
        });
        
        // Join lines back together
        return sortedLines.join('\n');
    }
    
    // Remove empty lines from text
    static removeEmptyLines(text) {
        if (!text || typeof text !== 'string') {
            return text;
        }
        
        const trimmed = text.trim();
        if (!trimmed) {
            return trimmed;
        }
        
        // Split text into lines
        const lines = text.split('\n');
        
        // Filter out empty lines (lines containing only whitespace)
        const nonEmptyLines = lines.filter(line => line.trim() !== '');
        
        // Join lines back together
        return nonEmptyLines.join('\n');
    }
    
    // Remove duplicate lines from text
    static removeDuplicateLines(text) {
        if (!text || typeof text !== 'string') {
            return text;
        }
        
        const trimmed = text.trim();
        if (!trimmed) {
            return trimmed;
        }
        
        // Split text into lines
        const lines = text.split('\n');
        
        // Use Set to track seen lines and filter duplicates
        const seen = new Set();
        const uniqueLines = [];
        
        for (const line of lines) {
            if (!seen.has(line)) {
                seen.add(line);
                uniqueLines.push(line);
            }
        }
        
        // Join lines back together
        return uniqueLines.join('\n');
    }
    

    
    // Enhanced notification with WordPress context awareness
    static showNotification(message, details = null) {
        try {
            // Remove any existing text transformer notifications
            document.querySelectorAll('.text-transformer-notification').forEach(notification => notification.remove());
            
            // Get selection position for better notification placement
            const selection = window.getSelection();
            let notificationX = 10;
            let notificationY = 10;
            
            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                const rect = range.getBoundingClientRect();
                
                // Position notification above the selection, centered horizontally
                notificationX = rect.left + (rect.width / 2);
                notificationY = rect.top - 60; // 60px above the selection for better visibility
                
                // Ensure notification stays within viewport bounds
                const notificationWidth = 250; // estimated notification width
                const notificationHeight = 50; // estimated notification height
                
                // Adjust horizontal position if too far right
                if (notificationX + notificationWidth / 2 > window.innerWidth - 10) {
                    notificationX = window.innerWidth - notificationWidth / 2 - 10;
                }
                // Adjust horizontal position if too far left
                if (notificationX - notificationWidth / 2 < 10) {
                    notificationX = notificationWidth / 2 + 10;
                }
                
                // If notification would appear above viewport, show it below selection instead
                if (notificationY - notificationHeight < 10) {
                    notificationY = rect.bottom + 25;
                }
            }
            
            // Create notification element with enhanced styling
            const notification = document.createElement('div');
            notification.className = 'text-transformer-notification';
            notification.style.cssText = `
                position: fixed;
                left: ${notificationX}px;
                top: ${notificationY}px;
                transform: translateX(-50%);
                z-index: 10000000;
                background: rgba(10, 10, 10, 0.95);
                color: #fff;
                padding: 10px 16px;
                border-radius: 8px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                font-size: 13px;
                font-weight: 500;
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
                backdrop-filter: blur(8px);
                border: 2px solid rgba(124, 58, 237, 0.5);
                pointer-events: none;
                animation: textTransformerFadeIn 0.3s ease-out;
                white-space: nowrap;
                max-width: 320px;
                text-align: center;
            `;
            
            // Add CSS animation if not already present
            if (!document.getElementById('text-transformer-notification-styles')) {
                const styles = document.createElement('style');
                styles.id = 'text-transformer-notification-styles';
                styles.textContent = `
                    @keyframes textTransformerFadeIn {
                        from { 
                            opacity: 0; 
                            transform: translateX(-50%) translateY(-10px) scale(0.9); 
                        }
                        to { 
                            opacity: 1; 
                            transform: translateX(-50%) translateY(0) scale(1); 
                        }
                    }
                    @keyframes textTransformerFadeOut {
                        from { 
                            opacity: 1; 
                            transform: translateX(-50%) translateY(0) scale(1); 
                        }
                        to { 
                            opacity: 0; 
                            transform: translateX(-50%) translateY(-10px) scale(0.9); 
                        }
                    }
                `;
                document.head.appendChild(styles);
            }
            
            // Enhanced notification content with WordPress and status indicators
            let icon = '●';
            let iconColor = '#7C3AED';
            
            // Check for WordPress context
            const isWordPress = document.body.classList.contains('wp-admin') || 
                               document.querySelector('.wp-admin') ||
                               document.querySelector('#wpadminbar') ||
                               document.querySelector('textarea.wp-editor-area');
            
            // Determine appropriate icon and color based on message and context
            if (message.includes('Transformed & Pasted')) {
                icon = isWordPress ? 'WP ✓' : '✓';
                iconColor = '#10B981'; // Green for complete success
            } else if (message.includes('Use Cmd+V') || message.includes('Use Ctrl+V')) {
                icon = isWordPress ? 'WP 📋' : '📋';
                iconColor = '#F59E0B'; // Orange for clipboard ready
            } else if (message.includes('In Clipboard')) {
                icon = isWordPress ? 'WP 📋' : '📋';
                iconColor = '#7C3AED'; // Purple for clipboard only
            } else if (message.includes('No Text Found')) {
                icon = '⚠';
                iconColor = '#EF4444'; // Red for error
            } else if (message.includes('Unchanged')) {
                icon = '=';
                iconColor = '#6B7280'; // Gray for no change
            } else if (message.includes('Manual Copy Required')) {
                icon = isWordPress ? 'WP ⚡' : '⚡';
                iconColor = '#F59E0B'; // Orange for manual action needed
            }
            
            // Create notification HTML with better formatting
            const notificationHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="color: ${iconColor}; font-size: 16px; font-weight: bold;">${icon}</span>
                    <span style="flex: 1;">${message}</span>
                </div>
            `;
            
            notification.innerHTML = notificationHTML;
            document.body.appendChild(notification);
            
            // Enhanced auto-remove with longer duration for important messages
            const duration = message.includes('No Text Found') || message.includes('Manual Copy Required') ? 2500 : 1500;
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'textTransformerFadeOut 0.3s ease-in';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, duration);
            
            console.log(`SEO Time Machines: Enhanced text transformer notification shown: ${message}`);
            return { success: true };
        } catch (error) {
            console.error('SEO Time Machines: Error showing enhanced text transformer notification:', error);
            return { error: error.message };
        }
    }
    
    // Universal paste system - works everywhere including WordPress/TinyMCE
    static async executeSmartPaste(transformedText, textInfo) {
        try {
            console.log(`SEO Time Machines: Attempting universal paste to ${textInfo.source}`);
            
            // STEP 1: Ensure transformed text is in clipboard using bulletproof method
            const clipboardSuccess = await this.bulletproofClipboardWrite(transformedText);
            if (!clipboardSuccess) {
                console.warn('SEO Time Machines: Failed to write to clipboard before paste attempt');
                // Continue anyway - maybe paste will work with existing clipboard content
            } else {
                console.log('SEO Time Machines: Transformed text confirmed in clipboard');
            }
            
            // STEP 2: PRIORITY - Check for focused input/textarea fields first
            const currentlyFocusedElement = document.activeElement;
            const isFocusedInputOrTextarea = currentlyFocusedElement && 
                (currentlyFocusedElement.tagName === 'INPUT' || currentlyFocusedElement.tagName === 'TEXTAREA') &&
                !currentlyFocusedElement.classList.contains('wp-editor-area'); // Allow WordPress textarea
                
            if (isFocusedInputOrTextarea && currentlyFocusedElement !== textInfo.element) {
                console.log('SEO Time Machines: Prioritizing currently focused input/textarea over TinyMCE:', {
                    tagName: currentlyFocusedElement.tagName,
                    id: currentlyFocusedElement.id,
                    name: currentlyFocusedElement.name
                });
                
                try {
                    const start = currentlyFocusedElement.selectionStart;
                    const end = currentlyFocusedElement.selectionEnd;
                    const value = currentlyFocusedElement.value;
                    
                    if (start !== null && end !== null) {
                        // Replace selected text or insert at cursor
                        currentlyFocusedElement.value = value.substring(0, start) + transformedText + value.substring(end);
                        currentlyFocusedElement.selectionStart = start + transformedText.length;
                        currentlyFocusedElement.selectionEnd = start + transformedText.length;
                        
                        // Trigger events for the focused element
                        const events = ['input', 'change', 'keyup', 'blur'];
                        events.forEach(eventType => {
                            currentlyFocusedElement.dispatchEvent(new Event(eventType, { bubbles: true }));
                        });
                        
                        console.log('SEO Time Machines: Successfully pasted to focused input/textarea');
                        return true;
                    }
                } catch (error) {
                    console.log('SEO Time Machines: Focused input/textarea paste failed, continuing with other methods:', error);
                }
            }
            
            // STEP 3: Enhanced iframe editor detection and paste (only if no focused input)
            const iframeEditor = this.detectTinyMCEEditor()?.iframe;
            if (iframeEditor && !isFocusedInputOrTextarea) {
                console.log('SEO Time Machines: Detected iframe editor, attempting iframe paste...');
                
                try {
                    const iframeDoc = iframeEditor.contentDocument;
                    const iframeWindow = iframeEditor.contentWindow;
                    
                    if (iframeDoc && iframeWindow) {
                        // Focus the iframe editor
                        iframeWindow.focus();
                        
                        // Try multiple iframe paste strategies
                        const iframePasteStrategies = [
                            () => iframeDoc.execCommand('paste'),
                            () => {
                                // Try pasting into active element within iframe
                                const activeEl = iframeDoc.activeElement;
                                if (activeEl) {
                                    activeEl.focus();
                                    return iframeDoc.execCommand('paste');
                                }
                                return false;
                            },
                            () => {
                                // Try finding and focusing contentEditable elements
                                const editables = iframeDoc.querySelectorAll('[contenteditable="true"], .mce-content-body, #tinymce');
                                for (const editable of editables) {
                                    try {
                                        editable.focus();
                                        if (iframeDoc.execCommand('paste')) return true;
                                    } catch (e) { continue; }
                                }
                                return false;
                            }
                        ];
                        
                        for (let i = 0; i < iframePasteStrategies.length; i++) {
                            try {
                                const success = iframePasteStrategies[i]();
                                if (success) {
                                    console.log(`SEO Time Machines: Iframe paste successful with strategy ${i + 1}`);
                                    return true;
                                }
                            } catch (error) {
                                console.log(`SEO Time Machines: Iframe paste strategy ${i + 1} failed:`, error);
                            }
                        }
                    }
                } catch (error) {
                    console.log('SEO Time Machines: Iframe paste failed, trying universal method:', error);
                }
            }
            
            // STEP 4: TinyMCE-specific paste handling (only if no focused input)
            if (textInfo.tinyMceEditor && !isFocusedInputOrTextarea) {
                console.log('SEO Time Machines: Attempting TinyMCE-specific paste...');
                
                try {
                    const tinyMceEditor = textInfo.tinyMceEditor;
                    const editor = tinyMceEditor.editor;
                    const iframeBody = tinyMceEditor.body;
                    
                    // Method 1: Use TinyMCE API if available
                    if (editor && typeof editor.setContent === 'function') {
                        try {
                            // Check if we have selected text to replace
                            const iframeWindow = tinyMceEditor.iframe.contentWindow;
                            const iframeSelection = iframeWindow ? iframeWindow.getSelection() : null;
                            
                            if (iframeSelection && iframeSelection.toString().trim()) {
                                // Replace selected content
                                editor.selection.setContent(transformedText);
                                console.log('SEO Time Machines: TinyMCE API paste - replaced selected content');
                            } else {
                                // Insert at cursor or replace all content
                                if (textInfo.source === 'tinymce_api_content' || textInfo.source === 'tinymce_dom_content') {
                                    // Replace all content
                                    editor.setContent(transformedText);
                                    console.log('SEO Time Machines: TinyMCE API paste - replaced all content');
                                } else {
                                    // Insert at cursor
                                    editor.insertContent(transformedText);
                                    console.log('SEO Time Machines: TinyMCE API paste - inserted at cursor');
                                }
                            }
                            
                            // TinyMCE-specific event handling
                            editor.fire('change');
                            editor.fire('keyup');
                            editor.fire('NodeChange');
                            
                            // Save content to textarea
                            if (typeof editor.save === 'function') {
                                editor.save();
                            }
                            
                            console.log('SEO Time Machines: TinyMCE API paste successful');
                            return true;
                        } catch (apiError) {
                            console.log('SEO Time Machines: TinyMCE API paste failed:', apiError);
                        }
                    }
                    
                    // Method 2: Direct DOM manipulation in TinyMCE iframe
                    if (iframeBody && iframeBody.contentEditable === 'true') {
                        try {
                            const iframeWindow = tinyMceEditor.iframe.contentWindow;
                            const iframeDoc = tinyMceEditor.iframe.contentDocument;
                            const iframeSelection = iframeWindow ? iframeWindow.getSelection() : null;
                            
                            // Focus the iframe body
                            iframeWindow.focus();
                            iframeBody.focus();
                            
                            if (iframeSelection && iframeSelection.rangeCount > 0) {
                                // Replace selected content or insert at cursor
                                const range = iframeSelection.getRangeAt(0);
                                range.deleteContents();
                                
                                // Insert transformed text as text node
                                const textNode = iframeDoc.createTextNode(transformedText);
                                range.insertNode(textNode);
                                
                                // Move cursor to end
                                range.setStartAfter(textNode);
                                range.collapse(true);
                                iframeSelection.removeAllRanges();
                                iframeSelection.addRange(range);
                                
                                console.log('SEO Time Machines: TinyMCE DOM manipulation paste successful');
                            } else {
                                // Replace all content
                                iframeBody.textContent = transformedText;
                                console.log('SEO Time Machines: TinyMCE DOM replacement successful');
                            }
                            
                            // Trigger TinyMCE events if editor instance is available
                            if (editor) {
                                editor.fire('change');
                                editor.fire('keyup');
                                editor.fire('NodeChange');
                                if (typeof editor.save === 'function') {
                                    editor.save();
                                }
                            }
                            
                            // Trigger generic events on iframe body
                            const events = ['input', 'change', 'keyup', 'blur'];
                            events.forEach(eventType => {
                                iframeBody.dispatchEvent(new Event(eventType, { bubbles: true }));
                            });
                            
                            return true;
                        } catch (domError) {
                            console.log('SEO Time Machines: TinyMCE DOM manipulation failed:', domError);
                        }
                    }
                    
                    // Method 3: Fallback execCommand paste in iframe context
                    try {
                        const iframeWindow = tinyMceEditor.iframe.contentWindow;
                        const iframeDoc = tinyMceEditor.iframe.contentDocument;
                        
                        // Ensure clipboard has transformed text
                        await this.bulletproofClipboardWrite(transformedText);
                        
                        // Focus iframe and attempt paste
                        iframeWindow.focus();
                        iframeBody.focus();
                        
                        const pasteSuccess = iframeDoc.execCommand('paste');
                        if (pasteSuccess) {
                            console.log('SEO Time Machines: TinyMCE iframe execCommand paste successful');
                            
                            // Trigger TinyMCE events
                            if (editor) {
                                editor.fire('change');
                                editor.fire('NodeChange');
                                if (typeof editor.save === 'function') {
                                    editor.save();
                                }
                            }
                            
                            return true;
                        }
                    } catch (execError) {
                        console.log('SEO Time Machines: TinyMCE iframe execCommand paste failed:', execError);
                    }
                } catch (error) {
                    console.log('SEO Time Machines: TinyMCE paste error:', error);
                }
            }
            
            // STEP 5: Direct element manipulation (when we know the exact element)
            if (textInfo.element) {
                const element = textInfo.element;
                
                // Enhanced INPUT/TEXTAREA manipulation with WordPress support
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    try {
                        const start = element.selectionStart;
                        const end = element.selectionEnd;
                        const value = element.value;
                        
                        if (start !== null && end !== null) {
                            // Replace selected text or insert at cursor
                            element.value = value.substring(0, start) + transformedText + value.substring(end);
                            element.selectionStart = start + transformedText.length;
                            element.selectionEnd = start + transformedText.length;
                            
                            // WordPress-specific event handling
                            if (textInfo.editorType === 'wordpress_visual' || 
                                element.classList.contains('wp-editor-area') ||
                                element.name === 'content') {
                                console.log('SEO Time Machines: Triggering WordPress-specific events');
                                
                                // WordPress editor events
                                const wpEvents = ['input', 'change', 'keyup', 'keydown', 'blur', 'focus'];
                                wpEvents.forEach(eventType => {
                                    element.dispatchEvent(new Event(eventType, { bubbles: true, cancelable: true }));
                                });
                                
                                // WordPress-specific custom events
                                if (window.wp && window.wp.hooks) {
                                    element.dispatchEvent(new CustomEvent('wp-editor-change', { 
                                        bubbles: true, 
                                        detail: { content: element.value } 
                                    }));
                                }
                                
                                // Trigger WordPress editor save
                                if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor) {
                                    tinyMCE.activeEditor.save();
                                }
                            } else {
                                // Standard events for non-WordPress editors
                                const events = ['input', 'change', 'keyup', 'blur'];
                                events.forEach(eventType => {
                                    element.dispatchEvent(new Event(eventType, { bubbles: true }));
                                });
                            }
                            
                            console.log('SEO Time Machines: Direct INPUT/TEXTAREA paste successful');
                            return true;
                        }
                    } catch (error) {
                        console.log('SEO Time Machines: Direct manipulation failed, using universal method:', error);
                    }
                }
                
                // Enhanced contentEditable manipulation
                if (element.contentEditable === 'true' || element.isContentEditable) {
                    try {
                        const selection = window.getSelection();
                        if (selection.rangeCount > 0) {
                            const range = selection.getRangeAt(0);
                            range.deleteContents();
                            
                            // Insert as text node to prevent HTML injection
                            const textNode = document.createTextNode(transformedText);
                            range.insertNode(textNode);
                            
                            // Move cursor to end
                            range.setStartAfter(textNode);
                            range.collapse(true);
                            selection.removeAllRanges();
                            selection.addRange(range);
                            
                            // Trigger events for contentEditable elements
                            element.dispatchEvent(new Event('input', { bubbles: true }));
                            element.dispatchEvent(new Event('change', { bubbles: true }));
                            
                            console.log('SEO Time Machines: Direct contentEditable paste successful');
                            return true;
                        }
                    } catch (error) {
                        console.log('SEO Time Machines: ContentEditable manipulation failed, using universal method:', error);
                    }
                }
            }
            
            // STEP 6: Universal execCommand('paste') with WordPress and focus enhancement
            console.log('SEO Time Machines: Using enhanced universal execCommand paste method');
            try {
                // WordPress-priority focus targets
                const wpTargets = [
                    document.querySelector('textarea.wp-editor-area'),
                    document.querySelector('textarea#content'),
                    document.querySelector('textarea[name="content"]')
                ].filter(Boolean);
                
                // Standard focus targets
                const standardTargets = [
                    document.activeElement,
                    document.querySelector('input:focus'),
                    document.querySelector('textarea:focus'),
                    document.querySelector('[contenteditable="true"]:focus'),
                    document.querySelector('input'),
                    document.querySelector('textarea'),
                    document.querySelector('[contenteditable="true"]')
                ].filter(Boolean);
                
                // Combine with WordPress targets first
                const focusTargets = [...wpTargets, ...standardTargets].filter((target, index, array) => 
                    array.indexOf(target) === index // Remove duplicates
                );
                
                for (const target of focusTargets) {
                    try {
                        target.focus();
                        if (target.select) target.select();
                        
                        const success = document.execCommand('paste');
                        if (success) {
                            console.log('SEO Time Machines: Universal execCommand paste successful');
                            return true;
                        }
                    } catch (error) {
                        console.log('SEO Time Machines: Focus target failed, trying next:', error);
                        continue;
                    }
                }
            } catch (error) {
                console.log('SEO Time Machines: Enhanced execCommand paste failed:', error);
            }
            
            // STEP 7: Keyboard simulation with WordPress priority targeting
            try {
                console.log('SEO Time Machines: Trying enhanced keyboard simulation paste');
                const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
                
                // WordPress-priority targets for keyboard simulation
                const wpKeyboardTargets = [
                    document.querySelector('textarea.wp-editor-area'),
                    document.querySelector('textarea#content'),
                    document.querySelector('textarea[name="content"]')
                ].filter(Boolean);
                
                const standardTargets = [
                    document.activeElement,
                    document.querySelector('input:focus'),
                    document.querySelector('textarea:focus'),
                    document.querySelector('[contenteditable="true"]:focus'),
                    document
                ];
                
                const targets = [...wpKeyboardTargets, ...standardTargets].filter((target, index, array) => 
                    array.indexOf(target) === index
                );
                
                for (const target of targets) {
                    if (!target) continue;
                    
                    try {
                        const pasteEvent = new KeyboardEvent('keydown', {
                            key: 'v',
                            code: 'KeyV',
                            ctrlKey: !isMac,
                            metaKey: isMac,
                            bubbles: true,
                            cancelable: true
                        });
                        
                        target.dispatchEvent(pasteEvent);
                        
                        // Also try paste event with WordPress handling
                        const pasteClipboardEvent = new ClipboardEvent('paste', {
                            bubbles: true,
                            cancelable: true,
                            clipboardData: new DataTransfer()
                        });
                        
                        // Add text data to clipboard event
                        pasteClipboardEvent.clipboardData.setData('text/plain', transformedText);
                        target.dispatchEvent(pasteClipboardEvent);
                        
                        // WordPress-specific handling
                        if (target.classList && (target.classList.contains('wp-editor-area') || 
                            target.name === 'content')) {
                            // Trigger WordPress editor events
                            target.dispatchEvent(new CustomEvent('wp-editor-paste', {
                                bubbles: true,
                                detail: { content: transformedText }
                            }));
                            
                            // WordPress content validation
                            if (window.wp && window.wp.editor && window.wp.editor.autop) {
                                // Apply WordPress auto-paragraph formatting if needed
                                const formattedContent = window.wp.editor.autop(transformedText);
                                if (formattedContent !== transformedText) {
                                    target.value = formattedContent;
                                    target.dispatchEvent(new Event('input', { bubbles: true }));
                                }
                            }
                        }
                        
                        console.log('SEO Time Machines: Enhanced keyboard simulation paste attempted');
                        return true; // Assume success for keyboard simulation
                    } catch (error) {
                        console.log('SEO Time Machines: Keyboard simulation target failed:', error);
                        continue;
                    }
                }
            } catch (error) {
                console.log('SEO Time Machines: Enhanced keyboard simulation failed:', error);
            }
            
            console.log('SEO Time Machines: All paste methods attempted - transformed text guaranteed in clipboard');
            return false; // Paste failed, but clipboard has correct content
            
        } catch (error) {
            console.error('SEO Time Machines: Universal paste error:', error);
            return false;
        }
    }
    
    // Detect WordPress Visual Editor (textarea-based)
    static detectWordPressEditor() {
        try {
            // WordPress Visual Editor selectors (non-iframe)
            const wpSelectors = [
                'textarea.wp-editor-area',
                'textarea#content',
                'textarea[name="content"]',
                'textarea[id*="wp-"]',
                'textarea[class*="wp-editor"]',
                'textarea[class*="wp-content"]'
            ];
            
            for (const selector of wpSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    console.log(`SEO Time Machines: Found WordPress editor via selector: ${selector}`);
                    return {
                        element: element,
                        type: 'wordpress_visual',
                        selector: selector
                    };
                }
            }
            
            // Check for WordPress admin page indicators
            if (document.body.classList.contains('wp-admin') || 
                document.querySelector('.wp-admin') ||
                document.querySelector('#wpadminbar')) {
                // Look for any textarea that might be WordPress editor
                const textareas = document.querySelectorAll('textarea');
                for (const textarea of textareas) {
                    if (textarea.name === 'content' || 
                        textarea.id === 'content' ||
                        textarea.classList.contains('wp-editor-area')) {
                        console.log('SEO Time Machines: Found WordPress editor in admin context');
                        return {
                            element: textarea,
                            type: 'wordpress_visual',
                            selector: 'admin_context'
                        };
                    }
                }
            }
            
            return null;
        } catch (error) {
            console.log('SEO Time Machines: Error detecting WordPress editor:', error);
            return null;
        }
    }
    
    // Wait for iframe content to be ready (timing helper)
    static async waitForIframeReady(iframe, maxWaitTime = 3000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const checkReady = () => {
                try {
                    const iframeDoc = iframe.contentDocument;
                    if (iframeDoc && iframeDoc.body && iframeDoc.readyState === 'complete') {
                        console.log('SEO Time Machines: Iframe content ready');
                        resolve(iframe);
                        return;
                    }
                } catch (error) {
                    // Iframe not accessible yet
                }
                
                if (Date.now() - startTime > maxWaitTime) {
                    console.log('SEO Time Machines: Iframe ready timeout');
                    resolve(null);
                    return;
                }
                
                setTimeout(checkReady, 100);
            };
            
            checkReady();
        });
    }
    
    // Enhanced TinyMCE detection with retry mechanism
    static async detectTinyMCEEditorWithRetry(maxRetries = 3, retryDelay = 500) {
        for (let attempt = 0; attempt < maxRetries; attempt++) {
            const result = this.detectTinyMCEEditor();
            if (result) {
                console.log(`SEO Time Machines: TinyMCE detected on attempt ${attempt + 1}`);
                return result;
            }
            
            if (attempt < maxRetries - 1) {
                console.log(`SEO Time Machines: TinyMCE detection attempt ${attempt + 1} failed, retrying...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
            }
        }
        
        console.log('SEO Time Machines: TinyMCE detection failed after all retries');
        return null;
    }
    
    // Enhanced TinyMCE iframe-based editors detection with WordPress Classic Editor support
    static detectTinyMCEEditor() {
        try {
            console.log('SEO Time Machines: Detecting TinyMCE editors...');
            
            // Quick cache check to avoid repeated detection (reduced cache time for better responsiveness)
            if (this._tinyMCEDetectionCache && (Date.now() - this._tinyMCEDetectionCache.timestamp) < 500) {
                console.log('SEO Time Machines: Using cached TinyMCE detection result');
                return this._tinyMCEDetectionCache.result;
            }
            
            // Method 0: WordPress Classic Editor specific detection (PRIORITY)
            const wordPressIframeSelectors = [
                '#content_ifr',           // WordPress Classic Editor main iframe
                '#content_wp_ifr',        // Alternative WordPress iframe
                'iframe[id*="content"]',  // Any iframe with 'content' in ID
                'iframe[id*="wp"]',       // Any iframe with 'wp' in ID
                'iframe[id*="editor"]'    // Any iframe with 'editor' in ID
            ];
            
            for (const selector of wordPressIframeSelectors) {
                try {
                    const iframe = document.querySelector(selector);
                    if (iframe) {
                        const iframeDoc = iframe.contentDocument;
                        if (iframeDoc) {
                            const iframeBody = iframeDoc.body;
                            if (iframeBody && iframeBody.contentEditable === 'true') {
                                console.log(`SEO Time Machines: Found WordPress TinyMCE editor via selector: ${selector}`);
                                
                                // Try to find associated TinyMCE editor instance
                                let editorInstance = null;
                                if (typeof tinyMCE !== 'undefined') {
                                    editorInstance = tinyMCE.activeEditor || 
                                                   (tinyMCE.editors && tinyMCE.editors[0]) ||
                                                   null;
                                    
                                    // Try to find by editor ID derived from iframe ID
                                    const editorId = iframe.id.replace('_ifr', '');
                                    if (editorId && tinyMCE.get) {
                                        const editorById = tinyMCE.get(editorId);
                                        if (editorById) editorInstance = editorById;
                                    }
                                }
                                
                                const result = {
                                    iframe: iframe,
                                    body: iframeBody,
                                    editor: editorInstance,
                                    type: 'wordpress_tinymce',
                                    selector: selector,
                                    bodyId: iframeBody.id,
                                    dataId: iframeBody.getAttribute('data-id')
                                };
                                
                                // Cache the result
                                this._tinyMCEDetectionCache = {
                                    result: result,
                                    timestamp: Date.now()
                                };
                                
                                return result;
                            }
                        }
                    }
                } catch (error) {
                    console.log(`SEO Time Machines: WordPress iframe selector ${selector} failed:`, error);
                    continue;
                }
            }
            
            const iframes = document.querySelectorAll('iframe');
            console.log(`SEO Time Machines: Found ${iframes.length} iframes to check`);
            
            // Method 1: Direct search for specific TinyMCE body patterns
            for (const iframe of iframes) {
                try {
                    // Enhanced cross-origin and about:blank handling
                    if (iframe.src && iframe.src.startsWith('http') && !iframe.src.startsWith(window.location.origin)) {
                        console.log('SEO Time Machines: Skipping cross-origin iframe');
                        continue;
                    }
                    
                    const iframeDoc = iframe.contentDocument;
                    if (!iframeDoc) {
                        console.log('SEO Time Machines: Iframe not accessible');
                        continue;
                    }
                    
                    const iframeBody = iframeDoc.body;
                    if (!iframeBody) {
                        console.log('SEO Time Machines: Iframe has no body');
                        continue;
                    }
                    
                    console.log('SEO Time Machines: Checking iframe body:', {
                        id: iframeBody.id,
                        className: iframeBody.className,
                        contentEditable: iframeBody.contentEditable,
                        dataId: iframeBody.getAttribute('data-id'),
                        tagName: iframeBody.tagName,
                        iframeId: iframe.id,
                        iframeSrc: iframe.src
                    });
                    
                    // Enhanced TinyMCE pattern matching
                    const isTargetBody = (
                        iframeBody.id === 'tinymce' ||
                        iframeBody.classList.contains('mce-content-body') ||
                        iframeBody.getAttribute('data-id') === 'vizualisEditor' ||
                        (iframeBody.contentEditable === 'true' && iframeBody.classList.contains('mce-content-body')) ||
                        (iframeBody.contentEditable === 'true' && iframe.id.includes('_ifr')) ||
                        (iframeBody.contentEditable === 'true' && iframe.id.includes('content'))
                    );
                    
                    if (isTargetBody) {
                        console.log('SEO Time Machines: Found TinyMCE body via direct detection!', {
                            method: 'direct_body_detection',
                            bodyId: iframeBody.id,
                            bodyClass: iframeBody.className,
                            dataId: iframeBody.getAttribute('data-id'),
                            iframeId: iframe.id
                        });
                        
                        // Enhanced editor instance detection
                        let editorInstance = null;
                        if (typeof tinyMCE !== 'undefined') {
                            // Try multiple ways to find the editor
                            editorInstance = tinyMCE.activeEditor || 
                                           (tinyMCE.editors && tinyMCE.editors[0]) ||
                                           null;
                            
                            // Try to find by iframe ID pattern
                            if (iframe.id && iframe.id.includes('_ifr')) {
                                const editorId = iframe.id.replace('_ifr', '');
                                if (tinyMCE.get) {
                                    const editorById = tinyMCE.get(editorId);
                                    if (editorById) editorInstance = editorById;
                                }
                            }
                            
                            // Try to find by data-id
                            const dataId = iframeBody.getAttribute('data-id');
                            if (dataId && tinyMCE.get) {
                                const editorById = tinyMCE.get(dataId);
                                if (editorById) editorInstance = editorById;
                            }
                            
                            // Try to find by body ID
                            if (iframeBody.id && tinyMCE.get) {
                                const editorById = tinyMCE.get(iframeBody.id);
                                if (editorById) editorInstance = editorById;
                            }
                        }
                        
                        const result = {
                            iframe: iframe,
                            body: iframeBody,
                            editor: editorInstance,
                            type: 'tinymce_direct_body',
                            selector: 'direct_body_detection',
                            bodyId: iframeBody.id,
                            dataId: iframeBody.getAttribute('data-id'),
                            iframeId: iframe.id
                        };
                        
                        // Cache the result
                        this._tinyMCEDetectionCache = {
                            result: result,
                            timestamp: Date.now()
                        };
                        
                        return result;
                    }
                } catch (error) {
                    console.log('SEO Time Machines: Error checking iframe:', error);
                    continue;
                }
            }
            
            // Method 2: Enhanced TinyMCE API detection with WordPress support
            if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor) {
                const activeEditor = tinyMCE.activeEditor;
                const editorId = activeEditor.id;
                
                // Enhanced iframe selectors including WordPress patterns
                const iframeSelectors = [
                    `#${editorId}_ifr`,           // Standard TinyMCE iframe
                    'iframe[id*="tinymce"]',      // Generic TinyMCE iframe
                    '#content_ifr',               // WordPress content iframe
                    'iframe[id*="content"]',      // Content-related iframes
                    'iframe[id*="editor"]',       // Editor-related iframes
                    'iframe[class*="mce"]',       // MCE class iframes
                    'iframe[data-id*="editor"]'   // Data-id editor iframes
                ];
                
                for (const selector of iframeSelectors) {
                    const iframe = document.querySelector(selector);
                    if (iframe) {
                        try {
                            const iframeDoc = iframe.contentDocument;
                            const iframeBody = iframeDoc ? iframeDoc.body : null;
                            
                            if (iframeBody && (iframeBody.id === 'tinymce' || 
                                              iframeBody.classList.contains('mce-content-body') ||
                                              iframeBody.contentEditable === 'true')) {
                                console.log(`SEO Time Machines: Found TinyMCE editor via API: ${selector}`);
                                const result = {
                                    iframe: iframe,
                                    body: iframeBody,
                                    editor: activeEditor,
                                    type: 'tinymce_api',
                                    selector: selector,
                                    bodyId: iframeBody.id,
                                    iframeId: iframe.id
                                };
                                
                                // Cache the result
                                this._tinyMCEDetectionCache = {
                                    result: result,
                                    timestamp: Date.now()
                                };
                                
                                return result;
                            }
                        } catch (error) {
                            // Cross-origin or access error, continue
                            continue;
                        }
                    }
                }
            }
            
            console.log('SEO Time Machines: No TinyMCE editor found');
            const result = null;
            
            // Cache the null result with shorter duration
            this._tinyMCEDetectionCache = {
                result: result,
                timestamp: Date.now()
            };
            
            return result;
        } catch (error) {
            console.log('SEO Time Machines: Error detecting TinyMCE editor:', error);
            return null;
        }
    }
    
    // Enhanced editor type detection
    static detectEditorType(element) {
        if (!element) return 'unknown';
        
        const tagName = element.tagName.toLowerCase();
        const className = element.className || '';
        const id = element.id || '';
        const name = element.name || '';
        
        // TinyMCE iframe body detection
        if (tagName === 'body' && (id === 'tinymce' || className.includes('mce-content-body'))) {
            return 'tinymce_iframe_body';
        }
        
        // WordPress Visual Editor detection
        if (className.includes('wp-editor-area') || 
            name === 'content' && (className.includes('wp-') || id.includes('wp-'))) {
            return 'wordpress_visual';
        }
        
        // TinyMCE detection
        if (className.includes('mce-') || id.includes('tinymce')) {
            return 'tinymce';
        }
        
        // Standard elements
        if (tagName === 'textarea') return 'textarea';
        if (tagName === 'input') return 'input';
        if (element.contentEditable === 'true') return 'contenteditable';
        
        return tagName;
    }
    
    // Format text for TinyMCE with proper HTML structure
    static formatTextForTinyMCE(transformedText, originalHtmlContent = '', isFullContent = false) {
        try {
            if (!transformedText) return '';
            
            // Escape HTML entities in the transformed text
            const escapedText = transformedText
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;');
            
            let htmlContent;
            
            if (originalHtmlContent && originalHtmlContent.includes('<')) {
                // Original had HTML structure, preserve basic formatting patterns
                if (originalHtmlContent.match(/<p[^>]*>/i)) {
                    // Had paragraphs, wrap in paragraph tags with proper line break handling
                    htmlContent = `<p>${escapedText.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`;
                } else if (originalHtmlContent.match(/<div[^>]*>/i)) {
                    // Had divs, wrap in div tags
                    htmlContent = `<div>${escapedText.replace(/\n/g, '<br>')}</div>`;
                } else if (originalHtmlContent.match(/<br[^>]*>/i)) {
                    // Had line breaks, preserve them
                    htmlContent = escapedText.replace(/\n/g, '<br>');
                } else {
                    // Basic HTML structure with line break preservation
                    htmlContent = escapedText.replace(/\n/g, '<br>');
                }
            } else {
                // No HTML structure detected, use WordPress-friendly paragraph wrapper
                if (isFullContent && escapedText.includes('\n\n')) {
                    // Multiple paragraphs detected
                    htmlContent = `<p>${escapedText.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`;
                } else if (escapedText.includes('\n')) {
                    // Single paragraph with line breaks
                    htmlContent = `<p>${escapedText.replace(/\n/g, '<br>')}</p>`;
                } else {
                    // Simple text, wrap in paragraph
                    htmlContent = `<p>${escapedText}</p>`;
                }
            }
            
            console.log('SEO Time Machines: Formatted text for TinyMCE:', {
                originalLength: transformedText.length,
                htmlLength: htmlContent.length,
                hasOriginalHtml: originalHtmlContent.includes('<'),
                isFullContent: isFullContent
            });
            
            return htmlContent;
        } catch (error) {
            console.log('SEO Time Machines: Error formatting text for TinyMCE:', error);
            // Fallback to simple paragraph wrapper
            return `<p>${transformedText.replace(/\n/g, '<br>')}</p>`;
        }
    }
    
    // Enhanced WordPress Classic Editor synchronization
    static async synchronizeWordPressEditor(editor, htmlContent, textContent) {
        try {
            console.log('SEO Time Machines: Synchronizing WordPress Classic Editor...');
            
            // WordPress-specific synchronization sequence
            if (editor && typeof editor.save === 'function') {
                // Save TinyMCE content to underlying textarea
                editor.save();
                console.log('SEO Time Machines: TinyMCE content saved to textarea');
            }
            
            // Find and update the underlying WordPress textarea
            const contentTextarea = document.querySelector('textarea.wp-editor-area') ||
                                  document.querySelector('textarea#content') ||
                                  document.querySelector('textarea[name="content"]');
            
            if (contentTextarea) {
                // Update textarea with text content (not HTML) for text mode compatibility
                contentTextarea.value = textContent;
                
                // Trigger WordPress editor events
                const wpEvents = ['input', 'change', 'keyup', 'blur'];
                wpEvents.forEach(eventType => {
                    contentTextarea.dispatchEvent(new Event(eventType, { bubbles: true, cancelable: true }));
                });
                
                console.log('SEO Time Machines: WordPress textarea synchronized');
            }
            
            // Enhanced TinyMCE Visual mode synchronization
            if (editor) {
                // Sequential event firing for proper WordPress integration
                const eventSequence = [
                    { event: 'change', delay: 0 },
                    { event: 'input', delay: 10 },
                    { event: 'keyup', delay: 20 },
                    { event: 'NodeChange', delay: 30 },
                    { event: 'SetContent', delay: 40 }
                ];
                
                eventSequence.forEach(({ event, delay }) => {
                    setTimeout(() => {
                        try {
                            if (typeof editor.fire === 'function') {
                                editor.fire(event, { 
                                    content: htmlContent,
                                    format: 'html'
                                });
                            }
                        } catch (e) {
                            console.log(`SEO Time Machines: WordPress event ${event} failed:`, e);
                        }
                    }, delay);
                });
                
                // Force Visual mode refresh
                setTimeout(() => {
                    try {
                        if (typeof editor.nodeChanged === 'function') {
                            editor.nodeChanged();
                        }
                        if (typeof editor.focus === 'function') {
                            editor.focus();
                        }
                    } catch (e) {
                        console.log('SEO Time Machines: Visual mode refresh failed:', e);
                    }
                }, 50);
            }
            
            // WordPress hooks integration
            if (window.wp && window.wp.hooks) {
                setTimeout(() => {
                    try {
                        if (window.wp.hooks.doAction) {
                            window.wp.hooks.doAction('wp.editor.change', textContent);
                        }
                        
                        // Custom WordPress synchronization event
                        window.dispatchEvent(new CustomEvent('wp-tinymce-content-synchronized', {
                            detail: {
                                htmlContent: htmlContent,
                                textContent: textContent,
                                editorId: editor ? editor.id : 'unknown',
                                timestamp: Date.now()
                            }
                        }));
                    } catch (e) {
                        console.log('SEO Time Machines: WordPress hooks integration failed:', e);
                    }
                }, 60);
            }
            
            console.log('SEO Time Machines: WordPress Classic Editor synchronization complete');
            return true;
        } catch (error) {
            console.log('SEO Time Machines: WordPress synchronization error:', error);
            return false;
        }
    }
    
    // TinyMCE integration helper - ensures compatibility with TinyMCE editors
    static ensureTinyMCECompatibility(tinyMceEditor, transformedText) {
        try {
            if (!tinyMceEditor || !transformedText) return false;
            
            const editor = tinyMceEditor.editor;
            const iframeBody = tinyMceEditor.body;
            
            console.log('SEO Time Machines: Ensuring TinyMCE compatibility');
            
            if (editor) {
                // Enhanced event sequence for both text and Visual mode
                const eventSequence = [
                    { event: 'BeforeSetContent', delay: 0, format: 'html' },
                    { event: 'SetContent', delay: 10, format: 'html' },
                    { event: 'NodeChange', delay: 20, format: 'html' },
                    { event: 'change', delay: 30, format: 'text' },
                    { event: 'input', delay: 40, format: 'text' },
                    { event: 'keyup', delay: 50, format: 'text' }
                ];
                
                eventSequence.forEach(({ event, delay, format }) => {
                    setTimeout(() => {
                        try {
                            if (typeof editor.fire === 'function') {
                                editor.fire(event, { 
                                    content: transformedText,
                                    format: format
                                });
                            }
                        } catch (e) {
                            console.log(`SEO Time Machines: TinyMCE event ${event} failed:`, e);
                        }
                    }, delay);
                });
                
                // Visual mode specific operations
                setTimeout(() => {
                    try {
                        // Force editor redraw for Visual mode
                        if (typeof editor.getBody === 'function') {
                            const editorBody = editor.getBody();
                            if (editorBody && typeof editor.selection.setCursorLocation === 'function') {
                                editor.selection.setCursorLocation(editorBody, 0);
                            }
                        }
                        
                        // Force nodeChanged to refresh Visual mode display
                        if (typeof editor.nodeChanged === 'function') {
                            editor.nodeChanged();
                            console.log('SEO Time Machines: Triggered TinyMCE nodeChanged for Visual mode');
                        }
                        
                        // Trigger focus to refresh Visual mode
                        if (typeof editor.focus === 'function') {
                            editor.focus();
                            console.log('SEO Time Machines: Triggered TinyMCE focus for Visual mode');
                        }
                        
                        // Content validation
                        if (typeof editor.getContent === 'function') {
                            const currentContent = editor.getContent({ format: 'text' });
                            console.log('SEO Time Machines: TinyMCE content validation:', {
                                expected: transformedText.substring(0, 50),
                                actual: currentContent.substring(0, 50),
                                match: currentContent.includes(transformedText)
                            });
                        }
                        
                    } catch (e) {
                        console.log('SEO Time Machines: TinyMCE Visual mode operations failed:', e);
                    }
                }, 100);
                
                // Final synchronization and save operations
                setTimeout(() => {
                    try {
                        // Save to underlying textarea
                        if (typeof editor.save === 'function') {
                            editor.save();
                            console.log('SEO Time Machines: TinyMCE content saved to underlying form element');
                        }
                        
                        // Trigger save events
                        if (typeof editor.fire === 'function') {
                            editor.fire('SaveContent');
                            editor.fire('PostProcess');
                        }
                        
                        // Update any associated form elements
                        const targetElement = document.getElementById(editor.id);
                        if (targetElement && (targetElement.tagName === 'TEXTAREA' || targetElement.tagName === 'INPUT')) {
                            targetElement.value = transformedText;
                            targetElement.dispatchEvent(new Event('change', { bubbles: true }));
                            targetElement.dispatchEvent(new Event('input', { bubbles: true }));
                            console.log('SEO Time Machines: Updated underlying form element');
                        }
                        
                        // Custom Visual mode sync event
                        window.dispatchEvent(new CustomEvent('tinymce-visual-mode-synced', {
                            detail: {
                                content: transformedText,
                                editorId: editor.id,
                                timestamp: Date.now()
                            }
                        }));
                        
                    } catch (e) {
                        console.log('SEO Time Machines: TinyMCE save operations failed:', e);
                    }
                }, 200);
            }
            
            // Enhanced iframe body events
            if (iframeBody) {
                // Enhanced DOM events for Visual mode compatibility
                const bodyEvents = ['input', 'change', 'keyup', 'focus', 'DOMSubtreeModified'];
                bodyEvents.forEach((eventType, index) => {
                    setTimeout(() => {
                        try {
                            const event = new Event(eventType, { bubbles: true, cancelable: true });
                            iframeBody.dispatchEvent(event);
                        } catch (e) {
                            console.log(`SEO Time Machines: TinyMCE iframe event ${eventType} failed:`, e);
                        }
                    }, index * 5);
                });
                
                // Custom TinyMCE transformation event
                setTimeout(() => {
                    try {
                        const customEvent = new CustomEvent('tinymce-text-transformer-update', {
                            bubbles: true,
                            detail: {
                                content: transformedText,
                                editorId: editor ? editor.id : 'unknown',
                                timestamp: Date.now()
                            }
                        });
                        iframeBody.dispatchEvent(customEvent);
                        
                        // Also dispatch on parent window
                        window.dispatchEvent(new CustomEvent('tinymce-content-transformed', {
                            detail: {
                                content: transformedText,
                                editorId: editor ? editor.id : 'unknown'
                            }
                        }));
                    } catch (e) {
                        console.log('SEO Time Machines: TinyMCE custom events failed:', e);
                    }
                }, 50);
                
                // Force browser reflow for Visual mode
                setTimeout(() => {
                    try {
                        const computedStyle = window.getComputedStyle(iframeBody);
                        const display = computedStyle.display;
                        iframeBody.style.display = 'none';
                        iframeBody.offsetHeight; // Force reflow
                        iframeBody.style.display = display;
                        console.log('SEO Time Machines: Forced Visual mode reflow');
                    } catch (e) {
                        console.log('SEO Time Machines: Visual mode reflow failed:', e);
                    }
                }, 150);
            }
            
            console.log('SEO Time Machines: TinyMCE compatibility ensured');
            return true;
        } catch (error) {
            console.log('SEO Time Machines: TinyMCE compatibility error:', error);
            return false;
        }
    }


    
    // Detect iframe-based editors (WordPress TinyMCE, etc.)

    
    // Debug helper method for TinyMCE detection issues
    static debugTinyMCEDetection() {
        console.log('=== TinyMCE DEBUG REPORT ===');
        
        // Check for TinyMCE global
        console.log('TinyMCE Global Available:', typeof tinyMCE !== 'undefined');
        if (typeof tinyMCE !== 'undefined') {
            console.log('TinyMCE Active Editor:', tinyMCE.activeEditor);
            console.log('TinyMCE Editors:', tinyMCE.editors);
        }
        
        // Check all iframes
        const iframes = document.querySelectorAll('iframe');
        console.log(`Total iframes found: ${iframes.length}`);
        
        iframes.forEach((iframe, index) => {
            console.log(`--- Iframe ${index + 1} ---`);
            console.log('Iframe ID:', iframe.id);
            console.log('Iframe Class:', iframe.className);
            console.log('Iframe Src:', iframe.src);
            
            try {
                const iframeDoc = iframe.contentDocument;
                if (iframeDoc) {
                    const body = iframeDoc.body;
                    if (body) {
                        console.log('Body ID:', body.id);
                        console.log('Body Class:', body.className);
                        console.log('Body contentEditable:', body.contentEditable);
                        console.log('Body data-id:', body.getAttribute('data-id'));
                        console.log('Body innerHTML (first 100 chars):', body.innerHTML.substring(0, 100));
                        console.log('Body textContent:', body.textContent || 'NO TEXT');
                        
                        // Check for TinyMCE markers
                        const isTinyMCE = body.id === 'tinymce' || 
                                        body.classList.contains('mce-content-body') ||
                                        body.getAttribute('data-id') === 'vizualisEditor';
                        console.log('Is TinyMCE Body:', isTinyMCE);
                    } else {
                        console.log('No body in iframe');
                    }
                } else {
                    console.log('Cannot access iframe content (cross-origin or no document)');
                }
            } catch (error) {
                console.log('Error accessing iframe:', error.message);
            }
        });
        
        // Try detection
        const detected = this.detectTinyMCEEditor();
        console.log('Detection Result:', detected);
        
        console.log('=== END DEBUG REPORT ===');
        return detected;
    }
    
    // Individual transformation methods with enhanced WordPress and TinyMCE support
    static async executeCapitalCase() {
        return await this.executeTransformation('capitalCase');
    }
    
    static async executeLowerCase() {
        return await this.executeTransformation('lowerCase');
    }
    
    static async executeUpperCase() {
        return await this.executeTransformation('upperCase');
    }
    
    static async executeSentenceCase() {
        return await this.executeTransformation('sentenceCase');
    }
    
    static async executeSlugify() {
        return await this.executeTransformation('slugify');
    }
    
    static async executeTrimToPage() {
        return await this.executeTransformation('trimToPage');
    }
    
    static async executeSortAlphabetically() {
        return await this.executeTransformation('sortAlphabetically');
    }
    
    static async executeRemoveEmptyLines() {
        return await this.executeTransformation('removeEmptyLines');
    }
    
    static async executeRemoveDuplicateLines() {
        return await this.executeTransformation('removeDuplicateLines');
    }

    static async executeTextTimeMachineLauncher() {
        try {
            // Get the configured URL from storage
            const settings = await this.safeStorageGet(['gmbExtractorSettings']);
            const url = settings.gmbExtractorSettings?.textTimeMachineUrl;

            // Validate URL is configured
            if (!url || url.trim() === '') {
                this.showNotification('Text Time Machine URL not configured');
                return { error: 'URL not configured' };
            }

            // Smart URL normalization - handle both file:// URLs and raw file paths
            let normalizedUrl = url.trim();
            if (!normalizedUrl.startsWith('file://') && !normalizedUrl.startsWith('http')) {
                // Raw file path - add file:// prefix
                if (normalizedUrl.startsWith('/')) {
                    // Absolute path (Mac/Linux format)
                    normalizedUrl = 'file://' + normalizedUrl;
                } else if (normalizedUrl.match(/^[A-Za-z]:/)) {
                    // Windows drive letter format (C:, D:, etc.)
                    normalizedUrl = 'file:///' + normalizedUrl;
                } else {
                    // Assume relative path
                    normalizedUrl = 'file:///' + normalizedUrl;
                }
            }
            // If already has file:// or http protocol, use as-is

            console.log('SEO Time Machines: Launching Text Time Machine via background script');
            console.log('SEO Time Machines: Original URL:', url);
            console.log('SEO Time Machines: Normalized URL:', normalizedUrl);

            // Method 1: Use background script (primary method - works with file:// URLs)
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                try {
                    chrome.runtime.sendMessage({
                        action: 'createTextTimeMachineLauncherTab',
                        url: normalizedUrl
                    }, (response) => {
                        if (chrome.runtime.lastError || !response || !response.success) {
                            console.log('SEO Time Machines: Background script not available, using fallback');
                            this._openTextTimeMachineWithFallback(normalizedUrl);
                        } else {
                            console.log('SEO Time Machines: Text Time Machine opened successfully via background script');
                            this.showNotification('Text Time Machine opened in new tab');
                        }
                    });
                    return { success: true };
                } catch (error) {
                    console.log('SEO Time Machines: Chrome runtime error, using fallback');
                    this._openTextTimeMachineWithFallback(normalizedUrl);
                    return { success: true };
                }
            } else {
                // Method 2: Direct fallback
                this._openTextTimeMachineWithFallback(normalizedUrl);
                return { success: true };
            }
        } catch (error) {
            console.error('SEO Time Machines: Error launching Text Time Machine:', error);
            this.showNotification('Error launching Text Time Machine');
            return { error: error.message };
        }
    }

    // Fallback method for opening Text Time Machine
    static _openTextTimeMachineWithFallback(url) {
        try {
            window.open(url, '_blank');
            this.showNotification('Text Time Machine opened in new window');
            console.log('SEO Time Machines: Text Time Machine opened via fallback method');
        } catch (error) {
            console.error('SEO Time Machines: Fallback method failed:', error);
            this.showNotification('Failed to open Text Time Machine');
        }
    }

    static async executeNewTabRedirect() {
        console.log('🟡 DEBUG: TextTransformers.executeNewTabRedirect() called');
        
        try {
            // Step 1: Get the configured URL from storage
            console.log('🟡 DEBUG: Step 1 - Getting settings from storage...');
            const settings = await this.safeStorageGet(['gmbExtractorSettings']);
            console.log('🟡 DEBUG: Settings retrieved:', settings);
            
            const url = settings.gmbExtractorSettings?.newTabRedirectUrl;
            console.log('🟡 DEBUG: Raw URL from settings:', url);

            // Step 2: Validate URL is configured
            console.log('🟡 DEBUG: Step 2 - Validating URL...');
            if (!url || url.trim() === '') {
                console.error('🟡 DEBUG: URL validation failed - URL is empty or undefined');
                this.showNotification('New Tab Redirect URL not configured');
                return { error: 'URL not configured' };
            }
            console.log('🟡 DEBUG: URL validation passed');

            // Step 3: Smart URL normalization
            console.log('🟡 DEBUG: Step 3 - Normalizing URL...');
            let normalizedUrl = url.trim();
            console.log('🟡 DEBUG: Trimmed URL:', normalizedUrl);
            
            if (!normalizedUrl.startsWith('file://') && !normalizedUrl.startsWith('http')) {
                console.log('🟡 DEBUG: URL lacks protocol, adding https://');
                normalizedUrl = 'https://' + normalizedUrl;
            } else {
                console.log('🟡 DEBUG: URL already has protocol, using as-is');
            }
            
            console.log('🟡 DEBUG: Final normalized URL:', normalizedUrl);

            console.log('SEO Time Machines: Launching New Tab Redirect via background script');
            console.log('SEO Time Machines: Original URL:', url);
            console.log('SEO Time Machines: Normalized URL:', normalizedUrl);

            // Step 4: Check Chrome runtime availability
            console.log('🟡 DEBUG: Step 4 - Checking Chrome runtime...');
            console.log('🟡 DEBUG: typeof chrome:', typeof chrome);
            console.log('🟡 DEBUG: chrome.runtime exists:', !!(chrome && chrome.runtime));
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                console.log('🟡 DEBUG: Chrome runtime available, sending message...');
                
                try {
                    const messageData = {
                        action: 'createNewTabRedirectTab',
                        url: normalizedUrl
                    };
                    console.log('🟡 DEBUG: Sending message to background script:', messageData);
                    
                    chrome.runtime.sendMessage(messageData, (response) => {
                        console.log('🟡 DEBUG: Background script response received:', response);
                        console.log('🟡 DEBUG: chrome.runtime.lastError:', chrome.runtime.lastError);
                        
                        if (chrome.runtime.lastError || !response || !response.success) {
                            console.error('🟡 DEBUG: Background script failed, using fallback');
                            console.log('SEO Time Machines: Background script not available, using fallback');
                            this._openNewTabRedirectWithFallback(normalizedUrl);
                        } else {
                            console.log('🟡 DEBUG: Background script success!');
                            console.log('SEO Time Machines: New Tab Redirect opened successfully via background script');
                            this.showNotification('New Tab Redirect opened in new tab');
                        }
                    });
                    
                    console.log('🟡 DEBUG: Message sent, returning success');
                    return { success: true };
                } catch (error) {
                    console.log('SEO Time Machines: Chrome runtime error, using fallback');
                    this._openNewTabRedirectWithFallback(normalizedUrl);
                    return { success: true };
                }
            } else {
                // Method 2: Direct fallback
                this._openNewTabRedirectWithFallback(normalizedUrl);
                return { success: true };
            }
        } catch (error) {
            console.error('SEO Time Machines: Error launching New Tab Redirect:', error);
            this.showNotification('Error launching New Tab Redirect');
            return { error: error.message };
        }
    }

    // Fallback method for opening New Tab Redirect
    static _openNewTabRedirectWithFallback(url) {
        try {
            window.open(url, '_blank');
            this.showNotification('New Tab Redirect opened in new window');
            console.log('SEO Time Machines: New Tab Redirect opened via fallback method');
        } catch (error) {
            console.error('SEO Time Machines: Fallback method failed:', error);
            this.showNotification('Failed to open New Tab Redirect');
        }
    }
    
    // WordPress integration helper - ensures compatibility with WordPress editors
    static ensureWordPressCompatibility(element, transformedText) {
        try {
            if (!element || !transformedText) return false;
            
            // Only apply to WordPress editors
            if (!element.classList.contains('wp-editor-area') && 
                element.name !== 'content' &&
                !document.body.classList.contains('wp-admin')) {
                return false;
            }
            
            console.log('SEO Time Machines: Ensuring WordPress compatibility');
            
            // Apply WordPress auto-paragraph formatting if available
            if (window.wp && window.wp.editor && window.wp.editor.autop && transformedText.includes('\n\n')) {
                const autoFormatted = window.wp.editor.autop(transformedText);
                if (autoFormatted !== transformedText) {
                    element.value = autoFormatted;
                    console.log('SEO Time Machines: Applied WordPress auto-paragraph formatting');
                } else {
                    element.value = transformedText;
                }
            } else {
                element.value = transformedText;
            }
            
            // WordPress-specific event sequence
            const wpEventSequence = [
                'focus',
                'input',
                'keyup',
                'change',
                'blur'
            ];
            
            wpEventSequence.forEach((eventType, index) => {
                setTimeout(() => {
                    element.dispatchEvent(new Event(eventType, { bubbles: true, cancelable: true }));
                }, index * 10); // Small delays between events
            });
            
            // WordPress hooks integration
            if (window.wp && window.wp.hooks) {
                setTimeout(() => {
                    // WordPress editor change hook
                    if (window.wp.hooks.doAction) {
                        window.wp.hooks.doAction('wp.editor.change', element.value);
                    }
                    
                    // Custom WordPress editor event
                    element.dispatchEvent(new CustomEvent('wp-text-transformer-update', {
                        bubbles: true,
                        detail: {
                            content: element.value,
                            element: element,
                            timestamp: Date.now()
                        }
                    }));
                }, 50);
            }
            
            // TinyMCE integration (if present)
            if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor) {
                setTimeout(() => {
                    try {
                        tinyMCE.activeEditor.save();
                        tinyMCE.triggerSave();
                    } catch (e) {
                        console.log('SEO Time Machines: TinyMCE save attempted');
                    }
                }, 100);
            }
            
            // WordPress form validation trigger
            const form = element.closest('form');
            if (form) {
                form.dispatchEvent(new Event('change', { bubbles: true }));
            }
            
            console.log('SEO Time Machines: WordPress compatibility ensured');
            return true;
        } catch (error) {
            console.log('SEO Time Machines: WordPress compatibility error:', error);
            return false;
        }
    }
    
    // Enhanced transformation execution with WordPress support
    static async executeWordPressTransformation(transformType) {
        try {
            console.log(`SEO Time Machines: Executing WordPress-aware ${transformType} transformation`);
            
            // First try WordPress-specific detection
            const wpEditor = this.detectWordPressEditor();
            if (wpEditor && wpEditor.element) {
                const element = wpEditor.element;
                const selectedText = element.value.substring(element.selectionStart, element.selectionEnd);
                
                if (selectedText && selectedText.trim()) {
                    // Transform selected text
                    const transformedText = this.transform(selectedText, transformType);
                    
                    // Replace in WordPress editor
                    const start = element.selectionStart;
                    const end = element.selectionEnd;
                    element.value = element.value.substring(0, start) + transformedText + element.value.substring(end);
                    element.selectionStart = start + transformedText.length;
                    element.selectionEnd = start + transformedText.length;
                    
                    // Ensure WordPress compatibility
                    this.ensureWordPressCompatibility(element, element.value);
                    
                    this.showNotification('WordPress Text Transformed');
                    return { success: true, message: `WordPress ${transformType} transformation complete` };
                } else if (element.value && element.value.trim()) {
                    // Transform all content
                    const transformedText = this.transform(element.value, transformType);
                    element.value = transformedText;
                    
                    // Ensure WordPress compatibility
                    this.ensureWordPressCompatibility(element, transformedText);
                    
                    this.showNotification('WordPress Content Transformed');
                    return { success: true, message: `WordPress ${transformType} transformation complete` };
                }
            }
            
            // Fallback to standard transformation
            return await this.executeTransformation(transformType);
        } catch (error) {
            console.error(`SEO Time Machines: WordPress ${transformType} transformation error:`, error);
            return { error: error.message };
        }
    }
    
    // Enhanced transformation execution with TinyMCE support
    static async executeTinyMCETransformation(transformType) {
        try {
            console.log(`SEO Time Machines: Executing TinyMCE-aware ${transformType} transformation`);
            
            // First try TinyMCE-specific detection
            const tinyMceEditor = this.detectTinyMCEEditor();
            if (tinyMceEditor && tinyMceEditor.body) {
                const editor = tinyMceEditor.editor;
                const iframeBody = tinyMceEditor.body;
                const iframeWindow = tinyMceEditor.iframe.contentWindow;
                const iframeSelection = iframeWindow ? iframeWindow.getSelection() : null;
                
                let selectedText = '';
                let shouldReplaceSelection = false;
                let originalHtmlContent = '';
                
                // Check for selected text first
                if (iframeSelection && iframeSelection.toString().trim()) {
                    selectedText = iframeSelection.toString();
                    shouldReplaceSelection = true;
                } else if (editor && typeof editor.getContent === 'function') {
                    // Get content in both HTML and text formats for Visual mode compatibility
                    try {
                        originalHtmlContent = editor.getContent({ format: 'html' });
                        selectedText = editor.getContent({ format: 'text' });
                        console.log('SEO Time Machines: Retrieved content via TinyMCE API', {
                            htmlLength: originalHtmlContent.length,
                            textLength: selectedText.length
                        });
                    } catch (apiError) {
                        console.log('SEO Time Machines: TinyMCE API content retrieval failed:', apiError);
                        selectedText = iframeBody.textContent || iframeBody.innerText || '';
                        originalHtmlContent = iframeBody.innerHTML || '';
                    }
                } else {
                    // Fallback to DOM extraction
                    selectedText = iframeBody.textContent || iframeBody.innerText || '';
                    originalHtmlContent = iframeBody.innerHTML || '';
                }
                
                if (selectedText && selectedText.trim()) {
                    // Transform the text
                    const transformedText = this.transform(selectedText, transformType);
                    
                    if (transformedText === selectedText) {
                        this.showNotification('TinyMCE Text Unchanged');
                        return { success: true, message: `TinyMCE ${transformType} - text unchanged` };
                    }
                    
                    // Enhanced TinyMCE content manipulation with WordPress compatibility
                    let insertionSuccess = false;
                    
                    if (editor && typeof editor.setContent === 'function') {
                        try {
                            if (shouldReplaceSelection && editor.selection && typeof editor.selection.setContent === 'function') {
                                // Replace selected content with enhanced formatting
                                const formattedText = this.formatTextForTinyMCE(transformedText, originalHtmlContent);
                                editor.selection.setContent(formattedText);
                                console.log('SEO Time Machines: TinyMCE API replaced selected content with formatting');
                            } else {
                                // Enhanced full content replacement with WordPress compatibility
                                const htmlContent = this.formatTextForTinyMCE(transformedText, originalHtmlContent, true);
                                
                                // Set content with proper format for Visual mode
                                editor.setContent(htmlContent, { format: 'html' });
                                console.log('SEO Time Machines: TinyMCE API set formatted HTML content for Visual mode');
                                
                                // Enhanced WordPress Classic Editor synchronization
                                await this.synchronizeWordPressEditor(editor, htmlContent, transformedText);
                            }
                            insertionSuccess = true;
                        } catch (apiError) {
                            console.log('SEO Time Machines: TinyMCE API insertion failed:', apiError);
                        }
                    }
                    
                    // Fallback to DOM manipulation if API failed
                    if (!insertionSuccess) {
                        try {
                            if (shouldReplaceSelection && iframeSelection.rangeCount > 0) {
                                // Replace selection
                                const range = iframeSelection.getRangeAt(0);
                                range.deleteContents();
                                const textNode = tinyMceEditor.iframe.contentDocument.createTextNode(transformedText);
                                range.insertNode(textNode);
                                range.setStartAfter(textNode);
                                range.collapse(true);
                                iframeSelection.removeAllRanges();
                                iframeSelection.addRange(range);
                                console.log('SEO Time Machines: TinyMCE DOM replaced selected content');
                            } else {
                                // Replace all content with basic HTML structure
                                iframeBody.innerHTML = `<p>${transformedText.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`;
                                console.log('SEO Time Machines: TinyMCE DOM replaced all content with HTML');
                            }
                            insertionSuccess = true;
                        } catch (domError) {
                            console.log('SEO Time Machines: TinyMCE DOM insertion failed:', domError);
                        }
                    }
                    
                    // Enhanced TinyMCE compatibility for Visual mode
                    this.ensureTinyMCECompatibility(tinyMceEditor, transformedText);
                    
                    // Write to clipboard as backup
                    await this.bulletproofClipboardWrite(transformedText);
                    
                    if (insertionSuccess) {
                        this.showNotification('TinyMCE Text Transformed');
                        return { success: true, message: `TinyMCE ${transformType} transformation complete` };
                    } else {
                        this.showNotification('TinyMCE Transform Complete (Use Cmd+V to Paste)');
                        return { success: true, message: `TinyMCE ${transformType} transformation copied to clipboard` };
                    }
                } else {
                    console.log('SEO Time Machines: No content found in TinyMCE editor');
                    this.showNotification('No Content Found in TinyMCE');
                    return { success: false, message: 'No content found in TinyMCE editor' };
                }
            }
            
            // Fallback to standard transformation
            return await this.executeTransformation(transformType);
        } catch (error) {
            console.error(`SEO Time Machines: TinyMCE ${transformType} transformation error:`, error);
            return { error: error.message };
        }
    }
    
    // Reset method for cleanup
    static reset() {
        try {
            // Clear WordPress compatibility cache
            delete this._wordpressDetected;
            
            console.log('SEO Time Machines: Text Transformers reset completed');
            return { success: true };
        } catch (error) {
            console.error('SEO Time Machines: Text Transformers reset error:', error);
            return { error: error.message };
        }
    }
}

// Export for use by global shortcut manager
window.TextTransformers = TextTransformers;

// Also export for module usage if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TextTransformers;
}

console.log('SEO Time Machines: Text Transformers loaded successfully');