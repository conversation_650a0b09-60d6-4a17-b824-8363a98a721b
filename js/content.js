// Content script for Google Maps pages
// Namespace check to prevent conflicts with other extensions
if (window.GMBExtractorLoaded) {
  console.log('GMB Data Extractor: Already loaded, skipping...');
} else {
  window.GMBExtractorLoaded = true;
  
  // Extension loaded in completely passive mode - no automatic execution
  console.log('GMB Data Extractor: Content script loaded (passive mode - manual trigger only)');

  // YouTube Ads Count Message Relay - Listen for messages from MAIN world
  if (window.location.hostname.includes('youtube.com')) {
    window.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'STM_ADS_COUNT_UPDATE') {
        console.log('Content: Relaying YouTube ads count update:', event.data.count);
        
        // Forward to background script
        chrome.runtime.sendMessage({
          action: 'updateYouTubeAdsCount',
          count: event.data.count
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.log('Content: Background script message failed:', chrome.runtime.lastError.message);
          }
        });
      }
    });
  }

// Function to ensure we're on the Overview tab before extracting data
function ensureOverviewTab() {
  return new Promise((resolve) => {
            console.log('SEO Time Machines: Checking if we need to switch to Overview tab...');
    
    // Look for Overview tab button using aria-label pattern
    const overviewButton = document.querySelector('button[role="tab"][aria-label^="Overview"]');
    
    if (overviewButton) {
                  console.log('SEO Time Machines: Found Overview tab button:', overviewButton);
      
      // Check if Overview tab is already selected
      const isSelected = overviewButton.getAttribute('aria-selected') === 'true';
      
      if (isSelected) {
        console.log('GMB Extractor: Overview tab already selected');
        resolve();
      } else {
        console.log('GMB Extractor: Clicking Overview tab...');
        overviewButton.click();
        
        // Wait for the content to load after clicking
        setTimeout(() => {
          console.log('GMB Extractor: Overview tab content should be loaded');
          resolve();
        }, 1500);
      }
    } else {
      console.log('GMB Extractor: Overview tab button not found, proceeding anyway');
      resolve();
    }
  });
}

// Listen for messages from popup - ONLY responds to explicit user actions (button clicks)
// This extension does NOT run automatically or in the background
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // Add a check to ensure this is our extension making the request
  if (!request.action) {
    return false; // Not our request, ignore
  }
  
  console.log('GMB Data Extractor: Received manual action request:', request.action);
  
  if (request.action === "getData") {
    // Ensure we're on Overview tab before extracting data
    ensureOverviewTab().then(() => {
      const data = extractGMBData();
      sendResponse(data);
    }).catch(error => {
      console.error('GMB Data Extractor: Error ensuring Overview tab:', error);
      const data = extractGMBData(); // Try extraction anyway
      sendResponse(data);
    });
  } else if (request.action === "getAttributes") {
    // Extract attributes separately
    extractAboutAttributes().then(attributes => {
      sendResponse({ attributes: attributes });
    }).catch(error => {
      console.error('GMB Data Extractor: Error extracting attributes:', error);
      sendResponse({ error: error.message });
    });
  } else if (request.action === "openPersistentPopup") {
    console.log('GMB Data Extractor: Opening persistent popup...');
    createPersistentPopup();
    sendResponse({ success: true });
  } else if (request.action === "refreshPopup") {
    console.log('GMB Data Extractor: Refreshing persistent popup...');
    refreshPersistentPopup();
    sendResponse({ success: true });
  }
  return true; // Keep message channel open for async response
});

// ProList extraction now stores data globally in window.lastExtractedServices and window.lastExtractedAttributes
// No need for event listeners since data is accessed directly when popup opens

// Global Progress Bar Functions - Reusable by any extractor
function showGlobalProgress() {
    const progressContainer = document.getElementById('gmbGlobalProgress');
    if (progressContainer) {
        progressContainer.style.display = 'block';
    }
}

function hideGlobalProgress() {
    const progressContainer = document.getElementById('gmbGlobalProgress');
    if (progressContainer) {
        progressContainer.style.display = 'none';
    }
}

function updateGlobalProgress(current, total, progressText = null) {
    const progressFill = document.getElementById('gmbGlobalProgressFill');
    const progressBar = progressFill?.parentElement;
    const progressTextElement = document.getElementById('gmbGlobalProgressText');
    
    if (!progressFill || !progressBar || !progressTextElement) {
        console.log('Global progress elements not found, popup may not be open');
        return;
    }
    
    // Calculate progress percentage
    const progress = total > 0 ? (current / total) * 100 : 0;
    
    // Update progress fill width
    progressFill.style.width = `${progress}%`;
    progressBar.setAttribute('aria-valuenow', Math.round(progress));
    
    // Update progress text
    const displayText = progressText || `${current} / ${total} businesses processed`;
    progressTextElement.textContent = displayText;
    
    console.log(`Global progress updated: ${Math.round(progress)}% (${current}/${total})`);
}

function setGlobalProgressComplete(successText = 'Extraction completed!') {
    const progressFill = document.getElementById('gmbGlobalProgressFill');
    const progressBar = progressFill?.parentElement;
    const progressTextElement = document.getElementById('gmbGlobalProgressText');
    
    if (progressFill && progressBar && progressTextElement) {
        // Set to 100% and mark as complete
        progressFill.style.width = '100%';
        progressFill.classList.add('gmb-global-progress-complete');
        progressBar.setAttribute('aria-valuenow', '100');
        progressTextElement.textContent = successText;
        
        console.log('Global progress marked as complete');
    }
}

function setGlobalProgressError(errorText = 'Extraction failed') {
    const progressFill = document.getElementById('gmbGlobalProgressFill');
    const progressTextElement = document.getElementById('gmbGlobalProgressText');
    
    if (progressFill && progressTextElement) {
        // Mark as error
        progressFill.classList.add('gmb-global-progress-error');
        progressTextElement.textContent = errorText;
        
        console.log('Global progress marked as error');
    }
}

function resetGlobalProgress() {
    const progressFill = document.getElementById('gmbGlobalProgressFill');
    const progressBar = progressFill?.parentElement;
    const progressTextElement = document.getElementById('gmbGlobalProgressText');
    
    if (progressFill && progressBar && progressTextElement) {
        // Reset all states
        progressFill.style.width = '0%';
        progressFill.classList.remove('gmb-global-progress-complete', 'gmb-global-progress-error');
        progressBar.setAttribute('aria-valuenow', '0');
        progressTextElement.textContent = '0 / 0 businesses processed';
        
        console.log('Global progress reset');
    }
}

// Make progress functions globally available
window.showGlobalProgress = showGlobalProgress;
window.hideGlobalProgress = hideGlobalProgress;
window.updateGlobalProgress = updateGlobalProgress;
window.setGlobalProgressComplete = setGlobalProgressComplete;
window.setGlobalProgressError = setGlobalProgressError;
window.resetGlobalProgress = resetGlobalProgress;

function updateExtractionProgress(extractorType, status, data) {
  // Find progress elements in the persistent popup
  const progressText = document.querySelector('#gmb-persistent-popup .gmb-progress__text');
  const statusText = document.querySelector('#gmb-persistent-popup .gmb-status__text');
  const statusIndicator = document.querySelector('#gmb-persistent-popup .gmb-status__indicator');

  if (!progressText || !statusText || !statusIndicator) {
    console.log('Content: Progress elements not found, popup may not be open');
    return;
  }

  console.log(`Content: Updating ${extractorType} progress:`, status, 'data:', data);

  switch (status) {
    case 'started':
      progressText.textContent = `Starting ${extractorType} extraction...`;
      statusText.textContent = `Found ${data.total} businesses to process`;
      statusIndicator.className = 'gmb-status__indicator--active';
      break;

    case 'progress':
      progressText.textContent = `Processing business ${data.current}/${data.total}`;
      statusText.textContent = `${extractorType} extraction: ${data.current}/${data.total} completed`;
      break;

    case 'completed':
      progressText.textContent = `${extractorType} extraction completed!`;
      statusText.textContent = `Successfully extracted ${extractorType} from ${data.total} businesses`;
      statusIndicator.className = 'gmb-status__indicator--active';
      
      // Show completion notification
      GMBNotifications.showSuccess(
        `${extractorType.charAt(0).toUpperCase() + extractorType.slice(1)} Extraction Complete!`,
        `Successfully processed ${data.total} businesses`
      );

      // Enable export functionality
      if (extractorType === 'services') {
        window.lastExtractedServices = data.data;
      } else if (extractorType === 'attributes') {
        window.lastExtractedAttributes = data.data;
      }
      
      // Enable the export button
      const exportButton = document.getElementById('gmbExportBtn');
      if (exportButton) {
        exportButton.disabled = false;
        console.log('Content: Export button enabled after successful extraction');
      }
      
      // Update stats and preview using reusable function
      updateExtractionStats(data, extractorType);
      break;

    case 'error':
      progressText.textContent = `${extractorType} extraction failed`;
      statusText.textContent = `Error: ${data.error}`;
      statusIndicator.className = 'gmb-status__indicator--error';
      
      // Show error notification
      GMBNotifications.showError(
        `${extractorType.charAt(0).toUpperCase() + extractorType.slice(1)} Extraction Failed`,
        data.error
      );
      break;
  }
}

// NAP Button Injection Functionality
function initializeNAPButtonInjection() {
  console.log('GMB Data Extractor: Initializing NAP button injection...');
  
  // Check and inject NAP button immediately
  checkAndInjectNAPButton();
  
  // Set up observer to watch for navigation changes
  setupNAPButtonObserver();
}

function checkAndInjectNAPButton() {
  try {
    // Check if we're on a single business page by looking for the specific heading structure
    const headingContainer = document.querySelector('div > div.zvLtDc + h1.DUwDvf.lfPIob');
    
    if (headingContainer && !document.getElementById('gmb-nap-btn')) {
      console.log('GMB Data Extractor: Single business page detected, injecting NAP button');
      injectNAPButton(headingContainer);
    }
  } catch (error) {
    console.error('GMB Data Extractor: Error checking for NAP button injection:', error);
  }
}

function injectNAPButton(headingElement) {
  try {
    // Create the NAP button with semantic BEM classes
    const napButton = document.createElement('button');
    napButton.id = 'gmb-nap-btn';
    napButton.className = 'gmb-nap-button';
    napButton.textContent = 'Copy NAP';
    napButton.setAttribute('aria-label', 'Copy business name, address, and phone number');
    napButton.setAttribute('title', 'Copy NAP (Name, Address, Phone)');

    // Add click handler
    napButton.addEventListener('click', () => {
      copyNAPDataFromPage();
    });

    // Find the parent container of the heading to inject the button
    const parentContainer = headingElement.parentElement;
    if (parentContainer) {
      // Add semantic BEM class to parent container
      parentContainer.classList.add('gmb-business-heading-container');
      
      // Simply append the button to the parent container
      parentContainer.appendChild(napButton);
      
      console.log('GMB Data Extractor: NAP button injected successfully');
    }
  } catch (error) {
    console.error('GMB Data Extractor: Error injecting NAP button:', error);
  }
}

function copyNAPDataFromPage() {
  try {
    console.log('GMB Data Extractor: Extracting NAP data for copy...');
    
    // Extract business name from the heading
    const headingElement = document.querySelector('h1.DUwDvf.lfPIob');
    const businessName = headingElement ? headingElement.textContent.trim() : '';
    
    // Extract address using multiple selectors
    const addressSelectors = [
      '[data-attrid="kc:/collection/knowledge_panels/local_business:address"] span',
      '.LrzXr',
      '.Io6YTe',
      'button[data-item-id="address"] .fontBodyMedium',
      '.rogA2c'
    ];
    
    let address = '';
    for (const selector of addressSelectors) {
      const addressElement = document.querySelector(selector);
      if (addressElement) {
        address = addressElement.textContent.trim();
        break;
      }
    }
    
    // Extract phone using multiple selectors
    const phoneSelectors = [
      'button[aria-label^="Phone:"]',
      '[data-attrid="kc:/collection/knowledge_panels/local_business:phone"] span',
      'button[data-item-id="phone"] .fontBodyMedium',
      '.rogA2c[data-item-id="phone"]'
    ];
    
    let phone = '';
    for (const selector of phoneSelectors) {
      const phoneElement = document.querySelector(selector);
      if (phoneElement) {
        if (selector.includes('aria-label')) {
          // Extract from aria-label: "Phone: (02) 6681 6249"
          const ariaLabel = phoneElement.getAttribute('aria-label');
          const phoneMatch = ariaLabel.match(/Phone:\s*(.+)/);
          if (phoneMatch) {
            phone = phoneMatch[1].trim();
          }
        } else {
          phone = phoneElement.textContent.trim();
        }
        break;
      }
    }
    
    // Format as requested: Name\nAddress\nPhone
    const napText = `${businessName}\n${address}\n${phone}`;
    
    console.log('GMB Data Extractor: Extracted NAP data:', { businessName, address, phone });
    
    // Copy to clipboard
    navigator.clipboard.writeText(napText).then(() => {
      showNAPFeedback('Copied!', 'success');
      console.log('GMB Data Extractor: NAP data copied successfully');
    }).catch(err => {
      console.error('GMB Data Extractor: Failed to copy NAP data:', err);
      showNAPFeedback('Failed', 'error');
    });

  } catch (error) {
    console.error('GMB Data Extractor: Error extracting NAP data:', error);
    showNAPFeedback('Error', 'error');
  }
}

function showNAPFeedback(message, type) {
  const button = document.getElementById('gmb-nap-btn');
  if (button) {
    const originalText = button.textContent;
    const originalClasses = button.className;
    
    // Update button text and add feedback class
    button.textContent = message;
    button.className = `gmb-nap-button gmb-nap-button--${type}`;
    
    setTimeout(() => {
      button.textContent = originalText;
      button.className = originalClasses;
    }, 1500);
  }
}

function setupNAPButtonObserver() {
  // Watch for URL changes to re-inject button when navigating between businesses
  let currentUrl = window.location.href;
  
  const observer = new MutationObserver(() => {
    if (window.location.href !== currentUrl) {
      currentUrl = window.location.href;
      // Small delay to allow page to load
      setTimeout(() => {
        checkAndInjectNAPButton();
      }, 500);
    }
  });

  // Start observing
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // Also check periodically in case mutation observer misses changes
  setInterval(() => {
    checkAndInjectNAPButton();
  }, 2000);
}

// Initialize NAP button injection when the script loads
setTimeout(() => {
  initializeNAPButtonInjection();
}, 1000);

// Function to extract GMB data from the current page
/**
 * Extract business name from h1 element in DOM as a fallback method.
 * This function specifically looks for the h1 pattern used by Google Maps.
 * @returns {string|null} - The extracted business name or null if not found
 */
function extractBusinessNameFromH1() {
  console.log('GMB Extractor: Starting h1 fallback business name extraction...');
  
  try {
    // Look for the specific h1 pattern used by Google Maps
    // <h1 class="DUwDvf lfPIob"><span class="a5H0ec"></span>Business Name<span class="G0bp3e"></span></h1>
    const h1Element = document.querySelector('h1.DUwDvf.lfPIob');
    
    if (h1Element) {
      console.log('GMB Extractor: Found h1.DUwDvf.lfPIob element:', h1Element);
      
      // Extract text content, removing the span elements
      let businessName = h1Element.textContent.trim();
      
      // Clean up any extra whitespace
      businessName = businessName.replace(/\s+/g, ' ').trim();
      
      console.log('GMB Extractor: Extracted business name from h1:', businessName);
      
      // Validate the business name
      if (businessName && 
          businessName.length > 3 && 
          businessName.length < 200 && 
          businessName !== 'Google Maps' &&
          !businessName.toLowerCase().includes('google maps')) {
        console.log('GMB Extractor: ✅ Found valid business name in h1:', businessName);
        return businessName;
      } else {
        console.log('GMB Extractor: ❌ Business name failed validation:', {
          name: businessName,
          length: businessName?.length,
          isGoogleMaps: businessName === 'Google Maps'
        });
      }
    } else {
      console.log('GMB Extractor: No h1.DUwDvf.lfPIob element found');
    }
    
    // Fallback: try any h1 element that might contain the business name
    console.log('GMB Extractor: Trying fallback h1 search...');
    const allH1s = document.querySelectorAll('h1');
    
    for (const h1 of allH1s) {
      const businessName = h1.textContent?.trim();
      
      if (businessName && 
          businessName.length > 3 && 
          businessName.length < 200 && 
          businessName !== 'Google Maps' &&
          !businessName.toLowerCase().includes('google maps') &&
          !businessName.toLowerCase().includes('search') &&
          !businessName.toLowerCase().includes('menu')) {
        console.log('GMB Extractor: ✅ Found valid business name in fallback h1:', businessName);
        return businessName;
      }
    }
    
    console.log('GMB Extractor: ❌ Could not extract business name from any h1 element');
    return null;
    
  } catch (error) {
    console.error('GMB Extractor: Error in h1 business name extraction:', error);
    return null;
  }
}

function extractGMBData() {
  const data = {};
  
  try {
    const url = window.location.href;
    const pageSource = document.documentElement.outerHTML;
    
    console.log('GMB Extractor: Starting extraction...');
    console.log('GMB Extractor: URL:', url);
    console.log('GMB Extractor: Page source length:', pageSource.length);
    
    // Business name and address - extract from meta content first
    const metaName = document.querySelector('meta[itemprop="name"]');
    console.log('GMB Extractor: Meta name element:', metaName);
    
    if (metaName) {
      const content = metaName.getAttribute('content');
      console.log('GMB Extractor: Meta content:', content);
      
      if (content && content.includes('·')) {
        // Split by · to separate business name and address
        const parts = content.split('·');
        data.businessName = parts[0].trim();
        if (parts[1]) {
          data.address = parts[1].trim();
        }
        console.log('GMB Extractor: Extracted from meta - Name:', data.businessName, 'Address:', data.address);
      } else if (content) {
        data.businessName = content;
        console.log('GMB Extractor: Extracted business name only:', data.businessName);
      }
    } else {
      console.log('GMB Extractor: No meta[itemprop="name"] found');
      
      // Let's also try other meta tags
      const metaTitle = document.querySelector('meta[property="og:title"]');
      const metaDescription = document.querySelector('meta[name="description"]');
      console.log('GMB Extractor: og:title meta:', metaTitle ? metaTitle.getAttribute('content') : 'not found');
      console.log('GMB Extractor: description meta:', metaDescription ? metaDescription.getAttribute('content') : 'not found');
    }
    
    // If meta extraction failed, try DOM selectors for business name
    if (!data.businessName) {
      const nameSelectors = [
        'h1[data-attrid="title"]',
        '[data-attrid="title"] h1',
        'h1.x3AX1-LfntMc-header-title-title',
        '.x3AX1-LfntMc-header-title-title',
        '.SPZz6b h1',
        '.qrShPb h1',
        'h1.DUwDvf',
        '.DUwDvf'
      ];
      
      const nameElement = findElementBySelectors(nameSelectors);
      if (nameElement) {
        data.businessName = nameElement.textContent.trim();
      }
    }

    // Fallback: If business name is "Google Maps", extract from h1 element in DOM
    if (data.businessName === 'Google Maps' || !data.businessName) {
      const h1BusinessName = extractBusinessNameFromH1();
      if (h1BusinessName && h1BusinessName !== 'Google Maps') {
        data.businessName = h1BusinessName;
        console.log('GMB Extractor: ✅ Used h1 fallback for business name:', data.businessName);
      }
    }
    
    // If meta extraction failed, try DOM selectors for address
    if (!data.address) {
      const addressSelectors = [
        '[data-attrid="kc:/collection/knowledge_panels/local_business:address"] span',
        '.LrzXr',
        '.Io6YTe',
        'button[data-item-id="address"] .fontBodyMedium',
        '.rogA2c'
      ];
      
      const addressElement = findElementBySelectors(addressSelectors);
      if (addressElement) {
        data.address = addressElement.textContent.trim();
      }
    }
    
    // Phone number - extract from frontend HTML first to preserve formatting
    console.log('GMB Extractor: Looking for phone in frontend HTML...');
    
    // First try to find phone using aria-label pattern which preserves formatting
    const phoneButtonWithAriaLabel = document.querySelector('button[aria-label^="Phone:"]');
    if (phoneButtonWithAriaLabel) {
      const ariaLabel = phoneButtonWithAriaLabel.getAttribute('aria-label');
      console.log('GMB Extractor: Found phone aria-label:', ariaLabel);
      
      // Extract phone from "Phone: (02) 6681 6249 " format
      const phoneMatch = ariaLabel.match(/Phone:\s*(.+?)(?:\s*$)/);
      if (phoneMatch) {
        data.phone = phoneMatch[1].trim();
        console.log('GMB Extractor: Extracted formatted phone from aria-label:', data.phone);
      }
    }
    
    // If aria-label method fails, try DOM selectors for formatted phone display
    if (!data.phone) {
      console.log('GMB Extractor: Trying DOM selectors for formatted phone...');
      const phoneSelectors = [
        'button[data-item-id="phone"] .fontBodyMedium',
        'button[data-item-id="phone"] .Io6YTe',
        '[data-attrid="kc:/collection/knowledge_panels/local_business:phone"] span',
        '.LrzXr.zdqRlf.kno-fv',
        'span[data-phone-number]'
      ];
      
      const phoneElement = findElementBySelectors(phoneSelectors);
      if (phoneElement) {
        data.phone = phoneElement.textContent.trim();
        console.log('GMB Extractor: Extracted formatted phone from DOM:', data.phone);
      }
    }
    
    // If DOM fails, try href attribute but clean it up for display
    if (!data.phone) {
      console.log('GMB Extractor: Trying phone link href as fallback...');
      const phoneLink = document.querySelector('a[href^="tel:"]');
      if (phoneLink) {
        const rawPhone = phoneLink.href.replace('tel:', '');
        // Try to format the raw phone number if it's all digits
        if (rawPhone.match(/^\d+$/)) {
          // Basic Australian phone formatting for common patterns
          if (rawPhone.length === 10 && rawPhone.startsWith('02')) {
            data.phone = `(${rawPhone.substring(0, 2)}) ${rawPhone.substring(2, 6)} ${rawPhone.substring(6)}`;
          } else if (rawPhone.length === 10 && (rawPhone.startsWith('03') || rawPhone.startsWith('07') || rawPhone.startsWith('08'))) {
            data.phone = `(${rawPhone.substring(0, 2)}) ${rawPhone.substring(2, 6)} ${rawPhone.substring(6)}`;
          } else {
            data.phone = rawPhone; // Use as-is if we can't format it
          }
        } else {
          data.phone = rawPhone;
        }
        console.log('GMB Extractor: Extracted and formatted phone from href:', data.phone);
      }
    }
    
    // Last resort: extract from page source (but this will be unformatted)
    if (!data.phone) {
      console.log('GMB Extractor: Falling back to page source for phone (unformatted)...');
      const phoneMatch = pageSource.match(/tel:(\d+)/);
      if (phoneMatch) {
        const rawPhone = phoneMatch[1];
        // Try to format the raw phone number
        if (rawPhone.length === 10 && rawPhone.startsWith('02')) {
          data.phone = `(${rawPhone.substring(0, 2)}) ${rawPhone.substring(2, 6)} ${rawPhone.substring(6)}`;
        } else if (rawPhone.length === 10 && (rawPhone.startsWith('03') || rawPhone.startsWith('07') || rawPhone.startsWith('08'))) {
          data.phone = `(${rawPhone.substring(0, 2)}) ${rawPhone.substring(2, 6)} ${rawPhone.substring(6)}`;
        } else {
          data.phone = rawPhone;
        }
        console.log('GMB Extractor: Extracted and formatted phone from page source:', data.phone);
      }
    }
    
    // Website - try DOM selectors first
    const websiteSelectors = [
      '[data-attrid="kc:/collection/knowledge_panels/local_business:website"] a',
      '.CL9Uqc a',
      'a[data-item-id="authority"]',
      'a[data-value="Website"]',
      'a[href^="http"]:not([href*="google"]):not([href*="maps"])'
    ];
    
    const websiteElement = findElementBySelectors(websiteSelectors);
    if (websiteElement) {
      data.website = websiteElement.href;
    }
    
    // Booking Link - extract separately from website
    const bookingLinkSelectors = [
      'a[data-item-id="action:3"]',
      'a[data-tooltip="Open booking link"]',
      'a[href*="book"]',
      'a[href*="booking"]',
      'a[href*="appointment"]',
      'a[href*="reserve"]'
    ];
    
    const bookingElement = findElementBySelectors(bookingLinkSelectors);
    if (bookingElement) {
      data.bookingLink = bookingElement.href;
    }
    
    // Business Status - Check for "Claim this business" indicator
    // Start with no status (will be determined by detection)
    data.businessStatus = null;
    
    // Look for unclaimed business indicator in specific div with exact classes
    console.log('GMB Extractor: Checking for "Claim this business" in specific elements...');
    const claimBusinessElements = document.querySelectorAll('.Io6YTe.fontBodyMedium.kR99db.fdkmkc');
    let foundClaimText = false;
    
    console.log(`GMB Extractor: Found ${claimBusinessElements.length} elements with target classes`);
    
    for (const element of claimBusinessElements) {
      console.log('GMB Extractor: Checking element:', element.textContent?.trim());
      if (element.textContent && element.textContent.trim().toLowerCase() === 'claim this business') {
        data.businessStatus = 'Un-Claimed';
        foundClaimText = true;
        console.log('GMB Extractor: Found "Claim this business" - setting status to Un-Claimed');
        console.log('GMB Extractor: Element text:', element.textContent.trim());
        console.log('GMB Extractor: Element classes:', element.className);
        break;
      }
    }
    
    // Additional fallback check - look for any element with just Io6YTe class
    if (!foundClaimText) {
      console.log('GMB Extractor: Trying fallback with just .Io6YTe class...');
      const fallbackElements = document.querySelectorAll('.Io6YTe');
      console.log(`GMB Extractor: Found ${fallbackElements.length} elements with .Io6YTe class`);
      
      for (const element of fallbackElements) {
        console.log('GMB Extractor: Checking fallback element:', element.textContent?.trim());
        if (element.textContent && element.textContent.trim().toLowerCase() === 'claim this business') {
          data.businessStatus = 'Un-Claimed';
          foundClaimText = true;
          console.log('GMB Extractor: Found "Claim this business" in fallback - setting status to Un-Claimed');
          console.log('GMB Extractor: Element text:', element.textContent.trim());
          console.log('GMB Extractor: Element classes:', element.className);
          break;
        }
      }
    }
    
    // If no claim text found, default to Claimed
    if (!foundClaimText) {
      data.businessStatus = 'Claimed';
      console.log('GMB Extractor: No "Claim this business" found - setting status to Claimed');
    }
    
    // Coordinates (latitude/longitude) - extract from URL first as we need them for category extraction
    const coordsMatch = url.match(/@(-?\d+\.\d+),(-?\d+\.\d+)/);
    if (coordsMatch) {
      data.coordinates = {
        latitude: coordsMatch[1],
        longitude: coordsMatch[2]
      };
    }

    // Categories - extract from page source using specific pattern
    console.log('GMB Extractor: Looking for categories in page source...');
    console.log('GMB Extractor: Business name for search:', data.businessName);
    console.log('GMB Extractor: Coordinates for search:', data.coordinates);
    
    // Look for the specific pattern: coordinates, business name, then categories
    // Pattern: null,null,-28.8715241,153.5632507],"0x6b90819ae0153ff3:0xf4687b91094cbe2e","Foundation Health Osteopathy | Ballina",null,["Osteopath","Kinesiologist","Massage therapist","Naturopathic practitioner","Sports massage therapist"]
    
    let foundCategories = false;
    
    // First, extract coordinates and business name from the pattern
    if (data.coordinates && data.businessName) {
      const lat = data.coordinates.latitude;
      const lng = data.coordinates.longitude;
      const businessName = data.businessName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // Escape regex special chars
      
      // Create pattern to find categories after coordinates and business name
      // Look for: lat,lng followed by business name, then null, then array of categories
      const coordPattern = `${lat},${lng}[^"]*"[^"]*","${businessName}"[^\\[]*\\[("([^"]+)"(?:,"([^"]+)")*?)\\]`;
      console.log('GMB Extractor: Searching for coordinate-based pattern:', coordPattern);
      
      const coordMatch = pageSource.match(new RegExp(coordPattern));
      console.log('GMB Extractor: Coordinate-based match:', coordMatch);
      
      if (coordMatch && coordMatch[1]) {
        try {
          const categoryString = `[${coordMatch[1]}]`;
          console.log('GMB Extractor: Trying to parse category string:', categoryString);
          const categoryArray = JSON.parse(categoryString);
          
          if (Array.isArray(categoryArray) && categoryArray.length > 0) {
            data.categories = categoryArray;
            data.mainCategory = categoryArray[0];
            data.additionalCategories = categoryArray.slice(1);
            console.log('GMB Extractor: Found categories using coordinate pattern:', categoryArray);
            console.log('GMB Extractor: Main category:', data.mainCategory);
            console.log('GMB Extractor: Additional categories:', data.additionalCategories);
            console.log('GMB Extractor: Total categories found:', categoryArray.length);
            foundCategories = true;
          }
        } catch (e) {
          console.log('GMB Extractor: Error parsing categories from coordinate pattern:', e);
        }
      }
    }
    
    // If coordinate-based search failed, try to find the pattern without exact coordinates
    if (!foundCategories && data.businessName) {
      console.log('GMB Extractor: Trying alternative pattern matching...');
      
      const businessName = data.businessName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      
      // Look for the pattern: latitude,longitude followed by business name and categories
      // Pattern: -XX.XXXXXXX,XXX.XXXXXXX],"hexid","Business Name",null,["Category1","Category2",...]
      const altPattern = `(-?\\d+\\.\\d+),(-?\\d+\\.\\d+)[^"]*"[^"]*","${businessName}"[^\\[]*\\[("([^"]+)"(?:,"([^"]+)")*?)\\]`;
      console.log('GMB Extractor: Searching for alternative pattern:', altPattern);
      
      const altMatch = pageSource.match(new RegExp(altPattern));
      console.log('GMB Extractor: Alternative pattern match:', altMatch);
      
      if (altMatch) {
        // Extract coordinates if we didn't have them before
        if (!data.coordinates) {
          data.coordinates = {
            latitude: altMatch[1],
            longitude: altMatch[2]
          };
          console.log('GMB Extractor: Extracted coordinates from pattern:', data.coordinates);
        }
        
        // Extract categories
        if (altMatch[3]) {
          try {
            const categoryString = `[${altMatch[3]}]`;
            console.log('GMB Extractor: Trying to parse category string:', categoryString);
            const categoryArray = JSON.parse(categoryString);
            
            if (Array.isArray(categoryArray) && categoryArray.length > 0) {
              data.categories = categoryArray;
              data.mainCategory = categoryArray[0];
              data.additionalCategories = categoryArray.slice(1);
              console.log('GMB Extractor: Found categories using alternative pattern:', categoryArray);
              console.log('GMB Extractor: Main category:', data.mainCategory);
              console.log('GMB Extractor: Additional categories:', data.additionalCategories);
              console.log('GMB Extractor: Total categories found:', categoryArray.length);
              foundCategories = true;
            }
          } catch (e) {
            console.log('GMB Extractor: Error parsing categories from alternative pattern:', e);
          }
        }
      }
    }
    
    // If still no categories found, try a more comprehensive search for the lat/lng + business name pattern
    if (!foundCategories) {
      console.log('GMB Extractor: Trying comprehensive lat/lng + business name search...');
      
      // First, let's search for any occurrence of coordinates followed by business name and categories
      // This will help us find the exact pattern in the page source
      if (data.coordinates) {
        const lat = data.coordinates.latitude;
        const lng = data.coordinates.longitude;
        
        console.log(`GMB Extractor: Searching for coordinates: ${lat},${lng}`);
        
        // Search for the coordinates in the page source to see what follows
        const coordSearchPattern = new RegExp(`${lat.replace('.', '\\.')},${lng.replace('.', '\\.')}`, 'g');
        let coordMatch;
        let searchIndex = 0;
        let foundCoordinates = false;
        
        while ((coordMatch = coordSearchPattern.exec(pageSource)) !== null && searchIndex < 5) {
          foundCoordinates = true;
          const startIndex = coordMatch.index;
          const endIndex = Math.min(startIndex + 1000, pageSource.length); // Look at 1000 chars after coordinates
          const snippet = pageSource.substring(startIndex, endIndex);
          
          console.log(`GMB Extractor: Found coordinates at index ${startIndex}, snippet:`, snippet.substring(0, 200));
          
          // Look for business name and categories in this snippet
          const businessNamePattern = /"([^"]{5,100})"/g;
          let nameMatch;
          
          while ((nameMatch = businessNamePattern.exec(snippet)) !== null) {
            const potentialBusinessName = nameMatch[1];
            
            console.log('GMB Extractor: Found potential business name in snippet:', potentialBusinessName);
            
            // Check if this looks like our business name (more flexible matching)
            const businessNameToMatch = data.businessName ? data.businessName.split('|')[0].trim() : '';
            const nameMatches = businessNameToMatch && (
              potentialBusinessName.includes(businessNameToMatch) ||
              businessNameToMatch.includes(potentialBusinessName) ||
              potentialBusinessName.toLowerCase().includes(businessNameToMatch.toLowerCase()) ||
              businessNameToMatch.toLowerCase().includes(potentialBusinessName.toLowerCase())
            );
            
            if (nameMatches || !data.businessName) {
              console.log('GMB Extractor: Found matching business name in snippet:', potentialBusinessName);
              
              // Now look for categories after this business name
              const nameIndex = nameMatch.index + nameMatch[0].length;
              const afterNameSnippet = snippet.substring(nameIndex, Math.min(nameIndex + 500, snippet.length));
              
              console.log('GMB Extractor: Looking for categories after business name:', afterNameSnippet.substring(0, 200));
              
              // Look for category arrays in the snippet after the business name
              const categoryArrayPattern = /\[("([^"]+)"(?:,"([^"]+)")*?)\]/g;
              let catMatch;
              
              while ((catMatch = categoryArrayPattern.exec(afterNameSnippet)) !== null) {
                const categoriesString = catMatch[1];
                console.log('GMB Extractor: Found potential categories after business name:', categoriesString);
                
                try {
                  const categoryString = `[${categoriesString}]`;
                  const categoryArray = JSON.parse(categoryString);
                  
                  console.log('GMB Extractor: Parsed category array:', categoryArray);
                  
                  // Validate that these look like business categories
                  if (Array.isArray(categoryArray) && 
                      categoryArray.length > 0 && 
                      categoryArray.length <= 10 &&
                      categoryArray.every(cat => typeof cat === 'string' && cat.length > 2 && cat.length < 100)) {
                    
                    // Filter out obvious non-categories including "Favourites"
                    const validCategories = categoryArray.filter(cat => {
                      const lowerCat = cat.toLowerCase();
                      return !lowerCat.includes('http') && 
                             !lowerCat.includes('google') && 
                             !lowerCat.includes('/maps/') &&
                             !lowerCat.includes('ggpht') &&
                             !lowerCat.includes('khms') &&
                             !lowerCat.match(/^[a-z]{2,3}$/) &&
                             !lowerCat.includes('@') &&
                             !lowerCat.startsWith('/') &&
                             lowerCat !== 'favourites' &&
                             cat.match(/^[A-Za-z\s&\-'.,()]+$/);
                    });
                    
                    console.log('GMB Extractor: Valid categories after filtering:', validCategories);
                    
                                         if (validCategories.length > 0 && validCategories.length === categoryArray.length) {
                       data.categories = validCategories;
                       data.mainCategory = validCategories[0];
                       data.additionalCategories = validCategories.slice(1);
                       console.log('GMB Extractor: ✅ Found categories using comprehensive search:', validCategories);
                       console.log('GMB Extractor: Main category:', data.mainCategory);
                       console.log('GMB Extractor: Additional categories:', data.additionalCategories);
                       console.log('GMB Extractor: Total categories found:', validCategories.length);
                       foundCategories = true;
                       break;
                     }
                  }
                } catch (e) {
                  console.log('GMB Extractor: Error parsing categories from comprehensive search:', e);
                }
              }
              
              if (foundCategories) break;
            }
          }
          
          if (foundCategories) break;
          searchIndex++;
        }
        
        if (!foundCoordinates) {
          console.log('GMB Extractor: ⚠️ Coordinates not found in page source. This might indicate the page structure has changed.');
        }
      } else {
        console.log('GMB Extractor: ⚠️ No coordinates available for search.');
      }
    }
    
        // If still no categories found, try a more aggressive search for the exact pattern
    if (!foundCategories) {
      console.log('GMB Extractor: Trying aggressive pattern search for lat,lng + business name + categories...');
      
      // First try the exact pattern from your example
      // Pattern: null,null,-28.8715241,153.5632507],"0x6b90819ae0153ff3:0xf4687b91094cbe2e","Foundation Health Osteopathy | Ballina",null,["Osteopath","Kinesiologist","Massage therapist","Naturopathic practitioner","Sports massage therapist"]
      const exactPattern = /(-?\d+\.\d+),(-?\d+\.\d+)\],"[^"]*","([^"]+)",null,\[("([^"]+)"(?:,"([^"]+)")*?)\]/g;
      
      let match;
      let searchIndex = 0;
      
      console.log('GMB Extractor: Trying exact pattern first...');
      while ((match = exactPattern.exec(pageSource)) !== null && searchIndex < 5) {
        const foundLat = match[1];
        const foundLng = match[2];
        const foundBusinessName = match[3];
        const categoriesString = match[4];
        
        console.log(`GMB Extractor: Found exact pattern match ${searchIndex + 1}:`, {
          lat: foundLat,
          lng: foundLng,
          businessName: foundBusinessName,
          categoriesString: categoriesString
        });
        
        // Check if coordinates match (if we have them)
        let coordsMatch = true;
        if (data.coordinates) {
          const latDiff = Math.abs(parseFloat(foundLat) - parseFloat(data.coordinates.latitude));
          const lngDiff = Math.abs(parseFloat(foundLng) - parseFloat(data.coordinates.longitude));
          coordsMatch = latDiff < 0.001 && lngDiff < 0.001;
        }
        
        // Check if business name matches (if we have it)
        let nameMatches = true;
        if (data.businessName) {
          const businessNameToMatch = data.businessName.split('|')[0].trim().toLowerCase();
          const foundNameLower = foundBusinessName.toLowerCase();
          nameMatches = foundNameLower.includes(businessNameToMatch) || 
                       businessNameToMatch.includes(foundNameLower);
        }
        
        console.log('GMB Extractor: Exact pattern validation:', { coordsMatch, nameMatches });
        
        if (coordsMatch || nameMatches || (!data.coordinates && !data.businessName)) {
          // Parse the categories string as JSON array
          try {
            const categoryString = `[${categoriesString}]`;
            console.log('GMB Extractor: Trying to parse exact pattern category string:', categoryString);
            const categoryArray = JSON.parse(categoryString);
            
            if (Array.isArray(categoryArray) && categoryArray.length > 0) {
              // Filter valid categories
              const extractedCategories = categoryArray.filter(cat => {
                if (typeof cat !== 'string') return false;
                const lowerCat = cat.toLowerCase();
                return cat.length > 2 && 
                       cat.length < 80 &&
                       !lowerCat.includes('http') && 
                       !lowerCat.includes('google') && 
                       !lowerCat.includes('/maps/') &&
                       !lowerCat.includes('ggpht') &&
                       !lowerCat.includes('khms') &&
                       !lowerCat.match(/^[a-z]{2,3}$/) &&
                       !lowerCat.includes('@') &&
                       !lowerCat.startsWith('/') &&
                       cat.match(/^[A-Za-z\s&\-'.,()]+$/);
              });
              
              console.log('GMB Extractor: Extracted categories from exact pattern:', extractedCategories);
              
              if (extractedCategories.length > 0) {
                data.categories = extractedCategories;
                data.mainCategory = extractedCategories[0];
                data.additionalCategories = extractedCategories.slice(1);
                
                // Update coordinates and business name if we found better ones
                if (!data.coordinates) {
                  data.coordinates = { latitude: foundLat, longitude: foundLng };
                }
                if (!data.businessName || foundBusinessName.length > data.businessName.length) {
                  data.businessName = foundBusinessName;
                }
                
                console.log('GMB Extractor: ✅ Found categories using exact pattern:', extractedCategories);
                console.log('GMB Extractor: Main category:', data.mainCategory);
                console.log('GMB Extractor: Additional categories:', data.additionalCategories);
                console.log('GMB Extractor: Total categories found:', extractedCategories.length);
                foundCategories = true;
                break;
              }
            }
          } catch (e) {
            console.log('GMB Extractor: Error parsing categories from exact pattern:', e);
          }
        }
        
        searchIndex++;
      }
      
      // If exact pattern didn't work, try the more flexible pattern
      if (!foundCategories) {
        console.log('GMB Extractor: Exact pattern failed, trying flexible pattern...');
        
                // Look for any pattern that has coordinates followed by categories
        // Pattern: lat,lng],"hexid","business name",null,["category1","category2",...] until null
        const aggressivePattern = /(-?\d+\.\d+),(-?\d+\.\d+)\][^"]*"[^"]*"[^"]*"([^"]+)"[^[]*\[("([^"]+)"(?:,"([^"]+)")*?)\]/g;
        
        let flexMatch;
        let flexSearchIndex = 0;
      
              while ((flexMatch = aggressivePattern.exec(pageSource)) !== null && flexSearchIndex < 10) {
          const foundLat = flexMatch[1];
          const foundLng = flexMatch[2];
          const foundBusinessName = flexMatch[3];
          const categoriesString = flexMatch[4];
          
          console.log(`GMB Extractor: Found flexible pattern match ${flexSearchIndex + 1}:`, {
            lat: foundLat,
            lng: foundLng,
            businessName: foundBusinessName,
            categoriesString: categoriesString
          });
          
          // Check if coordinates match (if we have them)
          let coordsMatch = true;
          if (data.coordinates) {
            const latDiff = Math.abs(parseFloat(foundLat) - parseFloat(data.coordinates.latitude));
            const lngDiff = Math.abs(parseFloat(foundLng) - parseFloat(data.coordinates.longitude));
            coordsMatch = latDiff < 0.001 && lngDiff < 0.001;
          }
          
          // Check if business name matches (if we have it)
          let nameMatches = true;
          if (data.businessName) {
            const businessNameToMatch = data.businessName.split('|')[0].trim().toLowerCase();
            const foundNameLower = foundBusinessName.toLowerCase();
            nameMatches = foundNameLower.includes(businessNameToMatch) || 
                         businessNameToMatch.includes(foundNameLower);
          }
          
          console.log('GMB Extractor: Flexible pattern validation:', { coordsMatch, nameMatches });
          
          if (coordsMatch && nameMatches) {
            // Parse the categories string as JSON array
            try {
              const categoryString = `[${categoriesString}]`;
              console.log('GMB Extractor: Trying to parse flexible pattern category string:', categoryString);
              const categoryArray = JSON.parse(categoryString);
              
              if (Array.isArray(categoryArray) && categoryArray.length > 0) {
                // Filter valid categories including "Favourites"
                const extractedCategories = categoryArray.filter(cat => {
                  if (typeof cat !== 'string') return false;
                  const lowerCat = cat.toLowerCase();
                  return cat.length > 2 && 
                         cat.length < 80 &&
                         !lowerCat.includes('http') && 
                         !lowerCat.includes('google') && 
                         !lowerCat.includes('/maps/') &&
                         !lowerCat.includes('ggpht') &&
                         !lowerCat.includes('khms') &&
                         !lowerCat.match(/^[a-z]{2,3}$/) &&
                         !lowerCat.includes('@') &&
                         !lowerCat.startsWith('/') &&
                         lowerCat !== 'favourites' &&
                         cat.match(/^[A-Za-z\s&\-'.,()]+$/);
                });
                
                console.log('GMB Extractor: Extracted categories from flexible pattern:', extractedCategories);
                
                if (extractedCategories.length > 0) {
                  data.categories = extractedCategories;
                  data.mainCategory = extractedCategories[0];
                  data.additionalCategories = extractedCategories.slice(1);
                  
                  // Update coordinates and business name if we found better ones
                  if (!data.coordinates) {
                    data.coordinates = { latitude: foundLat, longitude: foundLng };
                  }
                  if (!data.businessName || foundBusinessName.length > data.businessName.length) {
                    data.businessName = foundBusinessName;
                  }
                  
                  console.log('GMB Extractor: ✅ Found categories using flexible pattern:', extractedCategories);
                  console.log('GMB Extractor: Main category:', data.mainCategory);
                  console.log('GMB Extractor: Additional categories:', data.additionalCategories);
                  console.log('GMB Extractor: Total categories found:', extractedCategories.length);
                  foundCategories = true;
                  break;
                }
              }
            } catch (e) {
              console.log('GMB Extractor: Error parsing categories from flexible pattern:', e);
            }
          }
          
          flexSearchIndex++;
        }
      }
    }
    
    // If still no categories found, try searching for any business name + categories pattern
    if (!foundCategories && data.businessName) {
      console.log('GMB Extractor: Trying business name + categories search...');
      
      const businessNameParts = data.businessName.split('|')[0].trim().split(' ');
      const mainBusinessName = businessNameParts.slice(0, Math.min(3, businessNameParts.length)).join(' '); // Use first 3 words
      
      console.log('GMB Extractor: Searching for business name:', mainBusinessName);
      
      // Find all occurrences of the business name
      const businessNamePattern = new RegExp(mainBusinessName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
      let nameMatch;
      let searchIndex = 0;
      
      while ((nameMatch = businessNamePattern.exec(pageSource)) !== null && searchIndex < 3) {
        const startIndex = Math.max(0, nameMatch.index - 200);
        const endIndex = Math.min(nameMatch.index + 500, pageSource.length);
        const snippet = pageSource.substring(startIndex, endIndex);
        
        console.log(`GMB Extractor: Found business name at index ${nameMatch.index}, snippet:`, snippet.substring(0, 300));
        
        // Look for category arrays near this business name
        const categoryArrayPattern = /\[("([^"]+)"(?:,"([^"]+)")*?)\]/g;
        let catMatch;
        
        while ((catMatch = categoryArrayPattern.exec(snippet)) !== null) {
          const categoriesString = catMatch[1];
          console.log('GMB Extractor: Found potential categories near business name:', categoriesString);
          
          try {
            const categoryString = `[${categoriesString}]`;
            const categoryArray = JSON.parse(categoryString);
            
            // Validate categories
            if (Array.isArray(categoryArray) && 
                categoryArray.length > 0 && 
                categoryArray.length <= 8 &&
                categoryArray.every(cat => typeof cat === 'string' && cat.length > 3 && cat.length < 80)) {
              
              // Filter out obvious non-categories including "Favourites"
              const validCategories = categoryArray.filter(cat => {
                const lowerCat = cat.toLowerCase();
                return !lowerCat.includes('http') && 
                       !lowerCat.includes('google') && 
                       !lowerCat.includes('/maps/') &&
                       !lowerCat.includes('ggpht') &&
                       !lowerCat.match(/^[a-z]{2,3}$/) &&
                       !lowerCat.includes('@') &&
                       !lowerCat.startsWith('/') &&
                       lowerCat !== 'favourites' &&
                       cat.match(/^[A-Za-z\s&\-'.,()]+$/);
              });
              
              if (validCategories.length > 0 && validCategories.length === categoryArray.length) {
                data.categories = validCategories;
                data.mainCategory = validCategories[0];
                data.additionalCategories = validCategories.slice(1);
                console.log('GMB Extractor: Found categories using business name search:', validCategories);
                console.log('GMB Extractor: Main category:', data.mainCategory);
                console.log('GMB Extractor: Additional categories:', data.additionalCategories);
                console.log('GMB Extractor: Total categories found:', validCategories.length);
                foundCategories = true;
                break;
              }
            }
          } catch (e) {
            console.log('GMB Extractor: Error parsing categories from business name search:', e);
          }
        }
        
        if (foundCategories) break;
        searchIndex++;
      }
    }
    
    // If coordinate-based search failed, try a more general approach
    if (!foundCategories) {
      // Look for business name followed by categories in the page source
      if (data.businessName) {
        const businessName = data.businessName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const namePattern = `"${businessName}"[^\\[]*\\[("([^"]+)"(?:,"([^"]+)")*?)\\]`;
        console.log('GMB Extractor: Searching for name-based pattern:', namePattern);
        
        const nameMatch = pageSource.match(new RegExp(namePattern));
        console.log('GMB Extractor: Name-based match:', nameMatch);
        
        if (nameMatch && nameMatch[1]) {
          try {
            const categoryString = `[${nameMatch[1]}]`;
            console.log('GMB Extractor: Trying to parse category string:', categoryString);
            const categoryArray = JSON.parse(categoryString);
            
            if (Array.isArray(categoryArray) && categoryArray.length > 0) {
              // Filter out "Favourites" and other invalid categories
              const validCategories = categoryArray.filter(cat => {
                const lowerCat = cat.toLowerCase();
                return typeof cat === 'string' &&
                       cat.length > 2 && 
                       cat.length < 80 &&
                       !lowerCat.includes('http') && 
                       !lowerCat.includes('google') && 
                       !lowerCat.includes('/maps/') &&
                       !lowerCat.includes('ggpht') &&
                       !lowerCat.includes('khms') &&
                       !lowerCat.match(/^[a-z]{2,3}$/) &&
                       !lowerCat.includes('@') &&
                       !lowerCat.startsWith('/') &&
                       lowerCat !== 'favourites' &&
                       cat.match(/^[A-Za-z\s&\-'.,()]+$/);
              });
              
              if (validCategories.length > 0) {
                data.categories = validCategories;
                data.mainCategory = validCategories[0];
                data.additionalCategories = validCategories.slice(1);
                console.log('GMB Extractor: Found categories using name pattern:', validCategories);
                foundCategories = true;
              }
            }
          } catch (e) {
            console.log('GMB Extractor: Error parsing categories from name pattern:', e);
          }
        }
      }
    }
    
    // Broad category search disabled - was picking up irrelevant data
    // If still no categories found, try the original broad search as fallback
    if (!foundCategories) {
      console.log('GMB Extractor: No categories found using specific patterns. Broad search disabled to avoid false positives.');
      console.log('GMB Extractor: Consider checking the page source manually for the category pattern.');
    }
    
    // If page source extraction failed, try DOM selectors for main category
    if (!data.categories) {
      console.log('GMB Extractor: Falling back to DOM selectors for categories...');
      const categorySelectors = [
        '[data-attrid="kc:/collection/knowledge_panels/local_business:category"] span',
        '.YhemCb',
        '.DkEaL',
        'button[jsaction*="category"] .fontBodyMedium'
      ];
      
      const categoryElement = findElementBySelectors(categorySelectors);
      if (categoryElement) {
        const mainCategoryFromDOM = categoryElement.textContent.trim();
        console.log('GMB Extractor: Found main category from DOM:', mainCategoryFromDOM);
        
        // Now search for additional categories in page source using the main category
        const additionalCategories = findAdditionalCategoriesAfterMain(pageSource, mainCategoryFromDOM);
        
        data.categories = [mainCategoryFromDOM, ...additionalCategories];
        data.mainCategory = mainCategoryFromDOM;
        data.additionalCategories = additionalCategories;
        
        console.log('GMB Extractor: Extracted main category from DOM:', data.mainCategory);
        console.log('GMB Extractor: Additional categories found in page source:', data.additionalCategories);
        console.log('GMB Extractor: Total categories found:', data.categories.length);
      }
    }
    
    // Rating - try DOM selectors first
    const ratingSelectors = [
      '[data-attrid="kc:/collection/knowledge_panels/local_business:star_score"] span',
      '.ceNzKf',
      '.MW4etd',
      '.fontDisplayLarge',
      '.Aq14fc'
    ];
    
    const ratingElement = findElementBySelectors(ratingSelectors);
    if (ratingElement) {
      data.rating = ratingElement.textContent.trim();
    }
    
    // Average Rating - extract from aria-label pattern "N stars" in DOM
    console.log('GMB Extractor: Looking for Average Rating with aria-label pattern in DOM...');
    
    // Try multiple selectors to find the rating element
    const averageRatingSelectors = [
      'span.ceNzKf[aria-label*="stars"]',
      'span[role="img"][aria-label*="stars"]',
      'span[aria-label*="stars"]',
      '.ceNzKf[aria-label]'
    ];
    
    let averageRatingElement = null;
    for (const selector of averageRatingSelectors) {
      averageRatingElement = document.querySelector(selector);
      if (averageRatingElement) {
        console.log(`GMB Extractor: Found rating element with selector: ${selector}`);
        break;
      }
    }
    
    if (averageRatingElement) {
      const ariaLabel = averageRatingElement.getAttribute('aria-label');
      console.log('GMB Extractor: Found aria-label:', ariaLabel);
      
      if (ariaLabel && ariaLabel.trim()) {
        // Extract the number from "N stars" or "N.N stars"
        const ratingMatch = ariaLabel.match(/(\d+(?:\.\d+)?)\s+stars?/);
        if (ratingMatch) {
          data.averageRating = ratingMatch[1];
          console.log('GMB Extractor: Extracted average rating:', data.averageRating);
        } else {
          console.log('GMB Extractor: Aria-label found but no rating pattern matched:', ariaLabel);
        }
      } else {
        console.log('GMB Extractor: Aria-label is empty or null');
      }
    } else {
      console.log('GMB Extractor: No average rating element found with any selector');
      
      // Debug: Let's see what rating-related elements exist
      const allRatingElements = document.querySelectorAll('span[aria-label], .ceNzKf, [role="img"]');
      console.log('GMB Extractor: Found', allRatingElements.length, 'potential rating elements for debugging');
      allRatingElements.forEach((el, index) => {
        if (index < 5) { // Only log first 5 to avoid spam
          console.log(`GMB Extractor: Element ${index}:`, {
            tagName: el.tagName,
            className: el.className,
            ariaLabel: el.getAttribute('aria-label'),
            role: el.getAttribute('role')
          });
        }
      });
    }
    
    // Review Count - extract from page source
    console.log('GMB Extractor: Looking for Review Count in page source...');
    
    // Look for review count pattern in page source: "N reviews" or "N review"
    const reviewCountMatches = pageSource.match(/"(\d+)\s+reviews?"/g);
    console.log('GMB Extractor: Review count matches found:', reviewCountMatches);
    
    if (reviewCountMatches && reviewCountMatches.length > 0) {
      // Extract the number from the first match (e.g., "6 reviews" -> "6")
      const firstMatch = reviewCountMatches[0];
      const numberMatch = firstMatch.match(/(\d+)/);
      if (numberMatch) {
        data.reviewCount = numberMatch[1];
        console.log('GMB Extractor: Found review count:', data.reviewCount);
      }
    } else {
      // Fallback to DOM selectors if page source extraction fails
      console.log('GMB Extractor: No review count found in page source, trying DOM selectors...');
      const reviewSelectors = [
        '[data-attrid="kc:/collection/knowledge_panels/local_business:review_count"] span',
        '.hqzQac',
        '.UY7F9',
        'button[data-value="Reviews"] span'
      ];
      
      const reviewElement = findElementBySelectors(reviewSelectors);
      if (reviewElement) {
        data.reviewCount = reviewElement.textContent.trim();
        console.log('GMB Extractor: Found review count via DOM selector:', data.reviewCount);
      }
    }
    
    // Coordinates already extracted above for category matching
    
    // Place ID - extract from URL or page source
    console.log('GMB Extractor: Looking for Place ID...');
    
    // First try URL parameter
    let placeIdMatch = url.match(/place_id:([A-Za-z0-9_-]+)/);
    if (placeIdMatch) {
      data.placeId = placeIdMatch[1];
      console.log('GMB Extractor: Found Place ID in URL:', data.placeId);
    } else {
      // Look for ChIJ Place IDs in page source (Google Place IDs start with ChIJ)
      const placeIdMatches = pageSource.match(/ChIJ[A-Za-z0-9_-]{20,}/g);
      console.log('GMB Extractor: Place ID matches found:', placeIdMatches);
      
      if (placeIdMatches && placeIdMatches.length > 0) {
        data.placeId = placeIdMatches[0];
        console.log('GMB Extractor: Selected Place ID:', data.placeId);
      }
    }
    
    // Knowledge Panel ID (KG ID) - extract from page source
    console.log('GMB Extractor: Looking for Knowledge Panel ID...');
    
    // Look for /g/ followed by alphanumeric characters (Google Knowledge Graph IDs)
    const kgIdPattern = /\/g\/([A-Za-z0-9_-]+)/g;
    const kgIdMatches = pageSource.match(kgIdPattern);
    console.log('GMB Extractor: KG ID matches found:', kgIdMatches);
    
    if (kgIdMatches && kgIdMatches.length > 0) {
      // Take the first valid KG ID found
      data.knowledgePanelId = kgIdMatches[0];
      console.log('GMB Extractor: Selected KG ID:', data.knowledgePanelId);
    }
    
    // CID Number - extract ONLY from ludocid pattern in page source
    console.log('GMB Extractor: Looking for CID number using ludocid pattern...');
    
    // Look for ludocid pattern in page source
    const ludocidMatch = pageSource.match(/ludocid\\\\u003d(\d+)\\\\u0026/);
    console.log('GMB Extractor: Ludocid pattern match:', ludocidMatch);
    
    if (ludocidMatch) {
      data.cidNumber = ludocidMatch[1];
      console.log('GMB Extractor: Found CID from ludocid pattern:', data.cidNumber);
    } else {
      console.log('GMB Extractor: No CID found - ludocid pattern not detected in page source');
    }
    

    
    // Generate GMB Links based on extracted data
    if (data.placeId) {
      data.reviewListDisplayLink = `https://search.google.com/local/reviews?placeid=${data.placeId}`;
      data.reviewRequestLink = `https://search.google.com/local/writereview?placeid=${data.placeId}`;
      data.gmbLinkWithPlaceId = `https://www.google.com/maps/place/?q=place_id:${data.placeId}`;
    }
    
    if (data.knowledgePanelId) {
      data.knowledgePanelPageLink = `https://www.google.com/search?kgmid=${data.knowledgePanelId}`;
      data.gmbPostUrl = `https://www.google.com/search?kgmid=${data.knowledgePanelId}&uact=5#lpstate=pid:-1`;
      data.askQuestionRequestUrl = `https://www.google.com/search?kgmid=${data.knowledgePanelId}&uact=5#lpqa=a,,d,1`;
      data.questionsAndAnswersUrl = `https://www.google.com/search?kgmid=${data.knowledgePanelId}&uact=5#lpqa=d,2`;
      data.products = `https://www.google.com/search?kgmid=${data.knowledgePanelId}#lpc=lpc`;
    }
    
    if (data.cidNumber) {
      data.gmbLinkWithCid = `https://www.google.com/maps/place/?cid=${data.cidNumber}`;
    }
    
    if (data.businessName && data.address) {
      const encodedName = encodeURIComponent(data.businessName);
      const encodedAddress = encodeURIComponent(data.address);
      data.services = `https://www.google.com/localservices/prolist?src=2&q=${encodedName}%20${encodedAddress}`;
    }
    
    if (data.address) {
      data.otherGmbsAtSameAddress = `https://www.google.com/maps/place/${encodeURIComponent(data.address)}`;
    }
    
    if (data.website) {
      const domain = data.website.replace(/https?:\/\//, '').split('/')[0];
      data.gmbsWithSameWebsiteDomain = `https://www.google.com/search?q="${domain}"&tbm=lcl`;
    }
    
    // Current URL
    data.url = url;
    
    // Timestamp
    data.extractedAt = new Date().toISOString();
    
    console.log('Extracted GMB data:', data);
    return data;
    
  } catch (error) {
    console.error('Error extracting GMB data:', error);
    return { error: error.message };
  }
}

// Helper function to find element by multiple selectors
function findElementBySelectors(selectors) {
  for (const selector of selectors) {
    const element = document.querySelector(selector);
    if (element) return element;
  }
  return null;
}

// Helper function to find additional categories after the main category in page source
function findAdditionalCategoriesAfterMain(pageSource, mainCategory) {
  console.log('GMB Extractor: Searching for additional categories after main category:', mainCategory);
  
  // Find the main category in the page source and look for what follows
  // Try different patterns as the category might be escaped in the page source
  const patterns = [
    `\\"${mainCategory}\\"`,  // Escaped quotes: \"Osteopath\"
    `"${mainCategory}"`,      // Regular quotes: "Osteopath"
    mainCategory              // Just the category name
  ];
  
  let mainCategoryIndex = -1;
  let foundPattern = '';
  
  for (const pattern of patterns) {
    mainCategoryIndex = pageSource.indexOf(pattern);
    if (mainCategoryIndex !== -1) {
      foundPattern = pattern;
      console.log('GMB Extractor: Found main category with pattern:', pattern, 'at index:', mainCategoryIndex);
      break;
    }
  }
  
  if (mainCategoryIndex === -1) {
    console.log('GMB Extractor: Main category not found in page source with any pattern');
    return [];
  }
  
  console.log('GMB Extractor: Found main category at index:', mainCategoryIndex);
  
  // Look for the pattern after the main category
  // Get a snippet after the main category to search for additional categories
  const startIndex = mainCategoryIndex + foundPattern.length;
  const snippet = pageSource.substring(startIndex, startIndex + 500);
  
  console.log('GMB Extractor: Snippet after main category:', snippet.substring(0, 200));
  
  // Look for the separator and extract categories
  const additionalCategories = [];
  // Try different separators as they can vary
  const separators = [
    ',\\"',     // comma-backslash-quote (most common)
    '\\",\\"',  // backslash-quote-comma-backslash-quote
    '","'       // regular comma in quotes
  ];
  
  let separator = '';
  let firstSeparatorIndex = -1;
  
  // Find which separator pattern exists in the snippet
  for (const sep of separators) {
    const index = snippet.indexOf(sep);
    if (index !== -1) {
      separator = sep;
      firstSeparatorIndex = index;
      console.log('GMB Extractor: Using separator pattern:', separator, 'found at index:', index);
      break;
    }
  }
  
  if (!separator) {
    console.log('GMB Extractor: No valid separator found in snippet');
    return [];
  }
  
  let currentIndex = 0;
  let categoryCount = 0;
  
  while (true) {
    categoryCount++;
    console.log(`GMB Extractor: Looking for category ${categoryCount}, currentIndex: ${currentIndex}`);
    
    const separatorIndex = snippet.indexOf(separator, currentIndex);
    console.log(`GMB Extractor: Separator found at index: ${separatorIndex}`);
    
    if (separatorIndex === -1) {
      console.log('GMB Extractor: No more separators found, stopping search');
      break;
    }
    
    // CRITICAL FIX: Check if we encounter closing bracket before the separator
    const closingBracketIndex = snippet.indexOf(']', currentIndex);
    if (closingBracketIndex !== -1 && closingBracketIndex < separatorIndex) {
      console.log(`GMB Extractor: Found closing bracket ']' at index ${closingBracketIndex} before separator at ${separatorIndex} - STOPPING as this marks end of categories array`);
      break;
    }
    
    // Find the start of the next category (after the separator)
    const categoryStart = separatorIndex + separator.length;
    console.log(`GMB Extractor: Category starts at index: ${categoryStart}`);
    
    // Find the end of the category (next quote)
    const categoryEnd = snippet.indexOf('"', categoryStart);
    console.log(`GMB Extractor: Category ends at index: ${categoryEnd}`);
    
    if (categoryEnd === -1) {
      console.log('GMB Extractor: No closing quote found, stopping search');
      break;
    }
    
    let category = snippet.substring(categoryStart, categoryEnd).trim();
    console.log(`GMB Extractor: Raw category extracted: "${category}"`);
    
    // Clean up the category - remove trailing backslashes and other escape characters
    category = category.replace(/\\+$/, '').replace(/\\"/g, '"').trim();
    
    console.log('GMB Extractor: Found potential additional category:', category);
    
    // Validate the category and filter out "Favourites"
    if (category.length > 2 && 
        category.length < 80 &&
        !category.toLowerCase().includes('http') && 
        !category.toLowerCase().includes('google') && 
        !category.toLowerCase().includes('/maps/') &&
        !category.toLowerCase().includes('ggpht') &&
        !category.toLowerCase().includes('khms') &&
        !category.toLowerCase().match(/^[a-z]{2,3}$/) &&
        !category.toLowerCase().includes('@') &&
        !category.startsWith('/') &&
        category.toLowerCase() !== 'favourites' &&
        category.match(/^[A-Za-z\s&\-'.,()]+$/)) {
      
      additionalCategories.push(category);
      console.log('GMB Extractor: Valid additional category added:', category);
    } else {
      console.log('GMB Extractor: Invalid category filtered out:', category);
      console.log('GMB Extractor: Validation details:', {
        length: category.length,
        hasHttp: category.toLowerCase().includes('http'),
        hasGoogle: category.toLowerCase().includes('google'),
        regexMatch: category.match(/^[A-Za-z\s&\-'.,()]+$/)
      });
      // Don't break here, continue looking for more categories
    }
    
    currentIndex = categoryEnd + 1; // Move past the closing quote
    console.log(`GMB Extractor: Moving to next search position: ${currentIndex}`);
    
    // Safety check to prevent infinite loop
    if (categoryCount > 10) {
      console.log('GMB Extractor: Safety limit reached, stopping search');
      break;
    }
  }
  
  console.log('GMB Extractor: Final additional categories found:', additionalCategories);
  console.log('GMB Extractor: Total additional categories count:', additionalCategories.length);
  console.log('GMB Extractor: Categories being returned:', JSON.stringify(additionalCategories));
  return additionalCategories;
}

// Auto-extract functionality completely removed to prevent conflicts with other extensions
// This extension only works when manually triggered by the user 

// Function to extract attributes from the About tab
async function extractAboutAttributes() {
  try {
    console.log('GMB Extractor: Looking for About button...');
    
    // Find the About button using the aria-label pattern
    let aboutButton = document.querySelector('button[aria-label^="About "]');
    
    // Try alternative selectors if the first one doesn't work
    if (!aboutButton) {
      const alternativeSelectors = [
        'button[role="tab"][aria-label*="About"]',
        'button[data-tab-index="2"]',
        'button:contains("About")',
        '[role="tab"]:contains("About")'
      ];
      
      for (const selector of alternativeSelectors) {
        try {
          if (selector.includes(':contains')) {
            // Handle :contains pseudo-selector manually
            const buttons = document.querySelectorAll('button[role="tab"]');
            for (const btn of buttons) {
              if (btn.textContent && btn.textContent.toLowerCase().includes('about')) {
                aboutButton = btn;
                break;
              }
            }
          } else {
            aboutButton = document.querySelector(selector);
          }
          if (aboutButton) {
            console.log(`GMB Extractor: About button found using selector: ${selector}`);
            break;
          }
        } catch (e) {
          console.log(`GMB Extractor: Error with selector ${selector}:`, e);
        }
      }
    }
    
    if (!aboutButton) {
      console.log('GMB Extractor: About button not found with any selector');
      return {};
    }
    
    console.log('GMB Extractor: About button found, clicking...');
    
    // Check if the About tab is already selected
    const isSelected = aboutButton.getAttribute('aria-selected') === 'true';
    if (isSelected) {
      console.log('GMB Extractor: About tab already selected');
    } else {
      // Click the About button
      aboutButton.click();
      
      // Wait for the content to load
      console.log('GMB Extractor: Waiting for About content to load...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Extract attributes from the About section
    const attributes = {};
    
    // Find all sections with h2 headings and their corresponding lists
    const sections = document.querySelectorAll('.iP2t7d.fontBodyMedium');
    
    console.log('GMB Extractor: Found', sections.length, 'attribute sections');
    
    sections.forEach((section, index) => {
      try {
        // Get the section title (h2)
        const titleElement = section.querySelector('h2.iL3Qke.fontTitleSmall');
        if (!titleElement) {
          console.log(`GMB Extractor: No title found for section ${index}`);
          return;
        }
        
        const sectionTitle = titleElement.textContent.trim();
        console.log(`GMB Extractor: Processing section: "${sectionTitle}"`);
        
        // Get all list items in this section
        const listItems = section.querySelectorAll('li.hpLkke');
        console.log(`GMB Extractor: Found ${listItems.length} items in section "${sectionTitle}"`);
        
        if (listItems.length > 0) {
          const sectionAttributes = [];
          
          listItems.forEach((item, itemIndex) => {
            try {
              // Get the span with aria-label that contains the attribute text
              const attributeSpan = item.querySelector('span[aria-label]');
              if (attributeSpan) {
                const attributeText = attributeSpan.getAttribute('aria-label');
                if (attributeText && attributeText.trim()) {
                  // Clean up the attribute text
                  const cleanedText = attributeText.trim();
                  sectionAttributes.push(cleanedText);
                  console.log(`GMB Extractor: Found attribute: "${cleanedText}"`);
                }
              } else {
                // Fallback: try to get text content if no aria-label
                const textContent = item.textContent?.trim();
                if (textContent && textContent.length > 0 && textContent.length < 200) {
                  console.log(`GMB Extractor: Found attribute via text content: "${textContent}"`);
                  sectionAttributes.push(textContent);
                }
              }
            } catch (itemError) {
              console.error(`GMB Extractor: Error processing item ${itemIndex} in section "${sectionTitle}":`, itemError);
            }
          });
          
          if (sectionAttributes.length > 0) {
            attributes[sectionTitle] = sectionAttributes;
            console.log(`GMB Extractor: Added ${sectionAttributes.length} attributes to section "${sectionTitle}"`);
          }
        }
      } catch (sectionError) {
        console.error(`GMB Extractor: Error processing section ${index}:`, sectionError);
      }
    });
    
    console.log('GMB Extractor: Final attributes extracted:', attributes);
    return attributes;
    
  } catch (error) {
    console.error('GMB Extractor: Error extracting attributes:', error);
    return {};
  }
}

// Persistent overlay popup functionality
let persistentPopup = null;

// Function to refresh popup content
function refreshPersistentPopup() {
    if (persistentPopup) {
        persistentPopup.remove();
        persistentPopup = null;
    }
    createPersistentPopup();
}

// Create persistent overlay popup
function createPersistentPopup() {
    // Remove existing popup if it exists
    if (persistentPopup) {
        persistentPopup.remove();
    }

    // Determine page type
    const isProListPage = window.location.href.includes('google.com/localservices/prolist');
    const isSingleBusinessPage = window.location.href.includes('google.com/maps/place/') || 
                                 window.location.pathname.includes('/place/');
    
    // Debug logging to help identify the issue
    console.log('🔍 PAGE DETECTION DEBUG:');
    console.log(`URL: ${window.location.href}`);
    console.log(`Pathname: ${window.location.pathname}`);
    console.log(`isProListPage: ${isProListPage}`);
    console.log(`isSingleBusinessPage: ${isSingleBusinessPage}`);
    
    // Check if this is a Multiple Listings page (Google Maps search results)
    // IMPORTANT: Only consider it multiple listings if it's NOT a single business page
    const isMultipleListingsPage = !isSingleBusinessPage && 
                                   !isProListPage &&
                                   window.location.href.includes('google.com/maps') && 
                                   (window.location.href.includes('/search') || 
                                    document.querySelector('[aria-label*="Results for"]') ||
                                    document.querySelector('[role="feed"]'));
    
    console.log(`isMultipleListingsPage: ${isMultipleListingsPage}`);
    console.log(`Final page type: ${isSingleBusinessPage ? 'Single Business' : isMultipleListingsPage ? 'Multiple Listings' : 'ProList'}`);

    // Create popup container
    persistentPopup = document.createElement('div');
    persistentPopup.id = 'gmb-persistent-popup';
    persistentPopup.className = 'gmb-search-extractor__docked-interface';
    
    // Generate different content based on page type
    if (isSingleBusinessPage) {
        // Single Business Extractor Layout
        persistentPopup.innerHTML = `
            <div class="gmb-search-extractor__header">
                <h1 class="gmb-search-extractor__title">Single Business Extractor</h1>
                <button class="gmb-search-extractor__close-btn" id="gmbCloseBtn" aria-label="Close popup">×</button>
            </div>
            <div class="gmb-search-extractor__content">
                <div class="gmb-search-extractor__status">
                    <div class="gmb-search-extractor__status-indicator" id="gmbStatusIndicator"></div>
                    <span class="gmb-search-extractor__status-text" id="gmbStatusText">Ready to extract data</span>
                </div>
                
                <div class="gmb-search-extractor__settings">
                    <fieldset class="gmb-search-extractor__extraction-options">
                        <legend class="gmb-search-extractor__options-legend">Extraction Options</legend>
                        <div class="gmb-search-extractor__checkbox-setting">
                            <input type="checkbox" id="gmbExtractAttributes" name="extractionType">
                            <label class="gmb-search-extractor__checkbox-label" for="gmbExtractAttributes">🏷️ Extract Attributes</label>
                        </div>
                        <div class="gmb-search-extractor__checkbox-setting">
                            <input type="checkbox" id="gmbExtractReviews" name="extractionType">
                            <label class="gmb-search-extractor__checkbox-label" for="gmbExtractReviews">⭐ Extract Reviews (do this separately)</label>
                        </div>
                        
                        <!-- Review Range Setting (only show when reviews extraction is checked) -->
                        <div class="gmb-search-extractor__setting" id="gmbReviewRangeSetting" style="display: none; margin-top: 10px;">
                            <label class="gmb-search-extractor__setting-label" for="gmbReviewRange">Review Limit:</label>
                            <input type="text" class="gmb-search-extractor__text-input" id="gmbReviewRange" placeholder="Enter max reviews (e.g., 50) or leave empty for all" maxlength="5" style="width: 200px;">
                            <div class="gmb-search-extractor__help-text">Leave empty to extract all reviews, or enter a number (1-99999)</div>
                        </div>
                    </fieldset>

                    <!-- Single Business Review Scraper Instructions -->
                    <div class="gmb-search-extractor__instructions" id="gmbReviewInstructions" style="display: none;">
                        <div class="gmb-search-extractor__instructions-header">
                            <span class="gmb-search-extractor__instructions-icon">🚀</span>
                            <h3 class="gmb-search-extractor__instructions-title">AUTOMATED EXTRACTION</h3>
                        </div>
                        <div class="gmb-search-extractor__instructions-content">
                            <p class="gmb-search-extractor__instructions-intro">The scraper will automatically handle everything:</p>
                            <ol class="gmb-search-extractor__instructions-list">
                                <li class="gmb-search-extractor__instructions-step">Switch to the <strong>"Reviews"</strong> tab</li>
                                <li class="gmb-search-extractor__instructions-step">Set a limit if you want to extract a specific number of reviews</li>
                            </ol>
                            <p style="color: #9ca3af; font-size: 12px; margin-top: 8px; font-style: italic;">Just click "Start Extraction" when ready!</p>
                        </div>
                    </div>
                </div>

                <!-- Global Progress Bar Component -->
                <div class="gmb-search-extractor__progress" id="gmbGlobalProgress" style="display: none;">
                    <div class="gmb-search-extractor__progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                        <div class="gmb-search-extractor__progress-fill" id="gmbGlobalProgressFill"></div>
                    </div>
                    <div class="gmb-search-extractor__progress-text" id="gmbGlobalProgressText">Processing...</div>
                </div>

                <div class="gmb-search-extractor__controls">
                    <button class="gmb-search-extractor__btn gmb-search-extractor__btn--start" id="gmbStartExtractionBtn">Start Extraction</button>
                    <button class="gmb-search-extractor__btn gmb-search-extractor__btn--export" id="gmbExportBtn" disabled>📊 Export CSV</button>
                </div>

                <div class="gmb-search-extractor__summary">
                    <div class="gmb-search-extractor__stat">
                        <div class="gmb-search-extractor__stat-label">ATTRIBUTES</div>
                        <div class="gmb-search-extractor__stat-value" id="gmbStatsAttributes">0</div>
                    </div>
                    <div class="gmb-search-extractor__stat">
                        <div class="gmb-search-extractor__stat-label">REVIEWS</div>
                        <div class="gmb-search-extractor__stat-value" id="gmbStatsReviews">0</div>
                    </div>
                </div>
            </div>
        `;
    } else if (isMultipleListingsPage) {
        // Multiple Listings Extractor Layout (Google Maps search results)
        persistentPopup.innerHTML = `
            <div class="gmb-search-extractor__header">
                <h1 class="gmb-search-extractor__title">Multiple Listings Extractor</h1>
                <button class="gmb-search-extractor__close-btn" id="gmbCloseBtn" aria-label="Close popup">×</button>
            </div>
            <div class="gmb-search-extractor__content">
                <div class="gmb-search-extractor__status">
                    <div class="gmb-search-extractor__status-indicator" id="gmbStatusIndicator"></div>
                    <span class="gmb-search-extractor__status-text" id="gmbStatusText">Ready to extract data</span>
                </div>
                
                <div class="gmb-search-extractor__settings">
                    <div class="gmb-search-extractor__setting">
                        <label class="gmb-search-extractor__setting-label">Business range to scrape:</label>
                        <input type="text" class="gmb-search-extractor__text-input" id="gmbBusinessRange" placeholder="Enter a range (e.g., &quot;3-7&quot;) or single number (e.g., &quot;5&quot;)" value="1-5">
                        <div class="gmb-search-extractor__help-text">Enter a range (e.g., "3-7") or single number (e.g., "5")</div>
                    </div>

                    <fieldset class="gmb-search-extractor__extraction-options">
                        <legend class="gmb-search-extractor__options-legend">Extraction Options</legend>
                        <div class="gmb-search-extractor__checkbox-setting">
                            <input type="checkbox" id="gmbExtractAttributes" name="extractionType">
                            <label class="gmb-search-extractor__checkbox-label" for="gmbExtractAttributes">🏷️ Extract Attributes</label>
                        </div>
                        <div class="gmb-search-extractor__checkbox-setting">
                            <input type="checkbox" id="gmbExtractReviews" name="extractionType">
                            <label class="gmb-search-extractor__checkbox-label" for="gmbExtractReviews">⭐ Extract Reviews (do this separately)</label>
                        </div>
                        
                        <!-- Review Range Setting (only show when reviews extraction is checked) -->
                        <div class="gmb-search-extractor__setting" id="gmbReviewRangeSetting" style="display: none; margin-top: 10px;">
                            <label class="gmb-search-extractor__setting-label" for="gmbReviewRange">Review Limit:</label>
                            <input type="text" class="gmb-search-extractor__text-input" id="gmbReviewRange" placeholder="Enter max reviews (e.g., 50) or leave empty for all" maxlength="5" style="width: 200px;">
                            <div class="gmb-search-extractor__help-text">Leave empty to extract all reviews, or enter a number (1-99999)</div>
                        </div>
                    </fieldset>

                    <!-- Multiple Listings Review Scraper Instructions -->
                    <div class="gmb-search-extractor__instructions" id="gmbReviewInstructions" style="display: none;">
                        <div class="gmb-search-extractor__instructions-header">
                            <span class="gmb-search-extractor__instructions-icon">🚀</span>
                            <h3 class="gmb-search-extractor__instructions-title">AUTOMATED EXTRACTION</h3>
                        </div>
                        <div class="gmb-search-extractor__instructions-content">
                            <p class="gmb-search-extractor__instructions-intro">The scraper will automatically handle everything:</p>
                            <ol class="gmb-search-extractor__instructions-list">
                                <li class="gmb-search-extractor__instructions-step">Switch to the <strong>"Reviews"</strong> tab</li>
                                <li class="gmb-search-extractor__instructions-step">Set a limit if you want to extract a specific number of reviews</li>
                            </ol>
                            <p style="color: #9ca3af; font-size: 12px; margin-top: 8px; font-style: italic;">Just click "Start Extraction" when ready!</p>
                        </div>
                    </div>
                </div>

                <!-- Global Progress Bar Component -->
                <div class="gmb-search-extractor__progress" id="gmbGlobalProgress" style="display: none;">
                    <div class="gmb-search-extractor__progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                        <div class="gmb-search-extractor__progress-fill" id="gmbGlobalProgressFill"></div>
                    </div>
                    <div class="gmb-search-extractor__progress-text" id="gmbGlobalProgressText">0 / 0 businesses processed</div>
                </div>

                <div class="gmb-search-extractor__controls">
                    <button class="gmb-search-extractor__btn gmb-search-extractor__btn--start" id="gmbStartExtractionBtn">Start Extraction</button>
                    <button class="gmb-search-extractor__btn gmb-search-extractor__btn--export" id="gmbExportBtn" disabled>📊 Export CSV</button>
                </div>

                <div class="gmb-search-extractor__summary">
                    <div class="gmb-search-extractor__stat">
                        <div class="gmb-search-extractor__stat-label">BUSINESSES</div>
                        <div class="gmb-search-extractor__stat-value" id="gmbStatsBusinesses">0</div>
                    </div>
                    <div class="gmb-search-extractor__stat">
                        <div class="gmb-search-extractor__stat-label">ATTRIBUTES</div>
                        <div class="gmb-search-extractor__stat-value" id="gmbStatsAttributes">0</div>
                    </div>
                    <div class="gmb-search-extractor__stat">
                        <div class="gmb-search-extractor__stat-label">REVIEWS</div>
                        <div class="gmb-search-extractor__stat-value" id="gmbStatsReviews">0</div>
                    </div>
                </div>
            </div>
        `;
    } else {
        // ProList Data Extractor Layout (original)
        persistentPopup.innerHTML = `
            <div class="gmb-search-extractor__header">
                <h1 class="gmb-search-extractor__title">Prolist Data Extractor</h1>
                <button class="gmb-search-extractor__close-btn" id="gmbCloseBtn" aria-label="Close popup">×</button>
            </div>
            <div class="gmb-search-extractor__content">
                <div class="gmb-search-extractor__status">
                    <div class="gmb-search-extractor__status-indicator" id="gmbStatusIndicator"></div>
                    <span class="gmb-search-extractor__status-text" id="gmbStatusText">Ready to extract services & products</span>
                </div>
                
                <div class="gmb-search-extractor__settings">
                    <div class="gmb-search-extractor__setting">
                        <label class="gmb-search-extractor__setting-label">Business range to scrape:</label>
                        <input type="text" class="gmb-search-extractor__text-input" id="gmbBusinessRange" placeholder="Enter a range (e.g., &quot;3-7&quot;) or single number (e.g., &quot;5&quot;)" value="1-5">
                        <div class="gmb-search-extractor__help-text">Enter a range (e.g., "3-7") or single number (e.g., "5")</div>
                    </div>

                    <fieldset class="gmb-search-extractor__extraction-options">
                        <legend class="gmb-search-extractor__options-legend">Extraction Options</legend>
                        <div class="gmb-search-extractor__checkbox-setting">
                            <input type="checkbox" id="gmbExtractServices" name="extractionType">
                            <label class="gmb-search-extractor__checkbox-label" for="gmbExtractServices">🔧 Extract Services</label>
                        </div>
                        <div class="gmb-search-extractor__checkbox-setting">
                            <input type="checkbox" id="gmbExtractAttributes" name="extractionType">
                            <label class="gmb-search-extractor__checkbox-label" for="gmbExtractAttributes">🏷️ Extract Attributes</label>
                        </div>
                        <div class="gmb-search-extractor__checkbox-setting">
                            <input type="checkbox" id="gmbExtractReviews" name="extractionType">
                            <label class="gmb-search-extractor__checkbox-label" for="gmbExtractReviews">⭐ Extract Reviews (do this separately) - To scrape in Bulk go to Maps Search Data Extractor - Access by Searching in Google Search then clicking "More Places" under 3 pack</label>
                        </div>
                        
                        <!-- Review Range Setting (only show when reviews extraction is checked) -->
                        <div class="gmb-search-extractor__setting" id="gmbReviewRangeSetting" style="display: none; margin-top: 10px;">
                            <label class="gmb-search-extractor__setting-label" for="gmbReviewRange">Review Limit:</label>
                            <input type="text" class="gmb-search-extractor__text-input" id="gmbReviewRange" placeholder="Enter max reviews (e.g., 50) or leave empty for all" maxlength="5" style="width: 200px;">
                            <div class="gmb-search-extractor__help-text">Leave empty to extract all reviews, or enter a number (1-99999)</div>
                        </div>
                    </fieldset>

                    <!-- Pro List Review Scraper Instructions -->
                    <div class="gmb-search-extractor__instructions" id="gmbReviewInstructions" style="display: none;">
                        <div class="gmb-search-extractor__instructions-header">
                            <span class="gmb-search-extractor__instructions-icon">🚀</span>
                            <h3 class="gmb-search-extractor__instructions-title">AUTOMATED EXTRACTION</h3>
                        </div>
                        <div class="gmb-search-extractor__instructions-content">
                            <p class="gmb-search-extractor__instructions-intro">The scraper will automatically handle everything:</p>
                            <ol class="gmb-search-extractor__instructions-list">
                                <li class="gmb-search-extractor__instructions-step">Switch to the <strong>"Reviews"</strong> tab</li>
                                <li class="gmb-search-extractor__instructions-step">Set a limit if you want to extract a specific number of reviews</li>
                            </ol>
                            <p style="color: #9ca3af; font-size: 12px; margin-top: 8px; font-style: italic;">Just click "Start Extraction" when ready!</p>
                        </div>
                    </div>
                </div>

                <!-- Global Progress Bar Component -->
                <div class="gmb-search-extractor__progress" id="gmbGlobalProgress" style="display: none;">
                    <div class="gmb-search-extractor__progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                        <div class="gmb-search-extractor__progress-fill" id="gmbGlobalProgressFill"></div>
                    </div>
                    <div class="gmb-search-extractor__progress-text" id="gmbGlobalProgressText">0 / 0 businesses processed</div>
                </div>

                <div class="gmb-search-extractor__controls">
                    <button class="gmb-search-extractor__btn gmb-search-extractor__btn--start" id="gmbStartExtractionBtn">Start Extraction</button>
                    <button class="gmb-search-extractor__btn gmb-search-extractor__btn--export" id="gmbExportBtn" disabled>📊 Export CSV</button>
                </div>

                <div class="gmb-search-extractor__summary">
                    <div class="gmb-search-extractor__stat">
                        <div class="gmb-search-extractor__stat-label">BUSINESSES</div>
                        <div class="gmb-search-extractor__stat-value" id="gmbStatsBusinesses">0</div>
                    </div>
                    <div class="gmb-search-extractor__stat">
                        <div class="gmb-search-extractor__stat-label">SERVICES</div>
                        <div class="gmb-search-extractor__stat-value" id="gmbStatsServices">0</div>
                    </div>
                    <div class="gmb-search-extractor__stat">
                        <div class="gmb-search-extractor__stat-label">ATTRIBUTES</div>
                        <div class="gmb-search-extractor__stat-value" id="gmbStatsAttributes">0</div>
                    </div>
                    <div class="gmb-search-extractor__stat">
                        <div class="gmb-search-extractor__stat-label">REVIEWS</div>
                        <div class="gmb-search-extractor__stat-value" id="gmbStatsReviews">0</div>
                    </div>
                </div>
            </div>
        `;
    }

    // Add popup to page (no need to create styles since they're in CSS file)
    document.body.appendChild(persistentPopup);

    // Add event listeners
    setupPersistentPopupEvents();

    return persistentPopup;
}

// Setup event listeners for persistent popup
function setupPersistentPopupEvents() {
    // Inject business numbers for Pro List pages and Multiple Listings pages
    const isProListPage = window.location.href.includes('google.com/localservices/prolist');
    const isSingleBusinessPage = window.location.href.includes('google.com/maps/place/');
    const isMultipleListingsPage = window.location.href.includes('google.com/maps') && 
                                   !isSingleBusinessPage && 
                                   !isProListPage &&
                                   (window.location.href.includes('/search') || 
                                    document.querySelector('[aria-label*="Results for"]') ||
                                    document.querySelector('[role="feed"]'));
    
    if (isProListPage) {
        injectBusinessNumbers();
    } else if (isMultipleListingsPage) {
        injectMultipleListingsNumbers();
    }
    
    const closeBtn = document.getElementById('gmbCloseBtn');
    const startExtractionBtn = document.getElementById('gmbStartExtractionBtn');
    const exportBtn = document.getElementById('gmbExportBtn');
    
    // New UI elements - check if they exist before using them
    const extractServicesCheckbox = document.getElementById('gmbExtractServices');
    const extractAttributesCheckbox = document.getElementById('gmbExtractAttributes');
    const extractReviewsCheckbox = document.getElementById('gmbExtractReviews');
    const businessRangeInput = document.getElementById('gmbBusinessRange');
    const progressText = document.getElementById('gmbProgressText');
    const statusIndicator = document.getElementById('gmbStatusIndicator');
    const statusText = document.getElementById('gmbStatusText');
    
    // Stats elements - some might not exist for single business pages
    const statsBusinesses = document.getElementById('gmbStatsBusinesses');
    const statsServices = document.getElementById('gmbStatsServices');
    const statsAttributes = document.getElementById('gmbStatsAttributes');
    const statsReviews = document.getElementById('gmbStatsReviews');

    let extractionData = {
        services: [],
        attributes: [],
        reviews: []
    };

    // Implement mutually exclusive checkboxes - filter out null/undefined elements
    const checkboxes = [extractServicesCheckbox, extractAttributesCheckbox, extractReviewsCheckbox].filter(Boolean);
    const reviewInstructions = document.getElementById('gmbReviewInstructions');
    const reviewRangeSetting = document.getElementById('gmbReviewRangeSetting');
    const reviewRangeInput = document.getElementById('gmbReviewRange');
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            if (checkbox.checked) {
                // Uncheck all other checkboxes
                checkboxes.forEach(otherCheckbox => {
                    if (otherCheckbox !== checkbox) {
                        otherCheckbox.checked = false;
                    }
                });
                
                // Show/hide review instructions and range input based on Extract Reviews checkbox
                if (checkbox === extractReviewsCheckbox && reviewInstructions) {
                    reviewInstructions.style.display = 'block';
                    if (reviewRangeSetting) {
                        reviewRangeSetting.style.display = 'block';
                    }
                } else {
                    if (reviewInstructions) {
                        reviewInstructions.style.display = 'none';
                    }
                    if (reviewRangeSetting) {
                        reviewRangeSetting.style.display = 'none';
                    }
                }
                
                // Enable start button if a checkbox is selected
                startExtractionBtn.disabled = false;
            } else {
                // Hide instructions and range input if Extract Reviews is unchecked
                if (checkbox === extractReviewsCheckbox) {
                    if (reviewInstructions) {
                        reviewInstructions.style.display = 'none';
                    }
                    if (reviewRangeSetting) {
                        reviewRangeSetting.style.display = 'none';
                    }
                }
                
                // If no checkboxes are checked, disable start button
                const anyChecked = checkboxes.some(cb => cb.checked);
                startExtractionBtn.disabled = !anyChecked;
            }
        });
    });

    // Close button
    closeBtn.addEventListener('click', () => {
        if (persistentPopup) {
            persistentPopup.remove();
            persistentPopup = null;
        }
    });

    // Start extraction button - this will handle the actual extraction
    startExtractionBtn.addEventListener('click', async () => {
        const selectedExtractor = getSelectedExtractor();
        
        // For single business pages, we don't need a business range
        let businessRange = null;
        if (isSingleBusinessPage) {
            // Single business always uses range 1-1
            businessRange = { start: 1, end: 1 };
        } else {
            // For ProList pages, parse the business range input
            if (!businessRangeInput) {
                alert('Business range input not found.');
                return;
            }
            businessRange = parseBusinessRange(businessRangeInput.value.trim());
            if (!businessRange) {
                alert('Please enter a valid business range (e.g., "1-5" or "3").');
                return;
            }
        }
        
        if (!selectedExtractor) {
            alert('Please select an extraction type first.');
            return;
        }

        // Start the appropriate extraction based on selected checkbox
        await startExtractionProcess(selectedExtractor, businessRange);
    });

    // Export button
    exportBtn.addEventListener('click', () => {
        exportExtractedData();
    });

    // Helper functions
    function getSelectedExtractor() {
        if (extractServicesCheckbox && extractServicesCheckbox.checked) return 'services';
        if (extractAttributesCheckbox && extractAttributesCheckbox.checked) return 'attributes';
        if (extractReviewsCheckbox && extractReviewsCheckbox.checked) return 'reviews';
        return null;
    }

    function parseBusinessRange(rangeStr) {
        if (!rangeStr) return null;
        
        // Handle single number (e.g., "5")
        if (/^\d+$/.test(rangeStr)) {
            const num = parseInt(rangeStr);
            return { start: num, end: num };
        }
        
        // Handle range (e.g., "1-5")
        const rangeMatch = rangeStr.match(/^(\d+)-(\d+)$/);
        if (rangeMatch) {
            const start = parseInt(rangeMatch[1]);
            const end = parseInt(rangeMatch[2]);
            if (start <= end) {
                return { start, end };
            }
        }
        
        return null;
    }

    async function startExtractionProcess(extractorType, businessRange) {
        console.log(`Starting ${extractorType} extraction for businesses ${businessRange.start}-${businessRange.end}`);
        
        // Update UI to show extraction in progress
        startExtractionBtn.disabled = true;
        if (progressText) {
            progressText.textContent = `Extracting ${extractorType}...`;
        }
        statusText.textContent = `${extractorType} extraction in progress`;
        statusIndicator.className = 'gmb-status__indicator--active';
        
        // Reset and show global progress bar for ProList extractions
        const isProListPage = window.location.href.includes('google.com/localservices/prolist');
        if (isProListPage) {
            resetGlobalProgress();
            showGlobalProgress();
        }
        
        try {
            if (extractorType === 'services') {
                console.log('Triggering services extraction...');
                
                // Check if we're on a Google Search page (for services) or Pro List page
                const isGoogleSearchPage = window.location.href.includes('google.com/search');
                const isProListPage = window.location.href.includes('google.com/localservices/prolist');
                
                if (!isGoogleSearchPage && !isProListPage) {
                    throw new Error('Services extraction works on Google Search pages with business listings or Pro List pages. Please navigate to an appropriate page.');
                }
                
                // Use the appropriate extractor based on page type
                if (isProListPage) {
                    // For Pro List pages, use ProListExtractor
                    console.log('Using Pro List services extractor...');
                    if (window.proListExtractor) {
                        const result = await window.proListExtractor.extractServicesFromProList(businessRange);
                        if (result && result.success) {
                            statusText.textContent = `Services extraction completed! Found services from ${result.businessCount} businesses`;
                            statusIndicator.className = 'gmb-status__indicator--active';
                            
                            // Store services data for potential export
                            window.lastExtractedServices = result;
                            
                            // Show completion notification
                            GMBNotifications.showSuccess('Pro List Services Extraction Complete!', 
                                `Successfully extracted services from ${result.businessCount} businesses (Range: ${result.range})`);
                        } else {
                            statusText.textContent = 'Services extraction failed';
                            statusIndicator.className = 'gmb-status__indicator--error';
                            throw new Error(result?.error || 'Unknown error during Pro List services extraction');
                        }
                    } else {
                        throw new Error('Pro List extractor not available');
                    }
                } else if (isGoogleSearchPage) {
                    // Use Google Search Services Extractor for Google Search pages
                    console.log('Using Google Search Services Extractor...');
                    if (window.googleSearchServicesExtractor) {
                        await window.googleSearchServicesExtractor.startServicesExtraction();
                        statusText.textContent = 'Google Search services extraction started';
                        statusIndicator.className = 'gmb-status__indicator--active';
                    } else {
                        throw new Error('Google Search Services Extractor not available. Please refresh the page.');
                    }
                }
            } else if (extractorType === 'attributes') {
                console.log('Triggering attributes extraction...');
                
                const isGoogleMapsPage = window.location.href.includes('google.com/maps');
                const isProListPage = window.location.href.includes('google.com/localservices/prolist');
                const isSingleBusinessPage = window.location.href.includes('google.com/maps/place/');
                const isMultipleListingsPage = window.location.href.includes('google.com/maps') && 
                                               !isSingleBusinessPage && 
                                               !isProListPage &&
                                               (window.location.href.includes('/search') || 
                                                document.querySelector('[aria-label*="Results for"]') ||
                                                document.querySelector('[role="feed"]'));
                
                if (isMultipleListingsPage) {
                    // Multiple Listings attributes extraction
                    console.log('Using Multiple Listings attributes extractor...');
                    
                    try {
                        const result = await extractAttributesFromMultipleListings(businessRange);
                        if (result && result.success) {
                            statusText.textContent = `Attributes extraction completed! Found attributes from ${result.businessCount} businesses`;
                            statusIndicator.className = 'gmb-status__indicator--active';
                            
                            // Store attributes data for potential export
                            window.lastExtractedAttributes = result;
                            
                            // Update attributes counter in UI
                            const statsAttributes = document.getElementById('gmbStatsAttributes');
                            if (statsAttributes) {
                                statsAttributes.textContent = result.totalAttributes || 0;
                            }
                            
                            // Enable export button (THIS WAS MISSING!)
                            const exportBtn = document.getElementById('gmbExportBtn');
                            if (exportBtn) {
                                exportBtn.disabled = false;
                            }
                            
                            // Update export button text
                            updateExportButtonText();
                            
                            // Show completion notification
                            GMBNotifications.showSuccess('Multiple Listings Attributes Extraction Complete!', 
                                `Successfully extracted attributes from ${result.businessCount} businesses (Range: ${result.range})`);
                        } else {
                            statusText.textContent = 'Multiple Listings attributes extraction failed';
                            statusIndicator.className = 'gmb-status__indicator--error';
                            throw new Error(result?.error || 'Unknown error during Multiple Listings attributes extraction');
                        }
                    } catch (error) {
                        statusText.textContent = 'Multiple Listings attributes extraction failed';
                        statusIndicator.className = 'gmb-status__indicator--error';
                        if (progressText) {
                            progressText.textContent = error.message;
                        }
                        throw error;
                    }
                } else if (isGoogleMapsPage) {
                    // Use the existing extractAboutAttributes function for Google Maps
                    console.log('Using Google Maps attributes extractor...');
                    const attributes = await extractAboutAttributes();
                    
                    if (attributes && Object.keys(attributes).length > 0) {
                        statusText.textContent = `Attributes extraction completed! Found ${Object.keys(attributes).length} attribute sections`;
                        statusIndicator.className = 'gmb-status__indicator--active';
                        
                        // Show notification
                        GMBNotifications.showSuccess('Google Maps Attributes Extraction Complete!', 
                            `Successfully extracted ${Object.keys(attributes).length} attribute sections`);
                        
                        // Store attributes data for potential export
                        window.lastExtractedAttributes = attributes;
                        
                        // Update attributes counter in UI
                        const statsAttributes = document.getElementById('gmbStatsAttributes');
                        if (statsAttributes) {
                            const attributeCount = Object.values(attributes).reduce((total, attrs) => total + attrs.length, 0);
                            statsAttributes.textContent = attributeCount;
                        }
                        
                        // Keep export button enabled if we have any data
                        const exportBtn = document.getElementById('gmbExportBtn');
                        if (exportBtn) {
                            exportBtn.disabled = false;
                        }
                        
                        // Update status to show both datasets if available
                        const hasReviews = window.lastExtractedBusinessReviews && window.lastExtractedBusinessReviews.reviews && window.lastExtractedBusinessReviews.reviews.length > 0;
                        if (hasReviews) {
                            statusText.textContent = `Attributes extraction completed! Found ${Object.keys(attributes).length} attribute sections. Both attributes and reviews ready for export.`;
                        }
                        
                        // Update export button text
                        updateExportButtonText();
                    } else {
                        statusText.textContent = 'Attributes extraction completed but no attributes found';
                        statusIndicator.className = 'gmb-status__indicator--active';
                    }
                    
                } else if (isProListPage) {
                    // For Pro List pages, extract basic business attributes
                    console.log('Using Pro List attributes extractor...');
                    if (window.proListExtractor) {
                        const result = await window.proListExtractor.extractAttributesFromProList(businessRange);
                        if (result && result.success) {
                            statusText.textContent = `Attributes extraction completed! Found attributes from ${result.businessCount} businesses`;
                            statusIndicator.className = 'gmb-status__indicator--active';
                            
                            // Store attributes data for potential export
                            window.lastExtractedAttributes = result;
                            
                            // Show completion notification
                            GMBNotifications.showSuccess('Pro List Attributes Extraction Complete!', 
                                `Successfully extracted attributes from ${result.businessCount} businesses (Range: ${result.range})`);
                        } else {
                            statusText.textContent = 'Attributes extraction failed';
                            statusIndicator.className = 'gmb-status__indicator--error';
                            throw new Error(result?.error || 'Unknown error during Pro List attributes extraction');
                        }
                    } else {
                        throw new Error('Pro List extractor not available');
                    }
                } else {
                    throw new Error('Attributes extraction works on Google Maps business pages or Pro List pages. Please navigate to an appropriate page.');
                }
            } else if (extractorType === 'reviews') {
                console.log('Triggering review extraction...');
                
                // Check if we're on a ProList page or single business page
                const isProListPage = window.location.href.includes('google.com/localservices/prolist');
                const isSingleBusinessPage = window.location.href.includes('google.com/maps/place/');
                const isMultipleListingsPage = window.location.href.includes('google.com/maps') && 
                                               !isSingleBusinessPage && 
                                               !isProListPage &&
                                               (window.location.href.includes('/search') || 
                                                document.querySelector('[aria-label*="Results for"]') ||
                                                document.querySelector('[role="feed"]'));
                
                if (isProListPage) {
                    // ProList reviews extraction
                    console.log('🔄 Starting ProList review extraction...');
                    statusText.textContent = 'ProList review extraction in progress...';
                    statusIndicator.className = 'gmb-status__indicator--active';
                    
                    try {
                        const proListReviewScraper = window.proListReviewScraper;
                        if (!proListReviewScraper) {
                            throw new Error('ProList Review Scraper not available. Please refresh the page and try again.');
                        }
                        
                        // Get the max reviews limit from the range input
                        let maxReviews = null;
                        const reviewRangeInput = document.getElementById('gmbReviewRange');
                        if (reviewRangeInput && reviewRangeInput.value.trim()) {
                            const rangeValue = parseInt(reviewRangeInput.value.trim(), 10);
                            if (!isNaN(rangeValue) && rangeValue > 0) {
                                maxReviews = rangeValue;
                                console.log(`🔢 ProList: Using review limit of ${maxReviews}`);
                            }
                        }
                        
                        // Call the correct method with maxReviews parameter
                        const result = await proListReviewScraper.executeExtraction(maxReviews);
                        
                        if (result && result.success) {
                            console.log(`✅ ProList review extraction completed! Found ${result.reviewCount} reviews`);
                            statusText.textContent = `ProList review extraction completed! Found ${result.reviewCount} reviews`;
                            statusIndicator.className = 'gmb-status__indicator--success';
                            
                            // Store review data for export
                            window.proListReviewData = result;
                            
                            // Enable export button
                            const exportBtn = document.getElementById('gmbExportBtn');
                            if (exportBtn) {
                                exportBtn.disabled = false;
                            }
                        } else {
                            throw new Error(result?.error || 'ProList review extraction failed');
                        }
                    } catch (error) {
                        console.error('❌ Error during ProList review extraction:', error);
                        statusText.textContent = 'ProList review extraction failed';
                        statusIndicator.className = 'gmb-status__indicator--error';
                        if (progressText) {
                            progressText.textContent = error.message;
                        }
                        throw error;
                    }
                } else if (isSingleBusinessPage) {
                    // Single business reviews extraction
                    console.log('🔄 Starting single business review extraction...');
                    statusText.textContent = 'Single business review extraction in progress...';
                    statusIndicator.className = 'gmb-status__indicator--active';
                    
                    try {
                        const reviewScraper = new SimplifiedReviewScraper();
                        const result = await reviewScraper.executeExtraction();
                        
                        if (result && result.success) {
                            console.log(`✅ Single business review extraction completed! Found ${result.reviewCount} reviews`);
                            statusText.textContent = `Review extraction completed! Found ${result.reviewCount} reviews`;
                            statusIndicator.className = 'gmb-status__indicator--success';
                            
                            // Store review data for export
                            window.lastExtractedReviews = result;
                            
                            // Enable export button
                            const exportBtn = document.getElementById('gmbExportBtn');
                            if (exportBtn) {
                                exportBtn.disabled = false;
                            }
                        } else {
                            throw new Error('Single business review extraction failed');
                        }
                    } catch (error) {
                        console.error('❌ Error during single business review extraction:', error);
                        statusText.textContent = 'Single business review extraction failed';
                        statusIndicator.className = 'gmb-status__indicator--error';
                        if (progressText) {
                            progressText.textContent = error.message;
                        }
                        // Auto-hide progress after a delay for single business pages
                        setTimeout(() => hideGlobalProgress(), 3000);
                    }
                } else if (isMultipleListingsPage) {
                    // Multiple Listings reviews extraction
                    console.log('🔄 Starting Multiple Listings review extraction...');
                    statusText.textContent = 'Multiple Listings review extraction in progress...';
                    statusIndicator.className = 'gmb-status__indicator--active';
                    
                    try {
                        // Get the range value from the input if available
                        let maxReviews = null;
                        const reviewRangeInput = document.getElementById('gmbReviewRange');
                        if (reviewRangeInput && reviewRangeInput.value.trim()) {
                            const rangeValue = parseInt(reviewRangeInput.value.trim(), 10);
                            if (!isNaN(rangeValue) && rangeValue > 0) {
                                maxReviews = rangeValue;
                                console.log(`🔢 Multiple Listings: Using review limit of ${maxReviews}`);
                            }
                        }
                        
                        if (maxReviews) {
                            console.log(`🔢 Multiple Listings: Starting extraction with limit of ${maxReviews} reviews`);
                        } else {
                            console.log(`🔢 Multiple Listings: Starting extraction with no limit (all reviews)`);
                        }
                        
                        const result = await extractReviewsFromMultipleListings(businessRange, maxReviews);
                        
                        if (result && result.success) {
                            let completionMessage = `Multiple Listings review extraction completed! Found ${result.totalReviews} reviews from ${result.successfulExtractions}/${result.businessCount} businesses`;
                            if (result.rangeApplied) {
                                completionMessage += ` (limit: ${result.maxReviews})`;
                            }
                            
                            console.log(`✅ ${completionMessage}`);
                            statusText.textContent = completionMessage;
                            statusIndicator.className = 'gmb-status__indicator--success';
                            
                            // Store review data for export
                            window.lastExtractedBusinessReviews = result;
                            
                            // Update the reviews counter in UI
                            const statsReviews = document.getElementById('gmbStatsReviews');
                            if (statsReviews) {
                                statsReviews.textContent = result.totalReviews || 0;
                            }
                            
                            // Enable export button
                            const exportBtn = document.getElementById('gmbExportBtn');
                            if (exportBtn) {
                                exportBtn.disabled = false;
                            }
                            
                            // Show completion notification
                            GMBNotifications.showSuccess('Multiple Listings Review Extraction Complete!', 
                                `Successfully extracted ${result.totalReviews} reviews from ${result.businessCount} businesses (Range: ${result.range})`);
                        } else {
                            const errorMsg = result?.error || 'Unknown error during Multiple Listings review extraction';
                            console.error('❌ Multiple Listings review extraction failed:', errorMsg);
                            statusText.textContent = 'Multiple Listings review extraction failed';
                            statusIndicator.className = 'gmb-status__indicator--error';
                            if (progressText) {
                                progressText.textContent = errorMsg;
                            }
                            throw new Error(errorMsg);
                        }
                    } catch (error) {
                        console.error('❌ Error during Multiple Listings review extraction:', error);
                        statusText.textContent = 'Multiple Listings review extraction failed';
                        statusIndicator.className = 'gmb-status__indicator--error';
                        if (progressText) {
                            progressText.textContent = error.message;
                        }
                        throw error;
                    }
                }
            }
        } catch (error) {
            console.error('Extraction error:', error);
            statusText.textContent = 'Extraction failed';
            statusIndicator.className = 'gmb-status__indicator--error';
            progressText.textContent = error.message;
        } finally {
            startExtractionBtn.disabled = false;
        }
    }

    function exportExtractedData() {
        console.log('Exporting extracted data...');
        
        const isProListPage = window.location.href.includes('google.com/localservices/prolist');
        const isSingleBusinessPage = window.location.href.includes('google.com/maps/place/');
        const selectedExtractor = getSelectedExtractor();
        
        // Debug logging
        console.log('🔍 Export Debug Info:', {
            currentUrl: window.location.href,
            isProListPage,
            isSingleBusinessPage,
            selectedExtractor,
            hasLastExtractedAttributes: !!window.lastExtractedAttributes,
            attributesType: window.lastExtractedAttributes?.type,
            hasLastExtractedBusinessReviews: !!window.lastExtractedBusinessReviews,
            reviewsType: window.lastExtractedBusinessReviews?.type
        });
        
        try {
            // PRIORITY 1: Check for Multiple Listings attributes export first
            if (window.lastExtractedAttributes && window.lastExtractedAttributes.type === 'multiple_listings_attributes') {
                console.log('🎯 Exporting Multiple Listings attributes...');
                const csvContent = convertMultipleListingsAttributesToCSV(window.lastExtractedAttributes);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(csvContent, `multiple-listings-attributes-${timestamp}.csv`);
                console.log('✅ Multiple Listings attributes exported successfully!');
                return;
            }
            
            // PRIORITY 2: Check for Multiple Listings reviews export
            if (window.lastExtractedBusinessReviews && window.lastExtractedBusinessReviews.type === 'multiple_listings_reviews') {
                console.log('🎯 Exporting Multiple Listings reviews...');
                const csvContent = convertMultipleListingsReviewsToCSV(window.lastExtractedBusinessReviews);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(csvContent, `multiple-listings-reviews-${timestamp}.csv`);
                console.log('✅ Multiple Listings reviews exported successfully!');
                return;
            }
            
            // PRIORITY 3: Check for ProList review data
            if (window.proListReviewData && window.proListReviewData.success && window.proListReviewData.reviews) {
                console.log('🎯 Exporting ProList reviews...');
                const csvContent = convertProListReviewsDataToCSV(window.proListReviewData);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(csvContent, `prolist-reviews-${timestamp}.csv`);
                console.log('✅ ProList reviews exported successfully!');
                return;
            }

            // Handle single business page exports
            if (isSingleBusinessPage) {
                const hasAttributes = window.lastExtractedAttributes && Object.keys(window.lastExtractedAttributes).length > 0;
                const hasReviews = window.lastExtractedBusinessReviews && window.lastExtractedBusinessReviews.reviews && window.lastExtractedBusinessReviews.reviews.length > 0;
                
                if (hasAttributes && hasReviews) {
                    // Export both attributes and reviews in separate files
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    
                    // Export attributes
                    const attributesCsvContent = convertGoogleMapsAttributesToCSV(window.lastExtractedAttributes);
                    downloadCSV(attributesCsvContent, `business-attributes-${timestamp}.csv`);
                    
                    // Export reviews with business name in filename
                    const reviewsCsvContent = convertSimplifiedReviewsToCSV(window.lastExtractedBusinessReviews);
                    
                    // Extract business name for filename
                    let businessName = 'unknown-business';
                    if (window.lastExtractedBusinessReviews.businessName) {
                        businessName = formatBusinessNameForFilename(window.lastExtractedBusinessReviews.businessName);
                    } else if (window.lastExtractedBusinessReviews.reviews && window.lastExtractedBusinessReviews.reviews.length > 0) {
                        const firstReview = window.lastExtractedBusinessReviews.reviews[0];
                        if (firstReview && firstReview.businessName) {
                            businessName = formatBusinessNameForFilename(firstReview.businessName);
                        }
                    }
                    
                    const reviewTimestamp = new Date().toISOString().split('T')[0]; // Get YYYY-MM-DD format
                    const reviewFilename = `${businessName}-reviews-${reviewTimestamp}.csv`;
                    downloadCSV(reviewsCsvContent, reviewFilename);
                    
                    console.log('Both business attributes and reviews exported successfully!');
                    return;
                } else if (hasAttributes) {
                    // Export just attributes
                    const attributesCsvContent = convertGoogleMapsAttributesToCSV(window.lastExtractedAttributes);
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    downloadCSV(attributesCsvContent, `business-attributes-${timestamp}.csv`);
                    console.log('Business attributes exported successfully!');
                    return;
                } else if (hasReviews) {
                    // Export just reviews with business name in filename
                    const csvContent = convertSimplifiedReviewsToCSV(window.lastExtractedBusinessReviews);
                    
                    // Extract business name for filename
                    let businessName = 'unknown-business';
                    if (window.lastExtractedBusinessReviews.businessName) {
                        businessName = formatBusinessNameForFilename(window.lastExtractedBusinessReviews.businessName);
                    } else if (window.lastExtractedBusinessReviews.reviews && window.lastExtractedBusinessReviews.reviews.length > 0) {
                        const firstReview = window.lastExtractedBusinessReviews.reviews[0];
                        if (firstReview && firstReview.businessName) {
                            businessName = formatBusinessNameForFilename(firstReview.businessName);
                        }
                    }
                    
                    const timestamp = new Date().toISOString().split('T')[0]; // Get YYYY-MM-DD format
                    const filename = `${businessName}-reviews-${timestamp}.csv`;
                    downloadCSV(csvContent, filename);
                    console.log('Business reviews exported successfully!');
                    return;
                } else {
                    alert('No data available to export. Please extract attributes or reviews first.');
                    return;
                }
            }
            
            // Handle ProList page exports
            if (isProListPage && window.proListExtractor) {
                window.proListExtractor.exportToCSV();
                return;
            }
            
            // Handle Google Search Services Extractor
            if (selectedExtractor === 'services' && window.googleSearchServicesExtractor) {
                window.googleSearchServicesExtractor.exportToCSV();
                return;
            }
            
            // Fallback: export all available data types
            console.log('🔄 Using fallback: exportAllAvailableData...');
            exportAllAvailableData();
            
        } catch (error) {
            console.error('Error during export:', error);
            alert(`Export failed: ${error.message}`);
        }
    }

    // Helper function to format business name for filename
    function formatBusinessNameForFilename(businessName) {
        if (!businessName) return 'unknown-business';
        
        return businessName
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
            .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
            .substring(0, 50) || 'unknown-business'; // Limit length and provide fallback
    }

    // Updated function to export all available data types
    function exportAllAvailableData() {
        console.log('Exporting all available data...');
        
        const availableData = [];
        let exportedCount = 0;
        
        try {
            // Check for Multiple Listings attributes first
            if (window.lastExtractedAttributes && window.lastExtractedAttributes.type === 'multiple_listings_attributes') {
                console.log('🎯 Fallback: Exporting Multiple Listings attributes...');
                const csvContent = convertMultipleListingsAttributesToCSV(window.lastExtractedAttributes);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(csvContent, `multiple-listings-attributes-${timestamp}.csv`);
                availableData.push('Multiple Listings Attributes');
                exportedCount++;
            }
            
            // Check for single business reviews from SimplifiedReviewScraper
            if (window.lastExtractedBusinessReviews && window.lastExtractedBusinessReviews.reviews) {
                const csvContent = convertSimplifiedReviewsToCSV(window.lastExtractedBusinessReviews);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(csvContent, `business-reviews-${timestamp}.csv`);
                availableData.push('Business Reviews');
                exportedCount++;
            }
            
            // Check for Multiple Listings reviews
            if (window.lastExtractedBusinessReviews && window.lastExtractedBusinessReviews.type === 'multiple_listings_reviews') {
                const reviewsCsvContent = convertMultipleListingsReviewsToCSV(window.lastExtractedBusinessReviews);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(reviewsCsvContent, `multiple-listings-reviews-${timestamp}.csv`);
                availableData.push('Multiple Listings Reviews');
                exportedCount++;
            }
            
            // Check for Google Maps attributes (non-Multiple Listings)
            if (window.lastExtractedAttributes && 
                typeof window.lastExtractedAttributes === 'object' && 
                Object.keys(window.lastExtractedAttributes).length > 0 &&
                window.lastExtractedAttributes.type !== 'multiple_listings_attributes') {
                const attributesCsvContent = convertGoogleMapsAttributesToCSV(window.lastExtractedAttributes);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(attributesCsvContent, `business-attributes-${timestamp}.csv`);
                availableData.push('Business Attributes');
                exportedCount++;
            }
            
            // Check for ProList business analysis data
            if (window.proListExtractor && window.proListExtractor.analysisData) {
                const basicCsvContent = window.proListExtractor.exportToCSV();
                if (basicCsvContent) {
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    downloadCSV(basicCsvContent, `prolist-business-analysis-${timestamp}.csv`);
                    availableData.push('ProList Business Analysis');
                    exportedCount++;
                }
            }

            // Check for ProList services data
            if (window.lastExtractedServices && window.lastExtractedServices.services) {
                const servicesCsvContent = convertProListServicesToCSV(window.lastExtractedServices);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(servicesCsvContent, `prolist-services-${timestamp}.csv`);
                availableData.push('ProList Services');
                exportedCount++;
            } else if (window.proListExtractor && window.proListExtractor.lastExtractedServices) {
                const servicesCsvContent = convertProListServicesToCSV(window.proListExtractor.lastExtractedServices);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(servicesCsvContent, `prolist-services-${timestamp}.csv`);
                availableData.push('ProList Services');
                exportedCount++;
            }
            
            // Check for ProList attributes data
            if (window.lastExtractedAttributes && window.lastExtractedAttributes.attributes) {
                const attributesCsvContent = convertProListAttributesToCSV(window.lastExtractedAttributes);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(attributesCsvContent, `prolist-attributes-${timestamp}.csv`);
                availableData.push('ProList Attributes');
                exportedCount++;
            } else if (window.proListExtractor && window.proListExtractor.lastExtractedAttributes) {
                const attributesCsvContent = convertProListAttributesToCSV(window.proListExtractor.lastExtractedAttributes);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(attributesCsvContent, `prolist-attributes-${timestamp}.csv`);
                availableData.push('ProList Attributes');
                exportedCount++;
            }
            
            // Check for ProList review snippets
            if (window.proListExtractor && 
                window.proListExtractor.analysisData && 
                window.proListExtractor.analysisData.reviewSnippets && 
                window.proListExtractor.analysisData.reviewSnippets.length > 0) {
                
                const reviewsCsvContent = convertProListReviewsToCSV(window.proListExtractor.analysisData);
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                downloadCSV(reviewsCsvContent, `prolist-reviews-${timestamp}.csv`);
                availableData.push('ProList Review Snippets');
                exportedCount++;
            }
            
            // Show summary
            if (exportedCount > 0) {
                setTimeout(() => {
                    alert(`Successfully exported ${exportedCount} data type(s): ${availableData.join(', ')}`);
                }, 500);
            } else {
                alert('No data available for export. Please run some extractions first.');
            }
            
        } catch (error) {
            console.error('Error exporting data:', error);
            alert(`Failed to export data: ${error.message}`);
        }
    }

    // New CSV conversion function for ProList services
    function convertProListServicesToCSV(result) {
        if (!result || !result.services || result.services.length === 0) {
            return 'No Pro List services data available';
        }
        
        let csvContent = 'Pro List Business Services\n\n';
        
        // Summary section
        csvContent += 'SUMMARY\n';
        csvContent += `Total Businesses,${result.businessCount || 'N/A'}\n`;
        csvContent += `Total Services,${result.services.length}\n`;
        if (result.analysis) {
            csvContent += `Unique Services,${result.analysis.uniqueServices || 'N/A'}\n`;
            csvContent += `Service Categories,${result.analysis.categories || 'N/A'}\n`;
        }
        csvContent += '\n';
        
        // Individual services
        csvContent += 'DETAILED SERVICES\n';
        csvContent += 'Business Index,Business Name,Service Category,Service Name,Extracted At\n';
        
        result.services.forEach(item => {
            csvContent += `${item.businessIndex || 'N/A'},"${(item.businessName || '').replace(/"/g, '""')}","${(item.service.category || '').replace(/"/g, '""')}","${(item.service.service || '').replace(/"/g, '""')}","${item.extractedAt || ''}"\n`;
        });
        
        return csvContent;
    }

    // New CSV conversion function for ProList reviews
    function convertProListReviewsToCSV(analysisData) {
        if (!analysisData || !analysisData.reviewSnippets || analysisData.reviewSnippets.length === 0) {
            return 'No Pro List review snippets available';
        }
        
        let csvContent = 'Pro List Review Snippets\n\n';
        
        // Summary section
        csvContent += 'SUMMARY\n';
        csvContent += `Total Businesses,${analysisData.totalBusinesses || 'N/A'}\n`;
        csvContent += `Review Snippets,${analysisData.reviewSnippets.length}\n`;
        csvContent += `Search Term,"${(analysisData.searchTerm || '').replace(/"/g, '""')}"\n`;
        csvContent += '\n';
        
        // Individual review snippets
        csvContent += 'REVIEW SNIPPETS\n';
        csvContent += 'Business Index,Business Name,Review Text,Extracted At\n';
        
        analysisData.reviewSnippets.forEach(item => {
            csvContent += `${item.businessIndex || 'N/A'},"${(item.businessName || '').replace(/"/g, '""')}","${(item.reviewText || '').replace(/"/g, '""')}","${item.extractedAt || analysisData.timestamp || ''}"\n`;
        });
        
        return csvContent;
    }
    
    // CSV conversion function for ProList Review Scraper data
    function convertProListReviewsDataToCSV(reviewData) {
        if (!reviewData || !reviewData.reviews || reviewData.reviews.length === 0) {
            return 'No ProList review data available';
        }
        
        let csvContent = 'ProList Reviews\n\n';
        
        // Summary section
        csvContent += 'SUMMARY\n';
        csvContent += `Total Reviews,${reviewData.reviewCount || reviewData.reviews.length}\n`;
        csvContent += `Success,${reviewData.success ? 'Yes' : 'No'}\n`;
        csvContent += `Range Applied,${reviewData.rangeApplied ? 'Yes' : 'No'}\n`;
        if (reviewData.rangeApplied && reviewData.maxReviews) {
            csvContent += `Max Reviews Limit,${reviewData.maxReviews}\n`;
        }
        csvContent += `Total Available,${reviewData.totalAvailable || 'N/A'}\n`;
        csvContent += `Extracted At,${new Date().toISOString()}\n`;
        csvContent += '\n';
        
        // Headers for individual reviews
        csvContent += 'DETAILED REVIEWS\n';
        csvContent += 'Review ID,Reviewer Name,Rating,Date,Review Text,Helpful Count,Business Response,Photo Count,Business Name,Extracted At\n';
        
        // Individual reviews
        reviewData.reviews.forEach((review, index) => {
            csvContent += `${review.id || index + 1},"${(review.reviewerName || '').replace(/"/g, '""')}",${review.rating || ''},"${(review.date || '').replace(/"/g, '""')}","${(review.reviewText || '').replace(/"/g, '""')}",${review.helpfulCount || ''},"${(review.response || '').replace(/"/g, '""')}",${review.photoCount || 0},"${(review.businessName || '').replace(/"/g, '""')}","${(review.extractedAt || '').replace(/"/g, '""')}"\n`;
        });
        
        return csvContent;
    }
    
    // Helper function to download CSV
    function downloadCSV(csvContent, filename) {
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    // CSV conversion functions for attributes
    function convertGoogleMapsAttributesToCSV(attributes) {
        if (!attributes || Object.keys(attributes).length === 0) {
            return 'No attributes data available';
        }
        
        let csvContent = 'Google Maps Business Attributes\n\n';
        csvContent += 'Section,Attribute\n';
        
        Object.keys(attributes).forEach(sectionTitle => {
            const sectionAttributes = attributes[sectionTitle];
            if (Array.isArray(sectionAttributes)) {
                sectionAttributes.forEach(attr => {
                    csvContent += `"${sectionTitle}","${attr.replace(/"/g, '""')}"\n`;
                });
            }
        });
        
        return csvContent;
    }

    function convertProListAttributesToCSV(result) {
        console.log('Converting ProList attributes to CSV...');
        
        if (!result || !result.results || result.results.length === 0) {
            return 'No ProList attributes data available for export';
        }
        
        // Create CSV content with summary info first
        let csvContent = `ProList Attributes Extraction Summary\n`;
        csvContent += `Type,${result.type || 'prolist_attributes'}\n`;
        csvContent += `Range,${result.range || 'N/A'}\n`;
        csvContent += `Total Businesses,${result.businessCount || 0}\n`;
        csvContent += `Successful Extractions,${result.successfulExtractions || 0}\n`;
        csvContent += `Total Attributes,${result.totalAttributes || 0}\n`;
        csvContent += `Error Count,${result.errorCount || 0}\n`;
        csvContent += `Timestamp,${result.timestamp || ''}\n\n`;
        
        // Add individual business attributes data
        csvContent += 'Individual Business Attributes\n';
        csvContent += 'Business Number,Business Name,Attribute Section,Attribute Item,Success,Error,Timestamp\n';
        
        result.results.forEach(business => {
            if (business.attributes && Object.keys(business.attributes).length > 0) {
                // Each business can have multiple attribute sections
                Object.keys(business.attributes).forEach(sectionName => {
                    const sectionAttributes = business.attributes[sectionName];
                    if (Array.isArray(sectionAttributes) && sectionAttributes.length > 0) {
                        sectionAttributes.forEach(attribute => {
                            csvContent += `${business.businessNumber || 'N/A'},"${(business.businessName || '').replace(/"/g, '""')}","${sectionName.replace(/"/g, '""')}","${attribute.replace(/"/g, '""')}",${business.success ? 'Yes' : 'No'},"${(business.error || '').replace(/"/g, '""')}",${business.timestamp || ''}\n`;
                        });
                    } else {
                        // If section is empty, still add a row to show the business was processed
                        csvContent += `${business.businessNumber || 'N/A'},"${(business.businessName || '').replace(/"/g, '""')}","${sectionName.replace(/"/g, '""')}","No attributes found",${business.success ? 'Yes' : 'No'},"${(business.error || '').replace(/"/g, '""')}",${business.timestamp || ''}\n`;
                    }
                });
            } else {
                // Business with no attributes or error
                csvContent += `${business.businessNumber || 'N/A'},"${(business.businessName || '').replace(/"/g, '""')}","No Attributes","No attributes found",${business.success ? 'Yes' : 'No'},"${(business.error || '').replace(/"/g, '""')}",${business.timestamp || ''}\n`;
            }
        });
        
        return csvContent;
    }

    function convertMultipleListingsAttributesToCSV(result) {
        console.log('Converting Multiple Listings attributes to CSV...');
        
        if (!result || !result.results || result.results.length === 0) {
            return 'No Multiple Listings attributes data available for export';
        }
        
        // Create CSV content with summary info first
        let csvContent = `Multiple Listings Attributes Extraction Summary\n`;
        csvContent += `Type,${result.type || 'multiple_listings_attributes'}\n`;
        csvContent += `Range,${result.range || 'N/A'}\n`;
        csvContent += `Total Businesses,${result.businessCount || 0}\n`;
        csvContent += `Successful Extractions,${result.successfulExtractions || 0}\n`;
        csvContent += `Total Attributes,${result.totalAttributes || 0}\n`;
        csvContent += `Error Count,${result.errorCount || 0}\n`;
        csvContent += `Timestamp,${result.timestamp || ''}\n\n`;
        
        // Add individual business attributes data
        csvContent += 'Individual Business Attributes\n';
        csvContent += 'Business Number,Business Name,Attribute Section,Attribute Item,Attribute Count,Success,Error,Timestamp\n';
        
        result.results.forEach(business => {
            if (business.attributes && Object.keys(business.attributes).length > 0) {
                // Each business can have multiple attribute sections
                Object.keys(business.attributes).forEach(sectionName => {
                    const sectionAttributes = business.attributes[sectionName];
                    if (Array.isArray(sectionAttributes) && sectionAttributes.length > 0) {
                        sectionAttributes.forEach(attribute => {
                            csvContent += `${business.businessNumber || 'N/A'},"${(business.businessName || '').replace(/"/g, '""')}","${sectionName.replace(/"/g, '""')}","${attribute.replace(/"/g, '""')}",${business.attributeCount || 0},${business.success ? 'Yes' : 'No'},"${(business.error || '').replace(/"/g, '""')}",${business.timestamp || ''}\n`;
                        });
                    } else {
                        // If section is empty, still add a row to show the business was processed
                        csvContent += `${business.businessNumber || 'N/A'},"${(business.businessName || '').replace(/"/g, '""')}","${sectionName.replace(/"/g, '""')}","No attributes in this section",${business.attributeCount || 0},${business.success ? 'Yes' : 'No'},"${(business.error || '').replace(/"/g, '""')}",${business.timestamp || ''}\n`;
                    }
                });
            } else {
                // Business with no attributes or error
                const errorMessage = business.error || business.message || 'No attributes found';
                csvContent += `${business.businessNumber || 'N/A'},"${(business.businessName || '').replace(/"/g, '""')}","No Attributes","${errorMessage.replace(/"/g, '""')}",${business.attributeCount || 0},${business.success ? 'Yes' : 'No'},"${(business.error || '').replace(/"/g, '""')}",${business.timestamp || ''}\n`;
            }
        });
        
        return csvContent;
    }

    // CSV conversion function for SimplifiedReviewScraper data
    function convertSimplifiedReviewsToCSV(reviewData) {
        if (!reviewData || !reviewData.reviews || reviewData.reviews.length === 0) {
            return 'No review data available';
        }
        
        let csvContent = 'Single Business Reviews\\n\\n';
        
        // Summary section
        csvContent += 'SUMMARY\\n';
        csvContent += `Total Reviews,${reviewData.reviewCount || reviewData.reviews.length}\\n`;
        if (reviewData.businessName) {
            csvContent += `Business Name,${reviewData.businessName.replace(/\"/g, '\"\"')}\\n`;
        }
        csvContent += `Extracted At,${new Date().toISOString()}\\n`;
        csvContent += '\\n';
        
        // Headers for individual reviews
        csvContent += 'DETAILED REVIEWS\\n';
        csvContent += 'Review ID,Reviewer Name,Rating,Date,Review Text,Helpful Count,Business Response,Photo Count,Extracted At\\n';
        
        // Individual reviews
        reviewData.reviews.forEach(review => {
            csvContent += `${review.id || ''},"${(review.reviewerName || '').replace(/\"/g, '\"\"')}",${review.rating || ''},"${(review.date || '').replace(/\"/g, '\"\"')}","${(review.reviewText || '').replace(/\"/g, '\"\"')}",${review.helpfulCount || ''},"${(review.response || '').replace(/\"/g, '\"\"')}",${review.photoCount || 0},"${(review.extractedAt || '').replace(/\"/g, '\"\"')}"\\n`;
        });
        
        return csvContent;
    }

    // CSV conversion function for Multiple Listings Reviews data
    function convertMultipleListingsReviewsToCSV(reviewData) {
        if (!reviewData || !reviewData.results || reviewData.results.length === 0) {
            return 'No Multiple Listings review data available';
        }
        
        let csvContent = 'Multiple Listings Reviews\\n\\n';
        
        // Summary section
        csvContent += 'SUMMARY\\n';
        csvContent += `Total Businesses Processed,${reviewData.businessCount || reviewData.results.length}\\n`;
        csvContent += `Successful Extractions,${reviewData.successfulExtractions || 0}\\n`;
        csvContent += `Total Reviews Extracted,${reviewData.totalReviews || 0}\\n`;
        csvContent += `Business Range,${reviewData.range || 'N/A'}\\n`;
        if (reviewData.maxReviews) {
            csvContent += `Review Limit Applied,${reviewData.maxReviews}\\n`;
        }
        csvContent += `Error Count,${reviewData.errorCount || 0}\\n`;
        csvContent += `Extracted At,${reviewData.timestamp || new Date().toISOString()}\\n`;
        csvContent += '\\n';
        
        // Business Summary section
        csvContent += 'BUSINESS SUMMARY\\n';
        csvContent += 'Business Number,Business Name,Review Count,Total Expected,Success,Error Message,Timestamp\\n';
        
        reviewData.results.forEach(business => {
            const errorMsg = (business.error || business.message || '').replace(/"/g, '""');
            csvContent += `${business.businessNumber || 'N/A'},"${(business.businessName || '').replace(/"/g, '""')}",${business.reviewCount || 0},${business.totalExpected || 0},${business.success ? 'Yes' : 'No'},"${errorMsg}","${business.timestamp || ''}"\\n`;
        });
        
        csvContent += '\\n';
        
        // Detailed Reviews section
        csvContent += 'DETAILED REVIEWS\\n';
        csvContent += 'Business Number,Business Name,Review ID,Reviewer Name,Rating,Date,Review Text,Helpful Count,Business Response,Photo Count,Extracted At\\n';
        
        // Process all reviews from all businesses
        reviewData.results.forEach(business => {
            if (business.reviews && business.reviews.length > 0) {
                business.reviews.forEach(review => {
                    csvContent += `${business.businessNumber || 'N/A'},"${(business.businessName || '').replace(/"/g, '""')}",${review.id || ''},"${(review.reviewerName || '').replace(/"/g, '""')}",${review.rating || ''},"${(review.date || '').replace(/"/g, '""')}","${(review.reviewText || '').replace(/"/g, '""')}",${review.helpfulCount || ''},"${(review.response || '').replace(/"/g, '""')}",${review.photoCount || 0},"${(review.extractedAt || '').replace(/"/g, '""')}"\\n`;
                });
            }
        });
        
        return csvContent;
    }

    // Helper function to update export button text based on available data
    function updateExportButtonText() {
        const exportBtn = document.getElementById('gmbExportBtn');
        if (!exportBtn) return;
        
        const isProListPage = window.location.href.includes('google.com/localservices/prolist');
        const isSingleBusinessPage = window.location.href.includes('google.com/maps/place/');
        
        if (isSingleBusinessPage) {
            const hasAttributes = window.lastExtractedAttributes && Object.keys(window.lastExtractedAttributes).length > 0;
            const hasReviews = window.lastExtractedBusinessReviews && window.lastExtractedBusinessReviews.reviews && window.lastExtractedBusinessReviews.reviews.length > 0;
            
            if (hasAttributes && hasReviews) {
                exportBtn.textContent = '📊 Export Both (Attributes + Reviews)';
            } else if (hasAttributes) {
                exportBtn.textContent = '📊 Export Attributes';
            } else if (hasReviews) {
                exportBtn.textContent = '📊 Export Reviews';
            } else {
                exportBtn.textContent = '📊 Export CSV';
            }
        } else {
            exportBtn.textContent = '📊 Export CSV';
        }
    }

    // Add input validation for review range input
    if (reviewRangeInput) {
        reviewRangeInput.addEventListener('input', function(e) {
            // Remove non-digits
            let value = e.target.value.replace(/[^\d]/g, '');
            
            // Limit to 5 digits
            if (value.length > 5) {
                value = value.substring(0, 5);
            }
            
            e.target.value = value;
            
            // Show validation feedback
            const helpText = e.target.parentElement.querySelector('.gmb-search-extractor__help-text');
            if (helpText) {
                if (value === '') {
                    helpText.textContent = 'Leave empty to extract all reviews, or enter a number (1-99999)';
                    helpText.style.color = '#9ca3af';
                } else {
                    const numValue = parseInt(value, 10);
                    if (numValue >= 1 && numValue <= 99999) {
                        helpText.textContent = `Will extract up to ${numValue.toLocaleString()} reviews`;
                        helpText.style.color = '#10b981';
                    } else {
                        helpText.textContent = 'Please enter a number between 1 and 99,999';
                        helpText.style.color = '#ef4444';
                    }
                }
            }
        });
    }
}

// Function to inject business numbers on Pro List pages
function injectBusinessNumbers() {
    console.log('GMB Extractor: Injecting business numbers...');
    
    try {
            // Find all business cards in the Pro List
            const businessCards = document.querySelectorAll('.DVBRsc[jsaction*="click:xxAyzd"]');
        console.log(`GMB Extractor: Found ${businessCards.length} business cards to number`);
        
        businessCards.forEach((card, index) => {
            // Check if number badge already exists
            if (card.querySelector('.gmb-business-number')) {
                return;
            }

            // Make card relatively positioned for absolute positioning of badge
            if (getComputedStyle(card).position === 'static') {
                card.style.position = 'relative';
            }
            
            // Create number badge
            const numberBadge = document.createElement('div');
            numberBadge.className = 'gmb-business-number';
            numberBadge.textContent = (index + 1).toString();
            
            // Inject the badge
            card.appendChild(numberBadge);
        });
        
        console.log(`GMB Extractor: Successfully injected ${businessCards.length} business numbers`);
            
        } catch (error) {
        console.error('GMB Extractor: Error injecting business numbers:', error);
    }
}

// Function to inject business numbers on Multiple Listings pages (Google Maps search results)
function injectMultipleListingsNumbers() {
    console.log('GMB Extractor: Injecting Multiple Listings business numbers...');
    
    try {
        // Multiple selectors for different Google Maps layouts
        const selectors = [
            // Standard search results in sidebar
            '[role="feed"] > div[jsaction]',
            // Alternative sidebar results
            '.Nv2PK.tH5CWc.THOPZb[role="button"]',
            // Business listings with data-cid
            '[data-cid]',
            // Generic search result items
            '.hfpxzc',
            // Expanded search results
            '.UaQhfb.fontBodyMedium',
            // Alternative layout
            '.rllt__details',
            // Another common pattern
            '.VkpGBb'
        ];
        
        let businessCards = [];
        
        // Try each selector until we find business listings
        for (const selector of selectors) {
            const elements = document.querySelectorAll(selector);
            console.log(`GMB Extractor: Trying selector "${selector}", found ${elements.length} elements`);
            
            if (elements.length > 0) {
                // Filter out elements that are clearly not business listings
                const validElements = Array.from(elements).filter(el => {
                    // Must have some text content
                    if (!el.textContent || el.textContent.trim().length < 10) return false;
                    
                    // Should not be a button with only a few characters
                    if (el.tagName === 'BUTTON' && el.textContent.trim().length < 5) return false;
                    
                    // Should not be a navigation element
                    if (el.getAttribute('aria-label') && el.getAttribute('aria-label').includes('navigation')) return false;
                    
                    // Should not be an advertisement indicator
                    if (el.textContent.includes('Ad') && el.textContent.length < 10) return false;
                    
                    return true;
                });
                
                if (validElements.length >= 3) { // Minimum threshold for valid business listings
                    businessCards = validElements;
                    console.log(`GMB Extractor: Using selector "${selector}" with ${businessCards.length} valid business listings`);
                    break;
                }
            }
        }
        
        if (businessCards.length === 0) {
            console.log('GMB Extractor: No business listings found with any selector, trying fallback approach...');
            
            // Fallback: look for any clickable elements that might be business listings
            const allClickable = document.querySelectorAll('[role="button"], [jsaction], a[href*="maps"]');
            const potentialBusinesses = Array.from(allClickable).filter(el => {
                const text = el.textContent?.trim() || '';
                // Look for elements with business-like characteristics
                return text.length > 20 && 
                       text.length < 200 &&
                       !text.toLowerCase().includes('google') &&
                       !text.toLowerCase().includes('search') &&
                       !text.toLowerCase().includes('filter') &&
                       !text.toLowerCase().includes('sort');
            });
            
            if (potentialBusinesses.length > 0) {
                businessCards = potentialBusinesses.slice(0, 20); // Limit to first 20
                console.log(`GMB Extractor: Using fallback approach with ${businessCards.length} potential businesses`);
            }
        }
        
        if (businessCards.length === 0) {
            console.log('GMB Extractor: No business listings found for numbering');
            return;
        }
        
        console.log(`GMB Extractor: Found ${businessCards.length} business listings to number`);
        
        businessCards.forEach((card, index) => {
            // Check if number badge already exists
            if (card.querySelector('.gmb-business-number')) {
                return;
            }

            // Make card relatively positioned for absolute positioning of badge
            if (getComputedStyle(card).position === 'static') {
                card.style.position = 'relative';
            }
            
            // Create number badge
            const numberBadge = document.createElement('div');
            numberBadge.className = 'gmb-business-number gmb-business-number--multiple-listings';
            numberBadge.textContent = (index + 1).toString();
            numberBadge.setAttribute('title', `Business #${index + 1} - Use this number in your range (e.g., "1-5" or "${index + 1}")`);
            
            // Inject the badge
            card.appendChild(numberBadge);
        });
        
        console.log(`GMB Extractor: Successfully injected ${businessCards.length} Multiple Listings business numbers`);
        
        // Remove the notification - user requested no browser notification
    } catch (error) {
        console.error('GMB Extractor: Error injecting Multiple Listings business numbers:', error);
    }
}

// Reusable function to update extraction stats
function updateExtractionStats(data, extractorType) {
    console.log('Content: updateExtractionStats called with extractorType:', extractorType, 'data:', data);
    
    // Get stats elements (not all may exist in every interface)
    const statsBusinesses = document.getElementById('gmbStatsBusinesses');
    const statsServices = document.getElementById('gmbStatsServices');
    const statsAttributes = document.getElementById('gmbStatsAttributes');
    const statsReviews = document.getElementById('gmbStatsReviews');
    
    console.log('Content: Stats elements found:', {
        statsBusinesses: !!statsBusinesses,
        statsServices: !!statsServices, 
        statsAttributes: !!statsAttributes,
        statsReviews: !!statsReviews
    });
    
    // Initialize counts
    let businessCount = 0;
    let serviceCount = 0;
    let attributeCount = 0;
    let reviewCount = 0;
    
    if (extractorType === 'services' && data && data.data) {
        const servicesData = data.data;
        businessCount = servicesData.businessCount || 0;
        
        if (servicesData.services && Array.isArray(servicesData.services)) {
            // For ProList format with flat services array
            serviceCount = servicesData.services.length;
        } else if (servicesData.services && Array.isArray(servicesData.services)) {
            // For nested format, count all services across businesses
            serviceCount = servicesData.services.reduce((total, business) => {
                return total + (business.services ? business.services.length : 0);
            }, 0);
        }
        
        console.log('Content: Services extraction stats:', { businessCount, serviceCount });
    } else if (extractorType === 'attributes' && data && data.data) {
        const attributesData = data.data;
        businessCount = attributesData.businessCount || 0;
        
        // Handle Multiple Listings format with direct totalAttributes
        if (attributesData.totalAttributes !== undefined) {
            attributeCount = attributesData.totalAttributes;
        } else if (attributesData.attributes && Array.isArray(attributesData.attributes)) {
            // Handle ProList format with nested attributes array
            attributeCount = attributesData.attributes.reduce((total, business) => {
                if (business.attributes) {
                    return total + Object.values(business.attributes).reduce((sum, attrs) => sum + attrs.length, 0);
                }
                return total;
            }, 0);
        }
        
        console.log('Content: Attributes extraction stats:', { businessCount, attributeCount });
    }
    
    // Update only the stats elements that exist
    if (statsBusinesses) {
        statsBusinesses.textContent = businessCount;
        console.log('Content: Updated businesses count:', businessCount);
    }
    if (statsServices) {
        statsServices.textContent = serviceCount;
        console.log('Content: Updated services count:', serviceCount);
    }
    if (statsAttributes) {
        statsAttributes.textContent = attributeCount;
        console.log('Content: Updated attributes count:', attributeCount);
    }
    if (statsReviews) {
        statsReviews.textContent = reviewCount;
        console.log('Content: Updated reviews count:', reviewCount);
    }
}

// Extract attributes from Multiple Listings (Google Maps search results) in a specified range
async function extractAttributesFromMultipleListings(businessRange) {
    console.log(`🔍 Starting Multiple Listings attributes extraction for range ${businessRange.start}-${businessRange.end}`);
    
    try {
        // Find all business listings using the provided selector
        const businessListings = document.querySelectorAll('.hfpxzc');
        console.log(`📍 Found ${businessListings.length} business listings on page`);
        
        if (businessListings.length === 0) {
            throw new Error('No business listings found on this page. Please ensure you are on a Google Maps search results page.');
        }
        
        // Validate range
        if (businessRange.start < 1 || businessRange.end > businessListings.length) {
            throw new Error(`Invalid range: ${businessRange.start}-${businessRange.end}. Page has ${businessListings.length} businesses. Please adjust your range.`);
        }
        
        // Calculate businesses to process (convert to 0-based indexing)
        const startIndex = businessRange.start - 1;
        const endIndex = businessRange.end - 1;
        const businessesToProcess = businessListings.length;
        const rangeToProcess = endIndex - startIndex + 1;
        
        console.log(`🎯 Processing businesses ${businessRange.start}-${businessRange.end} (${rangeToProcess} businesses)`);
        
        const results = [];
        let processedCount = 0;
        let errorCount = 0;
        
        // Update global progress
        resetGlobalProgress();
        showGlobalProgress();
        updateGlobalProgress(0, rangeToProcess, 'Starting Multiple Listings attributes extraction...');
        
        // Initialize counters in UI
        updateExtractionStats({
            data: {
                businessCount: 0,
                totalAttributes: 0
            }
        }, 'attributes');
        
        // Process each business in the specified range
        for (let i = startIndex; i <= endIndex; i++) {
            const currentBusinessNumber = i + 1;
            const businessListing = businessListings[i];
            
            console.log(`🔄 Processing business ${currentBusinessNumber}/${businessListings.length}...`);
            updateGlobalProgress(processedCount, rangeToProcess, `Processing business ${currentBusinessNumber} of ${businessRange.end}...`);
            
            try {
                // Get business name from the listing for reference
                const businessNameElement = businessListing.querySelector('.qBF1Pd, .fontHeadlineSmall');
                const businessName = businessNameElement ? businessNameElement.textContent.trim() : `Business ${currentBusinessNumber}`;
                
                console.log(`📍 Extracting attributes for: ${businessName}`);
                
                // Click on the business listing to open the individual popup using natural click
                simulateNaturalClick(businessListing);
                console.log(`✅ ATTRIBUTES EXTRACTION - Simulated natural click on business listing`);
                
                // Wait for the individual business popup to load
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Wait for the individual business page to load completely
                let attempts = 0;
                const maxAttempts = 10;
                while (attempts < maxAttempts) {
                    // Check if we have navigated to a business-specific page
                    if (window.location.href.includes('google.com/maps/place/') || 
                        document.querySelector('[data-value="About"]') ||
                        document.querySelector('button[aria-label*="About"]')) {
                        break;
                    }
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    attempts++;
                }
                
                if (attempts >= maxAttempts) {
                    console.warn(`⚠️ Timeout waiting for business page to load for ${businessName}`);
                    throw new Error('Timeout waiting for business page to load');
                }
                
                // Extract attributes using the existing function
                const attributes = await extractAboutAttributes();
                
                if (attributes && Object.keys(attributes).length > 0) {
                    console.log(`✅ Successfully extracted attributes for ${businessName}:`, Object.keys(attributes));
                    
                    // Count total attributes
                    const totalAttributesForBusiness = Object.values(attributes).reduce((total, attrs) => total + attrs.length, 0);
                    
                    results.push({
                        businessNumber: currentBusinessNumber,
                        businessName: businessName,
                        attributes: attributes,
                        attributeCount: totalAttributesForBusiness,
                        success: true,
                        timestamp: new Date().toISOString()
                    });
                } else {
                    console.log(`ℹ️ No attributes found for ${businessName}`);
                    results.push({
                        businessNumber: currentBusinessNumber,
                        businessName: businessName,
                        attributes: {},
                        attributeCount: 0,
                        success: true,
                        message: 'No attributes found',
                        timestamp: new Date().toISOString()
                    });
                }
                
                // Navigate back to the search results page
                window.history.back();
                
                // Wait for the search results page to load again
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                // Wait for search results to be visible again
                attempts = 0;
                while (attempts < 5) {
                    const currentListings = document.querySelectorAll('.hfpxzc');
                    if (currentListings.length > 0) {
                        break;
                    }
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    attempts++;
                }
                
            } catch (error) {
                console.error(`❌ Error extracting attributes for business ${currentBusinessNumber}:`, error);
                errorCount++;
                
                results.push({
                    businessNumber: currentBusinessNumber,
                    businessName: businessName || `Business ${currentBusinessNumber}`,
                    attributes: {},
                    attributeCount: 0,
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                
                // Try to navigate back if we're stuck on a business page
                if (window.location.href.includes('google.com/maps/place/')) {
                    window.history.back();
                    await new Promise(resolve => setTimeout(resolve, 1500));
                }
            }
            
            processedCount++;
            updateGlobalProgress(processedCount, rangeToProcess, `Completed ${processedCount}/${rangeToProcess} businesses`);
            
            // Update the extraction stats after each business is processed
            const currentTotalAttributes = results.reduce((total, r) => total + r.attributeCount, 0);
            updateExtractionStats({
                data: {
                    businessCount: processedCount,
                    totalAttributes: currentTotalAttributes
                }
            }, 'attributes');
        }
        
        // Calculate totals
        const successfulExtractions = results.filter(r => r.success && r.attributeCount > 0);
        const totalAttributes = results.reduce((total, r) => total + r.attributeCount, 0);
        
        // Update final progress
        if (errorCount === 0) {
            setGlobalProgressComplete(`✅ Attributes extraction completed! Found attributes from ${successfulExtractions.length}/${rangeToProcess} businesses`);
        } else {
            setGlobalProgressComplete(`⚠️ Attributes extraction completed with ${errorCount} errors. Found attributes from ${successfulExtractions.length}/${rangeToProcess} businesses`);
        }
        
        // Final update to extraction stats
        updateExtractionStats({
            data: {
                businessCount: rangeToProcess,
                totalAttributes: totalAttributes
            }
        }, 'attributes');
        
        console.log(`🎉 Multiple Listings attributes extraction completed!`);
        console.log(`📊 Results: ${successfulExtractions.length}/${rangeToProcess} businesses with attributes, ${totalAttributes} total attributes, ${errorCount} errors`);
        
        return {
            success: true,
            type: 'multiple_listings_attributes',
            range: `${businessRange.start}-${businessRange.end}`,
            businessCount: rangeToProcess,
            successfulExtractions: successfulExtractions.length,
            totalAttributes: totalAttributes,
            errorCount: errorCount,
            results: results,
            timestamp: new Date().toISOString()
        };
        
    } catch (error) {
        console.error('❌ Error during Multiple Listings attributes extraction:', error);
        setGlobalProgressError(`❌ Attributes extraction failed: ${error.message}`);
        
        return {
            success: false,
            error: error.message,
            type: 'multiple_listings_attributes',
            range: `${businessRange.start}-${businessRange.end}`,
            timestamp: new Date().toISOString()
        };
    }
}

function refreshPersistentPopup() {
}

function simulateNaturalClick(element) {
    const rect = element.getBoundingClientRect();
    
    // Click on the LEFT portion of the business listing to avoid "Website"/"Directions" links
    // Use about 30% from the left edge to stay in the safe "red box" area
    const clickX = rect.left + (rect.width * 0.3);
    const clickY = rect.top + rect.height / 2;
    
    // Create and dispatch mouse events in the correct sequence
    const mouseDownEvent = new MouseEvent('mousedown', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: clickX,
        clientY: clickY,
        button: 0, // Left click
        buttons: 1
    });
    
    const mouseUpEvent = new MouseEvent('mouseup', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: clickX,
        clientY: clickY,
        button: 0,
        buttons: 0
    });
    
    const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: clickX,
        clientY: clickY,
        button: 0,
        buttons: 0
    });
    
    // Dispatch events in natural sequence
    element.dispatchEvent(mouseDownEvent);
    setTimeout(() => {
        element.dispatchEvent(mouseUpEvent);
        setTimeout(() => {
            element.dispatchEvent(clickEvent);
        }, 10);
    }, 50);
    
    console.log(`🖱️ Simulated natural click at LEFT area (${clickX}, ${clickY}) on element:`, element);
}

async function extractReviewsFromMultipleListings(businessRange, maxReviews = null) {
    console.log(`🔍 Starting Multiple Listings reviews extraction for range ${businessRange.start}-${businessRange.end}`);
    
    if (maxReviews !== null) {
        console.log(`🔢 Review limit set to: ${maxReviews} per business`);
    } else {
        console.log(`🔢 No review limit - extracting all reviews`);
    }
    
    try {
        // Find all business listings using the provided selector
        const businessListings = document.querySelectorAll('.hfpxzc');
        console.log(`📍 Found ${businessListings.length} business listings on page`);
        
        if (businessListings.length === 0) {
            throw new Error('No business listings found on this page. Please ensure you are on a Google Maps search results page.');
        }
        
        // Validate range
        if (businessRange.start < 1 || businessRange.end > businessListings.length) {
            throw new Error(`Invalid range: ${businessRange.start}-${businessRange.end}. Page has ${businessListings.length} businesses. Please adjust your range.`);
        }
        
        // Calculate businesses to process (convert to 0-based indexing)
        const startIndex = businessRange.start - 1;
        const endIndex = businessRange.end - 1;
        const rangeToProcess = endIndex - startIndex + 1;
        
        console.log(`🎯 Processing businesses ${businessRange.start}-${businessRange.end} (${rangeToProcess} businesses)`);
        
        const results = [];
        let processedCount = 0;
        let errorCount = 0;
        let totalReviewsExtracted = 0;
        
        // Update global progress
        resetGlobalProgress();
        showGlobalProgress();
        updateGlobalProgress(0, rangeToProcess, 'Starting Multiple Listings reviews extraction...');
        
        // Initialize counters in UI
        updateExtractionStats({
            data: {
                businessCount: 0,
                totalReviews: 0
            }
        }, 'reviews');
        
        // Process each business in the specified range
        for (let i = startIndex; i <= endIndex; i++) {
            const currentBusinessNumber = i + 1;
            const businessListing = businessListings[i];
            
            console.log(`🔄 Processing business ${currentBusinessNumber}/${businessListings.length}...`);
            updateGlobalProgress(processedCount, rangeToProcess, `Processing business ${currentBusinessNumber} of ${businessRange.end}...`);
            
            try {
                // Get business name from the listing for reference
                const businessNameElement = businessListing.querySelector('.qBF1Pd, .fontHeadlineSmall');
                const businessName = businessNameElement ? businessNameElement.textContent.trim() : `Business ${currentBusinessNumber}`;
                
                console.log(`📍 Extracting reviews for: ${businessName}`);
                console.log(`🎯 REVIEW EXTRACTION - Business listing element details:`, {
                    tagName: businessListing.tagName,
                    className: businessListing.className,
                    href: businessListing.href
                });
                
                // Get business URL for reference but don't use for navigation
                const businessUrl = businessListing.href;
                console.log(`🔗 REVIEW EXTRACTION - Business URL: ${businessUrl}`);
                
                // Use natural click simulation instead of direct navigation
                simulateNaturalClick(businessListing);
                console.log(`✅ REVIEW EXTRACTION - Simulated natural click on business listing`);

                // Wait for the individual business page to load
                console.log(`⏳ REVIEW EXTRACTION - Waiting for business page to load...`);
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Wait for the individual business page to load completely
                console.log(`🔄 REVIEW EXTRACTION - Starting page load detection...`);
                let attempts = 0;
                const maxAttempts = 10;
                while (attempts < maxAttempts) {
                    console.log(`🔍 REVIEW EXTRACTION - Load attempt ${attempts + 1}/${maxAttempts}`);
                    console.log(`🌐 REVIEW EXTRACTION - Current URL: ${window.location.href}`);
                    
                    // Check for various indicators that the page has loaded
                    const isBizPage = window.location.href.includes('google.com/maps/place/');
                    const reviewsTab = document.querySelector('button[aria-label*="Reviews"]');
                    const reviewsTabByDataIndex = document.querySelector('[data-tab-index="1"]');
                    
                    console.log(`📊 REVIEW EXTRACTION - Page load indicators:`, {
                        isBizPage: isBizPage,
                        reviewsTabFound: !!reviewsTab,
                        reviewsTabByDataIndexFound: !!reviewsTabByDataIndex
                    });
                    
                    // Check if we have navigated to a business-specific page
                    if (isBizPage && (reviewsTabByDataIndex || reviewsTab)) {
                        console.log(`✅ REVIEW EXTRACTION - Business page loaded successfully!`);
                        break;
                    }
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    attempts++;
                }
                
                if (attempts >= maxAttempts) {
                    console.warn(`⚠️ Timeout waiting for business page to load for ${businessName}`);
                    throw new Error('Timeout waiting for business page to load');
                }
                
                // Calculate review limit for this business
                let businessMaxReviews = maxReviews;
                if (maxReviews !== null) {
                    console.log(`🔢 Business ${businessName}: Extracting up to ${businessMaxReviews} reviews`);
                } else {
                    console.log(`🔢 Business ${businessName}: Extracting all available reviews (no limit)`);
                }
                
                // Create SimplifiedReviewScraper instance
                const reviewScraper = new SimplifiedReviewScraper();
                console.log(`🚀 Starting review extraction for ${businessName} with limit: ${businessMaxReviews}`);
                
                // Extract reviews using SimplifiedReviewScraper with timeout protection
                let reviewResult;
                try {
                    // Set a timeout for review extraction (5 minutes max per business)
                    const timeoutPromise = new Promise((_, reject) => {
                        setTimeout(() => reject(new Error('Review extraction timeout (5 minutes)')), 5 * 60 * 1000);
                    });
                    
                    const extractionPromise = reviewScraper.executeExtraction(businessMaxReviews);
                    reviewResult = await Promise.race([extractionPromise, timeoutPromise]);
                    
                    console.log(`📊 Review extraction completed for ${businessName}:`, {
                        success: reviewResult?.success,
                        reviewCount: reviewResult?.reviewCount,
                        totalExpected: reviewResult?.totalExpected
                    });
                    
                } catch (extractionError) {
                    console.error(`❌ Review extraction failed for ${businessName}:`, extractionError);
                    throw new Error(`Review extraction failed: ${extractionError.message}`);
                }
                
                if (reviewResult && reviewResult.success && reviewResult.reviewCount > 0) {
                    console.log(`✅ Successfully extracted ${reviewResult.reviewCount} reviews for ${businessName}`);
                    
                    totalReviewsExtracted += reviewResult.reviewCount;
                    
                    results.push({
                        businessNumber: currentBusinessNumber,
                        businessName: businessName,
                        reviewCount: reviewResult.reviewCount,
                        reviews: reviewResult.reviews,
                        totalExpected: reviewResult.totalExpected || 0,
                        rangeApplied: reviewResult.rangeApplied || false,
                        maxReviews: reviewResult.maxReviews || null,
                        success: true,
                        timestamp: new Date().toISOString()
                    });
                } else {
                    console.log(`ℹ️ No reviews found for ${businessName}`);
                    results.push({
                        businessNumber: currentBusinessNumber,
                        businessName: businessName,
                        reviewCount: 0,
                        reviews: [],
                        totalExpected: 0,
                        rangeApplied: false,
                        maxReviews: null,
                        success: true,
                        message: 'No reviews found',
                        timestamp: new Date().toISOString()
                    });
                }
                
                // Navigate back to the search results page
                console.log(`🔙 Navigating back to search results from ${businessName}...`);
                window.history.back();
                
                // Wait for the search results page to load again
                console.log(`⏳ Waiting for search results page to reload...`);
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Wait for search results to be visible again
                console.log(`🔍 Checking for search results visibility...`);
                attempts = 0;
                while (attempts < 5) {
                    const currentListings = document.querySelectorAll('.hfpxzc');
                    console.log(`🔍 Attempt ${attempts + 1}: Found ${currentListings.length} business listings`);
                    if (currentListings.length > 0) {
                        console.log(`✅ Search results are visible again, ready for next business`);
                        break;
                    }
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    attempts++;
                }
                
                if (attempts >= 5) {
                    console.warn(`⚠️ Could not confirm search results visibility after navigation back`);
                }
                
            } catch (error) {
                console.error(`❌ Error extracting reviews for business ${currentBusinessNumber}:`, error);
                errorCount++;
                
                results.push({
                    businessNumber: currentBusinessNumber,
                    businessName: businessName || `Business ${currentBusinessNumber}`,
                    reviewCount: 0,
                    reviews: [],
                    totalExpected: 0,
                    rangeApplied: false,
                    maxReviews: null,
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                
                // Try to navigate back if we're stuck on a business page
                if (window.location.href.includes('google.com/maps/place/')) {
                    console.log(`🔙 Error recovery: Navigating back from failed business...`);
                    window.history.back();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
            
            processedCount++;
            updateGlobalProgress(processedCount, rangeToProcess, `Completed ${processedCount}/${rangeToProcess} businesses (${totalReviewsExtracted} reviews)`);
            
            // Update the extraction stats after each business is processed
            updateExtractionStats({
                data: {
                    businessCount: processedCount,
                    totalReviews: totalReviewsExtracted
                }
            }, 'reviews');
        }
        
        // Calculate totals
        const successfulExtractions = results.filter(r => r.success && r.reviewCount > 0);
        
        // Update final progress
        let completionMessage = `✅ Reviews extraction completed! Found ${totalReviewsExtracted} reviews from ${successfulExtractions.length}/${processedCount} businesses`;
        if (maxReviews !== null) {
            completionMessage += ` (${maxReviews} per business)`;
        }
        if (errorCount > 0) {
            completionMessage = `⚠️ Reviews extraction completed with ${errorCount} errors. Found ${totalReviewsExtracted} reviews from ${successfulExtractions.length}/${processedCount} businesses`;
        }
        
        setGlobalProgressComplete(completionMessage);
        
        // Final update to extraction stats
        updateExtractionStats({
            data: {
                businessCount: processedCount,
                totalReviews: totalReviewsExtracted
            }
        }, 'reviews');
        
        console.log(`🎉 Multiple Listings reviews extraction completed!`);
        console.log(`📊 Results: ${successfulExtractions.length}/${processedCount} businesses with reviews, ${totalReviewsExtracted} total reviews, ${errorCount} errors`);
        
        return {
            success: true,
            type: 'multiple_listings_reviews',
            range: `${businessRange.start}-${businessRange.end}`,
            businessCount: processedCount,
            successfulExtractions: successfulExtractions.length,
            totalReviews: totalReviewsExtracted,
            errorCount: errorCount,
            maxReviews: maxReviews,
            rangeApplied: maxReviews !== null,
            results: results,
            timestamp: new Date().toISOString()
        };
        
    } catch (error) {
        console.error('❌ Error during Multiple Listings reviews extraction:', error);
        setGlobalProgressError(`❌ Reviews extraction failed: ${error.message}`);
        
        return {
            success: false,
            error: error.message,
            type: 'multiple_listings_reviews',
            range: `${businessRange.start}-${businessRange.end}`,
            timestamp: new Date().toISOString()
        };
    }
}

function refreshPersistentPopup() {
}

} // End of namespace protection