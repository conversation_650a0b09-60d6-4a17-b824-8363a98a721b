// Content script for Google Search pages - Find Citations Button Injection
// Works alongside the Copy NAP button to find business citations

// Namespace check to prevent conflicts with other extensions
if (window.FindCitationsInjectorLoaded) {
  console.log('Find Citations Injector: Already loaded, skipping...');
} else {
  window.FindCitationsInjectorLoaded = true;
  
  // Additional check to prevent conflicts with other STM extensions
  if (window.location.href.includes('google.com/maps') || window.location.href.includes('google.com/search') || window.location.href.includes('google.com/localservices/prolist')) {
    console.log('Find Citations Injector: Content script loaded for Google Maps, Search, or Pro List pages');
  } else {
    console.log('Find Citations Injector: Not a Google maps, search or Pro List page, script loaded but inactive');
  }

  // Settings management
  let settings = { citationHunter: true }; // Default enabled
  
  // Load settings from storage
  const loadSettings = async () => {
    try {
      const result = await chrome.storage.local.get('gmbExtractorSettings');
      if (result.gmbExtractorSettings) {
        settings = { ...settings, ...result.gmbExtractorSettings };
      }
      console.log('Find Citations Injector: Loaded settings:', settings);
    } catch (error) {
      console.error('Find Citations Injector: Error loading settings:', error);
    }
  };
  
  // Listen for settings updates
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'updateSettings') {
      settings = { ...settings, ...message.settings };
      console.log('Find Citations Injector: Settings updated:', settings);
      
      // Re-check if we should show/hide buttons based on new settings
      if (!settings.citationHunter) {
        // Remove any existing buttons if Citation Hunter is disabled
        const existingButton = document.getElementById('gmb-search-popup-citations-btn');
        if (existingButton) {
          existingButton.remove();
          console.log('Find Citations Injector: Button removed due to settings change');
        }
      } else {
        // Re-initialize if Citation Hunter is enabled
        setTimeout(() => {
          checkAndInjectFindCitationsButtons();
        }, 500);
      }
    }
  });

  // Track if button has been injected to prevent duplicates
  let citationsButtonInjected = false;
  let lastHeadingElement = null;
  let intervalId = null;
  let isPopupOpen = false;
  let debounceTimeout = null;

  // NAP Hunter query permutations logic
  const buildCitationQueries = (name, address, phone) => {
    const queries = [];
    
    // Surround props with quotes like NAP Hunter does
    const quotedName = name.length > 0 ? '"' + name + '"' : '';
    const quotedAddress = address.length > 0 ? '"' + address + '"' : '';
    const quotedPhone = phone.length > 0 ? '"' + phone + '"' : '';

    // All 14 permutations from NAP Hunter
    // Business Name
    if (quotedName.length > 0) {
      queries.push(quotedName);
    }
    // Business Name & Phone Number
    if (quotedName.length > 0 && quotedPhone.length > 0) {
      queries.push(quotedName + ' ' + quotedPhone);
    }
    // Business Name - Phone Number
    if (quotedName.length > 0 && quotedPhone.length > 0) {
      queries.push(quotedName + ' -' + quotedPhone);
    }
    // Street Address & Phone Number
    if (quotedPhone.length > 0 && quotedAddress.length > 0) {
      queries.push(quotedAddress + ' ' + quotedPhone);
    }
    // Street Address - Phone Number
    if (quotedPhone.length > 0 && quotedAddress.length > 0) {
      queries.push(quotedAddress + ' -' + quotedPhone);
    }
    // Business Name & Street Address
    if (quotedName.length > 0 && quotedAddress.length > 0) {
      queries.push(quotedName + ' ' + quotedAddress);
    }
    // Business Name - Street Address
    if (quotedName.length > 0 && quotedAddress.length > 0) {
      queries.push(quotedName + ' -' + quotedAddress);
    }
    // Business Name & Phone Number - Street Address
    if (quotedName.length > 0 && quotedPhone.length > 0 && quotedAddress.length > 0) {
      queries.push(quotedName + ' ' + quotedPhone + ' -' + quotedAddress);
    }
    // Business Name & Street Address - Phone Number
    if (quotedName.length > 0 && quotedPhone.length > 0 && quotedAddress.length > 0) {
      queries.push(quotedName + ' ' + quotedAddress + ' -' + quotedPhone);
    }
    // Street Address - Business Name
    if (quotedName.length > 0 && quotedAddress.length > 0) {
      queries.push(quotedAddress + ' -' + quotedName);
    }
    // Phone Number - Business Name
    if (quotedPhone.length > 0 && quotedName.length > 0) {
      queries.push(quotedPhone + ' -' + quotedName);
    }
    // Phone Number - Street Address
    if (quotedPhone.length > 0 && quotedAddress.length > 0) {
      queries.push(quotedPhone + ' -' + quotedAddress);
    }
    // Phone Number - Street Address - Business Name
    if (quotedPhone.length > 0 && quotedName.length > 0 && quotedAddress.length > 0) {
      queries.push(quotedPhone + ' -' + quotedAddress + ' -' + quotedName);
    }
    // Street Address - Business Name - Phone Number
    if (quotedAddress.length > 0 && quotedName.length > 0 && quotedPhone.length > 0) {
      queries.push(quotedAddress + ' -' + quotedName + ' -' + quotedPhone);
    }

    return queries;
  };

  // Initialize Find Citations button injection for all supported pages
  function initializeFindCitationsInjection() {
    console.log('Find Citations Injector: Initializing Find Citations button injection for Maps, Search and Pro List pages...');
    
    // Run on Google Maps, Google search pages and Pro List pages - more flexible URL matching
    if (!window.location.href.includes('google.com/maps') && !window.location.href.includes('google.com/search') && !window.location.href.includes('google.com/localservices/prolist')) {
      console.log('Find Citations Injector: Not a Google maps, search or Pro List page, skipping...');
      return;
    }
    
    // Load settings first, then check and inject buttons
    loadSettings().then(() => {
      // Check and inject Find Citations buttons immediately
      checkAndInjectFindCitationsButtons();
      
      // Set up observer to watch for dynamic content changes
      setupFindCitationsButtonObserver();
    });
  }

  function checkAndInjectFindCitationsButtons() {
    try {
      // Check if Citation Hunter is enabled in settings
      if (!settings.citationHunter) {
        console.log('Find Citations Injector: Citation Hunter disabled in settings, skipping button injection');
        return;
      }
      
      // Look for business headings in Google Maps, Google Search and Pro List (same logic as Copy NAP)
      let businessHeading = null;
      
      // Google Maps business heading - primary selector
      businessHeading = document.querySelector('h1[data-attrid="title"]');
      
      // If no Google Maps heading found, try Google Search popup window heading
      if (!businessHeading) {
        businessHeading = document.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-dtype="d3ifr"][data-attrid="title"]') ||
                         document.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-attrid="title"]') ||
                         document.querySelector('h2.qrShPb[data-attrid="title"]');
      }
      
      // If no Google Search heading found, look for open cards in different modes
      if (!businessHeading) {
        // ProList open card heading
        businessHeading = document.querySelector('.rgnuSb.tZPcob');
        
        // If no ProList open card, look for Multiple mode open card
        if (!businessHeading) {
          businessHeading = document.querySelector('.DUwDvf.lfPIob');
        }
      }
      
      if (businessHeading) {
        if (!isPopupOpen) {
          console.log('Find Citations Injector: Business popup/card detected');
          isPopupOpen = true;
        }
        
        // Check if this is the same heading we already processed
        if (lastHeadingElement === businessHeading && citationsButtonInjected) {
          return; // Silent return, no need to log repeatedly
        }
        
        // Check if Find Citations button already exists anywhere in the document
        const existingButton = document.getElementById('gmb-search-popup-citations-btn');
        if (existingButton) {
          return; // Silent return, button already exists
        }
        
        // Check if button exists in the heading's parent containers
        const headingContainer = businessHeading.closest('div');
        if (headingContainer && headingContainer.querySelector('#gmb-search-popup-citations-btn')) {
          return; // Silent return, button already exists in container
        }
        
        console.log('Find Citations Injector: Injecting Find Citations button');
        injectFindCitationsButton(businessHeading);
        
        // Track that we've injected the button for this heading
        citationsButtonInjected = true;
        lastHeadingElement = businessHeading;
        
        // Start monitoring for popup closure
        startPopupMonitoring();
        
      } else {
        // Popup is closed
        if (isPopupOpen) {
          console.log('Find Citations Injector: Business popup/card closed');
          isPopupOpen = false;
          // Reset tracking when no popup is found
          citationsButtonInjected = false;
          lastHeadingElement = null;
          // Stop monitoring
          stopPopupMonitoring();
        }
      }
      
    } catch (error) {
      console.error('Find Citations Injector: Error checking for Find Citations button injection:', error);
    }
  }

  function startPopupMonitoring() {
    // Only start monitoring if not already running
    if (!intervalId) {
      intervalId = setInterval(() => {
        // Check if popup still exists (all types) - using same flexible selectors
        let businessHeading = document.querySelector('h1[data-attrid="title"]') ||
                             document.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-dtype="d3ifr"][data-attrid="title"]') ||
                             document.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-attrid="title"]') ||
                             document.querySelector('h2.qrShPb[data-attrid="title"]');
        
        // If no Google Maps or Search heading, check for open cards in different modes
        if (!businessHeading) {
          // ProList open card heading
          businessHeading = document.querySelector('.rgnuSb.tZPcob');
          
          // If no ProList open card, look for Multiple mode open card
          if (!businessHeading) {
            businessHeading = document.querySelector('.DUwDvf.lfPIob');
          }
        }
        
        if (!businessHeading) {
          // Popup closed, reset everything
          isPopupOpen = false;
          citationsButtonInjected = false;
          lastHeadingElement = null;
          stopPopupMonitoring();
        }
      }, 2000); // Check every 2 seconds only when popup is open
    }
  }

  function stopPopupMonitoring() {
    if (intervalId) {
      clearInterval(intervalId);
      intervalId = null;
    }
  }

  function injectFindCitationsButton(headingElement) {
    try {
      // Double-check that button doesn't exist before creating
      if (document.getElementById('gmb-search-popup-citations-btn')) {
        console.log('Find Citations Injector: Button already exists, aborting injection');
        return;
      }
      
      // Create the Find Citations button
      const citationsButton = document.createElement('button');
      citationsButton.id = 'gmb-search-popup-citations-btn';
      citationsButton.textContent = 'Find Citations';
      citationsButton.style.cssText = `
        background: white;
        height: 28px;
        width: 100px;
        min-width: 100px;
        max-width: 100px;
        color: #ff8c00;
        border: 1px solid #ff8c00;
        border-radius: 3px;
        padding: 2px 6px;
        font-size: 11px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 1px 2px rgba(0,0,0,0.15);
        opacity: 0.9;
        vertical-align: middle;
        display: inline-block;
        flex-shrink: 0;
        flex-grow: 0;
        margin: 0;
        z-index: 1000;
        position: relative;
        box-sizing: border-box;
      `;

      // Add hover effects
      citationsButton.addEventListener('mouseenter', () => {
        citationsButton.style.background = '#fff5eb';
        citationsButton.style.opacity = '1';
        citationsButton.style.transform = 'translateY(-0.5px)';
        citationsButton.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
        citationsButton.style.borderColor = '#e67e00';
      });

      citationsButton.addEventListener('mouseleave', () => {
        citationsButton.style.background = 'white';
        citationsButton.style.opacity = '0.9';
        citationsButton.style.transform = 'translateY(0)';
        citationsButton.style.boxShadow = '0 1px 2px rgba(0,0,0,0.15)';
        citationsButton.style.borderColor = '#ff8c00';
      });

      // Add click handler
      citationsButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        findCitations(headingElement, citationsButton);
      });

      // Use the exact same wrapper approach as NAP injector
      const parentContainer = headingElement.parentElement;
      if (parentContainer) {
        // Check if NAP wrapper already exists (from NAP injector)
        let wrapperDiv = parentContainer.querySelector('.gmb-nap-wrapper');
        
        if (!wrapperDiv) {
          // Create a wrapper div to hold the heading and buttons (same as NAP injector)
          wrapperDiv = document.createElement('div');
          wrapperDiv.className = 'gmb-nap-wrapper';
          wrapperDiv.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 8px;
            width: 400px;
            max-width: 100%;
          `;
          
          // Insert wrapper before the heading
          parentContainer.insertBefore(wrapperDiv, headingElement);
          
          // Move heading into wrapper
          wrapperDiv.appendChild(headingElement);
          
          // Create a button container for horizontal layout
          const buttonContainer = document.createElement('div');
          buttonContainer.className = 'gmb-button-container';
          buttonContainer.style.cssText = `
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
          `;
          
          wrapperDiv.appendChild(buttonContainer);
        }
        
        // Add button to the button container (will be to the right of NAP button)
        const buttonContainer = wrapperDiv.querySelector('.gmb-button-container');
        if (buttonContainer) {
          // Insert Find Citations button before the Copy NAP button (to the left)
          const napButton = buttonContainer.querySelector('.gmb-nap-button, button[id*="nap"]');
          if (napButton) {
            buttonContainer.insertBefore(citationsButton, napButton);
          } else {
            // If no NAP button found, just add it first
            buttonContainer.insertBefore(citationsButton, buttonContainer.firstChild);
          }
        } else {
          // Fallback - add directly to wrapper
          wrapperDiv.appendChild(citationsButton);
        }
        
        console.log('Find Citations Injector: Find Citations button injected successfully');
      }
    } catch (error) {
      console.error('Find Citations Injector: Error injecting Find Citations button:', error);
    }
  }

  function findCitations(headingElement, buttonElement) {
    try {
      console.log('Find Citations Injector: Button clicked - extracting NAP data...');
      
      // Show loading state
      showCitationsFeedback(buttonElement, 'Loading...', '#ff8c00');
      
      // Add a delay to ensure popup content is fully loaded
      setTimeout(() => {
        try {
          console.log('Find Citations Injector: Extracting NAP data from popup window...');
          
          let businessName = '';
          
          // Extract business name based on the heading element type (same logic as Copy NAP)
          if (headingElement.tagName === 'H1') {
            // Google Maps business heading - extract from span inside h1
            const businessNameSpan = headingElement.querySelector('span');
            businessName = businessNameSpan ? businessNameSpan.textContent.trim() : headingElement.textContent.trim();
          } else if (headingElement.tagName === 'H2') {
            // Google Search popup window - extract from span inside h2
            const businessNameSpan = headingElement.querySelector('span');
            businessName = businessNameSpan ? businessNameSpan.textContent.trim() : '';
          } else if (headingElement.classList.contains('rgnuSb') && headingElement.classList.contains('tZPcob')) {
            // ProList open card - business name is directly in the element
            businessName = headingElement.textContent.trim();
          } else if (headingElement.classList.contains('DUwDvf') && headingElement.classList.contains('lfPIob')) {
            // Multiple mode open card - business name is directly in the element
            businessName = headingElement.textContent.trim();
          } else {
            // Fallback - try to get text content directly
            businessName = headingElement.textContent.trim();
          }
          
          console.log('Find Citations Injector: Extracted business name:', businessName);
          
          let address = '';
          let phone = '';
          
          console.log('Find Citations Injector: Searching for address and phone...');
          
          // Extract address using same logic as Copy NAP
          const addressElement = document.querySelector('span.LrzXr');
          if (addressElement) {
            address = addressElement.textContent.trim();
            console.log('Find Citations Injector: Found address with LrzXr:', address);
          } else {
            console.log('Find Citations Injector: Address element with class LrzXr not found, trying fallbacks...');
            
            const fallbackAddressSelectors = [
              '[data-attrid="kc:/collection/knowledge_panels/local_business:address"] span',
              '.rogA2c',
              'button[data-item-id="address"] .fontBodyMedium',
              '[data-attrid*="address"] span',
              '.fccl3c',
              '.rgnuSb.xYjf2e + div span',
              '.DUwDvf + div span',
              'span[style*="color"]:not([aria-label])',
              'div[data-attrid*="address"]',
              'span[data-attrid*="address"]'
            ];
            
            for (const selector of fallbackAddressSelectors) {
              const fallbackAddressElement = document.querySelector(selector);
              if (fallbackAddressElement && fallbackAddressElement.textContent.trim()) {
                const addressText = fallbackAddressElement.textContent.trim();
                if (addressText.length > 10 && (
                    addressText.includes('St ') || addressText.includes('Ave ') || addressText.includes('Rd ') || 
                    addressText.includes('Street') || addressText.includes('Avenue') || addressText.includes('Road') ||
                    addressText.includes(' NSW') || addressText.includes(' QLD') || addressText.includes(' VIC') || 
                    addressText.includes(' SA') || addressText.includes(' WA') || addressText.includes(' TAS') || 
                    addressText.includes(' NT') || addressText.includes(' ACT') ||
                    addressText.match(/\d{4}$/)
                )) {
                  address = addressText;
                  console.log(`Find Citations Injector: Found address with fallback selector ${selector}:`, address);
                  break;
                }
              }
            }
          }
          
          // Extract phone using same logic as Copy NAP
          const phoneElement = document.querySelector('span[aria-label*="Call phone"]');
          if (phoneElement) {
            phone = phoneElement.textContent.trim();
            console.log('Find Citations Injector: Found phone with aria-label:', phone);
          } else {
            console.log('Find Citations Injector: Phone element with aria-label "Call phone" not found, trying fallbacks...');
            
            const fallbackPhoneSelectors = [
              'span[aria-label*="Phone"]',
              'button[aria-label^="Phone:"]',
              '[data-attrid="kc:/collection/knowledge_panels/local_business:phone"] span',
              'button[data-item-id="phone"] .fontBodyMedium',
              '.rogA2c[data-item-id="phone"]',
              'a[href^="tel:"]',
              'span[jsaction*="phone"]',
              'div[data-attrid*="phone"] span',
              'button[data-attrid*="phone"]'
            ];
            
            for (const selector of fallbackPhoneSelectors) {
              const fallbackPhoneElement = document.querySelector(selector);
              if (fallbackPhoneElement) {
                let phoneText = '';
                
                if (selector.includes('aria-label')) {
                  const ariaLabel = fallbackPhoneElement.getAttribute('aria-label');
                  if (ariaLabel && ariaLabel.includes('Call phone')) {
                    phoneText = fallbackPhoneElement.textContent.trim();
                  } else if (ariaLabel && ariaLabel.includes('Phone:')) {
                    const phoneMatch = ariaLabel.match(/Phone:\s*(.+)/);
                    if (phoneMatch) {
                      phoneText = phoneMatch[1].trim();
                    }
                  }
                } else if (selector.includes('tel:')) {
                  const href = fallbackPhoneElement.getAttribute('href');
                  phoneText = href.replace('tel:', '').trim();
                } else {
                  phoneText = fallbackPhoneElement.textContent.trim();
                }
                
                const phonePattern = /[\(\d\)\s\-\+]{8,}/;
                if (phoneText && phonePattern.test(phoneText)) {
                  phone = phoneText;
                  console.log(`Find Citations Injector: Found phone with fallback selector ${selector}:`, phone);
                  break;
                }
              }
            }
          }
          
          // If still no phone found, try pattern matching
          if (!phone) {
            console.log('Find Citations Injector: Trying pattern matching for phone...');
            const phonePatterns = [
              /\(\d{2}\)\s*\d{4}\s*\d{4}/,
              /\d{2}\s*\d{4}\s*\d{4}/,
              /\+61\s*\d{1}\s*\d{4}\s*\d{4}/,
              /1300\s*\d{3}\s*\d{3}/,
              /1800\s*\d{3}\s*\d{3}/,
              /04\d{2}\s*\d{3}\s*\d{3}/
            ];
            
            const allSpans = document.querySelectorAll('span, div, button');
            for (const element of allSpans) {
              const text = element.textContent.trim();
              for (const pattern of phonePatterns) {
                const match = text.match(pattern);
                if (match) {
                  phone = match[0];
                  console.log('Find Citations Injector: Found phone via pattern matching:', phone);
                  break;
                }
              }
              if (phone) break;
            }
          }
          
          // Log what we found
          console.log('Find Citations Injector: Extraction results:', {
            businessName: businessName || 'NOT FOUND',
            address: address || 'NOT FOUND', 
            phone: phone || 'NOT FOUND'
          });
          
          // Generate citation search queries
          const queries = buildCitationQueries(businessName, address, phone);
          
          if (queries.length === 0) {
            console.log('Find Citations Injector: No valid queries generated');
            showCitationsFeedback(buttonElement, 'No data', '#ef4444');
            return;
          }
          
          console.log(`Find Citations Injector: Generated ${queries.length} search queries:`, queries);
          
          // Open background tabs for each query
          openCitationTabs(queries, buttonElement);

        } catch (error) {
          console.error('Find Citations Injector: Error extracting NAP data:', error);
          showCitationsFeedback(buttonElement, 'Error', '#ef4444');
        }
      }, 1000); // Wait 1 second for popup content to fully load

    } catch (error) {
      console.error('Find Citations Injector: Error in findCitations:', error);
      showCitationsFeedback(buttonElement, 'Error', '#ef4444');
    }
  }

  async function openCitationTabs(queries, buttonElement) {
    try {
      console.log(`Find Citations Injector: Requesting ${queries.length} background tabs...`);
      
      // Send message to background script to create tabs
      chrome.runtime.sendMessage({
        action: 'createCitationTabs',
        queries: queries
      }, (response) => {
        if (chrome.runtime.lastError) {
          console.error('Find Citations Injector: Error sending message:', chrome.runtime.lastError);
          showCitationsFeedback(buttonElement, 'Error', '#ef4444');
          return;
        }
        
        if (response && response.success) {
          showCitationsFeedback(buttonElement, `${response.successCount} tabs`, '#22c55e');
          console.log(`Find Citations Injector: Successfully opened ${response.successCount}/${response.totalCount} tabs`);
        } else {
          showCitationsFeedback(buttonElement, 'Failed', '#ef4444');
          console.log('Find Citations Injector: Failed to open any tabs');
        }
      });
      
    } catch (error) {
      console.error('Find Citations Injector: Error requesting citation tabs:', error);
      showCitationsFeedback(buttonElement, 'Error', '#ef4444');
    }
  }

  function showCitationsFeedback(buttonElement, message, color) {
    if (buttonElement) {
      const originalText = buttonElement.textContent;
      const originalColor = buttonElement.style.color;
      const originalBorderColor = buttonElement.style.borderColor;
      
      buttonElement.textContent = message;
      buttonElement.style.color = color;
      buttonElement.style.borderColor = color;
      
      setTimeout(() => {
        buttonElement.textContent = originalText;
        buttonElement.style.color = originalColor;
        buttonElement.style.borderColor = originalBorderColor;
      }, 2000);
    }
  }

  function setupFindCitationsButtonObserver() {
    // Watch for dynamic content changes in search results
    let currentUrl = window.location.href;
    
    const observer = new MutationObserver((mutations) => {
      let shouldCheck = false;
      
      // Check if URL changed
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        shouldCheck = true;
        // Reset tracking when URL changes
        citationsButtonInjected = false;
        lastHeadingElement = null;
        isPopupOpen = false;
        stopPopupMonitoring();
        console.log('Find Citations Injector: URL changed, resetting state');
      }
      
      // Only check for mutations if we're on a supported page
      if (!window.location.href.includes('google.com/maps') && !window.location.href.includes('google.com/search') && !window.location.href.includes('google.com/localservices/prolist')) {
        return;
      }
      
      // Check if new content was added that might contain a business popup
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Skip nodes that are clearly from other extensions
              if (node.id && (node.id.includes('STM') || node.id.includes('STM'))) {
                return;
              }
              if (node.className && typeof node.className === 'string' && 
                  (node.className.includes('STM') || node.className.includes('STM'))) {
                return;
              }
              
              // Only check for business popup elements specifically
              const hasBusinessPopup = node.querySelector && 
                (node.querySelector('h1[data-attrid="title"]') ||
                 node.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-dtype="d3ifr"][data-attrid="title"]') ||
                 node.querySelector('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-attrid="title"]') ||
                 node.querySelector('h2.qrShPb[data-attrid="title"]') ||
                 node.querySelector('.rgnuSb.tZPcob, .rgnuSb.xYjf2e') ||
                 node.matches && (node.matches('h1[data-attrid="title"]') ||
                                  node.matches('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-dtype="d3ifr"][data-attrid="title"]') ||
                                  node.matches('h2.qrShPb.pXs6bb.PZPZlf.q8U8x.aTI8gc.PPT5v.hNKfZe[data-attrid="title"]') ||
                                  node.matches('h2.qrShPb[data-attrid="title"]') ||
                                  node.matches('.rgnuSb.tZPcob, .rgnuSb.xYjf2e')));
              
              if (hasBusinessPopup) {
                shouldCheck = true;
                console.log('Find Citations Injector: Business popup/card detected in new content');
              }
            }
          });
        }
      });
      
      if (shouldCheck) {
        // Use debouncing to prevent excessive checks
        if (debounceTimeout) {
          clearTimeout(debounceTimeout);
        }
        
        debounceTimeout = setTimeout(() => {
          checkAndInjectFindCitationsButtons();
          debounceTimeout = null;
        }, 800);
      }
    });

    // Start observing with specific configuration
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributeFilter: ['data-ved', 'data-attrid', 'class']
    });
    
    console.log('Find Citations Injector: Mutation observer setup complete');
  }

  // Initialize when the script loads
  setTimeout(() => {
    initializeFindCitationsInjection();
  }, 1500);

  // Also initialize on DOM content loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        initializeFindCitationsInjection();
      }, 1000);
    });
  }

  // Initialize on window load as well
  window.addEventListener('load', () => {
    setTimeout(() => {
      initializeFindCitationsInjection();
    }, 1000);
  });
} 