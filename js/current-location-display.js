// Current Location Display on Google SERP
// This file displays the current location from the location changer above the tracked domains jump navigation

(function() {
  'use strict';
  
  let isEnabled = true; // Default to enabled
  let currentLocationData = null; // Store current location data
  
  function initializeCurrentLocationDisplay() {
    // Prevent multiple loads
    if (window.GMBCurrentLocationDisplayLoaded) {
      console.log('GMB Current Location Display: Already loaded');
      return;
    }
    window.GMBCurrentLocationDisplayLoaded = true;
    
    console.log('GMB Current Location Display: Loading on', window.location.href);

    // Load settings and location data
    if (typeof chrome !== 'undefined' && chrome.storage) {
      // Load both extension settings and location changer settings
      chrome.storage.local.get(['gmbExtractorSettings', 'settings'], function(result) {
        const gmbSettings = result.gmbExtractorSettings || {};
        const locationSettings = result.settings || {};
        
        isEnabled = gmbSettings.currentLocationDisplayEnabled !== false; // Default to true
        currentLocationData = locationSettings;
        
        console.log('GMB Current Location Display: Settings loaded, enabled:', isEnabled, 'location:', currentLocationData);
        
        if (isEnabled && currentLocationData && currentLocationData.location) {
          init();
        }
      });
    }
  }

  // Function to create current location display
  function createCurrentLocationDisplay() {
    if (!isEnabled || !currentLocationData || !currentLocationData.location) {
      console.log('GMB Current Location Display: Not enabled or no location data');
      return;
    }

    // Remove existing display
    const existingDisplay = document.getElementById('gmb-current-location-display');
    if (existingDisplay) {
      existingDisplay.remove();
    }

    // Only show if location overwrite is enabled
    if (!currentLocationData.enabled) {
      console.log('GMB Current Location Display: Location overwrite not enabled');
      return;
    }

    // Create display container
    const locationDisplay = document.createElement('div');
    locationDisplay.id = 'gmb-current-location-display';
    locationDisplay.style.cssText = `
      background: #1a1a1a !important;
      border: 1px solid #333333 !important;
      border-radius: 8px !important;
      padding: 12px 16px !important;
      margin: 16px 0 8px 0 !important;
      display: flex !important;
      align-items: center !important;
      gap: 8px !important;
      font-family: Google Sans,Roboto,Arial,sans-serif !important;
      color: #9aa0a6 !important;
      font-size: 14px !important;
    `;

    // Create location icon and text
    const locationIcon = document.createElement('span');
    locationIcon.textContent = '🌍';
    locationIcon.style.cssText = `
      font-size: 16px !important;
      flex-shrink: 0 !important;
    `;

    const locationLabel = document.createElement('span');
    locationLabel.textContent = 'Current Location:';
    locationLabel.style.cssText = `
      color: #9aa0a6 !important;
      font-weight: 500 !important;
      flex-shrink: 0 !important;
    `;

    const locationText = document.createElement('span');
    locationText.textContent = currentLocationData.location;
    locationText.style.cssText = `
      color: #ffffff !important;
      font-weight: 400 !important;
      flex: 1 !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    `;

    // Add elements to container
    locationDisplay.appendChild(locationIcon);
    locationDisplay.appendChild(locationLabel);
    locationDisplay.appendChild(locationText);

    // Find insertion point - before jump navigation or at top of results
    const jumpNavigation = document.getElementById('gmb-jump-navigation');
    const resultsSection = document.querySelector('.O4T6Pe.TPKH4e') || 
                           document.querySelector('#rso') ||
                           document.querySelector('#search > div:first-child');

    if (jumpNavigation && jumpNavigation.parentNode) {
      // Insert before jump navigation
      jumpNavigation.parentNode.insertBefore(locationDisplay, jumpNavigation);
    } else if (resultsSection && resultsSection.parentNode) {
      // Insert before the results section to place at top
      resultsSection.parentNode.insertBefore(locationDisplay, resultsSection);
    } else {
      // Fallback: insert at beginning of search container
      const searchContainer = document.querySelector('#search');
      if (searchContainer && searchContainer.firstChild) {
        searchContainer.insertBefore(locationDisplay, searchContainer.firstChild);
      }
    }

    console.log('GMB Current Location Display: Created and inserted location display');
  }

  // Function to update location display when data changes
  function updateLocationDisplay() {
    const existingDisplay = document.getElementById('gmb-current-location-display');
    if (existingDisplay) {
      // Remove existing and recreate
      existingDisplay.remove();
      setTimeout(createCurrentLocationDisplay, 100);
    } else {
      createCurrentLocationDisplay();
    }
  }

  // Initialize
  function init() {
    // Only run on Google search pages
    if (!window.location.href.includes('/search')) {
      console.log('GMB Current Location Display: Not a Google search page, skipping');
      return;
    }

    // Run when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', createCurrentLocationDisplay);
    } else {
      // DOM already ready - wait a bit for other elements to load
      setTimeout(createCurrentLocationDisplay, 1000);
    }
    
    // Also run on window load as backup
    window.addEventListener('load', () => {
      setTimeout(createCurrentLocationDisplay, 1500);
    });

    // Observer for dynamic content changes and jump navigation creation
    const observer = new MutationObserver((mutations) => {
      let shouldUpdate = false;
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1) {
              // Check if jump navigation was added
              if (node.id === 'gmb-jump-navigation' || 
                  (node.querySelector && node.querySelector('#gmb-jump-navigation'))) {
                shouldUpdate = true;
              }
              // Check if search results were updated
              if (node.querySelector && (node.querySelector('.g') || node.querySelector('[data-hveid]'))) {
                shouldUpdate = true;
              }
            }
          });
        }
      });
      
      if (shouldUpdate) {
        setTimeout(updateLocationDisplay, 200);
      }
    });

    // Start observing
    const targetNode = document.querySelector('#search, #rso') || document.body;
    observer.observe(targetNode, {
      childList: true,
      subtree: true
    });

    console.log('GMB Current Location Display: Initialized observer for dynamic content');
  }
  
  // Listen for settings updates from the settings page
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'updateSettings') {
        isEnabled = message.settings.currentLocationDisplayEnabled !== false;
        
        if (!isEnabled) {
          // Remove existing display if disabled
          const existingDisplay = document.getElementById('gmb-current-location-display');
          if (existingDisplay) {
            existingDisplay.remove();
          }
          console.log('GMB Current Location Display: Disabled via settings update - removed display');
        } else {
          // Re-enable
          console.log('GMB Current Location Display: Enabled via settings update');
          if (window.location.href.includes('/search')) {
            // Reload location data and display
            chrome.storage.local.get(['settings'], function(result) {
              currentLocationData = result.settings || {};
              if (currentLocationData && currentLocationData.location) {
                setTimeout(updateLocationDisplay, 100);
              }
            });
          }
        }
        sendResponse({received: true});
      }
    });

    // Listen for location changes from the location changer
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'sync' && changes.settings && isEnabled) {
        const newLocationData = changes.settings.newValue || {};
        currentLocationData = newLocationData;
        
        if (window.location.href.includes('/search')) {
          setTimeout(updateLocationDisplay, 100);
        }
      }
    });
  }
  
  // Export for manual testing
  window.updateCurrentLocationDisplay = updateLocationDisplay;
  
  // Initialize immediately
  initializeCurrentLocationDisplay();
})(); 