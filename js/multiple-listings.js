// Multiple listings detection and analysis for Google Maps search results
// Namespace check to prevent conflicts with other extensions
if (window.GMBMultipleListingsLoaded) {
  console.log('GMB Multiple Listings: Already loaded, skipping...');
} else {
  window.GMBMultipleListingsLoaded = true;
  console.log('GMB Multiple Listings: Analyzer loaded (passive mode - manual trigger only)');

/**
 * Detects if the current page contains multiple business listings
 * @returns {boolean} True if multiple listings are detected
 */
function detectMultipleListings() {
  console.log('GMB Multiple: Checking for multiple listings...');
  
  try {
    // Check for the aria-label pattern that indicates search results
    const resultsContainer = document.querySelector('[aria-label*="Results for"]');
    if (resultsContainer) {
      console.log('GMB Multiple: Found results container with aria-label');
      return true;
    }
    
    // Alternative check: Look for multiple business listing containers
    const businessListings = document.querySelectorAll('[data-cid]');
    if (businessListings && businessListings.length > 1) {
      console.log('GMB Multiple: Found multiple business listings via data-cid');
      return true;
    }
    
    // Check for search results feed
    const feedElement = document.querySelector('[role="feed"]');
    if (feedElement) {
      console.log('GMB Multiple: Found feed element');
      return true;
    }
    
    // Check page source for multiple listings indicators
    const pageSource = document.documentElement.outerHTML;
    
    // Look for categorical-search-results-injection pattern
    if (pageSource.includes('categorical-search-results-injection')) {
      console.log('GMB Multiple: Found categorical search results injection');
      return true;
    }
    
    // Look for multiple gcid patterns (business category identifiers)
    const gcidMatches = pageSource.match(/gcid:[a-z_]+/g);
    if (gcidMatches && gcidMatches.length > 3) {
      console.log('GMB Multiple: Found multiple gcid patterns:', gcidMatches.length);
      return true;
    }
    
    console.log('GMB Multiple: No multiple listings detected');
    return false;
    
  } catch (error) {
    console.error('GMB Multiple: Error detecting multiple listings:', error);
    return false;
  }
}

/**
 * Extracts and analyzes categories from multiple listings
 * @returns {Object} Category analysis data
 */
function analyzeCategories() {
  console.log('GMB Multiple: Starting category analysis...');
  
  try {
    const pageSource = document.documentElement.outerHTML;
    const categories = new Map();
    const businessCategories = new Map(); // Track categories per business
    
    // Step 1: Extract main categories from DOM elements (visible categories)
    const mainCategoryElements = document.querySelectorAll('[data-value]');
    const mainCategories = new Set();
    
    mainCategoryElements.forEach(element => {
      const categoryText = element.textContent?.trim();
      if (categoryText && categoryText.length > 2 && categoryText.length < 80) {
        mainCategories.add(categoryText);
        console.log('GMB Multiple: Found main category from DOM:', categoryText);
      }
    });
    
    // Step 2: Extract business listings with their main categories from page source
    const businessPattern = /\[\["([^"]+)","([^"]+)"\],[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,[^,]*,"gcid:([a-z_]+)"\]/g;
    let match;
    
    while ((match = businessPattern.exec(pageSource)) !== null) {
      const businessId = match[1]; // First ID in the business listing
      const businessName = match[2]; // Business name
      const mainCategoryId = match[3]; // The gcid category
      
      if (businessId && mainCategoryId) {
        const mainCategoryName = formatCategoryName(mainCategoryId);
        
        // Initialize business categories tracking
        if (!businessCategories.has(businessId)) {
          businessCategories.set(businessId, {
            name: businessName,
            categories: new Set(),
            mainCategory: mainCategoryName
          });
        }
        
        // Add main category
        businessCategories.get(businessId).categories.add(mainCategoryName);
        
        // Filter out "Favourites" when adding to categories count
        if (mainCategoryName.toLowerCase() !== 'favourites') {
          categories.set(mainCategoryName, (categories.get(mainCategoryName) || 0) + 1);
        }
        
        console.log(`GMB Multiple: Business ${businessName} has main category: ${mainCategoryName}`);
      }
    }
    
    // Step 3: For each business, find additional categories using the same logic as content.js
    for (const [businessId, businessData] of businessCategories.entries()) {
      const mainCategory = businessData.mainCategory;
      const additionalCategories = findAdditionalCategoriesAfterMain(pageSource, mainCategory);
      
      console.log(`GMB Multiple: Business ${businessData.name} additional categories:`, additionalCategories);
      
      // Add additional categories to this business
      additionalCategories.forEach(additionalCategory => {
        businessData.categories.add(additionalCategory);
        
        // Filter out "Favourites" when adding to categories count
        if (additionalCategory.toLowerCase() !== 'favourites') {
          categories.set(additionalCategory, (categories.get(additionalCategory) || 0) + 1);
        }
      });
    }
    
    // Calculate total unique businesses
    const totalListings = businessCategories.size;
    
    // Calculate total category instances across all businesses (main + additional)
    let totalCategoryInstances = 0;
    for (const businessData of businessCategories.values()) {
      totalCategoryInstances += businessData.categories.size;
    }
    
    // Calculate max categories per business (main + additional)
    let maxCategoriesPerListing = 0;
    for (const businessData of businessCategories.values()) {
      maxCategoriesPerListing = Math.max(maxCategoriesPerListing, businessData.categories.size);
    }
    
    // Convert to array and sort by count
    const categoryArray = Array.from(categories.entries())
      .filter(([category, count]) => category.toLowerCase() !== 'favourites') // Filter out "Favourites"
      .sort((a, b) => b[1] - a[1])
      .map(([category, count]) => ({
        category: category,
        count: count,
        percentage: totalCategoryInstances > 0 ? ((count / totalCategoryInstances) * 100).toFixed(2) : 0
      }));
    
    // Calculate statistics
    const stats = {
      totalCategories: categories.size,
      totalListings: totalListings,
      averageCategoriesPerListing: totalListings > 0 ? (totalCategoryInstances / totalListings).toFixed(2) : 0,
      maxCategoriesPerListing: maxCategoriesPerListing,
      topCategories: categoryArray.slice(0, 10)
    };
    
    console.log('GMB Multiple: Category analysis complete:', stats);
    console.log('GMB Multiple: Business breakdown:', Array.from(businessCategories.values()).map(b => ({
      name: b.name,
      categoryCount: b.categories.size,
      categories: Array.from(b.categories)
    })));
    
    return {
      success: true,
      data: stats,
      categories: categoryArray
    };
    
  } catch (error) {
    console.error('GMB Multiple: Error analyzing categories:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Find additional categories after main category using the same logic as content.js
 * @param {string} pageSource - The page source HTML
 * @param {string} mainCategory - The main category to search after
 * @returns {Array} Array of additional category names
 */
function findAdditionalCategoriesAfterMain(pageSource, mainCategory) {
  console.log('GMB Multiple: Searching for additional categories after main category:', mainCategory);
  
  // Find the main category in the page source and look for what follows
  // Try different patterns as the category might be escaped in the page source
  const patterns = [
    `\\"${mainCategory}\\"`,  // Escaped quotes: \"House Cleaning Service\"
    `"${mainCategory}"`,      // Regular quotes: "House Cleaning Service"
    mainCategory              // Just the category name
  ];
  
  let mainCategoryIndex = -1;
  let foundPattern = '';
  
  for (const pattern of patterns) {
    mainCategoryIndex = pageSource.indexOf(pattern);
    if (mainCategoryIndex !== -1) {
      foundPattern = pattern;
      console.log('GMB Multiple: Found main category with pattern:', pattern, 'at index:', mainCategoryIndex);
      break;
    }
  }
  
  if (mainCategoryIndex === -1) {
    console.log('GMB Multiple: Main category not found in page source with any pattern');
    return [];
  }
  
  // Look for the pattern after the main category
  const startIndex = mainCategoryIndex + foundPattern.length;
  const snippet = pageSource.substring(startIndex, startIndex + 500);
  
  console.log('GMB Multiple: Snippet after main category:', snippet.substring(0, 200));
  
  // Look for the separator and extract categories
  const additionalCategories = [];
  const separators = [
    ',\\"',     // comma-backslash-quote (most common)
    '\\",\\"',  // backslash-quote-comma-backslash-quote
    '","'       // regular comma in quotes
  ];
  
  let separator = '';
  let firstSeparatorIndex = -1;
  
  // Find which separator pattern exists in the snippet
  for (const sep of separators) {
    const index = snippet.indexOf(sep);
    if (index !== -1) {
      separator = sep;
      firstSeparatorIndex = index;
      console.log('GMB Multiple: Using separator pattern:', separator, 'found at index:', index);
      break;
    }
  }
  
  if (!separator) {
    console.log('GMB Multiple: No valid separator found in snippet');
    return [];
  }
  
  let currentIndex = 0;
  let categoryCount = 0;
  
  while (true) {
    categoryCount++;
    console.log(`GMB Multiple: Looking for category ${categoryCount}, currentIndex: ${currentIndex}`);
    
    const separatorIndex = snippet.indexOf(separator, currentIndex);
    console.log(`GMB Multiple: Separator found at index: ${separatorIndex}`);
    
    if (separatorIndex === -1) {
      console.log('GMB Multiple: No more separators found, stopping search');
      break;
    }
    
    // CRITICAL FIX: Check if we encounter closing bracket before the separator
    const closingBracketIndex = snippet.indexOf(']', currentIndex);
    if (closingBracketIndex !== -1 && closingBracketIndex < separatorIndex) {
      console.log(`GMB Multiple: Found closing bracket ']' at index ${closingBracketIndex} before separator at ${separatorIndex} - STOPPING as this marks end of categories array`);
      break;
    }
    
    // Find the start of the next category (after the separator)
    const categoryStart = separatorIndex + separator.length;
    console.log(`GMB Multiple: Category starts at index: ${categoryStart}`);
    
    // Find the end of the category (next quote)
    const categoryEnd = snippet.indexOf('"', categoryStart);
    console.log(`GMB Multiple: Category ends at index: ${categoryEnd}`);
    
    if (categoryEnd === -1) {
      console.log('GMB Multiple: No closing quote found, stopping search');
      break;
    }
    
    let category = snippet.substring(categoryStart, categoryEnd).trim();
    console.log(`GMB Multiple: Raw category extracted: "${category}"`);
    
    // Clean up the category - remove trailing backslashes and other escape characters
    category = category.replace(/\\+$/, '').replace(/\\"/g, '"').trim();
    
    console.log('GMB Multiple: Found potential additional category:', category);
    
    // Validate the category
    if (category.length > 2 && 
        category.length < 80 &&
        category.toLowerCase() !== 'favourites' && // Filter out "Favourites"
        !category.toLowerCase().includes('http') && 
        !category.toLowerCase().includes('google') && 
        !category.toLowerCase().includes('/maps/') &&
        !category.toLowerCase().includes('ggpht') &&
        !category.toLowerCase().includes('khms') &&
        !category.toLowerCase().match(/^[a-z]{2,3}$/) &&
        !category.toLowerCase().includes('@') &&
        !category.startsWith('/') &&
        category.match(/^[A-Za-z\s&\-'.,()]+$/)) {
      
      additionalCategories.push(category);
      console.log('GMB Multiple: Valid additional category added:', category);
    } else {
      console.log('GMB Multiple: Invalid category filtered out:', category);
    }
    
    currentIndex = categoryEnd + 1; // Move past the closing quote
    console.log(`GMB Multiple: Moving to next search position: ${currentIndex}`);
    
    // Safety check to prevent infinite loop
    if (categoryCount > 10) {
      console.log('GMB Multiple: Safety limit reached, stopping search');
      break;
    }
  }
  
  console.log('GMB Multiple: Final additional categories found:', additionalCategories);
  return additionalCategories;
}

/**
 * Formats category names for better readability
 * @param {string} category - Raw category identifier
 * @returns {string} Formatted category name
 */
function formatCategoryName(category) {
  return category
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Gets the search term from the page
 * @returns {string} The search term used
 */
function getSearchTerm() {
  try {
    // Try to get from meta tags
    const metaName = document.querySelector('meta[itemprop="name"]');
    if (metaName) {
      return metaName.getAttribute('content');
    }
    
    // Try to get from page title
    const title = document.title;
    if (title && title !== 'Google Maps') {
      return title;
    }
    
    // Try to extract from URL
    const url = window.location.href;
    const searchMatch = url.match(/search\/([^\/]+)/);
    if (searchMatch) {
      return decodeURIComponent(searchMatch[1].replace(/\+/g, ' '));
    }
    
    return 'Unknown search term';
  } catch (error) {
    console.error('GMB Multiple: Error getting search term:', error);
    return 'Unknown search term';
  }
}

/**
 * Main function to analyze multiple listings
 * @returns {Object} Complete analysis data
 */
async function analyzeMultipleListings() {
  console.log('GMB Multiple: Starting multiple listings analysis...');
  
  const isMultiple = detectMultipleListings();
  
  if (!isMultiple) {
    return {
      isMultipleListings: false,
      message: 'This page does not contain multiple business listings'
    };
  }
  
  const searchTerm = getSearchTerm();
  const categoryAnalysis = analyzeCategories();
  
  // Also run review analysis if available
  let reviewAnalysis = null;
  try {
    if (window.reviewAnalyzer) {
      console.log('GMB Multiple: Starting review analysis...');
      await window.reviewAnalyzer.startAnalysis();
      reviewAnalysis = {
        success: true,
        reviewCount: window.reviewAnalyzer.reviewData.length,
        totalListings: window.reviewAnalyzer.totalListings,
        data: window.reviewAnalyzer.reviewData,
        message: `Found review data for ${window.reviewAnalyzer.reviewData.length} businesses`
      };
      console.log('GMB Multiple: Review analysis completed:', reviewAnalysis);
    } else {
      console.log('GMB Multiple: Review analyzer not available');
    }
  } catch (error) {
    console.error('GMB Multiple: Error during review analysis:', error);
    reviewAnalysis = {
      success: false,
      error: error.message,
      reviewCount: 0
    };
  }
  
  return {
    isMultipleListings: true,
    searchTerm: searchTerm,
    timestamp: new Date().toISOString(),
    analysis: {
      categories: categoryAnalysis,
      reviews: reviewAnalysis
    }
  };
}

// Listen for messages from popup - ONLY responds to explicit user actions (button clicks)
// This extension does NOT run automatically or in the background
chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
  // Add a check to ensure this is our extension making the request
  if (!request.action) {
    return false; // Not our request, ignore
  }
  
  console.log('GMB Multiple Listings: Received manual action request:', request.action);
  
  if (request.action === "analyzeMultipleListings") {
    console.log('GMB Multiple: Received analyze request');
    try {
      const analysis = await analyzeMultipleListings();
      sendResponse(analysis);
    } catch (error) {
      console.error('GMB Multiple: Error during analysis:', error);
      sendResponse({
        isMultipleListings: false,
        error: error.message,
        message: 'Analysis failed'
      });
    }
  } else if (request.action === "detectMultipleListings") {
    console.log('GMB Multiple: Received detection request');
    const isMultiple = detectMultipleListings();
    sendResponse({ isMultipleListings: isMultiple });
  }
  return true; // Keep message channel open for async response
});

// Auto-detect on page load - DISABLED to prevent conflicts with other extensions
// document.addEventListener('DOMContentLoaded', () => {
//   setTimeout(() => {
//     const isMultiple = detectMultipleListings();
//     console.log('GMB Multiple: Auto-detection result:', isMultiple);
//   }, 1000);
// });

// Export functions for use in other scripts
window.GMBMultipleListings = {
  detectMultipleListings,
  analyzeCategories,
  analyzeMultipleListings,
  getSearchTerm
};

} // End of namespace protection 