/**
 * Centralized Badge Manager
 * Coordinates badge updates between Pomodoro Timer and Quick Timer
 * Priority: Quick Timer (alerts) > Pomodoro Timer
 */

class CentralizedBadgeManager {
    constructor() {
        this.isSupported = this.checkBadgeSupport();
        this.currentSource = null; // 'quick-timer' | 'pomodoro' | null
        this.currentText = '';
        this.currentColor = '#7C3AED';
        
        // Timer states
        this.quickTimerActive = false;
        this.pomodoroActive = false;
        
        // Badge data from each source
        this.badgeData = {
            'quick-timer': { text: '', color: '#f97316', active: false },
            'pomodoro': { text: '', color: '#7C3AED', active: false }
        };
        
        console.log('CentralizedBadgeManager: Initialized');
    }

    /**
     * Check if badge API is supported
     */
    checkBadgeSupport() {
        return chrome.action && 
               typeof chrome.action.setBadgeText === 'function' &&
               typeof chrome.action.setBadgeBackgroundColor === 'function';
    }

    /**
     * Update badge from specific source with priority handling
     */
    updateBadge(source, text, color = null) {
        if (!this.isSupported) return;

        // Only log for non-timer sources to reduce console spam
        if (source !== 'quick-timer') {
            console.log(`CentralizedBadgeManager: Update request from ${source}:`, { text, color });
        }

        // Update source data
        this.badgeData[source] = {
            text: text || '',
            color: color || this.badgeData[source].color,
            active: !!(text && text.trim())
        };

        // Update active flags
        this.quickTimerActive = this.badgeData['quick-timer'].active;
        this.pomodoroActive = this.badgeData['pomodoro'].active;

        // Only log source states for non-timer updates to reduce console spam
        if (source !== 'quick-timer') {
            console.log('CentralizedBadgeManager: Source states:', {
                quickTimer: this.quickTimerActive,
                pomodoro: this.pomodoroActive
            });
        }

        // Determine which source should display
        this.updateDisplayBasedOnPriority();
    }

    /**
     * Clear badge from specific source
     */
    clearBadge(source) {
        console.log(`CentralizedBadgeManager: Clear request from ${source}`);
        this.updateBadge(source, '', null);
    }

    /**
     * Update display based on priority rules
     * Priority: Quick Timer > Pomodoro Timer > Clear
     */
    updateDisplayBasedOnPriority() {
        let newSource = null;
        let newText = '';
        let newColor = '#7C3AED';

        // Priority logic: Quick Timer takes precedence
        if (this.quickTimerActive) {
            newSource = 'quick-timer';
            newText = this.badgeData['quick-timer'].text;
            newColor = this.badgeData['quick-timer'].color;
        } else if (this.pomodoroActive) {
            newSource = 'pomodoro';
            newText = this.badgeData['pomodoro'].text;
            newColor = this.badgeData['pomodoro'].color;
        }

        // Only log priority decisions for non-timer updates to reduce console spam
        if (newSource !== 'quick-timer') {
            console.log('CentralizedBadgeManager: Priority decision:', {
                newSource,
                newText,
                newColor,
                previousSource: this.currentSource
            });
        }

        // Only update if something changed
        if (newSource !== this.currentSource || newText !== this.currentText || newColor !== this.currentColor) {
            this.applyBadgeUpdate(newText, newColor);
            this.currentSource = newSource;
        }
    }

    /**
     * Apply the actual badge update to Chrome extension
     */
    applyBadgeUpdate(text, color) {
        if (!this.isSupported) return;

        try {
            // Update text if changed
            if (text !== this.currentText) {
                chrome.action.setBadgeText({ text });
                this.currentText = text;
                // Only log badge text updates for non-timer changes to reduce console spam  
                if (this.currentSource !== 'quick-timer') {
                    console.log(`CentralizedBadgeManager: Applied badge text: "${text}"`);
                }
            }

            // Update color if changed
            if (color !== this.currentColor) {
                chrome.action.setBadgeBackgroundColor({ color });
                this.currentColor = color;
                console.log(`CentralizedBadgeManager: Applied badge color: ${color}`);
            }

        } catch (error) {
            console.error('CentralizedBadgeManager: Error applying badge update:', error);
        }
    }

    /**
     * Get current badge state for debugging
     */
    getCurrentState() {
        return {
            currentSource: this.currentSource,
            currentText: this.currentText,
            currentColor: this.currentColor,
            quickTimerActive: this.quickTimerActive,
            pomodoroActive: this.pomodoroActive,
            badgeData: this.badgeData
        };
    }

    /**
     * Force refresh badge display (useful for initialization)
     */
    refresh() {
        console.log('CentralizedBadgeManager: Force refresh requested');
        this.updateDisplayBasedOnPriority();
    }

    /**
     * Handle Quick Timer specific updates
     */
    updateQuickTimer(remaining) {
        if (!remaining || remaining <= 0) {
            this.clearBadge('quick-timer');
            return;
        }

        const totalSeconds = Math.ceil(remaining / 1000);
        let badgeText = '';
        
        if (totalSeconds >= 3600) {
            // Show hours
            const hours = Math.floor(totalSeconds / 3600);
            badgeText = hours + 'h';
        } else if (totalSeconds >= 60) {
            // Show minutes
            const minutes = Math.floor(totalSeconds / 60);
            badgeText = minutes + 'm';
        } else {
            // Show seconds
            badgeText = totalSeconds + 's';
        }
        
        this.updateBadge('quick-timer', badgeText, '#f97316');
    }

    /**
     * Handle Pomodoro Timer specific updates
     */
    updatePomodoro(timeRemaining, state, isPaused) {
        if (!timeRemaining || state === 'idle') {
            this.clearBadge('pomodoro');
            return;
        }

        // Format time as MM:SS
        const minutes = Math.floor(timeRemaining / 60);
        const seconds = timeRemaining % 60;
        const text = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // Determine badge color
        let color = '#7C3AED'; // Purple for work
        if (isPaused) {
            color = '#ef4444'; // Red for paused
        } else if (state === 'short_break' || state === 'long_break') {
            color = '#22c55e'; // Green for break
        }

        this.updateBadge('pomodoro', text, color);
    }
}

// Export for use in background script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CentralizedBadgeManager;
}

// Global instance for background script
if (typeof window === 'undefined' && typeof self !== 'undefined') {
    // Running in service worker context
    self.centralizedBadgeManager = new CentralizedBadgeManager();
}