/**
 * Website Blocking System for Pomodoro Timer
 * Advanced site blocking system implementation
 */

// Advanced site blocking implementation
const getBlockedSites = async () => {
    const { blockedSites: e } = await chrome.storage.local.get("blockedSites");
    return e;
};

const getAllowedUrls = async () => {
    const { allowedUrls: e } = await chrome.storage.local.get("allowedUrls");
    return e;
};

const isSiteBlocked = async (e) => {
    const s = new URL(e),
        l = s.hostname.replace(/^www\./, "");
    return ((await getAllowedUrls()) || []).some((o) => s.href === o) ? !1 : ((await getBlockedSites()) || []).some((o) => l === o);
};

const getCurrentRuleIds = async () => {
    try {
        return (await chrome.declarativeNetRequest.getDynamicRules()).map((s) => s.id);
    } catch (e) {
        return console.error("Error getting existing rules:", e), [];
    }
};

const updateBlockingRules = async () => {
    let e = 1;
    const l = ((await getAllowedUrls()) || []).map((t) => ({ id: e++, priority: 2, action: { type: "allow" }, condition: { urlFilter: t, resourceTypes: ["main_frame"] } })),
        r = ((await getBlockedSites()) || []).map((t) => ({ id: e++, priority: 1, action: { type: "block" }, condition: { urlFilter: `||${t}/`, resourceTypes: ["main_frame"] } })),
        o = [...l, ...r];
    try {
        const t = await getCurrentRuleIds();
        await chrome.declarativeNetRequest.updateDynamicRules({ removeRuleIds: t, addRules: o });
    } catch (t) {
        console.error("Error updating rules:", t);
    }
};

const removeAllBlockingRules = async () => {
    const e = await getCurrentRuleIds();
    await chrome.declarativeNetRequest.updateDynamicRules({ removeRuleIds: e });
};

class BlockingSystem {
    constructor() {
        this.isSupported = this.checkDeclarativeNetRequestSupport();
        this.isActive = false;
        this.blockedSites = [];
        this.whitelistedSites = [];
        this.activeRuleIds = [];
        this.nextRuleId = 1000; // Start from 1000 to avoid conflicts

        this.loadSettings();
    }

    /**
     * Check if declarativeNetRequest API is supported
     */
    checkDeclarativeNetRequestSupport() {
        return chrome.declarativeNetRequest && 
               typeof chrome.declarativeNetRequest.updateDynamicRules === 'function';
    }

    /**
     * Load blocking settings from storage
     */
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['blockedSites', 'allowedUrls']);

            // Use direct arrays format
            this.blockedSites = result.blockedSites || [
                'facebook.com',
                'twitter.com', 
                'instagram.com',
                'youtube.com',
                'reddit.com',
                'tiktok.com'
            ];

            this.whitelistedSites = result.allowedUrls || [
                'google.com',
                'maps.google.com',
                'github.com'
            ];

        } catch (error) {
            console.error('BlockingSystem: Error loading settings:', error);
            this.setDefaultSites();
        }
    }

    /**
     * Set default blocked and whitelisted sites
     */
    setDefaultSites() {
        this.blockedSites = [
            'facebook.com',
            'twitter.com', 
            'instagram.com',
            'youtube.com',
            'reddit.com',
            'tiktok.com'
        ];
        
        this.whitelistedSites = [
            'google.com',
            'maps.google.com',
            'github.com'
        ];
    }

    /**
     * Validate domain format
     */
    isValidDomain(domain) {
        if (!domain || typeof domain !== 'string') return false;
        
        // Basic domain validation
        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
        return domainRegex.test(domain) && domain.length <= 253;
    }

    /**
     * Generate unique rule ID
     */
    generateRuleId() {
        return this.nextRuleId++;
    }

    /**
     * Create blocking rules
     */
    createBlockingRules() {
        const rules = [];

        // Create whitelist rules (higher priority)
        this.whitelistedSites.forEach(site => {
            const ruleId = this.generateRuleId();
            rules.push({
                id: ruleId,
                priority: 2,
                action: { type: 'allow' },
                condition: {
                    urlFilter: `||${site}/*`,
                    resourceTypes: ['main_frame']
                }
            });
            this.activeRuleIds.push(ruleId);
        });

        // Create blocklist rules (lower priority)
        this.blockedSites.forEach(site => {
            const ruleId = this.generateRuleId();
            rules.push({
                id: ruleId,
                priority: 1,
                action: { type: 'redirect', redirect: { url: chrome.runtime.getURL('blocked.html') } },
                condition: {
                    urlFilter: `||${site}/`,
                    resourceTypes: ['main_frame']
                }
            });
            this.activeRuleIds.push(ruleId);
        });

        return rules;
    }

    /**
     * Start blocking websites
     */
    async startBlocking() {
        if (!this.isSupported) {
            console.warn('BlockingSystem: declarativeNetRequest not supported');
            return false;
        }

        if (this.isActive) {
            console.log('BlockingSystem: Already active');
            return true;
        }

        try {
            // Use blocking rules function
            await updateBlockingRules();
            
            this.isActive = true;
            console.log('BlockingSystem: Started blocking');
            return true;

        } catch (error) {
            console.error('BlockingSystem: Error starting blocking:', error);
            return false;
        }
    }

    /**
     * Stop blocking websites
     */
    async stopBlocking() {
        if (!this.isSupported) {
            return true;
        }

        try {
            // Use remove rules function
            await removeAllBlockingRules();
            
            this.isActive = false;
            console.log('BlockingSystem: Stopped blocking');
            return true;

        } catch (error) {
            console.error('BlockingSystem: Error stopping blocking:', error);
            return false;
        }
    }

    /**
     * Check if a site is blocked
     */
    async isSiteBlocked(url) {
        if (!url) return false;

        try {
            // Use site blocking check function
            return await isSiteBlocked(url);
        } catch (error) {
            console.error('BlockingSystem: Error checking site:', error);
            return false;
        }
    }

    /**
     * Get current blocking status
     */
    getStatus() {
        return {
            isActive: this.isActive,
            blockedSites: [...this.blockedSites],
            whitelistedSites: [...this.whitelistedSites],
            activeRules: this.activeRuleIds.length,
            isSupported: this.isSupported
        };
    }

    /**
     * Update settings and restart blocking if active
     */
    async updateSettings(settings) {
        const wasActive = this.isActive;
        
        if (wasActive) {
            await this.stopBlocking();
        }
        
        await this.loadSettings();
        
        if (wasActive) {
            await this.startBlocking();
        }
    }

    /**
     * Add site to blocklist
     */
    async addBlockedSite(site) {
        if (!site || !this.isValidDomain(site)) {
            return false;
        }

        const normalizedSite = site.toLowerCase().trim();
        if (this.blockedSites.includes(normalizedSite)) {
            return false; // Already blocked
        }

        this.blockedSites.push(normalizedSite);
        await this.saveSettings();
        
        // Restart blocking if active
        if (this.isActive) {
            await this.startBlocking();
        }
        
        return true;
    }

    /**
     * Remove site from blocklist
     */
    async removeBlockedSite(site) {
        if (!site) return false;

        const normalizedSite = site.toLowerCase().trim();
        const index = this.blockedSites.indexOf(normalizedSite);
        
        if (index === -1) {
            return false; // Not in blocklist
        }

        this.blockedSites.splice(index, 1);
        await this.saveSettings();
        
        // Restart blocking if active
        if (this.isActive) {
            await this.startBlocking();
        }
        
        return true;
    }

    /**
     * Add site to whitelist
     */
    async addWhitelistedSite(site) {
        if (!site || !this.isValidDomain(site)) {
            return false;
        }

        const normalizedSite = site.toLowerCase().trim();
        if (this.whitelistedSites.includes(normalizedSite)) {
            return false; // Already whitelisted
        }

        this.whitelistedSites.push(normalizedSite);
        await this.saveSettings();
        
        // Restart blocking if active
        if (this.isActive) {
            await this.startBlocking();
        }
        
        return true;
    }

    /**
     * Remove site from whitelist
     */
    async removeWhitelistedSite(site) {
        if (!site) return false;

        const normalizedSite = site.toLowerCase().trim();
        const index = this.whitelistedSites.indexOf(normalizedSite);
        
        if (index === -1) {
            return false; // Not in whitelist
        }

        this.whitelistedSites.splice(index, 1);
        await this.saveSettings();
        
        // Restart blocking if active
        if (this.isActive) {
            await this.startBlocking();
        }
        
        return true;
    }

    /**
     * Save settings to storage
     */
    async saveSettings() {
        try {
            await chrome.storage.local.set({
                blockedSites: this.blockedSites,
                allowedUrls: this.whitelistedSites
            });
        } catch (error) {
            console.error('BlockingSystem: Error saving settings:', error);
        }
    }

    /**
     * Clean up resources
     */
    async cleanup() {
        await this.stopBlocking();
    }
}

// Create blocked page content
const createBlockedPageContent = (timeRemaining) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Site Blocked - Pomodoro Timer</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #d1d5db;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .container {
            text-align: center;
            max-width: 500px;
            padding: 40px;
            background: rgba(124, 58, 237, 0.1);
            border: 2px solid #7C3AED;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }
        .icon {
            width: 64px;
            height: 64px;
            background: #7C3AED;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 24px;
        }
        h1 {
            color: #7C3AED;
            font-size: 28px;
            margin-bottom: 16px;
            font-weight: 700;
        }
        .message {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 24px;
            opacity: 0.9;
        }
        .timer {
            font-size: 24px;
            font-weight: 600;
            color: #7C3AED;
            margin-bottom: 16px;
        }
        .subtitle {
            font-size: 14px;
            opacity: 0.7;
            font-style: italic;
        }
        .tomato {
            color: #ff6b6b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            <span class="tomato">🍅</span>
        </div>
        <h1>Pomodoro Timer Active</h1>
        <div class="message">
            This site is blocked during your focus session.
        </div>
        <div class="timer" id="timer">
            Time remaining: ${timeRemaining}
        </div>
        <div class="subtitle">
            Stay focused on your SEO work!
        </div>
    </div>
    
    <script>
        // Auto-close when timer ends or is paused
        const checkTimer = setInterval(() => {
            chrome.runtime.sendMessage({action: 'getPomodoroState'}, (response) => {
                if (!response || response.state === 'idle' || response.isPaused) {
                    console.log('🍅 Blocked page: Timer is idle or paused, closing blocked page');
                    window.close();
                }
            });
        }, 2000); // Check more frequently for better responsiveness
    </script>
</body>
</html>
`;

// Export blocking functions and BlockingSystem for service worker
export { 
    getAllowedUrls as a, 
    updateBlockingRules as b, 
    getBlockedSites as g, 
    isSiteBlocked as i, 
    removeAllBlockingRules as u,
    BlockingSystem,
    createBlockedPageContent,
    updateBlockingRules,
    removeAllBlockingRules,
    getBlockedSites,
    getAllowedUrls
};

// Export for use in background script (fallback)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { BlockingSystem, createBlockedPageContent, updateBlockingRules, removeAllBlockingRules, getBlockedSites, getAllowedUrls };
}