/**
 * Sound Preview System - Advanced Audio Pattern (Clean & Simple)
 * Direct communication to offscreen document
 */

console.log('Sound Preview Loading...');

// Advanced audio sound URLs
const SOUND_URLS = {
    // Notification sounds
    'Bell Meditation': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Bell+Meditation.mp3',
    'Celestial Gong': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Celestial+Gong.mp3',
    'Deep Meditation Bell Crown Chakra': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Deep+Meditation+Bell+Crown+Chakra.mp3',
    'Deep Meditation Bell Heart Chakra': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Deep+Meditation+Bell+Heart+Chakra.mp3',
    'Funky': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Funky.mp3',
    'Mechanical': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Mechanical.wav',
    'Notification 1': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Notification+1.mp3',
    'Notification 2': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Notification+2.mp3',
    'Notification 3': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Notification+3.mp3',
    'Notification 4': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Notification+4.mp3',
    'Old Church Bell 2': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Old+Church+Bell+2.mp3',
    'Old Church Bell 3': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/Old+Church+Bell+3.mp3',
    'Clock': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/clock.mp3',
    'Clock 2': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/clock2.mp3',
    'Clock 3': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/clock3.mp3',
    'Butterfly': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/butterfly.mp3',
    'GTA': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/gta.mp3',
    'iPhone': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/iphone.mp3',
    'Radar': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/radar.mp3',
    'Tada': 'https://lstk-music.s3.ap-southeast-2.amazonaws.com/tada.mp3',
    
    // Ticking sounds (local files)
    'Clock Ticking 1': chrome.runtime.getURL('sounds/clock-ticking-1.wav'),
    'Clock Ticking 2': chrome.runtime.getURL('sounds/clock-ticking-2.mp3')
};

// Direct offscreen communication
const playAudioSound = (soundName, volume = 0.7) => {
    const soundUrl = SOUND_URLS[soundName];
    if (!soundUrl) {
        console.error('Sound not found:', soundName);
        return;
    }
    
    console.log('Playing sound:', soundName);
    
    // Audio message format - direct to offscreen
    chrome.runtime.sendMessage({
        action: "playSound",
        selectedSound: soundUrl,
        soundVolume: volume,
        isSoundEnabled: true
    }, (response) => {
        if (chrome.runtime.lastError) {
            console.warn('Error playing sound:', chrome.runtime.lastError.message);
        }
    });
};

// Special ticking preview - stops current chronometer, plays 3 ticks, resumes if needed
let tickingPreviewInterval = null;
const playTickingPreview = (soundName, volume = 0.3) => {
    const soundUrl = SOUND_URLS[soundName];
    if (!soundUrl) {
        console.error('Ticking sound not found:', soundName);
        return;
    }
    
    console.log('Starting 3-tick preview for:', soundName);
    
    // Clear any existing preview
    if (tickingPreviewInterval) {
        clearInterval(tickingPreviewInterval);
        tickingPreviewInterval = null;
    }
    
    // Ask background to pause current chronometer
    chrome.runtime.sendMessage({ action: 'pauseChronometer' }, (response) => {
        if (chrome.runtime.lastError) {
            console.warn('Error pausing chronometer for preview:', chrome.runtime.lastError.message);
        }
    });
    
    let tickCount = 0;
    
    // Play first tick immediately
    chrome.runtime.sendMessage({
        action: "playSound",
        selectedSound: soundUrl,
        soundVolume: volume,
        isSoundEnabled: true
    });
    tickCount++;
    
    // Play 2 more ticks with intervals
    tickingPreviewInterval = setInterval(() => {
        if (tickCount < 3) {
            chrome.runtime.sendMessage({
                action: "playSound",
                selectedSound: soundUrl,
                soundVolume: volume,
                isSoundEnabled: true
            });
            tickCount++;
        } else {
            // Done with preview - clear interval and resume chronometer
            clearInterval(tickingPreviewInterval);
            tickingPreviewInterval = null;
            
            // Ask background to resume chronometer if it was running
            chrome.runtime.sendMessage({ action: 'resumeChronometer' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.warn('Error resuming chronometer after preview:', chrome.runtime.lastError.message);
                }
            });
            
            console.log('3-tick preview completed');
        }
    }, 800); // Slightly faster than normal ticking for preview
};

// Debounce utility for volume sliders
let volumeDebounceTimeout = null;
const debounceVolumeStorage = (callback, delay = 300) => {
    if (volumeDebounceTimeout) {
        clearTimeout(volumeDebounceTimeout);
    }
    volumeDebounceTimeout = setTimeout(callback, delay);
};

// Audio dropdown listeners
document.addEventListener('DOMContentLoaded', () => {
    console.log('Setting up dropdown listeners...');
    
    // Work completed sound
    const workSound = document.getElementById('pomodoroWorkCompletedSound');
    if (workSound) {
        workSound.addEventListener('change', () => {
            chrome.storage.local.get(['pomodoroAudioSettings'], (result) => {
                const audioSettings = result.pomodoroAudioSettings || {};
                const volume = (audioSettings.notificationVolume || 70) / 100;
                
                // Play sound preview
                playAudioSound(workSound.value, volume);
                
                // Save setting to local storage
                chrome.storage.local.set({
                    pomodoroAudioSettings: {
                        ...audioSettings,
                        workCompletedSound: workSound.value
                    }
                });
            });
        });
    }
    
    // End break sound
    const breakSound = document.getElementById('pomodoroEndBreakSound');
    if (breakSound) {
        breakSound.addEventListener('change', () => {
            chrome.storage.local.get(['pomodoroAudioSettings'], (result) => {
                const audioSettings = result.pomodoroAudioSettings || {};
                const volume = (audioSettings.notificationVolume || 70) / 100;
                
                // Play sound preview
                playAudioSound(breakSound.value, volume);
                
                // Save setting to local storage
                chrome.storage.local.set({
                    pomodoroAudioSettings: {
                        ...audioSettings,
                        endBreakSound: breakSound.value
                    }
                });
            });
        });
    }
    
    // Ticking sound with 3-tick preview
    const tickingSound = document.getElementById('pomodoroTickingSound');
    if (tickingSound) {
        tickingSound.addEventListener('change', () => {
            chrome.storage.local.get(['pomodoroAudioSettings'], (result) => {
                const audioSettings = result.pomodoroAudioSettings || {};
                const volume = (audioSettings.chronometerVolume || 30) / 100;
                
                // Play 3-tick preview
                playTickingPreview(tickingSound.value, volume);
                
                // Save setting to local storage
                chrome.storage.local.set({
                    pomodoroAudioSettings: {
                        ...audioSettings,
                        tickingSound: tickingSound.value
                    }
                });
            });
        });
    }
    
    // Real-time volume control for notifications
    const notificationVolumeSlider = document.getElementById('pomodoroNotificationVolume');
    if (notificationVolumeSlider) {
        notificationVolumeSlider.addEventListener('input', () => {
            const volume = notificationVolumeSlider.value / 100;
            const volumePercent = parseInt(notificationVolumeSlider.value);
            
            // Update volume immediately
            chrome.runtime.sendMessage({
                action: "adjust-notification-volume",
                notificationVolume: volume
            }, (response) => {
                if (chrome.runtime.lastError) {
                    console.warn('Error adjusting notification volume:', chrome.runtime.lastError.message);
                }
            });
            
            // Save setting to local storage - debounced
            debounceVolumeStorage(() => {
                chrome.storage.local.get(['pomodoroAudioSettings'], (result) => {
                    const audioSettings = result.pomodoroAudioSettings || {};
                    chrome.storage.local.set({
                        pomodoroAudioSettings: {
                            ...audioSettings,
                            notificationVolume: volumePercent
                        }
                    });
                });
            });
        });
    }
    
    // Real-time volume control for chronometer - STOPS and RESTARTS with new volume
    const chronometerVolumeSlider = document.getElementById('pomodoroChronometerVolume');
    if (chronometerVolumeSlider) {
        chronometerVolumeSlider.addEventListener('input', () => {
            const volume = chronometerVolumeSlider.value / 100;
            const volumePercent = parseInt(chronometerVolumeSlider.value);
            
            // STOP current chronometer completely
            chrome.runtime.sendMessage({ action: 'pauseChronometer' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.warn('Error pausing chronometer:', chrome.runtime.lastError.message);
                }
            });
            
            // Update volume immediately
            chrome.runtime.sendMessage({
                action: "adjust-music-volume",
                musicVolume: volume
            }, (response) => {
                if (chrome.runtime.lastError) {
                    console.warn('Error adjusting volume:', chrome.runtime.lastError.message);
                }
            });
            
            // Save setting to local storage - debounced
            debounceVolumeStorage(() => {
                chrome.storage.local.get(['pomodoroAudioSettings'], (result) => {
                    const audioSettings = result.pomodoroAudioSettings || {};
                    chrome.storage.local.set({
                        pomodoroAudioSettings: {
                            ...audioSettings,
                            chronometerVolume: volumePercent
                        }
                    });
                });
            });
            
            // RESTART chronometer with new volume (after small delay)
            setTimeout(() => {
                chrome.runtime.sendMessage({ action: 'resumeChronometer' }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.warn('Error resuming chronometer:', chrome.runtime.lastError.message);
                    }
                });
            }, 100);
        });
    }
    
    // Audio checkbox settings (moved from pomodoro-popup.js to prevent conflicts)
    
    const completionNotifications = document.getElementById('pomodoroCompletionNotifications');
    if (completionNotifications) {
        completionNotifications.addEventListener('change', () => {
            chrome.storage.local.get(['pomodoroAudioSettings'], (result) => {
                const audioSettings = result.pomodoroAudioSettings || {};
                chrome.storage.local.set({
                    pomodoroAudioSettings: {
                        ...audioSettings,
                        completionNotifications: completionNotifications.checked
                    }
                });
            });
        });
    }
    
    const chronometerSound = document.getElementById('pomodoroChronometerSound');
    if (chronometerSound) {
        chronometerSound.addEventListener('change', () => {
            chrome.storage.local.get(['pomodoroAudioSettings'], (result) => {
                const audioSettings = result.pomodoroAudioSettings || {};
                chrome.storage.local.set({
                    pomodoroAudioSettings: {
                        ...audioSettings,
                        chronometerEnabled: chronometerSound.checked
                    }
                });
            });
        });
    }
    
    const chronometerOnBreak = document.getElementById('pomodoroChronometerOnBreak');
    if (chronometerOnBreak) {
        chronometerOnBreak.addEventListener('change', () => {
            chrome.storage.local.get(['pomodoroAudioSettings'], (result) => {
                const audioSettings = result.pomodoroAudioSettings || {};
                chrome.storage.local.set({
                    pomodoroAudioSettings: {
                        ...audioSettings,
                        chronometerOnBreak: chronometerOnBreak.checked
                    }
                });
            });
        });
    }
});

// Test functions (for debugging)
window.testAudioSound = (soundName = 'Clock') => {
    playAudioSound(soundName, 0.7);
};

window.testAudioSoundUrl = (url, volume = 0.7) => {
    chrome.runtime.sendMessage({
        action: "playSound",
        selectedSound: url,
        soundVolume: volume,
        isSoundEnabled: true
    });
};

console.log('Sound Preview Ready!');

// Enhanced Audio Preview System - State Management
let currentPreviewState = {
    isPlaying: false,
    currentButton: null,
    currentSound: null,
    currentType: null, // 'notification' or 'ticking'
    playingAudio: null
};

// Preview button UI management
const updatePreviewButtonUI = (button, isPlaying) => {
    if (!button) return;
    
    const svg = button.querySelector('svg path');
    if (!svg) return;
    
    if (isPlaying) {
        // Change to stop icon
        svg.setAttribute('d', 'M6 19h4V5H6v14zm8-14v14h4V5h-4z');
        button.classList.add('playing');
        button.title = 'Stop preview';
    } else {
        // Change to play icon
        svg.setAttribute('d', 'M8 5v14l11-7z');
        button.classList.remove('playing');
        button.title = button.id.includes('Ticking') ? 'Preview ticking sound (3 ticks)' : 'Preview sound';
    }
};

// Enhanced preview system - stops any current preview and starts new one
const playEnhancedPreview = (soundName, volume = 0.7, isTickingSound = false, button = null) => {
    console.log('Enhanced Preview:', soundName, 'volume:', volume, 'isTickingSound:', isTickingSound);
    
    // Stop any currently playing preview
    stopCurrentPreview();
    
    // Update state
    currentPreviewState.isPlaying = true;
    currentPreviewState.currentButton = button;
    currentPreviewState.currentSound = soundName;
    currentPreviewState.currentType = isTickingSound ? 'ticking' : 'notification';
    
    // Update button UI
    updatePreviewButtonUI(button, true);
    
    if (isTickingSound) {
        // Use existing ticking preview logic (3 ticks)
        playTickingPreview(soundName, volume);
        // Auto-stop after ticking preview completes
        setTimeout(() => {
            if (currentPreviewState.isPlaying && currentPreviewState.currentType === 'ticking') {
                stopCurrentPreview();
            }
        }, 3000); // 3 ticks + buffer time
    } else {
        // Play full-length notification sound
        playFullAudioSound(soundName, volume);
    }
};

// Enhanced full-length audio playback
const playFullAudioSound = (soundName, volume = 0.7) => {
    const soundUrl = SOUND_URLS[soundName];
    if (!soundUrl) {
        console.error('Sound not found:', soundName);
        stopCurrentPreview();
        return;
    }
    
    console.log('Playing full-length sound:', soundName);
    
    // Send message to background for full playback
    chrome.runtime.sendMessage({
        action: "playSound",
        selectedSound: soundUrl,
        soundVolume: volume,
        isSoundEnabled: true,
        isPreview: true,
        fullLength: true
    }, (response) => {
        if (chrome.runtime.lastError) {
            console.warn('Error playing full sound:', chrome.runtime.lastError.message);
            stopCurrentPreview();
        } else {
            console.log('Full sound playback started successfully');
            // Set up automatic cleanup after typical notification sound duration
            setTimeout(() => {
                if (currentPreviewState.isPlaying && currentPreviewState.currentType === 'notification') {
                    stopCurrentPreview();
                }
            }, 5000); // 5 second timeout for notification sounds
        }
    });
};

// Stop current preview and reset state
const stopCurrentPreview = () => {
    console.log('Stopping current preview...');
    
    // Update button UI
    updatePreviewButtonUI(currentPreviewState.currentButton, false);
    
    // Clear any ticking intervals
    if (tickingPreviewInterval) {
        clearInterval(tickingPreviewInterval);
        tickingPreviewInterval = null;
        
        // Resume chronometer if it was running
        chrome.runtime.sendMessage({ action: 'resumeChronometer' }, (response) => {
            if (chrome.runtime.lastError) {
                console.warn('Error resuming chronometer after preview:', chrome.runtime.lastError.message);
            }
        });
    }
    
    // Send stop message to background
    chrome.runtime.sendMessage({
        action: "stopPreview"
    }, (response) => {
        if (chrome.runtime.lastError) {
            console.warn('Error stopping preview:', chrome.runtime.lastError.message);
        }
    });
    
    // Reset state
    currentPreviewState = {
        isPlaying: false,
        currentButton: null,
        currentSound: null,
        currentType: null,
        playingAudio: null
    };
};

// Enhanced click handler for preview buttons
const handlePreviewButtonClick = (button, soundName, isTickingSound = false) => {
    console.log('Preview button clicked:', soundName, 'isTickingSound:', isTickingSound);
    
    // If currently playing the same sound, stop it
    if (currentPreviewState.isPlaying && currentPreviewState.currentButton === button) {
        stopCurrentPreview();
        return;
    }
    
    // Get current volume for preview
    let volume = 0.7;
    if (isTickingSound) {
        const chronometerVolumeSlider = document.getElementById('pomodoroChronometerVolume');
        volume = chronometerVolumeSlider ? parseInt(chronometerVolumeSlider.value) / 100 : 0.3;
    } else {
        const notificationVolumeSlider = document.getElementById('pomodoroNotificationVolume');
        volume = notificationVolumeSlider ? parseInt(notificationVolumeSlider.value) / 100 : 0.7;
    }
    
    // Start new preview
    playEnhancedPreview(soundName, volume, isTickingSound, button);
};

// Enhanced event listener setup for new preview buttons
document.addEventListener('DOMContentLoaded', () => {
    console.log('Setting up enhanced preview button listeners...');
    
    // Work sound preview button
    const workSoundPreview = document.getElementById('pomodoroWorkSoundPreview');
    const workSoundSelect = document.getElementById('pomodoroWorkCompletedSound');
    
    if (workSoundPreview && workSoundSelect) {
        workSoundPreview.addEventListener('click', () => {
            handlePreviewButtonClick(workSoundPreview, workSoundSelect.value, false);
        });
    }
    
    // Break sound preview button
    const breakSoundPreview = document.getElementById('pomodoroBreakSoundPreview');
    const breakSoundSelect = document.getElementById('pomodoroEndBreakSound');
    
    if (breakSoundPreview && breakSoundSelect) {
        breakSoundPreview.addEventListener('click', () => {
            handlePreviewButtonClick(breakSoundPreview, breakSoundSelect.value, false);
        });
    }
    
    // Ticking sound preview button
    const tickingSoundPreview = document.getElementById('pomodoroTickingSoundPreview');
    const tickingSoundSelect = document.getElementById('pomodoroTickingSound');
    
    if (tickingSoundPreview && tickingSoundSelect) {
        tickingSoundPreview.addEventListener('click', () => {
            handlePreviewButtonClick(tickingSoundPreview, tickingSoundSelect.value, true);
        });
    }
    
    // Add escape key listener to stop all previews
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && currentPreviewState.isPlaying) {
            stopCurrentPreview();
        }
    });
});

// Export functions for external use
window.soundPreviewSystem = {
    playEnhancedPreview,
    stopCurrentPreview,
    handlePreviewButtonClick,
    currentPreviewState
};

console.log('Enhanced Sound Preview System Ready!');