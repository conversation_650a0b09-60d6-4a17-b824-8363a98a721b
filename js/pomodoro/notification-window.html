<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pomodoro Timer Complete</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
            background: #0a0a0a;
            color: #d1d5db;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            overflow: hidden;
        }
        
        .notification-panel {
            background: #0a0a0a;
            border: 2px solid #7C3AED;
            border-radius: 12px;
            padding: 30px;
            width: 350px;
            box-shadow: 0 10px 30px rgba(124, 58, 237, 0.3);
            text-align: center;
            position: relative;
        }
        
        .icon-container {
            margin-bottom: 20px;
        }
        
        .icon-container img {
            width: 64px;
            height: 64px;
            border-radius: 8px;
        }
        
        .title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #ffffff;
        }
        
        .message {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 10px;
            color: #d1d5db;
        }
        
        .timestamp {
            font-size: 14px;
            color: #9ca3af;
            margin-bottom: 25px;
        }
        
        .snooze-section {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
            justify-content: center;
        }
        
        .snooze-button, .close-button {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .snooze-button {
            background: #7C3AED;
            color: white;
            flex: 1;
        }
        
        .snooze-button:hover {
            background: #6B32D3;
            transform: translateY(-1px);
        }
        
        .time-select {
            padding: 10px;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            color: #d1d5db;
            font-size: 14px;
            cursor: pointer;
        }
        
        .close-button {
            background: #2a2a2a;
            color: #d1d5db;
            width: 100%;
        }
        
        .close-button:hover {
            background: #333;
            transform: translateY(-1px);
        }
        
        /* Break period styling */
        body.break-period .notification-panel {
            border-color: #22c55e;
            box-shadow: 0 10px 30px rgba(34, 197, 94, 0.3);
        }
        
        body.break-period .snooze-button {
            background: #22c55e;
        }
        
        body.break-period .snooze-button:hover {
            background: #16a34a;
        }
        
        /* Pulse animation for icon during notification */
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .icon-container img {
            animation: pulse 2s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="notification-panel">
        <div class="icon-container">
            <img src="/images/icon128.png" alt="Pomodoro Timer">
        </div>
        <div class="title" id="notificationTitle">Timer Complete!</div>
        <div class="message" id="notificationMessage">Time's up!</div>
        <div class="timestamp" id="notificationTimestamp"></div>
        <div class="snooze-section">
            <button class="snooze-button" id="snoozeBtn">Snooze</button>
            <select class="time-select" id="snoozeTime">
                <option value="5" selected>5 min</option>
                <option value="10">10 min</option>
                <option value="15">15 min</option>
                <option value="20">20 min</option>
                <option value="25">25 min</option>
                <option value="30">30 min</option>
            </select>
        </div>
        <button class="close-button" id="closeBtn">Close</button>
    </div>
    <script src="notification-window.js"></script>
</body>
</html>