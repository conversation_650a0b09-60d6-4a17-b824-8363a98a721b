/**
 * Badge Manager for Pomodoro Timer
 * Handles extension icon badge updates
 */

class BadgeManager {
    constructor() {
        this.isSupported = this.checkBadgeSupport();
        this.currentText = '';
        this.currentColor = '#7C3AED';
    }

    /**
     * Check if badge API is supported
     */
    checkBadgeSupport() {
        return chrome.action && 
               typeof chrome.action.setBadgeText === 'function' &&
               typeof chrome.action.setBadgeBackgroundColor === 'function';
    }

    /**
     * Update badge with timer info - routes through centralized badge manager
     */
    updateBadge(timeRemaining, state, isPaused) {
        if (!this.isSupported) return;

        try {
            if (state === 'idle') {
                this.clearBadge();
                return;
            }

            // Send update through background script to centralized badge manager
            chrome.runtime.sendMessage({
                action: 'updatePomodoroBadge',
                timeRemaining: timeRemaining,
                state: state,
                isPaused: isPaused
            }).catch(error => {
                // Fallback to direct update if background not available
                console.warn('BadgeManager: Background not available, using fallback:', error);
                this.directUpdateBadge(timeRemaining, state, isPaused);
            });

            // Add pause indicator overlay if paused
            if (isPaused) {
                this.addPauseOverlay();
            } else {
                this.removePauseOverlay();
            }

        } catch (error) {
            console.error('BadgeManager: Error updating badge:', error);
        }
    }

    /**
     * Direct badge update (fallback when centralized manager not available)
     */
    directUpdateBadge(timeRemaining, state, isPaused) {
        // Format time as MM:SS
        const minutes = Math.floor(timeRemaining / 60);
        const seconds = timeRemaining % 60;
        const text = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // Determine badge color
        let color = '#7C3AED'; // Purple for work
        if (isPaused) {
            color = '#ef4444'; // Red for paused
        } else if (state === 'short_break' || state === 'long_break') {
            color = '#22c55e'; // Green for break
        }

        // Only update if changed
        if (text !== this.currentText) {
            chrome.action.setBadgeText({ text });
            this.currentText = text;
        }

        if (color !== this.currentColor) {
            chrome.action.setBadgeBackgroundColor({ color });
            this.currentColor = color;
        }
    }

    /**
     * Clear badge
     */
    clearBadge() {
        if (!this.isSupported) return;

        try {
            chrome.action.setBadgeText({ text: '' });
            this.currentText = '';
            this.removePauseOverlay();
        } catch (error) {
            console.error('BadgeManager: Error clearing badge:', error);
        }
    }

    /**
     * Add pause overlay to icon
     */
    addPauseOverlay() {
        // Note: Chrome doesn't support dynamic icon overlays
        // We'll rely on the red badge color to indicate paused state
        // This method is a placeholder for potential future enhancement
    }

    /**
     * Remove pause overlay from icon
     */
    removePauseOverlay() {
        // Placeholder for future enhancement
    }

    /**
     * Flash badge for notifications
     */
    async flashBadge(color = '#ffffff', duration = 500) {
        if (!this.isSupported) return;

        const originalColor = this.currentColor;
        
        try {
            // Flash to notification color
            await chrome.action.setBadgeBackgroundColor({ color });
            
            // Wait
            await new Promise(resolve => setTimeout(resolve, duration));
            
            // Restore original color
            await chrome.action.setBadgeBackgroundColor({ color: originalColor });
        } catch (error) {
            console.error('BadgeManager: Error flashing badge:', error);
        }
    }

    /**
     * Set badge for error state
     */
    setErrorBadge() {
        if (!this.isSupported) return;

        try {
            chrome.action.setBadgeText({ text: '!' });
            chrome.action.setBadgeBackgroundColor({ color: '#dc2626' });
            this.currentText = '!';
            this.currentColor = '#dc2626';
        } catch (error) {
            console.error('BadgeManager: Error setting error badge:', error);
        }
    }

    /**
     * Animate badge for completion
     */
    async animateCompletion() {
        if (!this.isSupported) return;

        try {
            // Quick flash animation
            for (let i = 0; i < 3; i++) {
                await this.flashBadge('#fbbf24', 200); // Yellow flash
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        } catch (error) {
            console.error('BadgeManager: Error animating completion:', error);
        }
    }
}

// Export for use in background script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BadgeManager;
}