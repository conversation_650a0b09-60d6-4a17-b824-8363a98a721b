/**
 * Pomodoro Notification Window Handler
 * Manages custom notification popups for timer completion
 */

// Parse URL parameters
const params = new URLSearchParams(window.location.search);
const sessionType = params.get('type') || 'work';
const notificationName = params.get('name') || 'pomodoro-notification';
const soundUrl = params.get('sound');
const soundVolume = parseFloat(params.get('volume') || '0.7');
const soundRepeats = parseInt(params.get('repeats') || '3');
const position = params.get('position') || 'br'; // bottom-right default

// Audio playback system
let audioElement = null;
let remainingRepeats = soundRepeats;

// Apply break period styling if needed
if (sessionType === 'short_break' || sessionType === 'long_break') {
    document.body.classList.add('break-period');
}

// Update UI elements
document.addEventListener('DOMContentLoaded', () => {
    // Set notification content based on session type
    const titles = {
        'work': 'Work Session Complete!',
        'short_break': 'Short Break Complete!',
        'long_break': 'Long Break Complete!'
    };
    
    const messages = {
        'work': 'Great job! Time for a break.',
        'short_break': 'Break is over. Ready to focus?',
        'long_break': 'Long break finished. Let\'s get back to work!'
    };
    
    document.getElementById('notificationTitle').textContent = titles[sessionType] || 'Timer Complete!';
    document.getElementById('notificationMessage').textContent = messages[sessionType] || 'Time\'s up!';
    document.getElementById('notificationTimestamp').textContent = new Date().toLocaleTimeString();
    
    // Position the window
    positionWindow();
    
    // Play notification sound
    if (soundUrl) {
        playNotificationSound();
    }
    
    // Load saved snooze preference
    chrome.storage.local.get(['pomodoroSnoozeIndex'], (result) => {
        if (result.pomodoroSnoozeIndex !== undefined) {
            document.getElementById('snoozeTime').selectedIndex = result.pomodoroSnoozeIndex;
        }
    });
});

// Position window based on preference
function positionWindow() {
    chrome.runtime.sendMessage({
        action: 'positionNotificationWindow',
        position: position,
        screen: {
            width: screen.width,
            height: screen.height
        },
        window: {
            width: window.outerWidth,
            height: window.outerHeight
        }
    });
}

// Play notification sound with repeats
function playNotificationSound() {
    if (!soundUrl) return;
    
    // Stop any existing audio
    if (audioElement) {
        audioElement.pause();
        audioElement = null;
    }
    
    audioElement = new Audio(soundUrl);
    audioElement.volume = soundVolume;
    
    audioElement.addEventListener('ended', () => {
        remainingRepeats--;
        if (remainingRepeats > 0) {
            audioElement.currentTime = 0;
            audioElement.play().catch(err => console.error('Audio replay error:', err));
        } else {
            audioElement = null;
        }
    });
    
    audioElement.play().catch(err => console.error('Audio play error:', err));
}

// Snooze button handler
document.getElementById('snoozeBtn').addEventListener('click', () => {
    const snoozeMinutes = parseInt(document.getElementById('snoozeTime').value);
    
    // Save snooze preference
    const selectedIndex = document.getElementById('snoozeTime').selectedIndex;
    chrome.storage.local.set({ pomodoroSnoozeIndex: selectedIndex });
    
    // Send snooze request to background
    chrome.runtime.sendMessage({
        action: 'pomodoroSnooze',
        minutes: snoozeMinutes,
        sessionType: sessionType
    }, () => {
        window.close();
    });
});

// Close button handler
document.getElementById('closeBtn').addEventListener('click', () => {
    window.close();
});

// ESC key to close
document.addEventListener('keyup', (e) => {
    if (e.key === 'Escape') {
        window.close();
    }
});

// Bring window to front when it loses focus
window.addEventListener('blur', () => {
    // Use Pomodoro-specific message to avoid conflicts
    setTimeout(() => {
        chrome.runtime.sendMessage({
            action: 'bringPomodoroNotificationToFront'
        });
    }, 100);
});

// Listen for messages from background
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'closePomodoroNotification' && request.name === notificationName) {
        window.close();
    }
});

// Stop audio when window closes
window.addEventListener('beforeunload', () => {
    if (audioElement) {
        audioElement.pause();
        audioElement = null;
    }
});