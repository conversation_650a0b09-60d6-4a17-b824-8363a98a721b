/**
 * TODO Manager for Pomodoro Timer
 * Handles task management and persistence
 */

class TodoManager {
    constructor() {
        this.todos = [];
        this.isEnabled = false;
        this.displayCount = 2;
        this.nextId = 1;

        this.loadSettings();
        this.loadTodos();
    }

    /**
     * Load settings from storage
     */
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};

            this.isEnabled = settings.tasksEnabled !== false;
            this.displayCount = parseInt(settings.pomodoroTodoDisplayCount) || 3;
        } catch (error) {
            console.error('TodoManager: Error loading settings:', error);
        }
    }

    /**
     * Load todos from storage
     */
    async loadTodos() {
        try {
            const result = await chrome.storage.local.get(['pomodoroTodos']);
            const stored = result.pomodoroTodos || {};
            
            this.todos = stored.todos || [];
            this.nextId = stored.nextId || 1;

            // Ensure all todos have valid IDs
            this.todos.forEach(todo => {
                if (!todo.id) {
                    todo.id = this.generateId();
                }
            });
        } catch (error) {
            console.error('TodoManager: Error loading todos:', error);
            this.todos = [];
            this.nextId = 1;
        }
    }

    /**
     * Save todos to storage
     */
    async saveTodos() {
        try {
            await chrome.storage.local.set({
                pomodoroTodos: {
                    todos: this.todos,
                    nextId: this.nextId
                }
            });
        } catch (error) {
            console.error('TodoManager: Error saving todos:', error);
        }
    }

    /**
     * Generate unique ID
     */
    generateId() {
        return this.nextId++;
    }

    /**
     * Add new todo
     */
    async addTodo(text) {
        if (!text || !text.trim()) {
            return null;
        }

        const todo = {
            id: this.generateId(),
            text: text.trim(),
            completed: false,
            createdAt: Date.now(),
            completedAt: null
        };

        this.todos.push(todo); // Add to end
        await this.saveTodos();
        
        return todo;
    }

    /**
     * Toggle todo completion
     */
    async toggleTodo(id) {
        const todo = this.todos.find(t => t.id === id);
        if (!todo) {
            return false;
        }

        todo.completed = !todo.completed;
        todo.completedAt = todo.completed ? Date.now() : null;

        await this.saveTodos();
        return true;
    }

    /**
     * Delete todo
     */
    async deleteTodo(id) {
        const index = this.todos.findIndex(t => t.id === id);
        if (index === -1) {
            return false;
        }

        this.todos.splice(index, 1);
        await this.saveTodos();
        return true;
    }

    /**
     * Update todo text
     */
    async updateTodo(id, newText) {
        const todo = this.todos.find(t => t.id === id);
        if (!todo || !newText || !newText.trim()) {
            return false;
        }

        todo.text = newText.trim();
        await this.saveTodos();
        return true;
    }

    /**
     * Get active (incomplete) todos
     */
    getActiveTodos() {
        return this.todos.filter(todo => !todo.completed);
    }

    /**
     * Get completed todos
     */
    getCompletedTodos() {
        return this.todos.filter(todo => todo.completed);
    }

    /**
     * Get todos for display (limited count)
     */
    getTodosForDisplay() {
        const activeTodos = this.getActiveTodos();
        return activeTodos.slice(0, this.displayCount);
    }

    /**
     * Get all todos
     */
    getAllTodos() {
        return [...this.todos];
    }

    /**
     * Clear completed todos
     */
    async clearCompleted() {
        this.todos = this.todos.filter(todo => !todo.completed);
        await this.saveTodos();
    }

    /**
     * Clear all todos
     */
    async clearAll() {
        this.todos = [];
        this.nextId = 1;
        await this.saveTodos();
    }

    /**
     * Get statistics
     */
    getStats() {
        const total = this.todos.length;
        const completed = this.getCompletedTodos().length;
        const active = this.getActiveTodos().length;
        
        return {
            total,
            completed,
            active,
            completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
        };
    }

    /**
     * Export todos
     */
    exportTodos() {
        return {
            todos: this.todos,
            exported: new Date().toISOString(),
            version: '1.0'
        };
    }

    /**
     * Import todos
     */
    async importTodos(data) {
        try {
            if (!data || !Array.isArray(data.todos)) {
                throw new Error('Invalid todo data format');
            }

            // Validate and clean imported todos
            const validTodos = data.todos.filter(todo => 
                todo && 
                typeof todo.text === 'string' && 
                todo.text.trim() &&
                typeof todo.completed === 'boolean'
            ).map(todo => ({
                id: this.generateId(),
                text: todo.text.trim(),
                completed: todo.completed,
                createdAt: todo.createdAt || Date.now(),
                completedAt: todo.completedAt || null
            }));

            this.todos = validTodos;
            await this.saveTodos();
            
            return validTodos.length;
        } catch (error) {
            console.error('TodoManager: Error importing todos:', error);
            throw error;
        }
    }

    /**
     * Update settings
     */
    async updateSettings(settings) {
        const oldDisplayCount = this.displayCount;
        
        await this.loadSettings();
        
        // If display count changed, trigger UI update
        if (oldDisplayCount !== this.displayCount) {
            this.notifyDisplayUpdate();
        }
    }

    /**
     * Notify about display updates (override in implementations)
     */
    notifyDisplayUpdate() {
        // This would be overridden by the popup implementation
        // to update the UI when display settings change
    }

    /**
     * Move todo up in order
     */
    async moveUp(id) {
        const index = this.todos.findIndex(t => t.id === id);
        if (index <= 0) {
            return false; // Already at top or not found
        }

        // Swap with previous item
        [this.todos[index - 1], this.todos[index]] = [this.todos[index], this.todos[index - 1]];
        
        await this.saveTodos();
        return true;
    }

    /**
     * Move todo down in order
     */
    async moveDown(id) {
        const index = this.todos.findIndex(t => t.id === id);
        if (index === -1 || index >= this.todos.length - 1) {
            return false; // Already at bottom or not found
        }

        // Swap with next item
        [this.todos[index], this.todos[index + 1]] = [this.todos[index + 1], this.todos[index]];
        
        await this.saveTodos();
        return true;
    }

    /**
     * Clean up old completed todos (optional maintenance)
     */
    async cleanupOldTodos(daysOld = 30) {
        const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
        const initialCount = this.todos.length;
        
        this.todos = this.todos.filter(todo => {
            if (!todo.completed) return true; // Keep active todos
            return (todo.completedAt || todo.createdAt) > cutoffTime;
        });

        if (this.todos.length !== initialCount) {
            await this.saveTodos();
            return initialCount - this.todos.length; // Number of todos removed
        }
        
        return 0;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TodoManager;
}