/**
 * Pomodoro Popup Integration
 * Handles Pomodoro timer UI in the main popup
 */

class PomodoroPopup {
    constructor() {
        this.todoManager = null;
        this.timerState = {
            currentState: 'idle',
            isPaused: false,
            timeRemaining: 0,
            isEnabled: false,
            currentMode: 'standard'
        };
        
        this.elements = {};
        this.isVisible = false;
        this.lastUpdateTime = null;
        this.statePollingInterval = null;
        this.storageListener = null;
        this.messageListener = null;
        this.headlessMode = false; // Set to true when no UI elements are available
        this.isSavingSettings = false; // Flag to prevent circular storage updates
        this.isLoadingSettings = false; // Flag to prevent race conditions in loadAllSettings
        this.settingsDebounceTimer = null; // Debounce timer for settings propagation
        this.backgroundReadyCache = null; // Cache background ready state
        this.lastBackgroundCheckTime = 0; // Timestamp of last background check
        this.selectedTaskId = null; // Currently selected task ID for keyboard shortcuts
        
        this.init();
    }

    /**
     * Initialize the popup integration
     */
    async init() {
        try {
            console.log('PomodoroPopup: Starting initialization...');
            
            // Initialize TodoManager if available
            if (typeof TodoManager !== 'undefined') {
                this.todoManager = new TodoManager();
                console.log('PomodoroPopup: TodoManager initialized');
            } else {
                console.warn('PomodoroPopup: TodoManager not available');
            }
            
            // Set up global error handler for uncaught promise rejections
            window.addEventListener('unhandledrejection', (event) => {
                if (event.reason && event.reason.message && event.reason.message.includes('Extension context invalidated')) {
                    // Silently handle extension context invalidation errors
                    event.preventDefault();
                    console.warn('PomodoroPopup: Extension context invalidated - preventing unhandled rejection');
                }
            });
            
            await this.initializeElements();
            
            // Check if any UI elements are available
            const hasAnyElements = this.elements.pomodoroSection || this.tasksElements.tasksSection;
            if (!hasAnyElements) {
                // No UI elements available - run in headless mode for background functionality
                this.headlessMode = true;
                console.log('PomodoroPopup: Running in headless mode (no UI elements)');
            }
            
            await this.loadSettings();
            
            // Load all settings from storage
            await this.loadAllSettings();
            
            // Get initial timer state from background
            await this.requestTimerState();
            
            await this.setupEventListeners();
            await this.updateDisplay();
            
            // Listen for timer state updates from background
            this.setupMessageListener();
            
            // Start polling for timer state
            this.startStatePolling();
            
            // Settings persistence test available via: window.pomodoroPopup.testSettingsPersistence()
            // Removed automatic test to reduce console spam
            
            console.log('PomodoroPopup: Initialization complete');
        } catch (error) {
            console.error('PomodoroPopup: Error during initialization:', error);
        }
    }

    /**
     * Initialize DOM elements for independent sections
     */
    async initializeElements() {
        // Pomodoro Timer section elements
        this.elements = {
            pomodoroSection: document.getElementById('pomodoroSection'),
            pomodoroHeader: document.getElementById('pomodoroTimerHeader'),
            pomodoroTitle: document.getElementById('pomodoroTimerTitle'),
            timeRemaining: document.getElementById('pomodoroTimeRemaining'),
            pomodoroStatusIndicator: document.getElementById('pomodoroStatusIndicator'),
            pomodoroIcon: document.getElementById('pomodoroTimerIcon'),
            pomodoroControls: document.getElementById('pomodoroControls'),
            pomodoroContent: document.getElementById('pomodoroTimerContent'),
            timeDisplay: document.getElementById('pomodoroTimeDisplay'),
            sessionInfo: document.getElementById('pomodoroSessionInfo'),
            startBtn: document.getElementById('pomodoroStartBtn'),
            pauseBtn: document.getElementById('pomodoroPauseBtn'),
            stopBtn: document.getElementById('pomodoroStopBtn'),
            resetBtn: document.getElementById('pomodoroResetBtn'),
            modeSelection: document.getElementById('pomodoroModeSelection'),
            todoIntegration: document.getElementById('pomodoroTodoIntegration'),
            activeTasks: document.getElementById('pomodoroActiveTasks'),
            
            // Settings accordions
            configHeader: document.getElementById('pomodoroConfigHeader'),
            audioHeader: document.getElementById('pomodoroAudioHeader'),
            blockingHeader: document.getElementById('pomodoroBlockingHeader'),
            
            // Timer Configuration
            workDuration: document.getElementById('pomodoroWorkDuration'),
            shortBreak: document.getElementById('pomodoroShortBreak'),
            longBreak: document.getElementById('pomodoroLongBreak'),
            numberOfCycles: document.getElementById('pomodoroNumberOfCycles'),
            resetDefaults: document.getElementById('pomodoroResetDefaults'),
            cycleDisplay: document.getElementById('pomodoroCycleDisplay'),
            
            // Audio Settings
            workCompletedSound: document.getElementById('pomodoroWorkCompletedSound'),
            endBreakSound: document.getElementById('pomodoroEndBreakSound'),
            completionNotifications: document.getElementById('pomodoroCompletionNotifications'),
            notifyPosition: document.getElementById('pomodoroNotifyPosition'),
            notificationVolume: document.getElementById('pomodoroNotificationVolume'),
            notificationVolumeValue: document.getElementById('pomodoroNotificationVolumeValue'),
            chronometerSound: document.getElementById('pomodoroChronometerSound'),
            chronometerOnBreak: document.getElementById('pomodoroChronometerOnBreak'),
            chronometerFrequency: document.getElementById('pomodoroChronometerFrequency'),
            tickingSound: document.getElementById('pomodoroTickingSound'),
            chronometerVolume: document.getElementById('pomodoroChronometerVolume'),
            chronometerVolumeValue: document.getElementById('pomodoroChronometerVolumeValue'),
            
            // Sound preview buttons
            workSoundPreview: document.getElementById('pomodoroWorkSoundPreview'),
            breakSoundPreview: document.getElementById('pomodoroBreakSoundPreview'),
            tickingSoundPreview: document.getElementById('pomodoroTickingSoundPreview'),
            
            // Website Blocking
            blockedSites: document.getElementById('pomodoroBlockedSites'),
            whitelistedSites: document.getElementById('pomodoroWhitelistedSites')
        };
        
        // Tasks section elements (independent)
        this.tasksElements = {
            tasksSection: document.getElementById('tasksSection'),
            tasksHeader: document.getElementById('tasksHeader'),
            tasksTitle: document.getElementById('tasksTitle'),
            tasksCount: document.getElementById('tasksCount'),
            tasksStatusIndicator: document.getElementById('tasksStatusIndicator'),
            tasksIcon: document.getElementById('tasksIcon'),
            tasksActiveTasks: document.getElementById('tasksActiveTasks'),
            tasksContent: document.getElementById('tasksContent'),
            tasksNewTaskInput: document.getElementById('tasksNewTaskInput'),
            tasksAddTaskBtn: document.getElementById('tasksAddTaskBtn'),
            tasksList: document.getElementById('tasksList'),
            tasksDisplayCount: document.getElementById('tasksDisplayCount'),
            tasksCompactToggle: document.getElementById('tasksCompactToggle'),
            tasksCompactIcon: document.getElementById('tasksCompactIcon')
        };
        
        // Count available elements without spamming console
        const pomodoroElementCount = Object.values(this.elements).filter(el => el).length;
        const tasksElementCount = Object.values(this.tasksElements).filter(el => el).length;
        
        // Only log if we have some elements (silent if no UI present)
        if (pomodoroElementCount > 0 || tasksElementCount > 0) {
            console.log(`PomodoroPopup: Elements initialized (${pomodoroElementCount} pomodoro, ${tasksElementCount} tasks)`);
        }
    }

    /**
     * Load settings and check if sections are enabled independently
     */
    async loadSettings() {
        try {
            // Use local storage for settings consistency with background timer
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            
            // Check independent toggles
            this.timerState.isEnabled = settings.pomodoroEnabled !== false;
            this.tasksEnabled = settings.tasksEnabled !== false;
            
            // Show/hide Pomodoro section based on enabled state
            if (this.elements.pomodoroSection) {
                this.elements.pomodoroSection.style.display = this.timerState.isEnabled ? 'block' : 'none';
            }
            
            // Show/hide Tasks section independently
            if (this.tasksElements.tasksSection) {
                this.tasksElements.tasksSection.style.display = this.tasksEnabled ? 'block' : 'none';
            }
            
            // Load TODO manager settings if Tasks are enabled
            if (this.todoManager && this.tasksEnabled) {
                await this.todoManager.loadSettings();
            }
            
        } catch (error) {
            console.error('PomodoroPopup: Error loading settings:', error);
        }
    }

    /**
     * Setup event listeners
     */
    async setupEventListeners() {
        // Header click to toggle content
        if (this.elements.header) {
            this.elements.header.addEventListener('click', () => {
                this.toggleContent();
            });
        }


        // Tasks section accordion header click
        if (this.tasksElements.tasksHeader) {
            this.tasksElements.tasksHeader.addEventListener('click', () => {
                this.toggleTasksContent();
            });
        }
        
        // Tasks section compact toggle click
        if (this.tasksElements.tasksCompactToggle) {
            this.tasksElements.tasksCompactToggle.addEventListener('click', () => {
                this.toggleTasksContent();
            });
        }

        // Pomodoro section accordion header click  
        if (this.elements.pomodoroHeader) {
            this.elements.pomodoroHeader.addEventListener('click', () => {
                this.togglePomodoroContent();
            });
        }

        // Timer control event listeners
        if (this.elements.startBtn) {
            this.elements.startBtn.addEventListener('click', () => {
                this.startTimer();
            });
        }

        if (this.elements.pauseBtn) {
            this.elements.pauseBtn.addEventListener('click', () => {
                this.pauseTimer();
            });
        }

        if (this.elements.stopBtn) {
            this.elements.stopBtn.addEventListener('click', () => {
                this.stopTimer();
            });
        }

        if (this.elements.resetBtn) {
            this.elements.resetBtn.addEventListener('click', () => {
                this.resetTimer();
            });
        }


        // Settings accordion event listeners
        if (this.elements.configHeader) {
            this.elements.configHeader.addEventListener('click', () => {
                this.toggleSettingsAccordion('config');
            });
        }

        if (this.elements.audioHeader) {
            this.elements.audioHeader.addEventListener('click', () => {
                this.toggleSettingsAccordion('audio');
            });
        }

        if (this.elements.blockingHeader) {
            this.elements.blockingHeader.addEventListener('click', () => {
                this.toggleSettingsAccordion('blocking');
            });
        }

        // Sound preview event listeners
        if (this.elements.workSoundPreview) {
            this.elements.workSoundPreview.addEventListener('click', () => {
                this.previewSound(this.elements.workCompletedSound.value, false);
            });
        }

        if (this.elements.breakSoundPreview) {
            this.elements.breakSoundPreview.addEventListener('click', () => {
                this.previewSound(this.elements.endBreakSound.value, false);
            });
        }

        if (this.elements.tickingSoundPreview) {
            this.elements.tickingSoundPreview.addEventListener('click', () => {
                this.previewSound(this.elements.tickingSound.value, true);
            });
        }

        // Volume slider real-time updates
        // Volume controls are handled by sound-preview.js with advanced audio system
        // Update display values only when settings change via storage listener

        // Settings change listeners for all inputs
        this.setupSettingsEventListeners();

        // Reset to defaults button
        if (this.elements.resetDefaults) {
            this.elements.resetDefaults.addEventListener('click', () => {
                this.resetToDefaults();
            });
        }

        // TODO management
        if (this.tasksElements.tasksAddTaskBtn) {
            this.tasksElements.tasksAddTaskBtn.addEventListener('click', () => {
                this.addTask();
            });
        }

        if (this.tasksElements.tasksNewTaskInput) {
            this.tasksElements.tasksNewTaskInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addTask();
                }
            });
        }

        // Settings change listener - local storage for consistency with background timer
        // Remove any existing listener first to prevent duplicates
        if (this.storageListener) {
            chrome.storage.onChanged.removeListener(this.storageListener);
        }
        
        this.storageListener = (changes, namespace) => {
            if (namespace === 'local') {
                // Handle settings changes
                if (changes.gmbExtractorSettings) {
                    console.log(`🔍 DEBUG: gmbExtractorSettings changed:`, {
                        oldValue: changes.gmbExtractorSettings.oldValue,
                        newValue: changes.gmbExtractorSettings.newValue
                    });
                    
                    // Skip reloading if we're currently saving settings to prevent circular updates
                    if (this.isSavingSettings) {
                        console.log(`🔍 DEBUG: Skipping loadSettings() - currently saving to prevent circular updates`);
                        return;
                    }
                    
                    console.log(`🔍 DEBUG: Calling loadSettings() and updateDisplay() from storage listener...`);
                    this.loadSettings();
                    this.updateDisplay();
                    
                    // Update TodoManager settings if it exists
                    if (this.todoManager) {
                        this.todoManager.updateSettings(changes.gmbExtractorSettings.newValue);
                        this.updateTasksDisplay();
                    }
                }
                
                // Handle timer state changes (CROSS-TAB SYNC)
                if (changes.pomodoroTimerState) {
                    // Timer state changed in storage, updating display
                    const newState = changes.pomodoroTimerState.newValue;
                    if (newState) {
                        this.timerState = {
                            ...this.timerState,
                            currentState: newState.currentState || 'idle',
                            timeRemaining: newState.timeRemaining || 0,
                            isPaused: newState.isPaused || false,
                            totalCycles: newState.totalCycles,
                            currentCycle: newState.currentCycle,
                            currentMode: newState.currentMode || 'standard'
                        };
                        this.updateDisplay();
                        // Cross-tab sync completed
                    }
                }
            }
        };
        
        chrome.storage.onChanged.addListener(this.storageListener);
        
        // Keyboard shortcuts for task reordering
        this.setupKeyboardShortcuts();
    }

    /**
     * Setup keyboard shortcuts for task reordering
     */
    setupKeyboardShortcuts() {
        // Add document-level keyboard event listener
        document.addEventListener('keydown', (e) => {
            // Only process Ctrl+Up/Down when a task is selected
            if (this.selectedTaskId && e.ctrlKey && !e.shiftKey && !e.altKey) {
                if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    this.moveSelectedTaskUp();
                } else if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    this.moveSelectedTaskDown();
                }
            }
        });

        // Clear selection when clicking outside tasks
        document.addEventListener('click', (e) => {
            const tasksSection = document.getElementById('tasksSection');
            if (tasksSection && !tasksSection.contains(e.target)) {
                this.clearTaskSelection();
            }
        });
    }

    /**
     * Select a task by ID
     */
    selectTask(taskId) {
        // Clear previous selection
        this.clearTaskSelection();
        
        // Set new selection
        this.selectedTaskId = taskId;
        
        // Update visual state
        this.updateTaskSelectionDisplay();
    }

    /**
     * Clear task selection
     */
    clearTaskSelection() {
        if (this.selectedTaskId) {
            this.selectedTaskId = null;
            this.updateTaskSelectionDisplay();
        }
    }

    /**
     * Update visual display of selected task
     */
    updateTaskSelectionDisplay() {
        // Remove selected class from all tasks
        const allTasks = document.querySelectorAll('.tasks-task-item');
        allTasks.forEach(task => {
            task.classList.remove('selected');
        });

        // Add selected class to current selected task
        if (this.selectedTaskId) {
            const selectedTask = document.querySelector(`[data-task-id="${this.selectedTaskId}"]`);
            if (selectedTask) {
                selectedTask.classList.add('selected');
            }
        }
    }

    /**
     * Move selected task up using keyboard shortcut
     */
    async moveSelectedTaskUp() {
        if (this.selectedTaskId) {
            await this.moveTaskUp(this.selectedTaskId);
            // Keep the task selected after moving
            this.updateTaskSelectionDisplay();
        }
    }

    /**
     * Move selected task down using keyboard shortcut
     */
    async moveSelectedTaskDown() {
        if (this.selectedTaskId) {
            await this.moveTaskDown(this.selectedTaskId);
            // Keep the task selected after moving
            this.updateTaskSelectionDisplay();
        }
    }

    /**
     * Setup message listener for timer state updates
     */
    setupMessageListener() {
        // Remove any existing listener first to prevent duplicates
        if (this.messageListener) {
            chrome.runtime.onMessage.removeListener(this.messageListener);
        }
        
        this.messageListener = (message, sender, sendResponse) => {
            if (message.action === 'pomodoroStateUpdate') {
                this.timerState = { ...this.timerState, ...message.state };
                this.updateDisplay();
            }
            
            // Handle chronometer state updates from other windows
            if (message.action === 'chronometerStateUpdate') {
                this.handleChronometerStateUpdate(message.state);
            }
        };
        
        chrome.runtime.onMessage.addListener(this.messageListener);
    }

    /**
     * Handle chronometer state updates from background
     */
    handleChronometerStateUpdate(chronometerState) {
        console.log('🔄 PomodoroPopup: Chronometer state update:', chronometerState);
        
        // Update UI to show if chronometer is active in another window
        if (chronometerState.isActive && chronometerState.ownerId) {
            // Another window owns the chronometer
            this.showChronometerOwnedByOther(chronometerState);
        } else {
            // Chronometer is available
            this.showChronometerAvailable();
        }
    }

    /**
     * Show UI indication that chronometer is owned by another window
     */
    showChronometerOwnedByOther(chronometerState) {
        // Add visual indicator that chronometer is active elsewhere
        if (this.elements.pomodoroSection) {
            this.elements.pomodoroSection.classList.add('chronometer-external');
            
            // Add a subtle indicator
            let indicator = this.elements.pomodoroSection.querySelector('.chronometer-external-indicator');
            if (!indicator) {
                indicator = document.createElement('div');
                indicator.className = 'chronometer-external-indicator';
                indicator.style.cssText = `
                    position: absolute;
                    top: 5px;
                    right: 5px;
                    width: 8px;
                    height: 8px;
                    background: #22c55e;
                    border-radius: 50%;
                    opacity: 0.7;
                    z-index: 1000;
                `;
                indicator.title = 'Chronometer active in another window';
                this.elements.pomodoroSection.style.position = 'relative';
                this.elements.pomodoroSection.appendChild(indicator);
            }
        }
        
        console.log('🟢 PomodoroPopup: Showing chronometer owned by another window');
    }

    /**
     * Show UI indication that chronometer is available
     */
    showChronometerAvailable() {
        // Remove external chronometer indicators
        if (this.elements.pomodoroSection) {
            this.elements.pomodoroSection.classList.remove('chronometer-external');
            
            const indicator = this.elements.pomodoroSection.querySelector('.chronometer-external-indicator');
            if (indicator) {
                indicator.remove();
            }
        }
        
        console.log('⚫ PomodoroPopup: Showing chronometer available');
    }
    
    /**
     * Start polling for timer state from storage (simplified approach)
     */
    startStatePolling() {
        // Read timer state from storage every second - simple and reliable
        this.statePollingInterval = setInterval(async () => {
            try {
                const result = await chrome.storage.local.get(['pomodoroTimerState']);
                if (result.pomodoroTimerState) {
                    const storageState = result.pomodoroTimerState;
                    
                    // Always update from storage (single source of truth)
                    this.timerState = {
                        ...this.timerState,
                        currentState: storageState.currentState || 'idle',
                        timeRemaining: storageState.timeRemaining || 0,
                        isPaused: storageState.isPaused || false,
                        totalCycles: storageState.totalCycles,
                        currentCycle: storageState.currentCycle,
                        currentMode: storageState.currentMode || 'standard'
                    };
                    
                    this.updateDisplay();
                }
            } catch (error) {
                console.error('PomodoroPopup: Error reading timer state from storage:', error);
            }
        }, 1000);
    }

    /**
     * Toggle Tasks section content visibility
     */
    toggleTasksContent() {
        if (!this.tasksElements.tasksContent) return;

        const isExpanded = this.tasksElements.tasksContent.classList.contains('expanded');
        
        if (isExpanded) {
            this.tasksElements.tasksContent.classList.remove('expanded');
            this.tasksElements.tasksContent.style.display = 'none';
            
            // Update both icons
            if (this.tasksElements.tasksIcon) {
                this.tasksElements.tasksIcon.classList.remove('expanded');
            }
            if (this.tasksElements.tasksCompactIcon) {
                this.tasksElements.tasksCompactIcon.classList.remove('expanded');
            }
        } else {
            this.tasksElements.tasksContent.classList.add('expanded');
            this.tasksElements.tasksContent.style.display = 'block';
            
            // Update both icons
            if (this.tasksElements.tasksIcon) {
                this.tasksElements.tasksIcon.classList.add('expanded');
            }
            if (this.tasksElements.tasksCompactIcon) {
                this.tasksElements.tasksCompactIcon.classList.add('expanded');
            }
            
            this.updateTodoList();
        }
    }

    /**
     * Toggle Pomodoro section content visibility
     */
    togglePomodoroContent() {
        if (!this.elements.pomodoroContent || !this.elements.pomodoroIcon) return;

        const isExpanded = this.elements.pomodoroContent.classList.contains('expanded');
        
        if (isExpanded) {
            this.elements.pomodoroContent.classList.remove('expanded');
            this.elements.pomodoroContent.style.display = 'none';
            this.elements.pomodoroIcon.classList.remove('expanded');
        } else {
            this.elements.pomodoroContent.classList.add('expanded');
            this.elements.pomodoroContent.style.display = 'block';
            this.elements.pomodoroIcon.classList.add('expanded');
        }
    }

    /**
     * Update the display based on current state
     */
    async updateDisplay() {
        // Skip UI updates in headless mode
        if (this.headlessMode) {
            return;
        }
        
        if (!this.timerState.isEnabled) {
            if (this.elements.section) {
                this.elements.section.style.display = 'none';
            }
            return;
        }

        if (this.elements.section) {
            this.elements.section.style.display = 'block';
        }

        // Update timer display
        this.updateTimerDisplay();
        
        // Update active tasks display (top section)
        await this.updateActiveTasks();
        
        // Update tasks display if tasks section is enabled
        if (this.tasksEnabled) {
            await this.updateTasksDisplay();
        }
    }

    /**
     * Update timer display
     */
    updateTimerDisplay() {
        // Skip UI updates in headless mode
        if (this.headlessMode) {
            return;
        }
        // Update header time remaining
        if (this.elements.timeRemaining) {
            if (this.timerState.currentState === 'idle') {
                this.elements.timeRemaining.textContent = '';
            } else {
                const minutes = Math.floor(this.timerState.timeRemaining / 60);
                const seconds = this.timerState.timeRemaining % 60;
                const timeText = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                // Add break type notification next to time remaining
                let displayText = `(${timeText} remaining`;
                if (this.timerState.currentState === 'short_break') {
                    displayText += ' - Short Break';
                } else if (this.timerState.currentState === 'long_break') {
                    displayText += ' - Long Break';
                }
                displayText += ')';
                
                this.elements.timeRemaining.textContent = displayText;
            }
        }

        // Update main timer display
        if (this.elements.timeDisplay) {
            if (this.timerState.currentState === 'idle') {
                // Show actual time remaining (whether 25min, 60min, or custom)
                const minutes = Math.floor(this.timerState.timeRemaining / 60);
                const seconds = this.timerState.timeRemaining % 60;
                this.elements.timeDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                const minutes = Math.floor(this.timerState.timeRemaining / 60);
                const seconds = this.timerState.timeRemaining % 60;
                this.elements.timeDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        // Update session info
        if (this.elements.sessionInfo) {
            const sessionTypes = {
                'idle': 'Ready to Focus',
                'work': 'Work Session',
                'short_break': 'Short Break',
                'long_break': 'Long Break'
            };
            this.elements.sessionInfo.textContent = sessionTypes[this.timerState.currentState] || 'Work Session';
        }

        // Update cycle display
        if (this.elements.cycleDisplay) {
            if (this.timerState.totalCycles && this.timerState.totalCycles !== 'unlimited') {
                // Show cycle display when timer is running or paused, not just when currentCycle > 0
                if (this.timerState.currentState !== 'idle') {
                    const displayCycle = this.timerState.currentCycle || 1; // Default to 1 if not set
                    this.elements.cycleDisplay.textContent = `Cycle ${displayCycle}/${this.timerState.totalCycles}`;
                    this.elements.cycleDisplay.style.display = 'inline';
                } else {
                    this.elements.cycleDisplay.style.display = 'none';
                }
            } else {
                this.elements.cycleDisplay.style.display = 'none';
            }
        }

        // Update control button visibility
        this.updateControlButtons();

        // Update status indicator and section styling for break periods
        if (this.elements.pomodoroStatusIndicator) {
            if (this.timerState.currentState !== 'idle') {
                this.elements.pomodoroStatusIndicator.style.display = 'block';
                if (this.timerState.isPaused) {
                    this.elements.pomodoroStatusIndicator.style.background = '#ef4444';
                } else if (this.timerState.currentState === 'short_break' || this.timerState.currentState === 'long_break') {
                    this.elements.pomodoroStatusIndicator.style.background = '#22c55e'; // Green for breaks
                } else {
                    this.elements.pomodoroStatusIndicator.style.background = '#7C3AED'; // Purple for work
                }
            } else {
                this.elements.pomodoroStatusIndicator.style.display = 'none';
            }
        }
        
        // Apply green background styling during break periods
        if (this.elements.pomodoroSection) {
            const isBreakPeriod = this.timerState.currentState === 'short_break' || this.timerState.currentState === 'long_break';
            if (isBreakPeriod) {
                this.elements.pomodoroSection.classList.add('break-period');
            } else {
                this.elements.pomodoroSection.classList.remove('break-period');
            }
        }
    }


    /**
     * Update active tasks display
     */
    async updateActiveTasks() {
        // Skip UI updates in headless mode
        if (this.headlessMode) {
            return;
        }
        
        if (!this.tasksElements.tasksActiveTasks || !this.todoManager) return;

        const displayTodos = this.todoManager.getTodosForDisplay();
        
        this.tasksElements.tasksActiveTasks.innerHTML = '';
        
        if (displayTodos.length === 0) {
            this.tasksElements.tasksActiveTasks.innerHTML = '<div class="tasks-task">No active tasks</div>';
            return;
        }

        displayTodos.forEach(todo => {
            const taskElement = this.createTaskElement(todo, false, true);
            this.tasksElements.tasksActiveTasks.appendChild(taskElement);
        });
    }

    /**
     * Update tasks section display including count
     */
    async updateTasksDisplay() {
        // Skip UI updates in headless mode
        if (this.headlessMode) {
            return;
        }
        
        if (!this.todoManager) return;

        // Update active tasks display
        await this.updateActiveTasks();
        
        // Update tasks count in header and handle compact mode
        if (this.tasksElements.tasksCount && this.tasksElements.tasksSection) {
            const activeTasks = this.todoManager.getActiveTodos();
            const displayCount = this.todoManager.displayCount;
            
            if (activeTasks.length > 0) {
                this.tasksElements.tasksCount.textContent = `(${Math.min(activeTasks.length, displayCount)}/${activeTasks.length})`;
                
                // Switch to compact mode when tasks exist
                this.tasksElements.tasksSection.classList.add('compact-mode');
            } else {
                this.tasksElements.tasksCount.textContent = '';
                
                // Switch to normal mode when no tasks
                this.tasksElements.tasksSection.classList.remove('compact-mode');
            }
        }
    }

    /**
     * Update full todo list
     */
    async updateTodoList() {
        if (!this.tasksElements.tasksList || !this.todoManager) return;

        const allTodos = this.todoManager.getAllTodos();
        const displayTodos = this.todoManager.getTodosForDisplay(); // Tasks already shown in TOP section
        
        // Filter out tasks already displayed in the top section
        const displayTodoIds = displayTodos.map(todo => todo.id);
        const remainingTodos = allTodos.filter(todo => !displayTodoIds.includes(todo.id));
        
        this.tasksElements.tasksList.innerHTML = '';
        
        if (remainingTodos.length === 0) {
            this.tasksElements.tasksList.innerHTML = '<div class="tasks-task-item">All tasks are shown above!</div>';
            return;
        }

        remainingTodos.forEach((todo, index) => {
            const taskElement = this.createTaskElement(todo, false);
            
            // Disable appropriate reorder buttons
            const upBtn = taskElement.querySelector('.tasks-reorder-btn--up');
            const downBtn = taskElement.querySelector('.tasks-reorder-btn--down');
            
            if (upBtn && index === 0) {
                upBtn.disabled = true;
            }
            if (downBtn && index === remainingTodos.length - 1) {
                downBtn.disabled = true;
            }
            
            this.tasksElements.tasksList.appendChild(taskElement);
        });
    }

    /**
     * Create task element
     */
    createTaskElement(todo, isCompact = false, showControls = true) {
        const div = document.createElement('div');
        div.className = `tasks-${isCompact ? 'task' : 'task-item'}${todo.completed ? ' completed' : ''}`;
        div.dataset.taskId = todo.id; // Add data attribute for selection
        
        // Add click handler for task selection when controls are enabled
        if (showControls) {
            div.addEventListener('click', (e) => {
                // Don't select if clicking on checkbox, text (for editing), or control buttons
                if (!e.target.classList.contains('tasks-task-checkbox') && 
                    !e.target.classList.contains('tasks-task-text') &&
                    !e.target.closest('.tasks-reorder-controls') &&
                    !e.target.classList.contains('tasks-task-delete')) {
                    this.selectTask(todo.id);
                }
            });
        }
        
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'tasks-task-checkbox';
        checkbox.checked = todo.completed;
        checkbox.addEventListener('change', () => {
            this.toggleTask(todo.id);
        });

        const textSpan = document.createElement('span');
        textSpan.className = 'tasks-task-text';
        textSpan.textContent = todo.text;
        textSpan.addEventListener('click', () => {
            if (!todo.completed) {
                this.editTask(todo.id, textSpan);
            }
        });

        div.appendChild(checkbox);
        div.appendChild(textSpan);

        if (showControls) {
            // Add selection grip
            const selectionGrip = document.createElement('div');
            selectionGrip.className = 'tasks-selection-grip';
            selectionGrip.innerHTML = '⋯'; // Three horizontal dots
            
            selectionGrip.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectTask(todo.id);
            });

            // Add reorder controls
            const reorderControls = document.createElement('div');
            reorderControls.className = 'tasks-reorder-controls';

            // Up arrow button
            const upBtn = document.createElement('button');
            upBtn.className = 'tasks-reorder-btn tasks-reorder-btn--up';
            upBtn.type = 'button';
            upBtn.title = 'Move up';
            upBtn.innerHTML = `
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
                </svg>
            `;
            upBtn.addEventListener('click', () => {
                this.moveTaskUp(todo.id);
            });

            // Down arrow button
            const downBtn = document.createElement('button');
            downBtn.className = 'tasks-reorder-btn tasks-reorder-btn--down';
            downBtn.type = 'button';
            downBtn.title = 'Move down';
            downBtn.innerHTML = `
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6z"/>
                </svg>
            `;
            downBtn.addEventListener('click', () => {
                this.moveTaskDown(todo.id);
            });

            reorderControls.appendChild(upBtn);
            reorderControls.appendChild(downBtn);
            
            div.appendChild(selectionGrip);
            div.appendChild(reorderControls);

            // Add delete button
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'tasks-task-delete';
            deleteBtn.textContent = '×';
            deleteBtn.addEventListener('click', () => {
                this.deleteTask(todo.id);
            });
            div.appendChild(deleteBtn);
        }

        return div;
    }

    /**
     * Add new task
     */
    async addTask() {
        if (!this.tasksElements.tasksNewTaskInput || !this.todoManager) return;

        const text = this.tasksElements.tasksNewTaskInput.value.trim();
        if (!text) return;

        try {
            await this.todoManager.addTodo(text);
            this.tasksElements.tasksNewTaskInput.value = '';
            
            await this.updateTasksDisplay();
            await this.updateTodoList();
        } catch (error) {
            console.error('PomodoroPopup: Error adding task:', error);
        }
    }

    /**
     * Toggle task completion
     */
    async toggleTask(id) {
        if (!this.todoManager) return;
        
        try {
            await this.todoManager.toggleTodo(id);
            await this.updateTasksDisplay();
            await this.updateTodoList();
        } catch (error) {
            console.error('PomodoroPopup: Error toggling task:', error);
        }
    }

    /**
     * Delete task
     */
    async deleteTask(id) {
        if (!this.todoManager) return;
        
        try {
            await this.todoManager.deleteTodo(id);
            await this.updateTasksDisplay();
            await this.updateTodoList();
        } catch (error) {
            console.error('PomodoroPopup: Error deleting task:', error);
        }
    }

    /**
     * Move task up
     */
    async moveTaskUp(id) {
        if (!this.todoManager) return;
        
        try {
            const moved = await this.todoManager.moveUp(id);
            if (moved) {
                await this.updateTasksDisplay();
                await this.updateTodoList();
            }
        } catch (error) {
            console.error('PomodoroPopup: Error moving task up:', error);
        }
    }

    /**
     * Move task down
     */
    async moveTaskDown(id) {
        if (!this.todoManager) return;
        
        try {
            const moved = await this.todoManager.moveDown(id);
            if (moved) {
                await this.updateTasksDisplay();
                await this.updateTodoList();
            }
        } catch (error) {
            console.error('PomodoroPopup: Error moving task down:', error);
        }
    }

    /**
     * Edit task (inline editing)
     */
    editTask(id, textElement) {
        const currentText = textElement.textContent;
        
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'tasks-new-task-input';
        input.value = currentText;
        input.style.fontSize = 'inherit';
        input.style.border = '1px solid #7C3AED';
        
        const saveEdit = async () => {
            const newText = input.value.trim();
            if (newText && newText !== currentText) {
                try {
                    if (this.todoManager) {
                        await this.todoManager.updateTodo(id, newText);
                        await this.updateTasksDisplay();
                        await this.updateTodoList();
                    }
                } catch (error) {
                    console.error('PomodoroPopup: Error updating task:', error);
                }
            } else {
                textElement.textContent = currentText;
            }
            textElement.style.display = 'inline';
            input.remove();
        };

        input.addEventListener('blur', saveEdit);
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                saveEdit();
            } else if (e.key === 'Escape') {
                textElement.style.display = 'inline';
                input.remove();
            }
        });

        textElement.style.display = 'none';
        textElement.parentNode.insertBefore(input, textElement.nextSibling);
        input.focus();
        input.select();
    }

    /**
     * Send message with robust error handling (adapted from popup.js)
     */
    sendMessageWithErrorHandling(message, timeoutMs = 15000) {
        return new Promise((resolve, reject) => {
            console.log('📤 PomodoroPopup: Sending message to background:', message.action);
            
            // Set up timeout for async operations
            const timeoutId = setTimeout(() => {
                reject(new Error(`Message timeout after ${timeoutMs}ms for action: ${message.action}`));
            }, timeoutMs);
            
            try {
                // Enhanced extension context validation
                if (!chrome.runtime?.id || typeof chrome === 'undefined' || !chrome.runtime.getManifest) {
                    clearTimeout(timeoutId);
                    reject(new Error('Extension context invalidated'));
                    return;
                }
                
                chrome.runtime.sendMessage(message, (response) => {
                    console.log('📦 PomodoroPopup: Raw response received:', response, 'lastError:', chrome.runtime.lastError);
                    
                    // Clear timeout since we got a response (or error)
                    clearTimeout(timeoutId);
                    
                    // Check for Chrome runtime errors
                    if (chrome.runtime.lastError) {
                        console.error('❌ PomodoroPopup: Chrome runtime error:', chrome.runtime.lastError.message);
                        // Invalidate background ready cache on connection errors
                        this.backgroundReadyCache = null;
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }
                    
                    // Check if we got a response
                    if (response === undefined || response === null) {
                        console.error('❌ PomodoroPopup: No response received from background script for action:', message.action);
                        reject(new Error('No response received from background script'));
                        return;
                    }
                    
                    console.log('📥 PomodoroPopup: Received response from background:', response);
                    
                    // Check if the response indicates an error
                    if (response.success === false) {
                        const errorMsg = response.error || 'Unknown error from background script';
                        console.error('❌ PomodoroPopup: Background script returned error:', errorMsg);
                        reject(new Error(errorMsg));
                        return;
                    }
                    
                    resolve(response);
                });
            } catch (error) {
                clearTimeout(timeoutId);
                console.error('❌ PomodoroPopup: Error sending message:', error);
                // Invalidate background ready cache on connection errors
                this.backgroundReadyCache = null;
                
                // Check for context invalidation specifically
                if (error.message && error.message.includes('Extension context invalidated')) {
                    reject(new Error('Extension context invalidated'));
                } else {
                    reject(new Error('Failed to send message: ' + error.message));
                }
            }
        });
    }

    /**
     * Check if background script is ready for timer operations (with caching)
     */
    async isBackgroundReady(forceCheck = false) {
        const now = Date.now();
        const cacheValidTime = 5000; // Cache for 5 seconds
        
        // Return cached result if it's still valid and not forced
        if (!forceCheck && this.backgroundReadyCache !== null && 
            (now - this.lastBackgroundCheckTime) < cacheValidTime) {
            return this.backgroundReadyCache;
        }
        
        try {
            console.log('🔍 PomodoroPopup: Testing background script connection...');
            if (!chrome.runtime?.id) {
                console.error('❌ PomodoroPopup: Chrome runtime ID not available');
                this.backgroundReadyCache = false;
                this.lastBackgroundCheckTime = now;
                return false;
            }
            
            const response = await this.sendMessageWithErrorHandling({
                action: 'ping'
            });
            
            console.log('🏓 PomodoroPopup: Ping response received:', response);
            const isReady = response && response.success;
            
            // Cache the result
            this.backgroundReadyCache = isReady;
            this.lastBackgroundCheckTime = now;
            
            return isReady;
        } catch (error) {
            console.log('PomodoroPopup: Background not ready:', error.message);
            
            // Cache negative result but with shorter time
            this.backgroundReadyCache = false;
            this.lastBackgroundCheckTime = now;
            
            return false;
        }
    }
    
    /**
     * Wait for background script to be ready (adapted from popup.js)
     */
    async waitForBackgroundReady(maxAttempts = 3, delayMs = 300) {
        for (let i = 0; i < maxAttempts; i++) {
            if (await this.isBackgroundReady()) {
                return true;
            }
            
            console.log(`PomodoroPopup: Background not ready, attempt ${i + 1}/${maxAttempts}`);
            await new Promise(resolve => setTimeout(resolve, delayMs));
        }
        
        return false;
    }
    
    /**
     * Send timer action with retry logic for better reliability after extension reloads
     */
    async sendTimerActionWithRetry(action, maxRetries = 2) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`PomodoroPopup: Sending timer action '${action}' (attempt ${attempt}/${maxRetries})`);
                
                // Wait for background script to be ready
                const isReady = await this.waitForBackgroundReady(3, 300);
                if (!isReady) {
                    throw new Error(`Background script not ready on attempt ${attempt}`);
                }
                
                // Send the action
                const response = await this.sendMessageWithErrorHandling({
                    action: action
                }, 8000); // 8 second timeout per attempt
                
                console.log(`PomodoroPopup: Timer action '${action}' succeeded on attempt ${attempt}`);
                return response;
                
            } catch (error) {
                console.warn(`PomodoroPopup: Timer action '${action}' failed on attempt ${attempt}:`, error.message);
                
                // If this was the last attempt, throw the error
                if (attempt === maxRetries) {
                    throw error;
                }
                
                // Wait before retrying (progressive backoff)
                const delay = attempt * 500; // 500ms, 1000ms delays
                console.log(`PomodoroPopup: Waiting ${delay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                
                // Clear background cache to force fresh check
                this.backgroundReadyCache = null;
            }
        }
    }
    
    /**
     * Timer Control Methods (simplified like working version)
     */
    async startTimer() {
        try {
            console.log('PomodoroPopup: Starting timer with current time preset');
            
            // Use retry mechanism for better reliability
            const response = await this.sendTimerActionWithRetry('pomodoroStart');
            
            console.log('PomodoroPopup: Start timer completed successfully');
            return response;
            
        } catch (error) {
            console.error('PomodoroPopup: Error starting timer:', error);
            
            // Provide user-friendly error message based on error type
            if (error.message.includes('not available') || error.message.includes('not ready') || error.message.includes('connection')) {
                this.showNotification?.('Timer start failed - background script unavailable. Please try again.', 'error');
            } else {
                this.showNotification?.('Failed to start timer', 'error');
            }
            throw error;
        }
    }

    async pauseTimer() {
        try {
            console.log('PomodoroPopup: Pausing/Resuming timer');
            
            const action = this.timerState.isPaused ? 'pomodoroResume' : 'pomodoroPause';
            console.log(`PomodoroPopup: Action to perform: ${action}`);
            
            // Use retry mechanism for better reliability
            const response = await this.sendTimerActionWithRetry(action);
            
            console.log('PomodoroPopup: Pause/Resume completed successfully');
            return response;
            
        } catch (error) {
            console.error('PomodoroPopup: Error pausing/resuming timer:', error);
            
            // Provide user-friendly error message based on error type
            if (error.message.includes('not available') || error.message.includes('not ready') || error.message.includes('connection')) {
                this.showNotification?.('Timer pause/resume failed - background script unavailable. Please try again.', 'error');
            } else {
                this.showNotification?.('Failed to pause/resume timer', 'error');
            }
            throw error;
        }
    }

    async stopTimer() {
        try {
            console.log('PomodoroPopup: Stopping timer');
            
            // Use retry mechanism for better reliability
            const response = await this.sendTimerActionWithRetry('pomodoroStop');
            
            console.log('PomodoroPopup: Stop timer completed successfully');
            return response;
            
        } catch (error) {
            console.error('PomodoroPopup: Error stopping timer:', error);
            
            // Provide user-friendly error message based on error type
            if (error.message.includes('not available') || error.message.includes('not ready') || error.message.includes('connection')) {
                this.showNotification?.('Timer stop failed - background script unavailable. Please try again.', 'error');
            } else {
                this.showNotification?.('Failed to stop timer', 'error');
            }
            throw error;
        }
    }

    async resetTimer() {
        try {
            console.log('PomodoroPopup: Resetting timer');
            
            // Use retry mechanism for better reliability (reset uses stop action)
            const response = await this.sendTimerActionWithRetry('pomodoroStop');
            
            console.log('PomodoroPopup: Reset timer completed successfully');
            return response;
            
        } catch (error) {
            console.error('PomodoroPopup: Error resetting timer:', error);
            
            // Provide user-friendly error message based on error type
            if (error.message.includes('not available') || error.message.includes('not ready') || error.message.includes('connection')) {
                this.showNotification?.('Timer reset failed - background script unavailable. Please try again.', 'error');
            } else {
                this.showNotification?.('Failed to reset timer', 'error');
            }
            throw error;
        }
    }

    /**
     * Request initial timer state from background
     */
    async requestTimerState() {
        try {
            // State will come from storage polling - no need to request it
            console.log('PomodoroPopup: Timer state will be loaded via storage polling');
        } catch (error) {
            console.log('PomodoroPopup: Error in timer state setup:', error);
        }
    }

    /**
     * Update control button visibility based on timer state
     */
    updateControlButtons() {
        // Skip UI updates in headless mode
        if (this.headlessMode) {
            return;
        }
        
        const { currentState, isPaused } = this.timerState;
        
        if (!this.elements.startBtn || !this.elements.pauseBtn || !this.elements.stopBtn) return;

        if (currentState === 'idle') {
            // Timer is idle - show Start and Reset
            this.elements.startBtn.style.display = 'inline-block';
            this.elements.startBtn.textContent = 'Start';
            this.elements.pauseBtn.style.display = 'none';
            this.elements.stopBtn.style.display = 'none';
            
        } else {
            // Timer is running or paused
            this.elements.startBtn.style.display = 'none';
            
            if (isPaused) {
                this.elements.pauseBtn.style.display = 'inline-block';
                this.elements.pauseBtn.textContent = 'Resume';
            } else {
                this.elements.pauseBtn.style.display = 'inline-block';
                this.elements.pauseBtn.textContent = 'Pause';
            }
            
            this.elements.stopBtn.style.display = 'inline-block';
            
        }
    }




    /**
     * Setup event listeners for all settings inputs
     */
    setupSettingsEventListeners() {
        // Audio-related settings are handled by sound-preview.js with STM system (local storage)
        const settingsInputs = [
            'workDuration', 'shortBreak', 'longBreak', 'numberOfCycles',
            'chronometerFrequency', 'blockedSites', 'whitelistedSites'
        ];
        // REMOVED: 'completionNotifications', 'chronometerSound', 'chronometerOnBreak', 'notifyPosition'
        // These are now handled by local storage to prevent conflicts with other features

        settingsInputs.forEach(inputName => {
            const element = this.elements[inputName];
            
            if (element && element.nodeType === Node.ELEMENT_NODE) {
                const eventType = element.type === 'checkbox' ? 'change' : 'input';
                element.addEventListener(eventType, () => {
                    const value = element.type === 'checkbox' ? element.checked : element.value;
                    
                    // Get setting name from data-setting attribute or fallback to mapping
                    let settingName = element.getAttribute('data-setting');
                    
                    if (!settingName) {
                        // Fallback mapping for timer settings
                        const settingMap = {
                            'workDuration': 'pomodoroWorkDuration',
                            'shortBreak': 'pomodoroShortBreak',
                            'longBreak': 'pomodoroLongBreak',
                            'numberOfCycles': 'pomodoroNumberOfCycles',
                            'chronometerFrequency': 'pomodoroChronometerFrequency',
                            'todoDisplayCount': 'pomodoroTodoDisplayCount'
                        };
                        settingName = settingMap[inputName] || `pomodoro${inputName.charAt(0).toUpperCase() + inputName.slice(1)}`;
                    }
                    
                    if (settingName) {
                        this.saveSettingToStorage(settingName, value);
                        
                        // If timer duration setting changed, trigger immediate refresh
                        const timerSettings = ['pomodoroWorkDuration', 'pomodoroShortBreak', 'pomodoroLongBreak'];
                        if (timerSettings.includes(settingName)) {
                            console.log('PomodoroPopup: Timer setting changed, triggering immediate refresh');
                            this.refreshTimerDisplay();
                        }
                        
                        // Handle cycles setting change - immediate refresh without restart
                        if (settingName === 'pomodoroNumberOfCycles') {
                            console.log('PomodoroPopup: Cycles setting changed, triggering immediate cycle display refresh');
                            // Update the current timer state with new cycles setting and refresh immediately
                            this.timerState.totalCycles = value;
                            this.updateTimerDisplay();
                            
                            // If timer is running, also notify background to update its cycles state
                            if (this.timerState.currentState !== 'idle') {
                                this.sendMessageWithErrorHandling({
                                    action: 'pomodoroUpdateCycles',
                                    totalCycles: value
                                }).catch(err => console.log('PomodoroPopup: Background cycles update:', err));
                            }
                        }
                    } else {
                        console.warn(`⚠️ DEBUG: No settingName found for inputName='${inputName}'`);
                    }
                });
            } else {
                console.warn(`⚠️ DEBUG: Element not found for inputName='${inputName}'`);
            }
        });
    }

    /**
     * Toggle settings accordion sections
     */
    toggleSettingsAccordion(section) {
        const contentElement = document.getElementById(`pomodoro${section.charAt(0).toUpperCase() + section.slice(1)}Content`);
        const iconElement = document.getElementById(`pomodoro${section.charAt(0).toUpperCase() + section.slice(1)}Icon`);
        
        if (!contentElement || !iconElement) return;

        const isExpanded = contentElement.classList.contains('expanded');
        
        if (isExpanded) {
            contentElement.classList.remove('expanded');
            contentElement.style.display = 'none';
            iconElement.classList.remove('expanded');
        } else {
            contentElement.classList.add('expanded');
            contentElement.style.display = 'block';
            iconElement.classList.add('expanded');
        }
    }

    /**
     * Preview sound using the existing sound preview system
     */
    async previewSound(soundName, isTickingSound = false) {
        try {
            console.log('PomodoroPopup: Previewing sound:', soundName, 'isTickingSound:', isTickingSound);
            
            // Get current volume for preview
            const volumeElement = isTickingSound ? this.elements.chronometerVolume : this.elements.notificationVolume;
            const volume = volumeElement ? parseInt(volumeElement.value) / 100 : 0.7;
            
            // Send message to background for preview
            await this.sendMessageWithErrorHandling({
                action: 'playPomodoroPreview',
                soundName: soundName,
                volume: volume,
                isTickingSound: isTickingSound
            });
        } catch (error) {
            console.error('PomodoroPopup: Error playing preview sound:', error);
        }
    }

    /**
     * Save individual setting to storage with enhanced validation and immediate propagation
     */
    async saveSettingToStorage(settingName, value) {
        try {
            console.log(`🔍 DEBUG: saveSettingToStorage called with settingName='${settingName}', value='${value}'`);
            
            // Set flag to prevent circular updates
            this.isSavingSettings = true;
            
            // Handle site blocking lists specially - save in array format
            if (settingName === 'pomodoroBlockedSites') {
                // Convert textarea content to array
                const sitesArray = value.split('\n')
                    .map(site => site.trim())
                    .filter(site => site.length > 0);
                
                await chrome.storage.local.set({ blockedSites: sitesArray });
                console.log('PomodoroPopup: Saved blocked sites:', sitesArray);
                this.debouncedSettingsPropagation(); // Use debounced propagation
                return;
            }
            
            if (settingName === 'pomodoroWhitelistedSites') {
                // Convert textarea content to array (but save as allowedUrls)
                const sitesArray = value.split('\n')
                    .map(site => site.trim())
                    .filter(site => site.length > 0);
                
                await chrome.storage.local.set({ allowedUrls: sitesArray });
                console.log('PomodoroPopup: Saved allowed URLs:', sitesArray);
                this.debouncedSettingsPropagation(); // Use debounced propagation
                return;
            }
            
            // Handle other Pomodoro settings normally
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            
            // Enhanced validation with reasonable limits
            let validatedValue = value;
            const isTimerSetting = settingName.includes('Duration') || settingName.includes('Break') || settingName === 'pomodoroUltraFocus';
            
            if (isTimerSetting) {
                // Skip validation if value is empty (user is still typing)
                if (value === '' || value === null || value === undefined) {
                    return;
                }
                
                const numValue = parseInt(value);
                
                // Validate that it's a valid integer
                if (isNaN(numValue) || !Number.isInteger(parseFloat(value))) {
                    console.warn(`❌ PomodoroPopup: Invalid ${settingName} value '${value}' - must be a whole number`);
                    return; // Don't save invalid values
                }
                
                // Enhanced validation with reasonable limits
                if (settingName === 'pomodoroWorkDuration') {
                    // Work duration should be reasonable (1-480 minutes = 8 hours max)
                    if (numValue < 1 || numValue > 480) {
                        console.warn(`❌ PomodoroPopup: Invalid work duration ${numValue}, must be 1-480 minutes`);
                        return;
                    }
                } else if (settingName === 'pomodoroShortBreak') {
                    // Short break should be reasonable (1-60 minutes max)
                    if (numValue < 1 || numValue > 60) {
                        console.warn(`❌ PomodoroPopup: Invalid short break ${numValue}, must be 1-60 minutes`);
                        return;
                    }
                } else if (settingName === 'pomodoroLongBreak') {
                    // Long break should be reasonable (1-120 minutes max)
                    if (numValue < 1 || numValue > 120) {
                        console.warn(`❌ PomodoroPopup: Invalid long break ${numValue}, must be 1-120 minutes`);
                        return;
                    }
                } else {
                    // General validation for other timer settings
                    if (numValue <= 0) {
                        console.warn(`❌ PomodoroPopup: Invalid ${settingName} value ${numValue}, must be positive whole number`);
                        return; // Don't save non-positive values
                    }
                }
                
                validatedValue = numValue;
            }
            
            // Handle cycles setting validation - allow positive integers or 'unlimited'
            if (settingName === 'pomodoroNumberOfCycles') {
                if (value === 'unlimited') {
                    validatedValue = 'unlimited';
                } else {
                    const numValue = parseInt(value);
                    if (isNaN(numValue) || numValue < 1) {
                        console.warn(`❌ PomodoroPopup: Invalid cycles value ${value}, must be positive integer or 'unlimited'`);
                        return; // Don't save invalid values
                    } else {
                        validatedValue = numValue;
                    }
                }
            }
            
            // Save the validated value
            settings[settingName] = validatedValue;
            
            await chrome.storage.local.set({ gmbExtractorSettings: settings });
            
            // Use debounced propagation for regular settings changes
            if (isTimerSetting || settingName === 'pomodoroNumberOfCycles') {
                this.debouncedSettingsPropagation();
            }
            
            console.log(`✅ PomodoroPopup: Saved setting ${settingName}:`, validatedValue);
            
        } catch (error) {
            console.error(`❌ PomodoroPopup: Error saving setting ${settingName}:`, error);
        } finally {
            // Clear flag after save completes
            setTimeout(() => {
                this.isSavingSettings = false;
            }, 100);
        }
    }


    /**
     * Load all settings from storage and populate inputs (with race condition protection)
     */
    async loadAllSettings() {
        // Prevent race conditions from multiple simultaneous calls
        if (this.isLoadingSettings) {
            console.log('🔄 POMODORO LOAD: Already loading settings, skipping duplicate call');
            return;
        }
        
        this.isLoadingSettings = true;
        
        try {
            // LOCKDOWN PROTECTION: Check if settings lockdown is active
            const lockdownCheck = await chrome.runtime.sendMessage({
                action: 'checkSettingsLockdown'
            });
            
            if (lockdownCheck && lockdownCheck.success && lockdownCheck.lockdownActive) {
                console.log('🔒 POMODORO LOAD: Settings lockdown active - SKIPPING all storage corrections');
                console.log('🔒 POMODORO LOAD: Loading settings read-only mode');
                
                // Load settings in read-only mode - no corrections, no storage writes
                await this.loadAllSettingsReadOnly();
                return;
            }
            // Use local storage for all Pomodoro settings like Pomodoro Grande
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            
            // FIXED: Conservative validation - only warn about issues, don't auto-correct
            // Auto-correction was causing cascading timer resets
            const defaultValues = {
                pomodoroWorkDuration: 25,
                pomodoroShortBreak: 5,
                pomodoroLongBreak: 15,
                pomodoroNumberOfCycles: 8
            };
            
            // Only validate, don't auto-correct unless explicitly requested
            Object.keys(defaultValues).forEach(key => {
                const storedValue = settings[key];
                
                if (storedValue !== undefined && storedValue !== null) {
                    let isInvalid = false;
                    
                    if (key === 'pomodoroNumberOfCycles') {
                        // Special handling for cycles - 'unlimited' is valid
                        if (storedValue === 'unlimited') {
                            console.log(`✅ POMODORO LOAD: Valid cycles setting: ${storedValue}`);
                            return; // Skip further validation for 'unlimited'
                        }
                        const numValue = parseInt(storedValue);
                        isInvalid = isNaN(numValue) || numValue < 1;
                    } else {
                        // Duration settings - must be positive integers
                        const numValue = parseInt(storedValue);
                        isInvalid = isNaN(numValue) || numValue < 1;
                    }
                    
                    if (isInvalid) {
                        console.warn(`⚠️ POMODORO VALIDATION: Invalid ${key}: ${storedValue} (keeping as-is, use Reset to Defaults if needed)`);
                    } else {
                        console.log(`✅ POMODORO LOAD: Valid setting ${key}: ${storedValue}`);
                    }
                } else {
                    // Only add missing settings if they're truly undefined (not during initialization)
                    if (typeof storedValue === 'undefined') {
                        console.log(`🔧 POMODORO LOAD: Setting missing ${key}, using default: ${defaultValues[key]}`);
                        settings[key] = defaultValues[key];
                    }
                }
            });
            
            // No automatic correction - user must explicitly reset if they want defaults
            
            // Timer fields with input protection - validate stored values and ensure proper defaults
            const timerFields = [
                { element: this.elements.workDuration, setting: 'pomodoroWorkDuration', default: 25, name: 'workDuration' },
                { element: this.elements.shortBreak, setting: 'pomodoroShortBreak', default: 5, name: 'shortBreak' },
                { element: this.elements.longBreak, setting: 'pomodoroLongBreak', default: 15, name: 'longBreak' }
            ];
            
            let additionalCorrectionsMade = false;
            
            timerFields.forEach(field => {
                if (field.element) {
                    // Skip updating if element is currently focused (user is typing)
                    if (document.activeElement === field.element) {
                        return;
                    }
                    
                    // Get stored value and validate it - use proper validation
                    let storedValue = settings[field.setting];
                    let value = field.default;
                    
                    // Ensure we have a proper stored value (after cleanup above)
                    if (storedValue !== undefined && storedValue !== null) {
                        const numValue = parseInt(storedValue);
                        // Use proper validation (at least 1 minute for all timers)
                        if (!isNaN(numValue) && numValue >= 1) {
                            value = numValue;
                        } else {
                            console.warn(`Invalid stored value for ${field.setting}: ${storedValue} (must be positive number >= 1), using default: ${field.default}`);
                            settings[field.setting] = field.default;
                            additionalCorrectionsMade = true;
                            value = field.default;
                        }
                    } else {
                        // If still no value, use default and save it
                        console.warn(`Missing value for ${field.setting}, using default: ${field.default}`);
                        settings[field.setting] = field.default;
                        additionalCorrectionsMade = true;
                        value = field.default;
                    }
                    
                    // Set the form element value
                    field.element.value = value;
                    console.log(`✅ POMODORO LOAD: Set ${field.setting} to ${value}`);
                }
            });
            
            // Save any additional corrections
            if (additionalCorrectionsMade) {
                await chrome.storage.local.set({ gmbExtractorSettings: settings });
                console.log('✅ POMODORO LOAD: Additional corrections saved to storage');
            }
            
            // Handle numberOfCycles with proper validation
            if (this.elements.numberOfCycles) {
                let cyclesValue = settings.pomodoroNumberOfCycles;
                const defaultCycles = 8;
                
                // Validate cycles - must be a positive integer >= 1 or 'unlimited'
                if (cyclesValue !== undefined && cyclesValue !== null) {
                    if (cyclesValue === 'unlimited') {
                        // 'unlimited' is valid, but we prefer numeric defaults
                        this.elements.numberOfCycles.value = 'unlimited';
                        console.log(`✅ POMODORO LOAD: Set numberOfCycles to unlimited`);
                    } else {
                        const numCycles = parseInt(cyclesValue);
                        if (!isNaN(numCycles) && numCycles >= 1) {
                            this.elements.numberOfCycles.value = numCycles;
                            console.log(`✅ POMODORO LOAD: Set numberOfCycles to ${numCycles}`);
                        } else {
                            console.warn(`Invalid cycles value: ${cyclesValue}, using default: ${defaultCycles}`);
                            this.elements.numberOfCycles.value = defaultCycles;
                            settings.pomodoroNumberOfCycles = defaultCycles;
                            await chrome.storage.local.set({ gmbExtractorSettings: settings });
                        }
                    }
                } else {
                    // No value stored, use default
                    console.warn(`Missing cycles value, using default: ${defaultCycles}`);
                    this.elements.numberOfCycles.value = defaultCycles;
                    settings.pomodoroNumberOfCycles = defaultCycles;
                    await chrome.storage.local.set({ gmbExtractorSettings: settings });
                }
            }
            
            // Load audio settings
            if (this.elements.workCompletedSound) this.elements.workCompletedSound.value = settings.pomodoroWorkCompletedSound || 'Bell Meditation';
            if (this.elements.endBreakSound) this.elements.endBreakSound.value = settings.pomodoroEndBreakSound || 'Celestial Gong';
            if (this.elements.tickingSound) this.elements.tickingSound.value = settings.pomodoroTickingSound || 'Clock Ticking 1';
            
            if (this.elements.completionNotifications) this.elements.completionNotifications.checked = settings.pomodoroCompletionNotifications !== false;
            if (this.elements.notifyPosition) this.elements.notifyPosition.value = settings.pomodoroNotifyPosition || 'br';
            if (this.elements.chronometerSound) this.elements.chronometerSound.checked = settings.pomodoroChronometerSound !== false;
            if (this.elements.chronometerOnBreak) this.elements.chronometerOnBreak.checked = settings.pomodoroChronometerOnBreak || false;
            
            if (this.elements.chronometerFrequency) this.elements.chronometerFrequency.value = settings.pomodoroChronometerFrequency || 2;
            
            // Load and update volume sliders
            const notificationVolume = settings.pomodoroNotificationVolume || 70;
            const chronometerVolume = settings.pomodoroChronometerVolume || 30;
            
            if (this.elements.notificationVolume) {
                this.elements.notificationVolume.value = notificationVolume;
                if (this.elements.notificationVolumeValue) this.elements.notificationVolumeValue.textContent = notificationVolume + '%';
            }
            
            if (this.elements.chronometerVolume) {
                this.elements.chronometerVolume.value = chronometerVolume;
                if (this.elements.chronometerVolumeValue) this.elements.chronometerVolumeValue.textContent = chronometerVolume + '%';
            }
            
            // Load display settings
            if (this.elements.todoDisplayCount) this.elements.todoDisplayCount.value = settings.pomodoroTodoDisplayCount || '2';
            
            // Load website blocking from storage
            try {
                // Load blocked sites from storage
                const blockedSitesResult = await chrome.storage.local.get(['blockedSites']);
                const blockedSitesArray = blockedSitesResult.blockedSites || ['facebook.com', 'twitter.com', 'instagram.com', 'youtube.com', 'reddit.com', 'tiktok.com'];
                if (this.elements.blockedSites) {
                    this.elements.blockedSites.value = blockedSitesArray.join('\n');
                }
                
                // Load allowed URLs from storage
                const allowedUrlsResult = await chrome.storage.local.get(['allowedUrls']);
                const allowedUrlsArray = allowedUrlsResult.allowedUrls || ['google.com', 'maps.google.com', 'github.com'];
                if (this.elements.whitelistedSites) {
                    this.elements.whitelistedSites.value = allowedUrlsArray.join('\n');
                }
            } catch (error) {
                console.error('PomodoroPopup: Error loading site blocking settings:', error);
                // Fallback to defaults
                if (this.elements.blockedSites) this.elements.blockedSites.value = 'facebook.com\ntwitter.com\ninstagram.com\nyoutube.com\nreddit.com\ntiktok.com';
                if (this.elements.whitelistedSites) this.elements.whitelistedSites.value = 'google.com\nmaps.google.com\ngithub.com';
            }
            
            console.log('PomodoroPopup: All settings loaded from storage');
        } catch (error) {
            console.error('PomodoroPopup: Error loading settings:', error);
        } finally {
            // Clear the loading flag to allow future calls
            this.isLoadingSettings = false;
        }
    }

    /**
     * Load settings in read-only mode during lockdown - no corrections, no storage writes
     */
    async loadAllSettingsReadOnly() {
        try {
            // Use local storage for all Pomodoro settings like Pomodoro Grande
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            
            console.log('🔒 POMODORO LOAD: Read-only mode - loading settings without corrections');
            
            // Load timer fields directly without any validation or corrections
            const timerFields = [
                { element: this.elements.workDuration, setting: 'pomodoroWorkDuration', default: 25, name: 'workDuration' },
                { element: this.elements.shortBreak, setting: 'pomodoroShortBreak', default: 5, name: 'shortBreak' },
                { element: this.elements.longBreak, setting: 'pomodoroLongBreak', default: 15, name: 'longBreak' }
            ];
            
            timerFields.forEach(field => {
                if (field.element) {
                    // Skip updating if element is currently focused (user is typing)
                    if (document.activeElement === field.element) {
                        return;
                    }
                    
                    // Use stored value or default - NO VALIDATION, NO CORRECTIONS
                    const storedValue = settings[field.setting];
                    const value = storedValue !== undefined ? storedValue : field.default;
                    
                    field.element.value = value;
                    console.log(`🔒 POMODORO LOAD: Read-only set ${field.setting} to ${value}`);
                }
            });
            
            // Handle numberOfCycles without validation
            if (this.elements.numberOfCycles) {
                const cyclesValue = settings.pomodoroNumberOfCycles !== undefined ? settings.pomodoroNumberOfCycles : 8;
                this.elements.numberOfCycles.value = cyclesValue;
                console.log(`🔒 POMODORO LOAD: Read-only set numberOfCycles to ${cyclesValue}`);
            }
            
            // Load audio settings without validation
            if (this.elements.workCompletedSound) this.elements.workCompletedSound.value = settings.pomodoroWorkCompletedSound || 'Bell Meditation';
            if (this.elements.endBreakSound) this.elements.endBreakSound.value = settings.pomodoroEndBreakSound || 'Celestial Gong';
            if (this.elements.tickingSound) this.elements.tickingSound.value = settings.pomodoroTickingSound || 'Clock Ticking 1';
            
            if (this.elements.completionNotifications) this.elements.completionNotifications.checked = settings.pomodoroCompletionNotifications !== false;
            if (this.elements.notifyPosition) this.elements.notifyPosition.value = settings.pomodoroNotifyPosition || 'br';
            if (this.elements.chronometerSound) this.elements.chronometerSound.checked = settings.pomodoroChronometerSound !== false;
            if (this.elements.chronometerOnBreak) this.elements.chronometerOnBreak.checked = settings.pomodoroChronometerOnBreak || false;
            
            if (this.elements.chronometerFrequency) this.elements.chronometerFrequency.value = settings.pomodoroChronometerFrequency || 2;
            
            // Load and update volume sliders
            const notificationVolume = settings.pomodoroNotificationVolume || 70;
            const chronometerVolume = settings.pomodoroChronometerVolume || 30;
            
            if (this.elements.notificationVolume) {
                this.elements.notificationVolume.value = notificationVolume;
                if (this.elements.notificationVolumeValue) this.elements.notificationVolumeValue.textContent = notificationVolume + '%';
            }
            
            if (this.elements.chronometerVolume) {
                this.elements.chronometerVolume.value = chronometerVolume;
                if (this.elements.chronometerVolumeValue) this.elements.chronometerVolumeValue.textContent = chronometerVolume + '%';
            }
            
            // Load display settings
            if (this.elements.todoDisplayCount) this.elements.todoDisplayCount.value = settings.pomodoroTodoDisplayCount || '2';
            
            // Load website blocking from storage (read-only)
            try {
                const blockedSitesResult = await chrome.storage.local.get(['blockedSites']);
                const blockedSitesArray = blockedSitesResult.blockedSites || ['facebook.com', 'twitter.com', 'instagram.com', 'youtube.com', 'reddit.com', 'tiktok.com'];
                if (this.elements.blockedSites) {
                    this.elements.blockedSites.value = blockedSitesArray.join('\n');
                }
                
                const allowedUrlsResult = await chrome.storage.local.get(['allowedUrls']);
                const allowedUrlsArray = allowedUrlsResult.allowedUrls || ['google.com', 'maps.google.com', 'github.com'];
                if (this.elements.whitelistedSites) {
                    this.elements.whitelistedSites.value = allowedUrlsArray.join('\n');
                }
            } catch (error) {
                console.error('PomodoroPopup: Error loading site blocking settings (read-only):', error);
                // Fallback to defaults
                if (this.elements.blockedSites) this.elements.blockedSites.value = 'facebook.com\ntwitter.com\ninstagram.com\nyoutube.com\nreddit.com\ntiktok.com';
                if (this.elements.whitelistedSites) this.elements.whitelistedSites.value = 'google.com\nmaps.google.com\ngithub.com';
            }
            
            console.log('🔒 POMODORO LOAD: Read-only mode completed - no storage writes performed');
        } catch (error) {
            console.error('PomodoroPopup: Error loading settings in read-only mode:', error);
        } finally {
            // Clear the loading flag to allow future calls
            this.isLoadingSettings = false;
        }
    }

    /**
     * Refresh timer display and sync with Work Duration setting when idle
     */
    async refreshTimerDisplay() {
        // Only refresh if timer is idle (not running)
        if (this.timerState.currentState === 'idle') {
            console.log('PomodoroPopup: Refreshing timer display with new settings');
            
            // Sync timer time with current Work Duration setting
            try {
                const result = await chrome.storage.local.get(['gmbExtractorSettings']);
                const settings = result.gmbExtractorSettings || {};
                const workDurationMinutes = parseInt(settings.pomodoroWorkDuration) || 25;
                
                // Update timer time to match Work Duration setting
                this.timerState.timeRemaining = workDurationMinutes * 60; // Convert to seconds
                
                console.log(`PomodoroPopup: Synced timer to Work Duration setting: ${workDurationMinutes} minutes`);
                
                // Save updated state to storage
                await chrome.storage.local.set({
                    pomodoroTimerState: {
                        currentState: this.timerState.currentState,
                        timeRemaining: this.timerState.timeRemaining,
                        isPaused: this.timerState.isPaused,
                        totalCycles: this.timerState.totalCycles,
                        currentCycle: this.timerState.currentCycle,
                        currentMode: 'standard',
                        sessionCount: this.timerState.sessionCount,
                        lastUpdate: Date.now()
                    }
                });
                
            } catch (error) {
                console.error('PomodoroPopup: Error syncing timer with Work Duration setting:', error);
            }
            
            this.updateTimerDisplay();
        }
    }

    /**
     * Force clear all Pomodoro timer cache from storage
     */
    async forceClearTimerCache() {
        try {
            console.log('🧹 PomodoroPopup: Force clearing ALL timer cache...');
            
            // Clear all Pomodoro-related keys from local storage
            const pomodoroKeys = [
                'pomodoroWorkDuration', 'pomodoroShortBreak', 'pomodoroLongBreak',
                'pomodoroNumberOfCycles', 'pomodoroWorkCompletedSound', 'pomodoroEndBreakSound',
                'pomodoroCompletionNotifications', 'pomodoroNotificationVolume', 'pomodoroChronometerSound',
                'pomodoroChronometerOnBreak', 'pomodoroChronometerFrequency', 'pomodoroTickingSound',
                'pomodoroChronometerVolume', 'pomodoroTodoDisplayCount', 'pomodoroBlockingEnabled',
                'pomodoroNotifyPosition', 'pomodoroCurrentState', 'pomodoroAudioSettings'
            ];
            
            // Get current settings and remove all Pomodoro keys
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            
            // Remove all Pomodoro keys from settings
            pomodoroKeys.forEach(key => {
                delete settings[key];
            });
            
            // Save cleaned settings back
            await chrome.storage.local.set({ gmbExtractorSettings: settings });
            
            // Also clear any standalone timer state
            await chrome.storage.local.remove([
                'pomodoroCurrentState', 
                'pomodoroAudioSettings',
                'blockedSites', 
                'allowedUrls'
            ]);
            
            console.log('✅ PomodoroPopup: Timer cache completely cleared');
        } catch (error) {
            console.error('❌ PomodoroPopup: Error clearing timer cache:', error);
        }
    }

    /**
     * Debounced settings propagation to prevent multiple rapid messages
     */
    debouncedSettingsPropagation() {
        // Clear any existing timer
        if (this.settingsDebounceTimer) {
            clearTimeout(this.settingsDebounceTimer);
        }
        
        // Set new timer - only propagate after user stops making changes
        this.settingsDebounceTimer = setTimeout(async () => {
            await this.propagateSettingsQuietly();
        }, 1000); // 1 second delay
    }
    
    /**
     * Quiet settings propagation that doesn't interfere with timer operations
     */
    async propagateSettingsQuietly() {
        try {
            console.log('🔄 PomodoroPopup: Quietly propagating settings changes...');
            
            // Only reload our own settings - don't force background reload
            await this.loadAllSettings();
            
            // Update display
            this.updateDisplay();
            
            // Refresh timer display
            this.refreshTimerDisplay();
            
            console.log('✅ PomodoroPopup: Settings propagated quietly');
        } catch (error) {
            console.error('❌ PomodoroPopup: Error in quiet propagation:', error);
        }
    }
    
    /**
     * Immediate propagation for critical changes only (like reset to defaults)
     */
    async propagateSettingsImmediately() {
        try {
            console.log('🔄 PomodoroPopup: Propagating CRITICAL settings changes immediately...');
            
            // Notify background script to reload timer settings
            await this.sendMessageWithErrorHandling({
                action: 'pomodoroForceReloadSettings'
            });
            
            // Reload our own settings
            await this.loadAllSettings();
            
            // Update display immediately
            this.updateDisplay();
            
            // Refresh timer display
            this.refreshTimerDisplay();
            
            console.log('✅ PomodoroPopup: Critical settings propagated immediately');
        } catch (error) {
            console.error('❌ PomodoroPopup: Error propagating critical settings:', error);
            
            // If propagation fails, offer to reload extension
            await this.offerExtensionReload('Settings propagation failed. Reload extension to ensure changes take effect?');
        }
    }

    /**
     * Offer to reload the extension when needed
     */
    async offerExtensionReload(message = 'Some changes may require an extension reload. Reload now?') {
        try {
            const userConfirmed = confirm(message);
            
            if (userConfirmed) {
                console.log('🔄 PomodoroPopup: User confirmed extension reload');
                
                // Send reload message to background
                await this.sendMessageWithErrorHandling({
                    action: 'pomodoroForceExtensionReload'
                });
            } else {
                console.log('🚫 PomodoroPopup: User declined extension reload');
            }
        } catch (error) {
            console.error('❌ PomodoroPopup: Error offering extension reload:', error);
        }
    }

    /**
     * Test function to verify settings persistence (for debugging)
     */
    async testSettingsPersistence() {
        try {
            console.log('🧪 POMODORO TEST: Starting settings persistence test...');
            
            // Test 1: Verify current settings in storage
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            
            console.log('🧪 POMODORO TEST: Current settings in storage:', {
                workDuration: settings.pomodoroWorkDuration,
                shortBreak: settings.pomodoroShortBreak,
                longBreak: settings.pomodoroLongBreak,
                numberOfCycles: settings.pomodoroNumberOfCycles
            });
            
            // Test 2: Verify HTML form values match storage
            const formValues = {
                workDuration: this.elements.workDuration?.value,
                shortBreak: this.elements.shortBreak?.value,
                longBreak: this.elements.longBreak?.value,
                numberOfCycles: this.elements.numberOfCycles?.value
            };
            
            console.log('🧪 POMODORO TEST: Current form values:', formValues);
            
            // Test 3: Check for mismatches
            let hasMismatch = false;
            if (settings.pomodoroWorkDuration != formValues.workDuration) {
                console.warn('🧪 POMODORO TEST: Work duration mismatch!', {
                    storage: settings.pomodoroWorkDuration,
                    form: formValues.workDuration
                });
                hasMismatch = true;
            }
            
            if (settings.pomodoroShortBreak != formValues.shortBreak) {
                console.warn('🧪 POMODORO TEST: Short break mismatch!', {
                    storage: settings.pomodoroShortBreak,
                    form: formValues.shortBreak
                });
                hasMismatch = true;
            }
            
            if (settings.pomodoroLongBreak != formValues.longBreak) {
                console.warn('🧪 POMODORO TEST: Long break mismatch!', {
                    storage: settings.pomodoroLongBreak,
                    form: formValues.longBreak
                });
                hasMismatch = true;
            }
            
            if (settings.pomodoroNumberOfCycles != formValues.numberOfCycles) {
                console.warn('🧪 POMODORO TEST: Number of cycles mismatch!', {
                    storage: settings.pomodoroNumberOfCycles,
                    form: formValues.numberOfCycles
                });
                hasMismatch = true;
            }
            
            if (hasMismatch) {
                console.error('🧪 POMODORO TEST: FAILED - Settings persistence has issues!');
                return false;
            } else {
                console.log('🧪 POMODORO TEST: PASSED - Settings persistence working correctly!');
                return true;
            }
            
        } catch (error) {
            console.error('🧪 POMODORO TEST: Error during persistence test:', error);
            return false;
        }
    }

    /**
     * Automatic extension reload for critical changes (respects timer operations)
     */
    async autoReloadExtensionIfNeeded() {
        try {
            console.log('🔄 PomodoroPopup: Checking if extension reload is needed...');
            
            // NEVER reload if timer is running
            if (this.timerState.currentState !== 'idle') {
                console.log('🚫 PomodoroPopup: Skipping auto-reload - timer is running');
                return;
            }
            
            // Check if we've just reset to defaults (critical change)
            const now = Date.now();
            const lastResetTime = this.lastResetTime || 0;
            
            if (now - lastResetTime < 5000) { // Within 5 seconds of reset
                console.log('🔄 PomodoroPopup: Recent reset detected, offering extension reload...');
                
                // Ask user before reloading (don't force it)
                const userWantsReload = confirm('Settings have been reset. Reload extension to ensure all changes take effect?');
                
                if (userWantsReload) {
                    // Show brief notification
                    if (this.elements.resetDefaults) {
                        this.elements.resetDefaults.textContent = 'Reloading Extension...';
                    }
                    
                    // Reload after short delay
                    setTimeout(async () => {
                        await this.sendMessageWithErrorHandling({
                            action: 'pomodoroForceExtensionReload'
                        });
                    }, 1000);
                } else {
                    console.log('🚫 PomodoroPopup: User declined extension reload');
                }
            }
        } catch (error) {
            console.error('❌ PomodoroPopup: Error in auto-reload check:', error);
        }
    }

    /**
     * Show visual feedback for reset completion
     */
    showResetFeedback() {
        if (this.elements.resetDefaults) {
            const originalText = this.elements.resetDefaults.textContent;
            const originalColor = this.elements.resetDefaults.style.background;
            
            // Show success feedback
            this.elements.resetDefaults.textContent = 'Reset Complete!';
            this.elements.resetDefaults.style.background = '#22c55e';
            this.elements.resetDefaults.disabled = true;
            
            // Restore original state after 2 seconds
            setTimeout(() => {
                this.elements.resetDefaults.textContent = originalText;
                this.elements.resetDefaults.style.background = originalColor;
                this.elements.resetDefaults.disabled = false;
            }, 2000);
        }
    }

    /**
     * Reset timer configuration to standard Pomodoro defaults with comprehensive cache clearing
     */
    async resetToDefaults() {
        try {
            console.log('🔄 PomodoroPopup: Starting comprehensive reset to defaults...');
            
            // Step 1: Force clear all cached timer values
            await this.forceClearTimerCache();
            
            // Step 2: Use the same defaults as settings.js - single source of truth
            const defaults = {
                pomodoroWorkDuration: 25,
                pomodoroShortBreak: 5,
                pomodoroLongBreak: 15,
                pomodoroNumberOfCycles: 8,
                pomodoroWorkCompletedSound: 'Bell Meditation',
                pomodoroEndBreakSound: 'Celestial Gong',
                pomodoroCompletionNotifications: true,
                pomodoroNotificationVolume: 70,
                pomodoroChronometerSound: true,
                pomodoroChronometerOnBreak: false,
                pomodoroChronometerFrequency: 2,
                pomodoroTickingSound: 'Clock Ticking 1',
                pomodoroChronometerVolume: 30,
                pomodoroTodoDisplayCount: 3,
                pomodoroBlockingEnabled: true
            };
            
            // Step 3: Update input fields immediately
            if (this.elements.workDuration) this.elements.workDuration.value = defaults.pomodoroWorkDuration;
            if (this.elements.shortBreak) this.elements.shortBreak.value = defaults.pomodoroShortBreak;
            if (this.elements.longBreak) this.elements.longBreak.value = defaults.pomodoroLongBreak;
            if (this.elements.numberOfCycles) this.elements.numberOfCycles.value = defaults.pomodoroNumberOfCycles;
            
            // Step 3.5: Manually trigger the save logic for each field (since programmatic value changes don't trigger events)
            await this.saveSettingToStorage('pomodoroWorkDuration', defaults.pomodoroWorkDuration);
            await this.saveSettingToStorage('pomodoroShortBreak', defaults.pomodoroShortBreak);
            await this.saveSettingToStorage('pomodoroLongBreak', defaults.pomodoroLongBreak);
            await this.saveSettingToStorage('pomodoroNumberOfCycles', defaults.pomodoroNumberOfCycles);
            
            // Step 4: Force save all defaults to storage (comprehensive)
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            
            // Force overwrite with defaults
            Object.assign(settings, defaults);
            
            await chrome.storage.local.set({ gmbExtractorSettings: settings });
            
            // Step 5: Apply the same UI refresh logic that manual typing triggers
            console.log('🔄 PomodoroPopup: Applying UI refresh logic like manual typing...');
            
            // Refresh timer display to sync with new work duration (like manual typing does)
            this.refreshTimerDisplay();
            
            // Update timer display for cycles changes (like manual typing does)
            this.updateTimerDisplay();
            
            // Use debounced settings propagation for proper sync (like manual typing does)
            this.debouncedSettingsPropagation();
            
            // Step 6: Force timer restart regardless of background change detection logic
            console.log('🔄 PomodoroPopup: Forcing timer restart after defaults reset...');
            await this.sendMessageWithErrorHandling({
                action: 'pomodoroForceTimerRestart',
                reason: 'resetDefaults'
            });
            
            // Step 7: Show success feedback without reload
            this.showResetFeedback();
            
            console.log('✅ PomodoroPopup: Timer configuration reset to defaults successfully:', defaults);
        } catch (error) {
            console.error('❌ PomodoroPopup: Error resetting timer configuration:', error);
        }
    }

    /**
     * Notify the header toggle button about timer state changes
     */
    notifyToggleButtonStateChange(state) {
        try {
            // Send message to popup.js to update toggle button state
            const event = new CustomEvent('pomodoroStateChange', {
                detail: { state: state }
            });
            document.dispatchEvent(event);
            console.log('PomodoroPopup: Dispatched state change event:', state);
        } catch (error) {
            console.warn('PomodoroPopup: Could not dispatch state change event:', error);
        }
    }

}

// Initialize when DOM is ready - with guard to prevent re-initialization
if (!window.pomodoroPopup) {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            if (!window.pomodoroPopup) {
                window.pomodoroPopup = new PomodoroPopup();
            }
        });
    } else {
        window.pomodoroPopup = new PomodoroPopup();
    }
}