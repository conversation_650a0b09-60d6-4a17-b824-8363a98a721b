/**
 * Offscreen Audio Handler - Advanced Audio Implementation
 * Clean implementation with NO backward compatibility
 */

// Audio system variables
let n = null;  // notification audio
let c = false; // isNotificationPlaying 
let o = null;  // background music

// Audio message listener
chrome.runtime.onMessage.addListener(i => {
    console.log('Offscreen Audio: Received message:', i);
    
    // playSound for notification/preview sounds
    i.action === "playSound" && (i != null && i.selectedSound) && (i != null && i.isSoundEnabled) && m(i);
    
    // playMusic for background music  
    i.action === "playMusic" && (i != null && i.selectedMusic) && (i != null && i.isMusicEnabled) && d(i).catch(console.error);
    
    // stopMusic
    i.action === "stopMusic" && a();
    
    // adjust-music-volume for real-time volume changes
    i.action === "adjust-music-volume" && o && i.musicVolume !== void 0 && (o.volume = i.musicVolume);
    
    // adjust-notification-volume for notification sounds
    i.action === "adjust-notification-volume" && n && i.notificationVolume !== void 0 && (n.volume = i.notificationVolume);
    
    // toggle-music
    i.action === "toggle-music" && (i.isMusicEnabled && o && i.isRunning ? o.play() : o == null || o.pause());
    
    // music-changed
    i.action === "music-changed" && i.selectedMusic && d(i).catch(console.error);
});

// playSound function
const m = i => {
    c && n && (n.pause(), n.currentTime = 0, c = !1);
    n = new Audio(i.selectedSound);
    (i == null ? void 0 : i.soundVolume) !== void 0 && (n.volume = i.soundVolume);
    n.play().then(() => {
        c = !0;
    }).catch(l => {
        console.error("Error playing soundAudio:", l);
        c = !1;
    });
    n.addEventListener("ended", () => {
        c = !1;
    });
};

// playMusic function
const d = async i => {
    try {
        if (a(), !i.selectedMusic || !i.isMusicEnabled) return;
        o = new Audio(i.selectedMusic);
        o.loop = !0;
        (i == null ? void 0 : i.musicVolume) !== void 0 && (o.volume = i.musicVolume);
        (i.isRunning || i.action === "playMusic") && await o.play();
    } catch (l) {
        console.error("Error playing background music:", l);
        a();
    }
};

// stopMusic function  
const a = () => {
    try {
        o && (o.pause(), o.currentTime = 0, o = null);
    } catch (i) {
        console.error("Error stopping music:", i);
    }
};

console.log('Advanced audio handler loaded (clean version)');