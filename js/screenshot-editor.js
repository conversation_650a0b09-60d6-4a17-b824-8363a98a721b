// STM Screenshot Editor - Standalone Implementation
class ScreenshotEditor {
    constructor() {
        this.canvas = document.getElementById('screenshot-canvas');
        this.ctx = this.canvas.getContext('2d', { willReadFrequently: true });
        this.loading = document.getElementById('loading');
        
        this.currentTool = 'arrow';
        this.currentColor = '#dc2626';
        this.currentStrokeWidth = 8;
        
        // Canvas and export settings
        this.defaultWidth = 1920;
        
        this.drawings = [];
        this.currentDrawing = null;
        this.isDrawing = false;
        this.startPos = null;
        
        this.screenshotImage = null;
        this.originalImageData = null; // Store original image data for blur restoration
        
        // Universal selection system
        this.selectedObject = null;
        this.isDraggingObject = false;
        this.isRotatingObject = false;
        this.dragOffset = null;
        this.rotationStartAngle = 0;
        this.rotationStartMouseAngle = 0;
        this.objectIdCounter = 0;
        this.activeTextInput = null;
        
        this.init();
    }
    
    async init() {
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Initialize stroke value display
        this.updateStrokeValue();
        
        // Load screenshot data
        await this.loadScreenshot();
    }
    
    async loadScreenshot() {
        try {
            // Load from storage (primary method following Handy Screenshot approach)
            const result = await chrome.storage.local.get(['screenshotData', 'screenshotTimestamp', 'selectionRect', 'devicePixelRatio']);
            
            if (result.screenshotData) {
                let finalImageData = result.screenshotData;
                
                // Check if we have selection coordinates (crop needed)
                if (result.selectionRect && result.devicePixelRatio) {
                    finalImageData = await this.cropImage(result.screenshotData, result.selectionRect, result.devicePixelRatio);
                }
                
                await this.displayScreenshot(finalImageData);
                
                // Auto-copy to clipboard (Handy Screenshot approach)
                await this.autoCopyToClipboard(finalImageData);
                
                // Clear the storage after loading
                chrome.storage.local.remove(['screenshotData', 'screenshotTimestamp', 'selectionRect', 'devicePixelRatio']);
                return;
            }
            
            // Fallback: Try clipboard
            const clipboardSuccess = await this.loadFromClipboard();
            
            if (clipboardSuccess) {
                return;
            }
            
            // Final fallback: Try URL parameter
            const urlParams = new URLSearchParams(window.location.search);
            const dataUrl = urlParams.get('screenshot');
            
            if (dataUrl) {
                await this.displayScreenshot(decodeURIComponent(dataUrl));
            } else {
                throw new Error('No screenshot data found in storage, clipboard, or URL');
            }
        } catch (error) {
            console.error('Screenshot Editor: Error loading screenshot:', error);
            this.showError('Failed to load screenshot data. Please try capturing again.');
        }
    }
    
    async autoCopyToClipboard(dataUrl) {
        try {
            
            // Convert data URL to blob
            const response = await fetch(dataUrl);
            const blob = await response.blob();
            
            // Copy to clipboard
            await navigator.clipboard.write([
                new ClipboardItem({
                    'image/png': blob
                })
            ]);
            
            this.showNotification('Screenshot ready to edit and copied to clipboard!', 'success');
            
        } catch (error) {
            console.error('Screenshot Editor: Auto-copy to clipboard failed:', error);
            // Don't show error notification for this - it's just a convenience feature
        }
    }
    
    async loadFromClipboard() {
        try {
            // Check if clipboard API is available
            if (!navigator.clipboard || !navigator.clipboard.read) {
                return false;
            }
            
            // Read from clipboard
            const clipboardItems = await navigator.clipboard.read();
            
            for (const clipboardItem of clipboardItems) {
                // Look for image data
                for (const type of clipboardItem.types) {
                    if (type.startsWith('image/')) {
                        
                        const blob = await clipboardItem.getType(type);
                        
                        // Convert blob to data URL
                        const reader = new FileReader();
                        
                        return new Promise((resolve) => {
                            reader.onload = async (e) => {
                                try {
                                    await this.displayScreenshot(e.target.result);
                                    resolve(true);
                                } catch (error) {
                                    console.error('Screenshot Editor: Error displaying clipboard image:', error);
                                    resolve(false);
                                }
                            };
                            
                            reader.onerror = () => {
                                console.error('Screenshot Editor: Error reading clipboard blob');
                                resolve(false);
                            };
                            
                            reader.readAsDataURL(blob);
                        });
                    }
                }
            }
            
            // No image found in clipboard
            return false;
            
        } catch (error) {
            return false;
        }
    }
    
    async displayScreenshot(dataUrl) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            
            img.onload = () => {
                
                const dpr = window.devicePixelRatio || 1;
                
                // Image data is now correctly sized from simple-area-selector.js (no DPI over-scaling)
                const actualWidth = img.width;
                const actualHeight = img.height;
                
                // Calculate display dimensions (respect defaultWidth setting, maintain aspect ratio)
                const aspectRatio = actualHeight / actualWidth;
                const displayWidth = Math.min(actualWidth, this.defaultWidth);
                const displayHeight = displayWidth * aspectRatio;
                
                // Set canvas internal dimensions to match display size (NOT scaled up)
                this.canvas.width = displayWidth;
                this.canvas.height = displayHeight;
                
                // Set CSS display size to match canvas size
                this.canvas.style.width = displayWidth + 'px';
                this.canvas.style.height = displayHeight + 'px';
                
                // Draw image directly at canvas size (no scaling)
                this.ctx.drawImage(img, 0, 0, displayWidth, displayHeight);
                
                // Store scaling factors for coordinate transformation
                this.scaleX = actualWidth / displayWidth;
                this.scaleY = actualHeight / displayHeight;
                this.offsetX = 0;
                this.offsetY = 0;
                this.devicePixelRatio = dpr;
                
                // Canvas scaling factors calculated
                
                // Store reference for later use
                this.screenshotImage = img;
                
                // Store original image data for blur restoration
                this.originalImageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
                
                // Show canvas and hide loading
                this.loading.style.display = 'none';
                this.canvas.style.display = 'block';
                
                // Ready for editing
                resolve();
            };
            
            img.onerror = () => {
                console.error('Screenshot Editor: Failed to load image');
                reject(new Error('Failed to load screenshot image'));
            };
            
            img.src = dataUrl;
        });
    }
    
    updateCanvasSize() {
        if (!this.screenshotImage) return;
        
        // Updating canvas size
        
        const actualWidth = this.screenshotImage.width;
        const actualHeight = this.screenshotImage.height;
        
        // Calculate new display dimensions
        const aspectRatio = actualHeight / actualWidth;
        const displayWidth = Math.min(actualWidth, this.defaultWidth);
        const displayHeight = displayWidth * aspectRatio;
        
        // Update canvas dimensions
        this.canvas.width = displayWidth;
        this.canvas.height = displayHeight;
        this.canvas.style.width = displayWidth + 'px';
        this.canvas.style.height = displayHeight + 'px';
        
        // Update scaling factors
        this.scaleX = actualWidth / displayWidth;
        this.scaleY = actualHeight / displayHeight;
        
        // Store updated original image data
        this.originalImageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        
        // Redraw everything with new dimensions
        this.redraw();
        
        // Canvas resized
    }
    
    // HANDY SCREENSHOT APPROACH: Crop full screenshot to selected area with proper devicePixelRatio handling
    async cropImage(fullImageDataUrl, selectionRect, devicePixelRatio) {
        return new Promise((resolve, reject) => {
            // Cropping image with Handy Screenshot approach
            
            const img = new Image();
            
            img.onload = () => {
                try {
                    // Create temporary canvas for cropping
                    const tempCanvas = document.createElement('canvas');
                    const tempCtx = tempCanvas.getContext('2d');
                    
                    // HANDY SCREENSHOT WAY: Canvas size = logical selection size (not scaled)
                    tempCanvas.width = selectionRect.width;
                    tempCanvas.height = selectionRect.height;
                    
                    // HANDY SCREENSHOT WAY: Source coordinates are scaled by devicePixelRatio
                    const sourceX = selectionRect.left * devicePixelRatio;
                    const sourceY = selectionRect.top * devicePixelRatio;
                    const sourceWidth = selectionRect.width * devicePixelRatio;
                    const sourceHeight = selectionRect.height * devicePixelRatio;
                    
                    // Draw the cropped portion
                    tempCtx.drawImage(
                        img,
                        sourceX,                    // Source x (device pixels)
                        sourceY,                    // Source y (device pixels)
                        sourceWidth,                // Source width (device pixels)
                        sourceHeight,               // Source height (device pixels)
                        0,                          // Destination x
                        0,                          // Destination y
                        selectionRect.width,        // Destination width (logical pixels)
                        selectionRect.height        // Destination height (logical pixels)
                    );
                    
                    const croppedDataUrl = tempCanvas.toDataURL('image/png');
                    resolve(croppedDataUrl);
                    
                } catch (error) {
                    console.error('Screenshot Editor: Error cropping image:', error);
                    reject(error);
                }
            };
            
            img.onerror = () => {
                console.error('Screenshot Editor: Failed to load image for cropping');
                reject(new Error('Failed to load image'));
            };
            
            img.src = fullImageDataUrl;
        });
    }
    
    setupEventListeners() {
        // Tool selection
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tool = e.target.dataset.tool;
                if (tool) {
                    this.selectTool(tool);
                }
            });
        });
        
        // Color and stroke controls with live text updates
        document.getElementById('color-picker').addEventListener('change', (e) => {
            this.currentColor = e.target.value;
            // Update selected object color in real-time
            if (this.selectedObject) {
                this.selectedObject.color = this.currentColor;
                this.redraw();
            }
        });
        
        document.getElementById('stroke-width').addEventListener('input', (e) => {
            this.currentStrokeWidth = parseInt(e.target.value);
            this.updateStrokeValue();
            // Update selected object properties in real-time
            if (this.selectedObject) {
                if (this.selectedObject.tool === 'text') {
                    this.selectedObject.fontSize = this.currentStrokeWidth * 4;
                    this.selectedObject.padding = this.currentStrokeWidth * 2;
                } else {
                    this.selectedObject.strokeWidth = this.currentStrokeWidth;
                }
                this.redraw();
            }
        });
        
        // Stroke control buttons
        document.getElementById('increase-stroke').addEventListener('click', () => {
            this.increaseStroke();
        });
        
        document.getElementById('decrease-stroke').addEventListener('click', () => {
            this.decreaseStroke();
        });
        
        // Width and quality controls
        document.getElementById('width-select').addEventListener('change', (e) => {
            this.defaultWidth = parseInt(e.target.value);
            this.updateCanvasSize();
        });
        
        
        // Action buttons
        document.getElementById('undo-btn').addEventListener('click', () => this.undo());
        document.getElementById('copy-btn').addEventListener('click', () => this.copyToClipboard());
        document.getElementById('save-btn').addEventListener('click', () => this.saveScreenshot());
        document.getElementById('close-btn').addEventListener('click', () => this.close());
        
        // Canvas drawing events
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.canvas.addEventListener('dblclick', (e) => this.handleDoubleClick(e));
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
    }
    
    selectTool(toolName) {
        // Update UI
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tool="${toolName}"]`).classList.add('active');
        
        // Update state
        this.currentTool = toolName;
        
        // Set appropriate cursor
        if (toolName === 'text') {
            this.canvas.style.cursor = 'text';
        } else if (toolName === 'blur' || toolName === 'pixelate') {
            this.canvas.style.cursor = 'cell';
        } else {
            this.canvas.style.cursor = 'crosshair';
        }
        
        // Tool selected
    }
    
    handleMouseDown(e) {
        if (!this.currentTool) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        // Simple coordinate transformation (no complex offset handling needed)
        this.startPos = {
            x: mouseX * (this.scaleX || 1),
            y: mouseY * (this.scaleY || 1)
        };
        
        // Check for rotation handle click first
        if (this.selectedObject) {
            const rotationHandlePos = this.getRotationHandlePosition(this.selectedObject);
            const distToHandle = Math.sqrt(
                Math.pow(this.startPos.x - rotationHandlePos.x, 2) + 
                Math.pow(this.startPos.y - rotationHandlePos.y, 2)
            );
            
            if (distToHandle <= 12) { // Rotation handle hit
                this.isRotatingObject = true;
                const center = this.getObjectCenter(this.selectedObject);
                this.rotationStartAngle = this.selectedObject.rotation || 0;
                this.rotationStartMouseAngle = Math.atan2(
                    this.startPos.y - center.y,
                    this.startPos.x - center.x
                );
                return; // Don't start drawing
            }
        }
        
        // Check for any object interaction (for any tool)
        const clickedObject = this.getObjectAt(this.startPos.x, this.startPos.y);
        
        if (clickedObject) {
            // Clicked on an existing object
            this.selectObject(clickedObject);
            this.isDraggingObject = true;
            
            // Calculate drag offset based on object type
            if (clickedObject.tool === 'text') {
                this.dragOffset = {
                    x: this.startPos.x - clickedObject.x,
                    y: this.startPos.y - clickedObject.y
                };
            } else {
                // For other objects, use startPos as reference point
                this.dragOffset = {
                    x: this.startPos.x - clickedObject.startPos.x,
                    y: this.startPos.y - clickedObject.startPos.y
                };
            }
            return; // Don't start drawing
        }
        
        // Clear selection if clicking elsewhere
        if (this.selectedObject) {
            this.deselectAllObjects();
        }
        
        this.isDrawing = true;
        
        // Initialize drawing object
        this.currentDrawing = {
            tool: this.currentTool,
            color: this.currentColor,
            strokeWidth: this.currentStrokeWidth,
            startPos: {...this.startPos},
            endPos: {...this.startPos},
            rotation: 0
        };
        
        if (this.currentTool === 'text') {
            this.handleTextInput(this.startPos);
            this.isDrawing = false;
        }
    }
    
    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        const currentPos = {
            x: mouseX * (this.scaleX || 1),
            y: mouseY * (this.scaleY || 1)
        };
        
        // Handle object rotation
        if (this.isRotatingObject && this.selectedObject) {
            const center = this.getObjectCenter(this.selectedObject);
            const currentMouseAngle = Math.atan2(
                currentPos.y - center.y,
                currentPos.x - center.x
            );
            
            const angleChange = currentMouseAngle - this.rotationStartMouseAngle;
            this.selectedObject.rotation = this.rotationStartAngle + angleChange;
            
            this.redraw();
            return;
        }
        
        // Handle object dragging
        if (this.isDraggingObject && this.selectedObject) {
            if (this.selectedObject.tool === 'text') {
                // Move text objects
                this.selectedObject.x = currentPos.x - this.dragOffset.x;
                this.selectedObject.y = currentPos.y - this.dragOffset.y;
            } else {
                // Move other objects by updating their start and end positions
                const deltaX = currentPos.x - this.dragOffset.x - this.selectedObject.startPos.x;
                const deltaY = currentPos.y - this.dragOffset.y - this.selectedObject.startPos.y;
                
                this.selectedObject.startPos.x += deltaX;
                this.selectedObject.startPos.y += deltaY;
                this.selectedObject.endPos.x += deltaX;
                this.selectedObject.endPos.y += deltaY;
            }
            this.redraw();
            return;
        }
        
        // Handle drawing operations
        if (!this.isDrawing || !this.currentDrawing) {
            // Update cursor based on what's under mouse
            this.updateCursor(currentPos);
            return;
        }
        
        // Simple coordinate transformation (no complex offset handling needed)
        this.currentDrawing.endPos = {
            x: currentPos.x,
            y: currentPos.y
        };
        
        this.redraw();
    }
    
    handleMouseUp(e) {
        // Handle rotation completion
        if (this.isRotatingObject) {
            this.isRotatingObject = false;
            this.rotationStartAngle = 0;
            this.rotationStartMouseAngle = 0;
            return;
        }
        
        // Handle object dragging completion
        if (this.isDraggingObject) {
            this.isDraggingObject = false;
            this.dragOffset = null;
            return;
        }
        
        if (!this.isDrawing || !this.currentDrawing) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        // Simple coordinate transformation (no complex offset handling needed)
        this.currentDrawing.endPos = {
            x: mouseX * (this.scaleX || 1),
            y: mouseY * (this.scaleY || 1)
        };
        
        // Add to drawings array for all tools and assign ID
        this.currentDrawing.id = 'obj_' + (++this.objectIdCounter);
        this.drawings.push(this.currentDrawing);
        
        // Auto-select the newly created object for immediate manipulation
        this.selectObject(this.currentDrawing);
        
        this.isDrawing = false;
        this.currentDrawing = null;
        
        // Redraw canvas for all tools
        this.redraw();
    }
    
    handleKeyDown(e) {
        // Allow stroke shortcuts even when text input is active (they don't interfere with typing)
        if (e.key === '-' || e.key === '=') {
            console.log(`Screenshot Editor: Stroke shortcut pressed: ${e.key}, current stroke: ${this.currentStrokeWidth}`);
            if (e.key === '-') {
                e.preventDefault();
                this.decreaseStroke();
                console.log(`Screenshot Editor: After decrease: ${this.currentStrokeWidth}`);
                return;
            } else if (e.key === '=') {
                e.preventDefault();
                this.increaseStroke();
                console.log(`Screenshot Editor: After increase: ${this.currentStrokeWidth}`);
                return;
            }
        }
        
        // Don't process other keys if text input is active
        if (this.activeTextInput) {
            // Defensive check: verify text input still exists in DOM
            if (!this.activeTextInput.parentNode) {
                this.activeTextInput = null;
            } else {
                return;
            }
        }
        
        // Tool shortcuts (case insensitive)
        const key = e.key.toLowerCase();
        if (key === 'a') {
            e.preventDefault();
            this.selectTool('arrow');
        } else if (key === 'b') {
            e.preventDefault();
            this.selectTool('box');
        } else if (key === 't') {
            e.preventDefault();
            this.selectTool('text');
        } else if (key === 'l') {
            e.preventDefault();
            this.selectTool('line');
        } else if (key === 'p') {
            e.preventDefault();
            this.selectTool('pixelate');
        } else if (key === 'r') {
            e.preventDefault();
            this.selectTool('blur');
        } else if (e.key === 'Escape') {
            this.close();
        } else if (e.key === 'Delete' || e.key === 'Backspace') {
            // Delete selected object
            if (this.selectedObject) {
                e.preventDefault();
                this.removeObject(this.selectedObject);
            }
        // Note: stroke shortcuts (- and =) are now handled at the top of this function
        } else if (e.ctrlKey || e.metaKey) {
            if (e.key === 'z') {
                e.preventDefault();
                this.undo();
            } else if (e.key === 's') {
                e.preventDefault();
                this.saveScreenshot();
            } else if (e.key === 'c') {
                e.preventDefault();
                this.copyToClipboard();
            }
        }
    }
    
    handleTextInput(pos) {
        // Create in-place text input for direct canvas editing
        this.createInPlaceTextInput(pos);
    }
    
    createInPlaceTextInput(pos, existingText = null) {
        // Remove any existing text input
        this.removeActiveTextInput();
        
        const textInput = document.createElement('input');
        textInput.type = 'text';
        textInput.value = existingText ? existingText.text : '';
        textInput.placeholder = 'Type text here...';
        
        // Calculate position - convert canvas coordinates to screen coordinates
        const rect = this.canvas.getBoundingClientRect();
        const fontSize = existingText ? existingText.fontSize : (this.currentStrokeWidth * 4);
        const color = existingText ? existingText.color : this.currentColor;
        
        // Position input at canvas coordinates
        const screenX = rect.left + (pos.x / (this.scaleX || 1));
        const screenY = rect.top + (pos.y / (this.scaleY || 1)) - fontSize; // Adjust for text baseline
        
        // Style the input to match the text appearance
        textInput.style.cssText = `
            position: fixed;
            left: ${screenX}px;
            top: ${screenY}px;
            font-size: ${fontSize}px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto';
            background: ${color};
            color: #FFFFFF;
            border: 2px solid #7C3AED;
            border-radius: 8px;
            padding: ${(this.currentStrokeWidth * 2)}px;
            outline: none;
            z-index: 10000;
            min-width: 100px;
        `;
        
        // Store reference and add to DOM
        this.activeTextInput = textInput;
        document.body.appendChild(textInput);
        
        // Focus and select existing text if editing
        setTimeout(() => {
            textInput.focus();
            if (existingText) {
                textInput.select();
            }
        }, 10);
        
        // Handle completion and cancellation
        const finishText = () => {
            const text = textInput.value.trim();
            
            if (text) {
                if (existingText) {
                    // Update existing text
                    existingText.text = text;
                    existingText.color = this.currentColor;
                    existingText.fontSize = this.currentStrokeWidth * 4;
                    existingText.padding = this.currentStrokeWidth * 2;
                } else {
                    // Create new text object
                    const textObject = {
                        tool: 'text',
                        text: text,
                        color: this.currentColor,
                        fontSize: this.currentStrokeWidth * 4,
                        padding: this.currentStrokeWidth * 2,
                        x: pos.x,
                        y: pos.y,
                        selected: true,
                        rotation: 0,
                        id: 'obj_' + (++this.objectIdCounter)
                    };
                    this.drawings.push(textObject);
                    this.selectedObject = textObject;
                }
                this.redraw();
            } else if (existingText) {
                // Remove empty text
                this.removeObject(existingText);
            }
            
            this.removeActiveTextInput();
        };
        
        // Event listeners
        textInput.addEventListener('blur', finishText);
        textInput.addEventListener('keydown', (e) => {
            // Allow stroke shortcuts (- and =) to pass through
            if (e.key === '-' || e.key === '=') {
                // Don't stop propagation for these keys - let them reach the main handler
                return;
            }
            
            e.stopPropagation(); // Prevent other canvas shortcuts
            
            if (e.key === 'Enter') {
                e.preventDefault();
                finishText();
            } else if (e.key === 'Escape') {
                this.removeActiveTextInput();
                if (!existingText) {
                    this.redraw();
                }
            }
        });
    }
    
    removeActiveTextInput() {
        if (this.activeTextInput) {
            try {
                // Check if the element is still in the DOM before removing
                if (this.activeTextInput.parentNode) {
                    this.activeTextInput.remove();
                }
            } catch (error) {
                // Silently handle - element was already removed or doesn't exist
            }
            // Always clear the reference, regardless of removal success
            this.activeTextInput = null;
        }
    }
    
    removeObject(object) {
        const index = this.drawings.indexOf(object);
        if (index > -1) {
            this.drawings.splice(index, 1);
            if (this.selectedObject === object) {
                this.selectedObject = null;
            }
            this.redraw();
        }
    }
    
    redraw() {
        // Start with original image data
        if (this.originalImageData) {
            this.ctx.putImageData(this.originalImageData, 0, 0);
        } else if (this.screenshotImage) {
            // Fallback: clear and redraw image
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.drawImage(this.screenshotImage, 0, 0, this.canvas.width, this.canvas.height);
        }
        
        // Apply blur and pixelate effects first (they modify the image data)
        this.drawings.forEach(drawing => {
            if (drawing.tool === 'blur') {
                this.applyBlurEffectOnContext(this.ctx, drawing);
            } else if (drawing.tool === 'pixelate') {
                this.applyPixelateEffectOnContext(this.ctx, drawing);
            }
        });
        
        // Draw all other completed drawings on top
        this.drawings.forEach(drawing => {
            if (drawing.tool !== 'blur' && drawing.tool !== 'pixelate') {
                this.drawShape(drawing);
            }
        });
        
        // Draw current drawing if in progress
        if (this.currentDrawing) {
            if (this.currentTool === 'blur') {
                // Show preview during blur drawing
                this.ctx.save();
                this.ctx.strokeStyle = '#7C3AED';
                this.ctx.lineWidth = 2;
                this.ctx.setLineDash([5, 5]);
                const width = this.currentDrawing.endPos.x - this.currentDrawing.startPos.x;
                const height = this.currentDrawing.endPos.y - this.currentDrawing.startPos.y;
                this.ctx.strokeRect(this.currentDrawing.startPos.x, this.currentDrawing.startPos.y, width, height);
                this.ctx.restore();
            } else if (this.currentTool === 'pixelate') {
                // Show preview during pixelate drawing
                this.ctx.save();
                this.ctx.strokeStyle = '#FF8C00';
                this.ctx.lineWidth = 2;
                this.ctx.setLineDash([5, 5]);
                const width = this.currentDrawing.endPos.x - this.currentDrawing.startPos.x;
                const height = this.currentDrawing.endPos.y - this.currentDrawing.startPos.y;
                this.ctx.strokeRect(this.currentDrawing.startPos.x, this.currentDrawing.startPos.y, width, height);
                this.ctx.restore();
            } else {
                this.drawShape(this.currentDrawing);
            }
        }
    }
    
    drawShape(drawing) {
        this.ctx.save();
        this.ctx.strokeStyle = drawing.color;
        this.ctx.lineWidth = drawing.strokeWidth;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
        
        // Add selection indicator for non-text objects
        const isSelected = drawing.selected && this.selectedObject === drawing;
        if (isSelected && drawing.tool !== 'text') {
            this.ctx.strokeStyle = '#7C3AED';
            this.ctx.lineWidth = Math.max(2, drawing.strokeWidth);
            this.ctx.setLineDash([5, 5]);
        } else {
            this.ctx.setLineDash([]);
        }
        
        // Apply rotation transform if object has rotation
        if (drawing.rotation && drawing.rotation !== 0) {
            const center = this.getObjectCenter(drawing);
            this.ctx.translate(center.x, center.y);
            this.ctx.rotate(drawing.rotation);
            this.ctx.translate(-center.x, -center.y);
        }
        
        switch (drawing.tool) {
            case 'line':
                this.ctx.beginPath();
                this.ctx.moveTo(drawing.startPos.x, drawing.startPos.y);
                this.ctx.lineTo(drawing.endPos.x, drawing.endPos.y);
                this.ctx.stroke();
                break;
                
            case 'box':
                const width = drawing.endPos.x - drawing.startPos.x;
                const height = drawing.endPos.y - drawing.startPos.y;
                this.ctx.strokeRect(drawing.startPos.x, drawing.startPos.y, width, height);
                break;
                
            case 'arrow':
                this.drawArrow(drawing.startPos, drawing.endPos);
                break;
                
            case 'text':
                this.ctx.save();
                
                // Apply rotation for text
                if (drawing.rotation && drawing.rotation !== 0) {
                    this.ctx.translate(drawing.x, drawing.y);
                    this.ctx.rotate(drawing.rotation);
                    this.ctx.translate(-drawing.x, -drawing.y);
                }
                
                // Set font for measurement and rendering
                this.ctx.font = `${drawing.fontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto'`;
                
                // Measure text dimensions
                const textMetrics = this.ctx.measureText(drawing.text);
                const textWidth = textMetrics.width;
                const textHeight = drawing.fontSize;
                
                // Calculate background box dimensions with padding
                const padding = drawing.padding || 8;
                const boxWidth = textWidth + (padding * 2);
                const boxHeight = textHeight + (padding * 2);
                
                // Position text box (y coordinate is baseline, so adjust for text height)
                const boxX = drawing.x;
                const boxY = drawing.y - textHeight;
                
                // Draw rounded background box
                this.drawRoundedRect(boxX, boxY, boxWidth, boxHeight, 8, drawing.color);
                
                // Draw white text centered in the box
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                const centerX = boxX + boxWidth / 2;
                const centerY = boxY + boxHeight / 2;
                this.ctx.fillText(drawing.text, centerX, centerY);
                
                // Reset text alignment to default
                this.ctx.textAlign = 'left';
                this.ctx.textBaseline = 'alphabetic';
                
                this.ctx.restore();
                
                // Draw selection indicator if this text is selected (without rotation)
                if (drawing.selected && this.selectedObject === drawing) {
                    this.drawTextSelection(boxX, boxY, boxWidth, boxHeight);
                }
                break;
                
            case 'blur':
                // Show selection border if selected
                if (isSelected) {
                    const blurWidth = drawing.endPos.x - drawing.startPos.x;
                    const blurHeight = drawing.endPos.y - drawing.startPos.y;
                    this.ctx.save();
                    this.ctx.strokeStyle = '#7C3AED';
                    this.ctx.lineWidth = 2;
                    this.ctx.setLineDash([5, 5]);
                    this.ctx.strokeRect(drawing.startPos.x, drawing.startPos.y, blurWidth, blurHeight);
                    this.ctx.restore();
                }
                break;
                
            case 'pixelate':
                // Show selection border if selected
                if (isSelected) {
                    const pixelWidth = drawing.endPos.x - drawing.startPos.x;
                    const pixelHeight = drawing.endPos.y - drawing.startPos.y;
                    this.ctx.save();
                    this.ctx.strokeStyle = '#FF8C00';
                    this.ctx.lineWidth = 2;
                    this.ctx.setLineDash([5, 5]);
                    this.ctx.strokeRect(drawing.startPos.x, drawing.startPos.y, pixelWidth, pixelHeight);
                    this.ctx.restore();
                }
                break;
        }
        
        this.ctx.restore();
        
        // Draw selection handles for selected non-text objects
        if (isSelected && drawing.tool !== 'text') {
            this.drawObjectSelection(drawing);
        }
    }
    
    getObjectCenter(drawing) {
        if (drawing.tool === 'text') {
            return { x: drawing.x, y: drawing.y };
        } else if (drawing.startPos && drawing.endPos) {
            return {
                x: (drawing.startPos.x + drawing.endPos.x) / 2,
                y: (drawing.startPos.y + drawing.endPos.y) / 2
            };
        }
        return { x: 0, y: 0 };
    }
    
    getObjectBounds(drawing) {
        if (drawing.tool === 'text') {
            const padding = drawing.padding || 8;
            const textWidth = this.measureTextWidth(drawing.text, drawing.fontSize);
            const textHeight = drawing.fontSize;
            
            return {
                x: drawing.x,
                y: drawing.y - textHeight,
                width: textWidth + (padding * 2),
                height: textHeight + (padding * 2)
            };
        } else if (drawing.startPos && drawing.endPos) {
            const minX = Math.min(drawing.startPos.x, drawing.endPos.x);
            const maxX = Math.max(drawing.startPos.x, drawing.endPos.x);
            const minY = Math.min(drawing.startPos.y, drawing.endPos.y);
            const maxY = Math.max(drawing.startPos.y, drawing.endPos.y);
            
            return {
                x: minX,
                y: minY,
                width: maxX - minX,
                height: maxY - minY
            };
        }
        return { x: 0, y: 0, width: 0, height: 0 };
    }
    
    drawObjectSelection(drawing) {
        this.ctx.save();
        
        const bounds = this.getObjectBounds(drawing);
        const handleSize = 8;
        const padding = 5;
        
        // Draw selection border
        this.ctx.strokeStyle = '#7C3AED';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 5]);
        this.ctx.strokeRect(
            bounds.x - padding, 
            bounds.y - padding, 
            bounds.width + padding * 2, 
            bounds.height + padding * 2
        );
        
        // Draw corner handles
        this.ctx.fillStyle = '#7C3AED';
        this.ctx.setLineDash([]);
        
        const handles = [
            { x: bounds.x - padding - handleSize/2, y: bounds.y - padding - handleSize/2 },
            { x: bounds.x + bounds.width + padding - handleSize/2, y: bounds.y - padding - handleSize/2 },
            { x: bounds.x - padding - handleSize/2, y: bounds.y + bounds.height + padding - handleSize/2 },
            { x: bounds.x + bounds.width + padding - handleSize/2, y: bounds.y + bounds.height + padding - handleSize/2 }
        ];
        
        handles.forEach(handle => {
            this.ctx.fillRect(handle.x, handle.y, handleSize, handleSize);
            this.ctx.strokeStyle = '#FFFFFF';
            this.ctx.lineWidth = 1;
            this.ctx.strokeRect(handle.x, handle.y, handleSize, handleSize);
        });
        
        // Draw rotation handle
        this.drawRotationHandle(
            bounds.x + bounds.width/2, 
            bounds.y - padding - 25
        );
        
        this.ctx.restore();
    }
    
    getRotationHandlePosition(drawing) {
        const bounds = this.getObjectBounds(drawing);
        return {
            x: bounds.x + bounds.width/2,
            y: bounds.y - 30 // Account for padding and handle offset
        };
    }
    
    drawArrow(start, end) {
        // Scale arrow head size with stroke width for better proportions
        const headLength = Math.max(15, this.ctx.lineWidth * 2);
        const angle = Math.atan2(end.y - start.y, end.x - start.x);
        
        // Draw line
        this.ctx.beginPath();
        this.ctx.moveTo(start.x, start.y);
        this.ctx.lineTo(end.x, end.y);
        this.ctx.stroke();
        
        // Draw arrowhead
        this.ctx.beginPath();
        this.ctx.moveTo(end.x, end.y);
        this.ctx.lineTo(
            end.x - headLength * Math.cos(angle - Math.PI / 6),
            end.y - headLength * Math.sin(angle - Math.PI / 6)
        );
        this.ctx.moveTo(end.x, end.y);
        this.ctx.lineTo(
            end.x - headLength * Math.cos(angle + Math.PI / 6),
            end.y - headLength * Math.sin(angle + Math.PI / 6)
        );
        this.ctx.stroke();
    }
    
    drawRoundedRect(x, y, width, height, radius, fillColor) {
        this.ctx.save();
        this.ctx.fillStyle = fillColor;
        
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
        
        this.ctx.fill();
        this.ctx.restore();
    }
    
    applyBlurEffect(drawing) {
        const startX = Math.min(drawing.startPos.x, drawing.endPos.x);
        const startY = Math.min(drawing.startPos.y, drawing.endPos.y);
        const width = Math.abs(drawing.endPos.x - drawing.startPos.x);
        const height = Math.abs(drawing.endPos.y - drawing.startPos.y);
        
        if (width <= 0 || height <= 0) return;
        
        // Get image data from the selected area
        const imageData = this.ctx.getImageData(startX, startY, width, height);
        const data = imageData.data;
        
        // Apply blur effect using a box blur algorithm
        // Cap blur radius for performance (max 50px for practical use)
        const blurRadius = Math.max(2, Math.min(50, Math.floor(drawing.strokeWidth)));
        
        // Performance warning for very high blur values
        if (drawing.strokeWidth > 50) {
            console.warn(`Screenshot Editor: High blur value (${drawing.strokeWidth}) capped at 50 for performance`);
        }
        
        const blurredData = this.boxBlur(data, width, height, blurRadius);
        
        // Create new image data with blurred pixels
        const blurredImageData = new ImageData(blurredData, width, height);
        
        // Put the blurred data back
        this.ctx.putImageData(blurredImageData, startX, startY);
    }
    
    applyPixelateEffect(drawing) {
        const startX = Math.min(drawing.startPos.x, drawing.endPos.x);
        const startY = Math.min(drawing.startPos.y, drawing.endPos.y);
        const width = Math.abs(drawing.endPos.x - drawing.startPos.x);
        const height = Math.abs(drawing.endPos.y - drawing.startPos.y);
        
        if (width <= 0 || height <= 0) return;
        
        // Pixelation size based on stroke width
        const pixelSize = Math.max(4, drawing.strokeWidth * 3);
        
        // Get image data from the selected area
        const imageData = this.ctx.getImageData(startX, startY, width, height);
        const data = imageData.data;
        
        // Apply pixelation effect
        for (let y = 0; y < height; y += pixelSize) {
            for (let x = 0; x < width; x += pixelSize) {
                // Get average color for this pixel block
                let r = 0, g = 0, b = 0, a = 0;
                let count = 0;
                
                for (let dy = 0; dy < pixelSize && y + dy < height; dy++) {
                    for (let dx = 0; dx < pixelSize && x + dx < width; dx++) {
                        const idx = ((y + dy) * width + (x + dx)) * 4;
                        r += data[idx];
                        g += data[idx + 1];
                        b += data[idx + 2];
                        a += data[idx + 3];
                        count++;
                    }
                }
                
                // Calculate average
                r = Math.floor(r / count);
                g = Math.floor(g / count);
                b = Math.floor(b / count);
                a = Math.floor(a / count);
                
                // Apply average color to entire block
                for (let dy = 0; dy < pixelSize && y + dy < height; dy++) {
                    for (let dx = 0; dx < pixelSize && x + dx < width; dx++) {
                        const idx = ((y + dy) * width + (x + dx)) * 4;
                        data[idx] = r;
                        data[idx + 1] = g;
                        data[idx + 2] = b;
                        data[idx + 3] = a;
                    }
                }
            }
        }
        
        // Put the pixelated data back
        this.ctx.putImageData(imageData, startX, startY);
    }
    
    boxBlur(data, width, height, radius) {
        // Performance optimization: for very small radius, return original data
        if (radius <= 1) {
            return new Uint8ClampedArray(data);
        }
        
        const output = new Uint8ClampedArray(data);
        const kernelSize = radius * 2 + 1;
        
        // Horizontal pass
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let r = 0, g = 0, b = 0, a = 0;
                
                // Calculate bounds to avoid repeated Math.max/min calls
                const startX = Math.max(0, x - radius);
                const endX = Math.min(width - 1, x + radius);
                const actualCount = endX - startX + 1;
                
                for (let nx = startX; nx <= endX; nx++) {
                    const idx = (y * width + nx) * 4;
                    r += data[idx];
                    g += data[idx + 1];
                    b += data[idx + 2];
                    a += data[idx + 3];
                }
                
                const idx = (y * width + x) * 4;
                output[idx] = r / actualCount;
                output[idx + 1] = g / actualCount;
                output[idx + 2] = b / actualCount;
                output[idx + 3] = a / actualCount;
            }
        }
        
        // Vertical pass
        const finalOutput = new Uint8ClampedArray(output);
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let r = 0, g = 0, b = 0, a = 0;
                
                // Calculate bounds to avoid repeated Math.max/min calls
                const startY = Math.max(0, y - radius);
                const endY = Math.min(height - 1, y + radius);
                const actualCount = endY - startY + 1;
                
                for (let ny = startY; ny <= endY; ny++) {
                    const idx = (ny * width + x) * 4;
                    r += output[idx];
                    g += output[idx + 1];
                    b += output[idx + 2];
                    a += output[idx + 3];
                }
                
                const idx = (y * width + x) * 4;
                finalOutput[idx] = r / actualCount;
                finalOutput[idx + 1] = g / actualCount;
                finalOutput[idx + 2] = b / actualCount;
                finalOutput[idx + 3] = a / actualCount;
            }
        }
        
        return finalOutput;
    }
    
    handleDoubleClick(e) {
        const rect = this.canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        const clickPos = {
            x: mouseX * (this.scaleX || 1),
            y: mouseY * (this.scaleY || 1)
        };
        
        const clickedObject = this.getObjectAt(clickPos.x, clickPos.y);
        if (clickedObject && clickedObject.tool === 'text') {
            // Edit existing text
            this.createInPlaceTextInput({x: clickedObject.x, y: clickedObject.y}, clickedObject);
        }
    }
    
    getObjectAt(x, y) {
        // Check all objects in reverse order (topmost first)
        for (let i = this.drawings.length - 1; i >= 0; i--) {
            const drawing = this.drawings[i];
            
            // Transform point if object is rotated
            let testX = x;
            let testY = y;
            
            if (drawing.rotation && drawing.rotation !== 0) {
                const center = this.getObjectCenter(drawing);
                const cos = Math.cos(-drawing.rotation);
                const sin = Math.sin(-drawing.rotation);
                
                // Translate to origin, rotate, translate back
                const relX = x - center.x;
                const relY = y - center.y;
                testX = center.x + (relX * cos - relY * sin);
                testY = center.y + (relX * sin + relY * cos);
            }
            
            if (drawing.tool === 'text') {
                // Check text bounds
                const padding = drawing.padding || 8;
                const textWidth = this.measureTextWidth(drawing.text, drawing.fontSize);
                const textHeight = drawing.fontSize;
                
                const boxX = drawing.x;
                const boxY = drawing.y - textHeight;
                const boxWidth = textWidth + (padding * 2);
                const boxHeight = textHeight + (padding * 2);
                
                if (testX >= boxX && testX <= boxX + boxWidth &&
                    testY >= boxY && testY <= boxY + boxHeight) {
                    return drawing;
                }
            } else if (drawing.startPos && drawing.endPos) {
                // Check other drawing types with tolerance
                const tolerance = Math.max(5, drawing.strokeWidth || 2);
                
                if (drawing.tool === 'line' || drawing.tool === 'arrow') {
                    // Check line/arrow hit detection
                    if (this.isPointNearLine(testX, testY, drawing.startPos, drawing.endPos, tolerance)) {
                        return drawing;
                    }
                } else if (drawing.tool === 'box' || drawing.tool === 'blur' || drawing.tool === 'pixelate') {
                    // Check rectangle bounds
                    const minX = Math.min(drawing.startPos.x, drawing.endPos.x) - tolerance;
                    const maxX = Math.max(drawing.startPos.x, drawing.endPos.x) + tolerance;
                    const minY = Math.min(drawing.startPos.y, drawing.endPos.y) - tolerance;
                    const maxY = Math.max(drawing.startPos.y, drawing.endPos.y) + tolerance;
                    
                    if (testX >= minX && testX <= maxX && testY >= minY && testY <= maxY) {
                        return drawing;
                    }
                }
            }
        }
        return null;
    }
    
    isPointNearLine(px, py, start, end, tolerance) {
        // Calculate distance from point to line segment
        const A = px - start.x;
        const B = py - start.y;
        const C = end.x - start.x;
        const D = end.y - start.y;
        
        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        
        if (lenSq === 0) {
            // Start and end are the same point
            const dist = Math.sqrt(A * A + B * B);
            return dist <= tolerance;
        }
        
        let param = dot / lenSq;
        
        let xx, yy;
        if (param < 0) {
            xx = start.x;
            yy = start.y;
        } else if (param > 1) {
            xx = end.x;
            yy = end.y;
        } else {
            xx = start.x + param * C;
            yy = start.y + param * D;
        }
        
        const dx = px - xx;
        const dy = py - yy;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        return distance <= tolerance;
    }
    
    measureTextWidth(text, fontSize) {
        // Create temporary canvas to measure text width
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');
        tempCtx.font = `${fontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto'`;
        return tempCtx.measureText(text).width;
    }
    
    selectObject(object) {
        // Deselect all objects first
        this.deselectAllObjects();
        
        // Select the clicked object
        object.selected = true;
        this.selectedObject = object;
        
        // Update controls to match selected object
        document.getElementById('color-picker').value = object.color;
        
        if (object.tool === 'text') {
            document.getElementById('stroke-width').value = Math.round(object.fontSize / 4);
            this.currentStrokeWidth = Math.round(object.fontSize / 4);
        } else {
            document.getElementById('stroke-width').value = object.strokeWidth;
            this.currentStrokeWidth = object.strokeWidth;
        }
        
        this.currentColor = object.color;
        this.updateStrokeValue();
        this.redraw();
    }
    
    deselectAllObjects() {
        console.log(`Screenshot Editor: Deselecting all objects - was selected: ${this.selectedObject ? this.selectedObject.tool : 'none'}`);
        this.drawings.forEach(drawing => {
            drawing.selected = false;
        });
        this.selectedObject = null;
        this.redraw();
    }
    
    updateCursor(pos) {
        // Check for rotation handle first
        if (this.selectedObject) {
            const rotationHandlePos = this.getRotationHandlePosition(this.selectedObject);
            const distToHandle = Math.sqrt(
                Math.pow(pos.x - rotationHandlePos.x, 2) + 
                Math.pow(pos.y - rotationHandlePos.y, 2)
            );
            
            if (distToHandle <= 12) {
                this.canvas.style.cursor = 'grab';
                return;
            }
        }
        
        const objectUnderMouse = this.getObjectAt(pos.x, pos.y);
        
        if (objectUnderMouse) {
            this.canvas.style.cursor = 'move';
        } else if (this.currentTool === 'text') {
            this.canvas.style.cursor = 'text';
        } else if (this.currentTool === 'blur' || this.currentTool === 'pixelate') {
            this.canvas.style.cursor = 'cell';
        } else {
            this.canvas.style.cursor = 'crosshair';
        }
    }
    
    drawTextSelection(boxX, boxY, boxWidth, boxHeight) {
        this.ctx.save();
        
        // Draw selection border
        this.ctx.strokeStyle = '#7C3AED';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 5]);
        this.ctx.strokeRect(boxX - 2, boxY - 2, boxWidth + 4, boxHeight + 4);
        
        // Draw selection handles (corner squares)
        const handleSize = 8;
        this.ctx.fillStyle = '#7C3AED';
        this.ctx.setLineDash([]);
        
        // Corner handles
        const handles = [
            { x: boxX - 2 - handleSize/2, y: boxY - 2 - handleSize/2 }, // Top-left
            { x: boxX + boxWidth + 2 - handleSize/2, y: boxY - 2 - handleSize/2 }, // Top-right
            { x: boxX - 2 - handleSize/2, y: boxY + boxHeight + 2 - handleSize/2 }, // Bottom-left
            { x: boxX + boxWidth + 2 - handleSize/2, y: boxY + boxHeight + 2 - handleSize/2 } // Bottom-right
        ];
        
        handles.forEach(handle => {
            this.ctx.fillRect(handle.x, handle.y, handleSize, handleSize);
            this.ctx.strokeStyle = '#FFFFFF';
            this.ctx.lineWidth = 1;
            this.ctx.strokeRect(handle.x, handle.y, handleSize, handleSize);
        });
        
        // Draw rotation handle
        this.drawRotationHandle(boxX + boxWidth/2, boxY - 25);
        
        this.ctx.restore();
    }
    
    drawRotationHandle(centerX, centerY) {
        const handleRadius = 6;
        
        // Draw connecting line
        this.ctx.strokeStyle = '#7C3AED';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([]);
        this.ctx.beginPath();
        this.ctx.moveTo(centerX, centerY + handleRadius + 2);
        this.ctx.lineTo(centerX, centerY + 20);
        this.ctx.stroke();
        
        // Draw rotation handle (circular)
        this.ctx.fillStyle = '#16a34a'; // Green for rotation
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, handleRadius, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Draw border
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        
        // Draw rotation arrow symbol
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 1.5;
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, 3, -Math.PI/4, Math.PI, false);
        this.ctx.stroke();
        
        // Arrow tip
        this.ctx.beginPath();
        this.ctx.moveTo(centerX - 2.5, centerY + 1.5);
        this.ctx.lineTo(centerX - 1, centerY + 3);
        this.ctx.lineTo(centerX - 3, centerY + 2.5);
        this.ctx.stroke();
    }
    
    updateStrokeValue() {
        const strokeValueElement = document.getElementById('stroke-value');
        if (strokeValueElement) {
            strokeValueElement.textContent = this.currentStrokeWidth;
        }
    }
    
    increaseStroke() {
        const newValue = Math.min(200, this.currentStrokeWidth + 5);
        console.log(`Screenshot Editor: increaseStroke called - current: ${this.currentStrokeWidth}, new: ${newValue}`);
        this.currentStrokeWidth = newValue;
        document.getElementById('stroke-width').value = newValue;
        this.updateStrokeValue();
        
        // Update selected object if any, OR show updated stroke for current tool
        if (this.selectedObject) {
            console.log(`Screenshot Editor: Updating selected object: ${this.selectedObject.tool}`);
            if (this.selectedObject.tool === 'text') {
                this.selectedObject.fontSize = this.currentStrokeWidth * 4;
                this.selectedObject.padding = this.currentStrokeWidth * 2;
            } else {
                this.selectedObject.strokeWidth = this.currentStrokeWidth;
            }
        } else {
            console.log(`Screenshot Editor: No selected object - stroke value (${this.currentStrokeWidth}) ready for next ${this.currentTool} operation`);
            console.log(`Screenshot Editor: Current tool is: ${this.currentTool}`);
        }
        
        // Always redraw to show updated stroke width (affects UI and future drawings)
        this.redraw();
    }
    
    decreaseStroke() {
        const newValue = Math.max(1, this.currentStrokeWidth - 5);
        console.log(`Screenshot Editor: decreaseStroke called - current: ${this.currentStrokeWidth}, new: ${newValue}`);
        this.currentStrokeWidth = newValue;
        document.getElementById('stroke-width').value = newValue;
        this.updateStrokeValue();
        
        // Update selected object if any, OR show updated stroke for current tool
        if (this.selectedObject) {
            console.log(`Screenshot Editor: Updating selected object: ${this.selectedObject.tool}`);
            if (this.selectedObject.tool === 'text') {
                this.selectedObject.fontSize = this.currentStrokeWidth * 4;
                this.selectedObject.padding = this.currentStrokeWidth * 2;
            } else {
                this.selectedObject.strokeWidth = this.currentStrokeWidth;
            }
        } else {
            console.log(`Screenshot Editor: No selected object - stroke value (${this.currentStrokeWidth}) ready for next ${this.currentTool} operation`);
            console.log(`Screenshot Editor: Current tool is: ${this.currentTool}`);
        }
        
        // Always redraw to show updated stroke width (affects UI and future drawings)
        this.redraw();
    }
    
    undo() {
        if (this.drawings.length > 0) {
            const lastDrawing = this.drawings.pop();
            
            // If we're undoing the selected object, clear selection
            if (this.selectedObject === lastDrawing) {
                this.selectedObject = null;
            }
            
            this.redraw();
        }
    }
    
    renderFinalCanvas() {
        // Create a temporary canvas for final rendering with effects applied
        const finalCanvas = document.createElement('canvas');
        finalCanvas.width = this.canvas.width;
        finalCanvas.height = this.canvas.height;
        const finalCtx = finalCanvas.getContext('2d');
        
        // Draw screenshot image
        if (this.screenshotImage) {
            finalCtx.drawImage(this.screenshotImage, 0, 0, this.canvas.width, this.canvas.height);
        }
        
        // Apply all drawings with actual effects
        this.drawings.forEach(drawing => {
            this.drawShapeWithEffects(finalCtx, drawing);
        });
        
        return finalCanvas;
    }
    
    drawShapeWithEffects(ctx, drawing) {
        ctx.save();
        ctx.strokeStyle = drawing.color;
        ctx.lineWidth = drawing.strokeWidth;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.setLineDash([]);
        
        // Apply rotation transform if object has rotation
        if (drawing.rotation && drawing.rotation !== 0) {
            const center = this.getObjectCenter(drawing);
            ctx.translate(center.x, center.y);
            ctx.rotate(drawing.rotation);
            ctx.translate(-center.x, -center.y);
        }
        
        switch (drawing.tool) {
            case 'line':
                ctx.beginPath();
                ctx.moveTo(drawing.startPos.x, drawing.startPos.y);
                ctx.lineTo(drawing.endPos.x, drawing.endPos.y);
                ctx.stroke();
                break;
                
            case 'box':
                const width = drawing.endPos.x - drawing.startPos.x;
                const height = drawing.endPos.y - drawing.startPos.y;
                ctx.strokeRect(drawing.startPos.x, drawing.startPos.y, width, height);
                break;
                
            case 'arrow':
                this.drawArrowOnContext(ctx, drawing.startPos, drawing.endPos);
                break;
                
            case 'text':
                this.drawTextOnContext(ctx, drawing);
                break;
                
            case 'blur':
                this.applyBlurEffectOnContext(ctx, drawing);
                break;
                
            case 'pixelate':
                this.applyPixelateEffectOnContext(ctx, drawing);
                break;
        }
        
        ctx.restore();
    }
    
    drawArrowOnContext(ctx, start, end) {
        const headLength = Math.max(15, ctx.lineWidth * 2);
        const angle = Math.atan2(end.y - start.y, end.x - start.x);
        
        // Draw line
        ctx.beginPath();
        ctx.moveTo(start.x, start.y);
        ctx.lineTo(end.x, end.y);
        ctx.stroke();
        
        // Draw arrowhead
        ctx.beginPath();
        ctx.moveTo(end.x, end.y);
        ctx.lineTo(
            end.x - headLength * Math.cos(angle - Math.PI / 6),
            end.y - headLength * Math.sin(angle - Math.PI / 6)
        );
        ctx.moveTo(end.x, end.y);
        ctx.lineTo(
            end.x - headLength * Math.cos(angle + Math.PI / 6),
            end.y - headLength * Math.sin(angle + Math.PI / 6)
        );
        ctx.stroke();
    }
    
    drawTextOnContext(ctx, drawing) {
        ctx.save();
        
        // Apply rotation for text
        if (drawing.rotation && drawing.rotation !== 0) {
            ctx.translate(drawing.x, drawing.y);
            ctx.rotate(drawing.rotation);
            ctx.translate(-drawing.x, -drawing.y);
        }
        
        // Set font for measurement and rendering
        ctx.font = `${drawing.fontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto'`;
        
        // Measure text dimensions
        const textMetrics = ctx.measureText(drawing.text);
        const textWidth = textMetrics.width;
        const textHeight = drawing.fontSize;
        
        // Calculate background box dimensions with padding
        const padding = drawing.padding || 8;
        const boxWidth = textWidth + (padding * 2);
        const boxHeight = textHeight + (padding * 2);
        
        // Position text box (y coordinate is baseline, so adjust for text height)
        const boxX = drawing.x;
        const boxY = drawing.y - textHeight;
        
        // Draw rounded background box
        this.drawRoundedRectOnContext(ctx, boxX, boxY, boxWidth, boxHeight, 8, drawing.color);
        
        // Draw white text centered in the box
        ctx.fillStyle = '#FFFFFF';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        const centerX = boxX + boxWidth / 2;
        const centerY = boxY + boxHeight / 2;
        ctx.fillText(drawing.text, centerX, centerY);
        
        // Reset text alignment to default
        ctx.textAlign = 'left';
        ctx.textBaseline = 'alphabetic';
        
        ctx.restore();
    }
    
    drawRoundedRectOnContext(ctx, x, y, width, height, radius, fillColor) {
        ctx.save();
        ctx.fillStyle = fillColor;
        
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
        
        ctx.fill();
        ctx.restore();
    }
    
    applyBlurEffectOnContext(ctx, drawing) {
        const startX = Math.min(drawing.startPos.x, drawing.endPos.x);
        const startY = Math.min(drawing.startPos.y, drawing.endPos.y);
        const width = Math.abs(drawing.endPos.x - drawing.startPos.x);
        const height = Math.abs(drawing.endPos.y - drawing.startPos.y);
        
        if (width <= 0 || height <= 0) return;
        
        // Get image data from the selected area
        const imageData = ctx.getImageData(startX, startY, width, height);
        const data = imageData.data;
        
        // Apply blur effect using a box blur algorithm
        // Cap blur radius for performance (max 50px for practical use)
        const blurRadius = Math.max(2, Math.min(50, Math.floor(drawing.strokeWidth)));
        
        // Performance warning for very high blur values
        if (drawing.strokeWidth > 50) {
            console.warn(`Screenshot Editor: High blur value (${drawing.strokeWidth}) capped at 50 for performance`);
        }
        
        const blurredData = this.boxBlur(data, width, height, blurRadius);
        
        // Create new image data with blurred pixels
        const blurredImageData = new ImageData(blurredData, width, height);
        
        // Put the blurred data back
        ctx.putImageData(blurredImageData, startX, startY);
    }
    
    applyPixelateEffectOnContext(ctx, drawing) {
        const startX = Math.min(drawing.startPos.x, drawing.endPos.x);
        const startY = Math.min(drawing.startPos.y, drawing.endPos.y);
        const width = Math.abs(drawing.endPos.x - drawing.startPos.x);
        const height = Math.abs(drawing.endPos.y - drawing.startPos.y);
        
        if (width <= 0 || height <= 0) return;
        
        // Pixelation size based on stroke width
        const pixelSize = Math.max(4, drawing.strokeWidth * 3);
        
        // Get image data from the selected area
        const imageData = ctx.getImageData(startX, startY, width, height);
        const data = imageData.data;
        
        // Apply pixelation effect
        for (let y = 0; y < height; y += pixelSize) {
            for (let x = 0; x < width; x += pixelSize) {
                // Get average color for this pixel block
                let r = 0, g = 0, b = 0, a = 0;
                let count = 0;
                
                for (let dy = 0; dy < pixelSize && y + dy < height; dy++) {
                    for (let dx = 0; dx < pixelSize && x + dx < width; dx++) {
                        const idx = ((y + dy) * width + (x + dx)) * 4;
                        r += data[idx];
                        g += data[idx + 1];
                        b += data[idx + 2];
                        a += data[idx + 3];
                        count++;
                    }
                }
                
                // Calculate average
                r = Math.floor(r / count);
                g = Math.floor(g / count);
                b = Math.floor(b / count);
                a = Math.floor(a / count);
                
                // Apply average color to entire block
                for (let dy = 0; dy < pixelSize && y + dy < height; dy++) {
                    for (let dx = 0; dx < pixelSize && x + dx < width; dx++) {
                        const idx = ((y + dy) * width + (x + dx)) * 4;
                        data[idx] = r;
                        data[idx + 1] = g;
                        data[idx + 2] = b;
                        data[idx + 3] = a;
                    }
                }
            }
        }
        
        // Put the pixelated data back
        ctx.putImageData(imageData, startX, startY);
    }

    async copyToClipboard() {
        try {
            
            // Render final canvas with effects applied
            const finalCanvas = this.renderFinalCanvas();
            
            finalCanvas.toBlob(async (blob) => {
                try {
                    const clipboardItem = {
                        'image/png': blob
                    };
                    
                    await navigator.clipboard.write([new ClipboardItem(clipboardItem)]);
                    
                    this.showNotification('Screenshot copied as PNG!', 'success');
                    
                    // Auto-close window after successful copy
                    setTimeout(() => {
                        this.close();
                    }, 1000);
                } catch (err) {
                    console.error('Screenshot Editor: Failed to copy to clipboard:', err);
                    this.showNotification('Failed to copy to clipboard', 'error');
                }
            }, 'image/png');
        } catch (error) {
            console.error('Screenshot Editor: Error in copyToClipboard:', error);
            this.showNotification('Failed to copy screenshot', 'error');
        }
    }
    
    saveScreenshot() {
        try {
            
            // Render final canvas with effects applied
            const finalCanvas = this.renderFinalCanvas();
            const imageData = finalCanvas.toDataURL('image/png');
            
            const link = document.createElement('a');
            link.download = `stm-screenshot-${Date.now()}.png`;
            link.href = imageData;
            link.click();
            
            this.showNotification('Screenshot saved as PNG!', 'success');
            
            // Auto-close window after successful save
            setTimeout(() => {
                this.close();
            }, 1000);
        } catch (error) {
            console.error('Screenshot Editor: Error saving screenshot:', error);
            this.showNotification('Failed to save screenshot', 'error');
        }
    }
    
    close() {
        // Clean up any active text input
        this.removeActiveTextInput();
        window.close();
    }
    
    showNotification(message, type = 'info') {
        // Simple notification system
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#dc2626' : type === 'success' ? '#16a34a' : '#7C3AED'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
        `;
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-out';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
    
    showError(message) {
        this.loading.innerHTML = `
            <div style="text-align: center;">
                <div style="color: #dc2626; font-size: 18px; margin-bottom: 10px;">Error</div>
                <div>${message}</div>
                <button onclick="window.close()" style="margin-top: 20px; padding: 8px 16px; background: #7C3AED; color: white; border: none; border-radius: 6px; cursor: pointer;">Close</button>
            </div>
        `;
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    new ScreenshotEditor();
});