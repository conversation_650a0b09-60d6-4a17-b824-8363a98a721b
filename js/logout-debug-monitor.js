// Logout Debug Monitor for SEO Time Machines
// Helps identify what's causing automatic logouts

(function() {
    'use strict';

    console.log('🔍 STM Logout Debug Monitor: Initializing authentication debug monitoring');

    const LogoutDebugMonitor = {
        
        // Track all storage operations
        storageOperations: [],
        
        // Track extension lifecycle events
        lifecycleEvents: [],
        
        // Start monitoring
        startMonitoring() {
            console.log('🔍 STM Logout Debug: Starting comprehensive monitoring for logout investigation');
            
            this.monitorStorageOperations();
            this.monitorExtensionLifecycle();
            this.monitorCookieOperations();
            this.setupPeriodicReporting();
        },
        
        // Monitor all storage operations
        monitorStorageOperations() {
            if (chrome.storage && chrome.storage.onChanged) {
                chrome.storage.onChanged.addListener((changes, namespace) => {
                    const timestamp = new Date().toISOString();
                    const operation = {
                        timestamp,
                        namespace,
                        changes: Object.keys(changes),
                        details: changes
                    };
                    
                    this.storageOperations.push(operation);
                    
                    // Log critical operations immediately
                    const criticalOperations = Object.keys(changes).filter(key => 
                        key.includes('auth') || 
                        key.includes('session') || 
                        key.includes('login') ||
                        key.includes('google') ||
                        key.includes('token')
                    );
                    
                    if (criticalOperations.length > 0) {
                        console.warn('🚨 STM Logout Debug: Critical storage operation detected:', {
                            timestamp,
                            namespace,
                            affectedKeys: criticalOperations,
                            operation
                        });
                        
                        // Check for deletions
                        criticalOperations.forEach(key => {
                            const change = changes[key];
                            if (change.oldValue && !change.newValue) {
                                console.error('⚠️ STM Logout Debug: AUTHENTICATION DATA DELETED:', {
                                    key,
                                    deletedValue: change.oldValue,
                                    timestamp
                                });
                            }
                        });
                    }
                });
            }
        },
        
        // Monitor extension lifecycle events
        monitorExtensionLifecycle() {
            const logLifecycleEvent = (event, details = {}) => {
                const timestamp = new Date().toISOString();
                const lifecycleEvent = {
                    timestamp,
                    event,
                    details
                };
                
                this.lifecycleEvents.push(lifecycleEvent);
                console.log(`🔄 STM Logout Debug: Extension lifecycle event - ${event}:`, lifecycleEvent);
            };
            
            // Monitor startup events
            if (chrome.runtime && chrome.runtime.onStartup) {
                chrome.runtime.onStartup.addListener(() => {
                    logLifecycleEvent('runtime.onStartup');
                });
            }
            
            // Monitor install events
            if (chrome.runtime && chrome.runtime.onInstalled) {
                chrome.runtime.onInstalled.addListener((details) => {
                    logLifecycleEvent('runtime.onInstalled', details);
                });
            }
            
            // Monitor suspend/resume
            if (chrome.runtime && chrome.runtime.onSuspend) {
                chrome.runtime.onSuspend.addListener(() => {
                    logLifecycleEvent('runtime.onSuspend');
                });
            }
            
            // Log when the monitor itself loads
            logLifecycleEvent('logout-debug-monitor-loaded');
        },
        
        // Monitor cookie operations (if accessible)
        monitorCookieOperations() {
            if (chrome.cookies && chrome.cookies.onChanged) {
                chrome.cookies.onChanged.addListener((changeInfo) => {
                    const timestamp = new Date().toISOString();
                    
                    // Only log Google-related cookies
                    if (changeInfo.cookie.domain.includes('google')) {
                        // Only log significant cookie events, not routine overwrites
                        const isSignificantEvent = changeInfo.removed && 
                            changeInfo.cause !== 'overwrite' &&
                            (changeInfo.cookie.name.includes('SID') || 
                             changeInfo.cookie.name.includes('AUTH') || 
                             changeInfo.cookie.name.includes('SESSION'));
                        
                        if (isSignificantEvent) {
                            console.warn('🚨 STM Logout Debug: Critical Google cookie REMOVED:', {
                                cookieName: changeInfo.cookie.name,
                                domain: changeInfo.cookie.domain,
                                cause: changeInfo.cause,
                                timestamp
                            });
                        }
                    }
                });
            }
        },
        
        // Periodic reporting
        setupPeriodicReporting() {
            setInterval(() => {
                this.generateDebugReport();
            }, 300000); // Report every 5 minutes
        },
        
        // Generate comprehensive debug report
        generateDebugReport() {
            const now = new Date().toISOString();
            const recentOperations = this.storageOperations.slice(-10); // Last 10 operations
            const recentLifecycle = this.lifecycleEvents.slice(-5); // Last 5 lifecycle events
            
            // Only report if there are new events since last report
            if (recentOperations.length > 0 || recentLifecycle.length > 0) {
                console.log('📊 STM Logout Debug Report:', {
                    timestamp: now,
                    totalStorageOperations: this.storageOperations.length,
                    totalLifecycleEvents: this.lifecycleEvents.length,
                    recentStorageOperations: recentOperations,
                    recentLifecycleEvents: recentLifecycle
                });
            }
        },
        
        // Get full debug data for troubleshooting
        getFullDebugData() {
            return {
                timestamp: new Date().toISOString(),
                storageOperations: this.storageOperations,
                lifecycleEvents: this.lifecycleEvents,
                summary: {
                    totalStorageOperations: this.storageOperations.length,
                    totalLifecycleEvents: this.lifecycleEvents.length,
                    authRelatedOperations: this.storageOperations.filter(op => 
                        op.changes.some(key => 
                            key.includes('auth') || 
                            key.includes('session') || 
                            key.includes('login') ||
                            key.includes('google') ||
                            key.includes('token')
                        )
                    ).length
                }
            };
        },
        
        // Initialize monitoring
        init() {
            this.startMonitoring();
            
            // Make debug functions globally available
            globalThis.STMLogoutDebugMonitor = this;
            
            console.log('✅ STM Logout Debug Monitor: Authentication debug monitoring active');
            console.log('💡 STM Logout Debug: Use globalThis.STMLogoutDebugMonitor.getFullDebugData() to get debug information');
        }
    };

    // Initialize monitor
    LogoutDebugMonitor.init();

    // Export for use in other parts of the extension
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = LogoutDebugMonitor;
    }

})();