// Advanced Cleaning Module - Context-aware content cleaning and filtering
window.GmbAdvancedCleaner = (function() {
    'use strict';
    
    // Import dependencies
    const ELEMENT_CLASSIFICATIONS = window.GmbReadabilityExtractor?.ELEMENT_CLASSIFICATIONS;
    
    // Cleaning rules configuration
    const CLEANING_RULES = {
        // Elements to always remove
        removeElements: new Set([
            'script', 'style', 'noscript', 'object', 'embed', 'applet',
            'iframe', 'frameset', 'frame', 'link', 'meta', 'base'
        ]),
        
        // Elements to remove conditionally (adjusted to be less aggressive)
        conditionalRemove: {
            'form': { minScore: -20, checkContent: true },  // Was -10
            'table': { minScore: -5, checkContent: true },   // Was 0
            'div': { minScore: -30, checkContent: true },    // Was -20
            'aside': { minScore: -20, checkContent: true },  // Was -10
            'footer': { minScore: -25, checkContent: true }, // Was -15
            'header': { minScore: -25, checkContent: true }, // Was -15
            'nav': { minScore: -30, checkContent: false }    // Was -20
        },
        
        // Attributes to remove
        removeAttributes: new Set([
            'onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout',
            'onmousedown', 'onmouseup', 'onmousemove', 'onkeydown',
            'onkeyup', 'onkeypress', 'onfocus', 'onblur', 'onchange',
            'onsubmit', 'onreset', 'onselect', 'data-ad', 'data-advertisement'
        ]),
        
        // Attributes to clean (remove if match pattern)
        cleanAttributes: {
            'class': /\b(ad|ads|advertisement|banner|breadcrumb|combx|comment|community|disqus|extra|foot|footer|header|menu|nav|remark|rss|share|shoutbox|sidebar|skyscraper|social|sponsor|supplement|widget)\b/gi,
            'id': /\b(ad|ads|advertisement|banner|breadcrumb|combx|comment|community|disqus|extra|foot|footer|header|menu|nav|remark|rss|share|shoutbox|sidebar|skyscraper|social|sponsor|supplement|widget)\b/gi
        },
        
        // Hidden element patterns
        hiddenPatterns: {
            style: [
                /display\s*:\s*none/i,
                /visibility\s*:\s*hidden/i,
                /opacity\s*:\s*0/i,
                /height\s*:\s*0/i,
                /width\s*:\s*0/i,
                /position\s*:\s*absolute[^;]*left\s*:\s*-\d+/i
            ],
            class: /\b(hidden|hide|invisible|collapsed)\b/i,
            attribute: ['hidden', 'aria-hidden="true"']
        },
        
        // Social media patterns
        socialPatterns: /facebook|twitter|linkedin|pinterest|reddit|tumblr|instagram|whatsapp|telegram|snapchat|tiktok|youtube|vimeo/i,
        
        // Advertisement patterns
        adPatterns: /adsense|doubleclick|googlesyndication|amazon-adsystem|taboola|outbrain|criteo|advertising|advertisement|^ad$|^ads$/i,
        
        // Tracking pixel patterns
        trackingPatterns: {
            width: /^[01]$/,
            height: /^[01]$/,
            src: /pixel|analytics|metrics|track|beacon|impression/i
        },
        
        // Empty element thresholds (adjusted to be less aggressive)
        emptyThresholds: {
            minTextLength: 10,    // Was 20
            minWordCount: 2,      // Was 3
            maxLinkDensity: 0.9   // Was 0.8
        }
    };
    
    // Default cleaning options
    const DEFAULT_OPTIONS = {
        removeHidden: true,
        removeSocial: true,
        removeAds: true,
        removeEmpty: true,
        removeTracking: true,
        cleanConditionally: true,
        cleanAttributes: true,
        preserveImages: true,
        preserveVideos: false,
        preserveIframes: false,
        preserveForms: false,
        showSiteElements: false,
        minTextLength: 25,
        minWordCount: 3,
        maxLinkDensity: 0.33,
        debug: false
    };
    
    class AdvancedCleaner {
        constructor(options = {}) {
            this.options = Object.assign({}, DEFAULT_OPTIONS, options);
            this.scorer = window.GmbContentScorer?.create();
            this.removed = [];
            this.cleaned = [];
        }
        
        // Main cleaning method
        clean(node, options = {}) {
            const opts = Object.assign({}, this.options, options);
            this.options = opts;
            this.removed = [];
            this.cleaned = [];
            
            // Phase 1: Remove unwanted elements
            this.removeUnwantedElements(node);
            
            // Phase 2: Remove hidden elements
            if (opts.removeHidden) {
                this.removeHiddenElements(node);
            }
            
            // Phase 3: Remove tracking pixels
            if (opts.removeTracking) {
                this.removeTrackingPixels(node);
            }
            
            // Phase 4: Clean attributes
            if (opts.cleanAttributes) {
                this.cleanElementAttributes(node);
            }
            
            // Phase 5: Remove social media embeds
            if (opts.removeSocial) {
                this.removeSocialEmbeds(node);
            }
            
            // Phase 6: Remove advertisements
            if (opts.removeAds) {
                this.removeAdvertisements(node);
            }
            
            // Phase 7: Clean conditionally
            if (opts.cleanConditionally) {
                this.cleanConditionally(node);
            }
            
            // Phase 8: Remove site structural elements (hidden by default)
            if (!opts.showSiteElements) {
                console.log('🟢 Phase 8: showSiteElements = false, starting removal...');
                
                // Enable debug mode for site element removal to show what's being removed
                const originalDebug = this.options.debug;
                this.options.debug = true;
                
                console.log('🟢 GMB Readability: Hiding site structural elements (default clean mode)');
                this.removeSiteStructuralElements(node);
                
                // Restore original debug setting
                this.options.debug = originalDebug;
                console.log('🟢 Phase 8 completed');
            } else {
                console.log('🟡 Phase 8: showSiteElements = true, preserving site elements');
            }
            
            // Phase 9: Remove empty elements
            if (opts.removeEmpty) {
                this.removeEmptyElements(node);
            }
            
            // Phase 10: Final cleanup
            this.finalCleanup(node);
            
            return {
                node: node,
                removed: this.removed,
                cleaned: this.cleaned
            };
        }
        
        // Remove unwanted elements
        removeUnwantedElements(node) {
            const toRemove = [];
            
            this.walkTree(node, (child) => {
                if (child.type !== 'element') return;
                
                // Always remove certain elements
                if (CLEANING_RULES.removeElements.has(child.tagName)) {
                    toRemove.push(child);
                    return;
                }
                
                // Check preservation options
                if (child.tagName === 'img' && !this.options.preserveImages) {
                    toRemove.push(child);
                } else if (child.tagName === 'video' && !this.options.preserveVideos) {
                    toRemove.push(child);
                } else if (child.tagName === 'iframe' && !this.options.preserveIframes) {
                    toRemove.push(child);
                } else if (child.tagName === 'form' && !this.options.preserveForms) {
                    toRemove.push(child);
                }
            });
            
            this.removeNodes(toRemove, 'unwanted-element');
        }
        
        // Remove hidden elements
        removeHiddenElements(node) {
            const toRemove = [];
            
            this.walkTree(node, (child) => {
                if (child.type !== 'element') return;
                
                // Check style attribute
                const style = child.getAttribute('style');
                if (style) {
                    for (const pattern of CLEANING_RULES.hiddenPatterns.style) {
                        if (pattern.test(style)) {
                            toRemove.push(child);
                            return;
                        }
                    }
                }
                
                // Check class attribute
                const className = child.getAttribute('class');
                if (className && CLEANING_RULES.hiddenPatterns.class.test(className)) {
                    toRemove.push(child);
                    return;
                }
                
                // Check hidden attributes
                for (const attr of CLEANING_RULES.hiddenPatterns.attribute) {
                    if (child.hasAttribute(attr)) {
                        toRemove.push(child);
                        return;
                    }
                }
            });
            
            this.removeNodes(toRemove, 'hidden-element');
        }
        
        // Remove tracking pixels
        removeTrackingPixels(node) {
            const toRemove = [];
            
            this.walkTree(node, (child) => {
                if (child.tagName !== 'img') return;
                
                const width = child.getAttribute('width');
                const height = child.getAttribute('height');
                const src = child.getAttribute('src');
                
                // Check dimensions
                if (width && height) {
                    if (CLEANING_RULES.trackingPatterns.width.test(width) &&
                        CLEANING_RULES.trackingPatterns.height.test(height)) {
                        toRemove.push(child);
                        return;
                    }
                }
                
                // Check src pattern
                if (src && CLEANING_RULES.trackingPatterns.src.test(src)) {
                    // Double-check it's small
                    if ((width && parseInt(width) <= 3) || 
                        (height && parseInt(height) <= 3)) {
                        toRemove.push(child);
                    }
                }
            });
            
            this.removeNodes(toRemove, 'tracking-pixel');
        }
        
        // Clean element attributes
        cleanElementAttributes(node) {
            this.walkTree(node, (child) => {
                if (child.type !== 'element') return;
                
                const toRemove = [];
                
                // Remove unwanted attributes
                for (const attr in child.attributes || {}) {
                    // Remove event handlers
                    if (CLEANING_RULES.removeAttributes.has(attr)) {
                        toRemove.push(attr);
                        continue;
                    }
                    
                    // Clean specific attributes
                    if (CLEANING_RULES.cleanAttributes[attr]) {
                        const value = child.getAttribute(attr);
                        const pattern = CLEANING_RULES.cleanAttributes[attr];
                        
                        if (pattern.test(value)) {
                            // Try to clean the value
                            const cleaned = value.replace(pattern, '').trim();
                            if (cleaned) {
                                child.setAttribute(attr, cleaned);
                                this.cleaned.push({
                                    node: child,
                                    attribute: attr,
                                    oldValue: value,
                                    newValue: cleaned
                                });
                            } else {
                                toRemove.push(attr);
                            }
                        }
                    }
                }
                
                // Remove attributes
                for (const attr of toRemove) {
                    child.removeAttribute(attr);
                }
            });
        }
        
        // Remove social media embeds
        removeSocialEmbeds(node) {
            const toRemove = [];
            
            this.walkTree(node, (child) => {
                if (child.type !== 'element') return;
                
                // Check various attributes
                const className = child.getAttribute('class') || '';
                const id = child.getAttribute('id') || '';
                const src = child.getAttribute('src') || '';
                const href = child.getAttribute('href') || '';
                
                const combined = `${className} ${id} ${src} ${href}`;
                
                if (CLEANING_RULES.socialPatterns.test(combined)) {
                    // Check if it's actually an embed/widget
                    if (child.tagName === 'iframe' || 
                        child.tagName === 'object' ||
                        child.tagName === 'embed' ||
                        className.includes('widget') ||
                        className.includes('embed')) {
                        toRemove.push(child);
                    }
                }
            });
            
            this.removeNodes(toRemove, 'social-embed');
        }
        
        // Remove advertisements
        removeAdvertisements(node) {
            const toRemove = [];
            
            this.walkTree(node, (child) => {
                if (child.type !== 'element') return;
                
                // Check various attributes
                const className = child.getAttribute('class') || '';
                const id = child.getAttribute('id') || '';
                const src = child.getAttribute('src') || '';
                const href = child.getAttribute('href') || '';
                
                const combined = `${className} ${id} ${src} ${href}`;
                
                if (CLEANING_RULES.adPatterns.test(combined)) {
                    toRemove.push(child);
                }
            });
            
            this.removeNodes(toRemove, 'advertisement');
        }
        
        // Clean conditionally based on content
        cleanConditionally(node) {
            const toRemove = [];
            
            for (const [tagName, rules] of Object.entries(CLEANING_RULES.conditionalRemove)) {
                this.walkTree(node, (child) => {
                    if (child.tagName !== tagName) return;
                    
                    // Skip if already marked for removal
                    if (toRemove.includes(child)) return;
                    
                    // Calculate score if scorer available
                    let score = 0;
                    if (this.scorer && rules.checkContent && typeof this.scorer.scoreElement === 'function') {
                        try {
                            const scoreData = this.scorer.scoreElement(child);
                            score = scoreData ? scoreData.contentScore : 0;
                        } catch (e) {
                            console.warn('Advanced Cleaner: Error scoring element:', e);
                            score = 0;
                        }
                    }
                    
                    // Check against minimum score
                    if (score < rules.minScore) {
                        // Additional checks
                        if (this.shouldRemoveConditionally(child, tagName)) {
                            toRemove.push(child);
                        }
                    }
                });
            }
            
            this.removeNodes(toRemove, 'conditional-removal');
        }
        
        // Remove site structural elements using proven readability approach
        removeSiteStructuralElements(node) {
            console.log('🟢 removeSiteStructuralElements() started');
            
            // Comprehensive patterns from successful readability libraries
            const REGEXPS = {
                unlikelyCandidates: /-ad-|ai2html|banner|breadcrumbs|combx|comment|community|cover-wrap|disqus|extra|footer|gdpr|header|legends|menu|related|remark|replies|rss|shoutbox|sidebar|skyscraper|social|sponsor|supplemental|ad-break|agegate|pagination|pager|popup|yom-remote/i,
                
                negative: /hidden|^hid$| hid$| hid |^hid |banner|combx|comment|com-|contact|foot|footer|footnote|gdpr|masthead|media|meta|outbrain|promo|related|scroll|share|shoutbox|sidebar|skyscraper|sponsor|shopping|tags|tool|widget/i,
                
                okMaybeItsACandidate: /and|article|body|column|content|main|shadow/i
            };
            
            console.log('🟢 Starting multi-pass filtering approach...');
            
            // Multi-pass filtering approach
            console.log('🟢 Running Pass 1: Unlikely Candidates');
            this.passOneUnlikelyCandidates(node, REGEXPS);
            
            console.log('🟢 Running Pass 2: Direct Removal');
            this.passTwoDirectRemoval(node);
            
            console.log('🟢 Running Pass 3: Conditional Cleaning');
            this.passThreeConditionalCleaning(node);
            
            console.log('🟢 Running Pass 4: Header Cleaning');
            this.passFourHeaderCleaning(node);
            
            console.log('🟢 removeSiteStructuralElements() completed');
        }
        
        // Pass 1: Remove unlikely candidates using comprehensive regex
        passOneUnlikelyCandidates(node, REGEXPS) {
            const toRemove = [];
            
            this.walkTree(node, (child) => {
                if (child.type !== 'element') return;
                
                const tagName = child.tagName.toLowerCase();
                const className = child.getAttribute('class') || '';
                const id = child.getAttribute('id') || '';
                
                // Combine class and id for pattern matching (proven approach)
                const classAndId = className + ' ' + id;
                
                // Skip if this might be a candidate for content
                if (REGEXPS.okMaybeItsACandidate.test(classAndId)) {
                    return;
                }
                
                // Check against unlikely candidates pattern
                if (REGEXPS.unlikelyCandidates.test(classAndId)) {
                    toRemove.push(child);
                    if (this.options.debug) {
                        this.logRemoval(child, `pass1-unlikely-candidate: ${classAndId}`);
                    }
                    return;
                }
                
                // Check against negative pattern
                if (REGEXPS.negative.test(classAndId)) {
                    toRemove.push(child);
                    if (this.options.debug) {
                        this.logRemoval(child, `pass1-negative-pattern: ${classAndId}`);
                    }
                    return;
                }
            });
            
            this.removeNodes(toRemove, 'pass1-unlikely-candidates');
        }
        
        // Pass 2: Direct removal of specific structural tags
        passTwoDirectRemoval(node) {
            const tagsToRemove = ['header', 'footer', 'aside', 'nav'];
            
            for (const tagName of tagsToRemove) {
                const elements = [];
                this.walkTree(node, (child) => {
                    if (child.type === 'element' && child.tagName.toLowerCase() === tagName) {
                        elements.push(child);
                    }
                });
                
                if (this.options.debug && elements.length > 0) {
                    console.log(`Pass 2: Removing ${elements.length} ${tagName} elements`);
                }
                
                this.removeNodes(elements, `pass2-direct-${tagName}`);
            }
        }
        
        // Pass 3: Conditional cleaning based on content quality
        passThreeConditionalCleaning(node) {
            const toRemove = [];
            
            this.walkTree(node, (child) => {
                if (child.type !== 'element') return;
                
                const tagName = child.tagName.toLowerCase();
                
                // Skip certain tags that shouldn't be conditionally removed
                if (['html', 'body', 'article', 'section', 'p', 'div'].includes(tagName)) {
                    return;
                }
                
                const classWeight = this.getClassWeight(child);
                const linkDensity = this.calculateLinkDensity(child);
                const textLength = this.getTextContent(child).length;
                
                // Remove if negative class weight and high link density
                if (classWeight < 0 && linkDensity > 0.33) {
                    toRemove.push(child);
                    if (this.options.debug) {
                        this.logRemoval(child, `pass3-conditional: weight=${classWeight}, linkDensity=${linkDensity}`);
                    }
                    return;
                }
                
                // Remove if very high link density (likely navigation)
                if (linkDensity > 0.8) {
                    toRemove.push(child);
                    if (this.options.debug) {
                        this.logRemoval(child, `pass3-high-link-density: ${linkDensity}`);
                    }
                    return;
                }
                
                // Remove if very short text and negative class weight
                if (textLength < 50 && classWeight < -10) {
                    toRemove.push(child);
                    if (this.options.debug) {
                        this.logRemoval(child, `pass3-short-negative: length=${textLength}, weight=${classWeight}`);
                    }
                    return;
                }
            });
            
            this.removeNodes(toRemove, 'pass3-conditional-cleaning');
        }
        
        // Pass 4: Header-specific cleaning with class weights (CONSERVATIVE)
        passFourHeaderCleaning(node) {
            const toRemove = [];
            
            // Target h1 and h2 elements with negative class weights (MUCH MORE CONSERVATIVE)
            for (let level = 1; level <= 2; level++) {
                this.walkTree(node, (child) => {
                    if (child.type === 'element' && 
                        child.tagName.toLowerCase() === `h${level}`) {
                        
                        const classWeight = this.getClassWeight(child);
                        const textContent = this.getTextContent(child);
                        const textLength = textContent.length;
                        
                        // CONSERVATIVE FIX: Only remove if header has no substantial content
                        if (textLength < 5) {
                            // Very short headers can be removed if negative weight
                            if (classWeight < 0) {
                                toRemove.push(child);
                                if (this.options.debug) {
                                    this.logRemoval(child, `pass4-header-h${level}: short & weight=${classWeight}`);
                                }
                            }
                        } else if (textLength < 15) {
                            // MODERATE FIX: Medium headers need VERY negative weight (multiple indicators)
                            if (classWeight < -40) {
                                toRemove.push(child);
                                if (this.options.debug) {
                                    this.logRemoval(child, `pass4-header-h${level}: medium & weight=${classWeight}`);
                                }
                            }
                        }
                        // TARGETED FIX: Headers with substantial content (15+ chars) are NEVER removed
                        // regardless of class weight - these are likely legitimate article headers
                    }
                });
            }
            
            this.removeNodes(toRemove, 'pass4-header-cleaning');
        }
        
        // Helper: Calculate class weight (positive = good, negative = bad)
        getClassWeight(element) {
            const className = element.getAttribute('class') || '';
            const id = element.getAttribute('id') || '';
            const combined = className + ' ' + id;
            
            let weight = 0;
            
            // Positive indicators
            if (/article|body|content|entry|hentry|main|page|pagination|post|text|blog|story/i.test(combined)) {
                weight += 25;
            }
            
            // Negative indicators  
            if (/comment|footer|header|menu|meta|nav|sidebar|widget|ad|banner|social|share|promo/i.test(combined)) {
                weight -= 25;
            }
            
            return weight;
        }
        
        // Check if element should be removed conditionally
        shouldRemoveConditionally(node, tagName) {
            const text = this.getTextContent(node);
            const textLength = text.length;
            
            // Check text length
            if (textLength < this.options.minTextLength) {
                return true;
            }
            
            // Check word count
            const wordCount = text.split(/\s+/).filter(w => w.length > 0).length;
            if (wordCount < this.options.minWordCount) {
                return true;
            }
            
            // Check link density
            const linkDensity = this.calculateLinkDensity(node);
            if (linkDensity > this.options.maxLinkDensity) {
                return true;
            }
            
            // Special checks for tables
            if (tagName === 'table') {
                return this.isLayoutTable(node);
            }
            
            // Special checks for lists
            if (tagName === 'ul' || tagName === 'ol') {
                return this.isNavigationList(node);
            }
            
            return false;
        }
        
        // Check if table is used for layout
        isLayoutTable(table) {
            // Count cells with substantial content
            let contentCells = 0;
            let totalCells = 0;
            
            this.walkTree(table, (child) => {
                if (child.tagName === 'td' || child.tagName === 'th') {
                    totalCells++;
                    const text = this.getTextContent(child).trim();
                    if (text.length > 50) {
                        contentCells++;
                    }
                }
            });
            
            // Layout tables typically have little content
            return totalCells > 0 && (contentCells / totalCells) < 0.1;
        }
        
        // Check if list is navigation
        isNavigationList(list) {
            let linkCount = 0;
            let itemCount = 0;
            
            this.walkTree(list, (child) => {
                if (child.tagName === 'li') {
                    itemCount++;
                }
                if (child.tagName === 'a') {
                    linkCount++;
                }
            });
            
            // Navigation lists are mostly links
            return itemCount > 0 && (linkCount / itemCount) > 0.8;
        }
        
        // Remove empty elements
        removeEmptyElements(node) {
            const preserveTags = new Set([
                'img', 'br', 'hr', 'input', 'video', 'audio', 'source',
                'track', 'area', 'param', 'col', 'embed'
            ]);
            
            let removed = true;
            while (removed) {
                removed = false;
                const toRemove = [];
                
                this.walkTree(node, (child) => {
                    if (child.type !== 'element') return;
                    if (preserveTags.has(child.tagName)) return;
                    
                    // Check if truly empty
                    if (this.isEmptyElement(child)) {
                        toRemove.push(child);
                        removed = true;
                    }
                });
                
                this.removeNodes(toRemove, 'empty-element');
            }
        }
        
        // Check if element is empty
        isEmptyElement(node) {
            // Has children?
            if (node.children && node.children.length > 0) {
                // Check if all children are whitespace or empty
                for (const child of node.children) {
                    if (child.type === 'text') {
                        if (child.data && child.data.trim()) {
                            return false;
                        }
                    } else if (child.type === 'element') {
                        if (!this.isEmptyElement(child)) {
                            return false;
                        }
                    }
                }
            }
            
            // Check for meaningful attributes
            if (node.tagName === 'a' && node.getAttribute('href')) {
                return false;
            }
            
            return true;
        }
        
        // Final cleanup pass
        finalCleanup(node) {
            // Merge adjacent text nodes
            this.mergeTextNodes(node);
            
            // Remove redundant whitespace
            this.cleanWhitespace(node);
            
            // Unwrap unnecessary wrapper elements
            this.unwrapElements(node);
        }
        
        // Merge adjacent text nodes
        mergeTextNodes(node) {
            if (!node.children || node.children.length < 2) return;
            
            const newChildren = [];
            let lastText = null;
            
            for (const child of node.children) {
                if (child.type === 'text') {
                    if (lastText) {
                        lastText.data += child.data;
                    } else {
                        lastText = child;
                        newChildren.push(child);
                    }
                } else {
                    lastText = null;
                    newChildren.push(child);
                    this.mergeTextNodes(child);
                }
            }
            
            node.children = newChildren;
        }
        
        // Clean whitespace
        cleanWhitespace(node) {
            this.walkTree(node, (child) => {
                if (child.type !== 'text') return;
                
                // Skip if in preformatted context
                if (this.isInPreformattedContext(child)) return;
                
                // Clean whitespace
                const original = child.data;
                child.data = child.data.replace(/\s+/g, ' ');
                
                // Trim at boundaries
                if (this.isFirstChild(child)) {
                    child.data = child.data.replace(/^\s+/, '');
                }
                if (this.isLastChild(child)) {
                    child.data = child.data.replace(/\s+$/, '');
                }
            });
        }
        
        // Unwrap unnecessary elements
        unwrapElements(node) {
            const toUnwrap = [];
            
            this.walkTree(node, (child) => {
                if (child.type !== 'element') return;
                
                // Unwrap divs and spans with single child
                if ((child.tagName === 'div' || child.tagName === 'span') &&
                    child.children.length === 1 &&
                    !child.getAttribute('class') &&
                    !child.getAttribute('id')) {
                    toUnwrap.push(child);
                }
            });
            
            for (const element of toUnwrap) {
                this.unwrapElement(element);
            }
        }
        
        // Unwrap single element
        unwrapElement(element) {
            if (!element.parent) return;
            
            const parent = element.parent;
            const index = parent.children.indexOf(element);
            
            if (index === -1) return;
            
            // Replace element with its children
            parent.children.splice(index, 1, ...element.children);
            
            // Update parent references
            for (const child of element.children) {
                child.parent = parent;
            }
        }
        
        // Helper methods
        removeNodes(nodes, reason) {
            for (const node of nodes) {
                if (node.parent) {
                    node.parent.removeChild(node);
                    this.removed.push({
                        node: node,
                        reason: reason,
                        tagName: node.tagName,
                        className: node.getAttribute('class'),
                        id: node.getAttribute('id')
                    });
                }
            }
        }
        
        walkTree(node, callback) {
            callback(node);
            if (node.children) {
                // Use slice to avoid issues with modifications during iteration
                const children = node.children.slice();
                for (const child of children) {
                    this.walkTree(child, callback);
                }
            }
        }
        
        getTextContent(node) {
            if (node.type === 'text') {
                return node.data || '';
            }
            
            let text = '';
            for (const child of node.children || []) {
                text += this.getTextContent(child);
            }
            return text;
        }
        
        calculateLinkDensity(node) {
            const textLength = this.getTextContent(node).length;
            if (textLength === 0) return 0;
            
            let linkTextLength = 0;
            this.walkTree(node, (child) => {
                if (child.tagName === 'a') {
                    linkTextLength += this.getTextContent(child).length;
                }
            });
            
            return linkTextLength / textLength;
        }
        
        isInPreformattedContext(node) {
            let current = node.parent;
            while (current) {
                if (ELEMENT_CLASSIFICATIONS?.PRESERVE_WHITESPACE?.has(current.tagName)) {
                    return true;
                }
                current = current.parent;
            }
            return false;
        }
        
        isFirstChild(node) {
            if (!node.parent) return true;
            return node.parent.children[0] === node;
        }
        
        isLastChild(node) {
            if (!node.parent) return true;
            const children = node.parent.children;
            return children[children.length - 1] === node;
        }
        
        // Debug helper
        logRemoval(node, reason) {
            if (this.options.debug) {
                console.log('Removing:', {
                    tagName: node.tagName,
                    class: node.getAttribute('class'),
                    id: node.getAttribute('id'),
                    reason: reason,
                    textSnippet: this.getTextContent(node).substring(0, 100)
                });
            }
        }
    }
    
    // Public API
    return {
        create: function(options) {
            return new AdvancedCleaner(options);
        },
        
        // Quick clean
        clean: function(node, options) {
            const cleaner = new AdvancedCleaner(options);
            return cleaner.clean(node);
        },
        
        // Export configuration
        CLEANING_RULES: CLEANING_RULES,
        DEFAULT_OPTIONS: DEFAULT_OPTIONS
    };
})();