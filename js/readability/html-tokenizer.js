// HTML Tokenizer Module - Advanced HTML parsing with proper tokenization
window.GmbHTMLTokenizer = (function() {
    'use strict';
    
    // Token types
    const TokenType = {
        TAG_OPEN: 'TAG_OPEN',
        TAG_CLOSE: 'TAG_CLOSE',
        TAG_SELF_CLOSE: 'TAG_SELF_CLOSE',
        TEXT: 'TEXT',
        COMMENT: 'COMMENT',
        CDATA: 'CDATA',
        DOCTYPE: 'DOCTYPE',
        ATTRIBUTE: 'ATTRIBUTE'
    };
    
    // Self-closing (void) elements in HTML5
    const VOID_ELEMENTS = new Set([
        'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input',
        'link', 'meta', 'param', 'source', 'track', 'wbr'
    ]);
    
    // Raw text elements (contents should not be parsed as HTML)
    const RAW_TEXT_ELEMENTS = new Set(['script', 'style']);
    
    // Escapable raw text elements
    const ESCAPABLE_RAW_TEXT_ELEMENTS = new Set(['textarea', 'title']);
    
    // Foreign elements that create new contexts
    const FOREIGN_ELEMENTS = new Set(['svg', 'math']);
    
    // States for the tokenizer state machine
    const State = {
        DATA: 'DATA',
        TAG_OPEN: 'TAG_OPEN',
        END_TAG_OPEN: 'END_TAG_OPEN',
        TAG_NAME: 'TAG_NAME',
        BEFORE_ATTRIBUTE_NAME: 'BEFORE_ATTRIBUTE_NAME',
        ATTRIBUTE_NAME: 'ATTRIBUTE_NAME',
        AFTER_ATTRIBUTE_NAME: 'AFTER_ATTRIBUTE_NAME',
        BEFORE_ATTRIBUTE_VALUE: 'BEFORE_ATTRIBUTE_VALUE',
        ATTRIBUTE_VALUE_DOUBLE_QUOTED: 'ATTRIBUTE_VALUE_DOUBLE_QUOTED',
        ATTRIBUTE_VALUE_SINGLE_QUOTED: 'ATTRIBUTE_VALUE_SINGLE_QUOTED',
        ATTRIBUTE_VALUE_UNQUOTED: 'ATTRIBUTE_VALUE_UNQUOTED',
        AFTER_ATTRIBUTE_VALUE_QUOTED: 'AFTER_ATTRIBUTE_VALUE_QUOTED',
        SELF_CLOSING_START_TAG: 'SELF_CLOSING_START_TAG',
        COMMENT_START: 'COMMENT_START',
        COMMENT: 'COMMENT',
        COMMENT_END: 'COMMENT_END',
        CDATA_START: 'CDATA_START',
        CDATA: 'CDATA',
        CDATA_END: 'CDATA_END',
        DOCTYPE: 'DOCTYPE',
        BEFORE_DOCTYPE_NAME: 'BEFORE_DOCTYPE_NAME',
        DOCTYPE_NAME: 'DOCTYPE_NAME',
        RAW_TEXT: 'RAW_TEXT',
        RCDATA: 'RCDATA'
    };
    
    class HTMLTokenizer {
        constructor(html) {
            this.html = html || '';
            this.position = 0;
            this.tokens = [];
            this.state = State.DATA;
            this.currentToken = null;
            this.currentAttribute = null;
            this.rawTextEndTag = null;
            this.buffer = '';
        }
        
        tokenize() {
            while (this.position < this.html.length) {
                const char = this.html[this.position];
                
                switch (this.state) {
                    case State.DATA:
                        this.handleDataState(char);
                        break;
                    case State.TAG_OPEN:
                        this.handleTagOpenState(char);
                        break;
                    case State.END_TAG_OPEN:
                        this.handleEndTagOpenState(char);
                        break;
                    case State.TAG_NAME:
                        this.handleTagNameState(char);
                        break;
                    case State.BEFORE_ATTRIBUTE_NAME:
                        this.handleBeforeAttributeNameState(char);
                        break;
                    case State.ATTRIBUTE_NAME:
                        this.handleAttributeNameState(char);
                        break;
                    case State.AFTER_ATTRIBUTE_NAME:
                        this.handleAfterAttributeNameState(char);
                        break;
                    case State.BEFORE_ATTRIBUTE_VALUE:
                        this.handleBeforeAttributeValueState(char);
                        break;
                    case State.ATTRIBUTE_VALUE_DOUBLE_QUOTED:
                        this.handleAttributeValueDoubleQuotedState(char);
                        break;
                    case State.ATTRIBUTE_VALUE_SINGLE_QUOTED:
                        this.handleAttributeValueSingleQuotedState(char);
                        break;
                    case State.ATTRIBUTE_VALUE_UNQUOTED:
                        this.handleAttributeValueUnquotedState(char);
                        break;
                    case State.AFTER_ATTRIBUTE_VALUE_QUOTED:
                        this.handleAfterAttributeValueQuotedState(char);
                        break;
                    case State.SELF_CLOSING_START_TAG:
                        this.handleSelfClosingStartTagState(char);
                        break;
                    case State.COMMENT:
                        this.handleCommentState(char);
                        break;
                    case State.DOCTYPE:
                        this.handleDoctypeState(char);
                        break;
                    case State.RAW_TEXT:
                        this.handleRawTextState(char);
                        break;
                    default:
                        // Skip character if in unknown state
                        break;
                }
                
                this.position++;
            }
            
            // Emit any remaining text
            if (this.buffer) {
                this.emitTextToken();
            }
            
            // Emit current token if exists
            if (this.currentToken) {
                this.emitCurrentToken();
            }
            
            return this.tokens;
        }
        
        handleDataState(char) {
            if (char === '<') {
                if (this.buffer) {
                    this.emitTextToken();
                }
                this.state = State.TAG_OPEN;
            } else {
                this.buffer += char;
            }
        }
        
        handleTagOpenState(char) {
            if (char === '!') {
                // Could be comment or DOCTYPE
                if (this.peek(2) === '--') {
                    this.position += 2; // Skip --
                    this.state = State.COMMENT;
                    this.currentToken = {
                        type: TokenType.COMMENT,
                        data: ''
                    };
                } else if (this.peek(7).toUpperCase() === 'DOCTYPE') {
                    this.position += 7; // Skip DOCTYPE
                    this.state = State.DOCTYPE;
                    this.currentToken = {
                        type: TokenType.DOCTYPE,
                        name: '',
                        publicId: null,
                        systemId: null
                    };
                } else if (this.peek(7) === '[CDATA[') {
                    this.position += 7; // Skip [CDATA[
                    this.state = State.CDATA;
                    this.currentToken = {
                        type: TokenType.CDATA,
                        data: ''
                    };
                } else {
                    // Invalid, treat as text
                    this.buffer += '<' + char;
                    this.state = State.DATA;
                }
            } else if (char === '/') {
                this.state = State.END_TAG_OPEN;
            } else if (this.isAlpha(char)) {
                this.currentToken = {
                    type: TokenType.TAG_OPEN,
                    tagName: char.toLowerCase(),
                    attributes: {},
                    selfClosing: false
                };
                this.state = State.TAG_NAME;
            } else {
                // Invalid tag, treat as text
                this.buffer += '<' + char;
                this.state = State.DATA;
            }
        }
        
        handleEndTagOpenState(char) {
            if (this.isAlpha(char)) {
                this.currentToken = {
                    type: TokenType.TAG_CLOSE,
                    tagName: char.toLowerCase()
                };
                this.state = State.TAG_NAME;
            } else {
                // Invalid end tag
                this.buffer += '</' + char;
                this.state = State.DATA;
            }
        }
        
        handleTagNameState(char) {
            if (this.isWhitespace(char)) {
                this.state = State.BEFORE_ATTRIBUTE_NAME;
            } else if (char === '/') {
                this.state = State.SELF_CLOSING_START_TAG;
            } else if (char === '>') {
                this.emitCurrentToken();
                this.state = State.DATA;
            } else {
                this.currentToken.tagName += char.toLowerCase();
            }
        }
        
        handleBeforeAttributeNameState(char) {
            if (this.isWhitespace(char)) {
                // Skip whitespace
            } else if (char === '/') {
                this.state = State.SELF_CLOSING_START_TAG;
            } else if (char === '>') {
                this.emitCurrentToken();
                this.state = State.DATA;
            } else {
                this.currentAttribute = {
                    name: char.toLowerCase(),
                    value: ''
                };
                this.state = State.ATTRIBUTE_NAME;
            }
        }
        
        handleAttributeNameState(char) {
            if (this.isWhitespace(char) || char === '/' || char === '>') {
                // End of attribute name
                if (this.currentToken.type === TokenType.TAG_OPEN) {
                    this.currentToken.attributes[this.currentAttribute.name] = this.currentAttribute.value;
                }
                
                if (char === '/') {
                    this.state = State.SELF_CLOSING_START_TAG;
                } else if (char === '>') {
                    this.emitCurrentToken();
                    this.state = State.DATA;
                } else {
                    this.state = State.AFTER_ATTRIBUTE_NAME;
                }
            } else if (char === '=') {
                this.state = State.BEFORE_ATTRIBUTE_VALUE;
            } else {
                this.currentAttribute.name += char.toLowerCase();
            }
        }
        
        handleAfterAttributeNameState(char) {
            if (this.isWhitespace(char)) {
                // Skip whitespace
            } else if (char === '=') {
                this.state = State.BEFORE_ATTRIBUTE_VALUE;
            } else if (char === '/') {
                if (this.currentToken.type === TokenType.TAG_OPEN) {
                    this.currentToken.attributes[this.currentAttribute.name] = this.currentAttribute.value;
                }
                this.state = State.SELF_CLOSING_START_TAG;
            } else if (char === '>') {
                if (this.currentToken.type === TokenType.TAG_OPEN) {
                    this.currentToken.attributes[this.currentAttribute.name] = this.currentAttribute.value;
                }
                this.emitCurrentToken();
                this.state = State.DATA;
            } else {
                // Start new attribute
                if (this.currentToken.type === TokenType.TAG_OPEN) {
                    this.currentToken.attributes[this.currentAttribute.name] = this.currentAttribute.value;
                }
                this.currentAttribute = {
                    name: char.toLowerCase(),
                    value: ''
                };
                this.state = State.ATTRIBUTE_NAME;
            }
        }
        
        handleBeforeAttributeValueState(char) {
            if (this.isWhitespace(char)) {
                // Skip whitespace
            } else if (char === '"') {
                this.state = State.ATTRIBUTE_VALUE_DOUBLE_QUOTED;
            } else if (char === "'") {
                this.state = State.ATTRIBUTE_VALUE_SINGLE_QUOTED;
            } else if (char === '>') {
                // Empty attribute value
                if (this.currentToken.type === TokenType.TAG_OPEN) {
                    this.currentToken.attributes[this.currentAttribute.name] = '';
                }
                this.emitCurrentToken();
                this.state = State.DATA;
            } else {
                this.currentAttribute.value += char;
                this.state = State.ATTRIBUTE_VALUE_UNQUOTED;
            }
        }
        
        handleAttributeValueDoubleQuotedState(char) {
            if (char === '"') {
                if (this.currentToken.type === TokenType.TAG_OPEN) {
                    this.currentToken.attributes[this.currentAttribute.name] = this.decodeEntities(this.currentAttribute.value);
                }
                this.state = State.AFTER_ATTRIBUTE_VALUE_QUOTED;
            } else {
                this.currentAttribute.value += char;
            }
        }
        
        handleAttributeValueSingleQuotedState(char) {
            if (char === "'") {
                if (this.currentToken.type === TokenType.TAG_OPEN) {
                    this.currentToken.attributes[this.currentAttribute.name] = this.decodeEntities(this.currentAttribute.value);
                }
                this.state = State.AFTER_ATTRIBUTE_VALUE_QUOTED;
            } else {
                this.currentAttribute.value += char;
            }
        }
        
        handleAttributeValueUnquotedState(char) {
            if (this.isWhitespace(char) || char === '>') {
                if (this.currentToken.type === TokenType.TAG_OPEN) {
                    this.currentToken.attributes[this.currentAttribute.name] = this.decodeEntities(this.currentAttribute.value);
                }
                
                if (char === '>') {
                    this.emitCurrentToken();
                    this.state = State.DATA;
                } else {
                    this.state = State.BEFORE_ATTRIBUTE_NAME;
                }
            } else {
                this.currentAttribute.value += char;
            }
        }
        
        handleAfterAttributeValueQuotedState(char) {
            if (this.isWhitespace(char)) {
                this.state = State.BEFORE_ATTRIBUTE_NAME;
            } else if (char === '/') {
                this.state = State.SELF_CLOSING_START_TAG;
            } else if (char === '>') {
                this.emitCurrentToken();
                this.state = State.DATA;
            } else {
                // Error in HTML, but we'll be lenient
                this.state = State.BEFORE_ATTRIBUTE_NAME;
                this.position--; // Reprocess this character
            }
        }
        
        handleSelfClosingStartTagState(char) {
            if (char === '>') {
                this.currentToken.selfClosing = true;
                this.emitCurrentToken();
                this.state = State.DATA;
            } else {
                // Error in HTML, but we'll be lenient
                this.state = State.BEFORE_ATTRIBUTE_NAME;
                this.position--; // Reprocess this character
            }
        }
        
        handleCommentState(char) {
            const nextTwo = this.peek(2);
            if (nextTwo === '--') {
                const nextThree = this.peek(3);
                if (nextThree === '-->') {
                    this.position += 2; // Will be incremented by 1 in main loop
                    this.tokens.push(this.currentToken);
                    this.currentToken = null;
                    this.state = State.DATA;
                } else {
                    this.currentToken.data += char;
                }
            } else {
                this.currentToken.data += char;
            }
        }
        
        handleDoctypeState(char) {
            if (char === '>') {
                this.tokens.push(this.currentToken);
                this.currentToken = null;
                this.state = State.DATA;
            } else {
                // Simplified DOCTYPE parsing
                if (!this.currentToken.name && this.isAlpha(char)) {
                    this.currentToken.name += char;
                }
            }
        }
        
        handleRawTextState(char) {
            // Look for closing tag
            if (char === '<' && this.peek(2) === '/' + this.rawTextEndTag[0]) {
                const endTag = '</' + this.rawTextEndTag + '>';
                const upcoming = this.html.substring(this.position, this.position + endTag.length);
                
                if (upcoming.toLowerCase() === endTag) {
                    // Emit text token
                    if (this.buffer) {
                        this.emitTextToken();
                    }
                    // Skip to end of closing tag
                    this.position += endTag.length - 1; // -1 because position will be incremented
                    // Emit closing tag
                    this.tokens.push({
                        type: TokenType.TAG_CLOSE,
                        tagName: this.rawTextEndTag
                    });
                    this.rawTextEndTag = null;
                    this.state = State.DATA;
                    return;
                }
            }
            
            this.buffer += char;
        }
        
        emitCurrentToken() {
            if (!this.currentToken) return;
            
            // Check if this is a void element
            if (this.currentToken.type === TokenType.TAG_OPEN) {
                const tagName = this.currentToken.tagName;
                
                // Automatically self-close void elements
                if (VOID_ELEMENTS.has(tagName)) {
                    this.currentToken.selfClosing = true;
                }
                
                // Handle raw text elements
                if (RAW_TEXT_ELEMENTS.has(tagName) || ESCAPABLE_RAW_TEXT_ELEMENTS.has(tagName)) {
                    this.rawTextEndTag = tagName;
                    this.state = State.RAW_TEXT;
                }
            }
            
            this.tokens.push(this.currentToken);
            this.currentToken = null;
        }
        
        emitTextToken() {
            if (this.buffer) {
                this.tokens.push({
                    type: TokenType.TEXT,
                    data: this.decodeEntities(this.buffer)
                });
                this.buffer = '';
            }
        }
        
        peek(length) {
            return this.html.substring(this.position, this.position + length);
        }
        
        isAlpha(char) {
            return /[a-zA-Z]/.test(char);
        }
        
        isWhitespace(char) {
            return /\s/.test(char);
        }
        
        decodeEntities(text) {
            // Basic entity decoding
            const entities = {
                '&amp;': '&',
                '&lt;': '<',
                '&gt;': '>',
                '&quot;': '"',
                '&#39;': "'",
                '&apos;': "'",
                '&nbsp;': '\u00A0'
            };
            
            let decoded = text;
            for (const [entity, char] of Object.entries(entities)) {
                decoded = decoded.replace(new RegExp(entity, 'g'), char);
            }
            
            // Decode numeric entities
            decoded = decoded.replace(/&#(\d+);/g, (match, code) => {
                return String.fromCharCode(parseInt(code, 10));
            });
            
            decoded = decoded.replace(/&#x([0-9a-fA-F]+);/g, (match, code) => {
                return String.fromCharCode(parseInt(code, 16));
            });
            
            return decoded;
        }
    }
    
    // Public API
    return {
        tokenize: function(html) {
            const tokenizer = new HTMLTokenizer(html);
            return tokenizer.tokenize();
        },
        TokenType: TokenType,
        VOID_ELEMENTS: VOID_ELEMENTS,
        RAW_TEXT_ELEMENTS: RAW_TEXT_ELEMENTS,
        ESCAPABLE_RAW_TEXT_ELEMENTS: ESCAPABLE_RAW_TEXT_ELEMENTS,
        FOREIGN_ELEMENTS: FOREIGN_ELEMENTS
    };
})();