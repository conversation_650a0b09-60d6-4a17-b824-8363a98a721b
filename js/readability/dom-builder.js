// DOM Builder Module - Constructs DOM tree from HTML tokens
window.GmbDOMBuilder = (function() {
    'use strict';
    
    // Import dependencies
    const TokenType = window.GmbHTMLTokenizer.TokenType;
    const VOID_ELEMENTS = window.GmbHTMLTokenizer.VOID_ELEMENTS;
    
    // Implicit end tags rules (simplified HTML5 parsing)
    const IMPLICIT_END_TAGS = {
        'li': new Set(['li']),
        'dt': new Set(['dt', 'dd']),
        'dd': new Set(['dt', 'dd']),
        'p': new Set(['address', 'article', 'aside', 'blockquote', 'div', 'dl', 'fieldset', 
                      'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header', 
                      'hgroup', 'hr', 'main', 'nav', 'ol', 'p', 'pre', 'section', 'table', 'ul']),
        'rt': new Set(['rt', 'rp']),
        'rp': new Set(['rt', 'rp']),
        'optgroup': new Set(['optgroup']),
        'option': new Set(['option', 'optgroup']),
        'thead': new Set(['tbody', 'tfoot']),
        'tbody': new Set(['tbody', 'tfoot']),
        'tfoot': new Set(['tbody']),
        'tr': new Set(['tr']),
        'td': new Set(['td', 'th']),
        'th': new Set(['td', 'th'])
    };
    
    // Elements that should be in specific parents
    const PARENT_REQUIREMENTS = {
        'li': new Set(['ul', 'ol', 'menu']),
        'dt': new Set(['dl']),
        'dd': new Set(['dl']),
        'caption': new Set(['table']),
        'colgroup': new Set(['table']),
        'thead': new Set(['table']),
        'tbody': new Set(['table']),
        'tfoot': new Set(['table']),
        'tr': new Set(['table', 'thead', 'tbody', 'tfoot']),
        'td': new Set(['tr']),
        'th': new Set(['tr']),
        'col': new Set(['colgroup'])
    };
    
    // Elements that auto-close when parent closes
    const SCOPE_ELEMENTS = new Set([
        'applet', 'caption', 'html', 'table', 'td', 'th', 'marquee', 
        'object', 'template', 'foreignObject', 'desc', 'title'
    ]);
    
    // Special formatting elements
    const FORMATTING_ELEMENTS = new Set([
        'a', 'b', 'big', 'code', 'em', 'font', 'i', 'nobr', 's', 
        'small', 'strike', 'strong', 'tt', 'u'
    ]);
    
    class DOMNode {
        constructor(type, tagName = null, attributes = {}, data = null) {
            this.type = type;
            this.tagName = tagName;
            this.attributes = attributes || {};
            this.children = [];
            this.parent = null;
            this.data = data; // For text, comment, CDATA nodes
            
            // Metadata for content analysis
            this.metadata = {
                depth: 0,
                textLength: 0,
                linkTextLength: 0,
                paragraphCount: 0,
                imageCount: 0,
                listCount: 0
            };
        }
        
        appendChild(child) {
            if (child.parent) {
                child.parent.removeChild(child);
            }
            
            child.parent = this;
            this.children.push(child);
            
            // Update depth
            child.updateDepth(this.metadata.depth + 1);
            
            return child;
        }
        
        removeChild(child) {
            const index = this.children.indexOf(child);
            if (index > -1) {
                this.children.splice(index, 1);
                child.parent = null;
            }
            return child;
        }
        
        updateDepth(depth) {
            this.metadata.depth = depth;
            for (const child of this.children) {
                child.updateDepth(depth + 1);
            }
        }
        
        getElementById(id) {
            if (this.attributes.id === id) {
                return this;
            }
            
            for (const child of this.children) {
                const result = child.getElementById(id);
                if (result) return result;
            }
            
            return null;
        }
        
        getElementsByTagName(tagName) {
            const results = [];
            const lowerTagName = tagName.toLowerCase();
            
            if (this.tagName === lowerTagName || tagName === '*') {
                results.push(this);
            }
            
            for (const child of this.children) {
                results.push(...child.getElementsByTagName(tagName));
            }
            
            return results;
        }
        
        getElementsByClassName(className) {
            const results = [];
            const classes = (this.attributes.class || '').split(/\s+/);
            
            if (classes.includes(className)) {
                results.push(this);
            }
            
            for (const child of this.children) {
                results.push(...child.getElementsByClassName(className));
            }
            
            return results;
        }
        
        getTextContent() {
            if (this.type === 'text') {
                return this.data || '';
            }
            
            let text = '';
            for (const child of this.children) {
                text += child.getTextContent();
            }
            
            return text;
        }
        
        getAttribute(name) {
            return this.attributes[name] || null;
        }
        
        setAttribute(name, value) {
            this.attributes[name] = value;
        }
        
        hasAttribute(name) {
            return name in this.attributes;
        }
        
        removeAttribute(name) {
            delete this.attributes[name];
        }
        
        clone(deep = true) {
            const cloned = new DOMNode(this.type, this.tagName, {...this.attributes}, this.data);
            cloned.metadata = {...this.metadata};
            
            if (deep) {
                for (const child of this.children) {
                    cloned.appendChild(child.clone(true));
                }
            }
            
            return cloned;
        }
        
        // Calculate metadata for content analysis
        calculateMetadata() {
            // Reset metadata
            this.metadata.textLength = 0;
            this.metadata.linkTextLength = 0;
            this.metadata.paragraphCount = 0;
            this.metadata.imageCount = 0;
            this.metadata.listCount = 0;
            
            // Count direct text content
            for (const child of this.children) {
                if (child.type === 'text') {
                    this.metadata.textLength += (child.data || '').length;
                } else if (child.type === 'element') {
                    // Recursively calculate child metadata
                    child.calculateMetadata();
                    
                    // Aggregate child metadata
                    this.metadata.textLength += child.metadata.textLength;
                    this.metadata.linkTextLength += child.metadata.linkTextLength;
                    this.metadata.paragraphCount += child.metadata.paragraphCount;
                    this.metadata.imageCount += child.metadata.imageCount;
                    this.metadata.listCount += child.metadata.listCount;
                    
                    // Count specific elements
                    switch (child.tagName) {
                        case 'p':
                            this.metadata.paragraphCount++;
                            break;
                        case 'img':
                            this.metadata.imageCount++;
                            break;
                        case 'ul':
                        case 'ol':
                            this.metadata.listCount++;
                            break;
                        case 'a':
                            this.metadata.linkTextLength += child.metadata.textLength;
                            break;
                    }
                }
            }
            
            return this.metadata;
        }
    }
    
    class DOMBuilder {
        constructor(tokens) {
            this.tokens = tokens;
            this.document = new DOMNode('document', null);
            this.currentNode = this.document;
            this.openElements = [this.document];
            this.activeFormattingElements = [];
            this.errors = [];
        }
        
        build() {
            for (let i = 0; i < this.tokens.length; i++) {
                const token = this.tokens[i];
                
                try {
                    switch (token.type) {
                        case TokenType.TAG_OPEN:
                            this.handleStartTag(token);
                            break;
                        case TokenType.TAG_CLOSE:
                            this.handleEndTag(token);
                            break;
                        case TokenType.TEXT:
                            this.handleText(token);
                            break;
                        case TokenType.COMMENT:
                            this.handleComment(token);
                            break;
                        case TokenType.CDATA:
                            this.handleCData(token);
                            break;
                        case TokenType.DOCTYPE:
                            this.handleDoctype(token);
                            break;
                    }
                } catch (error) {
                    this.errors.push({
                        token: token,
                        error: error.message,
                        position: i
                    });
                }
            }
            
            // Calculate metadata for all nodes
            this.document.calculateMetadata();
            
            return {
                document: this.document,
                errors: this.errors
            };
        }
        
        handleStartTag(token) {
            const tagName = token.tagName;
            
            // Handle implicit end tags
            this.handleImplicitEndTags(tagName);
            
            // Check parent requirements
            this.ensureProperParent(tagName);
            
            // Create element node
            const element = new DOMNode('element', tagName, token.attributes);
            
            // Special handling for certain tags
            if (tagName === 'html') {
                // Find or create HTML element
                let htmlElement = this.document.getElementsByTagName('html')[0];
                if (!htmlElement) {
                    this.currentNode.appendChild(element);
                } else {
                    // Merge attributes
                    Object.assign(htmlElement.attributes, token.attributes);
                    element = htmlElement;
                }
            } else if (tagName === 'head' || tagName === 'body') {
                // Ensure these are children of html
                let htmlElement = this.document.getElementsByTagName('html')[0];
                if (!htmlElement) {
                    htmlElement = new DOMNode('element', 'html');
                    this.document.appendChild(htmlElement);
                }
                htmlElement.appendChild(element);
            } else {
                this.currentNode.appendChild(element);
            }
            
            // Track formatting elements
            if (FORMATTING_ELEMENTS.has(tagName)) {
                this.activeFormattingElements.push(element);
            }
            
            // Update current node if not self-closing
            if (!token.selfClosing && !VOID_ELEMENTS.has(tagName)) {
                this.currentNode = element;
                this.openElements.push(element);
            }
        }
        
        handleEndTag(token) {
            const tagName = token.tagName;
            
            // Find matching open element
            let found = false;
            for (let i = this.openElements.length - 1; i >= 0; i--) {
                const element = this.openElements[i];
                if (element.tagName === tagName) {
                    // Close all elements up to and including this one
                    while (this.openElements.length > i) {
                        this.openElements.pop();
                    }
                    this.currentNode = this.openElements[this.openElements.length - 1] || this.document;
                    found = true;
                    break;
                }
                
                // Check if we hit a scope element
                if (SCOPE_ELEMENTS.has(element.tagName)) {
                    break;
                }
            }
            
            if (!found) {
                this.errors.push({
                    type: 'unmatched-end-tag',
                    tagName: tagName
                });
            }
        }
        
        handleText(token) {
            const text = token.data;
            
            // Merge adjacent text nodes
            const lastChild = this.currentNode.children[this.currentNode.children.length - 1];
            if (lastChild && lastChild.type === 'text') {
                lastChild.data += text;
            } else {
                const textNode = new DOMNode('text', null, null, text);
                this.currentNode.appendChild(textNode);
            }
        }
        
        handleComment(token) {
            const commentNode = new DOMNode('comment', null, null, token.data);
            this.currentNode.appendChild(commentNode);
        }
        
        handleCData(token) {
            // Treat CDATA as text in HTML context
            const textNode = new DOMNode('text', null, null, token.data);
            this.currentNode.appendChild(textNode);
        }
        
        handleDoctype(token) {
            const doctypeNode = new DOMNode('doctype', null, {
                name: token.name,
                publicId: token.publicId,
                systemId: token.systemId
            });
            this.document.appendChild(doctypeNode);
        }
        
        handleImplicitEndTags(tagName) {
            // Check if current element should be implicitly closed
            const currentTag = this.currentNode.tagName;
            
            if (currentTag && IMPLICIT_END_TAGS[currentTag]) {
                if (IMPLICIT_END_TAGS[currentTag].has(tagName)) {
                    // Close current element
                    this.openElements.pop();
                    this.currentNode = this.openElements[this.openElements.length - 1] || this.document;
                    
                    // Recursively check parent
                    this.handleImplicitEndTags(tagName);
                }
            }
        }
        
        ensureProperParent(tagName) {
            if (PARENT_REQUIREMENTS[tagName]) {
                const validParents = PARENT_REQUIREMENTS[tagName];
                let hasValidParent = false;
                
                // Check if any open element is a valid parent
                for (let i = this.openElements.length - 1; i >= 0; i--) {
                    if (validParents.has(this.openElements[i].tagName)) {
                        hasValidParent = true;
                        // Close elements until we reach the valid parent
                        while (this.currentNode !== this.openElements[i]) {
                            this.openElements.pop();
                            this.currentNode = this.openElements[this.openElements.length - 1];
                        }
                        break;
                    }
                }
                
                // If no valid parent found, create one
                if (!hasValidParent) {
                    const parentTag = Array.from(validParents)[0];
                    const parentElement = new DOMNode('element', parentTag);
                    this.currentNode.appendChild(parentElement);
                    this.currentNode = parentElement;
                    this.openElements.push(parentElement);
                }
            }
        }
    }
    
    // Public API
    return {
        build: function(tokens) {
            const builder = new DOMBuilder(tokens);
            return builder.build();
        },
        DOMNode: DOMNode,
        IMPLICIT_END_TAGS: IMPLICIT_END_TAGS,
        PARENT_REQUIREMENTS: PARENT_REQUIREMENTS
    };
})();