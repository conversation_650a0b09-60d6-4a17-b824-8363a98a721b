// HTML Serializer Module - Proper HTML output generation with formatting
window.GmbHTMLSerializer = (function() {
    'use strict';
    
    // Import element classifications
    const ELEMENT_CLASSIFICATIONS = window.GmbReadabilityExtractor?.ELEMENT_CLASSIFICATIONS;
    
    // Serialization configuration
    const SERIALIZATION_CONFIG = {
        // Void (self-closing) elements
        voidElements: new Set([
            'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input',
            'link', 'meta', 'param', 'source', 'track', 'wbr'
        ]),
        
        // Raw text elements (contents are not HTML)
        rawTextElements: new Set(['script', 'style']),
        
        // Escapable raw text elements
        escapableRawTextElements: new Set(['textarea', 'title']),
        
        // Elements that preserve whitespace
        preserveWhitespace: new Set(['pre', 'code', 'textarea']),
        
        // Optional closing tags (HTML5)
        optionalClosingTags: new Set([
            'p', 'li', 'dt', 'dd', 'option', 'optgroup',
            'rt', 'rp', 'td', 'th', 'tr', 'tbody', 'thead', 'tfoot'
        ]),
        
        // Elements that should be on their own line
        blockElements: new Set([
            'address', 'article', 'aside', 'blockquote', 'details', 'dialog',
            'dd', 'div', 'dl', 'dt', 'fieldset', 'figcaption', 'figure',
            'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header',
            'hgroup', 'hr', 'li', 'main', 'nav', 'ol', 'p', 'pre', 'section',
            'table', 'tbody', 'thead', 'tfoot', 'tr', 'ul'
        ]),
        
        // Attributes that don't need values in HTML5
        booleanAttributes: new Set([
            'async', 'autofocus', 'autoplay', 'checked', 'controls', 'default',
            'defer', 'disabled', 'hidden', 'loop', 'multiple', 'muted', 'open',
            'readonly', 'required', 'reversed', 'scoped', 'selected'
        ]),
        
        // Attributes that contain URLs (need special encoding)
        urlAttributes: new Set([
            'action', 'background', 'cite', 'classid', 'codebase', 'data',
            'formaction', 'href', 'icon', 'longdesc', 'manifest', 'poster',
            'profile', 'src', 'usemap'
        ]),
        
        // Order for common attributes (for consistency)
        attributeOrder: [
            'id', 'class', 'name', 'type', 'href', 'src', 'alt', 'title',
            'width', 'height', 'value', 'placeholder', 'style'
        ]
    };
    
    // Default options
    const DEFAULT_OPTIONS = {
        format: 'html', // 'html' or 'xhtml'
        prettyPrint: true,
        indentSize: 2,
        indentChar: ' ',
        maxLineLength: 120,
        preserveWhitespace: true,
        sortAttributes: false,
        removeEmptyAttributes: true,
        quoteAttributes: 'double', // 'single', 'double', or 'auto'
        minimizeBoolean: true,
        collapseWhitespace: false,
        removeComments: false,
        entityEncoding: 'named' // 'named', 'numeric', or 'none'
    };
    
    class HTMLSerializer {
        constructor(options = {}) {
            this.options = Object.assign({}, DEFAULT_OPTIONS, options);
            this.entityProcessor = window.GmbHTMLEntities?.create({
                mode: this.options.entityEncoding === 'none' ? 'minimal' : 'standard',
                encodeNonASCII: this.options.entityEncoding === 'numeric'
            });
            this.indentLevel = 0;
            this.buffer = [];
            this.lineLength = 0;
        }
        
        // Main serialization method
        serialize(node, options = {}) {
            const opts = Object.assign({}, this.options, options);
            this.options = opts;
            this.buffer = [];
            this.indentLevel = 0;
            this.lineLength = 0;
            
            this.serializeNode(node);
            
            return this.buffer.join('');
        }
        
        // Serialize a single node
        serializeNode(node) {
            if (!node) return;
            
            switch (node.type) {
                case 'document':
                    this.serializeDocument(node);
                    break;
                case 'doctype':
                    this.serializeDoctype(node);
                    break;
                case 'element':
                    this.serializeElement(node);
                    break;
                case 'text':
                    this.serializeText(node);
                    break;
                case 'comment':
                    this.serializeComment(node);
                    break;
                case 'cdata':
                    this.serializeCData(node);
                    break;
            }
        }
        
        // Serialize document node
        serializeDocument(node) {
            for (const child of node.children || []) {
                this.serializeNode(child);
            }
        }
        
        // Serialize DOCTYPE
        serializeDoctype(node) {
            const name = node.attributes?.name || 'html';
            const publicId = node.attributes?.publicId;
            const systemId = node.attributes?.systemId;
            
            let doctype = `<!DOCTYPE ${name}`;
            
            if (publicId) {
                doctype += ` PUBLIC "${publicId}"`;
                if (systemId) {
                    doctype += ` "${systemId}"`;
                }
            } else if (systemId) {
                doctype += ` SYSTEM "${systemId}"`;
            }
            
            doctype += '>';
            
            this.write(doctype);
            this.newLine();
        }
        
        // Serialize element
        serializeElement(node) {
            // Critical fix: Check for missing or invalid tagName
            if (!node || !node.tagName) {
                console.warn('HTML Serializer: Node missing tagName:', node);
                return;
            }
            
            const tagName = node.tagName.toLowerCase();
            
            const isVoid = SERIALIZATION_CONFIG.voidElements.has(tagName);
            const isBlock = SERIALIZATION_CONFIG.blockElements.has(tagName);
            const preserveWS = SERIALIZATION_CONFIG.preserveWhitespace.has(tagName);
            
            // Start tag
            if (isBlock && this.options.prettyPrint) {
                this.ensureNewLine();
                this.writeIndent();
            }
            
            this.write('<');
            this.write(tagName);
            
            // Attributes
            this.serializeAttributes(node);
            
            // Close start tag
            if (isVoid) {
                if (this.options.format === 'xhtml') {
                    this.write(' />');
                } else {
                    this.write('>');
                }
                
                if (isBlock && this.options.prettyPrint) {
                    this.newLine();
                }
                return;
            }
            
            this.write('>');
            
            // Children
            const hasChildren = node.children && node.children.length > 0;
            
            if (hasChildren) {
                const oldPreserveWS = this.options.preserveWhitespace;
                if (preserveWS) {
                    this.options.preserveWhitespace = true;
                }
                
                if (isBlock && this.options.prettyPrint && !preserveWS) {
                    this.newLine();
                    this.indentLevel++;
                }
                
                for (const child of node.children) {
                    this.serializeNode(child);
                }
                
                if (isBlock && this.options.prettyPrint && !preserveWS) {
                    this.indentLevel--;
                    this.ensureNewLine();
                    this.writeIndent();
                }
                
                this.options.preserveWhitespace = oldPreserveWS;
            }
            
            // End tag
            this.write('</');
            this.write(tagName);
            this.write('>');
            
            if (isBlock && this.options.prettyPrint) {
                this.newLine();
            }
        }
        
        // Serialize attributes
        serializeAttributes(node) {
            if (!node.attributes || Object.keys(node.attributes).length === 0) {
                return;
            }
            
            let attributes = Object.entries(node.attributes);
            
            // Filter empty attributes if needed
            if (this.options.removeEmptyAttributes) {
                attributes = attributes.filter(([name, value]) => {
                    return value !== '' || SERIALIZATION_CONFIG.booleanAttributes.has(name);
                });
            }
            
            // Sort attributes if needed
            if (this.options.sortAttributes) {
                attributes.sort((a, b) => {
                    const aIndex = SERIALIZATION_CONFIG.attributeOrder.indexOf(a[0]);
                    const bIndex = SERIALIZATION_CONFIG.attributeOrder.indexOf(b[0]);
                    
                    if (aIndex !== -1 && bIndex !== -1) {
                        return aIndex - bIndex;
                    } else if (aIndex !== -1) {
                        return -1;
                    } else if (bIndex !== -1) {
                        return 1;
                    }
                    
                    return a[0].localeCompare(b[0]);
                });
            }
            
            for (const [name, value] of attributes) {
                this.write(' ');
                this.write(name);
                
                // Boolean attributes
                if (SERIALIZATION_CONFIG.booleanAttributes.has(name)) {
                    if (this.options.minimizeBoolean && this.options.format === 'html') {
                        // In HTML5, boolean attributes don't need value
                        continue;
                    }
                }
                
                this.write('=');
                
                // Quote character
                let quote = '"';
                if (this.options.quoteAttributes === 'single') {
                    quote = "'";
                } else if (this.options.quoteAttributes === 'auto') {
                    // Use single quotes if value contains double quotes
                    if (value.includes('"') && !value.includes("'")) {
                        quote = "'";
                    }
                }
                
                this.write(quote);
                
                // Encode attribute value with attribute name for URL handling
                const encoded = this.encodeAttributeValue(value, quote, name);
                this.write(encoded);
                
                this.write(quote);
            }
        }
        
        // Encode attribute value with enhanced URL handling
        encodeAttributeValue(value, quoteChar, attributeName = '') {
            // Special handling for URL attributes to ensure proper formatting
            if (SERIALIZATION_CONFIG.urlAttributes.has(attributeName)) {
                // Ensure URL is properly formatted (no double encoding)
                value = this.normalizeURLValue(value);
            }
            
            if (!this.entityProcessor) {
                // Basic encoding
                value = value.replace(/&/g, '&amp;');
                value = value.replace(/</g, '&lt;');
                value = value.replace(/>/g, '&gt;');
                
                if (quoteChar === '"') {
                    value = value.replace(/"/g, '&quot;');
                } else {
                    value = value.replace(/'/g, '&#39;');
                }
                
                return value;
            }
            
            return this.entityProcessor.encodeAttribute(value, quoteChar);
        }
        
        // Normalize URL values to ensure proper formatting
        normalizeURLValue(url) {
            if (!url || typeof url !== 'string') {
                return url;
            }
            
            // Decode any existing HTML entities in URL first
            url = url.replace(/&amp;/g, '&')
                     .replace(/&lt;/g, '<')
                     .replace(/&gt;/g, '>')
                     .replace(/&quot;/g, '"')
                     .replace(/&#39;/g, "'");
            
            // Validate and clean URL
            try {
                // If it's a valid URL, return it as-is
                new URL(url);
                return url;
            } catch (e) {
                // If not a valid absolute URL, check if it's a relative URL or fragment
                if (url.startsWith('#') || url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
                    return url;
                }
                // Return original if we can't process it
                return url;
            }
        }
        
        // Serialize text node
        serializeText(node) {
            let text = node.data || '';
            
            // Check if we're in a context that preserves whitespace
            const inPreformatted = this.isInPreformattedContext(node);
            
            if (!inPreformatted && this.options.collapseWhitespace) {
                // Collapse whitespace
                text = text.replace(/\s+/g, ' ');
                
                // Trim if at beginning or end
                if (this.isFirstSignificantChild(node)) {
                    text = text.replace(/^\s+/, '');
                }
                if (this.isLastSignificantChild(node)) {
                    text = text.replace(/\s+$/, '');
                }
            }
            
            // Encode text
            if (this.entityProcessor) {
                text = this.entityProcessor.encode(text, 'text');
            } else {
                // Basic encoding
                text = text.replace(/&/g, '&amp;');
                text = text.replace(/</g, '&lt;');
                text = text.replace(/>/g, '&gt;');
            }
            
            // Handle line breaks in pretty print mode
            if (this.options.prettyPrint && !inPreformatted && text.includes('\n')) {
                const lines = text.split('\n');
                for (let i = 0; i < lines.length; i++) {
                    if (i > 0) this.newLine();
                    const line = lines[i].trim();
                    if (line) {
                        this.writeIndent();
                        this.write(line);
                    }
                }
            } else {
                this.write(text);
            }
        }
        
        // Serialize comment
        serializeComment(node) {
            if (this.options.removeComments) {
                return;
            }
            
            const comment = node.data || '';
            
            if (this.options.prettyPrint) {
                this.ensureNewLine();
                this.writeIndent();
            }
            
            this.write('<!--');
            this.write(comment);
            this.write('-->');
            
            if (this.options.prettyPrint) {
                this.newLine();
            }
        }
        
        // Serialize CDATA
        serializeCData(node) {
            const data = node.data || '';
            
            this.write('<![CDATA[');
            this.write(data);
            this.write(']]>');
        }
        
        // Helper methods
        write(str) {
            this.buffer.push(str);
            this.lineLength += str.length;
            
            // Handle max line length
            if (this.options.maxLineLength > 0 && 
                this.lineLength > this.options.maxLineLength) {
                // Find last space to break at
                let breakIndex = -1;
                for (let i = this.buffer.length - 1; i >= 0; i--) {
                    if (this.buffer[i].includes(' ')) {
                        const spaceIndex = this.buffer[i].lastIndexOf(' ');
                        breakIndex = i;
                        // Split the buffer item
                        const before = this.buffer[i].substring(0, spaceIndex);
                        const after = this.buffer[i].substring(spaceIndex + 1);
                        this.buffer[i] = before;
                        this.newLine();
                        this.writeIndent();
                        this.write(after);
                        break;
                    }
                }
            }
        }
        
        writeIndent() {
            const indent = this.options.indentChar.repeat(
                this.options.indentSize * this.indentLevel
            );
            this.write(indent);
        }
        
        newLine() {
            this.buffer.push('\n');
            this.lineLength = 0;
        }
        
        ensureNewLine() {
            if (this.buffer.length > 0 && 
                this.buffer[this.buffer.length - 1] !== '\n') {
                this.newLine();
            }
        }
        
        // Check if node is in preformatted context
        isInPreformattedContext(node) {
            let current = node.parent;
            while (current) {
                if (SERIALIZATION_CONFIG.preserveWhitespace.has(current.tagName)) {
                    return true;
                }
                current = current.parent;
            }
            return false;
        }
        
        // Check if node is first significant child
        isFirstSignificantChild(node) {
            if (!node.parent) return true;
            
            const siblings = node.parent.children;
            for (const sibling of siblings) {
                if (sibling === node) return true;
                if (sibling.type === 'text' && sibling.data.trim()) {
                    return false;
                }
                if (sibling.type === 'element') {
                    return false;
                }
            }
            
            return true;
        }
        
        // Check if node is last significant child
        isLastSignificantChild(node) {
            if (!node.parent) return true;
            
            const siblings = node.parent.children;
            let foundNode = false;
            
            for (const sibling of siblings) {
                if (sibling === node) {
                    foundNode = true;
                    continue;
                }
                if (foundNode) {
                    if (sibling.type === 'text' && sibling.data.trim()) {
                        return false;
                    }
                    if (sibling.type === 'element') {
                        return false;
                    }
                }
            }
            
            return true;
        }
        
        // Minify HTML
        minify(node) {
            const oldOptions = this.options;
            
            this.options = Object.assign({}, this.options, {
                prettyPrint: false,
                collapseWhitespace: true,
                removeComments: true,
                removeEmptyAttributes: true,
                minimizeBoolean: true
            });
            
            const result = this.serialize(node);
            this.options = oldOptions;
            
            return result;
        }
        
        // Pretty print HTML
        prettyPrint(node) {
            const oldOptions = this.options;
            
            this.options = Object.assign({}, this.options, {
                prettyPrint: true,
                collapseWhitespace: false,
                sortAttributes: true
            });
            
            const result = this.serialize(node);
            this.options = oldOptions;
            
            return result;
        }
        
        // Serialize to XML
        serializeXML(node) {
            const oldOptions = this.options;
            
            this.options = Object.assign({}, this.options, {
                format: 'xhtml',
                minimizeBoolean: false,
                quoteAttributes: 'double'
            });
            
            const result = this.serialize(node);
            this.options = oldOptions;
            
            return result;
        }
        
        // Get outer HTML (include the node itself)
        getOuterHTML(node) {
            return this.serialize(node);
        }
        
        // Get inner HTML (only children)
        getInnerHTML(node) {
            this.buffer = [];
            this.indentLevel = 0;
            this.lineLength = 0;
            
            if (node.children) {
                for (const child of node.children) {
                    this.serializeNode(child);
                }
            }
            
            return this.buffer.join('');
        }
        
        // Helper: Get text content of a node (for debugging)
        getNodeTextContent(node) {
            if (!node) return '';
            
            if (node.type === 'text') {
                return node.data || '';
            }
            
            let text = '';
            if (node.children) {
                for (const child of node.children) {
                    text += this.getNodeTextContent(child);
                }
            }
            
            return text.substring(0, 100); // Limit for debugging
        }
        
        // Create HTML fragment from string
        static createFragment(html) {
            // This would typically use the tokenizer and DOM builder
            // For now, return a simple wrapper
            return {
                type: 'document-fragment',
                children: [{
                    type: 'text',
                    data: html
                }]
            };
        }
    }
    
    // Public API
    return {
        create: function(options) {
            return new HTMLSerializer(options);
        },
        
        // Quick serialization methods
        serialize: function(node, options) {
            const serializer = new HTMLSerializer(options);
            return serializer.serialize(node);
        },
        
        minify: function(node, options) {
            const serializer = new HTMLSerializer(options);
            return serializer.minify(node);
        },
        
        prettyPrint: function(node, options) {
            const serializer = new HTMLSerializer(options);
            return serializer.prettyPrint(node);
        },
        
        // Export configuration
        SERIALIZATION_CONFIG: SERIALIZATION_CONFIG,
        DEFAULT_OPTIONS: DEFAULT_OPTIONS
    };
})();