// Link Processing Module - Comprehensive link handling with security awareness
window.GmbLinkProcessor = (function() {
    'use strict';
    
    // Safe URL schemes
    const SAFE_URL_SCHEMES = new Set([
        'http:', 'https:', 'ftp:', 'ftps:', 'mailto:', 'tel:', 'sms:', 'data:'
    ]);
    
    // Unsafe URL patterns that should be removed
    const UNSAFE_URL_PATTERNS = [
        /^javascript:/i,
        /^vbscript:/i,
        /^file:/i,
        /^res:/i,
        /^ms-its:/i,
        /^mhtml:/i,
        /^mk:/i,
        /^about:/i,
        /^wysiwyg:/i,
        /^data:(?!image\/)/i  // Allow data URIs only for images
    ];
    
    // Common tracking parameters to remove
    const TRACKING_PARAMS = new Set([
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
        'fbclid', 'gclid', 'dclid', 'msclkid', 'mc_cid', 'mc_eid',
        '_ga', '_gid', '_gat', '_gl', '_gac',
        'pk_campaign', 'pk_kwd', 'pk_source', 'pk_medium', 'pk_content',
        'affiliate', 'ref', 'referer', 'referrer'
    ]);
    
    // Link types for classification
    const LinkType = {
        INTERNAL: 'internal',
        EXTERNAL: 'external',
        ANCHOR: 'anchor',
        JAVASCRIPT: 'javascript',
        MAILTO: 'mailto',
        TEL: 'tel',
        FILE: 'file',
        DATA: 'data',
        UNKNOWN: 'unknown'
    };
    
    class LinkProcessor {
        constructor(options = {}) {
            this.baseURL = options.baseURL || this.detectBaseURL();
            this.documentURL = options.documentURL || window.location.href;
            this.removeTracking = options.removeTracking !== false;
            this.removeJavaScriptLinks = options.removeJavaScriptLinks !== false;
            this.resolveRelative = options.resolveRelative !== false;
            this.preserveAnchors = options.preserveAnchors !== false;
            
            // Performance optimizations
            this.enableCaching = options.enableCaching !== false;
            this.batchSize = options.batchSize || 100;
            
            // Caching for performance
            this.urlCache = new Map();
            this.classificationCache = new Map();
            
            // Parse URLs using enhanced detection
            try {
                this.baseURLObj = new URL(this.baseURL);
                this.documentURLObj = new URL(this.documentURL);
            } catch (e) {
                this.baseURLObj = null;
                this.documentURLObj = null;
            }
        }
        
        // Enhanced base URL detection using document.baseURI and document.documentURI
        detectBaseURL() {
            // Try document.baseURI first (most reliable)
            if (typeof document !== 'undefined' && document.baseURI) {
                return document.baseURI;
            }
            
            // Try base element
            if (typeof document !== 'undefined') {
                const baseEl = document.querySelector('base[href]');
                if (baseEl) {
                    const baseHref = baseEl.getAttribute('href');
                    if (baseHref) {
                        try {
                            return new URL(baseHref, window.location.href).href;
                        } catch (e) {
                            // Fall through to window.location.href
                        }
                    }
                }
            }
            
            // Fallback to window.location.href
            return window.location.href;
        }
        
        // Process all links in a DOM tree
        processLinks(domNode) {
            const links = this.findAllLinks(domNode);
            const results = {
                total: links.length,
                processed: 0,
                removed: 0,
                resolved: 0,
                linksByType: {},
                errors: []
            };
            
            for (const linkType of Object.values(LinkType)) {
                results.linksByType[linkType] = 0;
            }
            
            for (const link of links) {
                try {
                    const result = this.processLink(link);
                    if (result.removed) {
                        results.removed++;
                    } else {
                        results.processed++;
                        if (result.resolved) {
                            results.resolved++;
                        }
                    }
                    results.linksByType[result.type]++;
                } catch (error) {
                    results.errors.push({
                        element: link,
                        error: error.message
                    });
                }
            }
            
            return results;
        }
        
        // Find all links in DOM tree
        findAllLinks(domNode) {
            const links = [];
            
            // Find <a> tags
            links.push(...this.findElementsWithAttribute(domNode, 'a', 'href'));
            
            // Find <area> tags (image maps)
            links.push(...this.findElementsWithAttribute(domNode, 'area', 'href'));
            
            // Find <form> tags
            links.push(...this.findElementsWithAttribute(domNode, 'form', 'action'));
            
            // Find other elements with URL attributes
            const urlAttributes = ['src', 'data', 'poster', 'background'];
            for (const attr of urlAttributes) {
                links.push(...this.findElementsWithAttribute(domNode, '*', attr));
            }
            
            return links;
        }
        
        // Helper to find elements with specific attribute
        findElementsWithAttribute(domNode, tagName, attribute) {
            const elements = [];
            
            // Critical fix: Handle case sensitivity and ensure proper comparison
            if (domNode && domNode.tagName) {
                const nodeTagName = domNode.tagName.toLowerCase();
                const searchTagName = tagName.toLowerCase();
                
                if (nodeTagName === searchTagName || tagName === '*') {
                    if (domNode.hasAttribute && domNode.hasAttribute(attribute)) {
                        elements.push({
                            element: domNode,
                            attribute: attribute,
                            url: domNode.getAttribute(attribute)
                        });
                    }
                }
            }
            
            // Recursively search children
            if (domNode && domNode.children) {
                for (const child of domNode.children) {
                    elements.push(...this.findElementsWithAttribute(child, tagName, attribute));
                }
            }
            
            return elements;
        }
        
        // Process individual link with enhanced error handling
        processLink(linkInfo) {
            const { element, attribute, url } = linkInfo;
            const result = {
                original: url,
                processed: url,
                type: LinkType.UNKNOWN,
                removed: false,
                resolved: false,
                isExternal: false,
                error: null
            };
            
            // Early validation
            if (!url || !url.trim()) {
                result.type = LinkType.UNKNOWN;
                result.error = 'Empty or null URL';
                return result;
            }
            
            try {
                // Classify link type
                result.type = this.classifyLink(url);
                
                // Handle JavaScript links
                if (result.type === LinkType.JAVASCRIPT && this.removeJavaScriptLinks) {
                    this.removeJavaScriptLink(element, attribute);
                    result.removed = true;
                    return result;
                }
                
                // Check if URL is safe
                if (this.isUnsafeURL(url)) {
                    this.removeUnsafeLink(element, attribute);
                    result.removed = true;
                    result.error = 'Unsafe URL pattern detected';
                    return result;
                }
                
                // Resolve relative URLs with enhanced error handling
                if (this.resolveRelative && this.isRelativeURL(url)) {
                    try {
                        const resolved = this.resolveURL(url);
                        if (resolved && resolved !== url) {
                            element.setAttribute(attribute, resolved);
                            result.processed = resolved;
                            result.resolved = true;
                        }
                    } catch (resolveError) {
                        result.error = `URL resolution failed: ${resolveError.message}`;
                        // Continue processing with original URL
                    }
                }
                
                // Remove tracking parameters with error handling
                if (this.removeTracking && (result.type === LinkType.INTERNAL || result.type === LinkType.EXTERNAL)) {
                    try {
                        const cleaned = this.removeTrackingParams(result.processed);
                        if (cleaned !== result.processed) {
                            element.setAttribute(attribute, cleaned);
                            result.processed = cleaned;
                        }
                    } catch (cleanError) {
                        result.error = `Tracking parameter removal failed: ${cleanError.message}`;
                        // Continue with unprocessed URL
                    }
                }
                
                // Check if external with error handling
                try {
                    result.isExternal = this.isExternalLink(result.processed);
                } catch (externalError) {
                    result.error = `External link detection failed: ${externalError.message}`;
                    result.isExternal = false;
                }
                
            } catch (error) {
                result.error = `General processing error: ${error.message}`;
                console.warn('Link processing error:', error, 'for URL:', url);
            }
            
            return result;
        }
        
        // Classify link type with caching for performance
        classifyLink(url) {
            const trimmed = url.trim();
            
            // Check cache for performance
            if (this.enableCaching && this.classificationCache.has(trimmed)) {
                return this.classificationCache.get(trimmed);
            }
            
            let linkType;
            
            // Anchor links
            if (trimmed.startsWith('#')) {
                linkType = LinkType.ANCHOR;
            }
            // JavaScript links
            else if (/^javascript:/i.test(trimmed)) {
                linkType = LinkType.JAVASCRIPT;
            }
            // Mailto links
            else if (/^mailto:/i.test(trimmed)) {
                linkType = LinkType.MAILTO;
            }
            // Tel links
            else if (/^tel:/i.test(trimmed)) {
                linkType = LinkType.TEL;
            }
            // File links
            else if (/^file:/i.test(trimmed)) {
                linkType = LinkType.FILE;
            }
            // Data URIs
            else if (/^data:/i.test(trimmed)) {
                linkType = LinkType.DATA;
            }
            // HTTP(S) or relative URLs
            else if (/^https?:/i.test(trimmed) || this.isRelativeURL(trimmed)) {
                linkType = this.isExternalLink(trimmed) ? LinkType.EXTERNAL : LinkType.INTERNAL;
            }
            else {
                linkType = LinkType.UNKNOWN;
            }
            
            // Cache result for performance
            if (this.enableCaching) {
                this.classificationCache.set(trimmed, linkType);
            }
            
            return linkType;
        }
        
        // Check if URL is unsafe
        isUnsafeURL(url) {
            for (const pattern of UNSAFE_URL_PATTERNS) {
                if (pattern.test(url)) {
                    return true;
                }
            }
            return false;
        }
        
        // Check if URL is relative
        isRelativeURL(url) {
            // Check for protocol-relative URLs
            if (url.startsWith('//')) {
                return false;
            }
            
            // Check for absolute URLs
            try {
                const urlObj = new URL(url);
                return false;
            } catch (e) {
                // If URL constructor throws, it's likely relative
                return true;
            }
        }
        
        // Resolve relative URL to absolute using enhanced approach with caching
        resolveURL(relativeURL) {
            if (!this.baseURLObj) {
                return relativeURL;
            }
            
            // Check cache for performance
            if (this.enableCaching && this.urlCache.has(relativeURL)) {
                return this.urlCache.get(relativeURL);
            }
            
            try {
                let resolved;
                
                // Handle fragment-only URLs (from working extension pattern)
                if (relativeURL.startsWith('#')) {
                    // If base and document URIs match, preserve fragment as-is
                    if (this.baseURL === this.documentURL && this.preserveAnchors) {
                        resolved = relativeURL;
                    } else {
                        // Otherwise, resolve against base URL
                        resolved = this.baseURLObj.href.split('#')[0] + relativeURL;
                    }
                } else if (relativeURL.startsWith('//')) {
                    // Handle protocol-relative URLs
                    resolved = this.baseURLObj.protocol + relativeURL;
                } else {
                    // Enhanced URL resolution using base URI (working extension approach)
                    const resolvedURL = new URL(relativeURL, this.baseURLObj);
                    resolved = resolvedURL.href;
                }
                
                // Cache result for performance
                if (this.enableCaching) {
                    this.urlCache.set(relativeURL, resolved);
                }
                
                return resolved;
            } catch (e) {
                console.warn('Failed to resolve URL:', relativeURL, 'against base:', this.baseURL, e);
                return relativeURL;
            }
        }
        
        // Check if link is external
        isExternalLink(url) {
            if (!this.baseURLObj) {
                return false;
            }
            
            try {
                const urlObj = new URL(url);
                return urlObj.hostname !== this.baseURLObj.hostname;
            } catch (e) {
                return false;
            }
        }
        
        // Remove JavaScript link using working extension approach
        removeJavaScriptLink(element, attribute) {
            if (element.tagName === 'a' || element.tagName === 'A') {
                // Follow working extension pattern: replace entire anchor with text node
                const textContent = element.textContent || '';
                const textNode = element.ownerDocument ? 
                    element.ownerDocument.createTextNode(textContent) : 
                    document.createTextNode(textContent);
                
                // Replace element with text node (working extension approach)
                if (element.parentNode) {
                    element.parentNode.replaceChild(textNode, element);
                }
            } else {
                // For other elements (img, etc.), remove the JavaScript attribute
                element.removeAttribute(attribute);
                
                // Mark as unsafe for additional security
                element.setAttribute('data-unsafe-js-removed', 'true');
            }
        }
        
        // Remove unsafe link
        removeUnsafeLink(element, attribute) {
            element.removeAttribute(attribute);
            
            // Add indicator that link was removed
            element.setAttribute('data-unsafe-link-removed', 'true');
        }
        
        // Remove tracking parameters from URL
        removeTrackingParams(url) {
            try {
                const urlObj = new URL(url);
                const params = new URLSearchParams(urlObj.search);
                
                let changed = false;
                for (const param of TRACKING_PARAMS) {
                    if (params.has(param)) {
                        params.delete(param);
                        changed = true;
                    }
                }
                
                if (changed) {
                    urlObj.search = params.toString();
                    return urlObj.href;
                }
            } catch (e) {
                // If URL parsing fails, return original
            }
            
            return url;
        }
        
        // Calculate link density for an element
        calculateLinkDensity(element) {
            const textLength = this.getTextLength(element);
            const linkTextLength = this.getLinkTextLength(element);
            
            if (textLength === 0) {
                return 0;
            }
            
            return linkTextLength / textLength;
        }
        
        // Get total text length
        getTextLength(element) {
            let length = 0;
            
            if (element.type === 'text') {
                length += (element.data || '').length;
            }
            
            for (const child of element.children || []) {
                length += this.getTextLength(child);
            }
            
            return length;
        }
        
        // Get link text length
        getLinkTextLength(element) {
            let length = 0;
            
            if (element.tagName === 'a' || element.tagName === 'A') {
                return this.getTextLength(element);
            }
            
            for (const child of element.children || []) {
                length += this.getLinkTextLength(child);
            }
            
            return length;
        }
        
        // Extract all URLs from srcset attribute
        parseSrcset(srcset) {
            const urls = [];
            if (!srcset) return urls;
            
            // Split by comma, but be careful about commas in URLs
            const candidates = srcset.split(/,\s*(?=(?:[^\(]*\([^\)]*\))*[^\)]*$)/);
            
            for (const candidate of candidates) {
                const parts = candidate.trim().split(/\s+/);
                if (parts.length > 0) {
                    urls.push({
                        url: parts[0],
                        descriptor: parts[1] || ''
                    });
                }
            }
            
            return urls;
        }
        
        // Process srcset attribute
        processSrcset(element) {
            const srcset = element.getAttribute('srcset');
            if (!srcset) return;
            
            const sources = this.parseSrcset(srcset);
            const processed = [];
            
            for (const source of sources) {
                if (this.isRelativeURL(source.url)) {
                    source.url = this.resolveURL(source.url);
                }
                
                if (!this.isUnsafeURL(source.url)) {
                    processed.push(`${source.url} ${source.descriptor}`.trim());
                }
            }
            
            if (processed.length > 0) {
                element.setAttribute('srcset', processed.join(', '));
            } else {
                element.removeAttribute('srcset');
            }
        }
    }
    
    // Public API
    return {
        create: function(options) {
            return new LinkProcessor(options);
        },
        
        // Utility functions
        isValidURL: function(url) {
            try {
                new URL(url);
                return true;
            } catch (e) {
                return false;
            }
        },
        
        isSafeURL: function(url) {
            for (const pattern of UNSAFE_URL_PATTERNS) {
                if (pattern.test(url)) {
                    return false;
                }
            }
            return true;
        },
        
        // Export constants
        LinkType: LinkType,
        SAFE_URL_SCHEMES: SAFE_URL_SCHEMES,
        TRACKING_PARAMS: TRACKING_PARAMS
    };
})();