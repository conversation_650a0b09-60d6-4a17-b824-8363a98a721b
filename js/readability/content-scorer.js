// Advanced Content Scoring Module - Sophisticated element scoring algorithm
window.GmbContentScorer = (function() {
    'use strict';
    
    // Scoring rules and weights
    const SCORING_RULES = {
        // Tag-based scores
        tagScores: {
            'article': 30,
            'main': 25,
            'section': 15,
            'div': 5,
            'p': 3,
            'pre': 3,
            'td': 3,
            'blockquote': 3,
            'figure': 3,
            'ol': -3,
            'ul': -3,
            'li': -3,
            'h1': -5,
            'h2': -5,
            'h3': -5,
            'h4': -5,
            'h5': -5,
            'h6': -5,
            'form': -3,
            'nav': -5,
            'aside': -5,
            'footer': -5,
            'header': -5,
            'address': -3,
            'dd': -3,
            'dt': -3,
            'dl': -3,
            'menu': -5
        },
        
        // Class and ID patterns
        classWeights: {
            positive: /article|body|content|entry|hentry|h-entry|main|page|pagination|post|text|blog|story|artikel|articulo|artigo|contenido|contenuto|inhalt/i,
            negative: /banner|breadcrumb|combx|comment|community|cover-wrap|disqus|extra|foot|footer|footnote|gdpr|masthead|media|meta|outbrain|promo|popup|related|scroll|share|shoutbox|sidebar|skyscraper|social|sponsor|shopping|tags|tool|widget|nav|navigation|menu|header|ad|advertisement|agegate|popup|modal|signin|signup|register|login|form/i
        },
        
        // Special indicators for quality content
        qualityIndicators: {
            positive: /entry-content|post-content|article-body|article-content|story-body|story-content|field-body|content-body|content-text/i,
            negative: /widget-area|sidebar-wrapper|ad-container|advertisement-area|social-share|comment-section/i
        },
        
        // Density thresholds
        thresholds: {
            minTextLength: 25,
            minParagraphLength: 80,
            maxLinkDensity: 0.33,
            minScore: 20,
            minContentScore: 20,
            siblingScoreThreshold: 0.2,
            siblingNodeThreshold: 10
        }
    };
    
    class ContentScorer {
        constructor(options = {}) {
            this.config = Object.assign({}, SCORING_RULES, options);
            this.candidates = new Map();
        }
        
        // Score all elements in DOM tree
        scoreTree(domNode) {
            // Reset candidates
            this.candidates.clear();
            
            // Initialize and score all elements
            this.walkTree(domNode, (node) => {
                if (node.type === 'element') {
                    this.initializeNode(node);
                    this.scoreNode(node);
                }
            });
            
            // Propagate scores to ancestors
            this.propagateScores();
            
            // Find top candidates
            return this.getTopCandidates();
        }
        
        // Walk DOM tree
        walkTree(node, callback) {
            callback(node);
            
            for (const child of node.children || []) {
                this.walkTree(child, callback);
            }
        }
        
        // Initialize node for scoring
        initializeNode(node) {
            const candidate = {
                node: node,
                contentScore: 0,
                textLength: 0,
                linkTextLength: 0,
                paragraphCount: 0,
                imageCount: 0,
                density: 0,
                className: (node.getAttribute('class') || '').toLowerCase(),
                id: (node.getAttribute('id') || '').toLowerCase()
            };
            
            // Base score from tag
            if (this.config.tagScores[node.tagName]) {
                candidate.contentScore = this.config.tagScores[node.tagName];
            }
            
            // Class and ID scoring
            candidate.contentScore += this.getClassWeight(candidate.className, candidate.id);
            
            this.candidates.set(node, candidate);
        }
        
        // Score individual node
        scoreNode(node) {
            const candidate = this.candidates.get(node);
            if (!candidate) return;
            
            // Calculate text metrics
            const metrics = this.calculateMetrics(node);
            Object.assign(candidate, metrics);
            
            // Score based on content
            if (node.tagName === 'p' || node.tagName === 'td' || node.tagName === 'pre') {
                this.scoreParagraph(node, candidate);
            }
            
            // Score based on density
            this.scoreDensity(candidate);
            
            // Score based on content patterns
            this.scoreContentPatterns(node, candidate);
            
            // Score based on sibling content
            this.scoreSiblings(node, candidate);
        }
        
        // Calculate content metrics
        calculateMetrics(node) {
            const metrics = {
                textLength: 0,
                linkTextLength: 0,
                paragraphCount: 0,
                imageCount: 0,
                listCount: 0,
                headingCount: 0,
                wordCount: 0,
                commaCount: 0,
                sentenceCount: 0
            };
            
            // Direct text content
            for (const child of node.children || []) {
                if (child.type === 'text') {
                    const text = child.data || '';
                    metrics.textLength += text.length;
                    metrics.wordCount += text.split(/\s+/).filter(w => w.length > 0).length;
                    metrics.commaCount += (text.match(/,/g) || []).length;
                    metrics.sentenceCount += (text.match(/[.!?]+/g) || []).length;
                } else if (child.type === 'element') {
                    // Count elements
                    switch (child.tagName) {
                        case 'p':
                            metrics.paragraphCount++;
                            break;
                        case 'img':
                            metrics.imageCount++;
                            break;
                        case 'ul':
                        case 'ol':
                            metrics.listCount++;
                            break;
                        case 'h1':
                        case 'h2':
                        case 'h3':
                        case 'h4':
                        case 'h5':
                        case 'h6':
                            metrics.headingCount++;
                            break;
                        case 'a':
                            // Calculate link text
                            const linkText = this.getTextContent(child);
                            metrics.linkTextLength += linkText.length;
                            break;
                    }
                    
                    // Recursively add child metrics
                    const childMetrics = this.calculateMetrics(child);
                    for (const key in childMetrics) {
                        metrics[key] += childMetrics[key];
                    }
                }
            }
            
            // Calculate density
            metrics.density = metrics.textLength > 0 ? 
                metrics.linkTextLength / metrics.textLength : 0;
            
            return metrics;
        }
        
        // Get text content of element
        getTextContent(node) {
            let text = '';
            
            if (node.type === 'text') {
                text = node.data || '';
            } else if (node.type === 'element') {
                for (const child of node.children || []) {
                    text += this.getTextContent(child);
                }
            }
            
            return text;
        }
        
        // Get class weight
        getClassWeight(className, id) {
            let weight = 0;
            
            // Check class
            if (className) {
                if (this.config.classWeights.positive.test(className)) {
                    weight += 25;
                }
                if (this.config.classWeights.negative.test(className)) {
                    weight -= 25;
                }
                if (this.config.qualityIndicators.positive.test(className)) {
                    weight += 15;
                }
                if (this.config.qualityIndicators.negative.test(className)) {
                    weight -= 15;
                }
            }
            
            // Check ID
            if (id) {
                if (this.config.classWeights.positive.test(id)) {
                    weight += 25;
                }
                if (this.config.classWeights.negative.test(id)) {
                    weight -= 25;
                }
            }
            
            return weight;
        }
        
        // Score paragraph elements
        scoreParagraph(node, candidate) {
            const text = this.getTextContent(node);
            const textLength = text.length;
            
            // Skip short paragraphs
            if (textLength < this.config.thresholds.minParagraphLength) {
                return;
            }
            
            // Base paragraph score
            let score = 1;
            
            // Length bonus
            score += Math.min(Math.floor(textLength / 100), 3);
            
            // Comma bonus (indicates complex sentences)
            const commas = (text.match(/,/g) || []).length;
            score += Math.min(commas, 3);
            
            // Quality indicators
            if (/\.(?:\s|$)/.test(text)) {
                score += 1; // Proper sentence ending
            }
            
            // Check for common article patterns
            if (/^(By |Posted |Written |Published )/i.test(text)) {
                score -= 1; // Likely metadata
            }
            
            candidate.contentScore += score;
            
            // Propagate to parent
            if (node.parent && this.candidates.has(node.parent)) {
                const parentCandidate = this.candidates.get(node.parent);
                parentCandidate.contentScore += score;
                
                // Grandparent gets half
                if (node.parent.parent && this.candidates.has(node.parent.parent)) {
                    const grandparentCandidate = this.candidates.get(node.parent.parent);
                    grandparentCandidate.contentScore += score / 2;
                }
            }
        }
        
        // Score based on link density using working extension approach
        scoreDensity(candidate) {
            const { density, textLength } = candidate;
            
            // Apply working extension penalty formula: score = contentScore * (1 - linkDensity)
            if (density > 0 && candidate.contentScore > 0) {
                const linkDensityPenalty = density;
                candidate.contentScore = candidate.contentScore * (1 - linkDensityPenalty);
            }
            
            // Additional penalty for very high link density (likely navigation)
            if (density > this.config.thresholds.maxLinkDensity) {
                const extraPenalty = (density - this.config.thresholds.maxLinkDensity) * 25;
                candidate.contentScore -= extraPenalty;
            }
            
            // Bonus for content with low link density and substantial text
            if (textLength > 500 && density < 0.1) {
                candidate.contentScore += 10;
            }
            
            // Store original score for debugging
            candidate.originalScore = candidate.contentScore / (1 - Math.max(0, Math.min(1, density)));
        }
        
        // Score based on content patterns
        scoreContentPatterns(node, candidate) {
            const { paragraphCount, imageCount, listCount, headingCount, textLength } = candidate;
            
            // Paragraph density bonus
            if (paragraphCount > 3 && textLength > 500) {
                candidate.contentScore += Math.min(paragraphCount * 2, 20);
            }
            
            // Image bonus (but not too many)
            if (imageCount > 0 && imageCount <= 3) {
                candidate.contentScore += imageCount * 2;
            } else if (imageCount > 10) {
                candidate.contentScore -= (imageCount - 10) * 2; // Gallery penalty
            }
            
            // List penalty (navigation menus)
            if (listCount > 3) {
                candidate.contentScore -= listCount * 3;
            }
            
            // Heading distribution
            if (headingCount > 0 && headingCount <= 5) {
                candidate.contentScore += headingCount * 2; // Well-structured content
            }
            
            // Check for data tables
            if (node.tagName === 'table') {
                const dataTableScore = this.scoreDataTable(node);
                candidate.contentScore += dataTableScore;
            }
        }
        
        // Score siblings
        scoreSiblings(node, candidate) {
            if (!node.parent) return;
            
            const siblings = node.parent.children || [];
            let siblingScore = 0;
            let siblingCount = 0;
            
            for (const sibling of siblings) {
                if (sibling === node) continue;
                
                const siblingCandidate = this.candidates.get(sibling);
                if (siblingCandidate && siblingCandidate.contentScore > 0) {
                    siblingScore += siblingCandidate.contentScore;
                    siblingCount++;
                }
            }
            
            if (siblingCount > 0) {
                const avgSiblingScore = siblingScore / siblingCount;
                candidate.contentScore += avgSiblingScore * 0.2; // 20% of sibling average
            }
        }
        
        // Score data tables
        scoreDataTable(node) {
            let score = 0;
            const cells = this.findTableCells(node);
            
            // Count cells with substantial text
            let textCells = 0;
            let numberCells = 0;
            
            for (const cell of cells) {
                const text = this.getTextContent(cell).trim();
                if (text.length > 10) {
                    textCells++;
                }
                if (/^\d+([.,]\d+)*$/.test(text)) {
                    numberCells++;
                }
            }
            
            // Data tables have good text/number distribution
            if (textCells > 5 && numberCells > 3) {
                score += 10;
            } else if (cells.length > 10 && textCells < 3) {
                score -= 10; // Layout table
            }
            
            return score;
        }
        
        // Find all table cells
        findTableCells(table) {
            const cells = [];
            
            this.walkTree(table, (node) => {
                if (node.tagName === 'td' || node.tagName === 'th') {
                    cells.push(node);
                }
            });
            
            return cells;
        }
        
        // Propagate scores up the tree
        propagateScores() {
            // Sort candidates by depth (deepest first)
            const sortedCandidates = Array.from(this.candidates.entries())
                .sort((a, b) => (b[0].metadata?.depth || 0) - (a[0].metadata?.depth || 0));
            
            for (const [node, candidate] of sortedCandidates) {
                if (candidate.contentScore <= 0) continue;
                
                // Propagate to parent
                if (node.parent && this.candidates.has(node.parent)) {
                    const parentCandidate = this.candidates.get(node.parent);
                    const propagationScore = candidate.contentScore * 0.3;
                    parentCandidate.contentScore += propagationScore;
                }
            }
        }
        
        // Get top scoring candidates
        getTopCandidates(limit = 5) {
            const scoredCandidates = Array.from(this.candidates.values())
                .filter(c => c.contentScore >= this.config.thresholds.minContentScore)
                .sort((a, b) => b.contentScore - a.contentScore)
                .slice(0, limit);
            
            // Enhance with additional metrics
            for (const candidate of scoredCandidates) {
                candidate.quality = this.calculateQuality(candidate);
            }
            
            return scoredCandidates;
        }
        
        // Calculate content quality score
        calculateQuality(candidate) {
            const { textLength, wordCount, sentenceCount, paragraphCount, density } = candidate;
            
            let quality = 0;
            
            // Text length factor
            if (textLength > 1000) quality += 20;
            else if (textLength > 500) quality += 10;
            else if (textLength > 200) quality += 5;
            
            // Word count factor
            if (wordCount > 300) quality += 15;
            else if (wordCount > 150) quality += 10;
            else if (wordCount > 50) quality += 5;
            
            // Sentence structure
            if (sentenceCount > 5) {
                const avgWordsPerSentence = wordCount / sentenceCount;
                if (avgWordsPerSentence > 10 && avgWordsPerSentence < 30) {
                    quality += 10; // Good sentence length
                }
            }
            
            // Paragraph structure
            if (paragraphCount > 3) {
                const avgWordsPerParagraph = wordCount / paragraphCount;
                if (avgWordsPerParagraph > 40 && avgWordsPerParagraph < 200) {
                    quality += 10; // Good paragraph length
                }
            }
            
            // Link density penalty
            if (density > 0.3) quality -= 10;
            else if (density > 0.2) quality -= 5;
            
            return quality;
        }
        
        // Find content by scoring algorithm
        findContent(domNode, options = {}) {
            const candidates = this.scoreTree(domNode);
            
            if (candidates.length === 0) {
                return null;
            }
            
            // Return best candidate
            const best = candidates[0];
            
            // Apply minimum quality threshold
            const minQuality = options.minQuality || 30;
            if (best.quality < minQuality) {
                return null;
            }
            
            return {
                node: best.node,
                score: best.contentScore,
                quality: best.quality,
                metrics: {
                    textLength: best.textLength,
                    wordCount: best.wordCount,
                    paragraphCount: best.paragraphCount,
                    density: best.density
                }
            };
        }
    }
    
    // Public API
    return {
        create: function(options) {
            return new ContentScorer(options);
        },
        
        // Quick scoring function
        scoreElement: function(element, options) {
            const scorer = new ContentScorer(options);
            scorer.initializeNode(element);
            scorer.scoreNode(element);
            return scorer.candidates.get(element);
        },
        
        // Export constants
        SCORING_RULES: SCORING_RULES
    };
})();