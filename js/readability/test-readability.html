<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Readability Extractor Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        button {
            background: #7C3AED;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #6B2FD6;
        }
        textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }
        #output {
            background: #f8f8f8;
            padding: 20px;
            border-radius: 4px;
            min-height: 200px;
            overflow: auto;
        }
        .metrics {
            background: #e8f4fd;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .test-content {
            border: 2px dashed #ddd;
            padding: 20px;
            margin-top: 20px;
            background: #fff;
        }
        .hidden { display: none; }
        .ad { background: #ffe0e0; padding: 10px; }
        .social { background: #e0e0ff; padding: 10px; }
    </style>
</head>
<body>
    <h1>Advanced Readability Extractor Test</h1>
    
    <div class="controls">
        <button onclick="testExtraction()">Test Extraction</button>
        <button onclick="testWithCustomHTML()">Test Custom HTML</button>
        <button onclick="testLegacyMode()">Test Legacy Mode</button>
        <button onclick="benchmarkPerformance()">Benchmark Performance</button>
        <button onclick="testModules()">Test Individual Modules</button>
    </div>

    <div class="container">
        <div class="panel">
            <h2>Input HTML</h2>
            <textarea id="inputHTML">
<article class="main-content">
    <header>
        <h1>The Future of Web Development</h1>
        <div class="byline">By John Doe | March 15, 2024</div>
    </header>
    
    <nav class="breadcrumb">Home > Tech > Web Development</nav>
    
    <div class="social-share">
        <a href="javascript:alert('share')">Share on Facebook</a>
        <a href="javascript:void(0)">Tweet this</a>
    </div>
    
    <div class="content-body">
        <p>Web development is constantly evolving with new frameworks and technologies emerging every year. <a href="https://example.com">Learn more</a> about the latest trends.</p>
        
        <div class="ad advertisement">
            <img src="pixel.gif" width="1" height="1" />
            Advertisement: Buy our product!
        </div>
        
        <p>React, Vue, and Angular continue to dominate the frontend landscape. Each framework has its strengths:</p>
        
        <ul>
            <li><strong>React</strong>: Flexible and component-based</li>
            <li><strong>Vue</strong>: Easy to learn and integrate</li>
            <li><strong>Angular</strong>: Full-featured enterprise solution</li>
        </ul>
        
        <div style="display:none">Hidden tracking content</div>
        
        <p>The future looks bright for web developers who stay current with these technologies.</p>
        
        <table>
            <tr><td>Framework</td><td>Popularity</td></tr>
            <tr><td>React</td><td>High</td></tr>
            <tr><td>Vue</td><td>Medium</td></tr>
        </table>
    </div>
    
    <footer class="article-footer">
        <div class="related-articles">
            <h3>Related Articles</h3>
            <a href="#">10 Best JavaScript Libraries</a>
        </div>
    </footer>
</article>

<aside class="sidebar widget">
    <h3>Popular Posts</h3>
    <ul>
        <li><a href="#">Post 1</a></li>
        <li><a href="#">Post 2</a></li>
    </ul>
</aside>
            </textarea>
        </div>
        
        <div class="panel">
            <h2>Extracted Content</h2>
            <div id="output"></div>
            <div id="metrics" class="metrics"></div>
        </div>
    </div>

    <div class="test-content">
        <h2>Test Article on Page</h2>
        <article>
            <p>This is a test article that exists on the page itself. It contains multiple paragraphs of content that should be extracted by the readability extractor.</p>
            <p>The extractor should be able to identify this as the main content area and extract it properly, while ignoring navigation elements and advertisements.</p>
            <div class="ad">This is an advertisement that should be removed.</div>
            <p>Final paragraph of the test content.</p>
        </article>
    </div>

    <!-- Load all the readability modules -->
    <script src="../readability/html-tokenizer.js"></script>
    <script src="../readability/dom-builder.js"></script>
    <script src="../readability/link-processor.js"></script>
    <script src="../readability/content-scorer.js"></script>
    <script src="../readability/html-entities.js"></script>
    <script src="../readability/content-extractor.js"></script>
    <script src="../readability/html-serializer.js"></script>
    <script src="../readability/advanced-cleaner.js"></script>
    <script src="../readability-extractor.js"></script>

    <script>
        function testExtraction() {
            const result = window.GmbReadabilityExtractor.extract();
            displayResult(result);
        }
        
        function testWithCustomHTML() {
            const html = document.getElementById('inputHTML').value;
            
            // Parse and extract from custom HTML
            const modules = window.GmbReadabilityExtractor.getModules();
            const tokens = modules.tokenizer.tokenize(html);
            const { document: domTree } = modules.domBuilder.build(tokens);
            
            // Process the DOM
            modules.linkProcessor.processLinks(domTree);
            modules.advancedCleaner.clean(domTree);
            
            // Extract content
            const extractionResult = modules.contentExtractor.extract(domTree);
            
            if (extractionResult && extractionResult.content) {
                const serialized = modules.htmlSerializer.serialize(extractionResult.content);
                displayResult({
                    content: serialized,
                    metadata: extractionResult.metadata,
                    type: extractionResult.type
                });
            }
        }
        
        function testLegacyMode() {
            // Configure to use legacy mode
            window.GmbReadabilityExtractor.configure({ useAdvancedParser: false });
            const result = window.GmbReadabilityExtractor.extract();
            displayResult(result);
            
            // Reset to advanced mode
            window.GmbReadabilityExtractor.configure({ useAdvancedParser: true });
        }
        
        function benchmarkPerformance() {
            const benchmark = window.GmbReadabilityExtractor.benchmark();
            
            document.getElementById('metrics').innerHTML = `
                <h3>Performance Metrics</h3>
                <p><strong>Extraction Time:</strong> ${benchmark.time.toFixed(2)}ms</p>
                <p><strong>Word Count:</strong> ${benchmark.metrics.wordCount}</p>
                <p><strong>Reading Time:</strong> ${benchmark.metrics.readingTime} minutes</p>
                <p><strong>Method:</strong> ${benchmark.metrics.method}</p>
            `;
            
            displayResult(benchmark.result);
        }
        
        function testModules() {
            const html = document.getElementById('inputHTML').value;
            const modules = window.GmbReadabilityExtractor.getModules();
            
            console.log('Available modules:', modules);
            
            // Test tokenizer
            console.time('Tokenization');
            const tokens = modules.tokenizer.tokenize(html);
            console.timeEnd('Tokenization');
            console.log('Tokens:', tokens);
            
            // Test DOM builder
            console.time('DOM Building');
            const { document: domTree, errors } = modules.domBuilder.build(tokens);
            console.timeEnd('DOM Building');
            console.log('DOM Tree:', domTree);
            console.log('Errors:', errors);
            
            // Test link processor
            console.time('Link Processing');
            const linkResults = modules.linkProcessor.processLinks(domTree);
            console.timeEnd('Link Processing');
            console.log('Link Results:', linkResults);
            
            // Test content scorer
            console.time('Content Scoring');
            const candidates = modules.contentScorer.scoreTree(domTree);
            console.timeEnd('Content Scoring');
            console.log('Top Candidates:', candidates);
            
            // Test cleaner
            console.time('Cleaning');
            const cleanResult = modules.advancedCleaner.clean(domTree);
            console.timeEnd('Cleaning');
            console.log('Cleaning Result:', cleanResult);
            
            displayResult({
                content: '<pre>Check console for detailed module test results</pre>',
                metrics: {
                    tokenCount: tokens.length,
                    errorCount: errors.length,
                    linksProcessed: linkResults.processed,
                    elementsRemoved: cleanResult.removed.length
                }
            });
        }
        
        function displayResult(result) {
            const output = document.getElementById('output');
            
            if (result.content) {
                output.innerHTML = `
                    <h3>Extracted Content</h3>
                    <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0;">
                        ${result.content}
                    </div>
                `;
            }
            
            if (result.metadata || result.title) {
                const metadata = result.metadata || {};
                document.getElementById('metrics').innerHTML = `
                    <h3>Metadata</h3>
                    <p><strong>Title:</strong> ${result.title || metadata.title || 'N/A'}</p>
                    <p><strong>Author:</strong> ${result.author || metadata.author || 'N/A'}</p>
                    <p><strong>Date:</strong> ${result.publishedDate || metadata.publishDate || 'N/A'}</p>
                    <p><strong>Type:</strong> ${result.contentType || result.type || 'N/A'}</p>
                    <p><strong>Method:</strong> ${result.extractionMethod || 'N/A'}</p>
                    ${result.wordCount ? `<p><strong>Words:</strong> ${result.wordCount}</p>` : ''}
                    ${result.metrics ? `<p><strong>Additional Metrics:</strong> ${JSON.stringify(result.metrics)}</p>` : ''}
                `;
            }
        }
        
        // Log configuration on load
        console.log('Readability Extractor Config:', window.GmbReadabilityExtractor.getConfig());
    </script>
</body>
</html>