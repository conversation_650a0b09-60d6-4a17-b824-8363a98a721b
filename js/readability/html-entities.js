// HTML Entity Decoder/Encoder Module - Comprehensive entity handling
window.GmbHTMLEntities = (function() {
    'use strict';
    
    // Complete HTML5 named entity map
    const NAMED_ENTITIES = {
        // Latin characters
        'amp': '&',
        'lt': '<',
        'gt': '>',
        'quot': '"',
        'apos': "'",
        'nbsp': '\u00A0',
        'iexcl': '¡',
        'cent': '¢',
        'pound': '£',
        'curren': '¤',
        'yen': '¥',
        'brvbar': '¦',
        'sect': '§',
        'uml': '¨',
        'copy': '©',
        'ordf': 'ª',
        'laquo': '«',
        'not': '¬',
        'shy': '\u00AD',
        'reg': '®',
        'macr': '¯',
        'deg': '°',
        'plusmn': '±',
        'sup2': '²',
        'sup3': '³',
        'acute': '´',
        'micro': 'µ',
        'para': '¶',
        'middot': '·',
        'cedil': '¸',
        'sup1': '¹',
        'ordm': 'º',
        'raquo': '»',
        'frac14': '¼',
        'frac12': '½',
        'frac34': '¾',
        'iquest': '¿',
        
        // Latin Extended-A
        'Agrave': 'À',
        'Aacute': 'Á',
        'Acirc': 'Â',
        'Atilde': 'Ã',
        'Auml': 'Ä',
        'Aring': 'Å',
        'AElig': 'Æ',
        'Ccedil': 'Ç',
        'Egrave': 'È',
        'Eacute': 'É',
        'Ecirc': 'Ê',
        'Euml': 'Ë',
        'Igrave': 'Ì',
        'Iacute': 'Í',
        'Icirc': 'Î',
        'Iuml': 'Ï',
        'ETH': 'Ð',
        'Ntilde': 'Ñ',
        'Ograve': 'Ò',
        'Oacute': 'Ó',
        'Ocirc': 'Ô',
        'Otilde': 'Õ',
        'Ouml': 'Ö',
        'times': '×',
        'Oslash': 'Ø',
        'Ugrave': 'Ù',
        'Uacute': 'Ú',
        'Ucirc': 'Û',
        'Uuml': 'Ü',
        'Yacute': 'Ý',
        'THORN': 'Þ',
        'szlig': 'ß',
        'agrave': 'à',
        'aacute': 'á',
        'acirc': 'â',
        'atilde': 'ã',
        'auml': 'ä',
        'aring': 'å',
        'aelig': 'æ',
        'ccedil': 'ç',
        'egrave': 'è',
        'eacute': 'é',
        'ecirc': 'ê',
        'euml': 'ë',
        'igrave': 'ì',
        'iacute': 'í',
        'icirc': 'î',
        'iuml': 'ï',
        'eth': 'ð',
        'ntilde': 'ñ',
        'ograve': 'ò',
        'oacute': 'ó',
        'ocirc': 'ô',
        'otilde': 'õ',
        'ouml': 'ö',
        'divide': '÷',
        'oslash': 'ø',
        'ugrave': 'ù',
        'uacute': 'ú',
        'ucirc': 'û',
        'uuml': 'ü',
        'yacute': 'ý',
        'thorn': 'þ',
        'yuml': 'ÿ',
        
        // Latin Extended-B
        'OElig': 'Œ',
        'oelig': 'œ',
        'Scaron': 'Š',
        'scaron': 'š',
        'Yuml': 'Ÿ',
        'fnof': 'ƒ',
        
        // Greek
        'Alpha': 'Α',
        'Beta': 'Β',
        'Gamma': 'Γ',
        'Delta': 'Δ',
        'Epsilon': 'Ε',
        'Zeta': 'Ζ',
        'Eta': 'Η',
        'Theta': 'Θ',
        'Iota': 'Ι',
        'Kappa': 'Κ',
        'Lambda': 'Λ',
        'Mu': 'Μ',
        'Nu': 'Ν',
        'Xi': 'Ξ',
        'Omicron': 'Ο',
        'Pi': 'Π',
        'Rho': 'Ρ',
        'Sigma': 'Σ',
        'Tau': 'Τ',
        'Upsilon': 'Υ',
        'Phi': 'Φ',
        'Chi': 'Χ',
        'Psi': 'Ψ',
        'Omega': 'Ω',
        'alpha': 'α',
        'beta': 'β',
        'gamma': 'γ',
        'delta': 'δ',
        'epsilon': 'ε',
        'zeta': 'ζ',
        'eta': 'η',
        'theta': 'θ',
        'iota': 'ι',
        'kappa': 'κ',
        'lambda': 'λ',
        'mu': 'μ',
        'nu': 'ν',
        'xi': 'ξ',
        'omicron': 'ο',
        'pi': 'π',
        'rho': 'ρ',
        'sigmaf': 'ς',
        'sigma': 'σ',
        'tau': 'τ',
        'upsilon': 'υ',
        'phi': 'φ',
        'chi': 'χ',
        'psi': 'ψ',
        'omega': 'ω',
        
        // Mathematical symbols
        'forall': '∀',
        'part': '∂',
        'exist': '∃',
        'empty': '∅',
        'nabla': '∇',
        'isin': '∈',
        'notin': '∉',
        'ni': '∋',
        'prod': '∏',
        'sum': '∑',
        'minus': '−',
        'lowast': '∗',
        'radic': '√',
        'prop': '∝',
        'infin': '∞',
        'ang': '∠',
        'and': '∧',
        'or': '∨',
        'cap': '∩',
        'cup': '∪',
        'int': '∫',
        'there4': '∴',
        'sim': '∼',
        'cong': '≅',
        'asymp': '≈',
        'ne': '≠',
        'equiv': '≡',
        'le': '≤',
        'ge': '≥',
        'sub': '⊂',
        'sup': '⊃',
        'nsub': '⊄',
        'sube': '⊆',
        'supe': '⊇',
        'oplus': '⊕',
        'otimes': '⊗',
        'perp': '⊥',
        'sdot': '⋅',
        
        // Arrows
        'larr': '←',
        'uarr': '↑',
        'rarr': '→',
        'darr': '↓',
        'harr': '↔',
        'crarr': '↵',
        'lArr': '⇐',
        'uArr': '⇑',
        'rArr': '⇒',
        'dArr': '⇓',
        'hArr': '⇔',
        
        // Technical symbols
        'bull': '•',
        'hellip': '…',
        'prime': '′',
        'Prime': '″',
        'oline': '‾',
        'frasl': '⁄',
        'weierp': '℘',
        'image': 'ℑ',
        'real': 'ℜ',
        'trade': '™',
        'alefsym': 'ℵ',
        
        // Punctuation
        'ndash': '–',
        'mdash': '—',
        'lsquo': '\'',
        'rsquo': '\'',
        'sbquo': '‚',
        'ldquo': '"',
        'rdquo': '"',
        'bdquo': '„',
        'dagger': '†',
        'Dagger': '‡',
        'permil': '‰',
        'lsaquo': '‹',
        'rsaquo': '›',
        'euro': '€',
        
        // Card suits
        'spades': '♠',
        'clubs': '♣',
        'hearts': '♥',
        'diams': '♦',
        
        // Other symbols
        'circ': 'ˆ',
        'tilde': '˜',
        'ensp': '\u2002',
        'emsp': '\u2003',
        'thinsp': '\u2009',
        'zwnj': '\u200C',
        'zwj': '\u200D',
        'lrm': '\u200E',
        'rlm': '\u200F'
    };
    
    // Build reverse map for encoding
    const ENCODE_MAP = {};
    for (const [name, char] of Object.entries(NAMED_ENTITIES)) {
        ENCODE_MAP[char] = `&${name};`;
    }
    
    // Characters that must be encoded in different contexts
    const ENCODE_RULES = {
        // Characters that must be encoded in text content
        text: {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '\u00A0': '&nbsp;'
        },
        
        // Characters that must be encoded in attributes
        attribute: {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&apos;',
            '\u00A0': '&nbsp;'
        },
        
        // Characters for aggressive encoding
        aggressive: ENCODE_MAP
    };
    
    class HTMLEntityProcessor {
        constructor(options = {}) {
            this.mode = options.mode || 'standard'; // standard, xml, aggressive
            this.allowUnsafe = options.allowUnsafe || false;
            this.encodeNonASCII = options.encodeNonASCII || false;
            this.decodeStrict = options.decodeStrict || false;
        }
        
        // Decode HTML entities in a string
        decode(text) {
            if (!text || typeof text !== 'string') {
                return text;
            }
            
            // Decode named entities
            text = this.decodeNamedEntities(text);
            
            // Decode numeric entities
            text = this.decodeNumericEntities(text);
            
            return text;
        }
        
        // Decode named entities
        decodeNamedEntities(text) {
            return text.replace(/&([a-zA-Z][a-zA-Z0-9]*);/g, (match, name) => {
                if (NAMED_ENTITIES[name]) {
                    return NAMED_ENTITIES[name];
                }
                
                // Handle common variations
                const lowerName = name.toLowerCase();
                if (NAMED_ENTITIES[lowerName]) {
                    return NAMED_ENTITIES[lowerName];
                }
                
                // In strict mode, invalid entities are left as-is
                if (this.decodeStrict) {
                    return match;
                }
                
                // In non-strict mode, return the entity as-is
                return match;
            });
        }
        
        // Decode numeric entities
        decodeNumericEntities(text) {
            // Decimal entities
            text = text.replace(/&#(\d+);/g, (match, code) => {
                const num = parseInt(code, 10);
                
                // Security check
                if (!this.isValidCodePoint(num)) {
                    return this.decodeStrict ? match : '';
                }
                
                return String.fromCharCode(num);
            });
            
            // Hexadecimal entities
            text = text.replace(/&#x([0-9a-fA-F]+);/g, (match, code) => {
                const num = parseInt(code, 16);
                
                // Security check
                if (!this.isValidCodePoint(num)) {
                    return this.decodeStrict ? match : '';
                }
                
                return String.fromCharCode(num);
            });
            
            return text;
        }
        
        // Check if code point is valid and safe
        isValidCodePoint(code) {
            // Basic range check
            if (code < 0 || code > 0x10FFFF) {
                return false;
            }
            
            // Surrogate pairs (invalid as individual code points)
            if (code >= 0xD800 && code <= 0xDFFF) {
                return false;
            }
            
            // Noncharacters
            if ((code >= 0xFDD0 && code <= 0xFDEF) ||
                (code & 0xFFFE) === 0xFFFE) {
                return false;
            }
            
            // Control characters (except common ones)
            if (!this.allowUnsafe) {
                if (code <= 0x1F && code !== 0x09 && code !== 0x0A && code !== 0x0D) {
                    return false;
                }
                if (code >= 0x7F && code <= 0x9F) {
                    return false;
                }
            }
            
            return true;
        }
        
        // Encode text for HTML
        encode(text, context = 'text') {
            if (!text || typeof text !== 'string') {
                return text;
            }
            
            const rules = ENCODE_RULES[context] || ENCODE_RULES.text;
            
            // Basic encoding
            text = this.encodeByRules(text, rules);
            
            // Encode non-ASCII if requested
            if (this.encodeNonASCII) {
                text = this.encodeNonASCIIChars(text);
            }
            
            return text;
        }
        
        // Encode by rules
        encodeByRules(text, rules) {
            // Build regex from rules
            const chars = Object.keys(rules);
            const regex = new RegExp(`[${chars.map(c => 
                c.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
            ).join('')}]`, 'g');
            
            return text.replace(regex, char => rules[char] || char);
        }
        
        // Encode non-ASCII characters
        encodeNonASCIIChars(text) {
            return text.replace(/[^\x00-\x7F]/g, char => {
                const code = char.charCodeAt(0);
                
                // Check if there's a named entity
                if (ENCODE_MAP[char]) {
                    return ENCODE_MAP[char];
                }
                
                // Use numeric entity
                return `&#${code};`;
            });
        }
        
        // Encode for attribute value
        encodeAttribute(text, quoteChar = '"') {
            text = this.encode(text, 'attribute');
            
            // Additional encoding based on quote character
            if (quoteChar === '"') {
                text = text.replace(/"/g, '&quot;');
            } else if (quoteChar === "'") {
                text = text.replace(/'/g, '&apos;');
            }
            
            return text;
        }
        
        // Strip all entities (decode and re-encode minimal)
        strip(text) {
            // First decode everything
            text = this.decode(text);
            
            // Then encode only the absolute minimum
            return this.encode(text, 'text');
        }
        
        // Validate entity references in text
        validate(text) {
            const errors = [];
            
            // Check named entities
            text.replace(/&([a-zA-Z][a-zA-Z0-9]*);/g, (match, name, offset) => {
                if (!NAMED_ENTITIES[name] && !NAMED_ENTITIES[name.toLowerCase()]) {
                    errors.push({
                        type: 'invalid-named-entity',
                        entity: match,
                        position: offset
                    });
                }
            });
            
            // Check numeric entities
            text.replace(/&#(\d+);/g, (match, code, offset) => {
                const num = parseInt(code, 10);
                if (!this.isValidCodePoint(num)) {
                    errors.push({
                        type: 'invalid-numeric-entity',
                        entity: match,
                        position: offset,
                        code: num
                    });
                }
            });
            
            // Check hex entities
            text.replace(/&#x([0-9a-fA-F]+);/g, (match, code, offset) => {
                const num = parseInt(code, 16);
                if (!this.isValidCodePoint(num)) {
                    errors.push({
                        type: 'invalid-hex-entity',
                        entity: match,
                        position: offset,
                        code: num
                    });
                }
            });
            
            return errors;
        }
        
        // Process entities in DOM node
        processNode(node) {
            if (node.type === 'text') {
                node.data = this.decode(node.data);
            } else if (node.type === 'element') {
                // Process attributes
                for (const [name, value] of Object.entries(node.attributes || {})) {
                    node.attributes[name] = this.decode(value);
                }
                
                // Process children
                for (const child of node.children || []) {
                    this.processNode(child);
                }
            }
        }
        
        // Get entity info
        getEntityInfo(entity) {
            // Remove & and ; if present
            const name = entity.replace(/^&|;$/g, '');
            
            if (NAMED_ENTITIES[name]) {
                return {
                    type: 'named',
                    name: name,
                    character: NAMED_ENTITIES[name],
                    codePoint: NAMED_ENTITIES[name].charCodeAt(0),
                    entity: `&${name};`
                };
            }
            
            // Check numeric
            const decimalMatch = name.match(/^#(\d+)$/);
            if (decimalMatch) {
                const code = parseInt(decimalMatch[1], 10);
                return {
                    type: 'numeric-decimal',
                    codePoint: code,
                    character: String.fromCharCode(code),
                    entity: `&#${code};`,
                    valid: this.isValidCodePoint(code)
                };
            }
            
            // Check hex
            const hexMatch = name.match(/^#x([0-9a-fA-F]+)$/i);
            if (hexMatch) {
                const code = parseInt(hexMatch[1], 16);
                return {
                    type: 'numeric-hex',
                    codePoint: code,
                    character: String.fromCharCode(code),
                    entity: `&#x${hexMatch[1]};`,
                    valid: this.isValidCodePoint(code)
                };
            }
            
            return null;
        }
    }
    
    // Public API
    return {
        create: function(options) {
            return new HTMLEntityProcessor(options);
        },
        
        // Quick decode function
        decode: function(text, options) {
            const processor = new HTMLEntityProcessor(options);
            return processor.decode(text);
        },
        
        // Quick encode function
        encode: function(text, context, options) {
            const processor = new HTMLEntityProcessor(options);
            return processor.encode(text, context);
        },
        
        // Export constants
        NAMED_ENTITIES: NAMED_ENTITIES,
        ENCODE_MAP: ENCODE_MAP
    };
})();