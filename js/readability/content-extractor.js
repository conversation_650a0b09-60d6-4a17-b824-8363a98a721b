// Content Extraction Engine - Sophisticated content extraction with structure preservation
window.GmbContentExtractor = (function() {
    'use strict';
    
    // Import dependencies
    const ELEMENT_CLASSIFICATIONS = window.GmbReadabilityExtractor?.ELEMENT_CLASSIFICATIONS;
    
    // Content extraction strategies
    const ExtractionStrategy = {
        SCORING: 'scoring',
        SEMANTIC: 'semantic',
        DENSITY: 'density',
        VISUAL: 'visual',
        HYBRID: 'hybrid'
    };
    
    // Content patterns for different types of pages
    const ContentPatterns = {
        ARTICLE: {
            selectors: ['article', '[role="article"]', '.article', '.post', '.entry', '.content', '.recipe-content', '.recipe-body'],
            indicators: ['published', 'author', 'date', 'category', 'tags', 'recipe', 'ingredients'],
            minParagraphs: 2,    // Was 3
            minWords: 150        // Was 300
        },
        BLOG_POST: {
            selectors: ['.post', '.blog-post', '.entry-content', '.post-content', '.content-wrapper'],
            indicators: ['comments', 'share', 'related', 'author-bio'],
            minParagraphs: 1,    // Was 2
            minWords: 100        // Was 200
        },
        NEWS: {
            selectors: ['.story', '.story-body', '.article-body', '[itemprop="articleBody"]'],
            indicators: ['byline', 'dateline', 'source', 'updated'],
            minParagraphs: 4,
            minWords: 250
        },
        WIKI: {
            selectors: ['#content', '.mw-parser-output', '.wiki-content'],
            indicators: ['references', 'citations', 'see-also', 'external-links'],
            minParagraphs: 3,
            minWords: 400
        },
        FORUM: {
            selectors: ['.post-content', '.message-content', '.bbp-reply-content'],
            indicators: ['avatar', 'signature', 'quote', 'reply'],
            minParagraphs: 1,
            minWords: 50
        }
    };
    
    // Extraction configuration
    const DEFAULT_CONFIG = {
        strategy: ExtractionStrategy.HYBRID,
        preserveStructure: true,
        preserveHeadings: true,
        preserveLists: true,
        preserveTables: true,
        preserveImages: true,
        preserveVideos: false,
        minTextLength: 100,      // Was 140 - less aggressive
        minScore: 15,            // Was 20 - less aggressive
        topCandidates: 5,
        ancestorDepth: 3,
        siblingThreshold: 0.2,
        removeEmpty: true,
        normalizeWhitespace: true
    };
    
    class ContentExtractor {
        constructor(options = {}) {
            this.config = Object.assign({}, DEFAULT_CONFIG, options);
            this.scorer = window.GmbContentScorer?.create();
            this.linkProcessor = window.GmbLinkProcessor?.create();
            this.entityProcessor = window.GmbHTMLEntities?.create();
        }
        
        // Main extraction method
        extract(domNode, options = {}) {
            const config = Object.assign({}, this.config, options);
            
            // Detect content type
            const contentType = this.detectContentType(domNode);
            
            // Choose extraction strategy
            let content = null;
            
            switch (config.strategy) {
                case ExtractionStrategy.SEMANTIC:
                    content = this.extractBySemantic(domNode, contentType);
                    break;
                case ExtractionStrategy.SCORING:
                    content = this.extractByScoring(domNode);
                    break;
                case ExtractionStrategy.DENSITY:
                    content = this.extractByDensity(domNode);
                    break;
                case ExtractionStrategy.VISUAL:
                    content = this.extractByVisual(domNode);
                    break;
                case ExtractionStrategy.HYBRID:
                default:
                    content = this.extractByHybrid(domNode, contentType);
                    break;
            }
            
            if (!content) {
                return null;
            }
            
            // Post-process content
            content = this.postProcess(content, config);
            
            // Extract metadata
            const metadata = this.extractMetadata(domNode, content);
            
            return {
                content: content,
                metadata: metadata,
                type: contentType,
                extractionMethod: config.strategy
            };
        }
        
        // Detect content type
        detectContentType(domNode) {
            for (const [type, pattern] of Object.entries(ContentPatterns)) {
                // Check selectors
                for (const selector of pattern.selectors) {
                    const elements = this.querySelectorAll(domNode, selector);
                    if (elements.length > 0) {
                        // Verify with indicators
                        const indicatorCount = this.countIndicators(domNode, pattern.indicators);
                        if (indicatorCount >= 2) {
                            return type;
                        }
                    }
                }
            }
            
            return 'ARTICLE'; // Default
        }
        
        // Count content indicators
        countIndicators(domNode, indicators) {
            let count = 0;
            
            for (const indicator of indicators) {
                // Check class names and IDs
                const pattern = new RegExp(indicator, 'i');
                const elements = this.findElementsByPattern(domNode, pattern);
                if (elements.length > 0) {
                    count++;
                }
            }
            
            return count;
        }
        
        // Extract by semantic HTML
        extractBySemantic(domNode, contentType) {
            const pattern = ContentPatterns[contentType] || ContentPatterns.ARTICLE;
            
            // Try semantic selectors
            for (const selector of pattern.selectors) {
                const candidates = this.querySelectorAll(domNode, selector);
                
                for (const candidate of candidates) {
                    if (this.isValidContent(candidate, pattern)) {
                        return this.expandContent(candidate);
                    }
                }
            }
            
            // Fallback to scoring
            return this.extractByScoring(domNode);
        }
        
        // Extract by content scoring
        extractByScoring(domNode) {
            if (!this.scorer) {
                console.warn('Content scorer not available');
                return null;
            }
            
            const result = this.scorer.findContent(domNode, {
                minQuality: 30
            });
            
            if (!result) {
                return null;
            }
            
            return this.expandContent(result.node);
        }
        
        // Extract by text density
        extractByDensity(domNode) {
            const blocks = this.findTextBlocks(domNode);
            let bestBlock = null;
            let bestDensity = 0;
            
            for (const block of blocks) {
                const density = this.calculateTextDensity(block);
                if (density > bestDensity) {
                    bestDensity = density;
                    bestBlock = block;
                }
            }
            
            if (!bestBlock || bestDensity < 0.5) {
                return null;
            }
            
            return this.expandContent(bestBlock);
        }
        
        // Extract by visual clustering
        extractByVisual(domNode) {
            // Find visual blocks (elements with similar styling)
            const blocks = this.findVisualBlocks(domNode);
            
            // Group by visual similarity
            const clusters = this.clusterBlocks(blocks);
            
            // Find main content cluster
            const mainCluster = this.findMainCluster(clusters);
            
            if (!mainCluster) {
                return null;
            }
            
            // Combine cluster elements
            return this.combineCluster(mainCluster);
        }
        
        // Hybrid extraction approach
        extractByHybrid(domNode, contentType) {
            // Try semantic first
            let content = this.extractBySemantic(domNode, contentType);
            
            if (!content || !this.isValidContent(content)) {
                // Fallback to scoring
                content = this.extractByScoring(domNode);
            }
            
            if (!content || !this.isValidContent(content)) {
                // Last resort: density-based
                content = this.extractByDensity(domNode);
            }
            
            return content;
        }
        
        // Check if content is valid
        isValidContent(node, pattern = null) {
            if (!node) return false;
            
            const text = this.getTextContent(node);
            const wordCount = text.split(/\s+/).filter(w => w.length > 0).length;
            
            // Check against pattern requirements
            if (pattern) {
                if (wordCount < pattern.minWords) return false;
                
                const paragraphs = this.countParagraphs(node);
                if (paragraphs < pattern.minParagraphs) return false;
            } else {
                // Default validation
                if (wordCount < 100) return false;
            }
            
            // Check link density
            const linkDensity = this.calculateLinkDensity(node);
            if (linkDensity > 0.5) return false;
            
            return true;
        }
        
        // Expand content to include relevant siblings and ancestors
        expandContent(node) {
            if (!node) return null;
            
            // Clone node to avoid modifying original
            let content = node.clone(true);
            
            // Check if we should expand to parent
            if (this.shouldExpandToParent(node)) {
                content = this.expandToParent(node);
            }
            
            // Include relevant siblings
            content = this.includeSiblings(content, node);
            
            // Clean the expanded content
            content = this.cleanContent(content);
            
            return content;
        }
        
        // Check if should expand to parent
        shouldExpandToParent(node) {
            if (!node.parent) return false;
            
            // Don't expand beyond certain elements
            const stopTags = new Set(['body', 'article', 'main', 'section']);
            if (stopTags.has(node.parent.tagName)) {
                return false;
            }
            
            // Check if parent adds significant content
            const nodeText = this.getTextContent(node).length;
            const parentText = this.getTextContent(node.parent).length;
            
            // Parent doesn't add much
            if (parentText < nodeText * 1.2) {
                return false;
            }
            
            return true;
        }
        
        // Expand to parent
        expandToParent(node) {
            const parent = node.parent;
            if (!parent) return node.clone(true);
            
            const expanded = parent.clone(false);
            
            // Include only relevant children
            for (const child of parent.children) {
                if (child === node) {
                    expanded.appendChild(node.clone(true));
                } else if (this.isRelevantSibling(child, node)) {
                    expanded.appendChild(child.clone(true));
                }
            }
            
            return expanded;
        }
        
        // Include relevant siblings
        includeSiblings(content, originalNode) {
            if (!originalNode.parent) return content;
            
            const siblings = originalNode.parent.children;
            const nodeIndex = siblings.indexOf(originalNode);
            
            // Create container if needed
            let container = content;
            if (content.tagName === originalNode.tagName) {
                container = new (window.GmbDOMBuilder?.DOMNode || Object)(
                    'element', 'div'
                );
                container.appendChild(content);
            }
            
            // Check siblings before
            for (let i = nodeIndex - 1; i >= 0; i--) {
                const sibling = siblings[i];
                if (this.isRelevantSibling(sibling, originalNode)) {
                    container.children.unshift(sibling.clone(true));
                } else {
                    break; // Stop at first non-relevant sibling
                }
            }
            
            // Check siblings after
            for (let i = nodeIndex + 1; i < siblings.length; i++) {
                const sibling = siblings[i];
                if (this.isRelevantSibling(sibling, originalNode)) {
                    container.appendChild(sibling.clone(true));
                } else {
                    break; // Stop at first non-relevant sibling
                }
            }
            
            return container;
        }
        
        // Check if sibling is relevant
        isRelevantSibling(sibling, node) {
            // Skip non-element nodes
            if (sibling.type !== 'element') return false;
            
            // Skip certain tags
            const skipTags = new Set(['script', 'style', 'nav', 'aside', 'footer', 'header']);
            if (skipTags.has(sibling.tagName)) return false;
            
            // Check if it's a heading
            if (ELEMENT_CLASSIFICATIONS?.HEADING_ELEMENTS?.has(sibling.tagName)) {
                return true; // Usually want to include headings
            }
            
            // Check text content
            const text = this.getTextContent(sibling);
            if (text.length < 30) return false;
            
            // Check link density
            const linkDensity = this.calculateLinkDensity(sibling);
            if (linkDensity > 0.5) return false;
            
            // Check class/id for negative indicators
            const className = (sibling.getAttribute('class') || '').toLowerCase();
            const id = (sibling.getAttribute('id') || '').toLowerCase();
            
            const negativePattern = /comment|share|social|related|sidebar|footer|header|nav|menu|widget|ad/i;
            if (negativePattern.test(className) || negativePattern.test(id)) {
                return false;
            }
            
            return true;
        }
        
        // Clean content
        cleanContent(node) {
            if (!node) return null;
            
            // Remove unwanted elements
            this.removeUnwantedElements(node);
            
            // Clean attributes
            this.cleanAttributes(node);
            
            // Remove empty elements
            if (this.config.removeEmpty) {
                this.removeEmptyElements(node);
            }
            
            // Normalize whitespace
            if (this.config.normalizeWhitespace) {
                this.normalizeWhitespace(node);
            }
            
            return node;
        }
        
        // Remove unwanted elements
        removeUnwantedElements(node) {
            const unwantedTags = new Set([
                'script', 'style', 'noscript', 'iframe', 'object', 'embed',
                'form', 'input', 'textarea', 'select', 'button'
            ]);
            
            // Remove by tag name
            const toRemove = [];
            this.walkTree(node, (child) => {
                if (child.type === 'element' && unwantedTags.has(child.tagName)) {
                    toRemove.push(child);
                }
            });
            
            for (const child of toRemove) {
                if (child.parent) {
                    child.parent.removeChild(child);
                }
            }
            
            // Remove by class/id patterns
            const negativePattern = /sidebar|widget|comment|share|social|footer|header|nav|menu|related|recommended|popup|modal|overlay|interstitial|subscription|newsletter|signup|signin/i;
            
            const toRemoveByPattern = [];
            this.walkTree(node, (child) => {
                if (child.type === 'element') {
                    const className = child.getAttribute('class') || '';
                    const id = child.getAttribute('id') || '';
                    
                    if (negativePattern.test(className) || negativePattern.test(id)) {
                        toRemoveByPattern.push(child);
                    }
                }
            });
            
            for (const child of toRemoveByPattern) {
                if (child.parent) {
                    child.parent.removeChild(child);
                }
            }
        }
        
        // Clean attributes
        cleanAttributes(node) {
            const allowedAttributes = new Set([
                'href', 'src', 'alt', 'title', 'width', 'height',
                'colspan', 'rowspan', 'datetime', 'cite'
            ]);
            
            this.walkTree(node, (child) => {
                if (child.type === 'element' && child.attributes) {
                    const attributesToRemove = [];
                    
                    for (const attrName of Object.keys(child.attributes)) {
                        // Remove event handlers
                        if (/^on/i.test(attrName)) {
                            attributesToRemove.push(attrName);
                        }
                        // Remove style (unless preserving)
                        else if (attrName === 'style' && !this.config.preserveStyles) {
                            attributesToRemove.push(attrName);
                        }
                        // Remove class (unless preserving)
                        else if (attrName === 'class' && !this.config.preserveClasses) {
                            attributesToRemove.push(attrName);
                        }
                        // Remove non-allowed attributes
                        else if (!allowedAttributes.has(attrName) && 
                                 !attrName.startsWith('data-')) {
                            attributesToRemove.push(attrName);
                        }
                    }
                    
                    for (const attr of attributesToRemove) {
                        child.removeAttribute(attr);
                    }
                }
            });
        }
        
        // Remove empty elements
        removeEmptyElements(node) {
            const preserveTags = new Set([
                'img', 'br', 'hr', 'input', 'video', 'audio', 'source', 'track'
            ]);
            
            let removedAny = true;
            while (removedAny) {
                removedAny = false;
                const toRemove = [];
                
                this.walkTree(node, (child) => {
                    if (child.type === 'element' && 
                        !preserveTags.has(child.tagName) &&
                        child.children.length === 0 &&
                        !this.getTextContent(child).trim()) {
                        toRemove.push(child);
                    }
                });
                
                for (const child of toRemove) {
                    if (child.parent) {
                        child.parent.removeChild(child);
                        removedAny = true;
                    }
                }
            }
        }
        
        // Normalize whitespace
        normalizeWhitespace(node) {
            this.walkTree(node, (child) => {
                if (child.type === 'text') {
                    // Don't normalize in pre elements
                    if (this.isInPreformattedContext(child)) {
                        return;
                    }
                    
                    // Normalize whitespace
                    child.data = child.data
                        .replace(/\s+/g, ' ')
                        .replace(/^\s+/, '')
                        .replace(/\s+$/, '');
                }
            });
        }
        
        // Check if node is in preformatted context
        isInPreformattedContext(node) {
            let current = node.parent;
            while (current) {
                if (ELEMENT_CLASSIFICATIONS?.PRESERVE_WHITESPACE?.has(current.tagName)) {
                    return true;
                }
                current = current.parent;
            }
            return false;
        }
        
        // Extract metadata
        extractMetadata(domNode, contentNode) {
            const metadata = {
                title: this.extractTitle(domNode),
                author: this.extractAuthor(domNode),
                publishDate: this.extractPublishDate(domNode),
                description: this.extractDescription(domNode),
                image: this.extractMainImage(domNode, contentNode),
                wordCount: 0,
                readingTime: 0
            };
            
            // Calculate word count and reading time
            if (contentNode) {
                const text = this.getTextContent(contentNode);
                const words = text.split(/\s+/).filter(w => w.length > 0);
                metadata.wordCount = words.length;
                metadata.readingTime = Math.ceil(words.length / 200); // 200 words per minute
            }
            
            return metadata;
        }
        
        // Extract title
        extractTitle(domNode) {
            // Try meta tags
            const metaTitle = this.findMetaContent(domNode, ['og:title', 'twitter:title']);
            if (metaTitle) return metaTitle;
            
            // Try h1
            const h1 = this.querySelector(domNode, 'h1');
            if (h1) return this.getTextContent(h1).trim();
            
            // Try title element
            const title = this.querySelector(domNode, 'title');
            if (title) return this.getTextContent(title).trim();
            
            return null;
        }
        
        // Extract author
        extractAuthor(domNode) {
            // Try meta tags
            const metaAuthor = this.findMetaContent(domNode, ['author', 'article:author', 'og:author']);
            if (metaAuthor) return metaAuthor;
            
            // Try byline patterns
            const bylinePatterns = [
                '[rel="author"]',
                '.author',
                '.byline',
                '.by-author',
                '[itemprop="author"]'
            ];
            
            for (const pattern of bylinePatterns) {
                const element = this.querySelector(domNode, pattern);
                if (element) {
                    return this.getTextContent(element).trim();
                }
            }
            
            return null;
        }
        
        // Extract publish date
        extractPublishDate(domNode) {
            // Try meta tags
            const metaDate = this.findMetaContent(domNode, [
                'article:published_time',
                'og:published_time',
                'publish_date',
                'date'
            ]);
            if (metaDate) return metaDate;
            
            // Try time elements
            const timeElement = this.querySelector(domNode, 'time[datetime]');
            if (timeElement) {
                return timeElement.getAttribute('datetime');
            }
            
            return null;
        }
        
        // Extract description
        extractDescription(domNode) {
            // Try meta tags
            const metaDesc = this.findMetaContent(domNode, [
                'description',
                'og:description',
                'twitter:description'
            ]);
            if (metaDesc) return metaDesc;
            
            return null;
        }
        
        // Extract main image
        extractMainImage(domNode, contentNode) {
            // Try meta tags
            const metaImage = this.findMetaContent(domNode, [
                'og:image',
                'twitter:image'
            ]);
            if (metaImage) return metaImage;
            
            // Try first image in content
            if (contentNode) {
                const img = this.querySelector(contentNode, 'img[src]');
                if (img) {
                    return img.getAttribute('src');
                }
            }
            
            return null;
        }
        
        // Helper: Find meta content
        findMetaContent(domNode, names) {
            for (const name of names) {
                const meta = this.querySelector(domNode, 
                    `meta[property="${name}"], meta[name="${name}"]`);
                if (meta) {
                    const content = meta.getAttribute('content');
                    if (content) return content;
                }
            }
            return null;
        }
        
        // Helper methods for DOM traversal
        querySelector(node, selector) {
            // Simple selector implementation
            const parts = selector.match(/^(\w+)?(\[([^\]]+)\])?(\.[^\s]+)?$/);
            if (!parts) return null;
            
            const [, tagName, , attribute, className] = parts;
            
            return this.findFirst(node, (child) => {
                if (child.type !== 'element') return false;
                if (tagName && child.tagName !== tagName) return false;
                if (className && !child.getAttribute('class')?.includes(className.slice(1))) return false;
                if (attribute) {
                    const [attrName, attrValue] = attribute.split('=');
                    if (attrValue) {
                        const value = attrValue.replace(/["']/g, '');
                        if (child.getAttribute(attrName) !== value) return false;
                    } else {
                        if (!child.hasAttribute(attrName)) return false;
                    }
                }
                return true;
            });
        }
        
        querySelectorAll(node, selector) {
            const results = [];
            // Simplified implementation - just handle basic selectors
            this.walkTree(node, (child) => {
                if (this.matchesSelector(child, selector)) {
                    results.push(child);
                }
            });
            return results;
        }
        
        matchesSelector(node, selector) {
            if (node.type !== 'element') return false;
            
            // Handle basic selectors
            if (selector.startsWith('.')) {
                return node.getAttribute('class')?.includes(selector.slice(1));
            } else if (selector.startsWith('#')) {
                return node.getAttribute('id') === selector.slice(1);
            } else if (selector.includes('[')) {
                // Handle attribute selector
                const match = selector.match(/(\w+)?\[([^\]]+)\]/);
                if (!match) return false;
                const [, tag, attr] = match;
                if (tag && node.tagName !== tag) return false;
                return node.hasAttribute(attr.split('=')[0]);
            } else {
                return node.tagName === selector;
            }
        }
        
        findFirst(node, predicate) {
            if (predicate(node)) return node;
            
            for (const child of node.children || []) {
                const result = this.findFirst(child, predicate);
                if (result) return result;
            }
            
            return null;
        }
        
        findElementsByPattern(node, pattern) {
            const results = [];
            
            this.walkTree(node, (child) => {
                if (child.type === 'element') {
                    const className = child.getAttribute('class') || '';
                    const id = child.getAttribute('id') || '';
                    
                    if (pattern.test(className) || pattern.test(id)) {
                        results.push(child);
                    }
                }
            });
            
            return results;
        }
        
        // Text-related helpers
        getTextContent(node) {
            if (node.type === 'text') {
                return node.data || '';
            }
            
            let text = '';
            for (const child of node.children || []) {
                text += this.getTextContent(child);
            }
            return text;
        }
        
        countParagraphs(node) {
            let count = 0;
            this.walkTree(node, (child) => {
                if (child.tagName === 'p') {
                    const text = this.getTextContent(child).trim();
                    if (text.length > 0) {
                        count++;
                    }
                }
            });
            return count;
        }
        
        calculateLinkDensity(node) {
            const textLength = this.getTextContent(node).length;
            if (textLength === 0) return 0;
            
            let linkTextLength = 0;
            this.walkTree(node, (child) => {
                if (child.tagName === 'a') {
                    linkTextLength += this.getTextContent(child).length;
                }
            });
            
            return linkTextLength / textLength;
        }
        
        calculateTextDensity(node) {
            const textLength = this.getTextContent(node).trim().length;
            const tagCount = this.countTags(node);
            
            if (tagCount === 0) return 0;
            return textLength / tagCount;
        }
        
        countTags(node) {
            let count = 0;
            this.walkTree(node, (child) => {
                if (child.type === 'element') {
                    count++;
                }
            });
            return count;
        }
        
        // Tree walking
        walkTree(node, callback) {
            callback(node);
            for (const child of node.children || []) {
                this.walkTree(child, callback);
            }
        }
        
        // Find text blocks
        findTextBlocks(node) {
            const blocks = [];
            
            this.walkTree(node, (child) => {
                if (child.type === 'element' && 
                    ELEMENT_CLASSIFICATIONS?.CONTENT_ELEMENTS?.has(child.tagName)) {
                    const text = this.getTextContent(child).trim();
                    if (text.length >= this.config.minTextLength) {
                        blocks.push(child);
                    }
                }
            });
            
            return blocks;
        }
        
        // Visual clustering methods (simplified)
        findVisualBlocks(node) {
            // This would ideally use computed styles, but we're working with DOM only
            const blocks = [];
            
            this.walkTree(node, (child) => {
                if (child.type === 'element' && 
                    child.children.length > 0 &&
                    this.getTextContent(child).trim().length > 100) {
                    blocks.push({
                        node: child,
                        depth: child.metadata?.depth || 0,
                        textLength: this.getTextContent(child).length,
                        tagName: child.tagName,
                        className: child.getAttribute('class') || ''
                    });
                }
            });
            
            return blocks;
        }
        
        clusterBlocks(blocks) {
            // Simple clustering by depth and tag name
            const clusters = {};
            
            for (const block of blocks) {
                const key = `${block.depth}-${block.tagName}`;
                if (!clusters[key]) {
                    clusters[key] = [];
                }
                clusters[key].push(block);
            }
            
            return Object.values(clusters);
        }
        
        findMainCluster(clusters) {
            if (clusters.length === 0) return null;
            
            // Find cluster with most text
            let bestCluster = null;
            let bestTextLength = 0;
            
            for (const cluster of clusters) {
                const totalText = cluster.reduce((sum, block) => sum + block.textLength, 0);
                if (totalText > bestTextLength) {
                    bestTextLength = totalText;
                    bestCluster = cluster;
                }
            }
            
            return bestCluster;
        }
        
        combineCluster(cluster) {
            if (!cluster || cluster.length === 0) return null;
            
            // Create container
            const container = new (window.GmbDOMBuilder?.DOMNode || Object)(
                'element', 'div'
            );
            
            // Add all nodes from cluster
            for (const block of cluster) {
                container.appendChild(block.node.clone(true));
            }
            
            return container;
        }
        
        // Post-processing
        postProcess(node, config) {
            if (!node) return null;
            
            // Process links
            if (this.linkProcessor) {
                this.linkProcessor.processLinks(node);
            }
            
            // Process entities
            if (this.entityProcessor) {
                this.entityProcessor.processNode(node);
            }
            
            // Preserve or remove specific elements based on config
            if (!config.preserveImages) {
                this.removeElements(node, 'img');
            }
            
            if (!config.preserveVideos) {
                this.removeElements(node, 'video');
                this.removeElements(node, 'iframe');
            }
            
            if (!config.preserveTables) {
                this.removeElements(node, 'table');
            }
            
            return node;
        }
        
        removeElements(node, tagName) {
            const toRemove = [];
            
            this.walkTree(node, (child) => {
                if (child.tagName === tagName) {
                    toRemove.push(child);
                }
            });
            
            for (const child of toRemove) {
                if (child.parent) {
                    child.parent.removeChild(child);
                }
            }
        }
    }
    
    // Public API
    return {
        create: function(options) {
            return new ContentExtractor(options);
        },
        
        // Quick extraction
        extract: function(domNode, options) {
            const extractor = new ContentExtractor(options);
            return extractor.extract(domNode);
        },
        
        // Export constants
        ExtractionStrategy: ExtractionStrategy,
        ContentPatterns: ContentPatterns,
        DEFAULT_CONFIG: DEFAULT_CONFIG
    };
})();