/**
 * Blocked Site Timer Script
 * Handles timer display and communication with background script
 */

// Display the blocked site
const hostname = window.location.hostname || 'Unknown Site';
document.getElementById('blocked-site').textContent = `${hostname} is blocked`;

// Timer update function
let initialTime = null;
let currentTime = null;

function updateTimer() {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({action: 'pomodoroGetState'}, (response) => {
            if (chrome.runtime.lastError) {
                console.log('Extension context lost');
                return;
            }
            
            if (response && response.success && response.state) {
                const state = response.state;
                
                // Check if timer is active and has time remaining
                if (state.currentState === 'work' && state.timeRemaining > 0) {
                    const timeInSeconds = state.timeRemaining;
                    const minutes = Math.floor(timeInSeconds / 60);
                    const seconds = timeInSeconds % 60;
                    
                    document.getElementById('timer').textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    
                    // Update progress bar based on work session (25 minutes = 1500 seconds)
                    const totalWorkTime = 25 * 60; // Default work time
                    const progress = ((totalWorkTime - timeInSeconds) / totalWorkTime) * 100;
                    document.getElementById('progress').style.width = `${Math.max(0, Math.min(100, progress))}%`;
                    
                } else if (state.currentState === 'idle') {
                    document.getElementById('timer').textContent = 'No Active Session';
                    document.getElementById('progress').style.width = '0%';
                    
                    // Show message that user should start a timer
                    const message = document.querySelector('.message');
                    message.innerHTML = 'This site is blocked, but no Pomodoro session is active.<br>Start a work session to see the timer here.';
                } else {
                    document.getElementById('timer').textContent = 'Session Complete';
                    document.getElementById('progress').style.width = '100%';
                    
                    // Close or redirect after a delay
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                }
            } else {
                // Fallback if no response
                document.getElementById('timer').textContent = 'Timer Unavailable';
                document.getElementById('progress').style.width = '0%';
            }
        });
    }
}

// Update timer immediately and then every second
updateTimer();
const timerInterval = setInterval(updateTimer, 1000);

// Clean up interval when page is hidden or closed
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        clearInterval(timerInterval);
    }
});

window.addEventListener('beforeunload', () => {
    clearInterval(timerInterval);
});