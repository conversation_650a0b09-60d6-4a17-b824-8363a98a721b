# Quick Actions Complete Integration Checklist

**CRITICAL LESSON LEARNED**: Adding a new Quick Action requires **12 MANDATORY integration points**. Missing ANY of these will cause failures.

## The Redirect Checker Case Study

During Redirect Checker implementation, we discovered the existing documentation missed **10 out of 12** required integration points, leading to:
- Settings toggle not appearing in UI
- Button not showing in popup 
- "executeRedirectCheckerScript is not defined" errors
- Complete non-functionality despite code existing

## THE 12 MANDATORY INTEGRATION POINTS

### 1. CREATE THE ACTION SCRIPT
**File**: `settings/quick-actions/{actionname}.js`
- Must export a class with static `execute()` and `reset()` methods
- Must follow universal popup standards
- Must implement DOM cleanup

### 2. MANIFEST.JSON WEB RESOURCES  
**File**: `manifest.json`
**Location**: `web_accessible_resources[0].resources` array
**Add**: `"settings/quick-actions/{actionname}.js"`
**Example**: `"settings/quick-actions/redirectchecker.js"`

### 3. DEFAULT SETTINGS (2 LOCATIONS)
**File**: `settings/settings.js`  
**Search for**: Both `defaultSettings` objects
**Add**: `{actionname}Enabled: false,`
**CRITICAL**: Must be `false`, not `true` (consistency with all other Quick Actions)

### 4. SETTINGS HTML TOGGLE
**File**: `settings/quick-actions-settings.html`
**Add**: Complete settings-item block with proper data-setting attribute
**Position**: Follow user requirements for placement order

### 5. QUICK ACTIONS REORDER INTEGRATION
**File**: `settings/quick-actions-reorder.js`
**Location**: `defaultButtons` array
**Add**: `{ id: '{actionname}Btn', name: '{Action Name}', setting: '{actionname}Enabled' }`

### 6. PROFILES INTEGRATION  
**File**: `settings/profiles.js`
**Location**: Profile settings objects
**Add**: `{actionname}Enabled: true,` to relevant profiles

### 7. POPUP SETTINGS ARRAY
**File**: `js/popup.js` 
**Location**: Button settings array (~line 4214)
**Add**: `{ id: '{actionname}Btn', setting: '{actionname}Enabled' }`

### 8. BACKGROUND SETTINGS LIST
**File**: `js/background.js`
**Location**: Settings permissions array (~line 1219)  
**Add**: `'{actionname}Enabled'` to the array

### 9. ONBOARDING INTEGRATION (2 LOCATIONS)
**File**: `js/onboarding-content.js`
**Location 1**: Settings array (~line 60) - Add: `'{actionname}Enabled'`
**Location 2**: Settings object (~line 1104) - Add: `{actionname}Enabled: true`

### 10. POPUP DOM ELEMENT REFERENCE
**File**: `js/popup.js`
**Location**: DOM element declarations (~line 80-90)
**Add**: `const {actionname}Btn = document.getElementById('{actionname}Btn');`

### 11. POPUP ELEMENT MAPPING
**File**: `js/popup.js`  
**Location**: Element mapping array (~line 4249)
**Add**: `{ id: '{actionname}Btn', element: {actionname}Btn }`

### 12. POPUP EXECUTION INTEGRATION (2 PARTS)
**File**: `js/popup.js`

**Part A - Event Listener** (~line 4078):
```javascript
if ({actionname}Btn) {
    {actionname}Btn.addEventListener('click', () => {
        execute{ActionName}Script();
    });
}
```

**Part B - Execute Function** (~line 4670):
```javascript
async function execute{ActionName}Script() {
    try {
        await restoreBeforeQuickAction('{Action Name}');
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (!tabs[0]) throw new Error('No active tab found');
        const tabId = tabs[0].id;

        await chrome.scripting.executeScript({
            target: { tabId },
            files: ['settings/quick-actions/{actionname}.js']
        });

        await chrome.scripting.executeScript({
            target: { tabId },
            func: () => {
                if (typeof {ActionName}Action !== 'undefined') {
                    {ActionName}Action.execute();
                } else {
                    throw new Error('{ActionName}Action not found');
                }
            }
        });

        showNotification('{Action Name} launched!', 'success');
        setTimeout(() => window.close(), 100);
    } catch (error) {
        console.error('Error executing {Action Name}:', error);
        showNotification('Could not launch {Action Name} on this page.', 'error');
    }
}
```

## COMMON FAILURE PATTERNS

### Settings Toggle Not Appearing
**Cause**: Missing integration points 3 or 4
**Fix**: Check settings.js defaults and HTML toggle

### Button Not Showing in Popup
**Cause**: Missing integration points 7, 10, 11
**Fix**: Check popup.js button array, DOM reference, element mapping

### "Function is not defined" Error  
**Cause**: Missing integration point 12
**Fix**: Create the execute function in popup.js

### Button Shows But Does Nothing
**Cause**: Missing integration points 2, 12  
**Fix**: Check manifest.json web resources and popup.js execute function

## VERIFICATION CHECKLIST

Before declaring a Quick Action complete:
- [ ] All 12 integration points implemented
- [ ] Settings toggle appears in correct position
- [ ] Button appears in popup when enabled
- [ ] Button executes without JavaScript errors
- [ ] Action functionality works as expected
- [ ] Extension can be reloaded without issues

## NAMING CONVENTIONS

- **File**: `{actionname}.js` (lowercase)
- **Class**: `{ActionName}Action` (PascalCase)  
- **Setting**: `{actionname}Enabled` (camelCase)
- **Button ID**: `{actionname}Btn` (camelCase)
- **Function**: `execute{ActionName}Script()` (PascalCase)

**Never skip any of these 12 integration points. The original documentation failure proves this.**