# Technology Stack

## Core Technologies
- **Chrome Extension Manifest V3**: Modern Chrome extension format
- **JavaScript (ES6+)**: Primary development language
- **HTML5 & CSS3**: User interface and styling
- **Chrome Extension APIs**: Extensions API, Storage API, Tabs API, Scripting API

## Architecture Components
- **Service Worker**: `js/background.js` for lifecycle management
- **Content Scripts**: Modular injection pattern based on URL matching
- **Popup Interface**: `popup.html` + `popup.js` for user interaction
- **Settings System**: Centralized configuration management
- **Message Passing**: Chrome runtime messaging for communication

## Key Libraries & APIs
- **Chrome Storage API**: Settings and data persistence
- **YouTube Data API v3**: Video metadata extraction
- **Offscreen Document API**: Audio playback in Manifest V3
- **AWS S3**: Audio file hosting for Pomodoro sounds
- **Readability Library**: Content extraction and analysis

## Audio System (SLTK)
- 28 AWS-hosted sounds + 2 local audio files
- Offscreen document pattern for audio playback
- Real-time volume control with debounced storage
- Specialized chronometer with seamless looping

## No Build System
- Direct file editing with Chrome reload
- No package.json or build tools required
- Manifest-based dependency management