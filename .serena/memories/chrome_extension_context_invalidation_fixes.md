# Chrome Extension Context Invalidation Error Fixes

This memory documents a comprehensive solution implemented to eliminate Chrome extension context invalidation errors that were causing console spam and breaking functionality.

## Problem Background

Chrome Manifest V3 extensions use ephemeral service workers that are terminated when inactive. This causes "Extension context invalidated" errors when content scripts try to access Chrome APIs after the service worker has been killed. The errors were appearing in:

- Gmail Email Pinner: "Context invalidated, skipping addPinIcons"  
- Gmail Jump Links: "Storage error: Error: Extension context invalidated"
- Gmail Thread Expander: "Error during scroll to first message: Error: Extension context invalidated"
- Drag Select Links: "Error loading settings: Error: Extension context invalidated"
- Email Pinner Popup: Complete failure due to direct Chrome API usage

## Solution Architecture

### 1. Centralized Context Manager (`js/context-manager.js`)

Created a universal context validation system:

```javascript
class GMBContextManager {
    constructor() {
        this.isValid = true;
        this.lastCheck = 0;
        this.invalidated = false;
        this.debugMode = false;
        this.listeners = new Set();
        this.cacheTimeout = 100; // Cache validation results for 100ms
    }
    
    isContextValid(forceCheck = false) {
        if (this.invalidated) return false;
        
        // Use cached result for frequent calls
        if (!forceCheck && Date.now() - this.lastCheck < this.cacheTimeout) {
            return this.isValid;
        }
        
        try {
            const valid = !!(chrome && chrome.storage && chrome.storage.local && 
                           chrome.runtime && chrome.runtime.id && chrome.runtime.getManifest);
            
            if (!valid) {
                this.markInvalidated('Extension context validation failed');
                return false;
            }
            
            chrome.runtime.getManifest(); // Test actual API access
            this.isValid = valid;
            this.lastCheck = Date.now();
            return true;
        } catch (error) {
            this.markInvalidated('Extension context test failed: ' + error.message);
            return false;
        }
    }
    
    async safeStorageGet(keys) {
        if (!this.isContextValid()) return {};
        
        try {
            return await chrome.storage.local.get(keys);
        } catch (error) {
            this.markInvalidated('Storage get failed: ' + error.message);
            return {};
        }
    }
    
    async safeStorageSet(items) {
        if (!this.isContextValid()) return false;
        
        try {
            await chrome.storage.local.set(items);
            return true;
        } catch (error) {
            this.markInvalidated('Storage set failed: ' + error.message);
            return false;
        }
    }
}

// Global instance
window.GMBContextManager = new GMBContextManager();
```

### 2. Service Worker Lifecycle Management (`js/background.js`)

Enhanced service worker with lifecycle monitoring:

```javascript
class ServiceWorkerLifecycleManager {
    constructor() {
        this.isActive = true;
        this.lastActivity = Date.now();
        this.activeOperations = new Set();
        this.contextValidationInterval = null;
        this.keepAliveInterval = null;
    }
    
    initializeLifecycleMonitoring() {
        // Context validation every 30 seconds
        this.contextValidationInterval = setInterval(() => {
            this.validateContext();
        }, 30000);
        
        // Keepalive mechanism - ping every 25 seconds during operations
        this.keepAliveInterval = setInterval(() => {
            if (this.activeOperations.size > 0) {
                this.updateActivity();
                console.log('💓 Service Worker: Keepalive ping');
            }
        }, 25000);
    }
    
    validateContext() {
        try {
            if (!chrome || !chrome.storage || !chrome.runtime) {
                this.handleContextInvalidation('Basic Chrome APIs unavailable');
                return false;
            }
            
            chrome.storage.local.get(['_contextTest'], (result) => {
                if (chrome.runtime.lastError) {
                    this.handleContextInvalidation('Storage API test failed');
                    return false;
                }
                this.updateActivity();
                return true;
            });
        } catch (error) {
            this.handleContextInvalidation('Context validation error: ' + error.message);
            return false;
        }
    }
}
```

### 3. Safe Chrome API Wrappers Pattern

Standard pattern implemented across all modules:

```javascript
// Context validation
function isExtensionContextValid() {
    try {
        return !!(chrome && chrome.storage && chrome.storage.local && 
                chrome.runtime && chrome.runtime.id);
    } catch (error) {
        return false;
    }
}

// Safe storage wrapper
async function safeChromeStorageGet(keys) {
    try {
        if (!isExtensionContextValid()) {
            return {};
        }
        return await chrome.storage.local.get(keys);
    } catch (error) {
        if (error.message && error.message.includes('Extension context invalidated')) {
            return {};
        }
        throw error;
    }
}

// Safe storage set wrapper
async function safeChromeStorageSet(data) {
    try {
        if (!isExtensionContextValid()) {
            return false;
        }
        await chrome.storage.local.set(data);
        return true;
    } catch (error) {
        if (error.message && error.message.includes('Extension context invalidated')) {
            return false;
        }
        throw error;
    }
}
```

## Files Modified

### Core Infrastructure
- **`js/context-manager.js`** - NEW: Centralized context management system
- **`manifest.json`** - Added context manager to Gmail content scripts loading order
- **`js/background.js`** - Enhanced with ServiceWorkerLifecycleManager and safe API wrappers

### Gmail Modules Updated  
- **`settings/gmail-email-pinner.js`** - Replaced custom context validation with centralized manager
- **`settings/gmail-jump-links.js`** - Added silent storage wrapper using centralized context manager  
- **`settings/gmail-thread-expander.js`** - Added graceful error handling in scroll functions

### Content Scripts Fixed
- **`alerts/email-pinner.js`** - CRITICAL: Replaced all direct Chrome API calls with safe wrappers
- **`settings/drag-select-links.js`** - Updated to use centralized context manager
- **`settings/global-shortcut-manager.js`** - Added safe storage wrappers for all Chrome API access

### Error Types Fixed
- **Syntax Errors**: Fixed duplicate closing brackets in `gmail-email-pinner.js`
- **Storage Errors**: All `chrome.storage.local` calls now use safe wrappers
- **Context Errors**: Silent handling when extension context becomes invalid
- **Runtime Errors**: Graceful fallback for tab creation and messaging

## Key Principles Applied

1. **Silent Error Handling**: Context invalidation is expected in MV3 - handle silently
2. **Graceful Degradation**: Features continue working with fallback behavior  
3. **Centralized Management**: Single source of truth for context validation
4. **Cached Validation**: Prevent excessive context checks (100ms cache timeout)
5. **Lifecycle Monitoring**: Service worker health tracking with keepalive mechanism

## Testing Strategy

1. **Normal Operation**: All features work as expected
2. **Service Worker Termination**: Let extension sit idle for 5+ minutes, features still work
3. **Gmail Navigation**: Extensive Gmail usage should not produce console errors
4. **Extension Reload**: Features recover gracefully after extension reload
5. **Console Monitoring**: Chrome Extensions console should be clean of context errors

## Results Achieved

- ✅ Zero context invalidation errors in console
- ✅ Email pinner popup works reliably  
- ✅ All Gmail features maintain functionality
- ✅ Silent error handling prevents console spam
- ✅ Graceful degradation when service worker terminates
- ✅ Improved user experience with no visible errors

This solution provides a robust foundation for handling Chrome Manifest V3 service worker lifecycle issues across the entire extension.