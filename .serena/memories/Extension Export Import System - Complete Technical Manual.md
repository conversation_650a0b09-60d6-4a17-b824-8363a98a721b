# SEO Time Machines Export/Import System - Complete Technical Manual

## Overview
The export/import system allows users to backup and restore ALL their extension settings and time-consuming data. This document provides a comprehensive guide for maintaining and extending this system.

## Core Principle: TIME-CONSUMING DATA MUST BE EXPORTED
Any data that takes user time to configure MUST be included in export/import:
- Curated lists (whitelists, blacklists, favorites)
- API keys and credentials
- Custom configurations
- User-created content (alerts, todos, pinned items)
- Personalized settings and preferences

## Complete List of Exportable Features

### 1. Main Extension Settings (`gmbExtractorSettings`)
- **Storage**: `chrome.storage.local`
- **Contains**: All toggle states and basic settings
- **Includes**: 
  - Feature toggles (100+ boolean flags)
  - Tracked domains list (trackedDomainsList)
  - UTM cleaner whitelist domains
  - Color settings, formats, durations
  - All feature-specific configurations

### 2. Large Settings (`gmbExtractorLargeSettings`)
- **Storage**: `chrome.storage.local`
- **Purpose**: Settings too large for sync storage
- **Contains**:
  - Video Speed Controller settings
  - Pomodoro timer configurations
  - Other large data structures

### 3. Profiles System (`profiles`)
- **Storage**: `chrome.storage.local`
- **Structure**: Array of profile objects
- **User Time**: Creating and configuring multiple profiles

### 4. HTML Cleaner Settings (`htmlCleanerSettings`)
- **Storage**: `chrome.storage.local`
- **Structure**: Object with 8 boolean options
- **User Time**: Customizing cleaning preferences

### 5. Keyboard Shortcuts (15+ shortcuts)
**Local Storage Shortcuts**:
- `copyElementShortcut`
- `colorpickerShortcut`
- `copyReplaceShortcut`

**Sync Storage Shortcuts**:
- `screenshotShortcut`
- `textTransformersCapitalCaseShortcut`
- `textTransformersLowerCaseShortcut`
- `textTransformersUpperCaseShortcut`
- `textTransformersSentenceCaseShortcut`
- `textTransformersSlugifyShortcut`
- `textTransformersTrimToPageShortcut`
- `textTransformersSortAlphabeticallyShortcut`
- `textTransformersRemoveEmptyLinesShortcut`
- `textTransformersRemoveDuplicateLinesShortcut`
- `textTimeMachineLauncherShortcut`
- `newTabRedirectShortcut`

### 6. Quick Actions Configuration
- `quickActionsOrder` - User-customized order
- `globalShortcutsEnabled` - Global toggle
- `copyElementEnabled` - Feature toggle
- `colorPickerEnabled` - Feature toggle

### 7. Whitelists and Blacklists (TIME-CONSUMING)
- `massUnsubscribeWhitelist` - Curated email senders
- `minimalReaderBlacklistDomains` - Sites to exclude
- `videoSpeedControllerBlacklist` - Sites to skip
- `blockedSites` - Pomodoro blocked sites
- `allowedUrls` - Pomodoro allowed URLs

### 8. Location Changer
- `settings` - Location settings object
- `locationChangerFavorites` - Saved favorite locations

### 9. Pomodoro Timer
- `pomodoroTodos` - User's todo items
- `pomodoroAudioSettings` - Custom audio preferences

### 10. Gmail Features (TIME-CONSUMING)
- `gmailPinnedEmails` - Carefully pinned important emails
- **Structure**: Array of {messageId, subject, from, date, snippet}

### 11. YouTube Integration
- `youtubeApiKey` - Manually obtained API key
- **Critical**: Users must get this from Google Console

### 12. Alerts System (TIME-CONSUMING)
- `activeAlerts` - User-created custom alerts
- **Structure**: Array of alert objects with id, title, time, enabled, etc.

### 13. Bulk Link Open (TIME-CONSUMING)
- `bulkLinkOpenSavedLists` - Curated link collections
- **Structure**: Object with named lists

### 14. Runtime States (NOT EXPORTED)
These are intentionally excluded as they're temporary:
- `pomodoroTimerState`
- `chronometerState`
- `quickTimerState`
- `screenshotData`
- Debug/developer states

## Technical Implementation Guide

### Adding Export/Import to a New Feature

#### Step 1: Identify Storage Pattern
Determine where your feature stores data:
```javascript
// Local storage (most common)
chrome.storage.local.set({ myFeatureData: data });

// Sync storage (for small data like shortcuts)
chrome.storage.sync.set({ myFeatureShortcut: shortcut });
```

#### Step 2: Add to Export Function
Location: `settings/settings.js` (~line 3540)

1. Add to storage retrieval:
```javascript
const allStorageData = await chrome.storage.local.get([
    // ... existing keys ...
    'myFeatureData', // Add your storage key
]);
```

2. Add to export data structure (~line 3660):
```javascript
const settingsData = {
    // ... existing fields ...
    
    // My Feature data - describe what it is
    myFeatureData: allStorageData.myFeatureData || defaultValue,
};
```

3. Add to metadata (~line 3720):
```javascript
metadata: {
    // ... existing metadata ...
    myFeatureDataCount: Array.isArray(allStorageData.myFeatureData) 
        ? allStorageData.myFeatureData.length : 0,
    hasMyFeatureData: !!(allStorageData.myFeatureData),
}
```

4. Update success message (~line 3775):
```javascript
const myFeatureCount = /* calculate count */;
const exportDetails = [
    // ... existing details ...
    myFeatureCount > 0 && `${myFeatureCount} my feature items`,
].filter(Boolean);
```

#### Step 3: Add to Import Function
Location: `settings/settings.js` (~line 4030)

1. Add import handler:
```javascript
// Import My Feature data
if (data.myFeatureData) {
    // Validate data structure
    const validData = /* validate and clean data */;
    
    await chrome.storage.local.set({ myFeatureData: validData });
    importStats.myFeatureDataCount = validData.length;
    console.log('Imported my feature data:', validData);
}
```

2. Update import success message (~line 4160):
```javascript
if (importStats.myFeatureDataCount > 0) {
    messageParts.push(`${importStats.myFeatureDataCount} my feature items`);
}
```

### Storage Patterns by Feature Type

#### Pattern 1: Simple Toggle/Setting
```javascript
// Export
featureEnabled: allStorageData.gmbExtractorSettings.featureEnabled || false,

// Import (handled by main settings import)
// No special handling needed
```

#### Pattern 2: Curated List (Array)
```javascript
// Export
myList: allStorageData.myList || [],

// Import with validation
if (data.myList && Array.isArray(data.myList)) {
    const validItems = data.myList.filter(item => 
        /* validation logic */
    );
    await chrome.storage.local.set({ myList: validItems });
}
```

#### Pattern 3: Complex Object
```javascript
// Export
myComplexData: allStorageData.myComplexData || {},

// Import with structure validation
if (data.myComplexData && typeof data.myComplexData === 'object') {
    // Validate required fields
    if (data.myComplexData.requiredField) {
        await chrome.storage.local.set({ myComplexData: data.myComplexData });
    }
}
```

#### Pattern 4: Sensitive Data (API Keys)
```javascript
// Export
apiKey: allStorageData.apiKey || null,

// Import with masking in logs
if (data.apiKey && typeof data.apiKey === 'string') {
    await chrome.storage.local.set({ apiKey: data.apiKey });
    console.log('Imported API key (masked):', 
        data.apiKey.substring(0, 6) + '...');
}
```

### Validation Best Practices

1. **Array Validation**:
```javascript
const validItems = (data.items || [])
    .filter(item => item && typeof item === 'string' && item.trim())
    .map(item => item.trim());
```

2. **Object Validation**:
```javascript
const isValidObject = obj => 
    obj && 
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.enabled === 'boolean';
```

3. **Prevent Data Loss**:
```javascript
// Always use logical OR with empty default
const items = data.items || [];
// Never use just data.items without fallback
```

## Testing Checklist

When adding export/import for a new feature:

1. **Export Testing**:
   - [ ] Create test data in your feature
   - [ ] Export settings
   - [ ] Verify JSON file contains your data
   - [ ] Check metadata counts are correct

2. **Import Testing**:
   - [ ] Clear all extension data
   - [ ] Import previously exported file
   - [ ] Verify feature data is restored
   - [ ] Test with corrupted/invalid data

3. **Edge Cases**:
   - [ ] Empty arrays/objects
   - [ ] Missing fields
   - [ ] Wrong data types
   - [ ] Very large datasets

4. **Cross-Tab Testing**:
   - [ ] Export from General Settings tab
   - [ ] Export from Extras tab
   - [ ] Export from Quick Actions tab
   - [ ] Verify data is complete from all tabs

## Common Pitfalls to Avoid

1. **Don't forget sync storage**: Some features use chrome.storage.sync
2. **Always validate imports**: Never trust imported data structure
3. **Preserve user work**: Time-consuming data is priority #1
4. **Handle missing data**: Use logical OR with defaults
5. **Test tab switching**: Export must work from any settings tab
6. **Count accurately**: Metadata should reflect actual counts
7. **Log appropriately**: Mask sensitive data in console logs

## Migration Considerations

When updating the export/import system:
1. Maintain backwards compatibility with v1.0 format
2. Handle missing fields gracefully
3. Document breaking changes
4. Increment version number if format changes significantly

## Feature Categories

**Critical User Data** (Time-consuming, MUST export):
- Curated lists and collections
- API keys and credentials  
- Custom configurations
- User-created content

**Settings** (Important but recreatable):
- Feature toggles
- UI preferences
- Keyboard shortcuts

**Runtime State** (DO NOT export):
- Timer states
- Temporary data
- Debug flags
- Session information