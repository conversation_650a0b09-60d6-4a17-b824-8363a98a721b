# Project Structure

## Root Directory Structure
```
GMB Extractor/
├── manifest.json           # Extension configuration & entry point
├── popup.html              # Main popup interface
├── offscreen.html          # Offscreen document for audio
├── blocked.html            # Blocked page interface
├── screenshot-editor.html  # Screenshot editing interface
├── CLAUDE.md              # Project guidance for Claude Code
├── CLAUDE.local.md        # User's private instructions
├── .gitignore             # Git ignore patterns
└── README files & tasks
```

## Core Directories

### `/js/` - JavaScript Modules
```
js/
├── background.js           # Service worker (extension lifecycle)
├── content.js             # Main content script coordinator  
├── popup.js               # Popup interface logic
├── universal-click-debugger.js  # Cross-site debugging
├── screenshot-selector.js  # Screenshot functionality
├── tooltip-system.js      # Tooltip management
├── alerts/                # Alert system components
├── pomodoro/              # Pomodoro timer system
├── utilities/             # Utility functions
├── readability/           # Content readability analysis
└── location-changer-js/   # Location changing functionality
```

### `/css/` - Styling Components
```
css/
├── gmb-extractor.css      # Google Maps styling
├── google-search-extractor.css  # Google Search styling
├── gmb-content-ui.css     # Content UI styling
├── gmail-sender-icons.css # Gmail enhancements
├── location-changer.css   # Location changer styling
└── minimal-reader.css     # Reader mode styling
```

### `/settings/` - Settings System & Quick Actions
```
settings/
├── settings.js            # Centralized settings management
├── settings.html          # Main settings interface
├── general-settings.html  # General configuration
├── extras-settings.html   # Advanced features
├── quick-actions-settings.html  # Quick Actions config
├── dom-snapshot-utility.js  # DOM restoration system
├── notification-utility.js  # Notification system
├── logging-utility.js     # Logging functionality
└── quick-actions/         # Individual Quick Action modules
    ├── htags.js
    ├── colorpicker.js
    ├── wordcounter.js
    └── [30+ other Quick Actions]
```

### Support Directories
- `/images/` - Extension icons and assets
- `/sounds/` - Audio files for Pomodoro timer
- `/alerts/` - Alert popup components  
- `/docs/` - Documentation and changelog
- `/memory-bank/` - Project patterns and documentation
- `/.claude/` - Claude Code configuration
- `/.vscode/` - VS Code workspace settings

## Content Script Loading Pattern

### URL-Based Injection
1. **Google Domains**: Core extraction functionality
2. **Gmail**: Email enhancement tools  
3. **YouTube**: Video analysis tools
4. **All URLs**: Universal Quick Actions and utilities

### Modular Architecture
- Platform-specific extractors in `/js/`
- Universal tools loaded on all sites
- Settings-driven feature toggles
- Namespace protection against conflicts