# Code Style & Conventions

## JavaScript Conventions
- **ES6+ Syntax**: Modern JavaScript features throughout
- **Function Naming**: camelCase with descriptive names
- **Variable Naming**: camelCase, descriptive and meaningful
- **Class Naming**: PascalCase for classes (e.g., `MyQuickAction`)
- **Constants**: UPPER_SNAKE_CASE for constants

## File Naming Conventions
- **JavaScript Files**: kebab-case (e.g., `google-search-extractor.js`)
- **CSS Files**: kebab-case matching functionality
- **HTML Files**: kebab-case with purpose indication
- **Settings Files**: descriptive names in settings/ directory

## Code Organization Patterns
### Namespace Protection Pattern
```javascript
if (window.GMBExtractorLoaded) {
  console.log('Already loaded, skipping...');
} else {
  window.GMBExtractorLoaded = true;
  // Initialize functionality
}
```

### Message Passing Pattern
```javascript
chrome.tabs.sendMessage(tabId, {
  action: "actionName",
  data: parameters
}, response => {
  // Handle response
});
```

### Quick Action Class Structure
```javascript
class MyQuickAction {
  static execute() {
    // Implementation
  }
  
  static reset() {
    // Cleanup
  }
}
```

## Critical Design Rules
- **NO EMOJIS**: Absolutely no emojis in any text, buttons, or notifications
- **Purple Brand Color**: #7C3AED for borders, buttons, progress bars
- **Dark Theme**: Background #0a0a0a, text #d1d5db
- **Memory Management**: Always remove event listeners and clear intervals

## Documentation Standards
- Comprehensive CLAUDE.md with project guidance
- Memory bank documentation for patterns
- Golden rules for specific features
- Changelog maintenance for version tracking