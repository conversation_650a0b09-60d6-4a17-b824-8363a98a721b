# Text Transformers Development Guide - Memory Bank

## Quick Reference for Text Transformers Implementation

### Core Architecture
- **Text Transformers Class**: `js/text-transformers.js` - Static class with unified execution pipeline
- **Settings Integration**: `settings/extras-settings.html` - Toggle controls and shortcut configuration  
- **Global Shortcut Manager**: `settings/global-shortcut-manager.js` - Cross-platform keyboard handling

### Essential Integration Points

#### 1. Text Transformers Class Changes
```javascript
// Add to transform() switch statement
case 'yourNewTransform':
    return this.yourNewTransformMethod(text);

// Add transformation method
static yourNewTransformMethod(text) {
    // transformation logic
    return transformedText;
}

// Add execute method  
static async executeYourNewTransform() {
    return await this.executeTransformation('yourNewTransform');
}
```

#### 2. Global Shortcut Manager Integration (8 Required Changes)

**A. Storage Loading** (Line ~70):
```javascript
'textTransformersYourNewTransformShortcut'
```

**B. Shortcut Registration** (Line ~144):
```javascript
if (result.textTransformersYourNewTransformShortcut) {
    this.shortcuts['textTransformersYourNewTransform'] = result.textTransformersYourNewTransformShortcut;
    hasActiveShortcuts = true;
}
```

**C. Storage Change Listener** (Line ~233):
```javascript
if (changes.textTransformersYourNewTransformShortcut) {
    this.shortcuts['textTransformersYourNewTransform'] = changes.textTransformersYourNewTransformShortcut.newValue;
}
```

**D. Shortcut Detection Logic** (Line ~303):
```javascript
(this.shortcuts['textTransformersYourNewTransform'] && this.matchesShortcut(pressedShortcut, this.shortcuts['textTransformersYourNewTransform'])) ||
```

**E. Keyboard Event Handler** (Line ~395):
```javascript
if (!shortcutMatched) {
    const textTransformersYourNewTransformShortcut = this.shortcuts['textTransformersYourNewTransform'];
    if (textTransformersYourNewTransformShortcut && this.matchesShortcut(pressedShortcut, textTransformersYourNewTransformShortcut)) {
        e.preventDefault();
        e.stopImmediatePropagation();
        this.executeTextTransformersYourNewTransform();
        shortcutMatched = true;
    }
}
```

**F. Execute Method** (After Line ~1235):
```javascript
async executeTextTransformersYourNewTransform() {
    console.log('SEO Time Machines: Text Transformers Your New Transform shortcut triggered');
    try {
        if (typeof window.TextTransformers !== 'undefined') {
            const result = await window.TextTransformers.executeYourNewTransform();
            if (result && result.error) {
                console.error('SEO Time Machines: Text Transformers error:', result.error);
            } else {
                console.log('SEO Time Machines: Text Transformers Your New Transform completed:', result);
            }
        } else {
            console.error('SEO Time Machines: TextTransformers not available');
        }
    } catch (error) {
        console.error('SEO Time Machines: Error executing Text Transformers Your New Transform:', error);
    }
}
```

**G. Tool Names Mapping** (Line ~500):
```javascript
'textTransformersYourNewTransform': 'Text Transformers (Your Description)'
```

**H. Storage Key Mapping** (Line ~1352):
```javascript
'textTransformersYourNewTransform': 'textTransformersYourNewTransformShortcut'
```

#### 3. Settings HTML Template
```html
<div class="custom-shortcut-container">
    <div class="custom-shortcut-label">
        <span style="color: #7C3AED; font-size: 18px;">●</span>
        [Name] Transformation Shortcut
    </div>
    <div class="custom-shortcut-input-wrapper">
        <input type="text" id="textTransformersYourNewTransformShortcut" class="custom-shortcut-input" data-setting="textTransformersYourNewTransformShortcut" placeholder="Click here and press keys to set shortcut..." title="Set keyboard shortcut for transformation">
        <div class="shortcut-help">
            <span class="help-icon">ℹ️</span>
            <div class="help-tooltip">
                <strong>[Name] Transformation</strong><br>
                [Description]<br>
                Example: "[input]" → "[output]"
            </div>
        </div>
    </div>
    <div class="shortcut-status"></div>
</div>
```

### Storage Patterns
- **Shortcuts**: Chrome Sync Storage (`textTransformers[Type]Shortcut`)
- **Enable/Disable**: Chrome Local Storage (`gmbExtractorSettings.textTransformersEnabled`)
- **Auto-Paste**: Chrome Local Storage (`gmbExtractorSettings.textTransformersAutoPaste`)

### Required Permissions
```json
{
  "permissions": [
    "storage",
    "clipboardRead", 
    "clipboardWrite"
  ]
}
```

### Text Input Support
1. **INPUT/TEXTAREA fields**: `selectionStart/selectionEnd`
2. **Regular text selection**: `window.getSelection().toString()`  
3. **Clipboard fallback**: `navigator.clipboard.readText()`

### Auto-Paste System
- Respects user preference: `gmbExtractorSettings.textTransformersAutoPaste`
- Handles INPUT/TEXTAREA and contenteditable elements
- Fallback to clipboard-only mode if paste fails
- Platform-aware (execCommand vs Keyboard simulation)

### Notification System
- Positioned near text selection
- Purple accent styling (`#7C3AED`)
- Fade in/out animations
- Viewport boundary detection

### Critical Implementation Rules
1. **Always follow exact naming patterns** for consistency
2. **Add ALL 8 Global Shortcut Manager integration points**
3. **Use proper storage types** (sync for shortcuts, local for settings)
4. **Handle platform differences** (Mac vs Windows keyboard shortcuts)
5. **Implement proper error handling** for clipboard and paste operations
6. **Test across different input types** (INPUT/TEXTAREA/contenteditable/regular selection)

### Common Pitfalls
- Missing any of the 8 required Global Shortcut Manager changes
- Using wrong storage type (sync vs local)
- Not handling clipboard permission failures
- Incorrect HTML data-setting attributes
- Missing platform-specific shortcut normalization
- Not updating conflict detection mappings

### Testing Checklist
- [ ] Text selection in different element types
- [ ] Keyboard shortcut registration and execution
- [ ] Auto-paste functionality with setting toggle
- [ ] Shortcut conflict detection and resolution
- [ ] Settings persistence across page reloads
- [ ] Platform-specific shortcut handling (Mac/Windows)
- [ ] Clipboard fallback when no text selected
- [ ] Error handling for clipboard permission issues

This memory provides the essential patterns and integration points needed to quickly implement new text transformation tools that work seamlessly with the existing STM extension architecture.