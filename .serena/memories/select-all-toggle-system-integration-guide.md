# Select All/Deselect All Toggle System - Complete Integration Guide

## System Overview

The Select All/Deselect All toggle system is a centralized mechanism that manages grouped toggle controls across different accordion sections in the settings interface. This system automatically synchronizes individual toggle states with their respective section's "select all" toggle, providing users with bulk enable/disable functionality.

## Core Architecture Components

### 1. accordionMappings Object Structure

**Location**: `settings/settings.js` lines 2750-2851 and 2980-3051 (duplicated for different methods)

Each accordion section is defined with:
```javascript
sectionName: {
    selectAllId: 'sectionSelectAll',  // ID of the select all toggle element
    settingSelectors: [               // CSS selectors for individual toggles
        '[data-setting="setting1Enabled"]',
        '[data-setting="setting2Enabled"]',
        // ... more selectors
    ]
}
```

**Current Accordion Sections:**
- `googleTools`: Search and location-related tools (7 settings)
- `businessTools`: Business data extraction tools (3 settings) 
- `developerTools`: Development and content tools (5 settings)
- `generalTools`: General utility tools (1 setting)
- `gmailTools`: Gmail enhancement tools (8 settings)
- `youtubeTools`: YouTube-related tools (6 settings)
- `productivity`: Productivity and time management (6 settings)
- `textTools`: Text transformation tools (1 setting)
- `quickActions`: Quick action button tools (20 settings)

### 2. Toggle Implementation System

**Location**: `settings/settings.js` lines 2852-2950

The system implements sophisticated toggle management:

#### Event Listener Setup:
- Iterates through each accordion section
- Sets up click handlers for select all toggles
- Implements state synchronization logic
- Adds MutationObserver for individual toggle monitoring

#### State Management:
```javascript
// State calculation logic
const activeCount = toggles.filter(toggle => 
    toggle.classList.contains('toggle-switch--active')
).length;

const shouldBeActive = activeCount === toggles.length;
```

#### Protective Mechanisms:
- **Pomodoro Exclusion**: Skips pomodoro-related toggles to prevent timer reset cascade
- **Debouncing**: 100ms timeout to prevent rapid firing
- **State Protection**: Only updates DOM when state actually needs to change

### 3. Storage System Architecture

#### Main Settings Storage (`chrome.storage.local`):
- **Primary Location**: `gmbExtractorSettings` object
- **Large Settings**: `gmbExtractorLargeSettings` for bulky configurations
- **Associated Data**: Whitelists, blacklists, configurations

#### Sync Storage (`chrome.storage.sync`):
- **Text Transformers Shortcuts**: All 10 text transformation shortcuts
- **Screenshot Shortcut**: Cross-device screenshot functionality
- **New Tab Redirect Shortcut**: Tab redirection hotkey

#### Local Storage Only:
- **Copy Element Shortcut**: `copyElementShortcut`
- **Color Picker Shortcut**: `colorpickerShortcut` 
- **Copy Replace Shortcut**: `copyReplaceShortcut`
- **Quick Actions Order**: `quickActionsOrder`
- **Feature States**: `copyElementEnabled`, `colorPickerEnabled`

## Complete Integration Checklist

### Step 1: Default Settings Integration

**Location**: `settings/settings.js` lines 100-200+

Add your new setting to the `defaultSettings` object:
```javascript
const defaultSettings = {
    // ... existing settings
    myNewFunctionEnabled: false,           // Main toggle
    myNewFunctionSetting1: 'defaultValue', // Configuration option 1
    myNewFunctionSetting2: true,           // Configuration option 2
    myNewFunctionShortcut: '',            // Keyboard shortcut (if applicable)
    // ... continue with all related settings
};
```

**Setting Naming Convention:**
- Main toggle: `functionNameEnabled` (boolean)
- Sub-settings: `functionNameSettingDescription` (various types)
- Shortcuts: `functionNameShortcut` (string)

### Step 2: HTML Toggle Creation

**Locations**: Various settings HTML files (`settings/quick-actions-settings.html`, `settings/extras-settings.html`, etc.)

Create the toggle element with proper structure:
```html
<div class="settings-item">
    <div class="settings-item__content">
        <div class="settings-item__info">
            <div class="settings-item__label">My New Function</div>
            <div class="settings-item__description">Description of what this function does</div>
        </div>
        <div class="settings-item__control">
            <div class="toggle-switch toggle-switch--active" 
                 id="myNewFunctionToggle" 
                 data-setting="myNewFunctionEnabled"></div>
        </div>
    </div>
</div>
```

**Critical Requirements:**
- `data-setting` attribute MUST match the key in defaultSettings
- `id` should follow pattern: `settingKeyToggle`
- Include `toggle-switch--active` class if default is true
- Place in appropriate accordion section

### Step 3: Accordion Mapping Updates

**Location**: `settings/settings.js` lines 2750-2851

Add your setting selector to the appropriate accordion section:
```javascript
appropriateSection: {
    selectAllId: 'appropriateSectionSelectAll',
    settingSelectors: [
        // ... existing selectors
        '[data-setting="myNewFunctionEnabled"]',  // Add your new setting here
    ]
}
```

**Section Selection Guide:**
- **quickActions**: User-facing webpage manipulation tools
- **developerTools**: Technical/debugging tools, content manipulation
- **googleTools**: Google Search/Maps specific functionality
- **productivity**: Time management, workflow tools
- **gmailTools**: Gmail-specific enhancements
- **youtubeTools**: YouTube-specific functionality
- **generalTools**: Utility functions, cleanup tools
- **businessTools**: Business data extraction tools
- **textTools**: Text transformation functionality

### Step 4: Export System Integration

**Location**: `settings/settings.js` lines 3537-3700

#### A. Add to Storage Query (if using local storage):
```javascript
const allStorageData = await chrome.storage.local.get([
    'gmbExtractorSettings',
    // ... existing keys
    'myNewFunctionShortcut',  // Add if using separate storage
]);
```

#### B. Add to Sync Storage Query (if using sync storage):
```javascript
const syncStorageData = await chrome.storage.sync.get([
    'screenshotShortcut',
    // ... existing shortcuts
    'myNewFunctionShortcut',  // Add if using sync storage
]);
```

#### C. Add to Export Data Object:
```javascript
const settingsData = {
    // ... existing data
    myNewFunctionShortcut: allStorageData.myNewFunctionShortcut || null,
    // ... or from syncStorageData if using sync storage
};
```

#### D. Update Metadata Counters:
```javascript
metadata: {
    // ... existing metadata
    hasMyNewFunctionShortcut: !!allStorageData.myNewFunctionShortcut,
    myNewFunctionConfigCount: // Count related configurations
}
```

### Step 5: Import System Integration

**Location**: `settings/settings.js` lines 3700-4000+

#### A. Add to v2.0 Format Import (Main Settings):
```javascript
// Import main settings
if (data.gmbExtractorSettings) {
    const mergedSettings = { ...this.defaultSettings, ...data.gmbExtractorSettings };
    // myNewFunctionEnabled will be automatically included
}
```

#### B. Add Shortcut Import (if applicable):
```javascript
// Import keyboard shortcuts
if (data.myNewFunctionShortcut) {
    await chrome.storage.local.set({ myNewFunctionShortcut: data.myNewFunctionShortcut });
    importStats.shortcuts++;
    console.log('Imported My New Function shortcut:', data.myNewFunctionShortcut);
}
```

#### C. Add Associated Data Import (if applicable):
```javascript
// Import associated configuration data
if (data.myNewFunctionConfigData && Array.isArray(data.myNewFunctionConfigData)) {
    await chrome.storage.local.set({ myNewFunctionConfigData: data.myNewFunctionConfigData });
    importStats.myNewFunctionConfigCount = data.myNewFunctionConfigData.length;
    console.log('Imported My New Function config:', data.myNewFunctionConfigData);
}
```

### Step 6: Settings Collection Integration

**Location**: `settings/settings.js` collectSettings() method

The system automatically collects settings that have `data-setting` attributes, but verify:
```javascript
collectSettings() {
    // ... existing logic
    const element = document.querySelector(`[data-setting="${key}"]`);
    // Your setting will be automatically collected if HTML is correct
}
```

### Step 7: UI Update Integration

**Location**: `settings/settings.js` updateUIFromSettings() method

The system automatically applies settings to UI elements with matching `data-setting` attributes.

## Storage Decision Matrix

### Use Main Settings (`gmbExtractorSettings`) When:
- Setting is a simple boolean, string, or number
- Setting is core functionality configuration
- Setting doesn't need cross-device sync
- Setting is under 1KB of data

### Use Sync Storage (`chrome.storage.sync`) When:
- Setting is a keyboard shortcut that should sync across devices
- Setting is user preference that benefits from cloud sync
- Data is small (sync storage has limits)

### Use Separate Local Storage When:
- Setting is large or complex data structure
- Setting is device-specific configuration
- Setting is temporary/cache data
- Setting needs special handling or validation

### Use Large Settings (`gmbExtractorLargeSettings`) When:
- Configuration data is large (>5KB)
- Data structure is complex (like video speed controller settings)
- Setting has multiple sub-objects and arrays

## Advanced Integration Patterns

### Shortcut Integration Pattern:
```javascript
// 1. Add to defaultSettings (in main settings)
myNewFunctionShortcut: '',

// 2. Store separately for quick access
await chrome.storage.local.set({ myNewFunctionShortcut: shortcutString });

// 3. Export separately
myNewFunctionShortcut: allStorageData.myNewFunctionShortcut || null,

// 4. Import with validation
if (data.myNewFunctionShortcut) {
    await chrome.storage.local.set({ myNewFunctionShortcut: data.myNewFunctionShortcut });
}
```

### Whitelist/Blacklist Pattern:
```javascript
// 1. Add to defaultSettings as array
myNewFunctionWhitelist: [],

// 2. Store in main settings object (automatically handled)

// 3. Export with metadata
myNewFunctionWhitelist: allStorageData.gmbExtractorSettings.myNewFunctionWhitelist || [],
metadata: {
    myNewFunctionWhitelistCount: Array.isArray(this.settings.myNewFunctionWhitelist) ? 
        this.settings.myNewFunctionWhitelist.length : 0
}

// 4. Import with validation
// Automatically handled in main settings merge
```

### Complex Configuration Pattern:
```javascript
// 1. Add to defaultSettings with full structure
myNewFunctionConfig: {
    option1: 'default',
    option2: true,
    advancedSettings: {
        subOption1: 100,
        subOption2: []
    }
},

// 2. Consider large settings if data is substantial
await chrome.storage.local.set({ 
    gmbExtractorLargeSettings: {
        ...existingLargeSettings,
        myNewFunctionConfig: complexConfigObject
    }
});
```

## Error Handling and Validation

### Required Validations:
1. **HTML Structure**: Ensure `data-setting` attribute matches defaultSettings key exactly
2. **Storage Consistency**: Verify setting is stored in expected location
3. **Export Coverage**: Confirm setting appears in export data
4. **Import Handling**: Test both v1.0 and v2.0 import scenarios
5. **Default Values**: Ensure graceful handling when setting is undefined
6. **Type Safety**: Validate data types during import/export

### Common Integration Errors:
1. **Mismatched Keys**: `data-setting` doesn't match defaultSettings key
2. **Missing Accordion Mapping**: Setting not included in settingSelectors array
3. **Storage Mismatch**: Storing in wrong location (local vs sync)
4. **Export Omission**: Forgetting to include in export data collection
5. **Import Missing**: Not handling in import restoration
6. **Type Inconsistency**: Changing data types between versions

## Testing Checklist

### Pre-Integration Testing:
- [ ] Default setting value appears correctly in UI
- [ ] Toggle responds to manual clicks
- [ ] Setting persists after page reload
- [ ] Setting appears in export data
- [ ] Setting restores correctly from import

### Select All Integration Testing:
- [ ] Individual toggle responds to section select all
- [ ] Section select all updates when individual toggles change
- [ ] No console errors during bulk operations
- [ ] Proper state synchronization across page loads

### Cross-Device Testing (if using sync storage):
- [ ] Shortcut syncs across Chrome profiles
- [ ] Import/export works across devices
- [ ] No data loss during sync operations

## Advanced Considerations

### Performance Optimization:
- Use debouncing for settings that trigger expensive operations
- Consider lazy loading for complex configurations
- Implement proper cleanup for event listeners

### Memory Management:
- Clear intervals/timeouts when features disabled
- Remove event listeners when elements destroyed
- Proper cleanup prevents memory leaks

### Security Considerations:
- Validate all imported data
- Sanitize user inputs in configuration fields
- Use proper escaping for HTML content

## Extension Integration

### Content Script Integration:
1. Add setting check in content scripts
2. Implement feature enable/disable logic
3. Add cleanup when setting disabled
4. Test across all supported domains

### Background Script Integration:
1. Monitor setting changes via storage listeners
2. Update service worker behavior based on settings
3. Handle cross-tab synchronization

This comprehensive guide ensures complete integration of new functions into the Select All/Deselect All toggle system while maintaining consistency with existing patterns and ensuring robust export/import functionality.