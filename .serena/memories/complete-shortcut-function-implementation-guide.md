# Complete Technical Guide: Implementing New Functions with Shortcuts

## CRITICAL DISCOVERY: Dual Storage Mechanism Requirements

### Root Cause Analysis from New Tab Redirect Case Study

**THE PROBLEM SOLVED**: New Tab Redirect shortcuts failed because they used LOCAL storage while global shortcut manager loaded from SYNC storage.

**Storage Architecture Discovery**:
- **Text Time Machine Launcher WORKS** because shortcuts are saved to SYNC storage AND loaded from SYNC storage
- **New Tab Redirect FAILED** because shortcuts were saved to LOCAL storage BUT loaded from SYNC storage
- **Solution**: Implement dual storage - save to BOTH LOCAL and SYNC storage

### Critical Storage Mechanism Patterns

#### Working Pattern (Text Time Machine Launcher):
1. User enters shortcut → UI saves to LOCAL storage via `autoSaveSettings()`
2. UI ALSO saves to SYNC storage via explicit `chrome.storage.sync.set()`
3. Global shortcut manager loads from SYNC storage
4. ✅ Shortcut execution works

#### Broken Pattern (New Tab Redirect - Before Fix):
1. User enters shortcut → UI saves to LOCAL storage via `autoSaveSettings()`
2. NO sync storage save
3. Global shortcut manager loads from SYNC storage → NOT FOUND
4. ❌ Shortcut execution fails

## Complete Shortcut Architecture

### File Structure and Components

#### Core Files for Shortcut Implementation:
- `settings/settings.js` - Settings management and storage
- `settings/extras-settings.html` - UI components and accordions
- `settings/global-shortcut-manager.js` - Cross-page shortcut handling
- `js/text-transformers.js` - Text-based function execution
- `js/background.js` - Tab creation and messaging
- `manifest.json` - Web accessible resources

### Shortcut Types and Patterns

#### Type 1: Text Transformer Shortcuts
**Use Case**: Text manipulation functions (case conversion, formatting, etc.)
**Integration Pattern**: Text Transformers class + Global shortcut manager
**Storage**: SYNC storage for shortcuts, LOCAL storage for settings

#### Type 2: Tab/URL Launcher Shortcuts  
**Use Case**: Open URLs, launch applications (Text Time Machine, New Tab Redirect)
**Integration Pattern**: Background script messaging + Global shortcut manager
**Storage**: SYNC storage for shortcuts, LOCAL storage for settings

#### Type 3: Quick Action Shortcuts
**Use Case**: DOM manipulation tools (HTags, Color Picker, etc.)
**Integration Pattern**: Content script injection + Global shortcut manager
**Storage**: LOCAL storage for settings, SYNC storage for shortcuts

## Technical Implementation Guide

### Step 1: Default Settings Configuration

**File**: `settings/settings.js`
**Location**: Search for `defaultSettings` objects (2 locations around lines 120 and 2050)

```javascript
// Add to BOTH defaultSettings objects
yourFunctionEnabled: true,
yourFunctionUrl: '',           // For URL launchers
yourFunctionShortcut: '',      // Always empty string default
yourFunctionAutoPaste: true,   // For text transformers
```

**CRITICAL**: Always use `''` (empty string) for shortcut defaults, never null or undefined.

### Step 2: UI Components Implementation

**File**: `settings/extras-settings.html`
**Pattern**: Follow accordion structure from New Tab Redirect implementation

#### Toggle Switch:
```html
<div class="settings-item settings-item--expanded">
    <div class="settings-item__content">
        <div class="settings-item__info">
            <div class="settings-item__label">Your Function Name</div>
            <div class="settings-item__description">Detailed description of functionality</div>
        </div>
        <div class="settings-item__control">
            <div class="toggle-switch toggle-switch--active" id="yourFunctionToggle" data-setting="yourFunctionEnabled"></div>
        </div>
    </div>
</div>
```

#### Accordion Configuration:
```html
<div class="copy-replace-accordion">
    <div class="copy-replace-header" id="yourFunctionHeader">
        <div class="copy-replace-title">
            <span style="color: #7C3AED; font-size: 16px;">●</span> Function Configuration
        </div>
        <span class="copy-replace-icon" id="yourFunctionIcon">▼</span>
    </div>
    <div class="copy-replace-content" id="yourFunctionContent">
        <div class="copy-replace-body">
            <!-- Configuration inputs here -->
        </div>
    </div>
</div>
```

#### Custom Shortcut Input:
```html
<div class="custom-shortcut-container">
    <div class="custom-shortcut-label">
        <span style="color: #7C3AED; font-size: 18px;">●</span>
        Your Function Shortcut
    </div>
    <div class="custom-shortcut-input-wrapper">
        <input type="text" id="yourFunctionShortcut" class="custom-shortcut-input" 
               data-setting="yourFunctionShortcut" 
               placeholder="Click here and press keys to set shortcut..." 
               title="Set keyboard shortcut for Your Function">
        <div class="shortcut-help">
            <span class="help-icon">ℹ️</span>
            <div class="help-tooltip">
                <strong>Your Function</strong><br>
                Description of what this shortcut does<br>
                Works globally across all websites.
            </div>
        </div>
    </div>
    <div class="shortcut-status"></div>
</div>
```

### Step 3: Accordion JavaScript Integration

**File**: `settings/settings.js`
**Method**: Add to `setupControls()` around line 1150

```javascript
// Add accordion setup call
this.setupYourFunctionAccordion();
```

**Method**: Create accordion setup method around line 1400

```javascript
setupYourFunctionAccordion() {
    const header = document.getElementById('yourFunctionHeader');
    const content = document.getElementById('yourFunctionContent');
    const icon = document.getElementById('yourFunctionIcon');
    
    if (header && content && icon) {
        header.addEventListener('click', () => {
            const isExpanded = content.classList.contains('expanded');
            
            if (isExpanded) {
                content.classList.remove('expanded');
                icon.classList.remove('expanded');
                header.classList.remove('expanded');
                icon.textContent = '▼';
            } else {
                content.classList.add('expanded');
                icon.classList.add('expanded');
                header.classList.add('expanded');
                icon.textContent = '▲';
            }
        });
    }
}
```

### Step 4: CRITICAL - Dual Storage Implementation

**File**: `settings/settings.js`
**Method**: Modify `saveShortcutFromInput()` around line 1720

```javascript
saveShortcutFromInput(inputElement, shortcut) {
    const storageKey = inputElement.getAttribute('data-setting');
    if (!storageKey) return;

    // Save to settings object (LOCAL storage via autoSaveSettings)
    if (!this.settings) this.settings = {};
    this.settings[storageKey] = shortcut;

    // Auto-save the settings to LOCAL storage
    this.autoSaveSettings();
    
    // CRITICAL: ALSO save to SYNC storage for global shortcuts
    if (storageKey === 'yourFunctionShortcut') {
        chrome.storage.sync.set({ [storageKey]: shortcut });
        console.log(`Shortcut also saved to SYNC storage for ${storageKey}:`, shortcut);
    }
    
    console.log(`Shortcut saved for ${storageKey}:`, shortcut);
}
```

**CRITICAL**: Add your shortcut to the dual storage check. This is THE most important step.

### Step 5: Export/Import System Integration

#### A. Sync Storage Loading Array
**File**: `settings/settings.js`
**Location**: Around line 3553

```javascript
const syncStorageData = await chrome.storage.sync.get([
    'screenshotShortcut',
    'textTransformersCapitalCaseShortcut',
    // ... other shortcuts ...
    'textTimeMachineLauncherShortcut',
    'yourFunctionShortcut'  // ADD HERE
]);
```

#### B. Export Data Structure
**File**: `settings/settings.js`
**Location**: Around line 3601

```javascript
textTimeMachineLauncherShortcut: syncStorageData.textTimeMachineLauncherShortcut || null,
yourFunctionShortcut: syncStorageData.yourFunctionShortcut || null,  // ADD HERE
globalShortcutsEnabled: allStorageData.globalShortcutsEnabled || true,
```

#### C. Import Functionality
**File**: `settings/settings.js`
**Location**: Around line 3875

```javascript
if (data.textTimeMachineLauncherShortcut) {
    await chrome.storage.sync.set({ textTimeMachineLauncherShortcut: data.textTimeMachineLauncherShortcut });
    importStats.shortcuts++;
    console.log('Imported Text Time Machine Launcher shortcut:', data.textTimeMachineLauncherShortcut);
}

if (data.yourFunctionShortcut) {
    await chrome.storage.sync.set({ yourFunctionShortcut: data.yourFunctionShortcut });
    importStats.shortcuts++;
    console.log('Imported Your Function shortcut:', data.yourFunctionShortcut);
}
```

### Step 6: Global Shortcut Manager Integration

**File**: `settings/global-shortcut-manager.js`

#### A. Storage Loading (Line ~82)
```javascript
const result = await chrome.storage.sync.get([
    'copyElementShortcut',
    // ... other shortcuts ...
    'textTimeMachineLauncherShortcut',
    'yourFunctionShortcut'  // ADD HERE
]);
```

#### B. Shortcut Registration (Line ~195)
```javascript
if (result.yourFunctionShortcut) {
    this.shortcuts['yourFunction'] = result.yourFunctionShortcut;
    hasActiveShortcuts = true;
}
```

#### C. Storage Change Listener (Line ~300)
```javascript
if (changes.yourFunctionShortcut) {
    this.shortcuts['yourFunction'] = changes.yourFunctionShortcut.newValue;
}
```

#### D. Shortcut Detection Logic (Line ~518)
```javascript
const yourFunctionShortcut = this.shortcuts['yourFunction'];
if (yourFunctionShortcut && this.matchesShortcut(pressedShortcut, yourFunctionShortcut)) {
    e.preventDefault();
    e.stopImmediatePropagation();
    this.executeYourFunction();
    shortcutMatched = true;
}
```

#### E. Execute Method (Add after line 1235)
```javascript
async executeYourFunction() {
    console.log('SEO Time Machines: Your Function shortcut triggered');
    try {
        // For text transformers:
        if (typeof window.TextTransformers !== 'undefined') {
            const result = await window.TextTransformers.executeYourFunction();
            // Handle result...
        }
        
        // For URL launchers:
        if (typeof window.TextTransformers !== 'undefined') {
            const result = await window.TextTransformers.executeYourFunction();
            // Handle result...
        }
        
        console.log('SEO Time Machines: Your Function completed');
    } catch (error) {
        console.error('SEO Time Machines: Error executing Your Function:', error);
    }
}
```

#### F. Tool Names Mapping (Line ~1623)
```javascript
'yourFunction': 'yourFunctionShortcut',
```

#### G. Settings Input Mapping (Line ~1661)
```javascript
'yourFunction': '#yourFunctionShortcut',
```

### Step 7: Function Implementation Patterns

#### For Text Transformers:
**File**: `js/text-transformers.js`

```javascript
// Add to transform() method switch statement
case 'yourTransform':
    return this.yourTransformMethod(text);

// Add transformation method
static yourTransformMethod(text) {
    // Transformation logic here
    return transformedText;
}

// Add execute method
static async executeYourFunction() {
    return await this.executeTransformation('yourTransform');
}
```

#### For URL Launchers:
**File**: `js/text-transformers.js`

```javascript
static async executeYourFunction() {
    console.log('🔄 Your Function: Starting execution');
    
    try {
        const settings = await this.getSettings();
        if (!settings || !settings.yourFunctionEnabled) {
            throw new Error('Your Function is disabled in settings');
        }

        const url = settings.yourFunctionUrl;
        if (!url) {
            throw new Error('No URL configured for Your Function');
        }

        console.log('🔄 Your Function: Launching URL:', url);
        
        // Send message to background script to create new tab
        chrome.runtime.sendMessage({
            action: 'createYourFunctionTab',
            url: url
        });

        return { success: true };
    } catch (error) {
        console.error('❌ Your Function: Error:', error);
        return { error: error.message };
    }
}
```

#### Background Script Handler:
**File**: `js/background.js`

```javascript
// Add to message listener
case 'createYourFunctionTab':
    try {
        const tab = await chrome.tabs.create({
            url: message.url,
            active: true
        });
        sendResponse({ success: true, tabId: tab.id });
    } catch (error) {
        console.error('Error creating Your Function tab:', error);
        sendResponse({ success: false, error: error.message });
    }
    break;
```

## Storage Architecture Details

### Chrome Storage Types and Usage

#### LOCAL Storage (chrome.storage.local)
- **Purpose**: Main settings object, large data, frequent changes
- **Key**: `gmbExtractorSettings` object
- **Contains**: Feature toggles, URLs, auto-paste settings
- **Access Pattern**: Settings UI auto-save via `autoSaveSettings()`

#### SYNC Storage (chrome.storage.sync)
- **Purpose**: Shortcuts only, cross-device sync
- **Keys**: Individual shortcut keys (e.g., `yourFunctionShortcut`)
- **Contains**: Keyboard shortcut strings only
- **Access Pattern**: Global shortcut manager loading, explicit saves

### Storage Method Mappings

#### Settings UI → Storage:
1. User changes setting → `data-setting` attribute → `this.settings[key]` → `autoSaveSettings()` → LOCAL storage
2. User enters shortcut → `data-setting` attribute → `saveShortcutFromInput()` → LOCAL + SYNC storage

#### Global Shortcut Manager → Storage:
1. Extension loads → `global-shortcut-manager.js` → `chrome.storage.sync.get()` → Load shortcuts
2. Shortcut pressed → Match shortcut → Execute function

## Tool Name Conventions

### Naming Patterns:
- **Setting Keys**: `yourFunctionEnabled`, `yourFunctionUrl`, `yourFunctionShortcut`
- **HTML IDs**: `yourFunctionToggle`, `yourFunctionHeader`, `yourFunctionContent`
- **CSS Classes**: Follow existing patterns (`custom-shortcut-input`, `copy-replace-accordion`)
- **JavaScript Methods**: `setupYourFunctionAccordion()`, `executeYourFunction()`

### File Naming:
- **HTML**: Use existing files (`extras-settings.html`)
- **JavaScript**: Add to existing classes (`text-transformers.js`, `global-shortcut-manager.js`)
- **No new files needed** for shortcut functions

## Critical Requirements Checklist

### Dual Storage Implementation ✅
- [ ] Shortcut saved to LOCAL storage via `autoSaveSettings()`
- [ ] Shortcut saved to SYNC storage via explicit `chrome.storage.sync.set()`
- [ ] Global shortcut manager loads from SYNC storage
- [ ] Export system includes shortcut from SYNC storage
- [ ] Import system saves shortcut to SYNC storage

### UI Components ✅
- [ ] Settings toggle with proper `data-setting` attribute
- [ ] Accordion header/content/icon elements with unique IDs
- [ ] Custom shortcut input with `.custom-shortcut-input` class
- [ ] Accordion JavaScript setup method created and called
- [ ] Purple branding colors (`#7C3AED`) used consistently

### Global Shortcut Manager Integration ✅
- [ ] Shortcut added to sync storage loading array
- [ ] Shortcut registration logic implemented
- [ ] Storage change listener added
- [ ] Shortcut detection logic implemented
- [ ] Execute method created
- [ ] Tool name mapping added
- [ ] Settings input mapping added

### Function Implementation ✅
- [ ] Function logic implemented in appropriate class
- [ ] Execute method created with proper error handling
- [ ] Background script message handler added (if needed)
- [ ] Proper logging and console output implemented

### Import/Export System ✅
- [ ] Shortcut included in export data structure
- [ ] Shortcut included in import functionality
- [ ] Proper statistics tracking for imports

## What NOT to Do - Critical Mistakes to Avoid

### ❌ Storage Mistakes
- **NEVER** save shortcuts only to LOCAL storage for global shortcuts
- **NEVER** use `null` or `undefined` as default values for shortcuts (use `''`)
- **NEVER** mix storage types for the same setting
- **NEVER** forget to add shortcuts to sync storage loading array

### ❌ UI Mistakes
- **NEVER** forget `data-setting` attributes on form elements
- **NEVER** use inconsistent ID naming patterns
- **NEVER** forget to call accordion setup methods
- **NEVER** use colors other than `#7C3AED` for purple branding

### ❌ Integration Mistakes
- **NEVER** implement partial global shortcut manager integration
- **NEVER** forget storage change listeners
- **NEVER** skip tool name mappings
- **NEVER** miss import/export system integration

### ❌ Function Implementation Mistakes
- **NEVER** forget error handling and try/catch blocks
- **NEVER** skip console logging for debugging
- **NEVER** implement functions without settings enabled checks
- **NEVER** forget to handle async operations properly

## Testing and Verification Procedures

### Manual Testing Checklist
1. **Settings UI**: Toggle appears, accordion opens/closes, shortcut input captures keys
2. **Storage**: Settings persist after page reload, export includes shortcut
3. **Shortcut Execution**: Shortcut works across different websites
4. **Error Handling**: Graceful failure when settings disabled or URL invalid
5. **Import/Export**: Settings backup/restore includes shortcuts

### Debugging Tools
- **Browser DevTools**: Check `chrome.storage.local` and `chrome.storage.sync`
- **Console Logs**: Follow execution path through logging statements
- **Extension Reload**: Test settings persistence across extension reloads
- **Cross-Tab Testing**: Verify shortcuts work in multiple browser tabs

### Common Issues and Solutions
- **Shortcut not working**: Check dual storage implementation
- **Settings not persisting**: Verify `data-setting` attributes
- **Accordion not opening**: Check JavaScript setup method
- **Export missing shortcut**: Add to sync storage loading array
- **Import failing**: Check sync storage save in import function

## New Tab Redirect Implementation Details

### What Was Discovered and Fixed:

#### Problem Analysis:
1. **Root Cause**: Storage mechanism mismatch between save and load operations
2. **Symptoms**: Shortcut captured and saved but never executed
3. **Diagnosis**: Console logs showed no debug output from global shortcut manager

#### Technical Fix Applied:

**File**: `settings/settings.js` - Modified `saveShortcutFromInput()` method
```javascript
// BEFORE (broken):
this.settings[storageKey] = shortcut;
this.autoSaveSettings(); // Only saved to LOCAL storage

// AFTER (working):
this.settings[storageKey] = shortcut;
this.autoSaveSettings(); // Save to LOCAL storage

// CRITICAL FIX: Also save to SYNC storage
if (storageKey === 'newTabRedirectShortcut') {
    chrome.storage.sync.set({ [storageKey]: shortcut });
}
```

**Integration Points Added:**
1. Sync storage loading array (line 3556)
2. Export data structure (line 3602) 
3. Import functionality (line 3880)

### Results:
- ✅ New Tab Redirect shortcuts now work identically to Text Time Machine Launcher
- ✅ Complete dual storage ensures global shortcut manager finds shortcuts
- ✅ Settings export/import includes New Tab Redirect shortcuts
- ✅ Cross-device sync works properly

This implementation serves as the proven pattern for all future shortcut functions.