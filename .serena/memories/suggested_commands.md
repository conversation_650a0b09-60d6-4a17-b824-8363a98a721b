# Suggested Development Commands

## Extension Development Commands

### Testing and Loading Extension
```bash
# Load unpacked extension for testing
# 1. Open Chrome → chrome://extensions/
# 2. Enable "Developer mode"
# 3. Click "Load unpacked" → select this directory
# 4. Extension icon appears in Chrome toolbar

# Extension reload during development
# Click reload button in chrome://extensions/ or use extension reload utility
```

### Testing Locations
```bash
# Google Maps testing
open "https://maps.google.com/"

# Google Search testing
open "https://google.com/search"

# Google Local Services testing
open "https://google.com/localservices/"

# Gmail testing
open "https://mail.google.com/"
```

## System Commands (macOS)

### File Operations
```bash
# List files
ls -la

# Find files
find . -name "*.js" -type f

# Search in files (use ripgrep if available)
rg "pattern" --type js

# File system navigation
cd /path/to/directory
```

### Audio Notification (Required)
```bash
# Sound notification after completing tasks
afplay /System/Library/Sounds/Submarine.aiff
```

## Version Management Commands
```bash
# Update version in manifest.json (manual edit)
# Update docs/CHANGELOG.md with changes

# Git operations
git status
git add .
git commit -m "Description of changes"
git push origin main
```

## No Build Process Required
- No npm, yarn, or other package managers
- No compilation or bundling steps
- Direct file editing with immediate testing
- Chrome extension reload for changes

## Development Workflow
1. Edit files directly in project directory
2. Reload extension in chrome://extensions/
3. Test functionality on target websites
4. Update version in manifest.json for releases
5. Document changes in CHANGELOG.md