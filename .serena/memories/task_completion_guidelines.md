# Task Completion Guidelines

## Required Steps After Code Changes

### 1. Sound Notification (MANDATORY)
```bash
# Always run this command after completing any task
afplay /System/Library/Sounds/Submarine.aiff
```

### 2. Extension Testing
- Reload extension in chrome://extensions/
- Test functionality on target websites:
  - Google Maps: https://maps.google.com/
  - Google Search: https://google.com/search
  - Google Local Services: https://google.com/localservices/
  - Gmail: https://mail.google.com/

### 3. Version Management (for releases)
- Update `manifest.json` version field (semantic versioning)
- Update `docs/CHANGELOG.md` with changes
- Document new features and bug fixes

### 4. No Linting/Formatting Tools
- No automated linting tools (<PERSON><PERSON>int, Prettier, etc.)
- Manual code review and formatting
- Follow established code conventions
- Test thoroughly across browsers

## Critical Protection Rules

### Data Extractor Protection (ABSOLUTE)
- **NEVER** modify data extractors or review analyzers
- Protected files include:
  - `multiple-listings.js`
  - `single-review-scraper.js`
  - `prolist-extraction.js`
  - `prolist-review-scraper.js`
  - `google-search-extractor.js`
  - `review-analysis.js`
  - `business-review-scraper.js`
- Only explicit bug fixes allowed - NO optimizations

### Memory Management Requirements
- Remove all event listeners when elements destroyed
- Clear intervals/timeouts when features disabled
- Proper cleanup prevents memory leaks
- Add all popups to DOM snapshot utility

### DOM Cleanup Requirements
Every function that manipulates DOM must implement:
```javascript
function resetMyFeature() {
  // Remove all created DOM elements
  // Clear global variables
  // Remove event listeners
  // Clear intervals/timeouts
}
```

## Quality Assurance Checklist

### Testing Requirements
- Multiple Google domains (.com, .co.uk, .ca, .com.au)
- Different page types (Maps, Search, Local Services, Gmail)
- Various browsers (Chrome, Edge, Brave)
- Feature interaction testing
- Settings import/export functionality

### Error Handling
- Try-catch blocks for all operations
- User-friendly error notifications
- Console logging for debugging (secret mode only)

### Documentation Updates
- Update relevant memory-bank files
- Check golden rules documentation
- Update CLAUDE.md if patterns change