# File:// URL Launcher via Keyboard Shortcuts - Complete Fix Guide

## Problem Summary
Text Time Machine Launcher was showing "URL not configured" error despite user entering a valid file:// URL in settings. The goal was to launch local HTML files via keyboard shortcuts from a Chrome extension.

## Root Causes Identified

### 1. Storage Structure Mismatch (Primary Issue)
**Problem:** Settings saved URL in nested structure, but launcher read from flat structure.
- **Settings saved to:** `chrome.storage.local.gmbExtractorSettings.textTimeMachineUrl`
- **Launcher read from:** `chrome.storage.local.textTimeMachineUrl` ❌ WRONG

**Symptom:** "URL not configured" error even when URL was properly entered in settings UI.

### 2. Missing Explicit File Permissions
**Problem:** While `<all_urls>` should cover file:// URLs, explicit permission is safer for file access.
- **Missing:** `"file://*/*"` in host_permissions array
- **Chrome Requirement:** Users must enable "Allow access to file URLs" in chrome://extensions/

## What We Tried (Unsuccessful Approaches)

### ❌ Attempt 1: Direct chrome.tabs.create() from Content Script
```javascript
// This doesn't work - content scripts have limited API access
chrome.tabs.create({ url: fileUrl });
```
**Why it failed:** Content scripts cannot use chrome.tabs.create() for file:// URLs due to security restrictions.

### ❌ Attempt 2: Background Script with Storage Bug
```javascript
// Background script was correct, but content script had wrong storage read
const settings = await this.safeStorageGet(['textTimeMachineUrl']);
const url = settings.textTimeMachineUrl; // Wrong - returns undefined
```
**Why it failed:** Storage mismatch prevented URL from being retrieved, causing "URL not configured" error.

### ❌ Attempt 3: URL Normalization Only
Added smart URL format handling but storage bug prevented the feature from working at all.

## ✅ Final Working Solution

### Fix 1: Correct Storage Structure
**File:** `js/text-transformers.js`
```javascript
// BEFORE (broken):
const settings = await this.safeStorageGet(['textTimeMachineUrl']);
const url = settings.textTimeMachineUrl;

// AFTER (working):
const settings = await this.safeStorageGet(['gmbExtractorSettings']);
const url = settings.gmbExtractorSettings?.textTimeMachineUrl;
```

### Fix 2: Add Explicit File Permission
**File:** `manifest.json`
```json
{
  "host_permissions": [
    "file://*/*",
    "<all_urls>"
  ]
}
```

### Fix 3: Background Script Communication Pattern
**Content Script → Background Script → chrome.tabs.create()**

**Content Script Pattern:**
```javascript
chrome.runtime.sendMessage({
    action: 'createTextTimeMachineLauncherTab',
    url: normalizedUrl
}, (response) => {
    if (response && response.success) {
        // Success
    } else {
        // Fallback to window.open()
    }
});
```

**Background Script Handler:**
```javascript
if (message.action === 'createTextTimeMachineLauncherTab') {
    chrome.tabs.create({ 
        url: message.url,
        active: true
    }, (tab) => {
        sendResponse({ 
            success: !chrome.runtime.lastError,
            tabId: tab?.id
        });
    });
    return true; // Async response
}
```

## Technical Architecture (Working)

### 1. Storage Pattern
- **Settings UI** saves to: `chrome.storage.local.gmbExtractorSettings.textTimeMachineUrl`
- **Launcher** reads from: `chrome.storage.local.gmbExtractorSettings.textTimeMachineUrl`
- **Match confirmed** ✅

### 2. Execution Flow
1. **User presses shortcut** → Global shortcut manager detects
2. **Global shortcut manager** → Calls `TextTransformers.executeTextTimeMachineLauncher()`
3. **Content script** → Reads URL from storage (correct structure)
4. **Content script** → Sends message to background script
5. **Background script** → Uses `chrome.tabs.create()` with full permissions
6. **File opens** in new tab successfully

### 3. URL Normalization (Bonus Feature)
Smart handling of different URL input formats:
```javascript
// Handles all these formats:
// ✅ file:///Users/<USER>/file.html (already correct)
// ✅ /Users/<USER>/file.html (Mac/Linux absolute)
// ✅ C:\path\file.html (Windows drive letter)
// ✅ path/file.html (relative path)
```

## Permissions Requirements

### Manifest Permissions
```json
{
  "permissions": ["tabs"],
  "host_permissions": ["file://*/*", "<all_urls>"]
}
```

### User Action Required
Users must manually enable:
1. Go to `chrome://extensions/`
2. Find extension → Click "Details"
3. Toggle ON **"Allow access to file URLs"**

**Note:** This cannot be automated - it's a Chrome security requirement since Chrome 118+.

## Key Learnings

### 1. Storage Consistency is Critical
Always verify that storage save and read operations use the same key structure. The nested `gmbExtractorSettings` pattern is used throughout this extension.

### 2. Background Script for File Access
Content scripts cannot reliably access file:// URLs. Use background script communication pattern for file operations.

### 3. Explicit Permissions Are Safer
Even when `<all_urls>` should cover file access, adding explicit `"file://*/*"` permission improves reliability.

### 4. User Permission Education
File:// URL access requires manual user activation in Chrome. Always provide clear instructions for users.

## Debugging Tips

### Check Storage Structure
```javascript
// Debug storage contents
chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
    console.log('Full settings:', result.gmbExtractorSettings);
    console.log('URL value:', result.gmbExtractorSettings?.textTimeMachineUrl);
});
```

### Verify File Permission
```javascript
// Check if extension has file URL access
chrome.extension.isAllowedFileSchemeAccess((isAllowed) => {
    console.log('File access allowed:', isAllowed);
});
```

### Test Background Communication
```javascript
// Test message passing
chrome.runtime.sendMessage({
    action: 'createTextTimeMachineLauncherTab',
    url: 'file:///test/path.html'
}, (response) => {
    console.log('Background response:', response);
});
```

## Similar Issues to Watch For
- Any feature showing "not configured" despite being configured = likely storage mismatch
- File:// URL features not working = check permissions and user settings
- Keyboard shortcuts not triggering = verify global shortcut manager integration
- Cross-platform file path issues = implement URL normalization

This pattern can be reused for any extension feature that needs to open local files via shortcuts or user actions.