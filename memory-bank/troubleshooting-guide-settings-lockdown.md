# Troubleshooting Guide: Settings Popup Lockdown System

## Quick Reference

**If timer values are resetting again**, check these in order:

1. **Check Console Logs**: Look for `🔒` lockdown activation messages
2. **Verify Window Detection**: Ensure settings URL matching is working
3. **Test Manual Activation**: Use message API to activate lockdown manually
4. **Check Backup/Restore**: Verify timer values are being backed up
5. **Test Timeout Safety**: Ensure auto-deactivation is working

## Common Issues and Solutions

### Issue 1: Lockdown Not Activating

**Symptoms**:
- Timer values still reset when opening settings
- No `🔒 STM: Settings popup window detected` message in console
- Settings notification doesn't appear

**Debugging Steps**:
1. Check if `chrome.windows.onCreated` listener is registered:
   ```javascript
   // Search for this in background.js console:
   console.log('Window created:', window.id);
   ```

2. Verify settings URL matching:
   ```javascript
   // Check if URL detection is working:
   const settingsUrl = chrome.runtime.getURL('settings/settings.html');
   console.log('Expected settings URL:', settingsUrl);
   ```

3. Test manual activation:
   ```javascript
   // From popup console:
   chrome.runtime.sendMessage({
     action: 'activateSettingsLockdown',
     windowId: 123
   }, console.log);
   ```

**Solutions**:
- **URL Mismatch**: Settings URL might have changed - update URL matching logic
- **Listener Missing**: Ensure background script fully loaded before settings open
- **Permission Issue**: Check if extension has proper window API permissions

### Issue 2: Lockdown Active But Values Still Reset

**Symptoms**:
- Console shows `🔒 STM: Settings lockdown active`
- Timer values still change to (1,1,15,8)
- Protection appears to be working but isn't effective

**Debugging Steps**:
1. Check if storage changes are being blocked:
   ```javascript
   // Look for this message in console:
   // "🔒 STM: Settings storage changes ignored during lockdown"
   ```

2. Verify timer validation is blocked:
   ```javascript
   // Look for this message:
   // "🔒 STM: Timer validation BLOCKED - Settings lockdown active"
   ```

3. Check read-only mode activation:
   ```javascript
   // Look for this message:
   // "🔒 POMODORO LOAD: Loading settings read-only mode"
   ```

**Solutions**:
- **Missing Protection**: Add lockdown checks to additional validation points
- **Race Condition**: Ensure lockdown activates before any settings operations
- **Bypass Path**: Check if there's an unprotected code path triggering resets

### Issue 3: Lockdown Won't Deactivate

**Symptoms**:
- Lockdown remains active after closing settings
- `settingsPopupActive` stays true
- Timer protection persists indefinitely

**Debugging Steps**:
1. Check window removal detection:
   ```javascript
   // Look for this message:
   // "🔓 STM: Settings popup window closed, deactivating lockdown"
   ```

2. Verify timeout safety mechanism:
   ```javascript
   // After 30 seconds, should see:
   // "⏰ STM: Settings lockdown auto-deactivated after timeout"
   ```

3. Check lockdown state:
   ```javascript
   // From any console:
   chrome.runtime.sendMessage({
     action: 'checkSettingsLockdown'
   }, console.log);
   ```

**Solutions**:
- **Window ID Mismatch**: Settings window ID changed - check tracking logic
- **Timeout Failure**: Safety timeout not working - check timeout management
- **Manual Deactivation**: Force deactivate via message API

### Issue 4: Backup/Restore Not Working

**Symptoms**:
- Timer values don't restore after settings close
- Backup shows null or undefined values
- Values restore to wrong numbers

**Debugging Steps**:
1. Check backup creation:
   ```javascript
   // Look for this message with actual values:
   // "💾 STM: Timer values backed up: {pomodoroWorkDuration: 25, ...}"
   ```

2. Check restore operation:
   ```javascript
   // Look for this message:
   // "🔄 STM: Timer values restored from backup: {...}"
   ```

3. Check storage state:
   ```javascript
   // From console:
   chrome.storage.local.get(['gmbExtractorSettings'], console.log);
   ```

**Solutions**:
- **Empty Backup**: Values were already corrupted before backup - check timing
- **Storage Permission**: Ensure extension has proper storage permissions
- **Async Issue**: Add await/timeout to ensure backup completes before lockdown

### Issue 5: Read-Only Mode Not Working

**Symptoms**:
- Settings still trigger storage writes during lockdown
- Console shows storage corrections being made
- `chrome.storage.local.set()` calls still happening

**Debugging Steps**:
1. Check read-only mode activation:
   ```javascript
   // Look for this exact message:
   // "🔒 POMODORO LOAD: Settings lockdown active - SKIPPING all storage corrections"
   ```

2. Check for storage operations:
   ```javascript
   // Search console for any of these during lockdown:
   // "chrome.storage.local.set"
   // "corrections saved to storage"
   // "Additional corrections made"
   ```

3. Verify lockdown check timing:
   ```javascript
   // Ensure lockdown check happens before loadAllSettings logic
   ```

**Solutions**:
- **Check Timing**: Ensure lockdown check happens early in loadAllSettings
- **Missing Return**: Ensure early return when read-only mode activated
- **Other Code Paths**: Check if other functions are calling storage.set

## Manual Testing Procedures

### Test 1: Basic Lockdown Functionality
1. Set timer values to (25,5,15,8)
2. Open settings popup
3. Verify lockdown notification appears
4. Toggle any setting
5. Close settings popup
6. Verify timer values remain (25,5,15,8)

### Test 2: Manual Activation
1. From popup console, run:
   ```javascript
   chrome.runtime.sendMessage({
     action: 'activateSettingsLockdown',
     windowId: 999
   }, console.log);
   ```
2. Verify response: `{success: true, lockdownActive: true}`
3. Try to change timer values
4. Verify changes are blocked

### Test 3: Status Checking
1. With lockdown active, run:
   ```javascript
   chrome.runtime.sendMessage({
     action: 'checkSettingsLockdown'
   }, console.log);
   ```
2. Verify response: `{success: true, lockdownActive: true, windowId: ...}`

### Test 4: Timeout Safety
1. Activate lockdown manually
2. Wait 35 seconds
3. Verify auto-deactivation message appears
4. Verify lockdown status is false

### Test 5: Error Recovery
1. Activate lockdown
2. Kill settings popup window forcefully
3. Wait for timeout or manually deactivate
4. Verify system returns to normal operation

## Console Log Reference

### Normal Operation Logs
```
🔒 STM: Settings popup window detected, activating lockdown
💾 STM: Timer values backed up: {pomodoroWorkDuration: 25, ...}
🔒 STM: Settings popup lockdown ACTIVATED - Timer values completely protected
🔒 Settings: Lockdown is active - Timer values are protected
🔒 POMODORO LOAD: Settings lockdown active - SKIPPING all storage corrections
🔒 POMODORO LOAD: Loading settings read-only mode
🔒 POMODORO LOAD: Read-only mode completed - no storage writes performed
🔓 STM: Settings popup window closed, deactivating lockdown
🔄 STM: Timer values restored from backup: {...}
🔓 STM: Settings popup lockdown DEACTIVATED - Timer values unprotected
```

### Error Logs to Watch For
```
❌ STM: Error in window creation handler: ...
❌ STM: Error backing up timer values: ...
❌ STM: Error restoring timer values: ...
❌ STM: Error in window removal handler: ...
❌ Background: Error activating settings lockdown: ...
❌ Settings: Error checking lockdown status: ...
```

### Warning Logs
```
⚠️ STM: No timer backup available for restore
⏰ STM: Settings lockdown auto-deactivated after timeout
```

## API Reference

### Message Actions

#### `activateSettingsLockdown`
**Purpose**: Manually activate lockdown
**Parameters**: 
- `windowId` (optional): Window ID to track
**Response**: `{success: boolean, lockdownActive: boolean, error?: string}`

#### `deactivateSettingsLockdown`
**Purpose**: Manually deactivate lockdown
**Parameters**: None
**Response**: `{success: boolean, lockdownActive: boolean, error?: string}`

#### `checkSettingsLockdown`
**Purpose**: Check current lockdown status
**Parameters**: None
**Response**: `{success: boolean, lockdownActive: boolean, windowId: number|null}`

### State Variables

#### Background Script Global Variables
- `settingsPopupActive`: Boolean flag for lockdown status
- `settingsPopupWindowId`: Current settings window ID
- `timerValueBackup`: Backup object with timer values
- `lockdownTimeout`: Timeout handle for auto-deactivation

## Recovery Procedures

### Emergency Lockdown Deactivation
If lockdown gets stuck, force deactivate:
```javascript
// From any extension context:
chrome.runtime.sendMessage({
  action: 'deactivateSettingsLockdown'
}, console.log);
```

### Manual Timer Value Restore
If backup/restore fails, manually set correct values:
```javascript
chrome.storage.local.get(['gmbExtractorSettings'], (result) => {
  const settings = result.gmbExtractorSettings || {};
  settings.pomodoroWorkDuration = 25;
  settings.pomodoroShortBreak = 5;
  settings.pomodoroLongBreak = 15;
  settings.pomodoroNumberOfCycles = 8;
  chrome.storage.local.set({gmbExtractorSettings: settings});
});
```

### Complete System Reset
If everything fails, reset the lockdown system:
```javascript
// From background script console:
settingsPopupActive = false;
settingsPopupWindowId = null;
timerValueBackup = null;
if (lockdownTimeout) {
  clearTimeout(lockdownTimeout);
  lockdownTimeout = null;
}
```

## Prevention Tips

### Code Modifications
- **Always test lockdown status** before making timer-related changes
- **Use read-only mode** when loading settings during lockdown
- **Add protection checks** to any new timer validation functions
- **Preserve lockdown messages** for debugging

### Testing Best Practices
- **Test with different timer values** to ensure backup/restore works
- **Test timeout scenarios** to verify safety mechanisms
- **Test error conditions** to ensure graceful degradation
- **Test multiple settings changes** to verify protection scope

### Monitoring
- **Watch console logs** for lockdown activation/deactivation
- **Monitor timer value persistence** across settings interactions
- **Check backup/restore operations** for correct values
- **Verify read-only mode activation** during settings loading

## Related Documentation

- **Main Implementation**: `memory-bank/settings-popup-lockdown-system-implementation.md`
- **Original Issue Plan**: `memory-bank/pomodoro-settings-reset-issue-plan.md`
- **System Patterns**: `memory-bank/systemPatterns.md`
- **Popup Design Standards**: `memory-bank/popup-design-standards.md`

---

**Last Updated**: 2025-07-14
**System Status**: ✅ Working Perfectly
**Known Issues**: None