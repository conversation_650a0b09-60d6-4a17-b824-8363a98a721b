# Complete List of Files Still Using Sync Storage

## Summary
**Total Files**: 32 files still reference `chrome.storage.sync`
**Categories**: Core files, Location features, Gmail enhancements, Search features, Quick Actions, Utilities

## Detailed Breakdown by Category

### 1. Core Extension Files (Already Handled)
- ✅ **js/background.js** - Main extension background script (ALREADY UPDATED in Phase 2)
- ✅ **js/popup.js** - Extension popup interface (ALREADY UPDATED in Phases 2 & 4)

### 2. Location Changer Features
These handle the location spoofing functionality:

- **js/location-changer-visibility.js**
  - Purpose: Controls visibility of location changer UI
  - Storage: Reads settings to check if enabled
  - Data: No data storage, read-only

- **js/location-changer-integration.js**
  - Purpose: Integrates location changer with Google services
  - Storage: Reads location settings
  - Data: No data storage, read-only

- **js/current-location-display.js**
  - Purpose: Shows current spoofed location in UI
  - Storage: Reads current location settings
  - Data: No data storage, read-only

### 3. Google Search Enhancement Features

- **js/find-citations-injector.js**
  - Purpose: Adds citation finder button to search results
  - Storage: Reads if feature is enabled
  - Data: No data storage, read-only

- **js/open-single-listing.js**
  - Purpose: Auto-opens single business listings
  - Storage: Reads if feature is enabled
  - Data: No data storage, read-only

- **js/prolist-extraction.js**
  - Purpose: Extracts data from Google Local Services
  - Storage: Reads extraction settings
  - Data: No data storage, read-only

- **settings/search-result-stats.js**
  - Purpose: Shows statistics about search results
  - Storage: Reads if feature is enabled
  - Data: No data storage, read-only

- **settings/serp-numbering.js**
  - Purpose: Numbers search results (1, 2, 3...)
  - Storage: Reads if feature is enabled
  - Data: No data storage, read-only

- **settings/sponsored-highlighter.js**
  - Purpose: Highlights sponsored/ad results
  - Storage: Reads highlight color and enabled state
  - Data: No data storage, read-only

- **settings/display-maps-search-fields.js**
  - Purpose: Shows additional search fields on Maps
  - Storage: Reads if feature is enabled
  - Data: No data storage, read-only

### 4. Gmail Enhancement Features

- **settings/gmail-time-formatter.js**
  - Purpose: Custom time format in Gmail
  - Storage: Reads time format string
  - Data: No data storage, read-only

- **settings/gmail-sender-icons.js**
  - Purpose: Shows sender icons in Gmail
  - Storage: Reads if feature is enabled
  - Data: No data storage, read-only

- **settings/gmail-thread-expander.js**
  - Purpose: Auto-expands Gmail threads
  - Storage: Reads if feature is enabled
  - Data: No data storage, read-only

- **settings/gmail-enhanced-timestamps.js**
  - Purpose: Enhanced timestamp display
  - Storage: Reads if feature is enabled
  - Data: No data storage, read-only

- **settings/reverse-gmail-order.js**
  - Purpose: Reverses Gmail message order
  - Storage: Reads if feature is enabled
  - Data: No data storage, read-only

### 5. Utility Features

- **settings/clicktocopy.js**
  - Purpose: Click to copy link URLs
  - Storage: Reads if feature is enabled
  - Data: No data storage, read-only

- **settings/drag-select-links.js**
  - Purpose: Drag to select multiple links
  - Storage: Reads settings (hotkey, color, filters)
  - Data: No data storage, read-only

- **js/utilities/top-urls-copier.js**
  - Purpose: Copies top search result URLs
  - Storage: Reads extraction settings
  - Data: No data storage, read-only

### 6. Keyboard Shortcuts & Global Features

- **settings/colorpicker-shortcut-handler.js**
  - Purpose: Color picker keyboard shortcut
  - Storage: Reads shortcut key binding
  - Data: WRITES shortcut settings ⚠️

- **settings/custom-shortcut-handler.js**
  - Purpose: Custom keyboard shortcuts
  - Storage: Reads/writes custom shortcuts
  - Data: WRITES shortcut data ⚠️

- **settings/global-shortcut-manager.js**
  - Purpose: Manages all global shortcuts
  - Storage: Reads/writes shortcut settings
  - Data: WRITES shortcut configuration ⚠️

### 7. Profile & Settings Management

- **js/profiles-frontend.js**
  - Purpose: Profile UI in content scripts
  - Storage: Reads available profiles
  - Data: No data storage, read-only

- **js/profiles-shortcuts.js**
  - Purpose: Keyboard shortcuts for profiles
  - Storage: Reads profile shortcuts
  - Data: No data storage, read-only

### 8. Quick Actions Components

- **settings/quick-actions/html-cleaner.js**
  - Purpose: Cleans HTML from copied content
  - Storage: Reads/writes cleaner settings
  - Data: WRITES HTML cleaner rules ⚠️

- **settings/quick-actions/copyelement.js**
  - Purpose: Copy element feature
  - Storage: Reads if enabled
  - Data: No data storage, read-only

- **settings/quick-actions/cleanselectedcontent.js**
  - Purpose: Cleans selected content
  - Storage: Reads cleaner settings
  - Data: No data storage, read-only

### 9. System Utilities

- **settings/logging-utility.js**
  - Purpose: Debug logging system
  - Storage: Reads debug mode setting
  - Data: No data storage, read-only

- **settings/notification-utility.js**
  - Purpose: Shows notifications
  - Storage: Reads notification settings
  - Data: No data storage, read-only

- **js/universal-click-debugger.js**
  - Purpose: Debug click events (developer mode)
  - Storage: Reads debug settings
  - Data: No data storage, read-only

### 10. Pomodoro System

- ✅ **js/pomodoro/todo-manager.js**
  - Purpose: Manages Pomodoro todos
  - Storage: ALREADY uses local storage for todos
  - Note: Only reads settings from sync, todos in local

## Priority Classification

### HIGH PRIORITY (Store Data) ⚠️
These files WRITE data that could grow large:
1. **settings/colorpicker-shortcut-handler.js** - Stores shortcut configurations
2. **settings/custom-shortcut-handler.js** - Stores custom shortcuts
3. **settings/global-shortcut-manager.js** - Stores global shortcut settings
4. **settings/quick-actions/html-cleaner.js** - Stores HTML cleaning rules

### LOW PRIORITY (Read Only) ✅
All other files (28 files) only READ settings and don't store data.

## Recommendation

1. **Immediate Action**: Update the 4 HIGH PRIORITY files that write data
2. **Future Cleanup**: The 28 read-only files can be updated gradually or left as-is
3. **No Risk**: Read-only files pose no risk of quota errors or data loss