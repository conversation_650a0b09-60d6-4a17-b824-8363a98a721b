# Screenshot System Architecture - SEO Time Machines

## Overview
SEO Time Machines screenshot system implements a Manifest V3 compatible approach inspired by Handy Screenshot. The system captures area selections and processes them through proper DOM environments while avoiding service worker limitations.

## Critical Architecture Decision

### Why This Approach Works
The screenshot system works because it **separates concerns between service worker and DOM environments**:

1. **Service Worker (background.js)**: Handles screenshot capture API calls and storage operations
2. **DOM Environment (screenshot-editor.js)**: Handles image processing, canvas operations, and cropping

This separation is **mandatory** in Manifest V3 because service workers cannot access DOM APIs like `Image`, `document.createElement('canvas')`, or canvas contexts.

### Previous Broken Approach
The original implementation tried to crop images directly in the background script:
```javascript
// ❌ BROKEN - These APIs don't exist in service workers
const img = new Image(); 
const canvas = document.createElement('canvas');
const ctx = canvas.getContext('2d');
```

This caused `Image is not defined` errors because service workers have no DOM access.

## Current Working Architecture

### 1. Content Script Selection (screenshot-selector.js)
**Location**: `js/screenshot-selector.js`
**Environment**: Injected into web pages
**Purpose**: Creates selection UI and captures coordinates

**Key Functions**:
- `startScreenshotSelection()`: Creates overlay with selection rectangle
- `sendSelectionToBackground()`: Sends coordinates + devicePixelRatio to background

**Message Format**:
```javascript
chrome.runtime.sendMessage({
    action: "CAPTURE_SELECTION",
    rect: { left, top, width, height },  // Logical pixels
    devicePixelRatio: window.devicePixelRatio
});
```

### 2. Background Script Processing (background.js)
**Location**: `js/background.js`
**Environment**: Service Worker
**Purpose**: Screenshot capture and data storage

**Key Functions**:
- `handleCaptureSelection()`: Main handler for CAPTURE_SELECTION messages
- `captureFullScreenshot()`: Uses chrome.tabs.captureVisibleTab API

**Critical Process**:
```javascript
// ✅ WORKING - Service worker stores data, doesn't process images
async function handleCaptureSelection(message) {
    // 1. Capture full screenshot
    const fullScreenshotData = await captureFullScreenshot();
    
    // 2. Store everything for editor to process
    await chrome.storage.local.set({ 
        screenshotData: fullScreenshotData,      // Full image data
        selectionRect: message.rect,             // Selection coordinates  
        devicePixelRatio: message.devicePixelRatio,
        screenshotTimestamp: Date.now()
    });
    
    // 3. Open editor
    const editorTab = await chrome.tabs.create({
        url: chrome.runtime.getURL('screenshot-editor.html')
    });
}
```

### 3. Screenshot Editor Processing (screenshot-editor.js)
**Location**: `js/screenshot-editor.js` + `screenshot-editor.html`
**Environment**: Regular webpage context (has DOM access)
**Purpose**: Image cropping, editing, and export

**Key Functions**:
- `loadScreenshot()`: Loads from storage and crops if needed
- `cropImage()`: Performs actual image cropping with devicePixelRatio handling
- `displayScreenshot()`: Renders final image in canvas

**Critical Cropping Logic** (Handy Screenshot approach):
```javascript
async cropImage(fullImageDataUrl, selectionRect, devicePixelRatio) {
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    
    // Canvas size = logical selection size (not scaled)
    tempCanvas.width = selectionRect.width;
    tempCanvas.height = selectionRect.height;
    
    // Source coordinates scaled by devicePixelRatio
    const sourceX = selectionRect.left * devicePixelRatio;
    const sourceY = selectionRect.top * devicePixelRatio;
    const sourceWidth = selectionRect.width * devicePixelRatio;
    const sourceHeight = selectionRect.height * devicePixelRatio;
    
    // Draw cropped portion
    tempCtx.drawImage(
        img,
        sourceX, sourceY, sourceWidth, sourceHeight,    // Source (device pixels)
        0, 0, selectionRect.width, selectionRect.height // Dest (logical pixels)
    );
}
```

## DevicePixelRatio Handling

### Why DevicePixelRatio Matters
Modern displays (Retina, high-DPI) have devicePixelRatio > 1:
- **Logical pixels**: What CSS/JavaScript sees (e.g., 100px wide)
- **Device pixels**: Actual screen pixels (e.g., 200px wide on 2x display)

### Screenshot Capture Reality
`chrome.tabs.captureVisibleTab()` captures at **device pixel resolution**, not logical resolution.

### Handy Screenshot Approach (Now Implemented)
1. **Selection coordinates**: Captured in logical pixels
2. **Screenshot data**: Full image at device pixel resolution  
3. **Cropping calculation**: Logical coordinates × devicePixelRatio = device pixel coordinates
4. **Canvas output**: Logical pixel dimensions

This ensures perfect 1:1 scaling regardless of display DPI.

## Message Flow

```
User selects area → Content Script → Background Script → Editor
                    ↓               ↓                   ↓
             Coordinates +    Full screenshot +    Crop image +
             devicePixelRatio     Storage           Display
```

## Storage Pattern

### Data Stored in chrome.storage.local:
```javascript
{
    screenshotData: "data:image/png;base64,iVBOR...",  // Full screenshot
    selectionRect: { left: 100, top: 50, width: 300, height: 200 },
    devicePixelRatio: 2,
    screenshotTimestamp: 1699123456789
}
```

### Data Lifecycle:
1. **Store**: Background script saves after capture
2. **Load**: Editor loads on initialization  
3. **Process**: Editor crops if selection coordinates exist
4. **Clean**: Editor clears storage after loading

## Integration Points

### Affected Systems:
1. **Global Shortcut Manager** (`settings/global-shortcut-manager.js`)
   - `executeScreenshot()` function calls `startScreenshotSelection()`
   - No changes required - maintains same API

2. **Content Script Coordinator** (`js/content.js`)
   - Loads `screenshot-selector.js` on all URLs
   - No changes required - existing injection works

3. **Manifest Permissions**
   - Uses existing `activeTab` and `tabs` permissions
   - Uses existing `storage` permission
   - No new permissions required

### Unaffected Systems:
- **Data Extractors**: No impact on core extraction functionality
- **Quick Actions**: Screenshot system is isolated from Quick Actions
- **Settings System**: Uses same shortcut configuration
- **Pomodoro/Audio**: Completely separate from screenshot functionality
- **Alert System**: No interaction with screenshot system

## Error Handling

### Common Failure Points:
1. **Permissions**: `activeTab` required for screenshot capture
2. **Timing**: User closes tab before capture completes
3. **Storage**: Quota limits for large screenshots
4. **Canvas**: Memory limits for very large selections

### Recovery Mechanisms:
- **Background Script**: Returns error objects with details
- **Content Script**: Shows user-friendly error messages
- **Editor**: Fallback to clipboard if storage fails
- **Storage**: Auto-cleanup with timestamps

## Performance Considerations

### Memory Usage:
- **Full Screenshot**: Stored temporarily in chrome.storage.local
- **Cropped Image**: Created in editor, replaces full screenshot
- **Cleanup**: Storage cleared after editor loads

### Optimization Features:
- **Auto-cleanup**: Removes storage data after loading
- **Display Scaling**: Editor scales large images for display (max 1200px wide)
- **Canvas Optimization**: Uses appropriate canvas dimensions

## Future Maintenance

### Critical Rules:
1. **Never move image processing back to service worker** - DOM APIs will never be available
2. **Maintain devicePixelRatio handling** - Essential for high-DPI displays  
3. **Keep storage cleanup** - Prevents quota issues
4. **Preserve Handy Screenshot scaling logic** - Proven approach

### Extension Points:
- **Editor Features**: Add drawing tools, filters, annotations
- **Export Formats**: Add JPEG, PDF, etc.
- **Cloud Integration**: Add upload capabilities
- **Batch Processing**: Support multiple selections

## Debugging

### Log Locations:
- **Selection**: `screenshot-selector.js` console logs
- **Capture**: `background.js` console logs with step numbers
- **Processing**: `screenshot-editor.js` console logs
- **Errors**: Check service worker console + editor console

### Common Debug Steps:
1. Check service worker registration: `chrome://extensions/`
2. Monitor background script: Service Worker → Console
3. Check storage data: DevTools → Application → Storage
4. Verify permissions: Extension details → Permissions

## Comparison to Handy Screenshot

### Similarities:
- **DevicePixelRatio scaling**: Identical approach
- **Background capture**: Both use chrome.tabs.captureVisibleTab
- **Canvas processing**: Both use canvas for cropping
- **Storage pattern**: Both store and process separately

### Differences:
- **Architecture**: STM uses separate editor page, Handy Screenshot uses iframe
- **Integration**: STM integrates with existing Quick Actions system
- **UI**: STM has custom editor interface vs Handy Screenshot's integrated UI

### Why This Works Better for SEO Time Machines:
- **Manifest V3 Compliance**: Proper service worker patterns
- **Extensibility**: Separate editor allows more features
- **Integration**: Works with existing SEO Time Machines architecture
- **Maintenance**: Clear separation of concerns