# YouTube Extensions Implementation Guide

## Overview

This comprehensive guide covers everything needed to implement YouTube-specific features in SEO Time Machines Chrome extension. It details the specific challenges, solutions, and patterns required for YouTube integration.

## Architecture Overview

### Core Challenge: MAIN vs ISOLATED Worlds

YouTube extensions face a unique challenge due to Chrome's content script execution contexts:

- **ISOLATED World**: Has access to Chrome extension APIs but limited page access
- **MAIN World**: Has full page access but NO Chrome extension APIs

YouTube requires MAIN world execution for:
- DOM manipulation of YouTube's dynamic interface
- Access to YouTube's video player APIs
- Bypassing YouTube's strict security policies

### Solution: Bridge Pattern

We use a **settings bridge** pattern to overcome the Chrome API limitation:

```
Settings System (Chrome Storage) 
    ↓ (Bridge Script - ISOLATED world)
localStorage 
    ↓ (YouTube Scripts - MAIN world)  
Feature Implementation
```

## File Structure and Responsibilities

### Required Files for Each YouTube Feature

1. **Main Feature File** (`youtube-{feature}.js`)
   - Runs in MAIN world
   - Implements feature functionality
   - Uses localStorage for settings
   - NO Chrome API access

2. **Settings Bridge** (`youtube-settings-bridge.js`)
   - Runs in ISOLATED world
   - Syncs Chrome storage to localStorage
   - Handles settings updates
   - Shared across all YouTube features

3. **Manifest Entries**
   - Bridge script runs first (ISOLATED)
   - Feature scripts run after (MAIN)
   - Proper execution order critical

## Step-by-Step Implementation Guide

### 1. Create the Feature File

```javascript
// youtube-{feature}.js
(function() {
    'use strict';
    
    // NO Chrome API checks - they don't exist in MAIN world
    let isEnabled = true;
    let debugMode = false;
    
    // Load settings from localStorage (NOT Chrome storage)
    function loadSettings() {
        try {
            const storedEnabled = localStorage.getItem('youtube{Feature}Enabled');
            const storedDebug = localStorage.getItem('debugMode');
            
            isEnabled = storedEnabled !== 'false'; // Default to true
            debugMode = storedDebug === 'true';
            
            if (debugMode) {
                console.log('[YouTube {Feature}] Settings loaded:', { enabled: isEnabled });
            }
            
            return isEnabled;
        } catch (error) {
            console.error('[YouTube {Feature}] Error loading settings:', error);
            return true; // Default to enabled on error
        }
    }
    
    // Settings change listener via localStorage events
    window.addEventListener('storage', (e) => {
        if (e.key === 'youtube{Feature}Enabled' || e.key === 'debugMode') {
            const wasEnabled = isEnabled;
            loadSettings();
            
            if (debugMode) {
                console.log('[YouTube {Feature}] Settings updated:', { enabled: isEnabled });
            }
            
            // Handle enable/disable changes
            if (wasEnabled !== isEnabled) {
                if (isEnabled) {
                    initialize{Feature}();
                } else {
                    disable{Feature}();
                }
            }
        }
    });
    
    // Synchronous initialization
    function initialize() {
        if (!isYouTubePage()) {
            console.log('[YouTube {Feature}] Not a YouTube watch page, exiting');
            return;
        }
        
        const shouldRun = loadSettings();
        
        if (shouldRun) {
            console.log('[YouTube {Feature}] Starting feature');
            initialize{Feature}();
        } else {
            console.log('[YouTube {Feature}] Feature disabled in settings');
        }
    }
    
    // Start when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // Handle YouTube SPA navigation
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            if (debugMode) {
                console.log('[YouTube {Feature}] Page navigation detected');
            }
            
            if (isEnabled) {
                setTimeout(() => {
                    if (isYouTubePage()) {
                        initialize{Feature}();
                    } else {
                        disable{Feature}();
                    }
                }, 500);
            }
        }
    }).observe(document, { subtree: true, childList: true });
})();
```

### 2. Update Settings Bridge

Add your feature's settings to `youtube-settings-bridge.js`:

```javascript
// In syncSettingsToLocalStorage function
localStorage.setItem('youtube{Feature}Enabled', settings.youtube{Feature}Enabled !== false ? 'true' : 'false');

// In message listener
localStorage.setItem('youtube{Feature}Enabled', settings.youtube{Feature}Enabled !== false ? 'true' : 'false');
```

### 3. Update Manifest.json

Ensure proper script execution order:

```json
{
  "matches": ["https://www.youtube.com/*"],
  "js": ["settings/youtube-settings-bridge.js"],
  "run_at": "document_start"
},
{
  "matches": ["https://www.youtube.com/*"],
  "js": ["settings/youtube-ads-skipper.js", "settings/youtube-screenshot.js", "settings/youtube-thumbnail-viewer.js", "settings/youtube-{feature}.js"],
  "run_at": "document_start",
  "world": "MAIN"
}
```

### 4. Add Settings UI

In `settings/extras-settings.html`, add to YouTube Tools accordion:

```html
<div class="toggle-group">
    <label>
        <input type="checkbox" data-setting="youtube{Feature}Enabled" class="toggle-switch">
        <span class="toggle-label">{Feature Name}</span>
        <span class="toggle-description">{Feature description}</span>
    </label>
</div>
```

Update `settings/settings.js` to include in Select All mapping:

```javascript
youtubeTools: {
    selectAllId: 'youtubeToolsSelectAll',
    settingSelectors: [
        '[data-setting="youtubeAdsSkipperEnabled"]',
        '[data-setting="videoSpeedControllerEnabled"]',
        '[data-setting="screenshotYouTubeEnabled"]',
        '[data-setting="youtubeThumbnailViewerEnabled"]',
        '[data-setting="youtube{Feature}Enabled"]' // Add this line
    ]
}
```

## Critical Implementation Rules

### 1. NO Chrome APIs in YouTube Scripts

❌ **NEVER do this:**
```javascript
// This will FAIL in MAIN world
if (typeof chrome === 'undefined' || !chrome.runtime) {
    return;
}

chrome.storage.sync.get(['gmbExtractorSettings'], (result) => {
    // This will throw "Cannot read properties of undefined"
});

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // This will throw "Cannot read properties of undefined"
});
```

✅ **ALWAYS do this:**
```javascript
// Use localStorage instead
const storedEnabled = localStorage.getItem('youtube{Feature}Enabled');

// Use storage events for settings changes
window.addEventListener('storage', (e) => {
    if (e.key === 'youtube{Feature}Enabled') {
        // Handle setting change
    }
});
```

### 2. Content Security Policy Compliance

❌ **NEVER use innerHTML with HTML:**
```javascript
button.innerHTML = "<span>Click me</span>"; // CSP violation
```

✅ **ALWAYS use textContent for text:**
```javascript
button.textContent = "Click me"; // CSP compliant
```

### 3. Proper YouTube Page Detection

```javascript
function isYouTubePage() {
    return window.location.hostname.includes('youtube.com') && 
           window.location.pathname.includes('/watch');
}
```

### 4. Handle YouTube's SPA Navigation

YouTube is a Single Page Application, so URL changes don't trigger page reloads:

```javascript
let lastUrl = location.href;
new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
        lastUrl = url;
        // Re-initialize or cleanup based on new page
        if (isYouTubePage()) {
            initializeFeature();
        } else {
            disableFeature();
        }
    }
}).observe(document, { subtree: true, childList: true });
```

### 5. Synchronous Initialization Only

❌ **NEVER use async/await:**
```javascript
async function initialize() {
    const shouldRun = await loadSettings(); // Will fail
}
```

✅ **ALWAYS use synchronous functions:**
```javascript
function initialize() {
    const shouldRun = loadSettings(); // Works
}
```

## Common Errors and Solutions

### Error: "Cannot read properties of undefined (reading 'addListener')"

**Cause:** Trying to use Chrome APIs in MAIN world
**Solution:** Remove all Chrome API calls, use localStorage and storage events

### Error: "This document requires 'TrustedHTML' assignment"

**Cause:** Using innerHTML with HTML content
**Solution:** Use textContent for text content

### Error: Feature not working after page navigation

**Cause:** YouTube SPA navigation not handled
**Solution:** Add mutation observer for URL changes

### Error: Settings not updating in real-time

**Cause:** Not listening for localStorage changes
**Solution:** Add storage event listener

### Error: Observer setup failing

**Cause:** DOM elements not ready when script runs
**Solution:** Add proper initialization timing and retry logic

## Memory Management Best Practices

### 1. Cleanup on Disable

```javascript
function disableFeature() {
    // Remove all created DOM elements
    if (featureButton && featureButton.parentNode) {
        featureButton.parentNode.removeChild(featureButton);
    }
    featureButton = null;
    
    // Disconnect observers
    if (featureObserver) {
        featureObserver.disconnect();
        featureObserver = null;
    }
    
    // Remove event listeners
    document.removeEventListener('keydown', handleKeydown);
    
    // Clear any intervals/timeouts
    if (retryInterval) {
        clearInterval(retryInterval);
        retryInterval = null;
    }
}
```

### 2. Prevent Memory Leaks

```javascript
// Always check if elements exist before creating
function createButton() {
    if (featureButton) return; // Already created
    
    featureButton = document.createElement("button");
    // ... setup button
}

// Use weak references where possible
const buttonCache = new WeakMap();
```

### 3. Efficient Observer Patterns

```javascript
// Use throttling for performance
let lastCheck = 0;
const THROTTLE_MS = 100;

function throttledCheck() {
    const now = Date.now();
    if (now - lastCheck > THROTTLE_MS) {
        lastCheck = now;
        performActualCheck();
    }
}

// Limit observer scope
observer.observe(targetElement, {
    childList: true,
    subtree: false, // Reduce scope for better performance
    attributes: true,
    attributeFilter: ['class'] // Only watch specific attributes
});
```

## Settings Integration Patterns

### 1. Default Values

```javascript
// Always provide sensible defaults
isEnabled = settings.youtube{Feature}Enabled !== false; // Default to true
debugMode = settings.debugMode === true; // Default to false
retryCount = parseInt(settings.retryCount) || 3; // Default to 3
```

### 2. Setting Key Naming Convention

- `youtube{Feature}Enabled` - Main toggle for the feature
- `{feature}OptionName` - Feature-specific options
- `debugMode` - Global debug flag

### 3. Settings Validation

```javascript
function loadSettings() {
    try {
        const storedValue = localStorage.getItem('youtube{Feature}Enabled');
        
        // Validate and sanitize
        if (storedValue !== null && storedValue !== 'true' && storedValue !== 'false') {
            console.warn('Invalid setting value, using default');
            localStorage.setItem('youtube{Feature}Enabled', 'true');
            return true;
        }
        
        return storedValue !== 'false';
    } catch (error) {
        console.error('Settings error:', error);
        return true; // Fail safe
    }
}
```

## Testing Checklist

### 1. Basic Functionality
- [ ] Feature works on YouTube watch pages
- [ ] Feature disabled on non-YouTube pages
- [ ] Toggle in settings works immediately
- [ ] Debug logging works when enabled

### 2. Navigation Testing
- [ ] Works after navigating between YouTube videos
- [ ] Properly cleans up when leaving YouTube
- [ ] Re-initializes correctly on return to YouTube

### 3. Error Handling
- [ ] No Chrome API errors in console
- [ ] No CSP violations
- [ ] Graceful handling of DOM changes
- [ ] Proper fallbacks for missing elements

### 4. Memory Management
- [ ] No memory leaks during enable/disable cycles
- [ ] Observers properly disconnected
- [ ] Event listeners removed on cleanup
- [ ] Intervals/timeouts cleared

### 5. Settings Integration
- [ ] Settings persist between sessions
- [ ] Real-time updates when settings change
- [ ] Proper default values
- [ ] Individual toggles work independently

## Debug and Troubleshooting

### 1. Enable Debug Mode

Set `debugMode: true` in settings to see detailed logging:

```javascript
if (debugMode) {
    console.log('[YouTube {Feature}] Debug info:', {
        enabled: isEnabled,
        pageType: window.location.pathname,
        elementFound: !!targetElement
    });
}
```

### 2. Common Debug Checks

```javascript
// Check if running in correct world
console.log('Chrome APIs available:', typeof chrome !== 'undefined' && !!chrome.runtime);

// Check localStorage sync
console.log('Settings in localStorage:', {
    enabled: localStorage.getItem('youtube{Feature}Enabled'),
    debug: localStorage.getItem('debugMode')
});

// Check YouTube page detection
console.log('Is YouTube page:', isYouTubePage());

// Check DOM readiness
console.log('Document ready state:', document.readyState);
```

### 3. Performance Monitoring

```javascript
// Monitor observer performance
let observerCallCount = 0;
const observer = new MutationObserver((mutations) => {
    observerCallCount++;
    if (observerCallCount % 100 === 0) {
        console.log(`Observer called ${observerCallCount} times`);
    }
    // ... rest of observer logic
});
```

## Migration from Chrome Storage

If migrating existing YouTube features from Chrome storage:

### 1. Update Storage Calls

❌ **Old Chrome storage pattern:**
```javascript
chrome.storage.sync.get(['gmbExtractorSettings'], (result) => {
    const settings = result.gmbExtractorSettings || {};
    isEnabled = settings.youtube{Feature}Enabled !== false;
});
```

✅ **New localStorage pattern:**
```javascript
function loadSettings() {
    const storedEnabled = localStorage.getItem('youtube{Feature}Enabled');
    isEnabled = storedEnabled !== 'false';
    return isEnabled;
}
```

### 2. Update Message Listeners

❌ **Old Chrome message pattern:**
```javascript
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'updateSettings') {
        // Handle settings update
    }
});
```

✅ **New storage event pattern:**
```javascript
window.addEventListener('storage', (e) => {
    if (e.key === 'youtube{Feature}Enabled') {
        const wasEnabled = isEnabled;
        loadSettings();
        
        if (wasEnabled !== isEnabled) {
            // Handle enable/disable
        }
    }
});
```

### 3. Remove Chrome API Dependencies

- Remove all `chrome.*` calls
- Remove async/await patterns
- Remove try-catch blocks around Chrome API calls
- Remove Chrome API availability checks

## Best Practices Summary

1. **Always use localStorage** for settings in YouTube scripts
2. **Never use Chrome APIs** in MAIN world scripts
3. **Use textContent** instead of innerHTML for CSP compliance
4. **Handle YouTube SPA navigation** with mutation observers
5. **Implement proper cleanup** to prevent memory leaks
6. **Provide sensible defaults** for all settings
7. **Use throttling** for performance-sensitive observers
8. **Test thoroughly** across different YouTube page types
9. **Follow consistent naming** conventions for settings keys
10. **Implement comprehensive debug logging** for troubleshooting

This guide ensures consistent, reliable YouTube feature implementation while avoiding the common pitfalls that can break functionality or cause errors.