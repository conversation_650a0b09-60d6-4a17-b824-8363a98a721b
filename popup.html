<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM Highlighter Toggle</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0&icon_names=visibility,visibility_off,download" />
    <link rel="stylesheet" href="popup.css">
    <script src="Chart.min.js"></script>
    <script src="html2canvas.min.js"></script>
    <script src="jspdf.umd.min.js"></script>

</head>
<body>
    <div class="popup-container">
        <div class="logo-title">
        <img src="icon.png" alt="Logo">
        <h1> SEO Render Insight Tool</h1>
    </div>
    
    <!-- Tab Navigation -->
    <div class="tab-nav">
        <button class="tab-btn active" data-tab="analysis">Analysis</button>
        <button class="tab-btn" data-tab="element-report" style="display:none;">
            Element Report
            <span class="new-badge">NEW</span>
        </button>
        <button class="tab-btn" data-tab="performance" style="display:none;">Performance <span class="new-badge">NEW</span></button>
        
    </div>
    
    <!-- Analysis Tab -->
    <div class="tab-content active" id="analysis-tab">
    <div class="top-part" style="position: relative;">
        <!-- Status and toggle on same line -->
        <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 8px;">
            <p id="statusText" style="margin: 0; font-size: 14px;">Checking status...</p>
            <label class="switch">
                <input type="checkbox" id="toggleButton">
                <span class="slider round"></span>
            </label>
        </div>

        <p class="note" style="margin: 0 0 12px 0; font-size: 12px;"> Toggle off to disable. Page refreshes when toggled.</p>
        
        <!-- Scroll hint banner (shown when user hasn't scrolled the page enough) -->
        <div id="scrollHint" class="hint-banner" style="display:none;">
            Tip: Scroll the page to reveal lazy‑loaded content for a fuller analysis.
        </div>
        
        <!-- Download Report Icon Button -->
        <button id="downloadReportBtn" class="icon-button" title="Download full report" style="display: none;">
            <img src="download.svg" alt="Download" class="download-icon">
        </button>
        
        <!-- Compact toggle row -->
        <div id="controlsRow" style="margin-top: 8px; display: flex; justify-content: space-around; align-items: center; background: #f8f9fa; padding: 8px; border-radius: 6px;">
            <div style="display: flex; align-items: center; gap: 6px;">
                <label style="font-size: 12px; color: #666;">Hide SSR borders</label>
                <label class="switch" style="transform: scale(0.6);">
                    <input type="checkbox" id="greenToggle">
                    <span class="slider round"></span>
                </label>
            </div>
            <div style="width: 1px; height: 16px; background: #ddd;"></div>
            <div style="display: flex; align-items: center; gap: 6px;">
                <label style="font-size: 12px; color: #666;">Show minimap</label>
                <label class="switch" style="transform: scale(0.6);">
                    <input type="checkbox" id="minimapToggle">
                    <span class="slider round"></span>
                </label>
            </div>
        </div><br>
        
        <!-- Accessibility settings (accordion) -->
        <div class="accordion">
            <button id="accHeader" class="accordion-header" aria-expanded="false" aria-controls="accPanel">
                Accessibility & Color Settings
            </button>
            <div id="accPanel" class="accordion-panel" role="region" aria-labelledby="accHeader">
                <div style="margin-bottom: 10px; font-size: 12px; color: #666;">
                    Choose a colorblind-friendly palette. Your choice is saved for next time.
                </div>
                <div class="palette-grid" id="paletteGrid">
                    <!-- Cards populated by script -->
                </div>
            </div>
        </div>
    </div>
    <div class="bottom-part">
        <p id="elementCountText"></p>
        <canvas id="myChart" width="250" height="250"></canvas>
    </div>
    </div>
    
    <!-- Element Report Tab -->
    <div class="tab-content" id="element-report-tab">
        <div style="padding: 15px;">
            <p style="font-size: 14px; margin-bottom: 15px; text-align: center;">SEO Element Analysis</p>
            <div id="elementReportContent" style="display: none;">
                <div style="height: 200px; position: relative; margin-bottom: 20px;">
                    <canvas id="elementChart"></canvas>
                </div>
                <div id="elementDetails" style="margin-top: 15px;"></div>
            </div>
            <div id="elementReportPlaceholder" style="text-align: center; padding: 40px 20px; color: #666;">
                <p>Enable CSR analysis to see element report</p>
            </div>
        </div>
    </div>
    
    <!-- Performance Tab -->
    <div class="tab-content" id="performance-tab">
        <div style="padding: 15px;">
            <div style="background:#fff;border-radius:15px;padding:12px;min-height:350px;">
                <p style="font-size:14px;margin:0 0 12px 0;text-align:center;">Live Rendering & Loading Signals <span class="help" data-tip="Key page milestones and thread blocking. Use this to identify slow paints (FCP/LCP), layout shifts (CLS), and main‑thread work that delays content visibility.">?</span></p>
                <div id="perfSummary" style="display:grid;grid-template-columns:repeat(2,1fr);gap:8px;margin-bottom:10px;font-size:12px;color:#333;"></div>
                <div style="border-top:1px solid #eee;margin:10px 0;"></div>
                <div>
                    <h4 style="margin:6px 0;font-size:13px;color:#333;">Potential blockers (CSS/JS) <span class="help" data-tip="Synchronous scripts and unconditioned stylesheets in &lt;head&gt; can delay rendering. Consider async/defer for scripts and splitting critical vs. non‑critical CSS.">?</span></h4>
                    <div id="perfBlockingList" style="font-size:12px;color:#444;max-height:120px;overflow:auto;"></div>
                </div>
                <div style="border-top:1px solid #eee;margin:10px 0;"></div>
                <div>
                    <h4 style="margin:6px 0;font-size:13px;color:#333;">Lazy‑loaded elements becoming visible <span class="help" data-tip="Shows when lazy items enter the viewport. If key content appears very late, search engines and users may miss it; consider preloading or earlier loading thresholds.">?</span></h4>
                    <div id="perfLazyList" style="font-size:12px;color:#444;max-height:120px;overflow:auto;"></div>
                </div>
                <div style="border-top:1px solid #eee;margin:10px 0;"></div>
                <div>
                    <h4 style="margin:6px 0;font-size:13px;color:#333;">Request waterfall (first 40) <span class="help" data-tip="Timeline of network requests by start & duration. Early/long CSS & JS often block rendering. Large fonts/images late in the line can push LCP.">?</span></h4>
                    <div id="perfWaterfall" style="max-height:180px;overflow:auto;">
                        <div id="perfWaterScale" style="position:relative;height:10px;margin:0 6px 6px 66px;background:linear-gradient(90deg,#f0f0f0 1px,transparent 1px);background-size:50px 10px;"></div>
                        <div id="perfWaterLegend" style="display:flex;gap:12px;align-items:center;font-size:11px;color:#666;margin:0 6px 6px 66px;"></div>
                        <div id="perfWaterRows"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    
    

    <div id="footerB" style="display:none;">
      <div class="seoant-ad">
        <div class="seoant-header">
          <img src="https://cdn.shopify.com/app-store/listing_images/89f080b72a27e71e4d48c9ad2493e78d/icon/CKvTs8ntkvkCEAE=.png" alt="SEOAnt Logo" class="seoant-logo">
          <p class="seoant-description">
            #1 Shopify AI SEO tools: technical SEO, competitor keyword traffic analysis, site speed up, JSON-LD schema, fix 404, etc.
          </p>
        </div>
        <a href="https://share.seoant.com/app/11684ff28f72937SkC" target="_blank" class="seoant-cta" id="review">Get 30% off</a>
      </div>
      <div id="footer">
        Made with&nbsp;<span id="heart"><img src="/heart.png" alt="heart"></span>&nbsp;by&nbsp;
        <a href="https://www.linkedin.com/in/ma-foroutan/" target="_blank">Amin Foroutan</a>
      </div>
    </div>
    </div>    

    <script src="popup.js"></script>
</body>
</html>


