chrome.runtime.onInstalled.addListener(() => {
  chrome.storage.sync.set({ enabled: false });
});

// Minimal background: no JS control, no extra permissions

chrome.action.onClicked.addListener((tab) => {
  chrome.storage.sync.get(['enabled', 'activeTabId'], (data) => {
      const currentState = data.enabled;
      chrome.storage.sync.set({ enabled: !currentState, activeTabId: tab.id }, () => {
          if (tab && tab.id) {
            chrome.tabs.reload(tab.id);
          }
      });
  });
});

chrome.tabs.onActivated.addListener((activeInfo) => {
  chrome.storage.sync.get(['activeTabId'], (data) => {
      if (data.activeTabId !== activeInfo.tabId) {
          chrome.storage.sync.set({ enabled: false });
          
          // Ask content script to stop observing
          chrome.runtime.sendMessage({ action: 'stop' });
      }
  });
});

// Simple page refresh using tabs API
function refreshPage(tabId) {
  if (tabId) chrome.tabs.reload(tabId);
}

function stopObserving() {
  // Function to stop the observer in the content script
  chrome.runtime.sendMessage({ action: "stop" });
}

// Listen for messages from popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "minimapClosed") {
    // Forward minimap closed message to popup
    chrome.runtime.sendMessage({ action: "minimapClosed" });
  }
});
