{"manifest_version": 3, "name": "⚡ SEO Time Machines", "version": "7.14.0", "permissions": ["activeTab", "tabs", "scripting", "storage", "declarativeNetRequestWithHostAccess", "cookies", "contextMenus", "notifications", "offscreen", "alarms", "clipboardRead", "clipboardWrite", "system.display"], "host_permissions": ["https://mail.google.com/*", "https://lstk-music.s3.ap-southeast-2.amazonaws.com/*", "https://*.google.com/*", "https://google.com/*", "https://*.google.co.in/*", "https://google.co.in/*", "https://*.google.com.br/*", "https://google.com.br/*", "https://*.google.co.uk/*", "https://google.co.uk/*", "https://*.google.co.jp/*", "https://google.co.jp/*", "https://*.google.de/*", "https://google.de/*", "https://*.google.fr/*", "https://google.fr/*", "https://*.google.ca/*", "https://google.ca/*", "https://*.google.com.au/*", "https://google.com.au/*", "https://*.google.ru/*", "https://google.ru/*", "https://*.google.it/*", "https://google.it/*", "https://*.google.es/*", "https://google.es/*", "https://*.google.com.mx/*", "https://google.com.mx/*", "https://*.google.co.kr/*", "https://google.co.kr/*", "https://*.google.nl/*", "https://google.nl/*", "https://*.google.pl/*", "https://google.pl/*", "https://*.google.com.tr/*", "https://google.com.tr/*", "https://*.google.com.ar/*", "https://google.com.ar/*", "https://*.google.co.id/*", "https://google.co.id/*", "https://*.google.com.hk/*", "https://google.com.hk/*", "file://*/*", "<all_urls>"], "declarative_net_request": {"rule_resources": []}, "incognito": "split", "background": {"service_worker": "js/background.js"}, "action": {"default_popup": "popup.html", "default_icon": {"16": "images/icon16.png", "48": "images/icon48.png", "128": "images/icon128.png"}}, "icons": {"16": "images/icon16.png", "48": "images/icon48.png", "128": "images/icon128.png"}, "content_scripts": [{"matches": ["*://*.google.com/maps/*", "*://*.google.com/localservices/*", "*://*.google.com/search*", "*://google.com/maps/*", "*://google.com/localservices/*", "*://google.com/search*", "*://*.google.co.in/maps/*", "*://*.google.co.in/localservices/*", "*://*.google.co.in/search*", "*://google.co.in/maps/*", "*://google.co.in/localservices/*", "*://google.co.in/search*", "*://*.google.com.br/maps/*", "*://*.google.com.br/localservices/*", "*://*.google.com.br/search*", "*://google.com.br/maps/*", "*://google.com.br/localservices/*", "*://google.com.br/search*", "*://*.google.co.uk/maps/*", "*://*.google.co.uk/localservices/*", "*://*.google.co.uk/search*", "*://google.co.uk/maps/*", "*://google.co.uk/localservices/*", "*://google.co.uk/search*", "*://*.google.co.jp/maps/*", "*://*.google.co.jp/localservices/*", "*://*.google.co.jp/search*", "*://google.co.jp/maps/*", "*://google.co.jp/localservices/*", "*://google.co.jp/search*", "*://*.google.de/maps/*", "*://*.google.de/localservices/*", "*://*.google.de/search*", "*://google.de/maps/*", "*://google.de/localservices/*", "*://google.de/search*", "*://*.google.fr/maps/*", "*://*.google.fr/localservices/*", "*://*.google.fr/search*", "*://google.fr/maps/*", "*://google.fr/localservices/*", "*://google.fr/search*", "*://*.google.ca/maps/*", "*://*.google.ca/localservices/*", "*://*.google.ca/search*", "*://google.ca/maps/*", "*://google.ca/localservices/*", "*://google.ca/search*", "*://*.google.com.au/maps/*", "*://*.google.com.au/localservices/*", "*://*.google.com.au/search*", "*://google.com.au/maps/*", "*://google.com.au/localservices/*", "*://google.com.au/search*", "*://*.google.ru/maps/*", "*://*.google.ru/localservices/*", "*://*.google.ru/search*", "*://google.ru/maps/*", "*://google.ru/localservices/*", "*://google.ru/search*", "*://*.google.it/maps/*", "*://*.google.it/localservices/*", "*://*.google.it/search*", "*://google.it/maps/*", "*://google.it/localservices/*", "*://google.it/search*", "*://*.google.es/maps/*", "*://*.google.es/localservices/*", "*://*.google.es/search*", "*://google.es/maps/*", "*://google.es/localservices/*", "*://google.es/search*", "*://*.google.com.mx/maps/*", "*://*.google.com.mx/localservices/*", "*://*.google.com.mx/search*", "*://google.com.mx/maps/*", "*://google.com.mx/localservices/*", "*://google.com.mx/search*", "*://*.google.co.kr/maps/*", "*://*.google.co.kr/localservices/*", "*://*.google.co.kr/search*", "*://google.co.kr/maps/*", "*://google.co.kr/localservices/*", "*://google.co.kr/search*", "*://*.google.nl/maps/*", "*://*.google.nl/localservices/*", "*://*.google.nl/search*", "*://google.nl/maps/*", "*://google.nl/localservices/*", "*://google.nl/search*", "*://*.google.pl/maps/*", "*://*.google.pl/localservices/*", "*://*.google.pl/search*", "*://google.pl/maps/*", "*://google.pl/localservices/*", "*://google.pl/search*", "*://*.google.com.tr/maps/*", "*://*.google.com.tr/localservices/*", "*://*.google.com.tr/search*", "*://google.com.tr/maps/*", "*://google.com.tr/localservices/*", "*://google.com.tr/search*", "*://*.google.com.ar/maps/*", "*://*.google.com.ar/localservices/*", "*://*.google.com.ar/search*", "*://google.com.ar/maps/*", "*://google.com.ar/localservices/*", "*://google.com.ar/search*", "*://*.google.co.id/maps/*", "*://*.google.co.id/localservices/*", "*://*.google.co.id/search*", "*://google.co.id/maps/*", "*://google.co.id/localservices/*", "*://google.co.id/search*", "*://*.google.com.hk/maps/*", "*://*.google.com.hk/localservices/*", "*://*.google.com.hk/search*", "*://google.com.hk/maps/*", "*://google.com.hk/localservices/*", "*://google.com.hk/search*"], "css": ["css/gmb-extractor.css", "css/google-search-services-extractor.css", "css/gmb-content-ui.css", "css/google-search-extractor.css", "css/location-changer.css"], "js": ["settings/drag-select-links.js", "settings/logging-utility.js", "settings/notification-utility.js", "settings/dom-snapshot-utility.js", "js/review-range-utility.js", "js/location-changer-integration.js", "js/content.js", "js/multiple-listings.js", "js/review-analysis.js", "js/single-review-scraper.js", "js/prolist-extraction.js", "js/prolist-review-scraper.js", "js/search-nap-injector.js", "js/find-citations-injector.js", "js/open-single-listing.js", "js/google-search-extractor.js", "js/google-search-multiple-review-scraper.js", "settings/serp-numbering.js", "settings/tracked-domains.js", "settings/search-result-stats.js", "js/utilities/top-urls-copier.js", "js/current-location-display.js"]}, {"matches": ["<all_urls>"], "js": ["settings/drag-select-links.js", "settings/logging-utility.js", "settings/global-shortcut-manager.js", "js/screenshot-selector.js", "settings/clicktocopy.js", "settings/copy-replace.js", "settings/utm-tracking-cleaner.js", "js/universal-click-debugger.js", "settings/quick-actions/wordcounter.js", "settings/quick-actions/showhidden.js", "settings/quick-actions/colorpicker.js", "settings/quick-actions/copyelement.js", "js/text-transformers.js", "settings/sponsored-highlighter.js", "js/alerts/alert-content-handler.js"], "run_at": "document_start", "all_frames": true, "match_about_blank": true}, {"matches": ["<all_urls>"], "js": ["settings/minimal-reader.js"], "run_at": "document_start", "all_frames": true}, {"matches": ["<all_urls>"], "js": ["js/readability/html-tokenizer.js", "js/readability/dom-builder.js", "js/readability/link-processor.js", "js/readability/content-scorer.js", "js/readability/html-entities.js", "js/readability/content-extractor.js", "js/readability/html-serializer.js", "js/readability/advanced-cleaner.js", "js/readability-extractor.js", "settings/minimal-reader-interface.js"], "run_at": "document_start", "all_frames": true, "world": "MAIN"}, {"matches": ["https://mail.google.com/*"], "css": ["css/gmail-sender-icons.css"], "js": ["js/context-manager.js", "settings/reverse-gmail-order.js", "settings/gmail-time-formatter.js", "settings/gmail-thread-expander.js", "settings/gmail-enhanced-timestamps.js", "settings/gmail-sender-icons.js", "settings/gmail-email-pinner.js", "settings/gmail-jump-links.js", "js/mass-unsubscribe.js"], "run_at": "document_end", "all_frames": false}, {"matches": ["https://www.youtube.com/*"], "js": ["settings/youtube-settings-bridge.js"], "run_at": "document_start"}, {"matches": ["https://www.youtube.com/*"], "js": ["settings/youtube-ads-skipper.js", "settings/youtube-screenshot.js", "settings/youtube-thumbnail-viewer.js", "settings/youtube-frames.js", "settings/youtube-gif.js"], "run_at": "document_start", "world": "MAIN"}, {"matches": ["<all_urls>"], "js": ["settings/video-speed-controller.js"], "run_at": "document_end", "all_frames": true}], "web_accessible_resources": [{"resources": ["text-time-machine.html", "css/reverse-gmail-order.css", "css/text-time-machine-dark.css", "js/universal-click-debugger.js", "settings/quick-actions/htags.js", "settings/quick-actions/showlinks.js", "settings/quick-actions/showhidden.js", "settings/quick-actions/quickedit.js", "settings/quick-actions/semantic-tooltips.js", "settings/quick-actions/semanticelements.js", "settings/quick-actions/pagestructure.js", "settings/quick-actions/headingstructure.js", "settings/quick-actions/metadata.js", "settings/quick-actions/utmbuilder.js", "settings/quick-actions/schema.js", "settings/quick-actions/keyword.js", "settings/quick-actions/keyworddensity.js", "settings/quick-actions/pagekeyworddensity.js", "settings/quick-actions/linkchecker.js", "settings/quick-actions/images.js", "settings/quick-actions/wordcounter.js", "settings/quick-actions/copyelement.js", "settings/quick-actions/linksextractor.js", "settings/quick-actions/bulklinkopen.js", "settings/quick-actions/colorpicker.js", "settings/quick-actions/fontinspector.js", "settings/quick-actions/fontstyles.js", "settings/quick-actions/colorpaletteextractor.js", "settings/quick-actions/cssclassinspector.js", "settings/quick-actions/screenreadersimulation.js", "settings/quick-actions/waybackmachine.js", "settings/quick-actions/email-generator.js", "settings/quick-actions/robotstxt.js", "settings/quick-actions/xmlsitemapchecker.js", "settings/quick-actions/mozdachecker.js", "settings/quick-actions/youtubeembedscraper.js", "settings/quick-actions/clean-and-title.js", "settings/quick-actions/cleanselectedcontent.js", "settings/quick-actions/html-cleaner.js", "settings/quick-actions/boldfromserp.js", "settings/quick-actions/responsive.js", "settings/quick-actions/seotests.js", "settings/quick-actions/trackerdetection.js", "js/tooltip-system.js", "js/info-icon-initializer.js", "js/text-transformers.js", "offscreen.html", "js/pomodoro/offscreen-audio.js", "blocked.html", "blocked.js", "sounds/clock-ticking-1.wav", "sounds/clock-ticking-2.mp3", "sounds/clock-ticking-3.mp3", "js/alerts/alert-popup.js", "alerts/email-pinner.html", "alerts/email-pinner.js", "css/minimal-reader.css", "js/readability/html-tokenizer.js", "js/readability/dom-builder.js", "js/readability/link-processor.js", "js/readability/content-scorer.js", "js/readability/html-entities.js", "js/readability/content-extractor.js", "js/readability/html-serializer.js", "js/readability/advanced-cleaner.js", "js/readability-extractor.js", "settings/minimal-reader-interface.js", "screenshot-editor.html", "js/screenshot-editor.js", "js/onboarding-content.js", "js/popup-shortcuts.js"], "matches": ["<all_urls>"]}]}