    document.addEventListener('DOMContentLoaded', () => {
    const toggleSwitch = document.querySelector('.switch input');
    const greenToggle = document.getElementById('greenToggle');
    const minimapToggle = document.getElementById('minimapToggle');
    const statusText = document.getElementById('statusText');
    const elementCountText = document.getElementById('elementCountText');
    const chartContainer = document.getElementById('myChart').parentElement;
    const downloadReportBtn = document.getElementById('downloadReportBtn');
    let myChart;
    let elementChart;
    let currentElementReport = null;
    // Color palettes for accessibility
    const palettes = [
        { id: 'default', name: 'Default', type: 'Normal Vision', csr: '#f44336', ssr: '#4CAF50' },
        { id: 'deuteranopia', name: 'Deuteranopia', type: 'Red-Green (common)', csr: '#E69F00', ssr: '#56B4E9' },
        { id: 'protanopia', name: 'Protanopia', type: 'Red-Green', csr: '#0072B2', ssr: '#F0E442' },
        { id: 'tritanopia', name: 'Tritanopia', type: 'Blue-Yellow', csr: '#D55E00', ssr: '#009E73' },
        { id: 'achromatopsia', name: 'Achromatopsia', type: 'No Color', csr: '#666666', ssr: '#AAAAAA' }
    ];
    let selectedPalette = palettes[0];
    let elementVisibility = {
        headings: true,
        images: true,
        links: true,
        text: true,
        lists: true,
        forms: true,
        media: true,
        tables: true
    };

    // Tab switching functionality
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetTab = btn.dataset.tab;
            
            // Remove active class from all tabs and contents
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            btn.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');
            
            // Re-render element report if switching to that tab and we have data
            if (targetTab === 'element-report' && currentElementReport) {
                // Add a small delay to ensure tab content is fully visible
                setTimeout(() => {
                    renderElementReport(currentElementReport);
                }, 50);
            } else if (targetTab === 'performance') {
                // Request snapshot and render
                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                    if (tabs[0]) {
                        chrome.tabs.sendMessage(tabs[0].id, { action: 'getPerformanceSnapshot' }, (snapshot) => {
                            if (snapshot) renderPerformance(snapshot);
                        });
                    }
                });
            }
        });
    });

    function updateUI(isEnabled) {
        toggleSwitch.checked = isEnabled;
        statusText.textContent = `CSR elements Highlighter is ${isEnabled ? 'ON' : 'OFF'}.`;
        chartContainer.style.display = isEnabled ? 'block' : 'none';
        downloadReportBtn.style.display = isEnabled ? 'block' : 'none';
        
        // Show/hide the entire controls row (SSR borders + minimap) based on main toggle
        const controlsRow = document.getElementById('controlsRow');
        if (controlsRow) controlsRow.style.display = isEnabled ? 'flex' : 'none';
        
            // Handle element report visibility
        if (!isEnabled) {
            document.getElementById('elementReportContent').style.display = 'none';
            document.getElementById('elementReportPlaceholder').style.display = 'block';
            currentElementReport = null;
            if (elementChart) {
                elementChart.destroy();
                elementChart = null;
            }
        }

            // Show/hide tabs when enabled
            const tabNav = document.querySelector('.tab-nav');
            if (tabNav) {
                const buttons = tabNav.querySelectorAll('.tab-btn');
                buttons.forEach((btn) => {
                    const tab = btn.getAttribute('data-tab');
                    if (tab !== 'analysis') {
                        btn.style.display = isEnabled ? 'inline-block' : 'none';
                    }
                });
            }

        chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, { action: isEnabled ? "start" : "stop" });
        });
    }

    chrome.storage.sync.get(['enabled', 'hideGreen', 'minimapEnabled', 'elementVisibility', 'colorPalette'], (data) => {
        updateUI(!!data.enabled);
        greenToggle.checked = data.hideGreen === true; // default to false (show green by default)
        
        // Initialize minimap toggle
        minimapToggle.checked = data.minimapEnabled === true;
        
        // Load saved element visibility preferences
        if (data.elementVisibility) {
            elementVisibility = { ...elementVisibility, ...data.elementVisibility };
        }
        // Load saved palette
        if (data.colorPalette) {
            const match = palettes.find(p => p.id === data.colorPalette.id) || data.colorPalette;
            if (match && match.csr && match.ssr) {
                selectedPalette = match;
                applyPalette(match);
            }
        }
        // Build palette UI
        buildPaletteCards();
        // No JS-control: nothing to check

        // Ensure footerB shows (SEOAnt ad variant)
        const fb = document.getElementById('footerB');
        if (fb) fb.style.display = 'block';
    });

    toggleSwitch.addEventListener('change', () => {
        const newState = toggleSwitch.checked;
        chrome.storage.sync.set({ enabled: newState }, () => {
            updateUI(newState);
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs[0]) {
                    chrome.tabs.reload(tabs[0].id);
                }
            });
        });
    });

    greenToggle.addEventListener('change', () => {
        const hideGreen = greenToggle.checked;
        chrome.storage.sync.set({ hideGreen: hideGreen }, () => {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs[0]) {
                    chrome.tabs.sendMessage(tabs[0].id, { 
                        action: "toggleGreen", 
                        showGreen: !hideGreen 
                    });
                }
            });
        });
    });

    // JS-control removed

    // Accessibility accordion logic
    const accHeader = document.getElementById('accHeader');
    const accPanel = document.getElementById('accPanel');
    accHeader.addEventListener('click', () => {
        const open = accPanel.classList.toggle('open');
        accHeader.setAttribute('aria-expanded', open ? 'true' : 'false');
    });

    function buildPaletteCards() {
        const grid = document.getElementById('paletteGrid');
        if (!grid) return;
        grid.innerHTML = '';
        palettes.forEach(p => {
            const card = document.createElement('div');
            card.className = 'palette-card';
            card.setAttribute('role', 'button');
            card.setAttribute('tabindex', '0');
            card.setAttribute('aria-label', `${p.name} palette`);
            card.innerHTML = `
                <div style="display:flex;justify-content:space-between;align-items:center;">
                    <div style="font-size:12px;color:#333;font-weight:600;">${p.name}</div>
                    <div style="font-size:11px;color:#777;">${p.type}</div>
                </div>
                <div class="swatches">
                    <div class="swatch" style="background:${p.csr}" title="CSR"></div>
                    <div class="swatch" style="background:${p.ssr}" title="SSR"></div>
                </div>
            `;
            card.addEventListener('click', () => selectPalette(p));
            card.addEventListener('keydown', (e) => { if (e.key === 'Enter' || e.key === ' ') selectPalette(p); });
            grid.appendChild(card);
        });
    }

    function selectPalette(palette) {
        selectedPalette = palette;
        chrome.storage.sync.set({ colorPalette: palette });
        applyPalette(palette);
        // Refresh charts with new colors if present
        if (myChart) { try { myChart.destroy(); } catch(e) {} }
        if (elementChart) { try { elementChart.destroy(); } catch(e) {} }
        // Trigger re-render from last message data if available
        if (message && message.total != null) {
            chrome.runtime.sendMessage({ action: 'updateMinimap' });
            chrome.runtime.sendMessage({ action: 'refreshCharts' });
            // Recreate the donut immediately
            const total = message.total || 0;
            const red = message.red || 0;
            const percentage = total > 0 ? Number(((red / total) * 100).toFixed(1)) : 0;
            const remaining = 100 - percentage;
            const ctx = document.getElementById('myChart').getContext('2d');
            myChart = createDonut(ctx, percentage, remaining);
            if (currentElementReport) renderElementReport(currentElementReport);
        }
    }

    function applyPalette(palette) {
        // Update CSS variables used by popup UI
        document.documentElement.style.setProperty('--csr-color', palette.csr);
        document.documentElement.style.setProperty('--ssr-color', palette.ssr);
        // Tell content script to apply outline variables
                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                    if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, {
                    action: 'applyPalette',
                    palette
                });
            }
        });
    }

    function createDonut(ctx, percentage, remaining) {
        return new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['CSR Content', 'SSR Content'],
                datasets: [{
                    data: [percentage, remaining],
                    backgroundColor: [getComputedStyle(document.documentElement).getPropertyValue('--csr-color').trim() || '#f44336',
                                     getComputedStyle(document.documentElement).getPropertyValue('--ssr-color').trim() || '#4CAF50'],
                    borderWidth: 0,
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                animation: { duration: 0 },
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            const dataset = data.datasets[tooltipItem.datasetIndex] || { data: [] };
                            const vals = dataset.data.map(v => Number(v || 0));
                            const total = vals.reduce((a, b) => a + b, 0);
                            const current = Number(dataset.data[tooltipItem.index] || 0);
                            const pct = total > 0 ? ((current / total) * 100).toFixed(1) : '0.0';
                            const label = data.labels[tooltipItem.index] || '';
                            return `${label}: ${pct}%`;
                        }
                    }
                },
                legend: { position: 'bottom', labels: { padding: 15, fontSize: 12 } }
            },
            plugins: [{
                afterDatasetsDraw: function(chart) {
                    const ctx2 = chart.ctx;
                    chart.data.datasets.forEach(function(dataset) {
                        const meta = chart.getDatasetMeta(0);
                        meta.data.forEach(function(bar, index) {
                            const value = Number(dataset.data[index]);
                            if (value > 5) {
                                ctx2.save();
                                const position = bar.tooltipPosition();
                                ctx2.fillStyle = 'white';
                                ctx2.font = 'bold 14px Arial';
                                ctx2.textAlign = 'center';
                                ctx2.textBaseline = 'middle';
                                ctx2.shadowColor = 'rgba(0, 0, 0, 0.5)';
                                ctx2.shadowBlur = 4;
                                ctx2.fillText(Number(value).toFixed(1) + '%', position.x, position.y);
                                ctx2.restore();
            }
        });
    });
                }
            }]
        });
    }

    function ms(x) { return x != null ? x.toFixed(0) + ' ms' : '—'; }
    function s1(x) { return x != null ? (x/1000).toFixed(1) + ' s' : '—'; }
    function badge(val, warnAt) {
        if (val == null) return '';
        return `<span class="badge ${val > warnAt ? 'badge-warn' : 'badge-ok'}">${val > warnAt ? 'Needs attention' : 'Looks good'}</span>`;
    }
    function renderPerformance(snapshot) {
        const summary = document.getElementById('perfSummary');
        const blockingList = document.getElementById('perfBlockingList');
        const lazyList = document.getElementById('perfLazyList');
        const wfRows = document.getElementById('perfWaterRows');
        const wfScale = document.getElementById('perfWaterScale');
        const wfLegend = document.getElementById('perfWaterLegend');
        if (!summary || !blockingList || !lazyList) return;

        // Key metrics
        const nav = snapshot.nav || {};
        const fcp = snapshot.paints && snapshot.paints.fcp;
        const lcp = snapshot.paints && snapshot.paints.lcp;
        const cls = snapshot.cls || 0;
        const tbt = (snapshot.longTasks || []).reduce((a, t) => a + (t.duration || 0), 0);
        summary.innerHTML = `
            <div class="kpi-card"><span class="kpi-title">Server response (TTFB)</span><span class="kpi-value">${ms(nav.ttfb)}</span></div>
            <div class="kpi-card"><span class="kpi-title">DOM ready (DCL)</span><span class="kpi-value">${s1(nav.domContentLoaded)}</span></div>
            <div class="kpi-card"><span class="kpi-title">Page loaded</span><span class="kpi-value">${s1(nav.load)}</span></div>
            <div class="kpi-card"><span class="kpi-title">First content paint</span><span class="kpi-value">${s1(fcp)} ${badge(fcp, 1800)}</span></div>
            <div class="kpi-card"><span class="kpi-title">Largest content paint</span><span class="kpi-value">${s1(lcp)} ${badge(lcp, 2500)}</span></div>
            <div class="kpi-card"><span class="kpi-title">Layout shift (CLS)</span><span class="kpi-value">${cls.toFixed(3)} ${badge(cls*1000, 0.1*1000)}</span></div>
            <div class="kpi-card"><span class="kpi-title">Main‑thread blocking (TBT)</span><span class="kpi-value">${ms(tbt)} ${badge(tbt, 200)}</span></div>
            <div class="kpi-card"><span class="kpi-title">Transfer size</span><span class="kpi-value">${(nav.transferSize/1024).toFixed(0)} KB</span></div>
        `;

        // Blocking resources
        const blocks = snapshot.blocking || [];
        blockingList.innerHTML = blocks.length ? blocks.slice(0, 12).map(b => `
            <div class="list-item">
                <span class="pill">${b.type}</span>
                <span title="${b.src}" style="overflow:hidden;text-overflow:ellipsis;white-space:nowrap;max-width:280px;">${(b.src||'').split('/').pop() || '(inline)'}</span>
                <span style="color:#999;">${b.hint}</span>
            </div>`).join('') : '<div style="color:#666;">No obvious render‑blockers detected</div>';

        // Lazy visibility
        const lazy = snapshot.lazy || [];
        lazyList.innerHTML = lazy.length ? lazy.slice(0, 25).map(l => `
            <div class="list-item">
                <span class="pill">${l.tag.toUpperCase()}</span>
                <span>visible at <strong>${s1(l.visibleAt)}</strong></span>
                ${l.src ? `<span title="${l.src}" style="overflow:hidden;text-overflow:ellipsis;white-space:nowrap;max-width:220px;">— ${(l.src||'').split('/').pop()}</span>` : ''}
            </div>
        `).join('') : '<div style="color:#666;">No lazy‑loaded elements observed</div>';

        // Waterfall
        const resources = snapshot.resources || [];
        if (wfRows && wfScale) {
            if (resources.length === 0) {
                wfRows.innerHTML = '<div style="color:#666;padding:6px;">No request data captured</div>';
                if (wfLegend) wfLegend.innerHTML = '';
            } else {
                const maxEnd = Math.max(...resources.map(r => r.end));
                const pxPerMs = 0.12; // slightly tighter scale
                wfScale.style.backgroundSize = `${Math.round(50)}px 10px`;
                if (wfLegend) {
                    const css = getComputedStyle(document.documentElement).getPropertyValue('--ssr-color').trim() || '#4CAF50';
                    const js = getComputedStyle(document.documentElement).getPropertyValue('--csr-color').trim() || '#f44336';
                    wfLegend.innerHTML = `
                        <span style="display:inline-flex;align-items:center;gap:6px;"><span style="width:10px;height:10px;background:${css};display:inline-block;border-radius:2px;"></span>CSS</span>
                        <span style="display:inline-flex;align-items:center;gap:6px;"><span style="width:10px;height:10px;background:${js};display:inline-block;border-radius:2px;"></span>JS</span>
                        <span style="display:inline-flex;align-items:center;gap:6px;"><span style="width:10px;height:10px;background:#7E57C2;display:inline-block;border-radius:2px;"></span>Font</span>
                        <span style="display:inline-flex;align-items:center;gap:6px;"><span style="width:10px;height:10px;background:#29B6F6;display:inline-block;border-radius:2px;"></span>Image</span>
                        <span style="display:inline-flex;align-items:center;gap:6px;"><span style="width:10px;height:10px;background:#90A4AE;display:inline-block;border-radius:2px;"></span>Other</span>
                    `;
                }
                wfRows.innerHTML = resources.map(r => {
                    const left = Math.max(0, r.start * pxPerMs);
                    const maxWidth = 420; // ensure bar stays within wrap
                    const width = Math.max(2, Math.min(maxWidth - left, r.duration * pxPerMs));
                    const color = (
                        r.type === 'css' ? getComputedStyle(document.documentElement).getPropertyValue('--ssr-color').trim() || '#4CAF50' :
                        r.type === 'script' ? getComputedStyle(document.documentElement).getPropertyValue('--csr-color').trim() || '#f44336' :
                        r.type === 'font' ? '#7E57C2' :
                        r.type === 'img' || r.type === 'image' ? '#29B6F6' :
                        '#90A4AE'
                    );
                    return `
                        <div class="wf-row" title="${r.name}">
                            <div class="wf-type">${r.type}</div>
                            <div class="wf-name">${r.file || r.name}</div>
                            <div class="wf-bar-wrap" style="max-width:${maxWidth}px;">
                                <div class="wf-bar" style="left:${left}px;width:${width}px;background:${color}"></div>
                            </div>
                            <div class="pill">${ms(r.duration)}</div>
                        </div>`;
                }).join('');
            }
        }
    }

    // Minimap toggle event handler
    minimapToggle.addEventListener('change', () => {
        const minimapEnabled = minimapToggle.checked;
        
        chrome.storage.sync.set({ minimapEnabled: minimapEnabled }, () => {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs[0]) {
                    chrome.tabs.sendMessage(tabs[0].id, {
                        action: 'toggleMinimap',
                        show: minimapEnabled
                    });
                }
            });
        });
    });

    function renderElementReport(elementReport) {
        if (!elementReport) return;
        
        const reportContent = document.getElementById('elementReportContent');
        const placeholder = document.getElementById('elementReportPlaceholder');
        const detailsDiv = document.getElementById('elementDetails');
        
        reportContent.style.display = 'block';
        placeholder.style.display = 'none';
        
        // Force a delay to ensure DOM is ready and tab is visible
        setTimeout(() => {
            // Double check that the canvas is visible before creating chart
            const elementCanvas = document.getElementById('elementChart');
            if (!elementCanvas || elementCanvas.offsetParent === null) {
                console.warn('Element chart canvas not visible, skipping chart creation');
                return;
            }
        
        // Prepare data for chart
        const labels = [];
        const ssrData = [];
        const csrData = [];
        const details = [];
        
        const categoryNames = {
            headings: 'Headings',
            images: 'Images',
            links: 'Links',
            text: 'Text Content',
            lists: 'Lists',
            forms: 'Forms',
            media: 'Media',
            tables: 'Tables'
        };
        
        Object.keys(categoryNames).forEach(key => {
            const category = elementReport[key];
            if (category && (category.ssr > 0 || category.csr > 0)) {
                labels.push(categoryNames[key]);
                ssrData.push(category.ssr);
                csrData.push(category.csr);
                
                // Add details
                let detail = `${categoryNames[key]}: ${category.ssr} SSR, ${category.csr} CSR`;
                if (key === 'text' && (category.ssrWords > 0 || category.csrWords > 0)) {
                    detail += ` (${category.ssrWords} SSR words, ${category.csrWords} CSR words)`;
                }
                details.push(detail);
            }
        });
        
        // Update details text with better styling
        let detailsHTML = '<div class="element-breakdown">';
        detailsHTML += `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                <h4 style="margin: 0; font-size: 13px; color: #333;">Element Breakdown</h4>
                <div style="display: flex; gap: 8px;">
                    <button class="visibility-control-btn" id="show-all-elements" title="Show all">
                        <img src="visibility.svg" style="width: 14px; height: 14px;">
                        All
                    </button>
                    <button class="visibility-control-btn" id="hide-all-elements" title="Hide all">
                        <img src="visibility.svg" style="width: 14px; height: 14px; opacity: 0.4;">
                        None
                    </button>
                </div>
            </div>
        `;
        
        if (details.length > 0) {
            detailsHTML += '<div class="element-grid">';
            
            Object.keys(categoryNames).forEach(key => {
                const category = elementReport[key];
                if (category && (category.ssr > 0 || category.csr > 0)) {
                    const total = category.ssr + category.csr;
                    const csrPercentage = total > 0 ? ((category.csr / total) * 100).toFixed(1) : 0;
                    
                    detailsHTML += `
                        <div class="element-item">
                            <div class="element-header">
                                <span class="element-name">${categoryNames[key]}</span>
                                <div class="element-header-right">
                                    <button class="element-visibility-toggle ${elementVisibility[key] ? 'visible' : ''}" 
                                            data-category="${key}" 
                                            title="Toggle visibility in minimap">
                                        <img src="visibility.svg" class="visibility-icon" style="width: 16px; height: 16px; opacity: ${elementVisibility[key] ? '1' : '0.4'};">
                                    </button>
                                    <span class="element-total">${total}</span>
                                </div>
                            </div>
                            <div class="element-bar">
                                <div class="element-bar-ssr" style="width: ${100 - csrPercentage}%"></div>
                                <div class="element-bar-csr" style="width: ${csrPercentage}%"></div>
                            </div>
                            <div class="element-counts">
                                <span class="ssr-count">SSR: ${category.ssr}</span>
                                <span class="csr-count">CSR: ${category.csr}</span>
                                ${key === 'text' && (category.ssrWords > 0 || category.csrWords > 0) ? 
                                    `<span class="word-count">(${category.ssrWords} / ${category.csrWords} words)</span>` : ''}
                            </div>
                        </div>
                    `;
                }
            });
            
            detailsHTML += '</div>';
        } else {
            detailsHTML += '<p style="color: #666;">No elements found</p>';
        }
        
        detailsHTML += '</div>';
        detailsDiv.innerHTML = detailsHTML;
        
        // Add click handlers for visibility toggles
        document.querySelectorAll('.element-visibility-toggle').forEach(button => {
            button.addEventListener('click', (e) => {
                const category = button.dataset.category;
                elementVisibility[category] = !elementVisibility[category];
                
                // Update button appearance
                const icon = button.querySelector('.visibility-icon');
                if (elementVisibility[category]) {
                    button.classList.add('visible');
                    icon.style.opacity = '1';
                } else {
                    button.classList.remove('visible');
                    icon.style.opacity = '0.4';
                }
                
                // Save to storage
                chrome.storage.sync.set({ elementVisibility: elementVisibility });
                
                // Send update to content script
                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                    if (tabs[0]) {
                        chrome.tabs.sendMessage(tabs[0].id, {
                            action: 'updateMinimapFilter',
                            elementVisibility: elementVisibility
                        });
                    }
                });
            });
        });
        
        // Add show all handler
        const showAllBtn = document.getElementById('show-all-elements');
        if (showAllBtn) {
            showAllBtn.addEventListener('click', () => {
                // Set all to visible
                Object.keys(elementVisibility).forEach(key => {
                    elementVisibility[key] = true;
                });
                
                // Update all toggle buttons
                document.querySelectorAll('.element-visibility-toggle').forEach(button => {
                    button.classList.add('visible');
                    button.querySelector('.visibility-icon').style.opacity = '1';
                });
                
                // Save and update
                chrome.storage.sync.set({ elementVisibility: elementVisibility });
                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                    if (tabs[0]) {
                        chrome.tabs.sendMessage(tabs[0].id, {
                            action: 'updateMinimapFilter',
                            elementVisibility: elementVisibility
                        });
                    }
                });
            });
        }
        
        // Add hide all handler
        const hideAllBtn = document.getElementById('hide-all-elements');
        if (hideAllBtn) {
            hideAllBtn.addEventListener('click', () => {
                // Set all to hidden
                Object.keys(elementVisibility).forEach(key => {
                    elementVisibility[key] = false;
                });
                
                // Update all toggle buttons
                document.querySelectorAll('.element-visibility-toggle').forEach(button => {
                    button.classList.remove('visible');
                    button.querySelector('.visibility-icon').style.opacity = '0.4';
                });
                
                // Save and update
                chrome.storage.sync.set({ elementVisibility: elementVisibility });
                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                    if (tabs[0]) {
                        chrome.tabs.sendMessage(tabs[0].id, {
                            action: 'updateMinimapFilter',
                            elementVisibility: elementVisibility
                        });
                    }
                });
            });
        }
        
        // Only create chart if we have data
        if (labels.length === 0) {
            return;
        }
        
        // Create or update chart
        const canvas = document.getElementById('elementChart');
        if (!canvas) {
            console.error('Element chart canvas not found');
            return;
        }
        
        const ctx = canvas.getContext('2d');
        
        // Ensure chart is properly destroyed before creating new one
        if (elementChart) {
            try {
                elementChart.destroy();
            } catch (e) {
                console.warn('Error destroying element chart:', e);
            }
            elementChart = null;
        }
        
        elementChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'SSR Elements',
                    data: ssrData,
                    backgroundColor: getComputedStyle(document.documentElement).getPropertyValue('--ssr-color').trim() || '#4CAF50',
                    stack: 'stack0'
                }, {
                    label: 'CSR Elements',
                    data: csrData,
                    backgroundColor: getComputedStyle(document.documentElement).getPropertyValue('--csr-color').trim() || '#f44336',
                    stack: 'stack0'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'x',
                scales: {
                    x: {
                        stacked: true,
                        ticks: {
                            autoSkip: false,
                            maxRotation: 45,
                            minRotation: 45,
                            font: {
                                size: 11
                            }
                        }
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1,
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                layout: {
                    padding: {
                        top: 10,
                        bottom: 10
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                const datasetIndex = context.datasetIndex;
                                const dataIndex = context.dataIndex;
                                const ssr = context.chart.data.datasets[0].data[dataIndex];
                                const csr = context.chart.data.datasets[1].data[dataIndex];
                                const total = ssr + csr;
                                const percentage = ((context.parsed.y / total) * 100).toFixed(1);
                                return percentage + '% of total';
                            }
                        }
                    }
                }
            }
        });
        }, 250); // Close setTimeout - increased delay for better reliability
    }

    // Download Report functionality
    downloadReportBtn.addEventListener('click', async () => {
        downloadReportBtn.disabled = true;
        downloadReportBtn.classList.add('loading');
        
        try {
            // Get current tab URL
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const currentUrl = tabs[0].url;
            
            // Request minimap screenshot from content script
            const minimapData = await new Promise((resolve) => {
                chrome.tabs.sendMessage(tabs[0].id, { action: 'captureMinimapForReport' }, (response) => {
                    resolve(response);
                });
            });
            
            // Create a temporary container for report generation
            const reportContainer = document.createElement('div');
            reportContainer.style.cssText = 'position: absolute; left: -9999px; top: -9999px; width: 800px; background: white; padding: 20px;';
            document.body.appendChild(reportContainer);
            
            // Add Material Icons support to report container
            const iconStyle = document.createElement('style');
            iconStyle.textContent = `
                .material-symbols-outlined {
                    font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 24;
                    font-family: 'Material Symbols Outlined';
                }
            `;
            reportContainer.appendChild(iconStyle);
            
            // Resolve current palette colors for report
            const reportCsrColor = getComputedStyle(document.documentElement).getPropertyValue('--csr-color').trim() || '#f44336';
            const reportSsrColor = getComputedStyle(document.documentElement).getPropertyValue('--ssr-color').trim() || '#4CAF50';
            
            // Build report content with simple, well-organized design
            reportContainer.innerHTML = `
                <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; color: #333;">
                    <!-- Header -->
                    <h1 style="font-size: 24px; margin: 0 0 20px 0; color: #333; text-align: left;">Content Rendering Analysis Report</h1>
                    
                    <!-- URL and Date -->
                    <div style="margin-bottom: 20px;">
                        <p style="margin: 0 0 5px 0; font-size: 13px; color: #666;">URL: <span style="font-family: monospace; color: #333;">${currentUrl}</span></p>
                        <p style="margin: 0 0 15px 0; font-size: 13px; color: #666;">Date: ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
                        <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 0;">
                    </div>
                    
                    <!-- Summary -->
                    <div style="margin-bottom: 20px;">
                        <h2 style="margin: 0 0 10px 0; font-size: 16px; color: #333;">Analysis Summary</h2>
                        <p style="margin: 0; font-size: 14px; color: #333;">${elementCountText.textContent}</p>
                    </div>
                    
                    <!-- Main Content Layout -->
                    <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                        <!-- Left Side: Charts and Analysis (70%) -->
                        <div style="flex: 0 0 70%;">
                            <!-- Content Distribution Chart -->
                            <div style="margin-bottom: 30px;">
                                <h3 style="margin: 0 0 10px 0; font-size: 16px; color: #333;">Content Distribution</h3>
                                <div style="padding: 10px; text-align: center;">
                                    <canvas id="reportChart" width="252" height="252"></canvas>
                                </div>
                            </div>
                            
                            ${currentElementReport ? `
                            <!-- Element Breakdown Section -->
                            <div>
                                <h3 style="margin: 0 0 10px 0; font-size: 16px; color: #333;">Element Breakdown by Category</h3>
                                <p style="margin: 0 0 15px 0; font-size: 13px; color: #666;">
                                    Shows the distribution of server-side rendered (SSR) vs client-side rendered (CSR) elements by category.
                                </p>
                                <div style="padding: 10px; text-align: center;">
                                    <canvas id="reportElementCanvas" width="500" height="200" style="display: block; margin: 0 auto;"></canvas>
                                </div>
                                
                                <!-- Element Details Table -->
                                <div style="margin-top: 20px;">
                                    <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
                                        <thead>
                                            <tr style="background: #f8f9fa;">
                                                <th style="padding: 6px; text-align: left; border: 1px solid #dee2e6;">Category</th>
                                                <th style="padding: 6px; text-align: center; border: 1px solid #dee2e6;">SSR Elements</th>
                                                <th style="padding: 6px; text-align: center; border: 1px solid #dee2e6;">CSR Elements</th>
                                                <th style="padding: 6px; text-align: center; border: 1px solid #dee2e6;">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody id="reportTableBody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                        
                        <!-- Right Side: Minimap (30%) -->
                        ${minimapData ? `
                                                 <div style="flex: 0 0 30%;">
                             <h3 style="margin: 0 0 10px 0; font-size: 16px; color: #333;">DOM Element Visualization</h3>
                             <div style="padding: 10px; text-align: center;">
                                 <img src="${minimapData}" style="max-width: 140px; height: auto; display: block; margin: 0 auto;">
                                 <p style="margin: 10px 0 0 0; font-size: 11px; color: #666; font-style: italic;">SSR (green) vs CSR (red) elements</p>
                             </div>
                         </div>
                        ` : ''}
                    </div>
                    
                    <!-- Footer -->
                    <div style="text-align: left; padding-top: 20px; border-top: 1px solid #e0e0e0;">
                        <p style="margin: 0; font-size: 10px; color: #999;">Generated by <a href="https://chromewebstore.google.com/detail/seo-render-insight-tool/ignachbibbeengfepmkeogegpfkigljc" style="color: #333; text-decoration: none;">SEO Render Insight Tool</a></p>
                    </div>
                </div>
            `;
            
            // Recreate the donut chart with selected palette
            const reportChartCtx = document.getElementById('reportChart').getContext('2d');
            const total = message.total || 0;
            const red = message.red || 0;
            const percentage = total > 0 ? Number(((red / total) * 100).toFixed(1)) : 0;
            const remaining = 100 - percentage;
            
            
            new Chart(reportChartCtx, {
                type: 'doughnut',
                data: {
                    labels: ['CSR Content', 'SSR Content'],
                    datasets: [{
                        data: [percentage, remaining],
                        backgroundColor: [reportCsrColor, reportSsrColor],
                        borderWidth: 0,
                    }]
                },
                options: {
                    responsive: false,
                    maintainAspectRatio: false,
                    animation: { duration: 0 },
                    // Chart.js v2 tooltip formatting
                    tooltips: {
                        callbacks: {
                            label: function(tooltipItem, data) {
                                const dataset = data.datasets[tooltipItem.datasetIndex] || { data: [] };
                                const vals = dataset.data.map(v => Number(v || 0));
                                const total = vals.reduce((a, b) => a + b, 0);
                                const current = Number(dataset.data[tooltipItem.index] || 0);
                                const pct = total > 0 ? ((current / total) * 100).toFixed(1) : '0.0';
                                const label = data.labels[tooltipItem.index] || '';
                                return `${label}: ${pct}%`;
                            }
                        }
                    },
                        legend: {
                            position: 'bottom',
                        labels: { padding: 15, fontSize: 12 }
                    }
                },
                plugins: [{
                    afterDatasetsDraw: function(chart) {
                        const ctx = chart.ctx;
                        chart.data.datasets.forEach(function(dataset, i) {
                            const meta = chart.getDatasetMeta(i);
                            meta.data.forEach(function(bar, index) {
                                const value = Number(dataset.data[index]);
                                if (value > 5) { // Only show label if slice is big enough
                                    ctx.save();
                                    const position = bar.tooltipPosition();
                                    ctx.fillStyle = 'white';
                                    ctx.font = 'bold 14px Arial';
                                    ctx.textAlign = 'center';
                                    ctx.textBaseline = 'middle';
                                    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
                                    ctx.shadowBlur = 4;
                                    ctx.fillText(Number(value).toFixed(1) + '%', position.x, position.y);
                                    ctx.restore();
                                }
                            });
                        });
                    }
                }]
            });
            
            // Recreate element chart if available
            if (currentElementReport) {
                const reportElementCtx = document.getElementById('reportElementCanvas').getContext('2d');
                
                const labels = [];
                const ssrData = [];
                const csrData = [];
                let tableHTML = '';
                
                const categoryNames = {
                    headings: 'Headings',
                    images: 'Images',
                    links: 'Links',
                    text: 'Text Content',
                    lists: 'Lists',
                    forms: 'Forms',
                    media: 'Media',
                    tables: 'Tables'
                };
                
                Object.keys(categoryNames).forEach(key => {
                    const category = currentElementReport[key];
                    if (category && (category.ssr > 0 || category.csr > 0)) {
                        labels.push(categoryNames[key]);
                        ssrData.push(category.ssr);
                        csrData.push(category.csr);
                        
                        // Add table row
                        const total = category.ssr + category.csr;
                        tableHTML += `
                            <tr>
                                <td style="padding: 6px; border: 1px solid #dee2e6;">${categoryNames[key]}</td>
                                <td style="padding: 6px; text-align: center; border: 1px solid #dee2e6; color: ${reportSsrColor};">${category.ssr}</td>
                                <td style="padding: 6px; text-align: center; border: 1px solid #dee2e6; color: ${reportCsrColor};">${category.csr}</td>
                                <td style="padding: 6px; text-align: center; border: 1px solid #dee2e6; font-weight: 600;">${total}</td>
                            </tr>
                        `;
                    }
                });
                
                // Populate table
                const tableBody = document.getElementById('reportTableBody');
                if (tableBody) {
                    tableBody.innerHTML = tableHTML;
                }
                
                new Chart(reportElementCtx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'SSR Elements',
                            data: ssrData,
                            backgroundColor: reportSsrColor,
                            stack: 'stack0'
                        }, {
                            label: 'CSR Elements',
                            data: csrData,
                            backgroundColor: reportCsrColor,
                            stack: 'stack0'
                        }]
                    },
                    options: {
                        responsive: false,
                        maintainAspectRatio: false,
                        indexAxis: 'x',
                        scales: {
                            x: { stacked: true },
                            y: { stacked: true, beginAtZero: true }
                        },
                        animation: { duration: 0 }
                    }
                });
            }
            
            // Wait for charts to render
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Capture the report container as image (Page 1 only)
            const canvas = await html2canvas(reportContainer, {
                backgroundColor: '#ffffff',
                scale: 1.25
            });
            
            // Generate PDF and add page 1 image only
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('p', 'mm', 'a4');
            const imgData1 = canvas.toDataURL('image/jpeg', 0.8);
            const imgWidth = 210; // A4 width in mm
            const imgHeight1 = (canvas.height * imgWidth) / canvas.width;
            pdf.addImage(imgData1, 'JPEG', 0, 0, imgWidth, imgHeight1, undefined, 'FAST');
            
            // Save the PDF
            const filename = `SEO-Report-${new Date().toISOString().slice(0, 10)}.pdf`;
            pdf.save(filename);
            
            // Clean up
            document.body.removeChild(reportContainer);
            
        } catch (error) {
            console.error('Error generating report:', error);
            alert('Failed to generate report. Please try again.');
        } finally {
            downloadReportBtn.disabled = false;
            downloadReportBtn.classList.remove('loading');
        }
    });
    
    // Store the latest message data for report generation
    let message = {};
    
    chrome.runtime.onMessage.addListener((msg) => {
        message = msg;
        if (message.action === 'minimapClosed') {
            // Update the toggle state when minimap is closed from the page
            minimapToggle.checked = false;
            chrome.storage.sync.set({ minimapEnabled: false });
            return;
        } else if (message.action === 'showScrollHint') {
            const hint = document.getElementById('scrollHint');
            if (hint) hint.style.display = 'block';
            // Auto-hide after 6s
            setTimeout(() => { if (hint) hint.style.display = 'none'; }, 6000);
            return;
        }
        
        const total = message.total || 0;
        const red = message.red || 0;
        const green = total - red;
        const percentage = total > 0 ? Number(((red / total) * 100).toFixed(1)) : 0;
        const remaining = 100 - percentage;
        
        // Store element report
        if (message.elementReport) {
            currentElementReport = message.elementReport;
            renderElementReport(currentElementReport);
        }
        
        if (total === 0) {
            elementCountText.textContent = `No content elements detected on this page.`;
        } else {
            elementCountText.textContent = `${red} out of ${total} content elements (${percentage}%) appear to be client-side rendered. These elements might not be indexed by search engines.`;
        }

        if (myChart) {
            myChart.destroy();
        }

        // Only create chart if there are elements to show
        if (total > 0) {
            const ctx = document.getElementById('myChart').getContext('2d');
            myChart = createDonut(ctx, percentage, remaining);
        }
    });
    });
