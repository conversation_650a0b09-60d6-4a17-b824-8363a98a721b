/* Text Time Machine - Dark/Light Theme CSS */
/* Preserves existing absolute positioning and JavaScript calculations */
/* Only changes visual styling - NO layout modifications */

:root {
  /* Modern dark theme with subtle gradients */
  --bg-primary: #0f0f0f;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #1e1e1e;
  --bg-quaternary: #2a2a2a;
  --bg-elevated: #242424;
  --bg-surface: rgba(42, 42, 42, 0.8);
  --bg-glass: rgba(30, 30, 30, 0.85);
  
  /* Refined text colors */
  --text-primary: #e2e2e2;
  --text-secondary: #a1a1aa;
  --text-tertiary: #71717a;
  --text-bright: #ffffff;
  --text-muted: #6b7280;
  
  /* Modern purple system */
  --accent-purple: #7C3AED;
  --accent-purple-hover: #8B5CF6;
  --accent-purple-soft: rgba(124, 58, 237, 0.1);
  --accent-purple-glow: rgba(124, 58, 237, 0.15);
  
  /* Subtle border system */
  --border-color: rgba(64, 64, 64, 0.6);
  --border-color-hover: rgba(100, 100, 100, 0.8);
  --border-color-focus: rgba(124, 58, 237, 0.4);
  --border-subtle: rgba(45, 45, 45, 0.8);
  
  /* Modern input system */
  --input-bg: rgba(38, 38, 38, 0.7);
  --input-bg-hover: rgba(42, 42, 42, 0.8);
  --input-bg-focus: rgba(45, 45, 45, 0.9);
  --input-border: rgba(64, 64, 64, 0.5);
  --input-border-focus: rgba(124, 58, 237, 0.6);
  
  /* Refined button system */
  --button-bg: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%);
  --button-bg-hover: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  --button-secondary: rgba(38, 38, 38, 0.8);
  --button-secondary-hover: rgba(45, 45, 45, 0.9);
  
  /* Shadow system */
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-soft: 0 2px 8px rgba(0, 0, 0, 0.15);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.2);
  --shadow-glow: 0 0 20px rgba(124, 58, 237, 0.15);
  
  /* Typography system */
  --font-mono: "Monaco","Menlo","Ubuntu Mono",monospace;
  --font-system: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  
  /* Motion system */
  --transition-speed: 0.2s;
  --transition-curve: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Radius system */
  --border-radius-sm: 4px;
  --border-radius: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  
  /* Line numbers system */
  --line-num-bg: #2a2a2a;
  --line-num-text: #a1a1aa;
  
  /* Spacing system */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 24px;
}

[data-theme="light"] {
  /* Light theme variables */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f8f8;
  --bg-tertiary: #e9ecef;
  --bg-quaternary: #d1d5db;
  --bg-elevated: #f3f4f6;
  --bg-surface: rgba(229, 229, 229, 0.8);
  --bg-glass: rgba(243, 244, 246, 0.85);
  
  --text-primary: #000000;
  --text-secondary: #777777;
  --text-tertiary: #9ca3af;
  --text-bright: #000000;
  --text-muted: #6b7280;
  
  --accent-purple: #7C3AED;
  --accent-purple-hover: #8B5CF6;
  --accent-purple-soft: rgba(124, 58, 237, 0.1);
  --accent-purple-glow: rgba(124, 58, 237, 0.15);
  
  --border-color: rgba(128, 128, 128, 0.6);
  --border-color-hover: rgba(100, 100, 100, 0.8);
  --border-color-focus: rgba(124, 58, 237, 0.4);
  --border-subtle: rgba(200, 200, 200, 0.8);
  
  --input-bg: rgba(229, 229, 229, 0.7);
  --input-bg-hover: rgba(209, 213, 219, 0.8);
  --input-bg-focus: rgba(203, 213, 225, 0.9);
  --input-border: rgba(156, 163, 175, 0.5);
  --input-border-focus: rgba(124, 58, 237, 0.6);
  
  --button-bg: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%);
  --button-bg-hover: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  --button-secondary: rgba(229, 229, 229, 0.8);
  --button-secondary-hover: rgba(209, 213, 219, 0.9);
  
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-soft: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(124, 58, 237, 0.1);
  
  --line-num-bg: #d1d5db;
  --line-num-text: #777777;
}

/* Theme toggle button */
.theme-toggle {
  background: var(--button-bg);
  color: #ffffff;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  padding: var(--space-sm) var(--space-md);
  cursor: pointer;
  font-size: 12px;
  font-family: var(--font-system);
  font-weight: var(--font-weight-semibold);
  margin: var(--space-xs);
  transition: all var(--transition-speed) var(--transition-curve);
  height: 32px;
  line-height: 1.2;
  box-shadow: var(--shadow-subtle);
  backdrop-filter: blur(8px);
}

.theme-toggle:hover {
  background: var(--button-bg-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium), var(--shadow-glow);
}

/* Body and basic elements - preserve all positioning */
body {
  height: 100%;
  margin: 0px;
  padding: 0px;
  font-family: var(--font-system);
  font-size: 16px;
  line-height: 1.7;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color .3s, color .3s;
}

html {
  height: 100%;
  background-color: var(--bg-primary);
}

#html {
  height: 100%;
}

/* Controls bar - preserve positioning */
#controls {
  position: absolute;
  top: 00px;
  left: 0px;
  width: 100%;
  line-height: 1.1;
  font-size: 14px;
  background: linear-gradient(to bottom, var(--bg-secondary), var(--bg-tertiary));
  border-bottom: 1px solid var(--border-subtle);
  color: var(--text-primary);
  height: 48px;
  backdrop-filter: blur(12px);
  box-shadow: var(--shadow-soft);
}

#control_box {
  padding: var(--space-sm) var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  height: 100%;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Functions panel - preserve positioning */
#functions {
  position: absolute;
  top: 48px!important;
  left: 0px;
  width: 350px;
  height: 70%;
  overflow: auto;
  background: linear-gradient(to bottom, var(--bg-secondary), var(--bg-tertiary));
  border-left: 2px solid var(--accent-purple);
  color: var(--text-primary);
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(12px);
}

#functions_box {
  padding: 60px var(--space-lg) var(--space-lg) var(--space-lg);
  position: relative;
}

/* Line numbers - preserve positioning with modern styling */
#line_box_div {
  position: absolute;
  top: 48px!important;
  left: 0px;
  display: block;
  width: 56px;
  margin: 0px;
  padding: 0px 8px 0px 4px;
  overflow: hidden;
  overflow-x: scroll;
  white-space: pre;
  text-align: right;
  font-family: arial;
  font-size: 16px;
  font-weight: var(--font-weight-medium);
  letter-spacing: 0px;
  line-height: 1.5;
  color: var(--line-num-text);
  border: 0px;
  background: linear-gradient(to right, var(--line-num-bg), var(--bg-tertiary));
  border-right: 1px solid var(--border-color);
  backdrop-filter: blur(4px);
  box-shadow: inset -1px 0 0 rgba(124, 58, 237, 0.1);
  transition: all var(--transition-speed) var(--transition-curve);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
}

/* Line numbers hover effect */
#line_box_div:hover {
  background: linear-gradient(to right, var(--bg-tertiary), var(--bg-quaternary));
  box-shadow: inset -1px 0 0 rgba(124, 58, 237, 0.2);
}

/* Text box - preserve positioning */
#text_box {
  position: absolute;
  top: 48px!important;
  left: 56px;
  display: block;
  width: 96%;
  margin: 0px;
  padding: 0px 0px 0px 3px;
  overflow: scroll;
  font-family: arial;
  font-size: 16px;
  letter-spacing: 0px;
  line-height: 1.5;
  border: 0px;
  background-color: transparent !important;
  color: var(--text-primary);
}

#text_box_div {
  position: absolute;
  top: 0px;
  left: 56px;
  z-index: -1;
  display: block;
  width: 96%;
  margin: 0px;
  padding: 0px 0px 0px 3px;
  overflow: scroll;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: arial;
  font-size: 16px;
  letter-spacing: 0px;
  line-height: 1.5;
  border: 0px;
  color: transparent;
  background-color: var(--bg-primary);
}

/* Dummy divs - preserve positioning */
#line_dummy_div {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: -10;
  display: block;
  margin: 0px;
  padding: 0px;
  overflow: hidden;
  white-space: pre-wrap;
  font-family: arial;
  font-size: 16px;
  letter-spacing: 0px;
  line-height: 1.5;
  border: 0px;
  color: transparent;
  background-color: var(--bg-primary);
}

#line_dummy_txt {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: -10;
  display: block;
  width: 10px;
  margin: 0px;
  padding: 0px;
  overflow: hidden;
  overflow-x: scroll;
  font-family: arial;
  font-size: 16px;
  letter-spacing: 0px;
  line-height: 1.5;
  border: 0px;
  color: transparent;
  background-color: var(--bg-primary);
}

#text_box_div_dummy {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: -10;
  display: block;
  width: 96%;
  height: 100px;
  margin: 0px;
  padding: 0px 0px 0px 3px;
  overflow: scroll;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: arial;
  font-size: 16px;
  letter-spacing: 0px;
  line-height: 150px;
  border: 0px;
  color: transparent;
  background-color: transparent;
}

#text_box_dummy {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: -10;
  display: block;
  width: 96%;
  height: 100px;
  margin: 0px;
  padding: 0px 0px 0px 3px;
  overflow: scroll;
  font-family: arial;
  font-size: 16px;
  letter-spacing: 0px;
  line-height: 150px;
  border: 0px;
  color: transparent;
  background-color: transparent;
}

/* Navigation and other panels */
#nav_functions {
  position: absolute;
  top: 40px;
  left: 0px;
  width: 350px;
  overflow: auto;
  background-color: var(--bg-secondary);
}

#nav_functions_box {
  padding: 16px;
}

#menu {
  position: absolute;
  z-index: -1;
  left: 0px;
  top: 50px;
  width: 0px;
  height: 0%;
  margin: 0px;
  overflow: auto;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
}

#wrdfrq {
  position: absolute;
  z-index: -6;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
  overflow: auto;
  background-color: var(--bg-secondary);
  border-right: 2px solid var(--border-color);
}

/* Input elements styling */
input {
  display: inline-block;
  height: 33px;
  line-height: 1;
  vertical-align: middle;
  font-size: 16px;
  outline: none;
  resize: none;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: var(--text-primary);
}

input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input[type='button'] {
  width: auto;
  margin: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  font-family: var(--font-system);
  font-weight: var(--font-weight-semibold);
  font-size: 12px;
  color: white;
  border: 1px solid transparent;
  background: var(--button-bg);
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: all var(--transition-speed) var(--transition-curve);
  position: relative;
  overflow: hidden;
  height: 32px;
  line-height: 1.2;
  box-shadow: var(--shadow-subtle);
  backdrop-filter: blur(8px);
}

input[type='button']::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

input[type='button']:hover {
  background: var(--button-bg-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium), var(--shadow-glow);
}

input[type='button']:hover::before {
  left: 100%;
}

input[type='button']:active {
  transform: translateY(0);
  box-shadow: var(--shadow-subtle);
}

input[type='button']:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  background: var(--button-secondary);
  box-shadow: none;
  color: var(--text-tertiary);
}

input[type='text'] {
  width: 100%;
  height: auto;
  min-height: 32px;
  overflow: auto;
  
  padding: var(--space-sm) var(--space-md);
  font-family: var(--font-system);
  font-size: 14px;
  font-weight: var(--font-weight-medium);
  color: var(--text-bright);
  background: var(--bg-quaternary);
  backdrop-filter: blur(8px);
  border: 1px solid var(--input-border);
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: var(--border-radius);
  transition: all var(--transition-speed) var(--transition-curve);
  box-shadow: var(--shadow-subtle);
}

input[type='text']:hover {
  background: var(--bg-surface);
  border-color: var(--border-color-hover);
  box-shadow: var(--shadow-soft);
}

input[type='text']:focus {
  outline: none;
  background: var(--bg-elevated);
  border-color: var(--input-border-focus);
  box-shadow: 0 0 0 3px var(--accent-purple-soft), var(--shadow-soft);
}

input[type='text']::placeholder {
  color: var(--text-muted);
  font-size: 13px;
  font-weight: var(--font-weight-normal);
}

input[type='checkbox'], input[type='radio'] {
  width: 18px;
  height: 18px;
  vertical-align: middle;
  margin: 0px 8px 0px 4px;
  padding: 0px;
  font-size: 16px;
  cursor: pointer;
  accent-color: var(--accent-purple);
  background: var(--bg-quaternary) !important;
  border: 2px solid var(--input-border) !important;
  border-radius: var(--border-radius-sm) !important;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
  transition: all var(--transition-speed) var(--transition-curve);
}

input[type='checkbox']:checked, input[type='radio']:checked {
  background: var(--accent-purple) !important;
  border-color: var(--accent-purple) !important;
}

input[type='checkbox']:checked::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

input[type='radio']:checked::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

input[type='checkbox']:hover, input[type='radio']:hover {
  border-color: var(--border-color-hover) !important;
  box-shadow: var(--shadow-subtle);
}

textarea {
  display: block;
  margin: var(--space-xs);
  padding: var(--space-md);
  overflow: scroll;
  font-family: var(--font-system);
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-bright);
  background: var(--bg-quaternary);
  backdrop-filter: blur(8px);
  border: 1px solid var(--input-border);
  outline: none;
  resize: none;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: var(--border-radius);
  transition: all var(--transition-speed) var(--transition-curve);
  box-shadow: var(--shadow-subtle);
}

textarea:hover {
  background: var(--bg-surface);
  border-color: var(--border-color-hover);
  box-shadow: var(--shadow-soft);
}

textarea:focus {
  background: var(--bg-elevated);
  border-color: var(--input-border-focus);
  box-shadow: 0 0 0 3px var(--accent-purple-soft), var(--shadow-soft);
}

/* Headers and text styling */
h1 {
  display: block;
  font-size: 18px;
  font-weight: 700;
  margin: 0px 0px 16px 0px;
  padding: 0px;
  color: var(--text-bright);
  letter-spacing: -0.5px;
}

/* Links */
a.control_text_link {
  cursor: pointer;
  color: var(--accent-purple);
  text-decoration: none;
}

a.control_text_link:hover {
  text-decoration: underline;
}

span.control_text {
  cursor: pointer;
  color: var(--accent-purple);
}

span.control_text:hover {
  text-decoration: underline;
}

/* Dividers */
div.hr {
  width: 100%;
  height: 1px;
  margin: 16px 0px;
  background: linear-gradient(to right, transparent, var(--border-color), transparent);
}

div.tptp_5 {
  padding: var(--space-md) 0px;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex-wrap: wrap;
  line-height: 1.4;
  font-family: var(--font-system);
}

/* Tables */
table {
  border-collapse: collapse;
}

table, td {
  padding: 0px;
}

td.wfctd {
  padding: 8px 12px;
  border: 1px solid var(--input-border);
  background: var(--bg-quaternary);
  color: var(--text-bright);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-subtle);
}

/* Special character spans */
span.spcar {
  display: inline-block;
  padding: 5px 10px 5px 10px;
  font-size: 18px;
  cursor: default;
  color: var(--text-primary);
}

span.spcar:hover {
  background-color: var(--accent-purple);
  color: white;
}

/* Count display */
#countdisplay td {
  padding: 6px 10px;
  border: 1px solid var(--input-border);
  background: var(--bg-quaternary);
  color: var(--text-bright);
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  line-height: 1.2;
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-subtle);
}

/* Special styling elements */
span.name, span.nameclick {
  display: inline-block;
  height: auto;
  line-height: 44px;
  font-size: 20px;
  font-weight: 600;
  padding: 0px 20px;
  text-align: center;
  background: linear-gradient(135deg, var(--accent-purple), var(--accent-purple-hover));
  border: 1px solid var(--accent-purple);
  border-radius: var(--border-radius);
  vertical-align: middle;
  color: white;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  transition: all var(--transition-speed) ease;
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.2);
}

span.nameclick {
  cursor: pointer;
}

span.nameclick:hover {
  background-color: var(--button-hover);
  border: 1px solid var(--button-hover);
}

/* Top notification bar */
#note {
  position: fixed;
  z-index: 101;
  top: 0;
  left: 0;
  right: 0;
  background: var(--bg-quaternary);
  text-align: center;
  line-height: 2.5;
  overflow: hidden;
  -webkit-box-shadow: 0 0 5px var(--shadow-color);
  -moz-box-shadow: 0 0 5px var(--shadow-color);
  box-shadow: 0 0 5px var(--shadow-color);
  color: var(--text-bright);
  height: 40px;
  border-bottom: 1px solid var(--border-color);
}

#note a {
  color: var(--accent-purple);
}

#note1, #note2, #note3 {
  width: 15%;
  display: inline-block;
  top: 50%;
  transform: translateY(-50%);
  position: relative;
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-purple);
}

/* File input styling */
input[type='file'] {
  display: inline-block;
  width: 92px;
  border-radius: 3px;
  overflow: hidden;
  padding: 0px;
  margin: 0px 0px 0px -92px;
  -moz-opacity: 0;
  opacity: 0;
  cursor: pointer;
}

/* Additional specific elements */
#jtbox {
  display: inline-block;
  margin: 5px 0px 0px 0px;
  padding: 8px 12px;
  font-family: var(--font-system);
  font-size: 14px;
  font-weight: var(--font-weight-medium);
  background: var(--bg-quaternary);
  border: 1px solid var(--input-border);
  outline: none;
  border-radius: var(--border-radius);
  color: var(--text-bright);
  box-shadow: var(--shadow-subtle);
}

#indextext {
  height: 150px;
  padding: 12px 16px;
  overflow-y: scroll;
  line-height: 1.6;
  font-size: 14px;
  font-weight: var(--font-weight-medium);
  background: var(--bg-quaternary);
  border: 1px solid var(--input-border);
  border-radius: var(--border-radius-md);
  color: var(--text-bright);
  box-shadow: var(--shadow-subtle);
  font-family: var(--font-system);
}

/* Responsive adjustments */
@media (max-width: 800px) {
  #note {
    line-height: 1;
    font-size: 80%;
    padding-bottom: 5px;
  }
  #note1, #note2, #note3 {
    width: 30%;
  }
}

/* Ensure all elements inherit theme properly */
div {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/* Additional navigation elements */
div.navcat {
  padding: 16px 0px 8px 16px;
  font-size: 18px;
  font-weight: 700;
  font-style: normal;
  color: var(--text-bright);
  letter-spacing: -0.3px;
  background: var(--bg-tertiary);
  border-radius: var(--border-radius);
  margin-bottom: 8px;
}

div.navdiv {
  height: 2px;
  padding: 0px;
  margin: 18px 10px 13px 10px;
  background-color: var(--border-color);
}

div.subtool {
  width: 100%;
  margin: 16px 0px;
  padding: 12px 16px;
  font-style: normal;
  font-weight: 600;
  color: #FFFFFF;
  background: linear-gradient(135deg, var(--accent-purple), var(--accent-purple-hover));
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.2);
}

a.nav {
  display: inline-block;
  padding: 0px;
  margin: 10px 0px 10px 10px;
  text-decoration: underline;
  color: var(--text-primary);
}

/* Body padding */
#bodypadding {
  padding: 10px 10px 0px 10px;
  margin-top: 40px;
}

#conbtn {
  position: absolute;
  z-index: 4;
  top: 0px;
  left: 0px;
}

/* Controls styling */
div.controls {
  display: block;
  padding-bottom: 4px;
}

#counters {
  padding-top: 2px;
  text-align: left;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* Compact counter styling */
.counter-group {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: var(--text-secondary);
}

.counter-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.counter-value {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background: var(--bg-glass);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-subtle);
  min-width: 32px;
  text-align: center;
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-subtle);
  transition: all var(--transition-speed) var(--transition-curve);
}

.counter-value:hover {
  background: var(--bg-elevated);
  border-color: var(--border-color-hover);
  box-shadow: var(--shadow-soft);
}

#footer {
  padding: 0px 4px 4px 4px;
}

/* Input divs */
#inptdiv {
  width: 100%;
  overflow-x: scroll;
  white-space: nowrap;
  padding: 4px 0px 3px 0px;
}

/* Line classes */
.mline {
  padding: 1px 0px 1px 0px;
}

/* Tally inputs */
input[type='text'].tally, input[type='text'].tallyclick {
  height: 32px;
  line-height: 1.3;
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  letter-spacing: 0.5px;
  padding: var(--space-sm) var(--space-md);
  text-align: center;
  border: 1px solid var(--input-border);
  background: var(--bg-quaternary);
  color: var(--text-bright);
  border-radius: var(--border-radius);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-subtle);
  transition: all var(--transition-speed) var(--transition-curve);
  min-width: 50px;
}

input[type='text'].tallyclick {
  cursor: pointer;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

input[type='text'].tally:hover, input[type='text'].tallyclick:hover {
  background: var(--bg-surface);
  border-color: var(--border-color-hover);
  box-shadow: var(--shadow-soft);
}

input[type='text'].tallyclick:hover {
  box-shadow: var(--shadow-soft), 0 0 0 2px var(--accent-purple-soft);
}

/* Button variants */
input[type='button'].cbtnm, input[type='button'].cbtnp {
  height: 28px;
  line-height: 1.2;
  font-size: 14px;
  padding: 6px 10px;
  cursor: pointer;
}

input[type='button']:hover.cbtnm, input[type='button']:hover.cbtnp {
  background-color: var(--button-hover);
}

/* Special spans */
span.cd {
  cursor: default;
}

span.hlct {
  display: inline-block;
  width: 65px;
  margin: 2px 0px 2px 0px;
  padding: 4px 8px;
  background: var(--bg-quaternary);
  border: 1px solid var(--input-border);
  border-radius: var(--border-radius);
  color: var(--text-bright);
  font-weight: var(--font-weight-medium);
  text-align: center;
  box-shadow: var(--shadow-subtle);
}

/* Table rows */
tr.wfctr {
  background: var(--bg-quaternary);
}

td.wfctdhl {
  background-color: var(--accent-purple);
  color: white;
}

/* Options */
option.padng {
  padding: 4px 12px;
  background: var(--bg-quaternary);
  color: var(--text-bright);
  font-weight: var(--font-weight-medium);
}

/* Modern navigation tabs styling */
.nav-tab, .control_text_link {
  display: inline-block;
  padding: 8px 16px;
  margin: 4px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all var(--transition-speed) ease;
  cursor: pointer;
}

.nav-tab:hover, .control_text_link:hover {
  background: var(--accent-purple);
  border-color: var(--accent-purple);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.2);
  text-decoration: none;
}

.nav-tab.active {
  background: var(--accent-purple);
  border-color: var(--accent-purple);
  color: white;
}

/* Improved top bar styling */
#note {
  position: fixed;
  z-index: 101;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to bottom, var(--bg-secondary), var(--bg-primary));
  text-align: center;
  line-height: 2.5;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
  color: var(--text-bright);
  height: 40px;
  border-bottom: 2px solid var(--accent-purple);
  font-family: var(--font-system);
  font-weight: 600;
}

/* Button groups */
.button-group {
  display: flex;
  gap: 8px;
  margin: 8px 0;
  flex-wrap: wrap;
}

.button-group input[type='button'] {
  flex: 0 0 auto;
  margin: 0;
}

/* Enhanced file input styling */
.file-input-wrapper {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.file-input-wrapper input[type='button'] {
  margin-right: 0;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Improved section styling */
.function-section {
  background: var(--bg-tertiary);
  border-radius: var(--border-radius);
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid var(--border-color);
}

.function-section:hover {
  border-color: var(--accent-purple);
  box-shadow: 0 0 0 1px rgba(124, 58, 237, 0.1);
}

/* Responsive button layout improvements */
@media (min-width: 400px) {
  .tptp_5 {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
    padding: 16px 0px;
  }
  
  .tptp_5 input[type='button'] {
    margin: 0;
    flex-shrink: 0;
  }
  
  .tptp_5 input[type='text'] {
    margin: 0;
    flex: 1 1 auto;
    min-width: 120px;
  }
}

/* Improved text elements */
.tptp_5 {
  line-height: 1.6;
  font-family: var(--font-system);
}

/* Better checkbox and radio spacing */
input[type='checkbox'] + label,
input[type='radio'] + label {
  margin-left: 4px;
  font-weight: 500;
  cursor: pointer;
}

/* Enhanced special character display */
span.spcar {
  display: inline-block;
  padding: 8px 12px;
  font-size: 16px;
  cursor: pointer;
  color: var(--text-primary);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin: 2px;
  transition: all var(--transition-speed) ease;
}

span.spcar:hover {
  background-color: var(--accent-purple);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
}

/* Navigation improvements */
a.nav {
  display: inline-block;
  padding: 8px 16px;
  margin: 8px 0px 8px 16px;
  text-decoration: none;
  color: var(--accent-purple);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: all var(--transition-speed) ease;
  font-weight: 500;
}

a.nav:hover {
  background: var(--accent-purple);
  color: white;
  border-color: var(--accent-purple);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.2);
}

/* Final modern enhancements */
.modern-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-subtle);
  border-radius: var(--border-radius-md);
  padding: var(--space-lg);
  backdrop-filter: blur(12px);
  box-shadow: var(--shadow-soft);
  transition: all var(--transition-speed) var(--transition-curve);
}

.modern-card:hover {
  box-shadow: var(--shadow-medium);
  border-color: var(--border-color-hover);
}

/* Enhanced scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-quaternary);
  border-radius: var(--border-radius-sm);
  transition: background var(--transition-speed) ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-purple);
}

/* Professional focus outline system */
*:focus-visible {
  outline: 2px solid var(--accent-purple);
  outline-offset: 2px;
}

/* Enhanced text selection */
::selection {
  background: var(--accent-purple-soft);
  color: var(--text-bright);
}

/* Critical override for inline styled counter inputs */
#char_cnt, #word_cnt, #sent_cnt, #line_cnt, #col_cnt {
  background: var(--bg-quaternary) !important;
  color: var(--text-bright) !important;
  border: 1px solid var(--input-border) !important;
  border-radius: var(--border-radius) !important;
  padding: var(--space-sm) var(--space-md) !important;
  font-family: var(--font-system) !important;
  font-size: 14px !important;
  font-weight: var(--font-weight-semibold) !important;
  text-align: center !important;
  box-shadow: var(--shadow-subtle) !important;
  backdrop-filter: blur(8px) !important;
  transition: all var(--transition-speed) var(--transition-curve) !important;
  height: 32px !important;
  line-height: 1.2 !important;
  min-width: 50px !important;
}

#char_cnt:hover, #word_cnt:hover, #sent_cnt:hover, #line_cnt:hover, #col_cnt:hover {
  background: var(--bg-surface) !important;
  border-color: var(--border-color-hover) !important;
  box-shadow: var(--shadow-soft) !important;
}

/* Line highlighting for text editor */
.tbd-highlighted {
  background-color: #505050 !important;
  padding: 2px 8px;
  margin: -2px -8px;
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-subtle);
  position: relative;
}

[data-theme="light"] .tbd-highlighted {
  background-color: #FFFFCC !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Ensure cursor is visible and white in text editor */
#text_box {
  caret-color: white !important;
}

[data-theme="light"] #text_box {
  caret-color: black !important;
}

/* Sidebar Search Functionality */
.sidebar-search-container {
  position: absolute;
  top: 0px;
  left: 0;
  right: 0;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  z-index: 10;
}

.sidebar-search {
  width: 100%;
  padding: 8px 32px 8px 12px;
  background: var(--bg-secondary);
  border: 1px solid rgba(124, 58, 237, 0.3);
  border-radius: var(--border-radius-md);
  color: var(--text-primary);
  font-size: 12px;
  font-family: inherit;
  transition: all var(--transition-speed) var(--transition-curve);
}

.sidebar-search:focus {
  outline: none !important;
  border-color: rgba(124, 58, 237, 0.3) !important;
  box-shadow: none !important;
}

.sidebar-search:focus-visible {
  outline: none !important;
}

/* Override focus states for main text editor */
#text_box:focus,
#text_box:focus-visible,
#text_box:active {
  outline: none !important;
  border: 0px !important;
  box-shadow: none !important;
}

.sidebar-search::placeholder {
  color: var(--text-secondary);
  font-size: 11px;
}

.sidebar-search-clear {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-speed) var(--transition-curve);
  opacity: 0;
  pointer-events: none;
}

.sidebar-search-container:hover .sidebar-search-clear,
.sidebar-search:focus + .sidebar-search-clear,
.sidebar-search-clear.visible {
  opacity: 1;
  pointer-events: all;
}

.sidebar-search-clear:hover {
  background: rgba(124, 58, 237, 0.2);
  color: #7C3AED;
  transform: translateY(-50%) scale(1.1);
}

/* Search highlighting for h1 sections */
#functions_box h1.search-highlight {
  color: #7C3AED !important;
  background: rgba(124, 58, 237, 0.1);
  border-radius: var(--border-radius-sm);
  padding: 4px 8px;
  margin: 4px -8px;
  transition: all var(--transition-speed) var(--transition-curve);
}

#functions_box h1.search-dimmed {
  opacity: 0.3;
  transition: opacity var(--transition-speed) var(--transition-curve);
}

/* Section containers during search */
#functions_box > div.search-highlight {
  background: rgba(124, 58, 237, 0.05);
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(124, 58, 237, 0.2);
  margin: 8px 0;
  padding: 8px;
  transition: all var(--transition-speed) var(--transition-curve);
}

#functions_box > div.search-dimmed {
  opacity: 0.3;
  transition: opacity var(--transition-speed) var(--transition-curve);
}

/* Control box separators */
.control-separator {
  width: 1px;
  height: 20px;
  background: var(--text-secondary);
  opacity: 0.8;
  margin: 0 8px;
  display: inline-block;
  vertical-align: middle;
}

/* Light theme adjustments for search */
[data-theme="light"] .sidebar-search {
  background: var(--bg-primary);
  border-color: rgba(124, 58, 237, 0.4);
}

[data-theme="light"] #functions_box h1.search-highlight {
  background: rgba(124, 58, 237, 0.15);
}

[data-theme="light"] #functions_box > div.search-highlight {
  background: rgba(124, 58, 237, 0.08);
  border-color: rgba(124, 58, 237, 0.3);
}