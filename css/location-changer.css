.location-changer-accordion {
    margin-bottom: 16px!important;
    background: #1a1a1a;
    border-radius: 12px;
    border: 1px solid #2a2a2a;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', '<PERSON><PERSON>', 'Helvetica Neue', Arial, sans-serif;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Hidden state for settings toggle */
.location-changer-accordion.location-changer-hidden {
    opacity: 0;
    transform: translateY(-10px);
    pointer-events: none;
}

.location-changer-header {
    padding: 16px;
    cursor: pointer;
    background: #1a1a1a;
    border-radius: 12px 12px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.location-changer-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left 0.5s;
}

.location-changer-header:hover::before {
    left: 100%;
}

.location-changer-header:hover {
    background: #262626;
    transform: translateY(-1px);
}

.location-changer-header.expanded {
    border-radius: 12px 12px 0 0;
    background: #262626;
}

.location-changer-title {
    font-weight: 600;
    font-size: 14px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: color 0.2s ease;
    color: #ffffff; /* White by default */
}

/* Only turn grey when location overwrite is disabled (unchecked) */
.location-changer-accordion:not(.active) .location-changer-title {
    color: #9ca3af;
}

/* Current Location Display Styles */
.current-location-display {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 0 12px 0 8px;
    overflow: hidden;
}

.current-location-text {
    font-size: 11px;
    font-weight: 400;
    color: #6b7280;
    text-align: right;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
    opacity: 0.8;
    font-style: italic;
    transition: color 0.2s ease, opacity 0.2s ease;
}

/* Active state - slightly brighter */
.location-changer-accordion.active .current-location-text {
    color: #9ca3af;
    opacity: 0.9;
}

/* Inactive state - more subtle */
.location-changer-accordion:not(.active) .current-location-text {
    color: #6b7280;
    opacity: 0.7;
}

/* Hover effect for the entire header */
.location-changer-header:hover .current-location-text {
    opacity: 1;
    color: #a1a1aa;
}

.location-changer-icon {
    transition: transform 0.2s ease;
    color: #9ca3af;
    font-size: 12px;
    font-weight: bold;
}

.location-changer-icon.expanded {
    transform: rotate(180deg);
}

.location-changer-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: rgb(15, 15, 15);
}

.location-changer-content.expanded {
    max-height: 100%;
}

.location-changer-body {
    padding: 16px;
    border-top: 1px solid #2a2a2a;
}

.location-input-group {
    margin-bottom: 12px;
}

.location-input-group:last-child {
    margin-bottom: 0;
}

.location-input-row {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.location-input-wrapper {
    flex: 1;
    position: relative;
}

.location-input-label {
    display: block;
    font-size: 11px;
    font-weight: 600;
    color: #9ca3af;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.location-input {
    width: 100%;
    padding: 10px 16px;
    background: #262626;
    border: 1px solid #333333;
    border-radius: 8px;
    font-size: 13px;
    color: #ffffff;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
    font-family: inherit;
}

/* Active state inputs - pure white text */
.location-changer-accordion.active .location-input {
    color: #ffffff;
}

/* Inactive state inputs - greyish text */
.location-changer-accordion:not(.active) .location-input {
    color: #9ca3af;
}

.location-input:focus {
    outline: none;
    border-color: #4285f4;
    background: #2a2a2a;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
    color: #ffffff; /* Always white when focused */
}

.location-input::placeholder {
    transition: color 0.2s ease;
}

/* Active state placeholders - lighter grey */
.location-changer-accordion.active .location-input::placeholder {
    color: #6b7280;
}

/* Inactive state placeholders - darker grey */
.location-changer-accordion:not(.active) .location-input::placeholder {
    color: #4b5563;
}

.location-input.text-danger {
    border-color: #ef4444;
    background-color: #2a1a1a;
}

.location-input.text-danger::placeholder {
    color: #ef4444;
}

.location-checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
}

.location-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: #7C3AED;
}

.location-checkbox-label {
    font-size: 13px;
    cursor: pointer;
    user-select: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

/* Active state checkbox label - pure white */
.location-changer-accordion.active .location-checkbox-label {
    color: #ffffff;
}

/* Inactive state checkbox label - greyish */
.location-changer-accordion:not(.active) .location-checkbox-label {
    color: #9ca3af;
}

/* Check Location Button */
.check-location-btn {
    background: #4a5568;
    color: #ffffff;
    border: 1px solid #6b7280;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    outline: none;
    font-family: inherit;
}

.check-location-btn:hover {
    background: #5a6679;
    border-color: #9ca3af;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.check-location-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.location-status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #22c55e;
    margin-right: 6px;
    transition: background-color 0.2s ease;
}

.location-status-indicator.disabled {
    background-color: #6b7280;
}

/* Typeahead dropdown styling - Dark theme */
.twitter-typeahead {
    width: 100% !important;
    flex: none !important;
}

.tt-menu {
    width: 100%;
    background: #1a1a1a;
    border: 1px solid #333333;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.tt-suggestion {
    padding: 10px 16px;
    font-size: 12px;
    cursor: pointer;
    border-bottom: 1px solid #2a2a2a;
    transition: background-color 0.15s ease;
    color: #e5e5e5;
}

.tt-suggestion:hover,
.tt-suggestion.tt-cursor {
    background-color: #262626;
}

.tt-suggestion:last-child {
    border-bottom: none;
}

.location-suggestion-name {
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 2px;
}

.location-suggestion-details {
    font-size: 11px;
    color: #9ca3af;
}

/* Custom scrollbar for typeahead */
.tt-menu::-webkit-scrollbar {
    width: 8px;
}

.tt-menu::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
}

.tt-menu::-webkit-scrollbar-thumb {
    background: #333333;
    border-radius: 4px;
}

.tt-menu::-webkit-scrollbar-thumb:hover {
    background: #404040;
}

/* Responsive design */
@media (max-width: 500px) {
    .location-input-row {
        flex-direction: column;
        gap: 4px;
    }
    
    .location-changer-body {
        padding: 12px;
    }
}

/* Animation for status indicator */
.location-status-indicator:not(.disabled) {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
}

/* Location Favorites Section */
.location-favorites-section {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #333333;
}

.location-favorites-header {
    margin-bottom: 16px;
}

.location-favorites-title {
    font-size: 11px;
    font-weight: 600;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.location-favorites-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.location-favorites-row {
    display: flex;
    gap: 8px;
    align-items: center;
}

.location-favorites-input {
    flex: 1;
    font-size: 13px !important;
    padding: 10px 16px !important;
    min-height: unset;
}

.location-favorites-select {
    flex: 1;
    font-size: 13px !important;
    padding: 10px 16px !important;
    min-height: unset;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px !important;
    appearance: none;
}

.location-favorites-btn {
    background: #4a5568;
    color: #ffffff;
    border: 1px solid #6b7280;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    outline: none;
    font-family: inherit;
    white-space: nowrap;
    min-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 38px;
}

.location-favorites-btn:hover:not(:disabled) {
    background: #5a6679;
    border-color: #9ca3af;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.location-favorites-btn:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.location-save-btn {
    background: #4a5568;
    border-color: #6b7280;
}

.location-save-btn:hover:not(:disabled) {
    background: #5a6679;
    border-color: #9ca3af;
}

.location-load-btn {
    background: #4a5568;
    border-color: #6b7280;
}

.location-load-btn:hover:not(:disabled) {
    background: #5a6679;
    border-color: #9ca3af;
}

.location-delete-btn {
    background: #4a5568;
    border-color: #6b7280;
    font-size: 16px;
    font-weight: 400;
    min-width: 40px;
    padding: 8px 12px;
    color: #ef4444;
}

.location-delete-btn:hover:not(:disabled) {
    background: #5a6679;
    border-color: #9ca3af;
    color: #f87171;
}

.location-delete-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: #4a5568;
    border-color: #6b7280;
    color: #6b7280;
}

.location-favorites-message {
    font-size: 12px;
    margin-top: 12px;
    padding: 8px 16px;
    border-radius: 6px;
    text-align: center;
    transition: all 0.2s ease;
    min-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.location-favorites-message-success {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.location-favorites-message-error {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.location-favorites-message-info {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Active state styles for favorites section */
.location-changer-accordion.active .location-favorites-title {
    color: #ffffff;
}

.location-changer-accordion:not(.active) .location-favorites-title {
    color: #9ca3af;
}

.location-changer-accordion.active .location-favorites-input,
.location-changer-accordion.active .location-favorites-select {
    color: #ffffff;
}

.location-changer-accordion:not(.active) .location-favorites-input,
.location-changer-accordion:not(.active) .location-favorites-select {
    color: #9ca3af;
}

.location-changer-accordion.active .location-favorites-input::placeholder,
.location-changer-accordion.active .location-favorites-select::placeholder {
    color: #6b7280;
}

.location-changer-accordion:not(.active) .location-favorites-input::placeholder,
.location-changer-accordion:not(.active) .location-favorites-select::placeholder {
    color: #4b5563;
}

/* Responsive adjustments for favorites */
@media (max-width: 500px) {
    .location-favorites-row {
        flex-direction: column;
        gap: 4px;
    }
    
    .location-favorites-btn {
        width: 100%;
    }
}