#gmb-services-docked-interface {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 430px;
  max-height: 90vh;
  background: #0f0f0f;
  color: #e5e5e5;
  border: 1px solid #2a2a2a;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  z-index: 999999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
  backdrop-filter: blur(10px);
  pointer-events: auto;
  user-select: none;
}

.gmb-services-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #1a1a1a;
  border-bottom: 1px solid #2a2a2a;
}

.gmb-services-header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.gmb-services-close-btn {
  background: #262626;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
  color: #ffffff;
  font-weight: bold;
  transition: all 0.2s ease;
  z-index: 10001;
  position: relative;
}

.gmb-services-close-btn:hover {
  background: #ff6666;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(255, 102, 102, 0.3);
}

.gmb-services-close-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2px 6px rgba(255, 102, 102, 0.2);
}

.gmb-services-content {
  padding: 20px;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

.gmb-services-content::-webkit-scrollbar {
  width: 8px;
}

.gmb-services-content::-webkit-scrollbar-track {
  background: #262626;
  border-radius: 4px;
}

.gmb-services-content::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

.gmb-services-status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #262626;
  border-radius: 8px;
  border: 1px solid #333333;
}

.gmb-services-status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ef4444;
}

.gmb-services-status-indicator.active {
  background: #22c55e;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.gmb-services-status-text {
  color: #d1d5db;
  font-size: 14px;
  font-weight: 500;
}

.gmb-services-settings {
  margin-bottom: 20px;
  padding: 20px;
  background: #262626;
  border-radius: 8px;
  border: 1px solid #333333;
}

.gmb-services-setting {
  display: flex;
  flex-direction: column;
  gap: 8px;
  cursor: pointer;
}

.gmb-services-setting-label {
  color: #d1d5db;
  font-size: 14px;
  font-weight: 500;
}

.gmb-services-text-input {
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.gmb-services-text-input:focus {
  outline: none;
  border-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.gmb-services-text-input:hover {
  border-color: #525252;
}

.gmb-services-help-text {
  color: #9ca3af;
  font-size: 12px;
  margin-top: 6px;
  font-style: italic;
}

.gmb-services-number-input {
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  width: 120px;
  text-align: center;
  transition: all 0.2s ease;
}

.gmb-services-extraction-options {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.gmb-services-checkbox-setting {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.gmb-services-checkbox-setting:hover {
  opacity: 0.8;
}

.gmb-services-checkbox-setting input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #f59e0b;
  cursor: pointer;
}

.gmb-services-checkbox-label {
  color: #d1d5db;
  font-size: 14px;
  font-weight: 500;
}

.gmb-services-products-section {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #1a1a1a;
  border-left: 3px solid #f59e0b;
  border-radius: 6px;
}

.gmb-services-section-header {
  font-size: 15px;
  font-weight: 600;
  color: #f59e0b;
  margin-bottom: 12px;
}

.gmb-services-product {
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #262626;
  border-radius: 6px;
  border-left: 2px solid #f59e0b;
}

.gmb-services-product-title {
  font-size: 13px;
  color: #e5e5e5;
  font-weight: 500;
}

.gmb-services-progress {
  margin-bottom: 20px;
}

.gmb-services-progress-bar {
  width: 100%;
  height: 10px;
  background: #262626;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 10px;
}

.gmb-services-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b, #d97706);
  transition: width 0.3s ease;
}

.gmb-services-progress-text {
  font-size: 13px;
  color: #9ca3af;
  text-align: center;
}

.gmb-services-controls {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.gmb-services-btn {
  flex: 1;
  padding: 14px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #262626;
  color: #ffffff;
  border: 1px solid #333333;
  min-width: 100px;
}

.gmb-services-btn:hover:not(:disabled) {
  background: #333333;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.gmb-services-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.gmb-services-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.gmb-services-btn-start {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-color: #16a34a;
}

.gmb-services-btn-start:hover:not(:disabled) {
  background: linear-gradient(135deg, #16a34a, #15803d);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.gmb-services-btn-stop {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-color: #dc2626;
}

.gmb-services-btn-stop:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.gmb-services-btn-export {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  border-color: #4f46e5;
}

.gmb-services-btn-export:hover:not(:disabled) {
  background: linear-gradient(135deg, #4f46e5, #4338ca);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.gmb-services-results {
  margin-top: 20px;
}

.gmb-services-summary {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #262626;
  border-radius: 8px;
  border: 1px solid #333333;
}

.gmb-services-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.gmb-services-stat-label {
  font-size: 11px;
  color: #9ca3af;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.gmb-services-stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
}

.gmb-services-list {
  max-height: 400px;
  overflow-y: auto;
}

.gmb-services-business {
  margin-bottom: 16px;
  padding: 16px 20px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #2a2a2a;
}

.gmb-services-business-name {
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 12px;
  font-size: 15px;
}

.gmb-services-category {
  margin-bottom: 12px;
  padding: 12px 16px;
  background: #262626;
  border-radius: 6px;
  border-left: 3px solid #f59e0b;
}

.gmb-services-category-name {
  font-weight: 500;
  color: #f59e0b;
  font-size: 14px;
  margin-bottom: 6px;
}

.gmb-services-category-desc {
  font-size: 13px;
  color: #d1d5db;
  margin-bottom: 10px;
  font-style: italic;
}

.gmb-services-service {
  margin-left: 16px;
  margin-bottom: 6px;
  padding: 8px 12px;
  background: #333333;
  border-radius: 4px;
  font-size: 13px;
  color: #e5e5e5;
  border-left: 2px solid #f59e0b;
}

.gmb-services-service-title {
  font-weight: 500;
  color: #ffffff;
}

.gmb-services-service-desc {
  color: #9ca3af;
  margin-top: 4px;
  font-size: 12px;
}

.gmb-services-products-section {
  margin-bottom: 16px;
  padding: 16px 20px;
  background: #1a1a1a;
  border-left: 3px solid #f59e0b;
  border-radius: 6px;
}

.gmb-services-section-header {
  font-size: 15px;
  font-weight: 600;
  color: #f59e0b;
  margin-bottom: 12px;
}

.gmb-services-product {
  margin-bottom: 10px;
  padding: 10px 14px;
  background: #262626;
  border-radius: 6px;
  border-left: 2px solid #f59e0b;
}

.gmb-services-product-title {
  font-size: 14px;
  color: #e5e5e5;
  font-weight: 500;
}

.gmb-services-qa-section {
  margin-bottom: 16px;
  padding: 16px 20px;
  background: #1a1a1a;
  border-left: 3px solid #8b5cf6;
  border-radius: 6px;
}

.gmb-services-qa {
  margin-bottom: 12px;
  padding: 10px 14px;
  background: #262626;
  border-radius: 6px;
  border-left: 2px solid #8b5cf6;
}

.gmb-services-qa-question {
  font-size: 13px;
  color: #e5e5e5;
  font-weight: 500;
  margin-bottom: 6px;
}

.gmb-services-qa-answer {
  font-size: 12px;
  color: #9ca3af;
  line-height: 1.4;
} 