/* Review Analysis Dashboard Styles */

.review-analysis {
  background: rgb(15, 15, 15);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 32px;
  margin: 10px 0;
  color: #ffffff;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.05);
  font-family: -apple-system, BlinkMacSystemFont, 'Inter', 'SF Pro Display', system-ui, sans-serif;
}

.review-analysis__header {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: -0.5px;
  justify-content: center;
}

.review-analysis__stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20px;
  margin-bottom: 32px;
}

.review-analysis__stats-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.review-analysis__stats-card:hover {
  background: rgba(255, 255, 255, 0.04);
  transform: translateY(-1px);
}

.review-analysis__stats-label {
  color: #9CA3AF;
  font-size: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.review-analysis__stats-value {
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
}

.review-analysis__top-performers {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 32px;
}

.review-analysis__performer-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.review-analysis__performer-card:hover {
  background: rgba(255, 255, 255, 0.04);
  transform: translateY(-1px);
}

.review-analysis__performer-label {
  color: #9CA3AF;
  font-size: 11px;
  margin-bottom: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.review-analysis__performer-name {
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
  font-size: 15px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.review-analysis__performer-name-link {
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
  font-size: 15px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  text-decoration: underline;
  transition: all 0.2s ease;
}

.review-analysis__performer-name-link:hover {
  color: #f3f4f6;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.review-analysis__performer-rating {
  color: #F59E0B;
  font-weight: 600;
  font-size: 14px;
}

.review-analysis__performer-reviews {
  color: #10B981;
  font-weight: 600;
  font-size: 14px;
}

.review-analysis__seo-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 32px;
}

.review-analysis__seo-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.review-analysis__seo-card:hover {
  background: rgba(255, 255, 255, 0.04);
  transform: translateY(-1px);
}

.review-analysis__seo-label {
  color: #9CA3AF;
  font-size: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.review-analysis__seo-value {
  font-size: 32px;
  font-weight: 700;
  color: #7C3AED;
  line-height: 1;
  margin-bottom: 8px;
}

.review-analysis__seo-subtitle {
  color: #E5E7EB;
  font-size: 12px;
  font-weight: 500;
}

.review-analysis__intelligence-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.intelligence-item {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.intelligence-item:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.1);
}

.intelligence-label {
  color: #7C3AED;
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.intelligence-value {
  color: #ffffff;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 6px;
  line-height: 1.3;
}

.intelligence-desc {
  color: #9CA3AF;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.4;
  font-style: italic;
}

.review-analysis__section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.review-analysis__section-title {
  color: #ffffff;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 20px;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.review-analysis__data-section__title {
  color: #ffffff;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 20px;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.review-analysis__chart-container {
  display: flex;
  align-items: flex-end;
  background: rgb(15, 15, 15);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 24px 20px 16px 20px;
  min-height: 300px;
  position: relative;
}

.review-analysis__chart-y-axis {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 240px;
  margin-right: 16px;
  padding-bottom: 40px;
}

.review-analysis__y-label {
  color: #9CA3AF;
  font-size: 12px;
  font-weight: 500;
  text-align: right;
  padding-right: 8px;
  line-height: 1;
}

.review-analysis__chart-area {
  flex: 1;
  position: relative;
  height: 240px;
  padding-bottom: 40px;
}

.review-analysis__chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.review-analysis__grid-line {
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  width: 100%;
}

.review-analysis__grid-line[data-value="0"] {
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}

.review-analysis__bars {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 100%;
  gap: 12px;
  padding: 0 8px;
  position: relative;
  z-index: 2;
}

.review-analysis__bar-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 80px;
}

.review-analysis__bar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  height: 200px;
  width: 100%;
  position: relative;
}

.review-analysis__bar {
  width: 100%;
  max-width: 60px;
  border-radius: 8px 8px 0 0;
  transition: all 0.8s ease-in-out;
  position: relative;
  min-height: 2px;
  background: linear-gradient(180deg, currentColor 0%, rgba(255, 255, 255, 0.1) 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.review-analysis__bar:hover {
  transform: scaleY(1.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

.review-analysis__bar--five-star {
  color: #22C55E;
}

.review-analysis__bar--four-star {
  color: #FBBF24;
}

.review-analysis__bar--three-star {
  color: #F59E0B;
}

.review-analysis__bar--two-star {
  color: #EF4444;
}

.review-analysis__bar--one-star {
  color: #EF4444;
}

.review-analysis__bar--no-rating {
  color: #6B7280;
}

.review-analysis__bar-value {
  color: #ffffff;
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 8px;
  background: rgb(15, 15, 15);
  margin-top: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
}

.review-analysis__bar-label {
  color: #E5E7EB;
  font-size: 11px;
  font-weight: 600;
  text-align: center;
  margin-top: 12px;
  line-height: 1.2;
  word-break: break-word;
}

.review-analysis__category-item {
  margin-bottom: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.review-analysis__category-item:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(2px);
}

.review-analysis__category-name {
  color: #E5E7EB;
  font-size: 14px;
  font-weight: 500;
}

.review-analysis__category-count {
  font-size: 13px;
  font-weight: 700;
  background: rgba(139, 92, 246, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
}

/* Responsive Design */
@media (max-width: 600px) {
  .review-analysis__stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
  
  .review-analysis__seo-metrics {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .review-analysis__seo-card {
    padding: 16px;
  }
  
  .review-analysis__seo-value {
    font-size: 28px;
  }
  
  .review-analysis__chart-container {
    min-height: 250px;
    padding: 20px 16px 12px 16px;
  }
  
  .review-analysis__chart-y-axis {
    height: 180px;
    margin-right: 12px;
    padding-bottom: 30px;
  }
  
  .review-analysis__chart-area {
    height: 180px;
    padding-bottom: 30px;
  }
  
  .review-analysis__bar-wrapper {
    height: 150px;
  }
  
  .review-analysis__bars {
    gap: 8px;
    padding: 0 4px;
  }
  
  .review-analysis__bar-column {
    max-width: 60px;
  }
  
  .review-analysis__bar {
    max-width: 40px;
  }
  
  .review-analysis__bar-value {
    font-size: 12px;
    padding: 2px 6px;
  }
  
  .review-analysis__bar-label {
    font-size: 10px;
    margin-top: 8px;
  }
  
  .review-analysis {
    padding: 24px;
  }
  
  .review-analysis__header {
    font-size: 20px;
    margin-bottom: 24px;
  }
  
  .review-analysis__stats-value {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .review-analysis__stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .review-analysis__top-performers {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .review-analysis__chart-container {
    min-height: 220px;
    padding: 16px 12px 8px 12px;
  }
  
  .review-analysis__chart-y-axis {
    height: 160px;
    padding-bottom: 25px;
  }
  
  .review-analysis__chart-area {
    height: 160px;
    padding-bottom: 25px;
  }
  
  .review-analysis__bar-wrapper {
    height: 135px;
  }
  
  .review-analysis__bars {
    gap: 6px;
    padding: 0 2px;
  }
  
  .review-analysis__bar-column {
    max-width: 50px;
  }
  
  .review-analysis__bar {
    max-width: 35px;
  }
  
  .review-analysis {
    padding: 20px;
    margin: 5px 0;
  }
  
  .review-analysis__header {
    font-size: 18px;
    margin-bottom: 20px;
  }
  
  .review-analysis__stats-value {
    font-size: 24px;
  }
  
  .review-analysis__section {
    padding: 20px;
    margin-bottom: 24px;
  }
} 