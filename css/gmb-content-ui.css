/* GMB Content Script UI Styles - STM Edition */
/* Using BEM methodology for semantic and maintainable CSS */

/* =================================
   NAP BUTTON COMPONENT
   ================================= */

/* Base NAP button styling */
.gmb-nap-button {
  background: #ff8c00;
  height: 28px;
  width: 100px;
  min-width: 100px;
  max-width: 100px;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  opacity: 0.9;
  vertical-align: middle;
  display: inline-block;
  flex-shrink: 0;
  flex-grow: 0;
  margin: 0;
  z-index: 1000;
  position: relative;
  box-sizing: border-box;
}

/* NAP button hover state */
.gmb-nap-button:hover {
  background: #e67e00;
  opacity: 1;
  transform: translateY(-0.5px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* NAP button active/clicked state */
.gmb-nap-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

/* NAP button feedback states */
.gmb-nap-button--success {
  background: #22c55e !important;
}

.gmb-nap-button--error {
  background: #ef4444 !important;
}

/* Container for business heading with NAP button */
.gmb-business-heading-container {
  display: flex !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  /* gap: 8px !important; */
}

/* =================================
   PERSISTENT POPUP COMPONENT
   ================================= */

/* Main popup container */
.gmb-persistent-popup {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 360px;
  max-height: 80vh;
  background: #0f0f0f;
  color: #e5e5e5;
  border: 1px solid #2a2a2a;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

/* Popup header */
.gmb-popup__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #1a1a1a;
  border-bottom: 1px solid #2a2a2a;
}

.gmb-popup__title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.gmb-popup__version {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 400;
}

/* Close button */
.gmb-popup__close-btn {
  background: #262626;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  color: #000000;
  font-weight: bold;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(255, 68, 68, 0.3);
}

.gmb-popup__close-btn:hover {
  background: #ff6666;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(255, 68, 68, 0.4);
}

/* Popup content area */
.gmb-popup__content {
  padding: 16px;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.gmb-popup__content::-webkit-scrollbar {
  width: 6px;
}

.gmb-popup__content::-webkit-scrollbar-track {
  background: #262626;
  border-radius: 3px;
}

.gmb-popup__content::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 3px;
}

/* Status indicator */
.gmb-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background: #262626;
  border-radius: 8px;
  border: 1px solid #333333;
}

.gmb-status__indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
  animation: gmb-pulse 2s infinite;
}

.gmb-status__indicator--active {
  background: #22c55e;
}

.gmb-status__indicator--error {
  background: #ef4444;
}

.gmb-status__text {
  color: #d1d5db;
  font-size: 14px;
  font-weight: 500;
}

/* Controls and buttons */
.gmb-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.gmb-controls--pro-list {
  display: flex;
  gap: 6px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.gmb-controls--pro-list .gmb-btn {
  flex: 1;
  padding: 10px 12px;
  font-size: 12px;
  font-weight: 700;
  min-height: 44px;
  min-width: 120px;
}

/* Base button styling */
.gmb-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #262626;
  color: #ffffff;
  border: 1px solid #333333;
}

.gmb-btn:hover {
  background: #333333;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.gmb-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
  background: #1a1a1a;
}

/* Button variants */
.gmb-btn--secondary {
  /* background: linear-gradient(135deg, #6366f1, #8b5cf6); */
  border-color: #7c3aed;
}

.gmb-btn--secondary:hover {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
}

.gmb-btn--nap {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-color: #16a34a;
}

.gmb-btn--nap:hover {
  background: linear-gradient(135deg, #16a34a, #15803d);
}

.gmb-btn--service {
  /* background: linear-gradient(135deg, #f59e0b, #d97706); */
  border-color: #d97706;
}

.gmb-btn--service:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
}

.gmb-btn--attributes {
  /* background: linear-gradient(135deg, #8b5cf6, #a855f7); */
  border-color: #8b5cf6;
}

.gmb-btn--attributes:hover {
  background: linear-gradient(135deg, #7c3aed, #9333ea);
}

/* Data containers */
.gmb-data-container {
  background: #1a1a1a;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #2a2a2a;
  margin-bottom: 16px;
}

.gmb-data-container--hidden {
  display: none;
}

/* Empty state */
.gmb-empty-state {
  text-align: center;
  color: #6b7280;
}

.gmb-empty-state__title {
  margin: 0 0 8px 0;
  color: #ffffff;
  font-size: 16px;
}

.gmb-empty-state__description {
  margin: 0;
  font-size: 13px;
  color: #9ca3af;
}

/* Section titles */
.gmb-section-title {
  font-size: 14px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid #333333;
}

/* Progress indicators */
.gmb-progress {
  padding: 12px;
  background: #262626;
  border-radius: 8px;
  margin-bottom: 12px;
}

.gmb-progress__text {
  color: #d1d5db;
  font-size: 13px;
  line-height: 1.5;
}

/* Inline progress text styling */
.gmb-progress__status {
  color: #fbbf24;
  font-weight: bold;
  margin-bottom: 12px;
}

.gmb-progress__description {
  color: #d1d5db;
  line-height: 1.5;
  margin-bottom: 12px;
}

.gmb-progress__details {
  color: #d1d5db;
  line-height: 1.6;
  font-size: 12px;
}

/* =================================
   RESULT DISPLAYS
   ================================= */

/* Error message */
.gmb-error {
  color: #ef4444;
}

/* Success results container */
.gmb-results__success {
  margin-bottom: 16px;
  padding: 12px;
  background: #262626;
  border-radius: 8px;
}

.gmb-results__success-title {
  color: #22c55e;
  font-weight: bold;
}

.gmb-results__success-details {
  color: #d1d5db;
  font-size: 12px;
  margin-top: 4px;
}

.gmb-results__success-note {
  color: #9ca3af;
  font-size: 11px;
  margin-top: 6px;
  font-style: italic;
}

/* Results sections */
.gmb-results__section {
  margin-bottom: 16px;
  padding: 12px;
  background: #1a1a1a;
  border-radius: 8px;
}

.gmb-results__section-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.gmb-results__section-title--services {
  color: #f59e0b;
}

.gmb-results__section-title--attributes {
  color: #a855f7;
}

.gmb-results__section-title--categories {
  color: #8b5cf6;
}

.gmb-results__section-title--samples {
  color: #22c55e;
}

/* Result items */
.gmb-results__item {
  margin-bottom: 4px;
  padding: 6px;
  background: #262626;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
}

.gmb-results__item-label {
  color: #d1d5db;
  font-size: 12px;
}

.gmb-results__item-value {
  font-size: 11px;
  font-weight: bold;
}

.gmb-results__item-value--services {
  color: #f59e0b;
}

.gmb-results__item-value--attributes {
  color: #a855f7;
}

.gmb-results__item-value--categories {
  color: #8b5cf6;
}

/* Business samples */
.gmb-business-sample {
  margin-bottom: 8px;
  padding: 8px;
  background: #262626;
  border-radius: 4px;
}

.gmb-business-sample--services {
  border-left: 3px solid #22c55e;
}

.gmb-business-sample--attributes {
  border-left: 3px solid #a855f7;
}

.gmb-business-sample__name {
  color: #fbbf24;
  font-weight: bold;
  margin-bottom: 4px;
}

.gmb-business-sample__count {
  color: #9ca3af;
  font-size: 11px;
  margin-bottom: 4px;
}

.gmb-business-sample__items {
  color: #d1d5db;
  font-size: 11px;
}

/* Review results */
.gmb-review {
  margin-bottom: 12px;
  padding: 12px;
  background: #1a1a1a;
  border-radius: 8px;
  border-left: 3px solid #6366f1;
}

.gmb-review__name {
  color: #fbbf24;
  font-weight: bold;
  margin-bottom: 4px;
}

.gmb-review__rating {
  color: #f59e0b;
  margin-bottom: 4px;
}

.gmb-review__date {
  color: #9ca3af;
  font-size: 11px;
  margin-bottom: 8px;
}

.gmb-review__text {
  color: #d1d5db;
  font-size: 12px;
  line-height: 1.4;
}

/* "More results" indicator */
.gmb-more-results {
  text-align: center;
  color: #9ca3af;
  font-style: italic;
  margin-top: 12px;
}

.gmb-more-results--review {
  margin-top: 12px;
}

/* =================================
   UTILITY CLASSES
   ================================= */

/* Utility classes for hiding elements */
.gmb-hidden {
  display: none !important;
}

.gmb-visible {
  display: block !important;
}

.gmb-flex {
  display: flex !important;
}

.gmb-inline-block {
  display: inline-block !important;
}

/* Text color utilities */
.gmb-text--primary {
  color: #ffffff;
}

.gmb-text--secondary {
  color: #d1d5db;
}

.gmb-text--muted {
  color: #9ca3af;
}

.gmb-text--success {
  color: #22c55e;
}

.gmb-text--warning {
  color: #fbbf24;
}

.gmb-text--error {
  color: #ef4444;
}

.gmb-text--info {
  color: #60a5fa;
}

/* Font weight utilities */
.gmb-font--bold {
  font-weight: bold;
}

.gmb-font--semibold {
  font-weight: 600;
}

.gmb-font--normal {
  font-weight: 400;
}

/* Font size utilities */
.gmb-text--xs {
  font-size: 11px;
}

.gmb-text--sm {
  font-size: 12px;
}

.gmb-text--base {
  font-size: 13px;
}

.gmb-text--lg {
  font-size: 14px;
}

/* Spacing utilities */
.gmb-mb--xs {
  margin-bottom: 4px;
}

.gmb-mb--sm {
  margin-bottom: 8px;
}

.gmb-mb--base {
  margin-bottom: 12px;
}

.gmb-mb--lg {
  margin-bottom: 16px;
}

.gmb-mt--xs {
  margin-top: 4px;
}

.gmb-mt--sm {
  margin-top: 8px;
}

.gmb-mt--base {
  margin-top: 12px;
}

.gmb-mt--lg {
  margin-top: 16px;
}

/* =================================
   ANIMATIONS
   ================================= */

@keyframes gmb-pulse {
  0% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1); 
  }
  100% { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* =================================
   RESPONSIVE DESIGN
   ================================= */

/* Large screens (desktop) */
@media (min-width: 1200px) {
  .gmb-persistent-popup {
    width: 360px;
  }
}

/* Medium screens (tablet) */
@media (max-width: 768px) {
  .gmb-persistent-popup {
    width: calc(100vw - 40px);
    max-width: 360px;
    top: 10px;
    right: 20px;
  }
  
  .gmb-nap-button {
    height: 24px;
    width: 70px;
    font-size: 10px;
    padding: 2px 4px;
  }
  
  .gmb-business-heading-container {
    gap: 6px !important;
  }
  
  .gmb-controls--pro-list .gmb-btn {
    min-width: 100px;
    font-size: 11px;
  }
}

/* Small screens (mobile) */
@media (max-width: 480px) {
  .gmb-persistent-popup {
    width: calc(100vw - 20px);
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .gmb-nap-button {
    height: 22px;
    width: 60px;
    font-size: 9px;
    padding: 1px 3px;
  }
  
  .gmb-business-heading-container {
    gap: 4px !important;
    flex-direction: column;
    align-items: flex-start !important;
  }
  
  .gmb-popup__header {
    padding: 12px 16px;
  }
  
  .gmb-popup__title {
    font-size: 14px;
  }
  
  .gmb-popup__content {
    padding: 12px;
  }
  
  .gmb-controls {
    flex-direction: column;
    gap: 6px;
  }
  
  .gmb-controls--pro-list {
    flex-direction: column;
  }
  
  .gmb-controls--pro-list .gmb-btn {
    min-width: auto;
    width: 100%;
  }
}

/* =================================
   BUSINESS RANGE INPUT COMPONENT
   ================================= */

/* Business range container */
.gmb-business-range {
  margin-bottom: 16px;
  padding: 16px;
  background: #262626;
  border-radius: 8px;
  border: 1px solid #333333;
}

.gmb-business-range__label {
  display: block;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.gmb-business-range__input {
  width: calc(100% - 0px);
  padding: 10px 16px;
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  margin-bottom: 8px;
  height: 30px;
  box-sizing: border-box;
}

.gmb-business-range__input:focus {
  outline: none;
  border-color: #d97706;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2)
}

.gmb-business-range__input:hover {
  border-color: #525252;
}

.gmb-business-range__input::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.gmb-business-range__help {
  color: #9ca3af;
  font-size: 12px;
  font-style: italic;
  line-height: 1.4;
}

/* =================================
   EXTRACTION OPTIONS COMPONENT
   ================================= */

/* Extraction options container */
.gmb-extraction-options {
  margin-bottom: 16px;
  padding: 16px;
  background: #262626;
  border-radius: 8px;
  border: 1px solid #333333;
}

.gmb-extraction-option {
  margin-bottom: 12px;
}

.gmb-extraction-option:last-child {
  margin-bottom: 0;
}

.gmb-extraction-option__label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 8px;
  border-radius: 6px;
}

.gmb-extraction-option__label:hover {
  background: #2a2a2a;
}

.gmb-extraction-option__checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  margin: 0;
  appearance: none;
  background: #404040;
  border: 1px solid #555555;
  border-radius: 3px;
  position: relative;
  transition: all 0.2s ease;
}

.gmb-extraction-option__checkbox:checked {
  background: #f59e0b;
  border-color: #d97706;
}

.gmb-extraction-option__checkbox:checked::after {
  content: '✓';
  position: absolute;
  color: white;
  font-size: 12px;
  font-weight: bold;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.gmb-extraction-option__text {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  user-select: none;
}

/* Checked state styling */
.gmb-extraction-option__checkbox:checked + .gmb-extraction-option__text {
  color: #f59e0b;
  font-weight: 600;
}

/* =================================
   BUSINESS NUMBER BADGES
   ================================= */

.gmb-business-number {
  position: absolute !important;
  top: 8px !important;
  left: 8px !important;
  background: linear-gradient(135deg, #6366f1, #4f46e5) !important;
  color: white !important;
  border-radius: 50% !important;
  width: 28px !important;
  height: 28px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: bold !important;
  font-size: 14px !important;
  z-index: 999998 !important;
  border: 2px solid white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
}

/* Multiple Listings specific styling */
.gmb-business-number--multiple-listings {
  background: linear-gradient(135deg, rgb(99, 102, 241 ), rgb(79, 70, 229)) !important;
  border: 2px solid #ffffff !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  top: 0px !important;
  left: 0px !important;
  right: auto !important;
  width: 28px !important;
  height: 28px !important;
  font-size: 14px !important;
  font-weight: bold !important;
  animation: none !important;
}

/* Hover effect for Multiple Listings numbers */
.gmb-business-number--multiple-listings:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4) !important;
  animation: none !important;
}

/* =================================
   SUMMARY STATS COMPONENT
   ================================= */

.gmb-summary-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
  padding: 16px;
  background: #262626;
  border-radius: 8px;
  border: 1px solid #333333;
}

.gmb-summary-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 4px;
}

.gmb-summary-stat__label {
  font-size: 11px;
  color: #9ca3af;
  margin-bottom: 4px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.gmb-summary-stat__value {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
}

/* =================================
   BUTTON COMPONENT UPDATES
   ================================= */

.gmb-btn--start {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-color: #16a34a;
}

.gmb-btn--start:hover:not(:disabled) {
  background: linear-gradient(135deg, #16a34a, #15803d);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.gmb-btn--export {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  border-color: #4f46e5;
}

.gmb-btn--export:hover:not(:disabled) {
  background: linear-gradient(135deg, #4f46e5, #4338ca);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* =================================
   GLOBAL PROGRESS BAR COMPONENT
   ================================= */

.gmb-global-progress {
  margin-bottom: 16px;
}

.gmb-global-progress-bar {
  width: 100%;
  height: 6px;
  background: #262626;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
  position: relative;
}

.gmb-global-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b, #d97706);
  transition: width 0.3s ease;
  width: 0%;
}

.gmb-global-progress-fill.gmb-global-progress-complete {
  background: linear-gradient(90deg, #22c55e, #16a34a);
}

.gmb-global-progress-fill.gmb-global-progress-error {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.gmb-global-progress-text {
  font-size: 12px;
  color: #9ca3af;
  text-align: center;
  margin: 0;
} 