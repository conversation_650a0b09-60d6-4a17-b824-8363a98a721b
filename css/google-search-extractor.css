/* Google Search Extractor Styles - Restored Original Styling with BEM */
/* Based on original google-search-services-extractor.css from commit 862bc33 */
/* Updated with better spacing to match the exact UI shown in image */

/* Business Number Badge Component */
.gmb-search-extractor__business-number {
  position: absolute !important;
  top: 5px !important;
  left: 5px !important;
  background: linear-gradient(135deg, #4f46e5, #7c3aed) !important;
  color: white !important;
  border-radius: 50% !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: bold !important;
  font-size: 12px !important;
  z-index: 999997 !important;
  border: 2px solid white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  font-family: Arial, sans-serif !important;
}

/* Card positioning utility */
.gmb-search-extractor__card-relative {
  position: relative !important;
}

/* Docked Interface - Original Compact Design with Better Spacing */
.gmb-search-extractor__docked-interface {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 360px;
  max-height: 80vh;
  background: #0f0f0f;
  color: #e5e5e5;
  border: 1px solid #2a2a2a;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  z-index: 999999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
  backdrop-filter: blur(10px);
  pointer-events: auto;
  user-select: none;
}

/* Header - Original Compact Style */
.gmb-search-extractor__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #1a1a1a;
  border-bottom: 1px solid #2a2a2a;
}

.gmb-search-extractor__title {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #ffffff;
}

.gmb-search-extractor__close-btn {
  background: #262626;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  color: #ffffff;
  font-weight: bold;
  transition: all 0.2s ease;
  z-index: 10001;
  position: relative;
}

.gmb-search-extractor__close-btn:hover {
  background: #ff6666;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(255, 102, 102, 0.3);
}

.gmb-search-extractor__close-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2px 6px rgba(255, 102, 102, 0.2);
}

/* Content - Better Spacing */
.gmb-search-extractor__content {
  padding: 12px;
  max-height: calc(80vh - 70px);
  overflow-y: auto;
}

.gmb-search-extractor__content::-webkit-scrollbar {
  width: 6px;
}

.gmb-search-extractor__content::-webkit-scrollbar-track {
  background: #262626;
  border-radius: 3px;
}

.gmb-search-extractor__content::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 3px;
}

/* Status - Better Spacing */
.gmb-search-extractor__status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px 14px;
  background: #262626;
  border-radius: 6px;
  border: 1px solid #333333;
}

.gmb-search-extractor__status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
}

.gmb-search-extractor__status-indicator--active {
  background: #22c55e;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.gmb-search-extractor__status-text {
  color: #d1d5db;
  font-size: 14px;
  font-weight: 500;
}

/* Settings - Better Spacing */
.gmb-search-extractor__settings {
  margin-bottom: 16px;
  padding: 12px 14px;
  background: #262626;
  border-radius: 6px;
  border: 1px solid #333333;
}

.gmb-search-extractor__setting {
  display: flex;
  flex-direction: column;
  gap: 8px;
  cursor: pointer;
  margin-bottom: 12px;
}

.gmb-search-extractor__setting-label {
  color: #d1d5db;
  font-size: 14px;
  font-weight: 500;
}

.gmb-search-extractor__text-input {
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 4px;
  padding: 8px 10px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  margin-top: 6px;
  margin-bottom: 6px;
}

.gmb-search-extractor__text-input:focus {
  outline: none;
  border-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.gmb-search-extractor__text-input:hover {
  border-color: #525252;
}

.gmb-search-extractor__help-text {
  color: #9ca3af;
  font-size: 13px;
  margin-top: 4px;
  font-style: italic;
}

/* Extraction Options - Better Spacing */
.gmb-search-extractor__extraction-options {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  border: none;
  padding: 0;
}

.gmb-search-extractor__options-legend {
  display: none;
}

.gmb-search-extractor__checkbox-setting {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: opacity 0.2s ease;
  padding: 2px 0;
}

.gmb-search-extractor__checkbox-setting:hover {
  opacity: 0.8;
}

.gmb-search-extractor__checkbox-setting input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #f59e0b;
  cursor: pointer;
}

.gmb-search-extractor__checkbox-label {
  color: #d1d5db;
  font-size: 13px;
  font-weight: 500;
}

/* Progress - Better Spacing */
.gmb-search-extractor__progress {
  margin-bottom: 16px;
}

.gmb-search-extractor__progress-bar {
  width: 100%;
  height: 6px;
  background: #262626;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.gmb-search-extractor__progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b, #d97706);
  transition: width 0.3s ease;
}

.gmb-search-extractor__progress-text {
  font-size: 12px;
  color: #9ca3af;
  text-align: center;
}

/* Controls - Better Spacing */
.gmb-search-extractor__controls {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.gmb-search-extractor__btn {
  flex: 1;
  padding: 10px 12px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #262626;
  color: #ffffff;
  border: 1px solid #333333;
  min-width: 60px;
}

.gmb-search-extractor__btn:hover:not(:disabled) {
  background: #333333;
  transform: translateY(-1px);
}

.gmb-search-extractor__btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.gmb-search-extractor__btn--start {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-color: #16a34a;
}

.gmb-search-extractor__btn--start:hover:not(:disabled) {
  background: linear-gradient(135deg, #16a34a, #15803d);
}

.gmb-search-extractor__btn--export {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  border-color: #4f46e5;
}

.gmb-search-extractor__btn--export:hover:not(:disabled) {
  background: linear-gradient(135deg, #4f46e5, #4338ca);
}

/* Results - Better Spacing */
.gmb-search-extractor__results {
  border-top: none;
  padding-top: 0;
}

/* Stats Summary - 3 Column Grid Layout */
.gmb-search-extractor__summary {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
  padding: 16px;
  background: #262626;
  border-radius: 8px;
  border: 1px solid #333333;
}

.gmb-search-extractor__stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 4px;
}

.gmb-search-extractor__stat-label {
  font-size: 11px;
  color: #9ca3af;
  margin-bottom: 4px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.gmb-search-extractor__stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
}

/* Business List - Better Spacing */
.gmb-search-extractor__list {
  max-height: 200px;
  overflow-y: auto;
}

.gmb-search-extractor__business {
  margin-bottom: 8px;
  padding: 8px 10px;
  background: #1a1a1a;
  border-radius: 6px;
  border: 1px solid #2a2a2a;
}

.gmb-search-extractor__business-name {
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 6px;
  font-size: 14px;
}

/* Products Section - Better Spacing */
.gmb-search-extractor__products-section {
  margin-bottom: 12px;
  padding: 10px 12px;
  background: #1a1a1a;
  border-left: 3px solid #f59e0b;
  border-radius: 4px;
}

.gmb-search-extractor__section-header {
  font-size: 15px;
  font-weight: 600;
  color: #f59e0b;
  margin-bottom: 8px;
}

.gmb-search-extractor__product {
  margin-bottom: 6px;
  padding: 6px 8px;
  background: #262626;
  border-radius: 4px;
  border-left: 2px solid #f59e0b;
}

.gmb-search-extractor__product-title {
  font-size: 13px;
  color: #e5e5e5;
  font-weight: 500;
}

/* Categories - Better Spacing */
.gmb-search-extractor__category {
  margin-bottom: 6px;
  padding: 6px 8px;
  background: #262626;
  border-radius: 4px;
  border-left: 3px solid #f59e0b;
}

.gmb-search-extractor__category--no-data {
  border-left-color: #ef4444;
}

.gmb-search-extractor__category-name {
  font-weight: 500;
  color: #f59e0b;
  font-size: 13px;
  margin-bottom: 4px;
}

.gmb-search-extractor__category-name--error {
  color: #ef4444;
}

.gmb-search-extractor__category-desc {
  font-size: 13px;
  color: #d1d5db;
  margin-bottom: 6px;
  font-style: italic;
}

/* Services - Better Spacing */
.gmb-search-extractor__service {
  margin-left: 8px;
  margin-bottom: 3px;
  padding: 4px 6px;
  background: #333333;
  border-radius: 3px;
  font-size: 12px;
  color: #e5e5e5;
}

.gmb-search-extractor__service-title {
  font-weight: 500;
  color: #ffffff;
}

/* Q&A Section - Better Spacing */
.gmb-search-extractor__qa-section {
  margin-bottom: 16px;
}

.gmb-search-extractor__qa {
  background: #262626;
  padding: 8px 10px;
  border-radius: 4px;
  margin-bottom: 6px;
  border-left: 3px solid #f59e0b;
}

.gmb-search-extractor__qa-question {
  font-weight: 500;
  color: #f59e0b;
  margin-bottom: 4px;
  font-size: 13px;
}

.gmb-search-extractor__qa-answer {
  color: #d1d5db;
  font-size: 12px;
  line-height: 1.4;
}

/* Visual Markers */
.gmb-search-extractor__marker--red {
  border: 3px solid #ff0000 !important;
  outline: 2px solid #ff4444 !important;
  box-shadow: 0 0 10px rgba(255, 0, 0, 0.5) !important;
  z-index: 999999 !important;
  background-color: rgba(255, 0, 0, 0.1) !important;
}

.gmb-search-extractor__marker--green {
  border: 2px solid #00ff00 !important;
  outline: 1px solid #44ff44 !important;
}

.gmb-search-extractor__marker--none {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

/* Position Marker */
.gmb-search-extractor__position-marker {
  position: fixed;
  width: 10px;
  height: 10px;
  background-color: #ff0000;
  border: 2px solid #ffffff;
  border-radius: 50%;
  z-index: 999999;
  pointer-events: none;
}

/* Hidden utility */
.gmb-search-extractor__hidden {
  visibility: hidden;
}

/* Transition animations */
.gmb-search-extractor__fade-out {
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 0;
  transform: translateX(100%);
}

/* Progress width utilities */
.gmb-search-extractor__progress-dynamic {
  transition: width 0.3s ease;
  width: var(--progress-width, 0%);
}

.gmb-search-extractor__progress-complete {
  width: 100% !important;
}

/* Export button states */
.gmb-search-extractor__export-btn--success {
  background: linear-gradient(135deg, #22c55e, #16a34a) !important;
}

.gmb-search-extractor__export-btn--default {
  background: linear-gradient(135deg, #6366f1, #4f46e5) !important;
}

/* Animation for loading states */
@keyframes gmb-search-extractor-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.gmb-search-extractor__loading {
  animation: gmb-search-extractor-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Visual marker fade-in/out */
@keyframes gmb-search-extractor-marker-appear {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

.gmb-search-extractor__marker-animated {
  animation: gmb-search-extractor-marker-appear 0.3s ease-out;
}

/* =================================
   INSTRUCTIONS COMPONENT
   ================================= */

/* Instructions container */
.gmb-search-extractor__instructions {
  margin: 16px 0;
  padding: 16px;
  background: #1a1a1a;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
}

/* Instructions header */
.gmb-search-extractor__instructions-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #333333;
}

.gmb-search-extractor__instructions-icon {
  font-size: 18px;
  display: block;
}

.gmb-search-extractor__instructions-title {
  color: #f59e0b;
  font-size: 14px;
  font-weight: 700;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Instructions content */
.gmb-search-extractor__instructions-content {
  padding: 4px 0;
}

.gmb-search-extractor__instructions-intro {
  color: #e5e5e5;
  font-size: 13px;
  margin-bottom: 12px;
  font-weight: 500;
}

/* Instructions list */
.gmb-search-extractor__instructions-list {
  margin: 0;
  padding-left: 20px;
  color: #d1d5db;
}

.gmb-search-extractor__instructions-step {
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 8px;
  color: #d1d5db;
}

.gmb-search-extractor__instructions-step:last-child {
  margin-bottom: 0;
}

.gmb-search-extractor__instructions-step strong {
  color: #f59e0b;
  font-weight: 600;
} 