/* Minimal Reader CSS - Exact Reeader Styling with GMB Namespace */
@import url(https://fonts.googleapis.com/css?family=Muli|Open+Sans);

/* Base App Styling */
#gmb-reader-root,
.gmb-reader-app {
    width: 100%;
    height: 100%;
}

.gmb-reader-app {
    overflow: scroll;
    position: fixed;
    top: 0;
    left: 0;
    animation: gmb-reader-slideFromBottom 0.5s ease-out;
    z-index: 9999999;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.gmb-reader-app-wrapper {
    margin: 0 auto;
    padding-top: 70px;
    font-family: Open Sans, sans-serif;
    max-width: 800px;
    width: 100%;
}

.gmb-reader-app-wrapper p {
    font-size: 1em;
    margin-bottom: 1.8rem !important;
    font-family: Open Sans, sans-serif !important;
}

.gmb-reader-app-wrapper p span.gmb-reader-fade {
    opacity: 0.2;
}

/* Header Styling */
.gmb-reader-app-header {
    position: fixed;
    height: 70px;
    width: 100%;
    left: 0;
    top: 0;
    border-bottom: 1px solid hsla(0, 0%, 75.7%, 0.2);
    z-index: 10;
}

.gmb-reader-app-header__content {
    height: 100%;
    padding: 0 25px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
}

/* Content Styling */
.gmb-reader-content__wrapper {
    padding: 25px;
}

/* Meta Information */
.gmb-reader-meta {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.gmb-reader-url {
    font-size: 16px;
    color: #000000;
    margin-bottom: 8px;
    word-break: break-all;
    line-height: 1.4;
    font-weight: 500;
}

.gmb-reader-reading-time {
    font-size: 13px;
    color: #000000;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Image Toggle */
.gmb-reader-hide-images img {
    display: none !important;
}

.gmb-reader-hide-images iframe,
.gmb-reader-hide-images video {
    display: none !important;
}

.gmb-reader-app-wrapper iframe,
.gmb-reader-app-wrapper img,
.gmb-reader-app-wrapper video {
    max-width: 100%;
    width: auto;
    height: auto;
}

.gmb-reader-app-wrapper a {
    color: #4e7de8;
}

.gmb-reader-app-wrapper a:hover {
    color: #3d63b9;
}

/* Typography */
.gmb-reader-app-wrapper h1,
.gmb-reader-app-wrapper h2,
.gmb-reader-app-wrapper h3,
.gmb-reader-app-wrapper h4,
.gmb-reader-app-wrapper h5,
.gmb-reader-app-wrapper h6 {
    margin-top: 1em;
    margin-bottom: 0.3em;
    font-family: Muli, sans-serif;
}

.gmb-reader-app-wrapper h1 {
    font-size: 1.5em;
    font-family: Muli, sans-serif;
    font-weight: 400;
}

.gmb-reader-app-wrapper h2 {
    font-size: 1.4em;
    font-family: Muli, sans-serif;
    font-weight: 400;
}

.gmb-reader-app-wrapper h3 {
    font-size: 1.3em;
    font-family: Muli, sans-serif;
    font-weight: 400;
}

.gmb-reader-app-wrapper h4 {
    font-size: 1.2em;
    font-family: Muli, sans-serif;
    font-weight: 400;
}

.gmb-reader-app-wrapper h5 {
    font-size: 1.1em;
    font-family: Muli, sans-serif;
    font-weight: 400;
}

.gmb-reader-app-wrapper h6 {
    font-size: 1em;
    font-family: Muli, sans-serif;
    font-weight: 400;
}

/* Code Block Styling */
.gmb-reader-app-wrapper pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    overflow-x: auto;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.5;
    color: #333;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.gmb-reader-app-wrapper pre code {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    font-size: inherit;
    color: inherit;
    display: block;
    white-space: pre;
    word-wrap: break-word;
}

.gmb-reader-app-wrapper :not(pre) > code {
    background: #f1f3f4;
    border: 1px solid #e8eaed;
    border-radius: 0.25rem;
    padding: 0.125rem 0.375rem;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.875em;
    color: #d63384;
    word-wrap: break-word;
}

/* Preserve syntax highlighting when present */
.gmb-reader-app-wrapper pre code[class*="language-"],
.gmb-reader-app-wrapper pre code[class*="lang-"] {
    background: transparent;
    color: inherit;
}

/* Handle long code lines */
.gmb-reader-app-wrapper pre {
    max-width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Command line styling */
.gmb-reader-app-wrapper pre code .token.comment,
.gmb-reader-app-wrapper pre code .token.prolog,
.gmb-reader-app-wrapper pre code .token.doctype,
.gmb-reader-app-wrapper pre code .token.cdata {
    color: #6a737d;
}

.gmb-reader-app-wrapper pre code .token.punctuation {
    color: #586069;
}

.gmb-reader-app-wrapper pre code .token.property,
.gmb-reader-app-wrapper pre code .token.tag,
.gmb-reader-app-wrapper pre code .token.boolean,
.gmb-reader-app-wrapper pre code .token.number,
.gmb-reader-app-wrapper pre code .token.constant,
.gmb-reader-app-wrapper pre code .token.symbol,
.gmb-reader-app-wrapper pre code .token.deleted {
    color: #005cc5;
}

.gmb-reader-app-wrapper pre code .token.selector,
.gmb-reader-app-wrapper pre code .token.attr-name,
.gmb-reader-app-wrapper pre code .token.string,
.gmb-reader-app-wrapper pre code .token.char,
.gmb-reader-app-wrapper pre code .token.builtin,
.gmb-reader-app-wrapper pre code .token.inserted {
    color: #032f62;
}

.gmb-reader-app-wrapper pre code .token.operator,
.gmb-reader-app-wrapper pre code .token.entity,
.gmb-reader-app-wrapper pre code .token.url,
.gmb-reader-app-wrapper pre code .token.variable {
    color: #d73a49;
}

.gmb-reader-app-wrapper pre code .token.atrule,
.gmb-reader-app-wrapper pre code .token.attr-value,
.gmb-reader-app-wrapper pre code .token.keyword {
    color: #d73a49;
}

.gmb-reader-app-wrapper pre code .token.function {
    color: #6f42c1;
}

.gmb-reader-app-wrapper pre code .token.regex,
.gmb-reader-app-wrapper pre code .token.important {
    color: #e36209;
}

/* Control Elements */
.gmb-reader-theme--toggle {
    display: flex;
    background-color: inherit;
    align-items: center;
}

.gmb-reader-font--update {
    display: inline-block;
    width: 30px;
    font-weight: 700;
    margin-right: 5px;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 22px;
    cursor: pointer;
}

.gmb-reader-font--update.gmb-reader-dec {
    font-size: 15px;
}

.gmb-reader-font--update.gmb-reader-inc {
    font-size: 22px;
    margin-right: 20px;
    position: relative;
}

.gmb-reader-font--update.gmb-reader-inc:after {
    content: "";
    display: inline-block;
    background-color: hsla(0, 0%, 75.7%, 0.4);
    width: 1px;
    height: 42px;
    position: absolute;
    right: -10px;
    top: 0;
}

.gmb-reader-theme--change {
    display: inline-block;
    width: 20px;
    height: 40px;
    border-radius: 20px;
    border: 1px solid #6d6d6d;
    margin: 0 5px;
    cursor: pointer;
}

.gmb-reader-theme--change.gmb-reader-square {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: 2px solid #6d6d6d;
    margin: 0 8px 0 0;
    transition: all 0.2s ease;
}

.gmb-reader-theme--change.gmb-reader-square:hover {
    border-color: #4e7de8;
    transform: scale(1.1);
}

.gmb-reader-button--close {
    padding: 10px;
    cursor: pointer;
}

/* Popup Controls */
.gmb-reader-popup-toggle__wrapper {
    position: relative;
    background-color: inherit;
}

.gmb-reader-popup__wrapper {
    position: absolute;
    min-width: 340px;
    width: 340px;
    top: 100%;
    right: 0;
    background-color: inherit;
    box-shadow: -3px 25px 35px -5px rgba(0, 0, 0, 0.3);
    border-radius: 5px 0 5px 5px;
    padding: 10px 10px 0;
    height: auto;
    max-height: none;
    overflow: visible;
}

h5.gmb-reader-popup--label {
    font-size: 14px;
    opacity: 0.4;
    letter-spacing: 1px;
    margin-top: 10px !important;
}

.gmb-reader-popup-toggle--button {
    position: relative;
    top: 5px;
    cursor: pointer;
    display: inline-block;
    box-sizing: border-box;
    padding: 0 15px;
}

.gmb-reader-popup-toggle--button figure {
    width: 6px !important;
    height: 6px !important;
    border-radius: 5px !important;
    background: #303030 !important;
    margin: 3px 0 !important;
}

.gmb-reader-popup__control {
    display: inline-block;
    width: 25px;
    height: 25px;
    line-height: 25px;
    margin-right: 8px;
}

.gmb-reader-popup__toggle {
    display: block;
    font-size: 14px;
    background-color: initial;
    transition: 0.3s;
    text-align: left;
    padding: 5px 0;
    cursor: pointer;
}

.gmb-reader-popup__toggle.gmb-reader-active {
    background-color: #4e7de8;
    color: #fff;
    text-align: center;
}

.gmb-reader-popup__wrapper h5 {
    font-size: 16px;
}


/* Animations */
@keyframes gmb-reader-slideFromBottom {
    0% {
        transform: translateY(100%);
        opacity: 0.3;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Theme: White */
.gmb-reader-theme-white {
    background-color: #fff;
    color: #303030;
}

.gmb-reader-theme-white h1,
.gmb-reader-theme-white h2,
.gmb-reader-theme-white h3,
.gmb-reader-theme-white h4,
.gmb-reader-theme-white h5,
.gmb-reader-theme-white h6,
.gmb-reader-theme-white p {
    color: #303030;
}

.gmb-reader-theme-white .gmb-reader-app-header__content {
    background-color: #fff;
}

/* White Theme Code Blocks */
.gmb-reader-theme-white .gmb-reader-app-wrapper pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #212529;
}

.gmb-reader-theme-white .gmb-reader-app-wrapper :not(pre) > code {
    background: #f1f3f4;
    border: 1px solid #e8eaed;
    color: #d63384;
}

/* Theme: Yellow (Sepia) */
.gmb-reader-theme-yellow {
    color: #303030;
    background-color: #f7f0e4;
}

.gmb-reader-theme-yellow h1,
.gmb-reader-theme-yellow h2,
.gmb-reader-theme-yellow h3,
.gmb-reader-theme-yellow h4,
.gmb-reader-theme-yellow h5,
.gmb-reader-theme-yellow h6,
.gmb-reader-theme-yellow p {
    color: #303030 !important;
}

.gmb-reader-theme-yellow .gmb-reader-meta {
    border-color: rgba(48, 48, 48, 0.15);
}

.gmb-reader-theme-yellow .gmb-reader-url {
    color: #000000;
}

.gmb-reader-theme-yellow .gmb-reader-reading-time {
    color: #000000;
}

.gmb-reader-theme-yellow .gmb-reader-app-header__content {
    background-color: #f7f0e4;
}

/* Yellow Theme Code Blocks */
.gmb-reader-theme-yellow .gmb-reader-app-wrapper pre {
    background: #f0e6d2;
    border: 1px solid #e8dcc0;
    color: #2c2c2c;
}

.gmb-reader-theme-yellow .gmb-reader-app-wrapper :not(pre) > code {
    background: #ede3cd;
    border: 1px solid #d9ceb1;
    color: #8b0000;
}

/* Theme: Dark */
.gmb-reader-theme-dark {
    color: #fff;
    background-color: #1c1f21;
}

.gmb-reader-theme-dark h1,
.gmb-reader-theme-dark h2,
.gmb-reader-theme-dark h3,
.gmb-reader-theme-dark h4,
.gmb-reader-theme-dark h5,
.gmb-reader-theme-dark h6,
.gmb-reader-theme-dark p {
    color: #fff !important;
}

.gmb-reader-theme-dark .gmb-reader-meta {
    border-color: rgba(255, 255, 255, 0.1);
}

.gmb-reader-theme-dark .gmb-reader-url {
    color: #ffffff;
}

.gmb-reader-theme-dark .gmb-reader-reading-time {
    color: #ffffff;
}

.gmb-reader-theme-dark .gmb-reader-popup-toggle--button figure {
    background: #fff !important;
}

.gmb-reader-theme-dark .gmb-reader-app-header__content {
    background-color: #1c1f21;
}

/* Dark Theme Code Blocks */
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre {
    background: #161b22;
    border: 1px solid #30363d;
    color: #c9d1d9;
}

.gmb-reader-theme-dark .gmb-reader-app-wrapper :not(pre) > code {
    background: #21262d;
    border: 1px solid #30363d;
    color: #f85149;
}

/* Dark Theme Syntax Highlighting */
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.comment,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.prolog,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.doctype,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.cdata {
    color: #8b949e;
}

.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.punctuation {
    color: #c9d1d9;
}

.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.property,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.tag,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.boolean,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.number,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.constant,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.symbol,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.deleted {
    color: #79c0ff;
}

.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.selector,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.attr-name,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.string,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.char,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.builtin,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.inserted {
    color: #a5d6ff;
}

.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.operator,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.entity,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.url,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.variable {
    color: #ff7b72;
}

.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.atrule,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.attr-value,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.keyword {
    color: #ff7b72;
}

.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.function {
    color: #d2a8ff;
}

.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.regex,
.gmb-reader-theme-dark .gmb-reader-app-wrapper pre code .token.important {
    color: #ffa657;
}

/* Focus Mode - Border Overlays */
.gmb-reader-focus .gmb-reader-content__wrapper > div:not(.gmb-reader-meta) {
    position: relative;
}

.gmb-reader-focus .gmb-reader-content__wrapper {
    text-align: justify;
}

.gmb-reader-focus .gmb-reader-content__wrapper > div:not(.gmb-reader-meta):after {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    background: transparent;
    position: absolute;
    top: 0;
    left: 0;
    border-left: var(--focus-gutter-width, 50px) solid;
    border-right: var(--focus-gutter-width, 50px) solid;
    box-sizing: border-box;
    pointer-events: none;
    transition: border-width 0.2s ease;
}

/* Exclude meta from focus mode effects */
.gmb-reader-focus .gmb-reader-meta {
    position: static !important;
}

.gmb-reader-focus .gmb-reader-meta:after {
    display: none !important;
}

.gmb-reader-focus h1,
.gmb-reader-focus h2,
.gmb-reader-focus h3,
.gmb-reader-focus h4,
.gmb-reader-focus h5,
.gmb-reader-focus h6,
.gmb-reader-focus iframe,
.gmb-reader-focus img,
.gmb-reader-focus table,
.gmb-reader-focus video {
    z-index: 1;
    position: relative;
    text-align: left;
}

.gmb-reader-focus.gmb-reader-theme-white .gmb-reader-content__wrapper > div:after {
    border-color: hsla(0, 0%, 100%, var(--focus-text-opacity, 0.4));
}

.gmb-reader-focus.gmb-reader-theme-yellow .gmb-reader-content__wrapper > div:after {
    border-color: rgba(247, 240, 228, var(--focus-text-opacity, 0.4));
}

.gmb-reader-focus.gmb-reader-theme-dark .gmb-reader-content__wrapper > div:after {
    border-color: rgba(28, 31, 33, var(--focus-text-opacity, 0.4));
}

/* Stop Words Fading - Independent of Focus Mode */
.gmb-reader-stopwords p span.gmb-reader-fade {
    opacity: var(--stopwords-opacity, 0.4);
    transition: opacity 0.2s ease;
}

/* Action Controls */
img.gmb-reader-icons {
    width: 32px;
    height: 32px;
    margin-right: 20px;
}

.gmb-reader-popup__action-wrapper {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
}

.gmb-reader-popup__action-wrapper .gmb-reader-font--update {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #6d6d6d;
    background: transparent;
    transition: all 0.2s ease;
    margin-right: 0;
}

.gmb-reader-popup__action-wrapper .gmb-reader-font--update:hover {
    border-color: #4e7de8;
    background: rgba(78, 125, 232, 0.1);
}

.gmb-reader-button--action {
    background: transparent;
    border: 0;
    outline: none;
    height: none;
    box-shadow: none;
    text-align: center;
    cursor: pointer;
    margin-right: 10px;
}

.gmb-reader-speed--toggle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.gmb-reader-speed--toggle.gmb-reader-active {
    background: rgba(78, 125, 232, 0.32);
}

/* Settings Accordion Styling - Following drag-select pattern */
.minimal-reader-accordion {
    margin-top: 15px;
    border: 1px solid #374151;
    border-radius: 8px;
    overflow: hidden;
}

.minimal-reader-header {
    background: #1a1a1a;
    padding: 12px 16px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    user-select: none;
}

.minimal-reader-header:hover {
    background: #2a2a2a;
}

.minimal-reader-title {
    font-size: 14px;
    font-weight: 500;
    color: #d1d5db;
    display: flex;
    align-items: center;
    gap: 8px;
}

.minimal-reader-icon {
    color: #9ca3af;
    font-size: 12px;
    transition: transform 0.2s ease;
}

.minimal-reader-header.expanded .minimal-reader-icon {
    transform: rotate(180deg);
}

.minimal-reader-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.minimal-reader-content.expanded {
    max-height: 1000px;
}

.minimal-reader-body {
    padding: 16px;
    background: #111111;
}

.minimal-reader-input-group {
    margin-bottom: 16px;
}

.minimal-reader-input-group:last-child {
    margin-bottom: 0;
}

.minimal-reader-input-label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #d1d5db;
    margin-bottom: 6px;
}

/* Accordion separator for visual spacing */
.gmb-reader-accordion-separator {
    border-top: 1px solid #374151;
    margin-top: 20px;
    margin-bottom: 15px;
    padding-top: 5px;
}

.minimal-reader-select {
    width: 100%;
    padding: 8px 12px;
    background: #0a0a0a;
    border: 1px solid #374151;
    border-radius: 6px;
    color: #d1d5db;
    font-size: 13px;
}

.minimal-reader-select:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 1px #7C3AED;
}

.minimal-reader-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.minimal-reader-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #d1d5db;
    cursor: pointer;
}

.minimal-reader-checkbox-label input[type="checkbox"] {
    margin: 0;
}

.minimal-reader-range {
    width: calc(100% - 50px);
    margin-right: 10px;
}

.minimal-reader-value {
    font-size: 13px;
    color: #9ca3af;
    min-width: 40px;
    display: inline-block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .gmb-reader-app-wrapper {
        max-width: 100%;
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .gmb-reader-content__wrapper {
        padding: 15px;
    }
    
    .gmb-reader-app-header__content {
        padding: 0 15px;
    }
    
    .gmb-reader-popup__wrapper {
        min-width: 250px;
        width: 250px;
    }
    
    /* Code block responsive adjustments for tablets */
    .gmb-reader-app-wrapper pre {
        padding: 0.875rem;
        margin: 0.875rem 0;
        font-size: 0.875em;
        border-radius: 0.375rem;
    }
    
    .gmb-reader-app-wrapper :not(pre) > code {
        padding: 0.1rem 0.3rem;
        font-size: 0.8em;
    }
}

@media (max-width: 480px) {
    .gmb-reader-app-wrapper {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .gmb-reader-content__wrapper {
        padding: 10px;
    }
    
    .gmb-reader-app-header__content {
        padding: 0 10px;
        font-size: 16px;
    }
    
    .gmb-reader-meta {
        margin-bottom: 15px;
        padding-bottom: 10px;
    }
    
    .gmb-reader-url {
        font-size: 14px;
        line-height: 1.3;
    }
    
    .gmb-reader-reading-time {
        font-size: 12px;
    }
    
    .gmb-reader-popup__wrapper {
        min-width: 220px;
        width: 220px;
        right: -10px;
    }
    
    /* Code block responsive adjustments for mobile */
    .gmb-reader-app-wrapper pre {
        padding: 0.75rem;
        margin: 0.75rem 0;
        font-size: 0.8em;
        border-radius: 0.25rem;
        overflow-x: auto;
        white-space: pre;
        word-wrap: normal;
    }
    
    .gmb-reader-app-wrapper :not(pre) > code {
        padding: 0.075rem 0.25rem;
        font-size: 0.75em;
        word-break: break-word;
    }
}

/* Theme-Aware Popup Styling */
.gmb-reader-popup__wrapper {
    position: absolute;
    top: 100%;
    right: 0;
    border: 2px solid #7C3AED;
    border-radius: 8px;
    padding: 16px;
    min-width: 200px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.6);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    z-index: 10000;
}



.gmb-reader-popup-btn {
    background: transparent;
    border: 1px solid;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    width: 100%;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

/* Two-column button layout */
.gmb-reader-popup-buttons-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 12px;
}

.gmb-reader-popup-buttons-grid .gmb-reader-popup-btn {
    width: 100%;
    margin-bottom: 0;
}

/* Single row for related buttons */
.gmb-reader-popup-buttons-row {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.gmb-reader-popup-buttons-row .gmb-reader-popup-btn {
    flex: 1;
    margin-bottom: 0;
}

.gmb-reader-popup-btn-last {
    margin-bottom: 0;
}

/* White Theme Popup */
.gmb-reader-popup-theme-white {
    background: #ffffff;
    color: #303030;
}



.gmb-reader-popup-theme-white .gmb-reader-popup-btn {
    border-color: #6b7280;
    color: #6b7280;
}

.gmb-reader-popup-theme-white .gmb-reader-popup-btn:hover:not(.gmb-reader-active) {
    background: #6b7280;
    color: white;
    border-color: #6b7280;
}

.gmb-reader-popup-theme-white .gmb-reader-popup-btn.gmb-reader-active {
    background: #7C3AED;
    color: white;
    border-color: #7C3AED;
}

/* Yellow Theme Popup */
.gmb-reader-popup-theme-yellow {
    background: #f7f0e4;
    color: #303030;
}



.gmb-reader-popup-theme-yellow .gmb-reader-popup-btn {
    border-color: #8b7355;
    color: #8b7355;
}

.gmb-reader-popup-theme-yellow .gmb-reader-popup-btn:hover:not(.gmb-reader-active) {
    background: #8b7355;
    color: white;
    border-color: #8b7355;
}

.gmb-reader-popup-theme-yellow .gmb-reader-popup-btn.gmb-reader-active {
    background: #7C3AED;
    color: white;
    border-color: #7C3AED;
}

/* Dark Theme Popup */
.gmb-reader-popup-theme-dark {
    background: #0a0a0a;
    color: #d1d5db;
}



.gmb-reader-popup-theme-dark .gmb-reader-popup-btn {
    border-color: #6b7280;
    color: #6b7280;
}

.gmb-reader-popup-theme-dark .gmb-reader-popup-btn:hover:not(.gmb-reader-active) {
    background: #6b7280;
    color: white;
    border-color: #6b7280;
}

.gmb-reader-popup-theme-dark .gmb-reader-popup-btn.gmb-reader-active {
    background: #7C3AED;
    color: white;
    border-color: #7C3AED;
}

/* Focus Ruler Overlay */
.gmb-reader-focus-ruler {
    position: fixed;
    left: 0;
    right: 0;
    background-color: rgba(124, 58, 237, 0.7);
    pointer-events: none;
    z-index: 99999999;
    transition: all 0.2s ease;
    height: 40px;
    top: 30%;
}

/* Focus Ruler Settings Accordion */
.gmb-reader-ruler-accordion {
    margin-top: 12px;
    margin-bottom: 15px;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.gmb-reader-ruler-settings {
    padding: 0;
}

.gmb-reader-ruler-control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.gmb-reader-ruler-control:last-child {
    margin-bottom: 0;
}

.gmb-reader-ruler-label {
    font-size: 12px;
    font-weight: 500;
    color: inherit;
    margin: 0;
}

/* Toggle Switch */
.gmb-reader-ruler-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.gmb-reader-ruler-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.gmb-reader-ruler-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #374151;
    transition: 0.3s;
    border-radius: 20px;
}

.gmb-reader-ruler-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

.gmb-reader-ruler-switch input:checked + .gmb-reader-ruler-slider {
    background-color: #7C3AED;
}

.gmb-reader-ruler-switch input:checked + .gmb-reader-ruler-slider:before {
    transform: translateX(20px);
}

/* Color Picker */
.gmb-reader-ruler-colors {
    display: flex;
    gap: 6px;
    align-items: center;
    flex-wrap: wrap;
}

.gmb-reader-ruler-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.gmb-reader-ruler-color:hover {
    transform: scale(1.1);
}

.gmb-reader-ruler-color-active {
    border-color: #7C3AED;
    transform: scale(1.1);
}

/* Hex Color Input */
.gmb-reader-ruler-hex-container {
    display: flex;
    gap: 4px;
    align-items: center;
    margin-top: 8px;
}

.gmb-reader-ruler-hex-input {
    width: 70px;
    height: 20px;
    padding: 2px 4px;
    font-size: 11px;
    border: 1px solid #374151;
    border-radius: 4px;
    background: #0a0a0a;
    color: #d1d5db;
    outline: none;
}

.gmb-reader-ruler-hex-input:focus {
    border-color: #7C3AED;
}

.gmb-reader-ruler-hex-apply {
    padding: 2px 6px;
    font-size: 10px;
    background: #7C3AED;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.gmb-reader-ruler-hex-apply:hover {
    background: #6D28D9;
}

/* Theme-specific hex input styling */
.gmb-reader-popup-theme-white .gmb-reader-ruler-hex-input {
    background: #ffffff;
    color: #303030;
    border-color: #e5e7eb;
}

.gmb-reader-popup-theme-yellow .gmb-reader-ruler-hex-input {
    background: #f7f0e4;
    color: #303030;
    border-color: #d1c7b3;
}

/* Range Sliders */
.gmb-reader-ruler-slider-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    max-width: 180px;
}

.gmb-reader-ruler-range {
    flex: 1;
    height: 4px;
    background: #374151;
    border-radius: 2px;
    outline: none;
    appearance: none;
}

.gmb-reader-ruler-range::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background: #7C3AED;
    border-radius: 50%;
    cursor: pointer;
}

.gmb-reader-ruler-range::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: #7C3AED;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.gmb-reader-ruler-value {
    font-size: 11px;
    color: #9ca3af;
    min-width: 35px;
    text-align: right;
}

/* Theme-specific styling for ruler accordion */
.gmb-reader-popup-theme-white .gmb-reader-ruler-accordion {
    border-top-color: rgba(0, 0, 0, 0.1);
}

.gmb-reader-popup-theme-white .gmb-reader-ruler-slider {
    background-color: #e5e7eb;
}

.gmb-reader-popup-theme-white .gmb-reader-ruler-range {
    background: #e5e7eb;
}

.gmb-reader-popup-theme-yellow .gmb-reader-ruler-accordion {
    border-top-color: rgba(48, 48, 48, 0.15);
}

.gmb-reader-popup-theme-yellow .gmb-reader-ruler-slider {
    background-color: #d1c7b3;
}

.gmb-reader-popup-theme-yellow .gmb-reader-ruler-range {
    background: #d1c7b3;
}

.gmb-reader-popup-theme-dark .gmb-reader-ruler-accordion {
    border-top-color: rgba(255, 255, 255, 0.1);
}

/* Simple Text Overlay Outline */
.gmb-reader-outline-panel {
    position: fixed;
    top: 90px; /* Below the 70px header + 20px margin */
    left: 20px;
    z-index: 99999998;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    max-width: 280px;
}

.gmb-reader-outline-content {
    max-height: calc(100vh - 110px); /* Full height minus header (70px) and margins (40px) */
    overflow-y: auto;
    padding-right: 8px; /* Space for scrollbar */
}

.gmb-reader-outline-empty {
    padding: 4px 0;
    color: #9ca3af;
    font-size: 14px;
    font-style: italic;
}

/* Simple text outline links - no heading inheritance possible */
.gmb-reader-outline-text-link {
    display: block !important;
    text-decoration: none !important;
    border: none !important;
    background: none !important;
    text-align: left !important;
    transition: color 0.2s ease !important;
    /* Reset all possible inheritance */
    text-transform: none !important;
    letter-spacing: normal !important;
}

.gmb-reader-outline-text-link:hover {
    color: #7C3AED !important;
}

/* Theme-specific outline styling */
.gmb-reader-outline-theme-white .gmb-reader-outline-text-link {
    color: #374151 !important;
}

.gmb-reader-outline-theme-white .gmb-reader-outline-empty {
    color: #9ca3af !important;
}

.gmb-reader-outline-theme-yellow .gmb-reader-outline-text-link {
    color: #303030 !important;
}

.gmb-reader-outline-theme-yellow .gmb-reader-outline-empty {
    color: #8b7355 !important;
}

.gmb-reader-outline-theme-dark .gmb-reader-outline-text-link {
    color: #d1d5db !important;
}

.gmb-reader-outline-theme-dark .gmb-reader-outline-empty {
    color: #6b7280 !important;
}