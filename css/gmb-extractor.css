/* GMB Data Extractor - Content Script Styles */
/* BEM Methodology: block__element--modifier */

/* =================================
   NAP BUTTON COMPONENT
   ================================= */

.gmb-nap-button {
  background: #ff8c00;
  height: 28px;
  width: 100px;
  min-width: 100px;
  max-width: 100px;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  opacity: 0.9;
  vertical-align: middle;
  display: inline-block;
  flex-shrink: 0;
  flex-grow: 0;
  margin: 0;
  z-index: 1000;
  position: relative;
  box-sizing: border-box;
}

.gmb-nap-button:hover {
  background: #e67e00;
  opacity: 1;
  transform: translateY(-0.5px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.gmb-nap-button--success {
  background: #22c55e;
}

.gmb-nap-button--error {
  background: #ef4444;
}

/* NAP Button Container */
.gmb-nap-button-container {
  display: flex !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  gap: 8px !important;
}

/* =================================
   PERSISTENT POPUP COMPONENT
   ================================= */

.gmb-persistent-popup {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 600px;
  max-height: 80vh;
  background: #0f0f0f;
  color: #e5e5e5;
  border: 1px solid #2a2a2a;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.gmb-persistent-popup__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #1a1a1a;
  border-bottom: 1px solid #2a2a2a;
}

.gmb-persistent-popup__title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.gmb-persistent-popup__version {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 400;
}

.gmb-persistent-popup__close-btn {
  background: #262626;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  color: #000000;
  font-weight: bold;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(255, 68, 68, 0.3);
}

.gmb-persistent-popup__close-btn:hover {
  background: #ff6666;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(255, 68, 68, 0.4);
}

.gmb-persistent-popup__content {
  padding: 16px;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.gmb-persistent-popup__content::-webkit-scrollbar {
  width: 6px;
}

.gmb-persistent-popup__content::-webkit-scrollbar-track {
  background: #262626;
  border-radius: 3px;
}

.gmb-persistent-popup__content::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 3px;
}

/* =================================
   STATUS COMPONENT
   ================================= */

.gmb-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background: #262626;
  border-radius: 8px;
  border: 1px solid #333333;
}

.gmb-status__indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
  animation: gmb-pulse 2s infinite;
}

.gmb-status__indicator--active {
  background: #22c55e;
}

.gmb-status__text {
  color: #d1d5db;
  font-size: 14px;
  font-weight: 500;
}

/* =================================
   CONTROLS COMPONENT
   ================================= */

.gmb-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.gmb-controls--pro-list {
  display: flex;
  gap: 6px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.gmb-controls--pro-list .gmb-btn {
  flex: 1;
  padding: 10px 12px;
  font-size: 12px;
  font-weight: 700;
  min-height: 44px;
  min-width: 120px;
}

/* =================================
   BUTTON COMPONENT
   ================================= */

.gmb-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #262626;
  color: #ffffff;
  border: 1px solid #333333;
}

.gmb-btn:hover {
  background: #333333;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.gmb-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
  background: #1a1a1a;
}

.gmb-btn--secondary {
  /* background: linear-gradient(135deg, #6366f1, #8b5cf6); */
  border-color: #7c3aed;
}

.gmb-btn--secondary:hover {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
}

.gmb-btn--nap {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-color: #16a34a;
}

.gmb-btn--nap:hover {
  background: linear-gradient(135deg, #16a34a, #15803d);
}

.gmb-btn--service {
  /* background: linear-gradient(135deg, #f59e0b, #d97706); */
  border-color: #d97706;
}

.gmb-btn--service:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
}

.gmb-btn--attributes {
  /* background: linear-gradient(135deg, #8b5cf6, #a855f7); */
  border-color: #8b5cf6;
}

.gmb-btn--attributes:hover {
  background: linear-gradient(135deg, #7c3aed, #9333ea);
}

/* =================================
   DATA CONTAINER COMPONENT
   ================================= */

.gmb-data-container {
  background: #1a1a1a;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #2a2a2a;
  margin-bottom: 16px;
}

.gmb-data-container--service {
  display: none;
}

.gmb-data-container--attributes {
  display: none;
}

.gmb-data-container--hidden {
  display: none;
}

/* =================================
   EMPTY STATE COMPONENT
   ================================= */

.gmb-empty-state {
  text-align: center;
  color: #6b7280;
}

.gmb-empty-state__title {
  margin: 0 0 8px 0;
  color: #ffffff;
  font-size: 16px;
}

.gmb-empty-state__description {
  margin: 0;
  font-size: 13px;
  color: #9ca3af;
}

/* =================================
   SECTION TITLE COMPONENT
   ================================= */

.gmb-section-title {
  font-size: 14px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid #333333;
}

/* =================================
   PROGRESS COMPONENT
   ================================= */

.gmb-progress {
  padding: 12px;
  background: #262626;
  border-radius: 8px;
  margin-bottom: 12px;
}

.gmb-progress__text {
  color: #d1d5db;
  font-size: 13px;
  line-height: 1.5;
}

.gmb-progress__title {
  color: #fbbf24;
  font-weight: bold;
  margin-bottom: 12px;
}

.gmb-progress__title--service {
  color: #fbbf24;
}

.gmb-progress__title--attributes {
  color: #fbbf24;
}

.gmb-progress__title--review {
  color: #fbbf24;
}

.gmb-progress__title--success {
  color: #22c55e;
}

.gmb-progress__title--error {
  color: #ef4444;
}

.gmb-progress__description {
  color: #d1d5db;
  line-height: 1.5;
  margin-bottom: 12px;
}

.gmb-progress__details {
  color: #d1d5db;
  line-height: 1.6;
  font-size: 12px;
}

/* =================================
   RESULT COMPONENTS
   ================================= */

.gmb-result {
  margin-bottom: 16px;
  padding: 12px;
  background: #262626;
  border-radius: 8px;
}

.gmb-result--success {
  background: #262626;
  border-left: 3px solid #22c55e;
}

.gmb-result--error {
  background: #262626;
  border-left: 3px solid #ef4444;
}

.gmb-result__title {
  color: #22c55e;
  font-weight: bold;
  margin-bottom: 4px;
}

.gmb-result__title--error {
  color: #ef4444;
}

.gmb-result__meta {
  color: #d1d5db;
  font-size: 12px;
  margin-top: 4px;
}

.gmb-result__note {
  color: #9ca3af;
  font-size: 11px;
  margin-top: 2px;
}

/* Review Result Component */
.gmb-review-item {
  margin-bottom: 12px;
  padding: 12px;
  background: #1a1a1a;
  border-radius: 8px;
  border-left: 3px solid #6366f1;
}

.gmb-review-item__header {
  color: #fbbf24;
  font-weight: bold;
  margin-bottom: 4px;
}

.gmb-review-item__rating {
  margin-bottom: 4px;
}

.gmb-review-item__date {
  color: #9ca3af;
  font-size: 11px;
  margin-bottom: 8px;
}

.gmb-review-item__content {
  color: #d1d5db;
  font-size: 12px;
  line-height: 1.4;
}

.gmb-review-item__more {
  text-align: center;
  color: #9ca3af;
  font-style: italic;
  margin-top: 12px;
}

/* =================================
   SERVICE/ATTRIBUTE ANALYSIS COMPONENTS
   ================================= */

/* Analysis Sections */
.gmb-analysis-section {
  margin-bottom: 16px;
  padding: 12px;
  background: #1a1a1a;
  border-radius: 8px;
}

.gmb-analysis-section__title {
  font-weight: bold;
  margin-bottom: 8px;
}

.gmb-analysis-section__title--service {
  color: #f59e0b;
}

.gmb-analysis-section__title--category {
  color: #8b5cf6;
}

.gmb-analysis-section__title--business {
  color: #22c55e;
}

.gmb-analysis-section__title--attribute {
  color: #a855f7;
}

/* Analysis Items */
.gmb-analysis-item {
  margin-bottom: 4px;
  padding: 6px;
  background: #262626;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.gmb-analysis-item__name {
  color: #d1d5db;
  font-size: 12px;
  flex: 1;
}

.gmb-analysis-item__value {
  font-size: 11px;
  font-weight: bold;
  margin-left: 8px;
}

.gmb-analysis-item__value--service {
  color: #f59e0b;
}

.gmb-analysis-item__value--category {
  color: #8b5cf6;
}

.gmb-analysis-item__value--attribute {
  color: #a855f7;
}

/* More indicator for results */
.gmb-results-more {
  text-align: center;
  color: #9ca3af;
  font-style: italic;
  margin-top: 8px;
}

/* =================================
   INSTRUCTIONS COMPONENT
   ================================= */

.gmb-instructions__highlight {
  color: #60a5fa;
}

/* =================================
   STAR RATING COMPONENT
   ================================= */

.gmb-star-rating {
  display: inline-flex;
  align-items: center;
  gap: 1px;
  font-size: 14px;
  color: #fbbf24;
}

.gmb-star-rating__value {
  margin-left: 4px;
  font-size: 12px;
  color: #9ca3af;
}

/* =================================
   UTILITY CLASSES
   ================================= */

.gmb-hidden {
  display: none !important;
}

.gmb-flex {
  display: flex !important;
}

.gmb-inline-block {
  display: inline-block !important;
}

.gmb-text-center {
  text-align: center;
}

/* Text Colors */
.gmb-text-success {
  color: #22c55e;
}

.gmb-text-error {
  color: #ef4444;
}

.gmb-text-warning {
  color: #fbbf24;
}

.gmb-text-info {
  color: #60a5fa;
}

.gmb-text-muted {
  color: #9ca3af;
}

.gmb-text-primary {
  color: #d1d5db;
}

.gmb-text-white {
  color: #ffffff;
}

/* Font Weights */
.gmb-font-bold {
  font-weight: bold;
}

.gmb-font-normal {
  font-weight: 400;
}

.gmb-font-italic {
  font-style: italic;
}

/* Margins */
.gmb-mb-16 {
  margin-bottom: 16px;
}

.gmb-mb-12 {
  margin-bottom: 12px;
}

.gmb-mb-8 {
  margin-bottom: 8px;
}

.gmb-mb-6 {
  margin-bottom: 6px;
}

.gmb-mb-4 {
  margin-bottom: 4px;
}

.gmb-mt-4 {
  margin-top: 4px;
}

.gmb-mt-2 {
  margin-top: 2px;
}

.gmb-mt-12 {
  margin-top: 12px;
}

/* Line Heights */
.gmb-line-height-normal {
  line-height: 1.5;
}

.gmb-line-height-relaxed {
  line-height: 1.6;
}

.gmb-line-height-tight {
  line-height: 1.4;
}

/* Font Sizes */
.gmb-text-xs {
  font-size: 11px;
}

.gmb-text-sm {
  font-size: 12px;
}

.gmb-text-md {
  font-size: 13px;
}

/* =================================
   ANIMATIONS
   ================================= */

@keyframes gmb-pulse {
  0% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1); 
  }
  100% { 
    opacity: 1; 
    transform: scale(1); 
  }
} 