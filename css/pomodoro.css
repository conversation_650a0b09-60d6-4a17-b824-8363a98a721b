/* Pomodoro Timer Styles */

/* Dark scrollbar styling for task lists */
.tasks-list::-webkit-scrollbar {
    width: 8px;
}

.tasks-list::-webkit-scrollbar-track {
    background: rgba(124, 58, 237, 0.1);
    border-radius: 4px;
}

.tasks-list::-webkit-scrollbar-thumb {
    background: rgba(124, 58, 237, 0.4);
    border-radius: 4px;
    transition: background 0.2s ease;
}

.tasks-list::-webkit-scrollbar-thumb:hover {
    background: rgba(124, 58, 237, 0.6);
}

.tasks-list::-webkit-scrollbar-thumb:active {
    background: #7C3AED;
}

/* Firefox scrollbar styling */
.tasks-list {
    scrollbar-width: thin;
    scrollbar-color: rgba(124, 58, 237, 0.4) rgba(124, 58, 237, 0.1);
}

/* Visual Timer Display Enhancement */
@keyframes pulse-timer {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
    100% {
        transform: scale(1);
    }
}

.pomodoro-time-display {
    font-size: 48px !important;
    font-weight: 700;
    letter-spacing: 2px;
    text-shadow: 0 2px 10px rgba(124, 58, 237, 0.3);
    animation: pulse-timer 2s ease-in-out infinite;
}

.pomodoro-section.break-period .pomodoro-time-display {
    text-shadow: 0 2px 10px rgba(34, 197, 94, 0.3);
}

/* Progress Ring Container */
.pomodoro-progress-ring {
    position: relative;
    width: 180px;
    height: 180px;
    margin: 0 auto 16px;
}

.pomodoro-progress-ring svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.pomodoro-progress-ring circle {
    fill: none;
    stroke-width: 8;
    stroke-linecap: round;
}

.pomodoro-progress-ring .progress-bg {
    stroke: rgba(124, 58, 237, 0.1);
}

.pomodoro-progress-ring .progress-bar {
    stroke: #7C3AED;
    transition: stroke-dashoffset 0.5s ease;
}

.pomodoro-section.break-period .pomodoro-progress-ring .progress-bg {
    stroke: rgba(34, 197, 94, 0.1);
}

.pomodoro-section.break-period .pomodoro-progress-ring .progress-bar {
    stroke: #22c55e;
}

.pomodoro-progress-ring .timer-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

/* Pomodoro Accordion Styles */
.pomodoro-accordion {
    background: rgba(124, 58, 237, 0.05);
    border: 1px solid rgba(124, 58, 237, 0.2);
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* Break Period Green Background */
.pomodoro-section.break-period {
    background: rgba(34, 197, 94, 0.05);
    border-color: rgba(34, 197, 94, 0.2);
}

.pomodoro-section.break-period .pomodoro-header {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.2);
}

.pomodoro-section.break-period .pomodoro-header:hover {
    background: rgba(34, 197, 94, 0.15);
}

.pomodoro-section.break-period .pomodoro-status-indicator {
    background: #22c55e;
}

.pomodoro-section.break-period .pomodoro-accordion {
    background: rgba(34, 197, 94, 0.05);
    border-color: rgba(34, 197, 94, 0.2);
}

.pomodoro-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    cursor: pointer;
    background: rgba(124, 58, 237, 0.1);
    border-bottom: 1px solid rgba(124, 58, 237, 0.2);
    transition: all 0.2s ease;
}

.pomodoro-header:hover {
    background: rgba(124, 58, 237, 0.15);
}

.pomodoro-header.expanded {
    background: rgba(124, 58, 237, 0.2);
}

.pomodoro-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #d1d5db;
}

.pomodoro-icon {
    font-size: 12px;
    color: #7C3AED;
    transition: transform 0.2s ease;
}

.pomodoro-icon.expanded {
    transform: rotate(180deg);
}

.pomodoro-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.pomodoro-content.expanded {
    max-height: 100%;
}

.pomodoro-body {
    padding: 16px;
}

/* Timer Configuration Grid */
.pomodoro-timer-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

/* Grid-specific input group styling */
.pomodoro-timer-grid .pomodoro-input-group {
    margin-bottom: 0;
}

/* Input Styles */
.pomodoro-input-group {
    margin-bottom: 16px;
}

.pomodoro-input-label {
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
    font-weight: 500;
    color: #d1d5db;
}

.pomodoro-input {
    width: 100%;
    padding: 8px 12px;
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 6px;
    color: #d1d5db;
    font-size: 13px;
    transition: all 0.2s ease;
}

.pomodoro-input:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.pomodoro-select {
    width: 100%;
    padding: 8px 12px;
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 6px;
    color: #d1d5db;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pomodoro-select:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.pomodoro-textarea {
    width: 100%;
    padding: 8px 12px;
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 6px;
    color: #d1d5db;
    font-size: 13px;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    transition: all 0.2s ease;
}

.pomodoro-textarea:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

/* Checkbox Styles */
.pomodoro-checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.pomodoro-checkbox {
    width: 16px;
    height: 16px;
    accent-color: #7C3AED;
    cursor: pointer;
}

.pomodoro-checkbox-label {
    font-size: 13px;
    color: #d1d5db;
    cursor: pointer;
    margin: 0;
}

/* Slider Styles */
.pomodoro-slider {
    width: calc(100% - 60px);
    height: 6px;
    background: #2a2a2a;
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
}

.pomodoro-slider::-webkit-slider-thumb {
    width: 18px;
    height: 18px;
    background: #7C3AED;
    border-radius: 50%;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    transition: all 0.2s ease;
}

.pomodoro-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 0 8px rgba(124, 58, 237, 0.5);
}

.pomodoro-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #7C3AED;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

.pomodoro-slider::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 0 8px rgba(124, 58, 237, 0.5);
}

.pomodoro-slider-value {
    display: inline-block;
    width: 50px;
    text-align: right;
    font-size: 12px;
    color: #7C3AED;
    font-weight: 600;
    margin-left: 10px;
}

/* Sound Selector Group */
.pomodoro-sound-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pomodoro-sound-selector .pomodoro-select {
    flex: 1;
}

.pomodoro-preview-btn {
    background: rgba(124, 58, 237, 0.1);
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 4px;
    color: #7C3AED;
    cursor: pointer;
    padding: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.pomodoro-preview-btn:hover {
    background: rgba(124, 58, 237, 0.2);
    border-color: #7C3AED;
    color: #9333EA;
}

.pomodoro-preview-btn:active {
    background: rgba(124, 58, 237, 0.3);
    transform: scale(0.95);
}

.pomodoro-preview-btn svg {
    pointer-events: none;
}

/* Frequency Input Group */
.pomodoro-frequency-group {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #d1d5db;
}

.pomodoro-frequency-input {
    width: 60px;
    padding: 6px 8px;
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 4px;
    color: #d1d5db;
    font-size: 13px;
    text-align: center;
    transition: all 0.2s ease;
}

.pomodoro-frequency-input:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

/* Popup TODO List Styles */
.pomodoro-todo-section {
    background: rgba(124, 58, 237, 0.05);
    border: 1px solid rgba(124, 58, 237, 0.2);
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.pomodoro-todo-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    cursor: pointer;
    background: rgba(124, 58, 237, 0.1);
    border-bottom: 1px solid rgba(124, 58, 237, 0.2);
    transition: all 0.2s ease;
}

.pomodoro-todo-header:hover {
    background: rgba(124, 58, 237, 0.15);
}

.pomodoro-todo-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #d1d5db;
}

.pomodoro-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #7C3AED;
    animation: pulse-pomodoro 2s infinite;
}

@keyframes pulse-pomodoro {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.pomodoro-todo-icon {
    font-size: 12px;
    color: #7C3AED;
    transition: transform 0.2s ease;
}

.pomodoro-todo-icon.expanded {
    transform: rotate(180deg);
}

.pomodoro-todo-active-tasks {
    padding: 12px 16px;
    border-bottom: 1px solid rgba(124, 58, 237, 0.1);
}

.pomodoro-todo-task {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 0;
    font-size: 13px;
    color: #d1d5db;
}

.pomodoro-todo-task.completed {
    opacity: 0.6;
    text-decoration: line-through;
}

.pomodoro-todo-checkbox {
    width: 14px;
    height: 14px;
    accent-color: #7C3AED;
    cursor: pointer;
}

.pomodoro-todo-text {
    flex: 1;
    cursor: pointer;
}

.pomodoro-todo-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.pomodoro-todo-content.expanded {
    max-height: 300px;
}

.pomodoro-todo-add-form {
    padding: 16px;
    border-bottom: 1px solid rgba(124, 58, 237, 0.1);
}

.pomodoro-todo-input {
    width: 100%;
    padding: 8px 12px;
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 6px;
    color: #d1d5db;
    font-size: 13px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.pomodoro-todo-input:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.pomodoro-todo-add-btn {
    background: #7C3AED;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pomodoro-todo-add-btn:hover {
    background: #6d28d9;
    transform: translateY(-1px);
}

.pomodoro-todo-list {
    padding: 12px 16px;
    max-height: 200px;
    overflow-y: auto;
}

.pomodoro-todo-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 0;
    font-size: 13px;
    color: #d1d5db;
    border-bottom: 1px solid rgba(124, 58, 237, 0.1);
}

.pomodoro-todo-item:last-child {
    border-bottom: none;
}

.pomodoro-todo-item.completed {
    opacity: 0.6;
}

.pomodoro-todo-item.completed .pomodoro-todo-text {
    text-decoration: line-through;
}

.pomodoro-todo-delete {
    background: #dc2626;
    color: white;
    border: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 10px;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
}

.pomodoro-todo-item:hover .pomodoro-todo-delete {
    opacity: 1;
}

.pomodoro-todo-delete:hover {
    background: #b91c1c;
    transform: scale(1.1);
}

/* Timer Controls in Popup */
.pomodoro-timer-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: rgba(124, 58, 237, 0.05);
    border-top: 1px solid rgba(124, 58, 237, 0.1);
}

.pomodoro-control-btn {
    background: #7C3AED;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pomodoro-control-btn:hover {
    background: #6d28d9;
    transform: translateY(-1px);
}

.pomodoro-control-btn.secondary {
    background: #374151;
}

.pomodoro-control-btn.secondary:hover {
    background: #4b5563;
}

.pomodoro-control-btn.danger {
    background: #dc2626;
}

.pomodoro-control-btn.danger:hover {
    background: #b91c1c;
}

.pomodoro-timer-display {
    flex: 1;
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    color: #7C3AED;
}

/* Website Blocking Overlay */
.pomodoro-blocking-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(10, 10, 10, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999999;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
}

.pomodoro-blocking-content {
    background: #1a1a1a;
    border: 2px solid #7C3AED;
    border-radius: 12px;
    padding: 40px;
    max-width: 500px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.pomodoro-blocking-title {
    font-size: 24px;
    font-weight: 700;
    color: #7C3AED;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.pomodoro-blocking-icon {
    width: 32px;
    height: 32px;
    background: #7C3AED;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
}

.pomodoro-blocking-message {
    font-size: 16px;
    color: #d1d5db;
    line-height: 1.5;
    margin-bottom: 20px;
}

.pomodoro-blocking-timer {
    font-size: 20px;
    font-weight: 600;
    color: #7C3AED;
    margin-bottom: 20px;
}

.pomodoro-blocking-subtitle {
    font-size: 14px;
    color: #9ca3af;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 600px) {
    .pomodoro-blocking-content {
        margin: 20px;
        padding: 30px 20px;
    }
    
    .pomodoro-blocking-title {
        font-size: 20px;
    }
    
    .pomodoro-blocking-message {
        font-size: 14px;
    }
    
    .pomodoro-blocking-timer {
        font-size: 18px;
    }
}

/* Animation for active timer */
.pomodoro-active {
    animation: glow-pomodoro 3s ease-in-out infinite alternate;
}

@keyframes glow-pomodoro {
    from {
        box-shadow: 0 0 5px rgba(124, 58, 237, 0.2);
    }
    to {
        box-shadow: 0 0 15px rgba(124, 58, 237, 0.6);
    }
}

/* Tasks Section Independent Styles */
.tasks-section {
    background: rgba(124, 58, 237, 0.05);
    border: 1px solid rgba(124, 58, 237, 0.2);
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.tasks-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    cursor: pointer;
    background: rgba(124, 58, 237, 0.1);
    border-bottom: 1px solid rgba(124, 58, 237, 0.2);
    transition: all 0.2s ease;
}

.tasks-header:hover {
    background: rgba(124, 58, 237, 0.15);
}

.tasks-header.expanded {
    background: rgba(124, 58, 237, 0.2);
}

.tasks-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #d1d5db;
}

.tasks-count {
    background: #7C3AED;
    color: white;
    font-size: 11px;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.tasks-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #7C3AED;
    animation: pulse-tasks 2s infinite;
}

@keyframes pulse-tasks {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.tasks-icon {
    font-size: 12px;
    color: #7C3AED;
    transition: all 0.2s ease;
    padding: 4px 6px;
    border-radius: 4px;
    border: 1px solid transparent;
}

/* Always show border when tasks are present (compact mode) - visual cue for new users */
.tasks-section.compact-mode .tasks-icon {
    padding: 6px 8px; /* Larger clickable area */
    border-color: rgba(124, 58, 237, 0.4); /* Always visible border */
    background: rgba(124, 58, 237, 0.08); /* Always visible background */
}

.tasks-section.compact-mode .tasks-icon:hover {
    border-color: rgba(124, 58, 237, 0.7); /* Enhanced on hover */
    background: rgba(124, 58, 237, 0.15); /* Enhanced on hover */
}

.tasks-icon.expanded {
    transform: rotate(180deg);
}

/* Compact Toggle Button Styles */
.tasks-compact-toggle {
    position: absolute;
    top: 8px; /* Position over the top of tasks area in compact mode */
    right: 8px;
    width: 24px; /* Larger clickable area */
    height: 24px; /* Larger clickable area */
    background: rgba(124, 58, 237, 0.08); /* Always visible background */
    border: 1px solid rgba(124, 58, 237, 0.4); /* Always visible border */
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 9999;
}

.tasks-compact-toggle:hover {
    background: rgba(124, 58, 237, 0.15); /* Enhanced on hover */
    border-color: rgba(124, 58, 237, 0.7); /* Enhanced on hover */
}

.tasks-compact-icon {
    font-size: 12px;
    color: #7C3AED;
    transition: transform 0.2s ease;
}

.tasks-compact-icon.expanded {
    transform: rotate(180deg);
}

/* Conditional Display Rules */
.tasks-section.compact-mode .tasks-header {
    display: none;
}

.tasks-section.compact-mode .tasks-compact-toggle {
    display: flex !important;
}


.tasks-section.compact-mode .tasks-active-tasks {
    padding: 12px 40px 12px 16px; /* Extra right padding for compact toggle */
}

.tasks-section:not(.compact-mode) .tasks-compact-toggle {
    display: none !important;
}

.tasks-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.tasks-content.expanded {
    max-height: 400px;
}

.tasks-active-tasks {
    padding: 12px 16px;
    border-bottom: 1px solid rgba(124, 58, 237, 0.1);
}

.tasks-add-form {
    padding: 16px;
    border-bottom: 1px solid rgba(124, 58, 237, 0.1);
    display: flex;
    gap: 8px;
    align-items: center;
}

.tasks-new-task-input {
    flex: 1;
    padding: 8px 12px;
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 6px;
    color: #d1d5db;
    font-size: 13px;
    transition: all 0.2s ease;
}

.tasks-new-task-input:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.tasks-new-task-input::placeholder {
    color: #6b7280;
}

.tasks-add-task-btn {
    background: #7C3AED;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tasks-add-task-btn:hover {
    background: #6d28d9;
    transform: translateY(-1px);
}

.tasks-list {
    padding: 12px 16px;
    max-height: 200px;
    overflow-y: auto;
}

.tasks-task-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #d1d5db;
    border-bottom: 1px solid rgba(124, 58, 237, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
}

.tasks-task-item.selected {
    background-color: rgba(124, 58, 237, 0.1);
    border-color: #7C3AED;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
}

.tasks-task-item .tasks-task-checkbox {
    margin-right: 8px !important;
}
.tasks-task-item:last-child {
    border-bottom: none;
}

.tasks-task-item.completed {
    opacity: 0.6;
}

.tasks-task-item.completed .tasks-task-text {
    text-decoration: line-through;
    margin-left: 12px!important;
}

.tasks-task-checkbox {
    width: 14px;
    height: 14px;
    accent-color: #7C3AED;
    cursor: pointer;
}

.tasks-task-text {
    flex: 1;
    cursor: pointer;
    margin-right: 8px; /* Add space for selection grip */
}

.tasks-task-delete {
    background: #dc2626;
    color: white;
    border: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 10px;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
}

.tasks-task-item:hover .tasks-task-delete {
    opacity: 1;
}

.tasks-task-delete:hover {
    background: #b91c1c;
    transform: scale(1.1);
}

/* Selection grip for tasks */
.tasks-selection-grip {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 20px;
    margin-right: 4px;
    background: rgba(124, 58, 237, 0.1);
    border: 1px solid rgba(124, 58, 237, 0.2);
    border-radius: 4px;
    color: #7C3AED;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    opacity: 0.6;
    transition: all 0.2s ease;
    user-select: none;
    position: relative;
}

.tasks-selection-grip:hover {
    background: rgba(124, 58, 237, 0.2);
    border-color: #7C3AED;
    opacity: 1;
    transform: scale(1.05);
}

.tasks-task-item.selected .tasks-selection-grip {
    background: rgba(124, 58, 237, 0.3);
    border-color: #7C3AED;
    opacity: 1;
}


/* Task reorder controls */
.tasks-reorder-controls {
    display: flex;
    flex-direction: column;
    gap: 2px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.tasks-task-item:hover .tasks-reorder-controls {
    opacity: 1;
}

.tasks-reorder-btn {
    background: rgba(124, 58, 237, 0.1);
    border: 1px solid rgba(124, 58, 237, 0.3);
    color: #7C3AED;
    border-radius: 4px;
    width: 20px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 10px;
    margin-right: 8px;
}

.tasks-reorder-btn:hover {
    background: rgba(124, 58, 237, 0.2);
    border-color: #7C3AED;
    transform: scale(1.1);
}

.tasks-reorder-btn:active {
    transform: scale(0.95);
}

.tasks-reorder-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    background: rgba(124, 58, 237, 0.05);
}

.tasks-reorder-btn:disabled:hover {
    transform: none;
    background: rgba(124, 58, 237, 0.05);
    border-color: rgba(124, 58, 237, 0.3);
}

.tasks-reorder-btn svg {
    pointer-events: none;
}

/* Compact task display for active tasks */
.tasks-task {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
    font-size: 13px;
    color: #d1d5db;
}

.tasks-task.completed {
    opacity: 0.6;
    text-decoration: line-through;
}

/* Tasks section when empty */
.tasks-empty {
    padding: 20px;
    text-align: center;
    color: #6b7280;
    font-style: italic;
    font-size: 13px;
}

/* Tasks Display Settings */
.tasks-display-settings {
    padding: 12px 16px;
    border-top: 1px solid rgba(124, 58, 237, 0.1);
    background: rgba(124, 58, 237, 0.02);
}

.tasks-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.tasks-input-label {
    font-size: 13px;
    color: #d1d5db;
    font-weight: 500;
    min-width: 80px;
}

.tasks-select {
    flex: 1;
    padding: 6px 10px;
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 4px;
    color: #d1d5db;
    font-size: 13px;
    transition: all 0.2s ease;
}

.tasks-select:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

/* Header Controls Layout */
.pomodoro-header-controls {
    padding: 8px 8px !important;
    border-bottom: 1px solid rgba(124, 58, 237, 0.1);
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Timer Display Styles */
.pomodoro-timer-display {
    text-align: center;
    padding: 16px 0;
}

.pomodoro-time-display {
    font-size: 32px;
    font-weight: 700;
    color: #7C3AED;
    font-family: 'Courier New', monospace;
    margin-bottom: 8px;
    text-shadow: 0 0 10px rgba(124, 58, 237, 0.3);
}

.pomodoro-session-info {
    font-size: 14px;
    color: #d1d5db;
    font-weight: 500;
    opacity: 0.8;
}

/* Break period timer styling */
.pomodoro-section.break-period .pomodoro-time-display {
    color: #22c55e;
    text-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
}

/* Header Right Side Layout */
.pomodoro-header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Mode Selection Styles - Icon Version */
.pomodoro-mode-selection {
    display: flex;
    gap: 4px;
    align-items: center;
}

.pomodoro-timer-controls {
    display: flex;
    gap: 8px;
    justify-content: center;
}

/* Icon Mode Buttons */
.pomodoro-mode-btn {
    background: transparent;
    border: 1px solid transparent;
    color: #888888; /* Light grey default */
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.pomodoro-mode-btn:hover {
    color: #ffffff; /* White on hover */
    transform: scale(1.05);
}

.pomodoro-mode-btn--active {
    color: #22c55e; /* Green when active/selected */
}

.pomodoro-mode-btn--active:hover {
    color: #22c55e; /* Keep green on hover when active */
}

/* SVG Icon Styling */
.pomodoro-mode-btn svg {
    transition: all 0.2s ease;
    pointer-events: none;
}

.pomodoro-mode-btn:hover svg {
    filter: drop-shadow(0 0 3px currentColor);
}

/* Additional Settings Styles */
.pomodoro-checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.pomodoro-checkbox {
    width: 16px;
    height: 16px;
    accent-color: #7C3AED;
    cursor: pointer;
}

.pomodoro-checkbox-label {
    font-size: 13px;
    color: #d1d5db;
    cursor: pointer;
    user-select: none;
}

.pomodoro-slider {
    width: 100%;
    height: 6px;
    background: #1a1a1a;
    border-radius: 3px;
    outline: none;
    margin-right: 10px;
    appearance: none;
}

.pomodoro-slider::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background: #7C3AED;
    border-radius: 50%;
    cursor: pointer;
}

.pomodoro-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: #7C3AED;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.pomodoro-slider-value {
    font-size: 12px;
    color: #7C3AED;
    font-weight: 600;
    min-width: 40px;
    text-align: right;
}

.pomodoro-frequency-group {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #d1d5db;
}

.pomodoro-frequency-input {
    width: 60px;
    padding: 4px 8px;
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 4px;
    color: #d1d5db;
    font-size: 13px;
    text-align: center;
}

.pomodoro-frequency-input:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.pomodoro-textarea {
    width: 100%;
    min-height: 80px;
    padding: 8px 12px;
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 6px;
    color: #d1d5db;
    font-size: 13px;
    font-family: inherit;
    resize: vertical;
    transition: all 0.2s ease;
}

.pomodoro-textarea:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.pomodoro-textarea::placeholder {
    color: #6b7280;
}

/* Sound Selector Group - Updated */
.pomodoro-sound-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pomodoro-sound-selector .pomodoro-select {
    flex: 1;
}

/* Updated Preview Button */
.pomodoro-preview-btn {
    background: rgba(124, 58, 237, 0.1);
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 4px;
    color: #7C3AED;
    cursor: pointer;
    padding: 6px 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.pomodoro-preview-btn:hover {
    background: rgba(124, 58, 237, 0.2);
    border-color: #7C3AED;
    color: #9333EA;
    transform: scale(1.05);
}

.pomodoro-preview-btn.playing {
    background: #7C3AED;
    border-color: #7C3AED;
    color: white;
    animation: pulse-playing 1.5s ease-in-out infinite;
}

.pomodoro-preview-btn.playing:hover {
    background: #9333EA;
    border-color: #9333EA;
    color: white;
    transform: scale(1.05);
}

@keyframes pulse-playing {
    0% {
        box-shadow: 0 0 0 0 rgba(124, 58, 237, 0.7);
    }
    70% {
        box-shadow: 0 0 0 8px rgba(124, 58, 237, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(124, 58, 237, 0);
    }
}

.pomodoro-preview-btn:active {
    background: rgba(124, 58, 237, 0.3);
    transform: scale(0.95);
}

.pomodoro-preview-btn svg {
    pointer-events: none;
    transition: all 0.2s ease;
}

.pomodoro-preview-btn:hover svg {
    filter: drop-shadow(0 0 3px rgba(124, 58, 237, 0.4));
}


/* Reset to Defaults Button */
.pomodoro-reset-btn {
    width: 100%;
    padding: 10px 16px;
    background: rgba(124, 58, 237, 0.1);
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 6px;
    color: #d1d5db;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.pomodoro-reset-btn:hover {
    background: rgba(124, 58, 237, 0.2);
    border-color: #7C3AED;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.2);
}

.pomodoro-reset-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(124, 58, 237, 0.2);
}

/* Cycle Display */
#pomodoroCycleDisplay {
    margin-left: 8px;
    padding: 2px 8px;
    background: rgba(124, 58, 237, 0.15);
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    color: #7C3AED;
    white-space: nowrap;
}