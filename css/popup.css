/* SEO Time Machines Popup Styles - BEM Methodology */

/* Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Base */
.popup {
    width: 750px;
    min-height: 400px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background: #0f0f0f;
    color: #e5e5e5;
    overflow-x: hidden;
}

.popup__container {
    padding: 16px;
    position: relative;
}

/* Header Block */
.header {
    text-align: center;
    margin-bottom: 16px;
    background: #1a1a1a;
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #2a2a2a;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    gap: 12px;
    justify-content: flex-start;
}

.header__logo {
    width: 120px;
    height: 120px;
    border-radius: 8px;
    background: #000000;
    object-fit: contain;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.header__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
}

.header__title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.header__title {
    color: #fff;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: -0.5px;
    text-align: left;
    margin: 0;
}

.header__version {
    font-size: 12px;
    color: #9ca3af;
    font-weight: 400;
}

.header__status-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    margin-left: 2px;
    width: 100%;
}

.header__status-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.header__actions {
    display: flex;
    align-items: center;
}

.header__button-group {
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Status Block */
.status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 12px;
    padding: 8px 16px;
    background: #262626;
    border-radius: 8px;
    border: 1px solid #333333;
}

.status__indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ef4444;
    animation: pulse 2s infinite;
}

.status__indicator--active {
    background: #22c55e;
}

.status__text {
    color: #d1d5db;
    font-size: 12px;
    font-weight: 500;
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
}

/* Controls Block */
.controls {
    display: flex;
    gap: 16px;
    margin-bottom: 8px!important;
}

.controls--compact {
    margin-bottom: 12px;
}

/* Button Block */
.btn {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgb(15, 15, 15);
    color: #ffffff;
    border: 1px solid #333333;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    background: #333333;
    transform: translateY(-1px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    border-color: #404040;
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
    background: #1a1a1a;
}

.btn:disabled::before {
    display: none;
}

.btn--secondary {
    border-color: #7c3aed;
    color: #ffffff;
}

.btn--secondary:hover {
    background: linear-gradient(135deg, #7c3aed, #a855f7);
    border-color: #8b5cf6;
}

.btn--secondary:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.btn--universal {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-color: #6366f1;
    color: #ffffff;
    font-weight: 700;
    position: relative;
    overflow: hidden;
}

.btn--universal:hover {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-color: #4f46e5;
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(99, 102, 241, 0.3);
}

.btn--universal:disabled {
    background: #374151;
    border-color: #4b5563;
    color: #9ca3af;
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn--nap {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    border-color: #16a34a;
    color: #ffffff;
}

.btn--nap:hover {
    background: linear-gradient(135deg, #16a34a, #15803d);
    border-color: #15803d;
}

.btn--nap:disabled {
    background: #1a1a1a;
    border-color: #333333;
}

.btn--nap.btn--copied {
    background: #22c55e;
    border-color: #22c55e;
}

.btn--orange {
    border-color: #f59e0b;
}

.btn--orange:hover {
    background: linear-gradient(135deg, #d97706, #ea580c);
    border-color: #d97706;
}

.btn--orange:disabled {
    background: #1a1a1a;
    border-color: #333333;
}

.btn--neutral {
    border-color: #4b5563;
    /*color: #ffffff;*/
    height: 32px;
    padding: 0 12px;
    font-size: 13px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    min-width: 28%!important;
    max-width: 100%!important;
}

.btn--neutral:hover {
    background: #4b5563;
    border-color: #6b7280;
}

.btn--neutral:disabled {
    background: #1a1a1a;
    border-color: #333333;
}

.btn--mini {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    background: #333333;
    color: #d1d5db;
    border: 1px solid #404040;
    white-space: nowrap;
    min-width: auto;
    height: 24px;
    line-height: 1;
}

.btn--mini:hover {
    background: #404040;
    border-color: #525252;
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.btn--mini:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.2);
}

.btn--mini.btn--orange {
    /*background: linear-gradient(135deg, #f59e0b, #f97316);*/
    border-color: #f59e0b;
    font-size: 12px;
    height: 30px;
}

.btn--mini.btn--orange:hover {
    background: linear-gradient(135deg, #d97706, #ea580c);
    border-color: #d97706;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn--mini.btn--orange:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(245, 158, 11, 0.2);
}

.btn--mini.btn--reload {
    background: transparent;
    border: 1px solid #333333;
    color: #6b7280;
    font-size: 11px;
    opacity: 0.7;
    width: 24px;
    height: 24px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.btn--mini.btn--reload svg {
    width: 12px;
    height: 12px;
    transition: transform 0.2s ease;
}

.btn--mini.btn--reload:hover {
    background: #262626;
    border-color: #404040;
    color: #d1d5db;
    opacity: 1;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn--mini.btn--reload:hover svg {
    transform: rotate(180deg);
}

.btn--mini.btn--reload:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}



/* Claim Status Block */
.claim-status {
    font-size: 10px;
    font-weight: 600;
    padding: 7px 8px;
    border-radius: 4px;
    white-space: nowrap;
    height: 26px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.claim-status--claimed {
    color: #22c55e;
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.claim-status--unclaimed {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Loading Block */
.loading {
    display: none;
    text-align: center;
    padding: 24px;
    color: #d1d5db;
}

.loading--show {
    display: block;
}

.loading__spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #333333;
    border-top: 2px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Search Block */
.search {
    width: 70%;
    display: inline-block;
    margin-right: 8px;
}

.search__input {
    width: 100%;
    padding: 8px 12px;
    background: #1a1a1a;
    border: 1px solid #525252;
    border-radius: 4px;
    color: #ffffff;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    height: 32px;
}

.search__input:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px rgba(107, 114, 128, 0.3);
}

.search__input::placeholder {
    color: #6b7280;
    font-size: 12px;
}

/* Data Container Block */
.data-container {
    background: #1a1a1a;
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #2a2a2a;
    max-height: 400px;
    overflow-y: auto;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

.data-container::-webkit-scrollbar {
    width: 6px;
}

.data-container::-webkit-scrollbar-track {
    background: #262626;
    border-radius: 3px;
}

.data-container::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 3px;
}

.data-container::-webkit-scrollbar-thumb:hover {
    background: #525252;
}

/* Data Section Block */
.data-section {
    margin-bottom: 20px;

}

.data-section__title {
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 1px solid #333333;
    letter-spacing: -0.2px;
}

/* Data Search Container Wrapper - Always visible */
.data-search-container-wrapper {
    display: block !important;
    visibility: visible !important;
    position: relative;
    z-index: 100;
    background: #0a0a0a;
    border-bottom: 1px solid rgba(124, 58, 237, 0.1);
    padding: 12px 16px;
    margin-bottom: 8px;
}

/* Data Search Container */
.data-search-container {
    position: relative;
    margin-bottom: 16px;
    margin-top: 12px;
    display: flex;
    align-items: center;
}

.data-search {
    width: 100%;
    padding: 10px 14px;
    padding-right: 36px;
    background: #1a1a1a;
    border: 1px solid rgba(124, 58, 237, 0.3);
    border-radius: 6px;
    color: #d1d5db;
    font-size: 13px;
    font-family: inherit;
    transition: all 0.2s ease;
}

.data-search:focus {
    outline: none;
    border-color: #7C3AED;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.data-search::placeholder {
    color: #6b7280;
    font-size: 12px;
}

.data-search-clear {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: #9ca3af;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0;
    pointer-events: none;
}

.data-search-container:hover .data-search-clear,
.data-search:focus + .data-search-clear,
.data-search-clear.visible {
    opacity: 1;
    pointer-events: all;
}

.data-search-clear:hover {
    background: rgba(124, 58, 237, 0.2);
    color: #7C3AED;
    transform: translateY(-50%) scale(1.1);
}

/* Enhanced visual feedback for search states */
.data-search.search-active {
    border-color: #7C3AED !important;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.3) !important;
}

.data-search.search-filtered {
    border-color: #f59e0b !important;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3) !important;
    background: rgba(245, 158, 11, 0.05) !important;
}

.data-search-container.search-active {
    background: rgba(124, 58, 237, 0.02);
    border-radius: 8px;
    padding: 2px;
}

.data-search-container.search-filtered {
    background: rgba(245, 158, 11, 0.05);
    border-radius: 8px;
    padding: 2px;
}

.data-search-container.search-filtered::after {
    content: "Filtered - Press Escape or × to clear";
    position: absolute;
    top: -20px;
    left: 0;
    font-size: 11px;
    color: #f59e0b;
    font-weight: 600;
    background: rgba(245, 158, 11, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

/* Search highlighting for data items */
.data-item.search-dimmed {
    opacity: 0.3;
    transition: opacity 0.2s ease;
}

.data-item.search-highlight {
    opacity: 1;
    background: rgba(124, 58, 237, 0.1);
    border-radius: 4px;
    padding: 4px 0;
    margin: 4px -8px;
    padding-left: 8px;
    padding-right: 8px;
}

/* Data Item Block */
.data-item {
    margin-bottom: 8px;
    padding: 12px;
    background: rgb(15, 15, 15);

    ;
    border-radius: 8px;
    border-left: 3px solid #6366f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.data-item:hover {
    background: #2a2a2a;
    transform: translateX(2px);
}

.data-item:last-child {
    margin-bottom: 0;
}

.data-item__label {
    color: #fbbf24;
    font-weight: 600;
    font-size: 12px;
    min-width: 100px;
    flex-shrink: 0;
    letter-spacing: 0.2px;
}

.data-item__value {
    color: #d1d5db;
    font-size: 13px;
    line-height: 1.4;
    word-break: break-all;
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.data-item__raw-value {
    color: #d1d5db;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    flex: 1;
    font-size: 12px;
}

/* Progress Bar Block */
.progress-bar {
    width: 100%;
    background: #333;
    border-radius: 4px;
    height: 8px;
    margin-left: 8px;
}

.progress-bar__fill {
    width: 0%;
    background: #6366f1;
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Copy Button Block */
.copy-btn {
    background: #333333;
    border: 1px solid #404040;
    color: #ffffff;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.copy-btn:hover {
    background: #404040;
    border-color: #525252;
    transform: translateY(-1px);
}

.copy-btn--copied,
.btn--copied {
    background: #22c55e;
    border-color: #22c55e;
    color: #ffffff;
}

/* Empty State Block */
.empty-state {
    text-align: center;
    padding: 32px 16px;
    color: #6b7280;
}

.empty-state__title {
    margin-bottom: 8px;
    font-size: 16px;
    color: #ffffff;
    font-weight: 600;
    letter-spacing: -0.2px;
}

.empty-state__description {
    font-size: 13px;
    line-height: 1.5;
    color: #9ca3af;
}

/* State modifiers */
.data-item--error {
    background: #2d1b1b;
    border-left-color: #ef4444;
    color: #fca5a5;
}

.data-item--success {
    background: #1b2d1b;
    border-left-color: #22c55e;
    color: #86efac;
}

/* Shortcut Block */
.shortcut {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #262626;
    border-radius: 12px;
    margin-bottom: 8px;
    border: 1px solid #333333;
    transition: all 0.2s ease;
    cursor: pointer;
}

.shortcut:hover {
    background: #2a2a2a;
    border-color: #404040;
    transform: translateX(2px);
}

.shortcut__name {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
}

.shortcut__key {
    background: #404040;
    color: #d1d5db;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    font-family: 'SF Mono', 'Monaco', monospace;
    border: 1px solid #525252;
}


/* Google Search Page Specific Styles */
.google-search-page .controls:nth-child(3),
.google-search-page .controls:nth-child(4),
.google-search-page .controls:nth-child(5) {
    display: none !important;
}

.google-search-page #exportServicesBtn {
    display: none !important;
}

.google-search-page #reviewAnalysisContainer .data-section__title,
.google-search-page #reviewProgress,
.google-search-page #reviewProgressBar {
    display: none !important;
}

.google-search-page #reviewAnalysisContainer .data-section__title,
.google-search-page #singleReviewContainer .data-section__title {
    font-size: 18px;
    margin-bottom: 16px;
}

/* Quick Timer Section */
.quick-timer-section {
    background: #1a1a1a;
    border-radius: 12px;
    border: 1px solid #2a2a2a;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    margin-bottom: 16px;
}

.timer-controls-form {
    padding: 16px;
}

/* Quick Actions Block */
.quick-actions-header {
    margin-bottom: 8px;
}

.quick-actions-title {
    color: #e5e5e5;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    text-align: left;
    letter-spacing: -0.2px;
}

.quick-actions-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

#quickActionsControls {
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 8px;
    padding: 12px;
}

#quickActionsControls .btn {
    flex: 0 1 auto;
    min-width: 120px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
}

#quickActionsControls .quick-actions-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

#quickActionsControls .quick-actions-buttons .btn {
    padding: 4px 8px;
    font-size: 14px;
    text-align: center;
    min-width: 50px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

#quickActionsControls .quick-actions-buttons .btn--mini {
    min-width: 24px;
    margin-left: 4px;
    height: 24px;
}

/* Profiles Block */
#profilesControls {
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;
}

.profiles-header {
    margin-bottom: 8px;
}

.profiles-title {
    color: #e5e5e5;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    text-align: left;
    letter-spacing: -0.2px;
}

.profiles-buttons {
    display: block;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

.profiles-buttons > .btn--secondary {
    padding: 4px 8px!important;
    min-width: none;
}

.btn--profile {
    flex: 0 1 auto;
    min-width: 120px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    background: #262626;
    border: 1px solid #404040;
    color: #e5e5e5;
}

.btn--profile:hover {
    background: #333333;
    border-color: #505050;
}

.btn--profile {
    flex: 0 1 auto;
    min-width: 120px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    background: #262626;
    border: 1px solid #404040;
    color: #e5e5e5;
}

.btn--profile:hover {
    background: #333333;
    border-color: #505050;
}