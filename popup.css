body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding: 25px 25px 15px 25px;
    background-color: #f0f4f9;
    color: #333;
}

:root {
    --ssr-color: #4CAF50; /* default SSR (green) */
    --csr-color: #f44336; /* default CSR (red) */
}

.popup-container {
    text-align: center;
    width: 480px;
}

h1 {
    color: #444;
    font-size: 20px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input { 
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

#statusText {
    margin-top: 10px;
    font-size: 14px;
    color: #666;
}

.logo-title{
    display:flex;
    align-items: center;
    justify-content: center;
}

.popup-container img{
    width:40px;
    height: 40px;
    margin-right: 10px;
}

.popup-container p{
    line-height: 1.5;
}

#statusText{
    font-weight: 600;
}

.note{
    margin:0;
    color: #8f8f8f;
}

a{
    color:#0a66c2;
    font-weight: 600;
    text-decoration: none;
    margin-top:20px;
}

#myChart{
    width:250px !important;
    height:auto !important;
    margin:auto;
    max-height:500px !important;
}

.top-part{
    border-radius: 15px;
    background-color: #fff;
    border:none;
    padding:12px 10px;
    margin-top: 5px;
}

#footer {
    display: flex;
    justify-content: center;
    text-align: center;
    padding: 10px;
    color: #5f6368;
    font-size: 12px;
    margin-top: 5px;
    border-top: #fff;
}
#footer a {
    color: #5f6368;
    text-decoration: none;
    margin-top:0 !important;
}
#footer a:hover {
    text-decoration: underline;
    color: transparent;
    background: linear-gradient(90deg, #2dbdad 0%, #30a3c5 10%, #804cbd 20%);
    background-size: 400% 100%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;
    font-weight: bold;
    -webkit-text-fill-color: transparent;
}
#heart img{
    color: #f18989;
    width:15px;
    height:15px;
    margin-right: 0px;
}

.promo-text {
    text-align: center;
    padding: 15px;
    margin: 5px 0 0 0;
    background-color: #f8f9fa;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.5;
    color: #4a4a4a;
}

.promo-text a {
    color: #2196F3;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.promo-text a:hover {
    color: #1976D2;
    text-decoration: underline;
}

.sponsored-badge {
    display: inline;
    background-color: #f8f9fa;
    color: #8f8f8f;
    font-size: 10px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    margin-left: 8px;
    vertical-align: middle;
}

/* Tab Navigation */
.tab-nav {
    display: flex;
    background-color: #f0f4f9;
    border-radius: 15px;
    padding: 5px;
    margin: 10px 0;
    border: 1px solid #e0e0e0;
}

.tab-btn {
    flex: 1;
    padding: 10px 15px;
    border: none;
    background-color: transparent;
    border-radius: 12px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    transition: all 0.3s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.tab-btn:hover {
    background-color: #fff;
    color: #444;
}

.tab-btn.active {
    background-color: #fff;
    color: #2196F3;
    border: 1px solid #e0e0e0;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.new-badge {
    background-color: #2196F3;
    color: white;
    font-size: 8px;
    font-weight: 600;
    padding: 2px 4px;
    border-radius: 6px;
    margin-left: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    vertical-align: middle;
}

/* Element Report Styles */
#element-report-tab {
    padding: 0;
}

#element-report-tab > div {
    background-color: #fff;
    border-radius: 15px;
    margin-top: 5px;
    min-height: 350px;
}

#elementDetails {
    line-height: 1.5;
    color: #444;
    text-align: left;
    padding: 0 15px;
}

#elementDetails strong {
    color: #333;
    margin-bottom: 8px;
    display: block;
    font-size: 13px;
}

#elementChart {
    margin: 0 auto;
    display: block;
}

/* Use CSS variables for chart legend samples if any custom legend used in future */

#elementReportPlaceholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

/* Element Breakdown Styles */
.element-breakdown {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 15px;
}

.element-grid {
    display: grid;
    gap: 12px;
}

.element-item {
    background: white;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #e0e0e0;
}

.element-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.element-header-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.element-name {
    font-weight: 600;
    font-size: 13px;
    color: #333;
}

.element-total {
    font-weight: 600;
    font-size: 13px;
    color: #666;
    background: #f0f0f0;
    padding: 2px 8px;
    border-radius: 12px;
}

.element-bar {
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
    display: flex;
    margin-bottom: 8px;
}

.element-bar-ssr {
    background: var(--ssr-color);
    height: 100%;
}

.element-bar-csr {
    background: var(--csr-color);
    height: 100%;
}

.element-counts {
    display: flex;
    gap: 12px;
    font-size: 11px;
    color: #666;
}

.ssr-count {
    color: var(--ssr-color);
    font-weight: 500;
}

.csr-count {
    color: var(--csr-color);
    font-weight: 500;
}

.word-count {
    color: #999;
    font-size: 10px;
}

/* Element visibility toggle button */
.element-visibility-toggle {
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    width: 28px;
    height: 28px;
}

.element-visibility-toggle:hover {
    background: #f0f0f0;
}

.element-visibility-toggle.visible {
    background: #e8f5e9;
    border-color: #4CAF50;
}

.element-visibility-toggle .visibility-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    margin: 0 !important;
}

.visibility-control-btn img {
    margin: 0 !important;
}

/* Show/Hide All buttons */
.visibility-control-btn {
    background: #f0f0f0;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 4px 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #666;
    transition: all 0.2s ease;
}

.visibility-control-btn:hover {
    background: #e0e0e0;
    color: #333;
}

.visibility-control-btn .material-symbols-outlined {
    font-size: 14px;
    font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 24;
}

/* Icon button styles */
.icon-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #f0f0f0;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

/* SEOAnt Ad Styles */
.seoant-ad {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 15px 25px;
  margin: 12px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.seoant-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 12px;
  gap: 12px;
  text-align: left;
}

.seoant-logo {
  width: 40px !important;
  height: 40px !important;
  border-radius: 6px;
  margin-right: 0 !important;
  flex-shrink: 0;
}

.seoant-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
}

.seoant-description {
  font-size: 12px;
  line-height: 1.3;
  color: #4a5568;
  margin: 0;
  text-align: left;
  font-weight: 400;
  flex: 1;
}

.seoant-cta {
  display: inline-block;
  padding: 8px 16px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border-radius: 6px;
  font-weight: 600;
  font-size: 12px;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 0 !important;
  -webkit-background-clip: initial !important;
  background-clip: initial !important;
  -webkit-text-fill-color: white !important;
}

.seoant-cta::before { /* Disable any generic bordered gradient effect tied to #review */
  display: none !important;
}

.seoant-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  color: white !important;
  -webkit-text-fill-color: white !important;
}

.seoant-cta:active {
  transform: translateY(1px);
}
/* Accessibility accordion */
.accordion {
    margin-top: 8px;
    text-align: left;
}
.accordion-header {
    width: 100%;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 8px 10px;
    font-size: 13px;
    color: #444;
    cursor: pointer;
}
.accordion-panel {
    display: none;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-top: none;
    border-radius: 0 0 8px 8px;
    padding: 10px;
}
.accordion-panel.open { display: block; }

.palette-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}
.palette-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 8px;
    background: #fafafa;
    cursor: pointer;
}
.kpi-card {
    background:#fff;
    border:1px solid #e0e0e0;
    border-radius:8px;
    padding:8px;
    display:flex;
    flex-direction:column;
    gap:2px;
}
.kpi-title { font-size:11px; color:#666; }
.kpi-value { font-size:14px; font-weight:600; color:#333; }
.badge { display:inline-block; padding:2px 6px; border-radius:6px; font-size:10px; }
.badge-warn { background:#fff3cd; color:#8a6d3b; border:1px solid #ffe8a1; }
.badge-ok { background:#e8f5e9; color:#2e7d32; border:1px solid #c8e6c9; }
.list-item { display:flex; align-items:center; gap:6px; padding:4px 0; }
.pill { font-size:10px; padding:2px 6px; border-radius:999px; border:1px solid #e0e0e0; background:#fafafa; }
.wf-row { display:flex; align-items:center; gap:6px; font-size:12px; color:#444; padding:3px 6px; }
.wf-name { width:240px; overflow:hidden; white-space:normal; text-overflow:clip; display:-webkit-box; -webkit-line-clamp:2; -webkit-box-orient: vertical; }
.wf-bar-wrap { position:relative; flex:1; height:10px; background:#f9f9f9; border:1px solid #eee; border-radius:4px; }
.wf-bar { position:absolute; top:0; height:100%; border-radius:4px; }
.wf-type { width:56px; text-transform:uppercase; font-size:10px; color:#666; }
.wf-bar-wrap { overflow: hidden; }
.help { display:inline-flex; align-items:center; justify-content:center; width:16px; height:16px; line-height:16px; text-align:center; border-radius:50%; background:#f0f0f0; color:#666; font-size:11px; cursor:help; margin-left:6px; position:relative; }
.help[data-tip]::after {
    content: attr(data-tip);
    position: absolute;
    bottom: 120%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.85);
    color: #fff;
    padding: 6px 8px;
    border-radius: 6px;
    font-size: 11px;
    line-height: 1.2;
    white-space: normal;
    min-width: 200px;
    max-width: 280px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.15s ease-in-out;
    z-index: 10000;
}
.help[data-tip]::before {
    content: '';
    position: absolute;
    bottom: 110%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 6px;
    border-style: solid;
    border-color: rgba(0,0,0,0.85) transparent transparent transparent;
    opacity: 0;
    transition: opacity 0.15s ease-in-out;
    z-index: 10001;
}
.help[data-tip]:hover::after, .help[data-tip]:hover::before { opacity: 1; }
.swatches {
    display: flex;
    gap: 6px;
    margin-top: 6px;
}
.swatch {
    flex: 1;
    height: 16px;
    border-radius: 4px;
}

.icon-button:hover {
    background: #2196F3;
    transform: none;
}

.icon-button:hover .download-icon {
    opacity: 1;
    filter: brightness(0) invert(1);
}

.icon-button:active {
    transform: scale(0.95);
    background: #d0d0d0;
}

.icon-button.loading {
    animation: pulse 1s infinite;
    cursor: not-allowed;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

.icon-button .material-symbols-outlined {
    font-size: 20px;
    color: #666;
    font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 24;
}

.icon-button .download-icon {
    width: 18px;
    height: 18px;
    opacity: 0.8;
    transition: opacity 0.2s ease;
    display: block;
    margin: 0 auto;
}

/* Hint banner */
.hint-banner {
  background: #fff3cd;
  color: #8a6d3b;
  border: 1px solid #ffe8a1;
  border-radius: 8px;
  padding: 8px 10px;
  font-size: 12px;
  margin: 8px 0 0 0;
  text-align: center;
}


