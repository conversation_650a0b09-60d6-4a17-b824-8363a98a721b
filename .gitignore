docs/TECHNICAL_ARCHITECTURE.md
docs/golden-rules-for-version-updates.md
docs/golden-rules-for-context-menu.md
docs/golden-rules-for-quick-actions.md
docs/TOGGLE_SETTINGS_GOLDEN_RULES.md
docs/golden-rules-for-global-shortcuts.md
docs/golden-rules-for-debugging-system.md
docs/golden-rules-for-extras-menu.md
memory-bank/activeContext.md
memory-bank/productContext.md
memory-bank/progress.md
memory-bank/projectbrief.md
memory-bank/systemPatterns.md
memory-bank/techContext.md
memory-bank/goldenRules.md
docs/memory-bank-gmail.md
memory-bank/gmail-development.md
memory-bank/popup-design-standards.md
memory-bank/debug-mode-architecture.md
memory-bank/context-menu-debugging.md
memory-bank/context-menu-development-guide.md
memory-bank/tooltip-and-context-patterns.md
memory-bank/universal-click-debugger-system.md
gemini.md
memory-bank/tracked-domains-system.md
.windsurfrules
.cursor/rules/design.mdc
.superdesign/design_iterations/default_ui_darkmode.css

# Context Menu Testing Files (console-based test suites)
test-context-menu-fixes.js
test-edge-cases.js
temp-context-menu-fix-template.js
.serena/cache/
.serena/.serena/cache/
