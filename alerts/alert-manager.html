<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alert Manager - SEO Time Machines</title>
    <link rel="stylesheet" href="../settings/settings.css">
    <style>
        body {
            background: #0a0a0a;
            color: #d1d5db;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            overflow-y: auto;
            box-sizing: border-box;
        }
        
        .alert-manager-container {
            max-width: 520px;
            margin: 0 auto;
            min-height: calc(100vh - 40px);
            display: flex;
            flex-direction: column;
        }
        
        .alert-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #7C3AED;
        }
        
        .alert-title {
            font-size: 24px;
            font-weight: 600;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-icon {
            color: #7C3AED;
        }
        
        .alert-form {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: 500;
            color: #e5e7eb;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 10px 14px;
            background: #0a0a0a;
            border: 1px solid #444;
            border-radius: 4px;
            color: #d1d5db;
            font-size: 14px;
            line-height: 1.4;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #7C3AED;
            box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
        }
        
        .time-inputs {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .time-input {
            width: 80px;
            height: 48px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            padding: 12px 16px;
            position: relative;
            background: #1a1a1a;
            border: 2px solid #444;
            border-radius: 8px;
            transition: all 0.2s ease;
        }
        
        .time-input:hover {
            border-color: #7C3AED;
            background: #1f1f1f;
        }
        
        .time-input:focus {
            outline: none;
            border-color: #7C3AED;
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.15);
            background: #1f1f1f;
        }
        
        /* Style the number input spinners */
        .time-input::-webkit-outer-spin-button,
        .time-input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }
        
        /* Custom spinner buttons */
        .time-input-wrapper {
            position: relative;
            display: inline-block;
        }
        
        .time-input-wrapper .time-input {
            padding-right: 30px; /* Space for custom arrows */
        }
        
        .time-spinner {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 1px;
        }
        
        .time-spinner-btn {
            width: 20px;
            height: 20px;
            background: #333;
            border: 1px solid #555;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s ease;
            font-size: 12px;
            color: #aaa;
            user-select: none;
        }
        
        .time-spinner-btn:hover {
            background: #7C3AED;
            border-color: #7C3AED;
            color: white;
        }
        
        .time-spinner-btn:active {
            transform: scale(0.95);
        }
        
        /* Colon separator styling */
        .time-inputs > span {
            font-size: 24px;
            font-weight: 700;
            color: #7C3AED;
            margin: 0 5px;
        }
        
        .time-repeat-container {
            display: flex;
            gap: 30px;
            align-items: flex-start;
        }
        
        .time-section {
            flex: 1;
            min-width: 200px;
        }
        
        /* Enhanced visual feedback and touch-friendly design */
        .time-input-wrapper:hover .time-input {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.1);
        }
        
        .time-spinner-btn:focus {
            outline: 2px solid #7C3AED;
            outline-offset: 2px;
        }
        
        /* Improved accessibility for keyboard navigation */
        .time-input:focus + .time-spinner .time-spinner-btn {
            border-color: #7C3AED;
        }
        
        /* Responsive improvements for mobile */
        @media (max-width: 480px) {
            .time-repeat-container {
                flex-direction: column;
                gap: 20px;
            }
            
            .time-inputs {
                justify-content: center;
                gap: 20px;
            }
            
            .time-input {
                width: 90px;
                height: 52px;
                font-size: 20px;
            }
        }
        
        .repeat-section {
            flex: 1;
            min-width: 120px;
        }
        
        .repeat-select {
            width: 100%;
        }
        
        .btn-add-alert {
            width: 100%;
            padding: 12px 20px;
            background: #7C3AED;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            text-align: center;
            min-height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-add-alert:hover {
            background: #6b31db;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
        }
        
        .btn-add-alert:active {
            transform: translateY(0);
        }
        
        .alerts-list {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 15px;
            flex: 1;
            min-height: 200px;
        }
        
        .alerts-list-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .alerts-list-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
            margin: 0;
        }
        
        .alerts-list-controls {
            display: flex;
            gap: 5px;
        }
        
        .alert-filter-btn {
            background: #0a0a0a;
            border: 1px solid #444;
            color: #d1d5db;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .alert-filter-btn:hover {
            border-color: #7C3AED;
            background: #1f1f1f;
        }
        
        .alert-filter-btn.active {
            background: #7C3AED;
            border-color: #7C3AED;
            color: white;
        }
        
        .alert-filter-btn.active:hover {
            background: #6b31db;
        }
        
        .alert-item {
            background: #0a0a0a;
            border: 1px solid #444;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s ease;
        }
        
        .alert-item:hover {
            border-color: #555;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }
        
        .alert-info {
            flex: 1;
        }
        
        .alert-name {
            font-weight: 500;
            color: #fff;
            margin-bottom: 4px;
        }
        
        .alert-details {
            font-size: 12px;
            color: #9ca3af;
        }
        
        .alert-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .alert-toggle {
            width: 40px;
            height: 20px;
            background: #444;
            border-radius: 10px;
            position: relative;
            cursor: pointer;
            transition: background 0.2s ease;
        }
        
        .alert-toggle.active {
            background: #7C3AED;
        }
        
        .alert-toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s ease;
        }
        
        .alert-toggle.active .alert-toggle-slider {
            transform: translateX(20px);
        }
        
        .btn-edit {
            background: transparent;
            border: 1px solid #7C3AED;
            color: #7C3AED;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
        }
        
        .btn-edit:hover {
            background: #7C3AED;
            color: white;
        }
        
        .btn-delete {
            background: transparent;
            border: 1px solid #ef4444;
            color: #ef4444;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-delete:hover {
            background: #ef4444;
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        
        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 10px;
            opacity: 0.5;
        }
        
        .time-from-now {
            font-size: 15px;
            color: #ffffff;
            font-weight: 500;
            background: rgba(124, 58, 237, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid rgba(124, 58, 237, 0.3);
        }
        
        .footer-info {
            margin-top: 20px;
            padding: 12px;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            font-size: 12px;
            color: #9ca3af;
            text-align: center;
            flex-shrink: 0;
        }
        
        /* Calendar Component Styles */
        .calendar-section {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .calendar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .calendar-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }
        
        .calendar-nav {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .calendar-nav-btn {
            background: #0a0a0a;
            border: 1px solid #444;
            color: #d1d5db;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .calendar-nav-btn:hover {
            border-color: #7C3AED;
            background: #1f1f1f;
        }
        
        .calendar-month-year {
            font-size: 14px;
            font-weight: 500;
            color: #e5e7eb;
            min-width: 120px;
            text-align: center;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #333;
            border: 1px solid #333;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .calendar-day-header {
            background: #0a0a0a;
            color: #9ca3af;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            padding: 8px 4px;
        }
        
        .calendar-day-header.weekend {
            color: #ef4444;
        }
        
        .calendar-day {
            background: #1a1a1a;
            color: #d1d5db;
            font-size: 14px;
            text-align: center;
            padding: 10px 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            min-height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .calendar-day:hover {
            background: #2a2a2a;
            color: #fff;
        }
        
        .calendar-day.other-month {
            color: #6b7280;
            background: #0f0f0f;
        }
        
        .calendar-day.other-month:hover {
            background: #1a1a1a;
            color: #9ca3af;
        }
        
        .calendar-day.today {
            background: #7C3AED;
            color: white;
            font-weight: 600;
        }
        
        .calendar-day.today:hover {
            background: #6b31db;
        }
        
        .calendar-day.selected {
            background: #7C3AED;
            color: white;
            font-weight: 600;
            box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.3);
        }
        
        .calendar-day.selected:hover {
            background: #6b31db;
        }
        
        .calendar-day.has-alerts::after {
            content: '';
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 4px;
            height: 4px;
            background: #22c55e;
            border-radius: 50%;
        }
        
        .calendar-day.selected.has-alerts::after {
            background: #a7f3d0;
        }
        
        .calendar-day.weekend {
            color: #ef4444;
        }
        
        .calendar-day.weekend:hover {
            color: #ef4444;
        }
        
        .calendar-day.weekend.other-month {
            color: #7f1d1d;
        }
        
        .calendar-day.weekend.other-month:hover {
            color: #ef4444;
        }
        
        .calendar-selected-date {
            margin-top: 15px;
            padding: 10px;
            background: rgba(124, 58, 237, 0.1);
            border: 1px solid rgba(124, 58, 237, 0.3);
            border-radius: 6px;
            font-size: 14px;
            color: #e5e7eb;
            text-align: center;
        }
        
        .calendar-selected-date strong {
            color: #7C3AED;
        }
        
        /* Alert Title Section */
        .alert-title-section {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        /* Date Accordion Styles */
        .date-accordion {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .accordion-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid transparent;
        }
        
        .accordion-header:hover {
            background: #222;
        }
        
        .accordion-header.active {
            border-bottom-color: #333;
        }
        
        .accordion-title {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }
        
        .selected-date-preview {
            font-size: 14px;
            font-weight: 400;
            color: #7C3AED;
            background: rgba(124, 58, 237, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid rgba(124, 58, 237, 0.3);
        }
        
        .accordion-arrow {
            font-size: 12px;
            color: #9ca3af;
            transition: transform 0.3s ease;
        }
        
        .accordion-arrow.expanded {
            transform: rotate(180deg);
        }
        
        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            padding: 0 20px;
        }
        
        .accordion-content.expanded {
            max-height: 500px;
            padding: 20px;
        }
        
        .accordion-content .calendar-header {
            margin-bottom: 15px;
            justify-content: center;
        }
        
        .accordion-content .calendar-selected-date {
            margin-top: 15px;
        }
        
        /* Alert Search Bar Styles */
        .alert-search-container {
            position: relative;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .alert-search {
            width: 100%;
            padding: 10px 14px;
            padding-right: 40px; /* Space for clear button */
            background: #0a0a0a;
            border: 1px solid rgba(124, 58, 237, 0.3);
            border-radius: 6px;
            color: #d1d5db;
            font-size: 14px;
            font-family: inherit;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }
        
        .alert-search:focus {
            outline: none;
            border-color: #7C3AED;
            box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
        }
        
        .alert-search::placeholder {
            color: #6b7280;
            font-size: 13px;
        }
        
        .alert-search-clear {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            color: #9ca3af;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            padding: 4px;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            opacity: 0;
            pointer-events: none;
        }
        
        .alert-search-container:hover .alert-search-clear,
        .alert-search:focus + .alert-search-clear,
        .alert-search-clear.visible {
            opacity: 1;
            pointer-events: all;
        }
        
        .alert-search-clear:hover {
            background: rgba(124, 58, 237, 0.2);
            color: #7C3AED;
            transform: translateY(-50%) scale(1.1);
        }
        
        /* Search result highlighting */
        .alert-item-highlighted {
            background: rgba(124, 58, 237, 0.1);
            border-color: rgba(124, 58, 237, 0.4);
        }
        
        .alert-search-empty-state {
            text-align: center;
            padding: 30px;
            color: #6b7280;
        }
        
        .alert-search-empty-state-icon {
            font-size: 36px;
            margin-bottom: 10px;
            opacity: 0.5;
        }
        
        /* Edit Modal Styles */
        .edit-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .edit-modal-overlay.show {
            display: flex;
        }
        
        .edit-modal {
            background: #0a0a0a;
            border: 2px solid #7C3AED;
            border-radius: 12px;
            color: #d1d5db;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto";
            width: 90%;
            max-width: 520px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
        }
        
        .edit-modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 20px 15px 20px;
            border-bottom: 1px solid #333;
        }
        
        .edit-modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .edit-modal-close {
            background: transparent;
            border: none;
            color: #9ca3af;
            font-size: 24px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            line-height: 1;
        }
        
        .edit-modal-close:hover {
            background: rgba(124, 58, 237, 0.2);
            color: #7C3AED;
        }
        
        .edit-modal-content {
            padding: 20px;
        }
        
        .edit-modal-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn-save, .btn-cancel {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }
        
        .btn-save {
            background: #7C3AED;
            color: white;
        }
        
        .btn-save:hover {
            background: #6b31db;
            transform: translateY(-1px);
        }
        
        .btn-cancel {
            background: #1a1a1a;
            color: #d1d5db;
            border: 1px solid #444;
        }
        
        .btn-cancel:hover {
            background: #2a2a2a;
            border-color: #555;
        }
    </style>
</head>
<body>
    <div class="alert-manager-container">
        <div class="alert-header">
            <h1 class="alert-title">
                <svg class="alert-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C10.9 2 10 2.9 10 4C10 4.1 10 4.2 10 4.29C7.12 5.14 5 7.82 5 11V17L3 19V20H21V19L19 17V11C19 7.82 16.88 5.14 14 4.29C14 4.2 14 4.1 14 4C14 2.9 13.1 2 12 2ZM12 22C13.11 22 14 21.11 14 20H10C10 21.11 10.89 22 12 22Z" fill="currentColor"/>
                </svg>
                Alert Manager
            </h1>
        </div>
        
        <div class="alert-title-section">
            <div class="form-group">
                <label class="form-label" for="alertTitle">Alert Title</label>
                <input type="text" id="alertTitle" class="form-input" placeholder="Check competitor metrics" maxlength="50">
            </div>
        </div>
        
        <div class="date-accordion">
            <div class="accordion-header" id="dateAccordionHeader">
                <div class="accordion-title">
                    <span>Select Date</span>
                    <span class="selected-date-preview" id="selectedDatePreview">Today</span>
                </div>
                <div class="accordion-arrow" id="dateAccordionArrow">▼</div>
            </div>
            <div class="accordion-content" id="dateAccordionContent">
                <div class="calendar-header">
                    <div class="calendar-nav">
                        <button class="calendar-nav-btn" id="prevMonth">&lt;</button>
                        <div class="calendar-month-year" id="monthYear"></div>
                        <button class="calendar-nav-btn" id="nextMonth">&gt;</button>
                    </div>
                </div>
                <div class="calendar-grid" id="calendar">
                    <!-- Calendar will be generated by JavaScript -->
                </div>
                <div class="calendar-selected-date" id="selectedDateDisplay">
                    Selected: <strong>Today</strong>
                </div>
            </div>
        </div>
        
        <div class="alert-form">
            
            <div class="form-group">
                <div class="time-repeat-container">
                    <div class="time-section">
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 5px;">
                            <label class="form-label" style="margin-bottom: 0;">Time</label>
                        </div>
                        <div class="time-inputs">
                            <div class="time-input-wrapper">
                                <input type="number" id="alertHour" class="form-input time-input" min="0" max="23" placeholder="14">
                                <div class="time-spinner">
                                    <div class="time-spinner-btn" data-target="alertHour" data-action="increment">▲</div>
                                    <div class="time-spinner-btn" data-target="alertHour" data-action="decrement">▼</div>
                                </div>
                            </div>
                            <span>:</span>
                            <div class="time-input-wrapper">
                                <input type="number" id="alertMinute" class="form-input time-input" min="0" max="59" placeholder="30">
                                <div class="time-spinner">
                                    <div class="time-spinner-btn" data-target="alertMinute" data-action="increment">▲</div>
                                    <div class="time-spinner-btn" data-target="alertMinute" data-action="decrement">▼</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="repeat-section">
                        <label class="form-label" for="alertRepeat">Repeat</label>
                        <select id="alertRepeat" class="form-select repeat-select" style="height: 48px;">
                            <option value="once">Once</option>
                            <option value="daily">Daily</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <button class="btn-add-alert" id="addAlertBtn">Add Alert</button>
        </div>
        
        <div class="alerts-list">
            <div class="alerts-list-header">
                <h2 class="alerts-list-title">Active Alerts</h2>
                <div class="alerts-list-controls">
                    <button class="alert-filter-btn active" id="showAllAlertsBtn">All Alerts</button>
                    <button class="alert-filter-btn" id="showDateAlertsBtn">Selected Date</button>
                </div>
            </div>
            
            <div class="alert-search-container">
                <input type="text" id="alertSearch" class="alert-search" 
                       placeholder="Search alerts... (3+ letters)" autocomplete="off">
                <button type="button" id="clearAlertSearch" class="alert-search-clear" 
                        title="Clear search">×</button>
            </div>
            <div id="alertsList">
                <div class="empty-state">
                    <div class="empty-state-icon">🔔</div>
                    <p>No alerts set yet. Create your first alert above.</p>
                </div>
            </div>
        </div>
        
        <div class="footer-info">
            Alerts will appear as browser notifications at the scheduled time.<br>
            Make sure notifications are enabled in your browser settings.
        </div>
    </div>
    
    <!-- Edit Modal -->
    <div class="edit-modal-overlay" id="editModalOverlay">
        <div class="edit-modal">
            <div class="edit-modal-header">
                <div class="edit-modal-title">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M18.5 2.49998C18.8978 2.10216 19.4374 1.87866 20 1.87866C20.5626 1.87866 21.1022 2.10216 21.5 2.49998C21.8978 2.89781 22.1213 3.43737 22.1213 3.99998C22.1213 4.56259 21.8978 5.10216 21.5 5.49998L12 15L8 16L9 12L18.5 2.49998Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    Edit Alert
                </div>
                <button class="edit-modal-close" id="editModalClose">&times;</button>
            </div>
            <div class="edit-modal-content">
                <div class="form-group">
                    <label class="form-label" for="editAlertTitle">Alert Title</label>
                    <input type="text" id="editAlertTitle" class="form-input" placeholder="Check competitor metrics" maxlength="50">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="editAlertDate">Date</label>
                    <input type="date" id="editAlertDate" class="form-input">
                </div>
                
                <div class="form-group">
                    <div class="time-repeat-container">
                        <div class="time-section">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 5px;">
                                <label class="form-label" style="margin-bottom: 0;">Time</label>
                            </div>
                            <div class="time-inputs">
                                <div class="time-input-wrapper">
                                    <input type="number" id="editAlertHour" class="form-input time-input" min="0" max="23" placeholder="14">
                                    <div class="time-spinner">
                                        <div class="time-spinner-btn" data-target="editAlertHour" data-action="increment">▲</div>
                                        <div class="time-spinner-btn" data-target="editAlertHour" data-action="decrement">▼</div>
                                    </div>
                                </div>
                                <span>:</span>
                                <div class="time-input-wrapper">
                                    <input type="number" id="editAlertMinute" class="form-input time-input" min="0" max="59" placeholder="30">
                                    <div class="time-spinner">
                                        <div class="time-spinner-btn" data-target="editAlertMinute" data-action="increment">▲</div>
                                        <div class="time-spinner-btn" data-target="editAlertMinute" data-action="decrement">▼</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="repeat-section">
                            <label class="form-label" for="editAlertRepeat">Repeat</label>
                            <select id="editAlertRepeat" class="form-select repeat-select" style="height: 48px;">
                                <option value="once">Once</option>
                                <option value="daily">Daily</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="edit-time-indicator" id="editTimeIndicator" style="text-align: right; margin-top: 35px; margin-bottom: 10px;">
                    <span class="time-from-now">Calculating...</span>
                </div>
                
                <div class="edit-modal-actions">
                    <button class="btn-cancel" id="editModalCancel">Cancel</button>
                    <button class="btn-save" id="editModalSave">Save Changes</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="alert-manager.js"></script>
</body>
</html>