// Alert Manager for SEO Time Machines
(function() {
    'use strict';
    
    // DOM Elements
    const alertTitle = document.getElementById('alertTitle');
    const alertHour = document.getElementById('alertHour');
    const alertMinute = document.getElementById('alertMinute');
    const alertRepeat = document.getElementById('alertRepeat');
    const addAlertBtn = document.getElementById('addAlertBtn');
    const alertsList = document.getElementById('alertsList');
    
    // State
    let activeAlerts = [];
    let selectedDate = new Date().toISOString().split('T')[0]; // Today in YYYY-MM-DD format
    let currentCalendarDate = new Date(); // For calendar navigation
    let filterByDate = false; // Whether to filter alerts by selected date
    let isAccordionExpanded = false; // Accordion state
    let currentSearchTerm = ''; // Current search term
    let searchIndex = []; // Search index for alerts
    
    // Initialize
    async function init() {
        await loadAlerts();
        setupEventListeners();
        setupAccordion();
        setupCalendar();
        setupAlertSearch();
        setupWindowPositioning();
        renderAlerts();
    }
    
    // Load alerts from storage
    async function loadAlerts() {
        try {
            const result = await chrome.storage.local.get(['activeAlerts']);
            activeAlerts = result.activeAlerts || [];
            
            // Migrate legacy weekly/weekdays alerts to daily
            await migrateLegacyAlerts();
            
            // Clean up any expired one-time alerts
            await cleanupExpiredAlerts();
        } catch (error) {
            console.error('Error loading alerts:', error);
            activeAlerts = [];
        }
    }
    
    // Migrate legacy weekly/weekdays alerts to daily alerts
    async function migrateLegacyAlerts() {
        let migrationCount = 0;
        
        activeAlerts = activeAlerts.map(alert => {
            if (alert.repeat === 'weekly' || alert.repeat === 'weekdays') {
                console.log(`Migrating ${alert.repeat} alert "${alert.title}" to daily`);
                
                // Clear the old alarm
                chrome.alarms.clear(alert.id).catch(() => {
                    // Ignore errors if alarm doesn't exist
                });
                
                // Convert to daily
                alert.repeat = 'daily';
                migrationCount++;
                
                // Reschedule as daily
                scheduleAlert(alert);
            }
            return alert;
        });
        
        if (migrationCount > 0) {
            console.log(`Migrated ${migrationCount} legacy alerts to daily`);
            await saveAlerts();
        }
    }
    
    // Save alerts to storage
    async function saveAlerts() {
        try {
            await chrome.storage.local.set({ activeAlerts });
            
            // Update popup badge count
            chrome.runtime.sendMessage({
                action: 'updateAlertBadge'
            });
        } catch (error) {
            console.error('Error saving alerts:', error);
        }
    }
    
    // Clean up expired one-time alerts
    async function cleanupExpiredAlerts() {
        const now = new Date();
        let cleanupCount = 0;
        
        // Filter out expired one-time alerts
        const originalLength = activeAlerts.length;
        activeAlerts = activeAlerts.filter(alert => {
            // Keep all recurring alerts (daily)
            if (alert.repeat !== 'once') {
                return true;
            }
            
            // For one-time alerts, check if the scheduled time has passed
            const alertTime = new Date();
            alertTime.setHours(alert.hour, alert.minute, 0, 0);
            
            // If alert time is in the past (today), it's expired
            if (alertTime <= now) {
                console.log(`Removing expired one-time alert: "${alert.title}" (${alert.time})`);
                
                // Also clear any lingering Chrome alarm
                chrome.alarms.clear(alert.id).catch(() => {
                    // Ignore errors if alarm doesn't exist
                });
                
                cleanupCount++;
                return false; // Remove this alert
            }
            
            return true; // Keep this alert
        });
        
        // If we removed any alerts, save the cleaned list
        if (cleanupCount > 0) {
            console.log(`Cleaned up ${cleanupCount} expired one-time alerts`);
            await saveAlerts();
        }
        
        return cleanupCount;
    }
    
    // Setup event listeners
    function setupEventListeners() {
        // Add alert button
        addAlertBtn.addEventListener('click', addAlert);
        
        // Enter key in title input
        alertTitle.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                addAlert();
            }
        });
        
        // Format time inputs and update time-from-now
        alertHour.addEventListener('input', (e) => {
            formatTimeInput(e);
            updateTimeFromNow();
        });
        alertMinute.addEventListener('input', (e) => {
            formatTimeInput(e);
            updateTimeFromNow();
        });
        
        // Custom spinner button functionality
        document.querySelectorAll('.time-spinner-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = btn.dataset.target;
                const action = btn.dataset.action;
                const input = document.getElementById(targetId);
                
                if (!input) return;
                
                let currentValue = parseInt(input.value) || 0;
                const min = parseInt(input.min) || 0;
                const max = parseInt(input.max) || 23;
                
                if (action === 'increment') {
                    currentValue = currentValue >= max ? min : currentValue + 1;
                } else if (action === 'decrement') {
                    currentValue = currentValue <= min ? max : currentValue - 1;
                }
                
                input.value = currentValue.toString().padStart(2, '0');
                
                // Trigger input event to update time-from-now and formatting
                input.dispatchEvent(new Event('input', { bubbles: true }));
                
                // Add visual feedback
                btn.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    btn.style.transform = '';
                }, 100);
            });
            
            // Add hover sound effect (optional)
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'translateY(-1px)';
            });
            
            btn.addEventListener('mouseleave', () => {
                btn.style.transform = '';
            });
        });
        
        // Set default time to current time + 10 minutes
        const now = new Date();
        const nextTime = new Date(now.getTime() + 10 * 60 * 1000);
        alertHour.value = nextTime.getHours().toString().padStart(2, '0');
        alertMinute.value = nextTime.getMinutes().toString().padStart(2, '0');
        
        // Filter buttons
        document.getElementById('showAllAlertsBtn').addEventListener('click', () => {
            setAlertFilter(false);
        });
        
        document.getElementById('showDateAlertsBtn').addEventListener('click', () => {
            setAlertFilter(true);
        });
        
        // Edit modal event listeners
        document.getElementById('editModalClose').addEventListener('click', hideEditModal);
        document.getElementById('editModalCancel').addEventListener('click', hideEditModal);
        document.getElementById('editModalSave').addEventListener('click', saveEditedAlert);
        document.getElementById('editModalOverlay').addEventListener('click', (e) => {
            if (e.target === document.getElementById('editModalOverlay')) {
                hideEditModal();
            }
        });
        
        // Edit modal time/date input listeners for real-time updates
        document.getElementById('editAlertDate').addEventListener('input', updateEditModalTimeFromNow);
        document.getElementById('editAlertHour').addEventListener('input', updateEditModalTimeFromNow);
        document.getElementById('editAlertMinute').addEventListener('input', updateEditModalTimeFromNow);
        
        // Add spinner functionality for edit modal time inputs
        document.querySelectorAll('.edit-modal .time-spinner-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = btn.dataset.target;
                const action = btn.dataset.action;
                const input = document.getElementById(targetId);
                
                if (!input) return;
                
                let currentValue = parseInt(input.value) || 0;
                const min = parseInt(input.min) || 0;
                const max = parseInt(input.max) || 23;
                
                if (action === 'increment') {
                    currentValue = currentValue >= max ? min : currentValue + 1;
                } else if (action === 'decrement') {
                    currentValue = currentValue <= min ? max : currentValue - 1;
                }
                
                input.value = currentValue.toString().padStart(2, '0');
                
                // Update time indicator immediately
                updateEditModalTimeFromNow();
                
                // Add visual feedback
                btn.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    btn.style.transform = '';
                }, 100);
            });
        });
        
        // Start updating time-from-now indicator
        updateTimeFromNow();
        setInterval(updateTimeFromNow, 1000); // Update every second
    }
    
    // Format time input (ensure 2 digits)
    function formatTimeInput(e) {
        const input = e.target;
        let value = parseInt(input.value) || 0;
        
        if (input.id === 'alertHour') {
            value = Math.min(Math.max(value, 0), 23);
        } else {
            value = Math.min(Math.max(value, 0), 59);
        }
        
        input.value = value.toString().padStart(2, '0');
    }
    
    // Update button text with time indicator
    function updateTimeFromNow() {
        const addAlertBtn = document.getElementById('addAlertBtn');
        if (!addAlertBtn) return;
        
        const hour = parseInt(alertHour.value) || 0;
        const minute = parseInt(alertMinute.value) || 0;
        
        // Create target time for selected date
        const now = new Date();
        const targetTime = new Date(selectedDate + 'T' + hour.toString().padStart(2, '0') + ':' + minute.toString().padStart(2, '0') + ':00');
        
        // Calculate difference
        const diffMs = targetTime.getTime() - now.getTime();
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        
        let timeText;
        if (diffMinutes < 0) {
            timeText = 'in the past';
        } else if (diffMinutes < 60) {
            timeText = `${diffMinutes} min from now`;
        } else if (diffMinutes < 1440) { // Less than 24 hours
            const hours = Math.floor(diffMinutes / 60);
            const remainingMinutes = diffMinutes % 60;
            if (remainingMinutes === 0) {
                timeText = `${hours} hour${hours > 1 ? 's' : ''} from now`;
            } else {
                timeText = `${hours}h ${remainingMinutes}m from now`;
            }
        } else {
            const days = Math.floor(diffMinutes / 1440);
            const hours = Math.floor((diffMinutes % 1440) / 60);
            if (hours === 0) {
                timeText = `${days} day${days > 1 ? 's' : ''} from now`;
            } else {
                timeText = `${days}d ${hours}h from now`;
            }
        }
        
        // Update button text to include time
        addAlertBtn.textContent = `Add Alert (${timeText})`;
    }
    
    // Update edit modal time indicator
    function updateEditModalTimeFromNow() {
        const editTimeIndicator = document.getElementById('editTimeIndicator');
        const editAlertDate = document.getElementById('editAlertDate');
        const editAlertHour = document.getElementById('editAlertHour');
        const editAlertMinute = document.getElementById('editAlertMinute');
        
        if (!editTimeIndicator || !editAlertDate || !editAlertHour || !editAlertMinute) return;
        
        const selectedDateStr = editAlertDate.value;
        const hour = parseInt(editAlertHour.value) || 0;
        const minute = parseInt(editAlertMinute.value) || 0;
        
        if (!selectedDateStr) {
            editTimeIndicator.innerHTML = '<span class="time-from-now">Select a date</span>';
            return;
        }
        
        // Create target time for selected date
        const now = new Date();
        const targetTime = new Date(selectedDateStr + 'T' + hour.toString().padStart(2, '0') + ':' + minute.toString().padStart(2, '0') + ':00');
        
        // Calculate difference
        const diffMs = targetTime.getTime() - now.getTime();
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        
        let timeText;
        if (diffMinutes < 0) {
            timeText = 'in the past';
        } else if (diffMinutes < 60) {
            timeText = `${diffMinutes} min from now`;
        } else if (diffMinutes < 1440) { // Less than 24 hours
            const hours = Math.floor(diffMinutes / 60);
            const remainingMinutes = diffMinutes % 60;
            if (remainingMinutes === 0) {
                timeText = `${hours} hour${hours > 1 ? 's' : ''} from now`;
            } else {
                timeText = `${hours}h ${remainingMinutes}m from now`;
            }
        } else {
            const days = Math.floor(diffMinutes / 1440);
            const hours = Math.floor((diffMinutes % 1440) / 60);
            if (hours === 0) {
                timeText = `${days} day${days > 1 ? 's' : ''} from now`;
            } else {
                timeText = `${days}d ${hours}h from now`;
            }
        }
        
        // Update indicator text
        editTimeIndicator.innerHTML = `<span class="time-from-now">${timeText}</span>`;
    }
    
    // Add new alert
    function addAlert() {
        const title = alertTitle.value.trim();
        const hour = parseInt(alertHour.value) || 0;
        const minute = parseInt(alertMinute.value) || 0;
        const repeat = alertRepeat.value;
        
        // Allow empty titles - fallback message "Time's Up!" will be shown
        // if (!title) {
        //     alertTitle.focus();
        //     alertTitle.style.borderColor = '#ef4444';
        //     setTimeout(() => {
        //         alertTitle.style.borderColor = '';
        //     }, 2000);
        //     return;
        // }
        
        // Create new alert
        const newAlert = {
            id: 'alert-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
            title: title,
            time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            hour: hour,
            minute: minute,
            repeat: repeat,
            enabled: true,
            created: new Date().toISOString(),
            alertDate: selectedDate // Add the selected date
        };
        
        // Add to list
        activeAlerts.push(newAlert);
        
        // Schedule alarm
        scheduleAlert(newAlert);
        
        // Save and render
        saveAlerts();
        renderAlerts();
        renderCalendar(); // Update calendar indicators
        
        // Clear form
        alertTitle.value = '';
        alertTitle.focus();
        
        // Show success feedback
        addAlertBtn.textContent = 'Alert Added!';
        addAlertBtn.style.background = '#22c55e';
        setTimeout(() => {
            addAlertBtn.style.background = '';
            updateTimeFromNow(); // Restore time display
        }, 1500);
    }
    
    // Get next fire time for alert (used for sorting)
    function getNextFireTime(alert) {
        const now = new Date();
        let nextFireTime;
        
        if (alert.alertDate) {
            // Use specific date from alert
            nextFireTime = new Date(alert.alertDate + 'T' + alert.time + ':00');
        } else {
            // Legacy support - use today's date
            nextFireTime = new Date();
            nextFireTime.setHours(alert.hour);
            nextFireTime.setMinutes(alert.minute);
            nextFireTime.setSeconds(0);
            nextFireTime.setMilliseconds(0);
        }
        
        // For one-time alerts, return the scheduled time (even if past)
        if (alert.repeat === 'once') {
            return nextFireTime.getTime();
        }
        
        // For daily alerts, find the next occurrence
        if (alert.repeat === 'daily') {
            // If the alert time hasn't passed today, use today
            const todayTime = new Date();
            todayTime.setHours(alert.hour, alert.minute, 0, 0);
            
            if (todayTime > now) {
                return todayTime.getTime(); // Later today
            } else {
                // Tomorrow at the same time
                const tomorrowTime = new Date(todayTime.getTime() + 24 * 60 * 60 * 1000);
                return tomorrowTime.getTime();
            }
        }
        
        return nextFireTime.getTime();
    }
    
    // Schedule alert with chrome.alarms
    function scheduleAlert(alert) {
        if (!alert.enabled) return;
        
        const now = new Date();
        let scheduledTime;
        
        if (alert.alertDate) {
            // Use specific date from alert
            scheduledTime = new Date(alert.alertDate + 'T' + alert.time + ':00');
        } else {
            // Legacy support - use today's date
            scheduledTime = new Date();
            scheduledTime.setHours(alert.hour);
            scheduledTime.setMinutes(alert.minute);
            scheduledTime.setSeconds(0);
            scheduledTime.setMilliseconds(0);
        }
        
        // For one-time alerts, if the time has passed, skip scheduling
        if (alert.repeat === 'once' && scheduledTime <= now) {
            console.log(`Skipping expired one-time alert: ${alert.title} scheduled for ${scheduledTime}`);
            return;
        }
        
        // For daily alerts on past dates, schedule for next occurrence 
        if (alert.repeat === 'daily' && scheduledTime <= now) {
            // Find next occurrence (today or tomorrow)
            const todayTime = new Date();
            todayTime.setHours(alert.hour, alert.minute, 0, 0);
            
            if (todayTime > now) {
                scheduledTime = todayTime; // Later today
            } else {
                scheduledTime = new Date(todayTime.getTime() + 24 * 60 * 60 * 1000); // Tomorrow
            }
        }
        
        // Create alarm
        const alarmInfo = {
            when: scheduledTime.getTime()
        };
        
        // Add periodInMinutes for repeating alarms
        if (alert.repeat === 'daily') {
            alarmInfo.periodInMinutes = 24 * 60; // 24 hours
        }
        
        chrome.alarms.create(alert.id, alarmInfo);
    }
    
    // Cancel alert
    function cancelAlert(alertId) {
        chrome.alarms.clear(alertId);
    }
    
    // Toggle alert enabled/disabled
    function toggleAlert(alertId) {
        const alert = activeAlerts.find(a => a.id === alertId);
        if (alert) {
            alert.enabled = !alert.enabled;
            
            if (alert.enabled) {
                scheduleAlert(alert);
            } else {
                cancelAlert(alertId);
            }
            
            saveAlerts();
            renderAlerts();
            renderCalendar(); // Update calendar indicators
        }
    }
    
    // Delete alert
    function deleteAlert(alertId) {
        const index = activeAlerts.findIndex(a => a.id === alertId);
        if (index !== -1) {
            cancelAlert(alertId);
            activeAlerts.splice(index, 1);
            saveAlerts();
            renderAlerts();
            renderCalendar(); // Update calendar indicators
        }
    }
    
    // Edit Modal Functions
    let currentEditingAlertId = null;
    let editModalTimeInterval = null;
    
    function showEditModal(alertId) {
        currentEditingAlertId = alertId;
        const alert = activeAlerts.find(a => a.id === alertId);
        if (!alert) return;
        
        // Populate modal fields with current alert data
        document.getElementById('editAlertTitle').value = alert.title || '';
        document.getElementById('editAlertDate').value = alert.alertDate || new Date().toISOString().split('T')[0];
        document.getElementById('editAlertHour').value = alert.hour.toString().padStart(2, '0');
        document.getElementById('editAlertMinute').value = alert.minute.toString().padStart(2, '0');
        document.getElementById('editAlertRepeat').value = alert.repeat || 'once';
        
        // Show modal
        document.getElementById('editModalOverlay').classList.add('show');
        
        // Start time indicator updates
        updateEditModalTimeFromNow(); // Initial update
        editModalTimeInterval = setInterval(updateEditModalTimeFromNow, 1000); // Update every second
    }
    
    function hideEditModal() {
        document.getElementById('editModalOverlay').classList.remove('show');
        currentEditingAlertId = null;
        
        // Stop time indicator updates
        if (editModalTimeInterval) {
            clearInterval(editModalTimeInterval);
            editModalTimeInterval = null;
        }
    }
    
    function saveEditedAlert() {
        if (!currentEditingAlertId) return;
        
        const alert = activeAlerts.find(a => a.id === currentEditingAlertId);
        if (!alert) return;
        
        // Get updated values from modal
        const newTitle = document.getElementById('editAlertTitle').value.trim();
        const newDate = document.getElementById('editAlertDate').value;
        const newHour = parseInt(document.getElementById('editAlertHour').value) || 0;
        const newMinute = parseInt(document.getElementById('editAlertMinute').value) || 0;
        const newRepeat = document.getElementById('editAlertRepeat').value;
        
        // Cancel old alarm
        cancelAlert(currentEditingAlertId);
        
        // Update alert object
        alert.title = newTitle;
        alert.alertDate = newDate;
        alert.hour = newHour;
        alert.minute = newMinute;
        alert.time = `${newHour.toString().padStart(2, '0')}:${newMinute.toString().padStart(2, '0')}`;
        alert.repeat = newRepeat;
        
        // Schedule updated alarm
        scheduleAlert(alert);
        
        // Save and refresh UI
        saveAlerts();
        renderAlerts();
        renderCalendar();
        
        // Hide modal
        hideEditModal();
        
        // Show success feedback
        const originalButton = document.querySelector(`[data-alert-id="${currentEditingAlertId}"].btn-edit`);
        if (originalButton) {
            const originalColor = originalButton.style.backgroundColor;
            originalButton.style.backgroundColor = '#22c55e';
            setTimeout(() => {
                originalButton.style.backgroundColor = originalColor;
            }, 1500);
        }
    }
    
    // Set alert filter mode
    function setAlertFilter(enableFilter) {
        filterByDate = enableFilter;
        
        // Update button states
        const allBtn = document.getElementById('showAllAlertsBtn');
        const dateBtn = document.getElementById('showDateAlertsBtn');
        
        if (enableFilter) {
            allBtn.classList.remove('active');
            dateBtn.classList.add('active');
        } else {
            allBtn.classList.add('active');
            dateBtn.classList.remove('active');
        }
        
        // Re-render alerts
        renderAlerts();
    }
    
    // Render alerts list
    function renderAlerts() {
        // Rebuild search index when alerts change
        buildAlertSearchIndex();
        
        // If there's an active search, maintain search results
        if (currentSearchTerm && currentSearchTerm.length >= 3) {
            performAlertSearch(currentSearchTerm);
            return;
        }
        
        // Filter alerts if needed
        let alertsToShow = activeAlerts;
        if (filterByDate) {
            alertsToShow = getAlertsForDate(selectedDate);
        }
        
        if (alertsToShow.length === 0) {
            const emptyMessage = filterByDate ? 
                `No alerts for selected date. <br><strong>Select a different date</strong> or click "All Alerts" to see all alerts.` :
                'No alerts set yet. Create your first alert above.';
                
            alertsList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">🔔</div>
                    <p>${emptyMessage}</p>
                </div>
            `;
            return;
        }
        
        // Sort alerts by next fire time (soonest first)
        const sortedAlerts = [...alertsToShow].sort((a, b) => {
            const nextFireTimeA = getNextFireTime(a);
            const nextFireTimeB = getNextFireTime(b);
            return nextFireTimeA - nextFireTimeB;
        });
        
        alertsList.innerHTML = sortedAlerts.map(alert => {
            // Format date info for display
            let dateInfo = '';
            if (alert.alertDate) {
                const alertDate = new Date(alert.alertDate + 'T00:00:00');
                const today = new Date().toISOString().split('T')[0];
                
                if (alert.alertDate === today) {
                    dateInfo = 'Today';
                } else {
                    const options = { month: 'short', day: 'numeric' };
                    dateInfo = alertDate.toLocaleDateString('en-US', options);
                }
            } else {
                dateInfo = 'Today'; // Legacy alerts default to today
            }
            
            const details = filterByDate ? 
                `${alert.time} - ${formatRepeat(alert.repeat)}` :
                `${alert.time} - ${dateInfo} - ${formatRepeat(alert.repeat)}`;
                
            return `
                <div class="alert-item" data-alert-id="${alert.id}">
                    <div class="alert-info">
                        <div class="alert-name">${escapeHtml(alert.title || "Time's Up!")}</div>
                        <div class="alert-details">${details}</div>
                    </div>
                    <div class="alert-controls">
                        <div class="alert-toggle ${alert.enabled ? 'active' : ''}" data-alert-id="${alert.id}">
                            <div class="alert-toggle-slider"></div>
                        </div>
                        <button class="btn-edit" data-alert-id="${alert.id}" title="Edit alert">
                            <svg width="16" height="16" viewBox="-0.175 0 0.4 0.4" xmlns="http://www.w3.org/2000/svg">
                                <g data-name="Layer 2">
                                    <path d="M.025.225A.025.025 0 1 1 .05.2a.025.025 0 0 1-.025.025m.025-.2A.025.025 0 1 0 .025.05.025.025 0 0 0 .05.025m0 .35A.025.025 0 1 0 .025.4.025.025 0 0 0 .05.375" data-name="Layer 1" fill="currentColor"/>
                                </g>
                            </svg>
                        </button>
                        <button class="btn-delete" data-alert-id="${alert.id}">Delete</button>
                    </div>
                </div>
            `;
        }).join('');
        
        // Add event listeners to controls
        alertsList.querySelectorAll('.alert-toggle').forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleAlert(toggle.dataset.alertId);
            });
        });
        
        alertsList.querySelectorAll('.btn-edit').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                showEditModal(btn.dataset.alertId);
            });
        });
        
        alertsList.querySelectorAll('.btn-delete').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                if (confirm('Delete this alert?')) {
                    deleteAlert(btn.dataset.alertId);
                }
            });
        });
    }
    
    // Format repeat text
    function formatRepeat(repeat) {
        const formats = {
            'once': 'One time',
            'daily': 'Every day'
        };
        return formats[repeat] || repeat;
    }
    
    // Escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Accordion Functions
    function setupAccordion() {
        const accordionHeader = document.getElementById('dateAccordionHeader');
        const accordionContent = document.getElementById('dateAccordionContent');
        const accordionArrow = document.getElementById('dateAccordionArrow');
        
        // Set initial state (collapsed)
        accordionContent.classList.remove('expanded');
        accordionArrow.classList.remove('expanded');
        accordionHeader.classList.remove('active');
        isAccordionExpanded = false;
        
        // Add click handler
        accordionHeader.addEventListener('click', toggleAccordion);
        
        // Update date preview initially
        updateDatePreview();
    }
    
    function toggleAccordion() {
        const accordionHeader = document.getElementById('dateAccordionHeader');
        const accordionContent = document.getElementById('dateAccordionContent');
        const accordionArrow = document.getElementById('dateAccordionArrow');
        
        isAccordionExpanded = !isAccordionExpanded;
        
        if (isAccordionExpanded) {
            accordionContent.classList.add('expanded');
            accordionArrow.classList.add('expanded');
            accordionHeader.classList.add('active');
        } else {
            accordionContent.classList.remove('expanded');
            accordionArrow.classList.remove('expanded');
            accordionHeader.classList.remove('active');
        }
    }
    
    function updateDatePreview() {
        const preview = document.getElementById('selectedDatePreview');
        const today = new Date().toISOString().split('T')[0];
        
        if (selectedDate === today) {
            preview.textContent = 'Today';
        } else {
            const date = new Date(selectedDate + 'T00:00:00');
            const options = { weekday: 'short', month: 'short', day: 'numeric' };
            preview.textContent = date.toLocaleDateString('en-US', options);
        }
    }
    
    // Calendar Functions
    function setupCalendar() {
        // Set up event listeners for calendar navigation
        document.getElementById('prevMonth').addEventListener('click', () => {
            currentCalendarDate.setMonth(currentCalendarDate.getMonth() - 1);
            renderCalendar();
        });
        
        document.getElementById('nextMonth').addEventListener('click', () => {
            currentCalendarDate.setMonth(currentCalendarDate.getMonth() + 1);
            renderCalendar();
        });
        
        // Initial render
        renderCalendar();
        updateSelectedDateDisplay();
    }
    
    function renderCalendar() {
        const calendar = document.getElementById('calendar');
        const monthYear = document.getElementById('monthYear');
        
        // Get current month info
        const year = currentCalendarDate.getFullYear();
        const month = currentCalendarDate.getMonth();
        const today = new Date().toISOString().split('T')[0];
        
        // Update month/year display
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                           'July', 'August', 'September', 'October', 'November', 'December'];
        monthYear.textContent = `${monthNames[month]} ${year}`;
        
        // Clear calendar
        calendar.innerHTML = '';
        
        // Day headers (Monday first)
        const dayHeaders = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        dayHeaders.forEach((day, index) => {
            const dayHeader = document.createElement('div');
            dayHeader.className = 'calendar-day-header';
            if (index >= 5) { // Saturday and Sunday
                dayHeader.classList.add('weekend');
            }
            dayHeader.textContent = day;
            calendar.appendChild(dayHeader);
        });
        
        // Get first day of month and number of days
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const startDate = new Date(firstDay);
        
        // Adjust for Monday-first week (0=Sunday, 1=Monday, etc.)
        let dayOfWeek = firstDay.getDay();
        dayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert Sunday (0) to 6, others shift by -1
        startDate.setDate(startDate.getDate() - dayOfWeek);
        
        // Generate 42 days (6 weeks)
        for (let i = 0; i < 42; i++) {
            const currentDate = new Date(startDate);
            currentDate.setDate(startDate.getDate() + i);
            
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = currentDate.getDate();
            
            const dateString = currentDate.toISOString().split('T')[0];
            
            // Add classes for styling
            if (currentDate.getMonth() !== month) {
                dayElement.classList.add('other-month');
            }
            
            if (dateString === today) {
                dayElement.classList.add('today');
            }
            
            if (dateString === selectedDate) {
                dayElement.classList.add('selected');
            }
            
            // Check if it's weekend (Saturday=6, Sunday=0)
            const dayOfWeek = currentDate.getDay();
            if (dayOfWeek === 0 || dayOfWeek === 6) {
                dayElement.classList.add('weekend');
            }
            
            // Check if this date has alerts
            const alertsForDate = getAlertsForDate(dateString);
            if (alertsForDate.length > 0) {
                dayElement.classList.add('has-alerts');
            }
            
            // Add click handler
            dayElement.addEventListener('click', () => {
                handleDateSelection(dateString);
            });
            
            calendar.appendChild(dayElement);
        }
    }
    
    function handleDateSelection(dateString) {
        selectedDate = dateString;
        renderCalendar(); // Re-render to update selected styling
        updateSelectedDateDisplay();
        updateDatePreview(); // Update accordion header preview
        updateTimeFromNow(); // Update the add button text
    }
    
    function updateSelectedDateDisplay() {
        const display = document.getElementById('selectedDateDisplay');
        const today = new Date().toISOString().split('T')[0];
        
        if (selectedDate === today) {
            display.innerHTML = 'Selected: <strong>Today</strong>';
        } else {
            const date = new Date(selectedDate + 'T00:00:00');
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            const formattedDate = date.toLocaleDateString('en-US', options);
            display.innerHTML = `Selected: <strong>${formattedDate}</strong>`;
        }
    }
    
    function getAlertsForDate(dateString) {
        return activeAlerts.filter(alert => {
            // If alert has alertDate, check exact match
            if (alert.alertDate) {
                return alert.alertDate === dateString;
            }
            // If no alertDate, assume it's for today (legacy alerts)
            const today = new Date().toISOString().split('T')[0];
            return dateString === today;
        });
    }
    
    // Alert Search Functions
    function setupAlertSearch() {
        const searchInput = document.getElementById('alertSearch');
        const clearButton = document.getElementById('clearAlertSearch');
        
        if (!searchInput || !clearButton) {
            console.log('Alert search elements not found');
            return;
        }
        
        // Search input event listener
        searchInput.addEventListener('input', (e) => {
            const term = e.target.value.trim();
            currentSearchTerm = term;
            performAlertSearch(term);
            
            // Toggle clear button visibility
            if (term.length > 0) {
                clearButton.classList.add('visible');
            } else {
                clearButton.classList.remove('visible');
            }
        });
        
        // Clear button event listener
        clearButton.addEventListener('click', () => {
            searchInput.value = '';
            currentSearchTerm = '';
            clearButton.classList.remove('visible');
            performAlertSearch('');
            searchInput.focus();
        });
        
        // Build initial search index
        buildAlertSearchIndex();
    }
    
    function buildAlertSearchIndex() {
        searchIndex = activeAlerts.map(alert => {
            // Format date info for search
            let dateText = '';
            if (alert.alertDate) {
                const alertDate = new Date(alert.alertDate + 'T00:00:00');
                const today = new Date().toISOString().split('T')[0];
                
                if (alert.alertDate === today) {
                    dateText = 'today';
                } else {
                    const options = { weekday: 'long', month: 'long', day: 'numeric' };
                    dateText = alertDate.toLocaleDateString('en-US', options).toLowerCase();
                }
            } else {
                dateText = 'today';
            }
            
            // Build searchable text
            const searchableFields = [
                alert.title || "time's up",
                alert.time,
                dateText,
                formatRepeat(alert.repeat).toLowerCase(),
                alert.repeat
            ];
            
            return {
                alert: alert,
                searchText: searchableFields.join(' ').toLowerCase()
            };
        });
    }
    
    function performAlertSearch(searchTerm) {
        if (searchTerm.length < 3) {
            // Show all alerts when search term is too short
            renderAlerts();
            return;
        }
        
        const term = searchTerm.toLowerCase();
        
        // Find matching alerts
        const matches = searchIndex.filter(item => {
            // Check for exact word matches first
            const words = item.searchText.split(/\s+/);
            const hasExactMatch = words.some(word => word === term);
            
            // Then check for partial matches
            const hasPartialMatch = item.searchText.includes(term);
            
            return hasExactMatch || hasPartialMatch;
        });
        
        // Render filtered alerts
        renderFilteredAlerts(matches.map(m => m.alert), searchTerm);
    }
    
    function renderFilteredAlerts(filteredAlerts, searchTerm) {
        if (filteredAlerts.length === 0) {
            alertsList.innerHTML = `
                <div class="alert-search-empty-state">
                    <div class="alert-search-empty-state-icon">🔍</div>
                    <p>No alerts found matching "<strong>${escapeHtml(searchTerm)}</strong>"</p>
                    <p style="font-size: 12px; margin-top: 10px;">Try searching for alert titles, times, or dates</p>
                </div>
            `;
            return;
        }
        
        // Sort alerts by next fire time (soonest first)
        const sortedAlerts = [...filteredAlerts].sort((a, b) => {
            const nextFireTimeA = getNextFireTime(a);
            const nextFireTimeB = getNextFireTime(b);
            return nextFireTimeA - nextFireTimeB;
        });
        
        // Render alerts with search highlighting
        alertsList.innerHTML = sortedAlerts.map(alert => {
            // Format date info for display
            let dateInfo = '';
            if (alert.alertDate) {
                const alertDate = new Date(alert.alertDate + 'T00:00:00');
                const today = new Date().toISOString().split('T')[0];
                
                if (alert.alertDate === today) {
                    dateInfo = 'Today';
                } else {
                    const options = { month: 'short', day: 'numeric' };
                    dateInfo = alertDate.toLocaleDateString('en-US', options);
                }
            } else {
                dateInfo = 'Today'; // Legacy alerts default to today
            }
            
            const details = `${alert.time} - ${dateInfo} - ${formatRepeat(alert.repeat)}`;
            
            return `
                <div class="alert-item alert-item-highlighted" data-alert-id="${alert.id}">
                    <div class="alert-info">
                        <div class="alert-name">${escapeHtml(alert.title || "Time's Up!")}</div>
                        <div class="alert-details">${details}</div>
                    </div>
                    <div class="alert-controls">
                        <div class="alert-toggle ${alert.enabled ? 'active' : ''}" data-alert-id="${alert.id}">
                            <div class="alert-toggle-slider"></div>
                        </div>
                        <button class="btn-edit" data-alert-id="${alert.id}" title="Edit alert">
                            <svg width="16" height="16" viewBox="-0.175 0 0.4 0.4" xmlns="http://www.w3.org/2000/svg">
                                <g data-name="Layer 2">
                                    <path d="M.025.225A.025.025 0 1 1 .05.2a.025.025 0 0 1-.025.025m.025-.2A.025.025 0 1 0 .025.05.025.025 0 0 0 .05.025m0 .35A.025.025 0 1 0 .025.4.025.025 0 0 0 .05.375" data-name="Layer 1" fill="currentColor"/>
                                </g>
                            </svg>
                        </button>
                        <button class="btn-delete" data-alert-id="${alert.id}">Delete</button>
                    </div>
                </div>
            `;
        }).join('');
        
        // Add event listeners to controls
        alertsList.querySelectorAll('.alert-toggle').forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleAlert(toggle.dataset.alertId);
            });
        });
        
        alertsList.querySelectorAll('.btn-edit').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                showEditModal(btn.dataset.alertId);
            });
        });
        
        alertsList.querySelectorAll('.btn-delete').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                if (confirm('Delete this alert?')) {
                    deleteAlert(btn.dataset.alertId);
                }
            });
        });
    }
    
    // Enhanced window position tracking with reliable persistence
    function setupWindowPositioning() {
        let saveTimeout;
        let lastKnownPosition = null;
        
        const saveWindowSettings = (immediate = false) => {
            if (!immediate && saveTimeout) {
                clearTimeout(saveTimeout);
            }
            
            const saveAction = () => {
                chrome.windows.getCurrent((window) => {
                    if (!window) return;
                    
                    const settings = {
                        width: window.width,
                        height: window.height,
                        x: window.left,
                        y: window.top,
                        lastSaved: Date.now()
                    };
                    
                    // Save to localStorage as primary storage (more reliable for position)
                    try {
                        localStorage.setItem('gmb-alert-manager-window-settings', JSON.stringify(settings));
                        console.log('Alert Manager window position saved to localStorage:', settings);
                    } catch (error) {
                        console.log('Could not save to localStorage:', error);
                    }
                    
                    // Also save to chrome.storage.local as backup
                    chrome.storage.local.set({
                        'gmb-alert-manager-window-settings': settings
                    }).catch(error => {
                        console.log('Could not save to chrome.storage:', error);
                    });
                    
                    lastKnownPosition = settings;
                });
            };
            
            if (immediate) {
                saveAction();
            } else {
                saveTimeout = setTimeout(saveAction, 300);
            }
        };
        
        // Check for position changes using window polling (more reliable than events)
        const checkPositionChange = () => {
            chrome.windows.getCurrent((window) => {
                if (!window) return;
                
                const currentPosition = {
                    width: window.width,
                    height: window.height,
                    x: window.left,
                    y: window.top
                };
                
                // Check if position changed
                if (!lastKnownPosition || 
                    lastKnownPosition.x !== currentPosition.x ||
                    lastKnownPosition.y !== currentPosition.y ||
                    lastKnownPosition.width !== currentPosition.width ||
                    lastKnownPosition.height !== currentPosition.height) {
                    
                    console.log('Alert Manager window position changed:', currentPosition);
                    saveWindowSettings(false); // Debounced save
                }
            });
        };
        
        // Save initial position when window loads
        setTimeout(() => {
            saveWindowSettings(true);
        }, 100);
        
        // Poll for position changes every 1 second (catches moves and resizes)
        const positionCheckInterval = setInterval(checkPositionChange, 1000);
        
        // Also listen for standard events as backup
        window.addEventListener('resize', () => saveWindowSettings(false));
        
        // Save immediately on unload
        window.addEventListener('beforeunload', () => {
            clearInterval(positionCheckInterval);
            saveWindowSettings(true);
        });
        
        // Save immediately on page hide (when window loses focus)
        window.addEventListener('pagehide', () => {
            saveWindowSettings(true);
        });
        
        console.log('Alert Manager window positioning setup complete');
    }
    
    // Initialize when DOM is ready
    init();
    
})();