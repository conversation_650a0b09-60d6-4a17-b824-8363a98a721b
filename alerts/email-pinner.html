<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pinned Emails</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #d1d5db;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto";
            font-size: 14px;
        }
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #333;
        }
        .title {
            margin: 0;
            color: #fff;
            font-size: 18px;
            font-weight: 600;
        }
        .email-item {
            background: rgba(124, 58, 237, 0.1);
            border: 1px solid rgba(124, 58, 237, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
        }
        .email-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        .email-title {
            margin: 0;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            flex: 1;
            margin-right: 10px;
        }
        .email-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .unpin-btn {
            background: #ef4444;
            border: none;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .unpin-btn:hover {
            background: #dc2626;
            transform: scale(1.05);
        }
        .email-sender {
            color: #888;
            font-size: 12px;
            margin-bottom: 8px;
        }
        .email-timestamp-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        .email-timestamp {
            color: #888;
            font-size: 12px;
        }
        .email-age {
            margin-left: auto;
        }
        .email-age-normal {
            color: #888;
            font-size: 11px;
            font-style: italic;
        }
        .email-age-warning {
            background: rgba(30, 30, 30, 0.8);
            color: #fff;
            border: 3px dashed #f97316;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
        }
        .email-age-critical {
            background: rgba(30, 30, 30, 0.8);
            color: #fff;
            border: 3px dashed #ef4444;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
        }
        .open-email-btn {
            background: #7C3AED;
            color: white;
            text-decoration: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
            transition: all 0.2s ease;
        }
        .open-email-btn:hover {
            background: #6d28d9;
            transform: scale(1.05);
        }
        .empty-state {
            text-align: center;
            color: #888;
            padding: 40px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title" id="pageTitle">Loading...</h1>
    </div>
    <div class="content" id="emailsContainer">
        <div class="empty-state">Loading pinned emails...</div>
    </div>

    <script src="email-pinner.js"></script>
</body>
</html>