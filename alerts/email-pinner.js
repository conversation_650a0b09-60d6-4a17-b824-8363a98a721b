// Email Pinner popup window JavaScript

// Context validation and safe Chrome API wrappers
function isExtensionContextValid() {
    try {
        return !!(chrome && chrome.storage && chrome.storage.local && chrome.runtime && chrome.runtime.id);
    } catch (error) {
        return false;
    }
}

async function safeChromeStorageGet(keys) {
    try {
        if (!isExtensionContextValid()) {
            console.log('Email Pinner Popup: Extension context invalid, returning empty data');
            return {};
        }
        return await chrome.storage.local.get(keys);
    } catch (error) {
        if (error.message && error.message.includes('Extension context invalidated')) {
            console.log('Email Pinner Popup: Context invalidated during storage get');
            return {};
        }
        console.error('Email Pinner Popup: Storage get error:', error);
        return {};
    }
}

async function safeChromeStorageSet(data) {
    try {
        if (!isExtensionContextValid()) {
            console.log('Email Pinner Popup: Extension context invalid, skipping storage set');
            return false;
        }
        await chrome.storage.local.set(data);
        return true;
    } catch (error) {
        if (error.message && error.message.includes('Extension context invalidated')) {
            console.log('Email Pinner Popup: Context invalidated during storage set');
            return false;
        }
        console.error('Email Pinner Popup: Storage set error:', error);
        return false;
    }
}

function safeCreateTab(url) {
    try {
        if (!isExtensionContextValid()) {
            console.log('Email Pinner Popup: Extension context invalid, opening in same tab');
            window.open(url, '_blank');
            return;
        }
        chrome.tabs.create({ url: url }, function() {
            if (chrome.runtime.lastError) {
                console.log('Email Pinner Popup: Tab creation failed, using fallback');
                window.open(url, '_blank');
            }
            // Close the popup window after opening the email
            window.close();
        });
    } catch (error) {
        console.log('Email Pinner Popup: Tab API failed, using fallback');
        window.open(url, '_blank');
    }
}

// Simple unpin function - remove div immediately
async function unpinEmail(emailId) {
    try {
        console.log('📌 Unpinning individual email:', emailId);
        
        // Get current pinned emails directly from storage using safe wrapper
        const result = await safeChromeStorageGet('gmailPinnedEmails');
        let pinnedEmails = result.gmailPinnedEmails || [];
        
        // ONLY support composite ID system - no fallbacks
        pinnedEmails = pinnedEmails.filter(email => 
            email.uniqueCompositeId !== emailId
        );
        
        // Save updated list using safe wrapper
        const saveSuccess = await safeChromeStorageSet({ gmailPinnedEmails: pinnedEmails });
        
        if (!saveSuccess) {
            console.log('📌 Failed to save updated pinned emails - extension context may be invalid');
            alert('Unable to save changes. Extension may need to be reloaded.');
            return;
        }
        
        console.log('📌 Individual email unpinned successfully, remaining:', pinnedEmails.length);
        
        // Remove the div immediately (no reload needed)
        const emailDiv = document.querySelector(`[data-email-id="${CSS.escape(emailId)}"]`);
        if (emailDiv) {
            emailDiv.remove();
        }
        
        // Update the counter in the title
        const titleElement = document.getElementById('pageTitle');
        if (titleElement) {
            titleElement.textContent = `Pinned Emails (${pinnedEmails.length})`;
        }
        
        // If no emails left, show empty state
        if (pinnedEmails.length === 0) {
            const container = document.getElementById('emailsContainer');
            if (container) {
                container.innerHTML = '<div class="empty-state">No pinned emails yet. Pin emails from Gmail to access them here.</div>';
            }
        }
        
    } catch (error) {
        console.error('📌 Error unpinning individual email:', error);
        alert('Error unpinning email. Please try again.');
    }
}

// Calculate days ago from email timestamp
function calculateDaysAgo(timestamp) {
    try {
        // Parse Gmail timestamp format like "Fri - 11 Jul, 00:35" or "Tue - 08 Jul, 20:56"
        // Extract day and month, assume current year
        const match = timestamp.match(/(\d{1,2})\s+(\w+)/);
        if (!match) {
            return 0; // If no match, assume recent
        }
        
        const day = parseInt(match[1]);
        const month = match[2];
        const currentYear = new Date().getFullYear();
        
        // Create date string in format that JavaScript can parse
        const dateString = `${month} ${day}, ${currentYear}`;
        const emailDate = new Date(dateString);
        
        if (isNaN(emailDate.getTime())) {
            return 0; // If parsing fails, assume recent
        }
        
        const now = new Date();
        const diffTime = Math.abs(now - emailDate);
        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
        
        return diffDays;
    } catch (error) {
        return 0; // If error, assume recent
    }
}

// Create age indicator HTML
function createAgeIndicator(daysAgo) {
    const dayText = daysAgo === 1 ? 'day' : 'days';
    
    if (daysAgo >= 5) {
        // Critical warning for 5+ days old
        return `<span class="email-age-critical">⛔ Email received ${daysAgo} ${dayText} ago</span>`;
    } else if (daysAgo >= 2) {
        // Warning for 2-4 days old
        return `<span class="email-age-warning">⚠️ Email received ${daysAgo} ${dayText} ago</span>`;
    } else if (daysAgo === 1) {
        // Normal for 1 day old
        return `<span class="email-age-normal">Email received ${daysAgo} ${dayText} ago</span>`;
    } else {
        // Today
        return `<span class="email-age-normal">Email received today</span>`;
    }
}

// Load and display pinned emails
async function loadPinnedEmails() {
    try {
        const result = await safeChromeStorageGet('gmailPinnedEmails');
        let pinnedEmails = result.gmailPinnedEmails || [];
        
        // ONLY keep emails with complete new system data - no fallbacks
        const originalLength = pinnedEmails.length;
        pinnedEmails = pinnedEmails.filter(email => {
            return email && 
                   email.uniqueCompositeId && 
                   email.nativeTimestamp && 
                   email.title && 
                   email.sender && 
                   email.url;
        });
        
        // If we filtered out old format emails, save the cleaned array
        if (pinnedEmails.length !== originalLength) {
            console.log(`📌 Filtered out ${originalLength - pinnedEmails.length} old format email entries`);
            const saveSuccess = await safeChromeStorageSet({ gmailPinnedEmails: pinnedEmails });
            
            if (!saveSuccess) {
                console.log('📌 Failed to save cleaned pinned emails - extension context may be invalid');
            }
        }
        
        // Update title with count
        document.getElementById('pageTitle').textContent = `Pinned Emails (${pinnedEmails.length})`;
        
        const container = document.getElementById('emailsContainer');
        
        if (pinnedEmails.length === 0) {
            container.innerHTML = '<div class="empty-state">No pinned emails yet. Pin emails from Gmail to access them here.</div>';
        } else {
            // Sort using ONLY native timestamp - no fallbacks
            const sortedEmails = pinnedEmails.sort((a, b) => {
                const parseTimestamp = (email) => {
                    try {
                        // ONLY use nativeTimestamp - no fallbacks
                        const parsedDate = new Date(email.nativeTimestamp);
                        return !isNaN(parsedDate.getTime()) ? parsedDate : new Date(email.dateAdded || 0);
                    } catch (error) {
                        return new Date(email.dateAdded || 0);
                    }
                };
                
                return parseTimestamp(a) - parseTimestamp(b);
            });
            
            container.innerHTML = sortedEmails.map(email => {
                // Use ONLY composite ID - no fallbacks
                const emailId = email.uniqueCompositeId;
                
                // Escape HTML to prevent XSS
                const title = email.title.replace(/'/g, '&#39;').replace(/"/g, '&quot;');
                const sender = email.sender.replace(/'/g, '&#39;').replace(/"/g, '&quot;');
                const safeEmailId = emailId.replace(/'/g, '&#39;').replace(/"/g, '&quot;');
                
                // Use ONLY native timestamp - no fallbacks
                const displayTimestamp = email.nativeTimestamp.replace(/'/g, '&#39;').replace(/"/g, '&quot;');
                
                // Calculate days since THIS specific email
                const daysAgo = calculateDaysAgo(email.nativeTimestamp);
                const ageIndicator = createAgeIndicator(daysAgo);
                
                // Determine if this is a jump link
                const isJumpLink = email.url.includes('jumpToMessage=');
                
                // For "Open Email" actions, use clean URL and add scroll parameter to navigate to jump links container
                let url = email.url;
                if (!isJumpLink) {
                    // Strip any jump parameters for "Open Email" actions
                    url = url.replace(/[?&]jumpToMessage=[^&]*&?/g, '');
                    url = url.replace(/[?&]$/, ''); // Clean up trailing ? or &
                    
                    // Add scroll parameter for "Open Email" actions to navigate to jump links container
                    const separator = url.includes('?') ? '&' : '?';
                    url = `${url}${separator}scrollToJumpLinks=true`;
                }
                url = url.replace(/'/g, '&#39;').replace(/"/g, '&quot;');
                
                // Show individual email indicator for all emails (since all are now individual)
                const emailTypeIndicator = '<span style="font-size: 10px; color: #7C3AED; margin-left: 8px;" title="Individual email from thread">📍</span>';
                
                // Determine button text and add jump arrow if applicable
                const buttonText = isJumpLink ? 'Jump to Message' : 'Open Email';
                const jumpIndicator = isJumpLink ? '<span style="margin-right: 4px;" title="Jump link - will scroll to specific message">↗</span>' : '';
                
                return `
                    <div class="email-item" data-email-id="${safeEmailId}">
                        <div class="email-header">
                            <h4 class="email-title">${title}${emailTypeIndicator}</h4>
                            <div class="email-actions">
                                <a href="${url}" target="_blank" class="open-email-btn">${jumpIndicator}${buttonText}</a>
                                <button class="unpin-btn" data-email-id="${safeEmailId}">Unpin</button>
                            </div>
                        </div>
                        <div class="email-sender">From: ${sender}</div>
                        <div class="email-timestamp-row">
                            <span class="email-timestamp">${displayTimestamp}</span>
                            <span class="email-age">${ageIndicator}</span>
                        </div>
                    </div>
                `;
            }).join('');
            
            // Add event delegation for unpin buttons
            addUnpinEventListeners();
        }
        
    } catch (error) {
        console.error('📌 Error loading pinned emails:', error);
        document.getElementById('emailsContainer').innerHTML = '<div class="empty-state">Error loading pinned emails.</div>';
    }
}

// Add event listeners for unpin buttons
function addUnpinEventListeners() {
    const container = document.getElementById('emailsContainer');
    if (!container) return;
    
    // Remove existing listener if any
    container.removeEventListener('click', handleButtonClick);
    
    // Add event delegation for all unpin buttons
    container.addEventListener('click', handleButtonClick);
}

// Handle button clicks (unpin and open email)
function handleButtonClick(event) {
    // Handle unpin button clicks
    if (event.target.classList.contains('unpin-btn')) {
        event.preventDefault();
        event.stopPropagation();
        
        const emailId = event.target.getAttribute('data-email-id');
        if (emailId) {
            unpinEmail(emailId);
        }
    }
    
    // Handle open email button clicks
    if (event.target.classList.contains('open-email-btn')) {
        event.preventDefault();
        event.stopPropagation();
        
        const emailUrl = event.target.getAttribute('href');
        if (emailUrl && emailUrl !== '#') {
            // Check if this is a jump link
            const isJumpLink = emailUrl.includes('jumpToMessage=');
            
            if (isJumpLink) {
                // Show jump feedback
                const originalText = event.target.textContent;
                event.target.textContent = 'Jumping...';
                event.target.style.opacity = '0.7';
                
                // Open the email with jump link using safe wrapper
                safeCreateTab(emailUrl);
            } else {
                // Regular email opening using safe wrapper
                safeCreateTab(emailUrl);
            }
        }
    }
}

// Load emails when page loads
document.addEventListener('DOMContentLoaded', loadPinnedEmails);