<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>minder</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #0a0a0a;
            color: #d1d5db;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            min-height: 100vh;
            overflow: auto;
            box-sizing: border-box;
        }
        
        .notification-header {
            background: #1a1a1a;
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            font-weight: 700;
            border-bottom: 1px solid #2a2a2a;
        }
        
        .notification-container {
            padding: 20px;
            text-align: center;
            min-height: calc(100vh - 52px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .notification-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 20px;
            background: #7C3AED;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .notification-message {
            font-size: 16px;
            color: #d1d5db;
            margin-bottom: 25px;
            line-height: 1.5;
        }
        
        .notification-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .btn {
            padding: 8px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-dismiss {
            background: #374151;
            color: #d1d5db;
        }
        
        .btn-dismiss:hover {
            background: #4b5563;
            transform: translateY(-1px);
        }
        
        .btn-snooze {
            background: #7C3AED;
            color: white;
        }
        
        .btn-snooze:hover {
            background: #6b31db;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
        }
        
        .snooze-options {
            display: none;
            margin-top: 15px;
            margin-bottom: 20px;
            gap: 8px;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
        }
        
        .snooze-options.show {
            display: flex;
        }
        
        .snooze-btn {
            padding: 6px 12px;
            background: #2a2a2a;
            border: 1px solid #374151;
            border-radius: 4px;
            font-size: 12px;
            color: #d1d5db;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 40px;
            max-width: 60px;
            text-align: center;
            white-space: nowrap;
        }
        
        .snooze-btn:hover {
            background: #374151;
            border-color: #6b7280;
            color: #d1d5db;
        }
    </style>
</head>
<body>
    <div class="notification-header">
        <span>Alert Reminder</span>
        <span id="alertTime" style="font-size: 12px; color: #9ca3af; font-weight: 400;"></span>
    </div>
    
    <div class="notification-container">
        <div class="notification-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C10.9 2 10 2.9 10 4C10 4.1 10 4.2 10 4.29C7.12 5.14 5 7.82 5 11V17L3 19V20H21V19L19 17V11C19 7.82 16.88 5.14 14 4.29C14 4.2 14 4.1 14 4C14 2.9 13.1 2 12 2ZM12 22C13.11 22 14 21.11 14 20H10C10 21.11 10.89 22 12 22Z" fill="white"/>
            </svg>
        </div>
        <p class="notification-message" id="alertMessage">Time for your scheduled task!</p>
        
        <div class="notification-actions">
            <button class="btn btn-dismiss" id="dismissBtn">Dismiss</button>
            <button class="btn btn-snooze" id="snoozeBtn">Snooze</button>
        </div>
        
        <div class="snooze-options" id="snoozeOptions">
            <button class="snooze-btn" data-minutes="5">5 min</button>
            <button class="snooze-btn" data-minutes="10">10 min</button>
            <button class="snooze-btn" data-minutes="15">15 min</button>
            <button class="snooze-btn" data-minutes="30">30 min</button>
            <button class="snooze-btn" data-minutes="60">1 hour</button>
        </div>
    </div>
    
    <script src="alert-notification.js"></script>
</body>
</html>