// Alert Notification Window Script
(function() {
    'use strict';
    
    // Get URL parameters
    const params = new URLSearchParams(window.location.search);
    const alertId = params.get('alertId');
    const alertTitle = params.get('title');
    const alertTime = params.get('time');
    
    // DOM elements
    const alertMessage = document.getElementById('alertMessage');
    const alertTimeEl = document.getElementById('alertTime');
    const dismissBtn = document.getElementById('dismissBtn');
    const snoozeBtn = document.getElementById('snoozeBtn');
    const snoozeOptions = document.getElementById('snoozeOptions');
    
    // Set alert details
    alertMessage.textContent = alertTitle || "Time's Up!";
    
    // Show current time
    const now = new Date();
    alertTimeEl.textContent = now.toLocaleTimeString();
    
    // Play notification sound if enabled
    async function playNotificationSound() {
        try {
            const result = await chrome.storage.local.get(['gmbExtractorSettings']);
            const settings = result.gmbExtractorSettings || {};
            
            if (settings.alertSound !== false) {
                // Create audio element
                const audio = new Audio(chrome.runtime.getURL('sounds/notification-bell.mp3'));
                audio.volume = 0.7;
                audio.play().catch(err => console.log('Audio play failed:', err));
            }
        } catch (error) {
            console.error('Error playing sound:', error);
        }
    }
    
    // Play sound on load
    playNotificationSound();
    
    // Store timeout IDs to clear selectively
    const managedTimeouts = new Set();
    const managedIntervals = new Set();
    
    // Wrapper function to track timeouts
    const createManagedTimeout = (callback, delay) => {
        const timeoutId = setTimeout(() => {
            managedTimeouts.delete(timeoutId);
            callback();
        }, delay);
        managedTimeouts.add(timeoutId);
        return timeoutId;
    };
    
    // Wrapper function to track intervals
    const createManagedInterval = (callback, delay) => {
        const intervalId = setInterval(callback, delay);
        managedIntervals.add(intervalId);
        return intervalId;
    };
    
    // Comprehensive alert dismissal function
    async function dismissAlert() {
        try {
            // Clear any audio that might be playing
            const audioElements = document.querySelectorAll('audio');
            audioElements.forEach(audio => {
                audio.pause();
                audio.currentTime = 0;
            });
            
            // Clear only our managed timers/intervals
            managedTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
            managedIntervals.forEach(intervalId => clearInterval(intervalId));
            managedTimeouts.clear();
            managedIntervals.clear();
            
            // Notify background script of dismissal
            chrome.runtime.sendMessage({
                action: 'alertDismissed',
                alertId: alertId,
                windowId: window.id || 'unknown'
            }).catch(() => {
                // Background script might not be available, ignore error
                console.log('Could not notify background script of dismissal');
            });
            
            // Clear local storage data for this alert
            if (alertId) {
                localStorage.removeItem(`alert-${alertId}`);
                localStorage.removeItem(`timer-${alertId}`);
            }
            
            // Clear any notification state
            try {
                chrome.storage.local.remove([
                    `snooze-${alertId}`,
                    `timer-quick-timer-${alertId}`,
                    `notification-${alertId}`
                ]);
            } catch (error) {
                console.log('Storage cleanup warning:', error);
            }
            
            // Close window immediately - no delay needed
            window.close();
            
        } catch (error) {
            console.error('Error during alert dismissal cleanup:', error);
            // Force close even if cleanup fails
            window.close();
        }
    }
    
    // Dismiss button
    dismissBtn.addEventListener('click', () => {
        dismissAlert();
    });
    
    // Snooze button - show options with dynamic window resizing
    snoozeBtn.addEventListener('click', () => {
        const isShowing = snoozeOptions.classList.contains('show');
        snoozeOptions.classList.toggle('show');
        
        // Resize window to accommodate snooze options
        resizeWindowForSnoozeOptions(!isShowing);
    });
    
    // Snooze option buttons
    document.querySelectorAll('.snooze-btn').forEach(btn => {
        btn.addEventListener('click', async () => {
            const minutes = parseInt(btn.dataset.minutes);
            
            if (alertId) {
                // Create snooze alarm
                const snoozeTime = Date.now() + (minutes * 60 * 1000);
                
                chrome.runtime.sendMessage({
                    action: 'snoozeAlert',
                    alertId: alertId,
                    snoozeTime: snoozeTime,
                    snoozeMinutes: minutes
                }, () => {
                    dismissAlert();
                });
            } else {
                dismissAlert();
            }
        });
    });
    
    // Auto-close removed - notifications persist until manually dismissed or snoozed
    // This ensures notifications remain visible even if user is away from computer
    // createManagedTimeout(() => {
    //     dismissAlert();
    // }, 5 * 60 * 1000);
    
    // Window positioning is now handled by background script during creation
    // No initial positioning needed - window is created at correct position
    
    // Load position from localStorage and sync to chrome.storage on startup
    function loadAndSyncPosition() {
        try {
            const saved = localStorage.getItem('gmb-alert-window-settings');
            if (saved) {
                const settings = JSON.parse(saved);
                console.log('Alert: Found position in localStorage, syncing to chrome.storage:', settings);
                
                // Sync localStorage position to chrome.storage for background script
                chrome.storage.local.set({
                    'gmb-alert-window-settings': settings
                }).catch(error => {
                    console.log('Could not sync to chrome.storage:', error);
                });
            }
        } catch (error) {
            console.log('Could not load from localStorage:', error);
        }
    }
    
    // Enhanced window position tracking with reliable persistence
    function setupWindowPositioning() {
        let saveTimeout;
        let lastKnownPosition = null;
        
        const saveWindowSettings = (immediate = false) => {
            if (!immediate && saveTimeout) {
                clearTimeout(saveTimeout);
            }
            
            const saveAction = () => {
                chrome.windows.getCurrent((window) => {
                    if (!window) return;
                    
                    // Validate position is on screen before saving
                    const maxLeft = screen.width - window.width;
                    const maxTop = screen.height - window.height;
                    
                    const settings = {
                        width: window.width,
                        height: window.height,
                        x: Math.max(0, Math.min(window.left, maxLeft)),
                        y: Math.max(0, Math.min(window.top, maxTop)),
                        lastSaved: Date.now()
                    };
                    
                    // Save to localStorage as primary storage (more reliable for position)
                    try {
                        localStorage.setItem('gmb-alert-window-settings', JSON.stringify(settings));
                        console.log('Alert window position saved to localStorage:', settings);
                    } catch (error) {
                        console.log('Could not save to localStorage:', error);
                    }
                    
                    // Also save to chrome.storage.local as backup
                    chrome.storage.local.set({
                        'gmb-alert-window-settings': settings
                    }).catch(error => {
                        console.log('Could not save to chrome.storage:', error);
                    });
                    
                    lastKnownPosition = settings;
                });
            };
            
            if (immediate) {
                saveAction();
            } else {
                saveTimeout = createManagedTimeout(saveAction, 300);
            }
        };
        
        // Check for position changes using window polling (more reliable than events)
        const checkPositionChange = () => {
            chrome.windows.getCurrent((window) => {
                if (!window) return;
                
                const currentPosition = {
                    width: window.width,
                    height: window.height,
                    x: window.left,
                    y: window.top
                };
                
                // Check if position changed
                if (!lastKnownPosition || 
                    lastKnownPosition.x !== currentPosition.x ||
                    lastKnownPosition.y !== currentPosition.y ||
                    lastKnownPosition.width !== currentPosition.width ||
                    lastKnownPosition.height !== currentPosition.height) {
                    
                    console.log('Alert window position changed:', currentPosition);
                    saveWindowSettings(false); // Debounced save
                }
            });
        };
        
        // Save initial position when window loads
        createManagedTimeout(() => {
            saveWindowSettings(true);
        }, 100);
        
        // Poll for position changes every 1 second (catches moves and resizes)
        const positionCheckInterval = createManagedInterval(checkPositionChange, 1000);
        
        // Also listen for standard events as backup
        window.addEventListener('resize', () => saveWindowSettings(false));
        
        // Save immediately on unload
        window.addEventListener('beforeunload', () => {
            saveWindowSettings(true);
        });
        
        // Save immediately on page hide (when window loses focus)
        window.addEventListener('pagehide', () => {
            saveWindowSettings(true);
        });
        
        console.log('Alert window positioning setup complete');
    }
    
    // Resize window to accommodate snooze options
    function resizeWindowForSnoozeOptions(showingSnoozeOptions) {
        chrome.windows.getCurrent((window) => {
            const baseHeight = 280; // Base height without snooze options
            const snoozeOptionsHeight = 90; // Additional height needed for snooze options
            
            let newHeight = baseHeight;
            if (showingSnoozeOptions) {
                newHeight = baseHeight + snoozeOptionsHeight;
            }
            
            // Update window height while maintaining position
            chrome.windows.update(window.id, {
                height: newHeight,
                width: Math.max(window.width, 400) // Ensure minimum width
            });
        });
    }
    
    // Load and sync position data on startup
    loadAndSyncPosition();
    
    // Setup window positioning for persistent location  
    setupWindowPositioning();
    
})();