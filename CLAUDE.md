# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with this Chrome Extension (Manifest V3) codebase.

# 🎯 CORE PHILOSOPHY

## PRIMARY DIRECTIVE: CLEAN, MINIMALISTIC CODE
- **ALWAYS make code as simple and elegant as possible** - This overrides ALL other considerations
- **REUSE existing code patterns** - Never write new code when existing code can be adapted
- **Minimum viable changes** - Make changes with as few lines of code as possible
- **Preserve functioning code** - NEVER modify working code unless explicitly requested
- **No overengineering** - Avoid complex solutions when simple ones exist

## CODE REUSE PRINCIPLES
1. **Study existing implementations** using serena MCP before writing anything new
2. **Copy and adapt** existing patterns rather than creating new ones
3. **Extend existing functions** instead of writing duplicate functionality
4. **Reuse existing CSS classes** and styling patterns
5. **Follow established naming conventions** and file organization

# 🔬 MANDATORY RESEARCH PROCESS

## PRE-IMPLEMENTATION RESEARCH (CRITICAL)
**Before starting ANY new feature, you MUST complete this research process:**

### 1. Feasibility Analysis
- **Browser limitations** - Verify feature is possible in Manifest V3
- **Google platform constraints** - Check if target site allows the functionality
- **Extension API availability** - Confirm required Chrome APIs are accessible
- **Security implications** - Ensure no potential security risks
- **Performance impact** - Assess resource usage and performance effects

### 2. Codebase Analysis (Use serena MCP)
- **Search for existing similar features** in the codebase
- **Identify reusable code patterns** and functions
- **Check memory bank documentation** for established patterns
- **Review related Quick Actions** for integration patterns
- **Analyze existing DOM manipulation** techniques

### 3. Technology Research (Use context7 MCP)
- **Latest documentation** for relevant technologies
- **Current best practices** for the specific feature type
- **Known limitations** and workarounds
- **Updated API specifications** and compatibility

### 4. Implementation Planning
- **Break down into minimal steps** using existing code where possible
- **Identify integration points** with existing systems
- **Plan cleanup and rollback** procedures
- **Define success criteria** and testing approach

## PARALLEL RESEARCH METHODOLOGY
- **Always use parallel subtasks** for research activities
- **Multiple concurrent searches** using serena and context7
- **Parallel documentation review** across multiple sources
- **Concurrent feasibility checks** across different aspects

# 🛠️ MCP TOOL USAGE

## serena MCP (Codebase Analysis)
**Use serena for ALL codebase-related research:**

```
1. find_symbol - Locate existing functions and classes
2. search_for_pattern - Find similar implementations
3. get_symbols_overview - Understand file structure
4. find_referencing_symbols - Check usage patterns
5. read_file - Study existing implementations (use sparingly)
```

**Always start with symbolic tools before reading entire files!**

## context7 MCP (Technical Documentation)
**Use context7 for technology research:**
- Latest Chrome Extension API documentation
- Manifest V3 specifications and limitations
- Google platform integration guidelines
- Security best practices and requirements
- Performance optimization techniques

# 🚨 CRITICAL PROTECTION RULES

## ABSOLUTE DATA EXTRACTOR PROTECTION
**NEVER EVER modify these files under ANY circumstances:**
- `js/multiple-listings.js` - Google Maps extraction
- `js/single-review-scraper.js` - Review scraping
- `js/prolist-extraction.js` - Local Services extraction
- `js/google-search-extractor.js` - Search extraction  
- `js/review-analysis.js` - Review analysis
- `js/business-review-scraper.js` - Business reviews
- `js/prolist-review-scraper.js` - ProList reviews

**These are mission-critical business modules. ANY changes can break core functionality.**

## DESIGN STANDARDS (NON-NEGOTIABLE)
- **NO EMOJIS** anywhere in the extension (buttons, notifications, titles)
- **Purple brand color**: #7C3AED for all UI elements
- **Dark theme**: Background #0a0a0a, text #d1d5db
- **Universal popup styling**: Follow exact patterns in `memory-bank/systemPatterns.md`
- **ESC key support**: All popups must implement ESC key closing

# 🧹 PROJECT CLEANUP PROCEDURES

## When Abandoning Partial Implementation
1. **Remove all DOM elements** created by the feature
2. **Clear all global variables** and window properties
3. **Remove event listeners** that were added
4. **Clear intervals and timeouts** 
5. **Remove CSS classes** and styling added
6. **Delete unused files** and remove from manifest.json
7. **Clean up settings** - remove toggles and configuration
8. **Update DOM cleanup utilities** - remove selectors and globals
9. **Remove Quick Action integration** if applicable
10. **Document cleanup** in commit message for future reference

## DOM Cleanup Pattern
```javascript
function resetFeatureName() {
  // Remove DOM elements
  document.querySelectorAll('.feature-selector').forEach(el => el.remove());
  
  // Clear globals
  delete window.FeatureHandler;
  delete window.featureVariable;
  
  // Remove listeners
  document.removeEventListener('click', featureClickHandler);
  
  // Clear timers
  clearInterval(featureInterval);
  clearTimeout(featureTimeout);
}
```

# 📋 ESSENTIAL DEVELOPMENT PATTERNS

## Project Structure
- **Chrome Extension (Manifest V3)** - SEO Time Machines data extractor
- **Service Worker**: `js/background.js`
- **Content Scripts**: Modular URL-based injection
- **Settings System**: `settings/settings.js` centralized configuration
- **Quick Actions**: `settings/quick-actions/` individual modules

## Core Technical Patterns
```javascript
// Namespace Protection
if (window.GMBExtractorLoaded) return;
window.GMBExtractorLoaded = true;

// Message Passing
chrome.tabs.sendMessage(tabId, {action: "actionName", data: parameters});

// Settings Integration
chrome.storage.sync.get(['settingName'], (result) => {
  if (result.settingName) {
    // Execute feature
  }
});
```

## Storage Patterns
- **chrome.storage.sync** - User settings and preferences
- **chrome.storage.local** - Audio settings and temporary data
- **Match existing storage patterns** when adding new features

## Quick Action Integration
1. Add to `settings/quick-actions/` directory
2. Follow class structure: `execute()` and `reset()` methods
3. Add toggle to `settings/quick-actions-settings.html`
4. Add to `defaultQuickActionButtons` array
5. Integrate with DOM cleanup utilities

# 🔧 COMMON WORKFLOWS

## Adding New Feature
1. **Complete mandatory research process** (above)
2. **Study existing similar features** using serena
3. **Adapt existing code patterns** instead of writing new
4. **Test viability** before committing to implementation
5. **Implement with minimal code changes**
6. **Add to cleanup systems** and settings integration
7. **Document in appropriate memory bank** files

## Debugging Issues
- **Secret Developer Mode**: 8 clicks on notifications toggle in settings
- **Use existing debugging tools** in the extension
- **Check console for existing error patterns**
- **Review similar working features** for troubleshooting

# 📚 DOCUMENTATION REFERENCES

## Memory Bank Documentation
- `memory-bank/systemPatterns.md` - Complete popup and technical patterns
- `memory-bank/developer-mode.md` - Debugging system architecture
- `memory-bank/` - All implementation patterns and project memories

## Critical Command Integration
- **updateNow** or **comGen**: Refer to `docs/golden-rules-for-version-updates.md`
- **prd**: Execute `.claude/commands/prd.md` for product research

## Sound Notification
After completing tasks, run: `afplay /System/Library/Sounds/Submarine.aiff`

# ⚡ SUCCESS CRITERIA

**A successfully implemented feature:**
1. **Reuses existing code patterns** extensively
2. **Requires minimal new code** (under 50 lines ideal)
3. **Integrates seamlessly** with existing systems
4. **Follows all established conventions**
5. **Includes proper cleanup** procedures
6. **Has been thoroughly researched** for viability
7. **Preserves all existing functionality**

**Remember: Simple, elegant, reusable code that builds on existing patterns is ALWAYS better than complex new implementations.**