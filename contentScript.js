(function() {
    'use strict';

    let observer;
    let perfState = {
        nav: null,
        paints: { fcp: null, lcp: null },
        cls: 0,
        longTasks: [],
        blocking: [],
        lazy: [],
        resources: [],
        timeline: []
    };

    // Check if JavaScript should be disabled for this page
    if (sessionStorage.getItem('seo-render-js-disabled') === 'true') {
        // Disable all scripts on the page
        disableAllScripts();
    }

    // Add styles for red and green borders
    const style = document.createElement('style');
    style.id = 'seo-render-styles';
    style.textContent = `
        .green-outline { outline: 1px solid var(--seo-ssr-color, green); }
        .red-outline { outline: 1px solid var(--seo-csr-color, red); }
    `;
    document.head.appendChild(style);

    function markInitialDOM(element) {
        if (!element.hasChildNodes()) {
            return;
        }
        element.childNodes.forEach(function(child) {
            if (child.nodeType === Node.ELEMENT_NODE && !child.classList.contains('green-outline')) {
                child.classList.add('green-outline');
                markInitialDOM(child);
            }
        });
    }

    function highlightChanges(mutations) {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList' && mutation.addedNodes.length) {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE && !node.classList.contains('red-outline')) {
                        // Skip minimap elements
                        if (node.id === 'seo-render-minimap' || 
                            node.id === 'seo-render-minimap-tooltip' ||
                            node.closest('#seo-render-minimap')) {
                            return;
                        }
                        node.classList.add('red-outline');
                        markInitialDOM(node);
                    }
                });
            }
            if (mutation.type === 'attributes' && !mutation.target.classList.contains('red-outline')) {
                // Skip minimap elements
                if (mutation.target.id === 'seo-render-minimap' || 
                    mutation.target.id === 'seo-render-minimap-tooltip' ||
                    mutation.target.closest('#seo-render-minimap')) {
                    return;
                }
                mutation.target.classList.add('red-outline');
            }
        });
        setTimeout(sendCounts, 500);
    }

    function startObserving() {
        observer = new MutationObserver(highlightChanges);
        const observerConfig = {
            childList: true,
            attributes: true,
            subtree: true
        };
        markInitialDOM(document.body);
        observer.observe(document.body, observerConfig);
        setTimeout(sendCounts, 500);
        setupPerformanceObservers();
    }

    function stopObserving() {
        if (observer) {
            observer.disconnect();
        }
    }

    function analyzeElementsByCategory() {
        const categories = {
            headings: { selectors: 'h1, h2, h3, h4, h5, h6', ssr: 0, csr: 0 },
            images: { selectors: 'img, picture, figure', ssr: 0, csr: 0 },
            links: { selectors: 'a', ssr: 0, csr: 0 },
            text: { selectors: 'p, span, div, article, section', ssr: 0, csr: 0, ssrWords: 0, csrWords: 0 },
            lists: { selectors: 'ul, ol, li', ssr: 0, csr: 0 },
            forms: { selectors: 'form, input, button, textarea, select', ssr: 0, csr: 0 },
            media: { selectors: 'video, audio, iframe', ssr: 0, csr: 0 },
            tables: { selectors: 'table, tr, td, th', ssr: 0, csr: 0 }
        };
        
        // Count words in text content
        function countWords(element) {
            // Get text content but exclude child elements that might be counted separately
            let text = '';
            for (let node of element.childNodes) {
                if (node.nodeType === Node.TEXT_NODE) {
                    text += node.textContent;
                }
            }
            return text.trim().split(/\s+/).filter(word => word.length > 0).length;
        }
        
        // Analyze each category
        Object.keys(categories).forEach(category => {
            const elements = document.querySelectorAll(categories[category].selectors);
            
            elements.forEach(element => {
                // Skip minimap elements
                if (element.closest('#seo-render-minimap') || element.closest('#seo-render-minimap-tooltip')) {
                    return;
                }
                
                // Skip hidden elements
                if (element.offsetParent === null && getComputedStyle(element).display !== 'contents') {
                    return;
                }
                
                // Count CSR vs SSR
                if (element.classList.contains('red-outline')) {
                    categories[category].csr++;
                    
                    // Count words for text elements
                    if (category === 'text') {
                        categories[category].csrWords += countWords(element);
                    }
                } else if (element.classList.contains('green-outline')) {
                    categories[category].ssr++;
                    
                    // Count words for text elements
                    if (category === 'text') {
                        categories[category].ssrWords += countWords(element);
                    }
                }
            });
        });
        
        return categories;
    }
    
    function sendCounts() {
        // Define SEO-relevant elements - content that search engines care about
        const seoRelevantSelectors = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',  // Headings
            'p', 'span', 'div',                    // Text containers
            'a',                                   // Links
            'img', 'picture', 'figure',            // Images
            'ul', 'ol', 'li',                      // Lists
            'table', 'tr', 'td', 'th',             // Tables
            'article', 'section', 'main', 'aside', // Semantic HTML5
            'nav', 'header', 'footer',             // Navigation/structure
            'blockquote', 'q', 'cite',             // Quotes
            'strong', 'em', 'b', 'i',              // Emphasis
            'form', 'input', 'button', 'label',    // Forms
            'video', 'audio', 'iframe',            // Media
            'time', 'address',                     // Semantic elements
            'dl', 'dt', 'dd',                      // Definition lists
            'pre', 'code',                         // Code blocks
            'details', 'summary'                   // Interactive elements
        ].join(', ');
        
        // Get all SEO-relevant elements
        const allRelevantElements = document.querySelectorAll(seoRelevantSelectors);
        
        // Filter out minimap elements and count
        let totalRelevant = 0;
        let redRelevant = 0;
        
        allRelevantElements.forEach(element => {
            // Skip if it's part of minimap
            if (element.closest('#seo-render-minimap') || element.closest('#seo-render-minimap-tooltip')) {
                return;
            }
            
            // Skip if it's a script-related element or hidden
            if (element.offsetParent === null && getComputedStyle(element).display !== 'contents') {
                return;
            }
            
            totalRelevant++;
            
            if (element.classList.contains('red-outline')) {
                redRelevant++;
            }
        });
        
        // Get detailed element analysis
        const elementReport = analyzeElementsByCategory();
        
        chrome.runtime.sendMessage({ 
            total: totalRelevant, 
            red: redRelevant,
            elementReport: elementReport
        });
    }
    
    // Scroll hint logic: if user hasn't scrolled a threshold, notify popup once
    let scrollNotified = false;
    const SCROLL_THRESHOLD = 600; // px
    function checkScrollHint() {
        if (!scrollNotified) {
            const scrolled = window.pageYOffset || document.documentElement.scrollTop || 0;
            if (scrolled < SCROLL_THRESHOLD) {
                chrome.runtime.sendMessage({ action: 'showScrollHint' });
            }
            scrollNotified = true;
        }
    }
    // Run after initial counts to avoid noise
    setTimeout(checkScrollHint, 1500);

    function toggleGreenOutlines(show) {
        const styleElement = document.getElementById('seo-render-styles');
        if (styleElement) {
            const outlineWidth = show ? '1px' : '0px';
            styleElement.textContent = `
                .green-outline { outline: ${outlineWidth} solid var(--seo-ssr-color, green); }
                .red-outline { outline: 1px solid var(--seo-csr-color, red); }
            `;
        }
    }

    function disableAllScripts() {
        // Disable all script tags
        const scripts = document.querySelectorAll('script');
        scripts.forEach(script => {
            if (script.src || script.innerHTML.trim()) {
                script.type = 'text/disabled';
                script.disabled = true;
            }
        });

        // Disable inline event handlers
        const elementsWithEvents = document.querySelectorAll('*[onclick], *[onload], *[onmouseover], *[onmouseout], *[onchange], *[onsubmit], *[onkeydown], *[onkeyup], *[onfocus], *[onblur]');
        elementsWithEvents.forEach(element => {
            const attributes = Array.from(element.attributes);
            attributes.forEach(attr => {
                if (attr.name.startsWith('on')) {
                    element.removeAttribute(attr.name);
                }
            });
        });

        // Prevent new scripts from being added
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.tagName === 'SCRIPT') {
                                node.type = 'text/disabled';
                                node.disabled = true;
                            }
                            // Also check for scripts within added elements
                            const scripts = node.querySelectorAll && node.querySelectorAll('script');
                            if (scripts) {
                                scripts.forEach(script => {
                                    script.type = 'text/disabled';
                                    script.disabled = true;
                                });
                            }
                        }
                    });
                }
            });
        });

        observer.observe(document.documentElement, {
            childList: true,
            subtree: true
        });
    }



    // Minimap functionality
    let minimapContainer = null;
    let minimapCanvas = null;
    let minimapCtx = null;
    let minimapOpen = false;
    let elementMap = new Map();
    let scrollIndicator = null;
    let tooltipElement = null;
    let scrollIndicatorVisible = true;
    let elementVisibilityFilter = {
        headings: true,
        images: true,
        links: true,
        text: true,
        lists: true,
        forms: true,
        media: true,
        tables: true
    };

    function createMinimap() {
        // Add Google Material Icons stylesheet if not already present
        if (!document.querySelector('link[href*="Material+Symbols+Outlined"]')) {
            const iconLink = document.createElement('link');
            iconLink.rel = 'stylesheet';
            iconLink.href = 'https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0&icon_names=visibility,visibility_off';
            document.head.appendChild(iconLink);
        }
        
        // Create minimap container
        minimapContainer = document.createElement('div');
        minimapContainer.id = 'seo-render-minimap';
        minimapContainer.style.cssText = `
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 150px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #ccc;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 999999;
            overflow: hidden;
            transition: all 0.3s ease;
            display: none;
        `;

        // Create header
        const header = document.createElement('div');
        header.style.cssText = `
            padding: 8px;
            background: #f5f5f5;
            border-bottom: 1px solid #ddd;
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
        `;
        header.innerHTML = `
            <style>
                #seo-render-minimap .material-symbols-outlined {
                    font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 24;
                    font-size: 18px;
                }
            </style>
            <span style="font-size: 12px; font-weight: bold; color: #333;">Page Minimap</span>
            <div style="display: flex; gap: 5px;">
                <button id="minimap-toggle-indicator" style="
                    background: none;
                    border: none;
                    cursor: pointer;
                    color: #666;
                    padding: 0;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                " title="Toggle scroll indicator">
                    <span class="material-symbols-outlined" id="visibility-icon">visibility</span>
                </button>
                <button id="minimap-close" style="
                    background: none;
                    border: none;
                    font-size: 16px;
                    cursor: pointer;
                    color: #666;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">&times;</button>
            </div>
        `;

        // Create canvas
        minimapCanvas = document.createElement('canvas');
        minimapCanvas.width = 150;
        minimapCanvas.height = 300;
        minimapCanvas.style.cssText = `
            display: block;
            cursor: pointer;
        `;
        minimapCtx = minimapCanvas.getContext('2d');

        // Create canvas container for proper scroll indicator positioning
        const canvasContainer = document.createElement('div');
        canvasContainer.style.cssText = `
            position: relative;
            overflow: hidden;
            height: 300px;
        `;
        
        // Create scroll indicator
        scrollIndicator = document.createElement('div');
        scrollIndicator.style.cssText = `
            position: absolute;
            left: 0;
            right: 0;
            background: rgba(0, 0, 255, 0.2);
            border: 1px solid rgba(0, 0, 255, 0.5);
            pointer-events: none;
            transition: top 0.1s ease, height 0.1s ease;
        `;

        // Create tooltip
        tooltipElement = document.createElement('div');
        tooltipElement.id = 'seo-render-minimap-tooltip';
        tooltipElement.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            pointer-events: none;
            display: none;
            white-space: nowrap;
            z-index: 1000000;
        `;
        document.body.appendChild(tooltipElement);

        // Create legend
        const legend = document.createElement('div');
        legend.style.cssText = `
            padding: 8px;
            background: #fafafa;
            border-top: 1px solid #ddd;
            font-size: 11px;
        `;
        legend.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
                <div style="width: 12px; height: 12px; background: var(--seo-ssr-color, green); margin-right: 6px;"></div>
                <span style="color: #555;">SSR Elements</span>
            </div>
            <div style="display: flex; align-items: center;">
                <div style="width: 12px; height: 12px; background: var(--seo-csr-color, red); margin-right: 6px;"></div>
                <span style="color: #555;">CSR Elements</span>
            </div>
        `;

        // Assemble minimap
        canvasContainer.appendChild(minimapCanvas);
        canvasContainer.appendChild(scrollIndicator);
        
        minimapContainer.appendChild(header);
        minimapContainer.appendChild(canvasContainer);
        minimapContainer.appendChild(legend);
        document.body.appendChild(minimapContainer);

        // Add drag functionality
        let isDragging = false;
        let dragStartX, dragStartY;
        let containerStartX, containerStartY;

        header.addEventListener('mousedown', (e) => {
            if (e.target.id === 'minimap-close') return;
            isDragging = true;
            dragStartX = e.clientX;
            dragStartY = e.clientY;
            const rect = minimapContainer.getBoundingClientRect();
            containerStartX = rect.left;
            containerStartY = rect.top;
            header.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            const deltaX = e.clientX - dragStartX;
            const deltaY = e.clientY - dragStartY;
            minimapContainer.style.right = 'auto';
            minimapContainer.style.left = (containerStartX + deltaX) + 'px';
            minimapContainer.style.top = (containerStartY + deltaY) + 'px';
            minimapContainer.style.transform = 'none';
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            header.style.cursor = 'move';
        });

        // Add close functionality
        document.getElementById('minimap-close').addEventListener('click', () => {
            hideMinimap();
            chrome.runtime.sendMessage({ action: 'minimapClosed' });
        });
        
        // Add toggle indicator functionality
        document.getElementById('minimap-toggle-indicator').addEventListener('click', () => {
            scrollIndicatorVisible = !scrollIndicatorVisible;
            scrollIndicator.style.display = scrollIndicatorVisible ? 'block' : 'none';
            const visibilityIcon = document.getElementById('visibility-icon');
            const toggleBtn = document.getElementById('minimap-toggle-indicator');
            
            if (scrollIndicatorVisible) {
                visibilityIcon.textContent = 'visibility';
                toggleBtn.style.opacity = '1';
            } else {
                visibilityIcon.textContent = 'visibility_off';
                toggleBtn.style.opacity = '0.5';
            }
        });

        // Add click to navigate functionality
        minimapCanvas.addEventListener('click', (e) => {
            const rect = minimapCanvas.getBoundingClientRect();
            const y = e.clientY - rect.top;
            const scrollRatio = y / minimapCanvas.height;
            const targetScroll = scrollRatio * document.documentElement.scrollHeight;
            window.scrollTo({
                top: targetScroll,
                behavior: 'smooth'
            });
        });

        // Add hover functionality
        minimapCanvas.addEventListener('mousemove', (e) => {
            const rect = minimapCanvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // Find element at this position
            let hoveredElement = null;
            elementMap.forEach((data, element) => {
                if (x >= data.x && x <= data.x + data.width &&
                    y >= data.y && y <= data.y + data.height) {
                    hoveredElement = element;
                }
            });

            if (hoveredElement) {
                const elementData = elementMap.get(hoveredElement);
                tooltipElement.textContent = `${hoveredElement.tagName.toLowerCase()}${hoveredElement.id ? '#' + hoveredElement.id : ''}${hoveredElement.className ? '.' + hoveredElement.className.split(' ').join('.') : ''} - ${elementData.type}`;
                tooltipElement.style.display = 'block';
                tooltipElement.style.left = (e.pageX + 10) + 'px';
                tooltipElement.style.top = (e.pageY - 25) + 'px';
            } else {
                tooltipElement.style.display = 'none';
            }
        });

        minimapCanvas.addEventListener('mouseleave', () => {
            tooltipElement.style.display = 'none';
        });
    }

    function updateMinimap() {
        if (!minimapCanvas || !minimapCtx) return;

        const pageHeight = document.documentElement.scrollHeight;
        const viewportHeight = window.innerHeight;
        const scale = minimapCanvas.height / pageHeight;

        // Clear canvas
        minimapCtx.clearRect(0, 0, minimapCanvas.width, minimapCanvas.height);

        // Draw page background
        minimapCtx.fillStyle = '#f0f0f0';
        minimapCtx.fillRect(0, 0, minimapCanvas.width, minimapCanvas.height);

        // Clear element map
        elementMap.clear();

        // Draw SSR elements (green)
        const greenElements = document.querySelectorAll('.green-outline');
        greenElements.forEach(element => {
            drawElement(element, 'green', 'SSR', scale);
        });

        // Draw CSR elements (red)
        const redElements = document.querySelectorAll('.red-outline');
        redElements.forEach(element => {
            drawElement(element, 'red', 'CSR', scale);
        });

        // Update scroll indicator
        updateScrollIndicator(scale, viewportHeight);
    }

    function getElementCategory(element) {
        const tagName = element.tagName.toLowerCase();
        
        // Check element categories
        if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) return 'headings';
        if (['img', 'picture', 'figure'].includes(tagName)) return 'images';
        if (tagName === 'a') return 'links';
        if (['p', 'span', 'div', 'article', 'section'].includes(tagName)) return 'text';
        if (['ul', 'ol', 'li'].includes(tagName)) return 'lists';
        if (['form', 'input', 'button', 'textarea', 'select'].includes(tagName)) return 'forms';
        if (['video', 'audio', 'iframe'].includes(tagName)) return 'media';
        if (['table', 'tr', 'td', 'th'].includes(tagName)) return 'tables';
        
        return null;
    }

    function drawElement(element, color, type, scale) {
        // Check if element category is visible
        const category = getElementCategory(element);
        if (category && !elementVisibilityFilter[category]) {
            return; // Skip drawing if category is hidden
        }
        
        const rect = element.getBoundingClientRect();
        const absoluteTop = rect.top + window.pageYOffset;
        const x = (rect.left / document.documentElement.clientWidth) * minimapCanvas.width;
        const y = absoluteTop * scale;
        const width = Math.max(2, (rect.width / document.documentElement.clientWidth) * minimapCanvas.width);
        const height = Math.max(1, rect.height * scale);

        // Store element data for hover functionality
        elementMap.set(element, { x, y, width, height, type, category });

        // Choose color from CSS variables by type
        let chosenColor = color;
        try {
            const rootStyles = getComputedStyle(document.documentElement);
            if (type === 'SSR') {
                chosenColor = rootStyles.getPropertyValue('--seo-ssr-color').trim() || color || 'green';
            } else if (type === 'CSR') {
                chosenColor = rootStyles.getPropertyValue('--seo-csr-color').trim() || color || 'red';
            }
        } catch (e) {}

        // Draw element
        minimapCtx.fillStyle = chosenColor;
        minimapCtx.globalAlpha = 0.6;
        minimapCtx.fillRect(x, y, width, height);
        minimapCtx.globalAlpha = 1;
    }

    function updateScrollIndicator(scale, viewportHeight) {
        if (!scrollIndicator) return;

        const scrollTop = window.pageYOffset;
        const indicatorTop = scrollTop * scale;
        const indicatorHeight = viewportHeight * scale;
        
        // Ensure indicator stays within canvas bounds
        const maxTop = minimapCanvas.height - indicatorHeight;
        const clampedTop = Math.max(0, Math.min(indicatorTop, maxTop));

        scrollIndicator.style.top = clampedTop + 'px';
        scrollIndicator.style.height = Math.min(indicatorHeight, minimapCanvas.height) + 'px';
    }

    function showMinimap() {
        if (!minimapContainer) {
            createMinimap();
        }
        minimapContainer.style.display = 'block';
        minimapOpen = true;
        updateMinimap();
    }

    function hideMinimap() {
        if (minimapContainer) {
            minimapContainer.style.display = 'none';
            minimapOpen = false;
        }
    }

    // Listen for scroll events to update scroll indicator
    let scrollTimeout;
    window.addEventListener('scroll', () => {
        if (!minimapOpen) return;
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
            const scale = minimapCanvas.height / document.documentElement.scrollHeight;
            updateScrollIndicator(scale, window.innerHeight);
        }, 50);
    });
    
    // Listen for resize events to update minimap
    let resizeTimeout;
    window.addEventListener('resize', () => {
        if (!minimapOpen) return;
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            updateMinimap();
        }, 100);
    });

    // Update minimap when DOM changes are detected
    const minimapObserver = new MutationObserver(() => {
        if (!minimapOpen) return;
        updateMinimap();
    });

    chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
        if (request.action === "start") {
            startObserving();
            // Check if minimap should be shown
            chrome.storage.sync.get(['minimapEnabled'], (data) => {
                if (data.minimapEnabled) {
                    showMinimap();
                    minimapObserver.observe(document.body, {
                        childList: true,
                        attributes: true,
                        subtree: true,
                        attributeFilter: ['class']
                    });
                }
            });
        } else if (request.action === "stop") {
            stopObserving();
            // Hide minimap when CSR analysis is stopped
            hideMinimap();
            minimapObserver.disconnect();
        } else if (request.action === "toggleGreen") {
            toggleGreenOutlines(request.showGreen);
        } else if (request.action === 'toggleMinimap') {
            // Check if CSR analysis is enabled before showing minimap
            chrome.storage.sync.get(['enabled'], (data) => {
                if (data.enabled && request.show) {
                    showMinimap();
                    minimapObserver.observe(document.body, {
                        childList: true,
                        attributes: true,
                        subtree: true,
                        attributeFilter: ['class']
                    });
                } else {
                    hideMinimap();
                    minimapObserver.disconnect();
                }
            });
        } else if (request.action === 'updateMinimap' && minimapOpen) {
            updateMinimap();
        } else if (request.action === 'applyPalette' && request.palette) {
            try {
                document.documentElement.style.setProperty('--seo-csr-color', request.palette.csr);
                document.documentElement.style.setProperty('--seo-ssr-color', request.palette.ssr);
                // Force minimap redraw if open
                if (minimapOpen) updateMinimap();
            } catch (e) {}
        } else if (request.action === 'updateMinimapFilter') {
            // Update element visibility filter
            if (request.elementVisibility) {
                elementVisibilityFilter = { ...elementVisibilityFilter, ...request.elementVisibility };
                // Redraw minimap with new filter
                if (minimapOpen) {
                    updateMinimap();
                }
            }
        } else if (request.action === 'captureMinimapForReport') {
            // Capture minimap for report if it's open
            if (minimapOpen && minimapCanvas) {
                try {
                    const dataUrl = minimapCanvas.toDataURL('image/png');
                    sendResponse(dataUrl);
                } catch (error) {
                    console.error('Error capturing minimap:', error);
                    sendResponse(null);
                }
            } else {
                sendResponse(null);
            }
            return true; // Keep message channel open for async response
        } else if (request.action === 'getPerformanceSnapshot') {
            // Return a shallow copy to avoid mutation issues
            try {
                perfState.resources = getResourceSnapshot();
            } catch (e) {}
            sendResponse(JSON.parse(JSON.stringify(perfState)));
            return true;
        }
    });

    chrome.storage.sync.get(['enabled', 'hideGreen', 'minimapEnabled', 'elementVisibility'], (data) => {
        if (data.enabled) {
            startObserving();
            
            // Load element visibility preferences
            if (data.elementVisibility) {
                elementVisibilityFilter = { ...elementVisibilityFilter, ...data.elementVisibility };
            }
            
            // Initialize minimap only if both extension and minimap are enabled
            if (data.minimapEnabled) {
                showMinimap();
                minimapObserver.observe(document.body, {
                    childList: true,
                    attributes: true,
                    subtree: true,
                    attributeFilter: ['class']
                });
            }
        }
        // Set initial green outline visibility (default to show green, so hideGreen should be false)
        toggleGreenOutlines(!data.hideGreen);

        // Apply saved color palette if present
        chrome.storage.sync.get(['colorPalette'], (cfg) => {
            if (cfg && cfg.colorPalette && cfg.colorPalette.csr && cfg.colorPalette.ssr) {
                try {
                    document.documentElement.style.setProperty('--seo-csr-color', cfg.colorPalette.csr);
                    document.documentElement.style.setProperty('--seo-ssr-color', cfg.colorPalette.ssr);
                } catch (e) {}
            }
        });
    });

    function setupPerformanceObservers() {
        try {
            // Navigation timing (once)
            const navEntries = performance.getEntriesByType('navigation');
            if (navEntries && navEntries[0]) {
                const n = navEntries[0];
                perfState.nav = {
                    ttfb: Math.max(0, n.responseStart - n.requestStart),
                    domContentLoaded: n.domContentLoadedEventEnd - n.startTime,
                    load: n.loadEventEnd - n.startTime,
                    transferSize: n.transferSize || 0
                };
            }

            // FCP
            new PerformanceObserver((list) => {
                for (const e of list.getEntries()) {
                    if (e.name === 'first-contentful-paint') {
                        perfState.paints.fcp = e.startTime;
                        perfState.timeline.push({ t: e.startTime, type: 'FCP' });
                    }
                }
            }).observe({ type: 'paint', buffered: true });

            // LCP
            new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const last = entries[entries.length - 1];
                if (last) {
                    perfState.paints.lcp = last.startTime;
                    perfState.timeline.push({ t: last.startTime, type: 'LCP' });
                }
            }).observe({ type: 'largest-contentful-paint', buffered: true });

            // CLS
            let clsValue = 0;
            new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                    }
                }
                perfState.cls = clsValue;
            }).observe({ type: 'layout-shift', buffered: true });

            // Long tasks
            new PerformanceObserver((list) => {
                for (const lt of list.getEntries()) {
                    perfState.longTasks.push({ start: lt.startTime, duration: lt.duration });
                }
            }).observe({ type: 'longtask', buffered: true });

            // Resource blocking heuristics
            const head = document.querySelector('head');
            if (head) {
                head.querySelectorAll('link[rel="stylesheet"], script').forEach((el) => {
                    const tag = el.tagName.toLowerCase();
                    const isCss = tag === 'link' && el.rel === 'stylesheet' && !el.media;
                    const isSyncJs = tag === 'script' && !el.async && !el.defer;
                    if (isCss || isSyncJs) {
                        perfState.blocking.push({
                            type: isCss ? 'css' : 'script',
                            src: el.href || el.src || '(inline)',
                            hint: isCss ? 'Stylesheet may delay render' : 'Synchronous script blocks parsing'
                        });
                    }
                });
            }

            // Lazy elements visibility
            const lazyCandidates = new Set();
            const addCandidate = (el) => {
                lazyCandidates.add(el);
            };
            document.querySelectorAll('img[loading="lazy"], img[data-src], iframe[loading="lazy"], iframe[data-src]').forEach(addCandidate);
            const io = new IntersectionObserver((entries) => {
                entries.forEach((e) => {
                    if (e.isIntersecting) {
                        const t = performance.now();
                        perfState.lazy.push({ tag: e.target.tagName.toLowerCase(), src: e.target.currentSrc || e.target.src || e.target.dataset.src || '', visibleAt: t });
                        io.unobserve(e.target);
                    }
                });
            }, { root: null, rootMargin: '0px', threshold: 0.01 });
            lazyCandidates.forEach((el) => io.observe(el));
        } catch (e) {
            // swallow
        }
    }

    function getResourceSnapshot() {
        try {
            const entries = performance.getEntriesByType('resource') || [];
            const list = entries.map(e => ({
                name: e.name,
                file: (e.name || '').split('/').pop(),
                start: e.startTime,
                duration: e.duration,
                end: e.startTime + e.duration,
                type: e.initiatorType || 'other',
                transferSize: e.transferSize || 0
            }));
            // Prioritize CSS/JS/Document-like first, then by start
            const priority = { css: 0, script: 1, xmlhttprequest: 3, fetch: 3, img: 4, image: 4, font: 2, other: 5 }; 
            list.sort((a, b) => (priority[a.type] ?? 9) - (priority[b.type] ?? 9) || a.start - b.start);
            return list.slice(0, 40);
        } catch (e) {
            return [];
        }
    }

})();
