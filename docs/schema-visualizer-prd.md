# Schema Visualizer Component - Product Requirements Document

## 📋 Document Information

- **Product**: SEO Time Machines Chrome Extension - Schema Visualizer Component
- **Version**: 1.0.0
- **Date**: 2025-08-28
- **Author**: <PERSON> Assistant
- **Status**: Implementation Ready

---

## 🎯 Executive Summary

The Schema Visualizer Component transforms the existing text-based JSON-LD schema extractor into an interactive, visual node-relationship graph system. This enhancement provides SEO professionals and web developers with an intuitive way to understand structured data relationships on web pages through dynamic visualizations.

### **Key Value Propositions**
- **Visual Schema Understanding**: Transform complex JSON-LD into interactive node graphs
- **Enhanced User Experience**: Full-screen visualization with intuitive controls
- **Professional SEO Tool**: Advanced schema analysis capabilities for technical SEO
- **Seamless Integration**: Builds on existing Quick Actions architecture

---

## 🔍 Current State Analysis

### **Existing Schema Implementation** (`settings/quick-actions/schema.js`)
```javascript
// Current implementation: Text-based modal overlay
class SchemaAction {
    static execute() {
        // Extracts JSON-LD scripts from page
        // Displays in 80% viewport modal with textarea
        // Copy-to-clipboard functionality
    }
    static reset() {
        // Removes modal from DOM
    }
}
```

### **Current User Experience Issues**
1. **Raw JSON Display**: Difficult to understand schema relationships
2. **Limited Analysis**: No visual connection mapping
3. **Modal Constraints**: 80% viewport limits detailed analysis
4. **No Interaction**: Static text display with no exploration capabilities

### **Current Technical Architecture**
- **Content Script Injection**: `chrome.scripting.executeScript`
- **DOM Manipulation**: Modal overlay with CSS-in-JS styling
- **Quick Actions Integration**: Button in popup, settings toggle
- **Brand Styling**: Purple #7C3AED gradient, dark theme

---

## 📊 Product Requirements

### **1. Core Visualization Engine**

#### **Primary Features**
- **Interactive Node Graph**: D3.js-style force-directed layout
- **Schema Relationships**: Visual connections between schema objects
- **Multiple Data Types**: Support for all JSON-LD schema types
- **Real-time Updates**: Dynamic visualization as data changes

#### **Visualization Controls**
- **Link Distance Slider**: Adjust node spacing (10px - 300px range)
- **Base Node Size Slider**: Control node diameter (8px - 50px range)
- **Charge Strength Slider**: Modify force simulation strength
- **Label Font Size Slider**: Adjust text readability (8px - 16px range)
- **Depth Control Slider**: Limit relationship depth display

#### **Interaction Features**
- **Node Selection**: Click to highlight and inspect individual schemas
- **Drag & Drop**: Repositioning of nodes for custom layouts
- **Zoom & Pan**: Mouse wheel zoom, click-drag canvas navigation
- **Hover Effects**: Node information tooltips on mouse over

### **2. User Interface Design**

#### **Layout Architecture**
```
┌─────────────────────────────────────────────────────┐
│ HEADER: Schema Visualizer | Controls Menu | Actions │
├─────────────────┬───────────────────────────────────┤
│ CONTROLS PANEL  │                                   │
│                 │                                   │
│ • Link Distance │          VISUALIZATION            │
│ • Node Size     │            CANVAS                │  
│ • Charge        │                                   │
│ • Font Size     │        (Node Graph Area)         │
│ • Depth         │                                   │
│                 │                                   │
│ [Export PNG]    │                                   │
│ [Export SVG]    │                                   │
│ [Raw JSON]      │                                   │
└─────────────────┴───────────────────────────────────┘
```

#### **Color Scheme & Branding**
- **Primary Background**: `#0a0a0a` (Extension standard)
- **Secondary Background**: `#1a1a1a` (Panel backgrounds)  
- **Text Primary**: `#d1d5db` (Light gray for readability)
- **Accent Purple**: `#7C3AED` (Brand color for nodes and controls)
- **Border Colors**: `#2a2a2a` (Subtle separation)
- **Success Green**: `#22c55e` (Action confirmations)

#### **Typography System**
- **Headers**: 18px, font-weight: 700, letter-spacing: -0.5px
- **Body Text**: 14px, line-height: 1.4
- **Labels**: 13px, font-weight: 600
- **Monospace**: 13px, font-family: 'SF Mono', Monaco, monospace
- **Font Stack**: `-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto'`

### **3. Technical Specifications**

#### **New File Architecture**
```
schema-visualizer.html           // Main visualization page
js/schema-visualizer.js         // Core visualization engine  
css/schema-visualizer.css       // Styling and animations
settings/quick-actions/schema.js // Enhanced launcher (existing file)
```

#### **Core Technologies**
- **Visualization**: Lightweight force-directed graph library (no external CDN)
- **DOM Manipulation**: Native JavaScript (following extension patterns)
- **Canvas/SVG**: Canvas for performance, SVG for export capabilities
- **Chrome APIs**: `chrome.tabs.create()` for new tab, `chrome.storage` for settings

#### **Performance Requirements**
- **Large Schemas**: Handle 100+ nodes with smooth interactions
- **Memory Usage**: < 50MB heap for typical schemas
- **Load Time**: < 2 seconds from button click to visualization
- **Export Speed**: < 5 seconds for PNG/SVG generation

### **4. Integration Requirements**

#### **Quick Actions Enhancement**
```javascript
// Enhanced Schema Button Integration
const buttonConfigs = [
    { 
        id: 'schemaBtn', 
        setting: 'schemaEnabled',
        modes: ['text', 'visual'], // New dual-mode support
        icon: 'schema-visualizer-icon' // Enhanced icon
    }
];
```

#### **Settings Integration**
```html
<!-- New Settings Toggle -->
<div class="settings-item">
    <div class="settings-item__info">
        <h4>Schema Visualizer</h4>
        <p>Interactive visual representation of structured data relationships</p>
    </div>
    <div class="settings-item__control">
        <div class="toggle-switch" data-setting="schemaVisualizerEnabled"></div>
    </div>
</div>
```

#### **Manifest.json Updates**
```json
{
  "web_accessible_resources": [{
    "resources": [
      "schema-visualizer.html",
      "js/schema-visualizer.js",
      "css/schema-visualizer.css"
    ],
    "matches": ["<all_urls>"]
  }]
}
```

---

## 🛠️ Implementation Roadmap

### **Phase 1: Core Infrastructure (Days 1-2)**
#### **Tasks**
1. ✅ Create `schema-visualizer.html` following `screenshot-editor.html` pattern
2. ✅ Implement basic page layout and dark theme styling  
3. ✅ Set up Canvas/SVG rendering foundation
4. ✅ Create data extraction and parsing pipeline

### **Phase 2: Visualization Engine (Days 3-4)**
#### **Tasks**
5. ✅ Implement node-graph force simulation
6. ✅ Create interactive node selection and highlighting
7. ✅ Add zoom and pan capabilities
8. ✅ Implement drag-and-drop node positioning

### **Phase 3: Controls & Interaction (Days 5-6)**
#### **Tasks**
9. ✅ Build controls panel with sliders
10. ✅ Implement real-time visualization updates
11. ✅ Add hover tooltips and node information
12. ✅ Create export functionality (PNG/SVG)

### **Phase 4: Integration & Polish (Days 7-8)**
#### **Tasks**
13. ✅ Enhance existing `schema.js` to launch visualizer
14. ✅ Update popup integration and settings
15. ✅ Add error handling and edge case management
16. ✅ Implement comprehensive testing suite

---

## ✅ Implementation Checklist

### **📁 File Creation & Setup**
- [ ] **HIGH**: Create `/schema-visualizer.html` with header, canvas, and controls layout
- [ ] **HIGH**: Create `/js/schema-visualizer.js` with core visualization class
- [ ] **HIGH**: Create `/css/schema-visualizer.css` with dark theme styling
- [ ] **MEDIUM**: Update `manifest.json` web_accessible_resources array
- [ ] **MEDIUM**: Research and integrate lightweight graph visualization library

### **🎨 UI/UX Implementation**  
- [ ] **HIGH**: Implement responsive layout with controls panel and canvas
- [ ] **HIGH**: Apply consistent dark theme styling (#0a0a0a, #7C3AED, #d1d5db)
- [ ] **MEDIUM**: Create interactive slider controls (5 sliders total)
- [ ] **MEDIUM**: Add hover effects and visual feedback systems
- [ ] **LOW**: Implement smooth animations and transitions

### **🔧 Visualization Engine**
- [ ] **HIGH**: Build JSON-LD data parsing and node extraction
- [ ] **HIGH**: Implement force-directed graph layout algorithm
- [ ] **HIGH**: Create node rendering with labels and connections
- [ ] **MEDIUM**: Add interactive node selection and highlighting
- [ ] **MEDIUM**: Implement zoom and pan canvas controls
- [ ] **LOW**: Add drag-and-drop node positioning

### **⚙️ Controls & Features**
- [ ] **MEDIUM**: Link Distance slider (10px-300px range)
- [ ] **MEDIUM**: Base Node Size slider (8px-50px range) 
- [ ] **MEDIUM**: Charge Strength slider for force simulation
- [ ] **MEDIUM**: Label Font Size slider (8px-16px range)
- [ ] **MEDIUM**: Depth Control slider for relationship limiting
- [ ] **LOW**: Export PNG functionality
- [ ] **LOW**: Export SVG functionality
- [ ] **LOW**: Raw JSON data view toggle

### **🔗 Integration & Settings**
- [ ] **HIGH**: Enhance `settings/quick-actions/schema.js` to support dual modes
- [ ] **HIGH**: Add new tab creation with `chrome.tabs.create()`
- [ ] **MEDIUM**: Update popup.html button with enhanced icon/tooltip
- [ ] **MEDIUM**: Add settings toggle in `quick-actions-settings.html`
- [ ] **MEDIUM**: Integrate with existing Quick Actions shortcuts system
- [ ] **LOW**: Add settings persistence for visualization preferences

### **🧪 Testing & Quality Assurance**
- [ ] **HIGH**: Test JSON-LD extraction from various website types
- [ ] **HIGH**: Verify visualization accuracy for different schema types
- [ ] **MEDIUM**: Performance testing with large schema datasets (100+ nodes)
- [ ] **MEDIUM**: Cross-browser compatibility testing
- [ ] **MEDIUM**: Memory usage optimization and leak prevention
- [ ] **LOW**: Accessibility compliance testing
- [ ] **LOW**: Error handling for malformed JSON-LD data

### **📋 Documentation & Cleanup**
- [ ] **MEDIUM**: Update CLAUDE.md with new component patterns
- [ ] **MEDIUM**: Create inline code documentation
- [ ] **LOW**: Add component to cleanup procedures
- [ ] **LOW**: Update extension version and changelog

---

## 📊 Success Metrics

### **User Experience Goals**
- **Visualization Speed**: < 2 seconds from JSON-LD to rendered graph
- **Interaction Smoothness**: 60fps during zoom/pan/drag operations  
- **Learning Curve**: Users understand schema relationships within 30 seconds
- **Feature Discovery**: 90% of users find and use at least 3 control options

### **Technical Performance**
- **Memory Usage**: < 50MB for typical schemas (10-50 nodes)
- **Large Schema Support**: Smooth handling of 100+ node schemas
- **Export Quality**: High-resolution PNG (300dpi) and scalable SVG
- **Integration Stability**: No conflicts with existing Quick Actions

### **Business Impact**
- **Feature Adoption**: 75% of schema button users try visualizer mode
- **User Retention**: Visualizer becomes primary schema analysis tool
- **Professional Value**: Enhanced technical SEO analysis capabilities
- **Extension Differentiation**: Unique feature in Chrome extension marketplace

---

## 🚨 Risk Assessment & Mitigation

### **Technical Risks**
| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| Performance issues with large schemas | HIGH | MEDIUM | Implement node clustering and progressive loading |
| Browser compatibility problems | MEDIUM | LOW | Use modern web standards with fallbacks |
| Memory leaks in visualization | HIGH | MEDIUM | Comprehensive cleanup procedures and testing |
| Integration conflicts | MEDIUM | LOW | Thorough testing with existing Quick Actions |

### **User Experience Risks**
| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| Learning curve too steep | MEDIUM | MEDIUM | Intuitive controls with helpful tooltips |
| Overwhelming for simple schemas | LOW | HIGH | Auto-adjust complexity based on data size |
| Export functionality issues | LOW | LOW | Fallback to raw JSON data copy |

---

## 🔮 Future Enhancement Opportunities

### **Advanced Features (Version 2.0)**
- **Schema Type Filtering**: Show/hide specific schema types
- **Relationship Analysis**: Highlight missing or broken schema connections
- **Validation Integration**: Visual indicators for schema validation errors
- **Custom Layouts**: Save and load preferred visualization layouts

### **Professional Tools (Version 3.0)**
- **Batch Analysis**: Compare schemas across multiple pages
- **Competitive Analysis**: Side-by-side schema comparison
- **Export Templates**: Pre-configured layouts for reporting
- **API Integration**: Connect with schema validation services

---

## 📝 Implementation Notes

### **Code Reuse Strategy**
Following CLAUDE.md guidelines for maximum code reuse:
- **Screenshot Editor Pattern**: Reuse HTML page structure and styling
- **Schema Action Pattern**: Enhance existing class instead of replacing
- **Dark Theme Consistency**: Reuse established color variables and styling
- **Quick Actions Integration**: Follow existing button and settings patterns

### **Minimal Code Approach**
- **Estimated New Code**: ~300 lines total across all files
- **Existing Code Reused**: 80% of UI patterns from screenshot-editor.html
- **Library Dependencies**: 1 lightweight graph library (~15kb compressed)
- **Performance Impact**: Minimal - only loads when visualizer is activated

### **Security Considerations**
- **Content Script Safety**: Uses existing injection patterns from schema.js
- **Data Sanitization**: All JSON-LD data sanitized before visualization
- **No External CDNs**: All dependencies bundled within extension
- **Permission Minimal**: Reuses existing Chrome extension permissions

---

## ✅ Acceptance Criteria

### **Core Functionality**
- [ ] Successfully extracts and visualizes JSON-LD data from any website
- [ ] Interactive controls modify visualization in real-time
- [ ] Node selection displays detailed schema information
- [ ] Export functions generate high-quality PNG and SVG files

### **Integration Requirements**
- [ ] Seamlessly integrates with existing Quick Actions system
- [ ] Settings toggle properly enables/disables visualizer mode
- [ ] No conflicts with other extension features
- [ ] Maintains extension performance and memory usage

### **User Experience Standards**
- [ ] Consistent dark theme styling across all components
- [ ] Intuitive controls that require no external documentation
- [ ] Smooth animations and responsive interactions
- [ ] Professional-grade output suitable for client reports

---

*This PRD serves as the definitive guide for implementing the Schema Visualizer Component. All implementation decisions should align with these specifications and the existing SEO Time Machines extension architecture.*