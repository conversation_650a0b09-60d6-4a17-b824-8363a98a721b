# SEO Time Machines

A powerful Chrome extension with **77+ tools and features** for extracting comprehensive business data from Google Maps, Google Local Services, and Google Search results. Includes productivity tools, SEO analysis, web development utilities, and extensive context menu functionality. Designed for digital marketers, lead generation specialists, and business analysts who need to efficiently gather business intelligence from Google's platforms.

## 🚀 Installation

### Method 1: Developer Mode Installation
1. Download or clone this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right corner
4. Click "Load unpacked" and select the extension folder
5. The SEO Time Machines icon will appear in your Chrome toolbar

## 🎯 Unique Features

Revolutionary tools that don't exist anywhere else, designed specifically to eliminate repetitive clicks and save hours of manual work for SEO professionals and web developers:

### Advanced Review Scraper with Owner Response Filtering
**Problem**: Manually separating customer reviews from business owner responses requires reading every review individually. **Solution**: AI-powered owner response detection with multi-layered filtering automatically excludes business replies, preserving only genuine customer feedback. Includes intelligent scrolling that triggers Google's lazy loading and range limiting (1-99,999 reviews). This tool is able to scrape 1,000 reviews in under 3 minutes, saving you hours of manual work.

### Cross-Platform Business Intelligence Extraction
**Problem**: Gathering business data from Google Maps, Local Services, and Search requires visiting multiple platforms and manual copying. **Solution**: Unified extraction system that pulls complete NAP data, reviews, coordinates, and Place IDs across Google's ecosystem with one click. Includes citation finding, competitor analysis, and bulk processing. Saves 2-3 minutes per business and eliminates manual data entry errors.

### YouTube Schema Generation with API Integration
**Problem**: Creating structured data markup for YouTube videos requires manual metadata collection and JSON-LD formatting. **Solution**: Automated schema generation using YouTube Data API v3 that extracts complete video metadata (duration, views, likes, descriptions) and produces ready-to-implement VideoObject markup. Batch processes hundreds of videos with CSV export. Saves 5+ minutes per video and ensures Schema.org compliance. Writing schema for 20 videos takes less than 5 seconds and saves you over 100 minutes of manual work.

### Gmail Email Pinner with Thread Navigation
**Problem**: Important emails get buried in busy Gmail threads, requiring endless scrolling to find critical communications. **Solution**: Individual email pinning system with intelligent age warnings and thread-aware jump links. Pin specific emails within long conversations, get visual warnings after 2+ days, and navigate directly to pinned messages with one click. Saves 30+ seconds per email retrieval and prevents forgotten follow-ups.

### Universal Text Transformers with Smart Detection  
**Problem**: Copying text between tools for formatting, case changes, or cleanup wastes time and breaks workflow. **Solution**: One-click text transformation system that automatically detects active editors (WordPress, TinyMCE, standard inputs) and applies 15+ transformations instantly. Works across any website with custom keyboard shortcuts. Eliminates 5-10 clicks per text operation and removes need for external tools.

### Enhanced Gmail Tools with Mass Management
**Problem**: Managing hundreds of promotional emails and organizing complex threads consumes hours weekly. **Solution**: Bulk unsubscribe system with whitelist intelligence, thread navigation enhancements, and custom timestamp formatting. Includes thread expansion and conversation flow optimization. Saves 15+ minutes daily on email management.

### Location Spoofing with Instant Verification
**Problem**: Checking local search results from different locations requires VPNs or travel, limiting geo-targeted SEO analysis. **Solution**: Instant location override system with coordinate precision, saved favorites, and one-click verification. Includes a fast launch system context menu system.

### Completely Modular Chrome Extension Platform
**Problem**: Most extensions force you to use all features or none, creating interface clutter and performance overhead. **Solution**: Revolutionary modular architecture with 30+ independent Quick Action modules and a total of 77+ tools, each with individual enable/disable controls, self-contained cleanup systems, and granular configuration options. Create your personalized SEO toolkit with zero dependencies and complete privacy preservation. Eliminates 50+ unnecessary features from your workflow while maintaining access to needed tools.

### Tracked Domains with Smart SERP Highlighting  
**Problem**: Finding your tracked domains across Google search results, Maps, and local listings requires manual scanning and position counting. **Solution**: Intelligent domain highlighting system with multi-strategy sponsored detection, automatic SERP position adjustment, and jump navigation links. Highlights tracked domains in purple with click-to-scroll navigation and accurate positioning that accounts for sponsored results. Saves 15-20 seconds per search and eliminates manual position tracking errors.

### Copy Element with Intelligent HTML Cleaning
**Problem**: Extracting clean HTML from web pages requires viewing source, copying messy code, and manual cleanup in external tools. **Solution**: Interactive visual element selector with hover detection, content type recognition, and 9-option configurable HTML cleaning pipeline. Shift+click for cleaned HTML, Cmd+Shift+click for raw HTML, with intelligent wrapper removal and citation cleaning. Reduces 8-step manual process to single click and ensures consistently clean markup.

### Copy Replace Domain Migration Tool
**Problem**: Migrating content between dev and live sites requires manually editing dozens of URLs, breaking workflow and introducing errors. **Solution**: Keyboard shortcut (Ctrl+Shift+V) that automatically detects URLs in clipboard, replaces configured source domain with target domain while preserving paths/parameters, and pastes instantly. Perfect for schema markup migration and content deployment. Eliminates 30+ seconds of manual URL editing per migration and prevents copy-paste errors.

### Pomodoro Timer with Advanced Audio Chronometer
**Problem**: Focus timers in Chrome extensions break during browser restarts and provide basic notification-only functionality. **Solution**: Production-grade Pomodoro system with persistent ticking chronometer using Chrome's offscreen audio API, cross-window synchronization, automatic recovery after extension reloads, and configurable ticking intervals. Independent volume controls for notifications vs. chronometer with custom audio assets. Only Chrome extension with truly persistent audio that survives browser crashes and maintains timing accuracy.

### UTM Tracking Cleaner with Platform Intelligence  
**Problem**: Sharing URLs with tracking parameters exposes browsing patterns and creates ugly links, but manually cleaning breaks legitimate functionality. **Solution**: Comprehensive tracking parameter removal system with smart platform protection for Google/YouTube/Facebook, whitelist domain management, and three privacy levels (cosmetic, block & reload, redirect following). Automatic detection of WordPress admin areas and page builders prevents functionality breaks. Eliminates privacy concerns while preserving website functionality.

### New Tab Redirect Keyboard Launcher
**Problem**: Switching between frequently used tools, dashboards, or local applications requires multiple clicks through bookmarks or typing long URLs. **Solution**: Configurable global keyboard shortcut that instantly opens any URL, local file, or application with smart protocol normalization and fallback mechanisms. Background script integration ensures reliable operation with comprehensive error handling. Reduces 5-8 clicks to single keyboard combination for instant tool access.

### Gmail Thread Jump Links with Pinned Email Navigation
**Problem**: Finding important emails within long Gmail conversation threads requires endless scrolling through chronological messages. **Solution**: Thread-aware navigation system that creates jump links specifically to emails you've pinned using the Gmail Email Pinner. Appears only in individual thread views with smart positioning that adapts to Gmail layout changes and thread expander integration. Eliminates scrolling through 50+ message threads to find critical communications.

### YouTube Screenshot Tool with Professional Capture
**Problem**: Capturing specific video moments for analysis or documentation requires third-party screen capture tools and manual file management. **Solution**: Integrated screenshot system with S2C (Screenshot to Clipboard) and S2D (Screenshot to Download) buttons, 'P' key shortcuts, and intelligent filename generation using video title and timestamp. Canvas-based capture at native resolution with organized naming conventions. Saves 4-5 tool switches and ensures professional screenshot organization.

### YouTube Frames Tool with Embedded Player Interface
**Problem**: Creating professional video thumbnails or showcasing video content requires complex image editing to add player interface elements. **Solution**: Advanced framing system that captures video screenshots with embedded SVG YouTube player controls, progress bars, and interface elements. Creates professional-looking framed screenshots that appear as authentic YouTube player interfaces. Eliminates need for Photoshop or design tools for video showcase materials.

### YouTube GIF Tool with Animated WebP Detection
**Problem**: YouTube's animated thumbnails (WebP format) are hidden in standard interfaces and require technical knowledge to access and view. **Solution**: Automated animated WebP detection system with hover-triggered preview and dedicated modal viewer. Identifies and extracts animated thumbnail content with high-quality playback controls. Only tool that provides easy access to YouTube's hidden animated thumbnail assets for competitive analysis.

### YouTube Ads Skipper with Multi-Strategy Blocking
**Problem**: YouTube ad blockers break frequently due to platform changes, causing inconsistent user experience and wasted time. **Solution**: Five-strategy ad blocking system including API cancelPlayback, skip button automation, fast-forward techniques, ad slot removal, and real-time detection with throttled mutation observer. Performance-optimized detection prevents browser slowdown while maintaining 99%+ ad blocking success rate. Most reliable YouTube ad blocking available in Chrome extensions.

## 📍 Maps Functions

### Review Scraping & Analysis
- **Complete Review Extraction**: Scrapes all reviews with ratings, dates, and full text. This provides a complete dataset for in-depth competitor analysis or for a web developer to showcase reviews on a client's website.
- **Owner Response Filtering**: Automatically excludes business owner responses, saving time by providing a clean dataset of only customer reviews for analysis.
- **Review Analytics**: Offers comprehensive analysis including sentiment trends, allowing SEOs to quickly identify a business's strengths and weaknesses from customer feedback.
- **CSV Export**: Exports reviews in a structured format, making it easy to import into other tools for further analysis or to create reports for clients.
- **Browser Notifications**: Get notified when large scraping operations complete, allowing you to work on other tasks without having to check on the progress.
- **Review Range Utility**: Filters reviews by date ranges, which is useful for analyzing review trends over specific periods, like during a marketing campaign.

### Multiple Listings Analysis
- **Bulk Category Analysis**: Analyzes business categories across multiple listings. This allows for quick market research to understand the competitive landscape in a specific area.
- **Market Research**: Understands category distribution in specific areas, which is invaluable for identifying service gaps or over-saturated markets.
- **Competitive Intelligence**: Compares business types in target markets, providing a high-level view of the main competitors in a given industry and location.
- **Statistical Insights**: Provides percentage breakdowns and trend analysis, offering data-driven insights for strategic decisions.


### Single Business Data Extraction
- **Complete Business Profiles**: Extracts name, address, phone, website, categories, ratings, and review counts. This allows a local SEO to quickly gather all essential business information for competitor analysis or a web developer to populate a local business schema, all without manual copy-pasting.
- **Smart Phone Formatting**: Preserves original phone number formatting from Google Maps, ensuring data consistency for citations which is a key local SEO ranking factor.
- **Coordinate Extraction**: Provides latitude/longitude coordinates for mapping and analysis. This is useful for geo-targeting analysis and for developers integrating with mapping APIs.
- **Place ID & CID Numbers**: Fetches Google's unique identifiers for businesses, crucial for creating precise links to Google Business Profiles and for developers using Google's APIs.
- **Generated Links**: Creates review request links, knowledge panel URLs, and more. The review link is a direct asset for reputation management campaigns.
- **Copy NAP**: Quick Name, Address, Phone copying with one click. This instantly copies the most critical citation elements, ensuring consistency and speeding up the citation building process.
- **About Tab Attributes**: Extracts detailed business information from "About" sections, providing rich, descriptive information (e.g., "women-owned," "outdoor seating") for creating detailed business descriptions or identifying market segments.

### Location Changer
- **Right-Click Context Menu Access**: Instant access to location changer functionality through right-click context menu without opening the extension popup. Includes quick location switching, location checking, and disable options with saved location favorites appearing directly in the menu.
- **Global Location Override**: Changes your location for Google Maps searches, allowing SEOs to check local search results from anywhere in the world without a VPN.
- **Coordinate Input**: Sets precise latitude/longitude coordinates, which is ideal for hyper-local rank checking.
- **Language & Region**: Configures host language (hl) and geo location (gl) to test international SEO performance accurately.
- **Quick Location Check**: Verifies your current location setting instantly, ensuring your search results are from the desired location.
- **Multiple Saved Locations**: Stores and quickly switches between locations, saving time for SEOs who manage clients in multiple cities.

## 🔍 Google Functions

### Google Search NAP Extraction
- **Auto NAP Buttons**: "Copy NAP" buttons automatically appear next to business names in search results. This saves time by providing immediate access to a business's contact information directly from the SERP.
- **Instant Copy**: Click buttons to copy Name, Address, and Phone in a clean, formatted text. This ensures data accuracy for citation building, a crucial factor in local SEO.
- **Multi-Business Support**: NAP buttons are available for all businesses in the search results, allowing for rapid data collection for lead generation or competitor analysis without visiting each website.
- **Dynamic Detection**: The tool auto-detects and adds buttons as search results load, seamlessly integrating with Google's infinite scroll.
- **No Page Navigation**: Extract data without leaving the search results page, dramatically speeding up the process of gathering local business data.

### Google Local Services (Pro List) Tools
- **Service Extraction**: Automatically extracts the services offered by businesses in the Pro List. This is a fast way to understand the core offerings of competitors in the local services space.
- **Service Analysis**: Identifies the most common services and categories, providing market insights for businesses looking to expand their service offerings.
- **Duplicate Removal**: Smart deduplication with proper capitalization ensures a clean and professional list of services, ready for reports or analysis.
- **Cross-Business Comparison**: Compares services across multiple providers, offering a clear view of the competitive landscape for specific services.
- **Pro List Review Scraping**: Extracts reviews from Local Services listings, providing another source of customer feedback for reputation management and competitor analysis.
- **Business Card Navigation**: Automatically navigates through business listings, automating the data collection process and saving significant time.

### Citation Finder
- **Find Citations**: Searches for business citations across the web. This automates the tedious process of finding where a business is listed online.
- **Citation Analysis**: Analyzes citation consistency and NAP data, which is critical for identifying and correcting inconsistencies that can harm local search rankings.
- **Bulk Citation Search**: Searches multiple businesses for citations, allowing for efficient competitor backlink and citation profile analysis.

### SERP Enhancement Tools
- **Search Result Stats**: Displays search result counts and statistics, providing a quick overview of the competitiveness of a keyword.
- **SERP Numbering**: Adds numbers to search results for easy reference, which is useful for tracking rankings and discussing specific results with clients.
- **Sponsored Highlighter**: Highlights sponsored results with custom colors, making it easy to distinguish paid from organic results at a glance.
- **Result Tracking**: Tracks and monitors specific search results, helping SEOs to keep an eye on key competitors or their own rankings over time.
- **Tracker Detection**: Advanced website tracking analysis (`trackerDetectionEnabled`) that identifies and analyzes tracking scripts, pixels, and analytics implementations for privacy auditing and competitive intelligence.
- **SEO Testing Suite**: Comprehensive SEO testing functionality (`seoTestsEnabled`) with automated checks for technical SEO elements, page optimization, and search engine compliance.
- **Top URLs Copier**: Quick extraction tool (`topUrlsCopierEnabled`) that copies the top search result URLs to clipboard for rapid competitive analysis and URL collection.

## 📖 Minimal Reader System

Transform any webpage into a clean, distraction-free reading experience for content analysis and research:

### Advanced Content Extraction
- **Universal Content Transformation**: Intelligently extracts main article content from any webpage using advanced readability algorithms, perfect for analyzing competitor content without visual distractions
- **Smart Content Scoring**: Analyzes DOM elements using text length, paragraph count, and semantic HTML indicators to identify the most valuable content sections
- **Cross-Site Compatibility**: Works on all websites with intelligent blacklist system for video platforms, ensuring consistent reading experience across diverse content sources

### Professional Reading Modes
- **Focus Mode**: Adds visual concentration aids with semi-transparent borders to content blocks, helping SEOs maintain focus during long content analysis sessions
- **Stop Word Fading**: Fades 55+ common English stop words to 40% opacity, enhancing reading speed for quick content scanning and keyword identification
- **Speed Reading Enhancement**: Advanced speed reading mode (`minimalReaderSpeedReadingEnabled`) with customizable reading pace, visual guides, and comprehension aids for rapid content analysis
- **Focus Ruler**: Customizable horizontal reading guide with color picker, height adjustment (1-100px), and opacity controls to help track reading position during detailed content analysis

### Theme and Typography Control
- **Professional Themes**: White, Sepia, and Dark themes optimized for different lighting conditions and extended reading sessions
- **Typography Customization**: Font size control (12-32px) with keyboard shortcuts (Ctrl/Cmd + Plus/Minus), font weight toggle, and optimized font families for readability
- **Reading Metrics**: Automatic reading time calculation at 200 words per minute, providing quick content length assessment for analysis planning

### Document Navigation
- **Auto-Generated Outline**: Creates clickable navigation from H1-H6 headings with visual hierarchy and smooth scrolling, ideal for analyzing content structure and heading optimization
- **Meta Information Display**: Shows reading time, URL, author, and publication date when available, providing context for content analysis
- **Floating Controls**: User-positionable control button (4 corner options) with visual state feedback and settings popup

**Value for SEO Professionals**: Analyze competitor content structure, identify heading hierarchy issues, assess content depth, and research topics without distraction. Perfect for content gap analysis and competitive research.

**Value for Web Developers**: Test content readability, validate heading structure, analyze font rendering, and ensure responsive typography across different devices and themes.

## ⏱️ Quick Timer System

Standalone countdown timer for ad-hoc timing needs with instant setup and flexible duration options:

### Flexible Timer Presets
- **Enhanced Quick Access Buttons**: Instant timer setup with expanded preset options including 5, 10, 15, 30 minutes along with existing 10 seconds, 30 seconds, 1 minute, and 1 hour presets
- **Immediate Start Capability**: Timer presets now feature immediate start functionality for streamlined workflow
- **Custom Duration Input**: Natural language parsing supports formats like "1min", "30sec", "2.5hrs", "45minutes" for any timing requirement
- **Add Time Feature**: Plus (+) buttons on each preset allow extending active timers without restarting
- **Visual Integration**: Timer appears in popup header as "HH:MM:SS" format when active with click-to-stop functionality

### Intelligent Timer Management
- **Priority Badge System**: Orange badge display (#f97316) takes highest priority over Pomodoro timer in extension icon
- **Real-time Countdown**: Live badge updates showing time remaining in "Xh", "Xm", or "Xs" format
- **Auto-collapse Interface**: Timer accordion automatically closes when timer starts to maintain clean UI
- **Persistent State**: Timer state survives browser restarts and syncs across all extension instances

### Professional Notification System
- **Chrome Alarm Integration**: Uses Chrome's alarm API for reliable completion notifications even when browser is closed
- **Audio Alerts**: Basic notification bell sound for timer completion with volume respect for user preferences
- **Browser Notifications**: Fallback notification system with proper permission handling
- **Snooze Functionality**: Quick snooze options for extended timing needs

### Advanced Technical Features
- **Natural Language Parser**: Intelligent duration interpretation handles multiple unit formats and decimal values
- **Alarm Management**: Unique alarm naming prevents conflicts with other extension timers
- **Storage Optimization**: Efficient state persistence using `chrome.storage.local` with automatic cleanup
- **Error Handling**: Visual feedback for invalid inputs with red border flash and user-friendly error messages

**Value for SEO Professionals**: Perfect for timing client calls, content review sessions, keyword research blocks, and competitive analysis tasks. Use preset buttons for standard timing needs or custom input for specific project requirements.

**Value for Web Developers**: Ideal for timing code review sessions, testing phases, deployment windows, and break reminders during long development sessions. Flexible timing supports both short debugging sessions and extended coding blocks.

## 🍅 Pomodoro Timer & Productivity System

Complete time management and focus system integrated with the SEO Time Machines for enhanced productivity during SEO work:

### Advanced Timer System
- **Complete Pomodoro Implementation**: Configurable work sessions (default 25 minutes), short breaks (5 minutes), long breaks (15 minutes), and cycle tracking (default 8 cycles)
- **State Persistence**: Timer state survives browser restarts and syncs across all extension instances, ensuring uninterrupted workflow
- **Cross-Tab Synchronization**: Timer state updates in real-time across all browser windows, providing consistent productivity tracking
- **Advanced Controls**: Start, pause, resume, stop, and reset functionality with visual feedback and progress indicators

### Advanced Audio System
- **Professional Sound Library**: 28+ AWS-hosted sounds plus 2 local audio files providing comprehensive notification options for different work environments
- **Manifest V3 Audio**: Offscreen document audio architecture ensuring reliable sound playback across all browser contexts
- **Dual Volume Control**: Separate controls for notification sounds (0-100%) and chronometer ticking with real-time adjustment and preview functionality
- **Smart Audio Preview**: Regular sounds play once for testing, ticking sounds play 3-tick sequences for optimal user experience

### Website Blocking System
- **Focus Mode Blocking**: Uses Chrome's Declarative Net Request API to block distracting websites during work sessions with priority-based rule management
- **Smart Default Lists**: Pre-configured blocked sites (social media, entertainment) and whitelisted sites (Google tools, development resources) optimized for SEO professionals
- **Custom Site Management**: Add or remove sites from blocked/whitelist with real-time updates that don't require extension reload
- **Professional Blocked Page**: Custom blocked page showing remaining timer, progress bar, and session information with automatic closure when timer completes
- **Pomodoro Blocking Integration**: Automatically enables website blocking during Pomodoro work sessions with `pomodoroBlockingEnabled` toggle for enhanced focus control

### Task Management Integration
- **Independent Task Management System**: Standalone task creation and management system that operates independently of timer functionality
- **Complete Todo System**: Create, edit, delete, and reorder tasks with completion tracking and visual feedback
- **Timer-Task Synchronization**: Tasks integrate seamlessly with timer sessions, providing context for focused work periods
- **Enhanced Task Interface**: Professional task management interface with improved organization and workflow integration
- **Persistent Storage**: Tasks saved to local storage with configurable display limits and compact mode when tasks exist
- **Productivity Analytics**: Track task completion rates and maintain focus during timed work sessions

### Chronometer and Alerts
- **Background Ticking**: Optional chronometer with seamless ticking sounds during work sessions, separate volume control, and cross-window indicators
- **Professional Alert System**: Time-based scheduling with repeat options (Once, Daily, Weekdays, Weekly) and multi-modal notifications
- **Intelligent Snooze**: Multiple snooze intervals (1-30 minutes) with automatic rescheduling and proper conflict resolution
- **Centralized Badge Management**: Color-coded extension badge (red alerts, orange timers, purple Pomodoro) with real-time count updates
- **Advanced Notification Utility**: Comprehensive notification system (`notificationUtilityEnabled`) with cross-platform compatibility, audio integration, and persistent state management for reliable alert delivery

**Value for SEO Professionals**: Maintain focused work sessions during keyword research, content audits, and competitive analysis. Use timer blocking to eliminate social media distractions during client work. Track task completion for better time management and client reporting.

**Value for Web Developers**: Structure development sessions with timed focus periods for coding, testing, and debugging. Use task management to track bug fixes and feature development. Maintain productivity during long development sessions with scheduled breaks.

## 📹 YouTube Tools Suite

Comprehensive video content management tools with advanced schema generation capabilities for SEO professionals and web developers working with YouTube content:

### Advanced Ad Control System
- **Multi-Strategy Ad Blocking**: Five different ad-skipping methods including API cancelPlayback, skip button automation, fast-forward techniques, and ad slot removal
- **Smart Ad Detection**: Monitors YouTube ad states and UI elements with throttled mutation observer for performance optimization
- **Real-time Settings**: Instant enable/disable functionality with settings integration and performance-optimized detection

### Professional Video Analysis Tools
- **Advanced Screenshot System**: Dual-button architecture with S2C (Screenshot to Clipboard) and S2D (Screenshot to Download) for optimized workflow
- **Smart File Naming**: Auto-generates descriptive filenames using video title and timestamp (e.g., "VideoTitle 5-23.png") with optimized naming conventions
- **Keyboard Shortcuts**: Press 'P' key for instant screenshots during content analysis, perfect for capturing key moments for competitive research
- **Canvas-based Capture**: High-quality frame extraction at native video resolution for professional documentation
- **Performance Optimizations**: Recent improvements include console spam reduction and enhanced memory management for smoother operation

### Thumbnail Management System
- **Multiple Quality Access**: Extract thumbnails in maxresdefault, hqdefault, sddefault, and mqdefault qualities for comprehensive thumbnail analysis
- **Interactive Viewer**: Professional popup interface with thumbnail preview, URL display, and copy-to-clipboard functionality
- **Competitive Analysis**: Click thumbnails to open in new tabs for detailed analysis of competitor thumbnail strategies
- **Batch Collection**: Combine with YouTube Embed Scraper for large-scale thumbnail research and analysis

### Universal Video Speed Control
- **Cross-Platform Speed Control**: Works on YouTube, Vimeo, and any website with video content, maintaining consistent playback speeds
- **Speed Guardian System**: Automatically restores user-set speeds when external changes occur, including after ad completion
- **Advanced Keyboard Shortcuts**: V (toggle display), S/D (speed control), Z/X (navigation), G (force speed) for rapid video analysis
- **Visual Overlay Controls**: Draggable overlay with hover controls, speed display, and purple brand theming for professional appearance

### YouTube Embed URL Scraper
- **Batch Processing**: Extract embed URLs from channel pages with configurable range selection (e.g., videos 1-10, 50-100)
- **Multiple Format Support**: Generate embed URLs, share URLs, and full URLs with structured CSV export for analysis
- **Visual Progress Tracking**: Real-time processing status with completion notifications and purple-numbered video overlays
- **Professional Data Export**: Structured CSV output with video titles, IDs, and URLs for comprehensive video content analysis
- **Automatic Schema Generation**: Integration with YouTube Data API v3 to generate JSON-LD structured data markup for videos
- **Comprehensive Video Metadata**: Extracts duration, view counts, thumbnails, and complete video information for SEO analysis

### Technical Bridge Architecture
- **Chrome Storage Bridge**: Sophisticated localStorage bridge system overcoming content script limitations for full YouTube DOM access
- **Content Security Compliance**: TrustedTypes implementation and strict separation avoiding innerHTML usage for security
- **Memory Management**: Comprehensive cleanup on disable/navigation with proper event listener removal and performance optimization

**Value for SEO Professionals**: Analyze competitor YouTube content without ads, capture screenshots for content research, extract video thumbnails for competitive analysis, and batch collect video URLs for content strategy development. Speed control enables efficient video analysis and research.

**Value for Web Developers**: Test video implementations across platforms, document video functionality with screenshots, analyze YouTube embed strategies, and ensure consistent video performance. Technical tools provide insights into video optimization and user experience.

## 🚀 Enhanced Bulk Link Opener

Professional bulk URL management system with advanced organizational features, window memory, and intelligent URL extraction capabilities:

### Advanced URL Management
- **Smart URL Processing**: Intelligent URL parsing with automatic protocol detection and validation for seamless bulk operations
- **Multiple Input Methods**: Support for direct URL input, clipboard import, and drag-and-drop functionality for flexible workflow integration
- **Enhanced URL Extraction Engine**: Powerful extraction from various text formats including HTML, Markdown, plain text, and CSV data with improved detection capabilities
- **Advanced Duplicate Detection**: Enhanced deduplication with domain-aware clustering and smart URL normalization

### Professional Window Management
- **Intelligent Window Position Memory**: Advanced window positioning system that remembers and restores optimal window locations across browser sessions for enhanced user experience
- **Enhanced Bulk Opening Strategies**: Multiple opening modes including new tabs, new windows, and incognito windows with improved performance optimization
- **Smart Tab Grouping**: Automatic tab organization by domain with color-coded grouping for improved navigation
- **Optimized Memory-Efficient Processing**: Enhanced intelligent batching prevents browser overload while maintaining rapid processing speeds

### Enhanced Saved Lists System
- **Advanced Persistent List Storage**: Save, organize, and manage multiple URL collections with enhanced descriptive naming and improved categorization system
- **Professional List Categories**: Enhanced organization with categories for different projects, clients, or research topics with better management interface
- **Improved Import/Export Functionality**: Seamless data portability with JSON and CSV export options for backup and sharing with enhanced reliability
- **Smart List Merging**: Combine multiple saved lists with intelligent duplicate handling and enhanced URL validation

### URL Extraction Features
- **Multi-Format Support**: Extract URLs from HTML code, Markdown documents, CSV files, and plain text with format recognition
- **Pattern Recognition**: Advanced regex patterns for identifying URLs in complex text structures and embedded content
- **Metadata Preservation**: Maintains associated metadata like link text, titles, and context during extraction
- **Batch Validation**: Real-time URL validation with broken link detection and status reporting

### Professional Interface
- **Dark Theme Design**: Consistent purple-branded interface matching extension's professional appearance
- **Drag-and-Drop Zones**: Visual drop zones for file upload and URL import with clear feedback indicators
- **Real-time Statistics**: Live counters showing total URLs, valid links, duplicates removed, and processing status
- **Progress Tracking**: Visual progress indicators for large batch operations with detailed completion reporting

### Performance Optimization
- **Intelligent Batching**: Smart batch processing prevents browser memory issues while maintaining speed
- **Background Processing**: Non-blocking URL validation and preparation with user notification system
- **Resource Management**: Efficient memory usage patterns with automatic cleanup and garbage collection
- **Error Recovery**: Robust error handling with retry mechanisms and detailed error reporting

**Value for SEO Professionals**: Streamline competitive analysis by bulk-opening competitor websites, organize research URLs into categorized lists, extract links from various sources for analysis, and maintain persistent collections of important URLs for ongoing projects.

**Value for Web Developers**: Efficiently test multiple URLs during development, organize testing resources into manageable lists, extract links from documentation and specifications, and maintain collections of development resources across projects.

## 🔗 YouTube Schema Generation System

Advanced schema markup generation for YouTube videos using official YouTube Data API v3 integration:

### API Configuration
- **YouTube Data API v3 Integration**: Configure your Google Cloud Console API key for automatic schema generation
- **Secure Storage**: API keys stored locally using `chrome.storage.local` for enhanced security and privacy
- **Settings Management**: Easy API key configuration through General Settings with validation and status indicators
- **Graceful Fallback**: YouTube Embed Scraper functions normally without API key but schema generation requires authentication

### Schema Markup Features
- **JSON-LD Generation**: Automatic creation of structured data markup compliant with Schema.org VideoObject specification
- **Complete Video Metadata**: Extracts title, description, duration, view counts, like counts, upload dates, and channel information
- **SEO-Optimized Output**: Generated schema includes all essential properties for enhanced search engine visibility
- **Multiple Format Support**: Schema data available in both raw JSON and HTML-wrapped `<script type="application/ld+json">` format
- **Batch Processing**: Generate schema markup for multiple videos simultaneously during bulk scraping operations

### Technical Implementation
- **YouTube Data API v3 Endpoints**: Uses official `videos` endpoint with `snippet`, `statistics`, and `contentDetails` parts
- **ISO 8601 Duration Parsing**: Converts YouTube's duration format to seconds and Schema.org-compliant time formats
- **Error Handling**: Comprehensive error management with fallback for videos without API data
- **Performance Optimization**: Parallel processing of schema generation during batch video extraction

### Schema Output Structure
Generated schema markup includes:
- **VideoObject Type**: Full Schema.org VideoObject implementation
- **Video Metadata**: Title, description, thumbnails (multiple resolutions), upload date, duration
- **Interaction Statistics**: View counts, like counts with proper InteractionCounter structure
- **Author Information**: Channel details with proper Person/Organization markup
- **Keywords**: Video tags converted to comma-separated keyword string
- **Embed Information**: Direct embed URLs for video integration

### Setup Instructions
1. **Google Cloud Console**: Create project and enable YouTube Data API v3
2. **API Key Creation**: Generate API key with YouTube Data API v3 permissions
3. **Extension Configuration**: Enter API key in General Settings → YouTube API Configuration
4. **Verification**: Status indicator confirms successful API key validation
5. **Usage**: Schema generation automatically activates in YouTube Embed Scraper

### CSV Export Integration
Schema markup seamlessly integrates with CSV export functionality:
- **Schema Column**: Dedicated column in exported CSV containing full JSON-LD markup
- **Ready-to-Use Format**: Schema wrapped in script tags for direct HTML implementation
- **Bulk Schema Export**: Generate schema for hundreds of videos in single operation
- **SEO Workflow Integration**: Perfect for content creators and SEO professionals managing large video libraries

**Value for SEO Professionals**: Generate schema markup for competitive analysis, create structured data for client video content, enhance video SEO with proper markup, and streamline large-scale video content optimization workflows.

**Value for Web Developers**: Implement structured data for video content, automate schema generation for video-heavy websites, ensure proper video markup compliance, and integrate with existing content management systems through CSV import functionality.

## 🔔 Alert & Notification System

Professional time management and notification system integrated with the productivity suite:

### Professional Alert Management
- **Calendar-Based Alert System**: Complete visual date picker with monthly navigation, today highlighting, and intuitive date selection for precise scheduling
- **Alert Manager Interface**: Dark-themed professional interface for creating time-based alerts with HH:MM format and real-time "time from now" indicators
- **Enhanced Alert Editing**: Alert editing capabilities with "X hours from now" scheduling functionality for precise timing adjustments
- **Advanced Alert Display**: Comprehensive alert organization with visual priority indicators and smart sorting by date proximity
- **Alert Search Functionality**: Real-time search capabilities across all alert content for quick filtering and discovery
- **Flexible Scheduling**: Once, Daily, Weekdays (Mon-Fri), and Weekly repeat options with intelligent next-occurrence calculation
- **Live Badge Integration**: Extension icon displays count of active alerts with toggle controls for individual alert management
- **Window Position Memory**: Alert Manager remembers size and position across browser sessions for consistent user experience

### Multi-Modal Notification System
- **DOM Popup Alerts**: In-page overlay notifications with bell icon, time display, and 60-second auto-dismiss countdown
- **Audio Integration**: Full STM audio system integration using Pomodoro "Work Completed" sound with user-configurable volume
- **Browser Notifications**: Fallback to Chrome's native notification system with proper permission handling
- **Notification Windows**: Dedicated notification windows for enhanced visibility during focused work sessions

### Intelligent Alert Controls
- **Advanced Snooze System**: Multiple snooze intervals (1, 2, 5, 10, 15, 30 minutes) with visual feedback and automatic Chrome alarm rescheduling
- **Smart Dismissal**: Comprehensive cleanup of DOM elements, storage, and alarms with race condition prevention for snooze vs dismiss conflicts
- **Auto-dismiss Safety**: 5-minute automatic dismissal if no user action with proper cleanup and memory management
- **Cross-Tab Synchronization**: Alert functionality works seamlessly across multiple browser windows with shared state management

### Centralized Badge System
- **Priority Management**: Quick Timer alerts take precedence over Pomodoro timer with color-coded system (red alerts, orange quick timers, purple Pomodoro)
- **Real-time Updates**: Badge text shows active alert count with performance-optimized updates to reduce console spam
- **State Coordination**: Tracks multiple timer sources without conflicts, automatically clearing badge when no active alerts remain

**Value for SEO Professionals**: Set timed reminders for client calls, content publication deadlines, and campaign monitoring. Use during long research sessions to maintain awareness of important meetings and deadlines. Integrate with Pomodoro system for comprehensive time management.

**Value for Web Developers**: Schedule reminders for deployment windows, testing deadlines, and team meetings. Use during long coding sessions to maintain awareness of project milestones and client communications. Combine with productivity system for structured development workflows.

## 🔗 New Tab Redirect Function

Configurable keyboard shortcut system for instant navigation to frequently used URLs:

### Quick URL Access
- **Customizable URL Target**: Configure any URL (websites, local files, applications) for instant access via keyboard shortcut
- **Smart URL Normalization**: Automatically adds HTTPS protocol to URLs without protocol specification for seamless navigation
- **Background Script Integration**: Uses Chrome's background script for reliable tab creation with fallback methods for maximum compatibility
- **Professional Notification System**: Visual feedback confirms successful URL opening with error handling for failed attempts

### Configuration and Setup
- **Flexible URL Configuration**: Set target URL in Extras Settings with support for web URLs, local files, and applications
- **Global Keyboard Shortcut**: Configure custom keyboard combination for instant access across all browser contexts
- **Settings Integration**: Toggle control in Extras Settings with persistent storage and cross-browser synchronization
- **Import/Export Support**: URL configuration included in settings backup and restore functionality

### Technical Implementation
- **Cross-Platform Support**: Works on all operating systems with consistent behavior across different browser environments
- **Error Recovery**: Comprehensive error handling with fallback mechanisms ensuring reliable operation
- **Chrome Extension Integration**: Seamless integration with extension's global shortcut system and notification framework
- **Debug Logging**: Detailed console logging for troubleshooting and configuration verification (developer mode)

**Value for SEO Professionals**: Instant access to frequently used SEO tools, client dashboards, analytics platforms, and research resources. Configure shortcuts to Google Search Console, Google Analytics, or competitor analysis tools for rapid workflow transitions.

**Value for Web Developers**: Quick navigation to development environments, staging servers, documentation sites, and project management tools. Set up shortcuts to localhost applications, GitHub repositories, or testing environments for streamlined development workflow.

## 🕰️ Text Time Machine Launcher Feature

Advanced launcher system for external text editing applications with seamless browser integration:

### External Application Integration
- **Local Application Launcher**: Configure path to external text editing applications for enhanced text processing capabilities
- **File Protocol Support**: Full support for file:// URLs and local file paths with intelligent path normalization
- **Cross-Platform Path Handling**: Smart detection and formatting for Windows (C:), Mac/Linux (/), and relative path formats
- **Background Script Processing**: Uses Chrome's extension background script for reliable external application launching

### Advanced Configuration System
- **Flexible Path Configuration**: Support for absolute paths, relative paths, and file:// protocol URLs
- **Automatic Path Normalization**: Intelligent conversion of various path formats to proper file:// URLs
- **Multiple Launch Methods**: Primary background script method with fallback to direct window.open for maximum compatibility
- **Persistent Settings Storage**: Configuration stored with extension settings and included in import/export functionality

### Professional Text Editing Workflow
- **Instant External Editor Access**: One-click launch of configured text editing applications via keyboard shortcut
- **Seamless Integration**: Designed to complement browser-based text transformation tools with external editing capabilities
- **Enhanced Text Processing**: Bridge between browser-based SEO tools and advanced local text editing applications
- **Workflow Optimization**: Rapid access to preferred text editors for complex content manipulation and formatting

### Technical Features
- **Smart URL Processing**: Automatic detection and conversion of file paths to proper URLs for cross-platform compatibility
- **Error Handling and Recovery**: Comprehensive error management with user feedback and fallback methods
- **Chrome Extension API Integration**: Full integration with extension's global shortcut system and notification framework
- **Debug and Logging**: Detailed console output for configuration verification and troubleshooting support

**Value for Content Creators**: Launch advanced text editors like Sublime Text, VS Code, or specialized writing applications for complex content creation and editing tasks. Perfect for SEO content creators who need powerful text processing capabilities.

**Value for Technical Writers**: Instant access to preferred Markdown editors, documentation tools, or code editors for technical content creation. Streamlines workflow between browser-based research and local content development environments.

## ⚡ Quick Actions

Available on non-Google pages for web analysis and SEO auditing:

### Content Analysis
- **Htags Highlighter**: Highlights and color-codes all heading tags (H1-H6) on the current page. This provides an instant visual overview of a page's structure, which is essential for on-page SEO analysis.
- **Heading Structure**: Analyzes and displays the page's heading structure with statistics. This helps web developers to ensure the page is using headings correctly for accessibility and SEO.
- **Show Links**: An advanced link analysis tool that highlights nofollow and dofollow links. This is a time-saver for SEOs auditing a site's internal and external linking strategy.
- **Show Hidden Elements**: Reveals hidden page elements with intelligent auto-detection (`showhiddenAutoDetectionEnabled`) that automatically identifies and highlights hidden content, useful for developers debugging layout issues or for SEOs checking for hidden text that might violate search engine guidelines.
- **Schema Markup**: Extracts and displays structured data (JSON-LD). This allows developers to quickly verify the correctness of their schema implementation and for SEOs to check competitors' structured data.
- **Page Structure**: A comprehensive page structure analysis tool. This gives a detailed overview of the DOM, which is invaluable for developers optimizing for performance and for SEOs analyzing the on-page elements.
- **Metadata**: Extracts and analyzes page metadata. This is a quick way to check for SEO basics like title tags and meta descriptions without having to dig into the source code.

### Media & Assets
- **Images**: An advanced image SEO audit tool that analyzes IMG tags, SVG elements, and CSS background images. This helps web developers identify oversized images for performance optimization and SEOs to check for missing alt text.
- **Copy Element**: Advanced element copying with hover selection and RAW HTML copy mode. This allows developers to quickly grab specific HTML snippets for reuse or debugging.
- **YouTube Embed Scraper**: Extracts YouTube video embed codes from pages. This is useful for content creators and web developers who need to quickly grab embed codes without navigating to YouTube.

### Design & Development Tools
- **Color Picker**: A professional color picker tool with real-time color sampling. This is a handy utility for web developers and designers to quickly identify colors on a page.
- **Quick Edit**: Enhanced live page editing with a Shift+Drag selection system. This allows developers to quickly make and test changes directly on the page without needing to go back to the code editor.

### Content Utilities
- **Word Counter**: Counts words and characters in selected text. This is useful for content creators and SEOs to ensure content meets length requirements or for quick analysis of text snippets.
- **Bold From SERP**: Extracts unique n-grams from all emphasized text on the page. This helps SEOs quickly identify important keywords and phrases used by competitors.
- **Links Extractor**: An interactive tool to extract and analyze links from selected page elements. This is a powerful tool for link building and competitive analysis, allowing for quick identification and export of links.
- **Responsive Device Simulator**: Tests website responsiveness across different devices. This is crucial for web developers to ensure a consistent user experience across various screen sizes and for SEOs to verify mobile-friendliness.

### UTM Parameter Management
- **UTM Builder**: Professional UTM parameter creation tool (`utmBuilderEnabled`) for generating campaign tracking URLs with source, medium, campaign, term, and content parameters for comprehensive marketing attribution
- **UTM Tracking Cleaner**: Advanced URL cleaning system (`utmTrackingCleanerEnabled`) that removes UTM parameters and tracking codes from URLs with configurable whitelist domains and multiple stripping methods for privacy protection
- **UTM Copy Clean**: One-click URL cleaning (`utmCopyCleanEnabled`) that automatically removes tracking parameters when copying URLs, ensuring clean links for sharing and analysis
- **UTM Clean and Go**: Intelligent navigation system (`utmCleanAndGoEnabled`) that automatically cleans UTM parameters before redirecting, providing seamless browsing with privacy protection

### Content Processing
- **Clean Content**: Clean selected text content by removing unwanted formatting (available via right-click context menu)

### Quick Actions Reset
- **Reset All**: Clear all quick action modifications with one button

## 📋 Context Menu Items

Right-click context menu options available on any page:

### Text Analysis (Selection Required)
- **📊 Count Words & Characters**: Analyzes selected text for word and character counts. This is useful for content optimization, ensuring text meets specific length requirements for SEO or readability.
- **🧹 Clean Content**: Cleans selected text by removing unwanted formatting and characters. This saves time for web developers and content managers by providing clean text for reuse.
- **📊 Calculate Keyword Density**: Calculates keyword density for selected text. This helps SEOs quickly assess keyword usage within specific content blocks.

### Main Toolkit Menu: ⚡ SEO Time Machines

#### 🌍 Location Controls
- **🎯 Check Location**: Verifies current location settings, ensuring accurate geo-targeted search results for SEO analysis.
- **🚫 Disable Fake Location**: Turns off location overrides, allowing for quick return to natural search results.
- **📍 Saved Locations**: Provides quick access to saved locations, streamlining the process for SEOs managing clients in various geographical areas.

#### 🔧 SEO Tools Submenu
- **🤖 Robots.txt Analyzer**: Analyzes robots.txt files and directives. This helps SEOs quickly identify potential crawling issues that could impact search engine visibility.
- **🕸️ Check XML Sitemap**: Validates and analyzes XML sitemaps structure and accessibility. This ensures that all important pages are discoverable by search engines.
- **📊 Page Keyword Density Checker**: Performs full page keyword density analysis. This allows SEOs to quickly assess keyword stuffing or under-optimization.
- **📈 MOZ DA Checker**: Checks domain authority via MOZ with SEO metrics. This provides a quick way to gauge a website's authority for competitive analysis.
- **❌ 404 Checker**: Checks for broken links and redirects on pages. This helps web developers and SEOs identify and fix issues that negatively impact user experience and search rankings.
- **⏰ Wayback Machine**: Accesses historical page versions through Internet Archive. This is useful for analyzing past website changes or recovering lost content.
- ** 📧 Email Generator**: Professional email composition tool with multiple templates (Cold Outreach, Follow-up, Introduction, Partnership) featuring customizable tone, length, and purpose for efficient business communication

#### 🛠️ DEV Tools Submenu
- **📃 Toggle Page Structure X-ray**: Visual page structure analysis with interactive hover inspection. This helps web developers quickly understand the DOM structure and identify layout issues.
- **🎨 Color Palette Extractor**: Extracts color palettes from pages for design consistency analysis. This is useful for web developers and designers to ensure brand consistency across a website.
- **🎯 Color Picker**: Professional color picker with draggable interface and real-time sampling. This allows developers to quickly identify and replicate colors used on any webpage.
- **🔤 Font Inspector**: Analyzes fonts used on the page including loading and performance metrics. This helps web developers optimize font loading for faster page speeds.
- **🖥️ Responsive Font Analysis**: Extracts and analyzes font styling information across different viewport sizes. This is crucial for ensuring consistent typography across responsive designs.
- **📄 CSS Class Inspector**: Inspects CSS classes and styling with real-time analysis. This helps web developers debug styling issues and understand how CSS is applied.
- **👓 Screen Reader Simulation**: Simulates screen reader experience for accessibility testing. This is vital for web developers to ensure their websites are accessible to all users.
- **🏷️ Highlight Semantic Elements**: Highlights semantic HTML elements with integrated tooltips for structure analysis. This helps web developers ensure proper use of HTML5 semantic tags for better SEO and accessibility.
- **🪄 Copy Element**: Advanced element copying tool with RAW HTML support and configurable cleaning options. This allows developers to quickly extract and reuse HTML snippets.

#### Direct Access Tools
- **✏️ Quick Edit**: Enhanced live page editing with advanced selection capabilities and bulk element management

## 🔧 EXTRAS

Advanced features available in Settings → Extras:

### Search Enhancement
- **Location Changer**: Enables location spoofing functionality in the main popup for geo-targeted searches. This allows SEOs to quickly check local search results from different areas without needing a VPN.
- **SERP Numbering**: Adds numbered indicators to Google search results (1, 2, 3...) for easy reference. This helps SEOs quickly identify and track ranking positions.
- **Sponsored Highlighter**: Highlights "Sponsored" text with customizable colored borders on Google pages. This allows for quick visual distinction between paid and organic results.
- **Search Result Stats**: Displays Google search result statistics (e.g., "About 1,000 results") at the top of search pages. This provides a quick overview of the competitiveness of a search query.
- **Current Location Display**: Displays the current location from Location Changer on Google search pages. This provides immediate confirmation of the active geo-targeting setting.

### Business Tools
- **Citation Hunter**: Enables "Find Citations" button to search for business mentions across the web. This automates the process of finding citation opportunities, saving significant time for local SEOs.
- **Open Single Listing**: Enables "Open Single Listing" button for quick access to clean business listing URLs in multiple listings mode. This streamlines the workflow for analyzing individual business profiles.

### Content Management
- **Auto Clean & Title on Copy Element**: Automatically cleans HTML and adds title attributes when using Shift+Click with Copy Element. This saves web developers time by providing clean, well-formatted HTML snippets.
- **Click to Copy Links**: Hold Ctrl and click any link on any webpage to instantly copy the link URL to clipboard with visual feedback. This is a quick way for SEOs and content managers to gather URLs for audits or reports.
- **Copy Replace System**: Advanced find-and-replace functionality (`copyReplaceEnabled`) with domain replacement capabilities, allowing for bulk URL modifications and content adaptation across different environments or client sites.
- **HTML Cleaning Options**: Configures which HTML cleaning operations to apply (styles, classes, IDs, comments, empty tags, extra spaces, data attributes, wrapper divs, citation numbers). This gives web developers fine-grained control over the cleanliness of copied HTML.
- **Top URLs Copier**: Adds a button to Google search pages that copies the top 5 search result URLs to clipboard. This is a rapid way for SEOs to gather competitor URLs for analysis.

### Gmail Tools
- **Show Gmail Icons**: Displays sender icons and domain information next to Gmail messages. This enhances visual identification and helps in quickly recognizing senders, useful for email management.
- **Reverse Gmail Order**: Reverses the order of Gmail conversation threads to show newest messages at the top. This improves navigation for users who prefer to see the latest communication first.
- **Show Gmail Time**: Displays custom formatted timestamps in Gmail with configurable date/time formats. This allows for better email organization and quick parsing of communication timelines.
- **Mass Unsubscribe**: Bulk email unsubscribe functionality (`massUnsubscribeEnabled`) that identifies and manages subscription emails with batch unsubscribe capabilities for efficient inbox management.
- **Gmail Thread Expander**: Enhanced thread navigation (`gmailThreadExpanderEnabled`) with automatic thread expansion, improved threading visualization, and streamlined conversation management for complex email chains.

## 📌 Gmail Email Pinner

Professional email pinning system for Gmail that transforms email management by providing persistent bookmarking of important emails:

### Advanced Email Management
- **Smart Email Detection**: Automatically detects individual Gmail emails vs inbox/list views using intelligent URL pattern analysis
- **Professional Visual Pin System**: Adds elegant pin icons next to email timestamps with refined visual feedback - unpinned pins appear almost invisible (light grey with subtle opacity) while pinned emails display in purple matching extension branding
- **Enhanced Hover Effects**: Smooth purple hover transitions with brand-matched theming (#7C3AED) and subtle background highlighting for improved user interaction
- **Cross-Tab Synchronization**: Real-time pin state updates across all Gmail windows and tabs for seamless workflow
- **Gmail SPA Navigation**: Seamlessly handles Gmail's single-page application navigation with persistent pin functionality

### Professional Email Organization
- **Dedicated Management Window**: Standalone 550x650px popup window for comprehensive pinned email management
- **Intelligent Age Warnings**: Three-tier visual system prevents forgotten emails:
  - **Normal** (0-1 days): Clean interface for recent pins
  - **Warning** (2-4 days): Orange dashed border with warning indicators
  - **Critical** (5+ days): Red dashed border with urgent attention markers
- **Smart Email Sorting**: Automatically sorts by age (oldest first) to prioritize emails requiring immediate attention
- **Direct Gmail Access**: One-click links to open emails directly in Gmail with proper URL handling

### Technical Excellence
- **Comprehensive Data Extraction**: Captures email title, sender, timestamp, full URL, and unique Gmail ID for reliable tracking
- **Advanced URL Parsing**: Handles complex Gmail URL patterns including search results, labels, folders, and thread navigation
- **Memory Management**: Proper cleanup of observers, event listeners, and DOM elements preventing memory leaks
- **CSP Compliance**: Fully compliant with Content Security Policy using secure DOM manipulation
- **Error Handling**: Comprehensive validation and error recovery for reliable operation

### Integration Features
- **Extension Popup Integration**: Pin counter badge in main popup shows total pinned emails at a glance
- **Settings Integration**: Toggle control in Gmail Tools section with default enabled state
- **Storage Optimization**: Efficient local storage using `chrome.storage.local` with data validation
- **Badge Management**: Real-time counter updates in extension popup reflecting current pin status

**Value for Email Management**: Pin important emails for follow-up, track pending responses, bookmark reference materials, and maintain focus on priority communications. Perfect for managing client communications, project emails, and important announcements without losing track in busy inboxes.

**Value for Business Users**: Organize client communications, track project milestones, bookmark important announcements, and maintain workflow continuity across email sessions. Ideal for professionals managing multiple email threads and requiring persistent access to critical communications.

## 🔗 Gmail Jump Links System

Revolutionary Gmail thread navigation system that transforms complex email conversations into structured, navigable content with persistent bookmarking capabilities:

### Advanced Thread Navigation
- **Enhanced Jump Link Insertion**: Automatically detects pinned emails within Gmail threads and inserts branded navigation links with improved visual integration
- **Persistent Email Bookmarking**: Maintains pinned email references across Gmail sessions with intelligent thread detection and enhanced navigation memory
- **Thread-Aware Navigation**: Seamlessly integrates with Gmail's conversation threading for pinned email access with refined cross-email navigation capabilities
- **Professional Visual Integration**: Purple-branded jump links blend naturally with Gmail's interface featuring enhanced positioning and improved workflow integration

### Professional Email Threading
- **Multi-Email Thread Support**: Handles complex email threads with multiple pinned messages for comprehensive navigation
- **Chronological Organization**: Jump links maintain proper temporal order within thread conversations
- **Real-time Thread Updates**: Dynamically updates navigation as new emails are added to threads
- **Cross-Thread Synchronization**: Maintains jump link integrity across different Gmail views and searches

### Enhanced Email Workflow
- **One-Click Navigation**: Direct access to important emails within long conversation threads
- **Context Preservation**: Jump links maintain email context within the broader conversation flow
- **Thread State Management**: Preserves navigation functionality across Gmail's dynamic content loading
- **Professional Branding**: Consistent purple brand theming matches extension's professional appearance

### Technical Integration Features
- **Gmail SPA Compatibility**: Full support for Gmail's single-page application architecture
- **Dynamic Content Handling**: Adapts to Gmail's lazy loading and content streaming
- **Thread ID Management**: Robust handling of Gmail's internal thread identification system
- **Memory Optimization**: Efficient observer patterns with proper cleanup for smooth Gmail performance

**Value for Email Management**: Transform complex email threads into navigable conversations, maintain quick access to important messages, and streamline communication workflow in busy Gmail environments. Perfect for managing client communications, project threads, and ongoing business correspondence.

**Value for Business Professionals**: Reduce time spent scrolling through long email threads, maintain focus on key messages, and improve response efficiency in professional email management. Ideal for complex project communications and multi-stakeholder conversations.

### Advanced Features
- **Tracked Domains**: Highlights your tracked domains in Google search results with customizable colored dashed underlines. This allows SEOs to quickly identify their own or client domains in search results.
- **Drag Select Links**: Hold hotkey and drag to select an area on any webpage, then extract and manage all links within the selected region in a popup interface. This is a powerful tool for link analysis and gathering URLs for specific sections of a page.

### Settings Page Organization
- **Accordion UI System**: Organizes settings into collapsible sections for enhanced navigation and user experience. This makes it faster for users to find and configure specific tools.
- **Structured Categories**: Four main sections including Google & Search Tools, Business & GMB Tools, Developer & Content Tools, and General & Browser Tools. This logical grouping helps users quickly locate relevant settings.
- **Select All/Deselect All Toggles**: Master toggle controls at the top of each accordion section for rapid feature configuration. This allows for quick enabling or disabling of entire categories of features, saving time during setup or testing.

### Import/Export
- **Export Settings**: Export current settings configuration to a file
- **Import Settings**: Import settings configuration from a file  
- **Reset to Defaults**: Restore all settings to their original default values
- **Clear All Data**: Remove all stored extraction data and cache

## 🔧 SEO TOOLS

### Technical SEO Analysis
- **Robots.txt Analyzer**: Parses and analyzes robots.txt directives. This helps SEOs quickly identify potential crawling issues that could impact search engine visibility.
- **XML Sitemap Checker**: Validates sitemap structure and accessibility. This ensures that all important pages are discoverable by search engines.
- **MOZ DA Checker**: Provides domain authority and SEO metrics. This offers a quick way to gauge a website's authority for competitive analysis.
- **Page Keyword Density**: Performs comprehensive keyword density analysis. This allows SEOs to quickly assess keyword stuffing or under-optimization.
- **Wayback Machine Integration**: Provides historical page analysis. This is useful for analyzing past website changes or recovering lost content.
- **404 Link Checker**: Identifies broken links and redirects. This helps web developers and SEOs fix issues that negatively impact user experience and search rankings.

### Content Analysis
- **Heading Structure Analysis**: Analyzes H1-H6 hierarchy and SEO compliance. This helps SEOs quickly identify and correct heading issues for better on-page optimization.
- **Schema Markup Extraction**: Extracts structured data for analysis. This allows web developers to quickly verify their schema implementation and SEOs to check competitors' structured data.
- **Metadata Analysis**: Analyzes title tags, meta descriptions, and other SEO tags. This provides a rapid way to audit on-page SEO elements without manual inspection.
- **Semantic Elements Highlighting**: Highlights HTML5 semantic structure for analysis. This helps web developers ensure proper use of semantic tags for better accessibility and SEO.

### Performance Tools
- **Page Structure X-ray**: Provides visual page layout analysis. This helps web developers quickly identify layout issues and optimize for better user experience and page load times.
- **Font Analysis**: Analyzes typography and web font optimization. This is crucial for web developers to ensure fast loading and consistent display of fonts across different devices.
- **Color Palette Extraction**: Extracts color palettes from pages for design consistency analysis. This helps web developers and designers maintain brand consistency and streamline design workflows.

## 🛠️ DEV TOOLS

### Visual Analysis
- **Page Structure X-ray**: Toggles visual overlays showing page structure. This helps web developers quickly understand the DOM structure and identify layout issues.
- **Element Inspector**: Provides advanced element selection and analysis. This allows developers to quickly inspect and debug specific elements on a webpage.
- **CSS Class Inspector**: Offers real-time CSS class analysis. This helps web developers debug styling issues and understand how CSS is applied.
- **Color Palette Extractor**: Extracts and analyzes color schemes. This is useful for web developers and designers to ensure brand consistency across a website.
- **Color Picker**: A professional color sampling tool with draggable panel interface and enhanced default hex auto-copying. This allows developers to quickly identify and replicate colors used on any webpage.

### Typography Tools
- **Font Inspector**: Analyzes font usage, loading, and performance. This helps web developers optimize font delivery for faster page load times, which is a key factor in user experience and SEO.
- **Font Styles**: Extracts font styling information. This allows web developers to quickly understand and replicate font styles across a website, ensuring design consistency.
- **Typography Analysis**: Provides comprehensive font and text analysis. This helps web developers ensure optimal readability and visual hierarchy, contributing to a better user experience.

### Accessibility Tools
- **Screen Reader Simulation**: Simulates screen reader experience for accessibility testing. This is vital for web developers to ensure their websites are accessible to all users, improving inclusivity and compliance.
- **Semantic Elements**: Highlights proper HTML5 semantic usage. This helps web developers ensure their code is well-structured and semantically correct, which benefits both accessibility and SEO.
- **Element Structure**: Analyzes DOM structure and accessibility. This provides web developers with insights into the underlying structure of a page, aiding in debugging and optimization for accessibility.

### Development Utilities
- **Copy Element**: Advanced element copying with multiple format options, RAW HTML mode, and configurable HTML cleaning. This allows web developers to quickly extract and reuse HTML snippets, saving time on manual copying and cleaning.
- **Quick Edit**: Enhanced live page editing with Shift+Drag bulk selection system for multi-element management and smart deletion. This enables web developers to rapidly prototype and test design changes directly in the browser.
- **Element Highlighting**: Provides visual element identification and selection with purple outline styling. This helps web developers quickly locate and understand the boundaries of elements on a page.
- **HTML Cleaner**: Configurable HTML cleaning with 9 options (styles, classes, IDs, comments, empty tags, extra spaces, data attributes, wrapper divs, citation numbers). This ensures that copied HTML is clean and ready for use, reducing post-processing time.
- **Links Extractor**: Comprehensive link analysis tool with multiple export formats, advanced filtering, and bulk selection capabilities. This is invaluable for SEOs performing link audits or competitive analysis, allowing for rapid data collection.
- **Click to Copy Links**: One-click link copying with Ctrl+Click activation and visual feedback system. This provides a fast and efficient way for SEOs and content managers to gather URLs.
- **Drag Select Links**: Advanced drag-to-select functionality for bulk link management with 7 output formats. This streamlines the process of collecting multiple links from a specific area of a webpage.
- **Responsive Device Simulator**: Professional device testing with 25+ device presets, overflow detection, and debug analysis. This is crucial for web developers to ensure a consistent and optimized user experience across various screen sizes.

### HTML Cleaning System
A comprehensive HTML cleaning system integrated into Copy Element functionality:

#### Configurable Cleaning Options
- **Remove Inline Styles**: Strips all `style=""` attributes from HTML elements. This helps web developers quickly clean up messy HTML for consistent styling.
- **Remove CSS Classes**: Removes all `class=""` attributes from HTML elements. Useful for developers who want to re-style content from scratch.
- **Remove Element IDs**: Eliminates all `id=""` attributes from HTML elements. This prevents ID conflicts when reusing HTML snippets.
- **Remove HTML Comments**: Strips out all `<!-- -->` comment blocks. Provides cleaner HTML for production or reuse.
- **Remove Empty Tags**: Removes tags with no content or only whitespace. Reduces HTML bloat and improves readability.
- **Remove Extra Spaces**: Normalizes whitespace and removes consecutive spaces. Ensures consistent formatting and reduces file size.
- **Remove Data Attributes**: Strips all `data-*` attributes (data-id, data-element_type, etc.). Useful for developers who only need the core content.
- **Remove Wrapper Divs**: Intelligently removes outer wrapper divs that only contain meaningful content. Simplifies HTML structure, making it easier to work with.
- **Remove Citation Numbers**: Advanced citation cleanup including citation links, trailing numbers, and isolated citation references. Provides clean content for reuse without extraneous references.

#### Smart Wrapper Div Removal
The wrapper div removal feature intelligently detects patterns like:
```html
<div><div><div><ul><li>Content...</li></ul></div></div></div>
```
And cleanly transforms them to:
```html
<ul><li>Content...</li></ul>
```

#### Integration Points
- **Copy Element**: Automatically applies cleaning when using "Clean and Title" feature
- **Settings Panel**: Accessible via HTML Cleaning Options button in Extras settings
- **Default Configuration**: All cleaning options enabled by default for optimal results

## 🎯 Global Features

### Keyboard Shortcuts
- **Popup Keyboard Shortcuts System**: Universal shortcuts (R-Reload, S-Settings, T-Timer, A-Alerts, E-Emails) designed to avoid web page conflicts for seamless navigation within the extension interface
- **Accordion Toggle Shortcuts**: Quick access shortcuts for main popup sections when popup is focused and no input fields are active:
  - **Q key**: Quick Timer toggle (open/close accordion)
  - **D key**: Todo tasks toggle (open/close accordion)
  - **L key**: Location Changer toggle (open/close accordion)
  - **T key**: Pomodoro timer start/stop toggle
- **Global Application Launchers**: Configurable keyboard shortcuts for external application and URL access:
  - **New Tab Redirect**: Instant navigation to configured URLs via customizable keyboard shortcut
  - **Text Time Machine Launcher**: One-click launch of external text editing applications with file protocol support
- **Customizable Shortcuts**: Set global keyboard shortcuts for common actions. This allows users to quickly access frequently used tools, significantly speeding up workflows for both SEOs and web developers.
- **Copy Element Shortcut**: Provides quick access to the element copying tool with RAW HTML support. This enables rapid extraction of HTML snippets without navigating through menus.
- **Color Picker Shortcut**: Offers global keyboard shortcuts for instant color picker activation. This allows designers and developers to quickly sample colors from any webpage.
- **Cross-Platform Support**: Works on Windows, Mac, and Linux, ensuring consistent functionality across different operating systems.

### Data Export
- **CSV Exports**: All extracted data can be exported to CSV format. This allows SEOs to easily import data into spreadsheets for further analysis and reporting, and web developers to process data programmatically.
- **Multiple Formats**: Various export options for different data types. This provides flexibility for integrating extracted data into diverse workflows and tools.
- **Batch Processing**: Handles large datasets efficiently, saving significant time when dealing with extensive data extraction tasks.

### Browser Integration
- **Enhanced Extension Stability**: Improved Chrome extension lifecycle handling with robust context invalidation fixes and enhanced error recovery for reliable operation across browser sessions.
- **Advanced Settings Management**: Offers comprehensive settings and configuration with enhanced stability and performance optimizations. This allows users to customize the tool to their specific needs, enhancing efficiency.
- **Optimized Storage Sync**: Synchronizes settings across Chrome instances with improved reliability and performance enhancements. This ensures a consistent user experience across multiple devices, saving time on re-configuration.
- **Memory Management**: Enhanced resource cleanup and optimization preventing memory leaks and improving overall extension performance during extended usage.

## 📊 Data Export Formats

### Business Data CSV
```csv
Business Name,Address,Phone,Website,Main Category,Rating,Review Count,Latitude,Longitude,Place ID,CID Number
```

### Reviews CSV
```csv
ID,Reviewer Name,Rating,Date,Review Text,Helpful Count,Business Response,Photo Count,Extracted At
```

### Services Analysis CSV
```csv
Service,Count,Percentage,Category,Business Count
```

### YouTube Videos CSV (with Schema Generation)
```csv
Index,Title,Embed URL,ID,Share URL,Full URL,Thumbnail URL,Duration Full,Duration Time,Duration Seconds,Views,Schema
```

### NAP Data Format
```
Business Name
Address
Phone Number
```

## 🔧 Usage Tips

### Google Search NAP Extraction
1. Search for businesses on Google (`google.com/search`)
2. Look for "Places" section in search results
3. Click "Copy NAP" buttons next to business names
4. Data is automatically formatted and copied to clipboard

### Maps Data Extraction
1. Navigate to Google Maps business listing
2. Click extension icon in toolbar
3. Use "Extract Data" for complete information
4. Use "Copy NAP" for quick name/address/phone

### Review Scraping
1. Open business on Google Maps
2. Go to Reviews tab
3. Scroll to load ALL reviews first
4. Click "Review Scraper" in extension popup
5. Wait for completion and export to CSV

### Location Override
1. Click extension icon
2. Expand "Location Changer" section
3. Enter desired location or coordinates
4. Enable "location overwrite" checkbox
5. All Google searches will use new location

### HTML Cleaning & Copy Element
1. Right-click on any page and select "Copy Element"
2. Hover over elements to see them highlighted
3. Click any element to copy its content (supports RAW HTML mode)
4. For clean HTML, use Shift+Click for "Clean and Title" option
5. Configure cleaning options via extension popup → Extras → HTML Cleaning Options
6. All 8 cleaning options are enabled by default for best results

### Enhanced Quick Edit
1. Right-click and select "Quick Edit" or use keyboard shortcuts
2. Page enters live editing mode with visual indicator
3. Hold Shift and drag to select multiple elements with purple outline
4. Press Delete or Backspace to remove selected elements
5. Smart deletion logic protects critical page elements
6. Press ESC to exit Quick Edit mode

### Color Picker Tool
1. Click Color Picker button in extension popup or use global keyboard shortcut
2. Draggable color picker panel appears with professional interface
3. Click anywhere on the page to sample colors in real-time
4. Color values automatically copied to clipboard with enhanced default hex format
5. Supports multiple color formats (HEX, RGB, HSL) with improved auto-copying settings
6. Panel can be repositioned by dragging the header with enhanced error handling
7. Close with ESC key or close button for clean exit

### Links Extractor Tool
1. Click Links Extractor button in extension popup or right-click menu
2. Professional interface displays all page links with comprehensive analysis
3. Filter by domain, link type, or search text for targeted extraction
4. Select individual links or use bulk selection (all/none/filtered)
5. Export in multiple formats: HTML, Markdown, CSV, JSON, plain text
6. Real-time statistics show total links, selected count, and domain distribution
7. Drag to reposition popup and resize panels for optimal workflow

### Click to Copy Links
1. Enable Click to Copy Links in extension settings (enabled by default)
2. Hold Ctrl and click any link on any webpage
3. Link URL is instantly copied to clipboard with visual feedback
4. Green flash animation confirms successful copy operation
5. Works universally across all websites without interfering with normal link behavior

### Drag Select Links
1. Activate via customizable keyboard shortcut or extension popup
2. Drag to select multiple links with visual selection feedback
3. Professional popup shows selected links with filtering options
4. Choose from 7 output formats including HTML, Markdown, CSV, JSON
5. Advanced filtering by domain, text search, and bulk operations
6. Export selected links via clipboard or CSV download
7. Customizable selection colors and hotkey configuration

### Responsive Device Simulator
1. Click Responsive Device Simulator button in extension popup
2. Professional dark-themed interface with 25+ device presets
3. Switch between iPhone, Android, iPad, laptop, and desktop devices instantly
4. Use rotation button (R) to switch between portrait/landscape modes
5. Zoom controls (25%-200%) with keyboard shortcuts (+/- keys)
6. Custom sizing with manual width/height inputs and memory storage
7. Debug mode (D key) highlights overflow elements with detailed analysis
8. Drag device container to reposition and use resize handles for custom dimensions
9. Export overflow analysis to CSV for detailed responsive debugging

### YouTube Schema Generation Setup
1. **Create Google Cloud Project**: Visit Google Cloud Console and create a new project
2. **Enable YouTube Data API v3**: In APIs & Services, enable YouTube Data API v3
3. **Generate API Key**: Create credentials → API Key with YouTube Data API v3 access
4. **Configure Extension**: Open extension settings → General Settings → YouTube API Configuration
5. **Enter API Key**: Input your API key (format: AIzaSy...) and click Save
6. **Verify Setup**: Green status indicator confirms successful configuration
7. **Use Schema Generation**: Navigate to any YouTube channel's videos page and use YouTube Embed Scraper
8. **Export with Schema**: Generated schema markup will appear in the Schema column of CSV exports

### YouTube Embed Scraper with Schema
1. Navigate to a YouTube channel's main videos page
2. Click the YouTube icon in extension popup to open the scraper
3. Configure video ranges (e.g., 1-10, 50-100) for batch processing
4. Click "Number Videos" to add visual indicators to all videos
5. Click "Extract Selected Range" to begin processing
6. Scraper will extract embed URLs, thumbnails, duration, views, and generate schema markup
7. Monitor progress in real-time log and completion notifications
8. Use "Export to CSV" to download comprehensive video data with schema markup
9. Schema column contains ready-to-use JSON-LD markup for direct implementation

## ⚠️ Important Notes

- **Rate Limiting**: Tool respects Google's rate limits for responsible usage
- **Manual Triggers**: All extractions require manual user activation
- **Data Privacy**: Only extracts publicly available information
- **Terms of Service**: Users responsible for complying with Google's ToS

## 📈 Version History

Current Version: **7.9**

***New in Version 7.9**: Calendar-Based Alert System with visual date picker and monthly navigation, Independent Task Management System integrated with Pomodoro timer, Quick Timer System with preset options and custom duration input, Popup Keyboard Shortcuts System with universal shortcuts, and Enhanced Alert Management with editing capabilities and search functionality.

See [CHANGELOG.md](CHANGELOG.md) for detailed version history and updates.

## 🙏 Acknowledgments

### Third-Party Components
- **Color Picker Tool**: Based on the excellent [CoPiBoo - Color Picker Bookmarklet](https://github.com/twogrey/CoPiBoo---Color-Picker-Bookmarklet) by twogrey. Enhanced with additional features including auto-copying, CSV export, and improved UI for seamless integration into our toolkit.

---

**Made with ❤️ for the digital marketing community**  
Demetre from SEO Lifetime Master Mind

*SEO Time Machines v7.9*