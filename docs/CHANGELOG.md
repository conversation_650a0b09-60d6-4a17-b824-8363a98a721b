# Changelog

All notable changes to the SEO Time Machines will be documented in this file.

## [7.14.0] - 2025-08-21

### New Features
- **New Tab Redirect Function**: Advanced tab redirection system with global keyboard shortcut support that allows users to automatically redirect new tabs to a custom URL. Features smart URL normalization with automatic protocol detection, supporting both HTTP/HTTPS URLs and file:// paths. Includes fallback mechanisms for reliable operation when background script is unavailable, ensuring consistent functionality across different Chrome extension contexts.

- **Text Time Machine Launcher Feature**: Comprehensive file and URL launcher system designed for rapid access to text editing applications and local files via configurable keyboard shortcuts. Supports multiple URL formats including file:// URLs, raw file paths, and web addresses with intelligent path normalization for cross-platform compatibility. Features robust fallback mechanisms using both Chrome extension APIs and direct window.open methods for maximum reliability when launching local applications or files.

### Improvements
- **Enhanced URL Handling**: Advanced URL normalization system with automatic protocol detection for file paths, supporting Windows drive letter formats (C:), Unix absolute paths (/), and relative paths with appropriate file:// prefix generation
- **Cross-Platform File Path Support**: Intelligent file path handling supporting Windows, macOS, and Linux path formats with automatic conversion to proper file:// URLs for browser compatibility
- **Robust Error Handling**: Comprehensive error handling and fallback mechanisms ensuring reliable tab creation and file launching even when Chrome extension context is invalidated
- **Smart Protocol Detection**: Automatic detection and normalization of URLs lacking protocols, adding https:// prefix for web URLs and file:// prefix for local file paths

### Technical Changes
- **Background Script Integration**: Enhanced background.js with new message handlers for 'createNewTabRedirectTab' and 'createTextTimeMachineLauncherTab' actions supporting reliable tab creation with proper error handling
- **Global Shortcut System Integration**: Both features integrated with the existing global shortcut manager system allowing users to configure custom keyboard shortcuts for instant access
- **Storage System Enhancement**: Extended settings system with new configuration fields 'newTabRedirectUrl' and 'textTimeMachineUrl' for persistent URL storage with validation
- **Chrome Extension Context Resilience**: Implemented dual-method approach using Chrome extension APIs as primary method with window.open fallback for scenarios where extension context becomes unavailable

### User Experience Enhancements
- **Instant Access Workflow**: Both features provide immediate access to configured URLs or applications through customizable keyboard shortcuts, eliminating multiple navigation steps
- **Professional Text Editor Integration**: Text Time Machine launcher specifically designed for rapid access to local text editing applications, supporting popular editors and custom file paths
- **Seamless Tab Management**: New Tab Redirect function provides automated tab redirection without user intervention while maintaining full control through toggle settings
- **Visual Feedback System**: Comprehensive notification system providing clear feedback for successful launches, configuration errors, and fallback method usage

## [7.13.0] - 2025-08-19

### New Features
- **Local Text Time Machine - Text Mechanic 2.0**: Introduced standalone Text Time Machine application in separate Local Text Editor folder for advanced text editing capabilities with comprehensive formatting tools and enhanced workflow functionality

### Improvements
- **Enhanced Control Box Layout**: Redesigned control box interface with improved visual hierarchy and better organization of button groups for more intuitive navigation
- **Repositioned Case Conversion Tools**: Moved Lower Case and Sentence Case buttons to main control bar for improved accessibility and streamlined text transformation workflow
- **Enhanced Theme Compatibility**: Improved light and dark theme compatibility for line numbers and interface elements with better contrast and readability
- **Optimized Search Functionality**: Enhanced search functionality positioning and accessibility for improved user workflow and feature discovery

- **Theme System Enhancement**: Improved CSS architecture for better light/dark theme support across interface components

## [7.12.1] - 2025-08-17

### Bug Fixes
- **WordPress Text Transformers Focus Detection**: Fixed critical issue where text transformations would affect TinyMCE editor content even when other input fields (ACF fields, post title) were focused. The keyboard shortcuts now properly respect the currently focused field, ensuring transformations only apply to the active element.

### Technical Changes
- **Removed TinyMCE-First Logic**: All text transformer execute methods now use unified focus detection instead of prioritizing TinyMCE editors
- **Enhanced Focus Detection**: Improved detection logic to handle focused INPUT and TEXTAREA elements with any content, not just selected text
- **Smart Paste Targeting**: Updated paste operations to prioritize currently focused input fields over TinyMCE editors

## [7.12.0] - 2025-08-17

### New Features
- **WordPress Classic Editor Visual Mode Support**: Full integration with WordPress Classic Editor's Visual mode through advanced TinyMCE iframe detection and content manipulation capabilities
- **Enhanced Text Transformers System**: Comprehensive text transformation capabilities with improved timing mechanisms and intelligent retry logic for reliable cross-platform operation
- **WordPress-Specific Content Synchronization**: Specialized content manipulation system designed specifically for WordPress Classic Editor environments with real-time synchronization between visual and text modes

### Improvements
- **Advanced TinyMCE Detection**: Enhanced iframe detection system with WordPress-specific patterns for reliable Visual Editor identification and access
- **Robust Timing and Retry Mechanisms**: Improved text transformer reliability with sophisticated retry logic and optimized timing for various content management systems
- **Enhanced Iframe Access Controls**: Updated manifest permissions with match_about_blank support for comprehensive iframe content manipulation across WordPress environments
- **Cross-Platform Clipboard Operations**: Strengthened clipboard functionality with enhanced compatibility across different operating systems and browser environments

### Technical Changes
- **TinyMCE Iframe Integration**: Advanced iframe access implementation specifically designed for WordPress Classic Editor Visual mode with comprehensive content detection
- **WordPress Pattern Recognition**: Specialized detection algorithms for WordPress-specific editor patterns and content structures
- **Enhanced Permission System**: Updated manifest.json with match_about_blank permission for complete iframe access and content manipulation capabilities
- **Improved Content Synchronization**: Advanced synchronization mechanisms between WordPress Visual and Text editor modes for seamless content transformation

### User Experience Enhancements
- **Seamless WordPress Integration**: Text transformers now work flawlessly within WordPress Classic Editor Visual mode for improved content creation workflow
- **Enhanced Reliability**: Improved success rates for text transformation operations across various content management platforms
- **Better Error Handling**: Enhanced error recovery and user feedback for text transformation operations in complex iframe environments

## [7.11.0] - 2025-08-15

### New Features
- **Location Changer Context Menu Access**: Right-click context menu now provides instant access to location changer functionality without opening the extension popup. Features include quick location switching, checking current location, and disabling fake location with seamless integration into existing location favorites system.

### Improvements
- **Enhanced Location Workflow**: Streamlined location management workflow through right-click access, eliminating the need to open the extension popup for common location tasks
- **Context Menu Location Shortcuts**: Saved location favorites now appear directly in the context menu for instant switching between frequently used locations
- **Quick Location Verification**: One-click location checking through context menu for instant verification of current location settings

### User Experience Enhancements
- **Professional Workflow Integration**: Location changer context menu designed specifically for SEO professionals who need rapid location switching during local search analysis
- **Reduced Navigation Steps**: Direct access to location functionality saves time and improves efficiency for local SEO tasks
- **Intuitive Right-Click Integration**: Context menu items follow familiar browser patterns for seamless user adoption

### Technical Changes
- **Context Menu API Implementation**: Enhanced background.js with comprehensive context menu integration for location changer functionality
- **Universal URL Pattern Support**: Context menu available across all Google domains and URLs for consistent access
- **Location Favorites Integration**: Context menu dynamically populated with saved location favorites for personalized quick access

## [7.10.0] - 2025-08-15

### New Features
- **Email Generator Context Menu Item**: The email generator is now accessible through the right-click context menu for quick access, it is designed to recursively generate email variations for any given email address by using the dot (.)trick.

### Improvements
- **Enhanced Keyboard Shortcuts**: Improved shortcut system with better conflict prevention and streamlined Quick Actions access for faster workflow

### Technical Changes
- **Global Shortcut Manager**: Updated global shortcut manager with improved conflict detection and resolution system for better shortcut activation
- **Context Menu Integration**: Email generator accessible through right-click context menus for enhanced accessibility
- **Popup Design Consistency**: Email generator follows universal popup standards with drag functionality and brand-consistent styling

## [7.9] - 2025-01-13

### New Features
- **Calendar-Based Alert System**: Complete visual date picker with monthly navigation, today highlighting, and intuitive date selection
- **Independent Task Management System**: Standalone task creation and management integrated with Pomodoro timer functionality
- **Quick Timer System**: Preset timer options (5, 10, 15, 30 minutes) with custom duration input and immediate start capability
- **Popup Keyboard Shortcuts System**: Universal shortcuts (R-Reload, S-Settings, T-Timer, A-Alerts, E-Emails) designed to avoid web page conflicts
- **Enhanced Alert Management**: Alert editing capabilities with "X hours from now" scheduling functionality for precise timing
- **Advanced Alert Display**: Comprehensive alert organization with visual priority indicators and smart sorting by date proximity
- **Alert Search Functionality**: Real-time search capabilities across all alert content for quick filtering and discovery

### Improvements
- **Chrome Extension Context Invalidation Fixes Phase 2**: Centralized context manager with comprehensive error handling and graceful degradation
- **Enhanced Audio System Integration**: Alert notifications integrated with STM audio system for consistent sound management
- **Accordion Interface Implementation**: Space-efficient accordion design for better organization of productivity features
- **Safe Shortcut System**: Carefully designed keyboard shortcuts that avoid conflicts with web page functionality
- **Alert Sorting Intelligence**: Smart sorting system prioritizing soonest alerts with visual indicators for overdue items
- **Pomodoro Timer Integration**: Enhanced timer system with task association and productivity workflow integration
- **User Experience Enhancements**: Improved popup layout with dedicated productivity sections and intuitive navigation

### Bug Fixes
- **Extension Context Error Resolution**: Context invalidation errors now handled gracefully without console spam or user disruption
- **Gmail Module Stability**: Safe Chrome API wrappers prevent extension crashes in Gmail context
- **Alert Migration System**: Automatic migration of legacy weekly/weekdays alerts to daily format for consistency
- **Memory Management**: Comprehensive cleanup systems prevent memory leaks in alert and timer functionality
- **Error Handling Enhancement**: Robust error handling across all new productivity features with user-friendly messaging

### Technical Changes
- **Modular Alert System Architecture**: New dedicated js/alerts/ directory with organized file structure for maintainability
- **Service Worker Lifecycle Management**: Enhanced background.js with improved extension lifecycle and context management
- **Enhanced Popup Infrastructure**: Updated popup.html with new productivity sections and accordion interface implementation
- **Context Manager Implementation**: Centralized context invalidation handling with graceful degradation patterns
- **Storage System Enhancements**: Improved alert and task storage with migration capabilities and data integrity checks
- **Audio Integration Framework**: Alert notifications properly integrated with STM audio system architecture
- **Keyboard Shortcut Infrastructure**: Safe shortcut system designed to coexist with web page functionality without conflicts

## [7.8.1] - 2025-08-12
### Extension Rebranding and UI Enhancements

#### Complete Rebranding to SEO Time Machines (STM)
- **Extension Name Update**: Complete rebranding from SLMM to "SEO Time Machines (STM)"
- **New Icon Suite**: Updated extension icons with professional STM branding across all sizes
- **Brand Consistency**: Updated all user-facing text, titles, and references throughout the extension
- **Asset Refresh**: New visual identity with enhanced professional appearance

#### User Interface Improvements
- **Enhanced Header Design**: Enlarged header logo for improved visibility and visual prominence
- **Optimized Popup Dimensions**: Increased popup width for better content display and user experience
- **Visual Polish**: Overall interface aesthetics improvements for professional appearance

#### Gmail Email Pinner Refinements
- **Subtle Pin Styling**: Unpinned emails now use subtle light grey (#e0e0e0) instead of red for reduced visual noise
- **Brand-Aligned Hover Effects**: Purple hover states (#7C3AED) matching extension brand colors
- **Enhanced Usability**: Improved pin visibility with 70% opacity for unpinned state and full opacity on hover
- **Maintained Functionality**: All existing pinning/unpinning capabilities preserved

#### Technical Stability Improvements
- **Context Invalidation Fixes**: Completed Phase 2 of Chrome extension context invalidation error handling
- **Enhanced Error Management**: Improved extension stability and lifecycle management
- **Background Script Optimization**: Updated background.js for better extension performance
- **Manifest Configuration**: Enhanced manifest.json for improved Chrome extension compliance

#### Development Workflow Enhancements
- **Extension Compiler**: Added new compilation tools for streamlined development
- **Documentation Updates**: Enhanced project documentation and memory bank resources
- **Code Organization**: Improved file structure and development utilities

## [7.8] - 2025-08-11
### Gmail Jump Links System and Bulk Link Opener Complete Overhaul

#### Gmail Jump Links Navigation System
- **Pinned Email Navigation**: Navigate quickly between pinned emails in Gmail threads with intuitive jump link system
- **Enhanced Email Pinner Integration**: Seamless integration with existing email pinning functionality for improved workflow
- **Thread-Based Navigation**: Smart detection and linking of pinned emails within Gmail conversation threads
- **Visual Navigation Indicators**: Clear visual cues for jump link positions and navigation status

#### Bulk Link Opener Complete Transformation
- **Window Position Memory**: Intelligent window positioning system that remembers and restores window locations
- **Saved Lists Management**: Comprehensive saved link lists with persistent storage and easy management
- **URL Extraction Tools**: Advanced URL detection and extraction capabilities from various text formats
- **Enhanced User Interface**: Completely redesigned interface with improved usability and visual design

#### Email Pinner Jump Links Integration  
- **Cross-Email Navigation**: Enhanced navigation capabilities between different pinned emails in threads
- **Context-Aware Linking**: Smart linking system that understands email context and relationships
- **Navigation Memory**: System remembers navigation patterns for improved user experience

#### User Experience Enhancements
- **Interface Consistency**: Improved visual consistency across all extension interfaces
- **Navigation Flow**: Enhanced user navigation patterns and interaction design
- **Accessibility Improvements**: Better accessibility features for improved usability

## [7.6] - 2025-08-03
### YouTube Video Intelligence Platform and Advanced Schema Generation

#### Complete YouTube Embed Scraper Transformation
- **Enhanced Data Extraction**: Expanded from 6 to 12 CSV columns with comprehensive video metadata including thumbnails, duration formats, view counts, and schema markup
- **YouTube Data API v3 Integration**: Complete API infrastructure for fetching detailed video metadata with secure key management in General Settings
- **Advanced Schema Generation**: Automated JSON-LD VideoObject schema markup generation compliant with Schema.org standards for enhanced SEO
- **Multi-Format Duration Processing**: Duration data in accessibility format, visual time format, and numeric seconds for comprehensive analysis
- **Intelligent View Count Processing**: Smart parsing of YouTube view formats (K/M/B suffixes) with conversion to actual numbers
- **High-Resolution Thumbnails**: Automatic extraction of maxresdefault.jpg thumbnails for professional presentation

#### YouTube API Configuration System
- **Secure API Key Management**: Dedicated YouTube API configuration section in General Settings with masked display and validation
- **Cross-Context Storage**: API keys stored securely in chrome.storage.local with proper access across extension contexts
- **Validation System**: API key format validation (AIza prefix, minimum 30 characters) with clear error messaging
- **Direct Integration Links**: One-click access to Google Cloud Console for API key generation and management

#### Advanced Schema Markup Generation
- **Complete VideoObject Schema**: Generates comprehensive structured data including title, description, duration, thumbnails, view statistics, and author information
- **SEO-Ready Output**: Schema wrapped in proper script tags for immediate website integration
- **Batch Processing Architecture**: Parallel processing of multiple videos with intelligent delay system and progress tracking
- **Graceful Degradation**: Functions without API key with appropriate status indicators and fallback behavior

#### Enhanced User Experience
- **Progress Tracking Integration**: Real-time progress updates during batch processing with global progress system
- **Quick Actions Integration**: YouTube Embed Scraper added to numbered shortcuts system for keyboard access
- **Error Handling**: Comprehensive error messaging for API issues, validation failures, and network problems
- **Status Indicators**: Clear visual feedback for API configuration status and processing state

#### Technical Architecture Improvements
- **API Integration Workflow**: Streamlined video ID extraction, batch API requests, and schema generation pipeline
- **Data Flow Enhancement**: Updated extraction workflows with enhanced return objects while maintaining backward compatibility
- **Memory Management**: Proper cleanup and resource management for API requests and large data processing
- **Storage Optimization**: Efficient handling of schema data and API responses with appropriate caching strategies

#### CSV Export Enhancements
- **Extended Export Format**: New YouTube Videos CSV format with schema column for structured data export
- **Data Analysis Support**: Comprehensive video metrics including duration calculations and engagement measurement
- **Professional Reporting**: Ready-to-use data exports for SEO analysis and content strategy planning

#### Minor UI Improvements
- **Gmail Email Pinner**: Reduced pin icon size from 16x16 to 14x14 pixels for improved visual proportion
- **Gmail Enhanced Timestamps**: Added line-height styling for better timestamp display consistency

## [7.5.1] - 2025-07-31
### Clean Slate Experience and Enhanced Reset Functionality

#### Clean Slate Default Settings
- **Optimized New User Experience**: Changed 60+ settings from enabled (true) to disabled (false) by default for streamlined first-time user experience
- **Focused Tool Activation**: New users now start with a clean interface and can selectively enable only the tools they need
- **Reduced Cognitive Load**: Minimized overwhelming feature exposure for new users while maintaining full functionality for power users
- **Strategic Default Configuration**: Carefully selected essential tools remain enabled while specialized features require conscious activation

#### Enhanced Reset to Defaults System
- **Selective Reset Capability**: New "Reset to Defaults" functionality in General Settings with comprehensive scope selection
- **Data Preservation Options**: Advanced reset system that can preserve user data while restoring default settings
- **Granular Reset Control**: Users can choose to reset specific setting categories or perform complete restoration
- **Safe Reset Process**: Intelligent reset system that maintains extension stability during default restoration

#### User Experience Improvements
- **Onboarding Integration**: Enhanced onboarding system works seamlessly with new clean slate defaults
- **Professional Workflow Support**: Clean defaults support professional onboarding categories without overwhelming new users
- **Power User Compatibility**: Existing power users retain their custom configurations while new users benefit from simplified start
- **Settings Management**: Improved settings interface with better organization and clearer default value indicators

#### Technical Architecture Enhancements
- **Default Settings Optimization**: Comprehensive review and optimization of defaultSettings object in settings.js
- **Reset Mechanism Implementation**: Robust reset-to-defaults system with proper error handling and validation
- **Storage Efficiency**: Optimized storage usage by setting appropriate default values that align with common use cases
- **Configuration Consistency**: Ensured all default settings align with recommended usage patterns and user feedback

#### Bug Fixes and Stability
- **Settings Synchronization**: Enhanced settings propagation to ensure reset functionality works across all extension contexts
- **Default Value Accuracy**: Corrected default values to match actual recommended usage patterns
- **Reset Process Reliability**: Comprehensive testing of reset functionality to ensure consistent behavior
- **Extension Lifecycle Compatibility**: Reset system properly handles extension reloads and updates

## [7.5] - 2025-07-31
### Professional Onboarding System and Enhanced Tool Management

#### New User Onboarding Experience
- **Intelligent Onboarding System**: Professional full-screen onboarding interface for new users with toolset selection
- **Toolset Categories**: Organized tools into 8 professional categories: Local SEO, Web Developer, Digital Marketing, Content Writer, Designer, Email Tools, YouTube Tools, and Maximum Power
- **Visual Category System**: Color-coded toolsets with unique icons and descriptions for easy identification
- **Adaptive Title System**: Dynamic onboarding title that changes based on selected toolsets
- **Smart Tool Activation**: Automatically enables relevant tools based on user's selected professional profile
- **Rerun Capability**: Onboarding can be rerun from settings, automatically resetting all toggles before selection

#### Onboarding System Architecture
- **Content Script Implementation**: Full-page overlay system with proper focus management and ESC key support
- **Storage Integration**: Seamless integration with extension's settings system for immediate tool activation
- **Auto-Show on Reload**: Smart flag system to resume onboarding after extension reload
- **Comprehensive Tool Mapping**: All 55+ tools properly categorized into relevant professional toolsets
- **Maximum Power Mode**: Special mode that enables all available tools for power users

#### Documentation and Tool Organization
- **README Enhancement**: Added comprehensive tool count (77+ tools) prominently in documentation
- **Missing Tool Integration**: Added documentation for previously undocumented tools including pomodoroBlockingEnabled, minimalReaderSpeedReadingEnabled, and UTM management tools
- **Complete Tool Coverage**: All settings.js tools now properly documented with descriptions and use cases
- **Context Menu Documentation**: Included all 22 context menu items in total tool count

#### Bug Fixes and Improvements
- **Onboarding Tool Synchronization**: Fixed missing tools in getAllAvailableSettings() method
- **Settings Propagation**: Enhanced immediate settings updates across all extension contexts
- **Tool Category Accuracy**: Ensured all tools are properly mapped to their relevant professional categories
- **Version Update**: Updated documentation to reflect current version 7.5

## [7.4] - 2025-07-30
### Major Brand Evolution and YouTube Feature Expansion

#### Complete Brand Transformation
- **Project Renamed**: Complete transition from "Local SEO Toolkit" to "SEO Time Machines"
- **Extension Name Update**: Manifest now displays "SEO Time Machines" as the official extension name
- **Legacy Reference Cleanup**: Maintained backward compatibility for AWS audio hosting while planning future migration
- **Brand Identity Consolidation**: All user-facing elements now reflect the new SEO Time Machines identity

#### Advanced YouTube Integration System
- **YouTube Frames Feature**: New frame capture system for YouTube videos with custom overlay and download functionality
- **YouTube GIF/WebP Viewer**: Enhanced image viewing system with hover-based WebP detection and display
- **Frame Generation with Branding**: Custom YouTube frame overlay system with embedded SVG graphics
- **Video Processing Tools**: Improved video screenshot and thumbnail management capabilities

#### YouTube Feature Architecture
- **Main World Script Integration**: YouTube features now run in MAIN world context for enhanced video API access
- **Settings Bridge System**: Advanced settings synchronization between extension contexts
- **Local Storage Integration**: Optimized settings management for YouTube-specific features
- **Cross-Context Communication**: Robust message passing system for YouTube feature coordination

#### Technical Infrastructure Improvements
- **Content Script Organization**: Enhanced modular loading system for YouTube-specific functionality
- **Observer Pattern Implementation**: Advanced DOM monitoring for dynamic YouTube content
- **Error Handling Enhancement**: Comprehensive error recovery systems for YouTube feature stability
- **Performance Optimization**: Reduced resource consumption for YouTube video processing

#### User Experience Enhancements
- **Settings Interface Updates**: Improved toggle controls for new YouTube features
- **Feature Integration**: Seamless integration of YouTube tools with existing Quick Actions system
- **Accessibility Improvements**: Enhanced keyboard navigation and screen reader support
- **Visual Consistency**: Unified design language across all new YouTube features

## [7.2.1] - 2025-07-24
### Critical Bug Fixes and Stability Improvements

#### Extension Context Invalidation Protection
- **Enhanced Error Handling**: Comprehensive protection against Chrome extension context invalidation errors
- **Global Promise Rejection Handlers**: Prevents unhandled promise rejections from crashing the extension
- **Context Validation System**: Robust checks for Chrome runtime availability before API operations
- **Graceful Degradation**: Maintains functionality during extension reloads and Chrome updates

#### Pomodoro Timer Stability
- **Context Validation in Message Passing**: Enhanced sendMessage method with comprehensive context checks
- **Element Validation**: Ensures DOM nodes exist before adding event listeners
- **Error Prevention**: Silently handles extension lifecycle events without disrupting user experience
- **Settings Synchronization**: Improved handling of notification position settings

#### Gmail Email Pinner Resilience
- **Atomic Operation Management**: Tracks and manages all async operations with cancellation support
- **Safe Chrome API Wrappers**: Retry mechanisms for transient storage operation failures
- **Operation Timeout Management**: Prevents hanging operations from consuming resources
- **Memory Leak Prevention**: Comprehensive cleanup of intervals, timeouts, and observers

### Technical Improvements

#### Error Recovery System
- **Tracked Operations**: New activeOperations set to manage concurrent async tasks
- **Context Invalidation Flags**: Prevents operations after extension context is lost
- **Safe Storage Operations**: Wrapped Chrome storage API calls with error handling
- **Resource Cleanup**: Automatic cleanup when context invalidation is detected

#### Memory Management Enhancements
- **Operation Lifecycle Management**: All async operations are now tracked and cancellable
- **Resource Deallocation**: Proper cleanup of event listeners and timers
- **Prevents Orphaned Operations**: Clears all active operations on context invalidation
- **Improved Extension Lifecycle Handling**: Better resilience to Chrome updates and reloads

## [7.2.0] - 2025-07-18
### Major New Feature: Copy Replace System Integration

#### Copy Replace System Integration
- **Advanced Text Replacement**: Real-time text replacement system with keyboard shortcuts and safety enforcement
- **Toggle Safety Checks**: Mandatory safety verification before Copy Replace functionality activation
- **Global Shortcut Integration**: Seamless integration with global shortcut manager for consistent hotkey handling
- **Settings Integration**: Proper default settings and real-time toggle controls

#### Level-Two Tooltip System
- **Enhanced Tooltip Architecture**: Dual-layer tooltip system for settings contexts with fallback processing
- **Dynamic Content Support**: Tooltip system handles dynamically generated content with proper initialization
- **Visual Indicators**: Purple dot indicators with hover effects and smooth animations
- **Settings Context Detection**: Intelligent detection of settings environments for appropriate tooltip display

### System Architecture Improvements

#### Real-Time Settings Synchronization
- **Automatic Settings Detection**: `chrome.storage.onChanged.addListener` implementation for immediate settings propagation
- **Cross-Component Synchronization**: Real-time updates between settings interface and content scripts
- **Improved User Experience**: Instant setting changes without extension reload requirement

#### Keyboard Shortcuts System
- **Task Management Shortcuts**: Ctrl+Up/Down for Pomodoro task reordering with visual selection indicators
- **Document-Level Handling**: Comprehensive keyboard event management with proper cleanup
- **Selection System**: Visual task selection with outside-click clearing functionality

#### Search NAP Injector Restoration
- **Toggle Functionality Restoration**: Restored missing Search NAP Injector toggle with enhanced settings integration
- **Improved URL Pattern Matching**: Better recognition of Google Search pages across different domains
- **Enhanced Error Handling**: Robust error handling and performance optimization

### Technical Enhancements

#### Storage Architecture Improvements
- **Enhanced Local Storage Usage**: Improved `chrome.storage.local` implementation for audio and persistent settings
- **Cross-Session Persistence**: Better state management across browser restarts and tab changes
- **Memory Optimization**: Reduced memory footprint with efficient data structures

#### DOM Cleanup and Memory Management
- **Comprehensive DOM Cleanup**: Enhanced DOM cleanup utilities with proper event listener management
- **Memory Leak Prevention**: Systematic cleanup procedures for all new features
- **Resource Management**: Proper cleanup of intervals, timeouts, and event listeners

#### Settings Interface Reorganization
- **Logical Feature Grouping**: Better organization of HTML cleaning options and related features
- **Enhanced User Workflow**: Improved settings interface with better visual hierarchy
- **Accessibility Improvements**: Better screen reader support and keyboard navigation

### User Interface Improvements

#### Audio System Enhancements
- **Enhanced Notification Styling**: Improved visual design for audio notifications with better button layouts
- **Responsive Design**: Better mobile and small screen support for audio controls
- **Visual Hierarchy**: Clearer button states and hover effects for audio interface

#### Popup Design Consistency
- **Universal Standards Compliance**: All new popups follow established design patterns with purple branding
- **Dark Theme Integration**: Consistent dark theme implementation across all new interfaces
- **Draggable Interfaces**: All popups maintain draggable functionality with ESC key support

### Performance Optimizations

#### Extension Architecture
- **Reduced Console Spam**: Minimized debug output in video embedding and other features
- **Enhanced Error Handling**: Better error recovery and user feedback systems
- **Improved Resource Usage**: More efficient memory and CPU usage patterns

#### Cross-Platform Compatibility
- **Multi-Domain Support**: Maintained compatibility across Google domains (.com, .co.uk, .ca, .com.au)
- **Browser Compatibility**: Enhanced support for Chrome, Edge, and Brave browsers
- **Platform-Specific Optimizations**: Better handling of different operating systems

### Developer Experience Improvements

#### Testing Infrastructure
- **DOM Snapshot Testing**: Enhanced testing utilities for DOM cleanup verification
- **Event Listener Testing**: Comprehensive testing for memory leak prevention
- **Integration Testing**: Better testing coverage for new feature interactions

#### Documentation Updates
- **Memory Bank Documentation**: Updated implementation patterns and golden rules
- **Settings Integration Guidelines**: Enhanced documentation for new feature integration
- **Technical Architecture**: Updated technical documentation for new system components

## [7.1.0] - 2025-07-15
### Major New Feature: Universal Screenshot System introduced in thsi commit 98574f229167ec022413f80a41ce27ac6feeb76c
- **Complete Screenshot Implementation**: Full-featured screenshot system with area selection, editing, and export capabilities across all websites
- **Area Selection Tool**: Interactive screenshot selector with crosshair cursor and purple-branded selection rectangle for precise area capture
- **Advanced Screenshot Editor**: Dedicated editor interface (`screenshot-editor.html`) with comprehensive editing tools and export options
- **Custom Keyboard Shortcuts**: Configurable keyboard shortcuts for screenshot activation with real-time key combination capture
- **Multi-Platform Compatibility**: Universal screenshot functionality working across all websites and web applications

### Screenshot System Architecture
- **Manifest V3 Compliance**: Proper separation of concerns between service worker and DOM environments for reliable screenshot processing
- **Three-Layer Processing**: Content script selection → Background script capture → Editor tab processing for optimal performance
- **Device Pixel Ratio Handling**: Accurate high-DPI display support with proper coordinate scaling for crisp screenshots
- **Memory-Safe Implementation**: Efficient image processing with automatic cleanup and failsafe timeout mechanisms

### YouTube Screenshot Integration
- **YouTube-Specific Features**: Enhanced screenshot functionality for YouTube videos with specialized capture options
- **Multiple Export Options**: Copy to clipboard, download to file, or both options available for YouTube screenshots
- **Filename Optimization**: Intelligent filename generation using video titles without redundant "screenshot" suffix
- **SPA Navigation Support**: Handles YouTube single-page application navigation with persistent screenshot buttons

### Technical Implementation
- **Chrome API Integration**: Uses `chrome.tabs.captureVisibleTab` API for high-quality full-screen capture
- **Canvas-Based Processing**: Advanced canvas operations for precise image cropping and editing in DOM environment
- **Storage Management**: Efficient temporary storage system using `chrome.storage.local` for screenshot data transfer
- **Event Delegation**: Proper event handling with cleanup mechanisms and memory leak prevention

### User Interface Features
- **Dark Theme Editor**: Full-featured screenshot editor with dark theme and purple branding consistency
- **Interactive Toolbar**: Comprehensive editing tools with intuitive icon-based interface
- **Real-Time Preview**: Live preview of edits with immediate visual feedback
- **Export Controls**: Multiple export formats and quality options for different use cases

### Settings Integration
- **Global Settings**: Screenshot tool integration in main extension settings with toggle controls
- **Custom Shortcuts**: Configurable keyboard shortcuts with visual key combination display
- **Platform Detection**: Automatic Mac/Windows detection for appropriate shortcut key labels
- **Settings Persistence**: Reliable settings storage and retrieval across browser sessions

### Web Accessible Resources
- **Editor Resources**: Added `screenshot-editor.html` and `js/screenshot-editor.js` to manifest web accessible resources
- **Content Script Integration**: Added `js/screenshot-selector.js` to universal content script loading pattern
- **Cross-Origin Support**: Proper resource accessibility for screenshot functionality across all domains

### Performance Optimizations
- **Lazy Loading**: Screenshot components only load when activated to minimize resource usage
- **Efficient Processing**: Optimized image processing algorithms for fast screenshot generation
- **Memory Management**: Automatic cleanup of temporary data and proper garbage collection
- **Failsafe Mechanisms**: 30-second timeout protection and error recovery systems

## [7.0.4] - 2025-07-14
### Bug Fixes and Performance Improvements
- **Minimal Reader Button Fix**: Resolved disappearing button issue affecting minimal reader interface functionality
- **Console Spam Reduction**: Eliminated excessive logging from screenshot editor, quick actions shortcuts, and storage protection system
- **Audio System Improvements**: Enhanced audio preview system functionality with better reliability and user experience
- **Pomodoro Timer Enhancements**: Improved timer reset functionality with proper state management and chronometer handling
- **Gmail Email Pinner Stability**: Fixed email extraction function preventing data corruption in pinned emails storage
- **Cookie Monitoring Optimization**: Reduced cookie logging frequency and improved conditional reporting for better performance

### Technical Enhancements
- **Storage Protection**: Minimized notification spam while maintaining data integrity protection
- **DOM Snapshot Utility**: Updated cleanup mechanisms for better memory management
- **Global Shortcut Manager**: Improved keyboard shortcut handling across all pages
- **Context Menu Testing**: Added comprehensive console-based test suite for context menu validation
- **Readability System**: Enhanced content extraction algorithms in readability engine
- **CSS Optimization**: Updated minimal reader CSS for better visual consistency

### Developer Experience
- **Debug Monitoring**: Improved logout debug monitoring with better event tracking
- **Background Script**: Enhanced service worker performance and reliability
- **Documentation Updates**: Comprehensive documentation improvements in memory bank system

## [7.0.3] - 2025-07-10
### Major New Feature: Gmail Email Pinner System
- **Complete Email Pinner Implementation**: Full-featured email pinning system for Gmail with pin icons, popup management, and intelligent age warnings
- **Gmail Pin Icons**: Pin icons appear on individual Gmail emails (not inbox) with visual state indicators (purple for unpinned, red for pinned)
- **Dedicated Email Management Popup**: Standalone popup window (550x650px) for managing pinned emails with sort-by-age functionality (oldest first)
- **Three-Tier Age Warning System**: Visual age indicators with normal state (0-1 days), orange warning (2-4 days), and red critical warning (5+ days) using dashed borders
- **Email Data Extraction**: Comprehensive extraction of email title, sender, timestamp, and direct Gmail URLs for quick access
- **Cross-Component Synchronization**: Real-time sync between Gmail content script and popup interface with automatic pin state updates

### Gmail Tools Integration
- **Settings Integration**: Email Pinner toggle in Gmail Tools section of extension settings with default enabled state
- **Popup Counter Badge**: Envelope icon with dynamic counter badge showing number of pinned emails in main extension popup
- **Smart URL Detection**: Works only on individual Gmail emails (URLs with long email IDs) and excludes main inbox view
- **Gmail SPA Compatibility**: Handles Gmail single-page application navigation with automatic re-injection of pin icons

### Technical Implementation
- **Chrome Storage Management**: Uses `chrome.storage.local` with `gmailPinnedEmails` key for optimal performance and data persistence
- **CSP Compliance**: Fully compliant with Content Security Policy using external JavaScript files and proper event delegation
- **Email Age Calculation**: Intelligent timestamp parsing for Gmail format with fallback handling and accurate days-ago calculation
- **DOM Cleanup Integration**: Proper cleanup of pin icons and event listeners when feature is disabled
- **Memory Management**: Event listener cleanup, mutation observer management, and proper resource disposal

### Web Accessible Resources
- **New Popup Files**: Added `alerts/email-pinner.html` and `alerts/email-pinner.js` to manifest web accessible resources
- **Content Script Integration**: Added `settings/gmail-email-pinner.js` to Gmail content script loading pattern
- **Popup Interface Styling**: Dark theme with purple branding consistency and responsive layout design

### User Experience Enhancements
- **Visual Age Warnings**: Subtle dark background with colored dashed borders (orange for 2-4 days, red for 5+ days)
- **Inline Timestamp Layout**: Age warnings positioned to the right of email timestamps for optimal space usage
- **Compact Button Layout**: Open Email and Unpin buttons arranged horizontally with proper spacing and hover effects
- **Empty State Handling**: Informative messages when no emails are pinned with call-to-action guidance

## [7.0.2] - 2025-07-08
### Performance Improvements: Enhanced Readability System
- **Readability Engine Optimization**: Improved content extraction performance with refined algorithms in `js/readability-extractor.js`
- **Advanced Content Cleaner**: Enhanced content cleaning pipeline with better noise reduction in `js/readability/advanced-cleaner.js`
- **Content Scoring Algorithm**: Optimized content scoring system for more accurate main content identification in `js/readability/content-scorer.js`
- **HTML Entity Processing**: Improved HTML entity handling for better text extraction in `js/readability/html-entities.js`
- **HTML Serialization**: Enhanced HTML serialization for cleaner output in `js/readability/html-serializer.js`
- **Link Processing**: Improved link processing and preservation in `js/readability/link-processor.js`
- **Minimal Reader Interface**: Enhanced user interface responsiveness in `settings/minimal-reader-interface.js`

### Technical Enhancements
- **Readability Pipeline**: Streamlined content extraction pipeline for faster processing
- **Memory Optimization**: Reduced memory footprint during content extraction operations
- **Error Handling**: Enhanced error handling throughout the readability system
- **Performance Monitoring**: Improved performance tracking for content extraction operations

## [7.0.1] - 2025-07-08
### Bug Fixes: Google Search Business Listings Detection
- **Enhanced Google Search Detection**: Improved coordinate-based detection for Google search pages with business listings
- **Business Listing Validation**: Added `hasCoordinatesInHash()` function to detect coordinate data in URL hash fragments (mv:[[)
- **Review Analysis Robustness**: Enhanced Google search review analysis to halt gracefully when no business listings are detected
- **Search Page Classification**: Better differentiation between regular search pages and local business search results
- **Error Handling**: Improved error messages when review analysis cannot proceed on non-business search pages
- **Popup Detection Logic**: Updated Google search detection to include both `sca_esv` and `mv:[[` parameters for comprehensive business listing identification

### Technical Improvements
- **Coordinate Detection**: Added pattern matching for `mv:[[` coordinate data in URL hash fragments
- **Review Analysis Guard**: Implemented early exit for review analysis when coordinates are not present
- **Message Passing**: Enhanced communication between content script and popup for analysis halt scenarios
- **Progress Tracking**: Better progress section visibility management during failed analysis attempts

## [7.0.0] - 2025-07-07
### Major Release: Comprehensive Productivity and Audio System Integration
This is a major release introducing five complete new subsystems that transform the SEO Time Machines into a comprehensive productivity platform. Version 7.0.0 represents the largest expansion of the extension with over 114 commits focusing on productivity, reading enhancement, video tools, and infrastructure improvements.

### Audio System: Professional Audio Architecture
- **12+ AWS-hosted Sound Library**: Complete audio library with 28 professionally curated sounds hosted on `lstk-music.s3.ap-southeast-2.amazonaws.com` plus 2 local audio files for comprehensive audio experiences
- **Offscreen Audio Architecture**: Manifest V3 compliant audio system using offscreen documents for reliable audio playback across all browser contexts
- **Real-time Volume Control**: Separate volume controls for notifications (0-100%) and chronometer ticking with immediate audio feedback and debounced storage updates
- **Sound Preview System**: Intelligent preview system with regular sounds playing once and ticking sounds playing 3-tick sequences for optimal user experience
- **Audio Storage Management**: Strategic use of `chrome.storage.local` for audio settings ensuring optimal performance and avoiding sync storage limitations

### Complete Pomodoro Timer System: Advanced Productivity Management
- **Full Pomodoro Implementation**: Complete work/break cycle system with configurable durations (work: 25min, short break: 5min, long break: 15min) and cycle tracking (default: 8 cycles)
- **Chronometer Integration**: Seamless background ticking with chronometer-specific volume control, optional break period ticking, and cross-window ownership indicators
- **Website Blocking System**: Declarative Net Request API implementation with priority-based rule management, default blocked sites (social media, entertainment), and whitelist override system
- **Custom Blocked Page**: Professional blocked page experience with real-time timer display, progress bars, and automatic page closure when timer completes
- **Todo Management Integration**: Complete task management system with CRUD operations, task reordering, completion tracking, and timer-task synchronization
- **Advanced Timer Controls**: Start/pause/resume/stop functionality with state persistence across browser restarts and multi-window synchronization

### Professional Minimal Reader System: Enhanced Reading Experience
- **Universal Content Extraction**: Advanced readability algorithms that transform any webpage into clean, distraction-free reading experiences with intelligent content scoring
- **Multiple Reading Modes**: Focus mode with visual concentration aids, Stop Word Fading (55+ English stop words), and customizable Focus Ruler with color picker
- **Document Outline Generation**: Auto-generated navigation from H1-H6 headings with clickable outline, visual hierarchy, and smooth scrolling
- **Theme System**: Professional themes (White, Sepia, Dark) with typography controls, font size adjustment (12-32px), and font weight options
- **Advanced Settings**: Floating action button with user-selectable positioning, reading time calculation, meta information display, and comprehensive localStorage synchronization
- **Cross-Site Compatibility**: Works universally across all websites with intelligent blacklist system for video platforms and frame detection

### Comprehensive YouTube Tools Suite: Video Content Management
- **Advanced Ad Blocking**: Multi-strategy YouTube ad skipping with 5 different fallback methods including API cancelPlayback, skip button automation, and fast-forward techniques
- **Professional Screenshot System**: Multiple capture modes (save/clipboard/both), format support (PNG/JPEG/WebP), smart filename generation, and keyboard shortcut activation (P key)
- **Thumbnail Management**: Multiple quality extraction (maxres/hq/sd/mq), interactive viewer popup, URL management, and direct thumbnail access
- **Universal Video Speed Controller**: Cross-platform speed control for YouTube, Vimeo, and all video content with Speed Guardian system, keyboard shortcuts (V/S/D/Z/X/G keys), and visual overlay controls
- **YouTube Embed URL Scraper**: Batch processing tool for extracting embed URLs from channel pages with range selection, multiple format support, and CSV export functionality
- **Bridge Architecture**: Sophisticated Chrome storage to localStorage bridge system overcoming content script limitations for full YouTube DOM access

### Comprehensive Alert and Notification System: Time Management
- **Professional Alert Manager**: Dark-themed alert creation interface with time-based scheduling, repeat options (Once/Daily/Weekdays/Weekly), and live badge counting
- **Multi-Modal Notifications**: DOM popups, audio notifications, browser notifications, and dedicated notification windows for enhanced visibility
- **Intelligent Snooze System**: Multiple snooze intervals (1-30 minutes) with automatic Chrome alarm rescheduling and race condition prevention
- **Centralized Badge Management**: Priority-based badge system with color coding (red alerts, orange timers, purple Pomodoro) and real-time count updates
- **Audio Integration**: Full audio system integration with user-configurable alert sounds and volume controls
- **Cross-Tab Synchronization**: Alert functionality works across multiple browser windows with proper state management and cleanup

### Infrastructure Revolution: Storage Architecture Migration
- **Complete Storage Migration**: Comprehensive migration from chrome.storage.sync to chrome.storage.local eliminating network dependency and quota limitations
- **Multi-Phase Implementation**: Four-phase migration covering core settings, event listeners, secondary storage, and critical data systems
- **Enhanced Reliability**: Eliminated sync conflicts, quota errors, and network-dependent failures while improving performance significantly
- **Hybrid Storage Strategy**: Strategic separation with main settings in local storage and specialized audio settings for privacy
- **Zero-Downtime Migration**: Seamless transition with complete data preservation and backward compatibility
- **Technical Achievement**: 18 core files updated with comprehensive event listener migration and storage key consolidation

### User Experience and Interface Enhancements
- **Universal Popup Standards**: Consistent purple brand theming (#7C3AED), dark theme integration, and ESC key support across all new interfaces
- **Draggable Interface System**: All popups support drag functionality with position memory and professional styling
- **Memory Management**: Comprehensive DOM cleanup requirements with proper event listener removal and timeout clearing
- **Settings Integration**: All new features fully integrated with centralized settings system including toggles, export/import, and real-time updates
- **Cross-Platform Compatibility**: Enhanced compatibility across different operating systems and browsers with proper fallback mechanisms

### Technical Architecture Improvements
- **Manifest V3 Compliance**: Full compliance with latest Chrome extension standards including service workers, declarative net request, and offscreen documents
- **Content Script Optimization**: Improved content script loading patterns with conditional injection based on URL matching
- **Message Passing Enhancement**: Sophisticated inter-component communication system with proper error handling and timeout management
- **Performance Optimization**: Throttled operations, lazy loading, and efficient DOM manipulation for optimal performance
- **Security Enhancements**: Content Security Policy compliance, TrustedTypes implementation, and secure injection patterns

### Major Feature Statistics
- **Total Commits**: 114 commits
- **New Subsystems**: 5 complete new feature systems (Audio, Pomodoro, Reader, YouTube, Alerts)
- **File Changes**: Over 80 files modified including 18 core architecture files
- **Settings Integration**: 60+ new settings with full export/import support
- **Audio Files**: 30 total audio files (28 AWS-hosted + 2 local)
- **Storage Migration**: Complete architectural overhaul from sync to local storage
- **UI Components**: 15+ new popup interfaces with unified design standards

This release represents a fundamental transformation of the SEO Time Machines from a specialized Google data extraction tool into a comprehensive productivity platform for SEO professionals and web developers, while maintaining all existing functionality and adding significant new capabilities.

## [6.30.0] - 2025-07-02
### Video Speed Controller: Comprehensive Video Playback Control System
- **Advanced Video Speed Control**: Implemented comprehensive video speed controller with keyboard shortcuts for precise playback control across all video platforms
- **Smart Overlay System**: Always-on floating speed display overlay with customizable opacity and professional purple-themed styling following extension design standards
- **Keyboard Shortcut Integration**: Full keyboard shortcut system with customizable bindings for speed control (faster/slower), navigation (rewind/advance), and quick actions (reset/fast mode)
- **Settings Integration**: Complete integration with extension settings system including opacity controls, start hidden option, speed memory, and blacklist management
- **Cross-Platform Compatibility**: Works on all video platforms including YouTube, Vimeo, and embedded videos with intelligent DOM detection and cleanup

### Enhanced Video Controller User Experience
- **Professional Visual Design**: Purple-themed floating overlay with gradient backgrounds, rounded corners, and smooth animations following extension branding standards
- **Smart Speed Memory**: Optional speed memory system that remembers playback speed across videos and browser sessions using Chrome storage sync
- **Intelligent Positioning**: Automatic positioning system that adapts to different video player layouts and maintains visibility during playback
- **Blink Feedback System**: Visual feedback system that briefly highlights the controller when speed changes occur for immediate user confirmation
- **Blacklist Management**: Comprehensive blacklist system allowing users to exclude specific websites from video speed control functionality

### Technical Architecture Improvements
- **YouTube Ads Skipper Priority System**: Intelligent initialization system that waits for YouTube ads skipper to complete before initializing video speed controller to prevent conflicts
- **Memory Management**: Comprehensive cleanup system with proper event listener removal, timeout clearing, and DOM element cleanup preventing memory leaks
- **Settings Validation**: Enhanced settings system with proper checkbox handling, opacity controls, and real-time settings updates across all components
- **Conflict Resolution**: Advanced conflict detection and resolution system ensuring video speed controller works harmoniously with other extension features

### UTM Cleaner Enhancements and System Improvements
- **Temporary Disable Functionality**: Added Shift+click temporary disable feature for UTM Cleaner allowing users to quickly bypass cleaning on specific pages
- **Enhanced Popup Functionality**: Improved UTM Cleaner popup with better user experience and clearer feedback for temporary disable operations
- **Sponsored Highlighter Detection**: Enhanced sponsored content detection to include "Promoted" text patterns in addition to existing detection methods
- **Notification System Refactoring**: Complete refactoring of notification system for enhanced user feedback and improved error handling across all extension components

### Settings System Enhancements
- **Checkbox Handling Improvements**: Enhanced settings system with proper boolean handling for checkbox inputs ensuring consistent behavior across all settings
- **Opacity Control Integration**: Real-time opacity control updates with immediate visual feedback and proper settings persistence
- **General Tools Integration**: Video Speed Controller fully integrated into General & Browser Tools accordion section with proper select all/deselect all functionality
- **Settings Validation**: Improved settings validation and error handling with better fallback mechanisms for corrupted or missing settings

## [6.29.0] - 2025-06-28
### Enhanced Settings Management and Data Integrity
- **Comprehensive Settings Export/Import System**: Enhanced export system to v2.0 format with complete coverage of all extension components including Mass Unsubscribe whitelist, HTML cleaner settings, keyboard shortcuts, profile configurations, and location settings
- **Profile Settings Integration**: Full integration of profile settings into export/import system ensuring all saved profiles are preserved during backup operations and can be restored across devices with complete settings configurations
- **Mass Unsubscribe Whitelist Integration**: Full integration of Mass Unsubscribe whitelist into settings export/import system ensuring whitelist preservation during backup operations and cross-device synchronization
- **Settings Validation and Recovery**: Enhanced settings import with validation for all data types including profile data, whitelist data, HTML cleaner options, and proper error handling for corrupted or missing entries
- **Default Settings Standardization**: Ensured all settings components use consistent default values and proper fallback mechanisms when Chrome storage access fails

### Advanced Storage Key Management
- **Complete Storage Coverage**: Export/import now handles all Chrome storage keys including `gmbExtractorSettings`, `htmlCleanerSettings`, `copyElementShortcut`, `colorpickerShortcut`, `quickActionsOrder`, `massUnsubscribeWhitelist`, and location settings
- **Cross-Component Synchronization**: Enhanced real-time settings synchronization across all extension components with proper message passing for immediate effect
- **Storage Error Handling**: Comprehensive error handling for all storage operations with graceful fallbacks and user-friendly messaging
- **Settings Persistence**: Improved settings loading and saving system with detailed debugging and individual element validation

### Import/Export Function Enhancements
- **Backward Compatibility**: Import system supports both v1.0 legacy format and new v2.0 comprehensive format with automatic detection and proper migration
- **Enhanced User Feedback**: Detailed success messages showing exactly what was exported/imported with statistics for settings count, tracked domains, HTML cleaning options, shortcuts, and whitelist entries
- **Metadata Integration**: Export includes comprehensive metadata with version information, extension details, and export statistics for better tracking and validation
- **Merge Strategies**: Smart merging of settings during import with duplicate handling and validation for all data types

### Technical Architecture Improvements
- **Default Settings Framework**: Standardized `defaultSettings` object across all components ensuring consistent behavior and proper initialization
- **Storage Key Consolidation**: Proper organization of settings across different Chrome storage keys with comprehensive coverage in export/import functionality
- **Error Recovery Enhancement**: Robust error handling in all settings operations with fallback to default settings when Chrome storage access fails
- **Cross-Platform Compatibility**: Enhanced compatibility across different operating systems and browsers with proper fallback mechanisms

## [Unreleased]
### Fixed
- **Review Analysis Sponsored Domain Exclusion**: Applied tracked domains system concepts to exclude sponsored results from ALL review analysis calculations across multiple views
  - **Files Updated**: `js/review-analysis.js`, `js/google-search-extractor.js`, `js/prolist-extraction.js`, `js/popup.js`
  - **Key Changes**:
    - Added enhanced sponsored detection logic using multiple methods (URL patterns, text detection, CSS classes)
    - Excluded sponsored results from top 3 position calculations across all analysis types
    - Updated aggregate data calculations to use organic businesses only
    - Modified rating distribution and category breakdown to exclude sponsored listings
    - Added `sponsoredExcluded` counter to track how many sponsored results were filtered out
    - Updated all competitive intelligence functions to use organic data only
  - **Impact**: Review analysis now provides accurate organic business metrics, excluding sponsored ads from position calculations and aggregate statistics
  - **Views Affected**: Google Search (`/search`), Maps Search (`/maps/search`), ProList (`/localservices/prolist`), Multiple Listings analysis

## [6.28.0] - 2025-01-28
### Advanced Settings Management: Select All/Deselect All Toggle System
- **Comprehensive Toggle Control**: Added Select All/Deselect All toggles to all accordion sections in Extras settings (Google & Search Tools, Business & GMB Tools, Developer & Content Tools, General & Browser Tools, Gmail Tools)
- **Smart State Management**: Intelligent toggle state tracking that automatically reflects current status - shows ON when all individual toggles are active, OFF when any are inactive
- **One-Click Section Control**: Click select all toggle to instantly turn ALL options in that section on or off, providing rapid configuration of related feature groups
- **Real-time State Updates**: Select all toggles automatically update when individual toggles are changed manually, maintaining accurate state representation at all times
- **Visual Design Enhancement**: Distinctive purple-themed styling with gradient top border and enhanced contrast to clearly differentiate master toggles from individual settings

### Enhanced Settings User Experience
- **Rapid Feature Configuration**: Quickly enable or disable entire feature categories with single click, dramatically improving settings workflow efficiency
- **Intuitive State Indication**: Clear visual feedback showing whether all, some, or no features in each section are active through master toggle state
- **Seamless Integration**: Perfect integration with existing settings system - no interference with individual toggle functionality or settings storage
- **Professional Visual Hierarchy**: Enhanced styling creates clear visual separation between master controls and individual settings for improved usability
- **Cross-Section Consistency**: Uniform implementation across all accordion sections providing consistent user experience throughout settings interface

### Technical Architecture Improvements
- **MutationObserver Integration**: Advanced DOM monitoring system tracks individual toggle changes and updates master toggle states automatically
- **Event Handler Optimization**: Sophisticated event handling prevents conflicts between master toggles and individual toggles while maintaining responsive performance
- **Settings Storage Isolation**: Master toggles operate as pure UI controls without creating additional storage overhead or interfering with existing settings data
- **State Synchronization System**: Robust state management ensures master toggles accurately reflect aggregate state of their controlled individual toggles
- **Performance Optimized**: Efficient implementation with minimal resource usage and smooth animations for professional user experience

## [6.27.0] - 2025-01-28
### Context Menu Debug Fix: Complete Forced Cleanup System
- **Root Cause Resolution**: Fixed persistent context menu interference from Extras features causing failures that required page refresh AND extension reload to function
- **Forced Cleanup Implementation**: Added comprehensive forced cleanup of ALL interfering Extras features as first step in context menu execution before normal cleanup
- **Drag Select Links Interference Fix**: Resolved global event listeners with capture: true and persistent preventContextMenu functions blocking right-click events
- **Images Action Interference Fix**: Fixed global listeners (keydown, mousemove, mouseup, touchmove, touchend) persisting after Images action completion
- **3-Step Execution Pattern**: Implemented Force Extras cleanup → Regular cleanup → Direct execution pattern for reliable context menu operation
- **Enhanced Context Menu Functions**: Updated executeWordCounter, executeCopyElement, executeCleanSelectedContent with identical forced cleanup patterns
- **Complete Documentation**: Updated memory bank with detailed debugging guide, golden rules for context menu development, and prevention checklists
- **Development Guidelines**: Added comprehensive checklists for creating new context menu items and Quick Actions to prevent future interference issues

### Technical Architecture Improvements
- **Universal Interference Prevention**: Force cleanup pattern removes window.isKeyPressed, window.isDragging, and contextmenu event listeners
- **Images Action Cleanup**: Comprehensive removal of window.imagesActionEscapeListener and all persistent global listeners with proper cleanup
- **Memory Bank Integration**: Complete debugging documentation in context-menu-debugging.md with fix details and 12-step prevention protocol
- **Golden Rules Enhancement**: Updated context menu golden rules with interference section and mandatory testing protocols for all new development

## [6.26.0] - 2025-01-28
### Mass Unsubscribe: Improved Gmail Navigation Detection
- **Inbox-Only Activation**: Mass Unsubscribe button now only appears in main Gmail inbox views (e.g., `/mail/u/1/#inbox`) and automatically hides when viewing individual emails or other Gmail sections
- **Smart URL Detection**: Robust pattern matching that supports all Gmail account numbers (`u/0`, `u/1`, `u/2`, etc.) while excluding individual email threads and other Gmail views
- **Automatic Navigation Handling**: Button dynamically appears/disappears based on Gmail's single-page application navigation using both hash change detection and DOM mutation monitoring
- **Enhanced User Experience**: Prevents Mass Unsubscribe functionality from appearing in inappropriate contexts where it wouldn't work properly
### Gmail Thread Expander: Complete Email Visibility with Sticky Timestamps
- **Automatic Thread Expansion**: Intelligent system that detects Gmail thread count buttons (.adx) and automatically expands conversation threads for complete email visibility
- **Sequential Message Expansion**: Professional timing system that expands individual messages (.hI, .ig) with optimized delays preventing Gmail interface overload
- **Sticky Timestamp Enhancement**: Revolutionary sticky positioning system for Gmail timestamps with beautiful purple gradient styling and enhanced readability
- **Purple Gradient Styling**: Premium timestamp styling with linear gradient backgrounds (purple to violet), rounded corners, shadows, and smooth hover effects
- **Intelligent Positioning**: Sticky timestamps stay visible at top of viewport while scrolling through long emails, switching contextually between messages
- **Auto-Scroll Integration**: Automatic scroll to top functionality after thread expansion providing clean starting position for email review workflow

### Enhanced Gmail Timestamp Experience
- **Professional Visual Design**: Beautiful purple-themed timestamps with gradient backgrounds, white text, rounded corners, and subtle shadow effects
- **Hover Interactions**: Smooth hover animations with transform effects and enhanced shadows for interactive feedback
- **Dark Theme Compatibility**: Optimized styling for Gmail's dark theme with appropriate color adjustments and contrast optimization
- **Print View Support**: Proper print media queries ensuring timestamps display correctly in printed email documents
- **Backdrop Blur Effects**: Modern backdrop blur styling for premium visual appearance on supported browsers
- **Responsive Sizing**: Adaptive sizing and positioning ensuring timestamps work across different screen sizes and Gmail layouts

### Advanced Technical Implementation
- **Mutation Observer Monitoring**: Sophisticated DOM monitoring that detects dynamically loaded Gmail content and processes both threads and timestamps automatically
- **Thread Deduplication System**: Intelligent tracking system using unique thread identifiers preventing duplicate processing of already expanded threads
- **CSS Injection Management**: Dynamic CSS style injection with proper cleanup ensuring clean enable/disable functionality without style conflicts
- **Performance Optimization**: Debounced processing system with smart timing controls reducing resource usage on large Gmail inboxes
- **Error Handling**: Comprehensive error handling for DOM access, click events, CSS injection, and edge cases with graceful degradation
- **Real-time Settings Integration**: Dynamic enable/disable functionality through extension's message passing system requiring no page refresh for immediate effect

### Perfect Gmail Workflow Integration
- **Seamless Reverse Order Compatibility**: Designed to work perfectly with existing Reverse Gmail Order feature providing complete conversation visibility with newest messages first
- **Settings Integration**: Full integration into Gmail Tools accordion section with individual toggle control for user customization
- **Default Enabled State**: Feature defaults to enabled for immediate productivity enhancement while remaining easily togglable for user preference
- **Debug Mode Support**: Comprehensive debug logging integration providing detailed console output when debug mode is enabled for development and troubleshooting
- **Gmail Page Detection**: Precise Gmail page detection ensuring feature only activates on mail.google.com domains for targeted functionality

## [6.25.0] - 2025-01-28
### Enhanced Mass Unsubscribe: Complete Whitelist Management System
- **Comprehensive Whitelist Functionality**: Added complete whitelist management system allowing users to select and preserve important email senders during mass unsubscribe operations
- **Seamless Workflow Integration**: Implemented instant whitelist addition without confirmation dialogs - selected senders smoothly disappear from the popup with animated transitions
- **Professional Whitelist Management Interface**: Added settings cog button next to Mass Unsubscribe button opening dedicated whitelist management popup with select all/deselect all functionality
- **Persistent Whitelist Storage**: Integrated Chrome storage sync for cross-device whitelist synchronization with automatic loading and saving capabilities
- **Smart Filtering System**: Enhanced unsubscribe scanning to automatically exclude whitelisted senders from future operations with real-time count display
- **Visual Lightning Bolt Enhancement**: Added lightning bolt icon to Mass Unsubscribe button for improved visual impact and speed indication

### Advanced Whitelist User Experience
- **Real-time Visual Updates**: Live removal of whitelisted items from popup with smooth fade-out and slide animations providing immediate visual feedback
- **Dynamic Title Updates**: Popup title automatically updates showing remaining items count and total whitelisted senders for complete transparency
- **Intelligent Re-indexing**: Automatic re-indexing of remaining items ensures consistent functionality after whitelist additions
- **Enhanced Button Layout**: Professional dual-button interface with "Add to Whitelist" (green) and "Unsubscribe All" (purple) for clear action distinction
- **Empty State Management**: Professional empty state display in whitelist popup with helpful instructions for first-time users
- **Instant Action Processing**: Zero interruption workflow allowing rapid curation of important senders before mass unsubscribe operations

### Enhanced Gmail Interface Protection
- **Comprehensive Navigation Filtering**: Advanced filtering system preventing Gmail sidebar navigation elements, label links, and interface components from appearing as unsubscribe opportunities
- **Multi-layer Interface Exclusion**: Enhanced detection excludes Gmail toolbar (.TK), header areas (.ar), navigation (.Cr), Google bar (.gb_), compose area (.z0), and InboxSDK elements
- **Email Row Validation**: Strict validation ensuring only legitimate unsubscribe buttons within actual email content areas are detected
- **Smart Unknown Sender Prevention**: Advanced validation requiring actual sender identification preventing "Unknown Sender" entries from appearing in popup
- **Text-based Detection Enhancement**: Improved backup detection method with comprehensive Gmail interface exclusions for cleaner scanning results

### Settings System Integration and Data Management
- **Complete Export/Import Support**: Added massUnsubscribeWhitelist to comprehensive settings export and import system ensuring whitelist preservation during backup operations
- **Cross-device Synchronization**: Full Chrome storage sync integration allowing whitelist to synchronize across all user devices automatically
- **Settings Validation and Recovery**: Enhanced settings import with validation for whitelist data and proper error handling for corrupted or missing whitelist entries
- **Export Metadata Enhancement**: Updated export system to include whitelist entry counts in export metadata and success notifications
- **Default Settings Integration**: Added whitelist to reset functionality ensuring clean defaults when resetting extension settings

### Technical Architecture and Performance
- **Advanced DOM Filtering**: Multi-criteria filtering system using CSS class detection, aria-label checking, and URL pattern matching for precise Gmail interface exclusion
- **Memory Efficient Storage**: Optimized storage operations with proper error handling and fallback mechanisms for reliable whitelist persistence
- **Session State Management**: Intelligent session memory tracking processed senders separately from persistent whitelist for optimal user workflow
- **Enhanced Error Recovery**: Comprehensive error handling for storage operations, DOM manipulation, and edge cases ensuring graceful degradation
- **Performance Optimized Scanning**: Efficient scanning algorithms with reduced false positives and improved accuracy for large Gmail inboxes

## [6.24.0] - 2025-01-27
### Critical Debug Mode Architecture Overhaul: Complete Conflict Resolution System
- **Comprehensive Debug Logic Redesign**: Complete architectural overhaul of debug mode system resolving all component conflicts and ensuring predictable behavior across all debug states
- **Universal Click Debugger State Management**: Enhanced Universal Click Debugger with intelligent state preservation and restoration, automatically enabling debug mode when activated and restoring original state when deactivated
- **Logging Utility Independence**: Fixed logging utility to work independently of secret developer mode, enabling debug mode toggle to function normally without requiring secret mode activation
- **Smart Conflict Resolution**: Implemented comprehensive conflict resolution where secret mode hide takes precedence over all other debug states, ensuring clean system behavior
- **Memory Bank Documentation**: Added complete debug mode architecture documentation with all 12 scenarios analyzed, precedence hierarchy, and conflict resolution rules

### Enhanced Universal Click Debugger Intelligence
- **Automatic Debug Mode Integration**: Universal Click Debugger now automatically enables debug mode when activated, providing seamless verbose logging experience
- **Original State Preservation**: Intelligent system saves original debug mode state before activation and restores it precisely when deactivated, respecting user preferences
- **Secret Mode Conflict Handling**: Universal Click Debugger gracefully handles secret mode hide events by clearing saved state and preventing restoration conflicts
- **Page Refresh State Protection**: Enhanced initialization logic prevents overwriting saved original state during page refreshes, maintaining session integrity
- **Comprehensive Storage Management**: Proper Chrome storage integration with local storage for temporary state and sync storage for persistent settings

### Logging Utility System Improvements
- **Global Console Control**: Enhanced logging utility now properly controls ALL console methods (log, error, warn, info, debug, trace, table, group, etc.) across entire extension
- **Simplified Debug Toggle**: Removed dual requirement system - debug mode toggle now works independently without requiring secret developer mode activation
- **Real-time Settings Updates**: Improved message passing system ensures debug mode changes take effect immediately across all content scripts and components
- **Hardcoded Log Restoration**: Fixed issue where hardcoded console.log statements in components like drag-select-links.js and contentScript.js were being suppressed inappropriately

### Secret Developer Mode Enhancements
- **Force Debug Disable Logic**: Secret mode hide now automatically disables debug mode and clears Universal Click Debugger state to prevent conflicts
- **State Cleanup Integration**: Enhanced secret mode with comprehensive cleanup logic that removes orphaned Universal Click Debugger state when hiding debug section
- **Settings Synchronization**: Improved integration with settings system ensuring debug mode toggle reflects actual state when secret mode is shown/hidden
- **Conflict Prevention**: Secret mode hide now takes absolute precedence over all other debug systems, ensuring clean disable functionality

### Technical Architecture Improvements
- **Precedence Hierarchy Implementation**: Established clear precedence system where Secret Mode Hide > Manual Toggle > Universal Auto-Enable > Universal Restore
- **Storage Key Standardization**: Unified storage architecture using proper gmbExtractorSettings.debugMode for all components instead of inconsistent key names
- **Message Passing Enhancement**: Improved cross-component communication with proper updateSettings messages for real-time debug mode changes
- **Error Handling Robustness**: Added comprehensive error handling for all storage operations, message passing, and state management with graceful fallbacks

### Comprehensive Scenario Testing
- **12-Scenario Validation**: Complete testing matrix covering normal flows, Universal Debugger flows, secret mode interactions, and complex multi-step sequences
- **Conflict Resolution Validation**: Verified all potential conflict scenarios including Universal active + secret hide, restoration after hide, and complex state sequences
- **State Consistency Verification**: Ensured all components maintain consistent behavior and no circular logic or competing state management
- **Memory Management**: Enhanced cleanup procedures for all debug components preventing memory leaks and state pollution

### Developer Experience Improvements
- **Debug Mode Predictability**: Debug mode now behaves predictably in all scenarios with clear rules for when logging is enabled/disabled
- **Transparent State Management**: All debug state changes now provide clear console feedback when debug mode is enabled, helping developers understand system behavior
- **Documentation Architecture**: Complete memory bank documentation ensures future developers understand the complex interactions and can extend safely
- **Testing Framework**: Established comprehensive testing scenarios for validating debug mode behavior across all possible state combinations

## [6.23.0] - 2025-01-27
### Secret Developer Mode: Hidden Debugging System
- **Secret Activation Method**: Hidden developer mode accessible via 8 rapid clicks on "Show notifications" toggle within 2 seconds for developer debugging access
- **Persistent Debug State**: Secret mode state persists across extension reloads and browser restarts using Chrome local storage for true developer convenience
- **Two-Factor Debug Authentication**: Console logging only enabled when both secret mode is active AND debug toggle is manually enabled ensuring complete privacy protection
- **Global Logging Control**: Comprehensive logging system integration across all extension components with strict security controls preventing accidental debug exposure
- **Zero-Trace Security**: Completely silent operation with no console messages, warnings, or hints until deliberately activated by developers

### Enhanced Universal Click Debugger
- **Advanced Cross-Page Activity Detection**: Comprehensive monitoring of tab creation, navigation changes, form submissions, and cross-domain interactions for thorough debugging
- **Real-Time Debug Interface**: Professional floating debug panel with color-coded messages, timestamp tracking, and session persistence for advanced debugging workflows
- **Comprehensive Interaction Testing**: Click testing capabilities for any page element, form monitoring, navigation detection, and external link tracking
- **Secret Mode Integration**: Universal Click Debugger only available when secret developer mode is active ensuring debugging tools remain hidden from end users
- **Session Persistence**: Debug activity counters and history persist across page loads and extension reloads for continuous development workflow

### Developer Security and Privacy Features
- **Complete Secrecy**: No documentation in visible UI, no hints in console output, and no traces of existence until deliberately activated by developers
- **Local Storage Security**: Secret mode state stored in local storage (not synced) ensuring debugging capabilities remain device-specific and private
- **Automatic Security Reset**: New installations default to debugMode: false with automatic reset for existing installations without secret mode knowledge
- **Professional Debug Logging**: When enabled, provides comprehensive debugging information for all extension components with proper categorization and formatting
- **Developer Responsibility**: Secret functionality designed for development and debugging purposes only with no impact on end user experience

### Technical Architecture Improvements
- **Secret Toggle Implementation**: Advanced click counting system with timing windows and state persistence integrated seamlessly with existing settings architecture
- **Debug Mode Visibility Control**: Hidden #debugModeSection element with dynamic visibility based on secret activation and proper DOM attribute management
- **Global Component Integration**: All extension components updated to respect the two-factor authentication system for debug logging ensuring consistent behavior
- **Enhanced Error Handling**: Comprehensive error handling for secret mode activation, state persistence, and debug logging with graceful fallbacks
- **Memory Management**: Proper cleanup procedures for secret mode functionality ensuring no performance impact when debugging is not active

## [6.22.0] - 2025-01-27
### Enhanced UTM Tracking Cleaner: Advanced Domain Validation System
- **Automatic 404 Domain Validation**: Added comprehensive domain validation system that automatically checks manually entered domains in UTM whitelist for 404 errors
- **Real-time Domain Cleanup**: Domains returning 404 status are automatically removed from whitelist input when users press Enter, move to new line, or close the settings box
- **Dual Protocol Testing**: Smart validation system tests both HTTP and HTTPS versions of domains using cross-origin safe requests for accurate availability checking
- **Visual Feedback System**: Color-coded status messages with auto-clearing notifications provide real-time feedback during domain validation process
- **Smart Domain Processing**: Intelligent domain cleaning that removes protocols, www prefixes, and paths before validation for consistent processing
- **Manual Entry Focus**: Validation specifically designed for manually entered domains in main settings page, preserving button-added domains which are already validated

### Advanced Domain Validation Features
- **Cross-Origin Compatibility**: Uses no-cors mode for domain checking to work reliably across different websites and security contexts
- **Intelligent Error Handling**: Distinguishes between actual 404 errors and network/CORS issues to prevent false positives from domain removal
- **Debounced Validation**: Smart timing system prevents excessive validation requests during rapid typing or bulk domain entry
- **Status Message Management**: Professional status display with automatic clearing and color-coded feedback for validation results
- **Integration with Main Settings**: Seamlessly integrated into main settings page textarea for UTM cleaner whitelist domains

### Technical Architecture Improvements
- **Modular Validation System**: Reusable domain validation functions exposed globally for potential use in other extension features
- **Enhanced Error Recovery**: Comprehensive error handling with graceful fallbacks for network issues and validation failures
- **Memory Management**: Efficient validation processing with proper cleanup and resource management
- **Settings Synchronization**: Real-time integration with Chrome storage system for immediate whitelist updates
- **Performance Optimization**: Optimized validation timing to balance user experience with server load considerations

## [6.21.0] - 2025-01-27
### Major Gmail Enhancement: Complete Gmail Tools Suite
- **Gmail Sender Icons Integration**: Added comprehensive Gmail sender icons functionality displaying domain favicons and company information next to email messages for enhanced visual identification
- **Reverse Gmail Order Feature**: Revolutionary conversation ordering system that displays newest messages at the top of Gmail threads with automatic compose area focus for improved email workflow
- **Gmail Time Formatter**: Advanced Gmail timestamp customization with multiple format options, real-time preview, and preset configurations for personalized email time display

### Gmail Sender Icons: Visual Email Management
- **Smart Domain Detection**: Intelligent email parsing system that extracts sender domains and displays corresponding favicon icons with fallback systems for reliable icon display
- **Visual Domain Indicators**: Clean domain text display alongside icons showing sender company information in Gmail inbox and conversation views with professional styling
- **Advanced Icon Positioning**: Precise icon placement following Gmail's native UI patterns with optimal positioning for emails with and without labels or other Gmail elements
- **Comprehensive Fallback System**: Multi-tier fallback system including Google favicon service, UI Avatars service, and colored letter bubbles ensuring icons always display regardless of domain favicon availability
- **Icon Caching System**: Intelligent caching mechanism that stores processed icons to prevent redundant API calls and improve performance across large email volumes

### Reverse Gmail Order: Enhanced Conversation Navigation
- **CSS-Based Conversation Reversal**: Sophisticated CSS injection system that reverses Gmail conversation order using flexbox column-reverse for seamless visual flow
- **Intelligent Reply Button Integration**: Advanced click listeners for Reply, Reply All, and Forward buttons with automatic compose area focus and scroll positioning
- **Multi-Method Focus System**: Comprehensive focus management with multiple fallback strategies including modern Gmail compose areas, contenteditable divs, and keyboard shortcut simulation
- **Smart Timing Controls**: Optimized timing system with 150ms delays allowing Gmail to process native actions before applying custom focus management
- **Print View Support**: Complete print layout compatibility with proper conversation ordering and visual styling for email archiving and documentation

### Gmail Time Formatter: Advanced Timestamp Customization
- **Comprehensive Format System**: Professional date/time formatting with support for 15+ format specifiers including year, month, day, time, weekday, and AM/PM options
- **Real-time Format Preview**: Live preview system showing formatted timestamps as users type custom format strings with instant visual feedback
- **Preset Format Library**: Collection of 5 common format presets including ISO dates, US formats, European formats, and 12/24 hour time systems
- **Intelligent Date Parsing**: Advanced date string parsing supporting multiple languages including Japanese date formats with month/day character recognition
- **Dynamic Width Management**: Smart container width adjustment system that prevents layout shifts and maintains visual consistency across different timestamp lengths
- **Mutation Observer Optimization**: Lightweight DOM monitoring specifically tuned for Gmail's dynamic content loading with debounced processing for optimal performance

### Enhanced Gmail Integration Architecture
- **Gmail Tools Section Integration**: All features seamlessly integrated into dedicated Gmail Tools accordion section in extension settings with individual toggle controls
- **Settings System Integration**: Complete integration with extension's settings import/export system ensuring Gmail preferences are preserved during backup and restore operations
- **Real-time Settings Updates**: Dynamic settings management allowing users to enable/disable any Gmail feature instantly without page refresh through extension's message passing system
- **Dark Theme Compatibility**: Full support for Gmail's dark theme across all features with appropriate color adjustments and contrast optimization for accessibility
- **Performance Monitoring**: Efficient DOM scanning and processing with requestAnimationFrame optimization preventing UI blocking during large email batch processing

### Technical Architecture Improvements
- **Gmail-Specific Content Scripts**: Dedicated content script execution for Gmail domain with proper permissions and execution timing optimized for Gmail's dynamic loading behavior
- **CSS Isolation and Control**: Conditional CSS styling systems that only apply feature styles when enabled preventing layout interference when features are disabled
- **Robust Error Handling**: Comprehensive error handling for favicon loading failures, date parsing errors, network issues, and edge cases ensuring graceful degradation
- **Memory Management**: Proper cleanup procedures for event listeners, DOM modifications, mutation observers, and temporary elements with comprehensive reset functionality
- **Cross-Component Communication**: Enhanced message passing between content scripts, settings panels, and background services for seamless feature integration

## [6.20.3] - 2025-01-27
### Enhanced UTM Tracking Cleaner: CMS Admin Area Protection
- **WordPress Admin Protection**: Added comprehensive protection for WordPress admin areas including wp-admin dashboard, wp-login.php, wp-cron.php, wp-content/plugins, wp-content/themes, and wp-includes directories
- **Multi-CMS Admin Support**: Extended protection to popular CMS platforms including Drupal (/admin/, /user/login), Joomla (/administrator/), Magento (/admin_), Shopify (/admin), PrestaShop (/adminps/), TYPO3 (/typo3/), MediaWiki (/mw-config/), Ghost (/ghost/), Craft CMS (/admin), Laravel (/nova/, /horizon/, /telescope/), and Django (/admin/, /django-admin/)
- **Smart Path Detection**: Implemented intelligent pathname-based detection that preserves query parameters when navigating to or clicking links pointing to CMS admin areas
- **Enhanced Link Protection**: Extended link click protection to check destination URLs for CMS admin paths, ensuring admin functionality remains intact while maintaining privacy on regular websites
- **Debug Logging Integration**: Added detailed console logging when CMS admin areas are detected, providing transparency and debugging capabilities for advanced users
- **Generic Admin Pattern Support**: Included protection for common admin patterns like /backend/, /dashboard/, /panel/, /manage/, /control/, and /cp/ to cover custom admin implementations

### Technical Architecture Improvements
- **Dual Protection System**: Enhanced protection logic that checks both current page location and destination link paths for comprehensive CMS admin area coverage
- **Modular Detection Function**: Created dedicated isCMSAdminArea() function for clean code organization and easy maintenance of admin path definitions
- **Enhanced Error Handling**: Improved error handling for URL parsing with proper fallbacks when destination URLs cannot be processed
- **Performance Optimization**: Efficient path checking using startsWith() method for fast admin area detection without regex overhead

## [6.20.2] - 2025-01-26
### Enhanced Drag Select Links: WordPress-Compatible URL Copying and Click to Copy Improvements
- **WordPress-Ready URL Copying**: Added new "URLs Only" button to Drag Select Links that copies URLs as HTML links with proper line breaks for direct pasting into WordPress text editors
- **Rich Text Editor Compatibility**: Implemented dual-format clipboard copying (HTML + plain text) ensuring clickable links in rich text editors while maintaining plain text fallback for text fields
- **Visual Line Separation**: URLs are copied with `<br>` tags ensuring each link appears on its own line when pasted into WordPress or other rich text editors
- **Improved Click to Copy Functionality**: Refactored link copying mechanism to use textarea-based selection instead of span elements for more reliable clipboard operations
- **Enhanced Link Preparation**: Streamlined link preparation process removing unnecessary modifier key tracking and improving copy operation reliability
- **Better Error Handling**: Added comprehensive fallback mechanisms for clipboard operations ensuring compatibility across different browsers and environments

### Technical Architecture Improvements
- **Modern Clipboard API Integration**: Implemented advanced clipboard functionality using ClipboardItem with proper MIME type handling for HTML and plain text formats
- **Simplified Event Handling**: Removed complex key tracking logic and streamlined event listeners for better performance and reliability
- **Enhanced Element Management**: Improved DOM element handling for clipboard operations with proper cleanup and error handling
- **Cross-Platform Compatibility**: Enhanced compatibility across different operating systems and browsers with proper fallback mechanisms

## [6.20.1] - 2025-01-26
### Enhanced UTM Tracking Cleaner: Complete Privacy Protection with Advertising Platform Support
- **Complete Query Parameter Removal**: Enhanced UTM cleaner to remove ALL query parameters (everything after "?") from URLs instead of selective tracking parameter removal, ensuring comprehensive privacy protection
- **Post-Landing URL Cleaning**: Added automatic URL cleaning functionality that activates 2 seconds after landing on websites from ads, allowing proper attribution tracking while ensuring privacy
- **Comprehensive Advertising Platform Protection**: Extended domain protection to include Facebook, Instagram, and YouTube advertising systems alongside existing Google Ads protection
- **Smart Click-Based Cleaning**: Implemented intelligent link cleaning that only activates when clicking links, preserving advertising redirect functionality while cleaning final destination URLs
- **Enhanced Debug Logging**: Added comprehensive console logging system for troubleshooting and monitoring UTM cleaner operations with detailed source and destination tracking

### Advanced Advertising Platform Compatibility
- **Facebook Ads Integration**: Complete protection for Facebook advertising domains including facebook.com, fb.com, instagram.com, l.facebook.com, l.messenger.com, lm.facebook.com, and fbcdn.net
- **YouTube Ads Support**: Full compatibility with YouTube advertising system including youtube.com, youtu.be, and all YouTube advertising redirects and monetization systems
- **Google Ads Preservation**: Maintained comprehensive Google Ads support including googleadservices.com, googlesyndication.com, doubleclick.net, and all Google Search/Maps advertising
- **Cross-Platform Protection**: Unified protection system that works seamlessly across all major advertising platforms while maintaining proper attribution and conversion tracking

### Enhanced User Experience and Privacy Features
- **Intelligent Domain Detection**: Advanced domain detection system that automatically identifies and protects all major advertising platforms from URL cleaning interference
- **Post-Landing Privacy**: Automatic URL cleaning after landing on business websites ensures privacy while allowing advertising platforms to complete their tracking and attribution processes
- **Settings Panel Improvements**: Enhanced settings interface with better descriptions explaining the new click-based cleaning approach and comprehensive platform protection
- **Notification Integration**: Added subtle privacy notifications when URLs are automatically cleaned, providing user awareness of privacy protection activities

### Technical Architecture Improvements
- **Protected Domain Framework**: Implemented comprehensive protected domain system with centralized domain management for Google, YouTube, Facebook, and Instagram platforms
- **Enhanced Error Handling**: Added robust error handling for URL parsing failures, storage operations, and settings management with comprehensive fallback mechanisms
- **Improved Settings Persistence**: Enhanced settings loading and saving system with detailed debugging, timeout handling, and individual element validation
- **Memory Management**: Optimized event listener management and cleanup procedures for improved performance and reliability across all supported platforms

## [6.20.0] - 2025-01-26
### Revolutionary Privacy and Productivity Features: YouTube Ads Skipper, UTM Tracking Cleaner, and Tracker Detection
- **YouTube Ads Skipper**: Comprehensive YouTube advertisement blocking system with intelligent ad detection, automatic skipping functionality, and detailed statistics tracking for improved browsing experience
- **UTM Tracking Cleaner**: Advanced URL cleaning system with 4 privacy levels, smart parameter stripping, copy-clean functionality, and click-through cleaning for enhanced privacy protection
- **Tracker Detection Tool**: Professional website analytics tool that identifies and analyzes tracking scripts, social media trackers, advertising networks, and privacy concerns with detailed reporting and CSV export capabilities

### Enhanced YouTube Ads Management System
- **Multi-Method Ad Detection**: Sophisticated ad detection using multiple techniques including video duration analysis, ad indicator detection, and player state monitoring for reliable ad identification
- **Automatic Skip Functionality**: Intelligent ad skipping with customizable delay settings, proper timing controls, and smooth user experience without interrupting legitimate content
- **Comprehensive Statistics Tracking**: Detailed tracking of ads skipped with count display, reset functionality, and persistent statistics storage for user awareness and productivity measurement
- **Advanced Configuration Options**: User-configurable settings including enable/disable toggle, skip delay customization, and statistics management with reset capabilities
- **Manifest Integration**: Proper YouTube domain integration with content script execution in MAIN world for reliable ad detection and interaction capabilities

### Advanced UTM Tracking Privacy Protection
- **Multi-Level Cleaning System**: Four distinct privacy levels from basic UTM removal to comprehensive tracking parameter elimination including Facebook, Google, and affiliate tracking parameters
- **Smart Copy-Clean Functionality**: Automatic URL cleaning when copying links to clipboard, ensuring shared URLs are privacy-friendly and professional
- **Click-Through URL Cleaning**: Real-time URL cleaning when clicking external links, preventing tracking parameter forwarding while maintaining navigation functionality
- **Configurable Privacy Settings**: User-customizable privacy levels with detailed descriptions, recommended defaults, and easy configuration through dedicated settings panel
- **Background Processing**: Efficient background processing with proper manifest permissions and content script integration for seamless operation

### Professional Tracker Detection and Analysis
- **Comprehensive Tracker Database**: Extensive database of 200+ tracking technologies including advertising networks, analytics platforms, social media trackers, and privacy-invasive technologies
- **Advanced Detection Algorithms**: Multi-method detection using script analysis, network request monitoring, DOM element inspection, and behavioral pattern recognition
- **Detailed Reporting System**: Professional reporting interface with tracker categorization, duplicate detection, privacy impact analysis, and comprehensive statistics
- **Export and Documentation**: CSV export functionality, detailed tracker information with descriptions, and professional reporting for privacy audits and compliance
- **Visual Feedback Interface**: Draggable analysis panel with real-time results, color-coded severity indicators, and professional dark theme integration

### Enhanced Notification System and Background Services
- **Advanced Notification Framework**: Comprehensive notification system with background service integration, user preference management, and enhanced fallback mechanisms
- **Chrome Extension API Integration**: Proper notifications permission handling, background script messaging, and cross-component communication for reliable notification delivery
- **Settings Management Integration**: User-configurable notification preferences with enable/disable controls and seamless integration with existing settings system
- **Debug and Testing Capabilities**: Built-in testing functions for notification debugging, settings validation, and troubleshooting support for development and user support

### Technical Architecture Improvements
- **Enhanced Permissions Management**: Updated manifest permissions for notifications, improved background script capabilities, and proper YouTube domain integration
- **Robust Error Handling**: Comprehensive error handling across all new features with graceful fallbacks, user-friendly messaging, and detailed logging for debugging
- **Settings System Integration**: All new features properly integrated with existing settings import/export system, default settings management, and user preference persistence
- **Cross-Component Communication**: Enhanced message passing between content scripts, background services, and popup interface for seamless feature integration
- **Memory Management**: Proper cleanup procedures, event listener management, and performance optimization for all new features

### User Experience Enhancements
- **Seamless Feature Integration**: All new features integrate smoothly with existing Quick Actions system, settings management, and user interface design
- **Professional Visual Design**: Consistent dark theme integration, brand purple color scheme, and professional styling across all new components
- **Comprehensive Documentation**: Detailed feature descriptions, configuration guides, and user-friendly explanations for all privacy and productivity features
- **Default Configuration**: Intelligent default settings that prioritize user privacy while maintaining functionality, with easy customization options for advanced users

## [6.19.1] - 2025-01-26
### Feature Removal: New Tab Redirect System
- **Complete Feature Removal**: Entirely removed the new tab redirect functionality from the extension for streamlined operation
- **File Cleanup**: Deleted new-tab-redirect.js and new-tab-redirect.html files to reduce extension size and complexity
- **Manifest Updates**: Removed chrome_url_overrides configuration from manifest.json to eliminate new tab override behavior
- **Settings Cleanup**: Removed newTabRedirectEnabled and newTabRedirectUrl settings from the configuration system
- **UI Improvements**: Updated General & Browser Tools accordion section with "More Features Coming Soon" placeholder message
- **Documentation Updates**: Cleaned up README.md and changelog entries to remove all references to the removed functionality

### Technical Architecture Improvements
- **Codebase Simplification**: Streamlined extension architecture by removing unused functionality and associated code
- **Storage Optimization**: Eliminated unnecessary settings storage for removed features to improve performance
- **Manifest Optimization**: Cleaned up Chrome extension manifest for better compliance and reduced permissions
- **UI Polish**: Enhanced extras settings interface with professional placeholder messaging for future features

## [6.19.0] - 2025-01-25
### Revolutionary Settings Page Organization: Accordion UI System
- **Collapsible Section Management**: Professional accordion components for organizing settings into logical, collapsible sections enhancing navigation and user experience
- **Structured Settings Categories**: Four main accordion sections including Google & Search Tools, Business & GMB Tools, Developer & Content Tools, and General & Browser Tools
- **Enhanced Visual Presentation**: Updated CSS styling for accordion elements with improved interaction design and smooth expansion/collapse animations
- **Intelligent Section Navigation**: JavaScript-powered accordion functionality ensuring smooth user experience with proper state management and section toggling
- **Improved Settings Accessibility**: Better organization of extension features making it easier for users to find and configure specific tools and options

### Technical Architecture Improvements
- **Clean UI Implementation**: Non-intrusive accordion implementation that maintains existing functionality while improving organization
- **Enhanced Settings Management**: Improved settings organization and user interface for better accessibility and navigation

## [6.18.0] - 2025-01-24
### Major Enhancement: UTM Builder Quick Action with Advanced Template Management
- **Comprehensive UTM Parameter Builder**: Professional UTM tracking URL generator with real-time URL construction and validation for campaign analytics and link tracking
- **Advanced Template System**: Robust template management with 6 preloaded marketing templates including Facebook Ads, Google Ads, YouTube Marketing, GMB Posts, Email Newsletter, and LinkedIn Ads
- **Smart Template Import/Export**: JSON-based template backup and restore system with merge strategies and duplicate handling for template management across environments
- **Intelligent Form Persistence**: Automatic saving and restoration of UTM parameter values and panel positioning for seamless workflow continuation across sessions
- **Interactive URL Copying**: Enhanced copy functionality with both button and direct URL clicking, featuring visual feedback with green highlighting and smooth animations
- **Professional Panel Interface**: Draggable and resizable panel with smooth drag functionality, viewport constraints, and consistent dark theme styling matching Quick Actions design system

### Enhanced User Experience Features
- **Typeahead Template Search**: Advanced template filtering with real-time search across all template fields including name, campaign, medium, source, term, and content
- **Visual Copy Feedback**: Comprehensive visual feedback system with button state changes, URL highlighting, scaling effects, and temporary success indicators for improved user interaction
- **Smart Panel Management**: Automatic panel expansion for dropdown visibility with intelligent positioning to prevent off-screen elements and smooth size restoration
- **Drag Functionality Optimization**: Fixed jumpy dragging behavior with proper offset calculations, viewport bounds checking, and smooth movement tracking for professional user experience
- **Template Management Controls**: Complete template lifecycle management with creation, editing, deletion, and bulk operations including default template restoration and smart merging
- **Persistent Settings**: LocalStorage integration for panel dimensions, position, and current UTM values with automatic restoration for consistent user preferences

### Technical Architecture Improvements
- **Robust Error Handling**: Comprehensive error handling for template operations, URL generation, and clipboard access with graceful fallbacks and user-friendly messaging
- **Memory Management**: Proper cleanup procedures for event listeners, DOM modifications, and temporary elements with comprehensive reset functionality
- **Clean DOM Integration**: Non-intrusive panel implementation with proper z-index management and escape key handling for seamless page integration
- **Modular Code Structure**: Well-organized function architecture with clear separation of concerns between UI, data management, and user interaction handling
- **Cross-Platform Compatibility**: Universal functionality across all websites with consistent behavior and reliable performance on various domains and page types

## [6.17.3] - 2025-01-24
### Enhanced Show Links Quick Action: Advanced Jump Functionality and Persistent Filter States
- **Reliable Multi-Method Jump Links**: Implemented robust link jumping system with three fallback methods including data attribute matching, href matching, and text+href combination matching for improved reliability
- **Persistent Filter States**: Added global filter state persistence using localStorage and window variables that remembers filter settings across all pages and browser sessions
- **Smart Hidden Menu Detection**: Enhanced navigation container detection with intelligent visibility checking to flash parent menu items when target links are in hidden submenus or dropdowns
- **Background Scroll Prevention**: Added comprehensive scroll event isolation to prevent background page scrolling when scrolling within the Show Links panel
- **Advanced Menu Trigger Detection**: Implemented sophisticated menu trigger detection for hidden submenus using aria attributes, data toggles, and common menu patterns
- **Enhanced Error Handling**: Added user feedback notifications for failed jump attempts with clear messaging about dynamically loaded content

### Advanced Navigation Container Intelligence
- **Visibility-Based Container Selection**: Smart algorithm that prioritizes visible navigation containers over hidden ones for better visual feedback
- **Menu Hierarchy Analysis**: Multi-level container analysis that identifies the best parent element to flash when target links are in nested menu structures
- **Parent Menu Item Flashing**: When links are in hidden dropdowns or submenus, the system intelligently flashes the visible parent menu item instead
- **Submenu Trigger Detection**: Automatic detection of menu trigger elements (buttons, links) that control submenu visibility for accurate highlighting
- **Cross-Platform Menu Support**: Enhanced support for various menu systems including Bootstrap dropdowns, custom navigation menus, and mobile hamburger menus

### Enhanced Links Extractor Quick Action: Purple Pulsing Jump Effects and Scroll Isolation
- **Purple Pulsing Highlight Effects**: Added sophisticated purple pulsing animation system matching Show Links visual style with 6-pulse cycles and smooth transitions
- **Scroll Event Isolation**: Implemented comprehensive scroll prevention to stop background page scrolling when interacting with Links Extractor popup panels
- **Enhanced Jump Visual Feedback**: Multi-stage pulsing animation with purple borders, glow effects, and scale transformations for professional visual feedback
- **Improved Element Cleanup**: Comprehensive cleanup system that properly restores original element styles after pulsing animations complete
- **Consistent Visual Language**: Unified visual design between Show Links and Links Extractor for cohesive user experience across tools

### Technical Architecture Improvements
- **Global Variable State Management**: Robust filter state management using window variables with localStorage backup for immediate access and long-term persistence
- **Multi-Fallback Link Detection**: Three-tier link detection system with data attributes, href matching, and text analysis for maximum reliability
- **Enhanced Error Recovery**: Comprehensive error handling with graceful fallbacks and user-friendly messaging for improved debugging
- **Memory Management**: Proper cleanup procedures for event listeners, style attributes, and temporary DOM modifications
- **Cross-Component Integration**: Improved integration between Show Links and Links Extractor with shared visual patterns and consistent behavior

## [6.17.2] - 2025-01-22
### Enhanced Responsive Device Simulator: Improved Debug Analysis System
- **Dual-Stage Debug Analysis**: Implemented two-stage debug analysis system that runs initial analysis after 300ms and secondary analysis after 800ms to catch layout changes that occur after CSS loading
- **Iframe Content Refresh**: Added comprehensive iframe refresh functionality that reloads page content after resizing to ensure accurate overflow detection with fresh DOM state
- **Root Cause Overflow Detection**: Enhanced overflow analysis algorithm to identify specific root cause elements rather than all affected elements, providing more actionable debugging information
- **Smart Element Filtering**: Implemented parent-child relationship filtering to prevent highlighting child elements when parent is already identified as the root cause
- **Enhanced Debug Cleanup**: Added comprehensive cleanup system that removes all debug-related styles, data attributes, and classes between analyses for accurate results

### Advanced Overflow Analysis Features
- **Progressive Layout Analysis**: Enhanced timing system that waits for CSS reflow completion before running analysis, ensuring elements are properly positioned for accurate detection
- **Improved Visual Feedback**: Updated notification text to "Potential Overflow Issue(s) Found" for more accurate user communication and professional presentation
- **Enhanced Root Cause Categorization**: Added detailed cause type identification including "extends-horizontal", "too-wide", and "positioned-horizontal" for specific debugging guidance
- **Overflow Container Detection**: Smart detection of overflow:hidden parents to prevent false positives from contained elements that don't cause actual scrollbars
- **Focus Management**: Improved focus flow and element positioning logic for better debug analysis accuracy after viewport dimension changes

### Technical Architecture Improvements
- **RefreshIframeAndDebug Function**: Added new comprehensive refresh function that handles iframe reloading, cleanup, and progressive debug analysis with proper error handling
- **Enhanced Timing Controls**: Implemented sophisticated timing system with multiple checkpoints to ensure accurate analysis at the right moments in the page lifecycle
- **Improved Error Handling**: Added fallback mechanisms and comprehensive error logging for iframe loading failures and analysis edge cases
- **Memory Management**: Enhanced cleanup procedures for debug states, ensuring no residual debug elements affect subsequent analyses
- **Cross-Component Integration**: Improved integration between manual resizing and debug analysis with automatic refresh triggering for seamless user experience

## [6.17.1] - 2025-01-20
### UI Styling Fixes and Microthemer Compatibility
- **Table Header Styling Fixes**: Fixed table header background colors across all components to ensure consistent dark theme styling
- **Microthemer Override Protection**: Added CSS override rules with `!important` declarations to prevent external theme conflicts
- **Improved CSS Specificity**: Updated CSS selectors to use direct child selectors (`>`) for more precise styling control
- **Enhanced Visual Consistency**: Standardized table header colors to `#3a3a3a` across Drag Select Links, Images Analysis, Links Extractor, and Responsive Device Simulator
- **Professional Dark Theme**: Ensured all table headers maintain proper contrast and readability with consistent styling

### Component-Specific Improvements
- **Drag Select Links**: Fixed checkbox and text header styling with proper `!important` declarations and consistent background colors
- **Images Analysis**: Updated inline table header styles from `#111111` to `#3a3a3a` for better dark theme consistency
- **Links Extractor**: Enhanced table header styling with CSS override rules to prevent external styling conflicts
- **Responsive Device Simulator**: Maintained consistent table header styling across all device simulation components

### Technical Enhancements
- **CSS Override System**: Implemented comprehensive CSS override rules to ensure extension styling takes precedence over external themes
- **Inline Style Updates**: Updated dynamically generated table headers to use consistent styling with `!important` declarations
- **Cross-Component Consistency**: Standardized styling approach across all Quick Actions tools for unified user experience

## [6.17.0] - 2025-06-20
### Major Feature: Responsive Device Simulator
- **Professional Device Testing**: Added comprehensive responsive device simulator with 25+ device presets including iPhone, Android, iPad, laptop, and desktop devices
- **Real-Time Device Switching**: Instant device switching with automatic dimension updates and zoom controls for testing different screen sizes
- **Advanced Debug Analysis**: Integrated overflow detection system with red border highlighting for elements causing horizontal/vertical scrollbars
- **Custom Size Support**: Manual device sizing with width/height inputs, custom size memory, and rotation functionality for landscape/portrait testing
- **Interactive Controls**: Draggable device container, zoom in/out controls (25%-200%), and keyboard shortcuts (ESC, R, +/-, D) for efficient testing
- **Overflow Intelligence**: Smart overflow analysis that identifies problematic elements, provides detailed debug information, and offers CSV export functionality
- **Professional UI**: Dark theme interface with device frames, dimension displays, and comprehensive debugging panel for professional responsive testing

### Enhanced Device Management System
- **Device Memory**: Automatic saving and restoration of last selected device across sessions
- **Smart Auto-Scaling**: Automatic device scaling when viewport exceeds browser window size
- **Manual Resize Handles**: Interactive resize handles for custom device dimensions with real-time dimension feedback
- **Focus Styling**: Professional focus indicators with brand purple styling for keyboard navigation accessibility
- **Error Handling**: Comprehensive error handling for iframe loading, script injection, and device switching operations

## [6.16.0] - 2025-06-20
### Major Feature: Drag Select Links Tool
- **Advanced Link Selection**: Comprehensive drag-to-select functionality for bulk link management with visual selection feedback
- **Multiple Output Formats**: Support for 7 different output formats including HTML, Markdown, CSV, JSON, plain text, and custom formatting options
- **Smart Filtering System**: Advanced filtering capabilities with domain-based filtering, text search, and checkbox-based selection management
- **Professional Interface**: Draggable popup interface with resizable panels, comprehensive statistics, and real-time link analysis
- **Bulk Operations**: Select all/none functionality, filtered selections, and batch processing capabilities for efficient link management
- **Export Capabilities**: Multiple export options including clipboard copying, CSV download, and formatted text output for various use cases

### Enhanced Link Management Features
- **Color Customization**: Configurable selection colors with real-time preview and color picker integration
- **Hotkey Support**: Customizable keyboard shortcuts for quick activation and enhanced productivity
- **Link Analysis**: Detailed link statistics including total count, selected count, and domain distribution analysis
- **Settings Integration**: Comprehensive settings panel with toggle controls, format preferences, and visual customization options
- **Memory Management**: Proper cleanup procedures and event listener management for optimal performance

## [6.15.0] - 2025-06-20
### Major Feature: Click to Copy Links
- **One-Click Link Copying**: Instant link copying functionality with Ctrl+Click activation for rapid link collection
- **Visual Feedback System**: Green flash animation and temporary styling changes to indicate successful copy operations
- **Smart Link Detection**: Automatic detection of clickable links with proper event handling and conflict prevention
- **Settings Integration**: Dedicated toggle switch in extras settings for enabling/disabling the click-to-copy functionality
- **Cross-Platform Support**: Universal functionality across all websites with consistent behavior and reliable performance
- **Non-Intrusive Design**: Seamless integration that doesn't interfere with normal link clicking or page functionality

### User Experience Enhancements
- **Instant Feedback**: Immediate visual confirmation when links are copied to clipboard
- **Keyboard Integration**: Ctrl+Click pattern that feels natural and intuitive for power users
- **Default Enabled**: Enabled by default for immediate productivity gains upon installation
- **Error Handling**: Graceful handling of copy failures with fallback mechanisms

## [6.14.0] - 2025-06-20
### Major Feature: Links Extractor Tool
- **Comprehensive Link Analysis**: Advanced link extraction tool with detailed analysis of all page links including internal, external, and anchor links
- **Multiple Export Formats**: Support for HTML, Markdown, CSV, JSON, and plain text formats with customizable output options
- **Advanced Filtering**: Domain-based filtering, link type categorization, and search functionality for efficient link management
- **Professional Interface**: Draggable popup interface with statistics panel, real-time counts, and comprehensive link details
- **Bulk Selection Tools**: Select all/none functionality, filtered selections, and checkbox-based management for bulk operations
- **Export Capabilities**: Multiple export options including clipboard copying, file downloads, and formatted text output

### Link Analysis Features
- **Link Categorization**: Automatic categorization of links by type (internal, external, anchor, email, phone)
- **Domain Analysis**: Domain-based grouping and filtering with visual domain distribution
- **Link Validation**: Basic link validation and status checking for quality assurance
- **Statistics Dashboard**: Real-time statistics including total links, selected links, and category breakdowns
- **Settings Integration**: Dedicated toggle and configuration options in Quick Actions settings panel

## [6.13.1] - 2025-01-31
### Enhanced Copy Element Performance and Citation Cleanup
- **Preloading Optimization**: Added preloadCopyElement method to improve loading speed and script activation time for faster responsiveness
- **Duplicate Script Prevention**: Implemented checks to prevent duplicate script loading and handle background injection more efficiently
- **Optimized Execution**: Enhanced executeCopyElement method to reduce wait times and improve user experience during script execution  
- **Performance Improvements**: Introduced quickCleanup and improved hover management in CopyElementAction class for better performance

### Advanced Citation Number Removal System
- **Comprehensive Citation Cleanup**: Enhanced citation number removal functionality to handle various cases including numbers at end of lines, sentences, and isolated numbers
- **Improved Regex Patterns**: Refined regex patterns for more accurate cleanup of citation numbers, ensuring cleaner text copying for users
- **HTML Cleaner Integration**: Added citation number removal functionality to HTMLCleanerAction with new configurable option for users
- **Content Quality Enhancement**: Finalized content trimming to remove remaining isolated numbers, improving overall content quality during copy actions

### Color Picker User Experience Improvements  
- **Enhanced Default Settings**: Updated ColorPickerAction to set default auto-copying option to hex format for better user experience
- **Improved Radio Button Logic**: Adjusted HTML structure to reflect new default selection with proper radio button state management
- **Error Handling Enhancement**: Added robust error handling to default to hex format in case of localStorage loading issues

### Technical Architecture Enhancements
- **Global Shortcut Manager Updates**: Enhanced global shortcut manager integration for improved Copy Element activation and management
- **Memory Management**: Improved cleanup procedures and event listener management for better extension performance
- **Settings Synchronization**: Enhanced settings integration across HTML Cleaner and Copy Element tools for consistent user preferences
- **Code Quality**: Added comprehensive error handling and improved documentation throughout the enhanced functionality

## [6.13.0] - 2025-02-01
### New Feature: Current Location Display on Search Pages
- **SERP Location Display**: Added new current location display feature that shows the active Location Changer location on Google search result pages
- **Smart Positioning**: Displays current location above tracked domains jump navigation or at the top of search results for optimal visibility
- **Settings Integration**: Added dedicated toggle switch in Extras settings for enabling/disabling the current location display feature
- **Conditional Display Logic**: Only shows when Location Changer is enabled and overwrite is active, preventing unnecessary display
- **Real-time Updates**: Automatically updates location display when Location Changer settings are modified via storage listeners

### Enhanced Location Changer Integration
- **Visibility Management**: Enhanced location-changer-visibility.js with integration helper function for SERP location display
- **Dynamic Content Handling**: Implemented MutationObserver for handling page updates and jump navigation creation
- **Consistent Styling**: Styled location display to match tracked domains navigation with dark theme and professional appearance
- **Global Settings Respect**: Integrated with extension settings system for user preference management and real-time updates

### Image Analysis and Bold from SERP Improvements
- **Enhanced Image Processing**: Continued improvements to image analysis functionality for better visual optimization detection
- **Bold from SERP Integration**: Maintained and enhanced Bold from SERP functionality for better text styling on search pages
- **Cross-Platform Consistency**: Ensured all features work seamlessly across Google search variations and international domains

### Technical Architecture Enhancements
- **New Script Integration**: Added current-location-display.js to manifest content scripts for proper loading on Google search pages
- **Storage Management**: Enhanced Chrome storage integration for location data retrieval and settings synchronization
- **Error Handling**: Comprehensive error handling and logging for debugging and user feedback
- **Performance Optimization**: Efficient DOM monitoring and display updates with proper cleanup procedures

## [6.12.0] - 2025-01-31
### Enhanced URL Permissions and Script Injection Logic
- **Comprehensive URL Pattern Support**: Updated manifest.json to include expanded URL patterns for Google domains, improving functionality access across various international Google sites
- **Advanced Script Injection System**: Enhanced background.js with detailed logging and robust error handling for script injection, particularly for restricted pages and edge cases
- **Improved Global Shortcut Manager**: Refined global-shortcut-manager.js with additional checks for restricted pages and enhanced script loading mechanisms for better reliability
- **Fallback Script Injection Methods**: Added comprehensive fallback mechanisms for script injection to handle cases where standard methods fail, ensuring consistent functionality across different browsing contexts

### Open Single Listing Feature Enhancement
- **Settings Management Integration**: Fully integrated Open Single Listing feature with the extension's settings system, allowing users to enable/disable the functionality
- **Asynchronous Settings Loading**: Implemented proper Chrome storage integration with real-time settings updates and user preference synchronization
- **Enhanced Button Logic**: Improved button injection logic based on user preferences with comprehensive logging for debugging and user feedback
- **Settings UI Integration**: Added dedicated toggle switch in extras-settings.html for the Open Single Listing feature with professional styling

### UI and Styling Improvements
- **Enhanced NAP Button Styling**: Added comprehensive CSS styling system for Search NAP Injector to ensure consistent button appearance across all Google search contexts
- **Business Name Heading Optimization**: Implemented proper styling for business name headings (.lfPIob) with 100% width and consistent display properties
- **Button Container Improvements**: Enhanced button container styling and spacing for professional appearance with proper shadow effects and transitions
- **Cross-Platform Consistency**: Ensured consistent button styling and layout across Google Maps, Search, and Pro List pages

### Technical Architecture Enhancements
- **Manifest Permission Optimization**: Updated host permissions and URL patterns for improved cross-domain functionality and better security
- **Error Handling Improvements**: Enhanced error handling throughout the codebase with comprehensive try-catch blocks and graceful fallbacks
- **Memory Management**: Improved cleanup procedures and memory management for better extension performance and stability
- **Code Organization**: Better separation of concerns with modular CSS injection and styling management

## [6.11.1] - 2025-01-31
### New Feature: Open Single Listing Button
- **Open Single Listing Integration**: Added "Open Single Listing" button positioned to the right of the NAP button in multiple listing mode
- **Share Link Automation**: Button automatically clicks the Google Maps share button, waits for dynamic link generation, and opens the business listing in a new tab
- **Multiple Listing Mode Focus**: Specifically designed for multiple listing mode to provide quick access to individual business pages
- **Professional UI Design**: Blue-themed button with Google-style colors and consistent sizing with existing button infrastructure
- **Smart Link Extraction**: Intelligent extraction of Google Maps share URLs from dynamically generated input fields
- **Error Handling**: Comprehensive error handling with visual feedback for missing share buttons or failed link extraction

### Technical Implementation
- **Seamless Integration**: Uses existing button container infrastructure alongside NAP and Find Citations buttons
- **Consistent Architecture**: Follows established patterns for button injection, event handling, and DOM management
- **Cross-Platform Support**: Works on Google Maps, Search, and Pro List pages in multiple listing contexts
- **Performance Optimized**: Efficient DOM monitoring and button injection with conflict prevention
- **User Feedback**: Visual button state changes showing loading, success, and error states

## [6.11.0] - 2025-01-31
### Enhanced Image Analysis: Visual Optimization Indicators
- **Optimization Opportunities Summary**: Added prominent summary section displaying average size reduction percentage, total images needing resizing, total size reduction potential, and compression issues count
- **Visual Row Highlighting**: Images requiring resizing now display with orange borders around entire table rows for immediate visual identification of optimization opportunities
- **Images Need Resizing Filter**: Added dedicated filter checkbox with orange accent color to quickly isolate images that require dimension optimization
- **Compression Detection System**: Comprehensive format analysis detecting PNG-to-JPG conversion opportunities, legacy format issues (GIF/BMP/TIFF), and non-standard format warnings

### Image Optimization Intelligence
- **Smart Optimization Detection**: Enhanced algorithm only flags images with significant optimization potential (>50px difference and >20% savings) to prevent unnecessary recommendations
- **Format Optimization Recommendations**: Actionable compression suggestions including PNG-to-JPG conversion for large images, GIF modernization to WebP/PNG, and legacy format conversion guidance
- **Professional Audit Presentation**: Orange-bordered optimization opportunities section with grid layout displaying key performance metrics in large, prominent numbers
- **Enhanced Filter Integration**: Seamless integration of resizing filter with existing filter system including proper debouncing and real-time count updates

### User Experience Improvements
- **Immediate Visual Feedback**: Orange borders and summary metrics provide instant understanding of optimization potential without requiring detailed analysis
- **One-Click Filtering**: Dedicated "Images Need Resizing Only" filter enables rapid focus on actionable optimization opportunities
- **Comprehensive Format Analysis**: Automatic detection of suboptimal image formats with specific conversion recommendations for better web performance
- **Professional Metrics Display**: Clean, grid-based presentation of optimization statistics with color-coded visual hierarchy for quick decision making

## [6.10.0] - 2025-01-31
### Major Enhancement: Advanced Image Analysis Tool
- **Background Image Detection**: Added comprehensive CSS background-image detection that automatically includes background images in the standard image audit without separate categories or filters
- **Chrome DevTools-Style Dimensions**: Enhanced dimensions column to display detailed information including rendered size, aspect ratios, intrinsic size, and real-time file size detection via HTTP headers
- **Performance Optimization Analysis**: Intelligent detection of oversized images with automatic calculation of potential file size savings when images are displayed smaller than their natural dimensions
- **Actionable Issue Recommendations**: Converted all image issues into specific action items with clear instructions (e.g., "Resize image to 172x50 pixels to reduce file size by 68%" instead of generic flags)
- **Visual Issue Presentation**: Redesigned Issues column with white bullet points in formatted lists for improved readability and professional appearance
- **Multi-Source Image Detection**: Seamlessly analyzes IMG tags, SVG elements, and CSS background images as unified image inventory without additional complexity

### Image Analysis Tool Technical Improvements
- **Real-Time File Size Fetching**: Implemented HTTP HEAD request system to fetch actual file sizes in kB format, displaying accurate file information similar to Chrome DevTools
- **Smart Optimization Detection**: Only flags images for optimization when there are significant size differences (>50px and >20% potential savings) to prevent unnecessary recommendations
- **Enhanced Dimension Analysis**: Provides comprehensive dimension information including rendered vs intrinsic size comparisons with calculated aspect ratios
- **Improved CSV Export**: Enhanced export functionality to include all new optimization recommendations and detailed dimension data for comprehensive reporting
- **Better User Experience**: Streamlined interface that treats all image types consistently while providing detailed technical analysis for optimization decisions

### Documentation Updates
- **Enhanced README**: Updated Media & Assets section with comprehensive description of new image analysis capabilities including multi-source detection and Chrome DevTools integration
- **Feature Documentation**: Added detailed documentation for background image detection, performance optimization features, and actionable recommendation system
- **Technical Architecture**: Documented the unified approach to image analysis that maintains simplicity while providing professional-grade audit capabilities

## [6.9.0] - 2025-06-14
### Major Feature: Advanced Color Picker Integration
- **New Color Picker Tool**: Added comprehensive color picker tool with draggable panel interface and real-time color sampling
- **Popup Integration**: Added Color Picker button to main popup interface for easy access across all websites
- **Global Shortcuts Support**: Integrated Color Picker with global shortcut manager for keyboard-based activation
- **Settings Configuration**: Added Color Picker toggle and description in Quick Actions settings panel
- **Professional UI**: Implemented draggable color picker panel with modern interface and user-friendly controls

### Enhanced Quick Edit Functionality
- **Advanced Selection System**: Introduced Shift+Drag selection functionality for bulk element selection and deletion
- **Multi-Element Management**: Added ability to select multiple elements simultaneously with visual feedback
- **Smart Deletion Logic**: Implemented intelligent element deletion with safety checks for critical page elements  
- **Visual Selection Indicators**: Added purple outline styling for selected elements with smooth animations
- **Enhanced Event Management**: Improved keyboard shortcuts (ESC, Delete/Backspace) and mouse interaction handling
- **Cleanup Optimization**: Enhanced cleanup process with proper event listener management and DOM restoration

### Advanced Copy Element Capabilities  
- **RAW HTML Copy Mode**: Added RAW HTML copying functionality to preserve original element structure
- **Enhanced gitignore Management**: Updated .gitignore for better development workflow and file management
- **Improved Error Handling**: Enhanced error handling and user feedback for copy operations

### Image Analysis Tool Enhancements
- **Advanced Filtering System**: Added comprehensive image filtering capabilities for better analysis
- **Image Resizing Features**: Implemented image resizing functionality within the analysis tool
- **Enhanced UI Controls**: Improved user interface with better navigation and control options

### User Experience and Interface Improvements
- **Consistent Button Styling**: Standardized close button styles across all Quick Actions for visual consistency
- **Enhanced Popup Design**: Improved popup button styles for better user experience and visual appeal
- **Settings Panel Updates**: Added Reset to Defaults button with warning styles in settings interface
- **Semantic Tooltips Integration**: Enhanced Semantic Elements action with integrated tooltip functionality
- **Responsive UI Elements**: Improved responsive design across all interface components

### Technical Architecture Enhancements
- **DOM Cleanup Refactoring**: Enhanced DOM cleanup logic in DOMSnapshotUtility and QuickActionsDOMRestore
- **Global Shortcut Architecture**: Improved global shortcut manager integration across multiple tools
- **Event Listener Management**: Enhanced event listener cleanup and management system
- **Debug Mode Improvements**: Refactored debug mode handling for better development experience
- **Memory Management**: Improved memory management with proper cleanup procedures across all tools

### Documentation and Development
- **Enhanced README**: Updated README.md with latest functionality descriptions and usage instructions
- **Code Quality Improvements**: Enhanced code documentation and commenting throughout the codebase
- **Development Workflow**: Improved development processes with better file organization and cleanup procedures

## [6.8.2] - 2025-01-31
### Critical Bug Fixes: Color Picker Context Menu Integration
- **Fixed Escape Listener Pattern**: Resolved critical issue where Color Picker was not following proper escape listener naming convention for global cleanup system
- **Enhanced Global Cleanup Integration**: Fixed Color Picker to properly integrate with DOM cleanup system using `colorPickerEscapeListener` instead of generic `handleKeyDown`
- **Complete DOM Cleanup**: Added missing `.color-picker-tooltip` selector to all cleanup files (background.js, dom-snapshot-utility.js, quick-actions-dom-restore.js)
- **Improved Reset Method**: Enhanced Color Picker reset function to properly clean up escape listeners from global scope

### Context Menu Compliance Improvements
- **Golden Rules Adherence**: Updated Color Picker to fully comply with context menu implementation golden rules
- **Self-Contained Lifecycle**: Enhanced cleanup system with proper global reference management and event listener removal
- **Memory Leak Prevention**: Improved memory management by properly deleting global escape listener references during cleanup
- **Backward Compatibility**: Maintained legacy `colorPickerCleanup` function while adding proper escape listener pattern

### Technical Architecture Enhancements
- **Standardized Event Handling**: Aligned Color Picker event handling with other context menu actions for consistency
- **Enhanced DOM Restoration**: Improved DOM snapshot utility integration to prevent conflicts with other quick actions
- **Comprehensive Cleanup Coverage**: Ensured all Color Picker UI elements are properly removed across all cleanup systems
- **Error Handling Improvement**: Added robust error handling in reset method with proper promise resolution

### User Experience Improvements
- **Reliable Context Menu Operation**: Color Picker now operates reliably within the context menu system without DOM conflicts
- **Consistent Cleanup Behavior**: ESC key and close button behavior now matches other context menu tools
- **Seamless Integration**: Color Picker no longer interferes with other quick actions or context menu items
- **Professional UI Management**: Enhanced draggable panel with proper cleanup and position saving functionality

## [6.8.1] - 2025-06-12
### Critical Bug Fixes: Copy Element Global Shortcuts
- **Fixed Toggle Functionality**: Resolved critical issue where Copy Element global shortcuts remained active even when the Copy Element toggle was switched OFF in settings
- **Corrected Settings Integration**: Fixed global shortcut manager to properly check both global shortcuts enabled and individual Copy Element enabled settings
- **Enhanced Real-time Updates**: Improved storage change listeners to immediately disable/enable shortcuts when Copy Element toggle is changed
- **Removed Invalid References**: Eliminated non-existent `globalShortcutsToggle` element references that were causing settings errors

### Custom Shortcut System Fixes
- **Fixed Method Call Errors**: Corrected invalid `setShortcut()` method calls to proper `updateInputValue()` method in custom shortcut handler
- **Improved Settings Synchronization**: Enhanced shortcut input field synchronization with saved settings for proper value display
- **Streamlined Architecture**: Simplified global shortcut management to use only Copy Element enabled toggle instead of redundant global toggle
- **Enhanced User Experience**: Users can now reliably set custom shortcuts and toggle Copy Element functionality on/off

### Technical Improvements
- **Simplified Settings Logic**: Removed unnecessary `globalShortcutsEnabled` complexity and streamlined to use individual feature toggles
- **Enhanced Error Handling**: Added comprehensive safety checks before executing Copy Element to prevent activation when disabled
- **Improved Code Documentation**: Added clear comments explaining the simplified toggle system and settings integration
- **Memory Management**: Enhanced cleanup of global shortcut manager references and event listeners

### User Experience Enhancements
- **Reliable Toggle Control**: Copy Element toggle now immediately disables all global shortcuts when switched OFF
- **Working Custom Shortcuts**: Users can successfully set new keyboard shortcuts through the settings interface
- **Instant Feedback**: Settings changes take effect immediately without requiring page refresh or extension reload
- **Consistent Behavior**: Toggle states are properly respected across all browser tabs and website contexts

## [6.8.0] - 2025-01-30
### New Feature: Clean Content Context Menu
- **Context Menu HTML Cleaning**: Added "Clean Content" context menu item for cleaning selected HTML content with right-click access
- **Settings Integration**: Clean Content uses all HTML cleaner settings from Extras menu for consistent cleaning behavior across the extension
- **Selection-Based Operation**: Works specifically with highlighted/selected text to clean HTML content without affecting the entire page
- **Top-Level Menu Placement**: Clean Content appears directly in the context menu alongside Word Counter, not nested in submenus
- **Notification Feedback**: Provides user feedback through small notifications confirming successful content cleaning operations
- **Clipboard Integration**: Automatically copies cleaned content to clipboard for immediate use in other applications

### Technical Implementation
- **New CleanSelectedContentAction Class**: Added comprehensive action class in settings/quick-actions/cleanselectedcontent.js
- **Background Script Integration**: Enhanced background.js with executeCleanSelectedContent function and proper context menu handling
- **DOM Cleanup Integration**: Integrated with existing DOM snapshot and restoration system for conflict-free operation
- **Settings Synchronization**: Reads HTML cleaner settings from Chrome storage to apply user-configured cleaning preferences
- **Error Handling**: Robust error handling with validation for text selection and graceful fallbacks for storage access
- **Cross-Platform Compatibility**: Works on all supported websites through universal document URL patterns

### User Experience Enhancements
- **Instant Access**: Right-click selected text to access Clean Content without opening popup or navigating menus
- **Consistent Behavior**: Uses same cleaning logic as existing HTML cleaner tools for predictable results
- **Visual Feedback**: Clear notifications indicate when content has been cleaned and copied to clipboard
- **Responsive Settings**: Immediately reflects changes made to HTML cleaning preferences in Extras settings
- **Seamless Integration**: Appears alongside existing Word Counter context menu item for natural workflow

## [6.7.0] - 2025-01-30
### Major Feature: Comprehensive HTML Cleaning System
- **8 Configurable Cleaning Options**: Added complete HTML cleaning system with granular control over style removal, class elimination, ID stripping, comment removal, empty tag cleanup, whitespace normalization, data attribute removal, and intelligent wrapper div removal
- **Smart Wrapper Div Removal**: Revolutionary feature that intelligently removes nested wrapper divs while preserving meaningful content structure (transforms `<div><div><div><ul>...</ul></div></div></div>` to clean `<ul>...</ul>`)
- **Copy Element Integration**: Seamless integration with Copy Element "Clean and Title" feature for automatic HTML cleaning on Shift+Click operations
- **Settings Panel Interface**: Professional configuration panel accessible via Extras settings with real-time preview and save/cancel functionality

### Enhanced Export/Import System (v2.0)
- **Comprehensive Settings Export**: Upgraded export system to v2.0 format supporting all extension components including HTML cleaner settings, keyboard shortcuts, Quick Actions order, and location configurations
- **Multi-Storage Support**: Export/import now handles all Chrome storage keys (gmbExtractorSettings, htmlCleanerSettings, copyElementShortcut, quickActionsOrder, globalShortcutsEnabled)
- **Backward Compatibility**: Import system supports both v1.0 legacy format and new v2.0 comprehensive format with automatic detection and proper migration
- **Enhanced User Feedback**: Detailed success messages showing exactly what was exported/imported with statistics for settings count, tracked domains, HTML cleaning options, and shortcuts

### Documentation and Code Quality Improvements
- **Updated README**: Enhanced documentation with comprehensive HTML cleaning system details, integration points, and usage instructions
- **Development Cleanup**: Removed all test and debug scripts (html-cleaner-debug.js, html-cleaner-standalone-debug.js, html-cleaner-console-test.js) for production-ready codebase
- **Manifest Optimization**: Cleaned up web_accessible_resources by removing debug script references
- **Version Consistency**: Updated all version references to maintain consistency across README and manifest files

### Technical Architecture Enhancements
- **Default Settings Standardization**: Ensured all 8 HTML cleaning options are enabled by default across all components for optimal out-of-box experience
- **Storage Key Consolidation**: Proper organization of settings across different Chrome storage keys with comprehensive coverage in export/import functionality
- **Error Handling Enhancement**: Robust error handling in HTML cleaning with fallback to default settings when Chrome storage access fails
- **Cross-Component Integration**: Seamless integration between Copy Element, HTML Cleaner settings panel, and export/import system

## [6.6.5] - 2025-01-30
### New Feature: Responsive Font Analysis Tool
- **Font Styles Analyzer**: Added comprehensive responsive font analysis tool with live viewport resizing across 9 breakpoints (Desktop XL to Mobile XS)
- **Interactive Breakpoint Slider**: Professional slider interface for exploring font usage across different screen sizes with smooth transitions
- **Heading Element Sorting**: Implemented intelligent H1-H6 sorting with proper numeric ordering for better heading analysis
- **Responsive Font Detection**: Smart detection of fonts that change sizes across breakpoints with detailed breakdown by viewport width
- **Context Menu Integration**: Added "Responsive Font Analysis" to context menu under DEV Tools for easy access

### Enhanced Image Analysis Capabilities  
- **SVG Support**: Extended Image Analysis tool to include SVG elements with accessibility checks and dimension retrieval
- **Comprehensive Image Summary**: Added filtering functionality and improved UI with sticky headers and scrollable content
- **Enhanced Error Handling**: Improved error handling in image analysis with better user feedback and interaction

### Documentation and Infrastructure Improvements
- **Enhanced README**: Completely revised installation instructions with Chrome Web Store and Developer Mode methods
- **Expanded Feature Documentation**: Added detailed descriptions for Maps Functions, Google Search Integration, Review Scraping, and browser integration capabilities
- **New Documentation Sections**: Added Browser Integration, Data Export Formats, and Usage Tips for improved user guidance
- **Repository Cleanup**: Removed outdated TODO.md file and updated .gitignore for better file management

### Technical Architecture Enhancements
- **Font Analysis Core**: Implemented sophisticated font collection system with comprehensive filtering to exclude analyzer UI elements
- **Smart Element Detection**: Enhanced element filtering with parent container detection and data attribute exclusion
- **Improved Context Menu System**: Updated background.js with new context menu items and enhanced execution functions
- **Enhanced Image Tool UI**: Added sticky headers, scrollable containers, and improved drag-and-drop functionality

## [*******] - 2025-01-30
### Critical Bug Fix: Auto Clean & Title Toggle Functionality
- preserve essential media attributes during HTML cleanup. Improved comments for clarity on attribute handling for images, videos, and iframes.

## [6.6.4] - 2025-01-30
### Critical Bug Fix: Auto Clean & Title Toggle Functionality
- **Fixed Toggle State Enforcement**: Resolved critical issue where Auto Clean & Title functionality always triggered on Shift+Click regardless of toggle setting
- **Enhanced Settings Persistence**: Improved auto-save mechanism for toggle switches to immediately update chrome storage when clicked
- **Bulletproof Error Handling**: Added comprehensive try-catch blocks and fallback logic to prevent crashes when settings access fails
- **Conditional Processing Logic**: Restructured Copy Element code to explicitly check toggle state before applying any HTML cleaning or title processing
- **Settings Integration**: Properly integrated autoCleanAndTitleEnabled setting with default false state and correct toggle behavior in extras settings
- **User Experience Fix**: Toggle OFF now correctly copies original HTML without modification, Toggle ON applies cleaning and title processing as intended

## [6.6.3] - 2025-01-30
### Critical Bug Fix: Copy Element DOM Pollution Prevention
- **Enhanced DOM Management**: Completely eliminated DOM pollution issues in Copy Element tool that prevented extracting both URLs and span text without page refresh
- **Simplified Event Prevention**: Replaced complex element modification with clean event prevention approach to avoid temporary DOM changes
- **Single Instance Control**: Added global instance tracking to prevent multiple Copy Element instances running simultaneously
- **Improved Cleanup Timing**: Extended notification visibility to 1 second for better user feedback while maintaining automatic restart functionality
- **Memory Leak Prevention**: Enhanced cleanup system removes all inline styles, data attributes, and references with thorough garbage collection
- **User Experience Enhancement**: Users can now extract link URLs and span text content alternately without requiring page refresh or experiencing functionality conflicts

## [6.6.2] - 2025-01-30
### Bug Fixes & Code Quality Improvements
- **Enhanced LinkCheckerAction Cleanup**: Improved reset method to follow golden rules for better cleanup of link checker elements and XHR requests
- **Global XHR Request Tracking**: Added comprehensive tracking system for XMLHttpRequests to ensure proper cleanup and prevent memory leaks
- **Code Documentation Enhancement**: Enhanced comments throughout LinkCheckerAction code for improved clarity and adherence to best practices
- **Cross-Platform Font Consistency**: Updated font-family in styles for better consistency across different platforms and operating systems
- **Improved Tool Reliability**: Strengthened cleanup processes with clearly defined execution paths, improving overall tool reliability and performance

## [6.6.1] - 2025-01-30
### Technical Refactoring
- **LinkCheckerAction Architecture**: Refactored LinkCheckerAction for enhanced cleanup and consistency following golden rules
- **Memory Management**: Improved cleanup of link checker elements and XHR requests with global tracking
- **Code Quality**: Enhanced comments throughout the code for clarity and best practice adherence
- **Platform Consistency**: Updated font-family in styles for cross-platform consistency

## [6.6.0] - 2025-01-30
### **Major Feature: Advanced Copy Element Tool with Multi-Format Support**
- **Universal Element Copying**: Introduced comprehensive Copy Element tool that can copy any webpage element including text, images, links, videos, and SVG graphics
- **Global Keyboard Shortcuts**: Added configurable global shortcuts (Ctrl+Shift+C) for instant Copy Element activation across all websites
- **Multi-Format Content Support**: Enhanced copying capabilities for various content types with specialized handling for YouTube/Vimeo videos, SVG elements, and JavaScript links
- **Professional Hover Interface**: Interactive hover system with visual labels and enhanced feedback for precise element selection

### **Enhanced Copy Element Functionality**
- **Image Download Integration**: Seamlessly integrated ImageDownloadHandler for advanced image processing and download capabilities
- **Video URL Extraction**: Smart video URL detection and cleaning for major platforms including YouTube, Vimeo, and embedded video content
- **SVG Code Extraction**: Full SVG element support with proper code extraction and parent element handling
- **Class-Based Architecture**: Refactored to professional class-based structure with improved state management and cleanup procedures

### **Advanced User Experience**
- **Visual Element Highlighting**: Professional hover effects with customizable color schemes and responsive label positioning
- **Context-Aware Notifications**: Intelligent notifications that adapt based on content type (text copied, image downloaded, video URL extracted, etc.)
- **Comprehensive Cleanup System**: Advanced cleanup procedures ensure pristine environment after tool execution with no DOM residue
- **Cross-Platform Compatibility**: Universal functionality across all supported Google platforms and external websites

### **Technical Architecture Improvements**
- **Global Event Management**: Sophisticated global reference system for better event handler management and cleanup
- **Dynamic Script Injection**: Enhanced popup integration with dynamic script injection for ImageDownloadHandler and CopyElementAction
- **Error Handling Enhancement**: Comprehensive error handling with clear user feedback for failed operations and missing active tabs
- **Memory Management**: Professional memory management with complete event listener cleanup and state reset capabilities

### **Settings and Configuration**
- **Global Shortcut Management**: Added Copy Element to global shortcuts configuration with customizable key combinations
- **Feature Toggle Integration**: Seamless integration with extras settings for enabling/disabling global Copy Element shortcuts
- **Responsive Design Updates**: Enhanced settings interface with improved layout and responsiveness for Copy Element configuration

## [6.5.4] - 2025-01-28
### **Critical Fix: Quick Actions Keyboard Shortcuts Dynamic Ordering**
- **DOM Order Synchronization**: Fixed critical issue where keyboard shortcut numbers (1-9) did not follow the custom order set in Quick Actions settings
- **Dynamic Button Reordering**: Implemented physical DOM element reordering to match user-defined Quick Actions sequence
- **Real-time Order Updates**: Shortcut assignments now update immediately when Quick Actions are reordered in settings
- **Settings Integration**: Enhanced communication between reorder system and shortcuts system for seamless synchronization

### **Technical Architecture Improvements**
- **Enhanced DOM Management**: Modified loadAndShowQuickActions() to physically reorder button elements instead of just showing/hiding them
- **Improved Storage Listening**: Added proper storage change listeners to trigger both DOM reordering and shortcuts refresh
- **Timing Optimization**: Implemented delayed refresh mechanisms to prevent race conditions during order changes
- **Debug Enhancement**: Added comprehensive debugging tools and console commands for testing order synchronization

### **User Experience Fixes**
- **Consistent Numbering**: Ctrl+Shift+1 now always corresponds to the first action in custom order, Ctrl+Shift+2 to second, etc.
- **Persistent Custom Order**: User-defined Quick Actions order maintains proper shortcut assignments across browser sessions
- **Immediate Feedback**: Changes in Quick Actions settings now immediately reflect in popup button order and shortcut numbers
- **Visual Number Badges**: Shortcut number indicators on buttons now accurately reflect current order position

## [6.5.3] - 2025-01-28
### **Major Enhancement: Professional 404 Link Checker with Sidebar Interface**
- **Smart Link Detection**: Only flags actual HTTP 404/400+ status errors, eliminates CORS and network false positives
- **Interactive Sidebar Interface**: Replaced navigation arrows with professional left-hand sidebar showing all broken links
- **Link Context Display**: Each broken link shows anchor text and truncated URL for easy identification
- **One-Click Navigation**: Click any sidebar item to smoothly scroll to broken link with center positioning
- **Draggable Positioning**: Full drag functionality on sidebar header with viewport constraints and touch support
- **Complete Cleanup System**: ESC key and close button provide instant reset with perfect DOM restoration

### **User Experience Improvements** 
- **Visual Link Highlighting**: Broken links highlighted with red borders and hover effects
- **Progress Notifications**: Real-time progress indicators during link checking process
- **Smart Error Filtering**: Advanced filtering ensures only genuine broken links are reported
- **Professional Styling**: Extension-themed dark interface with consistent typography and spacing
- **Cross-Platform Compatibility**: Works reliably across all browser types and screen sizes

### **Technical Architecture**
- **Accurate HTTP Testing**: Uses exact same XHR logic as proven bookmarklet implementations
- **Memory Management**: Comprehensive cleanup of event listeners, global variables, and DOM elements
- **Performance Optimized**: Efficient link checking with proper timeout handling and request management
- **Conflict Prevention**: Integrates seamlessly with universal DOM restoration system

## [6.5.2] - 2025-01-28
### **New Feature: 404 Link Checker**
- **404 Checker Context Menu**: Added new "404 Checker" tool accessible via right-click context menu under SEO Tools
- **External Link Analysis**: Integrates Brett Terpstra's professional link checker bookmarklet for comprehensive 404 detection
- **Smart Panel Display**: Shows loading progress and results in draggable, resizable panel with professional styling
- **Complete DOM Restoration**: Follows golden rules with full DOM cleanup before execution and comprehensive reset functionality

### **Technical Integration**
- **Modular Architecture**: New `linkchecker.js` file in `settings/quick-actions/` following established patterns
- **Context Menu Integration**: Added to SEO Tools submenu with proper background script execution
- **Conflict Prevention**: Integrated with universal DOM restoration system for conflict-free operation
- **Settings Persistence**: Panel position and size automatically saved and restored across sessions

### **Enhanced User Experience**
- **Draggable Interface**: Full drag functionality with viewport constraints and touch support
- **Loading States**: Visual progress indicators while external script loads and executes
- **Consistent Styling**: Matches extension's dark theme with professional typography and spacing
- **Reset Integration**: Included in universal "Reset All" functionality for complete cleanup

## [6.5.1] - 2025-01-28
### 🐛 **Bug Fixes & DOM Cleanup Enhancement**
- **🧹 Word Counter DOM Cleanup**: Added `.word-counter-panel` selector to both DOM cleanup utilities for complete consistency
- **⚡ Enhanced Cleanup Coverage**: Word Counter now fully integrated into universal DOM restoration system
- **🔄 Perfect Action Switching**: Ensures Word Counter panels are properly cleaned when switching between popup and context menu actions
- **🛡️ Memory Management**: Prevents potential DOM conflicts and ensures clean state across all Quick Action executions

### 🛠️ **Technical Improvements**
- **📁 Unified Cleanup System**: Enhanced `quick-actions-dom-restore.js` and `background.js` cleanup selectors
- **🎯 Complete Coverage**: Word Counter now follows identical DOM cleanup principles as all other Quick Actions
- **🔧 Consistency**: Maintains architectural consistency across entire Quick Actions ecosystem

## [6.5.0] - 2025-06-09
### 🚀 **Major Feature: Enhanced Panel UX with Drag Functionality**
- **🖱️ Word Counter Context Menu**: Added new word counter tool accessible via right-click context menu on selected text
- **📊 Real-time Word Statistics**: Displays word count, character count, and selected text analysis instantly in styled panel
- **🎯 Smart Context Detection**: Word counter only appears when text is selected, with proper fallback for various browsers

### 🎨 **Universal Drag Functionality for Analysis Panels**
- **🔄 Draggable Analysis Panels**: Made all major analysis panels draggable for optimal workspace organization
  - 🔍 Font Inspector - Interactive font property analysis tool
  - 🎨 CSS Class Inspector - CSS class analysis and selector extraction
  - 🎨 Color Palette Extractor - Comprehensive color analysis and export
  - 🗺️ XML Sitemap Checker - Sitemap validation and discovery  
  - 📊 Page Keyword Density - Page-wide keyword analysis
  - 📊 Word Counter - Text selection analysis panel

### ⚡ **Enhanced User Experience**
- **🖱️ Smooth Drag Operations**: Professional drag experience with visual feedback and cursor changes
- **📐 Viewport Constraints**: Panels automatically constrain to browser window boundaries
- **🎯 Smart Button Exclusion**: Close and action buttons excluded from drag behavior for intuitive interaction
- **📱 Touch Device Support**: Full touch/mobile support with touch event handling for tablets and phones
- **💾 Memory Management**: Proper event listener cleanup prevents memory leaks during panel operations

### 🛠️ **Technical Architecture Improvements**
- **🔄 Dynamic Content Handling**: Drag functionality persists through async content updates and panel refreshes
- **🎯 Reusable Drag System**: Standardized drag implementation across all analysis panels
- **🧹 Event Management**: Comprehensive event listener cleanup and re-establishment for panel content changes
- **⚡ Performance Optimized**: Efficient positioning calculations with smooth 60fps drag operations

### 🐛 **Bug Fixes & Stability**
- **✅ Context Menu Visibility**: Fixed word counter visibility in selection-based context menus
- **🔧 Panel Positioning**: Resolved positioning conflicts when panels are moved and content updated
- **💻 Cross-browser Compatibility**: Enhanced compatibility across different browser implementations
- **🎯 Event Conflict Resolution**: Prevented drag events from interfering with panel functionality

## [6.4.2] - 2025-01-28
### 🚀 **Major Feature: DOM Snapshot & Restoration System**
- **📸 Initial DOM Snapshot**: Automatically captures complete page state when pages finish loading for perfect restoration
- **🔄 Universal DOM Restoration**: Restores page to initial state before every Quick Action and context menu execution
- **⚡ Seamless Action Switching**: Switch between popup Quick Actions and context menu actions without conflicts or page refresh
- **🧹 Comprehensive State Management**: Captures and restores document properties, body classes, styles, event listeners, and global variables
- **🛡️ Conflict Prevention**: Eliminates DOM modification conflicts between different action systems

### 🔧 **Enhanced Context Menu System**
- **🎯 Universal Cleanup**: All context menu actions now use DOM snapshot restoration before execution
- **📋 Comprehensive Coverage**: Updated all 12+ context menu actions (Quick Edit, Page Structure, Color Palette, Font Inspector, etc.)
- **🔄 Automatic Reset**: Context menu actions automatically reset DOM to initial state after each interaction
- **⚡ Performance Optimized**: Smart fallback to manual cleanup when snapshot unavailable

### ⚡ **Enhanced Quick Actions Integration**
- **🔄 Pre-execution Restoration**: All Quick Actions now restore DOM state before execution for clean slate
- **📊 Intelligent Reset System**: Enhanced "Reset All" function with DOM snapshot restoration as primary method
- **🎯 Action-Specific Cleanup**: Each Quick Action gets personalized DOM preparation with comprehensive cleanup
- **💾 Persistent State Management**: DOM snapshots survive browser sessions and extension updates

### 🏗️ **Advanced Technical Architecture**
- **📁 Modular Utilities**: New `dom-snapshot-utility.js` and `quick-actions-dom-restore.js` for clean code organization
- **🔧 Smart Detection**: Automatic detection of DOM snapshot availability with graceful fallbacks
- **⚡ Async Operations**: Full async/await implementation for reliable DOM operations
- **🛠️ Debug Support**: Comprehensive logging and debug modes for troubleshooting

### 🎨 **User Experience Improvements**
- **🔄 Zero Conflicts**: No more interference between popup and context menu Quick Actions
- **⚡ Instant Switching**: Switch between different action types without page refresh or manual cleanup
- **💎 Clean Execution**: Every action starts with a pristine page state for consistent behavior
- **📱 Universal Compatibility**: Works across all supported Google platforms (Maps, Search, Pro List)

### 🐛 **Critical Bug Fixes**
- **✅ Action Interference**: Fixed critical issue where Quick Actions would interfere with each other
- **🔧 Context Menu Conflicts**: Resolved context menu actions failing after popup Quick Actions usage
- **⚡ State Persistence**: Fixed DOM modifications persisting between different action executions
- **🎯 Element Cleanup**: Enhanced removal of all action-generated elements, overlays, and panels

### 🛠️ **Developer Experience**
- **📝 Enhanced Logging**: Comprehensive logging system for DOM operations and restoration processes
- **🔧 Modular Design**: Clean separation of concerns with reusable utility classes
- **🎯 Error Handling**: Robust error handling with graceful fallbacks for all DOM operations
- **📊 Debug Tools**: Built-in debug modes and state inspection tools for development

## [6.4.1] - 2025-01-28
### 🐛 **Critical Bug Fixes**
- **🔧 Context Menu Conflict Resolution**: Fixed critical issue where context menu actions (Quick Edit, Page Structure, Color Palette Extractor) would fail after using popup Quick Actions
- **🧹 Global Scope Cleanup**: Added comprehensive cleanup function that clears global conflicts before context menu action execution
- **⚡ Seamless Workflow**: Users can now switch between popup and context menu Quick Actions without requiring page refresh

### 🛠️ **Enhanced Quick Edit Functionality**
- **🔒 Improved Error Handling**: Added try-catch blocks around Quick Edit execute and reset methods with proper error logging
- **🎯 Better Context Menu Compatibility**: Enhanced QuickEditAction class structure for reliable context menu execution
- **🧹 Enhanced Cleanup Logic**: Improved element management with unique IDs and better selector targeting for animation styles
- **💾 Robust State Management**: Better handling of escape key listeners and global scope assignment

### 🚀 **Technical Architecture Improvements**
- **🔄 Generic Cleanup System**: New `cleanupGlobalScope()` function clears action classes, event listeners, DOM panels, and document state
- **🎯 Multi-Action Support**: Enhanced cleanup covers all Quick Actions (Page Structure X-ray, Color Palette Extractor, Quick Edit, etc.)
- **⚡ Conflict Prevention**: Automatic cleanup of residual state that could interfere with subsequent executions
- **🛡️ Defensive Programming**: Better error handling and graceful fallbacks for all context menu actions

### 🔧 **Developer Experience**
- **📝 Enhanced Logging**: Improved error logging and debugging capabilities for context menu execution
- **🧪 Consistent Execution Pattern**: Standardized cleanup-first approach for all context menu actions
- **🎯 Maintainable Code**: Modular cleanup function that can be easily extended for new Quick Actions

## [6.4.0] - 2025-01-28
### 🌍 **Enhanced Location Changer UX**
- **📍 Current Location Display**: Added real-time current location display to the right of "Location Changer" header with subtle, elegant typography
- **🎨 Responsive Design**: Smart text truncation for long location names with ellipsis (35+ character limit) and 200px max width
- **⚡ Live Updates**: Current location updates automatically when locations are selected via typeahead or manual input
- **💎 Visual Polish**: Subtle italic font styling with state-aware color transitions (active/inactive states) and smooth hover effects

### ⚡ **Enhanced Quick Actions & Context Menu System**
- **🎯 Comprehensive Context Menu**: Expanded right-click context menu with 12+ professional SEO and development tools
- **⌨️ Escape Key Functionality**: Universal Escape key support across all Quick Actions for instant reset and cleanup
- **🔍 Advanced Page Inspector**: Enhanced Page Structure X-ray with hover element inspection and detailed DOM analysis
- **🎨 Color Palette Extractor**: Redesigned color extraction tool with improved UI and better color detection algorithms
- **🔧 Developer Tools**: Added Font Inspector, CSS Class Inspector, Robots.txt Analyzer, and XML Sitemap Checker
- **📊 SEO Analysis**: Enhanced Keyword Density Calculator, MozDA Checker, and Screen Reader Simulation tools

### 🛠️ **Technical Architecture Improvements**
- **🎯 Smart Element Detection**: Improved DOM element targeting with better fallback strategies for current location display
- **💾 Enhanced State Management**: Robust state handling for location changes with proper Chrome storage integration
- **🔄 Performance Optimizations**: Optimized Quick Actions execution with reduced memory footprint and faster response times
- **📱 Cross-Platform Consistency**: Unified functionality across Google Maps, Search, and Pro List interfaces

### 🐛 **Code Quality & Bug Fixes**
- **✅ Location Synchronization**: Fixed location display synchronization issues across different input methods
- **🔧 Context Menu Integration**: Improved context menu item organization and execution reliability
- **⚡ Quick Actions Stability**: Enhanced error handling and cleanup for all Quick Actions tools
- **📐 UI Consistency**: Standardized styling and behavior across all new location and Quick Actions features

## [6.3.0] - 2025-01-28
### 🎨 **Enhanced Sponsored Highlighter Customization**
- **🌈 Color Picker Integration**: Added inline color selection controls directly next to "Sponsored Highlighter" setting for immediate customization
- **🎯 Real-time Color Updates**: Selected colors take effect instantly on all Google pages without requiring page refresh
- **⚙️ Persistent Color Settings**: Custom color preferences saved to Chrome sync storage and synchronized across devices
- **🔧 Smart Validation**: Automatic hex color code validation with error styling for invalid inputs and uppercase conversion
- **🎨 Professional UI**: Clean inline color picker with swatch preview and hex input field matching extension's design language
- **💾 Settings Synchronization**: Color picker and hex input field stay synchronized with real-time updates and paste handling
- **🚀 Improved User Experience**: Streamlined color customization without accordion interface for faster access

### 🛠️ **Technical Enhancements**
- **📝 Enhanced Settings Architecture**: Extended settings management system to handle color customization with full validation
- **🔄 Dynamic CSS Injection**: Smart CSS injection system that updates highlight styles with new colors in real-time
- **⚡ Optimized Performance**: Improved sponsored text highlighting with better color handling and reduced DOM manipulation
- **🎯 Cross-Platform Consistency**: Color customization works seamlessly across Google Maps, Search, and Pro List pages

### 🐛 **Code Quality Improvements**
- **📁 Clean Code Organization**: Modular color picker implementation following established patterns from tracked domains
- **🔧 Robust Error Handling**: Enhanced error handling for color validation and settings persistence
- **💻 Developer Experience**: Improved code maintainability with consistent naming conventions and clear separation of concerns

## [6.2.0] - 2025-01-24
### 🚀 **Major Feature: Comprehensive Quick Actions System**
- **⚡ Quick Actions Framework**: Complete quick actions system with 10+ specialized SEO and web analysis tools accessible directly from the popup
- **⌨️ Keyboard Shortcuts**: Full keyboard shortcut support for quick actions with enhanced focus detection and execution handling
- **🎛️ Centralized Control**: Dedicated Quick Actions settings page with individual toggles for each tool
- **🔄 State Management**: Persistent panel positions and sizes using localStorage with automatic restoration across sessions

### 🔍 **Advanced SEO Analysis Tools**
- **📊 Keyword Density Calculator**: Replaced keyword highlighter with professional density analysis tool for selected text
- **🏗️ Heading Structure Analyzer**: Interactive H1-H6 structure analysis with visual hierarchy and navigation in dedicated window
- **🖼️ Images SEO Audit**: Comprehensive image analysis for alt text, file names, and SEO optimization opportunities
- **📋 Metadata Analyzer**: Complete page metadata and social tag extraction with detailed reporting
- **🔗 Schema Extractor**: JSON-LD structured data extraction and display for technical SEO analysis

### 🎬 **Content Analysis & Extraction**
- **📺 YouTube Embed Scraper**: Automated scraping of YouTube video embed codes from channel pages with modern UI
- **🔤 Bold Text Extractor**: N-gram extraction from emphasized text elements for content analysis
- **👁️ Hidden Elements Revealer**: Toggle visibility of hidden page elements for debugging and content discovery
- **🔗 Link Analyzer**: Comprehensive link highlighting based on rel attributes with visual categorization
- **🏷️ H-Tags Highlighter**: Visual identification and highlighting of heading tags with interactive controls

### 🎨 **Enhanced User Interface & Experience**
- **🔄 Extension Reload**: One-click extension reload button in header for development and troubleshooting
- **📱 Responsive Design**: Improved popup layout with better spacing, alignment, and mobile-friendly Quick Actions
- **🎯 Smart Execution**: Automatic popup closure after successful quick action execution for seamless workflow
- **💾 Settings Persistence**: Enhanced settings export/import with comprehensive metadata and validation
- **🖥️ Window Management**: Intelligent window positioning and bounds checking for settings and analysis panels

### 🛠️ **Technical Architecture Improvements**
- **📁 Modular Organization**: Dedicated `/settings/quick-actions/` directory structure for clean code organization
- **🔧 Utility System**: Comprehensive utility files for extension reload, logging, and notification management
- **⚡ Performance Optimization**: Improved SERP position calculation with robust fallback mechanisms
- **🎯 Enhanced Detection**: Better Google search result detection and tracking with refined selectors
- **💾 Storage Management**: Advanced localStorage integration for panel persistence and user preferences

### 🔍 **SERP & Search Enhancements**
- **📊 Improved Position Tracking**: Enhanced SERP position calculation with multiple fallback methods for accuracy
- **🎯 Better Result Detection**: Refined logic for identifying search results while avoiding "People also ask" sections
- **📈 Location Persistence**: Automatic saving of location changes with real-time background settings updates
- **🔄 Consistent Numbering**: Unified SERP numbering logic across all position tracking features

### 🐛 **Bug Fixes & Stability**
- **✅ Category Extraction**: Improved category filtering to exclude invalid entries like "Favourites"
- **🔧 Settings Loading**: Enhanced settings validation and error handling for import/export operations
- **🎯 Focus Management**: Better keyboard focus detection and event handling for quick actions
- **💾 Data Persistence**: Resolved issues with location data saving and Chrome storage synchronization
- **🔄 State Consistency**: Fixed quick action state management and reset functionality

### 🌟 **Developer Experience**
- **📝 Enhanced Debugging**: Comprehensive logging and error reporting for troubleshooting
- **🔧 Development Tools**: Extension reload functionality for faster development iteration
- **📊 Code Quality**: Improved code organization with modular architecture and clear separation of concerns
- **🎯 Maintainability**: Better documentation and consistent coding standards across all modules

## [6.1.3] - 2025-01-22
- Enhances category extraction by filtering out invalid entries like "Favourites" and handling edge cases with closing brackets within category snippets. This prevents incorrect category identification and improves data accuracy. Also adds a version bump to the changelog.

## [6.1] - 2025-01-22
### 🌟 **Major Feature: Location Favorites System**
- **💾 Save Favorite Locations**: Users can now save frequently used locations with custom names for quick access in the Location Changer
- **⚡ One-Click Loading**: Load saved locations instantly with all coordinates, language settings, and regional preferences
- **🗑️ Smart Management**: Delete unwanted favorites with confirmation dialogs and real-time dropdown updates
- **💼 Professional Interface**: Clean, compact favorites section integrated seamlessly below Location Changer controls
- **📱 Responsive Design**: Mobile-friendly layout with stacked buttons and proper spacing on smaller screens

### 🎨 **Enhanced Location Changer UI**
- **📐 Improved Spacing**: Better visual hierarchy with consistent margins, padding, and element spacing throughout
- **🎯 Consistent Styling**: All buttons now use uniform gray color scheme matching the extension's design language
- **📏 Standardized Sizing**: Input fields, buttons, and controls now have consistent dimensions and typography
- **🔧 Flexible Layout**: Location Changer content area now expands dynamically (max-height: 100%) for better content accommodation
- **💎 Visual Polish**: Enhanced button hover effects, proper border radius, and professional color scheme

### 🔧 **Technical Implementation**
- **💾 LocalStorage Integration**: Favorites stored locally using browser storage with JSON serialization for data persistence
- **🏗️ Modular Architecture**: New `favorites.js` file with class-based structure for clean code organization
- **📝 Smart Field Mapping**: Automatic detection and population of all Location Changer fields (place, coordinates, language, region)
- **⚠️ Data Validation**: Input validation ensures locations have required data before saving, with user-friendly error messages
- **🔄 Real-time Updates**: Favorites dropdown updates immediately when locations are added or removed

### 🎯 **User Experience Enhancements**
- **💬 Visual Feedback**: Success and error messages with color-coded styling for clear status communication
- **⌨️ Keyboard Support**: Enter key in name input field automatically saves locations for efficient workflow
- **🔒 Duplicate Handling**: Smart name collision detection - updating existing favorites instead of creating duplicates
- **📊 Alphabetical Sorting**: Favorites automatically sorted alphabetically in dropdown for easy location finding
- **🎨 State-Aware Styling**: UI adapts to Location Changer active/inactive states with appropriate color schemes

### 🛠️ **Code Quality & Architecture**
- **📁 File Organization**: Favorites functionality properly organized within `/js/location-changer-js/` directory structure
- **🔗 Integration**: Seamlessly integrated with existing popup.html without disrupting current functionality  
- **🎯 Error Handling**: Comprehensive try-catch blocks for localStorage operations with graceful fallback behavior
- **📦 Clean Dependencies**: No additional external libraries required - uses native browser APIs and existing extension infrastructure
- **🧹 Maintainable Code**: Well-commented, modular code structure for easy future enhancements and bug fixes

### 🐛 **Bug Fixes & Improvements**
- **✅ Dynamic Content Height**: Fixed Location Changer max-height constraint to allow proper expansion with new favorites section
- **🎨 Button Color Consistency**: Resolved color scheme inconsistencies - all buttons now match extension's professional gray theme
- **📐 Layout Spacing**: Fixed cramped appearance with proper margins, padding, and element spacing throughout favorites section
- **🔧 Script Loading**: Proper script inclusion in popup.html for correct favorites functionality initialization
- **💾 Data Persistence**: Ensured favorites data survives browser restarts and extension updates

## [6.0] - 2025-01-20
### 🌍 **Major Feature: Location Changer Integration**
- **📍 Location Spoofing**: Fully integrated Location Changer functionality allowing users to spoof their geographic location for targeted business searches
- **🗺️ Interactive Map Interface**: Visual location selection with map-based coordinate picking for precise location setting
- **🌐 Global Database**: Access to comprehensive database of known locations with typeahead search functionality
- **⚙️ Advanced Controls**: Settings for latitude, longitude, language preferences, and regional targeting
- **🔄 Real-time Updates**: Location changes take effect immediately without requiring page refresh
- **💾 Persistent Settings**: Location preferences saved across sessions with full Chrome sync integration

### 🔍 **Enhanced Citation Discovery System**
- **🎯 Strategic Button Positioning**: Find Citations button now appears to the left of Copy NAP button for optimal workflow
- **🤖 Intelligent NAP Extraction**: Enhanced NAP data extraction logic with improved fallback strategies for address and phone detection
- **📑 Advanced Query Generation**: 14 strategic citation search queries using proven NAP Hunter methodology
- **⚡ Performance Optimized**: Staggered tab creation prevents browser overwhelm while maintaining rapid deployment
- **🛡️ Error Handling**: Robust error handling with clear user feedback for failed citation searches
- **🔄 Background Processing**: All citation tabs managed by background service worker for optimal performance

### 🎨 **UI/UX Design Improvements** 
- **📏 Consistent Button Sizing**: Standardized all NAP buttons to 100px width with unified styling across all platforms
- **🎯 Optimal Button Layout**: Find Citations and Copy NAP buttons now display side-by-side with proper horizontal alignment
- **💎 Visual Harmony**: Refined button styling with consistent colors, borders, and hover effects for professional appearance
- **📱 Responsive Design**: Enhanced responsive behavior for different screen sizes and popup window dimensions
- **🔧 Smart Injection Logic**: Prevented duplicate button injection on single business pages while maintaining multi-listing functionality

### ⚡ **Critical Copy Text Button Fix**
- **✅ Google Search Support**: Fixed Copy Text button functionality on Google Search pages (`google.com/maps/search`)
- **📊 Review Analysis Data**: Added proper handling for `google_search_review_analysis` data type in copy functions
- **📈 Comprehensive Data Export**: Copy Text now includes complete rating distribution, category breakdowns, and individual business data
- **🔄 Data Type Detection**: Enhanced data type recognition for proper formatting across different analysis types
- **💼 Business-Ready Format**: Structured data export optimized for Google Sheets with proper tab-separated formatting

### 🏗️ **Architecture & Code Quality**
- **📁 Enhanced File Organization**: Better separation of Location Changer components within organized directory structure
- **🔧 Modular Integration**: Location Changer functionality cleanly integrated without disrupting existing features
- **⚡ Performance Optimizations**: Reduced code duplication and improved injection logic efficiency
- **🛡️ Conflict Prevention**: Enhanced namespace protection to prevent conflicts with other Chrome extensions
- **📦 Asset Management**: Proper handling of Location Changer assets including Bootstrap, jQuery, and Handlebars libraries

### 🔧 **Technical Enhancements**
- **🔄 Background Service Worker**: Enhanced background.js with location management and citation tab handling
- **💾 Chrome Storage Integration**: Improved settings persistence for location preferences and user configurations
- **🎯 Content Script Optimization**: Streamlined content script loading with proper dependency management
- **⚙️ Manifest Updates**: Updated manifest permissions and host permissions for enhanced functionality
- **🔍 Advanced Selectors**: Improved DOM element detection with multiple fallback strategies

### 🛠️ **Developer Experience**
- **📝 Clean Code Architecture**: Modular approach to new features with clear separation of concerns
- **🔧 Debugging Improvements**: Enhanced error logging and debugging capabilities for development
- **📊 Performance Monitoring**: Better performance tracking for location changes and citation operations
- **🎯 Focused Implementation**: Strategic feature additions without compromising existing functionality
- **🔄 Maintainable Codebase**: Improved code organization for easier future enhancements and bug fixes

### 🐛 **Bug Fixes & Stability**
- **✅ Button Positioning**: Fixed Find Citations button alignment issues across different Google page types
- **🔧 Injection Logic**: Resolved duplicate button injection problems on single business pages
- **📊 Data Export**: Fixed Copy Text button functionality for Google Search review analysis data
- **⚡ Performance Issues**: Eliminated race conditions in button injection and location setting
- **🎯 Cross-Platform**: Ensured consistent functionality across Google Maps, Search, and Pro List pages

### 🌟 **User Experience Enhancements**
- **🎯 Intuitive Controls**: Location Changer interface designed for ease of use with clear visual feedback
- **⚡ Instant Feedback**: Real-time location updates with immediate visual confirmation
- **📱 Mobile-Friendly**: Enhanced responsive design for different device types and screen orientations
- **🔄 Seamless Integration**: New features blend naturally with existing extension workflow
- **💼 Professional Polish**: Enterprise-grade user interface with consistent design language throughout

## [5.1] - 2025-01-15
### 🎯 **Major Feature: Citation Hunter**
- **🔍 Find Citations Button**: New "Find Citations" button appears alongside "Copy NAP" for comprehensive citation discovery
- **🤖 Smart Query Generation**: Automatically generates 14 strategic search queries using NAP Hunter methodology for maximum citation coverage
- **📑 Lazy-Loading Tabs**: Opens citation search tabs with placeholder content that only loads Google searches when clicked for optimal performance
- **⚡ Background Processing**: All citation tabs open in background to prevent browser slowdown and maintain workflow efficiency

### ⚙️ **Settings Integration & Control**
- **🎛️ Citation Hunter Toggle**: Added dedicated setting in EXTRAS > Advanced Features to enable/disable Citation Hunter functionality
- **🔄 Real-time Control**: Settings changes take effect immediately without page refresh - toggle on/off Citation Hunter instantly
- **💾 Persistent Preferences**: Citation Hunter state saves with other extension settings and syncs across devices
- **🎯 Granular Control**: Users can disable just Citation Hunter while keeping other extension features active

### 🌐 **Cross-Platform Compatibility**
- **🗺️ Google Maps Support**: Citation Hunter now works on Google Maps business listings (primary target platform)
- **🔍 Google Search Integration**: Full compatibility with Google Search business popups and knowledge panels
- **📋 Pro List Support**: Works with Google Local Services Pro List pages for service-based businesses
- **🔗 Universal NAP Extraction**: Consistent NAP data extraction across all supported Google platforms

### 🎨 **Enhanced User Interface**
- **📐 Side-by-Side Layout**: Copy NAP and Find Citations buttons now appear horizontally aligned under business headings
- **🎯 Consistent Sizing**: Both buttons standardized to 100px width for professional, uniform appearance
- **💎 Visual Harmony**: Find Citations button uses white background with orange border to complement orange Copy NAP button
- **📱 Responsive Design**: Button layout adapts gracefully across different screen sizes and popup widths

### 🔧 **Technical Architecture**
- **📨 Message Passing System**: Implemented proper Chrome extension architecture with background script handling tab creation
- **🛡️ Content Script Security**: Content scripts now use message passing instead of direct Chrome APIs for enhanced security
- **🔄 Background Tab Management**: Smart tab lifecycle management with automatic cleanup and tracking
- **⚡ Performance Optimized**: Staggered tab creation (100ms intervals) prevents browser overwhelm while maintaining speed

### 🐛 **Bug Fixes & Improvements**
- **✅ Tab Loading Fix**: Resolved "about:blank" tabs issue by removing problematic discard functionality
- **🎯 Button Positioning**: Fixed vertical stacking issues - buttons now properly align horizontally in all scenarios
- **🔧 Settings Persistence**: Enhanced settings loading to ensure Citation Hunter state loads correctly on page initialization
- **🛠️ Error Handling**: Improved error handling for tab creation with graceful fallbacks and user feedback

### 📊 **User Experience Enhancements**
- **💬 Clear Feedback**: Button shows loading state, success count, and error messages for transparent operation status
- **📈 Progress Indication**: Visual feedback shows "X tabs" created so users know exactly how many searches were generated
- **🎨 Professional Polish**: Hover effects, transitions, and consistent styling for enterprise-grade user experience
- **⚡ Instant Gratification**: Quick placeholder creation gives immediate visual feedback while searches prepare in background

### 🚀 **Performance Features**
- **💾 Zero Initial Load**: Placeholder tabs consume minimal resources until actually visited by user
- **🔄 Sequential Processing**: Background script creates tabs sequentially to prevent browser resource conflicts
- **📊 Smart Resource Management**: Only active tab consumes full resources - all others remain dormant until needed
- **⚡ Rapid Deployment**: Complete citation search setup in under 2 seconds with 14 ready-to-use search queries

## [5.0] - 2025-01-06
### 🎛️ **Major Feature: Complete Settings System**
- **⚙️ Advanced Settings Interface**: Full tabbed settings system with General Settings and Extras tabs, accessible from header on all pages
- **💾 Persistent Storage**: Settings automatically save to Chrome sync storage and persist across sessions and devices  
- **📥📤 Import/Export**: Export settings configurations to JSON files and import them on other devices/browsers
- **🔄 Live Settings Updates**: Real-time settings changes with immediate effect on content scripts (no page refresh required)

### 🔔 **Notification Control System**
- **🔕 Global Notification Management**: Complete control over browser notifications with user toggle (ON by default)
- **⚡ Instant Toggle**: Notifications can be turned OFF immediately and stay OFF until manually re-enabled
- **🔗 Smart Integration**: All extraction completion notifications now respect user preferences
- **✅ Fallback Graceful**: Works seamlessly even when notification permissions are denied

### 🐛 **Debug & Logging Control**
- **🔧 Global Debug Mode**: Centralized logging control system (OFF by default to reduce console noise)
- **📊 Developer-Friendly**: Enable debug mode to see detailed extraction progress and troubleshooting info
- **⚡ Performance Optimized**: Silent operation by default with zero console overhead when disabled
- **🔄 Runtime Switching**: Toggle debug mode on/off without page refresh

### 🎯 **Live Content Script Controls**
- **🔢 SERP Numbering**: Add numbered indicators (1, 2, 3...) to Google search results with live toggle
- **🟥 Sponsored Highlighter**: Highlight "Sponsored" text with red borders on Google pages with instant control
- **⚡ Real-time Updates**: Both features can be toggled ON/OFF instantly from settings without page refresh
- **🎨 Visual Enhancement**: Improve search result navigation and sponsored content identification

### 🏗️ **File Organization & Architecture**
- **📁 Settings Folder**: Created dedicated `/settings/` folder for all settings-related utilities and components
- **🔧 Utility System**: Modular logging-utility.js and notification-utility.js for consistent behavior across extension
- **🧹 Clean Separation**: Settings utilities separated from core functionality for better maintainability
- **📦 Organized Structure**: Improved project structure with logical file organization

### 🎨 **Enhanced User Interface**
- **⚙️ Header Settings Button**: Settings button prominently placed to the right of "SEO Time Machines" heading on all pages
- **🌚 Dark & Subtle Design**: Settings button uses dark, professional styling that integrates seamlessly with UI
- **👆 Always Accessible**: Settings available on Maps pages, Pro List pages, Search pages, and idle states
- **💼 Compact Design**: Maintains small, unobtrusive appearance while being clearly interactive

### 🐛 **Critical Bug Fixes**
- **✅ Settings Persistence**: Fixed settings not saving properly - now all toggles maintain state across page refreshes
- **🔧 Element Loading Order**: Fixed "Element not found" errors by ensuring HTML loads before JavaScript UI updates
- **⚡ Race Condition Prevention**: Improved settings loading sequence to prevent initialization conflicts
- **🔄 Chrome Storage Integration**: Enhanced Chrome storage API integration with proper error handling

### ⚡ **Performance Improvements**
- **📦 Optimized Loading**: Settings utilities load first to ensure proper initialization order
- **🔄 Reduced Redundancy**: Eliminated duplicate HTML sections and streamlined settings management
- **💾 Efficient Storage**: Settings only store actual form values, reducing storage overhead
- **🚀 Fast UI Updates**: Instant UI state changes without waiting for storage operations

### 🛠️ **Developer Experience**
- **📝 Clean Code**: Removed excessive debug logging noise while maintaining essential error reporting
- **🔧 Modular Design**: Settings system designed for easy addition of new features and controls
- **📊 Smart Defaults**: Sensible defaults (notifications ON, debug OFF) for optimal user experience
- **🎯 Focused Architecture**: Clear separation between settings management, utilities, and core functionality

## [4.1] - 2024-12-27
- **🔧 Critical Bug Fix**: Fixed sponsored results filtering - sponsored listings are now properly included in all extractions instead of being filtered out
- **📊 Real-time Counter Updates**: Fixed Multiple Listings Extractor business and attributes counters to update in real-time as each business is processed
- **⚡ Enhanced User Experience**: Progress tracking now shows accurate business count progression during Multiple Listings attribute extraction
- **🛠️ Code Optimization**: Improved updateExtractionStats function to handle different data formats for better compatibility across extraction types

## [4.0] - 2024-12-21
- **🚀 Major Google Search Integration Overhaul**: Complete rewrite of Google Search functionality with advanced business detection
- **📊 Enhanced Services Extraction**: New docked interface for extracting services and products from Google Search business popups 
- **🔍 Advanced Business Detection**: Improved detection algorithms for Google Search maps listings with sca_esv parameter support
- **⭐ Comprehensive Review Analysis**: Enhanced review analytics with aggregate statistics, rating distributions, and category breakdowns
- **🎯 Smart Filtering**: Automatic filtering of sponsored listings and duplicate businesses in search results
- **💼 ProList Enhancements**: Improved Pro List category extraction logic and data handling with better error recovery
- **🖥️ UI/UX Improvements**: Refactored popup interface with cleaner layout and better button organization
- **📈 Progress Tracking**: Global progress bars for enhanced user feedback during long operations
- **🔧 Code Optimization**: Major refactoring of extraction logic with improved performance and reliability
- **🎨 Modern Styling**: Updated CSS components with better responsive design and visual hierarchy
- **📱 Enhanced NAP Extraction**: Improved Search NAP Injector with better popup detection and data extraction
- **🛠️ Background Service Worker**: Added background.js for improved extension lifecycle management
- **📂 File Organization**: Complete project restructuring with organized CSS, JS, and documentation folders
- **🐛 Critical Bug Fixes**: Fixed rating distribution calculations, review scraper button visibility, and extraction reliability
- **⚡ Performance Optimizations**: Reduced conflicts with other extensions and improved memory management

## [3.5] - 2024-12-21
- **Google Search Integration**: Added "Copy NAP" buttons to Google search business popups
- **Search Results NAP Extraction**: Extract Name, Address, Phone from Google search listings
- **Performance Optimization**: Reduced conflicts with other extensions and improved efficiency

## [3.4] - 2024-12-21
- **UI Improvement**: Moved "Copy NAP" button to the right of business heading
- **Bug Fix**: Fixed click to copy function on single export to include all data

## [3.3] - 2024-12-20
- **Bug Fix**: Fixed missing "Review Scraper" button in single business view
- **UI Enhancement**: Side-by-side button layout for better organization

## [3.2] - 2024-12-20
- **Critical Fix**: Fixed Pro List attribute extraction not finding text content
- **Enhancement**: Improved "View More" button detection with multiple selector strategies
- **Data Quality**: Now extracts all available attributes (11+ per business)

## [3.1] - 2024-12-19
- **Smart Deduplication**: Advanced duplicate removal for services regardless of case
- **Professional Formatting**: Automatic proper capitalization of service names
- **Browser Notifications**: Desktop notifications for completed operations

## [3.0] - 2024-12-18
- **Google Local Services Support**: Full integration with Pro List platform
- **Service Scraper**: Extract services offered by businesses in Pro Lists
- **Service Analysis**: Comprehensive analysis of most common services
- **Persistent Popup**: Stay-on-top interface for continuous workflow

## [2.5] - 2024-12-15
- **Multiple Listings Analysis**: Analyze business categories across search results
- **Review Analytics**: Comprehensive review insights and statistics
- **Category Statistics**: Percentage breakdowns and trend analysis

## [2.0] - 2024-12-10
- **Complete Review Scraping**: Extract all reviews with full metadata
- **Automatic Expansion**: Intelligent expansion of truncated reviews
- **CSV Export**: Structured export with comprehensive fields

## [1.5] - 2024-12-05
- **About Tab Attributes**: Extract detailed business attributes and amenities
- **Enhanced Data**: Phone formatting, coordinates, Place ID & CID numbers
- **Generated Links**: Create review request and knowledge panel URLs

## [1.0] - 2024-11-30
- **Initial Release**: Basic business data extraction from Google Maps
- **Core Features**: Name, address, phone, website, category extraction
- **CSV Export**: Simple data export functionality

---

**For detailed technical documentation, see [README.md](README.md)**

*GMB Extract* w