# 🚨 CRITICAL: Data Extractor Protection Rules

## ABSOLUTE PROHIBITION - NEVER VIOLATE

**NEVER EVER interfere with the operations of data extractors or review analyzers under ANY circumstances.**

This is a **CRITICAL BUSINESS RULE** that must NEVER be violated by any developer, AI assistant, or contributor to this project.

## Protected Modules

The following files contain mission-critical data extraction logic and are **ABSOLUTELY PROTECTED**:

### Google Maps Extractors
- `js/multiple-listings.js` - Multiple business listings extraction
- `js/single-review-scraper.js` - Individual review scraping
- `js/review-analysis.js` - Review analysis and processing
- `js/business-review-scraper.js` - Business review collection

### Google Local Services Extractors  
- `js/prolist-extraction.js` - ProList business data extraction
- `js/prolist-review-scraper.js` - ProList review scraping

### Google Search Extractors
- `js/google-search-extractor.js` - Search results business data extraction
- `js/google-search-multiple-review-scraper.js` - Search page review collection

## FORBIDDEN ACTIONS

### ❌ NEVER DO THIS:
- Modify extraction algorithms or logic
- "Optimize" or "improve" extraction code
- Refactor extraction functions
- Add performance enhancements to extractors
- Change data collection methods
- Modify review analysis logic
- Update extraction timing or flow
- Change DOM selectors in extractors
- Modify data processing pipelines
- Add new features to extraction modules

### ✅ ONLY PERMITTED:
- **Explicit bug fixes** requested by the user
- **Critical security patches** (with user approval)
- **Documentation updates** that don't affect code

## WHY THIS RULE EXISTS

1. **Business Critical**: These extractors are the core value proposition of the extension
2. **Fragile Logic**: Extraction code is highly sensitive to Google's DOM changes
3. **Revenue Impact**: Breaking extractors directly impacts user productivity and business operations
4. **Complex Dependencies**: Changes can have cascading effects across the entire system
5. **User Trust**: Users depend on reliable, consistent data extraction

## CONSEQUENCES OF VIOLATION

Violating this rule can result in:
- Complete loss of extraction functionality
- Data corruption or incomplete extraction
- User productivity disruption
- Loss of user trust and adoption
- Potential business liability

## ENFORCEMENT

- This rule is documented in multiple locations for redundancy
- All contributors must acknowledge this rule before contributing
- Code reviews must specifically check for extractor modifications
- Automated tests should protect against extraction logic changes

## EMERGENCY PROCEDURES

If a critical bug is discovered in an extractor:

1. **DO NOT FIX IMMEDIATELY**
2. Document the exact issue and impact
3. Get explicit user approval for the specific fix
4. Make minimal, surgical changes only
5. Test extensively on multiple Google domains
6. Monitor for any regression issues

## REMEMBER

**When in doubt, DON'T TOUCH THE EXTRACTORS.**

The data extraction modules are the heart of this extension. Protecting them protects the entire project's value and reliability.