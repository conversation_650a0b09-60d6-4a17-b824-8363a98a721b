# Golden Rules for Version Updates

Use Subtasks and agents for this

## Pre-Release Analysis

Analyze recent code changes and update CHANGELOG.md with a new version entry. Look for changes in:

1. **Chrome Extension Core**:
   - `manifest.json` for version and permission changes
   - `js/` directory for feature and improvement changes
   - `css/` directory for styling updates
   - `settings/` directory for new settings or configuration changes

2. **Content Scripts & Background**:
   - `js/background.js` for service worker changes
   - `content.js` for content script coordinator updates
   - Platform-specific extractors (Google Maps, Search, Local Services)

3. **Settings & Configuration**:
   - `settings/settings.js` for new settings or defaults
   - Settings HTML files for UI changes
   - Quick Actions in `settings/quick-actions/`

4. **Documentation & Memory Bank**:
   - `memory-bank/` directory for updated patterns
   - `docs/` directory for golden rules changes
   - `CLAUDE.md` for development guidance updates

## Version Update Checklist

### 1. Core File Updates
- **ALWAYS** update `manifest.json` version field (semantic versioning)
- **ALWAYS** update `docs/CHANGELOG.md` with new version entry at TOP
- **ALWAYS** update `docs/README.md` if function or form changes occurred
- **NO EMOJIS** in the final CHANGELOG.md (follows project NO EMOJIS POLICY)

### 2. Settings Integration Verification
**CRITICAL**: Check if any new storage keys, settings, or features have been added:
- Search for new `chrome.storage.sync.set()` calls in codebase
- Look for new storage keys in `chrome.storage.sync.get()` calls
- Ensure ALL new settings are added to export function in `settings/settings.js`
- Ensure ALL new settings are handled in import function in `settings/settings.js`
- Add new settings to `defaultSettings` object with recommended defaults
- Verify "Reset to Defaults" functionality includes all new settings
- Test import/export works with both old and new setting files

### 3. Chrome Extension Specific Requirements
- Verify content script loading patterns are maintained
- Check namespace protection (`window.GMBExtractorLoaded`) is intact
- Ensure message passing between popup and content scripts works
- Validate DOM cleanup requirements are implemented for new features
- Test popup design standards compliance (purple branding, dark theme)

### 4. Feature Integration Verification
- **Quick Actions**: New actions added to `defaultQuickActionButtons` array
- **DOM Cleanup**: New features integrated with DOM snapshot utility
- **Progress Tracking**: Global progress functions used for long operations
- **Settings Integration**: All features have proper toggle controls

## Version History Analysis

When looking for updates:
- Go back to last committed version on main/origin branch
- Move forward to identify all changes since last release
- Focus on user-facing changes and technical improvements
- Include any new features in import/export functionality

## Changelog Format

Follow existing changelog structure:
- Use semantic versioning (major.minor.patch)
- Use today's date for release
- Include technical details for Chrome extension changes
- Create summary statement for Github commit description

## Commit Message Standards

### Git Commit Format
Start Github messages with version number followed by message:
```
v6.5.1 updates styling and fixes popup positioning issues
```

### `comGen` Command Workflow
When running "comGen" command:
1. Generate comprehensive commit message based on recent changes
2. Follow established commit message format with version prefix
3. Include technical details for Chrome extension specific changes
4. Prepare for Github push to main/origin branch

## Release Workflow

1. **Analysis Phase**: Review all changes since last version
2. **Documentation Phase**: Update changelog, README, and version files
3. **Verification Phase**: Test settings import/export, new features
4. **Commit Phase**: Use proper commit message format with version prefix
5. **Release Phase**: Push to main/origin branch

## Extension-Specific Considerations

### Manifest V3 Compliance
- Ensure service worker changes don't break lifecycle management
- Verify content script injection patterns remain functional
- Check host permissions for new extraction features

### Memory Management
- Verify DOM cleanup requirements are met for new features
- Check event listener cleanup implementation
- Ensure no memory leaks in popup or content scripts

### Settings System
- All new features must integrate with centralized settings
- Toggle controls must follow established patterns
- Settings changes must propagate to content scripts immediately

### Brand Consistency
- Purple brand color (#7C3AED) maintained across new UI elements
- Dark theme consistency in all popup interfaces
- NO EMOJIS policy enforced in all user-facing text

## Testing Requirements

Before version release:
- Test across multiple Google domains (.com, .co.uk, .ca, .com.au)
- Verify functionality on different page types (Maps, Search, Local Services)
- Test in multiple browsers (Chrome, Edge, Brave)
- Validate settings import/export with both old and new versions
- Confirm DOM cleanup prevents memory leaks