---
description: Compile Chrome extension into production-ready ZIP file
allowed-tools: Bash(*)
---

# IMPORTANT 
Run this command EVERY time I run it, even if I have already run it before.

# Chrome Extension Compiler

Compile the Chrome extension into a production-ready ZIP file by copying everything and then removing development files.

## Compilation Process

```bash
#!/bin/bash

# 1. Setup variables
VERSION=$(grep '"version":' manifest.json | sed 's/.*"version": *"\([^"]*\)".*/\1/')
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="$HOME/Desktop/SEO-Time-Machines-Builds"
OUTPUT_FILE="$OUTPUT_DIR/SEO-Time-Machines-v${VERSION}-${TIMESTAMP}.zip"

echo "📦 Compiling Chrome Extension v${VERSION}"

# 2. Create output directory
mkdir -p "$OUTPUT_DIR"

# 3. Create temp directory and copy everything
TEMP_DIR=$(mktemp -d)
cp -R . "$TEMP_DIR/extension_build"
cd "$TEMP_DIR/extension_build"

# 4. Delete excluded directories
rm -rf .git .claude .serena .vscode memory-bank docs .superdesign .cursor

# 5. Delete excluded files
rm -f CLAUDE.md CLAUDE.local.md gemini.md Readability-tasks.md .gitignore .windsurfrules
rm -f test-*.js temp-*.js
rm -f blocked.js

# 6. Create ZIP
zip -r "$OUTPUT_FILE" . >/dev/null 2>&1

# 7. Get file size and display summary
FILESIZE=$(stat -f%z "$OUTPUT_FILE" 2>/dev/null || stat -c%s "$OUTPUT_FILE" 2>/dev/null)
if [ "$FILESIZE" -gt 1048576 ]; then
    SIZE_MB=$(echo "scale=1; $FILESIZE/1048576" | bc)
    READABLE_SIZE="${SIZE_MB} MB"
else
    SIZE_KB=$(echo "scale=1; $FILESIZE/1024" | bc)
    READABLE_SIZE="${SIZE_KB} KB"
fi

# 8. Cleanup and notify
cd "$HOME"
rm -rf "$TEMP_DIR"

echo "✅ COMPILATION COMPLETE!"
echo "📦 File: GMB-Extension-v${VERSION}-${TIMESTAMP}.zip"
echo "📏 Size: $READABLE_SIZE"
echo "📂 Location: $OUTPUT_DIR"

# Play completion sound
afplay /System/Library/Sounds/Submarine.aiff 2>/dev/null &
```

## Excluded Items

### Directories:
- `.git/`
- `.claude/`
- `.serena/`
- `.vscode/`
- `memory-bank/`
- `docs/`
- `.superdesign/`
- `.cursor/`

### Files:
- `CLAUDE.md`
- `CLAUDE.local.md`
- `gemini.md`
- `Readability-tasks.md`
- `.gitignore`
- `.windsurfrules`
- `test-*.js`
- `temp-*.js`